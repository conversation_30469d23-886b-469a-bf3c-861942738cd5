# Environment Variables Template for Episteme
# Copy this file to .env.local and fill in the actual values
# NEVER commit .env files with real secrets to version control

# =============================================================================
# SECURITY NOTICE
# =============================================================================
# This file contains environment variable templates for local development.
# For production deployment, use Google Cloud Secret Manager.
# 
# Setup Instructions:
# 1. Copy this file: cp .env.template .env.local
# 2. Fill in real values in .env.local
# 3. Add .env.local to .gitignore (already done)
# 4. Source the file: source .env.local
# =============================================================================

# Database Configuration
DATABASE_URL=postgresql://username:password@host:port/database_name
POSTGRES_PASSWORD=your_secure_postgres_password

# Redis Configuration  
REDIS_URL=redis://:password@host:port
REDIS_PASSWORD=your_secure_redis_password

# Google Cloud Platform
GOOGLE_APPLICATION_CREDENTIALS=/path/to/your/service-account-key.json
GCP_PROJECT_ID=your-gcp-project-id
BIGQUERY_DATASET=your_dataset_name

# Authentication & Security
JWT_SECRET=your_jwt_secret_key_here
SESSION_SECRET=your_session_secret_key_here

# External Services
MINIO_ROOT_USER=your_minio_user
MINIO_ROOT_PASSWORD=your_minio_password

# Monitoring & Observability
GRAFANA_ADMIN_USER=admin
GRAFANA_ADMIN_PASSWORD=your_grafana_password

# Application Configuration
ENVIRONMENT=development
LOG_LEVEL=INFO

# =============================================================================
# PRODUCTION DEPLOYMENT NOTES
# =============================================================================
# For production deployment on Google Cloud:
# 1. Store all secrets in Google Cloud Secret Manager
# 2. Use Cloud Run service accounts or Workload Identity
# 3. Reference secrets via GCP_PROJECT_ID and secret names
# 4. Never use .env files in production containers
# =============================================================================