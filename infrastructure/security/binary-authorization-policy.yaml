# Binary Authorization Policy Configuration for Episteme Platform
# This file defines the Binary Authorization policy to be imported into GCP

# Global policy evaluation mode
globalPolicyEvaluationMode: ENABLE

# Default admission rule - applies to all images unless overridden
defaultAdmissionRule:
  requireAttestationsBy:
  - projects/vibe-match-463114/attestors/episteme-production-attestor
  enforcementMode: ENFORCED_BLOCK_AND_AUDIT_LOG
  evaluationMode: REQUIRE_ATTESTATION

# Whitelist patterns for trusted images that don't require attestations
admissionWhitelistPatterns:
# Google-managed base images
- namePattern: gcr.io/google-containers/*
- namePattern: gcr.io/google_containers/*
- namePattern: k8s.gcr.io/*
- namePattern: gcr.io/gke-release/*
- namePattern: gcr.io/distroless/*
- namePattern: gcr.io/cloud-builders/*
- namePattern: gcr.io/google.com/cloudsdktool/*

# Istio-related images
- namePattern: gcr.io/istio-release/*

# Monitoring and observability (using full registry paths)
- namePattern: docker.io/prom/*
- namePattern: docker.io/grafana/*

# Development and testing images (consider removing for production)
- namePattern: gcr.io/cloudshell-images/*

# Cluster-specific admission rules (currently empty)
clusterAdmissionRules: {}

# Kubernetes namespace-specific rules
kubernetesNamespaceAdmissionRules:
  # Production namespace requires stricter controls
  production:
    requireAttestationsBy:
    - projects/vibe-match-463114/attestors/episteme-production-attestor
    enforcementMode: ENFORCED_BLOCK_AND_AUDIT_LOG
    evaluationMode: REQUIRE_ATTESTATION
  
  # Staging namespace - same as default for now
  staging:
    requireAttestationsBy:
    - projects/vibe-match-463114/attestors/episteme-production-attestor
    enforcementMode: ENFORCED_BLOCK_AND_AUDIT_LOG
    evaluationMode: REQUIRE_ATTESTATION
  
  # Development namespace - more permissive for testing
  development:
    requireAttestationsBy:
    - projects/vibe-match-463114/attestors/episteme-production-attestor
    enforcementMode: DRYRUN_AUDIT_LOG_ONLY
    evaluationMode: REQUIRE_ATTESTATION

# Service account-specific rules (currently empty)
kubernetesServiceAccountAdmissionRules: {}

# Istio service identity rules (currently empty)
istioServiceIdentityAdmissionRules: {}

# Policy metadata
name: projects/vibe-match-463114/policy

# Additional configuration notes:
# 1. This policy requires all images to be attested by the episteme-production-attestor
# 2. Enforcement mode is set to ENFORCED_BLOCK_AND_AUDIT_LOG for production security
# 3. Whitelisted patterns allow essential Google-managed images
# 4. Different namespaces can have different security requirements
# 5. Production namespace requires additional security attestor (when implemented)
#
# To apply this policy:
# gcloud container binauthz policy import policy.yaml
#
# To test policy evaluation:
# gcloud container binauthz policy evaluate --image-url=IMAGE_URL