# Binary Authorization for Episteme Platform

This directory contains the Binary Authorization configuration and scripts for securing container deployments on the Episteme platform.

## Overview

Binary Authorization ensures that only trusted container images can be deployed to Cloud Run services. It works by:

1. **Requiring cryptographic attestations** on all container images
2. **Verifying signatures** before allowing deployment
3. **Blocking unsigned or unauthorized** container images
4. **Providing audit logs** for all deployment decisions

## Quick Start

### 1. Initial Setup

Run the setup script to configure Binary Authorization:

```bash
# Set environment variables
export PROJECT_ID="vibe-match-463114"
export REGION="us-central1"

# Run the setup script
./scripts/security/setup-binary-authorization.sh
```

This will:
- Enable required GCP APIs
- Create Cloud KMS keys for signing
- Set up the attestation authority
- Create the Binary Authorization policy
- Configure service accounts and permissions

### 2. Validate Setup

Verify the configuration:

```bash
./scripts/security/validate-binary-authorization.sh
```

### 3. Test Enforcement

Test that unsigned images are blocked:

```bash
./scripts/security/test-unsigned-image.sh
```

## Files and Structure

```
infrastructure/security/
├── README.md                           # This file
├── binary-authorization-policy.yaml    # Binary Authorization policy configuration
└── ...

scripts/security/
├── setup-binary-authorization.sh       # Initial setup script
├── validate-binary-authorization.sh    # Validation and testing script
└── test-unsigned-image.sh             # Test enforcement with unsigned image

research/google-cloud/binary-authorization/
└── setup.md                           # Comprehensive setup documentation

research/security/
└── container-security.md              # Container security best practices

.github/actions/sign-container-image/
└── action.yml                         # GitHub Actions for image signing
```

## Operational Procedures

### Signing Images in CI/CD

Images are automatically signed during the CI/CD process:

1. **Cloud Build**: Uses the signing step in `cloudbuild.yaml`
2. **GitHub Actions**: Uses the `sign-container-image` action

### Manual Image Signing

If you need to manually sign an image:

```bash
# Set variables
export PROJECT_ID="vibe-match-463114"
export IMAGE_URL="us-central1-docker.pkg.dev/vibe-match-463114/episteme/service:tag"
export ATTESTOR_NAME="episteme-production-attestor"

# Sign the image
gcloud beta container binauthz attestations sign-and-create \
  --artifact-url="${IMAGE_URL}" \
  --attestor="projects/${PROJECT_ID}/attestors/${ATTESTOR_NAME}" \
  --attestor-project="${PROJECT_ID}" \
  --keyversion-project="${PROJECT_ID}" \
  --keyversion-location="global" \
  --keyversion-keyring="episteme-binauthz-keys" \
  --keyversion-key="signing-key" \
  --keyversion="1"
```

### Verifying Image Attestations

Check if an image has valid attestations:

```bash
# List attestations
gcloud beta container binauthz attestations list \
  --artifact-url="${IMAGE_URL}" \
  --attestor="projects/${PROJECT_ID}/attestors/${ATTESTOR_NAME}"

# Test policy evaluation
gcloud container binauthz policy evaluate --image-url="${IMAGE_URL}"
```

### Updating the Policy

To modify the Binary Authorization policy:

1. Edit `infrastructure/security/binary-authorization-policy.yaml`
2. Import the updated policy:

```bash
gcloud container binauthz policy import infrastructure/security/binary-authorization-policy.yaml
```

## Emergency Procedures

### Temporarily Disable Binary Authorization

In case of emergency, you can temporarily disable enforcement:

```bash
# Create emergency policy file
cat > /tmp/emergency-policy.yaml << EOF
defaultAdmissionRule:
  enforcementMode: DRYRUN_AUDIT_LOG_ONLY
name: projects/${PROJECT_ID}/policy
EOF

# Import emergency policy
gcloud container binauthz policy import /tmp/emergency-policy.yaml

# Clean up
rm /tmp/emergency-policy.yaml
```

**⚠️ Warning**: This disables security enforcement. Restore the original policy as soon as possible.

### Restore Normal Policy

```bash
gcloud container binauthz policy import infrastructure/security/binary-authorization-policy.yaml
```

## Monitoring and Alerts

### View Binary Authorization Logs

```bash
# View policy violations
gcloud logging read 'resource.type="cloud_run_revision" AND protoPayload.serviceName="binaryauthorization.googleapis.com"' --limit=50

# View attestation activities
gcloud logging read 'resource.type="cloud_run_revision" AND jsonPayload.message:"attestation"' --limit=50
```

### Common Issues and Solutions

1. **Deployment Blocked**: Verify image has required attestations
2. **Attestation Failed**: Check service account permissions and KMS key access
3. **Policy Evaluation Error**: Validate policy syntax and attestor references

## Security Considerations

- **Key Rotation**: Regularly rotate Cloud KMS keys
- **Service Account Security**: Limit access to signing service accounts
- **Policy Reviews**: Regularly review and update Binary Authorization policies
- **Audit Logging**: Monitor Binary Authorization logs for security incidents

## Contact and Support

For issues with Binary Authorization:
1. Check the validation script output
2. Review the troubleshooting section in `research/google-cloud/binary-authorization/setup.md`
3. Contact the security team for policy changes

## Related Documentation

- [Binary Authorization Setup Guide](../../../research/google-cloud/binary-authorization/setup.md)
- [Container Security Best Practices](../../../research/security/container-security.md)
- [GitHub Actions Documentation](../../../.github/actions/sign-container-image/action.yml)