# Service Account Configuration for Cloud Run Services
# Each service gets a dedicated service account with minimal permissions

service_accounts:
  # Analysis Engine Service Account
  analysis-engine:
    name: "ccl-analysis-engine-sa"
    display_name: "Analysis Engine Service Account"
    description: "Service account for Analysis Engine with minimal permissions"
    roles:
      - "roles/spanner.databaseUser"  # Read/write to Spanner database
      - "roles/storage.objectViewer"  # Read uploaded files for analysis
      - "roles/monitoring.metricWriter"  # Write metrics to Cloud Monitoring
      - "roles/logging.logWriter"  # Write logs to Cloud Logging
      - "roles/cloudtrace.agent"  # Write trace data
    
  # Query Intelligence Service Account  
  query-intelligence:
    name: "ccl-query-intelligence-sa"
    display_name: "Query Intelligence Service Account"
    description: "Service account for Query Intelligence with AI/ML permissions"
    roles:
      - "roles/spanner.databaseUser"  # Read/write to Spanner database
      - "roles/aiplatform.user"  # Access Vertex AI for embeddings and LLM
      - "roles/monitoring.metricWriter"  # Write metrics
      - "roles/logging.logWriter"  # Write logs
      - "roles/cloudtrace.agent"  # Write trace data
      - "roles/storage.objectViewer"  # Read model artifacts
    
  # Pattern Mining Service Account
  pattern-mining:
    name: "ccl-pattern-mining-sa"
    display_name: "Pattern Mining Service Account"  
    description: "Service account for Pattern Mining with ML training permissions"
    roles:
      - "roles/spanner.databaseUser"  # Read/write to Spanner database
      - "roles/aiplatform.user"  # Access Vertex AI for training
      - "roles/ml.developer"  # ML Engine permissions for model training
      - "roles/storage.objectAdmin"  # Read/write model artifacts and training data
      - "roles/monitoring.metricWriter"  # Write metrics
      - "roles/logging.logWriter"  # Write logs
      - "roles/cloudtrace.agent"  # Write trace data
      - "roles/bigquery.dataEditor"  # Read/write training data in BigQuery
    
  # Marketplace Service Account
  marketplace:
    name: "ccl-marketplace-sa"
    display_name: "Marketplace Service Account"
    description: "Service account for Marketplace with transaction permissions"
    roles:
      - "roles/spanner.databaseUser"  # Read/write to Spanner database
      - "roles/pubsub.publisher"  # Publish transaction events
      - "roles/monitoring.metricWriter"  # Write metrics
      - "roles/logging.logWriter"  # Write logs
      - "roles/cloudtrace.agent"  # Write trace data
      - "roles/storage.objectViewer"  # Read pattern artifacts for marketplace
    
  # Collaboration Service Account
  collaboration:
    name: "ccl-collaboration-sa"
    display_name: "Collaboration Service Account"
    description: "Service account for Collaboration with real-time permissions"
    roles:
      - "roles/spanner.databaseUser"  # Read/write to Spanner database
      - "roles/pubsub.editor"  # Pub/Sub for real-time collaboration
      - "roles/monitoring.metricWriter"  # Write metrics
      - "roles/logging.logWriter"  # Write logs
      - "roles/cloudtrace.agent"  # Write trace data
    
  # Web Frontend Service Account
  web:
    name: "ccl-web-sa"
    display_name: "Web Frontend Service Account"
    description: "Service account for Web Frontend with minimal permissions"
    roles:
      - "roles/monitoring.metricWriter"  # Write metrics
      - "roles/logging.logWriter"  # Write logs
      - "roles/cloudtrace.agent"  # Write trace data
      # Note: Web frontend should primarily make authenticated calls to other services
      # It should not directly access databases or other GCP services

# Service-to-service communication permissions
service_communication:
  # Analysis Engine can be called by other services
  analysis-engine:
    allowed_callers:
      - "ccl-query-intelligence-sa"
      - "ccl-pattern-mining-sa"
      - "ccl-web-sa"
    
  # Query Intelligence can be called by web and other services
  query-intelligence:
    allowed_callers:
      - "ccl-web-sa"
      - "ccl-collaboration-sa"
    
  # Pattern Mining can be called by other services
  pattern-mining:
    allowed_callers:
      - "ccl-query-intelligence-sa"
      - "ccl-marketplace-sa"
      - "ccl-web-sa"
    
  # Marketplace can be called by web
  marketplace:
    allowed_callers:
      - "ccl-web-sa"
    
  # Collaboration can be called by web
  collaboration:
    allowed_callers:
      - "ccl-web-sa"

# Security policies
security_policies:
  # Disable default service account usage
  disable_default_service_account: true
  
  # Require service account impersonation for admin operations
  require_impersonation: true
  
  # Enable audit logging for all service account activities
  audit_logging: true
  
  # Rotate service account keys (if any) every 90 days
  key_rotation_days: 90
  
  # Enable VPC Service Controls for additional security
  vpc_service_controls: true