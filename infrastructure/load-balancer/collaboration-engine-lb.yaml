# Collaboration Engine Load Balancer Configuration
# Optimized for WebSocket connections with SSL/TLS termination

apiVersion: compute.cnrm.cloud.google.com/v1beta1
kind: ComputeGlobalAddress
metadata:
  name: collaboration-engine-ip
  namespace: default
  labels:
    service: collaboration-engine
    environment: production
spec:
  addressType: EXTERNAL
  ipVersion: IPV4
  description: "Static IP for Collaboration Engine load balancer"

---
apiVersion: compute.cnrm.cloud.google.com/v1beta1
kind: ComputeBackendService
metadata:
  name: collaboration-engine-backend
  namespace: default
  labels:
    service: collaboration-engine
    environment: production
spec:
  description: "Backend service for Collaboration Engine with WebSocket support"
  protocol: HTTPS
  portName: http1
  timeoutSec: 3600  # 1 hour for WebSocket connections
  connectionDraining:
    drainingTimeoutSec: 300  # 5 minutes graceful shutdown
  sessionAffinity: CLIENT_IP  # Sticky sessions for WebSocket
  affinityCookieTtlSec: 3600  # 1 hour cookie TTL
  
  # Health check configuration
  healthChecks:
  - healthCheckRef:
      name: collaboration-engine-health-check
  
  # Backend configuration pointing to Cloud Run NEG
  backends:
  - group: "https://www.googleapis.com/compute/v1/projects/vibe-match-463114/regions/us-central1/networkEndpointGroups/collaboration-engine-neg"
    balancingMode: UTILIZATION
    capacityScaler: 1.0
    maxUtilization: 0.8
  
  # Connection draining for graceful WebSocket closure
  connectionDraining:
    drainingTimeoutSec: 300
  
  # Enable Cloud CDN for static assets
  enableCDN: false  # Disabled for WebSocket compatibility
  
  # Logging
  logConfig:
    enable: true
    sampleRate: 1.0

---
apiVersion: compute.cnrm.cloud.google.com/v1beta1
kind: ComputeHealthCheck
metadata:
  name: collaboration-engine-health-check
  namespace: default
  labels:
    service: collaboration-engine
    environment: production
spec:
  description: "Health check for Collaboration Engine"
  type: HTTPS
  httpsHealthCheck:
    host: ""
    port: 443
    portSpecification: USE_FIXED_PORT
    requestPath: "/health/live"
    proxyHeader: NONE
  checkIntervalSec: 10
  timeoutSec: 5
  healthyThreshold: 2
  unhealthyThreshold: 3

---
apiVersion: compute.cnrm.cloud.google.com/v1beta1
kind: ComputeURLMap
metadata:
  name: collaboration-engine-url-map
  namespace: default
  labels:
    service: collaboration-engine
    environment: production
spec:
  description: "URL map for Collaboration Engine with WebSocket routing"
  defaultService:
    backendServiceRef:
      name: collaboration-engine-backend
  
  hostRules:
  - hosts:
    - "collab.episteme.app"
    - "collaboration.episteme.app"
    pathMatcher: collaboration-paths
  
  pathMatchers:
  - name: collaboration-paths
    defaultService:
      backendServiceRef:
        name: collaboration-engine-backend
    
    # WebSocket path configuration
    pathRules:
    - paths:
      - "/ws"
      - "/ws/*"
      service:
        backendServiceRef:
          name: collaboration-engine-backend
      routeAction:
        timeout: 3600s  # 1 hour for WebSocket
        retryPolicy:
          numRetries: 0  # No retries for WebSocket
    
    # API paths
    - paths:
      - "/api/*"
      service:
        backendServiceRef:
          name: collaboration-engine-backend
      routeAction:
        timeout: 30s
        retryPolicy:
          numRetries: 2
          perTryTimeout: 10s
    
    # Health check paths
    - paths:
      - "/health"
      - "/health/*"
      service:
        backendServiceRef:
          name: collaboration-engine-backend
      routeAction:
        timeout: 5s

---
apiVersion: compute.cnrm.cloud.google.com/v1beta1
kind: ComputeTargetHTTPSProxy
metadata:
  name: collaboration-engine-https-proxy
  namespace: default
  labels:
    service: collaboration-engine
    environment: production
spec:
  description: "HTTPS proxy for Collaboration Engine with SSL termination"
  urlMapRef:
    name: collaboration-engine-url-map
  sslCertificates:
  - name: episteme-wildcard-cert  # Managed SSL certificate
  quicOverride: ENABLE  # Enable QUIC for better performance

---
apiVersion: compute.cnrm.cloud.google.com/v1beta1
kind: ComputeGlobalForwardingRule
metadata:
  name: collaboration-engine-forwarding-rule
  namespace: default
  labels:
    service: collaboration-engine
    environment: production
spec:
  description: "Global forwarding rule for Collaboration Engine"
  ipProtocol: TCP
  portRange: "443"
  target:
    targetHTTPSProxyRef:
      name: collaboration-engine-https-proxy
  ipAddress:
    addressRef:
      name: collaboration-engine-ip
  loadBalancingScheme: EXTERNAL
  networkTier: PREMIUM

---
# SSL Certificate configuration
apiVersion: compute.cnrm.cloud.google.com/v1beta1
kind: ComputeManagedSSLCertificate
metadata:
  name: episteme-wildcard-cert
  namespace: default
  labels:
    service: collaboration-engine
    environment: production
spec:
  managed:
    domains:
    - "*.episteme.app"
    - "episteme.app"

---
# Network Endpoint Group for Cloud Run
apiVersion: compute.cnrm.cloud.google.com/v1beta1
kind: ComputeRegionNetworkEndpointGroup
metadata:
  name: collaboration-engine-neg
  namespace: default
  labels:
    service: collaboration-engine
    environment: production
spec:
  region: us-central1
  networkEndpointType: SERVERLESS
  cloudRun:
    service: collaboration-engine-production
    urlMask: "<service>.<region>.run.app/<path>"

---
# Firewall rules for WebSocket
apiVersion: compute.cnrm.cloud.google.com/v1beta1
kind: ComputeFirewall
metadata:
  name: allow-collaboration-websocket
  namespace: default
  labels:
    service: collaboration-engine
    environment: production
spec:
  description: "Allow WebSocket connections to Collaboration Engine"
  direction: INGRESS
  priority: 1000
  sourceRanges:
  - "0.0.0.0/0"  # Allow from anywhere
  allowed:
  - protocol: tcp
    ports:
    - "443"  # HTTPS/WSS
  - protocol: tcp
    ports:
    - "80"   # HTTP/WS (redirect to HTTPS)
  targetTags:
  - collaboration-engine
  - websocket
  networkRef:
    name: default

---
# HTTP to HTTPS redirect
apiVersion: compute.cnrm.cloud.google.com/v1beta1
kind: ComputeURLMap
metadata:
  name: collaboration-engine-http-redirect
  namespace: default
  labels:
    service: collaboration-engine
    environment: production
spec:
  description: "HTTP to HTTPS redirect for Collaboration Engine"
  defaultUrlRedirect:
    httpsRedirect: true
    redirectResponseCode: MOVED_PERMANENTLY_DEFAULT
    stripQuery: false

---
apiVersion: compute.cnrm.cloud.google.com/v1beta1
kind: ComputeTargetHTTPProxy
metadata:
  name: collaboration-engine-http-proxy
  namespace: default
  labels:
    service: collaboration-engine
    environment: production
spec:
  description: "HTTP proxy for redirect to HTTPS"
  urlMapRef:
    name: collaboration-engine-http-redirect

---
apiVersion: compute.cnrm.cloud.google.com/v1beta1
kind: ComputeGlobalForwardingRule
metadata:
  name: collaboration-engine-http-forwarding
  namespace: default
  labels:
    service: collaboration-engine
    environment: production
spec:
  description: "HTTP forwarding rule for redirect"
  ipProtocol: TCP
  portRange: "80"
  target:
    targetHTTPProxyRef:
      name: collaboration-engine-http-proxy
  ipAddress:
    addressRef:
      name: collaboration-engine-ip
  loadBalancingScheme: EXTERNAL
  networkTier: PREMIUM