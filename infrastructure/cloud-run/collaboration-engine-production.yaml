# Collaboration Engine Production Cloud Run Configuration
# Optimized for real-time WebSocket connections and <50ms latency

apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: collaboration-engine-production
  namespace: default
  labels:
    cloud.googleapis.com/location: us-central1
    environment: production
    service: collaboration-engine
    version: v1.0.0
  annotations:
    run.googleapis.com/ingress: all
    run.googleapis.com/vpc-access-connector: episteme-connector
    run.googleapis.com/execution-environment: gen2
    run.googleapis.com/binary-authorization: default
spec:
  template:
    metadata:
      labels:
        run.googleapis.com/startupProbeType: custom
        environment: production
        service: collaboration-engine
      annotations:
        # Auto-scaling configuration optimized for WebSocket
        autoscaling.knative.dev/minScale: "1"
        autoscaling.knative.dev/maxScale: "50"
        autoscaling.knative.dev/target: "100"  # Target connections per instance
        autoscaling.knative.dev/metric: "concurrency"
        autoscaling.knative.dev/window: "60s"
        
        # Performance optimization for low latency
        run.googleapis.com/cpu-throttling: "false"
        run.googleapis.com/memory: "4Gi"
        run.googleapis.com/cpu: "2"
        run.googleapis.com/startup-cpu-boost: "true"
        
        # WebSocket configuration
        run.googleapis.com/session-affinity: "true"  # Sticky sessions for WebSocket
        
        # Monitoring integration
        prometheus.io/scrape: "true"
        prometheus.io/port: "9003"
        prometheus.io/path: "/metrics"
        
        # Security annotations
        run.googleapis.com/network-tags: "collaboration-engine,production,websocket"
    spec:
      containerConcurrency: 1000  # Max concurrent requests per instance
      timeoutSeconds: 3600  # 1 hour for WebSocket connections
      serviceAccountName: <EMAIL>
      containers:
      - name: collaboration-engine
        image: us-central1-docker.pkg.dev/vibe-match-463114/ccl-services/collaboration-engine:latest
        ports:
        - name: http1
          containerPort: 8003
        - name: metrics
          containerPort: 9003
        env:
        # Core service configuration
        - name: ENVIRONMENT
          value: "production"
        - name: RUST_LOG
          value: "collaboration_engine=info,tower_http=debug"
        - name: PORT
          value: "8003"
        - name: METRICS_PORT
          value: "9003"
        - name: GCP_PROJECT_ID
          value: "vibe-match-463114"
        - name: GCP_REGION
          value: "us-central1"
        
        # WebSocket configuration
        - name: WS_HEARTBEAT_INTERVAL_SECS
          value: "30"
        - name: WS_CLIENT_TIMEOUT_SECS
          value: "60"
        - name: WS_MAX_CONNECTIONS_PER_USER
          value: "10"
        - name: WS_MAX_MESSAGE_SIZE
          value: "1048576"  # 1MB
        - name: ENABLE_MESSAGE_COMPRESSION
          value: "true"
        - name: MESSAGE_COMPRESSION_THRESHOLD
          value: "1024"  # Compress messages > 1KB
        
        # Performance optimization
        - name: MESSAGE_BATCH_SIZE
          value: "50"
        - name: MESSAGE_BATCH_TIMEOUT_MS
          value: "10"  # 10ms batching window
        - name: MAX_CONCURRENT_SESSIONS
          value: "1000"
        - name: TOKIO_WORKER_THREADS
          value: "4"  # Optimized for 2 CPU cores
        
        # Database configuration
        - name: SPANNER_INSTANCE_ID
          valueFrom:
            secretKeyRef:
              name: collaboration-engine-spanner-instance
              key: latest
        - name: SPANNER_DATABASE_ID
          valueFrom:
            secretKeyRef:
              name: collaboration-engine-spanner-database
              key: latest
        - name: SPANNER_POOL_MIN
          value: "5"
        - name: SPANNER_POOL_MAX
          value: "50"
        
        # Redis configuration (for session state and rate limiting)
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: collaboration-engine-redis-url
              key: latest
        - name: REDIS_POOL_SIZE
          value: "20"
        - name: REDIS_CONNECTION_TIMEOUT_MS
          value: "5000"
        
        # JWT configuration
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: collaboration-engine-jwt-secret
              key: latest
        - name: JWT_ISSUER
          value: "episteme"
        - name: JWT_AUDIENCE
          value: "episteme-platform"
        - name: JWT_ACCESS_TOKEN_EXPIRY_SECS
          value: "900"  # 15 minutes
        - name: JWT_REFRESH_TOKEN_EXPIRY_SECS
          value: "86400"  # 24 hours
        
        # Rate limiting
        - name: RATE_LIMIT_REQUESTS_PER_MINUTE
          value: "60"
        - name: RATE_LIMIT_WEBSOCKET_MESSAGES_PER_MINUTE
          value: "60"
        - name: RATE_LIMIT_CURSOR_UPDATES_PER_MINUTE
          value: "180"  # 3x message rate
        
        # Session configuration
        - name: MAX_SESSION_PARTICIPANTS
          value: "100"
        - name: SESSION_IDLE_TIMEOUT_MINUTES
          value: "30"
        - name: MAX_MESSAGE_HISTORY
          value: "1000"
        
        # Feature flags
        - name: ENABLE_MESSAGE_PERSISTENCE
          value: "true"
        - name: ENABLE_PRESENCE_TRACKING
          value: "true"
        - name: ENABLE_ANALYTICS
          value: "true"
        
        # Integration configuration
        - name: ANALYSIS_ENGINE_URL
          value: "https://analysis-engine-572735000332.us-central1.run.app"
        - name: QUERY_INTELLIGENCE_URL
          value: "https://query-intelligence-production-572735000332.us-central1.run.app"
        - name: SERVICE_TIMEOUT_SECS
          value: "30"
        - name: INTEGRATION_CACHE_TTL_SECS
          value: "300"
        
        # Monitoring configuration
        - name: ENABLE_METRICS
          value: "true"
        - name: ENABLE_TRACING
          value: "true"
        - name: OTLP_ENDPOINT
          valueFrom:
            secretKeyRef:
              name: collaboration-engine-otlp-endpoint
              key: latest
        - name: OTEL_SERVICE_NAME
          value: "collaboration-engine"
        - name: OTEL_EXPORTER_OTLP_PROTOCOL
          value: "grpc"
        
        # Security configuration
        - name: ENABLE_AUTH
          value: "true"
        - name: ENABLE_CSRF_PROTECTION
          value: "true"
        - name: CORS_ALLOWED_ORIGINS
          value: "https://episteme.app,https://app.episteme.app"
        
        resources:
          limits:
            cpu: "2000m"
            memory: "4Gi"
          requests:
            cpu: "1000m"
            memory: "2Gi"
        
        # Health and readiness probes
        startupProbe:
          httpGet:
            path: /health/live
            port: 8003
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 20  # Allow 100 seconds for startup
          successThreshold: 1
        
        livenessProbe:
          httpGet:
            path: /health/live
            port: 8003
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 5
          failureThreshold: 3
          successThreshold: 1
        
        readinessProbe:
          httpGet:
            path: /health/ready
            port: 8003
          initialDelaySeconds: 5
          periodSeconds: 10
          timeoutSeconds: 3
          failureThreshold: 3
          successThreshold: 1

