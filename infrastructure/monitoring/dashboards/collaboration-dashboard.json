{"displayName": "Collaboration Engine Dashboard", "mosaicLayout": {"columns": 12, "tiles": [{"width": 6, "height": 4, "widget": {"title": "WebSocket Connections", "xyChart": {"dataSets": [{"timeSeriesQuery": {"prometheusQuery": "websocket_connections_total{service=\"collaboration-engine\"}", "unitOverride": "1"}, "plotType": "LINE", "targetAxis": "Y1"}], "timeshiftDuration": "0s", "yAxis": {"label": "Connections", "scale": "LINEAR"}, "chartOptions": {"mode": "COLOR"}}}}, {"xPos": 6, "width": 6, "height": 4, "widget": {"title": "Message Latency (P95)", "xyChart": {"dataSets": [{"timeSeriesQuery": {"prometheusQuery": "histogram_quantile(0.95, sum(rate(websocket_latency_seconds_bucket{service=\"collaboration-engine\"}[5m])) by (le, message_type))", "unitOverride": "s"}, "plotType": "LINE", "targetAxis": "Y1"}], "yAxis": {"label": "Latency (seconds)", "scale": "LINEAR"}, "thresholds": [{"value": 0.05, "direction": "ABOVE", "color": "RED", "label": "50ms Target"}]}}}, {"yPos": 4, "width": 4, "height": 4, "widget": {"title": "Active Sessions", "scorecard": {"timeSeriesQuery": {"prometheusQuery": "collaboration_sessions_active{service=\"collaboration-engine\"}", "unitOverride": "1"}, "sparkChartView": {"sparkChartType": "SPARK_LINE"}, "thresholds": [{"value": 1000.0, "direction": "ABOVE", "color": "YELLOW"}]}}}, {"xPos": 4, "yPos": 4, "width": 4, "height": 4, "widget": {"title": "Rate Limit Violations", "xyChart": {"dataSets": [{"timeSeriesQuery": {"prometheusQuery": "sum(rate(rate_limit_exceeded_total{service=\"collaboration-engine\"}[5m])) by (limit_type)", "unitOverride": "1/s"}, "plotType": "STACKED_AREA", "targetAxis": "Y1"}], "yAxis": {"label": "Violations/sec", "scale": "LINEAR"}}}}, {"xPos": 8, "yPos": 4, "width": 4, "height": 4, "widget": {"title": "Error Rate", "scorecard": {"timeSeriesQuery": {"prometheusQuery": "sum(rate(collaboration_errors_total{service=\"collaboration-engine\"}[5m]))", "unitOverride": "1/s"}, "sparkChartView": {"sparkChartType": "SPARK_LINE"}, "thresholds": [{"value": 0.01, "direction": "ABOVE", "color": "YELLOW"}, {"value": 0.1, "direction": "ABOVE", "color": "RED"}]}}}, {"yPos": 8, "width": 6, "height": 4, "widget": {"title": "Messages Per Second", "xyChart": {"dataSets": [{"timeSeriesQuery": {"prometheusQuery": "sum(rate(websocket_messages_total{service=\"collaboration-engine\"}[1m])) by (message_type)", "unitOverride": "1/s"}, "plotType": "STACKED_AREA", "targetAxis": "Y1"}], "yAxis": {"label": "Messages/sec", "scale": "LINEAR"}}}}, {"xPos": 6, "yPos": 8, "width": 6, "height": 4, "widget": {"title": "Storage Latency", "xyChart": {"dataSets": [{"timeSeriesQuery": {"prometheusQuery": "histogram_quantile(0.95, sum(rate(storage_latency_seconds_bucket{service=\"collaboration-engine\"}[5m])) by (le, storage))", "unitOverride": "s"}, "plotType": "LINE", "targetAxis": "Y1"}], "yAxis": {"label": "Latency (seconds)", "scale": "LINEAR"}}}}, {"yPos": 12, "width": 4, "height": 4, "widget": {"title": "CPU Usage", "xyChart": {"dataSets": [{"timeSeriesQuery": {"prometheusQuery": "rate(container_cpu_usage_seconds_total{service=\"collaboration-engine\"}[5m])", "unitOverride": "1"}, "plotType": "LINE", "targetAxis": "Y1"}], "yAxis": {"label": "CPU Cores", "scale": "LINEAR"}, "thresholds": [{"value": 1.6, "direction": "ABOVE", "color": "YELLOW", "label": "80% of limit"}]}}}, {"xPos": 4, "yPos": 12, "width": 4, "height": 4, "widget": {"title": "Memory Usage", "xyChart": {"dataSets": [{"timeSeriesQuery": {"prometheusQuery": "container_memory_usage_bytes{service=\"collaboration-engine\"} / 1024 / 1024 / 1024", "unitOverride": "By"}, "plotType": "LINE", "targetAxis": "Y1"}], "yAxis": {"label": "Memory (GB)", "scale": "LINEAR"}, "thresholds": [{"value": 3.2, "direction": "ABOVE", "color": "YELLOW", "label": "80% of limit"}]}}}, {"xPos": 8, "yPos": 12, "width": 4, "height": 4, "widget": {"title": "Instance Count", "scorecard": {"timeSeriesQuery": {"prometheusQuery": "count(up{service=\"collaboration-engine\"})", "unitOverride": "1"}, "sparkChartView": {"sparkChartType": "SPARK_LINE"}}}}, {"yPos": 16, "width": 12, "height": 4, "widget": {"title": "JWT Authentication", "xyChart": {"dataSets": [{"timeSeriesQuery": {"prometheusQuery": "sum(rate(auth_attempts_total{service=\"collaboration-engine\"}[5m])) by (result)", "unitOverride": "1/s"}, "plotType": "STACKED_AREA", "targetAxis": "Y1"}], "yAxis": {"label": "Auth Attempts/sec", "scale": "LINEAR"}}}}, {"yPos": 20, "width": 6, "height": 4, "widget": {"title": "Message Compression", "xyChart": {"dataSets": [{"timeSeriesQuery": {"prometheusQuery": "histogram_quantile(0.95, sum(rate(message_compression_ratio_bucket{service=\"collaboration-engine\"}[5m])) by (le))", "unitOverride": "1"}, "plotType": "LINE", "targetAxis": "Y1"}], "yAxis": {"label": "Compression Ratio", "scale": "LINEAR"}}}}, {"xPos": 6, "yPos": 20, "width": 6, "height": 4, "widget": {"title": "Session Participants", "xyChart": {"dataSets": [{"timeSeriesQuery": {"prometheusQuery": "histogram_quantile(0.95, sum(rate(session_participants_bucket{service=\"collaboration-engine\"}[5m])) by (le))", "unitOverride": "1"}, "plotType": "LINE", "targetAxis": "Y1"}], "yAxis": {"label": "Participants per Session", "scale": "LINEAR"}}}}]}, "dashboardFilters": [{"filterType": "RESOURCE_LABEL", "labelKey": "service", "templateVariable": "SERVICE"}, {"filterType": "RESOURCE_LABEL", "labelKey": "environment", "templateVariable": "ENVIRONMENT"}]}