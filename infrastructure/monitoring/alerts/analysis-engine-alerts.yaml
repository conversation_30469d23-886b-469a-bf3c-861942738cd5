# Analysis Engine Production Alerting Rules
# Comprehensive monitoring for Phase 2 deployment

groups:
  - name: analysis-engine-production
    interval: 30s
    rules:
      
      # Service Health Alerts
      - alert: AnalysisEngineDown
        expr: up{job="analysis-engine"} == 0
        for: 1m
        labels:
          severity: critical
          service: analysis-engine
          environment: production
        annotations:
          summary: "Analysis Engine service is down"
          description: "Analysis Engine has been down for more than 1 minute. Service URL: {{ $labels.instance }}"
          runbook_url: "https://docs.episteme.dev/runbooks/service-down"
          
      - alert: AnalysisEngineHighErrorRate
        expr: (rate(http_requests_total{job="analysis-engine",status=~"5.."}[5m]) / rate(http_requests_total{job="analysis-engine"}[5m])) * 100 > 5
        for: 2m
        labels:
          severity: warning
          service: analysis-engine
        annotations:
          summary: "High error rate in Analysis Engine"
          description: "Error rate is {{ $value }}% over the last 5 minutes (threshold: 5%)"
          
      # Performance Alerts
      - alert: AnalysisEngineHighLatency
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job="analysis-engine"}[5m])) > 1.0
        for: 3m
        labels:
          severity: warning
          service: analysis-engine
        annotations:
          summary: "High latency in Analysis Engine"
          description: "95th percentile latency is {{ $value }}s (threshold: 1.0s)"
          
      - alert: AnalysisEngineSlowProcessing
        expr: rate(analysis_processing_duration_seconds_sum{job="analysis-engine"}[5m]) / rate(analysis_processing_duration_seconds_count{job="analysis-engine"}[5m]) > 30
        for: 5m
        labels:
          severity: warning
          service: analysis-engine
        annotations:
          summary: "Slow analysis processing"
          description: "Average analysis processing time is {{ $value }}s (threshold: 30s)"
          
      # Resource Utilization Alerts
      - alert: AnalysisEngineHighCPU
        expr: cpu_usage_percent{job="analysis-engine"} > 85
        for: 5m
        labels:
          severity: warning
          service: analysis-engine
        annotations:
          summary: "High CPU usage in Analysis Engine"
          description: "CPU usage is {{ $value }}% (threshold: 85%)"
          
      - alert: AnalysisEngineHighMemory
        expr: (memory_usage_bytes{job="analysis-engine"} / (4 * 1024 * 1024 * 1024)) * 100 > 90
        for: 5m
        labels:
          severity: critical
          service: analysis-engine
        annotations:
          summary: "High memory usage in Analysis Engine"
          description: "Memory usage is {{ $value }}% of 4Gi limit (threshold: 90%)"
          
      # Business Logic Alerts
      - alert: AnalysisEngineQueueBacklog
        expr: active_analyses{job="analysis-engine"} > 100
        for: 3m
        labels:
          severity: warning
          service: analysis-engine
        annotations:
          summary: "Analysis queue backlog building up"
          description: "{{ $value }} active analyses in queue (threshold: 100)"
          
      - alert: AnalysisEngineParsingFailures
        expr: rate(parsing_failures_total{job="analysis-engine"}[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
          service: analysis-engine
        annotations:
          summary: "High parsing failure rate"
          description: "Parsing failure rate: {{ $value }} failures/second"
          
      # Database Connection Alerts
      - alert: AnalysisEngineDBConnectionIssues
        expr: database_connections_active{job="analysis-engine"} == 0
        for: 1m
        labels:
          severity: critical
          service: analysis-engine
        annotations:
          summary: "No active database connections"
          description: "Analysis Engine has no active database connections"
          
      - alert: AnalysisEngineDBConnectionPoolExhaustion
        expr: database_connections_active{job="analysis-engine"} > 90
        for: 2m
        labels:
          severity: warning
          service: analysis-engine
        annotations:
          summary: "Database connection pool near exhaustion"
          description: "Active connections: {{ $value }} (approaching pool limit)"
          
      # Auto-scaling Alerts
      - alert: AnalysisEngineMaxInstancesReached
        expr: cloud_run_instances_count{service="analysis-engine-production"} >= 100
        for: 2m
        labels:
          severity: warning
          service: analysis-engine
        annotations:
          summary: "Analysis Engine at maximum instance count"
          description: "Service has reached maximum auto-scaling instances (100)"
          
      # Evidence Gate 2 Performance Monitoring
      - alert: AnalysisEnginePerformanceDegradation
        expr: rate(lines_of_code_processed_total{job="analysis-engine"}[1m]) < 1131  # 67,900 LOC/sec minimum
        for: 10m
        labels:
          severity: warning
          service: analysis-engine
          performance_tier: evidence_gate_2
        annotations:
          summary: "Analysis Engine performance below Evidence Gate 2 target"
          description: "Processing rate: {{ $value }} LOC/sec (target: 1131+ LOC/sec sustained)"
          runbook_url: "https://docs.episteme.dev/runbooks/performance-degradation"
          
  - name: analysis-engine-security
    interval: 60s
    rules:
      
      # Security Alerts
      - alert: AnalysisEngineUnauthorizedRequests
        expr: rate(http_requests_total{job="analysis-engine",status="401"}[5m]) > 0.5
        for: 2m
        labels:
          severity: warning
          service: analysis-engine
          category: security
        annotations:
          summary: "High rate of unauthorized requests"
          description: "{{ $value }} unauthorized requests/second to Analysis Engine"
          
      - alert: AnalysisEngineRateLimitExceeded
        expr: rate(http_requests_total{job="analysis-engine",status="429"}[5m]) > 1
        for: 1m
        labels:
          severity: info
          service: analysis-engine
          category: security
        annotations:
          summary: "Rate limiting active"
          description: "{{ $value }} rate-limited requests/second"
          
      - alert: AnalysisEngineCSRFFailures
        expr: rate(csrf_validation_failures_total{job="analysis-engine"}[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
          service: analysis-engine
          category: security
        annotations:
          summary: "CSRF validation failures detected"
          description: "{{ $value }} CSRF failures/second (possible attack)"
          
  - name: analysis-engine-business
    interval: 300s  # 5 minute intervals for business metrics
    rules:
      
      # Business Performance Indicators
      - alert: AnalysisEngineLanguageSupportDegraded
        expr: supported_languages_count{job="analysis-engine"} < 18
        for: 5m
        labels:
          severity: warning
          service: analysis-engine
          category: business
        annotations:
          summary: "Language support below expected level"
          description: "Supporting {{ $value }} languages (target: 18+)"
          
      - alert: AnalysisEngineSuccessRateDecline
        expr: (rate(analysis_completions_total{job="analysis-engine",status="success"}[10m]) / rate(analysis_completions_total{job="analysis-engine"}[10m])) * 100 < 95
        for: 10m
        labels:
          severity: warning
          service: analysis-engine
          category: business
        annotations:
          summary: "Analysis success rate declining"
          description: "Success rate: {{ $value }}% (target: 95%+)"