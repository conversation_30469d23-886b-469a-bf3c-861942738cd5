# Collaboration Engine Alert Rules
# Production monitoring alerts for WebSocket service

apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: collaboration-engine-alerts
  namespace: default
  labels:
    prometheus: kube-prometheus
    service: collaboration-engine
    environment: production
spec:
  groups:
  - name: collaboration.latency
    interval: 30s
    rules:
    
    # High WebSocket Message Latency
    - alert: CollaborationHighLatency
      expr: |
        histogram_quantile(0.95,
          sum(rate(websocket_latency_seconds_bucket{service="collaboration-engine"}[5m])) by (le, message_type)
        ) > 0.05
      for: 5m
      labels:
        severity: critical
        service: collaboration-engine
        team: platform
      annotations:
        summary: "High WebSocket latency detected (>50ms P95)"
        description: "Collaboration Engine P95 latency is {{ $value | humanizeDuration }} for message type {{ $labels.message_type }}. Target is <50ms."
        runbook_url: "https://wiki.episteme.app/runbooks/collaboration-latency"
        dashboard_url: "https://console.cloud.google.com/monitoring/dashboards/collaboration-engine"
    
    # Storage Latency
    - alert: CollaborationStorageLatency
      expr: |
        histogram_quantile(0.95,
          sum(rate(storage_latency_seconds_bucket{service="collaboration-engine"}[5m])) by (le, storage)
        ) > 0.2
      for: 5m
      labels:
        severity: warning
        service: collaboration-engine
        team: platform
      annotations:
        summary: "High storage latency for {{ $labels.storage }}"
        description: "Storage operation latency is {{ $value | humanizeDuration }} (P95) for {{ $labels.storage }}."
        runbook_url: "https://wiki.episteme.app/runbooks/storage-latency"

  - name: collaboration.errors
    interval: 30s
    rules:
    
    # High Error Rate
    - alert: CollaborationHighErrorRate
      expr: |
        sum(rate(collaboration_errors_total{service="collaboration-engine"}[5m])) > 0.1
      for: 5m
      labels:
        severity: critical
        service: collaboration-engine
        team: platform
        pagerduty: true
      annotations:
        summary: "High error rate in Collaboration Engine"
        description: "Error rate is {{ $value | humanize }} errors/sec. Normal is <0.01/sec."
        runbook_url: "https://wiki.episteme.app/runbooks/collaboration-errors"
        dashboard_url: "https://console.cloud.google.com/monitoring/dashboards/collaboration-engine"
    
    # WebSocket Connection Failures
    - alert: CollaborationConnectionFailures
      expr: |
        rate(websocket_connection_errors_total{service="collaboration-engine"}[5m]) > 0.05
      for: 5m
      labels:
        severity: warning
        service: collaboration-engine
        team: platform
      annotations:
        summary: "High WebSocket connection failure rate"
        description: "Connection failure rate is {{ $value | humanize }} failures/sec."
        runbook_url: "https://wiki.episteme.app/runbooks/websocket-connections"

  - name: collaboration.capacity
    interval: 30s
    rules:
    
    # High Connection Count
    - alert: CollaborationHighConnections
      expr: |
        websocket_connections_total{service="collaboration-engine"} > 800
      for: 10m
      labels:
        severity: warning
        service: collaboration-engine
        team: platform
      annotations:
        summary: "High WebSocket connection count"
        description: "{{ $value }} active connections (threshold: 800). Consider scaling up."
        runbook_url: "https://wiki.episteme.app/runbooks/collaboration-scaling"
    
    # Session Limit Approaching
    - alert: CollaborationSessionLimit
      expr: |
        collaboration_sessions_active{service="collaboration-engine"} > 900
      for: 5m
      labels:
        severity: warning
        service: collaboration-engine
        team: platform
      annotations:
        summary: "Approaching session limit"
        description: "{{ $value }} active sessions (limit: 1000 per instance)."
        runbook_url: "https://wiki.episteme.app/runbooks/session-limits"

  - name: collaboration.rate_limiting
    interval: 30s
    rules:
    
    # High Rate Limit Violations
    - alert: CollaborationRateLimitViolations
      expr: |
        sum(rate(rate_limit_exceeded_total{service="collaboration-engine"}[5m])) by (limit_type) > 1
      for: 5m
      labels:
        severity: warning
        service: collaboration-engine
        team: platform
      annotations:
        summary: "High rate limit violations for {{ $labels.limit_type }}"
        description: "{{ $value | humanize }} violations/sec for {{ $labels.limit_type }} limit."
        runbook_url: "https://wiki.episteme.app/runbooks/rate-limiting"
    
    # Sustained Rate Limiting
    - alert: CollaborationSustainedRateLimiting
      expr: |
        sum(rate(rate_limit_exceeded_total{service="collaboration-engine"}[15m])) > 0.5
      for: 15m
      labels:
        severity: critical
        service: collaboration-engine
        team: platform
      annotations:
        summary: "Sustained rate limiting detected"
        description: "Rate limiting has been active for 15+ minutes at {{ $value | humanize }} violations/sec."
        runbook_url: "https://wiki.episteme.app/runbooks/sustained-rate-limiting"

  - name: collaboration.availability
    interval: 30s
    rules:
    
    # Service Down
    - alert: CollaborationDown
      expr: |
        up{service="collaboration-engine"} == 0
      for: 2m
      labels:
        severity: critical
        service: collaboration-engine
        team: platform
        pagerduty: true
      annotations:
        summary: "Collaboration Engine is down"
        description: "Collaboration Engine has been down for 2+ minutes."
        runbook_url: "https://wiki.episteme.app/runbooks/service-down"
        dashboard_url: "https://console.cloud.google.com/monitoring/dashboards/collaboration-engine"
    
    # Partial Outage
    - alert: CollaborationPartialOutage
      expr: |
        count(up{service="collaboration-engine"} == 1) < 2
      for: 5m
      labels:
        severity: warning
        service: collaboration-engine
        team: platform
      annotations:
        summary: "Collaboration Engine running with reduced capacity"
        description: "Only {{ $value }} instance(s) running. Minimum recommended: 2."
        runbook_url: "https://wiki.episteme.app/runbooks/partial-outage"

  - name: collaboration.resources
    interval: 30s
    rules:
    
    # High CPU Usage
    - alert: CollaborationHighCPU
      expr: |
        rate(container_cpu_usage_seconds_total{service="collaboration-engine"}[5m]) > 1.6
      for: 10m
      labels:
        severity: warning
        service: collaboration-engine
        team: platform
      annotations:
        summary: "High CPU usage in Collaboration Engine"
        description: "CPU usage is {{ $value | humanize }} cores (80% of 2 core limit)."
        runbook_url: "https://wiki.episteme.app/runbooks/high-cpu"
    
    # High Memory Usage
    - alert: CollaborationHighMemory
      expr: |
        container_memory_usage_bytes{service="collaboration-engine"} / 1024 / 1024 / 1024 > 3.2
      for: 10m
      labels:
        severity: warning
        service: collaboration-engine
        team: platform
      annotations:
        summary: "High memory usage in Collaboration Engine"
        description: "Memory usage is {{ $value | humanize }}GB (80% of 4GB limit)."
        runbook_url: "https://wiki.episteme.app/runbooks/high-memory"

  - name: collaboration.authentication
    interval: 30s
    rules:
    
    # High Authentication Failure Rate
    - alert: CollaborationAuthFailures
      expr: |
        sum(rate(auth_attempts_total{service="collaboration-engine",result="failure"}[5m])) /
        sum(rate(auth_attempts_total{service="collaboration-engine"}[5m])) > 0.1
      for: 5m
      labels:
        severity: warning
        service: collaboration-engine
        team: platform
        security: true
      annotations:
        summary: "High authentication failure rate"
        description: "{{ $value | humanizePercentage }} of authentication attempts are failing."
        runbook_url: "https://wiki.episteme.app/runbooks/auth-failures"
    
    # JWT Token Expiry Issues
    - alert: CollaborationJWTExpiry
      expr: |
        rate(jwt_validation_errors_total{service="collaboration-engine",error="expired"}[5m]) > 0.1
      for: 5m
      labels:
        severity: warning
        service: collaboration-engine
        team: platform
      annotations:
        summary: "High rate of expired JWT tokens"
        description: "{{ $value | humanize }} expired tokens/sec. Check token refresh logic."
        runbook_url: "https://wiki.episteme.app/runbooks/jwt-expiry"

---
# Alert Manager Configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: collaboration-alertmanager-config
  namespace: default
data:
  alertmanager.yml: |
    global:
      resolve_timeout: 5m
      slack_api_url: 'YOUR_SLACK_WEBHOOK_URL'
      pagerduty_url: 'https://events.pagerduty.com/v2/enqueue'
    
    route:
      group_by: ['alertname', 'cluster', 'service']
      group_wait: 10s
      group_interval: 10s
      repeat_interval: 12h
      receiver: 'default'
      routes:
      - match:
          severity: critical
          service: collaboration-engine
        receiver: 'pagerduty-critical'
        continue: true
      - match:
          severity: warning
          service: collaboration-engine
        receiver: 'slack-warnings'
    
    receivers:
    - name: 'default'
      slack_configs:
      - channel: '#platform-alerts'
        title: 'Collaboration Engine Alert'
        text: '{{ range .Alerts }}{{ .Annotations.summary }}\n{{ .Annotations.description }}{{ end }}'
    
    - name: 'pagerduty-critical'
      pagerduty_configs:
      - service_key: 'YOUR_PAGERDUTY_SERVICE_KEY'
        description: '{{ .GroupLabels.alertname }}: {{ .CommonAnnotations.summary }}'
        details:
          firing: '{{ .Alerts.Firing | len }}'
          resolved: '{{ .Alerts.Resolved | len }}'
          service: 'collaboration-engine'
    
    - name: 'slack-warnings'
      slack_configs:
      - channel: '#platform-warnings'
        title: 'Collaboration Engine Warning'
        text: '{{ range .Alerts }}{{ .Annotations.summary }}\n{{ .Annotations.description }}{{ end }}'
        send_resolved: true