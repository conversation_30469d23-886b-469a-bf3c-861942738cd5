apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: episteme-alertmanager
  namespace: default
  labels:
    cloud.googleapis.com/location: us-central1
  annotations:
    run.googleapis.com/ingress: internal
    run.googleapis.com/vpc-access-connector: episteme-connector
    run.googleapis.com/execution-environment: gen2
spec:
  template:
    metadata:
      labels:
        run.googleapis.com/startupProbeType: custom
      annotations:
        autoscaling.knative.dev/minScale: "1"
        autoscaling.knative.dev/maxScale: "3"
        run.googleapis.com/cpu-throttling: "false"
        run.googleapis.com/memory: "1Gi"
        run.googleapis.com/cpu: "500m"
        run.googleapis.com/startup-cpu-boost: "true"
    spec:
      containerConcurrency: 500
      timeoutSeconds: 300
      serviceAccountName: <EMAIL>
      containers:
      - name: alertmanager
        image: prom/alertmanager:v0.26.0
        ports:
        - name: http1
          containerPort: 9093
        env:
        - name: PAGERDUTY_INTEGRATION_KEY
          valueFrom:
            secretKeyRef:
              name: alertmanager-secrets
              key: pagerduty_key
        - name: SLACK_WEBHOOK_URL
          valueFrom:
            secretKeyRef:
              name: alertmanager-secrets
              key: slack_webhook
        - name: SMTP_PASSWORD
          valueFrom:
            secretKeyRef:
              name: alertmanager-secrets
              key: smtp_password
        command:
        - /bin/alertmanager
        args:
        - --config.file=/etc/alertmanager/alertmanager.yml
        - --storage.path=/alertmanager
        - --web.external-url=https://episteme-alertmanager-dot-vibe-match-463114.uc.r.appspot.com
        - --web.route-prefix=/
        - --cluster.listen-address=0.0.0.0:9094
        volumeMounts:
        - name: alertmanager-config
          mountPath: /etc/alertmanager
          readOnly: true
        - name: alertmanager-storage
          mountPath: /alertmanager
        resources:
          limits:
            cpu: "500m"
            memory: "1Gi"
          requests:
            cpu: "250m"
            memory: "512Mi"
        startupProbe:
          httpGet:
            path: /-/ready
            port: 9093
          initialDelaySeconds: 15
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        livenessProbe:
          httpGet:
            path: /-/healthy
            port: 9093
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /-/ready
            port: 9093
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 5
          failureThreshold: 2
      volumes:
      - name: alertmanager-config
        configMap:
          name: alertmanager-config
      - name: alertmanager-storage
        persistentVolumeClaim:
          claimName: alertmanager-storage-pvc

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: alertmanager-config
  namespace: default
data:
  alertmanager.yml: |
    global:
      smtp_smarthost: 'smtp.gmail.com:587'
      smtp_from: '<EMAIL>'
      smtp_auth_username: '<EMAIL>'
      smtp_auth_password: '${SMTP_PASSWORD}'
      pagerduty_url: 'https://events.pagerduty.com/v2/enqueue'

    # Templates for notifications
    templates:
      - '/etc/alertmanager/templates/*.tmpl'

    # Routing tree
    route:
      group_by: ['alertname', 'cluster', 'service']
      group_wait: 10s
      group_interval: 10s
      repeat_interval: 12h
      receiver: 'web.hook.default'
      routes:
        # Critical alerts - immediate PagerDuty
        - match:
            severity: critical
          receiver: 'pagerduty-critical'
          group_wait: 0s
          repeat_interval: 5m

        # High severity alerts - Slack + Email
        - match:
            severity: high
          receiver: 'high-priority-alerts'
          group_wait: 30s
          repeat_interval: 30m

        # Security alerts - immediate escalation
        - match:
            category: security
          receiver: 'security-team'
          group_wait: 0s
          repeat_interval: 10m

        # Performance alerts
        - match:
            category: performance
          receiver: 'engineering-team'
          group_wait: 1m
          repeat_interval: 1h

        # Service-specific routing
        - match:
            service: analysis-engine
          receiver: 'backend-team'

        - match:
            service: collaboration
          receiver: 'frontend-team'

        - match:
            service: marketplace
          receiver: 'product-team'

    # Inhibit rules to reduce noise
    inhibit_rules:
      # Inhibit lower severity alerts when critical is firing
      - source_match:
          severity: 'critical'
        target_match:
          severity: 'warning'
        equal: ['alertname', 'service', 'instance']

      # Inhibit instance down alerts when entire service is down
      - source_match:
          alertname: 'ServiceDown'
        target_match:
          alertname: 'InstanceDown'
        equal: ['service']

    # Receivers define how to send notifications
    receivers:
      # Default webhook (for development)
      - name: 'web.hook.default'
        webhook_configs:
          - url: 'https://episteme-webhook-handler-dot-vibe-match-463114.uc.r.appspot.com/webhook'
            send_resolved: true
            http_config:
              bearer_token: '${WEBHOOK_TOKEN}'

      # PagerDuty for critical alerts
      - name: 'pagerduty-critical'
        pagerduty_configs:
          - service_key: '${PAGERDUTY_INTEGRATION_KEY}'
            description: '🚨 CRITICAL: {{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
            details: |
              {{ range .Alerts -}}
              Alert: {{ .Annotations.summary }}
              Description: {{ .Annotations.description }}
              Service: {{ .Labels.service }}
              Instance: {{ .Labels.instance }}
              Severity: {{ .Labels.severity }}
              Started: {{ .StartsAt }}
              {{ end }}
            client: 'Episteme Alertmanager'
            client_url: 'https://episteme-grafana-dot-vibe-match-463114.uc.r.appspot.com'
            links:
              - href: 'https://episteme-grafana-dot-vibe-match-463114.uc.r.appspot.com/d/episteme-overview'
                text: 'Service Overview Dashboard'
        email_configs:
          - to: '<EMAIL>'
            subject: '🚨 CRITICAL: {{ .GroupLabels.alertname }} on {{ .GroupLabels.service }}'
            body: |
              PagerDuty incident created for critical alert.
              
              {{ range .Alerts -}}
              Alert: {{ .Annotations.summary }}
              Description: {{ .Annotations.description }}
              Service: {{ .Labels.service }}
              Instance: {{ .Labels.instance }}
              Severity: {{ .Labels.severity }}
              Started: {{ .StartsAt }}
              {{ end }}

      # High priority alerts
      - name: 'high-priority-alerts'
        email_configs:
          - to: '<EMAIL>'
            subject: '⚠️ HIGH: {{ .GroupLabels.alertname }} on {{ .GroupLabels.service }}'
            body: |
              {{ range .Alerts -}}
              Alert: {{ .Annotations.summary }}
              Description: {{ .Annotations.description }}
              Service: {{ .Labels.service }}
              Instance: {{ .Labels.instance }}
              Severity: {{ .Labels.severity }}
              Started: {{ .StartsAt }}
              
              Dashboard: https://episteme-grafana-dot-vibe-match-463114.uc.r.appspot.com/d/episteme-overview
              {{ end }}
        slack_configs:
          - api_url: '${SLACK_WEBHOOK_URL}'
            channel: '#alerts-high'
            title: '⚠️ High Priority Alert: {{ .GroupLabels.alertname }}'
            text: |
              {{ range .Alerts -}}
              *Service:* {{ .Labels.service }}
              *Alert:* {{ .Annotations.summary }}
              *Description:* {{ .Annotations.description }}
              *Severity:* {{ .Labels.severity }}
              *Dashboard:* <https://episteme-grafana-dot-vibe-match-463114.uc.r.appspot.com/d/episteme-overview|View Dashboard>
              {{ end }}

      # Security team notifications
      - name: 'security-team'
        email_configs:
          - to: '<EMAIL>'
            subject: '🔒 SECURITY: {{ .GroupLabels.alertname }}'
            body: |
              {{ range .Alerts -}}
              Security Alert: {{ .Annotations.summary }}
              Description: {{ .Annotations.description }}
              Service: {{ .Labels.service }}
              Instance: {{ .Labels.instance }}
              Severity: {{ .Labels.severity }}
              Started: {{ .StartsAt }}
              
              This may indicate a security incident. Please investigate immediately.
              Dashboard: https://episteme-grafana-dot-vibe-match-463114.uc.r.appspot.com/d/episteme-security
              {{ end }}
        slack_configs:
          - api_url: '${SLACK_WEBHOOK_URL}'
            channel: '#security-alerts'
            title: '🔒 Security Alert: {{ .GroupLabels.alertname }}'
            color: 'danger'

      # Engineering team for performance issues
      - name: 'engineering-team'
        email_configs:
          - to: '<EMAIL>'
            subject: '📊 PERFORMANCE: {{ .GroupLabels.alertname }}'
        slack_configs:
          - api_url: '${SLACK_WEBHOOK_URL}'
            channel: '#engineering'
            title: '📊 Performance Alert: {{ .GroupLabels.alertname }}'

      # Backend team
      - name: 'backend-team'
        slack_configs:
          - api_url: '${SLACK_WEBHOOK_URL}'
            channel: '#backend-alerts'
            title: '🔧 Backend Alert: {{ .GroupLabels.alertname }}'

      # Frontend team
      - name: 'frontend-team'
        slack_configs:
          - api_url: '${SLACK_WEBHOOK_URL}'
            channel: '#frontend-alerts'
            title: '🎨 Frontend Alert: {{ .GroupLabels.alertname }}'

      # Product team
      - name: 'product-team'
        slack_configs:
          - api_url: '${SLACK_WEBHOOK_URL}'
            channel: '#product-alerts'
            title: '💰 Product Alert: {{ .GroupLabels.alertname }}'

---
apiVersion: v1
kind: Secret
metadata:
  name: alertmanager-secrets
  namespace: default
type: Opaque
stringData:
  pagerduty_key: "CHANGE_ME_IN_PRODUCTION"
  slack_webhook: "CHANGE_ME_IN_PRODUCTION"
  smtp_password: "CHANGE_ME_IN_PRODUCTION"
  webhook_token: "CHANGE_ME_IN_PRODUCTION"

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: alertmanager-storage-pvc
  namespace: default
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: ssd-retain