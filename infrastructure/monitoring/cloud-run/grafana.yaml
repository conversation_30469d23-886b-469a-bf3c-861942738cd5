apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: episteme-grafana
  namespace: default
  labels:
    cloud.googleapis.com/location: us-central1
  annotations:
    run.googleapis.com/ingress: internal-and-cloud-load-balancing
    run.googleapis.com/vpc-access-connector: episteme-connector
    run.googleapis.com/execution-environment: gen2
spec:
  template:
    metadata:
      labels:
        run.googleapis.com/startupProbeType: custom
      annotations:
        autoscaling.knative.dev/minScale: "1"
        autoscaling.knative.dev/maxScale: "5"
        run.googleapis.com/cpu-throttling: "false"
        run.googleapis.com/memory: "2Gi"
        run.googleapis.com/cpu: "1"
        run.googleapis.com/startup-cpu-boost: "true"
    spec:
      containerConcurrency: 100
      timeoutSeconds: 300
      serviceAccountName: <EMAIL>
      containers:
      - name: grafana
        image: grafana/grafana:10.0.0
        ports:
        - name: http1
          containerPort: 3000
        env:
        - name: GF_SECURITY_ADMIN_USER
          valueFrom:
            secretKeyRef:
              name: grafana-secrets
              key: admin_user
        - name: GF_SECURITY_ADMIN_PASSWORD
          valueFrom:
            secretKeyRef:
              name: grafana-secrets
              key: admin_password
        - name: GF_USERS_ALLOW_SIGN_UP
          value: "false"
        - name: GF_SERVER_ROOT_URL
          value: "https://episteme-grafana-dot-vibe-match-463114.uc.r.appspot.com"
        - name: GF_SERVER_SERVE_FROM_SUB_PATH
          value: "true"
        - name: GF_INSTALL_PLUGINS
          value: "redis-datasource,googlesheets-datasource"
        - name: GF_DATABASE_TYPE
          value: "postgres"
        - name: GF_DATABASE_HOST
          valueFrom:
            secretKeyRef:
              name: grafana-secrets
              key: db_host
        - name: GF_DATABASE_NAME
          value: "grafana"
        - name: GF_DATABASE_USER
          valueFrom:
            secretKeyRef:
              name: grafana-secrets
              key: db_user
        - name: GF_DATABASE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: grafana-secrets
              key: db_password
        - name: GF_DATABASE_SSL_MODE
          value: "require"
        - name: GF_SESSION_PROVIDER
          value: "redis"
        - name: GF_SESSION_PROVIDER_CONFIG
          valueFrom:
            secretKeyRef:
              name: grafana-secrets
              key: redis_url
        - name: GF_SECURITY_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: grafana-secrets
              key: secret_key
        - name: GF_AUTH_GOOGLE_ENABLED
          value: "true"
        - name: GF_AUTH_GOOGLE_CLIENT_ID
          valueFrom:
            secretKeyRef:
              name: grafana-secrets
              key: google_client_id
        - name: GF_AUTH_GOOGLE_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              name: grafana-secrets
              key: google_client_secret
        - name: GF_AUTH_GOOGLE_SCOPES
          value: "https://www.googleapis.com/auth/userinfo.profile https://www.googleapis.com/auth/userinfo.email"
        - name: GF_AUTH_GOOGLE_AUTH_URL
          value: "https://accounts.google.com/o/oauth2/auth"
        - name: GF_AUTH_GOOGLE_TOKEN_URL
          value: "https://accounts.google.com/o/oauth2/token"
        - name: GF_AUTH_GOOGLE_ALLOWED_DOMAINS
          value: "episteme.io"
        - name: GF_SMTP_ENABLED
          value: "true"
        - name: GF_SMTP_HOST
          value: "smtp.gmail.com:587"
        - name: GF_SMTP_USER
          valueFrom:
            secretKeyRef:
              name: grafana-secrets
              key: smtp_user
        - name: GF_SMTP_PASSWORD
          valueFrom:
            secretKeyRef:
              name: grafana-secrets
              key: smtp_password
        - name: GF_SMTP_FROM_ADDRESS
          value: "<EMAIL>"
        - name: GF_SMTP_FROM_NAME
          value: "Episteme Grafana"
        volumeMounts:
        - name: grafana-storage
          mountPath: /var/lib/grafana
        - name: grafana-provisioning
          mountPath: /etc/grafana/provisioning
          readOnly: true
        - name: grafana-dashboards
          mountPath: /var/lib/grafana/dashboards
          readOnly: true
        resources:
          limits:
            cpu: "1000m"
            memory: "2Gi"
          requests:
            cpu: "500m"
            memory: "1Gi"
        startupProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 6
        livenessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 5
          failureThreshold: 2
      volumes:
      - name: grafana-storage
        persistentVolumeClaim:
          claimName: grafana-storage-pvc
      - name: grafana-provisioning
        configMap:
          name: grafana-provisioning
      - name: grafana-dashboards
        configMap:
          name: grafana-dashboards

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-provisioning
  namespace: default
data:
  datasources.yml: |
    apiVersion: 1
    datasources:
      - name: Prometheus
        type: prometheus
        access: proxy
        url: http://episteme-prometheus:9090
        isDefault: true
        editable: true
        basicAuth: false
        jsonData:
          timeInterval: 15s
          queryTimeout: 60s
          httpMethod: POST
          
      - name: Jaeger
        type: jaeger
        access: proxy
        url: http://episteme-jaeger:16686
        editable: true
        jsonData:
          tracesToLogs:
            datasourceUid: 'prometheus'
            tags: ['job', 'instance']

      - name: Google Cloud Monitoring
        type: stackdriver
        access: proxy
        jsonData:
          tokenUri: https://oauth2.googleapis.com/token
          clientEmail: <EMAIL>
          defaultProject: vibe-match-463114
          authenticationType: jwt
        secureJsonData:
          privateKey: $GCP_SERVICE_ACCOUNT_KEY

  dashboards.yml: |
    apiVersion: 1
    providers:
      - name: 'episteme-dashboards'
        orgId: 1
        folder: 'Episteme'
        type: file
        disableDeletion: false
        updateIntervalSeconds: 10
        allowUiUpdates: true
        options:
          path: /var/lib/grafana/dashboards

---
apiVersion: v1
kind: Secret
metadata:
  name: grafana-secrets
  namespace: default
type: Opaque
stringData:
  admin_user: "admin"
  admin_password: "CHANGE_ME_IN_PRODUCTION"
  secret_key: "CHANGE_ME_IN_PRODUCTION"
  db_host: "YOUR_POSTGRES_HOST"
  db_user: "grafana"
  db_password: "CHANGE_ME_IN_PRODUCTION"
  redis_url: "redis://YOUR_REDIS_HOST:6379"
  google_client_id: "YOUR_GOOGLE_CLIENT_ID"
  google_client_secret: "YOUR_GOOGLE_CLIENT_SECRET"
  smtp_user: "YOUR_SMTP_USER"
  smtp_password: "YOUR_SMTP_PASSWORD"

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: grafana-storage-pvc
  namespace: default
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 20Gi
  storageClassName: ssd-retain