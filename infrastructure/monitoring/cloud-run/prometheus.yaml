apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: episteme-prometheus
  namespace: default
  labels:
    cloud.googleapis.com/location: us-central1
  annotations:
    run.googleapis.com/ingress: internal
    run.googleapis.com/vpc-access-connector: episteme-connector
    run.googleapis.com/execution-environment: gen2
spec:
  template:
    metadata:
      labels:
        run.googleapis.com/startupProbeType: custom
      annotations:
        autoscaling.knative.dev/minScale: "1"
        autoscaling.knative.dev/maxScale: "3"
        run.googleapis.com/cpu-throttling: "false"
        run.googleapis.com/memory: "4Gi"
        run.googleapis.com/cpu: "2"
        run.googleapis.com/startup-cpu-boost: "true"
    spec:
      containerConcurrency: 1000
      timeoutSeconds: 300
      serviceAccountName: <EMAIL>
      containers:
      - name: prometheus
        image: prom/prometheus:v2.45.0
        ports:
        - name: http1
          containerPort: 9090
        env:
        - name: GOOGLE_CLOUD_PROJECT
          value: "vibe-match-463114"
        - name: PROMETHEUS_STORAGE_PATH
          value: "/prometheus"
        - name: PROMETHEUS_RETENTION_TIME
          value: "30d"
        - name: PROMETHEUS_RETENTION_SIZE
          value: "50GB"
        command:
        - /bin/prometheus
        args:
        - --config.file=/etc/prometheus/prometheus.yml
        - --storage.tsdb.path=/prometheus
        - --web.console.libraries=/usr/share/prometheus/console_libraries
        - --web.console.templates=/usr/share/prometheus/consoles
        - --web.enable-lifecycle
        - --web.enable-admin-api
        - --storage.tsdb.retention.time=$(PROMETHEUS_RETENTION_TIME)
        - --storage.tsdb.retention.size=$(PROMETHEUS_RETENTION_SIZE)
        - --web.external-url=https://episteme-prometheus-dot-vibe-match-463114.uc.r.appspot.com
        - --web.route-prefix=/
        volumeMounts:
        - name: prometheus-config
          mountPath: /etc/prometheus
          readOnly: true
        - name: prometheus-storage
          mountPath: /prometheus
        resources:
          limits:
            cpu: "2000m"
            memory: "4Gi"
          requests:
            cpu: "1000m"
            memory: "2Gi"
        startupProbe:
          httpGet:
            path: /-/ready
            port: 9090
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 6
        livenessProbe:
          httpGet:
            path: /-/healthy
            port: 9090
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /-/ready
            port: 9090
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 5
          failureThreshold: 2
      volumes:
      - name: prometheus-config
        configMap:
          name: prometheus-config
      - name: prometheus-storage
        persistentVolumeClaim:
          claimName: prometheus-storage-pvc

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
  namespace: default
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
      evaluation_interval: 15s
      external_labels:
        cluster: 'episteme-production'
        environment: 'cloud-run'
        region: 'us-central1'

    # Alertmanager configuration
    alerting:
      alertmanagers:
        - static_configs:
            - targets:
              - episteme-alertmanager:9093

    # Rules
    rule_files:
      - "alerts/*.yml"

    # Scrape configurations
    scrape_configs:
      # Self monitoring
      - job_name: 'prometheus'
        static_configs:
          - targets: ['localhost:9090']
        scrape_interval: 30s

      # Google Cloud Monitoring integration
      - job_name: 'cloud-monitoring'
        gce_sd_configs:
          - project: vibe-match-463114
            zone: us-central1-a
            port: 9090
        relabel_configs:
          - source_labels: [__meta_gce_public_ip]
            target_label: __address__
            replacement: ${1}:9090

      # Episteme Services Discovery
      - job_name: 'analysis-engine'
        static_configs:
          - targets: ['analysis-engine-572735000332.us-central1.run.app']
        scrape_interval: 10s
        scrape_timeout: 5s
        metrics_path: /metrics
        scheme: https

      - job_name: 'query-intelligence'
        http_sd_configs:
          - url: 'https://episteme-query-intelligence-dot-vibe-match-463114.uc.r.appspot.com/metrics'
        scrape_interval: 10s
        scrape_timeout: 5s
        metrics_path: /metrics

      - job_name: 'pattern-mining'
        http_sd_configs:
          - url: 'https://episteme-pattern-mining-dot-vibe-match-463114.uc.r.appspot.com/metrics'
        scrape_interval: 10s
        scrape_timeout: 5s
        metrics_path: /metrics

      - job_name: 'collaboration'
        http_sd_configs:
          - url: 'https://episteme-collaboration-dot-vibe-match-463114.uc.r.appspot.com/metrics'
        scrape_interval: 5s  # Higher frequency for real-time service
        scrape_timeout: 3s
        metrics_path: /metrics

      - job_name: 'marketplace'
        http_sd_configs:
          - url: 'https://episteme-marketplace-dot-vibe-match-463114.uc.r.appspot.com/metrics'
        scrape_interval: 10s
        scrape_timeout: 5s
        metrics_path: /metrics

      - job_name: 'web-frontend'
        http_sd_configs:
          - url: 'https://episteme-web-dot-vibe-match-463114.uc.r.appspot.com/api/metrics'
        scrape_interval: 30s
        scrape_timeout: 10s
        metrics_path: /api/metrics

    # Remote write to Google Cloud Monitoring
    remote_write:
      - url: 'https://monitoring.googleapis.com/v1/projects/vibe-match-463114/location/global/prometheus/api/v1/write'
        oauth2:
          client_id: prometheus-client
          client_secret_file: /etc/prometheus/oauth2_secret
          token_url: 'https://oauth2.googleapis.com/token'
          scopes:
            - 'https://www.googleapis.com/auth/monitoring.write'

    # Storage retention
    storage:
      tsdb:
        retention_time: 30d
        retention_size: 50GB

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: prometheus-storage-pvc
  namespace: default
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 100Gi
  storageClassName: ssd-retain