# Analysis Engine Production Cloud Run Configuration
# Optimized for Phase 2 deployment with monitoring integration

apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: analysis-engine-production
  namespace: default
  labels:
    cloud.googleapis.com/location: us-central1
    environment: production
    service: analysis-engine
    version: v2.0.0
  annotations:
    run.googleapis.com/ingress: all
    run.googleapis.com/vpc-access-connector: episteme-connector
    run.googleapis.com/execution-environment: gen2
    run.googleapis.com/binary-authorization: default
spec:
  template:
    metadata:
      labels:
        run.googleapis.com/startupProbeType: custom
        environment: production
        service: analysis-engine
      annotations:
        # Auto-scaling configuration
        autoscaling.knative.dev/minScale: "1"
        autoscaling.knative.dev/maxScale: "100"
        autoscaling.knative.dev/target: "50"
        
        # Performance optimization
        run.googleapis.com/cpu-throttling: "false"
        run.googleapis.com/memory: "4Gi"
        run.googleapis.com/cpu: "2"
        run.googleapis.com/startup-cpu-boost: "true"
        
        # Monitoring integration
        prometheus.io/scrape: "true"
        prometheus.io/port: "8001"
        prometheus.io/path: "/metrics"
        
        # Security annotations
        run.googleapis.com/network-tags: "analysis-engine,production"
    spec:
      containerConcurrency: 1000
      timeoutSeconds: 300
      serviceAccountName: <EMAIL>
      containers:
      - name: analysis-engine
        image: us-central1-docker.pkg.dev/vibe-match-463114/ccl-services/analysis-engine:latest
        ports:
        - name: http1
          containerPort: 8001
        env:
        # Core service configuration
        - name: ENVIRONMENT
          value: "production"
        - name: RUST_LOG
          value: "info"
        - name: GCP_PROJECT_ID
          value: "vibe-match-463114"
        - name: GCP_REGION
          value: "us-central1"
        
        # Performance optimization
        - name: MEMORY_LIMIT_MB
          value: "3584"  # 90% of 4Gi container limit
        - name: CPU_LIMIT_PERCENT
          value: "85"
        - name: MAX_CONCURRENT_ANALYSES
          value: "50"
        - name: ANALYSIS_ENGINE_ADDR
          value: "0.0.0.0:8001"
        
        # Database configuration
        - name: SPANNER_INSTANCE_ID
          value: "episteme-production"
        - name: SPANNER_DATABASE_ID
          value: "episteme"
        
        # Storage configuration
        - name: STORAGE_BUCKET
          value: "episteme-storage-production"
        
        # Redis configuration (for caching and rate limiting)
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: analysis-engine-secrets
              key: redis-url
        
        # JWT configuration
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: analysis-engine-secrets
              key: jwt-secret
        
        # Monitoring configuration
        - name: ENABLE_METRICS
          value: "true"
        - name: METRICS_PORT
          value: "8001"
        - name: ENABLE_RESOURCE_MONITORING
          value: "true"
        - name: MONITORING_INTERVAL_SECONDS
          value: "5"
        
        # Security configuration
        - name: ENABLE_AUTH
          value: "true"
        - name: ENABLE_RATE_LIMITING
          value: "true"
        - name: RATE_LIMIT_PER_HOUR
          value: "10000"
        
        # File processing limits
        - name: MAX_FILE_SIZE_BYTES
          value: "52428800"  # 50MB
        - name: PARSE_TIMEOUT_SECONDS
          value: "30"
        - name: MAX_ANALYSIS_MEMORY_MB
          value: "512"
        
        resources:
          limits:
            cpu: "2000m"
            memory: "4Gi"
          requests:
            cpu: "1000m"
            memory: "2Gi"
        
        # Health and readiness probes
        startupProbe:
          httpGet:
            path: /health
            port: 8001
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 20  # Allow 100 seconds for startup
          successThreshold: 1
        
        livenessProbe:
          httpGet:
            path: /health
            port: 8001
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 5
          failureThreshold: 3
          successThreshold: 1
        
        readinessProbe:
          httpGet:
            path: /health
            port: 8001
          initialDelaySeconds: 5
          periodSeconds: 10
          timeoutSeconds: 3
          failureThreshold: 3
          successThreshold: 1

---
# Secret configuration for Analysis Engine
apiVersion: v1
kind: Secret
metadata:
  name: analysis-engine-secrets
  namespace: default
type: Opaque
stringData:
  redis-url: "redis://episteme-redis:6379"
  jwt-secret: "CHANGE_ME_IN_PRODUCTION_USE_SECRET_MANAGER"