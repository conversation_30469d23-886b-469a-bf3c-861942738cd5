name: Performance Testing

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'services/pattern-mining/**'
  pull_request:
    branches: [ main ]
    paths:
      - 'services/pattern-mining/**'
  schedule:
    # Run nightly performance tests at 2 AM UTC
    - cron: '0 2 * * *'
  workflow_dispatch:
    inputs:
      test_type:
        description: 'Type of performance test'
        required: true
        default: 'quick'
        type: choice
        options:
          - quick
          - comprehensive
          - regression-only
          - establish-baseline
      baseline_name:
        description: 'Baseline name (for regression testing or new baseline)'
        required: false
        type: string

env:
  SERVICE_NAME: pattern-mining
  SERVICE_PATH: services/pattern-mining
  PYTHON_VERSION: '3.11'

jobs:
  setup-service:
    name: Setup Service for Testing
    runs-on: ubuntu-latest
    outputs:
      service-url: ${{ steps.service.outputs.url }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
      
      - name: Install dependencies
        working-directory: ${{ env.SERVICE_PATH }}
        run: |
          pip install -r requirements.txt
          pip install -r requirements-dev.txt
      
      - name: Start Redis for caching
        run: |
          sudo apt-get install -y redis-server
          sudo systemctl start redis-server
          sudo systemctl enable redis-server
      
      - name: Configure test environment
        working-directory: ${{ env.SERVICE_PATH }}
        run: |
          cp .env.example .env
          echo "ENVIRONMENT=ci" >> .env
          echo "REDIS_URL=redis://localhost:6379" >> .env
          echo "SERVICE_VERSION=${{ github.sha }}" >> .env
      
      - name: Start service
        working-directory: ${{ env.SERVICE_PATH }}
        run: |
          python -m uvicorn pattern_mining.api.main:app --host 0.0.0.0 --port 8000 &
          echo $! > service.pid
          sleep 10  # Wait for service to start
      
      - name: Health check
        run: |
          for i in {1..30}; do
            if curl -f http://localhost:8000/health; then
              echo "Service is healthy"
              break
            fi
            echo "Waiting for service... ($i/30)"
            sleep 2
          done
      
      - name: Set service URL
        id: service
        run: echo "url=http://localhost:8000" >> $GITHUB_OUTPUT

  quick-performance-tests:
    name: Quick Performance Tests
    needs: setup-service
    runs-on: ubuntu-latest
    if: github.event.inputs.test_type == 'quick' || github.event.inputs.test_type == '' || github.event_name != 'workflow_dispatch'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
      
      - name: Install test dependencies
        working-directory: ${{ env.SERVICE_PATH }}
        run: |
          pip install -r requirements.txt
          pip install -r requirements-dev.txt
          pip install pytest pytest-asyncio httpx
      
      - name: Run quick performance tests
        working-directory: ${{ env.SERVICE_PATH }}
        run: |
          python -m pytest tests/performance/test_performance_scenarios.py::TestPerformanceScenarios::test_single_user_performance_scenario -v
          python -m pytest tests/performance/test_performance_scenarios.py::TestPerformanceScenarios::test_concurrent_users_scenario -v
          python -m pytest tests/performance/test_load_testing.py::TestLoadTesting::test_baseline_load_10_users -v
        env:
          SERVICE_URL: ${{ needs.setup-service.outputs.service-url }}
      
      - name: Upload quick test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: quick-performance-results
          path: ${{ env.SERVICE_PATH }}/tests/performance/results/

  comprehensive-performance-tests:
    name: Comprehensive Performance Tests
    needs: setup-service
    runs-on: ubuntu-latest
    if: github.event.inputs.test_type == 'comprehensive' || github.event_name == 'schedule'
    timeout-minutes: 60
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
      
      - name: Install test dependencies
        working-directory: ${{ env.SERVICE_PATH }}
        run: |
          pip install -r requirements.txt
          pip install -r requirements-dev.txt
          pip install pytest pytest-asyncio httpx psutil
      
      - name: Run comprehensive performance test suite
        working-directory: ${{ env.SERVICE_PATH }}
        run: |
          python -m pytest tests/performance/ -v -m "performance and not slow" --tb=short
        env:
          SERVICE_URL: ${{ needs.setup-service.outputs.service-url }}
      
      - name: Run load testing
        working-directory: ${{ env.SERVICE_PATH }}
        run: |
          python -m pytest tests/performance/test_load_testing.py -v
        env:
          SERVICE_URL: ${{ needs.setup-service.outputs.service-url }}
      
      - name: Run stress testing
        working-directory: ${{ env.SERVICE_PATH }}
        run: |
          python -m pytest tests/performance/test_stress_testing.py::TestStressTesting::test_breaking_point_identification -v
          python -m pytest tests/performance/test_stress_testing.py::TestStressTesting::test_spike_load_handling -v
        env:
          SERVICE_URL: ${{ needs.setup-service.outputs.service-url }}
      
      - name: Run resource monitoring tests
        working-directory: ${{ env.SERVICE_PATH }}
        run: |
          python -m pytest tests/performance/test_resource_monitoring.py -v
        env:
          SERVICE_URL: ${{ needs.setup-service.outputs.service-url }}
      
      - name: Generate performance report
        working-directory: ${{ env.SERVICE_PATH }}
        run: |
          python scripts/generate_performance_report.py --results-dir tests/performance/results/
        if: always()
      
      - name: Upload comprehensive test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: comprehensive-performance-results
          path: ${{ env.SERVICE_PATH }}/tests/performance/results/

  regression-testing:
    name: Performance Regression Testing
    needs: setup-service
    runs-on: ubuntu-latest
    if: github.event.inputs.test_type == 'regression-only' || github.event_name == 'pull_request'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
      
      - name: Install test dependencies
        working-directory: ${{ env.SERVICE_PATH }}
        run: |
          pip install -r requirements.txt
          pip install -r requirements-dev.txt
          pip install pytest pytest-asyncio httpx
      
      - name: Download baseline from cache
        uses: actions/cache@v3
        with:
          path: ${{ env.SERVICE_PATH }}/tests/performance/baselines/
          key: performance-baselines-${{ github.repository }}-${{ github.ref_name }}
          restore-keys: |
            performance-baselines-${{ github.repository }}-main
            performance-baselines-${{ github.repository }}-
      
      - name: Run regression tests
        working-directory: ${{ env.SERVICE_PATH }}
        run: |
          python -m pytest tests/performance/test_performance_regression.py -v
        env:
          SERVICE_URL: ${{ needs.setup-service.outputs.service-url }}
          BASELINE_NAME: ${{ github.event.inputs.baseline_name || 'main_baseline' }}
      
      - name: Check for performance regression
        working-directory: ${{ env.SERVICE_PATH }}
        run: |
          if [ -f "tests/performance/results/regression_results.json" ]; then
            python -c "
            import json
            with open('tests/performance/results/regression_results.json', 'r') as f:
                results = json.load(f)
            
            if results.get('overall_regression_detected', False):
                print('❌ Performance regression detected!')
                print(f'Severity: {results.get(\"regression_severity\", \"unknown\")}')
                failed_metrics = results.get('failed_metrics', [])
                for metric in failed_metrics:
                    change = results['metric_changes'][metric]
                    print(f'  {metric}: {change[\"percent_change\"]:+.1f}% change')
                exit(1)
            else:
                print('✅ No performance regression detected')
            "
          fi
      
      - name: Upload regression test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: regression-test-results
          path: ${{ env.SERVICE_PATH }}/tests/performance/results/

  establish-baseline:
    name: Establish Performance Baseline
    needs: setup-service
    runs-on: ubuntu-latest
    if: github.event.inputs.test_type == 'establish-baseline'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
      
      - name: Install test dependencies
        working-directory: ${{ env.SERVICE_PATH }}
        run: |
          pip install -r requirements.txt
          pip install -r requirements-dev.txt
          pip install pytest pytest-asyncio httpx
      
      - name: Establish new baseline
        working-directory: ${{ env.SERVICE_PATH }}
        run: |
          python -c "
          import asyncio
          import httpx
          from tests.performance.test_performance_regression import PerformanceRegressionDetector
          
          async def establish_baseline():
              baseline_name = '${{ github.event.inputs.baseline_name }}' or 'auto_baseline_${{ github.sha }}'
              async with httpx.AsyncClient(base_url='${{ needs.setup-service.outputs.service-url }}', timeout=30.0) as client:
                  detector = PerformanceRegressionDetector()
                  baseline = await detector.establish_baseline(client, baseline_name)
                  print(f'✅ Baseline established: {baseline_name}')
                  print(f'   Avg response time: {baseline.avg_response_time_ms:.1f}ms')
                  print(f'   Throughput: {baseline.throughput_rps:.1f} RPS')
                  print(f'   Error rate: {baseline.error_rate_percent:.1f}%')
          
          asyncio.run(establish_baseline())
          "
        env:
          SERVICE_URL: ${{ needs.setup-service.outputs.service-url }}
      
      - name: Save baseline to cache
        uses: actions/cache@v3
        with:
          path: ${{ env.SERVICE_PATH }}/tests/performance/baselines/
          key: performance-baselines-${{ github.repository }}-${{ github.ref_name }}-${{ github.sha }}
      
      - name: Upload baseline
        uses: actions/upload-artifact@v3
        with:
          name: performance-baseline
          path: ${{ env.SERVICE_PATH }}/tests/performance/baselines/

  performance-monitoring:
    name: Performance Monitoring & Alerting
    needs: [quick-performance-tests, comprehensive-performance-tests, regression-testing]
    runs-on: ubuntu-latest
    if: always() && (needs.quick-performance-tests.result != 'skipped' || needs.comprehensive-performance-tests.result != 'skipped' || needs.regression-testing.result != 'skipped')
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Download all test results
        uses: actions/download-artifact@v3
        with:
          path: test-results/
      
      - name: Analyze performance trends
        run: |
          echo "Analyzing performance test results..."
          
          # Check for any critical performance issues
          critical_issues=false
          
          if [ -f "test-results/regression-test-results/regression_results.json" ]; then
            echo "Checking regression test results..."
            python -c "
            import json
            try:
                with open('test-results/regression-test-results/regression_results.json', 'r') as f:
                    results = json.load(f)
                if results.get('overall_regression_detected', False) and results.get('regression_severity') in ['major', 'critical']:
                    print('❌ Critical performance regression detected')
                    exit(1)
            except Exception as e:
                print(f'Could not analyze regression results: {e}')
            "
            if [ $? -eq 1 ]; then
              critical_issues=true
            fi
          fi
          
          if [ "$critical_issues" = true ]; then
            echo "performance_alert=true" >> $GITHUB_ENV
          else
            echo "performance_alert=false" >> $GITHUB_ENV
          fi
      
      - name: Create performance summary
        run: |
          echo "## 📊 Performance Test Summary" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          
          if [ "${{ needs.quick-performance-tests.result }}" != "skipped" ]; then
            echo "### Quick Performance Tests: ${{ needs.quick-performance-tests.result }}" >> $GITHUB_STEP_SUMMARY
          fi
          
          if [ "${{ needs.comprehensive-performance-tests.result }}" != "skipped" ]; then
            echo "### Comprehensive Tests: ${{ needs.comprehensive-performance-tests.result }}" >> $GITHUB_STEP_SUMMARY
          fi
          
          if [ "${{ needs.regression-testing.result }}" != "skipped" ]; then
            echo "### Regression Testing: ${{ needs.regression-testing.result }}" >> $GITHUB_STEP_SUMMARY
          fi
          
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Test Results Available In Artifacts**" >> $GITHUB_STEP_SUMMARY
      
      - name: Performance Alert
        if: env.performance_alert == 'true'
        uses: actions/github-script@v6
        with:
          script: |
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: '⚠️ **Performance Regression Detected** ⚠️\n\nCritical performance issues were found in this PR. Please review the performance test results before merging.\n\n📊 Check the workflow artifacts for detailed analysis.'
            });

  cleanup:
    name: Cleanup Test Environment
    needs: [setup-service, quick-performance-tests, comprehensive-performance-tests, regression-testing, establish-baseline]
    runs-on: ubuntu-latest
    if: always()
    
    steps:
      - name: Stop test services
        run: |
          # Kill any remaining service processes
          pkill -f "uvicorn pattern_mining" || true
          pkill -f "redis-server" || true
          echo "Test environment cleanup completed"