name: Sign and Attest Container Image
description: 'Sign container images with Binary Authorization'

inputs:
  image_url:
    description: 'Full URL of the container image to sign'
    required: true
  project_id:
    description: 'GCP Project ID'
    required: true
  attestor_name:
    description: 'Binary Authorization attestor name'
    required: false
    default: 'episteme-production-attestor'
  key_ring_name:
    description: 'Cloud KMS key ring name'
    required: false
    default: 'episteme-binauthz-keys'
  key_name:
    description: 'Cloud KMS key name'
    required: false
    default: 'signing-key'
  service_account_key:
    description: 'Base64 encoded service account key'
    required: true

runs:
  using: 'composite'
  steps:
    - name: Set up Cloud SDK
      uses: google-github-actions/setup-gcloud@v1
      with:
        service_account_key: ${{ inputs.service_account_key }}
        project_id: ${{ inputs.project_id }}
        export_default_credentials: true

    - name: Configure gcloud for Binary Authorization
      shell: bash
      run: |
        gcloud config set project ${{ inputs.project_id }}
        gcloud components install beta --quiet

    - name: Sign container image
      shell: bash
      env:
        IMAGE_URL: ${{ inputs.image_url }}
        PROJECT_ID: ${{ inputs.project_id }}
        ATTESTOR_NAME: ${{ inputs.attestor_name }}
        KEY_RING_NAME: ${{ inputs.key_ring_name }}
        KEY_NAME: ${{ inputs.key_name }}
      run: |
        echo "Signing image: ${IMAGE_URL}"
        
        # Verify image exists before signing
        if ! gcloud container images describe "${IMAGE_URL}" &> /dev/null; then
          echo "Error: Image ${IMAGE_URL} does not exist or is not accessible"
          exit 1
        fi
        
        # Sign the image with Binary Authorization
        gcloud beta container binauthz attestations sign-and-create \
          --artifact-url="${IMAGE_URL}" \
          --attestor="projects/${PROJECT_ID}/attestors/${ATTESTOR_NAME}" \
          --attestor-project="${PROJECT_ID}" \
          --keyversion-project="${PROJECT_ID}" \
          --keyversion-location="global" \
          --keyversion-keyring="${KEY_RING_NAME}" \
          --keyversion-key="${KEY_NAME}" \
          --keyversion="1"
        
        echo "Image signing completed successfully"

    - name: Verify attestation
      shell: bash
      env:
        IMAGE_URL: ${{ inputs.image_url }}
        PROJECT_ID: ${{ inputs.project_id }}
        ATTESTOR_NAME: ${{ inputs.attestor_name }}
      run: |
        echo "Verifying attestation for image: ${IMAGE_URL}"
        
        # List attestations for the image
        gcloud beta container binauthz attestations list \
          --artifact-url="${IMAGE_URL}" \
          --attestor="projects/${PROJECT_ID}/attestors/${ATTESTOR_NAME}" \
          --format="table(name,createTime)" || echo "No attestations found"
        
        # Test policy evaluation
        if gcloud container binauthz policy evaluate --image-url="${IMAGE_URL}"; then
          echo "✅ Image passes Binary Authorization policy evaluation"
        else
          echo "❌ Image fails Binary Authorization policy evaluation"
          exit 1
        fi