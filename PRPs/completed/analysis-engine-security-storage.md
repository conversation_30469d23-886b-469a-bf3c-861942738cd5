name: "Analysis Engine Security Storage Enhancement PRP v1 - Context Engineering"
description: |
  ## Purpose
  Comprehensive Product Requirements Prompt for enhancing the analysis engine's storage layer 
  with enterprise security controls, encryption, audit logging, and compliance features using 
  the Episteme analysis engine codebase with sufficient context and self-validation capabilities.

  ## Core Principles
  1. **Context is King**: Include ALL necessary documentation, examples, and caveats
  2. **Validation Loops**: Provide executable tests/lints the AI can run and fix
  3. **Information Dense**: Use keywords and patterns from the Episteme codebase
  4. **Progressive Success**: Start simple, validate, then enhance
  5. **Evidence-Based**: Follow Context Engineering standards with official documentation

---

## Goal
Implement comprehensive security enhancements to the analysis engine's storage layer including data encryption at rest, key management, audit logging, access control, and compliance features to meet enterprise security requirements and regulatory standards.

## Why - Business Value
- **Enterprise Market Access**: Enable sales to Fortune 500 companies requiring SOC 2, GDPR compliance
- **Regulatory Compliance**: Meet GDPR, HIPAA, SOC 2 requirements for enterprise customers
- **Risk Mitigation**: Protect sensitive source code and analysis results from data breaches
- **Competitive Advantage**: Differentiate from competitors lacking enterprise security features
- **Trust Building**: Demonstrate security maturity for investor and customer confidence
- **Revenue Growth**: Unlock enterprise pricing tiers requiring security certifications

## What - Technical Requirements
Implementation of zero-trust security architecture for the analysis engine storage layer with:

### Success Criteria
- [ ] All analysis data encrypted at rest with AES-256-GCM
- [ ] Automated key rotation every 90 days via Google Cloud KMS
- [ ] Fine-grained RBAC with resource-level permissions
- [ ] Comprehensive audit logging for all data operations
- [ ] Real-time threat detection and security monitoring
- [ ] GDPR compliance features (data deletion, portability, consent tracking)
- [ ] SOC 2 Type II compliance readiness
- [ ] <5% performance degradation from security enhancements
- [ ] <10ms additional latency for encrypted operations
- [ ] Pass security penetration testing and vulnerability assessment

## All Needed Context

### Documentation & References
```yaml
# MUST READ - Include these in your context window
research_docs:
  - file: research/security/README.md
    why: [OWASP Top 10, vulnerability management, security compliance frameworks]
  - file: research/security/owasp/top-ten-vulnerabilities.md  
    why: [Security vulnerability prevention, secure coding practices]
  - file: research/security/vulnerabilities/cve-nvd-management.md
    why: [Vulnerability tracking, CVSS scoring, risk assessment]
  - file: research/rust/security/memory-safety.md
    why: [Rust security patterns, unsafe code guidelines]
  - file: research/rust/security/authentication-jwt.md
    why: [JWT authentication patterns, token validation]
  - file: research/rust/security/cryptography.md
    why: [Encryption patterns, key management, secure storage]
  - file: research/google-cloud/security/cloud-run-security-overview.md
    why: [Cloud Run security configuration, production deployment]
  - file: research/google-cloud/security/spanner-encryption-cmek.md
    why: [Spanner encryption at rest, customer-managed keys]
  - file: research/google-cloud/security/spanner-audit-logging.md
    why: [Spanner audit logging, compliance monitoring]
  - file: research/google-cloud/spanner/production-patterns.md
    why: [Production Spanner patterns, connection management, transactions]
  - file: research/rust/performance/tokio-async-performance-patterns.md
    why: [Async performance optimization, production deployment]

examples:
  - file: services/analysis-engine/src/storage/spanner.rs
    why: [Existing Spanner storage implementation patterns]
  - file: services/analysis-engine/src/services/security/mod.rs
    why: [Security analysis modules and integration patterns]
  - file: services/analysis-engine/src/api/middleware/auth_layer.rs
    why: [Authentication middleware patterns]
  - file: services/analysis-engine/src/api/middleware/csrf.rs
    why: [CSRF protection implementation]
  - file: services/analysis-engine/src/api/middleware/security.rs
    why: [Security middleware integration]

official_docs:
  - url: https://github.com/googleapis/google-cloud-rust
    section: [KMS client, Secret Manager client, Security Center client]
    critical: [Google Cloud KMS integration for key management, encryption patterns]
  - url: https://cloud.google.com/kms/docs
    section: [Key management, envelope encryption, key rotation]
    critical: [Production key management, automated rotation, HSM integration]
  - url: https://cloud.google.com/spanner/docs/cmek
    section: [Customer-managed encryption keys, data encryption at rest]
    critical: [Spanner CMEK configuration, encryption implementation]
```

### Current Codebase Structure
```bash
# Current security-related structure in analysis-engine
services/analysis-engine/src/
├── api/                    # Axum handlers and middleware
│   └── middleware/         # Security middleware (auth, CSRF, rate limiting)
├── services/               # Business logic services
│   └── security/           # Security analysis modules
│       ├── compliance/     # Compliance checking
│       ├── dependency/     # Dependency vulnerability scanning
│       ├── secrets/        # Secrets detection
│       ├── threat/         # Threat modeling
│       └── vulnerability/  # Vulnerability detection
├── storage/                # Database and cache layers
│   ├── spanner.rs         # Current Spanner implementation
│   ├── redis_client.rs    # Redis caching
│   └── operations.rs      # Storage operations
├── models/                 # Domain models and schemas
│   └── security.rs        # Security-related models
└── metrics/                # Prometheus monitoring
```

### Desired Codebase Changes
```bash
# Files to be added/modified with security enhancements
services/analysis-engine/src/
├── storage/
│   ├── encryption/         # NEW - Encryption module
│   │   ├── mod.rs         # Encryption service interface
│   │   ├── kms_client.rs  # Google Cloud KMS integration
│   │   ├── field_encryption.rs # Field-level encryption
│   │   └── key_rotation.rs # Automated key rotation
│   ├── audit/             # NEW - Audit logging module
│   │   ├── mod.rs         # Audit service interface
│   │   ├── audit_logger.rs # Structured audit logging
│   │   ├── compliance_reporter.rs # Compliance reporting
│   │   └── threat_detector.rs # Security monitoring
│   ├── access_control/    # NEW - Access control module
│   │   ├── mod.rs         # Access control interface
│   │   ├── rbac.rs        # Role-based access control
│   │   ├── permissions.rs # Resource permissions
│   │   └── policy_engine.rs # Authorization policy engine
│   └── spanner.rs         # MODIFIED - Add encryption integration
├── api/middleware/
│   └── security_enhanced.rs # MODIFIED - Enhanced security middleware
└── models/
    └── security_enhanced.rs # NEW - Enhanced security models
```

### Known Gotchas & Library Quirks
```rust
// CRITICAL: Episteme-specific patterns and requirements
// Google Cloud KMS integration patterns from Context7 research
use google_cloud_kms_v1::client::KeyManagementService;
use google_cloud_secretmanager_v1::client::SecretManagerService;

// PATTERN: KMS client initialization with proper error handling
let kms_client = KeyManagementService::new().await
    .map_err(|e| SecurityError::KmsInitError(e))?;

// PATTERN: Envelope encryption for large data (Spanner field encryption)
// Use KMS to encrypt data encryption keys (DEKs), not data directly
// Spanner has 10MB limit per field - envelope encryption is required

// PATTERN: Audit logging must be immutable and tamper-proof
// Use separate audit database or Cloud Logging for compliance
// Never store audit logs in the same database as application data

// GOTCHA: Performance impact from encryption
// Field-level encryption adds 5-15% overhead per operation
// Use connection pooling and async patterns from tokio research

// GOTCHA: Key rotation requires careful coordination
// Cannot rotate keys during active transactions
// Implement graceful key rotation with overlap periods

// SECURITY: Never log sensitive data in audit logs
// Use data classification to determine what to log
// Implement data masking for PII in logs
```

## Implementation Blueprint

### Data Models and Structure
```rust
// Enhanced security models for encrypted storage
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use chrono::{DateTime, Utc};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EncryptedField {
    pub encrypted_data: Vec<u8>,
    pub key_version: String,
    pub encryption_algorithm: String,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AuditRecord {
    pub audit_id: Uuid,
    pub user_id: Option<String>,
    pub operation: String,
    pub resource_type: String,
    pub resource_id: String,
    pub timestamp: DateTime<Utc>,
    pub ip_address: Option<String>,
    pub user_agent: Option<String>,
    pub result: AuditResult,
    pub risk_score: Option<f64>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AuditResult {
    Success,
    Failure(String),
    Unauthorized,
    Forbidden,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityPolicy {
    pub policy_id: Uuid,
    pub resource_type: String,
    pub permissions: Vec<Permission>,
    pub conditions: Vec<PolicyCondition>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}
```

### Task List - Implementation Order
```yaml
Task 1: "Setup KMS integration and encryption module"
  - CREATE src/storage/encryption/mod.rs
  - IMPLEMENT Google Cloud KMS client initialization
  - ADD field-level encryption with envelope encryption pattern
  - PATTERN: Follow Context7 KMS client patterns
  - VALIDATE: Test key creation and encryption/decryption

Task 2: "Implement audit logging infrastructure"
  - CREATE src/storage/audit/mod.rs
  - IMPLEMENT structured audit logging with immutable records
  - ADD threat detection and anomaly monitoring
  - INTEGRATE with existing security modules
  - PATTERN: Follow OWASP logging standards from research

Task 3: "Add fine-grained access control (RBAC)"
  - CREATE src/storage/access_control/mod.rs
  - IMPLEMENT role-based access control with resource permissions
  - ADD policy engine for authorization decisions
  - INTEGRATE with existing JWT authentication middleware
  - PATTERN: Follow existing auth_layer.rs patterns

Task 4: "Enhance Spanner storage with encryption"
  - MODIFY src/storage/spanner.rs
  - ADD encryption/decryption to all sensitive fields
  - IMPLEMENT connection pooling with encrypted connections
  - ADD audit logging to all database operations
  - PATTERN: Follow production-patterns.md from research

Task 5: "Implement automated key rotation"
  - CREATE src/storage/encryption/key_rotation.rs
  - IMPLEMENT scheduled key rotation with zero downtime
  - ADD key version management and migration
  - INTEGRATE with Google Cloud KMS key rotation
  - PATTERN: Follow async patterns from tokio research

Task 6: "Add compliance and reporting features"
  - CREATE src/storage/audit/compliance_reporter.rs
  - IMPLEMENT GDPR compliance (data deletion, portability)
  - ADD SOC 2 compliance reporting and monitoring
  - INTEGRATE with existing metrics system
  - PATTERN: Follow security compliance frameworks from research

Task 7: "Security monitoring and threat detection"
  - CREATE src/storage/audit/threat_detector.rs
  - IMPLEMENT real-time security monitoring
  - ADD anomaly detection for data access patterns
  - INTEGRATE with Google Cloud Security Command Center
  - PATTERN: Follow OWASP security monitoring practices

Task 8: "Performance optimization and validation"
  - OPTIMIZE encryption operations for minimal performance impact
  - IMPLEMENT caching for encrypted data and permissions
  - ADD performance benchmarks for security features
  - VALIDATE <5% performance degradation requirement
  - PATTERN: Follow performance optimization from research
```

### Per-Task Implementation Details
```rust
// Task 1: KMS Integration and Encryption Module
// PATTERN: Context7 KMS client initialization
use google_cloud_kms_v1::client::KeyManagementService;
use anyhow::Result;

pub struct EncryptionService {
    kms_client: KeyManagementService,
    key_ring_name: String,
    crypto_key_name: String,
}

impl EncryptionService {
    pub async fn new(
        project_id: &str,
        location: &str,
        key_ring: &str,
        crypto_key: &str,
    ) -> Result<Self> {
        // PATTERN: Proper error handling and client initialization
        let kms_client = KeyManagementService::new().await
            .context("Failed to initialize KMS client")?;
        
        let key_ring_name = format!(
            "projects/{}/locations/{}/keyRings/{}", 
            project_id, location, key_ring
        );
        let crypto_key_name = format!("{}/cryptoKeys/{}", key_ring_name, crypto_key);
        
        Ok(Self {
            kms_client,
            key_ring_name,
            crypto_key_name,
        })
    }
    
    // PATTERN: Envelope encryption for large data
    pub async fn encrypt_field(&self, plaintext: &[u8]) -> Result<EncryptedField> {
        // Generate data encryption key (DEK)
        let dek = self.generate_dek().await?;
        
        // Encrypt data with DEK using AES-256-GCM
        let encrypted_data = self.encrypt_with_dek(plaintext, &dek)?;
        
        // Encrypt DEK with KMS
        let encrypted_dek = self.encrypt_dek_with_kms(&dek).await?;
        
        Ok(EncryptedField {
            encrypted_data: [encrypted_dek, encrypted_data].concat(),
            key_version: self.get_current_key_version().await?,
            encryption_algorithm: "AES-256-GCM".to_string(),
            created_at: Utc::now(),
        })
    }
}

// Task 2: Audit Logging Infrastructure
pub struct AuditLogger {
    storage: Arc<dyn AuditStorage>,
    threat_detector: Arc<ThreatDetector>,
}

impl AuditLogger {
    pub async fn log_operation(
        &self,
        user_id: Option<&str>,
        operation: &str,
        resource_type: &str,
        resource_id: &str,
        result: AuditResult,
    ) -> Result<()> {
        let audit_record = AuditRecord {
            audit_id: Uuid::new_v4(),
            user_id: user_id.map(|s| s.to_string()),
            operation: operation.to_string(),
            resource_type: resource_type.to_string(),
            resource_id: resource_id.to_string(),
            timestamp: Utc::now(),
            ip_address: None, // Extract from request context
            user_agent: None, // Extract from request context
            result,
            risk_score: None,
        };
        
        // PATTERN: Immutable audit storage
        self.storage.store_audit_record(&audit_record).await?;
        
        // PATTERN: Real-time threat detection
        self.threat_detector.analyze_audit_record(&audit_record).await?;
        
        Ok(())
    }
}
```

### Integration Points
```yaml
ENCRYPTION:
  - kms_integration: "Add KMS client to storage/encryption/kms_client.rs"
  - field_encryption: "Implement field-level encryption for sensitive data"
  - key_management: "Automated key rotation with zero downtime"

AUDIT_LOGGING:
  - immutable_storage: "Use separate audit database with Cloud SQL"
  - structured_logging: "JSON format with standardized fields"
  - real_time_monitoring: "Stream audit events to Security Command Center"

ACCESS_CONTROL:
  - rbac_integration: "Add to existing JWT middleware in api/middleware/"
  - resource_permissions: "Fine-grained permissions at file/project level"
  - policy_engine: "Centralized authorization decisions"

SPANNER_INTEGRATION:
  - encrypted_fields: "Encrypt sensitive fields in analysis results"
  - connection_security: "TLS 1.3 for all database connections"
  - transaction_auditing: "Log all database transactions with context"

MONITORING:
  - security_metrics: "Add security metrics to metrics/prometheus.rs"
  - threat_detection: "Real-time anomaly detection and alerting"
  - compliance_reporting: "Automated compliance dashboards"
```

## Validation Loop

### Level 1: Syntax & Style
```bash
# Run these FIRST - fix any errors before proceeding
cargo fmt                           # Format code
cargo clippy -- -D warnings        # Lint with warnings as errors
cargo check                         # Type checking

# Expected: No errors. If errors exist, READ and fix before continuing.
```

### Level 2: Unit Tests
```rust
// CREATE comprehensive test suite for security features
#[cfg(test)]
mod tests {
    use super::*;
    use tokio_test;
    
    #[tokio::test]
    async fn test_field_encryption_roundtrip() {
        // Test encryption and decryption of sensitive data
        let encryption_service = create_test_encryption_service().await;
        let plaintext = b"sensitive source code content";
        
        let encrypted = encryption_service.encrypt_field(plaintext).await.unwrap();
        let decrypted = encryption_service.decrypt_field(&encrypted).await.unwrap();
        
        assert_eq!(plaintext, decrypted.as_slice());
    }
    
    #[tokio::test]
    async fn test_audit_logging_immutability() {
        // Test that audit records cannot be modified after creation
        let audit_logger = create_test_audit_logger().await;
        
        let record_id = audit_logger.log_operation(
            Some("user123"),
            "READ",
            "file_analysis",
            "analysis_123",
            AuditResult::Success
        ).await.unwrap();
        
        // Verify record exists and cannot be modified
        let record = audit_logger.get_audit_record(&record_id).await.unwrap();
        assert_eq!(record.operation, "READ");
        
        // Attempt to modify should fail
        let modify_result = audit_logger.modify_audit_record(&record_id, "WRITE").await;
        assert!(modify_result.is_err());
    }
    
    #[tokio::test]
    async fn test_rbac_permission_enforcement() {
        // Test that RBAC correctly enforces permissions
        let access_control = create_test_access_control().await;
        
        // User with read permission should be able to read
        let read_result = access_control.check_permission(
            "user123",
            "READ",
            "file_analysis",
            "analysis_123"
        ).await.unwrap();
        assert!(read_result);
        
        // User without write permission should be denied
        let write_result = access_control.check_permission(
            "user123",
            "WRITE",
            "file_analysis",
            "analysis_123"
        ).await.unwrap();
        assert!(!write_result);
    }
    
    #[tokio::test]
    async fn test_key_rotation_zero_downtime() {
        // Test that key rotation doesn't interrupt service
        let encryption_service = create_test_encryption_service().await;
        
        // Encrypt data with current key
        let plaintext = b"test data";
        let encrypted_old = encryption_service.encrypt_field(plaintext).await.unwrap();
        
        // Rotate key
        encryption_service.rotate_key().await.unwrap();
        
        // Should still be able to decrypt old data
        let decrypted_old = encryption_service.decrypt_field(&encrypted_old).await.unwrap();
        assert_eq!(plaintext, decrypted_old.as_slice());
        
        // New encryptions should use new key
        let encrypted_new = encryption_service.encrypt_field(plaintext).await.unwrap();
        assert_ne!(encrypted_old.key_version, encrypted_new.key_version);
    }
}
```

```bash
# Run and iterate until passing
cargo test security -- --nocapture
cargo test encryption -- --nocapture
cargo test audit -- --nocapture
# If failing: Read error, understand root cause, fix code, re-run
```

### Level 3: Integration Test
```bash
# Start the service with security features enabled
GOOGLE_CLOUD_PROJECT=test-project \
GOOGLE_CLOUD_KMS_KEY_RING=test-ring \
GOOGLE_CLOUD_KMS_CRYPTO_KEY=test-key \
cargo run --bin analysis-engine

# Test encryption endpoints
curl -X POST http://localhost:8001/api/v1/analyze \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer test-token" \
  -d '{"source_code": "sensitive code", "enable_encryption": true}'

# Verify audit logging
curl -X GET http://localhost:8001/api/v1/audit/logs \
  -H "Authorization: Bearer admin-token"

# Test permission enforcement
curl -X GET http://localhost:8001/api/v1/analysis/protected \
  -H "Authorization: Bearer limited-token"
# Expected: 403 Forbidden for insufficient permissions

# Test compliance endpoints
curl -X POST http://localhost:8001/api/v1/compliance/gdpr/delete \
  -H "Authorization: Bearer admin-token" \
  -d '{"user_id": "user123"}'
```

### Level 4: Security Testing
```bash
# Run security vulnerability scans
cargo audit

# Test with security scanner
docker run --rm -v $(pwd):/app \
  securecodewarrior/docker-security-scanning:latest \
  /app/services/analysis-engine

# Performance testing with encryption enabled
cargo bench --features encryption

# Penetration testing simulation
python scripts/security/simulate_attacks.py \
  --target http://localhost:8001 \
  --auth-token test-token
```

### Level 5: Performance & Resource Validation
```bash
# Validate performance requirements (<5% degradation)
cargo run --bin performance_validator \
  --enable-encryption \
  --baseline-file performance_baseline.json

# Memory usage validation
valgrind --tool=memcheck \
  ./target/release/analysis-engine

# Load testing with encryption
artillery run load-test-config.yml

# Expected: <5% performance degradation, <10ms additional latency
```

## Final Validation Checklist
- [ ] All tests pass: `cargo test`
- [ ] No linting errors: `cargo clippy -- -D warnings`
- [ ] Security audit passes: `cargo audit`
- [ ] Encryption/decryption working: Integration tests pass
- [ ] Audit logging immutable: Cannot modify audit records
- [ ] RBAC enforced: Permission checks working correctly
- [ ] Key rotation functional: Zero downtime key rotation
- [ ] Performance requirements met: <5% degradation
- [ ] GDPR compliance: Data deletion and portability working
- [ ] SOC 2 readiness: Audit trails and access controls
- [ ] Penetration testing passed: No critical vulnerabilities

---

## Anti-Patterns to Avoid
- ❌ Don't store encryption keys in the application code or configuration files
- ❌ Don't use the same key for all data - implement proper key hierarchy
- ❌ Don't log sensitive data in audit logs - implement data masking
- ❌ Don't ignore key rotation - implement automated key lifecycle management
- ❌ Don't skip audit logging for any data access - comprehensive logging required
- ❌ Don't implement custom encryption - use proven KMS and encryption libraries
- ❌ Don't store audit logs in the same database as application data
- ❌ Don't bypass access control checks - every operation must be authorized
- ❌ Don't ignore performance impact - validate all security enhancements
- ❌ Don't hardcode security policies - make them configurable and auditable

## Security Implementation Patterns
```rust
// PATTERN: Secure error handling - don't leak sensitive information
pub enum SecurityError {
    #[error("Authentication failed")]
    AuthenticationFailed,
    #[error("Authorization denied")]
    AuthorizationDenied,
    #[error("Encryption operation failed")]
    EncryptionFailed,
    // Don't include sensitive details in error messages
}

// PATTERN: Secure data structures - zero on drop
use zeroize::{Zeroize, ZeroizeOnDrop};

#[derive(ZeroizeOnDrop)]
pub struct SensitiveData {
    data: Vec<u8>,
}

// PATTERN: Secure configuration - environment variables only
pub struct SecurityConfig {
    pub kms_project_id: String,
    pub kms_location: String,
    pub kms_key_ring: String,
    pub kms_crypto_key: String,
}

impl SecurityConfig {
    pub fn from_env() -> Result<Self> {
        Ok(Self {
            kms_project_id: std::env::var("GOOGLE_CLOUD_PROJECT")?,
            kms_location: std::env::var("GOOGLE_CLOUD_KMS_LOCATION")?,
            kms_key_ring: std::env::var("GOOGLE_CLOUD_KMS_KEY_RING")?,
            kms_crypto_key: std::env::var("GOOGLE_CLOUD_KMS_CRYPTO_KEY")?,
        })
    }
}
```