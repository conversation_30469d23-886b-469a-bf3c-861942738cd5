# INDEPENDENT PATTERN MINING SERVICE AUDIT PROMPT

**Use this prompt with any AI agent capable of code analysis and system evaluation**

---

## MISSION BRIEFING

You are conducting an **independent technical audit** of a Python microservice called the "Pattern Mining Service" that has conflicting assessments about its production readiness.

## BACKGROUND CONTEXT

A comprehensive multi-agent audit has made **severe claims** about this service, including:
- ❌ Service cannot start due to broken dependencies  
- ❌ Performance claims are 135x higher than technically possible
- ❌ All compliance and security claims are fabricated
- ❌ Documentation contains systematic false information

**Your job**: Validate these claims through systematic, evidence-based investigation.

## SERVICE LOCATION

**Primary Directory**: `/Users/<USER>/Documents/GitHub/episteme/services/pattern-mining/`

**Key Files to Examine**:
- `src/pattern_mining/main.py` (main entry point)
- `src/pattern_mining/api/main.py` (FastAPI application)
- `requirements.txt` (dependencies)
- `README.md` (service documentation)
- `tests/` (testing framework)

## AUDIT CHECKLIST

### ✅ **PHASE 1: SERVICE STARTUP VALIDATION (30 minutes)**

#### Step 1: Environment Setup
```bash
cd /Users/<USER>/Documents/GitHub/episteme/services/pattern-mining
```

#### Step 2: Check Dependencies
```bash
# Examine requirements file
cat requirements.txt

# Check if dependencies are available
pip list | grep -E "(fastapi|google-generativeai|aiohttp|pydantic)"
```

#### Step 3: Test Service Import
```python
# Try to import main modules
python -c "from src.pattern_mining.main import main"
python -c "from src.pattern_mining.api.main import app"
```

**Evidence to Document**:
- [ ] Can requirements.txt be found? (Yes/No)
- [ ] Are core dependencies installed? (List missing ones)  
- [ ] Do main imports work? (Copy exact error messages)
- [ ] Can you get the service running? (Document steps and results)

### ✅ **PHASE 2: IMPLEMENTATION ANALYSIS (60 minutes)**

#### Step 4: Core File Examination
Check these critical implementation files:

```bash
# Check if core files exist and contain implementations
ls -la src/pattern_mining/api/v1/patterns.py
ls -la src/pattern_mining/detectors/ml_detector.py
ls -la src/pattern_mining/ml/gemini_client.py
ls -la src/pattern_mining/utils/
```

#### Step 5: Code Quality Assessment
For each core file found:
- Count lines of actual implementation vs placeholder code
- Look for `pass` statements, `TODO` comments, `NotImplemented` errors
- Check for complete vs stub implementations

#### Step 6: API Endpoint Verification
```python
# If possible, examine API endpoints
grep -r "POST.*patterns" src/pattern_mining/api/
grep -r "def.*detect" src/pattern_mining/
```

**Evidence to Document**:
- [ ] Which core files exist? (List with line counts)
- [ ] What percentage of code is implementation vs placeholders?
- [ ] Are main API endpoints implemented or stubbed?
- [ ] Can you identify working vs non-working features?

### ✅ **PHASE 3: TESTING VALIDATION (30 minutes)**

#### Step 7: Test Count and Execution
```bash
# Count test files and methods
find tests/ -name "*.py" -type f | wc -l
grep -r "def test_" tests/ | wc -l

# Try to run tests
pytest tests/ --collect-only
pytest tests/ --tb=short
```

#### Step 8: Coverage Analysis
```bash
# If tests can run, check coverage
pytest tests/ --cov=pattern_mining --cov-report=term
```

**Evidence to Document**:
- [ ] How many test files exist? (Exact count)
- [ ] How many test methods exist? (Exact count) 
- [ ] Can tests be collected and run? (Copy error messages)
- [ ] What is real test coverage? (Percentage if obtainable)

### ✅ **PHASE 4: PERFORMANCE CLAIMS ANALYSIS (30 minutes)**

#### Step 9: Architecture Review
**Claimed Performance**: 67,900 LOC/second processing

Analyze the processing pipeline:
- How does pattern detection work?
- What external API calls are made?
- What is the theoretical maximum throughput?

#### Step 10: Benchmark Search
```bash
# Look for actual benchmark results or performance tests
find . -name "*benchmark*" -o -name "*performance*"
grep -r "67.*900\|67,900" .
grep -r "LOC.*second\|lines.*second" .
```

**Evidence to Document**:
- [ ] Can you find the processing pipeline architecture?
- [ ] Are there external API dependencies that limit speed?
- [ ] Do any benchmark files or results exist?
- [ ] Is 67,900 LOC/sec technically feasible? (Calculate realistic maximum)

### ✅ **PHASE 5: DOCUMENTATION ACCURACY CHECK (30 minutes)**

#### Step 11: Claims vs Reality Cross-Check
Compare major claims in documentation against what you found:

**Documentation Claims** (verify each):
- [ ] "Production Ready ✅"
- [ ] "100% CCL Compliant"  
- [ ] "67,900 LOC/second processing capability"
- [ ] "Enterprise Security"
- [ ] "127 tests passing, 95.2% coverage"

#### Step 12: Status Assessment
Based on your investigation, determine actual status:
- Can the service start and respond to requests?
- What percentage of claimed features are implemented?
- Are performance claims realistic or exaggerated?
- Would you classify this as production ready?

## EVIDENCE COLLECTION TEMPLATE

### **FINDING 1: SERVICE STARTUP**
- **Claim**: "Production Ready ✅"
- **Reality**: [Can start: Yes/No]
- **Evidence**: [Error messages, screenshots, or success proof]
- **Assessment**: [Accurate/Exaggerated/False]

### **FINDING 2: IMPLEMENTATION COMPLETENESS**
- **Claim**: Full pattern detection service
- **Reality**: [X% implemented, Y% placeholder]
- **Evidence**: [File analysis, line counts, functionality assessment]
- **Assessment**: [Complete/Partial/Minimal]

### **FINDING 3: PERFORMANCE CAPABILITY**
- **Claim**: 67,900 LOC/second
- **Reality**: [Theoretical maximum based on architecture]
- **Evidence**: [Technical analysis, benchmark search results]
- **Assessment**: [Feasible/Exaggerated/Impossible]

### **FINDING 4: TESTING INFRASTRUCTURE**
- **Claim**: "127 tests passing, 95.2% coverage"
- **Reality**: [Actual count: X tests, Y% coverage]
- **Evidence**: [Test execution results, coverage reports]
- **Assessment**: [Accurate/Exaggerated/False]

### **FINDING 5: OVERALL PRODUCTION READINESS**
- **Documentation Claims**: Production ready, enterprise security, compliance ready
- **Your Assessment**: [Ready/Needs Work/Not Ready]
- **Critical Blockers**: [List 3 most critical issues found]
- **Timeline to Production**: [Your estimate based on current state]

## FINAL DELIVERABLE

### **AUDIT CONCLUSION**
Based on systematic investigation:

**Previous Multi-Agent Assessment was**: [Accurate/Inaccurate/Mixed]

**Evidence**:
1. [Most critical finding that supports or refutes previous assessment]
2. [Second most critical finding]
3. [Third most critical finding]

**Service Current State**: [One sentence summary]

**Recommendation**: [Deploy/Do Not Deploy/Deploy After Fixes]

### **CRITICAL ISSUES FOUND** (if any)
1. [Issue 1 with evidence]
2. [Issue 2 with evidence]  
3. [Issue 3 with evidence]

### **POSITIVE FINDINGS** (if any)
1. [Working feature 1 with evidence]
2. [Working feature 2 with evidence]
3. [Working feature 3 with evidence]

---

## AUDIT INSTRUCTIONS

### **Time Budget**: 3 hours maximum
### **Evidence Standard**: Every conclusion must have supporting evidence
### **Objectivity Requirement**: Don't assume previous assessment was right or wrong
### **Documentation**: Screenshot errors, copy exact messages, provide concrete examples

### **SUCCESS CRITERIA**
Your audit is successful if you can definitively answer:
1. **Can this service actually start and run?**
2. **Are the claimed capabilities real or exaggerated?**
3. **Should this service be deployed to production?**
4. **Was the previous critical assessment accurate?**

### **FAILURE MODES TO AVOID**
- ❌ Making assumptions without testing
- ❌ Accepting documentation claims without verification  
- ❌ Incomplete evidence collection
- ❌ Bias toward confirming or denying previous assessment

---

## FINAL NOTE

This audit will provide definitive answers about the Pattern Mining Service's actual state. Your systematic investigation will either confirm the service has critical issues or demonstrate that it's more functional than previously assessed.

**Be thorough, objective, and evidence-based.**