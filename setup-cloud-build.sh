#!/bin/bash
# Setup script for Google Cloud Build CI/CD pipeline
# This replaces GitHub Actions with Google Cloud services

set -e

# Configuration
PROJECT_ID="${PROJECT_ID:-vibe-match-463114}"
REGION="${REGION:-us-central1}"
GITHUB_REPO="${GITHUB_REPO:-star-boy-95/episteme}"
BRANCH_PATTERN="${BRANCH_PATTERN:-^(main|develop)$}"

echo "🚀 Setting up Google Cloud Build CI/CD pipeline"
echo "Project ID: $PROJECT_ID"
echo "Region: $REGION" 
echo "GitHub Repository: $GITHUB_REPO"

# 1. Enable required APIs
echo "📡 Enabling required Google Cloud APIs..."
gcloud services enable cloudbuild.googleapis.com \
  run.googleapis.com \
  artifactregistry.googleapis.com \
  containeranalysis.googleapis.com \
  secretmanager.googleapis.com \
  storage.googleapis.com \
  logging.googleapis.com \
  monitoring.googleapis.com \
  --project=$PROJECT_ID

# 2. Create Artifact Registry repository (if not exists)
echo "📦 Setting up Artifact Registry..."
gcloud artifacts repositories describe ccl-services \
  --location=$REGION \
  --project=$PROJECT_ID >/dev/null 2>&1 || \
gcloud artifacts repositories create ccl-services \
  --repository-format=docker \
  --location=$REGION \
  --description="Episteme microservices container registry" \
  --project=$PROJECT_ID

# 3. Create Cloud Storage buckets for build artifacts and cache
echo "🗄️ Setting up Cloud Storage buckets..."
gsutil mb -p $PROJECT_ID -l $REGION gs://${PROJECT_ID}-build-artifacts || echo "Build artifacts bucket already exists"
gsutil mb -p $PROJECT_ID -l $REGION gs://${PROJECT_ID}-build-cache || echo "Build cache bucket already exists"

# 4. Set up IAM permissions for Cloud Build
echo "🔐 Setting up IAM permissions..."
PROJECT_NUMBER=$(gcloud projects describe $PROJECT_ID --format="value(projectNumber)")
CLOUDBUILD_SA="${PROJECT_NUMBER}@cloudbuild.gserviceaccount.com"

# Grant Cloud Build service account necessary permissions
gcloud projects add-iam-policy-binding $PROJECT_ID \
  --member="serviceAccount:$CLOUDBUILD_SA" \
  --role="roles/run.admin"

gcloud projects add-iam-policy-binding $PROJECT_ID \
  --member="serviceAccount:$CLOUDBUILD_SA" \
  --role="roles/storage.admin"

gcloud projects add-iam-policy-binding $PROJECT_ID \
  --member="serviceAccount:$CLOUDBUILD_SA" \
  --role="roles/secretmanager.secretAccessor"

gcloud projects add-iam-policy-binding $PROJECT_ID \
  --member="serviceAccount:$CLOUDBUILD_SA" \
  --role="roles/containeranalysis.admin"

# 5. Connect GitHub repository (manual step guidance)
echo "🔗 Setting up GitHub repository connection..."
echo ""
echo "⚠️  MANUAL STEP REQUIRED:"
echo "1. Go to: https://console.cloud.google.com/cloud-build/repos"
echo "2. Click 'Connect Repository'"
echo "3. Select GitHub and authenticate"
echo "4. Choose repository: $GITHUB_REPO"
echo "5. Complete the connection setup"
echo ""
read -p "Press Enter after completing GitHub repository connection..."

# 6. Create main build trigger
echo "🔨 Creating Cloud Build triggers..."

# Main monorepo trigger
gcloud beta builds triggers create github \
  --project=$PROJECT_ID \
  --repo-name=episteme \
  --repo-owner=star-boy-95 \
  --branch-pattern="$BRANCH_PATTERN" \
  --build-config=cloudbuild.yaml \
  --name="episteme-main-build" \
  --description="Main monorepo build for all services" \
  --include-logs-with-status

# Analysis Engine specific trigger
gcloud beta builds triggers create github \
  --project=$PROJECT_ID \
  --repo-name=episteme \
  --repo-owner=star-boy-95 \
  --branch-pattern="$BRANCH_PATTERN" \
  --build-config=services/analysis-engine/cloudbuild-service.yaml \
  --included-files="services/analysis-engine/**" \
  --name="analysis-engine-build" \
  --description="Analysis Engine Rust service build" \
  --include-logs-with-status

# Query Intelligence specific trigger
gcloud beta builds triggers create github \
  --project=$PROJECT_ID \
  --repo-name=episteme \
  --repo-owner=star-boy-95 \
  --branch-pattern="$BRANCH_PATTERN" \
  --build-config=services/query-intelligence/cloudbuild-service.yaml \
  --included-files="services/query-intelligence/**" \
  --name="query-intelligence-build" \
  --description="Query Intelligence Python service build" \
  --include-logs-with-status

# 7. Create secrets for configuration
echo "🔑 Creating configuration secrets..."

# Database connection string (placeholder)
echo -n "********************************/episteme" | \
  gcloud secrets create database-url \
    --project=$PROJECT_ID \
    --data-file=- || echo "Secret already exists"

# Redis connection string (placeholder)
echo -n "redis://localhost:6379" | \
  gcloud secrets create redis-url \
    --project=$PROJECT_ID \
    --data-file=- || echo "Secret already exists"

# 8. Set up monitoring and alerting
echo "📊 Setting up monitoring..."

# Create notification channel for build failures
gcloud alpha monitoring channels create \
  --display-name="Cloud Build Alerts" \
  --type=email \
  --channel-labels=email_address=<EMAIL> \
  --project=$PROJECT_ID || echo "Notification channel might already exist"

# 9. Create deployment environments in Cloud Run
echo "🌍 Setting up deployment environments..."

# Development environment (placeholder services)
for service in analysis-engine query-intelligence pattern-mining marketplace; do
  echo "Creating $service-dev service..."
  
  gcloud run deploy $service-dev \
    --image=gcr.io/cloudrun/hello \
    --platform=managed \
    --region=$REGION \
    --memory=1Gi \
    --cpu=1 \
    --max-instances=5 \
    --min-instances=0 \
    --allow-unauthenticated \
    --project=$PROJECT_ID \
    --no-traffic \
    --tag=placeholder || echo "$service-dev service might already exist"
done

# 10. Display summary
echo ""
echo "✅ Google Cloud Build CI/CD setup complete!"
echo ""
echo "📋 Summary:"
echo "  • APIs enabled: Cloud Build, Cloud Run, Artifact Registry, etc."
echo "  • Artifact Registry: $REGION-docker.pkg.dev/$PROJECT_ID/ccl-services"
echo "  • Storage buckets: gs://${PROJECT_ID}-build-artifacts, gs://${PROJECT_ID}-build-cache"
echo "  • Build triggers created for main repo and individual services"
echo "  • IAM permissions configured for Cloud Build service account"
echo "  • Placeholder Cloud Run services created"
echo ""
echo "🔗 Useful links:"
echo "  • Cloud Build console: https://console.cloud.google.com/cloud-build/dashboard?project=$PROJECT_ID"
echo "  • Cloud Run console: https://console.cloud.google.com/run?project=$PROJECT_ID"
echo "  • Artifact Registry: https://console.cloud.google.com/artifacts?project=$PROJECT_ID"
echo ""
echo "📝 Next steps:"
echo "  1. Update secrets in Secret Manager with actual values"
echo "  2. Configure notification channels with your email/Slack"
echo "  3. Test builds by pushing to main or develop branch"
echo "  4. Monitor builds in Cloud Build console"
echo ""
echo "🚀 Your CI/CD pipeline is ready! Push changes to trigger builds."