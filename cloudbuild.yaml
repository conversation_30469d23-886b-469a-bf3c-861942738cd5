# Main Cloud Build configuration for multi-service monorepo
# Replaces GitHub Actions workflows with Google Cloud Build
steps:
  # 1. Detect changed services
  - name: 'gcr.io/cloud-builders/git'
    id: 'detect-changes'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        set -e
        echo "Detecting changed services..."
        
        # Get list of changed files
        if [ "$_TRIGGER_TYPE" = "push" ]; then
          CHANGED_FILES=$(git diff --name-only HEAD~1 HEAD)
        else
          CHANGED_FILES=$(git diff --name-only origin/main HEAD)
        fi
        
        echo "Changed files:"
        echo "$CHANGED_FILES"
        
        # Detect which services need building
        SERVICES=""
        if echo "$CHANGED_FILES" | grep -q "services/analysis-engine/"; then
          SERVICES="$SERVICES analysis-engine"
        fi
        if echo "$CHANGED_FILES" | grep -q "services/query-intelligence/"; then
          SERVICES="$SERVICES query-intelligence"  
        fi
        if echo "$CHANGED_FILES" | grep -q "services/pattern-mining/"; then
          SERVICES="$SERVICES pattern-mining"
        fi
        if echo "$CHANGED_FILES" | grep -q "services/marketplace/"; then
          SERVICES="$SERVICES marketplace"
        fi
        
        # If no services changed, build all (fallback)
        if [ -z "$SERVICES" ]; then
          SERVICES="analysis-engine query-intelligence pattern-mining marketplace"
        fi
        
        echo "Services to build: $SERVICES"
        echo "$SERVICES" > /workspace/services_to_build.txt

  # 2. Run parallel builds for each service
  - name: 'gcr.io/cloud-builders/docker'
    id: 'build-analysis-engine'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        if grep -q "analysis-engine" /workspace/services_to_build.txt; then
          echo "Building Analysis Engine..."
          cd services/analysis-engine
          
          # Build and test with Docker
          docker build -t temp-analysis-engine:test .
          
          # Run tests inside container
          docker run --rm temp-analysis-engine:test cargo test --all-features
          
          # Build production image
          docker build -t $_IMAGE_REGISTRY/analysis-engine:$BUILD_ID .
          docker build -t $_IMAGE_REGISTRY/analysis-engine:latest .
          
          echo "Analysis Engine build complete"
        else
          echo "Skipping Analysis Engine - no changes detected"
        fi
    waitFor: ['detect-changes']

  - name: 'gcr.io/cloud-builders/docker' 
    id: 'build-query-intelligence'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        if grep -q "query-intelligence" /workspace/services_to_build.txt; then
          echo "Building Query Intelligence..."
          cd services/query-intelligence
          
          # Build and test Python service
          docker build -t temp-query-intelligence:test .
          
          # Run tests inside container  
          docker run --rm temp-query-intelligence:test python -m pytest tests/ -v
          
          # Build production image
          docker build -t $_IMAGE_REGISTRY/query-intelligence:$BUILD_ID .
          docker build -t $_IMAGE_REGISTRY/query-intelligence:latest .
          
          echo "Query Intelligence build complete"
        else
          echo "Skipping Query Intelligence - no changes detected"
        fi
    waitFor: ['detect-changes']

  - name: 'gcr.io/cloud-builders/docker'
    id: 'build-pattern-mining'
    entrypoint: 'bash' 
    args:
      - '-c'
      - |
        if grep -q "pattern-mining" /workspace/services_to_build.txt; then
          echo "Building Pattern Mining..."
          cd services/pattern-mining
          
          # Build and test Python service
          docker build -t temp-pattern-mining:test .
          
          # Run tests inside container
          docker run --rm temp-pattern-mining:test python -m pytest tests/ -v
          
          # Build production image
          docker build -t $_IMAGE_REGISTRY/pattern-mining:$BUILD_ID .
          docker build -t $_IMAGE_REGISTRY/pattern-mining:latest .
          
          echo "Pattern Mining build complete"
        else
          echo "Skipping Pattern Mining - no changes detected"
        fi
    waitFor: ['detect-changes']

  - name: 'gcr.io/cloud-builders/docker'
    id: 'build-marketplace'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        if grep -q "marketplace" /workspace/services_to_build.txt; then
          echo "Building Marketplace..."
          cd services/marketplace
          
          # Build and test Go service
          docker build -t temp-marketplace:test .
          
          # Run tests inside container
          docker run --rm temp-marketplace:test go test ./... -v
          
          # Build production image
          docker build -t $_IMAGE_REGISTRY/marketplace:$BUILD_ID .
          docker build -t $_IMAGE_REGISTRY/marketplace:latest .
          
          echo "Marketplace build complete"
        else
          echo "Skipping Marketplace - no changes detected"
        fi
    waitFor: ['detect-changes']

  # 3. Security scanning with Container Analysis
  - name: 'gcr.io/cloud-builders/gcloud'
    id: 'security-scan'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        echo "Running security scans..."
        
        # Scan each built service
        for service in $(cat /workspace/services_to_build.txt); do
          echo "Scanning $service..."
          
          # Push image for scanning
          docker push $_IMAGE_REGISTRY/$service:$BUILD_ID
          
          # Trigger Container Analysis scan
          gcloud beta container images scan $_IMAGE_REGISTRY/$service:$BUILD_ID \
            --project=$PROJECT_ID
          
          # Wait for scan results
          sleep 30
          
          # Get scan results
          gcloud beta container images list-tags $_IMAGE_REGISTRY/$service \
            --filter="tags:$BUILD_ID" \
            --format="table(digest,tags,timestamp)"
        done
    waitFor: ['build-analysis-engine', 'build-query-intelligence', 'build-pattern-mining', 'build-marketplace']

  # 4. Binary Authorization - Sign and attest images
  - name: 'gcr.io/cloud-builders/gcloud'
    id: 'sign-images'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        echo "🔐 Signing images with Binary Authorization..."
        
        # Binary Authorization configuration
        PROJECT_ID=${PROJECT_ID}
        ATTESTOR_NAME="episteme-production-attestor"
        KEY_LOCATION="global"
        KEY_RING="episteme-binauthz-keys"
        KEY_NAME="signing-key"
        
        # Function to sign and attest an image
        sign_image() {
          local SERVICE_NAME=$1
          local IMAGE_URL="${_IMAGE_REGISTRY}/$SERVICE_NAME:$BUILD_ID"
          
          echo "Signing image: $IMAGE_URL"
          
          # Get image digest
          IMAGE_DIGEST=$(gcloud container images describe $IMAGE_URL --format='get(image_summary.digest)')
          
          if [ -z "$IMAGE_DIGEST" ]; then
            echo "Error: Could not get digest for $IMAGE_URL"
            exit 1
          fi
          
          echo "Image digest: $IMAGE_DIGEST"
          
          # Create attestation
          gcloud beta container binauthz attestations sign-and-create \
            --artifact-url="$IMAGE_URL@$IMAGE_DIGEST" \
            --attestor="$ATTESTOR_NAME" \
            --attestor-project="$PROJECT_ID" \
            --keyversion-project="$PROJECT_ID" \
            --keyversion-location="$KEY_LOCATION" \
            --keyversion-keyring="$KEY_RING" \
            --keyversion-key="$KEY_NAME" \
            --keyversion="1" || {
              echo "Warning: Failed to sign $IMAGE_URL - continuing with deployment"
              return 1
            }
          
          echo "✅ Successfully signed: $IMAGE_URL"
          return 0
        }
        
        # Sign each built service image
        SIGNED_IMAGES=""
        for service in $(cat /workspace/services_to_build.txt); do
          echo "Attempting to sign $service..."
          if sign_image "$service"; then
            SIGNED_IMAGES="$SIGNED_IMAGES $service"
          fi
        done
        
        echo "Signed images: $SIGNED_IMAGES"
        echo "$SIGNED_IMAGES" > /workspace/signed_images.txt
        
        if [ -z "$SIGNED_IMAGES" ]; then
          echo "⚠️  Warning: No images were successfully signed"
        else
          echo "✅ Binary Authorization signing complete"
        fi
    waitFor: ['security-scan']

  # 5. Deploy to staging (on main branch)
  - name: 'gcr.io/cloud-builders/gcloud'
    id: 'deploy-staging'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        if [ "$BRANCH_NAME" = "main" ]; then
          echo "Deploying to staging environment..."
          
          for service in $(cat /workspace/services_to_build.txt); do
            echo "Deploying $service to staging..."
            
            # Deploy to Cloud Run
            gcloud run deploy $service-staging \
              --image $_IMAGE_REGISTRY/$service:$BUILD_ID \
              --platform managed \
              --region us-central1 \
              --memory 2Gi \
              --cpu 2 \
              --max-instances 10 \
              --min-instances 0 \
              --timeout 300s \
              --concurrency 1000 \
              --port 8001 \
              --set-env-vars "ENVIRONMENT=staging,VERSION=$BUILD_ID" \
              --allow-unauthenticated \
              --no-traffic \
              --tag staging-$BUILD_ID
            
            # Run smoke tests
            SERVICE_URL=$(gcloud run services describe $service-staging \
              --region us-central1 \
              --format 'value(status.url)')
            
            TAGGED_URL="${SERVICE_URL/https:\/\//https://staging-$BUILD_ID---}"
            
            echo "Running smoke tests for $service at $TAGGED_URL"
            curl -f $TAGGED_URL/health || echo "Health check failed for $service"
            
            # Gradually shift traffic: 0% -> 50% -> 100%
            gcloud run services update-traffic $service-staging \
              --to-tags staging-$BUILD_ID=50 \
              --region us-central1
            
            sleep 30
            
            gcloud run services update-traffic $service-staging \
              --to-tags staging-$BUILD_ID=100 \
              --region us-central1
              
            echo "$service deployed to staging successfully"
          done
        else
          echo "Skipping staging deployment - not main branch"
        fi
    waitFor: ['sign-images']

  # 6. Deploy to production (manual approval required)
  - name: 'gcr.io/cloud-builders/gcloud'
    id: 'deploy-production'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        if [ "$_DEPLOY_TO_PROD" = "true" ] && [ "$BRANCH_NAME" = "main" ]; then
          echo "Deploying to production environment..."
          
          for service in $(cat /workspace/services_to_build.txt); do
            echo "Deploying $service to production..."
            
            # Deploy to Cloud Run with canary strategy
            gcloud run deploy $service-production \
              --image $_IMAGE_REGISTRY/$service:$BUILD_ID \
              --platform managed \
              --region us-central1 \
              --memory 4Gi \
              --cpu 4 \
              --max-instances 1000 \
              --min-instances 3 \
              --timeout 300s \
              --concurrency 1000 \
              --port 8001 \
              --set-env-vars "ENVIRONMENT=production,VERSION=$BUILD_ID" \
              --allow-unauthenticated \
              --no-traffic \
              --tag prod-$BUILD_ID
            
            # Canary deployment: 10% -> 50% -> 100%
            echo "Starting canary deployment for $service..."
            
            gcloud run services update-traffic $service-production \
              --to-tags prod-$BUILD_ID=10 \
              --region us-central1
            
            echo "10% traffic routed to new version. Waiting 5 minutes..."
            sleep 300
            
            gcloud run services update-traffic $service-production \
              --to-tags prod-$BUILD_ID=50 \
              --region us-central1
              
            echo "50% traffic routed. Waiting 10 minutes..."
            sleep 600
            
            gcloud run services update-traffic $service-production \
              --to-tags prod-$BUILD_ID=100 \
              --region us-central1
            
            echo "$service deployed to production successfully"
          done
        else
          echo "Skipping production deployment - manual approval required or not main branch"
        fi
    waitFor: ['deploy-staging']

# Substitutions (variables)
substitutions:
  _IMAGE_REGISTRY: 'us-central1-docker.pkg.dev/vibe-match-463114/ccl-services'
  _DEPLOY_TO_PROD: 'false'  # Set to 'true' manually for production deployments
  _TRIGGER_TYPE: 'push'

# Options
options:
  machineType: 'E2_HIGHCPU_8'  # High CPU for parallel builds
  diskSizeGb: 100
  logging: CLOUD_LOGGING_ONLY
  env:
    - 'DOCKER_BUILDKIT=1'
  
# Timeout for entire build
timeout: '3600s'  # 60 minutes

# Build artifacts to store
artifacts:
  images: 
    - '${_IMAGE_REGISTRY}/analysis-engine:$BUILD_ID'
    - '${_IMAGE_REGISTRY}/analysis-engine:latest' 
    - '${_IMAGE_REGISTRY}/query-intelligence:$BUILD_ID'
    - '${_IMAGE_REGISTRY}/query-intelligence:latest'
    - '${_IMAGE_REGISTRY}/pattern-mining:$BUILD_ID'
    - '${_IMAGE_REGISTRY}/pattern-mining:latest'
    - '${_IMAGE_REGISTRY}/marketplace:$BUILD_ID'
    - '${_IMAGE_REGISTRY}/marketplace:latest'