# Production Troubleshooting Guide

**Document Type**: Technical Guide  
**Last Updated**: 2025-01-16  
**Scope**: Cross-service production issues

## Overview

This guide provides detailed troubleshooting procedures for common production issues that affect multiple services in the Episteme platform.

## Quick Diagnostic Commands

### Platform Health Assessment

```bash
# One-command platform health check
./scripts/diagnostics/platform_health_check.sh

# Individual service health verification
curl -f https://analysis-engine-l3nxty7oka-uc.a.run.app/health || echo "❌ Analysis Engine DOWN"
curl -f https://pattern-mining.ccl-platform.com/health || echo "❌ Pattern Mining DOWN"  
curl -f https://query-intelligence.ccl-platform.com/health || echo "❌ Query Intelligence DOWN"

# Infrastructure health check
gcloud run services list --platform=managed --region=us-central1 \
  --format="table(metadata.name,status.url,status.conditions.status)"

# Database connectivity test
timeout 10 gcloud spanner databases execute-sql ccl_main \
  --instance=ccl-instance --sql="SELECT 1 as health_check" || echo "❌ Database DOWN"

# Cache connectivity test  
timeout 5 redis-cli -h *********** ping || echo "❌ Redis DOWN"
```

## Common Production Issues

### 1. Complete Platform Outage

**Symptoms**
- All health endpoints return 5xx errors
- No successful requests to any service
- User reports of complete unavailability

**Initial Diagnosis (2 minutes)**
```bash
# Check Google Cloud service status
curl -s "https://status.cloud.google.com/incidents.json" | \
  jq '.[] | select(.begin > (now - 3600)) | {service_name, begin, most_recent_update}'

# Verify Cloud Run platform status
gcloud run services list --region=us-central1 --format="table(metadata.name,status.url)"

# Check recent deployments (possible cause)
gcloud run revisions list --region=us-central1 \
  --filter="metadata.creationTimestamp>=$(date -d '2 hours ago' -u +%Y-%m-%dT%H:%M:%SZ)" \
  --format="table(metadata.name,metadata.creationTimestamp)"

# Infrastructure event audit
gcloud logging read 'protoPayload.serviceName="run.googleapis.com" AND severity>=ERROR' \
  --limit=20 --format="table(timestamp,protoPayload.methodName,protoPayload.response.error)"
```

**Resolution Steps**
```bash
# If deployment-related: Rollback all services
./scripts/emergency/coordinated_rollback.sh --all-services --to-stable

# If infrastructure-related: Check and restart services
./scripts/emergency/restart_all_services.sh --verify-health

# If database-related: Check Spanner status and failover if needed
./scripts/emergency/database_failover.sh --verify-before-switch

# Verify recovery
./scripts/diagnostics/verify_platform_recovery.sh --comprehensive
```

### 2. High Cross-Service Latency

**Symptoms**
- All services responding but with high latency (>5s)
- Timeout errors in inter-service calls
- Users experiencing slow responses

**Diagnosis**
```bash
# Check end-to-end request tracing
gcloud logging read 'jsonPayload.trace_id!="" AND jsonPayload.duration_ms>5000' \
  --limit=50 --format="table(timestamp,resource.labels.service_name,jsonPayload.operation,jsonPayload.duration_ms)"

# Database performance analysis
gcloud spanner databases execute-sql ccl_main \
  --instance=ccl-instance \
  --sql="SELECT * FROM INFORMATION_SCHEMA.QUERY_STATS 
         WHERE INTERVAL_END >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 1 HOUR)
         ORDER BY AVG_LATENCY_SECONDS DESC LIMIT 10"

# Cache performance check
redis-cli -h *********** --latency-history -i 1

# Network latency assessment
./scripts/diagnostics/network_latency_test.sh --between-services
```

**Resolution**
```bash
# Scale up resources immediately
gcloud run services update analysis-engine --cpu=4 --memory=8Gi --max-instances=100
gcloud run services update pattern-mining --cpu=2 --memory=4Gi --max-instances=50
gcloud run services update query-intelligence --cpu=2 --memory=4Gi --max-instances=30

# Optimize database if bottleneck identified
gcloud spanner instances update ccl-instance --nodes=5

# Clear cache if performance degraded
redis-cli -h *********** FLUSHDB  # Use with caution

# Enable performance monitoring
./scripts/monitoring/enable_detailed_monitoring.sh --duration 1h
```

### 3. Authentication Failures Across All Services

**Symptoms**
- 401 Unauthorized errors from all services
- JWT validation failures
- Users unable to authenticate

**Diagnosis**
```bash
# Check JWT secret consistency
kubectl get secrets jwt-secret -o yaml | grep -A1 data

# Verify token generation service
./scripts/diagnostics/test_token_generation.sh

# Check authentication middleware logs
gcloud logging read 'jsonPayload.event="auth_failure" OR jsonPayload.error="jwt"' \
  --limit=50 --format="table(timestamp,resource.labels.service_name,jsonPayload.error)"

# Test authentication flow
./scripts/diagnostics/end_to_end_auth_test.sh
```

**Resolution**
```bash
# Emergency JWT secret rotation
./scripts/security/emergency_jwt_rotation.sh --force --all-services

# Restart all services to pick up new secrets
kubectl rollout restart deployment/analysis-engine
kubectl rollout restart deployment/pattern-mining
kubectl rollout restart deployment/query-intelligence

# Verify authentication restoration
./scripts/diagnostics/verify_auth_recovery.sh --all-endpoints

# Generate new test tokens
./scripts/security/generate_test_tokens.sh --validity 24h
```

### 4. Database Connection Pool Exhaustion

**Symptoms**
- "Connection pool exhausted" errors from all services
- Database timeout errors
- Intermittent service availability

**Diagnosis**
```bash
# Check active connections
gcloud spanner databases execute-sql ccl_main \
  --instance=ccl-instance \
  --sql="SELECT * FROM INFORMATION_SCHEMA.SPANNER_STATISTICS 
         WHERE CATEGORY = 'QUERY_STATS' AND NAME = 'ACTIVE_QUERIES'"

# Review connection pool configurations
kubectl get deployments -o yaml | grep -A5 -B5 "DATABASE_POOL_SIZE"

# Check for connection leaks
./scripts/diagnostics/connection_leak_analysis.sh

# Monitor connection usage over time
./scripts/monitoring/database_connection_monitoring.sh --realtime
```

**Resolution**
```bash
# Increase connection pool limits
kubectl set env deployment/analysis-engine DATABASE_POOL_SIZE=75 DATABASE_MAX_OVERFLOW=25
kubectl set env deployment/pattern-mining DATABASE_POOL_SIZE=50 DATABASE_MAX_OVERFLOW=20
kubectl set env deployment/query-intelligence DATABASE_POOL_SIZE=35 DATABASE_MAX_OVERFLOW=15

# Scale database if needed
gcloud spanner instances update ccl-instance --nodes=4

# Force connection pool reset
./scripts/maintenance/reset_connection_pools.sh --all-services

# Monitor connection pool health
./scripts/monitoring/connection_pool_monitoring.sh --alert-threshold 80
```

### 5. Cache Failure and High Cache Miss Rate

**Symptoms**
- High latency across all services
- Cache hit rate drops to near 0%
- Redis connection errors

**Diagnosis**
```bash
# Check Redis instance status
gcloud redis instances describe analysis-engine-cache --region=us-central1 \
  --format="table(displayName,state,currentLocationId,memorySizeGb)"

# Test cache connectivity from services
./scripts/diagnostics/test_cache_from_services.sh

# Analyze cache performance
redis-cli -h *********** INFO stats | grep -E "keyspace_hits|keyspace_misses|used_memory"

# Check VPC connector status
gcloud compute networks vpc-access connectors describe episteme-connector \
  --region=us-central1 --format="yaml(state,ipCidrRange)"
```

**Resolution**
```bash
# Redis failover if instance is unhealthy
gcloud redis instances failover analysis-engine-cache --region=us-central1

# Update cache endpoints if failover occurred
kubectl set env deployment/analysis-engine REDIS_URL="redis://NEW_IP:6379"
kubectl set env deployment/pattern-mining REDIS_URL="redis://NEW_IP:6379"
kubectl set env deployment/query-intelligence REDIS_URL="redis://NEW_IP:6379"

# Enable graceful degradation (cache-disabled mode)
./scripts/emergency/enable_no_cache_mode.sh --all-services

# Warm up cache after recovery
./scripts/maintenance/warm_up_cache.sh --priority-keys
```

### 6. Cross-Service Communication Failures

**Symptoms**
- Services appear healthy individually but can't communicate
- Inter-service API calls failing
- Cascade failures across services

**Diagnosis**
```bash
# Test inter-service connectivity
./scripts/diagnostics/inter_service_connectivity.sh --verbose

# Check VPC and firewall configuration
gcloud compute firewall-rules list --filter="name:episteme*" \
  --format="table(name,direction,priority,sourceRanges,allowed)"

# Review service mesh configuration (if applicable)
kubectl get services -o yaml | grep -A10 -B5 "clusterIP"

# Analyze request routing
gcloud logging read 'jsonPayload.service_call=true AND jsonPayload.success=false' \
  --format="table(timestamp,resource.labels.service_name,jsonPayload.target_service,jsonPayload.error)"
```

**Resolution**
```bash
# Recreate VPC connector if corrupted
./scripts/infrastructure/recreate_vpc_connector.sh --verify-connectivity

# Update firewall rules for service communication
gcloud compute firewall-rules update episteme-internal \
  --allow tcp:8080,tcp:8081,tcp:8082 \
  --source-ranges 10.0.0.0/8

# Restart services to refresh network connections
./scripts/maintenance/rolling_restart.sh --service-order dependency-aware

# Verify inter-service communication
./scripts/diagnostics/test_service_mesh.sh --comprehensive
```

## Performance Troubleshooting

### Identifying Performance Bottlenecks

```bash
# CPU bottleneck identification
gcloud monitoring metrics list \
  --filter='metric.type:"run.googleapis.com/container/cpu/utilization"' \
  --format="table(metricDescriptor.displayName)"

# Memory bottleneck analysis
./scripts/diagnostics/memory_usage_analysis.sh --all-services --detailed

# Database query performance analysis
gcloud spanner databases execute-sql ccl_main \
  --instance=ccl-instance \
  --sql="SELECT 
           TEXT,
           AVG_LATENCY_SECONDS,
           AVG_ROWS_SCANNED,
           AVG_CPU_SECONDS
         FROM INFORMATION_SCHEMA.QUERY_STATS 
         WHERE INTERVAL_END >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 1 HOUR)
         ORDER BY AVG_LATENCY_SECONDS DESC LIMIT 10"

# Network bottleneck assessment
./scripts/diagnostics/network_throughput_test.sh --between-regions
```

### Memory Leak Detection

```bash
# Service memory trend analysis
./scripts/diagnostics/memory_leak_detection.sh --service all --period 24h

# Force garbage collection and measure impact
./scripts/maintenance/force_gc_all_services.sh --measure-impact

# Container restart if memory leak confirmed
kubectl delete pods -l app=analysis-engine  # Kubernetes will recreate
gcloud run services update pattern-mining --max-instances=0  # Then restore
sleep 30
gcloud run services update pattern-mining --max-instances=50
```

### Database Performance Optimization

```bash
# Identify slow queries
gcloud spanner databases execute-sql ccl_main \
  --instance=ccl-instance \
  --sql="SELECT TEXT, AVG_LATENCY_SECONDS, EXECUTION_COUNT
         FROM INFORMATION_SCHEMA.QUERY_STATS 
         WHERE AVG_LATENCY_SECONDS > 1.0
         ORDER BY AVG_LATENCY_SECONDS DESC"

# Check index usage
gcloud spanner databases execute-sql ccl_main \
  --instance=ccl-instance \
  --sql="SELECT * FROM INFORMATION_SCHEMA.INDEXES WHERE SPANNER_STATE = 'READ_WRITE'"

# Optimize query performance
./scripts/database/analyze_and_optimize.sh --slow-queries --suggest-indexes
```

## Security Troubleshooting

### Suspicious Activity Detection

```bash
# Monitor for unusual request patterns
gcloud logging read 'httpRequest.status>=400 AND httpRequest.userAgent!=""' \
  --limit=100 --format="table(timestamp,httpRequest.remoteIp,httpRequest.requestUrl,httpRequest.status)" | \
  sort | uniq -c | sort -nr

# Check for authentication bypass attempts
gcloud logging read 'jsonPayload.event_type="security_event"' \
  --format="table(timestamp,resource.labels.service_name,jsonPayload.event,jsonPayload.source_ip)"

# Analyze API key usage patterns
./scripts/security/api_key_usage_analysis.sh --suspicious-patterns
```

### Access Control Issues

```bash
# Verify IAM permissions
gcloud projects get-iam-policy vibe-match-463114 \
  --flatten="bindings[].members" \
  --filter="bindings.members:serviceAccount:*analysis-engine*"

# Check service account key status
gcloud iam service-accounts keys list \
  --iam-account=<EMAIL>

# Audit recent permission changes
gcloud logging read 'protoPayload.serviceName="cloudresourcemanager.googleapis.com" AND 
                     protoPayload.methodName="SetIamPolicy"' \
  --limit=20 --format="table(timestamp,protoPayload.authenticationInfo.principalEmail)"
```

## Monitoring and Alerting Troubleshooting

### Alert Fatigue and False Positives

```bash
# Analyze alert frequency and accuracy
./scripts/monitoring/alert_analysis.sh --period 7d --false-positive-rate

# Tune alert thresholds based on recent data
./scripts/monitoring/optimize_alert_thresholds.sh --service all --auto-tune

# Review alert escalation effectiveness
./scripts/monitoring/escalation_analysis.sh --incidents-resolved-time
```

### Missing Alerts

```bash
# Verify monitoring agent health
./scripts/monitoring/check_monitoring_agents.sh --all-services

# Test alert delivery
./scripts/monitoring/test_alert_delivery.sh --to all-channels

# Check alert policy configuration
gcloud alpha monitoring policies list --format="table(displayName,enabled,conditions)"
```

## Recovery Verification

### Post-Incident Verification Checklist

```bash
# Comprehensive platform health verification
./scripts/diagnostics/comprehensive_health_check.sh --post-incident

# End-to-end functionality test
./scripts/testing/end_to_end_integration_test.sh --production-safe

# Performance baseline verification
./scripts/testing/performance_baseline_test.sh --compare-to-historical

# Security posture verification
./scripts/security/security_posture_check.sh --comprehensive

# Monitoring system verification
./scripts/monitoring/verify_monitoring_recovery.sh --all-metrics
```

### Load Testing After Recovery

```bash
# Gradual load increase to verify stability
./scripts/testing/gradual_load_test.sh --start-load 10% --max-load 100% --duration 30m

# Stress test critical endpoints
./scripts/testing/stress_test_critical_endpoints.sh --duration 10m

# Monitor for any degradation during load test
./scripts/monitoring/realtime_monitoring.sh --during-load-test
```

## Emergency Contact Integration

### Automated Diagnostic Reports

```bash
# Generate comprehensive diagnostic report
./scripts/diagnostics/generate_emergency_report.sh \
  --incident-id "INC-2025-001" \
  --send-to "<EMAIL>" \
  --include-logs --include-metrics --include-recommendations

# Real-time status for management
./scripts/diagnostics/management_dashboard.sh --realtime --incident-mode
```

---

**Document Owner**: Platform SRE Team  
**Review Frequency**: Monthly  
**Last Tested**: 2025-01-16  
**Next Review**: 2025-02-16

> **Note**: This troubleshooting guide should be used in conjunction with the [Production Runbook](/docs/PRODUCTION_RUNBOOK.md) and individual service runbooks for comprehensive incident response.