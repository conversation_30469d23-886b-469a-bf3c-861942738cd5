# Production Recovery Procedures

**Document Type**: Emergency Procedures  
**Last Updated**: 2025-01-16  
**Scope**: System recovery and restoration procedures

## Overview

This document provides detailed recovery procedures for the Episteme platform, covering various failure scenarios and restoration strategies.

## Recovery Classification

### Recovery Types

| Type | Description | RTO Target | RPO Target |
|------|-------------|------------|------------|
| **Hot Recovery** | Automatic failover, no manual intervention | < 5 minutes | < 1 minute |
| **Warm Recovery** | Semi-automatic, minimal manual steps | < 15 minutes | < 5 minutes |
| **Cold Recovery** | Full manual restoration process | < 60 minutes | < 30 minutes |
| **Disaster Recovery** | Complete rebuild from backups | < 4 hours | < 1 hour |

### Failure Scenarios

1. **Service-Level Failures**: Individual service outages
2. **Platform-Level Failures**: Multiple service outages
3. **Infrastructure Failures**: Regional or provider-level outages
4. **Data Corruption**: Database or cache data integrity issues
5. **Security Breaches**: Compromised systems requiring isolation and cleanup

## Service Recovery Procedures

### Analysis Engine Recovery

**Scenario: Service unresponsive or crashing**

```bash
# 1. Quick health assessment
curl -f https://analysis-engine-l3nxty7oka-uc.a.run.app/health || echo "FAILED"

# 2. Check recent logs for error patterns
gcloud logging read 'resource.labels.service_name="analysis-engine" AND severity>=ERROR' \
  --limit=20 --format="table(timestamp,jsonPayload.error)"

# 3. Review recent deployments
gcloud run revisions list --service=analysis-engine --region=us-central1 --limit=5

# 4. Recovery action - rollback if deployment-related
STABLE_REVISION=$(gcloud run revisions list --service=analysis-engine \
  --region=us-central1 --format="value(metadata.name)" --limit=2 | tail -1)
gcloud run services update-traffic analysis-engine \
  --to-revisions=$STABLE_REVISION=100 --region=us-central1

# 5. If not deployment-related - restart service
gcloud run services update analysis-engine \
  --region=us-central1 --update-env-vars=RESTART_TRIGGER=$(date +%s)

# 6. Verify recovery
sleep 30
curl -f https://analysis-engine-l3nxty7oka-uc.a.run.app/health && echo "RECOVERED"

# 7. Restore to full capacity
gcloud run services update analysis-engine \
  --max-instances=100 --region=us-central1
```

### Pattern Mining Recovery

**Scenario: ML processing pipeline failure**

```bash
# 1. Check ML pipeline status
curl -f https://pattern-mining.ccl-platform.com/health/pipeline || echo "PIPELINE FAILED"

# 2. Verify BigQuery ML model availability
bq query --use_legacy_sql=false \
  "SELECT * FROM ML.MODELS WHERE model_name = 'pattern_detection_model'"

# 3. Check Ray cluster status
ray status --address=$RAY_ADDRESS || echo "RAY CLUSTER DOWN"

# 4. Recovery - restart Ray cluster if needed
ray stop --force
ray start --head --dashboard-host=0.0.0.0 --port=10001

# 5. Restart pattern mining service
gcloud run services update pattern-mining \
  --update-env-vars=RAY_RESTART=$(date +%s)

# 6. Verify ML pipeline recovery
./scripts/testing/test_pattern_detection.sh --sample-data

# 7. Resume background processing
curl -X POST https://pattern-mining.ccl-platform.com/admin/resume-processing \
  -H "Authorization: Bearer $ADMIN_TOKEN"
```

### Query Intelligence Recovery

**Scenario: Gemini API integration failure**

```bash
# 1. Check Gemini API integration
curl -f https://query-intelligence.ccl-platform.com/health/gemini || echo "GEMINI FAILED"

# 2. Test Gemini API directly
curl -X POST https://generativelanguage.googleapis.com/v1/models/gemini-2.5-flash:generateContent \
  -H "Authorization: Bearer $GEMINI_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{"contents": [{"parts": [{"text": "test"}]}]}'

# 3. If API key issue - rotate key
./scripts/security/rotate_gemini_key.sh --emergency

# 4. If rate limiting - enable fallback mode
kubectl set env deployment/query-intelligence \
  GEMINI_FALLBACK_MODE=true \
  GEMINI_RATE_LIMIT_BACKOFF=true

# 5. Restart service with new configuration
kubectl rollout restart deployment/query-intelligence

# 6. Verify recovery
./scripts/testing/test_query_intelligence.sh --basic-queries

# 7. Gradually increase traffic
gcloud run services update-traffic query-intelligence \
  --to-latest=50 --to-tags=stable=50
```

## Database Recovery Procedures

### Spanner Connection Recovery

**Scenario: Database connection failures across services**

```bash
# 1. Test database connectivity
timeout 10 gcloud spanner databases execute-sql ccl_main \
  --instance=ccl-instance --sql="SELECT 1" || echo "DB UNREACHABLE"

# 2. Check Spanner instance status
gcloud spanner instances describe ccl-instance \
  --format="yaml(state,displayName,nodeCount)"

# 3. Check for long-running transactions
gcloud spanner databases execute-sql ccl_main \
  --instance=ccl-instance \
  --sql="SELECT * FROM INFORMATION_SCHEMA.SPANNER_STATISTICS 
         WHERE CATEGORY = 'QUERY_STATS' AND NAME = 'ACTIVE_QUERIES'"

# 4. Reset connection pools across all services
./scripts/database/reset_connection_pools.sh --all-services --force

# 5. If Spanner instance degraded - scale up temporarily
gcloud spanner instances update ccl-instance --nodes=5

# 6. Verify database recovery
./scripts/testing/database_connectivity_test.sh --all-services

# 7. Monitor database performance
./scripts/monitoring/database_performance_monitoring.sh --duration 30m
```

### Data Corruption Recovery

**Scenario: Detected data corruption in production database**

```bash
# 1. Immediately enable read-only mode to prevent further corruption
kubectl set env deployment/analysis-engine DATABASE_READ_ONLY=true
kubectl set env deployment/pattern-mining DATABASE_READ_ONLY=true
kubectl set env deployment/query-intelligence DATABASE_READ_ONLY=true

# 2. Assess corruption scope
gcloud spanner databases execute-sql ccl_main \
  --instance=ccl-instance \
  --sql="SELECT table_name, COUNT(*) as total_rows,
                COUNT(CASE WHEN data IS NULL THEN 1 END) as corrupted_rows
         FROM INFORMATION_SCHEMA.TABLES t
         JOIN ccl_main.analysis_results ar ON TRUE
         GROUP BY table_name"

# 3. Create emergency backup before any changes
gcloud spanner backups create emergency-backup-$(date +%Y%m%d-%H%M%S) \
  --instance=ccl-instance \
  --database=ccl_main \
  --retention-period=30d

# 4. Identify last known good backup
gcloud spanner backups list --instance=ccl-instance \
  --filter="createTime<'$(date -d '1 hour ago' -u +%Y-%m-%dT%H:%M:%SZ)'" \
  --format="table(name,createTime,sizeBytes)"

# 5. Restore from backup to temporary database
BACKUP_NAME="projects/vibe-match-463114/instances/ccl-instance/backups/backup-name"
gcloud spanner databases restore ccl_main_recovery \
  --source-backup=$BACKUP_NAME \
  --instance=ccl-instance

# 6. Validate restored data integrity
./scripts/database/validate_data_integrity.sh --database ccl_main_recovery

# 7. Switch services to recovery database
./scripts/database/switch_database.sh \
  --from ccl_main --to ccl_main_recovery --verify-first

# 8. Re-enable write operations
kubectl set env deployment/analysis-engine DATABASE_READ_ONLY=false
kubectl set env deployment/pattern-mining DATABASE_READ_ONLY=false
kubectl set env deployment/query-intelligence DATABASE_READ_ONLY=false

# 9. Verify full recovery
./scripts/testing/end_to_end_functionality_test.sh --include-writes
```

## Cache Recovery Procedures

### Redis Instance Recovery

**Scenario: Redis cache completely unavailable**

```bash
# 1. Check Redis instance status
gcloud redis instances describe analysis-engine-cache --region=us-central1

# 2. Test connectivity
timeout 5 redis-cli -h *********** ping || echo "REDIS UNREACHABLE"

# 3. If instance is down - failover to backup
gcloud redis instances failover analysis-engine-cache --region=us-central1

# 4. Update service configurations with new endpoint
NEW_REDIS_IP=$(gcloud redis instances describe analysis-engine-cache \
  --region=us-central1 --format="value(host)")
kubectl set env deployment/analysis-engine REDIS_URL="redis://$NEW_REDIS_IP:6379"
kubectl set env deployment/pattern-mining REDIS_URL="redis://$NEW_REDIS_IP:6379"
kubectl set env deployment/query-intelligence REDIS_URL="redis://$NEW_REDIS_IP:6379"

# 5. If failover not available - enable cache-disabled mode
if [ $? -ne 0 ]; then
  kubectl set env deployment/analysis-engine CACHE_ENABLED=false
  kubectl set env deployment/pattern-mining CACHE_ENABLED=false
  kubectl set env deployment/query-intelligence CACHE_ENABLED=false
fi

# 6. Verify services can operate without cache
./scripts/testing/test_cacheless_operation.sh --all-services

# 7. Once Redis is restored, warm up the cache
./scripts/cache/warm_up_cache.sh --priority-data --parallel

# 8. Re-enable cache gradually
./scripts/cache/gradual_cache_enablement.sh --all-services
```

## Network Recovery Procedures

### VPC Connectivity Recovery

**Scenario: Inter-service communication failures**

```bash
# 1. Test VPC connector status
gcloud compute networks vpc-access connectors describe episteme-connector \
  --region=us-central1

# 2. Test inter-service connectivity
./scripts/network/test_inter_service_connectivity.sh --verbose

# 3. Check firewall rules
gcloud compute firewall-rules list --filter="name:episteme*" \
  --format="table(name,direction,allowed,sourceRanges,targetTags)"

# 4. If VPC connector is unhealthy - recreate
gcloud compute networks vpc-access connectors delete episteme-connector \
  --region=us-central1 --quiet

gcloud compute networks vpc-access connectors create episteme-connector \
  --region=us-central1 \
  --subnet=default \
  --subnet-project=vibe-match-463114 \
  --min-instances=2 \
  --max-instances=10

# 5. Update services to use new connector
./scripts/network/update_vpc_connector.sh --all-services --connector episteme-connector

# 6. Verify connectivity restoration
./scripts/network/comprehensive_connectivity_test.sh

# 7. Monitor network performance
./scripts/monitoring/network_performance_monitoring.sh --duration 15m
```

## Security Incident Recovery

### Compromised Service Recovery

**Scenario: Service security breach detected**

```bash
# 1. Immediately isolate affected service
AFFECTED_SERVICE="analysis-engine"  # Replace with actual service
gcloud run services update $AFFECTED_SERVICE \
  --ingress=internal --region=us-central1

# 2. Revoke all authentication tokens
./scripts/security/revoke_all_tokens.sh --service $AFFECTED_SERVICE

# 3. Rotate all secrets for the service
./scripts/security/rotate_service_secrets.sh --service $AFFECTED_SERVICE --force

# 4. Create clean deployment from known good image
LAST_GOOD_IMAGE=$(gcloud run revisions list --service=$AFFECTED_SERVICE \
  --region=us-central1 --limit=5 --format="value(spec.template.spec.containers[0].image)" | \
  grep -v "$(date +%Y%m%d)")  # Avoid today's potentially compromised images

gcloud run deploy $AFFECTED_SERVICE-clean \
  --image=$LAST_GOOD_IMAGE \
  --region=us-central1 \
  --no-traffic

# 5. Security scan the clean deployment
./scripts/security/comprehensive_security_scan.sh --service $AFFECTED_SERVICE-clean

# 6. If clean deployment passes - switch traffic
gcloud run services update-traffic $AFFECTED_SERVICE \
  --to-revisions=$AFFECTED_SERVICE-clean=100

# 7. Enable external access gradually
gcloud run services update $AFFECTED_SERVICE \
  --ingress=all --region=us-central1

# 8. Monitor for continued security issues
./scripts/security/enhanced_monitoring.sh --service $AFFECTED_SERVICE --duration 24h
```

## Disaster Recovery Procedures

### Regional Failover

**Scenario: Complete us-central1 region outage**

```bash
# 1. Verify regional outage scope
./scripts/disaster-recovery/verify_regional_outage.sh --region us-central1

# 2. Activate disaster recovery region
./scripts/disaster-recovery/activate_dr_region.sh --target us-east1

# 3. Update DNS to point to DR region
./scripts/disaster-recovery/update_dns_to_dr.sh --region us-east1

# 4. Scale up DR services
gcloud run services update analysis-engine-dr --region=us-east1 --min-instances=10
gcloud run services update pattern-mining-dr --region=us-east1 --min-instances=5
gcloud run services update query-intelligence-dr --region=us-east1 --min-instances=3

# 5. Verify database replication to DR region
./scripts/disaster-recovery/verify_database_replication.sh --to us-east1

# 6. Test full platform functionality in DR region
./scripts/testing/dr_functionality_test.sh --region us-east1

# 7. Monitor DR region performance
./scripts/monitoring/dr_region_monitoring.sh --region us-east1 --duration 4h

# 8. Plan for fallback to primary region when available
./scripts/disaster-recovery/prepare_fallback_plan.sh --primary us-central1
```

### Complete Platform Rebuild

**Scenario: Catastrophic failure requiring complete rebuild**

```bash
# 1. Document current state and gather evidence
./scripts/disaster-recovery/document_failure_state.sh --comprehensive

# 2. Verify backup integrity
./scripts/disaster-recovery/verify_all_backups.sh --test-restore

# 3. Create new clean environment
./scripts/disaster-recovery/create_clean_environment.sh --project-suffix recovery

# 4. Restore database from backup
./scripts/disaster-recovery/restore_database.sh \
  --backup-name "backup-20250116" \
  --target-project "vibe-match-463114-recovery"

# 5. Deploy services from clean images
./scripts/disaster-recovery/deploy_clean_services.sh \
  --from-images "registry.gcr.io/vibe-match-463114/clean-images"

# 6. Restore configuration from secure backup
./scripts/disaster-recovery/restore_configuration.sh \
  --config-backup "gs://episteme-config-backup/latest"

# 7. Comprehensive testing before traffic switch
./scripts/testing/comprehensive_rebuild_testing.sh

# 8. Gradual traffic migration
./scripts/disaster-recovery/gradual_traffic_migration.sh \
  --from-project "vibe-match-463114" \
  --to-project "vibe-match-463114-recovery"
```

## Recovery Validation Procedures

### Post-Recovery Health Checks

```bash
# Comprehensive health verification script
#!/bin/bash
set -e

echo "🔍 Starting post-recovery validation..."

# 1. Service health checks
echo "✅ Testing service health..."
curl -f https://analysis-engine-l3nxty7oka-uc.a.run.app/health
curl -f https://pattern-mining.ccl-platform.com/health
curl -f https://query-intelligence.ccl-platform.com/health

# 2. Database connectivity
echo "✅ Testing database connectivity..."
gcloud spanner databases execute-sql ccl_main \
  --instance=ccl-instance --sql="SELECT COUNT(*) FROM analysis_results"

# 3. Cache connectivity
echo "✅ Testing cache connectivity..."
redis-cli -h *********** ping

# 4. Inter-service communication
echo "✅ Testing inter-service communication..."
./scripts/testing/inter_service_api_test.sh

# 5. End-to-end functionality
echo "✅ Testing end-to-end functionality..."
./scripts/testing/e2e_recovery_test.sh

# 6. Performance baseline
echo "✅ Verifying performance baseline..."
./scripts/testing/performance_baseline_test.sh --compare-pre-incident

# 7. Security posture
echo "✅ Verifying security posture..."
./scripts/security/post_recovery_security_check.sh

echo "🎉 Recovery validation complete!"
```

### Performance Recovery Verification

```bash
# Load testing after recovery
./scripts/testing/post_recovery_load_test.sh \
  --duration 30m \
  --load-pattern gradual \
  --max-rps 1000 \
  --monitor-degradation

# Latency verification
./scripts/testing/latency_verification.sh \
  --p95-threshold 500ms \
  --p99-threshold 1000ms \
  --duration 15m

# Error rate verification
./scripts/testing/error_rate_verification.sh \
  --max-error-rate 0.1% \
  --duration 15m \
  --alert-on-exceed
```

### Data Consistency Verification

```bash
# Database integrity check
./scripts/database/integrity_check.sh \
  --comprehensive \
  --report-inconsistencies

# Cache consistency verification
./scripts/cache/consistency_check.sh \
  --compare-with-database \
  --fix-inconsistencies

# Cross-service data consistency
./scripts/testing/cross_service_data_consistency.sh \
  --verify-analysis-results \
  --verify-pattern-cache \
  --verify-query-history
```

## Recovery Time Tracking

### Recovery Metrics Collection

```bash
# Track recovery metrics
./scripts/metrics/recovery_metrics.sh \
  --incident-id "INC-2025-001" \
  --start-time "2025-01-16T10:00:00Z" \
  --detection-time "2025-01-16T10:05:00Z" \
  --mitigation-time "2025-01-16T10:20:00Z" \
  --resolution-time "2025-01-16T10:35:00Z"

# Generate recovery report
./scripts/reporting/recovery_report.sh \
  --incident-id "INC-2025-001" \
  --include-timeline \
  --include-metrics \
  --include-lessons-learned
```

---

**Document Owner**: Platform SRE Team  
**Review Frequency**: Quarterly  
**Last Tested**: 2025-01-16  
**Next Review**: 2025-04-16

> **Critical**: All recovery procedures should be tested regularly during planned maintenance windows. Never execute recovery procedures in production without proper authorization and backup verification.