# Analysis Engine Streaming Parser Enhancement

**Created**: 2025-08-01  
**Priority**: High  
**Complexity**: Medium-High  
**Personas**: Backend, Performance  
**MCP Servers**: Sequential, Context7  

## Goal

Enhance the existing analysis engine streaming parser to achieve production-grade performance, reliability, and scalability for processing large codebases (>1M LOC) with improved memory efficiency, backpressure management, and real-time progress tracking.

## Why - Business Value

- **Performance Leadership**: Maintain competitive advantage with 67,900+ LOC/s throughput (20x minimum requirement)
- **Enterprise Scalability**: Support analysis of massive codebases (9.1M+ LOC validated) without memory exhaustion
- **Real-time Insights**: Provide live progress tracking and metrics for long-running analyses
- **Reliability**: 99.9% uptime with graceful degradation and error recovery
- **Cost Efficiency**: Optimize resource usage for Cloud Run deployment (4GB memory limit)

## What - Technical Requirements

### Enhanced Streaming Capabilities
- **Progressive Analysis**: Stream AST chunks as they're parsed rather than batch processing
- **Memory Bounded**: Maintain <4GB memory usage regardless of codebase size
- **Backpressure Management**: Intelligent throttling when downstream systems are overwhelmed
- **Real-time Progress**: WebSocket-based progress updates with detailed metrics
- **Error Recovery**: Graceful handling of parser failures without stopping entire analysis

### Performance Requirements
- **Throughput**: Maintain >67,900 LOC/s performance with streaming enhancements
- **Latency**: <100ms first-chunk response time for streaming requests
- **Memory**: <4GB peak usage for analyses up to 10M LOC
- **Concurrent**: Support 50+ simultaneous streaming analyses
- **Recovery**: <5 second recovery from parser errors

### Success Criteria
- [ ] Stream first AST chunk within 100ms of request
- [ ] Maintain 67,900+ LOC/s throughput in streaming mode
- [ ] Memory usage stays below 4GB for 10M LOC analysis
- [ ] 99.9% analysis completion rate with error recovery
- [ ] WebSocket progress updates with <1s latency
- [ ] Graceful handling of client disconnections
- [ ] Integration with existing Redis caching layer

## All Needed Context

### Documentation & References
```yaml
research_docs:
  - file: research/rust/performance/tokio-async-performance-patterns.md
    why: Async streaming performance patterns and production optimization
  - file: research/google-cloud/spanner/production-patterns.md
    why: Database integration patterns for streaming analytics
  - file: research/rust/ffi-safety/tree-sitter-ffi-patterns.md
    why: Safe FFI patterns for tree-sitter integration in streaming context

examples:
  - file: examples/analysis-engine/ast_parser.rs
    why: Existing parser patterns and parallel processing
  - file: services/analysis-engine/src/parser/streaming/
    why: Current streaming implementation foundation
  - file: services/analysis-engine/src/api/handlers/websocket.rs
    why: WebSocket handling patterns for real-time updates

official_docs:
  - url: https://tokio.rs/tokio/tutorial/streams
    section: Async streams and backpressure
    critical: Stream adapter patterns and error handling
  - url: https://docs.rs/tokio-stream/latest/tokio_stream/
    section: Stream utilities and combinators
    critical: Throttling and buffering patterns
```

### Current Codebase Structure
```bash
services/analysis-engine/src/
├── parser/
│   ├── streaming/              # Current streaming implementation
│   │   ├── file_processor.rs  # StreamingFileProcessor base
│   │   ├── memory_monitor.rs  # Memory usage tracking
│   │   ├── progress_reporter.rs # Progress tracking
│   │   └── hasher.rs          # Content hashing
│   ├── parser_pool.rs         # Parser instance management
│   └── language_registry.rs   # Multi-language support
├── api/
│   ├── handlers/
│   │   ├── analysis.rs        # Batch analysis endpoints
│   │   └── websocket.rs       # Real-time communication
│   └── middleware/            # Auth, rate limiting, metrics
├── services/analyzer/         # Analysis orchestration
├── storage/                   # Spanner, Redis integration
└── metrics/                   # Prometheus monitoring
```

### Desired Codebase Changes
```bash
services/analysis-engine/src/
├── parser/
│   ├── streaming/
│   │   ├── enhanced_processor.rs    # Enhanced StreamingFileProcessor
│   │   ├── backpressure_manager.rs  # Intelligent throttling
│   │   ├── progress_streamer.rs     # WebSocket progress streaming
│   │   ├── chunk_buffer.rs          # Memory-bounded chunk buffering
│   │   └── error_recovery.rs        # Graceful error handling
│   └── stream_coordinator.rs        # Multi-stream orchestration
├── api/handlers/
│   └── streaming_analysis.rs        # New streaming analysis endpoints
└── models/
    └── streaming.rs                  # Streaming-specific data models
```

### Known Gotchas & Library Quirks
```rust
// CRITICAL: Memory management in streaming context
// Tree-sitter ASTs can consume significant memory - must be dropped promptly
// Existing pattern: ContentChunk needs size limits and lifecycle management

// CRITICAL: Tokio stream backpressure
// Must use bounded channels and implement proper backpressure signals
// Without this, memory usage can spike beyond 4GB limit

// CRITICAL: WebSocket connection management
// Client disconnections during long analyses must be handled gracefully
// Need to implement cleanup of resources when clients disconnect

// CRITICAL: Redis AST caching integration
// Streaming must leverage existing content hash-based caching
// Cache hits should bypass parsing entirely for repeated content
```

## Implementation Blueprint

### Data Models and Structure
```rust
// Enhanced streaming models building on existing ContentChunk
#[derive(Debug, Clone)]
pub struct StreamingChunk {
    pub id: ChunkId,
    pub file_path: String,
    pub content_hash: String,
    pub ast_nodes: Vec<AstNode>,
    pub metrics: ChunkMetrics,
    pub progress: ProgressInfo,
    pub error: Option<ParseError>,
}

#[derive(Debug)]
pub struct StreamingAnalysisRequest {
    pub analysis_id: String,
    pub repository_path: String,
    pub languages: Vec<String>,
    pub streaming_config: StreamingConfig,
    pub progress_callback: Option<WebSocketSender>,
}

#[derive(Debug)]
pub struct StreamingConfig {
    pub chunk_size_bytes: usize,        // Default: 64KB
    pub max_memory_mb: usize,           // Default: 3GB (safety buffer)
    pub backpressure_threshold: f32,    // Default: 0.8 (80% buffer)
    pub progress_interval_ms: u64,      // Default: 1000ms
    pub error_recovery_enabled: bool,   // Default: true
}
```

### Task List - Implementation Order
```yaml
Task 1: "Enhance existing streaming foundation"
  - MODIFY src/parser/streaming/file_processor.rs
  - ADD memory-bounded chunk processing
  - IMPLEMENT backpressure signaling
  - PATTERN: Build on existing StreamingFileProcessor

Task 2: "Implement backpressure management"
  - CREATE src/parser/streaming/backpressure_manager.rs
  - IMPLEMENT intelligent throttling based on memory usage
  - ADD downstream system monitoring
  - PATTERN: Use tokio::sync::Semaphore for flow control

Task 3: "Add WebSocket progress streaming"
  - CREATE src/parser/streaming/progress_streamer.rs
  - IMPLEMENT real-time progress updates
  - INTEGRATE with existing WebSocket infrastructure
  - PATTERN: Follow existing websocket.rs patterns

Task 4: "Enhance error recovery"
  - CREATE src/parser/streaming/error_recovery.rs
  - IMPLEMENT graceful parser failure handling
  - ADD automatic retry with exponential backoff
  - PATTERN: Circuit breaker pattern for failing parsers

Task 5: "Create streaming API endpoints"
  - CREATE src/api/handlers/streaming_analysis.rs
  - IMPLEMENT /api/v1/stream/analyze endpoint
  - ADD authentication and rate limiting
  - PATTERN: Follow existing analysis.rs handler patterns

Task 6: "Integrate with caching layer"
  - MODIFY streaming components to use Redis AST cache
  - IMPLEMENT cache-first streaming logic
  - ADD cache warming for large analyses
  - PATTERN: Follow existing Redis integration in storage/

Task 7: "Add comprehensive monitoring"
  - ADD Prometheus metrics for streaming operations
  - IMPLEMENT detailed performance tracking
  - ADD alerting for memory and performance thresholds
  - PATTERN: Follow existing metrics/ patterns

Task 8: "Performance optimization"
  - IMPLEMENT parallel chunk processing
  - ADD adaptive chunk sizing based on content type
  - OPTIMIZE memory allocation patterns
  - VALIDATE >67,900 LOC/s throughput maintained
```

### Per-Task Implementation Details
```rust
// Task 1: Enhanced StreamingFileProcessor
pub struct EnhancedStreamingProcessor {
    base_processor: StreamingFileProcessor,
    backpressure_manager: BackpressureManager,
    progress_streamer: ProgressStreamer,
    config: StreamingConfig,
    memory_monitor: MemoryMonitor,
}

impl EnhancedStreamingProcessor {
    pub async fn process_stream<S>(&mut self, 
        stream: S,
        progress_tx: Option<WebSocketSender>
    ) -> impl Stream<Item = Result<StreamingChunk, ProcessingError>>
    where S: Stream<Item = ContentChunk>
    {
        stream
            .map(|chunk| self.process_chunk(chunk))
            .buffer_unordered(self.config.max_concurrent_chunks)
            .throttle(self.backpressure_manager.compute_delay())
            .map(|result| {
                if let Some(ref tx) = progress_tx {
                    self.send_progress(tx, &result);
                }
                result
            })
    }
    
    async fn process_chunk(&mut self, chunk: ContentChunk) -> Result<StreamingChunk, ProcessingError> {
        // Check memory pressure before processing
        if self.memory_monitor.usage_percentage() > self.config.backpressure_threshold {
            return Err(ProcessingError::BackpressureLimit);
        }
        
        // Try cache first
        if let Some(cached_ast) = self.try_cache_lookup(&chunk.content_hash).await? {
            return Ok(self.create_streaming_chunk(chunk, cached_ast));
        }
        
        // Parse with error recovery
        match self.parse_with_recovery(&chunk).await {
            Ok(ast) => {
                // Cache the result
                self.cache_ast(&chunk.content_hash, &ast).await?;
                Ok(self.create_streaming_chunk(chunk, ast))
            },
            Err(e) => {
                // Return chunk with error info instead of failing entire stream
                Ok(StreamingChunk {
                    id: chunk.id,
                    file_path: chunk.file_path,
                    content_hash: chunk.content_hash,
                    ast_nodes: vec![],
                    metrics: ChunkMetrics::default(),
                    progress: self.compute_progress(),
                    error: Some(e),
                })
            }
        }
    }
}

// Task 2: Backpressure Management
pub struct BackpressureManager {
    memory_threshold: f32,
    buffer_threshold: f32,
    current_load: Arc<AtomicF32>,
    last_adjustment: Instant,
}

impl BackpressureManager {
    pub fn compute_delay(&self) -> Duration {
        let load = self.current_load.load(Ordering::Relaxed);
        if load > self.buffer_threshold {
            // Exponential backoff based on load
            let delay_ms = ((load - self.buffer_threshold) * 1000.0) as u64;
            Duration::from_millis(delay_ms.min(5000)) // Max 5s delay
        } else {
            Duration::from_millis(0)
        }
    }
    
    pub async fn update_load_metrics(&self, memory_usage: f32, buffer_usage: f32) {
        let combined_load = (memory_usage + buffer_usage) / 2.0;
        self.current_load.store(combined_load, Ordering::Relaxed);
    }
}
```

### Integration Points
```yaml
REDIS_CACHE:
  - integration: "Extend existing content hash-based caching"
  - pattern: "Cache-first lookup, async cache population"
  - performance: "20-30% improvement for repeated content"

WEBSOCKET_PROGRESS:
  - integration: "Extend existing WebSocket infrastructure"
  - pattern: "Broadcast progress updates to subscribed clients"
  - frequency: "1-second intervals with throttling"

SPANNER_ANALYTICS:
  - integration: "Stream analysis results to Spanner"
  - pattern: "Batch inserts of streaming chunks"
  - consistency: "Eventual consistency acceptable for analytics"

PROMETHEUS_METRICS:
  - integration: "Extend existing metrics collection"
  - metrics: "streaming_chunks_processed, memory_usage, backpressure_events"
  - alerting: "Memory threshold, throughput degradation alerts"
```

## Validation Loop

### Level 1: Syntax & Style
```bash
# CRITICAL: Must pass before proceeding
cargo fmt                                    # Format all code
cargo clippy -- -D warnings                 # Zero warnings policy
cargo check --all-targets                   # Type checking

# Expected: No errors. Fix any issues before continuing.
```

### Level 2: Unit Tests
```rust
#[cfg(test)]
mod tests {
    use super::*;
    use tokio_test;
    
    #[tokio::test]
    async fn test_streaming_processor_memory_bounded() {
        let config = StreamingConfig {
            max_memory_mb: 100, // Small limit for testing
            ..Default::default()
        };
        
        let mut processor = EnhancedStreamingProcessor::new(config);
        let large_chunks = create_large_test_chunks(1000); // Should exceed memory
        
        let results: Vec<_> = processor
            .process_stream(stream::iter(large_chunks), None)
            .collect()
            .await;
        
        // Should handle backpressure without OOM
        assert!(results.iter().any(|r| matches!(r, Err(ProcessingError::BackpressureLimit))));
        
        // Memory usage should stay bounded
        assert!(processor.memory_monitor.peak_usage_mb() <= 150); // Some overhead allowed
    }
    
    #[tokio::test]
    async fn test_error_recovery_continues_stream() {
        let mut processor = EnhancedStreamingProcessor::new(StreamingConfig::default());
        let mixed_chunks = vec![
            create_valid_chunk("good1.rs"),
            create_invalid_chunk("bad.rs"),    // Should fail to parse
            create_valid_chunk("good2.rs"),
        ];
        
        let results: Vec<_> = processor
            .process_stream(stream::iter(mixed_chunks), None)
            .collect()
            .await;
        
        assert_eq!(results.len(), 3);
        assert!(results[0].is_ok());
        assert!(results[1].as_ref().unwrap().error.is_some()); // Error recorded but stream continues
        assert!(results[2].is_ok());
    }
    
    #[tokio::test]
    async fn test_websocket_progress_updates() {
        let (progress_tx, mut progress_rx) = mpsc::channel(100);
        let mut processor = EnhancedStreamingProcessor::new(StreamingConfig::default());
        
        // Process chunks with progress updates
        let chunks = create_test_chunks(10);
        let _results: Vec<_> = processor
            .process_stream(stream::iter(chunks), Some(progress_tx))
            .collect()
            .await;
        
        // Should receive multiple progress updates
        let mut update_count = 0;
        while progress_rx.try_recv().is_ok() {
            update_count += 1;
        }
        assert!(update_count >= 5); // Should get updates during processing
    }
}
```

### Level 3: Integration Test
```bash
# Start the enhanced analysis engine
cargo run --bin analysis-engine

# Test streaming analysis endpoint
curl -X POST http://localhost:8001/api/v1/stream/analyze \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer test-token" \
  -d '{
    "repository_path": "/test-data/large-repo",
    "languages": ["rust", "python"],
    "streaming_config": {
      "chunk_size_bytes": 65536,
      "max_memory_mb": 3072,
      "progress_interval_ms": 1000
    }
  }'

# Expected: Stream of JSON chunks, memory stays <4GB, progress updates via WebSocket
```

### Level 4: Performance & Load Testing
```bash
# Test with large repository (>1M LOC)
./test_streaming_performance.sh /test-data/large-repos/rust-lang-rust

# Expected results:
# - First chunk within 100ms
# - Sustained >67,900 LOC/s throughput
# - Memory usage <4GB throughout
# - Graceful handling of backpressure

# Concurrent streaming test
./test_concurrent_streaming.sh 50 # 50 simultaneous streams

# WebSocket connection resilience test
./test_websocket_resilience.sh
```

## Final Validation Checklist
- [ ] All tests pass: `cargo test --all`
- [ ] Zero warnings: `cargo clippy -- -D warnings`
- [ ] Streaming performance: >67,900 LOC/s maintained
- [ ] Memory bounded: <4GB for 10M LOC analysis
- [ ] First chunk latency: <100ms
- [ ] Error recovery: Stream continues after parser failures
- [ ] WebSocket progress: Real-time updates working
- [ ] Cache integration: Redis caching provides 20-30% improvement
- [ ] Concurrent handling: 50+ simultaneous streams supported
- [ ] Graceful degradation: Handles client disconnections

## Anti-Patterns to Avoid
- ❌ Don't accumulate ASTs in memory - stream and drop promptly
- ❌ Don't ignore backpressure signals - implement proper throttling
- ❌ Don't fail entire stream on single parser error - isolate failures
- ❌ Don't block async operations - use proper async/await patterns
- ❌ Don't hardcode buffer sizes - make them configurable
- ❌ Don't ignore WebSocket disconnections - clean up resources
- ❌ Don't skip progress updates - users need feedback for long operations
- ❌ Don't bypass existing caching - leverage Redis for performance