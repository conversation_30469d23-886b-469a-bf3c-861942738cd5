# ✅ Analysis Engine - Deployment Checklist

**Service**: Analysis Engine  
**Last Updated**: 2025-08-06  
**Purpose**: Ensure consistent, safe, and complete deployments

## 📋 Pre-Deployment Checklist

### 🔍 Code Review & Testing
- [ ] **Code Review**
  - [ ] PR approved by 2+ team members
  - [ ] Security review completed if security-related changes
  - [ ] Performance impact assessed
  - [ ] Breaking changes documented

- [ ] **Testing**
  - [ ] Unit tests passing (`cargo test`)
  - [ ] Integration tests passing (`cargo test --features integration`)
  - [ ] Performance benchmarks run (`cargo bench`)
  - [ ] Security scan completed (`cargo audit`)
  - [ ] No hardcoded secrets (`./scripts/security/validate-no-hardcoded-secrets.sh`)

- [ ] **Documentation**
  - [ ] API changes documented
  - [ ] Configuration changes noted
  - [ ] Migration guide written (if needed)
  - [ ] CHANGELOG.md updated

### 🏗️ Infrastructure Preparation
- [ ] **Dependencies**
  - [ ] Spanner schema migrations reviewed
  - [ ] Redis compatibility verified
  - [ ] External API versions checked
  - [ ] Infrastructure capacity verified

- [ ] **Configuration**
  - [ ] Environment variables documented
  - [ ] Secrets rotated if needed
  - [ ] Feature flags configured
  - [ ] Resource limits appropriate

- [ ] **Monitoring**
  - [ ] New metrics added to dashboards
  - [ ] Alert thresholds reviewed
  - [ ] Log formats validated
  - [ ] Error tracking configured

### 📦 Build Preparation
- [ ] **Version Management**
  - [ ] Version number incremented
  - [ ] Git tag created
  - [ ] Release notes drafted
  - [ ] Dependencies locked (`Cargo.lock`)

- [ ] **Build Verification**
  ```bash
  # Clean build
  cargo clean
  cargo build --release
  
  # Docker build
  docker build -t analysis-engine:test .
  
  # Vulnerability scan
  docker scan analysis-engine:test
  ```

## 🚀 Deployment Execution Checklist

### 📝 Pre-Deployment Steps
- [ ] **Communication**
  - [ ] Deployment announced in #analysis-engine-deploys
  - [ ] Maintenance window scheduled (if needed)
  - [ ] Stakeholders notified
  - [ ] Status page updated

- [ ] **Backup Current State**
  ```bash
  # Backup current configuration
  gcloud run services describe analysis-engine \
    --region=us-central1 \
    --export > backup/analysis-engine-$(date +%Y%m%d-%H%M%S).yaml
  
  # Record current revision
  CURRENT_REVISION=$(gcloud run services describe analysis-engine \
    --region=us-central1 \
    --format="value(spec.template.metadata.name)")
  echo "Current revision: $CURRENT_REVISION" > backup/current-revision.txt
  ```

### 🔧 Deployment Process
- [ ] **Build & Push**
  ```bash
  # Build production image
  docker build \
    --platform linux/amd64 \
    -t gcr.io/vibe-match-463114/analysis-engine:v${VERSION} \
    -t gcr.io/vibe-match-463114/analysis-engine:latest \
    -f Dockerfile.simple .
  
  # Push to registry
  docker push gcr.io/vibe-match-463114/analysis-engine:v${VERSION}
  docker push gcr.io/vibe-match-463114/analysis-engine:latest
  ```

- [ ] **Deploy to Staging** (if applicable)
  ```bash
  # Deploy to staging environment
  gcloud run deploy analysis-engine-staging \
    --image gcr.io/vibe-match-463114/analysis-engine:v${VERSION} \
    --region=us-central1
  
  # Run staging tests
  ./scripts/test-staging.sh
  ```

- [ ] **Deploy to Production**
  ```bash
  # Deploy with traffic management
  gcloud run deploy analysis-engine \
    --image gcr.io/vibe-match-463114/analysis-engine:v${VERSION} \
    --region=us-central1 \
    --no-traffic
  
  # Get new revision name
  NEW_REVISION=$(gcloud run services describe analysis-engine \
    --region=us-central1 \
    --format="value(status.latestCreatedRevisionName)")
  ```

### 🔄 Traffic Migration
- [ ] **Gradual Rollout**
  ```bash
  # 10% canary
  gcloud run services update-traffic analysis-engine \
    --to-revisions=${NEW_REVISION}=10 \
    --region=us-central1
  
  # Monitor for 10 minutes
  sleep 600
  
  # 50% traffic
  gcloud run services update-traffic analysis-engine \
    --to-revisions=${NEW_REVISION}=50 \
    --region=us-central1
  
  # Monitor for 10 minutes
  sleep 600
  
  # 100% traffic
  gcloud run services update-traffic analysis-engine \
    --to-revisions=${NEW_REVISION}=100 \
    --region=us-central1
  ```

## ✅ Post-Deployment Checklist

### 🔍 Immediate Validation (0-5 minutes)
- [ ] **Health Checks**
  ```bash
  # Basic health
  curl https://analysis-engine-572735000332.us-central1.run.app/health
  
  # Detailed health
  curl https://analysis-engine-572735000332.us-central1.run.app/health/detailed
  
  # Dependency checks
  curl https://analysis-engine-572735000332.us-central1.run.app/health/ready
  ```

- [ ] **Functionality Tests**
  ```bash
  # API functionality
  ./scripts/smoke-test.sh
  
  # Critical paths
  ./scripts/test-critical-paths.sh
  ```

- [ ] **Metrics Verification**
  - [ ] Error rate normal (<1%)
  - [ ] Latency acceptable (<200ms P95)
  - [ ] Memory usage stable
  - [ ] CPU usage normal

### 📊 Extended Monitoring (5-30 minutes)
- [ ] **Performance Monitoring**
  - [ ] Response times stable
  - [ ] Cache hit rates normal
  - [ ] Database query times acceptable
  - [ ] No memory leaks detected

- [ ] **Error Monitoring**
  ```bash
  # Watch error logs
  gcloud logging tail "severity>=ERROR" --format="value(textPayload)"
  
  # Check error patterns
  gcloud logging read "severity>=ERROR AND timestamp>=\"$(date -u -d '30 minutes ago' +%Y-%m-%dT%H:%M:%SZ)\"" \
    --format="table(timestamp,jsonPayload.error_type)"
  ```

- [ ] **Business Metrics**
  - [ ] Analysis completion rate normal
  - [ ] API usage patterns expected
  - [ ] No customer complaints
  - [ ] SLO/SLI metrics green

### 📝 Documentation & Cleanup
- [ ] **Update Documentation**
  - [ ] Deployment log created
  - [ ] Runbook updates applied
  - [ ] Known issues documented
  - [ ] Team wiki updated

- [ ] **Cleanup**
  ```bash
  # Remove old revisions (keep last 3)
  gcloud run revisions list \
    --service=analysis-engine \
    --region=us-central1 \
    --format="value(name)" \
    --filter="metadata.generation<$(($(gcloud run services describe analysis-engine --region=us-central1 --format="value(metadata.generation)") - 3))" | \
    xargs -I {} gcloud run revisions delete {} --region=us-central1 --quiet
  ```

- [ ] **Communication**
  - [ ] Deployment success announced
  - [ ] Stakeholders notified
  - [ ] Status page updated
  - [ ] Release notes published

## 🚨 Rollback Checklist

### Quick Rollback Decision Criteria
- [ ] Error rate >5% for 5 minutes
- [ ] P95 latency >1s for 5 minutes
- [ ] Critical functionality broken
- [ ] Data corruption detected
- [ ] Security vulnerability exposed

### Rollback Procedure
- [ ] **Immediate Actions**
  ```bash
  # Rollback traffic
  gcloud run services update-traffic analysis-engine \
    --to-revisions=${CURRENT_REVISION}=100 \
    --region=us-central1
  
  # Verify rollback
  curl https://analysis-engine-572735000332.us-central1.run.app/api/v1/version
  ```

- [ ] **Post-Rollback**
  - [ ] Incident report started
  - [ ] Root cause investigation begun
  - [ ] Fix planned and tested
  - [ ] Stakeholders notified

## 📊 Deployment Metrics

### Success Criteria
- **Deployment Duration**: <30 minutes
- **Rollback Time**: <5 minutes
- **Zero Downtime**: ✓
- **Error Rate**: <0.1% increase
- **Performance Impact**: <10% degradation

### Key Metrics to Track
```sql
-- Deployment impact query
SELECT 
  deployment_id,
  start_time,
  end_time,
  TIMESTAMP_DIFF(end_time, start_time, MINUTE) as duration_minutes,
  error_rate_change,
  latency_p95_change,
  rollback_required
FROM deployment_history
WHERE deployment_date > TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY)
ORDER BY start_time DESC;
```

## 🛠️ Automation Scripts

### Full Deployment Script
```bash
#!/bin/bash
# scripts/deploy-with-checklist.sh

set -e

VERSION=$1
if [ -z "$VERSION" ]; then
  echo "Usage: $0 <version>"
  exit 1
fi

echo "🚀 Starting deployment of version $VERSION"

# Pre-deployment checks
./scripts/pre-deploy-check.sh || exit 1

# Build and push
./scripts/build-and-push.sh $VERSION || exit 1

# Deploy with canary
./scripts/canary-deploy.sh $VERSION || exit 1

# Validate deployment
./scripts/post-deploy-validation.sh || exit 1

echo "✅ Deployment complete!"
```

---

**Remember**: This checklist is a living document. Update it based on lessons learned from each deployment.