# 🔌 Analysis Engine - WebSocket API Documentation

> **Context Engineering**: This documentation covers the WebSocket APIs for real-time analysis progress updates, including both standard and streaming analysis WebSocket endpoints.

## 📋 Overview

The Analysis Engine provides two WebSocket APIs for real-time progress updates:

1. **Standard Analysis WebSocket** - Progress updates for batch analysis jobs
2. **Streaming Analysis WebSocket** - Real-time updates for streaming analysis with enhanced metrics

Both WebSocket endpoints require authentication and provide low-latency updates (<100ms).

## 🔐 Authentication

WebSocket connections require authentication via query parameter:

```
wss://analysis-engine-572735000332.us-central1.run.app/ws/analysis/{analysis_id}?token={jwt_token}
```

The JWT token must be valid and have permissions to access the specified analysis.

## 📡 Standard Analysis WebSocket

### Endpoint
```websocket
wss://analysis-engine-572735000332.us-central1.run.app/ws/analysis/{analysis_id}
```

### Connection Example

```javascript
const analysisId = '550e8400-e29b-41d4-a716-446655440000';
const token = 'your-jwt-token';
const ws = new WebSocket(
  `wss://analysis-engine-572735000332.us-central1.run.app/ws/analysis/${analysisId}?token=${token}`
);

ws.onopen = () => {
  console.log('Connected to analysis progress stream');
};

ws.onmessage = (event) => {
  const update = JSON.parse(event.data);
  handleProgressUpdate(update);
};

ws.onerror = (error) => {
  console.error('WebSocket error:', error);
};

ws.onclose = (event) => {
  console.log('Connection closed:', event.code, event.reason);
};
```

### Message Types

#### Progress Update
```json
{
  "type": "progress",
  "stage": "parsing_files",
  "progress": 45,
  "message": "Parsing file 450 of 1000",
  "timestamp": "2025-08-06T12:02:30Z"
}
```

#### Stage Complete
```json
{
  "type": "stage_complete",
  "stage": "parsing_files",
  "duration_seconds": 45,
  "files_processed": 1000,
  "timestamp": "2025-08-06T12:03:00Z"
}
```

#### Error
```json
{
  "type": "error",
  "stage": "security_analysis",
  "error_code": "SCAN_TIMEOUT",
  "message": "Security scan timed out after 300 seconds",
  "recoverable": true,
  "timestamp": "2025-08-06T12:05:00Z"
}
```

#### Analysis Complete
```json
{
  "type": "analysis_complete",
  "analysis_id": "550e8400-e29b-41d4-a716-446655440000",
  "status": "completed",
  "total_duration_seconds": 180,
  "result_url": "/api/v1/analysis/550e8400-e29b-41d4-a716-446655440000",
  "timestamp": "2025-08-06T12:06:00Z"
}
```

### Stages

The analysis progresses through these stages:

1. `repository_clone` - Cloning the repository
2. `language_detection` - Detecting programming languages
3. `file_collection` - Collecting files to analyze
4. `parsing_files` - Parsing source code
5. `pattern_detection` - Detecting code patterns
6. `security_analysis` - Running security scans
7. `quality_metrics` - Calculating quality metrics
8. `generating_results` - Generating final results

## ⚡ Streaming Analysis WebSocket

### Endpoint
```websocket
wss://analysis-engine-572735000332.us-central1.run.app/api/v1/stream/progress/{analysis_id}
```

### Connection Example

```javascript
const analysisId = '550e8400-e29b-41d4-a716-446655440000';
const token = 'your-jwt-token';
const ws = new WebSocket(
  `wss://analysis-engine-572735000332.us-central1.run.app/api/v1/stream/progress/${analysisId}?token=${token}`
);

ws.onmessage = (event) => {
  const update = JSON.parse(event.data);
  
  switch (update.type) {
    case 'chunk_processed':
      updateProgressBar(update.progress.progress_percent);
      updateThroughput(update.metrics.throughput_loc_per_sec);
      break;
      
    case 'performance_update':
      updatePerformanceMetrics(update);
      break;
      
    case 'streaming_complete':
      handleCompletion(update);
      break;
  }
};
```

### Enhanced Message Types

#### Chunk Processed
```json
{
  "type": "chunk_processed",
  "chunk_id": {
    "file_path": "src/main.rs",
    "offset": 1024,
    "size": 2048
  },
  "metrics": {
    "parse_duration_ms": 45,
    "ast_node_count": 234,
    "cache_hit": true,
    "throughput_loc_per_sec": 67500
  },
  "progress": {
    "bytes_processed": 2883584,
    "total_bytes": 10485760,
    "chunks_completed": 45,
    "progress_percent": 28.8,
    "estimated_completion_ms": 4200,
    "memory_usage_percent": 0.45
  },
  "timestamp": "2025-08-06T12:02:30.123Z"
}
```

#### Performance Update
```json
{
  "type": "performance_update",
  "throughput": {
    "current_loc_per_sec": 67500,
    "average_loc_per_sec": 65200,
    "peak_loc_per_sec": 71000
  },
  "cache": {
    "hit_rate": 0.73,
    "miss_rate": 0.27,
    "total_lookups": 156
  },
  "backpressure": {
    "active": false,
    "memory_threshold": 0.8,
    "current_usage": 0.45
  },
  "timestamp": "2025-08-06T12:02:30.456Z"
}
```

#### Memory Warning
```json
{
  "type": "memory_warning",
  "current_usage_percent": 0.85,
  "threshold_percent": 0.8,
  "action": "backpressure_activated",
  "message": "Memory usage high, activating backpressure",
  "timestamp": "2025-08-06T12:02:45.789Z"
}
```

#### Streaming Complete
```json
{
  "type": "streaming_complete",
  "analysis_id": "550e8400-e29b-41d4-a716-446655440000",
  "status": "completed",
  "total_chunks": 156,
  "total_duration_ms": 5643,
  "average_throughput_loc_per_sec": 66890,
  "cache_hit_rate": 0.73,
  "result_url": "/api/v1/analysis/550e8400-e29b-41d4-a716-446655440000",
  "timestamp": "2025-08-06T12:03:05.789Z"
}
```

## 🔒 Security Features

### WebSocket Rate Limiting

The WebSocket endpoints implement rate limiting to prevent abuse:

- **Message Rate**: 60 messages per minute
- **Cursor Updates**: 20 cursor position updates per second
- **Connection Limit**: 5 concurrent WebSocket connections per user

### Input Validation

All incoming WebSocket messages are validated:
- Maximum message size: 64KB
- JSON schema validation
- SQL injection prevention
- XSS protection

### Connection Security

- **TLS Required**: All WebSocket connections must use WSS (WebSocket Secure)
- **Origin Validation**: CORS headers are checked
- **Token Expiration**: Connections are closed when JWT expires
- **Idle Timeout**: Connections idle for 5 minutes are closed

## 🛠️ Client Implementation

### JavaScript/Browser

```javascript
class AnalysisProgressTracker {
  constructor(analysisId, token) {
    this.analysisId = analysisId;
    this.token = token;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
  }

  connect() {
    const url = `wss://analysis-engine-572735000332.us-central1.run.app/ws/analysis/${this.analysisId}?token=${this.token}`;
    this.ws = new WebSocket(url);
    
    this.ws.onopen = () => {
      console.log('Connected');
      this.reconnectAttempts = 0;
    };
    
    this.ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      this.handleMessage(data);
    };
    
    this.ws.onerror = (error) => {
      console.error('WebSocket error:', error);
    };
    
    this.ws.onclose = (event) => {
      if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
        this.reconnect();
      }
    };
  }
  
  reconnect() {
    this.reconnectAttempts++;
    const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000);
    console.log(`Reconnecting in ${delay}ms...`);
    setTimeout(() => this.connect(), delay);
  }
  
  handleMessage(data) {
    switch (data.type) {
      case 'progress':
        this.onProgress(data);
        break;
      case 'error':
        this.onError(data);
        break;
      case 'analysis_complete':
        this.onComplete(data);
        break;
    }
  }
  
  onProgress(data) {
    // Override in subclass
  }
  
  onError(data) {
    // Override in subclass
  }
  
  onComplete(data) {
    // Override in subclass
  }
}
```

### Python

```python
import asyncio
import json
import websockets
from datetime import datetime

class AnalysisProgressTracker:
    def __init__(self, analysis_id, token):
        self.analysis_id = analysis_id
        self.token = token
        self.url = f"wss://analysis-engine-572735000332.us-central1.run.app/ws/analysis/{analysis_id}?token={token}"
    
    async def connect(self):
        async with websockets.connect(self.url) as websocket:
            await self.handle_messages(websocket)
    
    async def handle_messages(self, websocket):
        async for message in websocket:
            data = json.loads(message)
            await self.process_message(data)
    
    async def process_message(self, data):
        message_type = data.get('type')
        
        if message_type == 'progress':
            print(f"Progress: {data['progress']}% - {data['message']}")
        elif message_type == 'error':
            print(f"Error: {data['message']}")
        elif message_type == 'analysis_complete':
            print(f"Analysis complete! Results: {data['result_url']}")
            return  # Exit the connection

# Usage
async def main():
    tracker = AnalysisProgressTracker(
        analysis_id="550e8400-e29b-41d4-a716-446655440000",
        token="your-jwt-token"
    )
    await tracker.connect()

asyncio.run(main())
```

### Node.js

```javascript
const WebSocket = require('ws');

class AnalysisProgressTracker {
  constructor(analysisId, token) {
    this.analysisId = analysisId;
    this.token = token;
    this.url = `wss://analysis-engine-572735000332.us-central1.run.app/ws/analysis/${analysisId}?token=${token}`;
  }

  connect() {
    this.ws = new WebSocket(this.url);
    
    this.ws.on('open', () => {
      console.log('Connected to analysis progress stream');
    });
    
    this.ws.on('message', (data) => {
      const message = JSON.parse(data);
      this.handleMessage(message);
    });
    
    this.ws.on('error', (error) => {
      console.error('WebSocket error:', error);
    });
    
    this.ws.on('close', (code, reason) => {
      console.log(`Connection closed: ${code} - ${reason}`);
    });
  }
  
  handleMessage(message) {
    console.log(`[${message.type}] ${JSON.stringify(message, null, 2)}`);
    
    if (message.type === 'analysis_complete') {
      this.ws.close();
    }
  }
}

// Usage
const tracker = new AnalysisProgressTracker(
  '550e8400-e29b-41d4-a716-446655440000',
  'your-jwt-token'
);
tracker.connect();
```

## 📊 Performance Characteristics

### Latency
- **Initial Connection**: <200ms
- **Message Delivery**: <100ms (95th percentile)
- **Reconnection**: Exponential backoff starting at 1s

### Throughput
- **Messages per Second**: Up to 10 per connection
- **Bandwidth**: ~5KB/s average per connection
- **Concurrent Connections**: 1000+ supported

### Reliability
- **Automatic Reconnection**: Client-side implementation recommended
- **Message Ordering**: Guaranteed in-order delivery
- **Buffering**: 100 messages buffered during temporary disconnections

## 🚨 Error Handling

### Connection Errors

| Code | Reason | Description | Action |
|------|--------|-------------|--------|
| 1000 | Normal Closure | Analysis complete or cancelled | No action needed |
| 1001 | Going Away | Server shutting down | Reconnect to new server |
| 1003 | Unsupported Data | Invalid message format | Check client implementation |
| 1008 | Policy Violation | Authentication failed or rate limit | Check token and rate limits |
| 1011 | Internal Error | Server error | Retry with backoff |

### Best Practices

1. **Implement Reconnection Logic**: Use exponential backoff
2. **Handle Token Expiration**: Refresh tokens before reconnecting
3. **Buffer Important State**: Don't rely solely on WebSocket for state
4. **Graceful Degradation**: Fall back to polling if WebSocket fails
5. **Monitor Connection Health**: Implement heartbeat/ping-pong

## 📚 Additional Resources

- [API Reference](./reference.md) - Complete REST API documentation
- [Authentication Guide](./reference.md#authentication) - JWT token management
- [Rate Limiting](./reference.md#rate-limiting--csrf-protection) - Rate limit details