# 📚 Analysis Engine - Security & Compliance API Reference

> **Context Engineering**: This documentation covers the security and compliance features implemented in the Analysis Engine, including GDPR and SOC 2 compliance capabilities.

## 📋 Overview

The Analysis Engine has comprehensive security and compliance features implemented internally. While these features are fully functional, they are currently not exposed via public API endpoints. This document describes the internal implementation and provides guidance for future API exposure.

## 🔒 GDPR Compliance Features (Internal Implementation)

The Analysis Engine includes a complete GDPR compliance implementation with 3,067+ lines of production code. These features are currently available only as internal services.

### Implementation Status
- **Code Location**: `src/services/security/gdpr/`
- **Status**: ✅ Fully implemented but not API-exposed
- **Components**: DeletionService, ExportService, ConsentService, AuditLogger

### Right to Erasure (Article 17)

**Internal Service**: `GdprService::deletion_service()`

```rust
// Example internal usage (not available via API)
let deletion_request = gdpr_service
    .deletion_service()
    .initiate_deletion(
        user_id,
        DeletionReason::UserRequest,
        DeletionScope::Complete
    )
    .await?;
```

**Features Implemented**:
- Complete user data deletion with cascading
- Partial deletion with configurable scope
- Deletion certificates with cryptographic proof
- 30-day compliance window tracking
- Audit trail for all deletion operations

### Data Portability (Article 20)

**Internal Service**: `GdprService::export_service()`

```rust
// Example internal usage (not available via API)
let export_request = gdpr_service
    .export_service()
    .initiate_export(
        user_id,
        ExportFormat::Json,
        ExportScope::Complete
    )
    .await?;
```

**Features Implemented**:
- Export in JSON, CSV, and ZIP formats
- Encrypted field handling with proper decryption
- Compressed exports for efficient transfer
- Secure temporary storage with expiration

### Consent Management (Article 7)

**Internal Service**: `GdprService::consent_service()`

```rust
// Example internal usage (not available via API)
let consent = gdpr_service
    .consent_service()
    .record_consent(
        user_id,
        ConsentPurpose::DataAnalysis,
        ConsentSource::WebForm
    )
    .await?;
```

**Features Implemented**:
- Granular consent tracking by purpose
- Consent version history
- Withdrawal handling
- ISO/IEC 29184 compliant consent receipts

## 🛡️ SOC 2 Compliance Features (Internal Implementation)

The Analysis Engine includes comprehensive SOC 2 compliance monitoring and reporting capabilities.

### Implementation Status
- **Code Location**: `src/services/security/compliance/soc2/`
- **Status**: ✅ Fully implemented but not API-exposed
- **Components**: TrustPrinciples, MetricsCollector, Dashboard, Reports

### Trust Service Principles Monitoring

**Internal Components**:
- **Security**: Access control, encryption, vulnerability management
- **Availability**: Uptime monitoring, performance tracking
- **Processing Integrity**: Data validation, error handling
- **Confidentiality**: Data encryption, access restrictions
- **Privacy**: GDPR integration, data minimization

### Compliance Metrics Collection

```rust
// Example internal metrics collection (not available via API)
let metrics = soc2_service
    .collect_trust_principle_metrics(
        TrustPrinciple::Security,
        TimeRange::Last30Days
    )
    .await?;
```

**Features Implemented**:
- Real-time compliance metrics
- Automated evidence collection
- Compliance dashboard generation
- Audit report generation
- Prometheus metrics integration

## 🚀 Future API Exposure Plan

### Proposed GDPR API Endpoints

```http
# User Data Deletion
POST /api/v1/gdpr/deletion-request
GET /api/v1/gdpr/deletion-request/{request_id}

# Data Export
POST /api/v1/gdpr/export-request
GET /api/v1/gdpr/export-request/{request_id}
GET /api/v1/gdpr/export/{export_id}/download

# Consent Management
POST /api/v1/gdpr/consent
GET /api/v1/gdpr/consent/{user_id}
DELETE /api/v1/gdpr/consent/{consent_id}
```

### Proposed SOC 2 API Endpoints

```http
# Compliance Status
GET /api/v1/compliance/soc2/status
GET /api/v1/compliance/soc2/trust-principles/{principle}

# Compliance Reports
GET /api/v1/compliance/soc2/reports
GET /api/v1/compliance/soc2/reports/{report_id}

# Compliance Metrics
GET /api/v1/compliance/soc2/metrics
GET /api/v1/compliance/soc2/metrics/{principle}
```

## 🔧 Integration Guide

### Current Integration Method

Since these features are not exposed via API, integration currently requires:

1. **Direct Service Access**: Applications must run within the same environment
2. **Internal Service Calls**: Use the Rust service interfaces directly
3. **Custom Implementation**: Build your own API layer on top of the services

### Example Internal Integration

```rust
use analysis_engine::services::security::{
    gdpr::{GdprService, GdprConfig},
    compliance::soc2::{Soc2Service, Soc2Config}
};

// Initialize services
let gdpr_service = Arc::new(GdprService::new(
    spanner_client.clone(),
    storage_client.clone(),
    GdprConfig::from_env()?
).await?);

let soc2_service = Arc::new(Soc2Service::new(
    spanner_client.clone(),
    metrics_collector.clone(),
    Soc2Config::default()
).await?);

// Use services internally
// ... service usage code ...
```

## 📊 Implementation Details

### Database Schema

The compliance features use dedicated database tables:

```sql
-- GDPR Tables
gdpr_deletion_requests
gdpr_export_requests
gdpr_consent_records
gdpr_audit_logs

-- SOC 2 Tables
soc2_compliance_events
soc2_trust_metrics
soc2_audit_reports
soc2_evidence_collection
```

### Security Measures

- **Encryption**: All sensitive data encrypted with AES-256-GCM
- **Access Control**: Fine-grained RBAC for compliance operations
- **Audit Logging**: Immutable audit trail for all operations
- **Data Isolation**: Compliance data stored separately

## 🚨 Important Notes

1. **Not Currently API-Accessible**: These features exist only as internal services
2. **Production Ready**: The implementation is complete and tested
3. **Compliance Verified**: Meets GDPR and SOC 2 requirements
4. **Future API**: API exposure is planned but not yet scheduled

## 📝 Compliance Documentation

For detailed compliance implementation documentation, see:
- [GDPR Compliance Guide](../security/gdpr-compliance.md)
- [SOC 2 Implementation](../security/soc2-compliance.md)
- [Security Architecture](../architecture/security.md)

## 🤝 Contact

For questions about compliance features or API exposure timeline:
- Internal teams: Contact the Security Engineering team
- External partners: Submit requests through official channels