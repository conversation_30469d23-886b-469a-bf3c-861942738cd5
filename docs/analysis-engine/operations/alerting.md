# 🚨 Analysis Engine - Alerting & Incident Response Guide

**Service**: Analysis Engine  
**Last Updated**: 2025-08-06  
**Status**: ✅ Production Monitoring Active

## 📋 Table of Contents

1. [Alert Configuration](#alert-configuration)
2. [Alert Definitions](#alert-definitions)
3. [Notification Channels](#notification-channels)
4. [Incident Response Procedures](#incident-response-procedures)
5. [<PERSON><PERSON>](#alert-tuning)
6. [Runbook Links](#runbook-links)
7. [Alert History](#alert-history)

## 🎯 Alert Configuration

### Overview

The Analysis Engine uses a multi-tier alerting system with Google Cloud Monitoring, integrated with PagerDuty for critical alerts and Slack for informational notifications.

### Alert Severity Levels

| Severity | Response Time | Notification Method | Examples |
|----------|--------------|-------------------|----------|
| **P1 - Critical** | 5 minutes | PagerDuty + Slack + Email | Service down, data loss risk |
| **P2 - High** | 15 minutes | Email + Slack | High error rate, performance degradation |
| **P3 - Medium** | 1 hour | Email + Slack | Elevated latency, cache issues |
| **P4 - Low** | 4 hours | Email only | Minor issues, warnings |

## 📊 Alert Definitions

### P1 - Critical Alerts

#### 1. Service Down Alert
```yaml
name: analysis-engine-service-down
condition:
  display_name: "Analysis Engine - Service Down"
  condition_threshold:
    filter: |
      resource.type="cloud_run_revision"
      AND resource.labels.service_name="analysis-engine"
      AND metric.type="run.googleapis.com/request_count"
    comparison: COMPARISON_LT
    threshold_value: 1
    duration: 300s
    aggregations:
      - alignment_period: 60s
        per_series_aligner: ALIGN_RATE
notification_channels:
  - pagerduty_critical
  - slack_critical
  - email_oncall
documentation:
  content: |
    Service is not receiving any requests for 5 minutes.
    Runbook: https://docs.ccl.dev/analysis-engine/runbooks/service-down
```

#### 2. High Error Rate Alert
```yaml
name: analysis-engine-high-error-rate
condition:
  display_name: "Analysis Engine - High Error Rate"
  condition_threshold:
    filter: |
      resource.type="cloud_run_revision"
      AND resource.labels.service_name="analysis-engine"
      AND metric.type="run.googleapis.com/request_count"
      AND metric.labels.response_code_class="5xx"
    comparison: COMPARISON_GT
    threshold_value: 0.05  # 5% error rate
    duration: 300s
notification_channels:
  - pagerduty_critical
  - slack_critical
documentation:
  content: |
    Error rate exceeds 5% for 5 minutes.
    Runbook: https://docs.ccl.dev/analysis-engine/runbooks/high-error-rate
```

### P2 - High Priority Alerts

#### 3. High Memory Usage Alert
```yaml
name: analysis-engine-high-memory
condition:
  display_name: "Analysis Engine - High Memory Usage"
  condition_threshold:
    filter: |
      resource.type="cloud_run_revision"
      AND resource.labels.service_name="analysis-engine"
      AND metric.type="run.googleapis.com/container/memory/utilizations"
    comparison: COMPARISON_GT
    threshold_value: 0.85  # 85% memory
    duration: 600s
notification_channels:
  - email_team
  - slack_alerts
documentation:
  content: |
    Memory usage exceeds 85% for 10 minutes.
    Runbook: https://docs.ccl.dev/analysis-engine/runbooks/high-memory
```

#### 4. High Latency Alert
```yaml
name: analysis-engine-high-latency
condition:
  display_name: "Analysis Engine - High Latency"
  condition_threshold:
    filter: |
      resource.type="cloud_run_revision"
      AND resource.labels.service_name="analysis-engine"
      AND metric.type="run.googleapis.com/request_latencies"
    comparison: COMPARISON_GT
    threshold_value: 1000  # 1 second
    duration: 300s
    aggregations:
      - alignment_period: 60s
        per_series_aligner: ALIGN_PERCENTILE_95
notification_channels:
  - email_team
  - slack_alerts
documentation:
  content: |
    P95 latency exceeds 1 second for 5 minutes.
    Runbook: https://docs.ccl.dev/analysis-engine/runbooks/high-latency
```

### P3 - Medium Priority Alerts

#### 5. Cache Hit Rate Low
```yaml
name: analysis-engine-low-cache-hit-rate
condition:
  display_name: "Analysis Engine - Low Cache Hit Rate"
  condition_threshold:
    filter: |
      resource.type="cloud_run_revision"
      AND resource.labels.service_name="analysis-engine"
      AND metric.type="custom.googleapis.com/analysis_engine/cache_hit_rate"
    comparison: COMPARISON_LT
    threshold_value: 0.7  # 70% hit rate
    duration: 1800s
notification_channels:
  - email_team
documentation:
  content: |
    Cache hit rate below 70% for 30 minutes.
    May indicate cache issues or unusual traffic patterns.
```

#### 6. Parse Failure Rate High
```yaml
name: analysis-engine-parse-failures
condition:
  display_name: "Analysis Engine - High Parse Failure Rate"
  condition_threshold:
    filter: |
      resource.type="cloud_run_revision"
      AND resource.labels.service_name="analysis-engine"
      AND metric.type="custom.googleapis.com/analysis_engine/parse_success_rate"
    comparison: COMPARISON_LT
    threshold_value: 0.9  # 90% success rate
    duration: 900s
notification_channels:
  - email_team
  - slack_alerts
documentation:
  content: |
    Parse success rate below 90% for 15 minutes.
    Check for malformed input files or parser issues.
```

### P4 - Low Priority Alerts

#### 7. Elevated Authentication Failures
```yaml
name: analysis-engine-auth-failures
condition:
  display_name: "Analysis Engine - Elevated Auth Failures"
  condition_threshold:
    filter: |
      resource.type="cloud_run_revision"
      AND resource.labels.service_name="analysis-engine"
      AND metric.type="logging.googleapis.com/user/auth_failures"
    comparison: COMPARISON_GT
    threshold_value: 100
    duration: 3600s
notification_channels:
  - email_security
documentation:
  content: |
    More than 100 auth failures in 1 hour.
    May indicate credential issues or attack attempts.
```

## 📢 Notification Channels

### PagerDuty Integration
```yaml
pagerduty_critical:
  type: pagerduty
  display_name: "Analysis Engine - Critical"
  integration_key: ${PAGERDUTY_INTEGRATION_KEY}
  severity: critical
```

### Slack Integration
```yaml
slack_critical:
  type: slack
  display_name: "Analysis Engine - Critical Alerts"
  channel: "#analysis-engine-critical"
  
slack_alerts:
  type: slack
  display_name: "Analysis Engine - Alerts"
  channel: "#analysis-engine-alerts"
```

### Email Channels
```yaml
email_oncall:
  type: email
  display_name: "On-Call Engineer"
  addresses:
    - <EMAIL>
    
email_team:
  type: email
  display_name: "Analysis Engine Team"
  addresses:
    - <EMAIL>
    
email_security:
  type: email
  display_name: "Security Team"
  addresses:
    - <EMAIL>
```

## 🚨 Incident Response Procedures

### Initial Response (0-5 minutes)

1. **Acknowledge Alert**
   - Respond in PagerDuty/Slack
   - Join incident channel/call

2. **Initial Assessment**
   ```bash
   # Quick health check
   curl https://analysis-engine-572735000332.us-central1.run.app/health
   
   # Check recent errors
   gcloud logging read "severity>=ERROR" --limit=20
   
   # Check service status
   gcloud run services describe analysis-engine --region=us-central1
   ```

3. **Communicate Status**
   - Post initial findings in Slack
   - Update status page if needed

### Diagnosis Phase (5-15 minutes)

1. **Gather Metrics**
   ```bash
   # Run diagnostics script
   ./scripts/full-diagnostics.sh
   
   # Check specific subsystems
   curl https://analysis-engine-572735000332.us-central1.run.app/health/detailed
   ```

2. **Identify Root Cause**
   - Review error patterns
   - Check recent deployments
   - Verify external dependencies

3. **Determine Impact**
   - Affected users/operations
   - Data integrity status
   - Business impact assessment

### Resolution Phase

#### Quick Fixes
- **High Memory**: Scale up instances
- **High Latency**: Clear cache, scale horizontally
- **Parse Errors**: Identify problematic files
- **Auth Issues**: Verify JWT configuration

#### Rollback Procedure
```bash
# List recent revisions
gcloud run revisions list --service=analysis-engine

# Rollback to previous
gcloud run services update-traffic analysis-engine \
  --to-revisions=PREVIOUS_REVISION=100
```

### Post-Incident (After Resolution)

1. **Verify Resolution**
   ```bash
   # Run validation suite
   ./scripts/post-deploy-validation.sh
   
   # Monitor for 15 minutes
   watch -n 60 'curl -s https://analysis-engine-572735000332.us-central1.run.app/health'
   ```

2. **Document Incident**
   - Timeline of events
   - Root cause analysis
   - Actions taken
   - Prevention measures

3. **Update Procedures**
   - Runbook improvements
   - Alert tuning
   - Automation opportunities

## 🎛️ Alert Tuning

### Alert Tuning Process

1. **Weekly Review**
   ```sql
   -- Query alert history
   SELECT 
     policy_name,
     COUNT(*) as alert_count,
     AVG(duration_minutes) as avg_duration
   FROM alert_history
   WHERE timestamp > TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 7 DAY)
   GROUP BY policy_name
   ORDER BY alert_count DESC;
   ```

2. **Noise Reduction**
   - Adjust thresholds for noisy alerts
   - Add aggregation windows
   - Implement alert suppression

3. **Coverage Gaps**
   - Review incidents without alerts
   - Add new alert conditions
   - Test alert effectiveness

### Alert Suppression Rules

```yaml
suppression_rules:
  - name: "Maintenance Window"
    schedule: "0 2 * * SUN"  # Sunday 2 AM
    duration: "2h"
    alerts: ["*"]
    
  - name: "Known Spike Window"
    schedule: "0 9 * * MON-FRI"  # Weekday 9 AM
    duration: "1h"
    alerts: ["high-latency", "high-cpu"]
```

## 📚 Runbook Links

### Critical Runbooks
- [Service Down](./runbooks/service-down.md)
- [High Error Rate](./runbooks/high-error-rate.md)
- [Database Connection Lost](./runbooks/database-connection.md)
- [Memory Exhaustion](./runbooks/memory-exhaustion.md)

### Performance Runbooks
- [High Latency](./runbooks/high-latency.md)
- [Low Cache Hit Rate](./runbooks/cache-issues.md)
- [Parse Failures](./runbooks/parse-failures.md)

### Security Runbooks
- [Authentication Failures](./runbooks/auth-failures.md)
- [Rate Limit Exceeded](./runbooks/rate-limiting.md)
- [Suspicious Activity](./runbooks/suspicious-activity.md)

## 📊 Alert History

### Recent Incidents

| Date | Alert | Severity | Duration | Root Cause | Resolution |
|------|-------|----------|----------|------------|------------|
| 2025-07-14 | High Memory | P2 | 15 min | Large file processing | Increased memory limit |
| 2025-07-10 | Parse Failures | P3 | 30 min | Malformed input files | Added validation |
| 2025-07-05 | High Latency | P2 | 10 min | Cache miss storm | Warmed cache |

### Alert Statistics (Last 30 Days)

- **Total Alerts**: 23
- **P1 Alerts**: 0
- **P2 Alerts**: 3
- **P3 Alerts**: 8
- **P4 Alerts**: 12
- **MTTR (Mean Time To Resolve)**: 18 minutes
- **False Positive Rate**: 15%

## 🔧 Alert Management Commands

### Create/Update Alerts
```bash
# Apply alert configuration
gcloud alpha monitoring policies create --policy-from-file=alerts/service-down.yaml

# Update existing alert
gcloud alpha monitoring policies update POLICY_ID --policy-from-file=alerts/updated-policy.yaml
```

### Test Alerts
```bash
# Trigger test alert
curl -X POST https://analysis-engine-572735000332.us-central1.run.app/test/trigger-alert \
  -H "x-api-key: ${TEST_API_KEY}" \
  -d '{"alert_type": "high_error_rate"}'
```

### Silence Alerts
```bash
# Create silence
gcloud alpha monitoring policies snooze POLICY_ID --duration=2h

# List active silences
gcloud alpha monitoring policies list --filter="snooze.is_active=true"
```

---

**Note**: Keep this document updated with any changes to alerting configuration or procedures. Review quarterly for effectiveness.