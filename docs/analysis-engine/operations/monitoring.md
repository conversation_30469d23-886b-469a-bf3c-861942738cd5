# 📊 Analysis Engine - Monitoring & Metrics Guide

> **Context Engineering**: This documentation covers the comprehensive monitoring, metrics, and observability features of the Analysis Engine, including Prometheus metrics, health checks, and operational dashboards.

## 📋 Overview

The Analysis Engine provides extensive monitoring capabilities through:

1. **Prometheus Metrics** - Detailed performance and operational metrics
2. **Health Endpoints** - Multiple health check endpoints for different purposes
3. **Circuit Breakers** - Automatic failure detection and recovery
4. **Backpressure Monitoring** - Memory and resource pressure indicators
5. **Security Monitoring** - Authentication and rate limiting metrics

## 🎯 Health Monitoring Endpoints

### Basic Health Check
```http
GET /health
```

Simple health check for load balancers and basic monitoring.

**Response**:
```json
{
  "status": "healthy",
  "service": "analysis-engine",
  "version": "1.0.0"
}
```

### Liveness Check
```http
GET /health/live
```

Kubernetes liveness probe endpoint. Returns 200 if the service is alive.

**Response**:
```json
{
  "status": "alive",
  "timestamp": "2025-08-06T10:00:00Z"
}
```

### Readiness Check
```http
GET /health/ready
```

Kubernetes readiness probe endpoint. Checks all dependencies.

**Response**:
```json
{
  "ready": true,
  "service": "analysis-engine",
  "checks": {
    "spanner": true,
    "storage": true,
    "pubsub": true,
    "redis": true
  }
}
```

### Detailed Health Check
```http
GET /health/detailed
```

Comprehensive health information including dependency latencies.

**Response**:
```json
{
  "status": "healthy",
  "service": "analysis-engine",
  "version": "1.0.0",
  "dependencies": {
    "spanner": {
      "status": "connected",
      "latency_ms": 12,
      "last_check": "2025-08-06T10:00:00Z"
    },
    "redis": {
      "status": "connected", 
      "latency_ms": 2,
      "last_check": "2025-08-06T10:00:00Z"
    },
    "storage": {
      "status": "connected",
      "latency_ms": 45,
      "last_check": "2025-08-06T10:00:00Z"
    },
    "pubsub": {
      "status": "connected",
      "latency_ms": 8,
      "last_check": "2025-08-06T10:00:00Z"
    }
  },
  "memory_usage_mb": 1024,
  "cpu_usage_percent": 35.2,
  "active_connections": 127,
  "uptime_seconds": 3600
}
```

### Authentication Status
```http
GET /health/auth
```

Check authentication system health.

**Response**:
```json
{
  "auth_enabled": true,
  "auth_type": "jwt",
  "rate_limiting_enabled": true,
  "csrf_protection_enabled": true,
  "active_sessions": 45,
  "jwt_keys_loaded": 3,
  "api_keys_cached": 127
}
```

## 📈 Prometheus Metrics

### Endpoint
```http
GET /metrics
```

Returns metrics in Prometheus exposition format.

### Key Metrics

#### HTTP Request Metrics
```prometheus
# Request duration histogram
http_request_duration_seconds_bucket{method="POST",path="/api/v1/analysis",status="200",le="0.1"} 1234
http_request_duration_seconds_sum{method="POST",path="/api/v1/analysis",status="200"} 156.7
http_request_duration_seconds_count{method="POST",path="/api/v1/analysis",status="200"} 1567

# Active requests gauge
http_requests_in_flight{method="POST",path="/api/v1/analysis"} 5

# Request counter
http_requests_total{method="POST",path="/api/v1/analysis",status="200"} 15670
```

#### Analysis Metrics
```prometheus
# Analysis duration histogram
analysis_duration_seconds_bucket{type="repository",status="completed",le="60"} 890
analysis_duration_seconds_sum{type="repository",status="completed"} 45678.9
analysis_duration_seconds_count{type="repository",status="completed"} 890

# Active analyses gauge
analyses_in_progress{type="repository"} 12

# Analysis counter
analyses_total{type="repository",status="completed"} 890
analyses_total{type="repository",status="failed"} 23
```

#### Parser Performance Metrics
```prometheus
# Parse duration by language
parse_duration_milliseconds_bucket{language="rust",status="success",le="100"} 4567
parse_duration_milliseconds_sum{language="rust",status="success"} 234567
parse_duration_milliseconds_count{language="rust",status="success"} 4567

# Lines of code parsed counter
lines_of_code_parsed_total{language="rust"} 12345678

# Parse success rate
parse_success_rate{language="rust"} 0.957
```

#### Security Metrics
```prometheus
# Authentication attempts
auth_attempts_total{type="jwt",status="success"} 45678
auth_attempts_total{type="jwt",status="failed"} 234

# Rate limit hits
rate_limit_exceeded_total{user="user123"} 5

# CSRF validation
csrf_validations_total{status="success"} 12345
csrf_validations_total{status="failed"} 67
```

#### Resource Metrics
```prometheus
# Memory usage
process_resident_memory_bytes 1073741824

# CPU usage
process_cpu_seconds_total 3456.78

# Open file descriptors
process_open_fds 234

# Thread count
process_threads 48
```

#### Cache Metrics
```prometheus
# Redis cache operations
redis_cache_hits_total{operation="get"} 123456
redis_cache_misses_total{operation="get"} 2345
redis_cache_hit_rate 0.981

# AST cache metrics
ast_cache_size_bytes 536870912
ast_cache_entries 12345
ast_cache_evictions_total 234
```

## 🔄 Circuit Breaker Monitoring

### Endpoint
```http
GET /circuit-breakers
```

Monitor circuit breaker states for external dependencies.

**Response**:
```json
{
  "spanner": {
    "state": "closed",
    "failure_count": 0,
    "success_count": 12345,
    "last_failure": null,
    "half_open_at": null
  },
  "redis": {
    "state": "half_open",
    "failure_count": 3,
    "success_count": 45678,
    "last_failure": "2025-08-06T09:55:00Z",
    "half_open_at": "2025-08-06T10:00:00Z"
  },
  "storage": {
    "state": "open",
    "failure_count": 10,
    "success_count": 23456,
    "last_failure": "2025-08-06T09:58:00Z",
    "half_open_at": "2025-08-06T10:03:00Z"
  }
}
```

### Circuit Breaker States

- **Closed**: Normal operation, requests flowing
- **Open**: Failures exceeded threshold, requests blocked
- **Half-Open**: Testing if service recovered

## 💾 Backpressure Monitoring

### Endpoint
```http
GET /backpressure
```

Monitor system resource pressure and throttling.

**Response**:
```json
{
  "backpressure_active": false,
  "memory_usage_percent": 0.45,
  "threshold_percent": 0.8,
  "active_analyses": 12,
  "max_concurrent": 100,
  "queue_depth": 5,
  "rejected_requests": 0,
  "throttle_rate": 0.0
}
```

### Backpressure Indicators

- **Memory Usage**: System memory utilization
- **Queue Depth**: Pending analysis queue size
- **Throttle Rate**: Request throttling percentage
- **Rejected Requests**: Requests rejected due to overload

## 📊 Granular Metrics

### Endpoint
```http
GET /api/v1/metrics/granular
```

Detailed metrics with filtering options.

**Query Parameters**:
- `operation` - Filter by operation name
- `language` - Filter by programming language
- `last_minutes` - Time range (default: 60)
- `detailed` - Include percentile breakdowns

**Example**:
```http
GET /api/v1/metrics/granular?operation=parse&language=rust&last_minutes=30&detailed=true
```

**Response**:
```json
{
  "operation": "parse",
  "language": "rust",
  "time_range_minutes": 30,
  "metrics": {
    "count": 1234,
    "success_rate": 0.957,
    "average_duration_ms": 45.6,
    "percentiles": {
      "p50": 38.2,
      "p90": 67.8,
      "p95": 89.4,
      "p99": 156.7
    },
    "throughput_per_second": 41.13,
    "error_breakdown": {
      "timeout": 23,
      "syntax_error": 12,
      "memory_limit": 5
    }
  }
}
```

## 🔐 Security Monitoring

### Security Stats Endpoint
```http
GET /security/stats
```

Comprehensive security middleware statistics.

**Response**:
```json
{
  "total_requests": 150000,
  "authenticated_requests": 149500,
  "blocked_requests": 500,
  "rate_limited_requests": 250,
  "auth_failures": 100,
  "average_auth_time_ms": 2.5,
  "active_sessions": 45,
  "unique_users_24h": 234,
  "suspicious_activities": 12,
  "blocked_ips": ["*************", "*********"],
  "last_reset": "2025-08-06T00:00:00Z"
}
```

### CSRF Status Endpoint
```http
GET /security/csrf-status
```

CSRF protection monitoring.

**Response**:
```json
{
  "csrf_protection_enabled": true,
  "token_expiry_seconds": 3600,
  "tokens_issued": 1250,
  "tokens_validated": 1200,
  "validation_failures": 15,
  "expired_tokens": 35,
  "active_tokens": 1215,
  "last_token_rotation": "2025-08-06T11:00:00Z"
}
```

## 📈 Dashboards & Visualization

### Grafana Dashboard

The Analysis Engine includes a pre-configured Grafana dashboard with:

1. **Service Overview**
   - Request rate and latency
   - Error rate
   - Active connections
   - CPU and memory usage

2. **Analysis Performance**
   - Analysis throughput
   - Parse success rates by language
   - Average analysis duration
   - Queue depth

3. **Security Monitoring**
   - Authentication success/failure rates
   - Rate limit violations
   - CSRF validation metrics
   - Suspicious activity alerts

4. **Dependency Health**
   - Circuit breaker states
   - External service latencies
   - Cache hit rates
   - Database connection pool metrics

### Example Grafana Queries

**Request Rate**:
```promql
rate(http_requests_total[5m])
```

**P95 Latency**:
```promql
histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket[5m])) by (le))
```

**Error Rate**:
```promql
rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m])
```

**Active Analyses**:
```promql
analyses_in_progress
```

## 🚨 Alerting Rules

### Prometheus Alert Configuration

```yaml
groups:
  - name: analysis_engine_alerts
    rules:
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.05
        for: 5m
        annotations:
          summary: "High error rate detected"
          description: "Error rate is above 5% for 5 minutes"
      
      - alert: HighMemoryUsage
        expr: process_resident_memory_bytes / 1024 / 1024 / 1024 > 3.5
        for: 10m
        annotations:
          summary: "High memory usage"
          description: "Memory usage above 3.5GB for 10 minutes"
      
      - alert: CircuitBreakerOpen
        expr: circuit_breaker_state{state="open"} == 1
        for: 1m
        annotations:
          summary: "Circuit breaker open"
          description: "Circuit breaker for {{ $labels.service }} is open"
      
      - alert: HighParseFailureRate
        expr: parse_success_rate < 0.9
        for: 5m
        annotations:
          summary: "High parse failure rate"
          description: "Parse success rate for {{ $labels.language }} below 90%"
```

## 📊 Operational Metrics

### SLI/SLO Definitions

**Service Level Indicators (SLIs)**:
- **Availability**: Successful health checks / Total health checks
- **Latency**: P95 response time < 200ms
- **Error Rate**: Successful requests / Total requests
- **Throughput**: Analyses completed per minute

**Service Level Objectives (SLOs)**:
- **Availability**: 99.9% (43.2 minutes downtime/month)
- **Latency**: 95% of requests < 200ms
- **Error Rate**: < 0.1% errors
- **Throughput**: > 100 analyses/minute

### Monitoring Best Practices

1. **Set Up Alerts**: Configure alerts for SLO violations
2. **Dashboard Access**: Ensure team has dashboard access
3. **Regular Reviews**: Weekly review of metrics trends
4. **Capacity Planning**: Monitor resource usage trends
5. **Incident Response**: Document and follow runbooks

## 🔧 Troubleshooting with Metrics

### High Latency Investigation
1. Check `http_request_duration_seconds` by endpoint
2. Review `parse_duration_milliseconds` by language
3. Examine circuit breaker states
4. Check backpressure status

### Memory Issues
1. Monitor `process_resident_memory_bytes`
2. Check `ast_cache_size_bytes`
3. Review backpressure metrics
4. Analyze memory usage by operation

### Authentication Problems
1. Check `/security/stats` for auth failures
2. Review `auth_attempts_total` metrics
3. Examine JWT key rotation logs
4. Verify CSRF token metrics

## 📚 Additional Resources

- [Operations Runbook](./runbook.md) - Operational procedures
- [Performance Tuning](../guides/performance-tuning.md) - Optimization guide
- [Troubleshooting Guide](../troubleshooting/README.md) - Common issues