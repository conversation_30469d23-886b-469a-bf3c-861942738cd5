# 🚀 Analysis Engine - Container Startup Resilience

> **Context Engineering Standards**: This operational guide follows enterprise reliability patterns with proven Cloud Run deployment practices and service resilience best practices.

## Overview

The Analysis Engine implements comprehensive startup retry logic to handle transient failures during container initialization on Cloud Run. This ensures reliable deployment even when dependent services (Spanner, Redis, Google Cloud Storage, Pub/Sub) experience temporary unavailability.

## 📋 Table of Contents

1. [Architecture](#architecture)
2. [Implementation Details](#implementation-details)
3. [Retry Behavior](#retry-behavior)
4. [Health Monitoring](#health-monitoring)
5. [Configuration](#configuration)
6. [Testing & Validation](#testing--validation)
7. [Troubleshooting](#troubleshooting)
8. [Best Practices](#best-practices)

## 🏗️ Architecture

### Core Components

1. **Startup Manager** (`src/startup.rs`)
   - Tracks initialization state for all services
   - Provides real-time status through health endpoints
   - Manages retry attempts with exponential backoff

2. **Service Status Tracking**
   ```rust
   pub enum ServiceStatus {
       NotStarted,
       Initializing { attempt: u32, last_error: Option<String> },
       Ready,
       Failed { error: String },
   }
   ```

3. **Retry Configuration**
   - Initial delay: 1 second
   - Maximum delay: 30 seconds
   - Exponential backoff multiplier: 2.0
   - Total timeout: 5 minutes (Cloud Run limit)
   - Default max attempts: 10

### Services with Retry Logic

#### Required Services (startup fails if these fail)
- **Spanner Database**: Primary data storage
- **Google Cloud Storage**: File storage and processing
- **Google Cloud Pub/Sub**: Event messaging

#### Optional Services (startup continues if these fail)
- **Redis**: Falls back to in-memory caching

## 🔄 Implementation Details

### Exponential Backoff Algorithm
```
Delay = min(initial_delay * (backoff_multiplier ^ attempt), max_delay)
```

Example retry sequence:
- Attempt 1: 1 second delay
- Attempt 2: 2 second delay
- Attempt 3: 4 second delay
- Attempt 4: 8 second delay
- Attempt 5: 16 second delay
- Attempt 6+: 30 second delay (capped)

### Custom Retry Configuration

Different retry configurations can be applied per service:

```rust
// Required service with standard retry
retry_with_backoff(
    "Spanner",
    &startup_manager,
    RetryConfig::default(),
    init_function,
).await?;

// Optional service with fewer retries
retry_optional_service(
    "Redis",
    &startup_manager,
    RetryConfig {
        max_attempts: 5,
        timeout_per_attempt: Duration::from_secs(10),
        ..Default::default()
    },
    init_function,
).await;
```

## 📊 Health Monitoring

### `/health` - Basic Health Check
```json
{
  "status": "initializing|healthy|degraded",
  "service": "analysis-engine",
  "version": "1.0.0",
  "startup": {
    "is_ready": false,
    "services_ready": "2/4",
    "startup_time_seconds": 15.3
  }
}
```

### `/health/ready` - Readiness Check
```json
{
  "ready": false,
  "service": "analysis-engine",
  "checks": {
    "spanner": true,
    "storage": false,
    "pubsub": true
  },
  "startup_status": {
    "is_ready": false,
    "total_services": 4,
    "ready_services": 3,
    "failed_services": 0,
    "startup_duration": "15.3s"
  },
  "service_details": [
    {
      "name": "Spanner",
      "status": "Ready",
      "startup_time_seconds": 2.5
    },
    {
      "name": "Google Cloud Storage",
      "status": "Initializing { attempt: 3, last_error: \"Connection timeout\" }",
      "startup_time_seconds": null
    }
  ]
}
```

### `/health/detailed` - Comprehensive Health Check
Includes all startup information plus system metrics, backpressure status, and circuit breaker states.

## ⚙️ Configuration

### Environment Variables

No additional environment variables are required. The retry logic uses existing service configurations.

### Monitoring Startup Times

Track key metrics:
- p50, p90, p99 startup durations
- Retry attempt frequencies
- Service-specific failure rates
- Total initialization time

## 🧪 Testing & Validation

### Local Testing

Run the test script to validate the implementation:
```bash
./scripts/test_startup_retry.sh
```

### Simulating Failures

To test retry behavior locally:

1. **Simulate Spanner failure**: Stop local Spanner emulator
2. **Simulate Redis failure**: Stop Redis server
3. **Simulate network issues**: Use incorrect connection strings
4. **Simulate slow startup**: Add artificial delays to init functions

### Cloud Run Testing

Deploy to a test environment and monitor:
- Cloud Run logs for retry attempts
- Health endpoint responses during startup
- Total startup time metrics

### Example Logs

```
INFO Attempting to initialize Spanner (attempt 1/10)
WARN Spanner initialization failed: Connection refused
INFO Retrying Spanner initialization in 1.0s...
INFO Attempting to initialize Spanner (attempt 2/10)
INFO Spanner initialized successfully after 2 attempts (3.5s)
```

## 🔧 Troubleshooting

### Common Issues

#### 1. Startup Timeout
- **Symptoms**: Container startup exceeds 5-minute Cloud Run limit
- **Diagnosis**: Check which service is taking longest to initialize
- **Resolution**: Consider reducing retry attempts for slow services

#### 2. Repeated Failures
- **Symptoms**: Same service failing consistently across retries
- **Diagnosis**: Check service credentials and permissions
- **Resolution**: Verify network connectivity and ensure dependent services are running

#### 3. Slow Startup
- **Symptoms**: Long initialization times affecting deployment speed
- **Diagnosis**: Review retry delays and backoff configuration
- **Resolution**: Consider parallel initialization where possible

### Debugging Commands

```bash
# Check Cloud Run logs for startup issues
gcloud logging read "resource.type=cloud_run_revision AND resource.labels.service_name=analysis-engine" --limit=50

# Monitor health endpoint during startup
watch -n 1 'curl -s https://analysis-engine-.../health/ready | jq'

# Check service-specific startup status
curl -s https://analysis-engine-.../health/detailed | jq '.startup_status'
```

## 📈 Best Practices

### Operational Excellence

1. **Monitor Startup Times**: Track p50, p90, p99 startup durations
2. **Set Alerts**: Alert on repeated startup failures or slow startups
3. **Review Logs**: Regularly review retry patterns to identify issues
4. **Tune Retry Config**: Adjust retry parameters based on observed behavior
5. **Graceful Degradation**: Design features to work without optional services

### Performance Optimization

1. **Parallel Initialization**: Initialize independent services concurrently where possible
2. **Circuit Breaker Integration**: Skip retries for known-bad services
3. **Startup Metrics**: Export retry metrics to Prometheus
4. **Configuration Hot-Reload**: Update retry config without restart
5. **Health Check Caching**: Cache health status to reduce load during startup

### Security Considerations

1. **Credential Management**: Ensure secure handling of service credentials
2. **Network Security**: Verify proper VPC and firewall configurations
3. **Audit Trail**: Log all startup attempts and failures for security analysis
4. **Graceful Degradation**: Fail securely when required services unavailable

## 🚀 Future Enhancements

1. **Parallel Initialization**: Initialize independent services concurrently
2. **Circuit Breaker Integration**: Skip retries for known-bad services
3. **Startup Metrics**: Export retry metrics to Prometheus
4. **Configuration Hot-Reload**: Update retry config without restart
5. **Health Check Caching**: Cache health status to reduce load during startup

---

## 📞 Support & Contact

### Getting Help
- **Operations Team**: <EMAIL>
- **Cloud Run Issues**: <EMAIL>
- **Monitoring**: <EMAIL>

### Related Documentation
- [Operations Runbook](./runbook.md)
- [Production Deployment](../deployment/production-deployment.md)
- [Troubleshooting Guide](../troubleshooting/README.md)