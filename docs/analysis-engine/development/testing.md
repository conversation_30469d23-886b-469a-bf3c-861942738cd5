# 🧪 Analysis Engine Testing Guide

**Service**: Analysis Engine  
**Last Updated**: 2025-08-06  
**Version**: 1.0.0

## 📋 Table of Contents

1. [Testing Philosophy](#testing-philosophy)
2. [Test Structure](#test-structure)
3. [Unit Testing](#unit-testing)
4. [Integration Testing](#integration-testing)
5. [End-to-End Testing](#end-to-end-testing)
6. [Performance Testing](#performance-testing)
7. [Security Testing](#security-testing)
8. [Test Data Management](#test-data-management)
9. [CI/CD Integration](#cicd-integration)
10. [Debugging Tests](#debugging-tests)

## 🎯 Testing Philosophy

### Core Principles
- **Test Pyramid**: More unit tests, fewer integration tests, minimal E2E tests
- **Fast Feedback**: Tests should run quickly and provide clear failure messages
- **Isolation**: Tests should not depend on external services when possible
- **Repeatability**: Tests must produce consistent results
- **Coverage**: Aim for 80%+ coverage, 100% for critical paths

### Testing Strategy
```
         /\
        /E2E\        <- Full system tests (5%)
       /------\
      /  Integ  \    <- Component integration (20%)
     /------------\
    /     Unit     \ <- Unit tests (75%)
   /----------------\
```

## 📁 Test Structure

### Directory Organization
```
analysis-engine/
├── src/
│   └── */mod.rs          # Unit tests in source files
├── tests/
│   ├── common/           # Shared test utilities
│   │   ├── mod.rs
│   │   ├── fixtures.rs   # Test data factories
│   │   └── helpers.rs    # Test helper functions
│   ├── integration/      # Integration tests
│   │   ├── api_test.rs
│   │   ├── parser_test.rs
│   │   └── security_test.rs
│   └── e2e/             # End-to-end tests
│       ├── full_analysis_test.rs
│       └── websocket_test.rs
├── benches/             # Performance benchmarks
│   ├── parser_bench.rs
│   └── analysis_bench.rs
└── fixtures/            # Test data files
    ├── repos/
    ├── languages/
    └── configs/
```

## 🔧 Unit Testing

### Basic Unit Test Structure
```rust
#[cfg(test)]
mod tests {
    use super::*;
    use pretty_assertions::assert_eq;

    #[test]
    fn test_parse_function_declaration() {
        // Arrange
        let input = "fn main() { println!(\"Hello\"); }";
        let expected_nodes = 7;
        
        // Act
        let result = parse_rust_code(input);
        
        // Assert
        assert!(result.is_ok(), "Parse should succeed");
        let ast = result.unwrap();
        assert_eq!(ast.node_count(), expected_nodes);
        assert_eq!(ast.root_node().kind(), "source_file");
    }

    #[test]
    fn test_parse_invalid_syntax() {
        // Arrange
        let input = "fn main() { unclosed";
        
        // Act
        let result = parse_rust_code(input);
        
        // Assert
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("syntax error"));
    }
}
```

### Testing Async Code
```rust
#[cfg(test)]
mod tests {
    use super::*;
    use tokio::test;

    #[tokio::test]
    async fn test_analyze_repository_async() {
        // Arrange
        let repo_url = "https://github.com/test/repo";
        let mock_client = MockHttpClient::new();
        
        // Act
        let result = analyze_repository(&mock_client, repo_url).await;
        
        // Assert
        assert!(result.is_ok());
        let analysis = result.unwrap();
        assert_eq!(analysis.repository_url, repo_url);
    }

    #[tokio::test(flavor = "multi_thread", worker_threads = 2)]
    async fn test_concurrent_analysis() {
        // Test with multiple threads
    }
}
```

### Mocking Dependencies
```rust
use mockall::automock;

#[automock]
trait StorageClient {
    async fn store(&self, key: &str, data: &[u8]) -> Result<()>;
    async fn retrieve(&self, key: &str) -> Result<Vec<u8>>;
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_with_mock_storage() {
        // Arrange
        let mut mock = MockStorageClient::new();
        mock.expect_store()
            .with(eq("test-key"), eq(b"test-data"))
            .times(1)
            .returning(|_, _| Ok(()));
        
        // Act
        let service = AnalysisService::new(Box::new(mock));
        let result = service.save_analysis("test-key", b"test-data").await;
        
        // Assert
        assert!(result.is_ok());
    }
}
```

### Property-Based Testing
```rust
use proptest::prelude::*;

#[cfg(test)]
mod tests {
    use super::*;

    proptest! {
        #[test]
        fn test_parse_any_valid_rust_code(code in valid_rust_code()) {
            let result = parse_rust_code(&code);
            prop_assert!(result.is_ok());
            let ast = result.unwrap();
            prop_assert!(ast.node_count() > 0);
        }

        #[test]
        fn test_file_size_limits(size in 0usize..100_000_000) {
            let result = validate_file_size(size);
            if size <= MAX_FILE_SIZE {
                prop_assert!(result.is_ok());
            } else {
                prop_assert!(result.is_err());
            }
        }
    }
}

fn valid_rust_code() -> impl Strategy<Value = String> {
    prop_oneof![
        Just("fn main() {}".to_string()),
        Just("struct Test { field: i32 }".to_string()),
        Just("impl Test { fn new() -> Self { Self { field: 0 } } }".to_string()),
    ]
}
```

## 🔗 Integration Testing

### Database Integration Tests
```rust
// tests/integration/database_test.rs
use analysis_engine::test_helpers::*;
use sqlx::PgPool;

#[tokio::test]
async fn test_analysis_persistence() {
    // Setup test database
    let db = setup_test_database().await;
    
    // Create service with real database
    let service = AnalysisService::new(db.clone());
    
    // Test data
    let analysis = create_test_analysis();
    
    // Save analysis
    let saved = service.save_analysis(&analysis).await.unwrap();
    assert_eq!(saved.id, analysis.id);
    
    // Retrieve analysis
    let retrieved = service.get_analysis(&analysis.id).await.unwrap();
    assert_eq!(retrieved, analysis);
    
    // Cleanup
    cleanup_test_database(&db).await;
}
```

### API Integration Tests
```rust
// tests/integration/api_test.rs
use actix_web::{test, App};
use analysis_engine::create_app;

#[actix_web::test]
async fn test_analyze_endpoint() {
    // Create test app
    let app = test::init_service(create_app()).await;
    
    // Create request
    let req = test::TestRequest::post()
        .uri("/api/v1/analyze")
        .set_json(&json!({
            "repository_url": "https://github.com/test/repo",
            "branch": "main"
        }))
        .to_request();
    
    // Send request
    let resp = test::call_service(&app, req).await;
    
    // Assert response
    assert!(resp.status().is_success());
    
    let body: AnalysisResponse = test::read_body_json(resp).await;
    assert_eq!(body.status, "pending");
}
```

### External Service Integration
```rust
// tests/integration/external_services_test.rs

#[tokio::test]
#[ignore] // Run with --ignored flag
async fn test_real_github_integration() {
    // Only run in CI or with real credentials
    if std::env::var("GITHUB_TOKEN").is_err() {
        eprintln!("Skipping: GITHUB_TOKEN not set");
        return;
    }
    
    let client = create_github_client();
    let result = client.get_repository("rust-lang/rust").await;
    
    assert!(result.is_ok());
    let repo = result.unwrap();
    assert_eq!(repo.name, "rust");
}
```

## 🌐 End-to-End Testing

### Full System Tests
```rust
// tests/e2e/full_analysis_test.rs
use analysis_engine::test_helpers::*;

#[tokio::test]
async fn test_complete_analysis_workflow() {
    // Start full application
    let app = spawn_app().await;
    
    // 1. Submit analysis request
    let response = app.client
        .post(&format!("{}/api/v1/analyze", app.address))
        .json(&json!({
            "repository_url": "https://github.com/small/test-repo",
            "enable_security": true,
            "enable_patterns": true
        }))
        .send()
        .await
        .unwrap();
    
    assert_eq!(response.status(), 200);
    let analysis: AnalysisResponse = response.json().await.unwrap();
    
    // 2. Poll for completion
    let final_status = poll_until_complete(&app.client, &analysis.id).await;
    assert_eq!(final_status.status, "completed");
    
    // 3. Verify results
    let results = app.client
        .get(&format!("{}/api/v1/analyses/{}", app.address, analysis.id))
        .send()
        .await
        .unwrap()
        .json::<AnalysisResult>()
        .await
        .unwrap();
    
    assert!(results.files_analyzed > 0);
    assert!(results.patterns.len() > 0);
    assert!(results.security_issues.is_some());
}
```

### WebSocket Testing
```rust
// tests/e2e/websocket_test.rs
use tokio_tungstenite::connect_async;

#[tokio::test]
async fn test_analysis_progress_websocket() {
    let app = spawn_app().await;
    
    // Connect to WebSocket
    let url = format!("ws://{}/ws/progress/test-analysis", app.address);
    let (ws_stream, _) = connect_async(url).await.unwrap();
    let (_, mut read) = ws_stream.split();
    
    // Start analysis
    let analysis_id = start_test_analysis(&app).await;
    
    // Collect progress updates
    let mut updates = vec![];
    while let Some(msg) = read.next().await {
        let msg = msg.unwrap();
        if let Message::Text(text) = msg {
            let update: ProgressUpdate = serde_json::from_str(&text).unwrap();
            updates.push(update);
            
            if update.status == "completed" {
                break;
            }
        }
    }
    
    // Verify progress sequence
    assert!(updates.len() > 2);
    assert_eq!(updates.first().unwrap().status, "started");
    assert_eq!(updates.last().unwrap().status, "completed");
}
```

## ⚡ Performance Testing

### Benchmark Tests
```rust
// benches/parser_bench.rs
use criterion::{black_box, criterion_group, criterion_main, Criterion, BenchmarkId};
use analysis_engine::parser::*;

fn benchmark_parser_performance(c: &mut Criterion) {
    let mut group = c.benchmark_group("parser_performance");
    
    for size in [1000, 10000, 100000].iter() {
        let content = generate_rust_code(*size);
        
        group.bench_with_input(
            BenchmarkId::new("parse_rust", size),
            &content,
            |b, content| {
                b.iter(|| {
                    let _ = parse_rust_code(black_box(content));
                });
            },
        );
    }
    
    group.finish();
}

fn benchmark_language_comparison(c: &mut Criterion) {
    let mut group = c.benchmark_group("language_comparison");
    
    let languages = vec![
        ("rust", include_str!("../fixtures/sample.rs")),
        ("python", include_str!("../fixtures/sample.py")),
        ("javascript", include_str!("../fixtures/sample.js")),
    ];
    
    for (lang, content) in languages {
        group.bench_with_input(
            BenchmarkId::new("parse", lang),
            content,
            |b, content| {
                b.iter(|| {
                    let _ = parse_code(black_box(content), black_box(lang));
                });
            },
        );
    }
}

criterion_group!(benches, benchmark_parser_performance, benchmark_language_comparison);
criterion_main!(benches);
```

### Load Testing
```rust
// tests/performance/load_test.rs
use tokio::task::JoinSet;
use std::time::Instant;

#[tokio::test]
#[ignore] // Run manually with cargo test --ignored
async fn test_concurrent_load() {
    let app = spawn_app().await;
    let client = reqwest::Client::new();
    
    let concurrent_requests = 100;
    let mut tasks = JoinSet::new();
    
    let start = Instant::now();
    
    for i in 0..concurrent_requests {
        let client = client.clone();
        let url = format!("{}/api/v1/analyze", app.address);
        
        tasks.spawn(async move {
            let response = client
                .post(&url)
                .json(&json!({
                    "repository_url": format!("https://github.com/test/repo-{}", i),
                    "branch": "main"
                }))
                .send()
                .await;
            
            response.unwrap().status()
        });
    }
    
    let mut success_count = 0;
    while let Some(result) = tasks.join_next().await {
        if result.unwrap().is_success() {
            success_count += 1;
        }
    }
    
    let duration = start.elapsed();
    
    println!("Processed {} requests in {:?}", concurrent_requests, duration);
    println!("Success rate: {}%", (success_count * 100) / concurrent_requests);
    println!("Requests per second: {:.2}", concurrent_requests as f64 / duration.as_secs_f64());
    
    assert!(success_count >= 95); // 95% success rate
}
```

## 🔒 Security Testing

### Security Test Suite
```rust
// tests/security/injection_test.rs

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_sql_injection_prevention() {
        let malicious_inputs = vec![
            "'; DROP TABLE analyses; --",
            "1' OR '1'='1",
            "admin'--",
            "1; UPDATE users SET role='admin'",
        ];
        
        for input in malicious_inputs {
            let result = validate_repository_url(input);
            assert!(result.is_err(), "Should reject SQL injection: {}", input);
        }
    }

    #[test]
    fn test_path_traversal_prevention() {
        let malicious_paths = vec![
            "../../../etc/passwd",
            "..\\..\\..\\windows\\system32",
            "file:///etc/passwd",
            "/dev/null; cat /etc/passwd",
        ];
        
        for path in malicious_paths {
            let result = validate_file_path(path);
            assert!(result.is_err(), "Should reject path traversal: {}", path);
        }
    }

    #[tokio::test]
    async fn test_rate_limiting() {
        let app = spawn_app().await;
        let client = create_test_client();
        
        // Make requests up to limit
        for _ in 0..RATE_LIMIT {
            let resp = client.get(&format!("{}/api/v1/health", app.address))
                .send()
                .await
                .unwrap();
            assert_eq!(resp.status(), 200);
        }
        
        // Next request should be rate limited
        let resp = client.get(&format!("{}/api/v1/health", app.address))
            .send()
            .await
            .unwrap();
        assert_eq!(resp.status(), 429);
    }

    #[test]
    fn test_jwt_validation() {
        // Test expired token
        let expired_token = create_jwt_with_expiry(-3600);
        assert!(validate_jwt(&expired_token).is_err());
        
        // Test invalid signature
        let invalid_token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.invalid.signature";
        assert!(validate_jwt(invalid_token).is_err());
        
        // Test valid token
        let valid_token = create_valid_jwt();
        assert!(validate_jwt(&valid_token).is_ok());
    }
}
```

### Fuzzing Tests
```rust
// tests/security/fuzz_test.rs
#[cfg(feature = "fuzzing")]
mod fuzz_tests {
    use arbitrary::{Arbitrary, Unstructured};

    #[test]
    fn fuzz_parser() {
        let data = include_bytes!("../corpus/parser_fuzz.bin");
        let mut u = Unstructured::new(data);
        
        if let Ok(input) = String::arbitrary(&mut u) {
            // Should not panic
            let _ = parse_code(&input, "rust");
        }
    }
}
```

## 📊 Test Data Management

### Test Fixtures
```rust
// tests/common/fixtures.rs
use fake::{Fake, Faker};
use uuid::Uuid;

pub fn create_test_analysis() -> Analysis {
    Analysis {
        id: Uuid::new_v4(),
        repository_url: "https://github.com/test/repo".to_string(),
        branch: "main".to_string(),
        status: AnalysisStatus::Pending,
        created_at: Utc::now(),
        updated_at: Utc::now(),
        results: None,
    }
}

pub fn create_test_user() -> User {
    User {
        id: Uuid::new_v4(),
        email: Faker.fake(),
        api_key: generate_api_key(),
        created_at: Utc::now(),
    }
}

pub fn create_large_repository() -> Repository {
    Repository {
        files: (0..1000)
            .map(|i| File {
                path: format!("src/file_{}.rs", i),
                content: generate_rust_code(1000),
                language: Language::Rust,
            })
            .collect(),
    }
}
```

### Test Database Management
```rust
// tests/common/database.rs
use sqlx::{PgPool, Postgres};
use uuid::Uuid;

pub async fn setup_test_database() -> PgPool {
    let db_name = format!("test_db_{}", Uuid::new_v4());
    let maintenance_url = std::env::var("TEST_DATABASE_URL")
        .unwrap_or_else(|_| "postgres://localhost/postgres".to_string());
    
    // Create test database
    let maintenance_pool = PgPool::connect(&maintenance_url).await.unwrap();
    sqlx::query(&format!("CREATE DATABASE {}", db_name))
        .execute(&maintenance_pool)
        .await
        .unwrap();
    
    // Connect to test database
    let test_url = format!("{}/{}", maintenance_url.rsplit('/').next().unwrap(), db_name);
    let test_pool = PgPool::connect(&test_url).await.unwrap();
    
    // Run migrations
    sqlx::migrate!("./migrations")
        .run(&test_pool)
        .await
        .unwrap();
    
    test_pool
}

pub async fn cleanup_test_database(pool: &PgPool) {
    let db_name = pool
        .connect_options()
        .get_database()
        .unwrap()
        .to_string();
    
    pool.close().await;
    
    let maintenance_url = std::env::var("TEST_DATABASE_URL")
        .unwrap_or_else(|_| "postgres://localhost/postgres".to_string());
    let maintenance_pool = PgPool::connect(&maintenance_url).await.unwrap();
    
    sqlx::query(&format!("DROP DATABASE IF EXISTS {}", db_name))
        .execute(&maintenance_pool)
        .await
        .unwrap();
}
```

## 🔄 CI/CD Integration

### GitHub Actions Configuration
```yaml
# .github/workflows/test.yml
name: Test Suite

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Install Rust
      uses: actions-rs/toolchain@v1
      with:
        profile: minimal
        toolchain: stable
        override: true
        components: rustfmt, clippy
    
    - name: Cache dependencies
      uses: actions/cache@v3
      with:
        path: |
          ~/.cargo/registry
          ~/.cargo/git
          target
        key: ${{ runner.os }}-cargo-${{ hashFiles('**/Cargo.lock') }}
    
    - name: Run tests
      env:
        TEST_DATABASE_URL: postgres://postgres:postgres@localhost:5432
        REDIS_URL: redis://localhost:6379
      run: |
        cargo test --all-features
        cargo test --doc
        cargo test --examples
    
    - name: Run integration tests
      run: cargo test --test '*' -- --ignored
    
    - name: Generate coverage report
      run: |
        cargo install cargo-tarpaulin
        cargo tarpaulin --out Xml --all-features
    
    - name: Upload coverage
      uses: codecov/codecov-action@v3
      with:
        files: ./cobertura.xml
```

### Test Scripts
```bash
#!/bin/bash
# scripts/test-all.sh

set -e

echo "Running format check..."
cargo fmt -- --check

echo "Running clippy..."
cargo clippy --all-features -- -D warnings

echo "Running unit tests..."
cargo test --lib

echo "Running integration tests..."
cargo test --test '*'

echo "Running doc tests..."
cargo test --doc

echo "Running benchmarks..."
cargo bench --no-run

echo "All tests passed!"
```

## 🐛 Debugging Tests

### Debugging Techniques

#### Print Debugging
```rust
#[test]
fn debug_complex_test() {
    env_logger::init(); // Initialize logger for tests
    
    let input = create_complex_input();
    dbg!(&input); // Debug print
    
    let result = process_input(input);
    eprintln!("Result: {:?}", result); // Error stream print
    
    assert!(result.is_ok());
}
```

#### VS Code Debugging
```json
// .vscode/launch.json
{
    "version": "0.2.0",
    "configurations": [
        {
            "type": "lldb",
            "request": "launch",
            "name": "Debug unit tests",
            "cargo": {
                "args": [
                    "test",
                    "--no-run",
                    "--lib"
                ],
                "filter": {
                    "name": "analysis_engine",
                    "kind": "lib"
                }
            },
            "args": ["test_name"],
            "cwd": "${workspaceFolder}"
        }
    ]
}
```

#### Test Isolation
```rust
// Run a single test
cargo test test_parse_function_declaration -- --nocapture

// Run tests in single thread for debugging
cargo test -- --test-threads=1

// Show test output even for passing tests
cargo test -- --nocapture

// Run with backtrace
RUST_BACKTRACE=1 cargo test
```

### Common Test Issues

#### Flaky Tests
```rust
// Use retry for flaky external dependencies
use backoff::{retry, ExponentialBackoff};

#[tokio::test]
async fn test_with_retry() {
    let result = retry(ExponentialBackoff::default(), || {
        async {
            let response = external_api_call().await?;
            Ok(response)
        }
    }).await;
    
    assert!(result.is_ok());
}
```

#### Test Timeouts
```rust
#[tokio::test(timeout = Duration::from_secs(10))]
async fn test_with_timeout() {
    // Test will fail if it takes longer than 10 seconds
    long_running_operation().await;
}
```

---

## 📚 Additional Resources

- [Rust Testing Book](https://doc.rust-lang.org/book/ch11-00-testing.html)
- [Tokio Testing](https://tokio.rs/tokio/topics/testing)
- [Criterion.rs Docs](https://bheisler.github.io/criterion.rs/book/)
- [Property Testing](https://proptest-rs.github.io/proptest/)