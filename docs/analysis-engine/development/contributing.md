# 🤝 Contributing to Analysis Engine

**Service**: Analysis Engine  
**Last Updated**: 2025-08-06  
**Version**: 1.0.0

## 📋 Table of Contents

1. [Getting Started](#getting-started)
2. [Development Setup](#development-setup)
3. [Code Standards](#code-standards)
4. [Testing Requirements](#testing-requirements)
5. [Pull Request Process](#pull-request-process)
6. [Security Guidelines](#security-guidelines)
7. [Documentation Standards](#documentation-standards)
8. [Community Guidelines](#community-guidelines)

## 🚀 Getting Started

Thank you for considering contributing to the Analysis Engine! This document provides guidelines and standards for contributing to the project.

### Prerequisites

- Rust 1.70+ (latest stable)
- <PERSON><PERSON> and Docker Compose
- Google Cloud SDK (for integration testing)
- Git with GPG signing configured
- Basic understanding of async Rust and web services

### Quick Setup

```bash
# Clone the repository
git clone https://github.com/episteme/analysis-engine.git
cd analysis-engine

# Install dependencies
cargo build

# Run tests
cargo test

# Start local development server
cargo run --bin analysis-engine
```

## 🛠️ Development Setup

### Environment Setup

1. **Install Rust Toolchain**
   ```bash
   curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
   rustup update stable
   rustup component add rustfmt clippy
   ```

2. **Install Development Tools**
   ```bash
   # macOS
   brew install protobuf grpcurl jq yq
   
   # Linux
   sudo apt-get install protobuf-compiler grpcurl jq yq
   ```

3. **Setup Pre-commit Hooks**
   ```bash
   cp scripts/pre-commit .git/hooks/pre-commit
   chmod +x .git/hooks/pre-commit
   ```

### Local Development Environment

```bash
# Copy environment template
cp .env.example .env.development

# Start local dependencies
docker-compose up -d

# Run database migrations
cargo run --bin migrate

# Start development server with hot reload
cargo watch -x run
```

### IDE Setup

#### VS Code
```json
// .vscode/settings.json
{
  "rust-analyzer.cargo.features": ["all"],
  "rust-analyzer.checkOnSave.command": "clippy",
  "editor.formatOnSave": true,
  "files.insertFinalNewline": true,
  "files.trimTrailingWhitespace": true
}
```

#### IntelliJ IDEA / CLion
- Install Rust plugin
- Enable format on save
- Configure Clippy as external linter

## 📏 Code Standards

### Rust Style Guide

We follow the official [Rust Style Guide](https://github.com/rust-dev-tools/fmt-rfcs/blob/master/guide/guide.md) with these additions:

#### Formatting
```rust
// Use rustfmt with project configuration
cargo fmt

// Configuration in rustfmt.toml
edition = "2021"
max_width = 100
use_small_heuristics = "Max"
imports_granularity = "Crate"
group_imports = "StdExternalCrate"
```

#### Naming Conventions
```rust
// Modules: snake_case
mod parser_service;

// Types: PascalCase
struct AnalysisResult;
enum ParseError;

// Functions: snake_case
fn parse_repository() -> Result<()>;

// Constants: SCREAMING_SNAKE_CASE
const MAX_FILE_SIZE: usize = 10_485_760;

// Type parameters: PascalCase, single letter preferred
fn process<T: Parser>(parser: T);
```

#### Error Handling
```rust
// Use anyhow for application errors
use anyhow::{Context, Result};

// Use thiserror for library errors
#[derive(Debug, thiserror::Error)]
pub enum AnalysisError {
    #[error("Failed to parse file: {0}")]
    ParseError(String),
    
    #[error("Database error")]
    DatabaseError(#[from] sqlx::Error),
}

// Always add context to errors
let content = fs::read_to_string(&path)
    .with_context(|| format!("Failed to read file: {}", path.display()))?;
```

#### Documentation
```rust
/// Brief description of the function.
///
/// # Arguments
/// * `repo_url` - URL of the repository to analyze
/// * `options` - Analysis configuration options
///
/// # Returns
/// * `Result<AnalysisResult>` - Analysis results or error
///
/// # Errors
/// * `ParseError` - If the repository cannot be parsed
/// * `NetworkError` - If the repository cannot be accessed
///
/// # Examples
/// ```
/// let result = analyze_repository("https://github.com/rust-lang/rust", &options)?;
/// assert!(result.files_analyzed > 0);
/// ```
pub fn analyze_repository(repo_url: &str, options: &Options) -> Result<AnalysisResult> {
    // Implementation
}
```

### Code Quality Standards

#### Clippy Lints
```toml
# In Cargo.toml
[lints.clippy]
all = "warn"
pedantic = "warn"
nursery = "warn"
cargo = "warn"

# Allow specific lints when justified
module_name_repetitions = "allow"
must_use_candidate = "allow"
```

#### Safety Requirements
```rust
// Document all unsafe blocks
unsafe {
    // SAFETY: The pointer is valid because we just created it
    // and the lifetime is guaranteed by the parent struct
    let data = ptr::read(raw_ptr);
}

// Prefer safe alternatives
use std::sync::Arc; // Instead of raw pointers
use parking_lot::RwLock; // Instead of std::sync::RwLock
```

## 🧪 Testing Requirements

### Test Coverage Requirements
- Minimum 80% code coverage for new code
- 100% coverage for security-critical code
- All public APIs must have tests

### Test Organization
```
tests/
├── unit/           # Unit tests for individual components
├── integration/    # Integration tests with real dependencies
├── e2e/           # End-to-end tests
└── fixtures/      # Test data and fixtures
```

### Writing Tests
```rust
#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_parse_rust_file() {
        // Arrange
        let content = r#"fn main() { println!("Hello"); }"#;
        
        // Act
        let result = parse_content(content, Language::Rust);
        
        // Assert
        assert!(result.is_ok());
        let ast = result.unwrap();
        assert_eq!(ast.node_count(), 7);
    }

    #[tokio::test]
    async fn test_async_analysis() {
        // Async test implementation
    }

    #[test]
    #[should_panic(expected = "invalid language")]
    fn test_invalid_language() {
        parse_content("test", Language::Unknown).unwrap();
    }
}
```

### Integration Tests
```rust
// tests/integration/api_test.rs
use analysis_engine::test_helpers::*;

#[tokio::test]
async fn test_full_analysis_flow() {
    let app = spawn_test_app().await;
    
    let response = app
        .post("/api/v1/analyze")
        .json(&json!({
            "repository_url": "https://github.com/test/repo",
            "branch": "main"
        }))
        .send()
        .await;
    
    assert_eq!(response.status(), 200);
}
```

### Performance Tests
```rust
// benches/parser_bench.rs
use criterion::{black_box, criterion_group, criterion_main, Criterion};

fn benchmark_parse_large_file(c: &mut Criterion) {
    let content = include_str!("../fixtures/large_file.rs");
    
    c.bench_function("parse_large_rust_file", |b| {
        b.iter(|| {
            parse_content(black_box(content), Language::Rust)
        });
    });
}

criterion_group!(benches, benchmark_parse_large_file);
criterion_main!(benches);
```

## 🔄 Pull Request Process

### Before Creating a PR

1. **Create an Issue**
   - Discuss the change in an issue first
   - Get approval for significant changes
   - Reference the issue in your PR

2. **Branch Naming**
   ```
   feature/add-python-parser
   fix/memory-leak-in-cache
   docs/update-api-reference
   perf/optimize-tree-traversal
   security/fix-jwt-validation
   ```

3. **Commit Messages**
   ```
   feat: add support for Python 3.10 syntax
   fix: resolve memory leak in AST cache
   docs: update API examples for v2 endpoints
   perf: optimize tree traversal by 30%
   security: validate JWT expiration correctly
   
   BREAKING CHANGE: removed deprecated analyze_sync method
   ```

### PR Checklist

- [ ] Tests pass locally (`cargo test`)
- [ ] Code formatted (`cargo fmt`)
- [ ] Clippy warnings resolved (`cargo clippy`)
- [ ] Documentation updated
- [ ] CHANGELOG.md updated
- [ ] Security impact assessed
- [ ] Performance impact measured
- [ ] Breaking changes documented

### PR Template
```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Breaking change
- [ ] Documentation update

## Testing
- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] Manual testing completed

## Checklist
- [ ] My code follows the project style
- [ ] I have added tests for my changes
- [ ] Documentation has been updated
- [ ] No security vulnerabilities introduced
```

### Review Process

1. **Automated Checks**
   - CI/CD pipeline must pass
   - Code coverage maintained
   - Security scan clean

2. **Code Review**
   - Minimum 2 approvals required
   - Security team approval for security changes
   - Performance team approval for critical paths

3. **Merge Requirements**
   - Squash and merge for features
   - Rebase and merge for fixes
   - No force pushes to main

## 🔒 Security Guidelines

### Security Best Practices

1. **Input Validation**
   ```rust
   // Validate all external input
   fn validate_url(url: &str) -> Result<Url> {
       let parsed = Url::parse(url)?;
       
       // Only allow HTTPS for security
       if parsed.scheme() != "https" {
           return Err(anyhow!("Only HTTPS URLs allowed"));
       }
       
       Ok(parsed)
   }
   ```

2. **Authentication & Authorization**
   ```rust
   // Always verify JWT tokens
   let claims = verify_jwt(&token)?;
   
   // Check permissions
   if !claims.has_permission("analyze:write") {
       return Err(AuthError::InsufficientPermissions);
   }
   ```

3. **Sensitive Data Handling**
   ```rust
   // Never log sensitive data
   tracing::info!(
       user_id = %user.id,
       // Don't log: password, api_key, jwt_token
       "User authenticated"
   );
   
   // Use secure random generation
   use rand::{thread_rng, Rng};
   let token: String = thread_rng()
       .sample_iter(&Alphanumeric)
       .take(32)
       .map(char::from)
       .collect();
   ```

### Security Review Requirements

- All PRs with security implications require security team review
- Run `cargo audit` before submitting PR
- No new dependencies without security justification
- Follow OWASP guidelines for web security

## 📚 Documentation Standards

### Code Documentation
- All public APIs must have doc comments
- Include examples for complex functions
- Document panic conditions
- Keep documentation up-to-date with code

### API Documentation
```rust
/// Analyzes a repository and returns comprehensive results.
///
/// This endpoint triggers a full analysis of the specified repository,
/// including AST parsing, security scanning, and pattern detection.
///
/// # Request
/// ```json
/// {
///   "repository_url": "https://github.com/rust-lang/rust",
///   "branch": "master",
///   "enable_security": true
/// }
/// ```
///
/// # Response
/// ```json
/// {
///   "analysis_id": "550e8400-e29b-41d4-a716-************",
///   "status": "completed",
///   "results": { ... }
/// }
/// ```
#[post("/api/v1/analyze")]
pub async fn analyze_repository(/* ... */) -> Result<Json<AnalysisResult>> {
    // Implementation
}
```

### Markdown Documentation
- Use proper headings hierarchy
- Include code examples
- Add diagrams where helpful
- Keep README concise, detailed docs in `/docs`

## 🤝 Community Guidelines

### Code of Conduct
We follow the [Rust Code of Conduct](https://www.rust-lang.org/policies/code-of-conduct).

### Communication Channels
- GitHub Issues: Bug reports and feature requests
- GitHub Discussions: General questions and discussions
- Slack: Real-time chat (invite required)

### Getting Help
- Check existing documentation
- Search closed issues
- Ask in discussions
- Tag maintainers for urgent issues

### Recognition
Contributors are recognized in:
- CONTRIBUTORS.md file
- Release notes
- Annual contributor report

---

Thank you for contributing to Analysis Engine! Your efforts help make code analysis better for everyone.