# 🔒 Analysis Engine - CSRF Protection Implementation

> **Context Engineering**: This documentation covers the CSRF (Cross-Site Request Forgery) protection implementation in the Analysis Engine, following OWASP guidelines and the EPIS-003 security resolution.

## 📋 Overview

The Analysis Engine implements comprehensive CSRF protection using the Double Submit Cookie pattern. This implementation was added as part of the EPIS-003 critical security gap resolution to protect all state-changing operations from cross-site request forgery attacks.

## 🛡️ Implementation Details

### Protection Pattern

The service uses the **Double Submit Cookie** pattern, which is recommended for stateless applications:

1. **Token Generation**: Server generates a cryptographically secure token
2. **Cookie Storage**: Token is stored in an HttpOnly, Secure, SameSite cookie
3. **Header Requirement**: Client must include the token in a custom header
4. **Dual Validation**: Server validates both cookie and header match

### Technical Implementation

#### Token Generation
```rust
// HMAC-SHA256 with secure random data
let token_data = format!("{}.{}.{}", user_id, timestamp, random_bytes);
let token = hmac_sha256(&token_data, &secret_key);
```

#### Cookie Configuration
```rust
Cookie::build("csrf_token", token.clone())
    .http_only(true)
    .secure(true)
    .same_site(SameSite::Strict)
    .max_age(Duration::hours(1))
    .path("/")
    .finish()
```

#### Middleware Integration
```rust
// Applied to all protected routes
.layer(middleware::from_fn_with_state(
    state.clone(),
    api::middleware::csrf_middleware,
))
```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `CSRF_SECRET_KEY` | Secret key for HMAC signing | Generated on startup |
| `CSRF_TOKEN_EXPIRY` | Token expiration time | 3600 seconds (1 hour) |
| `CSRF_HEADER_NAME` | Header name for token | `X-CSRF-Token` |

### Security Settings

- **Token Lifetime**: 1 hour (configurable)
- **Cookie Settings**: 
  - HttpOnly: Prevents JavaScript access
  - Secure: HTTPS only
  - SameSite=Strict: Prevents cross-site submission
- **Token Format**: HMAC-SHA256 signed data

## 📡 API Usage

### Getting a CSRF Token

Before making any state-changing requests, clients must obtain a CSRF token:

```http
GET /api/v1/csrf-token
Authorization: Bearer <jwt_token>
```

**Response**:
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expires_at": "2025-08-06T13:00:00Z"
}
```

The token is also set as a cookie in the response.

### Using the CSRF Token

Include the token in the `X-CSRF-Token` header for all POST, PUT, DELETE requests:

```http
POST /api/v1/analysis
Authorization: Bearer <jwt_token>
X-CSRF-Token: <csrf_token>
Content-Type: application/json

{
  "repository_url": "https://github.com/user/repo"
}
```

### Error Responses

Missing or invalid CSRF token:
```json
{
  "error": {
    "code": "CSRF_VALIDATION_FAILED",
    "message": "Invalid or missing CSRF token"
  }
}
```

## 🔍 Monitoring

### CSRF Protection Status Endpoint

```http
GET /security/csrf-status
```

**Response**:
```json
{
  "csrf_protection_enabled": true,
  "token_expiry_seconds": 3600,
  "tokens_issued": 1250,
  "tokens_validated": 1200,
  "validation_failures": 15,
  "last_token_rotation": "2025-08-06T11:00:00Z"
}
```

### Metrics

The following metrics are tracked:
- Total tokens issued
- Successful validations
- Failed validations
- Token expiration events
- Average validation time

## 🏗️ Implementation Architecture

### Middleware Stack Order

1. **Request ID** - Assigns unique ID to each request
2. **Metrics** - Starts timing and tracking
3. **Security Headers** - Sets security headers
4. **Rate Limiting** - Enforces rate limits
5. **CSRF Protection** - Validates CSRF tokens ← 
6. **Authentication** - Validates JWT/API keys
7. **Route Handler** - Processes the request

### Code Structure

```
src/api/middleware/
├── csrf.rs              # CSRF middleware implementation
│   ├── CsrfMiddleware   # Main middleware struct
│   ├── CsrfToken        # Token generation and validation
│   └── CsrfConfig       # Configuration handling
```

## 🧪 Testing

### Validation Script

A comprehensive validation script is available:

```javascript
// services/analysis-engine/validate_csrf.js
node validate_csrf.js
```

This script tests:
- Token generation
- Cookie setting
- Header validation
- Error handling
- Expiration behavior

### Manual Testing

```bash
# Get CSRF token
curl -c cookies.txt -H "Authorization: Bearer $JWT" \
  https://analysis-engine.../api/v1/csrf-token

# Use token in request
CSRF_TOKEN=$(cat cookies.txt | grep csrf_token | awk '{print $7}')
curl -b cookies.txt \
  -H "Authorization: Bearer $JWT" \
  -H "X-CSRF-Token: $CSRF_TOKEN" \
  -X POST https://analysis-engine.../api/v1/analysis
```

## 🚨 Security Considerations

### Threat Model

**Protected Against**:
- Cross-site request forgery attacks
- Session riding
- One-click attacks
- Malicious link exploitation

**Requirements**:
- HTTPS must be enabled (for Secure cookie)
- Clients must support cookies
- JavaScript clients must read and send token header

### Best Practices

1. **Token Rotation**: Tokens expire after 1 hour
2. **Per-Session Tokens**: Each session gets unique token
3. **Strict Validation**: Both cookie and header must match
4. **No GET Mutations**: Only POST/PUT/DELETE require tokens
5. **Error Logging**: All validation failures are logged

## 🔗 Integration Examples

### JavaScript/Axios
```javascript
// Get CSRF token on app initialization
const csrfResponse = await axios.get('/api/v1/csrf-token');
const csrfToken = csrfResponse.data.token;

// Include in all requests
axios.defaults.headers.common['X-CSRF-Token'] = csrfToken;
```

### Python/Requests
```python
# Get CSRF token
session = requests.Session()
csrf_resp = session.get(f"{API_BASE}/api/v1/csrf-token", 
                        headers={"Authorization": f"Bearer {token}"})
csrf_token = csrf_resp.json()["token"]

# Use in requests
response = session.post(
    f"{API_BASE}/api/v1/analysis",
    headers={
        "Authorization": f"Bearer {token}",
        "X-CSRF-Token": csrf_token
    },
    json=data
)
```

## 📝 Compliance

This CSRF implementation meets:
- **OWASP CSRF Prevention Cheat Sheet** guidelines
- **NIST 800-63B** session management requirements
- **PCI DSS 6.5.9** CSRF protection requirements
- **SOC 2** security control requirements

## 🔄 Migration Guide

For existing API clients:

1. **Update client code** to fetch CSRF token on initialization
2. **Store token** from response or cookie
3. **Include header** in all state-changing requests
4. **Handle 403 errors** by refreshing token

## 📞 Support

For CSRF protection issues:
- Check the [API Reference](../api/reference.md#csrf-protection)
- Review [Security Guide](./security-guide.md)
- Contact security team for assistance