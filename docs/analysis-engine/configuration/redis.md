# Redis Configuration for Analysis Engine

## Production Configuration (Deployed: 2025-07-14)

The Redis instance has been successfully created and is ready for use.

### Instance Details

```bash
Name: analysis-engine-cache
Tier: BASIC
Memory: 4GB
Version: Redis 7.0
State: READY
Region: us-central1
Network: default
```

### Connection Details

```bash
REDIS_HOST=***********
REDIS_PORT=6379
REDIS_URL=redis://***********:6379
```

### Important Notes

1. **VPC Connector Required**: Cloud Run services need a VPC connector to access Redis
2. **Private IP Only**: Redis is only accessible within the VPC (no public IP)
3. **No Authentication**: Basic tier doesn't require authentication
4. **Auto-eviction**: With 4GB memory, can cache ~100K analysis results

## Setup Instructions

1. **Create VPC Connector** (if not exists):
   ```bash
   ./scripts/setup-vpc-connector.sh
   ```

2. **Update Cloud Run Service**:
   The setup script will automatically update the service with:
   - Redis URL environment variable
   - VPC connector configuration

3. **Verify Connection**:
   Check logs for "Redis connection pool created successfully"

## Monitoring

### Key Metrics
- Memory Usage: Monitor in Cloud Console
- Connection Count: Should stay under 65K
- Hit Rate: Target >80% for optimal performance
- Eviction Rate: Should be minimal with 4GB

### Useful Commands

```bash
# Get Redis instance details
gcloud redis instances describe analysis-engine-cache \
    --region=us-central1 \
    --project=vibe-match-463114

# View Redis metrics
gcloud monitoring time-series list \
    --filter='metric.type="redis.googleapis.com/stats/memory/usage_ratio" AND resource.labels.instance_id="analysis-engine-cache"' \
    --project=vibe-match-463114

# Check Cloud Run logs for Redis
gcloud logging read "resource.type=cloud_run_revision AND textPayload:Redis" \
    --limit=10 \
    --project=vibe-match-463114
```

## Cost Information

- **Instance Type**: Basic Tier (no replication)
- **Hourly Cost**: ~$0.196/hour (4GB @ $0.049/GB/hour)
- **Monthly Cost**: ~$142/month
- **Network**: Egress charges apply for cross-zone traffic

## Performance Tuning

The Analysis Engine uses Redis for:
- Analysis result caching (TTL: 24 hours)
- Rate limiting counters (TTL: 1 hour)
- Session data (TTL: 1 hour)
- Temporary computation results (TTL: 15 minutes)

Cache keys are structured as:
- `analysis:{repo_id}:{commit_hash}` - Full analysis results
- `file:{hash}:{language}` - Individual file analysis
- `rate:{user_id}:{window}` - Rate limit counters