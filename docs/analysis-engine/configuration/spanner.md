# Spanner Configuration for Analysis Engine

## Production Configuration (Verified: 2025-07-14)

The Spanner instance and database have been successfully created and verified.

### Connection Details

```bash
SPANNER_PROJECT_ID=vibe-match-463114
SPANNER_INSTANCE_ID=ccl-instance
SPANNER_DATABASE_ID=ccl_main
```

### Instance Details

- **Instance Name**: `projects/vibe-match-463114/instances/ccl-instance`
- **Configuration**: `projects/vibe-match-463114/instanceConfigs/regional-us-central1`
- **Node Count**: 1
- **State**: READY
- **Region**: us-central1 (regional configuration)

### Database Details

- **Database Name**: `ccl_main`
- **Created**: 2025-07-14
- **Tables**: Will be created automatically on first analysis run
- **State**: READY

### Service Account Permissions

- **Service Account**: `<EMAIL>`
- **Permissions**: ✅ Has Spanner permissions (verified)

## Important Notes

1. The database `ccl_main` was created fresh and contains no tables yet
2. The Analysis Engine will automatically create required tables on first run
3. The service account already has the necessary Spanner permissions
4. The instance is using 1 node in a regional configuration (us-central1)

## Next Steps

1. Deploy the Analysis Engine with these Spanner credentials
2. The service will automatically initialize the database schema
3. Monitor table creation in the Cloud Console
4. Set up Spanner monitoring and alerts

## Monitoring

Monitor the following in Cloud Console:
- CPU utilization (should stay below 65% for single node)
- Storage usage
- Read/write operations per second
- Latency metrics

## Cost Information

- **Instance Type**: Regional (1 node)
- **Estimated Cost**: ~$0.90/hour for single node
- **Storage Cost**: $0.30/GB/month
- **Consider**: Scaling to 2+ nodes for production workloads