# Episteme Platform Disaster Recovery Plan

**Document Version**: 1.0  
**Last Updated**: January 2025  
**Classification**: Internal  
**Owner**: Platform Engineering Team

## Executive Summary

This disaster recovery (DR) plan provides comprehensive procedures for the Episteme platform - an AI-powered code intelligence platform built on Google Cloud Platform. The plan ensures business continuity with defined Recovery Time Objectives (RTO) and Recovery Point Objectives (RPO) across all critical services.

### Platform Overview
- **Analysis Engine** (Rust): Code parsing and AST analysis
- **Query Intelligence** (Python): AI/ML natural language processing  
- **Pattern Mining** (Python): ML-powered pattern detection
- **Marketplace** (Go): Pattern sharing and commerce platform

## 1. Recovery Objectives (RPO/RTO)

### Service Classification and Targets

| Service | Criticality | RTO Target | RPO Target | Availability SLA |
|---------|-------------|------------|------------|------------------|
| Analysis Engine | Critical | 15 minutes | 5 minutes | 99.99% |
| Query Intelligence | Critical | 15 minutes | 5 minutes | 99.99% |
| Pattern Mining | Important | 30 minutes | 15 minutes | 99.9% |
| Marketplace | Important | 1 hour | 15 minutes | 99.9% |
| Web Frontend | Standard | 30 minutes | 1 hour | 99.5% |

### Infrastructure Components

| Component | RTO | RPO | Backup Frequency |
|-----------|-----|-----|------------------|
| Spanner Database | 5 minutes | 1 minute | Continuous |
| BigQuery Analytics | 15 minutes | 5 minutes | Every 6 hours |
| Redis Cache | 10 minutes | 15 minutes | Every 4 hours |
| Cloud Storage | 5 minutes | 1 minute | Real-time |
| Container Images | 15 minutes | N/A | On deployment |

## 2. Architecture and Dependencies

### Regional Architecture
```
Primary Region: us-central1 (Iowa)
├── Cloud Run Services (2-50 instances per service)
├── Spanner Multi-region (3 nodes)
├── BigQuery (US multi-region)
├── Memorystore Redis (5GB, HA)
└── Cloud Storage (Multi-region)

Secondary Region: us-east1 (South Carolina)
├── Cloud Run Services (0-10 standby instances)
├── Spanner Read Replicas
├── BigQuery Cross-region backups
└── Cloud Storage Replication
```

### Service Dependencies
```mermaid
graph TD
    A[Analysis Engine] --> D[Spanner DB]
    A --> E[Redis Cache]
    B[Query Intelligence] --> D
    B --> F[BigQuery]
    B --> G[Vertex AI]
    C[Pattern Mining] --> D
    C --> F
    C --> E
    H[Web Frontend] --> A
    H --> B
    H --> C
```

### External Dependencies
- **Gemini API**: AI/ML processing (has fallback to Vertex AI)
- **Auth0**: Authentication (has local fallback)
- **Stripe**: Payment processing (degraded mode available)
- **GitHub**: Repository access (read-only mode for cached data)

## 3. Backup Procedures

### 3.1 Database Backups

#### Spanner Database
```bash
# Automated backup creation (daily at 2 AM UTC)
gcloud spanner backups create backup-$(date +%Y%m%d) \
    --database=episteme-prod \
    --instance=episteme-prod-instance \
    --expiration-date=$(date -d "+30 days" '+%Y-%m-%dT%H:%M:%SZ')

# Cross-region backup replication
gcloud spanner backups copy backup-$(date +%Y%m%d)-dr \
    --source-backup=backup-$(date +%Y%m%d) \
    --source-instance=episteme-prod-instance \
    --destination-instance=episteme-dr-instance \
    --destination-region=us-east1
```

#### BigQuery Analytics
```bash
# Dataset export (every 6 hours)
bq extract --destination_format=AVRO \
    --compression=SNAPPY \
    'episteme-prod:analytics.*' \
    'gs://episteme-backups/bigquery/$(date +%Y%m%d_%H)/*.avro'

# Cross-region dataset copy
bq cp \
    --source_project_id=episteme-prod \
    --destination_project_id=episteme-dr \
    analytics:pattern_cache \
    analytics_dr:pattern_cache_$(date +%Y%m%d)
```

### 3.2 Application Backups

#### Container Images
```bash
# Backup current production images
SERVICES=("analysis-engine" "query-intelligence" "pattern-mining" "marketplace")

for service in "${SERVICES[@]}"; do
    CURRENT_IMAGE=$(gcloud run services describe $service \
        --region=us-central1 \
        --format="value(spec.template.spec.containers[0].image)")
    
    # Tag and push to DR registry
    docker pull $CURRENT_IMAGE
    docker tag $CURRENT_IMAGE gcr.io/episteme-dr/$service:backup-$(date +%Y%m%d)
    docker push gcr.io/episteme-dr/$service:backup-$(date +%Y%m%d)
done
```

#### Configuration and Secrets
```bash
# Export service configurations
mkdir -p /tmp/config-backup
for service in "${SERVICES[@]}"; do
    gcloud run services describe $service \
        --region=us-central1 \
        --format=export > /tmp/config-backup/$service-config.yaml
done

# Backup secrets (metadata only, values handled separately)
gcloud secrets list --format="value(name)" > /tmp/config-backup/secrets-list.txt

# Upload to secure storage
gsutil -m cp -r /tmp/config-backup gs://episteme-secure-backups/config/$(date +%Y%m%d)/
```

### 3.3 Redis Cache Backup
```bash
# Create Redis backup
gcloud redis instances export episteme-cache \
    --destination=gs://episteme-backups/redis/cache-$(date +%Y%m%d_%H%M%S).rdb \
    --region=us-central1

# Verify backup completion
gsutil stat gs://episteme-backups/redis/cache-$(date +%Y%m%d_%H%M%S).rdb
```

## 4. Recovery Procedures

### 4.1 Service-Level Recovery

#### Single Service Failure
```bash
# 1. Assess service health
curl -H "Authorization: Bearer $(gcloud auth print-identity-token)" \
    https://analysis-engine-url/health

# 2. Check recent deployments
gcloud run revisions list --service=analysis-engine --region=us-central1 --limit=5

# 3. Rollback to previous version if needed
PREVIOUS_REVISION=$(gcloud run revisions list \
    --service=analysis-engine \
    --format="value(metadata.name)" \
    --limit=2 | tail -1)

gcloud run services update-traffic analysis-engine \
    --to-revisions=$PREVIOUS_REVISION=100 \
    --region=us-central1

# 4. Verify service recovery
./scripts/health-check.sh analysis-engine
```

#### Database Recovery
```bash
# 1. Create new database from backup
LATEST_BACKUP=$(gcloud spanner backups list \
    --instance=episteme-prod-instance \
    --format="value(name)" \
    --sort-by="~createTime" \
    --limit=1)

gcloud spanner databases restore episteme-prod-restored \
    --source-backup=$LATEST_BACKUP \
    --instance=episteme-prod-instance

# 2. Validate data integrity
gcloud spanner databases execute-sql episteme-prod-restored \
    --instance=episteme-prod-instance \
    --sql="SELECT COUNT(*) FROM repositories; SELECT COUNT(*) FROM analysis_results;"

# 3. Switch application traffic
# Update connection strings in secrets
gcloud secrets versions add DATABASE_URL \
    --data-file=<(echo "spanner://projects/episteme-prod/instances/episteme-prod-instance/databases/episteme-prod-restored")

# 4. Restart services to pick up new database
for service in "${SERVICES[@]}"; do
    gcloud run services update $service --region=us-central1 --no-traffic
    sleep 10
    gcloud run services update-traffic $service --to-latest --region=us-central1
done
```

### 4.2 Regional Disaster Recovery

#### Primary Region Failure
```bash
#!/bin/bash
# Regional failover script

echo "Starting regional disaster recovery procedure..."

# 1. Verify primary region is down
PRIMARY_HEALTH=$(curl -s -o /dev/null -w "%{http_code}" https://api.episteme.dev/health || echo "000")
if [ "$PRIMARY_HEALTH" != "200" ]; then
    echo "Primary region confirmed down, initiating failover..."
else
    echo "Primary region appears healthy, manual verification required"
    exit 1
fi

# 2. Scale up DR region services
echo "Scaling up services in DR region..."
SERVICES=("analysis-engine" "query-intelligence" "pattern-mining" "marketplace")

for service in "${SERVICES[@]}"; do
    gcloud run services update $service \
        --region=us-east1 \
        --min-instances=2 \
        --max-instances=20 \
        --project=episteme-dr
done

# 3. Update DNS to point to DR region
gcloud dns record-sets update api.episteme.dev \
    --type=A \
    --zone=episteme-zone \
    --rrdatas=**************  # DR region load balancer IP

# 4. Verify services in DR region
sleep 60  # Allow time for scaling
for service in "${SERVICES[@]}"; do
    echo "Checking $service in DR region..."
    curl -f https://$service-dr.episteme.dev/health || echo "WARNING: $service health check failed"
done

# 5. Notify stakeholders
./scripts/notify-stakeholders.sh "DR_ACTIVATED" "Primary region failure - services running in DR region"

echo "Regional failover completed. Monitor services and prepare for primary region recovery."
```

#### Primary Region Recovery
```bash
#!/bin/bash
# Primary region recovery script

echo "Starting primary region recovery procedure..."

# 1. Verify primary region services are healthy
PRIMARY_HEALTH=$(curl -s -o /dev/null -w "%{http_code}" https://api.episteme.dev/health)
if [ "$PRIMARY_HEALTH" != "200" ]; then
    echo "Primary region not ready for recovery"
    exit 1
fi

# 2. Sync data from DR region to primary
echo "Synchronizing data from DR to primary region..."

# Spanner sync (automatic with multi-region setup)
# BigQuery sync
bq cp \
    --source_project_id=episteme-dr \
    --destination_project_id=episteme-prod \
    analytics_dr:pattern_cache_latest \
    analytics:pattern_cache

# Redis sync
gcloud redis instances import episteme-cache \
    --source=gs://episteme-backups/redis/dr-sync-$(date +%Y%m%d).rdb \
    --region=us-central1

# 3. Gradually shift traffic back to primary region
echo "Shifting traffic back to primary region..."

# Start with 10% traffic
gcloud compute url-maps update episteme-lb \
    --default-service=episteme-backend-primary \
    --set-host-rule="api.episteme.dev/*=90%:episteme-backend-dr,10%:episteme-backend-primary"

sleep 300  # Monitor for 5 minutes

# Increase to 50%
gcloud compute url-maps update episteme-lb \
    --set-host-rule="api.episteme.dev/*=50%:episteme-backend-dr,50%:episteme-backend-primary"

sleep 300  # Monitor for 5 minutes

# Complete cutover
gcloud compute url-maps update episteme-lb \
    --default-service=episteme-backend-primary

# 4. Scale down DR region
for service in "${SERVICES[@]}"; do
    gcloud run services update $service \
        --region=us-east1 \
        --min-instances=0 \
        --max-instances=2 \
        --project=episteme-dr
done

echo "Primary region recovery completed."
```

### 4.3 Data Corruption Recovery

#### Database Corruption
```bash
#!/bin/bash
# Data corruption recovery procedure

echo "Starting data corruption recovery..."

# 1. Immediately enable read-only mode
for service in "${SERVICES[@]}"; do
    gcloud run services update $service \
        --set-env-vars="READ_ONLY_MODE=true" \
        --region=us-central1
done

# 2. Assess corruption extent
gcloud spanner databases execute-sql episteme-prod \
    --instance=episteme-prod-instance \
    --sql="
    SELECT 
        table_name,
        COUNT(*) as total_rows,
        COUNT(CASE WHEN created_at IS NULL THEN 1 END) as null_created_at,
        COUNT(CASE WHEN updated_at IS NULL THEN 1 END) as null_updated_at
    FROM information_schema.tables t
    JOIN information_schema.columns c ON t.table_name = c.table_name
    GROUP BY table_name;"

# 3. Restore from point-in-time backup
CORRUPTION_TIME="2025-01-15T14:30:00Z"  # Time when corruption was detected
RESTORE_TIME=$(date -d "$CORRUPTION_TIME - 1 hour" '+%Y-%m-%dT%H:%M:%SZ')

gcloud spanner databases restore episteme-prod-clean \
    --source-database=episteme-prod \
    --source-instance=episteme-prod-instance \
    --restore-time=$RESTORE_TIME

# 4. Validate restored data
gcloud spanner databases execute-sql episteme-prod-clean \
    --instance=episteme-prod-instance \
    --sql="SELECT COUNT(*) as repository_count FROM repositories"

# 5. Switch to clean database
gcloud secrets versions add DATABASE_URL \
    --data-file=<(echo "spanner://projects/episteme-prod/instances/episteme-prod-instance/databases/episteme-prod-clean")

# 6. Restart services with clean database
for service in "${SERVICES[@]}"; do
    gcloud run services update $service \
        --unset-env-vars="READ_ONLY_MODE" \
        --region=us-central1
done

echo "Data corruption recovery completed. Monitor for data consistency."
```

## 5. Testing Procedures

### 5.1 Monthly DR Tests

#### Service Failover Test
```bash
#!/bin/bash
# Monthly service failover test

echo "Starting monthly DR test - $(date)"

# 1. Test service rollback capability
TEST_SERVICE="analysis-engine"
CURRENT_REVISION=$(gcloud run services describe $TEST_SERVICE \
    --region=us-central1 \
    --format="value(status.latestReadyRevisionName)")

PREVIOUS_REVISION=$(gcloud run revisions list \
    --service=$TEST_SERVICE \
    --format="value(metadata.name)" \
    --limit=2 | tail -1)

# Rollback to previous version
gcloud run services update-traffic $TEST_SERVICE \
    --to-revisions=$PREVIOUS_REVISION=100 \
    --region=us-central1

# Verify service health
sleep 30
curl -f https://analysis-engine-url/health

# Rollback to current version
gcloud run services update-traffic $TEST_SERVICE \
    --to-revisions=$CURRENT_REVISION=100 \
    --region=us-central1

echo "Service failover test completed successfully"
```

#### Backup Restoration Test
```bash
#!/bin/bash
# Test database backup restoration

# 1. Create test database from latest backup
LATEST_BACKUP=$(gcloud spanner backups list \
    --instance=episteme-prod-instance \
    --format="value(name)" \
    --sort-by="~createTime" \
    --limit=1)

TEST_DB="episteme-test-restore-$(date +%Y%m%d)"

gcloud spanner databases restore $TEST_DB \
    --source-backup=$LATEST_BACKUP \
    --instance=episteme-prod-instance

# 2. Run data validation queries
REPO_COUNT=$(gcloud spanner databases execute-sql $TEST_DB \
    --instance=episteme-prod-instance \
    --sql="SELECT COUNT(*) as count FROM repositories" \
    --format="value(rows[0][0])")

if [ "$REPO_COUNT" -gt 0 ]; then
    echo "Backup restoration test PASSED - $REPO_COUNT repositories restored"
else
    echo "Backup restoration test FAILED - No data found"
fi

# 3. Cleanup test database
gcloud spanner databases delete $TEST_DB --instance=episteme-prod-instance --quiet

echo "Backup restoration test completed"
```

### 5.2 Quarterly DR Drills

#### Full Regional Failover Drill
```bash
#!/bin/bash
# Quarterly full DR drill

echo "Starting quarterly DR drill - $(date)"

# 1. Simulate primary region failure
echo "Simulating primary region failure..."
gcloud compute firewall-rules create block-primary-region \
    --action=DENY \
    --rules=all \
    --source-ranges=0.0.0.0/0 \
    --target-tags=primary-region

# 2. Execute DR failover
./scripts/regional-failover.sh

# 3. Validate DR region functionality
sleep 120
for service in "${SERVICES[@]}"; do
    HEALTH_STATUS=$(curl -s -o /dev/null -w "%{http_code}" https://$service-dr.episteme.dev/health)
    if [ "$HEALTH_STATUS" = "200" ]; then
        echo "$service DR: HEALTHY"
    else
        echo "$service DR: FAILED (HTTP $HEALTH_STATUS)"
    fi
done

# 4. Test data operations in DR
gcloud spanner databases execute-sql episteme-prod \
    --instance=episteme-dr-instance \
    --sql="INSERT INTO test_dr_table (id, test_data, created_at) VALUES (GENERATE_UUID(), 'DR drill test', CURRENT_TIMESTAMP())"

# 5. Restore primary region
echo "Restoring primary region access..."
gcloud compute firewall-rules delete block-primary-region --quiet

# 6. Execute recovery procedure
./scripts/primary-region-recovery.sh

echo "Quarterly DR drill completed. Review logs and update procedures as needed."
```

### 5.3 Annual Chaos Engineering

#### Comprehensive Failure Simulation
```bash
#!/bin/bash
# Annual chaos engineering test

echo "Starting annual chaos engineering test - $(date)"

# 1. Random service failure injection
RANDOM_SERVICE=${SERVICES[$RANDOM % ${#SERVICES[@]}]}
echo "Injecting failure in $RANDOM_SERVICE"

# Introduce artificial latency and errors
gcloud run services update $RANDOM_SERVICE \
    --set-env-vars="CHAOS_LATENCY_MS=5000,CHAOS_ERROR_RATE=0.3" \
    --region=us-central1

# 2. Database connection disruption
gcloud compute firewall-rules create chaos-block-spanner \
    --action=DENY \
    --rules=tcp:9010 \
    --source-tags=cloud-run \
    --target-tags=spanner

# 3. Monitor system behavior
sleep 300  # 5 minutes of chaos

# Check error rates
gcloud logging read \
    'resource.labels.service_name="'$RANDOM_SERVICE'" AND severity>=ERROR' \
    --limit=50 --format=json | jq '.[] | .timestamp'

# 4. Verify circuit breakers and fallbacks
curl -H "Authorization: Bearer $(gcloud auth print-identity-token)" \
    https://api.episteme.dev/health/circuit-breaker

# 5. Restore normal operations
gcloud run services update $RANDOM_SERVICE \
    --unset-env-vars="CHAOS_LATENCY_MS,CHAOS_ERROR_RATE" \
    --region=us-central1

gcloud compute firewall-rules delete chaos-block-spanner --quiet

echo "Chaos engineering test completed. Analyze metrics and improve resilience."
```

## 6. Monitoring and Alerting

### 6.1 DR-Specific Monitoring

#### Backup Health Monitoring
```bash
# Create backup monitoring metrics
gcloud logging metrics create backup_success_count \
    --description="Count of successful backups" \
    --log-filter='resource.type="cloud_function"
                  AND jsonPayload.message="Backup completed successfully"'

gcloud logging metrics create backup_failure_count \
    --description="Count of failed backups" \
    --log-filter='resource.type="cloud_function"
                  AND jsonPayload.message="Backup failed"'
```

#### Cross-Region Latency Monitoring
```bash
# Monitor cross-region latency
gcloud monitoring timeseries create \
    --timeseries='[{
        "metric": {
            "type": "custom.googleapis.com/dr/cross_region_latency",
            "labels": {
                "source_region": "us-central1",
                "target_region": "us-east1"
            }
        },
        "resource": {
            "type": "global",
            "labels": {}
        },
        "points": [{
            "interval": {
                "endTime": "'$(date -u +%Y-%m-%dT%H:%M:%SZ)'"
            },
            "value": {
                "doubleValue": 50.0
            }
        }]
    }]'
```

### 6.2 Alert Policies

#### Critical DR Alerts
```yaml
# dr-critical-alerts.yaml
displayName: "DR Critical Alerts"
conditions:
  - displayName: "Backup failure"
    conditionThreshold:
      filter: 'metric.type="logging.googleapis.com/user/backup_failure_count"'
      comparison: COMPARISON_GREATER_THAN
      thresholdValue: 0
      duration: 60s
  - displayName: "Cross-region latency high"
    conditionThreshold:
      filter: 'metric.type="custom.googleapis.com/dr/cross_region_latency"'
      comparison: COMPARISON_GREATER_THAN
      thresholdValue: 200.0
      duration: 300s
  - displayName: "Primary region unhealthy"
    conditionThreshold:
      filter: 'metric.type="compute.googleapis.com/instance/up"'
      comparison: COMPARISON_LESS_THAN
      thresholdValue: 0.9
      duration: 300s
notificationChannels:
  - "projects/episteme-prod/notificationChannels/pagerduty-dr"
  - "projects/episteme-prod/notificationChannels/slack-dr"
```

## 7. Communication and Escalation

### 7.1 Incident Communication

#### Status Page Updates
```bash
# Update status page during incidents
curl -X PATCH "https://api.statuspage.io/v1/pages/PAGE_ID/incidents/INCIDENT_ID" \
  -H "Authorization: OAuth TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "incident": {
      "status": "investigating",
      "message": "We are investigating reports of service degradation in our primary region."
    }
  }'
```

#### Stakeholder Notifications
```bash
#!/bin/bash
# stakeholder-notification.sh

INCIDENT_TYPE=$1
MESSAGE=$2

# Slack notification
curl -X POST -H 'Content-type: application/json' \
    --data '{
        "text":"🚨 DR Event: '$INCIDENT_TYPE'",
        "attachments":[{
            "color":"danger",
            "fields":[{
                "title":"Details",
                "value":"'$MESSAGE'",
                "short":false
            }]
        }]
    }' \
    $SLACK_WEBHOOK_URL

# Email notification to executives
python3 scripts/send-executive-alert.py \
    --subject="Episteme DR Event: $INCIDENT_TYPE" \
    --message="$MESSAGE" \
    --recipients="<EMAIL>,<EMAIL>"

# PagerDuty escalation
curl -X POST "https://events.pagerduty.com/v2/enqueue" \
  -H "Content-Type: application/json" \
  -d '{
    "routing_key": "PAGERDUTY_KEY",
    "event_action": "trigger",
    "payload": {
      "summary": "Episteme DR Event: '$INCIDENT_TYPE'",
      "source": "disaster-recovery-system",
      "severity": "critical"
    }
  }'
```

### 7.2 Escalation Matrix

| Incident Severity | Initial Response | Escalation (15 min) | Escalation (30 min) | Executive (60 min) |
|-------------------|------------------|---------------------|---------------------|---------------------|
| SEV1 (Complete Outage) | On-call Engineer | Platform Team Lead | Engineering Director | CTO |
| SEV2 (Major Degradation) | On-call Engineer | Platform Team Lead | Engineering Director | - |
| SEV3 (Minor Issues) | On-call Engineer | Platform Team Lead | - | - |

## 8. Post-Incident Procedures

### 8.1 Incident Documentation

#### Post-Mortem Template
```markdown
# Incident Post-Mortem: [INCIDENT_ID]

## Incident Summary
- **Start Time**: [TIMESTAMP]
- **End Time**: [TIMESTAMP]
- **Duration**: [MINUTES]
- **Impact**: [DESCRIPTION]
- **Root Cause**: [TECHNICAL_CAUSE]

## Timeline
| Time | Event | Actions Taken |
|------|-------|---------------|
| 14:00 | Service alerts triggered | On-call paged |
| 14:05 | Investigation started | Checked service logs |
| 14:15 | Root cause identified | Started recovery procedure |
| 14:30 | Services restored | Monitored for stability |

## What Went Well
- Alert system worked as expected
- Team responded quickly
- Recovery procedures were effective

## What Could Be Improved
- Earlier detection possible with better monitoring
- Recovery could be automated
- Communication could be more proactive

## Action Items
- [ ] Implement automated recovery for this scenario
- [ ] Add monitoring for early detection
- [ ] Update runbooks with lessons learned
- [ ] Schedule follow-up training session
```

### 8.2 Lessons Learned Integration

#### Procedure Updates
```bash
#!/bin/bash
# Update DR procedures based on lessons learned

# 1. Version current procedures
cp docs/disaster-recovery-plan.md docs/archive/dr-plan-v$(date +%Y%m%d).md

# 2. Update procedures with lessons learned
# (Manual editing required)

# 3. Update training materials
cp docs/disaster-recovery-plan.md training/dr-procedures-latest.md

# 4. Schedule training update
gcloud scheduler jobs create http dr-training-reminder \
    --schedule="0 9 1 * *" \  # First of each month
    --uri="https://training-system.com/schedule-dr-training" \
    --http-method=POST
```

## 9. Compliance and Audit

### 9.1 Regulatory Requirements

#### Data Protection Compliance
- **GDPR**: 72-hour breach notification requirement
- **SOC 2**: Continuous monitoring and incident documentation
- **ISO 27001**: Annual DR testing and documentation review

#### Audit Trail Maintenance
```bash
# Maintain comprehensive audit logs
gcloud logging sinks create dr-audit-sink \
    bigquery.googleapis.com/projects/episteme-audit/datasets/dr_logs \
    --log-filter='protoPayload.serviceName=~"spanner|run|storage"
                  AND (protoPayload.methodName=~"Backup|Restore|Failover"
                  OR jsonPayload.event_type="disaster_recovery")'

# Regular audit report generation
python3 scripts/generate-dr-audit-report.py \
    --start-date="2025-01-01" \
    --end-date="2025-01-31" \
    --output="/tmp/dr-audit-$(date +%Y%m).pdf"
```

### 9.2 Third-Party Assessments

#### Annual DR Assessment Checklist
- [ ] DR plan review and update
- [ ] Recovery procedure testing
- [ ] Staff training validation
- [ ] Vendor dependency assessment
- [ ] Technology refresh planning
- [ ] Cost optimization review
- [ ] Compliance gap analysis

## Appendices

### Appendix A: Emergency Contact Information

| Role | Primary | Secondary | Phone | Email |
|------|---------|-----------|--------|--------|
| Platform Team Lead | John Doe | Jane Smith | ******-0101 | <EMAIL> |
| Database Administrator | Alice Johnson | Bob Wilson | ******-0102 | <EMAIL> |
| Security Lead | Carol Brown | Dave Davis | ******-0103 | <EMAIL> |
| Engineering Director | Eve Miller | Frank Garcia | ******-0104 | <EMAIL> |

### Appendix B: Vendor Support Contacts

| Vendor | Support Level | Contact | Account # |
|--------|---------------|---------|-----------|
| Google Cloud | Premium | ************** | ACC-123456 |
| Auth0 | Enterprise | <EMAIL> | ORG-789012 |
| Stripe | Business | <EMAIL> | ACC-345678 |

### Appendix C: Infrastructure Details

#### Cloud Resource Inventory
```bash
# Primary region resources
gcloud compute instances list --zones=us-central1-a,us-central1-b,us-central1-c
gcloud run services list --region=us-central1
gcloud spanner instances describe episteme-prod-instance

# DR region resources  
gcloud compute instances list --zones=us-east1-a,us-east1-b,us-east1-c
gcloud run services list --region=us-east1
gcloud spanner instances describe episteme-dr-instance
```

### Appendix D: Cost Analysis

#### DR Infrastructure Costs (Monthly)
| Component | Primary Region | DR Region | Backup Storage | Total |
|-----------|----------------|-----------|----------------|--------|
| Cloud Run | $2,400 | $400 | - | $2,800 |
| Spanner | $6,000 | $2,000 | - | $8,000 |
| BigQuery | $800 | $200 | $300 | $1,300 |
| Redis | $400 | $100 | - | $500 |
| Storage | $200 | $50 | $150 | $400 |
| **Total** | **$9,800** | **$2,750** | **$450** | **$13,000** |

---

**Document Control**
- **Version**: 1.0
- **Approved By**: [Engineering Director]
- **Review Date**: [Quarterly]
- **Next Update**: [Annual or after major incidents]