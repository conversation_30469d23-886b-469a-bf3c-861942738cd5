# 🚀 GitHub Actions to Google Cloud CI/CD Migration Guide

This guide provides complete instructions for migrating from GitHub Actions to Google Cloud services for CI/CD pipelines.

## 📋 Overview

We're migrating from GitHub Actions (which has billing limitations) to Google Cloud services that provide:

- **Cost-effective pricing**: Pay-per-use with generous free tiers
- **Better integration**: Native GCP service integration  
- **Advanced deployment strategies**: Canary, blue-green, progressive deployments
- **Enterprise features**: Advanced monitoring, security scanning, compliance

## 🔄 Service Mapping

| GitHub Actions Feature | Google Cloud Alternative | Benefits |
|------------------------|---------------------------|----------|
| Workflows | Cloud Build | Native Docker builds, parallel steps |
| Deployments | Cloud Deploy | Progressive deployments, rollback |
| Secrets | Secret Manager | Enterprise-grade secret management |
| Artifacts | Artifact Registry | Vulnerability scanning, compliance |
| Security Scanning | Container Analysis | Integrated security scanning |
| Monitoring | Cloud Monitoring | Real-time metrics, alerting |

## 📁 Configuration Files Created

### 1. Main Build Configuration
- **File**: `cloudbuild.yaml`
- **Purpose**: Main monorepo build configuration
- **Features**: 
  - Multi-service detection and building
  - Parallel builds for changed services only
  - Security scanning with Container Analysis
  - Automatic staging deployment
  - Manual production approval

### 2. Service-Specific Builds
- **Analysis Engine**: `services/analysis-engine/cloudbuild-service.yaml`
- **Query Intelligence**: `services/query-intelligence/cloudbuild-service.yaml`
- **Pattern Mining**: Similar structure for Python services
- **Marketplace**: Similar structure for Go services

### 3. Deployment Pipeline
- **File**: `clouddeploy.yaml`
- **Features**:
  - Progressive deployment stages (dev → staging → production)
  - Canary deployments with automatic traffic splitting
  - Manual approval gates for production
  - Automated rollback on failure
  - Custom verification steps

### 4. Setup Script
- **File**: `setup-cloud-build.sh`
- **Purpose**: One-command setup of entire CI/CD pipeline
- **Actions**:
  - Enables required APIs
  - Creates Artifact Registry
  - Sets up IAM permissions
  - Creates build triggers
  - Configures monitoring

## 🛠️ Migration Steps

### Step 1: Prerequisites

1. **Google Cloud Project**: Ensure you have a GCP project with billing enabled
2. **Permissions**: Need Project Owner or equivalent permissions
3. **gcloud CLI**: Install and authenticate gcloud CLI
4. **GitHub Repository**: Repository should be accessible to Google Cloud Build

```bash
# Install gcloud CLI (if not already installed)
# https://cloud.google.com/sdk/docs/install

# Authenticate
gcloud auth login
gcloud config set project your-project-id
```

### Step 2: Run Setup Script

```bash
# Make script executable
chmod +x setup-cloud-build.sh

# Set environment variables (optional, defaults provided)
export PROJECT_ID="ccl-platform"
export REGION="us-central1"
export GITHUB_REPO="star-boy-95/episteme"

# Run setup
./setup-cloud-build.sh
```

### Step 3: Manual Configuration Steps

#### A. GitHub Repository Connection
1. Go to [Cloud Build Console](https://console.cloud.google.com/cloud-build/repos)
2. Click "Connect Repository"
3. Select GitHub and authenticate
4. Choose your repository
5. Complete connection setup

#### B. Update Secrets
```bash
# Update database connection
echo -n "your-actual-database-url" | gcloud secrets versions add database-url --data-file=-

# Update Redis connection  
echo -n "your-actual-redis-url" | gcloud secrets versions add redis-url --data-file=-
```

#### C. Configure Notifications
```bash
# Create email notification channel
gcloud alpha monitoring channels create \
  --display-name="Build Alerts" \
  --type=email \
  --channel-labels=email_address=<EMAIL>
```

### Step 4: Test the Pipeline

1. **Push to develop branch**: Should trigger development builds and deployments
2. **Push to main branch**: Should trigger staging builds with approval gate
3. **Monitor in Cloud Build Console**: Check build logs and deployment status

## 🏗️ Architecture Comparison

### Before (GitHub Actions)
```
GitHub Push → GitHub Actions → (Billing Issues) → ❌ Failed
```

### After (Google Cloud)
```
GitHub Push → Cloud Build Trigger → Build & Test → Security Scan
     ↓
Container Registry → Cloud Deploy → Development → Staging → Production
     ↓                                 ↓           ↓         ↓
   Security Scan              Auto Deploy    Approval   Manual Gate
```

## 💰 Cost Comparison

### GitHub Actions
- **Issue**: Billing/spending limit failures
- **Cost**: Variable, can be expensive for large teams
- **Free Tier**: Limited minutes per month

### Google Cloud Build
- **Cost**: $0.003 per build-minute (first 120 minutes/day free)
- **Example**: ~$5-10/month for typical usage
- **Benefits**: Pay-per-use, no surprise billing limits

## 🔧 Advanced Features

### 1. Canary Deployments
```yaml
# In clouddeploy.yaml
canaryDeployment:
  percentages: [10, 25, 50, 100]  # Progressive traffic shift
  verify: true                    # Automatic verification
```

### 2. Automatic Rollback
```bash
# Cloud Deploy automatically rolls back on:
# - Health check failures
# - Error rate increases  
# - Custom verification failures
```

### 3. Multi-Environment Support
- **Development**: Automatic deployment on develop/main branches
- **Staging**: Automatic with performance testing
- **Production**: Manual approval with enhanced monitoring

### 4. Security Integration
- **Container Analysis**: Automatic vulnerability scanning
- **Binary Authorization**: Signed container enforcement
- **Secret Manager**: Centralized secret management
- **IAM Integration**: Fine-grained access control

## 📊 Monitoring and Observability

### Cloud Monitoring Integration
```bash
# Automatic metrics collection for:
# - Build success/failure rates
# - Deployment duration
# - Service health and performance
# - Resource utilization
```

### Alerting Setup
```bash
# Alerts for:
# - Build failures
# - Deployment failures
# - High error rates
# - Performance degradation
```

## 🚨 Troubleshooting

### Common Issues

1. **Permission Errors**
   ```bash
   # Grant additional permissions to Cloud Build SA
   gcloud projects add-iam-policy-binding $PROJECT_ID \
     --member="serviceAccount:$CLOUDBUILD_SA" \
     --role="roles/editor"
   ```

2. **GitHub Connection Issues**
   - Ensure repository is public or GitHub App is properly installed
   - Check that webhooks are configured correctly

3. **Build Failures**
   ```bash
   # Check build logs
   gcloud builds log [BUILD_ID]
   
   # List recent builds
   gcloud builds list --limit=10
   ```

4. **Deployment Issues**
   ```bash
   # Check Cloud Deploy rollout status
   gcloud deploy rollouts list --delivery-pipeline=episteme-delivery-pipeline
   
   # Get rollout details
   gcloud deploy rollouts describe [ROLLOUT_ID]
   ```

## 🔍 Testing Your Migration

### Validation Checklist

- [ ] All services build successfully
- [ ] Docker images are pushed to Artifact Registry  
- [ ] Development deployments work automatically
- [ ] Staging deployments trigger from main branch
- [ ] Production requires manual approval
- [ ] Security scanning completes without critical issues
- [ ] Health checks pass for all services
- [ ] Rollback works correctly on failure
- [ ] Monitoring and alerting are active

### Test Commands
```bash
# Trigger manual build
gcloud builds submit --config=cloudbuild.yaml

# Check service health
curl -f https://analysis-engine-dev-[hash]-uc.a.run.app/health

# Test deployment pipeline
gcloud deploy releases create release-$(date +%s) \
  --delivery-pipeline=episteme-delivery-pipeline \
  --images=analysis-engine=us-central1-docker.pkg.dev/PROJECT/ccl-services/analysis-engine:latest
```

## 📈 Benefits Achieved

✅ **Cost Control**: Predictable pricing, no billing surprises  
✅ **Reliability**: Enterprise-grade infrastructure  
✅ **Advanced Deployments**: Canary, blue-green, progressive  
✅ **Security**: Integrated vulnerability scanning and compliance  
✅ **Monitoring**: Comprehensive observability and alerting  
✅ **Scalability**: Handles large-scale builds and deployments  
✅ **Integration**: Native GCP service integration  

## 🚀 Next Steps

1. **Monitor Initial Deployments**: Watch first few builds closely
2. **Configure Alerts**: Set up email/Slack notifications
3. **Optimize Build Times**: Add caching, optimize Docker layers
4. **Security Hardening**: Configure Binary Authorization policies
5. **Performance Tuning**: Adjust resource allocations based on usage
6. **Documentation**: Update team processes and runbooks

## 📞 Support

- **Google Cloud Support**: Use Cloud Console support
- **Documentation**: [Cloud Build Docs](https://cloud.google.com/build/docs)
- **Community**: [Stack Overflow - google-cloud-build](https://stackoverflow.com/questions/tagged/google-cloud-build)

---

🎉 **Migration Complete!** Your CI/CD pipeline is now running on Google Cloud with enhanced reliability, security, and cost control.