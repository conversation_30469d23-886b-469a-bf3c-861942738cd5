# 🛠️ Google Cloud Build Setup Instructions

Your Google Cloud infrastructure is now configured! Follow these steps to complete the migration.

## ✅ Completed Automatically
- ✅ Google Cloud APIs enabled
- ✅ Artifact Registry created (`us-central1-docker.pkg.dev/vibe-match-463114/ccl-services`)
- ✅ Cloud Storage buckets created
- ✅ IAM permissions configured
- ✅ Project configuration updated

## 🔗 Step 1: Connect GitHub Repository (MANUAL STEP)

**Important**: You need to complete this step to enable automatic builds.

1. **Open Cloud Build Console**: https://console.cloud.google.com/cloud-build/repos?project=vibe-match-463114

2. **Connect Repository**:
   - Click "**Connect Repository**" button
   - Select "**GitHub**" as the source
   - Click "**Authenticate with GitHub**"
   - Sign in to your GitHub account if prompted
   - Grant permissions to Google Cloud Build

3. **Select Repository**:
   - Choose "**star-boy-95/episteme**" from the list
   - Click "**Connect**"

4. **Complete Connection**:
   - Review the connection settings
   - Click "**Done**" to finish

## 🚀 Step 2: Complete Setup

After connecting GitHub, run the continuation script:

```bash
./continue-setup.sh
```

This will:
- ✅ Create build triggers for your repository
- ✅ Set up secrets in Secret Manager
- ✅ Create placeholder Cloud Run services
- ✅ Configure monitoring

## 🧪 Step 3: Test Your Pipeline

After setup completion, test the pipeline:

```bash
# Make a small change and push to trigger builds
git add .
git commit -m "test: trigger cloud build pipeline"  
git push origin main
```

## 📊 Step 4: Monitor Your Builds

Watch your builds in real-time:
- **Build Console**: https://console.cloud.google.com/cloud-build/builds?project=vibe-match-463114
- **Triggers**: https://console.cloud.google.com/cloud-build/triggers?project=vibe-match-463114

## 🔐 Step 5: Update Secrets (Optional)

Update placeholder secrets with real values:

```bash
# Database URL
echo -n "your-real-database-url" | gcloud secrets versions add database-url --data-file=-

# Redis URL  
echo -n "your-real-redis-url" | gcloud secrets versions add redis-url --data-file=-

# Slack webhook (for notifications)
echo -n "your-slack-webhook-url" | gcloud secrets versions add slack-webhook-url --data-file=-
```

## 🚨 Troubleshooting

### GitHub Connection Issues
- Ensure you have admin access to the `star-boy-95/episteme` repository
- Check that the GitHub App is properly installed
- Try disconnecting and reconnecting if issues persist

### Permission Errors
```bash
# If you get permission errors, ensure you're authenticated:
gcloud auth login
gcloud config set project vibe-match-463114
```

### Build Failures
- Check build logs in Cloud Build console
- Verify Dockerfile exists in service directories
- Ensure all configuration files are committed

## 📱 Expected Results

After setup, you should see:

1. **Automatic Builds**: Pushes to `main` or `develop` trigger builds
2. **Security Scanning**: Automatic vulnerability scanning
3. **Service Deployment**: Successful builds deploy to development environment
4. **Build History**: Visible in Cloud Build console

## 🎯 Success Indicators

✅ GitHub repository shows "Connected" status  
✅ Build triggers are created and enabled  
✅ Test commit triggers a build  
✅ Build completes successfully  
✅ Services deploy to development environment  

## 📞 Need Help?

If you encounter issues:
1. Check the build logs in Cloud Build console
2. Verify all secrets are properly configured  
3. Ensure GitHub repository connection is working
4. Review IAM permissions if getting access errors

---

**Once GitHub is connected, your pipeline will be fully automated and replace GitHub Actions entirely!**