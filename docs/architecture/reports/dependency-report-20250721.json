{"services": {"collaboration": {"name": "collaboration", "language": "typescript", "framework": "express", "port": 8002, "repository_path": "/Users/<USER>/Documents/GitHub/episteme/services/collaboration", "dependencies": [{"source": "collaboration", "target": "google-cloud-spanner", "dependency_type": "DependencyType.DATABASE", "protocol": "gRPC", "port": 443, "criticality": "CriticalityLevel.CRITICAL", "description": "Google Cloud Spanner database client", "health_check_endpoint": null, "timeout_ms": null, "retry_policy": null}, {"source": "collaboration", "target": "google-cloud-firestore", "dependency_type": "DependencyType.DATABASE", "protocol": "gRPC", "port": 443, "criticality": "CriticalityLevel.HIGH", "description": "Firestore for real-time document synchronization", "health_check_endpoint": null, "timeout_ms": null, "retry_policy": null}, {"source": "collaboration", "target": "redis", "dependency_type": "DependencyType.CACHE", "protocol": "TCP", "port": 6379, "criticality": "CriticalityLevel.HIGH", "description": "Redis for caching and session management", "health_check_endpoint": null, "timeout_ms": null, "retry_policy": null}, {"source": "collaboration", "target": "analysis-engine", "dependency_type": "DependencyType.HTTP_API", "protocol": "HTTP", "port": 8001, "criticality": "CriticalityLevel.HIGH", "description": "Code analysis for collaborative editing", "health_check_endpoint": null, "timeout_ms": null, "retry_policy": null}, {"source": "collaboration", "target": "redis", "dependency_type": "DependencyType.CACHE", "protocol": "TCP", "port": 6379, "criticality": "CriticalityLevel.HIGH", "description": "Shared Redis cache for performance optimization", "health_check_endpoint": null, "timeout_ms": null, "retry_policy": null}, {"source": "collaboration", "target": "prometheus", "dependency_type": "DependencyType.MONITORING", "protocol": "HTTP", "port": 9090, "criticality": "CriticalityLevel.LOW", "description": "Prometheus metrics collection", "health_check_endpoint": null, "timeout_ms": null, "retry_policy": null}], "exposed_apis": ["/health", "/metrics", "/collaborate", "/documents", "/sessions"], "health_check": "/health", "metrics_endpoint": "/metrics"}, "query-intelligence": {"name": "query-intelligence", "language": "python", "framework": "unknown", "port": 8003, "repository_path": "/Users/<USER>/Documents/GitHub/episteme/services/query-intelligence", "dependencies": [{"source": "query-intelligence", "target": "analysis-engine", "dependency_type": "DependencyType.HTTP_API", "protocol": "HTTP", "port": 8001, "criticality": "CriticalityLevel.CRITICAL", "description": "Get code context for intelligent responses", "health_check_endpoint": null, "timeout_ms": null, "retry_policy": null}, {"source": "query-intelligence", "target": "pattern-mining", "dependency_type": "DependencyType.HTTP_API", "protocol": "HTTP", "port": 8004, "criticality": "CriticalityLevel.MEDIUM", "description": "Get pattern insights for query responses", "health_check_endpoint": null, "timeout_ms": null, "retry_policy": null}, {"source": "query-intelligence", "target": "redis", "dependency_type": "DependencyType.CACHE", "protocol": "TCP", "port": 6379, "criticality": "CriticalityLevel.HIGH", "description": "Shared Redis cache for performance optimization", "health_check_endpoint": null, "timeout_ms": null, "retry_policy": null}, {"source": "query-intelligence", "target": "prometheus", "dependency_type": "DependencyType.MONITORING", "protocol": "HTTP", "port": 9090, "criticality": "CriticalityLevel.LOW", "description": "Prometheus metrics collection", "health_check_endpoint": null, "timeout_ms": null, "retry_policy": null}], "exposed_apis": ["/health", "/metrics", "/query", "/search", "/suggestions"], "health_check": "/health", "metrics_endpoint": "/metrics"}, "pattern-mining": {"name": "pattern-mining", "language": "python", "framework": "<PERSON><PERSON><PERSON>", "port": 8003, "repository_path": "/Users/<USER>/Documents/GitHub/episteme/services/pattern-mining", "dependencies": [{"source": "pattern-mining", "target": "big<PERSON>y", "dependency_type": "DependencyType.DATABASE", "protocol": "https", "port": null, "criticality": "CriticalityLevel.CRITICAL", "description": "Primary data warehouse for pattern storage and analysis", "health_check_endpoint": "SELECT 1", "timeout_ms": 5000, "retry_policy": "exponential_backoff"}, {"source": "pattern-mining", "target": "redis", "dependency_type": "DependencyType.CACHE", "protocol": "redis", "port": 6379, "criticality": "CriticalityLevel.CRITICAL", "description": "Caching layer for pattern analysis results", "health_check_endpoint": "PING", "timeout_ms": 1000, "retry_policy": "linear_backoff"}, {"source": "pattern-mining", "target": "vertex-ai", "dependency_type": "DependencyType.EXTERNAL_SERVICE", "protocol": "https", "port": null, "criticality": "CriticalityLevel.HIGH", "description": "Google AI Platform for ML model inference", "health_check_endpoint": "/health", "timeout_ms": 10000, "retry_policy": "exponential_backoff"}, {"source": "pattern-mining", "target": "gemini-api", "dependency_type": "DependencyType.EXTERNAL_SERVICE", "protocol": "https", "port": null, "criticality": "CriticalityLevel.HIGH", "description": "Gemini API for advanced pattern recognition", "health_check_endpoint": "/v1/models", "timeout_ms": 15000, "retry_policy": "exponential_backoff"}, {"source": "pattern-mining", "target": "analysis-engine", "dependency_type": "DependencyType.HTTP_API", "protocol": "http", "port": 8001, "criticality": "CriticalityLevel.HIGH", "description": "Core analysis engine for AST processing", "health_check_endpoint": "/health", "timeout_ms": 5000, "retry_policy": "circuit_breaker"}, {"source": "pattern-mining", "target": "prometheus", "dependency_type": "DependencyType.MONITORING", "protocol": "http", "port": 9090, "criticality": "CriticalityLevel.MEDIUM", "description": "Metrics collection and monitoring", "health_check_endpoint": "/api/v1/status", "timeout_ms": 2000, "retry_policy": "none"}], "exposed_apis": ["/health", "/metrics", "/patterns/detect", "/patterns/analyze", "/patterns/similarity", "/patterns/export"], "health_check": "/health", "metrics_endpoint": "/metrics"}, "web": {"name": "web", "language": "typescript", "framework": "next", "port": 3000, "repository_path": "/Users/<USER>/Documents/GitHub/episteme/services/web", "dependencies": [{"source": "web", "target": "analysis-engine", "dependency_type": "DependencyType.HTTP_API", "protocol": "HTTP", "port": null, "criticality": "CriticalityLevel.CRITICAL", "description": "API calls to analysis-engine service", "health_check_endpoint": null, "timeout_ms": null, "retry_policy": null}, {"source": "web", "target": "collaboration", "dependency_type": "DependencyType.HTTP_API", "protocol": "HTTP", "port": null, "criticality": "CriticalityLevel.CRITICAL", "description": "API calls to collaboration service", "health_check_endpoint": null, "timeout_ms": null, "retry_policy": null}, {"source": "web", "target": "marketplace", "dependency_type": "DependencyType.HTTP_API", "protocol": "HTTP", "port": null, "criticality": "CriticalityLevel.CRITICAL", "description": "API calls to marketplace service", "health_check_endpoint": null, "timeout_ms": null, "retry_policy": null}, {"source": "web", "target": "query-intelligence", "dependency_type": "DependencyType.HTTP_API", "protocol": "HTTP", "port": null, "criticality": "CriticalityLevel.CRITICAL", "description": "API calls to query-intelligence service", "health_check_endpoint": null, "timeout_ms": null, "retry_policy": null}, {"source": "web", "target": "redis", "dependency_type": "DependencyType.CACHE", "protocol": "TCP", "port": 6379, "criticality": "CriticalityLevel.HIGH", "description": "Shared Redis cache for performance optimization", "health_check_endpoint": null, "timeout_ms": null, "retry_policy": null}, {"source": "web", "target": "prometheus", "dependency_type": "DependencyType.MONITORING", "protocol": "HTTP", "port": 9090, "criticality": "CriticalityLevel.LOW", "description": "Prometheus metrics collection", "health_check_endpoint": null, "timeout_ms": null, "retry_policy": null}], "exposed_apis": [], "health_check": "/health", "metrics_endpoint": "/metrics"}, "marketplace": {"name": "marketplace", "language": "go", "framework": "gin", "port": 8005, "repository_path": "/Users/<USER>/Documents/GitHub/episteme/services/marketplace", "dependencies": [{"source": "marketplace", "target": "postgresql", "dependency_type": "DependencyType.DATABASE", "protocol": "postgres", "port": 5432, "criticality": "CriticalityLevel.CRITICAL", "description": "Primary database for marketplace data", "health_check_endpoint": "SELECT 1", "timeout_ms": 3000, "retry_policy": "exponential_backoff"}, {"source": "marketplace", "target": "redis", "dependency_type": "DependencyType.CACHE", "protocol": "redis", "port": 6379, "criticality": "CriticalityLevel.HIGH", "description": "Caching layer for product data and sessions", "health_check_endpoint": "PING", "timeout_ms": 1000, "retry_policy": "linear_backoff"}, {"source": "marketplace", "target": "analysis-engine", "dependency_type": "DependencyType.HTTP_API", "protocol": "http", "port": 8001, "criticality": "CriticalityLevel.HIGH", "description": "Analysis engine for code pattern validation", "health_check_endpoint": "/health", "timeout_ms": 5000, "retry_policy": "circuit_breaker"}, {"source": "marketplace", "target": "prometheus", "dependency_type": "DependencyType.MONITORING", "protocol": "http", "port": 9090, "criticality": "CriticalityLevel.MEDIUM", "description": "Metrics collection and monitoring", "health_check_endpoint": "/api/v1/status", "timeout_ms": 2000, "retry_policy": "none"}], "exposed_apis": ["/health", "/metrics", "/products/search", "/products/recommend", "/marketplace/publish", "/marketplace/browse"], "health_check": "/health", "metrics_endpoint": "/metrics"}, "analysis-engine": {"name": "analysis-engine", "language": "rust", "framework": "axum", "port": 8001, "repository_path": "/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine", "dependencies": [{"source": "analysis-engine", "target": "google-cloud-spanner", "dependency_type": "DependencyType.DATABASE", "protocol": "gRPC", "port": 443, "criticality": "CriticalityLevel.CRITICAL", "description": "Primary database for persistent storage", "health_check_endpoint": null, "timeout_ms": 30000, "retry_policy": null}, {"source": "analysis-engine", "target": "redis", "dependency_type": "DependencyType.CACHE", "protocol": "TCP", "port": 6379, "criticality": "CriticalityLevel.HIGH", "description": "Caching layer for performance optimization", "health_check_endpoint": null, "timeout_ms": 5000, "retry_policy": null}, {"source": "analysis-engine", "target": "prometheus", "dependency_type": "DependencyType.MONITORING", "protocol": "HTTP", "port": 9090, "criticality": "CriticalityLevel.LOW", "description": "Metrics collection and monitoring", "health_check_endpoint": null, "timeout_ms": null, "retry_policy": null}, {"source": "analysis-engine", "target": "redis", "dependency_type": "DependencyType.CACHE", "protocol": "TCP", "port": 6379, "criticality": "CriticalityLevel.HIGH", "description": "Shared Redis cache for performance optimization", "health_check_endpoint": null, "timeout_ms": null, "retry_policy": null}, {"source": "analysis-engine", "target": "prometheus", "dependency_type": "DependencyType.MONITORING", "protocol": "HTTP", "port": 9090, "criticality": "CriticalityLevel.LOW", "description": "Prometheus metrics collection", "health_check_endpoint": null, "timeout_ms": null, "retry_policy": null}], "exposed_apis": ["/health", "/metrics", "/analyze", "/parse", "/languages"], "health_check": "/health", "metrics_endpoint": "/metrics"}}, "dependencies": [{"source": "collaboration", "target": "google-cloud-spanner", "dependency_type": "DependencyType.DATABASE", "protocol": "gRPC", "port": 443, "criticality": "CriticalityLevel.CRITICAL", "description": "Google Cloud Spanner database client", "health_check_endpoint": null, "timeout_ms": null, "retry_policy": null}, {"source": "collaboration", "target": "google-cloud-firestore", "dependency_type": "DependencyType.DATABASE", "protocol": "gRPC", "port": 443, "criticality": "CriticalityLevel.HIGH", "description": "Firestore for real-time document synchronization", "health_check_endpoint": null, "timeout_ms": null, "retry_policy": null}, {"source": "collaboration", "target": "redis", "dependency_type": "DependencyType.CACHE", "protocol": "TCP", "port": 6379, "criticality": "CriticalityLevel.HIGH", "description": "Redis for caching and session management", "health_check_endpoint": null, "timeout_ms": null, "retry_policy": null}, {"source": "collaboration", "target": "analysis-engine", "dependency_type": "DependencyType.HTTP_API", "protocol": "HTTP", "port": 8001, "criticality": "CriticalityLevel.HIGH", "description": "Code analysis for collaborative editing", "health_check_endpoint": null, "timeout_ms": null, "retry_policy": null}, {"source": "collaboration", "target": "redis", "dependency_type": "DependencyType.CACHE", "protocol": "TCP", "port": 6379, "criticality": "CriticalityLevel.HIGH", "description": "Shared Redis cache for performance optimization", "health_check_endpoint": null, "timeout_ms": null, "retry_policy": null}, {"source": "collaboration", "target": "prometheus", "dependency_type": "DependencyType.MONITORING", "protocol": "HTTP", "port": 9090, "criticality": "CriticalityLevel.LOW", "description": "Prometheus metrics collection", "health_check_endpoint": null, "timeout_ms": null, "retry_policy": null}, {"source": "query-intelligence", "target": "analysis-engine", "dependency_type": "DependencyType.HTTP_API", "protocol": "HTTP", "port": 8001, "criticality": "CriticalityLevel.CRITICAL", "description": "Get code context for intelligent responses", "health_check_endpoint": null, "timeout_ms": null, "retry_policy": null}, {"source": "query-intelligence", "target": "pattern-mining", "dependency_type": "DependencyType.HTTP_API", "protocol": "HTTP", "port": 8004, "criticality": "CriticalityLevel.MEDIUM", "description": "Get pattern insights for query responses", "health_check_endpoint": null, "timeout_ms": null, "retry_policy": null}, {"source": "query-intelligence", "target": "redis", "dependency_type": "DependencyType.CACHE", "protocol": "TCP", "port": 6379, "criticality": "CriticalityLevel.HIGH", "description": "Shared Redis cache for performance optimization", "health_check_endpoint": null, "timeout_ms": null, "retry_policy": null}, {"source": "query-intelligence", "target": "prometheus", "dependency_type": "DependencyType.MONITORING", "protocol": "HTTP", "port": 9090, "criticality": "CriticalityLevel.LOW", "description": "Prometheus metrics collection", "health_check_endpoint": null, "timeout_ms": null, "retry_policy": null}, {"source": "pattern-mining", "target": "big<PERSON>y", "dependency_type": "DependencyType.DATABASE", "protocol": "https", "port": null, "criticality": "CriticalityLevel.CRITICAL", "description": "Primary data warehouse for pattern storage and analysis", "health_check_endpoint": "SELECT 1", "timeout_ms": 5000, "retry_policy": "exponential_backoff"}, {"source": "pattern-mining", "target": "redis", "dependency_type": "DependencyType.CACHE", "protocol": "redis", "port": 6379, "criticality": "CriticalityLevel.CRITICAL", "description": "Caching layer for pattern analysis results", "health_check_endpoint": "PING", "timeout_ms": 1000, "retry_policy": "linear_backoff"}, {"source": "pattern-mining", "target": "vertex-ai", "dependency_type": "DependencyType.EXTERNAL_SERVICE", "protocol": "https", "port": null, "criticality": "CriticalityLevel.HIGH", "description": "Google AI Platform for ML model inference", "health_check_endpoint": "/health", "timeout_ms": 10000, "retry_policy": "exponential_backoff"}, {"source": "pattern-mining", "target": "gemini-api", "dependency_type": "DependencyType.EXTERNAL_SERVICE", "protocol": "https", "port": null, "criticality": "CriticalityLevel.HIGH", "description": "Gemini API for advanced pattern recognition", "health_check_endpoint": "/v1/models", "timeout_ms": 15000, "retry_policy": "exponential_backoff"}, {"source": "pattern-mining", "target": "analysis-engine", "dependency_type": "DependencyType.HTTP_API", "protocol": "http", "port": 8001, "criticality": "CriticalityLevel.HIGH", "description": "Core analysis engine for AST processing", "health_check_endpoint": "/health", "timeout_ms": 5000, "retry_policy": "circuit_breaker"}, {"source": "pattern-mining", "target": "prometheus", "dependency_type": "DependencyType.MONITORING", "protocol": "http", "port": 9090, "criticality": "CriticalityLevel.MEDIUM", "description": "Metrics collection and monitoring", "health_check_endpoint": "/api/v1/status", "timeout_ms": 2000, "retry_policy": "none"}, {"source": "web", "target": "analysis-engine", "dependency_type": "DependencyType.HTTP_API", "protocol": "HTTP", "port": null, "criticality": "CriticalityLevel.CRITICAL", "description": "API calls to analysis-engine service", "health_check_endpoint": null, "timeout_ms": null, "retry_policy": null}, {"source": "web", "target": "collaboration", "dependency_type": "DependencyType.HTTP_API", "protocol": "HTTP", "port": null, "criticality": "CriticalityLevel.CRITICAL", "description": "API calls to collaboration service", "health_check_endpoint": null, "timeout_ms": null, "retry_policy": null}, {"source": "web", "target": "marketplace", "dependency_type": "DependencyType.HTTP_API", "protocol": "HTTP", "port": null, "criticality": "CriticalityLevel.CRITICAL", "description": "API calls to marketplace service", "health_check_endpoint": null, "timeout_ms": null, "retry_policy": null}, {"source": "web", "target": "query-intelligence", "dependency_type": "DependencyType.HTTP_API", "protocol": "HTTP", "port": null, "criticality": "CriticalityLevel.CRITICAL", "description": "API calls to query-intelligence service", "health_check_endpoint": null, "timeout_ms": null, "retry_policy": null}, {"source": "web", "target": "redis", "dependency_type": "DependencyType.CACHE", "protocol": "TCP", "port": 6379, "criticality": "CriticalityLevel.HIGH", "description": "Shared Redis cache for performance optimization", "health_check_endpoint": null, "timeout_ms": null, "retry_policy": null}, {"source": "web", "target": "prometheus", "dependency_type": "DependencyType.MONITORING", "protocol": "HTTP", "port": 9090, "criticality": "CriticalityLevel.LOW", "description": "Prometheus metrics collection", "health_check_endpoint": null, "timeout_ms": null, "retry_policy": null}, {"source": "marketplace", "target": "postgresql", "dependency_type": "DependencyType.DATABASE", "protocol": "postgres", "port": 5432, "criticality": "CriticalityLevel.CRITICAL", "description": "Primary database for marketplace data", "health_check_endpoint": "SELECT 1", "timeout_ms": 3000, "retry_policy": "exponential_backoff"}, {"source": "marketplace", "target": "redis", "dependency_type": "DependencyType.CACHE", "protocol": "redis", "port": 6379, "criticality": "CriticalityLevel.HIGH", "description": "Caching layer for product data and sessions", "health_check_endpoint": "PING", "timeout_ms": 1000, "retry_policy": "linear_backoff"}, {"source": "marketplace", "target": "analysis-engine", "dependency_type": "DependencyType.HTTP_API", "protocol": "http", "port": 8001, "criticality": "CriticalityLevel.HIGH", "description": "Analysis engine for code pattern validation", "health_check_endpoint": "/health", "timeout_ms": 5000, "retry_policy": "circuit_breaker"}, {"source": "marketplace", "target": "prometheus", "dependency_type": "DependencyType.MONITORING", "protocol": "http", "port": 9090, "criticality": "CriticalityLevel.MEDIUM", "description": "Metrics collection and monitoring", "health_check_endpoint": "/api/v1/status", "timeout_ms": 2000, "retry_policy": "none"}, {"source": "analysis-engine", "target": "google-cloud-spanner", "dependency_type": "DependencyType.DATABASE", "protocol": "gRPC", "port": 443, "criticality": "CriticalityLevel.CRITICAL", "description": "Primary database for persistent storage", "health_check_endpoint": null, "timeout_ms": 30000, "retry_policy": null}, {"source": "analysis-engine", "target": "redis", "dependency_type": "DependencyType.CACHE", "protocol": "TCP", "port": 6379, "criticality": "CriticalityLevel.HIGH", "description": "Caching layer for performance optimization", "health_check_endpoint": null, "timeout_ms": 5000, "retry_policy": null}, {"source": "analysis-engine", "target": "prometheus", "dependency_type": "DependencyType.MONITORING", "protocol": "HTTP", "port": 9090, "criticality": "CriticalityLevel.LOW", "description": "Metrics collection and monitoring", "health_check_endpoint": null, "timeout_ms": null, "retry_policy": null}, {"source": "analysis-engine", "target": "redis", "dependency_type": "DependencyType.CACHE", "protocol": "TCP", "port": 6379, "criticality": "CriticalityLevel.HIGH", "description": "Shared Redis cache for performance optimization", "health_check_endpoint": null, "timeout_ms": null, "retry_policy": null}, {"source": "analysis-engine", "target": "prometheus", "dependency_type": "DependencyType.MONITORING", "protocol": "HTTP", "port": 9090, "criticality": "CriticalityLevel.LOW", "description": "Prometheus metrics collection", "health_check_endpoint": null, "timeout_ms": null, "retry_policy": null}], "graph_stats": {"nodes": 14, "edges": 28, "density": 0.15384615384615385, "is_weakly_connected": true}, "validation_issues": {}}