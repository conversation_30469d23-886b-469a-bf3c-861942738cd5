# Episteme Service Dependency Graph

**Document Version**: 1.0  
**Last Updated**: 2025-01-21  
**Issue**: EPIS-028 - Service dependencies not mapped  

## Visual Dependency Graph

This document provides visual representations of service dependencies within the Episteme platform.

## High-Level Architecture Diagram

```mermaid
graph TB
    %% External Users
    Users[👥 Users] --> Web[Web Frontend<br/>Next.js]
    
    %% Core Services Layer
    Web --> Collaboration[Collaboration Service<br/>Node.js/TypeScript]
    Web --> AnalysisEngine[Analysis Engine<br/>Rust]
    Web --> Marketplace[Marketplace<br/>Go]
    Web --> QueryIntel[Query Intelligence<br/>Python/FastAPI]
    
    %% Service-to-Service Dependencies
    Collaboration --> AnalysisEngine
    QueryIntel --> AnalysisEngine
    QueryIntel --> PatternMining[Pattern Mining<br/>Python/FastAPI]
    PatternMining --> AnalysisEngine
    
    %% Infrastructure Layer
    AnalysisEngine --> Spanner[(Google Cloud Spanner)]
    Collaboration --> Spanner
    Collaboration --> Firestore[(Google Firestore)]
    PatternMining --> BigQuery[(Google BigQuery)]
    PatternMining --> AIPlatform[Google AI Platform]
    QueryIntel --> PubSub[Google Pub/Sub]
    QueryIntel --> SecretManager[Google Secret Manager]
    QueryIntel --> Pinecone[(Pinecone Vector DB)]
    
    %% Shared Infrastructure
    AnalysisEngine --> Redis[(Redis Cache)]
    Collaboration --> Redis
    PatternMining --> Redis
    QueryIntel --> Redis
    Marketplace --> Redis
    
    %% Monitoring
    AnalysisEngine --> Prometheus[📊 Prometheus]
    Collaboration --> Prometheus
    PatternMining --> Prometheus
    QueryIntel --> Prometheus
    Marketplace --> Prometheus
    
    %% Styling
    classDef frontend fill:#e1f5fe
    classDef service fill:#f3e5f5
    classDef database fill:#e8f5e8
    classDef external fill:#fff3e0
    classDef monitoring fill:#fce4ec
    
    class Web frontend
    class Collaboration,AnalysisEngine,Marketplace,QueryIntel,PatternMining service
    class Spanner,Firestore,BigQuery,Redis,Pinecone database
    class AIPlatform,PubSub,SecretManager,Prometheus external
    class Prometheus monitoring
```

## Service Dependency Matrix

```mermaid
graph LR
    subgraph "Service Dependencies"
        Web[Web] --> |HTTP/WS| Collaboration
        Web --> |HTTP| AnalysisEngine
        Web --> |HTTP| Marketplace  
        Web --> |HTTP| QueryIntel
        
        Collaboration --> |HTTP| AnalysisEngine
        PatternMining --> |HTTP| AnalysisEngine
        QueryIntel --> |HTTP| PatternMining
        QueryIntel --> |HTTP| AnalysisEngine
    end
    
    classDef critical fill:#ffcdd2
    classDef normal fill:#c8e6c9
    
    class Web,AnalysisEngine critical
    class Collaboration,Marketplace,QueryIntel,PatternMining normal
```

## Infrastructure Dependencies

```mermaid
graph TB
    subgraph "Google Cloud Platform"
        Spanner[(Cloud Spanner<br/>Primary Database)]
        Firestore[(Firestore<br/>Document Store)]
        BigQuery[(BigQuery<br/>Analytics)]
        Storage[(Cloud Storage<br/>File Storage)]
        PubSub[Pub/Sub<br/>Messaging]
        AIPlatform[AI Platform<br/>ML Models]
        SecretManager[Secret Manager<br/>Credentials]
        CloudLogging[Cloud Logging<br/>Centralized Logs]
    end
    
    subgraph "Third-Party Services"
        Pinecone[(Pinecone<br/>Vector Search)]
        OpenAI[OpenAI API<br/>Language Models]
    end
    
    subgraph "Local Infrastructure"
        Redis[(Redis<br/>Caching)]
        Prometheus[Prometheus<br/>Metrics]
        Jaeger[Jaeger<br/>Tracing]
        Kafka[Kafka<br/>Event Streaming]
        MinIO[MinIO<br/>Dev Storage]
    end
    
    %% Service connections to infrastructure
    AnalysisEngine -.-> Spanner
    AnalysisEngine -.-> Redis
    AnalysisEngine -.-> Prometheus
    
    Collaboration -.-> Spanner
    Collaboration -.-> Firestore
    Collaboration -.-> Redis
    
    PatternMining -.-> BigQuery
    PatternMining -.-> AIPlatform
    PatternMining -.-> Storage
    PatternMining -.-> Redis
    
    QueryIntel -.-> PubSub
    QueryIntel -.-> SecretManager
    QueryIntel -.-> Pinecone
    QueryIntel -.-> OpenAI
    QueryIntel -.-> Redis
    
    classDef gcp fill:#4285f4,color:#fff
    classDef thirdparty fill:#ff9800,color:#fff
    classDef local fill:#4caf50,color:#fff
    
    class Spanner,Firestore,BigQuery,Storage,PubSub,AIPlatform,SecretManager,CloudLogging gcp
    class Pinecone,OpenAI thirdparty
    class Redis,Prometheus,Jaeger,Kafka,MinIO local
```

## Data Flow Diagram

```mermaid
sequenceDiagram
    participant U as User
    participant W as Web Frontend
    participant C as Collaboration
    participant A as Analysis Engine
    participant P as Pattern Mining
    participant Q as Query Intelligence
    participant S as Spanner
    participant F as Firestore
    participant B as BigQuery
    participant R as Redis
    
    %% Code Analysis Flow
    Note over U,R: Code Analysis Flow
    U->>W: Upload code
    W->>A: Analyze request
    A->>S: Store analysis
    A->>P: Trigger pattern detection
    P->>B: Store patterns
    A->>W: Return results
    W->>U: Display analysis
    
    %% Collaboration Flow
    Note over U,R: Real-time Collaboration
    U->>W: Edit document
    W->>C: WebSocket update
    C->>F: Sync document
    C->>R: Cache session
    C->>W: Broadcast changes
    W->>U: Update UI
    
    %% Query Intelligence Flow
    Note over U,R: Intelligent Query
    U->>W: Ask question
    W->>Q: Process query
    Q->>A: Get code context
    Q->>P: Get patterns
    Q->>Q: Generate response
    Q->>W: Return answer
    W->>U: Display result
```

## Critical Path Analysis

```mermaid
graph TB
    subgraph "Critical Services (Tier 1)"
        T1_Web[Web Frontend]
        T1_Analysis[Analysis Engine]
        T1_Spanner[(Spanner)]
        T1_Redis[(Redis)]
    end
    
    subgraph "Important Services (Tier 2)"
        T2_Collab[Collaboration]
        T2_Query[Query Intelligence]
        T2_Firestore[(Firestore)]
        T2_Pinecone[(Pinecone)]
    end
    
    subgraph "Supporting Services (Tier 3)"
        T3_Pattern[Pattern Mining]
        T3_Market[Marketplace]
        T3_BigQuery[(BigQuery)]
        T3_AI[AI Platform]
    end
    
    subgraph "Monitoring (Tier 4)"
        T4_Prometheus[Prometheus]
        T4_Jaeger[Jaeger]
        T4_Grafana[Grafana]
    end
    
    %% Critical dependencies
    T1_Web --> T1_Analysis
    T1_Analysis --> T1_Spanner
    T1_Analysis --> T1_Redis
    
    %% Important dependencies
    T1_Web --> T2_Collab
    T1_Web --> T2_Query
    T2_Collab --> T2_Firestore
    T2_Query --> T2_Pinecone
    
    %% Supporting dependencies
    T2_Query --> T3_Pattern
    T3_Pattern --> T3_BigQuery
    T3_Pattern --> T3_AI
    
    classDef critical fill:#f44336,color:#fff
    classDef important fill:#ff9800,color:#fff
    classDef supporting fill:#4caf50,color:#fff
    classDef monitoring fill:#2196f3,color:#fff
    
    class T1_Web,T1_Analysis,T1_Spanner,T1_Redis critical
    class T2_Collab,T2_Query,T2_Firestore,T2_Pinecone important
    class T3_Pattern,T3_Market,T3_BigQuery,T3_AI supporting
    class T4_Prometheus,T4_Jaeger,T4_Grafana monitoring
```

## Network Topology

```mermaid
graph TB
    subgraph "Internet"
        Internet[🌐 Internet]
    end
    
    subgraph "Load Balancer / CDN"
        LB[Load Balancer]
        CDN[CDN]
    end
    
    subgraph "VPC - Frontend Subnet"
        Web[Web Frontend<br/>:3000]
        Nginx[Nginx<br/>:80,:443]
    end
    
    subgraph "VPC - Services Subnet"
        AnalysisEngine[Analysis Engine<br/>:8001]
        Collaboration[Collaboration<br/>:8002]
        QueryIntel[Query Intelligence<br/>:8003]
        PatternMining[Pattern Mining<br/>:8004]
        Marketplace[Marketplace<br/>:8005]
    end
    
    subgraph "VPC - Data Subnet"
        Redis[Redis<br/>:6379]
        Kafka[Kafka<br/>:9092]
    end
    
    subgraph "External Services"
        Spanner[(Spanner)]
        Firestore[(Firestore)]
        BigQuery[(BigQuery)]
        Pinecone[(Pinecone)]
    end
    
    Internet --> LB
    LB --> CDN
    CDN --> Nginx
    Nginx --> Web
    
    Web --> AnalysisEngine
    Web --> Collaboration
    Web --> QueryIntel
    Web --> Marketplace
    
    AnalysisEngine --> Redis
    Collaboration --> Redis
    QueryIntel --> Redis
    PatternMining --> Redis
    
    AnalysisEngine --> Spanner
    Collaboration --> Spanner
    Collaboration --> Firestore
    PatternMining --> BigQuery
    QueryIntel --> Pinecone
    
    QueryIntel --> PatternMining
    PatternMining --> AnalysisEngine
    
    classDef internet fill:#e3f2fd
    classDef lb fill:#f3e5f5
    classDef frontend fill:#e8f5e8
    classDef services fill:#fff3e0
    classDef data fill:#fce4ec
    classDef external fill:#f1f8e9
    
    class Internet internet
    class LB,CDN lb
    class Web,Nginx frontend
    class AnalysisEngine,Collaboration,QueryIntel,PatternMining,Marketplace services
    class Redis,Kafka data
    class Spanner,Firestore,BigQuery,Pinecone external
```

## Dependency Failure Impact

```mermaid
graph TD
    subgraph "Failure Scenarios"
        F1[Spanner Failure]
        F2[Redis Failure] 
        F3[Analysis Engine Failure]
        F4[Network Partition]
    end
    
    subgraph "Impact Assessment"
        F1 --> I1[Complete Platform Down<br/>🔴 Critical]
        F2 --> I2[Degraded Performance<br/>🟡 Warning]
        F3 --> I3[Core Features Unavailable<br/>🔴 Critical]
        F4 --> I4[Service Isolation<br/>🟠 Major]
    end
    
    subgraph "Recovery Actions"
        I1 --> R1[Activate Spanner Failover<br/>Auto-recovery: 2-5 min]
        I2 --> R2[Direct DB Queries<br/>Graceful Degradation]
        I3 --> R3[Load Balancer Redirect<br/>Auto-recovery: 30 sec]
        I4 --> R4[Circuit Breaker Pattern<br/>Retry with Backoff]
    end
    
    classDef failure fill:#ffcdd2
    classDef critical fill:#f44336,color:#fff
    classDef warning fill:#ff9800,color:#fff
    classDef major fill:#ff5722,color:#fff
    classDef recovery fill:#4caf50,color:#fff
    
    class F1,F2,F3,F4 failure
    class I1,I3 critical
    class I2 warning
    class I4 major
    class R1,R2,R3,R4 recovery
```

## Deployment Dependencies

```mermaid
graph TB
    subgraph "Deployment Order"
        D1[1. Infrastructure<br/>Spanner, Redis, etc.]
        D2[2. Core Services<br/>Analysis Engine]
        D3[3. Business Services<br/>Collaboration, Query Intel]
        D4[4. Supporting Services<br/>Pattern Mining, Marketplace]
        D5[5. Frontend<br/>Web Application]
        D6[6. Monitoring<br/>Prometheus, Grafana]
    end
    
    D1 --> D2
    D2 --> D3
    D3 --> D4
    D4 --> D5
    D5 --> D6
    
    %% Validation at each stage
    D1 -.-> V1[✓ Infrastructure Health]
    D2 -.-> V2[✓ Core API Health]
    D3 -.-> V3[✓ Service Integration]
    D4 -.-> V4[✓ Feature Completeness]
    D5 -.-> V5[✓ End-to-End Testing]
    D6 -.-> V6[✓ Monitoring Active]
    
    classDef deployment fill:#e3f2fd
    classDef validation fill:#e8f5e8
    
    class D1,D2,D3,D4,D5,D6 deployment
    class V1,V2,V3,V4,V5,V6 validation
```

## Scaling Dependencies

```mermaid
graph LR
    subgraph "Horizontal Scaling"
        HS1[Web Frontend<br/>Stateless ✓]
        HS2[Analysis Engine<br/>Stateless ✓]
        HS3[Collaboration<br/>Session-aware ⚠️]
        HS4[Query Intelligence<br/>Stateless ✓]
    end
    
    subgraph "Vertical Scaling"
        VS1[Pattern Mining<br/>CPU/Memory Intensive]
        VS2[Spanner<br/>Managed Scaling]
        VS3[Redis<br/>Memory Dependent]
    end
    
    subgraph "Auto-Scaling Triggers"
        AS1[CPU > 70%]
        AS2[Memory > 80%]
        AS3[Queue Depth > 100]
        AS4[Response Time > 500ms]
    end
    
    AS1 --> HS1
    AS1 --> HS2
    AS2 --> VS1
    AS3 --> HS3
    AS4 --> HS4
    
    classDef horizontal fill:#4caf50,color:#fff
    classDef vertical fill:#2196f3,color:#fff
    classDef trigger fill:#ff9800,color:#fff
    
    class HS1,HS2,HS3,HS4 horizontal
    class VS1,VS2,VS3 vertical
    class AS1,AS2,AS3,AS4 trigger
```

## Maintenance Windows

This dependency graph should be reviewed and updated:
- **Weekly**: Service health and performance metrics
- **Monthly**: Dependency accuracy and new integrations
- **Quarterly**: Architecture evolution and optimization opportunities
- **Yearly**: Complete dependency audit and modernization planning

## Tools and Automation

For maintaining these dependency graphs:
- **Diagram Source**: Mermaid.js (version controlled)
- **Auto-generation**: Dependency scanner tools
- **Validation**: Automated dependency checks in CI/CD
- **Monitoring**: Real-time dependency health dashboards