# Service Dependencies Mapping

**Document Version**: 1.0  
**Last Updated**: 2025-01-21  
**Issue**: EPIS-028 - Service dependencies not mapped  

## Overview

This document provides a comprehensive mapping of all service dependencies within the Episteme platform. It includes internal service dependencies, external service dependencies, infrastructure requirements, and data flow patterns.

## Service Architecture Overview

The Episteme platform consists of 6 core microservices:

1. **analysis-engine** (Rust) - High-performance code analysis engine
2. **collaboration** (Rust) - Real-time collaboration service
3. **web** (TypeScript/Next.js) - Web frontend application
4. **marketplace** (Go) - Marketplace and transaction service
5. **pattern-mining** (Python/FastAPI) - ML-powered pattern detection
6. **query-intelligence** (Python/FastAPI) - Intelligent query processing

## Service Dependencies Matrix

### Internal Service Dependencies

| Service | Depends On | Dependency Type | Protocol | Critical |
|---------|------------|----------------|----------|----------|
| web | collaboration | API calls | HTTP/WebSocket | Yes |
| web | analysis-engine | API calls | HTTP | Yes |
| web | marketplace | API calls | HTTP | Yes |
| web | query-intelligence | API calls | HTTP | Yes |
| collaboration | analysis-engine | Code analysis | HTTP | Yes |
| pattern-mining | analysis-engine | Code parsing | HTTP | Yes |
| query-intelligence | pattern-mining | Pattern data | HTTP | No |
| query-intelligence | analysis-engine | Code context | HTTP | Yes |

### External Service Dependencies

#### Google Cloud Platform Services

| Service | GCP Dependency | Usage | Critical |
|---------|----------------|-------|----------|
| analysis-engine | Cloud Spanner | Primary database | Yes |
| collaboration | Cloud Spanner | Real-time data | Yes |
| pattern-mining | BigQuery | Analytics | Yes |
| pattern-mining | AI Platform | ML models | Yes |
| pattern-mining | Cloud Storage | Model artifacts | Yes |
| query-intelligence | Pub/Sub | Event streaming | Yes |
| query-intelligence | Secret Manager | API keys | Yes |
| query-intelligence | KMS | Encryption | Yes |

#### Third-Party Services

| Service | External Dependency | Purpose | Critical |
|---------|-------------------|---------|----------|
| query-intelligence | Pinecone | Vector search | Yes |
| query-intelligence | OpenAI API | Language models | Yes |
| All services | Redis | Caching | Yes |
| All services | Prometheus | Metrics | No |
| All services | Jaeger | Tracing | No |

### Infrastructure Dependencies

#### Database Layer
- **Primary**: Google Cloud Spanner (ACID transactions, global consistency)
- **Cache**: Redis (session storage, query caching)
- **Analytics**: BigQuery (data warehouse, ML training)
- **Documents**: Cloud Spanner (real-time collaboration state)

#### Message Queue & Event Streaming
- **Events**: Google Pub/Sub (async communication)
- **Real-time**: Kafka (high-throughput streaming)
- **WebSockets**: Direct service connections (collaboration)

#### Storage & Assets
- **Files**: Google Cloud Storage (code repositories, artifacts)
- **Development**: MinIO (S3-compatible local storage)
- **Static Assets**: CDN integration

#### Monitoring & Observability
- **Metrics**: Prometheus + Grafana
- **Tracing**: Jaeger (distributed tracing)
- **Logging**: Google Cloud Logging
- **Alerting**: Google Cloud Monitoring

## Service-Specific Dependency Details

### analysis-engine (Rust)

**Core Dependencies:**
- `axum` - Web framework
- `tower-http` - HTTP middleware
- `tokio` - Async runtime
- `tree-sitter` - Code parsing
- `google-cloud-spanner` - Database
- `redis` - Caching

**External Services:**
- Google Cloud Spanner (primary database)
- Redis (query caching)
- Prometheus (metrics)

**APIs Exposed:**
- `/analyze` - Code analysis endpoint
- `/parse` - Language parsing
- `/health` - Health check
- `/metrics` - Prometheus metrics

### collaboration (Rust)

**Core Dependencies:**
- `axum` - Web framework
- `tokio` - Async runtime
- `axum-tungstenite` - WebSocket communication
- `google-cloud-spanner` - Database client
- `redis` - Redis client
- `prometheus` - Metrics collection
- `opentelemetry` - Distributed tracing

**External Services:**
- Google Cloud Spanner (collaboration state)
- Redis (session management, rate limiting)
- Prometheus (metrics)

**APIs Exposed:**
- `/sessions` - Collaboration sessions
- `/ws` - WebSocket real-time communication
- `/teams` - Team management
- `/health` - Health check endpoints
- `/metrics` - Prometheus metrics

### web (TypeScript/Next.js)

**Core Dependencies:**
- `next` - React framework
- `@tanstack/react-query` - State management
- `axios` - HTTP client
- `zustand` - Global state

**Service Dependencies:**
- collaboration service (real-time features)
- analysis-engine (code analysis)
- marketplace (transactions)
- query-intelligence (search)

### marketplace (Go)

**Core Dependencies:**
- `gin-gonic/gin` - Web framework
- `lib/pq` - PostgreSQL driver
- OpenTelemetry - Observability

**External Services:**
- PostgreSQL (transactional data)
- Redis (caching)

### pattern-mining (Python/FastAPI)

**Core Dependencies:**
- `fastapi` - Web framework
- `google-cloud-bigquery` - Analytics
- `google-cloud-aiplatform` - ML platform
- `torch` - Deep learning
- `transformers` - NLP models
- `scikit-learn` - ML algorithms

**External Services:**
- BigQuery (data warehouse)
- AI Platform (model serving)
- Cloud Storage (artifacts)
- Redis (caching)

### query-intelligence (Python/FastAPI)

**Core Dependencies:**
- `fastapi` - Web framework
- `google-genai` - Google AI
- `langchain` - LLM framework
- `pinecone-client` - Vector search
- `redis` - Caching

**External Services:**
- Pinecone (vector database)
- Google AI Platform (language models)
- Pub/Sub (event streaming)
- Secret Manager (API keys)

## Data Flow Patterns

### Code Analysis Flow
1. **User Request** → Web Frontend
2. **Web** → analysis-engine (code analysis)
3. **analysis-engine** → Spanner (store results)
4. **analysis-engine** → pattern-mining (trigger pattern detection)
5. **pattern-mining** → BigQuery (analytics)

### Real-Time Collaboration Flow
1. **User Action** → Web Frontend
2. **Web** → collaboration service (WebSocket)
3. **collaboration** → Spanner (collaboration state)
4. **collaboration** → Redis (session state, rate limiting)
5. **collaboration** → Other clients (real-time updates)

### Query Intelligence Flow
1. **User Query** → Web Frontend
2. **Web** → query-intelligence
3. **query-intelligence** → Pinecone (vector search)
4. **query-intelligence** → analysis-engine (code context)
5. **query-intelligence** → pattern-mining (pattern insights)

## Failure Impact Analysis

### Critical Path Dependencies

**High Impact Failures:**
- **Spanner Down**: Analysis-engine, collaboration services fail
- **Redis Down**: All services degraded (no caching)
- **analysis-engine Down**: Core functionality unavailable

**Medium Impact Failures:**
- **BigQuery Down**: Analytics disabled, pattern-mining degraded
- **Pinecone Down**: Query intelligence degraded

**Low Impact Failures:**
- **Prometheus Down**: No metrics (functionality preserved)
- **Jaeger Down**: No distributed tracing

### Recovery Strategies

1. **Database Failures**: Spanner has built-in regional failover
2. **Cache Failures**: Services fallback to direct database queries
3. **Service Failures**: Load balancer routes to healthy instances
4. **External API Failures**: Graceful degradation with cached responses

## Dependency Validation

### Health Check Endpoints
All services expose `/health` endpoints that verify:
- Database connectivity
- External service availability
- Cache connectivity
- Required dependencies

### Monitoring Alerts
- Service dependency health checks
- Database connection pool monitoring
- External API response time tracking
- Cache hit rate monitoring

## Security Dependencies

### Authentication & Authorization
- JWT tokens for service-to-service communication
- Google Cloud IAM for GCP service access
- API key management through Secret Manager

### Network Security
- VPC peering for service communication
- TLS/HTTPS for all external communication
- Internal service mesh for secure communication

## Operational Considerations

### Deployment Dependencies
- Services must be deployed in dependency order
- Database migrations must complete before service startup
- Cache warming strategies for optimal performance

### Scaling Considerations
- Stateless services can scale horizontally
- Database connections are pooled and managed
- Cache invalidation strategies prevent stale data

## Maintenance and Updates

This document should be updated when:
- New services are added
- Service dependencies change
- External service integrations are modified
- Infrastructure changes affect dependencies

**Review Schedule**: Monthly review of dependency accuracy
**Owner**: Platform Architecture Team
**Stakeholders**: Development teams, DevOps, Security team