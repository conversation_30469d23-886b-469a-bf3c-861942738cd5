# Dependency Configuration Guide

This guide explains how to configure service dependencies in the Episteme platform using the enhanced dependency analysis tools.

## Overview

The dependency analysis system has been enhanced to support explicit dependency configuration through `.episteme-deps.yml` files, providing better visibility into service relationships and impact analysis.

## Configuration Format

### Service Configuration File: `.episteme-deps.yml`

Place this file in the root of each service directory to define explicit dependencies:

```yaml
# Service metadata
service:
  name: service-name
  language: rust|python|typescript|go
  framework: axum|fastapi|express|gin
  port: 8001
  criticality: critical|high|medium|low

# Dependency definitions
dependencies:
  databases:
    - name: spanner|bigquery|postgresql
      type: database
      criticality: critical|high|medium|low
      protocol: grpc|https|postgres
      port: 443|5432
      description: "Database description"
      health_check: "SELECT 1"
      timeout_ms: 5000
      retry_policy: "exponential_backoff|linear_backoff|circuit_breaker"
      
  caches:
    - name: redis
      type: cache
      criticality: critical|high|medium|low
      protocol: redis
      port: 6379
      description: "Cache description"
      health_check: "PING"
      timeout_ms: 1000
      retry_policy: "linear_backoff"
      
  internal_services:
    - name: service-name
      type: http_api
      criticality: high|medium|low
      protocol: http|https
      port: 8001
      description: "Internal service description"
      health_check: "/health"
      timeout_ms: 5000
      retry_policy: "circuit_breaker"
      
  external_services:
    - name: vertex-ai|gemini-api
      type: external_service
      criticality: high|medium|low
      protocol: https
      description: "External service description"
      health_check: "/health"
      timeout_ms: 10000
      retry_policy: "exponential_backoff"
      
  monitoring:
    - name: prometheus
      type: monitoring
      criticality: medium|low
      protocol: http
      port: 9090
      description: "Monitoring service description"
      health_check: "/api/v1/status"
      timeout_ms: 2000
      retry_policy: "none"

# Exposed APIs
exposed_apis:
  - "/health"
  - "/metrics"
  - "/api/endpoint1"
  - "/api/endpoint2"

# Health check configuration
health_check:
  endpoint: "/health"
  interval_seconds: 30
  timeout_ms: 5000

# Metrics configuration
metrics:
  endpoint: "/metrics"
  port: 8001
```

## Dependency Types

- **database**: Primary data stores (Spanner, BigQuery, PostgreSQL)
- **cache**: Caching layers (Redis)
- **http_api**: Internal HTTP services
- **external_service**: Third-party APIs
- **monitoring**: Observability services
- **websocket**: WebSocket connections
- **message_queue**: Message queues (Kafka, Pub/Sub)

## Criticality Levels

- **critical**: Service cannot function without this dependency
- **high**: Service degraded performance without this dependency  
- **medium**: Service optional features affected
- **low**: Service monitoring/logging affected

## Retry Policies

- **exponential_backoff**: Exponential backoff with jitter
- **linear_backoff**: Fixed interval retries
- **circuit_breaker**: Circuit breaker pattern
- **none**: No retry logic

## Using the Analysis Tools

### Basic Analysis
```bash
./scripts/dependency-mapper.sh analyze
```

### Impact Analysis
```bash
./scripts/dependency-mapper.sh impact <service-name>
```

### Dependency Validation
```bash
./scripts/dependency-mapper.sh validate
```

### Graph Generation
```bash
./scripts/dependency-mapper.sh graph
```

## Migration from Auto-Detection

Services without `.episteme-deps.yml` files will use automatic dependency detection based on package files (Cargo.toml, package.json, pyproject.toml, go.mod).

To migrate:
1. Run `./scripts/dependency-mapper.sh analyze` to see current dependencies
2. Create `.episteme-deps.yml` with explicit configuration
3. Run validation to ensure correctness

## Benefits

1. **Explicit Dependencies**: Clear documentation of all service relationships
2. **Impact Analysis**: Understand failure propagation patterns
3. **Health Monitoring**: Automated dependency health checks
4. **Retry Configuration**: Service-specific retry policies
5. **Visualization**: Generate dependency graphs for documentation

## Examples

See the following services for example configurations:
- `services/pattern-mining/.episteme-deps.yml`
- `services/marketplace/.episteme-deps.yml`

## Integration with CI/CD

The dependency analysis tools can be integrated into CI/CD pipelines:

```bash
# Validate dependencies before deployment
./scripts/dependency-mapper.sh validate || exit 1

# Update documentation automatically
./scripts/dependency-mapper.sh update-docs
```