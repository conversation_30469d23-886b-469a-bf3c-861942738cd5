# CSRF Protection Implementation

**EPIS-003 Security Enhancement** - Cross-Site Request Forgery Protection

## Overview

This document describes the CSRF protection implementation added to the Analysis Engine service as part of security hardening initiative EPIS-003.

## Implementation Details

### CSRF Middleware

**Location**: `src/api/middleware/csrf.rs`

The CSRF protection uses the **Double Submit Cookie pattern**, which is an industry-standard approach for CSRF protection that doesn't require server-side session storage.

### Security Features

1. **Double Submit Cookie Pattern**
   - CSRF token sent in both cookie and header
   - Tokens must match for request to be valid
   - Prevents CSRF attacks even with XSS vulnerabilities

2. **Cryptographically Secure Tokens**
   - 32 bytes of cryptographically secure random data
   - 8-byte timestamp for expiration tracking
   - 8-byte HMAC-SHA256 signature for integrity
   - Base64-encoded for safe transport

3. **Time-based Expiration**
   - Tokens expire after 1 hour (3600 seconds)
   - Prevents token replay attacks
   - Automatic cleanup of expired tokens

4. **Secure Cookie Configuration**
   - `HttpOnly`: Prevents JavaScript access
   - `Secure`: HTTPS-only transmission
   - `SameSite=Strict`: Browser-level CSRF protection
   - `Path=/`: Available to all application routes

## Protected Operations

### State-Changing Methods
CSRF protection is enforced for all state-changing HTTP methods:
- `POST`
- `PUT` 
- `DELETE`
- `PATCH`

### Exempted Endpoints
The following endpoints bypass CSRF protection for operational monitoring:
- `/health*` - Health check endpoints
- `/metrics` - Prometheus metrics
- `/security/*` - Security monitoring endpoints

## Usage Instructions

### For API Clients

1. **Obtain CSRF Token**
   ```bash
   # Make a GET request to any protected endpoint
   curl -c cookies.txt http://localhost:8001/api/v1/languages
   ```

2. **Extract Token from Response**
   - **Cookie**: Look for `csrf_token=<token>` in Set-Cookie header
   - **Header**: Check `X-CSRF-Token` response header

3. **Use Token in State-Changing Requests**
   ```bash
   # Include token in X-CSRF-Token header
   curl -b cookies.txt \
        -H "X-CSRF-Token: <token>" \
        -H "Content-Type: application/json" \
        -d '{"content":"test","language":"javascript"}' \
        http://localhost:8001/api/v1/analyze
   ```

### For Frontend Applications

1. **JavaScript Token Extraction**
   ```javascript
   // Token available in response header after GET request
   const csrfToken = response.headers['x-csrf-token'];
   
   // Or extract from cookie if needed
   const csrfCookie = document.cookie
     .split(';')
     .find(cookie => cookie.trim().startsWith('csrf_token='))
     ?.split('=')[1];
   ```

2. **Include in Requests**
   ```javascript
   fetch('/api/v1/analyze', {
     method: 'POST',
     headers: {
       'Content-Type': 'application/json',
       'X-CSRF-Token': csrfToken
     },
     credentials: 'include', // Include cookies
     body: JSON.stringify(data)
   });
   ```

## Monitoring

### CSRF Status Endpoint

**Endpoint**: `GET /security/csrf-status`

**Response**:
```json
{
  "csrf_protection": {
    "enabled": true,
    "method": "Double Submit Cookie",
    "token_lifetime_seconds": 3600,
    "protected_methods": ["POST", "PUT", "DELETE", "PATCH"],
    "cookie_security": {
      "http_only": true,
      "secure": true,
      "same_site": "Strict"
    }
  }
}
```

### Error Responses

When CSRF validation fails, the API returns:

**Status**: `403 Forbidden`
**Response**:
```json
{
  "error": {
    "error_type": "Authorization",
    "message": "CSRF protection: <specific error>"
  },
  "csrf_protection": {
    "required": true,
    "instructions": {
      "step_1": "Make a GET request to obtain CSRF token",
      "step_2": "Include X-CSRF-Token header in state-changing requests", 
      "step_3": "Ensure CSRF cookie is sent with request"
    }
  }
}
```

## Validation Testing

A comprehensive test script is available at `validate_csrf.js` which validates:

1. CSRF status endpoint functionality
2. CSRF token generation on GET requests
3. CSRF token validation on POST requests
4. Health endpoint exemption from CSRF checks

**Run Tests**:
```bash
# Start the service
cargo run

# Run validation (in separate terminal)
node validate_csrf.js
```

## Security Benefits

1. **CSRF Attack Prevention**: Prevents malicious websites from making unauthorized requests
2. **Defense in Depth**: Works alongside existing authentication and rate limiting
3. **XSS Mitigation**: HttpOnly cookies prevent token theft via XSS
4. **Replay Attack Prevention**: Time-based token expiration
5. **Browser Protection**: SameSite=Strict provides additional browser-level protection

## Configuration

The CSRF implementation uses these configurable constants (in `csrf.rs`):

- `CSRF_TOKEN_LENGTH`: 32 bytes
- `CSRF_COOKIE_NAME`: "csrf_token"
- `CSRF_HEADER_NAME`: "X-CSRF-Token"
- `CSRF_TOKEN_LIFETIME`: 3600 seconds (1 hour)

## Integration Notes

- CSRF middleware is applied only to protected routes requiring authentication
- Compatible with Axum 0.8 middleware system
- No database or session storage required
- Minimal performance impact on request processing
- Graceful error handling with informative error messages

## Security Considerations

1. **HTTPS Required**: Secure cookies require HTTPS in production
2. **Token Rotation**: Tokens are automatically rotated on each GET request
3. **Clock Synchronization**: Server time must be accurate for token expiration
4. **XSS Prevention**: Combine with XSS protection for complete security

This implementation provides robust CSRF protection while maintaining usability and performance for the Analysis Engine API.