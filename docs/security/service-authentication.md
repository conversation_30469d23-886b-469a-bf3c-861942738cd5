# Service-to-Service Authentication Security Fix

This document describes the implementation of dedicated service accounts for Cloud Run services to replace insecure default credential usage.

## Problem

**Issue**: EPIS-008 - Workload Identity missing for service-to-service auth

Services were using default credentials instead of dedicated service accounts, creating security risks:
- Services using default Cloud Run service account with broad permissions
- No principle of least privilege
- Privilege escalation risk
- "Default credentials" warnings in service logs

## Solution

Implemented dedicated service accounts per service with minimal required permissions:

### Service Account Mapping

| Service | Service Account | Permissions |
|---------|----------------|-------------|
| analysis-engine | `ccl-analysis-engine-sa` | Spanner DB, Storage read, Monitoring, Logging |
| query-intelligence | `ccl-query-intelligence-sa` | Spanner DB, Vertex AI, Monitoring, Logging |
| pattern-mining | `ccl-pattern-mining-sa` | Spanner DB, Vertex AI, ML training, Storage, BigQuery |
| marketplace | `ccl-marketplace-sa` | Spanner DB, Pub/Sub publish, Monitoring, Logging |
| collaboration | `ccl-collaboration-sa` | Spanner DB, Pub/Sub full, Monitoring, Logging |
| web | `ccl-web-sa` | Monitoring, Logging only (calls other services) |

## Implementation Files

### Infrastructure
- `infrastructure/security/service-accounts.yml` - Service account definitions
- `scripts/security/setup-service-accounts.sh` - Creates service accounts and assigns permissions
- `scripts/security/validate-authentication.sh` - Validates proper authentication setup

### Configuration Updates
- `environments/production.yml` - Updated with service account assignments
- `scripts/ccl-deploy` - Modified to use dedicated service accounts during deployment

### Code Changes
- `services/pattern-mining/src/pattern_mining/ml/gemini_client.py` - Added logging for credential source
- `services/pattern-mining/src/pattern_mining/security/secret_rotation.py` - Removed unused default import
- `services/query-intelligence/src/query_intelligence/services/llm_service_v2.py` - Added logging for credential source
- `services/analysis-engine/src/api/handlers/health.rs` - Enhanced auth status endpoint with security validation

## Usage Instructions

### 1. Set Up Service Accounts

```bash
# Create all service accounts with proper permissions
./scripts/security/setup-service-accounts.sh

# Verify service accounts were created
gcloud iam service-accounts list --project=vibe-match-463114
```

### 2. Deploy Services with Service Accounts

```bash
# Deploy all services with proper service account assignment
./scripts/ccl-deploy rollout production

# Or deploy specific service
./scripts/ccl-deploy rollout production analysis-engine
```

### 3. Validate Authentication

```bash
# Validate all services
./scripts/security/validate-authentication.sh

# Validate specific service
./scripts/security/validate-authentication.sh --service analysis-engine

# Check specific project/region
./scripts/security/validate-authentication.sh --project vibe-match-463114 --region us-central1
```

## Verification

### Health Check Endpoints

Each service now provides authentication status via `/health/auth`:

```bash
# Check analysis engine auth status
curl https://analysis-engine-l3nxty7oka-uc.a.run.app/health/auth

# Expected response includes:
{
  "auth_method": "cloud_run_service_account",
  "auth_security_status": "secure",
  "using_dedicated_service_account": "true",
  "expected_service_account": "<EMAIL>"
}
```

### Log Monitoring

Monitor for default credential warnings:

```bash
# Check for default credentials warnings
gcloud logging read 'resource.type="cloud_run_revision" AND ("default credentials" OR "Application Default Credentials")' \
  --project=vibe-match-463114 \
  --limit=10
```

## Security Benefits

1. **Principle of Least Privilege**: Each service has only the permissions it needs
2. **Isolation**: Compromise of one service doesn't affect others
3. **Auditability**: Clear tracking of which service performed which actions
4. **Compliance**: Meets security compliance requirements
5. **Monitoring**: Easy detection of unauthorized credential usage

## Service-to-Service Communication

Services authenticate to each other using:
1. Google-signed ID tokens
2. Automatic credential injection by Cloud Run
3. Service account permissions for Cloud Run Invoker role

Example service-to-service call:
```python
# Python example (automatic with google-auth library)
import google.auth.transport.requests
import google.oauth2.id_token

def call_service(service_url):
    auth_req = google.auth.transport.requests.Request()
    id_token = google.oauth2.id_token.fetch_id_token(auth_req, service_url)
    
    headers = {'Authorization': f'Bearer {id_token}'}
    response = requests.get(service_url, headers=headers)
    return response
```

## Rollback Procedure

If issues occur, rollback by removing service account assignments:

```bash
# Deploy without service account (uses default)
gcloud run deploy SERVICE_NAME \
  --image=IMAGE_URL \
  --region=us-central1 \
  --project=vibe-match-463114
  # (omit --service-account flag)
```

## Monitoring and Alerts

The validation script can be run as a cron job or CI/CD check:

```bash
# Add to CI/CD pipeline
./scripts/security/validate-authentication.sh || exit 1

# Schedule regular validation
0 */6 * * * /path/to/validate-authentication.sh --project vibe-match-463114
```

## Troubleshooting

### Common Issues

1. **403 Forbidden errors**: Service account lacks required permissions
   ```bash
   # Grant missing role
   gcloud projects add-iam-policy-binding PROJECT_ID \
     --member=serviceAccount:SA_EMAIL \
     --role=ROLE
   ```

2. **Service account not found**: Run setup script
   ```bash
   ./scripts/security/setup-service-accounts.sh
   ```

3. **Service using default credentials**: Redeploy with service account
   ```bash
   ./scripts/ccl-deploy rollout production SERVICE_NAME
   ```

### Debug Commands

```bash
# Check current service account for Cloud Run service
gcloud run services describe SERVICE_NAME \
  --region=us-central1 \
  --format="value(spec.template.spec.serviceAccountName)"

# Check service account permissions
gcloud projects get-iam-policy PROJECT_ID \
  --flatten="bindings[].members" \
  --filter="bindings.members:serviceAccount:SA_EMAIL"

# Test service connectivity
curl -f https://SERVICE_URL/health/auth
```

This implementation ensures secure service-to-service authentication and eliminates the risk of default credential misuse.