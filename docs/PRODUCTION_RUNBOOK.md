# 🚀 Episteme Platform Production Runbook

**Status**: ✅ **PRODUCTION OPERATIONAL**  
**Platform URL**: https://episteme-platform.ccl.dev  
**Last Updated**: 2025-01-16  
**Version**: 1.0.0  
**Coverage**: Cross-service operations, platform-wide incidents, systemic recovery

## 📋 Table of Contents

1. [Platform Overview](#platform-overview)
2. [Service Health Dashboard](#service-health-dashboard)
3. [Cross-Service Incident Response](#cross-service-incident-response)
4. [Platform-Wide Troubleshooting](#platform-wide-troubleshooting)
5. [Escalation Procedures](#escalation-procedures)
6. [Disaster Recovery](#disaster-recovery)
7. [Security Operations](#security-operations)
8. [Maintenance Procedures](#maintenance-procedures)
9. [Emergency Contacts](#emergency-contacts)

## 🎯 Platform Overview

The Episteme platform consists of three core production services operating in a microservices architecture:

### Production Services
- **Analysis Engine** (Rust): Code analysis and AST parsing
  - URL: https://analysis-engine-l3nxty7oka-uc.a.run.app
  - [Service Runbook](/docs/analysis-engine/operations/runbook.md)
- **Pattern Mining** (Python): ML-powered pattern detection  
  - URL: https://pattern-mining.ccl-platform.com
  - [Service Runbook](/docs/pattern-mining/operations-runbook.md)
- **Query Intelligence** (Python): Natural language query interface
  - URL: https://query-intelligence.ccl-platform.com
  - [Service Runbook](/docs/query-intelligence/operations/runbook.md)

### Shared Infrastructure
- **Database**: Google Cloud Spanner (`ccl-instance/ccl_main`)
- **Cache**: Redis 7.0 (4GB) via VPC connector
- **Storage**: Cloud Storage (`ccl-analysis-artifacts`)
- **Monitoring**: Google Cloud Monitoring + Prometheus
- **Platform**: Google Cloud Run (auto-scaling)

### Service Dependencies
```
Query Intelligence → Analysis Engine → Pattern Mining
       ↓                    ↓              ↓
   Gemini API         Tree-sitter      BigQuery ML
       ↓                    ↓              ↓
    Spanner ← ←  ← ← ← ← Redis Cache ← ← ← ←
```

## ✅ Service Health Dashboard

### Platform Health Check Commands

```bash
# Quick platform status verification
curl -s https://analysis-engine-l3nxty7oka-uc.a.run.app/health && echo " ✅ Analysis Engine"
curl -s https://pattern-mining.ccl-platform.com/health && echo " ✅ Pattern Mining"  
curl -s https://query-intelligence.ccl-platform.com/health && echo " ✅ Query Intelligence"

# Cross-service integration test
curl -X POST https://query-intelligence.ccl-platform.com/api/v1/query \
  -H "Authorization: Bearer ${API_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{"query": "Find functions with high complexity", "repository_url": "https://github.com/example/test-repo"}'

# Infrastructure health check
gcloud run services list --platform=managed --region=us-central1 \
  --filter="metadata.name:(analysis-engine OR pattern-mining OR query-intelligence)" \
  --format="table(metadata.name,status.url,status.conditions.status)"

# Database connectivity verification
gcloud spanner databases execute-sql ccl_main \
  --instance=ccl-instance \
  --sql="SELECT 1 as health_check"

# Cache connectivity verification  
redis-cli -h *********** ping
```

### Platform Metrics Dashboard

| Service | Normal State | Warning Threshold | Critical Threshold |
|---------|--------------|------------------|-------------------|
| **Overall Platform Availability** | >99.9% | <99.5% | <99% |
| **Cross-Service Request Success** | >99% | <95% | <90% |
| **End-to-End Query Latency** | <5s | >10s | >30s |
| **Database Connection Pool** | <80% | >90% | >95% |
| **Redis Cache Hit Rate** | >85% | <70% | <50% |

### Monitoring Commands

```bash
# Platform-wide error monitoring
gcloud logging read 'severity>=ERROR AND (
  resource.labels.service_name="analysis-engine" OR 
  resource.labels.service_name="pattern-mining" OR 
  resource.labels.service_name="query-intelligence"
)' --limit=50 --format="table(timestamp,resource.labels.service_name,jsonPayload.error)"

# Cross-service performance monitoring
gcloud monitoring metrics list --filter='metric.type:"run.googleapis.com/request_latencies"'

# Infrastructure utilization overview
gcloud compute instances list --format="table(name,zone,status,machineType,internalIP)"
```

## 🚨 Cross-Service Incident Response

### Incident Classification Matrix

| Type | Description | Services Affected | Response Team |
|------|-------------|------------------|---------------|
| **Platform Outage** | All services down | All 3 services | All teams + Infrastructure |
| **Cascading Failure** | One service failure affects others | 2+ services | Primary + Secondary teams |
| **Database Incident** | Spanner unavailable/slow | All 3 services | Database + All service teams |
| **Cache Failure** | Redis unavailable | All 3 services | Infrastructure + Service teams |
| **External API Failure** | Gemini API down | Query Intelligence + dependent | Query Intelligence team |
| **Authentication Failure** | JWT/Auth service down | All 3 services | Security + All teams |

### Severity Definitions (Cross-Service)

#### P0 - Platform Critical
- **Complete platform outage** (all services down)
- **Data loss or corruption** across services  
- **Security breach** affecting multiple services
- **Response Time**: 5 minutes
- **Escalation**: Immediate executive notification

#### P1 - Platform High
- **Major service unavailable** (1 core service down)
- **Severe performance degradation** (>50% requests failing)
- **Cross-service authentication failures**
- **Response Time**: 15 minutes
- **Escalation**: Engineering management

#### P2 - Platform Medium  
- **Minor service degradation** (increased latency/errors)
- **Non-critical feature unavailable**
- **Dependency service partial outage**
- **Response Time**: 1 hour
- **Escalation**: Service teams

### Cross-Service Incident Response Procedures

#### P0 - Complete Platform Outage

**Immediate Actions (0-5 minutes)**
```bash
# 1. Platform status assessment
./scripts/emergency/platform_status_check.sh

# 2. Infrastructure triage
gcloud run services list --platform=managed --region=us-central1 \
  --format="table(metadata.name,status.conditions.type,status.conditions.status)"

# 3. Database health verification
gcloud spanner instances describe ccl-instance --format="yaml(state,displayName)"

# 4. Activate incident war room
./scripts/emergency/activate_war_room.sh P0 "Platform Outage"

# 5. External status page update
curl -X POST "https://api.statuspage.io/v1/pages/PAGE_ID/incidents" \
  -H "Authorization: OAuth TOKEN" \
  -d "incident[name]=Platform Maintenance&incident[status]=investigating"
```

**Investigation Phase (5-15 minutes)**
```bash
# Check for recent deployments across all services
gcloud run revisions list --region=us-central1 \
  --filter="metadata.creationTimestamp>=$(date -d '2 hours ago' -u +%Y-%m-%dT%H:%M:%SZ)" \
  --format="table(metadata.name,metadata.creationTimestamp)"

# Review infrastructure changes
gcloud logging read 'protoPayload.serviceName="cloudresourcemanager.googleapis.com"' \
  --format="table(timestamp,protoPayload.methodName)"

# Check external dependency status
curl -s https://status.cloud.google.com/incidents.json | jq '.[] | select(.begin > now - 7200)'

# Database incident analysis
gcloud spanner operations list --instance=ccl-instance --filter="metadata.statements.sql"
```

**Recovery Actions**
```bash
# If deployment-related: Coordinated rollback
./scripts/emergency/coordinated_rollback.sh

# If infrastructure-related: Disaster recovery activation  
./scripts/emergency/activate_disaster_recovery.sh

# If database-related: Database failover
./scripts/emergency/database_failover.sh
```

#### P1 - Cascading Service Failure

**Service Isolation (0-10 minutes)**
```bash
# 1. Identify failing service
./scripts/diagnostics/identify_failing_service.sh

# 2. Enable circuit breakers to prevent cascade
kubectl set env deployment/analysis-engine CIRCUIT_BREAKER_ENABLED=true
kubectl set env deployment/pattern-mining CIRCUIT_BREAKER_ENABLED=true
kubectl set env deployment/query-intelligence CIRCUIT_BREAKER_ENABLED=true

# 3. Scale healthy services to handle load
gcloud run services update analysis-engine --max-instances=200
gcloud run services update pattern-mining --max-instances=100

# 4. Enable degraded mode for dependent services
./scripts/emergency/enable_degraded_mode.sh
```

**Root Cause Analysis**
```bash
# Analyze service interaction patterns
gcloud logging read 'jsonPayload.service_call_failed=true' \
  --format="table(timestamp,resource.labels.service_name,jsonPayload.target_service)"

# Check database connection patterns
gcloud spanner databases execute-sql ccl_main \
  --instance=ccl-instance \
  --sql="SELECT * FROM spanner_sys.lock_stats ORDER BY total_wait_time_seconds DESC LIMIT 10"

# Review API rate limiting
redis-cli -h *********** GET rate_limit:*
```

## 🔧 Platform-Wide Troubleshooting

> **Detailed Guide**: For comprehensive troubleshooting procedures, see [Platform Troubleshooting Guide](/docs/operations/troubleshooting-guide.md)

### Common Cross-Service Issues

#### 1. Database Connection Exhaustion

**Symptoms**: All services reporting database timeout errors

**Diagnosis**
```bash
# Check active connections across all services
gcloud spanner databases execute-sql ccl_main \
  --instance=ccl-instance \
  --sql="SELECT * FROM INFORMATION_SCHEMA.SPANNER_STATISTICS 
         WHERE CATEGORY = 'QUERY_STATS' AND NAME = 'ACTIVE_QUERIES'"

# Review connection pool settings per service
kubectl get deployments -o yaml | grep -A 5 -B 5 "DATABASE_POOL_SIZE"
```

**Resolution**
```bash
# Increase connection pool limits
kubectl set env deployment/analysis-engine DATABASE_POOL_SIZE=50
kubectl set env deployment/pattern-mining DATABASE_POOL_SIZE=30  
kubectl set env deployment/query-intelligence DATABASE_POOL_SIZE=20

# Scale database if needed
gcloud spanner instances update ccl-instance --nodes=5

# Force connection pool refresh
./scripts/maintenance/refresh_connection_pools.sh
```

#### 2. Redis Cache Failure

**Symptoms**: High latency across all services, cache miss rate approaching 100%

**Diagnosis**
```bash
# Check Redis instance health
gcloud redis instances describe analysis-engine-cache --region=us-central1

# Verify VPC connectivity
gcloud compute networks subnets describe default --region=us-central1

# Test cache connectivity from each service
./scripts/diagnostics/test_cache_connectivity.sh
```

**Resolution**
```bash
# Redis failover to backup instance
gcloud redis instances failover analysis-engine-cache --region=us-central1

# Update service configurations to use backup
kubectl set env deployment/analysis-engine REDIS_URL="redis://***********:6379"
kubectl set env deployment/pattern-mining REDIS_URL="redis://***********:6379"
kubectl set env deployment/query-intelligence REDIS_URL="redis://***********:6379"

# Enable graceful degradation (cache-less operation)
./scripts/emergency/enable_cacheless_mode.sh
```

#### 3. Authentication Service Failure

**Symptoms**: 401 errors across all services, JWT validation failures

**Diagnosis**
```bash
# Check JWT secret consistency across services
kubectl get secrets jwt-secret -o yaml | base64 -d

# Verify authentication middleware health
gcloud logging read 'jsonPayload.event="auth_failure"' \
  --format="table(timestamp,resource.labels.service_name,jsonPayload.reason)"

# Test token generation and validation
./scripts/diagnostics/test_jwt_flow.sh
```

**Resolution**
```bash
# Emergency JWT secret rotation
./scripts/security/emergency_jwt_rotation.sh

# Restart all services to pick up new secret
kubectl rollout restart deployment/analysis-engine
kubectl rollout restart deployment/pattern-mining  
kubectl rollout restart deployment/query-intelligence

# Verify authentication recovery
./scripts/diagnostics/verify_auth_recovery.sh
```

#### 4. Network Connectivity Issues

**Symptoms**: Inter-service communication failures, timeout errors

**Diagnosis**
```bash
# Check VPC connector status
gcloud compute networks vpc-access connectors describe episteme-connector \
  --region=us-central1

# Test inter-service connectivity
./scripts/diagnostics/network_connectivity_test.sh

# Review firewall rules
gcloud compute firewall-rules list --filter="name:episteme*"
```

**Resolution**
```bash
# Recreate VPC connector if corrupted
./scripts/infrastructure/recreate_vpc_connector.sh

# Update firewall rules
gcloud compute firewall-rules update episteme-internal \
  --allow tcp:8080,tcp:6379,tcp:5432

# Restart services to refresh network connections
./scripts/maintenance/rolling_restart.sh
```

### Performance Troubleshooting

#### End-to-End Latency Analysis

```bash
# Trace request flow across services
gcloud logging read 'jsonPayload.trace_id="TRACE_ID"' \
  --format="table(timestamp,resource.labels.service_name,jsonPayload.operation,jsonPayload.duration_ms)"

# Identify bottleneck services
./scripts/performance/identify_bottlenecks.sh

# Database query performance analysis
gcloud spanner databases execute-sql ccl_main \
  --instance=ccl-instance \
  --sql="SELECT * FROM INFORMATION_SCHEMA.QUERY_STATS 
         WHERE INTERVAL_END >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 1 HOUR)
         ORDER BY AVG_LATENCY_SECONDS DESC LIMIT 10"
```

#### Resource Utilization Analysis

```bash
# Cross-service resource monitoring
gcloud monitoring metrics list --filter='metric.type:"run.googleapis.com/container/cpu/utilization"'

# Memory usage patterns
./scripts/monitoring/memory_analysis.sh

# Network bandwidth utilization
gcloud compute networks get-effective-firewalls episteme-vpc
```

## 📞 Escalation Procedures

> **Detailed Procedures**: For comprehensive escalation procedures, see [Escalation Procedures Guide](/docs/operations/escalation-procedures.md)

### Escalation Matrix

| Incident Type | Primary Contact | Secondary Contact | Executive Contact |
|---------------|----------------|-------------------|-------------------|
| **Platform Outage (P0)** | Platform Team Lead | VP Engineering | CTO |
| **Multi-Service (P1)** | Service Team Leads | Engineering Director | VP Engineering |
| **Security Incident** | Security Team Lead | CISO | CTO |
| **Data Loss/Corruption** | Database Team Lead | Engineering Director | CTO |
| **External Dependency** | DevOps Team Lead | Infrastructure Director | VP Engineering |

### Escalation Triggers

#### Automatic Escalation

- **15 minutes**: P0 incident not acknowledged
- **30 minutes**: P0 incident not mitigated  
- **1 hour**: P1 incident not resolved
- **2 hours**: Customer-facing impact continues

#### Manual Escalation Triggers

- **Scope Increase**: More services affected than initially assessed
- **Expertise Required**: Technical skills beyond current team capabilities
- **External Communication**: Customer/public communication needed
- **Legal/Compliance**: Regulatory requirements triggered

### Escalation Procedures

```bash
# Activate escalation process
./scripts/escalation/escalate_incident.sh --level P0 --type "Platform Outage" \
  --description "All services unavailable" --impact "100% customer impact"

# Emergency executive notification
./scripts/emergency/executive_notification.sh --severity CRITICAL \
  --summary "Platform down, ETA unknown"

# Activate external vendor support
./scripts/escalation/activate_vendor_support.sh --vendor google-cloud \
  --priority URGENT --case-type infrastructure
```

### Communication Protocols

#### Internal Communication

- **War Room**: Google Meet (link in Slack channel)
- **Primary Channel**: #incident-response
- **Service Channels**: #analysis-engine-ops, #pattern-mining-ops, #query-intelligence-ops
- **Executive Updates**: #exec-alerts

#### External Communication

- **Status Page**: https://status.episteme-platform.com
- **Customer Email**: <EMAIL>
- **Social Media**: @EpistemeStatus (Twitter)
- **Blog Updates**: https://blog.episteme-platform.com/incidents

## 🛡️ Disaster Recovery

> **Detailed Procedures**: For comprehensive recovery procedures, see [Recovery Procedures Guide](/docs/operations/recovery-procedures.md)

### Disaster Recovery Scenarios

#### Regional Outage (us-central1)

**Detection**
```bash
# Verify regional outage scope
gcloud compute regions describe us-central1 --format="yaml(status)"

# Check service availability in backup region
curl -s https://analysis-engine-backup.us-east1.run.app/health
```

**Activation**
```bash
# 1. Activate disaster recovery region (us-east1)
./scripts/disaster-recovery/activate_dr_region.sh us-east1

# 2. Update DNS to point to DR region
gcloud dns record-sets transaction start --zone=episteme-platform-com
gcloud dns record-sets transaction add --zone=episteme-platform-com \
  --name="api.episteme-platform.com." --type=A --ttl=300 "34.74.XXX.XXX"
gcloud dns record-sets transaction execute --zone=episteme-platform-com

# 3. Scale DR services
gcloud run services update analysis-engine-dr --region=us-east1 --min-instances=5
gcloud run services update pattern-mining-dr --region=us-east1 --min-instances=3  
gcloud run services update query-intelligence-dr --region=us-east1 --min-instances=2

# 4. Verify DR functionality
./scripts/disaster-recovery/verify_dr_services.sh us-east1
```

#### Database Disaster Recovery

**Point-in-Time Recovery**
```bash
# 1. Assess data corruption scope
gcloud spanner databases execute-sql ccl_main \
  --instance=ccl-instance \
  --sql="SELECT COUNT(*) as corrupted_records FROM analysis_results WHERE data IS NULL"

# 2. Create backup before recovery
gcloud spanner backups create emergency-backup-$(date +%Y%m%d-%H%M) \
  --instance=ccl-instance \
  --database=ccl_main

# 3. Restore from point-in-time
gcloud spanner databases restore ccl_main_restored \
  --source-backup=projects/vibe-match-463114/instances/ccl-instance/backups/backup-20250116
  
# 4. Switch services to restored database  
./scripts/disaster-recovery/switch_database.sh ccl_main_restored
```

#### Complete Platform Recovery

**Recovery Sequence**
```bash
# 1. Infrastructure verification
./scripts/disaster-recovery/verify_infrastructure.sh

# 2. Database recovery
./scripts/disaster-recovery/restore_database.sh

# 3. Cache reconstruction  
./scripts/disaster-recovery/rebuild_cache.sh

# 4. Sequential service restoration
./scripts/disaster-recovery/restore_analysis_engine.sh
./scripts/disaster-recovery/restore_pattern_mining.sh  
./scripts/disaster-recovery/restore_query_intelligence.sh

# 5. Integration testing
./scripts/disaster-recovery/integration_tests.sh

# 6. Production traffic restoration
./scripts/disaster-recovery/restore_production_traffic.sh
```

## 🔒 Security Operations

### Security Incident Response

#### Security Breach Detection

```bash
# Monitor for security events across all services
gcloud logging read 'jsonPayload.event_type="security_event" AND severity>=WARNING' \
  --format="table(timestamp,resource.labels.service_name,jsonPayload.event,jsonPayload.source_ip)"

# Check for unauthorized access attempts  
gcloud logging read 'httpRequest.status>=400 AND httpRequest.userAgent!=""' \
  --format="table(timestamp,httpRequest.remoteIp,httpRequest.requestUrl,httpRequest.status)"

# Audit authentication events
./scripts/security/audit_auth_events.sh --last-hours 24
```

#### Immediate Response Actions

```bash
# 1. Isolate affected services
gcloud run services update AFFECTED_SERVICE --ingress=internal

# 2. Rotate all secrets immediately
./scripts/security/emergency_secret_rotation.sh --all-services

# 3. Enable enhanced logging
kubectl set env deployment/analysis-engine SECURITY_LOG_LEVEL=DEBUG
kubectl set env deployment/pattern-mining SECURITY_LOG_LEVEL=DEBUG
kubectl set env deployment/query-intelligence SECURITY_LOG_LEVEL=DEBUG

# 4. Block suspicious IPs
./scripts/security/block_ips.sh --file suspicious_ips.txt

# 5. Notify security team
./scripts/security/notify_security_team.sh --breach-type UNAUTHORIZED_ACCESS
```

### Access Control Operations

#### Emergency Access Revocation

```bash
# Revoke user access across all services
./scripts/security/revoke_user_access.sh --user <EMAIL> --all-services

# Review service account permissions
gcloud projects get-iam-policy vibe-match-463114 \
  --flatten="bindings[].members" \
  --filter="bindings.members:serviceAccount"

# Audit API key usage
./scripts/security/audit_api_keys.sh --suspicious-activity
```

#### Secret Management

```bash
# Platform-wide secret rotation
./scripts/security/rotate_secrets.sh --platform-wide --confirm

# Verify secret synchronization across services
./scripts/security/verify_secret_sync.sh

# Update secret versions in all deployments
./scripts/security/update_secret_versions.sh --latest
```

## 🛠️ Maintenance Procedures

### Scheduled Maintenance

#### Monthly Platform Maintenance

**Pre-maintenance Checklist**
```bash
# 1. Verify backup integrity
./scripts/maintenance/verify_backups.sh --all-services

# 2. Check dependency versions
./scripts/maintenance/check_dependencies.sh --security-updates

# 3. Review performance metrics
./scripts/maintenance/performance_report.sh --last-month

# 4. Validate disaster recovery procedures
./scripts/maintenance/test_dr_procedures.sh --dry-run
```

**Maintenance Execution**
```bash
# 1. Enable maintenance mode
./scripts/maintenance/enable_maintenance_mode.sh

# 2. Update dependencies across all services
./scripts/maintenance/update_dependencies.sh --security-patches

# 3. Database maintenance
gcloud spanner databases ddl update ccl_main \
  --instance=ccl-instance \
  --ddl-file=maintenance/monthly_optimization.sql

# 4. Cache optimization
./scripts/maintenance/optimize_cache.sh

# 5. Performance tuning
./scripts/maintenance/performance_tuning.sh --all-services

# 6. Security audit
./scripts/maintenance/security_audit.sh --comprehensive

# 7. Disable maintenance mode
./scripts/maintenance/disable_maintenance_mode.sh
```

#### Coordinated Deployments

```bash
# Multi-service deployment coordination
./scripts/deployment/coordinated_deployment.sh \
  --services "analysis-engine,pattern-mining,query-intelligence" \
  --strategy blue-green \
  --rollback-plan automatic

# Verify cross-service compatibility
./scripts/deployment/compatibility_test.sh --integration

# Monitor deployment health
./scripts/deployment/monitor_deployment.sh --duration 30m
```

### Capacity Planning

#### Resource Scaling Analysis

```bash
# Analyze historical resource usage
./scripts/capacity/usage_analysis.sh --period 90d --forecast 30d

# Identify scaling bottlenecks
./scripts/capacity/bottleneck_analysis.sh --all-services

# Cost optimization review
./scripts/capacity/cost_optimization.sh --recommendations
```

#### Proactive Scaling

```bash
# Implement predictive scaling
./scripts/capacity/predictive_scaling.sh --enable --algorithm ml-based

# Update auto-scaling parameters
gcloud run services update analysis-engine \
  --min-instances=3 --max-instances=150 \
  --cpu-throttling --concurrency=100

# Monitor scaling effectiveness
./scripts/capacity/scaling_metrics.sh --realtime
```

## 📞 Emergency Contacts

### Platform Team Contacts

#### Primary On-Call Rotation
- **Week 1**: Platform Team Lead - <EMAIL>
- **Week 2**: Senior SRE - <EMAIL>  
- **Week 3**: DevOps Lead - <EMAIL>
- **Week 4**: Infrastructure Lead - <EMAIL>

#### Service Team Leads
- **Analysis Engine**: <EMAIL>
- **Pattern Mining**: <EMAIL>
- **Query Intelligence**: <EMAIL>

#### Management Escalation
- **Engineering Director**: <EMAIL>
- **VP Engineering**: <EMAIL>
- **CTO**: <EMAIL>

### External Support

#### Vendor Support
| Vendor | Contact | Account ID | Priority Support |
|--------|---------|------------|-----------------|
| Google Cloud | <EMAIL> | ********* | ✅ Premium |
| Datadog | <EMAIL> | DD-98765 | ✅ Enterprise |
| PagerDuty | <EMAIL> | PD-11111 | ✅ Professional |

#### Emergency Services
- **Google Cloud Critical Support**: **************
- **Security Hotline**: <EMAIL>
- **Legal Emergency**: <EMAIL>

### Communication Channels

#### Internal
- **Primary Alert Channel**: #incident-response
- **Executive Channel**: #exec-alerts  
- **Platform Channel**: #platform-ops
- **All Hands**: #all-hands

#### External
- **Status Page**: https://status.episteme-platform.com
- **Customer Support**: <EMAIL>
- **Security Issues**: <EMAIL>
- **Press Inquiries**: <EMAIL>

---

## 🧰 Emergency Scripts Location

All emergency scripts referenced in this runbook are located in:
- **Repository**: `/scripts/emergency/`
- **Production Servers**: `/opt/episteme/emergency/`
- **Cloud Storage Backup**: `gs://episteme-emergency-scripts/`

### Quick Access Commands

```bash
# Clone emergency scripts locally
git clone https://github.com/episteme/emergency-scripts.git
cd emergency-scripts

# Download from cloud storage
gsutil -m cp -r gs://episteme-emergency-scripts/ ./emergency/

# Execute from production server
ssh platform-admin@********** "cd /opt/episteme/emergency && ./platform_status_check.sh"
```

## 📊 SLA Commitments

### Platform-Level SLAs

- **Availability**: 99.9% uptime (max 43.8 minutes downtime/month)
- **Response Time**: 95th percentile < 5 seconds end-to-end
- **Error Rate**: < 0.1% of all requests
- **Recovery Time Objective (RTO)**: 15 minutes for P0 incidents
- **Recovery Point Objective (RPO)**: 5 minutes maximum data loss

---

**Last Verified**: 2025-01-16  
**Next Review**: 2025-02-16  
**Runbook Owner**: Platform Team  
**Version**: 1.0.0

> **Note**: This production runbook consolidates cross-service operations and should be used in conjunction with individual service runbooks for service-specific procedures. For service-specific troubleshooting, refer to the individual service runbooks linked in the Platform Overview section.