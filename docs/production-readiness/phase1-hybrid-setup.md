# Phase 1 Hybrid Development Setup

This guide implements the hybrid approach for Phase 1 production readiness work, combining IDE productivity with Docker validation.

## Quick Start

```bash
# 1. Create monitoring stack configuration (in IDE)
cp docker/docker-compose.yml docker/docker-compose.monitoring.yml

# 2. Start monitoring services (in Docker)
docker-compose -f docker/docker-compose.monitoring.yml up -d

# 3. Edit configurations (in IDE)
code docker/docker-compose.monitoring.yml
code infrastructure/monitoring/prometheus.yml

# 4. Restart to test changes (in Docker)
docker-compose -f docker/docker-compose.monitoring.yml restart
```

## Task-Specific Workflows

### Binary Authorization Enforcement

**IDE Work:**
```bash
# Create enforcement script
cat > scripts/security/enforce-binary-authorization.sh << 'EOF'
#!/bin/bash
# Binary Authorization Enforcement Script

set -euo pipefail

PROJECT_ID=${GCP_PROJECT_ID:-vibe-match-463114}
ATTESTOR_NAME="episteme-production-attestor"
KEY_LOCATION="global"
KEY_RING="episteme-binauthz-keys"
KEY_NAME="signing-key"

echo "🔐 Enforcing Binary Authorization..."

# Function to sign and attest an image
sign_image() {
    local IMAGE_URL=$1
    local IMAGE_DIGEST=$2
    
    echo "Signing image: ${IMAGE_URL}@${IMAGE_DIGEST}"
    
    gcloud beta container binauthz attestations sign-and-create \
        --artifact-url="${IMAGE_URL}@${IMAGE_DIGEST}" \
        --attestor="${ATTESTOR_NAME}" \
        --attestor-project="${PROJECT_ID}" \
        --keyversion-project="${PROJECT_ID}" \
        --keyversion-location="${KEY_LOCATION}" \
        --keyversion-keyring="${KEY_RING}" \
        --keyversion-key="${KEY_NAME}" \
        --keyversion="1"
}

# Main execution
if [ $# -eq 0 ]; then
    echo "Usage: $0 <image-url> <image-digest>"
    exit 1
fi

sign_image "$1" "$2"
echo "✅ Image signed and attested successfully"
EOF

chmod +x scripts/security/enforce-binary-authorization.sh
```

**Docker Testing:**
```bash
# Build test image
docker build -t gcr.io/${PROJECT_ID}/test-app:latest .

# Get image digest
IMAGE_DIGEST=$(docker inspect gcr.io/${PROJECT_ID}/test-app:latest --format='{{index .RepoDigests 0}}' | cut -d'@' -f2)

# Test signing (requires real GCP environment)
./scripts/security/enforce-binary-authorization.sh \
  "gcr.io/${PROJECT_ID}/test-app:latest" \
  "${IMAGE_DIGEST}"
```

### Monitoring Stack Deployment

**IDE Work:**
```yaml
# Edit docker/docker-compose.monitoring.yml
version: '3.8'

services:
  prometheus:
    image: prom/prometheus:v2.45.0
    container_name: episteme-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/usr/share/prometheus/console_libraries'
      - '--web.console.templates=/usr/share/prometheus/consoles'
    networks:
      - episteme-network

  grafana:
    image: grafana/grafana:10.0.0
    container_name: episteme-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning:ro
    networks:
      - episteme-network

  jaeger:
    image: jaegertracing/all-in-one:1.47
    container_name: episteme-jaeger
    ports:
      - "16686:16686"  # Jaeger UI
      - "14250:14250"  # gRPC
      - "14268:14268"  # HTTP
      - "4317:4317"    # OTLP gRPC
      - "4318:4318"    # OTLP HTTP
    environment:
      - COLLECTOR_OTLP_ENABLED=true
    networks:
      - episteme-network

  # Service-specific exporters
  node-exporter:
    image: prom/node-exporter:v1.6.0
    container_name: episteme-node-exporter
    ports:
      - "9100:9100"
    networks:
      - episteme-network

volumes:
  prometheus_data:
  grafana_data:

networks:
  episteme-network:
    external: true
```

**Docker Validation:**
```bash
# Start monitoring stack
docker-compose -f docker/docker-compose.monitoring.yml up -d

# Verify services are running
docker ps | grep episteme-

# Access UIs
open http://localhost:3000     # Grafana (admin/admin)
open http://localhost:9090     # Prometheus
open http://localhost:16686    # Jaeger
```

### Security Validation

**IDE Work:**
```bash
# Create comprehensive security validation script
cat > scripts/security/validate-phase1-security.sh << 'EOF'
#!/bin/bash
# Phase 1 Security Validation Script

set -euo pipefail

echo "🔒 Running Phase 1 Security Validation..."

# Check Binary Authorization
echo "1️⃣ Validating Binary Authorization..."
./scripts/security/validate-binary-authorization.sh

# Check for hardcoded secrets
echo "2️⃣ Checking for hardcoded secrets..."
./scripts/security/validate-no-hardcoded-secrets.sh

# Run dependency audits
echo "3️⃣ Running dependency audits..."
cd services/analysis-engine && cargo audit
cd ../query-intelligence && pip-audit
cd ../pattern-mining && pip-audit
cd ../collaboration && npm audit
cd ../..

# Test CSRF protection
echo "4️⃣ Testing CSRF protection..."
cd services/analysis-engine && node validate_csrf.js
cd ../..

# Check monitoring endpoints
echo "5️⃣ Validating monitoring endpoints..."
for service in "analysis-engine:8001" "query-intelligence:8002" "pattern-mining:8003"; do
    IFS=':' read -ra ADDR <<< "$service"
    name="${ADDR[0]}"
    port="${ADDR[1]}"
    
    echo "Checking $name metrics endpoint..."
    curl -f "http://localhost:$port/metrics" > /dev/null 2>&1 && \
        echo "✅ $name metrics endpoint active" || \
        echo "❌ $name metrics endpoint not responding"
done

echo "✅ Phase 1 Security Validation Complete"
EOF

chmod +x scripts/security/validate-phase1-security.sh
```

**Docker Testing:**
```bash
# Start all services
docker-compose up -d

# Run security validation
./scripts/security/validate-phase1-security.sh

# Run OWASP ZAP scan
docker run -t owasp/zap2docker-stable zap-baseline.py \
  -t http://host.docker.internal:8001 \
  -r zap-report.html
```

## File Organization

```
episteme/
├── docker/
│   ├── docker-compose.yml              # Original
│   ├── docker-compose.monitoring.yml   # New monitoring stack
│   └── monitoring/
│       ├── prometheus.yml              # Prometheus config
│       └── grafana/
│           └── provisioning/
│               ├── dashboards/
│               └── datasources/
├── scripts/
│   └── security/
│       ├── enforce-binary-authorization.sh
│       ├── validate-phase1-security.sh
│       └── setup-monitoring-alerts.sh
└── infrastructure/
    └── monitoring/
        ├── alerts/
        └── dashboards/
```

## Best Practices

### IDE Development
1. Use VSCode with Docker extension for container logs
2. Install YAML validation extensions for config files
3. Use GitLens for tracking configuration changes
4. Set up file watchers for auto-reload

### Docker Operations
```bash
# Useful aliases for Phase 1 work
alias dcm='docker-compose -f docker/docker-compose.monitoring.yml'
alias dclogs='docker-compose logs -f'
alias dcrestart='docker-compose restart'

# Quick monitoring check
dcm ps
dcm logs prometheus
```

### Configuration Management
1. Keep secrets in `.env` files (never commit)
2. Use volume mounts for config hot-reload
3. Document all configuration changes
4. Test changes in Docker before committing

## Troubleshooting

### Common Issues

**Port Conflicts:**
```bash
# Check what's using ports
lsof -i :3000  # Grafana
lsof -i :9090  # Prometheus

# Use alternative ports in docker-compose.monitoring.yml
ports:
  - "3001:3000"  # Alternative Grafana port
```

**Permission Issues:**
```bash
# Fix volume permissions
docker exec -it episteme-prometheus chmod -R 777 /prometheus
```

**Memory Issues:**
```bash
# Increase Docker memory allocation
# Docker Desktop > Preferences > Resources > Memory: 8GB minimum
```

## Phase 1 Validation Checklist

- [ ] Binary Authorization scripts created and tested
- [ ] Monitoring stack running (Prometheus, Grafana, Jaeger)
- [ ] All services exposing metrics endpoints
- [ ] Security validation script passing
- [ ] No hardcoded secrets detected
- [ ] OWASP ZAP scan completed
- [ ] Dashboards configured in Grafana
- [ ] Alerts configured in Prometheus
- [ ] Documentation updated
- [ ] All changes committed to Git

## Next Steps

After completing Phase 1 setup:
1. Deploy monitoring stack to Cloud Run
2. Configure production alerting
3. Set up Binary Authorization in CI/CD
4. Document runbooks for incident response