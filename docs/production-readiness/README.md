# Production Readiness Checklists

This directory contains comprehensive production readiness checklists for all Episteme platform services. Each checklist ensures services meet our production quality standards before deployment.

## Service Checklists

1. **[Analysis Engine](./analysis-engine-checklist.md)** - Rust-based code analysis service
2. **[Query Intelligence](./query-intelligence-checklist.md)** - Natural language query processing
3. **[Pattern Mining](./pattern-mining-checklist.md)** - ML-powered pattern detection
4. **[Collaboration](./collaboration-checklist.md)** - Real-time collaboration service
5. **[Marketplace](./marketplace-checklist.md)** - Pattern marketplace and monetization
6. **[Web Frontend](./web-frontend-checklist.md)** - Next.js user interface

## Production Standards

All services must meet these minimum requirements:

### Performance
- Response time: <100ms (p95)
- Throughput: Service-specific targets
- Resource usage: Within defined limits
- Scalability: Horizontal scaling ready

### Reliability
- Uptime: 99.9% SLA
- Error rate: <0.1%
- Recovery time: <5 minutes
- Graceful degradation

### Security
- Authentication: JWT/OAuth implemented
- Authorization: RBAC configured
- Rate limiting: DDoS protection
- Audit logging: Complete trail

### Quality
- Test coverage: >80%
- Documentation: Complete
- Monitoring: Metrics exposed
- Runbooks: Incident response ready

### Deployment
- Containerized: Docker optimized
- CI/CD: Automated pipeline
- Health checks: Liveness/readiness
- Rollback: Quick recovery

## Usage

1. Select the appropriate service checklist
2. Review all requirements systematically
3. Mark items as complete with evidence
4. Document any exceptions or waivers
5. Get sign-off from service owner

## Validation

Each checklist includes:
- Specific validation commands
- Expected outcomes
- Evidence requirements
- Sign-off process

## Updates

These checklists are living documents. Update them when:
- New requirements are identified
- Standards change
- Lessons learned from incidents
- Industry best practices evolve