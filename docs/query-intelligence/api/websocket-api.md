# WebSocket API Documentation

**🔗 Navigation**: [← API Documentation](README.md) | [↑ Query Intelligence](../README.md) | [↗ Developer Guide](../guides/developer-guide.md)

---

## WebSocket API

The Query Intelligence service provides real-time streaming capabilities via WebSocket connections for interactive query processing.

### Connection Endpoint

```
wss://query-intelligence-l3nxty7oka-uc.a.run.app/api/v1/ws/query
```

### Authentication

WebSocket connections require JWT authentication via the `Authorization` header:

```javascript
const ws = new WebSocket('wss://query-intelligence-l3nxty7oka-uc.a.run.app/api/v1/ws/query', {
  headers: {
    'Authorization': 'Bearer YOUR_JWT_TOKEN'
  }
});
```

### Message Format

#### Request Format
```json
{
  "query": "How does authentication work in this codebase?",
  "repository_id": "repo-123",
  "include_context": true,
  "stream": true
}
```

#### Response Format
```json
{
  "type": "response_chunk",
  "chunk": "Authentication in this codebase is handled through...",
  "is_final": false,
  "query_id": "query-456",
  "timestamp": "2025-07-18T10:30:00Z"
}
```

### Complete Example

```javascript
const ws = new WebSocket('wss://query-intelligence-l3nxty7oka-uc.a.run.app/api/v1/ws/query', {
  headers: {
    'Authorization': 'Bearer YOUR_JWT_TOKEN'
  }
});

ws.onopen = function() {
  console.log('WebSocket connected');
  
  // Send query
  ws.send(JSON.stringify({
    query: "Explain the database schema",
    repository_id: "repo-123",
    include_context: true,
    stream: true
  }));
};

ws.onmessage = function(event) {
  const data = JSON.parse(event.data);
  
  if (data.type === 'response_chunk') {
    console.log('Received chunk:', data.chunk);
    
    if (data.is_final) {
      console.log('Query completed');
    }
  }
};

ws.onerror = function(error) {
  console.error('WebSocket error:', error);
};

ws.onclose = function() {
  console.log('WebSocket disconnected');
};
```

---

## Related Documentation

- **[REST API Documentation](README.md)** - Complete REST API reference
- **[Integration Guide](../guides/integration-guide.md)** - Implementation patterns and best practices
- **[Developer Guide](../guides/developer-guide.md)** - Local development and testing
- **[Architecture Documentation](../architecture/README.md)** - System design and WebSocket architecture

---

**🏠 [Query Intelligence Home](../README.md)** | **📚 [All Documentation](../README.md#documentation)**