# Query Intelligence Performance Testing

## Overview

This document provides comprehensive guidance on performance testing for the Query Intelligence service, including testing frameworks, procedures, and validation criteria.

## Testing Framework

### Location
- **Primary Framework**: `/services/query-intelligence/tests/performance/`
- **Load Testing Scripts**: `/services/query-intelligence/performance/`
- **Historical Reports**: `/services/query-intelligence/archive/project-history/performance-reports/`

### Testing Tools
- **K6**: API load testing and performance validation
- **Artillery**: WebSocket load testing and concurrent connections
- **Python Framework**: Comprehensive performance analysis
- **Puppeteer**: End-to-end browser testing

## Performance Testing Types

### 1. Comprehensive Performance Analysis

#### Quick Analysis (30 minutes)
```bash
cd /services/query-intelligence/tests/performance
python -m comprehensive_performance_analysis --duration=30m
```

**Analysis Phases:**
- **Baseline**: 60 seconds idle system analysis
- **Load Testing**: 40% of time under load
- **Sustained Load**: 40% of time sustained performance
- **Scaling**: 20% of time auto-scaling validation

#### Full Production Validation (4 hours)
```bash
cd /services/query-intelligence/tests/performance
python -m comprehensive_performance_analysis --duration=4h --include-benchmarks
```

**Includes:**
- All quick analysis phases
- Comprehensive benchmark suite
- Memory leak detection
- Long-term stability validation

### 2. API Load Testing (K6)

#### Load Test Configuration
```javascript
// k6-load-test.js
import http from 'k6/http';
import { check, sleep } from 'k6';

export let options = {
  stages: [
    { duration: '5m', target: 100 },   // Ramp up
    { duration: '10m', target: 500 },  // Intermediate load
    { duration: '10m', target: 1000 }, // Target load
    { duration: '10m', target: 1500 }, // Stress test
    { duration: '5m', target: 0 },     // Ramp down
  ],
  thresholds: {
    'http_req_duration': ['p(95)<200'], // 95% of requests under 200ms
    'http_req_failed': ['rate<0.01'],   // Error rate under 1%
  },
};
```

#### Running K6 Tests
```bash
cd /services/query-intelligence/performance
k6 run load-tests/k6-load-test.js
```

### 3. WebSocket Load Testing (Artillery)

#### Artillery Configuration
```yaml
# artillery-config.yml
config:
  target: 'wss://query-intelligence-l3nxty7oka-uc.a.run.app'
  phases:
    - duration: 300
      arrivalRate: 10
      name: "Ramp up"
    - duration: 600
      arrivalRate: 50
      name: "Sustained load"
    - duration: 300
      arrivalRate: 100
      name: "Peak load"
  processor: "./performance-processor.js"
  
scenarios:
  - name: "WebSocket streaming"
    weight: 100
    engine: ws
```

#### Running Artillery Tests
```bash
cd /services/query-intelligence/performance
artillery run load-tests/artillery-config.yml
```

### 4. Memory and Resource Testing

#### Memory Profiling
```python
# Run memory profiling
from tests.performance.benchmarks.memory_profiling import MemoryProfiler

profiler = MemoryProfiler(
    profile_interval=1.0,
    enable_tracemalloc=True
)

# Run under load
results = await profiler.run_memory_benchmark(duration_minutes=60)
```

#### CPU Utilization Analysis
```python
# Run CPU analysis
from tests.performance.benchmarks.cpu_utilization_analysis import CPUAnalyzer

analyzer = CPUAnalyzer(analysis_interval=0.5)
results = await analyzer.run_cpu_benchmark(duration_minutes=30)
```

## Performance Validation Criteria

### Production Readiness Checklist
- [ ] **API Throughput**: 1000+ QPS sustained
- [ ] **Response Time**: P95 <200ms under load
- [ ] **WebSocket Capacity**: 500+ concurrent connections
- [ ] **Error Rate**: <1% under stress
- [ ] **Memory Usage**: <4GB under full load
- [ ] **CPU Utilization**: <80% sustained
- [ ] **Cache Hit Rate**: >75%
- [ ] **Recovery Time**: <30s from load spikes

### Performance Targets
| Metric | Target | Validation Method |
|--------|--------|-------------------|
| **API Throughput** | 1000+ QPS | K6 load testing |
| **Response Time (P95)** | <200ms | K6 latency testing |
| **WebSocket Capacity** | 500+ concurrent | Artillery WebSocket testing |
| **Error Rate** | <1% | K6 error rate validation |
| **Memory Usage** | <4GB | Memory profiling |
| **CPU Utilization** | <80% | CPU analysis |
| **Cache Hit Rate** | >75% | Cache efficiency analysis |

## Testing Procedures

### Pre-deployment Testing
1. **Environment Setup**: Ensure test environment matches production
2. **Baseline Measurement**: Record current performance metrics
3. **Load Testing**: Execute K6 and Artillery tests
4. **Resource Analysis**: Run memory and CPU profiling
5. **Validation**: Verify all targets are met

### Continuous Testing
1. **Scheduled Tests**: Run daily performance validation
2. **Regression Testing**: Test after each deployment
3. **Capacity Planning**: Regular load testing to validate scaling
4. **Performance Monitoring**: Continuous metric collection

### Performance Regression Testing
```bash
# Run regression test suite
cd /services/query-intelligence/tests/regression
python -m regression_tests --baseline=production
```

## Test Execution

### Local Testing
```bash
# Set up local environment
cd /services/query-intelligence
docker-compose up -d

# Run performance tests
python -m tests.performance.comprehensive_performance_analysis
```

### Production Testing
```bash
# Run against production (with caution)
export TARGET_URL="https://query-intelligence-l3nxty7oka-uc.a.run.app"
python -m tests.performance.comprehensive_performance_analysis --target=$TARGET_URL
```

### CI/CD Integration
```yaml
# GitHub Actions workflow
name: Performance Testing
on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  performance-test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Run Performance Tests
        run: |
          cd services/query-intelligence
          python -m tests.performance.comprehensive_performance_analysis --duration=15m
```

## Test Results Analysis

### Performance Metrics
- **Response Time Distribution**: P50, P95, P99 latencies
- **Throughput**: Requests per second at various loads
- **Error Rate**: Success/failure rates under load
- **Resource Usage**: Memory, CPU, network utilization

### Report Generation
```python
# Generate performance report
from tests.performance.comprehensive_performance_analysis import ComprehensivePerformanceAnalyzer

analyzer = ComprehensivePerformanceAnalyzer()
results = await analyzer.run_comprehensive_analysis()

# Export results
analyzer.export_results("performance_report.json")
analyzer.export_executive_summary("executive_summary.json")
```

### Historical Comparison
- **Performance Trends**: Compare against historical baselines
- **Regression Detection**: Identify performance degradation
- **Optimization Tracking**: Monitor improvement over time

## Troubleshooting Performance Tests

### Common Issues
1. **Test Environment**: Ensure consistent test conditions
2. **Network Latency**: Account for network variations
3. **Resource Constraints**: Verify test infrastructure capacity
4. **Concurrent Users**: Manage test load appropriately

### Debug Commands
```bash
# Check system resources during test
htop
iostat -x 1

# Monitor network connections
netstat -an | grep ESTABLISHED

# Check application logs
docker logs query-intelligence-container
```

## Performance Test Automation

### Automated Test Suite
```python
# Automated performance testing
import asyncio
from tests.performance.automation import PerformanceTestSuite

suite = PerformanceTestSuite()
results = await suite.run_full_suite()

# Validate results
if results.overall_score < 80:
    raise Exception("Performance regression detected")
```

### Alert Integration
```python
# Send alerts on performance regression
from monitoring.alerts import send_performance_alert

if results.response_time_p95 > 200:
    send_performance_alert(
        "High latency detected",
        f"P95 response time: {results.response_time_p95}ms"
    )
```

## Best Practices

### Test Design
1. **Realistic Load**: Use production-like traffic patterns
2. **Gradual Ramp-up**: Avoid sudden load spikes
3. **Sustained Testing**: Test under load for extended periods
4. **Edge Cases**: Test error conditions and edge cases

### Test Environment
1. **Environment Parity**: Match production configuration
2. **Isolated Testing**: Avoid interference from other services
3. **Resource Monitoring**: Track system resources during testing
4. **Repeatable Tests**: Ensure consistent test conditions

### Result Analysis
1. **Statistical Significance**: Use sufficient sample sizes
2. **Trend Analysis**: Compare against historical data
3. **Root Cause Analysis**: Investigate performance issues
4. **Actionable Insights**: Provide clear optimization recommendations

## References

### Documentation
- [Performance Monitoring](./performance-monitoring.md)
- [Performance Optimization](./performance-optimization.md)
- [Operations Runbook](../operations/runbook.md)

### Test Frameworks
- [K6 Documentation](https://k6.io/docs/)
- [Artillery Documentation](https://artillery.io/docs/)
- [Python Performance Testing](../../../services/query-intelligence/tests/performance/README.md)

### Historical Reports
- [Production Certification](../../../services/query-intelligence/archive/project-history/performance-reports/FINAL_PRODUCTION_LOAD_TESTING_CERTIFICATION.md)
- [Performance Baseline](../../../services/query-intelligence/archive/project-history/performance-reports/PERFORMANCE_BASELINE.md)

---

**Last Updated**: July 18, 2025
**Next Review**: August 18, 2025
**Document Owner**: Query Intelligence Team