# Query Intelligence Performance Documentation

## Overview

This directory contains performance documentation for the Query Intelligence service, including performance targets, monitoring guidelines, and optimization strategies.

## Performance Targets

### Core Performance Metrics
- **API Throughput**: 1000+ QPS sustained
- **Response Time**: P95 <200ms under load
- **WebSocket Capacity**: 500+ concurrent connections
- **Error Rate**: <1% under stress
- **Recovery Time**: <30s from load spikes

### Resource Utilization
- **Memory Usage**: <4GB under full load
- **CPU Utilization**: <80% sustained
- **Cache Hit Rate**: >75%
- **Auto-scaling Efficiency**: >70%

## Documentation Structure

### Current Performance Status
- **Status**: ✅ Production Certified
- **Last Certification**: July 14, 2025
- **Certification Level**: 1000+ QPS Capacity
- **Performance Score**: 100% Production Ready

### Key Documents
- [`performance-monitoring.md`](./performance-monitoring.md) - Performance monitoring and alerting
- [`performance-optimization.md`](./performance-optimization.md) - Optimization strategies and best practices
- [`performance-testing.md`](./performance-testing.md) - Testing framework and procedures

### Historical Performance Reports
Historical performance reports and certifications are maintained in:
- `/services/query-intelligence/archive/project-history/performance-reports/`
  - `FINAL_PRODUCTION_LOAD_TESTING_CERTIFICATION.md` - Production certification
  - `PERFORMANCE_BASELINE.md` - Performance baseline and metrics
  - `PRODUCTION_PERFORMANCE_SUMMARY.md` - Production readiness summary

### Load Testing Framework
The comprehensive load testing framework is located at:
- `/services/query-intelligence/tests/performance/` - Performance testing framework
- `/services/query-intelligence/performance/` - Load testing scripts and tools

## Quick Reference

### Performance Monitoring
- **Health Check**: `https://query-intelligence-l3nxty7oka-uc.a.run.app/health`
- **Readiness Check**: `https://query-intelligence-l3nxty7oka-uc.a.run.app/ready`
- **Metrics Dashboard**: Google Cloud Console Run service metrics

### Key Commands
```bash
# Check current performance
gcloud monitoring time-series list --filter='metric.type="run.googleapis.com/request_latencies"'

# Run performance tests
cd /services/query-intelligence/performance
python run_performance_validation.py

# Run comprehensive analysis
cd /services/query-intelligence/tests/performance
python -m comprehensive_performance_analysis
```

### Alert Thresholds
- **Critical**: P95 >500ms, Error rate >5%, Memory >90%
- **Warning**: P95 >200ms, Error rate >1%, Memory >80%

## Support

For performance-related issues:
1. Check the monitoring dashboard
2. Review recent performance metrics
3. Consult the operations runbook
4. Contact the Query Intelligence team

---

**Last Updated**: July 18, 2025
**Next Review**: August 18, 2025
**Document Owner**: Query Intelligence Team