# Query Intelligence Performance Monitoring

## Overview

This document provides comprehensive performance monitoring guidelines for the Query Intelligence service, including metrics, alerting, and analysis procedures.

## Key Performance Metrics

### Response Time Metrics
```bash
# Check current latency
gcloud monitoring time-series list \
  --filter='metric.type="run.googleapis.com/request_latencies"' \
  --interval.end-time=$(date -Iseconds) \
  --interval.start-time=$(date -d '1 hour ago' -Iseconds)
```

**Alert Thresholds:**
- **Warning**: P95 >200ms for >10 minutes
- **Critical**: P95 >500ms for >5 minutes

### Throughput Metrics
```bash
# Check request rate
gcloud monitoring time-series list \
  --filter='metric.type="run.googleapis.com/request_count"' \
  --interval.end-time=$(date -Iseconds) \
  --interval.start-time=$(date -d '1 hour ago' -Iseconds)
```

**Performance Targets:**
- **Sustained**: 1000 QPS
- **Peak**: 2000 QPS
- **Auto-scaling**: Up to 200 instances

### Error Rate Metrics
```bash
# Check error rate
gcloud logs read 'resource.type="cloud_run_revision" 
  AND resource.labels.service_name="query-intelligence" 
  AND httpRequest.status>=400' \
  --limit=50 \
  --format="table(timestamp,httpRequest.status,httpRequest.requestUrl)"
```

**Alert Thresholds:**
- **Warning**: >1% for 10 minutes
- **Critical**: >5% for 5 minutes
- **Emergency**: >10% for 2 minutes

### Resource Usage Metrics
```bash
# Check memory usage
gcloud monitoring time-series list \
  --filter='metric.type="run.googleapis.com/container/memory/utilizations"'

# Check CPU usage
gcloud monitoring time-series list \
  --filter='metric.type="run.googleapis.com/container/cpu/utilizations"'
```

**Alert Thresholds:**
- **Memory**: >80% warning, >90% critical
- **CPU**: >75% warning, >85% critical

## Monitoring Dashboard

### Google Cloud Console
- **URL**: https://console.cloud.google.com/run/detail/us-central1/query-intelligence
- **Key Metrics**: Request count, latency, error rate, memory usage

### Custom Metrics
- **Cache Hit Rate**: >75% target
- **WebSocket Connections**: 500+ concurrent capacity
- **Circuit Breaker Status**: CLOSED for healthy operation

## Alerting Configuration

### Critical Alerts (Immediate Response)
- **Service Down**: Health check failures for >2 minutes
- **High Error Rate**: >5% error rate for >5 minutes
- **High Latency**: P95 >500ms for >5 minutes
- **Memory Exhaustion**: >90% memory usage for >5 minutes

### Warning Alerts (Response within 1 hour)
- **Moderate Error Rate**: >1% error rate for >10 minutes
- **Moderate Latency**: P95 >200ms for >10 minutes
- **High Memory Usage**: >80% memory usage for >15 minutes
- **Dependency Issues**: Any dependency unhealthy

## Performance Analysis

### Daily Performance Check
```bash
# Morning health check
curl https://query-intelligence-l3nxty7oka-uc.a.run.app/health
curl https://query-intelligence-l3nxty7oka-uc.a.run.app/ready

# Check metrics trends
gcloud monitoring time-series list \
  --filter='metric.type="run.googleapis.com/request_latencies"' \
  --interval.end-time=$(date -Iseconds) \
  --interval.start-time=$(date -d '24 hours ago' -Iseconds)
```

### Weekly Performance Review
```bash
# Generate weekly performance report
gcloud monitoring time-series list \
  --filter='metric.type="run.googleapis.com/request_latencies"' \
  --interval.end-time=$(date -Iseconds) \
  --interval.start-time=$(date -d '7 days ago' -Iseconds)

# Review scaling patterns
gcloud monitoring time-series list \
  --filter='metric.type="run.googleapis.com/container/instance_count"' \
  --interval.end-time=$(date -Iseconds) \
  --interval.start-time=$(date -d '7 days ago' -Iseconds)
```

## Performance Troubleshooting

### High Latency Investigation
1. **Check resource usage**: Memory and CPU utilization
2. **Verify dependencies**: Analysis Engine, Pattern Mining, Redis
3. **Review error logs**: Recent error patterns
4. **Check cache performance**: Redis hit rates

### High Error Rate Investigation
1. **Analyze error types**: Authentication, validation, dependency errors
2. **Check dependency health**: All upstream services
3. **Review recent deployments**: Potential regression causes
4. **Verify configuration**: Environment variables and secrets

### Memory Issues Investigation
1. **Check memory trends**: Growth patterns over time
2. **Review cache usage**: Redis memory utilization
3. **Analyze request patterns**: Memory-intensive operations
4. **Check for memory leaks**: Long-running processes

## Performance Optimization

### Immediate Actions
- **Scale up instances**: Increase min/max instances
- **Adjust resource limits**: Increase memory/CPU allocation
- **Clear cache**: Reset Redis cache if needed
- **Enable circuit breakers**: Protect against cascading failures

### Long-term Optimizations
- **Code optimization**: Async processing, caching improvements
- **Resource tuning**: Right-sizing based on usage patterns
- **Cache strategy**: Optimize cache keys and TTL values
- **Auto-scaling**: Fine-tune scaling parameters

## Monitoring Best Practices

### Proactive Monitoring
1. **Set up trend analysis**: Monitor performance over time
2. **Implement capacity planning**: Predict resource needs
3. **Create performance budgets**: Prevent performance regression
4. **Regular performance reviews**: Weekly and monthly analysis

### Reactive Monitoring
1. **Alert response procedures**: Clear escalation paths
2. **Incident documentation**: Track performance incidents
3. **Root cause analysis**: Identify underlying issues
4. **Post-incident reviews**: Learn from performance problems

## Integration with Operations

### Operations Runbook
- **Location**: `/docs/query-intelligence/operations/runbook.md`
- **Incident Response**: Step-by-step procedures
- **Escalation**: Contact information and procedures

### Performance Testing
- **Framework**: `/services/query-intelligence/tests/performance/`
- **Load Testing**: K6 and Artillery scripts
- **Continuous Testing**: Automated performance validation

---

**Last Updated**: July 18, 2025
**Next Review**: August 18, 2025
**Document Owner**: Query Intelligence Team