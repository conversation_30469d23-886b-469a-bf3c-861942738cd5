# Query Intelligence Monitoring Guide

## Overview

This guide provides comprehensive monitoring setup and operations procedures for the Query Intelligence service, consolidating deployment, operations, and troubleshooting information.

## Service Overview

### Key Service Information
- **Service Name**: Query Intelligence
- **Status**: ✅ Production Ready
- **URL**: https://query-intelligence-l3nxty7oka-uc.a.run.app
- **Platform**: Google Cloud Run
- **Owner**: Query Intelligence Team

### Performance Targets
- **Response Time**: P95 <200ms
- **Throughput**: 1000+ QPS sustained
- **Error Rate**: <1%
- **Availability**: 99.9%
- **Memory Usage**: <4GB
- **CPU Utilization**: <80%

## Monitoring Architecture

### Core Components
- **Prometheus**: Metrics collection and alerting
- **Grafana**: Visualization and dashboards
- **Alertmanager**: Alert routing and notifications
- **Jaeger**: Distributed tracing
- **Cloud Monitoring**: Google Cloud native monitoring

### Infrastructure Location
- **Configuration**: `/services/query-intelligence/monitoring/`
- **Deployment Scripts**: `/services/query-intelligence/monitoring/scripts/`
- **Documentation**: `/services/query-intelligence/monitoring/docs/`

## Health Monitoring

### Health Endpoints

#### Primary Health Check
```bash
curl https://query-intelligence-l3nxty7oka-uc.a.run.app/health
```

**Expected Response:**
```json
{
  "status": "healthy",
  "timestamp": "2025-07-18T12:00:00Z"
}
```

#### Readiness Check
```bash
curl https://query-intelligence-l3nxty7oka-uc.a.run.app/ready
```

**Expected Response:**
```json
{
  "status": "ready",
  "checks": {
    "redis": "ok",
    "analysis_engine": "ok",
    "pattern_mining": "ok",
    "genai_service": "ok"
  },
  "timestamp": "2025-07-18T12:00:00Z"
}
```

### Monitoring Dashboards

#### Available Dashboards
1. **Executive Dashboard**: High-level service metrics
2. **Technical Performance Dashboard**: Detailed performance metrics
3. **Realtime Operations Dashboard**: Live operational metrics
4. **Security Monitoring Dashboard**: Security-related metrics
5. **Capacity Planning Dashboard**: Resource usage and scaling

#### Dashboard Access
- **Grafana URL**: Configure via monitoring deployment
- **Access Control**: Role-based access control
- **Mobile Access**: Responsive dashboard design

## Metrics Collection

### Key Metrics

#### Response Time Metrics
```bash
# Check current latency
gcloud monitoring time-series list \
  --filter='metric.type="run.googleapis.com/request_latencies"' \
  --interval.end-time=$(date -Iseconds) \
  --interval.start-time=$(date -d '1 hour ago' -Iseconds)
```

#### Throughput Metrics
```bash
# Check request rate
gcloud monitoring time-series list \
  --filter='metric.type="run.googleapis.com/request_count"' \
  --interval.end-time=$(date -Iseconds) \
  --interval.start-time=$(date -d '1 hour ago' -Iseconds)
```

#### Error Rate Metrics
```bash
# Check error logs
gcloud logs read 'resource.type="cloud_run_revision" 
  AND resource.labels.service_name="query-intelligence" 
  AND httpRequest.status>=400' \
  --limit=50
```

#### Resource Usage Metrics
```bash
# Memory usage
gcloud monitoring time-series list \
  --filter='metric.type="run.googleapis.com/container/memory/utilizations"'

# CPU usage
gcloud monitoring time-series list \
  --filter='metric.type="run.googleapis.com/container/cpu/utilizations"'
```

## Alerting Configuration

### Alert Categories

#### Critical Alerts (Immediate Response <5 minutes)
- **Service Down**: Health check failures >2 minutes
- **High Error Rate**: >5% error rate >5 minutes
- **High Latency**: P95 >500ms >5 minutes
- **Memory Exhaustion**: >90% memory usage >5 minutes

#### Warning Alerts (Response <1 hour)
- **Moderate Error Rate**: >1% error rate >10 minutes
- **Moderate Latency**: P95 >200ms >10 minutes
- **High Memory Usage**: >80% memory usage >15 minutes
- **Dependency Issues**: Any dependency unhealthy

#### Info Alerts (Response <4 hours)
- **Scaling Events**: Auto-scaling activities
- **Performance Trends**: Performance degradation trends
- **Cache Performance**: Cache hit rate below target

### Alert Configuration Files
- **Prometheus Alerts**: `/services/query-intelligence/monitoring/alerting/prometheus-alerts.yml`
- **Alertmanager Config**: `/services/query-intelligence/monitoring/alerting/alertmanager-config.yml`
- **Escalation Procedures**: `/services/query-intelligence/monitoring/alerting/escalation-procedures.yml`

## Operational Procedures

### Daily Operations

#### Morning Health Check
```bash
# Check service health
curl https://query-intelligence-l3nxty7oka-uc.a.run.app/health
curl https://query-intelligence-l3nxty7oka-uc.a.run.app/ready

# Check monitoring components
kubectl get pods -n monitoring

# Review active alerts
curl -s http://localhost:9093/api/v1/alerts | jq '.data[] | select(.status.state == "active")'
```

#### Performance Review
1. **Response Time**: Check P95 latency trends
2. **Throughput**: Monitor request rate patterns
3. **Error Rate**: Review error rate trends
4. **Resource Usage**: Monitor memory and CPU utilization

### Weekly Operations

#### Performance Analysis
```bash
# Generate weekly report
gcloud monitoring time-series list \
  --filter='metric.type="run.googleapis.com/request_latencies"' \
  --interval.end-time=$(date -Iseconds) \
  --interval.start-time=$(date -d '7 days ago' -Iseconds)
```

#### Alert Review
1. **Alert Frequency**: Analyze alert patterns
2. **Alert Effectiveness**: Review alert resolution times
3. **Threshold Tuning**: Adjust alert thresholds if needed
4. **Documentation Updates**: Update runbooks and procedures

### Monthly Operations

#### Infrastructure Maintenance
1. **Component Updates**: Update Prometheus, Grafana, Alertmanager
2. **Security Updates**: Apply security patches
3. **Backup Testing**: Verify backup and recovery procedures
4. **Performance Optimization**: Optimize monitoring queries

## Incident Response

### Incident Classification

#### Severity 1 (Critical)
- **Definition**: Service down or >50% error rate
- **Response Time**: <5 minutes
- **Escalation**: On-call + manager

#### Severity 2 (High)
- **Definition**: Degraded performance or >10% error rate
- **Response Time**: <15 minutes
- **Escalation**: On-call engineer

#### Severity 3 (Medium)
- **Definition**: Minor issues or warnings
- **Response Time**: <1 hour
- **Escalation**: During business hours

### Incident Response Steps

#### Step 1: Initial Assessment (2 minutes)
```bash
# Check service health
curl https://query-intelligence-l3nxty7oka-uc.a.run.app/health

# Check recent logs
gcloud logs read 'resource.type="cloud_run_revision" 
  AND resource.labels.service_name="query-intelligence"' \
  --limit=20
```

#### Step 2: Root Cause Analysis (5 minutes)
```bash
# Check recent deployments
gcloud run revisions list --service=query-intelligence --region=us-central1 --limit=5

# Check dependencies
curl https://analysis-engine-572735000332.us-central1.run.app/health
```

#### Step 3: Mitigation (10 minutes)
```bash
# Scale up instances
gcloud run services update query-intelligence \
  --min-instances=10 \
  --region=us-central1

# Or rollback to previous version
PREVIOUS_REVISION=$(gcloud run revisions list --service=query-intelligence --region=us-central1 --limit=2 --format="value(metadata.name)" | tail -n 1)
gcloud run services update-traffic query-intelligence \
  --to-revisions="$PREVIOUS_REVISION=100" \
  --region=us-central1
```

## Distributed Tracing

### Jaeger Configuration
- **Location**: `/services/query-intelligence/monitoring/observability/tracing-config.py`
- **Integration**: OpenTelemetry collector
- **Retention**: 7 days

### Tracing Analysis
```bash
# Access Jaeger UI
kubectl port-forward -n monitoring svc/jaeger-query 16686:16686

# Analyze traces
# Open browser to http://localhost:16686
```

## SLI/SLO Monitoring

### Service Level Indicators (SLIs)
- **Availability**: Percentage of successful requests
- **Latency**: 95th percentile response time
- **Error Rate**: Percentage of failed requests
- **Throughput**: Requests per second

### Service Level Objectives (SLOs)
- **Availability**: 99.9% uptime
- **Latency**: P95 <200ms
- **Error Rate**: <1%
- **Throughput**: 1000+ QPS

### Error Budget Management
```bash
# Check error budget status
curl -s http://localhost:9090/api/v1/query?query=error_budget_remaining

# Monitor burn rate
curl -s http://localhost:9090/api/v1/query?query=burn_rate_1h
```

## Troubleshooting

### Common Issues

#### Prometheus Not Collecting Metrics
1. **Check targets**: Verify service discovery
2. **Check connectivity**: Network access to service
3. **Check configuration**: Prometheus config syntax
4. **Check logs**: Prometheus container logs

#### Grafana Dashboards Not Loading
1. **Check data source**: Prometheus connection
2. **Check queries**: Dashboard query syntax
3. **Check permissions**: Dashboard access rights
4. **Check resources**: Grafana resource limits

#### Alerts Not Firing
1. **Check alert rules**: Prometheus alert configuration
2. **Check Alertmanager**: Alert routing configuration
3. **Check notification channels**: Slack, email, PagerDuty
4. **Check silences**: Active alert silences

### Debug Commands
```bash
# Check Prometheus targets
kubectl port-forward -n monitoring svc/prometheus-operated 9090:9090
curl -s http://localhost:9090/api/v1/targets

# Check Grafana health
kubectl port-forward -n monitoring svc/grafana 3000:3000
curl -s http://localhost:3000/api/health

# Check Alertmanager status
kubectl port-forward -n monitoring svc/alertmanager 9093:9093
curl -s http://localhost:9093/api/v1/status
```

## Performance Optimization

### Query Optimization
1. **Identify slow queries**: Monitor query performance
2. **Optimize recording rules**: Pre-calculate common queries
3. **Use appropriate time ranges**: Avoid excessive data queries
4. **Implement caching**: Cache frequently accessed data

### Resource Optimization
1. **Right-size components**: Adjust CPU and memory limits
2. **Optimize retention**: Balance storage vs. query performance
3. **Use efficient storage**: Configure appropriate storage classes
4. **Monitor resource usage**: Track component resource consumption

## Integration with Operations

### Operations Runbook
- **Location**: `/docs/query-intelligence/operations/runbook.md`
- **Integration**: Shared incident response procedures
- **Escalation**: Common contact information

### Performance Monitoring
- **Location**: `/docs/query-intelligence/performance/performance-monitoring.md`
- **Integration**: Performance metrics and alerting
- **Optimization**: Performance tuning recommendations

## Best Practices

### Monitoring
1. **Comprehensive Coverage**: Monitor all critical components
2. **Appropriate Granularity**: Balance detail vs. noise
3. **Realistic Thresholds**: Set thresholds based on actual performance
4. **Regular Review**: Continuously improve monitoring

### Alerting
1. **Actionable Alerts**: Every alert should have a clear action
2. **Proper Prioritization**: Use appropriate severity levels
3. **Avoid Alert Fatigue**: Don't over-alert
4. **Clear Documentation**: Document alert response procedures

### Dashboards
1. **User-Focused**: Design for specific audiences
2. **Performance**: Optimize for fast loading
3. **Maintenance**: Keep current with service changes
4. **Access Control**: Appropriate permissions

---

**Last Updated**: July 18, 2025
**Next Review**: August 18, 2025
**Document Owner**: Query Intelligence Team