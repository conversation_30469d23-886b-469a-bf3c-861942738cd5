# Query Intelligence Monitoring Documentation

## Overview

This directory contains comprehensive monitoring documentation for the Query Intelligence service, including deployment guides, operations procedures, and alerting configurations.

## Documentation Structure

### Core Monitoring Documents
- [`monitoring-guide.md`](./monitoring-guide.md) - Complete monitoring setup and operations
- [`alerting-guide.md`](./alerting-guide.md) - Alerting configuration and procedures
- [`dashboards-guide.md`](./dashboards-guide.md) - Dashboard setup and usage

### Monitoring Infrastructure
The complete monitoring infrastructure is maintained in:
- `/services/query-intelligence/monitoring/` - Infrastructure configuration
  - `alerting/` - Prometheus alerts and escalation procedures
  - `dashboards/` - Grafana dashboards (JSON format)
  - `observability/` - SLI/SLO configuration and enhanced metrics
  - `docs/` - Detailed deployment and operations guides

## Quick Reference

### Service Health
- **Health Check**: `https://query-intelligence-l3nxty7oka-uc.a.run.app/health`
- **Readiness Check**: `https://query-intelligence-l3nxty7oka-uc.a.run.app/ready`
- **Metrics Dashboard**: Google Cloud Console Run service

### Key Monitoring Components
- **Prometheus**: Metrics collection and alerting
- **Grafana**: Visualization and dashboards
- **Alertmanager**: Alert routing and notifications
- **Jaeger**: Distributed tracing
- **Cloud Monitoring**: Google Cloud native monitoring

### Alert Categories
- **Critical**: Service down, high error rate (>5%), high latency (>500ms)
- **Warning**: Moderate errors (>1%), moderate latency (>200ms), resource usage
- **Info**: Dependency issues, scaling events, performance trends

## Monitoring Deployment

### Quick Deployment
```bash
# Deploy monitoring infrastructure
cd /services/query-intelligence/monitoring
./scripts/deploy-monitoring.sh

# Import dashboards
./scripts/import-dashboards.sh

# Configure alerting
kubectl apply -f alerting/prometheus-alerts.yml
```

### Detailed Deployment
Refer to the comprehensive deployment guide:
- [Monitoring Infrastructure Deployment](../../services/query-intelligence/monitoring/docs/deployment-guide.md)

## Operations

### Daily Operations
- **Health Check**: Verify all monitoring components are operational
- **Alert Review**: Check active alerts and silenced alerts
- **Dashboard Review**: Review key metrics and trends
- **SLI/SLO Status**: Monitor service level indicators

### Incident Response
- **Alert Acknowledgment**: Acknowledge and assign alerts
- **Escalation**: Follow escalation procedures for critical issues
- **Communication**: Update stakeholders on incident status
- **Post-incident**: Document lessons learned and improvements

### Maintenance
- **Updates**: Keep monitoring components updated
- **Backup**: Regular backup of configurations and dashboards
- **Performance**: Monitor monitoring system performance
- **Documentation**: Keep monitoring documentation current

## Integration Points

### Performance Monitoring
- **Performance Metrics**: Links to [Performance Monitoring](../performance/performance-monitoring.md)
- **Load Testing**: Integration with performance testing framework
- **Resource Optimization**: Connection to optimization strategies

### Operations Runbook
- **Incident Response**: Integration with [Operations Runbook](../operations/runbook.md)
- **Troubleshooting**: Links to common issue resolution
- **Escalation**: Contact information and procedures

## Monitoring Architecture

### Components
- **Prometheus**: Metrics collection, recording rules, alerting rules
- **Grafana**: Dashboards, visualization, user interface
- **Alertmanager**: Alert routing, notifications, silencing
- **Jaeger**: Distributed tracing, performance analysis
- **Cloud Monitoring**: Native Google Cloud monitoring integration

### Data Flow
1. **Metrics Collection**: Prometheus scrapes metrics from service
2. **Data Storage**: Metrics stored in Prometheus TSDB
3. **Visualization**: Grafana queries Prometheus for dashboard data
4. **Alerting**: Prometheus evaluates alert rules and sends to Alertmanager
5. **Notifications**: Alertmanager routes alerts to appropriate channels

## Configuration Management

### Alert Configuration
- **Location**: `/services/query-intelligence/monitoring/alerting/`
- **Files**: `prometheus-alerts.yml`, `alertmanager-config.yml`
- **Updates**: Version controlled, deployed via CI/CD

### Dashboard Configuration
- **Location**: `/services/query-intelligence/monitoring/dashboards/`
- **Format**: JSON dashboard definitions
- **Management**: Imported via Grafana API

### SLI/SLO Configuration
- **Location**: `/services/query-intelligence/monitoring/observability/`
- **Files**: `sli-slo-config.yml`, `enhanced-metrics.py`
- **Integration**: Prometheus recording rules and Grafana dashboards

## Support and Troubleshooting

### Common Issues
1. **Prometheus not collecting metrics**: Check service discovery and targets
2. **Grafana dashboards not loading**: Verify data source configuration
3. **Alerts not firing**: Check alert rules and Prometheus configuration
4. **High monitoring resource usage**: Optimize queries and retention

### Getting Help
1. **Documentation**: Check monitoring guides and troubleshooting
2. **Logs**: Review monitoring component logs
3. **Support**: Contact Platform Engineering team
4. **Escalation**: Follow incident response procedures

## Best Practices

### Monitoring
1. **Proactive Monitoring**: Set up alerts before issues occur
2. **Comprehensive Coverage**: Monitor all critical service components
3. **Realistic Thresholds**: Set alert thresholds based on actual performance
4. **Regular Review**: Regularly review and update monitoring configuration

### Alerting
1. **Actionable Alerts**: Every alert should have a clear action
2. **Alert Fatigue**: Avoid too many low-priority alerts
3. **Escalation Paths**: Clear escalation procedures for different alert types
4. **Documentation**: Document alert response procedures

### Dashboards
1. **User-Focused**: Design dashboards for specific user needs
2. **Performance**: Optimize dashboard queries for fast loading
3. **Maintenance**: Keep dashboards updated with service changes
4. **Access Control**: Appropriate permissions for different user roles

---

**Last Updated**: July 18, 2025
**Next Review**: August 18, 2025
**Document Owner**: Query Intelligence Team