# Query Intelligence Dashboards Guide

## Overview

This guide provides comprehensive information about monitoring dashboards for the Query Intelligence service, including dashboard descriptions, usage instructions, and maintenance procedures.

## Dashboard Architecture

### Dashboard Categories
1. **Executive Dashboards**: High-level business metrics
2. **Technical Dashboards**: Detailed technical metrics
3. **Operational Dashboards**: Real-time operational metrics
4. **Security Dashboards**: Security-related metrics
5. **Capacity Planning Dashboards**: Resource planning metrics

### Dashboard Storage
- **Location**: `/services/query-intelligence/monitoring/dashboards/`
- **Format**: JSON dashboard definitions
- **Management**: Version controlled and deployed via CI/CD

## Available Dashboards

### 1. Executive Dashboard
**File**: `executive-dashboard.json`  
**Purpose**: High-level service overview for executives and managers

#### Key Metrics
- **Service Availability**: 99.9% uptime target
- **User Experience**: Response time and error rate
- **Business Impact**: Request volume and success rate
- **Cost Efficiency**: Resource utilization and cost trends

#### Panels
- Service Health Overview
- Response Time Trends (24h/7d/30d)
- Error Rate Summary
- Request Volume Patterns
- Cost Analysis
- SLA Compliance

#### Usage
- **Audience**: Executives, managers, stakeholders
- **Update Frequency**: Real-time with 1-minute refresh
- **Access**: Read-only for most users

### 2. Technical Performance Dashboard
**File**: `technical-performance-dashboard.json`  
**Purpose**: Detailed technical metrics for engineers

#### Key Metrics
- **Response Time**: P50, P95, P99 latencies
- **Throughput**: Requests per second
- **Error Analysis**: Error types and patterns
- **Resource Usage**: CPU, memory, network

#### Panels
- Response Time Distribution
- Request Rate by Endpoint
- Error Rate by Type
- Memory Usage Over Time
- CPU Utilization
- Cache Hit Rate
- Database Performance
- External Service Latency

#### Usage
- **Audience**: Engineers, SREs, DevOps
- **Update Frequency**: Real-time with 30-second refresh
- **Access**: Read/write for engineering team

### 3. Realtime Operations Dashboard
**File**: `realtime-operations-dashboard.json`  
**Purpose**: Live operational metrics for incident response

#### Key Metrics
- **Current Status**: Live service health
- **Active Alerts**: Current alert status
- **Resource Utilization**: Real-time resource usage
- **Performance**: Live performance metrics

#### Panels
- Service Status Indicator
- Active Alerts Summary
- Live Request Rate
- Current Response Time
- Memory Usage Gauge
- CPU Usage Gauge
- Active Connections
- Circuit Breaker Status

#### Usage
- **Audience**: On-call engineers, operations team
- **Update Frequency**: Real-time with 5-second refresh
- **Access**: Read-only for operations team

### 4. Security Monitoring Dashboard
**File**: `security-monitoring-dashboard.json`  
**Purpose**: Security-related metrics and events

#### Key Metrics
- **Authentication**: Login attempts and failures
- **Authorization**: Permission denied events
- **Rate Limiting**: Rate limit violations
- **Suspicious Activity**: Anomaly detection

#### Panels
- Authentication Success/Failure Rate
- Rate Limit Violations
- Suspicious IP Addresses
- Failed Authorization Attempts
- Security Events Timeline
- Threat Intelligence Integration

#### Usage
- **Audience**: Security team, DevSecOps
- **Update Frequency**: Real-time with 1-minute refresh
- **Access**: Restricted to security team

### 5. Capacity Planning Dashboard
**File**: `capacity-planning-dashboard.json`  
**Purpose**: Resource planning and scaling analysis

#### Key Metrics
- **Resource Trends**: Historical usage patterns
- **Scaling Events**: Auto-scaling activities
- **Capacity Forecasting**: Future resource needs
- **Cost Analysis**: Resource cost optimization

#### Panels
- Resource Usage Trends (7d/30d/90d)
- Scaling Events History
- Capacity Forecasting
- Cost Per Request
- Instance Utilization
- Peak Load Analysis

#### Usage
- **Audience**: Infrastructure team, capacity planners
- **Update Frequency**: Hourly refresh
- **Access**: Read-only for most users

## Dashboard Access and Permissions

### Role-Based Access Control
```yaml
roles:
  viewer:
    permissions: ["read"]
    dashboards: ["executive", "technical", "operations"]
    
  editor:
    permissions: ["read", "write"]
    dashboards: ["technical", "operations", "capacity"]
    
  admin:
    permissions: ["read", "write", "admin"]
    dashboards: ["all"]
    
  security:
    permissions: ["read", "write"]
    dashboards: ["security", "operations"]
```

### Access URLs
- **Grafana Base URL**: Configure during deployment
- **Executive Dashboard**: `/d/executive/query-intelligence-executive`
- **Technical Dashboard**: `/d/technical/query-intelligence-technical`
- **Operations Dashboard**: `/d/operations/query-intelligence-operations`
- **Security Dashboard**: `/d/security/query-intelligence-security`
- **Capacity Dashboard**: `/d/capacity/query-intelligence-capacity`

## Dashboard Usage

### Navigation
1. **Home**: Overview of all available dashboards
2. **Favorites**: Bookmark frequently used dashboards
3. **Search**: Find specific dashboards or panels
4. **Folders**: Organize dashboards by category

### Time Range Selection
- **Quick Ranges**: Last 5m, 15m, 1h, 6h, 12h, 24h, 7d, 30d
- **Custom Range**: Specify exact start and end times
- **Relative Time**: From now minus X time
- **Absolute Time**: Fixed time range

### Panel Interactions
- **Zoom**: Click and drag to zoom into time range
- **Drill Down**: Click on metrics to drill down
- **Legend**: Click to show/hide series
- **Tooltip**: Hover for detailed information

### Alerting Integration
- **Alert Status**: Visual indicators for active alerts
- **Alert History**: Historical alert data
- **Alert Annotations**: Alerts marked on time series

## Dashboard Maintenance

### Dashboard Deployment
```bash
# Deploy all dashboards
cd /services/query-intelligence/monitoring
./scripts/import-dashboards.sh

# Deploy specific dashboard
grafana-cli dashboards install executive-dashboard.json
```

### Dashboard Updates
1. **Edit JSON**: Modify dashboard JSON files
2. **Version Control**: Commit changes to Git
3. **CI/CD Pipeline**: Automated deployment
4. **Validation**: Test dashboard functionality

### Dashboard Backup
```bash
# Export dashboard
curl -H "Authorization: Bearer $GRAFANA_API_KEY" \
  "http://grafana.example.com/api/dashboards/uid/dashboard-uid" \
  > dashboard-backup.json

# Import dashboard
curl -X POST \
  -H "Authorization: Bearer $GRAFANA_API_KEY" \
  -H "Content-Type: application/json" \
  -d @dashboard-backup.json \
  "http://grafana.example.com/api/dashboards/db"
```

## Dashboard Customization

### Creating Custom Dashboards
1. **Clone Template**: Start with existing dashboard
2. **Modify Panels**: Add/remove/modify panels
3. **Configure Queries**: Update Prometheus queries
4. **Test Thoroughly**: Validate all panels work correctly
5. **Document Changes**: Update this guide

### Panel Configuration
```json
{
  "type": "graph",
  "title": "Response Time",
  "targets": [
    {
      "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
      "legendFormat": "95th percentile"
    }
  ],
  "yAxes": [
    {
      "label": "Time (seconds)",
      "min": 0
    }
  ]
}
```

### Alert Rules Integration
```json
{
  "alert": {
    "conditions": [
      {
        "query": {
          "queryType": "prometheus",
          "refId": "A"
        },
        "reducer": {
          "params": [],
          "type": "last"
        },
        "evaluator": {
          "params": [0.5],
          "type": "gt"
        }
      }
    ],
    "executionErrorState": "alerting",
    "noDataState": "no_data",
    "frequency": "10s",
    "handler": 1,
    "name": "High Response Time",
    "message": "Response time is above 500ms"
  }
}
```

## Performance Optimization

### Dashboard Performance
1. **Optimize Queries**: Use efficient Prometheus queries
2. **Limit Time Range**: Avoid excessively long time ranges
3. **Reduce Refresh Rate**: Use appropriate refresh intervals
4. **Cache Results**: Enable query result caching

### Query Optimization
```promql
# Good: Efficient query
rate(http_requests_total[5m])

# Bad: Inefficient query
increase(http_requests_total[1h])[5m:]

# Good: Using recording rules
request_rate_5m

# Bad: Complex calculation in dashboard
sum(rate(http_requests_total[5m])) by (instance) / sum(rate(http_requests_total[5m]))
```

## Troubleshooting

### Common Issues

#### Dashboard Not Loading
1. **Check Grafana Status**: Verify Grafana is running
2. **Check Data Source**: Verify Prometheus connection
3. **Check Permissions**: Verify user has access
4. **Check Browser**: Clear cache and cookies

#### Panels Showing No Data
1. **Check Queries**: Verify Prometheus queries
2. **Check Time Range**: Ensure data exists in time range
3. **Check Metrics**: Verify metrics are being collected
4. **Check Labels**: Verify label selectors are correct

#### Slow Dashboard Performance
1. **Optimize Queries**: Reduce query complexity
2. **Limit Time Range**: Use shorter time ranges
3. **Reduce Refresh Rate**: Increase refresh interval
4. **Check Resources**: Verify Grafana resources

### Debug Commands
```bash
# Check Grafana logs
kubectl logs -n monitoring grafana-0

# Check Prometheus connectivity
curl -s http://prometheus.example.com/api/v1/query?query=up

# Test query performance
curl -s "http://prometheus.example.com/api/v1/query?query=your_query_here"
```

## Dashboard Best Practices

### Design Principles
1. **User-Focused**: Design for specific user needs
2. **Clear Layout**: Logical organization and flow
3. **Consistent Styling**: Use consistent colors and fonts
4. **Informative Titles**: Clear panel and dashboard titles
5. **Appropriate Visualizations**: Choose right chart types

### Query Best Practices
1. **Use Recording Rules**: Pre-calculate common queries
2. **Efficient Selectors**: Use specific label selectors
3. **Appropriate Ranges**: Use suitable time ranges
4. **Avoid Subqueries**: Minimize complex subqueries
5. **Test Performance**: Validate query performance

### Maintenance Best Practices
1. **Regular Review**: Review and update dashboards monthly
2. **Version Control**: Store dashboards in Git
3. **Documentation**: Document dashboard purpose and usage
4. **Testing**: Test dashboards after updates
5. **Backup**: Regular dashboard backups

## Integration with Alerting

### Alert Annotations
```json
{
  "annotations": {
    "list": [
      {
        "builtIn": 1,
        "datasource": "-- Grafana --",
        "enable": true,
        "hide": true,
        "iconColor": "rgba(0, 211, 255, 1)",
        "name": "Annotations & Alerts",
        "type": "dashboard"
      }
    ]
  }
}
```

### Alert Status Panels
```json
{
  "type": "stat",
  "title": "Active Alerts",
  "targets": [
    {
      "expr": "ALERTS{alertstate=\"firing\"}",
      "legendFormat": "Active Alerts"
    }
  ],
  "fieldConfig": {
    "defaults": {
      "color": {
        "mode": "thresholds"
      },
      "thresholds": {
        "steps": [
          {
            "color": "green",
            "value": null
          },
          {
            "color": "yellow",
            "value": 1
          },
          {
            "color": "red",
            "value": 5
          }
        ]
      }
    }
  }
}
```

## Mobile Access

### Mobile-Optimized Dashboards
1. **Responsive Design**: Dashboards adapt to mobile screens
2. **Touch-Friendly**: Large buttons and touch targets
3. **Simplified Layout**: Fewer panels for mobile viewing
4. **Fast Loading**: Optimized for mobile networks

### Mobile Apps
- **Grafana Mobile App**: Official mobile application
- **Web Browser**: Mobile-optimized web interface
- **Custom Apps**: Build custom mobile interfaces

---

**Last Updated**: July 18, 2025
**Next Review**: August 18, 2025
**Document Owner**: Query Intelligence Team