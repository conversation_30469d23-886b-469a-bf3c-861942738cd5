# Query Intelligence Alerting Guide

## Overview

This guide provides comprehensive alerting configuration and management procedures for the Query Intelligence service, including alert definitions, escalation procedures, and response playbooks.

## Alert Categories

### Critical Alerts (Severity 1)
**Response Time**: <5 minutes  
**Escalation**: On-call engineer + manager  
**Notification**: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, SMS

#### Service Down
- **Condition**: Health check failures >2 minutes
- **Impact**: Complete service unavailability
- **Response**: Immediate investigation and mitigation

#### High Error Rate
- **Condition**: >5% error rate >5 minutes
- **Impact**: Significant user impact
- **Response**: Investigate root cause and implement fix

#### High Latency
- **Condition**: P95 >500ms >5 minutes
- **Impact**: Poor user experience
- **Response**: Check resources and dependencies

#### Memory Exhaustion
- **Condition**: >90% memory usage >5 minutes
- **Impact**: Service instability
- **Response**: Scale up or restart service

### Warning Alerts (Severity 2)
**Response Time**: <1 hour  
**Escalation**: On-call engineer  
**Notification**: <PERSON>lack, Email

#### Moderate Error Rate
- **Condition**: >1% error rate >10 minutes
- **Impact**: Some user impact
- **Response**: Monitor trends and investigate

#### Moderate Latency
- **Condition**: P95 >200ms >10 minutes
- **Impact**: Degraded performance
- **Response**: Check system resources

#### High Memory Usage
- **Condition**: >80% memory usage >15 minutes
- **Impact**: Potential performance issues
- **Response**: Monitor and plan scaling

#### Dependency Issues
- **Condition**: Any dependency unhealthy
- **Impact**: Reduced functionality
- **Response**: Contact dependency team

### Info Alerts (Severity 3)
**Response Time**: <4 hours  
**Escalation**: During business hours  
**Notification**: Slack

#### Scaling Events
- **Condition**: Auto-scaling activities
- **Impact**: Informational
- **Response**: Monitor scaling patterns

#### Performance Trends
- **Condition**: Performance degradation trends
- **Impact**: Potential future issues
- **Response**: Investigate and optimize

#### Cache Performance
- **Condition**: Cache hit rate below target
- **Impact**: Reduced performance
- **Response**: Optimize caching strategy

## Alert Configuration

### Prometheus Alert Rules
**Location**: `/services/query-intelligence/monitoring/alerting/prometheus-alerts.yml`

#### Service Down Alert
```yaml
groups:
- name: query-intelligence-critical
  rules:
  - alert: ServiceDown
    expr: up{job="query-intelligence"} == 0
    for: 2m
    labels:
      severity: critical
      service: query-intelligence
    annotations:
      summary: "Query Intelligence service is down"
      description: "Service has been down for more than 2 minutes"
      runbook_url: "https://docs.episteme.com/runbooks/service-down"
```

#### High Error Rate Alert
```yaml
  - alert: HighErrorRate
    expr: |
      (
        rate(http_requests_total{job="query-intelligence", code=~"5.."}[5m]) /
        rate(http_requests_total{job="query-intelligence"}[5m])
      ) * 100 > 5
    for: 5m
    labels:
      severity: critical
      service: query-intelligence
    annotations:
      summary: "High error rate detected"
      description: "Error rate is {{ $value }}% for more than 5 minutes"
```

#### High Latency Alert
```yaml
  - alert: HighLatency
    expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job="query-intelligence"}[5m])) > 0.5
    for: 5m
    labels:
      severity: critical
      service: query-intelligence
    annotations:
      summary: "High latency detected"
      description: "95th percentile latency is {{ $value }}s"
```

### Alertmanager Configuration
**Location**: `/services/query-intelligence/monitoring/alerting/alertmanager-config.yml`

#### Routing Configuration
```yaml
route:
  group_by: ['alertname', 'severity']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'default'
  routes:
  - match:
      severity: critical
    receiver: 'critical-alerts'
  - match:
      severity: warning
    receiver: 'warning-alerts'
  - match:
      severity: info
    receiver: 'info-alerts'
```

#### Receivers Configuration
```yaml
receivers:
- name: 'default'
  slack_configs:
  - api_url: 'https://hooks.slack.com/services/...'
    channel: '#alerts'
    
- name: 'critical-alerts'
  slack_configs:
  - api_url: 'https://hooks.slack.com/services/...'
    channel: '#critical-alerts'
    title: "🚨 CRITICAL: {{ .GroupLabels.alertname }}"
  pagerduty_configs:
  - routing_key: 'YOUR_PAGERDUTY_ROUTING_KEY'
    description: "Critical alert: {{ .GroupLabels.alertname }}"
    
- name: 'warning-alerts'
  slack_configs:
  - api_url: 'https://hooks.slack.com/services/...'
    channel: '#warnings'
    title: "⚠️ WARNING: {{ .GroupLabels.alertname }}"
  email_configs:
  - to: '<EMAIL>'
    subject: "Warning: {{ .GroupLabels.alertname }}"
```

## Escalation Procedures

### Escalation Matrix
| Severity | Initial Response | Escalation 1 | Escalation 2 | Escalation 3 |
|----------|------------------|--------------|--------------|--------------|
| Critical | On-call engineer | Senior engineer | Manager | Director |
| Warning | On-call engineer | Senior engineer | Manager | - |
| Info | On-call engineer | - | - | - |

### Contact Information
- **On-call Engineer**: @oncall-query-intelligence (Slack)
- **Senior Engineer**: @senior-query-intelligence (Slack)
- **Manager**: @manager-query-intelligence (Slack)
- **Director**: <EMAIL>

### Escalation Timeframes
- **Critical**: Escalate after 15 minutes if no response
- **Warning**: Escalate after 1 hour if no response
- **Info**: Escalate during business hours if needed

## Alert Response Procedures

### Critical Alert Response

#### Service Down Response
1. **Acknowledge Alert** (30 seconds)
   ```bash
   curl -X POST http://localhost:9093/api/v1/alerts/acknowledge \
     -d '{"alertId":"service-down-alert"}'
   ```

2. **Check Service Health** (1 minute)
   ```bash
   curl https://query-intelligence-l3nxty7oka-uc.a.run.app/health
   curl https://query-intelligence-l3nxty7oka-uc.a.run.app/ready
   ```

3. **Check Recent Logs** (2 minutes)
   ```bash
   gcloud logs read 'resource.type="cloud_run_revision" 
     AND resource.labels.service_name="query-intelligence"' \
     --limit=50
   ```

4. **Immediate Mitigation** (5 minutes)
   ```bash
   # Scale up instances
   gcloud run services update query-intelligence \
     --min-instances=10 \
     --region=us-central1
   
   # Or restart service
   gcloud run services update query-intelligence \
     --set-env-vars="RESTART_TIMESTAMP=$(date +%s)" \
     --region=us-central1
   ```

#### High Error Rate Response
1. **Acknowledge Alert** (30 seconds)
2. **Check Error Types** (2 minutes)
   ```bash
   gcloud logs read 'resource.type="cloud_run_revision" 
     AND resource.labels.service_name="query-intelligence" 
     AND severity>=ERROR' \
     --limit=20
   ```

3. **Check Dependencies** (3 minutes)
   ```bash
   curl https://analysis-engine-572735000332.us-central1.run.app/health
   redis-cli -h [REDIS_HOST] ping
   ```

4. **Implement Fix** (10 minutes)
   - Enable fallback mode if needed
   - Rollback to previous version if regression
   - Fix configuration issues

### Warning Alert Response

#### Moderate Latency Response
1. **Acknowledge Alert** (5 minutes)
2. **Check Resource Usage** (5 minutes)
   ```bash
   gcloud monitoring time-series list \
     --filter='metric.type="run.googleapis.com/container/memory/utilizations"'
   ```

3. **Analyze Trends** (10 minutes)
   ```bash
   gcloud monitoring time-series list \
     --filter='metric.type="run.googleapis.com/request_latencies"' \
     --interval.end-time=$(date -Iseconds) \
     --interval.start-time=$(date -d '1 hour ago' -Iseconds)
   ```

4. **Take Action** (30 minutes)
   - Increase resources if needed
   - Optimize queries if possible
   - Schedule maintenance if required

## Alert Testing

### Test Alert Generation
```bash
# Generate test alert
kubectl apply -f - <<EOF
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: test-alerts
  namespace: monitoring
spec:
  groups:
  - name: test
    rules:
    - alert: TestAlert
      expr: up == 1
      labels:
        severity: warning
      annotations:
        summary: "Test alert"
        description: "This is a test alert"
EOF
```

### Verify Alert Routing
```bash
# Check alerts in Alertmanager
curl -s http://localhost:9093/api/v1/alerts

# Check notifications
# - Verify Slack notifications
# - Check email delivery
# - Confirm PagerDuty integration
```

## Alert Tuning

### Threshold Optimization
1. **Analyze Historical Data**
   ```bash
   # Check false positive rate
   gcloud monitoring time-series list \
     --filter='metric.type="run.googleapis.com/request_latencies"' \
     --interval.end-time=$(date -Iseconds) \
     --interval.start-time=$(date -d '30 days ago' -Iseconds)
   ```

2. **Adjust Thresholds**
   - Increase thresholds if too many false positives
   - Decrease thresholds if missing real issues
   - Consider time-based thresholds for different periods

3. **Update Alert Rules**
   ```bash
   # Apply updated alert rules
   kubectl apply -f monitoring/alerting/prometheus-alerts.yml
   ```

### Alert Noise Reduction
1. **Silence Recurring Alerts**
   ```bash
   # Create silence for maintenance
   curl -X POST http://localhost:9093/api/v1/silences \
     -d '{"matchers":[{"name":"alertname","value":"MaintenanceAlert"}],"startsAt":"2025-07-18T10:00:00Z","endsAt":"2025-07-18T12:00:00Z"}'
   ```

2. **Aggregate Similar Alerts**
   - Group related alerts by service
   - Use alert inhibition rules
   - Implement alert dependencies

## Alert Documentation

### Runbook Templates
Each alert should have a corresponding runbook with:
- **Symptom**: What the alert indicates
- **Impact**: Effect on users and service
- **Diagnosis**: How to investigate the issue
- **Mitigation**: Steps to resolve the problem
- **Prevention**: How to prevent recurrence

### Example Runbook Entry
```markdown
## High Memory Usage Alert

### Symptom
Memory usage > 80% for 15 minutes

### Impact
- Potential performance degradation
- Risk of out-of-memory errors
- Possible service instability

### Diagnosis
1. Check memory usage trends
2. Identify memory-intensive processes
3. Review recent changes

### Mitigation
1. Scale up service instances
2. Restart service if memory leak suspected
3. Optimize memory usage

### Prevention
1. Implement memory monitoring
2. Regular memory profiling
3. Optimize application memory usage
```

## Alert Metrics and Reporting

### Alert Metrics
- **Mean Time to Detection (MTTD)**: Time from issue to alert
- **Mean Time to Acknowledgment (MTTA)**: Time from alert to acknowledgment
- **Mean Time to Resolution (MTTR)**: Time from alert to resolution
- **False Positive Rate**: Percentage of alerts that are false positives

### Monthly Alert Report
```bash
# Generate monthly alert report
curl -s http://localhost:9093/api/v1/alerts/groups | \
  jq '.data[] | .alerts[] | {alertname: .labels.alertname, severity: .labels.severity, state: .status.state}'
```

## Integration with Monitoring

### Grafana Integration
- **Alert Visualization**: Dashboard showing active alerts
- **Alert History**: Historical alert data
- **Alert Correlation**: Correlate alerts with metrics

### Prometheus Integration
- **Recording Rules**: Pre-calculate alert conditions
- **Alert Federation**: Federate alerts across clusters
- **Alert Metrics**: Expose alert metrics for monitoring

## Best Practices

### Alert Design
1. **Clear Naming**: Use descriptive alert names
2. **Appropriate Severity**: Match severity to impact
3. **Actionable Alerts**: Every alert should have a clear action
4. **Avoid Alert Fatigue**: Don't over-alert

### Alert Management
1. **Regular Review**: Review and tune alerts monthly
2. **Documentation**: Keep runbooks updated
3. **Testing**: Test alert routes regularly
4. **Metrics**: Track alert effectiveness

### Incident Response
1. **Quick Acknowledgment**: Acknowledge alerts quickly
2. **Clear Communication**: Keep stakeholders informed
3. **Root Cause Analysis**: Investigate and document issues
4. **Continuous Improvement**: Learn from incidents

---

**Last Updated**: July 18, 2025
**Next Review**: August 18, 2025
**Document Owner**: Query Intelligence Team