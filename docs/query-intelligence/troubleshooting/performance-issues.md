# Performance Issues Troubleshooting

**🔗 Navigation**: [← Troubleshooting Guide](README.md) | [↑ Query Intelligence](../README.md) | [↗ Performance Documentation](../performance/README.md)

---

## Performance Issues Troubleshooting

This guide covers common performance issues and their solutions for the Query Intelligence service.

### High Response Times

#### Symptoms
- Response times >500ms consistently
- Increased p95/p99 latency
- Client timeouts

#### Diagnosis
```bash
# Check current metrics
curl https://query-intelligence-l3nxty7oka-uc.a.run.app/metrics | grep response_time

# Check Redis connection
redis-cli -h your-redis-host ping

# Check analysis engine health
curl http://analysis-engine:8001/health
```

#### Solutions

1. **Check Redis Cache**
   ```bash
   # Check cache hit rate
   redis-cli info stats | grep keyspace_hits
   
   # Clear cache if needed
   redis-cli flushdb
   ```

2. **Optimize Query Processing**
   ```python
   # Increase concurrent processing
   MAX_CONCURRENT_QUERIES=100
   
   # Adjust timeout settings
   QUERY_TIMEOUT=60
   ```

3. **Scale Resources**
   ```bash
   # Increase Cloud Run instances
   gcloud run services update query-intelligence \
     --min-instances=10 \
     --max-instances=100
   ```

### Memory Issues

#### Symptoms
- Out of memory errors
- High memory usage >80%
- Service restarts

#### Diagnosis
```bash
# Check memory usage
docker stats query-intelligence

# Check memory metrics
curl https://query-intelligence-l3nxty7oka-uc.a.run.app/metrics | grep memory
```

#### Solutions

1. **Increase Memory Limits**
   ```yaml
   # Cloud Run configuration
   resources:
     limits:
       memory: "4Gi"
   ```

2. **Optimize Cache Usage**
   ```python
   # Reduce cache TTL
   CACHE_TTL=1800  # 30 minutes
   
   # Limit cache size
   REDIS_MAXMEMORY=1gb
   ```

### High CPU Usage

#### Symptoms
- CPU usage >80%
- Slow processing
- Request queuing

#### Diagnosis
```bash
# Check CPU metrics
top -p $(pgrep -f query-intelligence)

# Check processing queue
curl https://query-intelligence-l3nxty7oka-uc.a.run.app/metrics | grep queue
```

#### Solutions

1. **Scale Horizontally**
   ```bash
   # Increase instances
   gcloud run services update query-intelligence \
     --max-instances=50
   ```

2. **Optimize Processing**
   ```python
   # Reduce model complexity
   GEMINI_MODEL_NAME=gemini-2.5-flash-lite
   
   # Limit token usage
   MAX_TOKENS=4096
   ```

---

## Related Documentation

- **[Performance Monitoring](../performance/performance-monitoring.md)** - Comprehensive monitoring setup
- **[Performance Optimization](../performance/performance-optimization.md)** - Optimization strategies
- **[Operations Runbook](../operations/runbook.md)** - Operational procedures
- **[Configuration Reference](../deployment/configuration-reference.md)** - Complete configuration options

---

**🏠 [Query Intelligence Home](../README.md)** | **📚 [All Documentation](../README.md#documentation)**