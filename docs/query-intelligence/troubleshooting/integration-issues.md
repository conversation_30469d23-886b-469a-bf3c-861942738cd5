# Integration Issues Troubleshooting

**🔗 Navigation**: [← Troubleshooting Guide](README.md) | [↑ Query Intelligence](../README.md) | [↗ Integration Guide](../guides/integration-guide.md)

---

## Integration Issues Troubleshooting

This guide covers common integration issues and their solutions when working with the Query Intelligence service.

### Authentication Issues

#### Invalid JWT Token

**Symptoms:**
- 401 Unauthorized responses
- "Invalid token" error messages
- Authentication failures

**Diagnosis:**
```bash
# Test token validity
curl -H "Authorization: Bearer YOUR_TOKEN" \
  https://query-intelligence-l3nxty7oka-uc.a.run.app/api/v1/query

# Decode JWT token (without verification)
echo "YOUR_TOKEN" | base64 -d
```

**Solutions:**
1. **Verify Token Format**
   ```python
   import jwt
   
   try:
       decoded = jwt.decode(token, options={"verify_signature": False})
       print(f"Token expires at: {decoded.get('exp')}")
   except jwt.InvalidTokenError as e:
       print(f"Invalid token: {e}")
   ```

2. **Check Token Expiration**
   ```python
   import time
   import jwt
   
   decoded = jwt.decode(token, options={"verify_signature": False})
   if decoded.get('exp', 0) < time.time():
       print("Token expired")
   ```

#### CORS Issues

**Symptoms:**
- Browser console errors about CORS
- OPTIONS requests failing
- Cross-origin request blocked

**Solutions:**
1. **Configure CORS Origins**
   ```bash
   # Set allowed origins
   CORS_ORIGINS='["https://your-frontend.com", "http://localhost:3000"]'
   ```

2. **Verify CORS Headers**
   ```bash
   curl -H "Origin: https://your-frontend.com" \
     -H "Access-Control-Request-Method: POST" \
     -H "Access-Control-Request-Headers: authorization,content-type" \
     -X OPTIONS \
     https://query-intelligence-l3nxty7oka-uc.a.run.app/api/v1/query
   ```

### Connection Issues

#### Service Unavailable

**Symptoms:**
- 503 Service Unavailable errors
- Connection timeouts
- DNS resolution failures

**Diagnosis:**
```bash
# Check service health
curl https://query-intelligence-l3nxty7oka-uc.a.run.app/health

# Check DNS resolution
nslookup query-intelligence-l3nxty7oka-uc.a.run.app

# Test connectivity
telnet query-intelligence-l3nxty7oka-uc.a.run.app 443
```

**Solutions:**
1. **Verify Service Status**
   ```bash
   # Check Cloud Run service
   gcloud run services describe query-intelligence \
     --region us-central1
   ```

2. **Check Network Configuration**
   ```bash
   # Verify firewall rules
   gcloud compute firewall-rules list
   ```

#### WebSocket Connection Issues

**Symptoms:**
- WebSocket connection refused
- Immediate disconnections
- Authentication failures on WebSocket

**Solutions:**
1. **Verify WebSocket URL**
   ```javascript
   // Correct WebSocket URL format
   const ws = new WebSocket(
     'wss://query-intelligence-l3nxty7oka-uc.a.run.app/api/v1/ws/query'
   );
   ```

2. **Add Authentication Headers**
   ```javascript
   // For Node.js WebSocket client
   const WebSocket = require('ws');
   
   const ws = new WebSocket(
     'wss://query-intelligence-l3nxty7oka-uc.a.run.app/api/v1/ws/query',
     {
       headers: {
         'Authorization': 'Bearer YOUR_JWT_TOKEN'
       }
     }
   );
   ```

### API Integration Issues

#### Rate Limiting

**Symptoms:**
- 429 Too Many Requests errors
- Requests being rejected
- Rate limit headers in response

**Solutions:**
1. **Implement Retry Logic**
   ```python
   import time
   import random
   
   def retry_with_backoff(func, max_retries=3):
       for attempt in range(max_retries):
           try:
               return func()
           except RateLimitError:
               if attempt == max_retries - 1:
                   raise
               wait_time = (2 ** attempt) + random.uniform(0, 1)
               time.sleep(wait_time)
   ```

2. **Check Rate Limit Headers**
   ```python
   response = requests.post(url, headers=headers, json=data)
   
   print(f"Rate limit: {response.headers.get('X-RateLimit-Limit')}")
   print(f"Remaining: {response.headers.get('X-RateLimit-Remaining')}")
   print(f"Reset: {response.headers.get('X-RateLimit-Reset')}")
   ```

#### Request/Response Format Issues

**Symptoms:**
- 400 Bad Request errors
- JSON parsing errors
- Schema validation failures

**Solutions:**
1. **Validate Request Format**
   ```python
   # Correct request format
   payload = {
       "query": "Your query here",
       "repository_id": "repo-123",
       "include_context": True
   }
   
   # Validate required fields
   required_fields = ["query", "repository_id"]
   for field in required_fields:
       if field not in payload:
           raise ValueError(f"Missing required field: {field}")
   ```

2. **Handle Response Errors**
   ```python
   try:
       response = requests.post(url, json=payload)
       response.raise_for_status()
       data = response.json()
   except requests.exceptions.HTTPError as e:
       print(f"HTTP error: {e}")
       print(f"Response: {response.text}")
   except ValueError as e:
       print(f"JSON decode error: {e}")
   ```

### External Service Integration

#### Analysis Engine Connection Issues

**Symptoms:**
- Analysis engine unavailable
- Code analysis failures
- Context retrieval errors

**Solutions:**
1. **Check Analysis Engine Health**
   ```bash
   curl http://analysis-engine:8001/health
   ```

2. **Configure Fallback Behavior**
   ```python
   # Graceful degradation
   try:
       analysis_result = await analysis_engine.analyze(code)
   except ServiceUnavailableError:
       # Fallback to basic processing
       analysis_result = await basic_analysis(code)
   ```

#### Redis Connection Issues

**Symptoms:**
- Cache misses
- Redis connection errors
- Performance degradation

**Solutions:**
1. **Test Redis Connection**
   ```bash
   redis-cli -h your-redis-host ping
   ```

2. **Implement Connection Pooling**
   ```python
   import redis
   from redis.connection import ConnectionPool
   
   pool = ConnectionPool(
       host='your-redis-host',
       port=6379,
       db=0,
       max_connections=20,
       retry_on_timeout=True
   )
   
   redis_client = redis.Redis(connection_pool=pool)
   ```

### Environment-Specific Issues

#### Development Environment

**Common Issues:**
- Missing environment variables
- Service discovery problems
- Local service dependencies

**Solutions:**
1. **Environment Setup**
   ```bash
   # Copy and configure .env file
   cp .env.example .env
   
   # Set required variables
   export GOOGLE_API_KEY=your-api-key
   export GCP_PROJECT_ID=your-project-id
   ```

2. **Local Service Dependencies**
   ```bash
   # Start dependencies with Docker Compose
   docker-compose up -d redis analysis-engine
   ```

#### Production Environment

**Common Issues:**
- Service account permissions
- Network connectivity
- Resource limits

**Solutions:**
1. **Service Account Setup**
   ```bash
   # Create service account
   gcloud iam service-accounts create query-intelligence-sa
   
   # Grant necessary permissions
   gcloud projects add-iam-policy-binding PROJECT_ID \
     --member="serviceAccount:query-intelligence-sa@PROJECT_ID.iam.gserviceaccount.com" \
     --role="roles/aiplatform.user"
   ```

2. **Network Configuration**
   ```bash
   # Configure VPC connector
   gcloud compute networks vpc-access connectors create query-intelligence-connector \
     --network=default \
     --region=us-central1 \
     --range=********/28
   ```

---

## Related Documentation

- **[Integration Guide](../guides/integration-guide.md)** - Complete integration patterns and best practices
- **[API Reference](../api/README.md)** - Complete API documentation
- **[Developer Guide](../guides/developer-guide.md)** - Local development setup
- **[Operations Runbook](../operations/runbook.md)** - Operational procedures and monitoring

---

**🏠 [Query Intelligence Home](../README.md)** | **📚 [All Documentation](../README.md#documentation)**