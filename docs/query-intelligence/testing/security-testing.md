# Security Testing

**🔗 Navigation**: [← Testing Strategy](strategy.md) | [↑ Query Intelligence](../README.md) | [↗ Architecture Security](../architecture/README.md)

---

## Security Testing

This document outlines the security testing approach, procedures, and validation criteria for the Query Intelligence service.

### Security Testing Scope

| Category | Components | Testing Approach |
|----------|------------|------------------|
| **Authentication** | JWT validation, token lifecycle | Automated + manual testing |
| **Authorization** | Role-based access, resource permissions | Automated test suite |
| **Input Validation** | Query sanitization, injection prevention | Fuzzing + boundary testing |
| **Rate Limiting** | API throttling, DDoS protection | Load testing with rate limits |
| **Data Protection** | Encryption, PII handling | Compliance validation |
| **Network Security** | HTTPS, certificate validation | Security scanning |

### Authentication Security Testing

#### JWT Token Validation

```python
# test_auth_security.py
import jwt
import time
import pytest
import requests
from datetime import datetime, timedelta

class TestJWTSecurity:
    
    def test_expired_token_rejection(self):
        """Test that expired tokens are rejected"""
        # Create expired token
        payload = {
            'sub': 'test-user',
            'iat': time.time() - 7200,  # 2 hours ago
            'exp': time.time() - 3600   # 1 hour ago (expired)
        }
        expired_token = jwt.encode(payload, 'secret-key', algorithm='HS256')
        
        response = requests.post(
            'https://query-intelligence-l3nxty7oka-uc.a.run.app/api/v1/query',
            headers={'Authorization': f'Bearer {expired_token}'},
            json={'query': 'test', 'repository_id': 'test'}
        )
        
        assert response.status_code == 401
        assert 'expired' in response.json().get('detail', '').lower()
    
    def test_malformed_token_rejection(self):
        """Test that malformed tokens are rejected"""
        malformed_tokens = [
            'invalid-token',
            'Bearer invalid-token',
            'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.invalid',
            ''
        ]
        
        for token in malformed_tokens:
            response = requests.post(
                'https://query-intelligence-l3nxty7oka-uc.a.run.app/api/v1/query',
                headers={'Authorization': f'Bearer {token}'},
                json={'query': 'test', 'repository_id': 'test'}
            )
            
            assert response.status_code == 401
    
    def test_token_signature_validation(self):
        """Test that tokens with invalid signatures are rejected"""
        # Create token with wrong signature
        payload = {
            'sub': 'test-user',
            'iat': time.time(),
            'exp': time.time() + 3600
        }
        invalid_token = jwt.encode(payload, 'wrong-secret', algorithm='HS256')
        
        response = requests.post(
            'https://query-intelligence-l3nxty7oka-uc.a.run.app/api/v1/query',
            headers={'Authorization': f'Bearer {invalid_token}'},
            json={'query': 'test', 'repository_id': 'test'}
        )
        
        assert response.status_code == 401
    
    def test_token_algorithm_confusion(self):
        """Test protection against algorithm confusion attacks"""
        # Try to use 'none' algorithm
        payload = {
            'sub': 'test-user',
            'iat': time.time(),
            'exp': time.time() + 3600
        }
        
        # Create token without signature
        header = {'alg': 'none', 'typ': 'JWT'}
        token_parts = [
            jwt.utils.base64url_encode(json.dumps(header).encode()),
            jwt.utils.base64url_encode(json.dumps(payload).encode()),
            ''
        ]
        none_token = '.'.join(token_parts)
        
        response = requests.post(
            'https://query-intelligence-l3nxty7oka-uc.a.run.app/api/v1/query',
            headers={'Authorization': f'Bearer {none_token}'},
            json={'query': 'test', 'repository_id': 'test'}
        )
        
        assert response.status_code == 401
```

### Input Validation Security Testing

#### SQL Injection Prevention

```python
# test_input_validation.py
import pytest
import requests

class TestInputValidation:
    
    def test_sql_injection_prevention(self):
        """Test protection against SQL injection"""
        sql_injection_payloads = [
            "'; DROP TABLE users; --",
            "1' OR '1'='1",
            "admin'; UPDATE users SET password='hacked' WHERE username='admin'; --",
            "1' UNION SELECT * FROM users--"
        ]
        
        for payload in sql_injection_payloads:
            response = requests.post(
                'https://query-intelligence-l3nxty7oka-uc.a.run.app/api/v1/query',
                headers={'Authorization': 'Bearer valid-token'},
                json={
                    'query': payload,
                    'repository_id': 'test-repo'
                }
            )
            
            # Should not return 500 error (indicating SQL injection)
            assert response.status_code != 500
            
            # Should sanitize the input
            if response.status_code == 200:
                result = response.json()
                assert 'DROP TABLE' not in result.get('response', '')
                assert 'UNION SELECT' not in result.get('response', '')
    
    def test_xss_prevention(self):
        """Test protection against XSS attacks"""
        xss_payloads = [
            "<script>alert('XSS')</script>",
            "<img src=x onerror=alert('XSS')>",
            "javascript:alert('XSS')",
            "<svg onload=alert('XSS')>"
        ]
        
        for payload in xss_payloads:
            response = requests.post(
                'https://query-intelligence-l3nxty7oka-uc.a.run.app/api/v1/query',
                headers={'Authorization': 'Bearer valid-token'},
                json={
                    'query': payload,
                    'repository_id': 'test-repo'
                }
            )
            
            if response.status_code == 200:
                result = response.json()
                response_text = result.get('response', '')
                
                # Check that HTML tags are escaped or removed
                assert '<script>' not in response_text
                assert '<img' not in response_text
                assert 'javascript:' not in response_text
                assert '<svg' not in response_text
    
    def test_command_injection_prevention(self):
        """Test protection against command injection"""
        command_injection_payloads = [
            "; ls -la",
            "| cat /etc/passwd",
            "&& rm -rf /",
            "`whoami`",
            "$(id)"
        ]
        
        for payload in command_injection_payloads:
            response = requests.post(
                'https://query-intelligence-l3nxty7oka-uc.a.run.app/api/v1/query',
                headers={'Authorization': 'Bearer valid-token'},
                json={
                    'query': f"Show me the code {payload}",
                    'repository_id': 'test-repo'
                }
            )
            
            # Should not execute system commands
            assert response.status_code != 500
            
            if response.status_code == 200:
                result = response.json()
                response_text = result.get('response', '')
                
                # Should not contain command output
                assert 'root:' not in response_text  # /etc/passwd content
                assert 'uid=' not in response_text   # id command output
    
    def test_path_traversal_prevention(self):
        """Test protection against path traversal attacks"""
        path_traversal_payloads = [
            "../../../etc/passwd",
            "..\\..\\..\\windows\\system32\\config\\sam",
            "/etc/shadow",
            "C:\\Windows\\System32\\drivers\\etc\\hosts"
        ]
        
        for payload in path_traversal_payloads:
            response = requests.post(
                'https://query-intelligence-l3nxty7oka-uc.a.run.app/api/v1/query',
                headers={'Authorization': 'Bearer valid-token'},
                json={
                    'query': f"Show me the file {payload}",
                    'repository_id': 'test-repo'
                }
            )
            
            if response.status_code == 200:
                result = response.json()
                response_text = result.get('response', '')
                
                # Should not access system files
                assert 'root:x:0:0:root:/root:/bin/bash' not in response_text
                assert 'password for this user' not in response_text
```

### Rate Limiting Security Testing

```python
# test_rate_limiting.py
import time
import asyncio
import aiohttp
import pytest

class TestRateLimiting:
    
    def test_per_user_rate_limiting(self):
        """Test per-user rate limiting"""
        # Send requests rapidly
        responses = []
        
        for i in range(100):  # Send 100 requests
            response = requests.post(
                'https://query-intelligence-l3nxty7oka-uc.a.run.app/api/v1/query',
                headers={'Authorization': 'Bearer test-token'},
                json={'query': f'test query {i}', 'repository_id': 'test'}
            )
            responses.append(response.status_code)
        
        # Should see 429 (Too Many Requests) responses
        rate_limited_responses = [r for r in responses if r == 429]
        assert len(rate_limited_responses) > 0
    
    def test_global_rate_limiting(self):
        """Test global rate limiting under high load"""
        async def send_request(session, user_id):
            headers = {
                'Authorization': f'Bearer test-token-{user_id}',
                'Content-Type': 'application/json'
            }
            
            async with session.post(
                'https://query-intelligence-l3nxty7oka-uc.a.run.app/api/v1/query',
                headers=headers,
                json={'query': f'test from user {user_id}', 'repository_id': 'test'}
            ) as response:
                return response.status
        
        async def flood_test():
            async with aiohttp.ClientSession() as session:
                tasks = []
                
                # Create 1000 requests from different users
                for i in range(1000):
                    task = asyncio.create_task(send_request(session, i % 100))
                    tasks.append(task)
                
                results = await asyncio.gather(*tasks, return_exceptions=True)
                return results
        
        results = asyncio.run(flood_test())
        
        # Should see some rate limiting (429 responses)
        rate_limited = [r for r in results if r == 429]
        assert len(rate_limited) > 0
    
    def test_rate_limit_headers(self):
        """Test rate limiting headers are present"""
        response = requests.post(
            'https://query-intelligence-l3nxty7oka-uc.a.run.app/api/v1/query',
            headers={'Authorization': 'Bearer test-token'},
            json={'query': 'test', 'repository_id': 'test'}
        )
        
        # Check for rate limiting headers
        assert 'X-RateLimit-Limit' in response.headers
        assert 'X-RateLimit-Remaining' in response.headers
        assert 'X-RateLimit-Reset' in response.headers
```

### Data Protection Testing

```python
# test_data_protection.py
import pytest
import requests
import re

class TestDataProtection:
    
    def test_pii_detection_and_redaction(self):
        """Test PII detection and redaction"""
        pii_test_cases = [
            {
                'query': 'My <NAME_EMAIL> and my phone is ************',
                'should_redact': ['<EMAIL>', '************']
            },
            {
                'query': 'My SSN is *********** and credit card is 4111-1111-1111-1111',
                'should_redact': ['***********', '4111-1111-1111-1111']
            },
            {
                'query': 'IP address ************* and API key sk-1234567890abcdef',
                'should_redact': ['sk-1234567890abcdef']
            }
        ]
        
        for test_case in pii_test_cases:
            response = requests.post(
                'https://query-intelligence-l3nxty7oka-uc.a.run.app/api/v1/query',
                headers={'Authorization': 'Bearer test-token'},
                json={'query': test_case['query'], 'repository_id': 'test'}
            )
            
            if response.status_code == 200:
                result = response.json()
                response_text = result.get('response', '')
                
                # Check that PII is redacted
                for pii in test_case['should_redact']:
                    assert pii not in response_text
                    assert '[REDACTED]' in response_text or '*' in response_text
    
    def test_sensitive_data_logging(self):
        """Test that sensitive data is not logged"""
        # This would typically involve checking log files
        # For now, we'll test that the response doesn't contain debug info
        
        response = requests.post(
            'https://query-intelligence-l3nxty7oka-uc.a.run.app/api/v1/query',
            headers={'Authorization': 'Bearer test-token'},
            json={
                'query': 'Test query with sensitive data: password123',
                'repository_id': 'test'
            }
        )
        
        # Response should not contain debug information
        assert response.status_code != 500
        
        if response.status_code == 200:
            result = response.json()
            
            # Should not contain internal debug info
            assert 'traceback' not in str(result).lower()
            assert 'internal_error' not in str(result).lower()
            assert 'debug' not in str(result).lower()
    
    def test_data_encryption_in_transit(self):
        """Test that data is encrypted in transit"""
        # Verify HTTPS is enforced
        http_response = requests.post(
            'http://query-intelligence-l3nxty7oka-uc.a.run.app/api/v1/query',
            headers={'Authorization': 'Bearer test-token'},
            json={'query': 'test', 'repository_id': 'test'},
            allow_redirects=False
        )
        
        # Should redirect to HTTPS
        assert http_response.status_code in [301, 302, 308]
        assert 'https://' in http_response.headers.get('location', '')
```

### Security Headers Testing

```python
# test_security_headers.py
import pytest
import requests

class TestSecurityHeaders:
    
    def test_security_headers_present(self):
        """Test that security headers are present"""
        response = requests.get('https://query-intelligence-l3nxty7oka-uc.a.run.app/health')
        
        # Check for security headers
        security_headers = {
            'X-Content-Type-Options': 'nosniff',
            'X-Frame-Options': 'DENY',
            'X-XSS-Protection': '1; mode=block',
            'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
            'Referrer-Policy': 'strict-origin-when-cross-origin'
        }
        
        for header, expected_value in security_headers.items():
            assert header in response.headers
            if expected_value:
                assert expected_value in response.headers[header]
    
    def test_csp_header(self):
        """Test Content Security Policy header"""
        response = requests.get('https://query-intelligence-l3nxty7oka-uc.a.run.app/health')
        
        if 'Content-Security-Policy' in response.headers:
            csp = response.headers['Content-Security-Policy']
            
            # Should not allow unsafe inline scripts
            assert 'unsafe-inline' not in csp
            assert 'unsafe-eval' not in csp
            
            # Should have restrictive default-src
            assert 'default-src' in csp
    
    def test_cors_headers(self):
        """Test CORS headers are properly configured"""
        response = requests.options(
            'https://query-intelligence-l3nxty7oka-uc.a.run.app/api/v1/query',
            headers={
                'Origin': 'https://malicious-site.com',
                'Access-Control-Request-Method': 'POST'
            }
        )
        
        # Should not allow arbitrary origins
        allowed_origins = response.headers.get('Access-Control-Allow-Origin', '')
        assert allowed_origins != '*'
        assert 'malicious-site.com' not in allowed_origins
```

### Penetration Testing

#### Automated Security Scanning

```python
# security_scanner.py
import requests
import json
import time
from urllib.parse import urljoin

class SecurityScanner:
    
    def __init__(self, base_url, token):
        self.base_url = base_url
        self.token = token
        self.vulnerabilities = []
    
    def scan_for_vulnerabilities(self):
        """Run automated security scans"""
        print("Starting security scan...")
        
        self.test_injection_vulnerabilities()
        self.test_authentication_bypasses()
        self.test_authorization_flaws()
        self.test_information_disclosure()
        
        return self.vulnerabilities
    
    def test_injection_vulnerabilities(self):
        """Test for various injection vulnerabilities"""
        injection_payloads = [
            # SQL Injection
            "'; DROP TABLE users; --",
            "1' OR '1'='1",
            
            # NoSQL Injection
            "'; return true; //",
            "1; return 1 == 1",
            
            # Command Injection
            "; cat /etc/passwd",
            "&& whoami",
            
            # LDAP Injection
            "*)(&(password=*))",
            "admin)(&(password=*))",
            
            # XML Injection
            "<?xml version='1.0'?><!DOCTYPE root [<!ENTITY test SYSTEM 'file:///etc/passwd'>]><root>&test;</root>",
        ]
        
        for payload in injection_payloads:
            try:
                response = requests.post(
                    f"{self.base_url}/api/v1/query",
                    headers={'Authorization': f'Bearer {self.token}'},
                    json={'query': payload, 'repository_id': 'test'},
                    timeout=10
                )
                
                if response.status_code == 500:
                    self.vulnerabilities.append({
                        'type': 'Injection Vulnerability',
                        'payload': payload,
                        'response_code': response.status_code,
                        'severity': 'High'
                    })
            except Exception as e:
                continue
    
    def test_authentication_bypasses(self):
        """Test for authentication bypass vulnerabilities"""
        bypass_attempts = [
            # No token
            None,
            # Empty token
            "",
            # Malformed tokens
            "invalid-token",
            "Bearer ",
            # Token with null bytes
            "Bearer token\x00admin",
        ]
        
        for token in bypass_attempts:
            headers = {}
            if token is not None:
                headers['Authorization'] = token
            
            try:
                response = requests.post(
                    f"{self.base_url}/api/v1/query",
                    headers=headers,
                    json={'query': 'test', 'repository_id': 'test'},
                    timeout=10
                )
                
                if response.status_code == 200:
                    self.vulnerabilities.append({
                        'type': 'Authentication Bypass',
                        'token': token,
                        'response_code': response.status_code,
                        'severity': 'Critical'
                    })
            except Exception as e:
                continue
    
    def test_authorization_flaws(self):
        """Test for authorization flaws"""
        # Test with different user tokens
        test_tokens = [
            f"Bearer user1-token",
            f"Bearer user2-token",
            f"Bearer admin-token",
            f"Bearer guest-token"
        ]
        
        for token in test_tokens:
            try:
                # Try to access admin endpoints
                response = requests.get(
                    f"{self.base_url}/api/v1/admin/users",
                    headers={'Authorization': token},
                    timeout=10
                )
                
                if response.status_code == 200:
                    self.vulnerabilities.append({
                        'type': 'Authorization Flaw',
                        'token': token,
                        'endpoint': '/api/v1/admin/users',
                        'severity': 'High'
                    })
            except Exception as e:
                continue
    
    def test_information_disclosure(self):
        """Test for information disclosure vulnerabilities"""
        disclosure_endpoints = [
            '/api/v1/config',
            '/api/v1/debug',
            '/api/v1/status',
            '/api/v1/logs',
            '/api/v1/users',
            '/api/v1/internal'
        ]
        
        for endpoint in disclosure_endpoints:
            try:
                response = requests.get(
                    f"{self.base_url}{endpoint}",
                    timeout=10
                )
                
                if response.status_code == 200:
                    response_text = response.text.lower()
                    
                    # Check for sensitive information
                    sensitive_keywords = [
                        'password', 'secret', 'key', 'token',
                        'database', 'connection', 'internal',
                        'debug', 'stack trace'
                    ]
                    
                    for keyword in sensitive_keywords:
                        if keyword in response_text:
                            self.vulnerabilities.append({
                                'type': 'Information Disclosure',
                                'endpoint': endpoint,
                                'keyword': keyword,
                                'severity': 'Medium'
                            })
                            break
            except Exception as e:
                continue
    
    def generate_report(self):
        """Generate security scan report"""
        report = {
            'scan_date': time.strftime('%Y-%m-%d %H:%M:%S'),
            'target': self.base_url,
            'total_vulnerabilities': len(self.vulnerabilities),
            'vulnerabilities_by_severity': {
                'Critical': len([v for v in self.vulnerabilities if v['severity'] == 'Critical']),
                'High': len([v for v in self.vulnerabilities if v['severity'] == 'High']),
                'Medium': len([v for v in self.vulnerabilities if v['severity'] == 'Medium']),
                'Low': len([v for v in self.vulnerabilities if v['severity'] == 'Low'])
            },
            'vulnerabilities': self.vulnerabilities
        }
        
        return json.dumps(report, indent=2)

# Usage
if __name__ == "__main__":
    scanner = SecurityScanner(
        'https://query-intelligence-l3nxty7oka-uc.a.run.app',
        'test-token'
    )
    
    vulnerabilities = scanner.scan_for_vulnerabilities()
    report = scanner.generate_report()
    
    print("Security Scan Report:")
    print(report)
```

### Security Test Automation

#### CI/CD Security Pipeline

```yaml
# .github/workflows/security-tests.yml
name: Security Tests
on:
  push:
    branches: [main]
  pull_request:
    branches: [main]
  schedule:
    - cron: '0 3 * * *'  # Daily at 3 AM

jobs:
  security-tests:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install security testing tools
      run: |
        pip install pytest requests aiohttp
        pip install bandit safety
    
    - name: Run static security analysis
      run: |
        bandit -r src/ -f json -o bandit-report.json
        safety check --json --output safety-report.json
    
    - name: Run security tests
      run: |
        pytest tests/security/ -v
      env:
        QUERY_INTELLIGENCE_URL: ${{ secrets.QUERY_INTELLIGENCE_URL }}
        JWT_TOKEN: ${{ secrets.JWT_TOKEN }}
    
    - name: Run vulnerability scanner
      run: |
        python security_scanner.py > security-scan-report.json
    
    - name: Upload security reports
      uses: actions/upload-artifact@v3
      with:
        name: security-reports
        path: |
          bandit-report.json
          safety-report.json
          security-scan-report.json
```

### Security Compliance Validation

```python
# test_compliance.py
import pytest
import requests
import ssl
import socket

class TestSecurityCompliance:
    
    def test_tls_version_compliance(self):
        """Test TLS version compliance"""
        # Test that only TLS 1.2+ is supported
        hostname = 'query-intelligence-l3nxty7oka-uc.a.run.app'
        
        # Create SSL context with TLS 1.1 (should fail)
        context = ssl.create_default_context()
        context.minimum_version = ssl.TLSVersion.TLSv1_1
        context.maximum_version = ssl.TLSVersion.TLSv1_1
        
        with pytest.raises(ssl.SSLError):
            with socket.create_connection((hostname, 443), timeout=10) as sock:
                with context.wrap_socket(sock, server_hostname=hostname) as ssock:
                    pass
    
    def test_cipher_suite_compliance(self):
        """Test cipher suite compliance"""
        hostname = 'query-intelligence-l3nxty7oka-uc.a.run.app'
        
        context = ssl.create_default_context()
        with socket.create_connection((hostname, 443), timeout=10) as sock:
            with context.wrap_socket(sock, server_hostname=hostname) as ssock:
                cipher = ssock.cipher()
                
                # Should use strong cipher suites
                assert cipher is not None
                assert 'AES' in cipher[0] or 'CHACHA20' in cipher[0]
                assert 'SHA256' in cipher[0] or 'SHA384' in cipher[0]
    
    def test_certificate_validation(self):
        """Test SSL certificate validation"""
        response = requests.get('https://query-intelligence-l3nxty7oka-uc.a.run.app/health')
        
        # Should not have certificate errors
        assert response.status_code == 200
        
        # Additional certificate checks would go here
        # (expiration, chain validation, etc.)
```

---

## Related Documentation

- **[Testing Strategy](strategy.md)** - Overall testing approach and methodology
- **[Architecture Security](../architecture/README.md)** - Security architecture and design
- **[Operations Security](../operations/runbook.md)** - Production security monitoring
- **[Developer Security Guide](../guides/developer-guide.md)** - Secure development practices

---

**🏠 [Query Intelligence Home](../README.md)** | **📚 [All Documentation](../README.md#documentation)**