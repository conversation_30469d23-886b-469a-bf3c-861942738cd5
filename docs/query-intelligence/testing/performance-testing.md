# Performance Testing

**🔗 Navigation**: [← Testing Strategy](strategy.md) | [↑ Query Intelligence](../README.md) | [↗ Performance Overview](../performance/README.md)

---

## Performance Testing

This document outlines the performance testing approach, procedures, and benchmarks for the Query Intelligence service.

### Performance Targets

| Metric | Target | Current | Status |
|--------|--------|---------|---------|
| **Response Time (p95)** | <500ms | 187ms | ✅ |
| **Response Time (p99)** | <1000ms | 324ms | ✅ |
| **Throughput** | >1000 QPS | 1850 QPS | ✅ |
| **Availability** | >99.9% | 99.95% | ✅ |
| **Error Rate** | <1% | 0.12% | ✅ |
| **Cache Hit Rate** | >80% | 92% | ✅ |

### Load Testing Framework

#### Test Environment Setup

```bash
# Install testing tools
pip install locust pytest-benchmark

# Setup test environment
export QUERY_INTELLIGENCE_URL=https://query-intelligence-l3nxty7oka-uc.a.run.app
export JWT_TOKEN=your-test-token
export REDIS_URL=redis://test-redis:6379
```

#### Locust Load Testing

```python
# locustfile.py
from locust import HttpUser, task, between
import json
import random

class QueryIntelligenceUser(HttpUser):
    wait_time = between(1, 3)
    
    def on_start(self):
        """Setup user session"""
        self.client.headers.update({
            'Authorization': f'Bearer {self.environment.parsed_options.jwt_token}',
            'Content-Type': 'application/json'
        })
    
    @task(3)
    def query_simple(self):
        """Simple query load test"""
        payload = {
            'query': random.choice([
                'How does authentication work?',
                'Explain the database schema',
                'What are the main API endpoints?',
                'Show me the caching strategy'
            ]),
            'repository_id': 'test-repo-123',
            'include_context': True
        }
        
        with self.client.post('/api/v1/query', json=payload, catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Got status {response.status_code}")
    
    @task(2)
    def query_complex(self):
        """Complex query load test"""
        payload = {
            'query': 'Analyze the entire codebase architecture and explain how all components interact with each other, including security patterns and performance optimizations',
            'repository_id': 'test-repo-123',
            'include_context': True
        }
        
        with self.client.post('/api/v1/query', json=payload, catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Got status {response.status_code}")
    
    @task(1)
    def health_check(self):
        """Health check load test"""
        with self.client.get('/health', catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Got status {response.status_code}")
    
    @task(1)
    def query_history(self):
        """Query history load test"""
        with self.client.get('/api/v1/queries?limit=10', catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Got status {response.status_code}")
```

#### WebSocket Load Testing

```python
# websocket_loadtest.py
import asyncio
import websockets
import json
import time
import statistics
from concurrent.futures import ThreadPoolExecutor

class WebSocketLoadTest:
    def __init__(self, url, token, num_connections=50):
        self.url = url
        self.token = token
        self.num_connections = num_connections
        self.response_times = []
        self.errors = []
    
    async def single_connection_test(self, connection_id):
        """Test a single WebSocket connection"""
        try:
            headers = {'Authorization': f'Bearer {self.token}'}
            async with websockets.connect(self.url, extra_headers=headers) as websocket:
                # Send test query
                start_time = time.time()
                await websocket.send(json.dumps({
                    'query': f'Test query from connection {connection_id}',
                    'repository_id': 'test-repo-123'
                }))
                
                # Receive response
                response = await websocket.recv()
                end_time = time.time()
                
                response_time = (end_time - start_time) * 1000  # Convert to ms
                self.response_times.append(response_time)
                
                print(f"Connection {connection_id}: {response_time:.2f}ms")
                
        except Exception as e:
            self.errors.append(f"Connection {connection_id}: {str(e)}")
    
    async def run_load_test(self):
        """Run concurrent WebSocket load test"""
        tasks = []
        for i in range(self.num_connections):
            task = asyncio.create_task(self.single_connection_test(i))
            tasks.append(task)
        
        await asyncio.gather(*tasks, return_exceptions=True)
        
        # Calculate statistics
        if self.response_times:
            avg_time = statistics.mean(self.response_times)
            p95_time = statistics.quantiles(self.response_times, n=20)[18]  # 95th percentile
            p99_time = statistics.quantiles(self.response_times, n=100)[98]  # 99th percentile
            
            print(f"\nWebSocket Load Test Results:")
            print(f"Connections: {self.num_connections}")
            print(f"Successful: {len(self.response_times)}")
            print(f"Errors: {len(self.errors)}")
            print(f"Average Response Time: {avg_time:.2f}ms")
            print(f"95th Percentile: {p95_time:.2f}ms")
            print(f"99th Percentile: {p99_time:.2f}ms")
            
            if self.errors:
                print(f"\nErrors:")
                for error in self.errors[:5]:  # Show first 5 errors
                    print(f"  - {error}")

# Run WebSocket load test
async def main():
    test = WebSocketLoadTest(
        'wss://query-intelligence-l3nxty7oka-uc.a.run.app/api/v1/ws/query',
        'your-jwt-token',
        num_connections=50
    )
    await test.run_load_test()

if __name__ == "__main__":
    asyncio.run(main())
```

### Benchmark Testing

#### API Endpoint Benchmarks

```python
# test_benchmarks.py
import pytest
import requests
import time
import json

class TestPerformanceBenchmarks:
    
    @pytest.fixture
    def client_config(self):
        return {
            'base_url': 'https://query-intelligence-l3nxty7oka-uc.a.run.app',
            'headers': {
                'Authorization': 'Bearer test-token',
                'Content-Type': 'application/json'
            }
        }
    
    def test_simple_query_performance(self, client_config, benchmark):
        """Benchmark simple query performance"""
        def query_simple():
            payload = {
                'query': 'How does authentication work?',
                'repository_id': 'test-repo-123'
            }
            response = requests.post(
                f"{client_config['base_url']}/api/v1/query",
                headers=client_config['headers'],
                json=payload
            )
            return response.json()
        
        result = benchmark(query_simple)
        assert 'response' in result
        assert 'query_id' in result
    
    def test_complex_query_performance(self, client_config, benchmark):
        """Benchmark complex query performance"""
        def query_complex():
            payload = {
                'query': 'Analyze the entire codebase architecture and explain how all components interact',
                'repository_id': 'test-repo-123',
                'include_context': True
            }
            response = requests.post(
                f"{client_config['base_url']}/api/v1/query",
                headers=client_config['headers'],
                json=payload
            )
            return response.json()
        
        result = benchmark(query_complex)
        assert 'response' in result
    
    def test_health_check_performance(self, client_config, benchmark):
        """Benchmark health check performance"""
        def health_check():
            response = requests.get(f"{client_config['base_url']}/health")
            return response.json()
        
        result = benchmark(health_check)
        assert result['status'] == 'healthy'
    
    def test_concurrent_queries(self, client_config):
        """Test concurrent query performance"""
        import concurrent.futures
        
        def single_query():
            payload = {
                'query': 'Test concurrent query',
                'repository_id': 'test-repo-123'
            }
            start_time = time.time()
            response = requests.post(
                f"{client_config['base_url']}/api/v1/query",
                headers=client_config['headers'],
                json=payload
            )
            end_time = time.time()
            return end_time - start_time
        
        # Test with 10 concurrent requests
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(single_query) for _ in range(10)]
            response_times = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        avg_time = sum(response_times) / len(response_times)
        max_time = max(response_times)
        
        assert avg_time < 1.0  # Average should be < 1 second
        assert max_time < 2.0  # Max should be < 2 seconds
        assert len(response_times) == 10  # All requests should complete
```

### Stress Testing

#### Gradual Load Increase

```python
# stress_test.py
import asyncio
import aiohttp
import time
import json
from datetime import datetime

class StressTest:
    def __init__(self, base_url, token):
        self.base_url = base_url
        self.token = token
        self.results = []
    
    async def send_query(self, session, query_id):
        """Send a single query"""
        payload = {
            'query': f'Stress test query {query_id}',
            'repository_id': 'stress-test-repo'
        }
        
        headers = {
            'Authorization': f'Bearer {self.token}',
            'Content-Type': 'application/json'
        }
        
        start_time = time.time()
        try:
            async with session.post(
                f'{self.base_url}/api/v1/query',
                headers=headers,
                json=payload
            ) as response:
                await response.json()
                end_time = time.time()
                
                self.results.append({
                    'query_id': query_id,
                    'response_time': (end_time - start_time) * 1000,
                    'status_code': response.status,
                    'timestamp': datetime.now().isoformat()
                })
        except Exception as e:
            self.results.append({
                'query_id': query_id,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            })
    
    async def run_stress_test(self, duration_minutes=10):
        """Run stress test with gradually increasing load"""
        print(f"Starting stress test for {duration_minutes} minutes...")
        
        async with aiohttp.ClientSession() as session:
            start_time = time.time()
            query_id = 0
            
            while time.time() - start_time < duration_minutes * 60:
                # Gradually increase load
                elapsed_minutes = (time.time() - start_time) / 60
                queries_per_second = min(10 + (elapsed_minutes * 2), 50)  # Max 50 QPS
                
                # Send batch of queries
                batch_size = int(queries_per_second)
                tasks = []
                
                for _ in range(batch_size):
                    task = asyncio.create_task(self.send_query(session, query_id))
                    tasks.append(task)
                    query_id += 1
                
                await asyncio.gather(*tasks, return_exceptions=True)
                
                # Wait for next second
                await asyncio.sleep(1)
                
                # Print progress
                if query_id % 100 == 0:
                    print(f"Sent {query_id} queries, current load: {queries_per_second:.1f} QPS")
    
    def analyze_results(self):
        """Analyze stress test results"""
        if not self.results:
            print("No results to analyze")
            return
        
        successful_queries = [r for r in self.results if 'response_time' in r]
        failed_queries = [r for r in self.results if 'error' in r]
        
        if successful_queries:
            response_times = [r['response_time'] for r in successful_queries]
            avg_time = sum(response_times) / len(response_times)
            p95_time = sorted(response_times)[int(len(response_times) * 0.95)]
            p99_time = sorted(response_times)[int(len(response_times) * 0.99)]
            
            print(f"\nStress Test Results:")
            print(f"Total Queries: {len(self.results)}")
            print(f"Successful: {len(successful_queries)}")
            print(f"Failed: {len(failed_queries)}")
            print(f"Success Rate: {len(successful_queries) / len(self.results) * 100:.2f}%")
            print(f"Average Response Time: {avg_time:.2f}ms")
            print(f"95th Percentile: {p95_time:.2f}ms")
            print(f"99th Percentile: {p99_time:.2f}ms")
            
            if failed_queries:
                print(f"\nSample Errors:")
                for error in failed_queries[:3]:
                    print(f"  - {error.get('error', 'Unknown error')}")

# Run stress test
async def main():
    test = StressTest(
        'https://query-intelligence-l3nxty7oka-uc.a.run.app',
        'your-jwt-token'
    )
    await test.run_stress_test(duration_minutes=5)
    test.analyze_results()

if __name__ == "__main__":
    asyncio.run(main())
```

### Performance Monitoring

#### Real-time Metrics Collection

```python
# performance_monitor.py
import time
import requests
import json
from datetime import datetime
import threading

class PerformanceMonitor:
    def __init__(self, base_url, token):
        self.base_url = base_url
        self.token = token
        self.metrics = []
        self.running = False
    
    def collect_metrics(self):
        """Collect performance metrics"""
        try:
            # Get Prometheus metrics
            response = requests.get(f'{self.base_url}/metrics')
            metrics_text = response.text
            
            # Parse relevant metrics
            metrics = self.parse_prometheus_metrics(metrics_text)
            
            timestamp = datetime.now().isoformat()
            self.metrics.append({
                'timestamp': timestamp,
                'metrics': metrics
            })
            
            # Print current metrics
            print(f"[{timestamp}] Response Time: {metrics.get('response_time_p95', 'N/A')}ms, "
                  f"QPS: {metrics.get('requests_per_second', 'N/A')}, "
                  f"Error Rate: {metrics.get('error_rate', 'N/A')}%")
            
        except Exception as e:
            print(f"Error collecting metrics: {e}")
    
    def parse_prometheus_metrics(self, metrics_text):
        """Parse Prometheus metrics format"""
        metrics = {}
        
        for line in metrics_text.split('\n'):
            if line.startswith('query_response_time_seconds'):
                if 'quantile="0.95"' in line:
                    value = float(line.split(' ')[-1]) * 1000  # Convert to ms
                    metrics['response_time_p95'] = value
            elif line.startswith('query_requests_total'):
                # Calculate requests per second (simplified)
                value = float(line.split(' ')[-1])
                metrics['total_requests'] = value
        
        return metrics
    
    def start_monitoring(self, interval=30):
        """Start monitoring with specified interval"""
        self.running = True
        
        def monitor_loop():
            while self.running:
                self.collect_metrics()
                time.sleep(interval)
        
        thread = threading.Thread(target=monitor_loop)
        thread.daemon = True
        thread.start()
        
        return thread
    
    def stop_monitoring(self):
        """Stop monitoring"""
        self.running = False
    
    def generate_report(self):
        """Generate performance report"""
        if not self.metrics:
            return "No metrics collected"
        
        report = {
            'monitoring_duration': len(self.metrics) * 30,  # Assuming 30s interval
            'total_measurements': len(self.metrics),
            'average_response_time': sum(m['metrics'].get('response_time_p95', 0) for m in self.metrics) / len(self.metrics),
            'measurements': self.metrics
        }
        
        return json.dumps(report, indent=2)
```

### Test Execution

#### Automated Test Suite

```bash
#!/bin/bash
# run_performance_tests.sh

echo "Starting Performance Test Suite..."

# Set environment variables
export QUERY_INTELLIGENCE_URL=https://query-intelligence-l3nxty7oka-uc.a.run.app
export JWT_TOKEN=your-test-token

# Run benchmark tests
echo "Running benchmark tests..."
python -m pytest test_benchmarks.py -v --benchmark-only

# Run load tests
echo "Running load tests..."
locust -f locustfile.py --headless -u 50 -r 10 -t 300s --host=$QUERY_INTELLIGENCE_URL

# Run WebSocket load test
echo "Running WebSocket load test..."
python websocket_loadtest.py

# Run stress test
echo "Running stress test..."
python stress_test.py

echo "Performance tests completed!"
```

#### CI/CD Integration

```yaml
# .github/workflows/performance-tests.yml
name: Performance Tests
on:
  schedule:
    - cron: '0 2 * * *'  # Daily at 2 AM
  workflow_dispatch:

jobs:
  performance-tests:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install dependencies
      run: |
        pip install locust pytest-benchmark requests websockets
    
    - name: Run performance tests
      run: |
        ./run_performance_tests.sh
      env:
        QUERY_INTELLIGENCE_URL: ${{ secrets.QUERY_INTELLIGENCE_URL }}
        JWT_TOKEN: ${{ secrets.JWT_TOKEN }}
    
    - name: Upload results
      uses: actions/upload-artifact@v3
      with:
        name: performance-results
        path: performance-results/
```

---

## Related Documentation

- **[Performance Overview](../performance/README.md)** - Performance targets and monitoring
- **[Performance Optimization](../performance/performance-optimization.md)** - Optimization strategies
- **[Testing Strategy](strategy.md)** - Overall testing approach
- **[Operations Runbook](../operations/runbook.md)** - Production monitoring and operations

---

**🏠 [Query Intelligence Home](../README.md)** | **📚 [All Documentation](../README.md#documentation)**