# Configuration Reference

**🔗 Navigation**: [← Production Deployment](production-deployment.md) | [↑ Query Intelligence](../README.md) | [↗ Operations Runbook](../operations/runbook.md)

---

## Configuration Reference

Complete reference for all configuration options available in the Query Intelligence service.

### Environment Variables

#### Core Configuration

| Variable | Description | Default | Required | Example |
|----------|-------------|---------|----------|---------|
| `ENVIRONMENT` | Deployment environment | `development` | Yes | `production` |
| `PORT` | Service port | `8002` | No | `8002` |
| `LOG_LEVEL` | Logging level | `INFO` | No | `DEBUG` |
| `DEBUG` | Debug mode | `False` | No | `True` |

#### AI/ML Configuration

| Variable | Description | Default | Required | Example |
|----------|-------------|---------|----------|---------|
| `GOOGLE_API_KEY` | Google GenAI API key | - | Yes* | `AIzaSy...` |
| `GCP_PROJECT_ID` | Google Cloud Project ID | - | Yes | `vibe-match-463114` |
| `GEMINI_MODEL_NAME` | Gemini model to use | `gemini-2.5-flash` | No | `gemini-2.5-pro` |
| `USE_VERTEX_AI` | Use Vertex AI instead of GenAI | `true` | No | `false` |
| `MAX_TOKENS` | Maximum tokens per response | `8192` | No | `4096` |
| `TEMPERATURE` | Model temperature | `0.3` | No | `0.7` |

*Required for development, optional for production with Vertex AI

#### Database & Cache Configuration

| Variable | Description | Default | Required | Example |
|----------|-------------|---------|----------|---------|
| `REDIS_URL` | Redis connection URL | - | No | `redis://localhost:6379` |
| `REDIS_PASSWORD` | Redis password | - | No | `secret123` |
| `REDIS_DB` | Redis database number | `0` | No | `1` |
| `CACHE_TTL` | Cache TTL in seconds | `3600` | No | `7200` |
| `SEMANTIC_CACHE_ENABLED` | Enable semantic caching | `true` | No | `false` |
| `SEMANTIC_CACHE_THRESHOLD` | Similarity threshold | `0.8` | No | `0.9` |

#### Security Configuration

| Variable | Description | Default | Required | Example |
|----------|-------------|---------|----------|---------|
| `JWT_SECRET` | JWT signing secret | - | Yes | `your-secret-key` |
| `JWT_ALGORITHM` | JWT algorithm | `HS256` | No | `RS256` |
| `JWT_EXPIRATION` | JWT expiration time | `86400` | No | `3600` |
| `CORS_ORIGINS` | CORS allowed origins | `["*"]` | No | `["https://app.example.com"]` |
| `ENABLE_WEBSOCKET_AUTH` | WebSocket authentication | `true` | No | `false` |

#### Rate Limiting

| Variable | Description | Default | Required | Example |
|----------|-------------|---------|----------|---------|
| `RATE_LIMIT_ENABLED` | Enable rate limiting | `true` | No | `false` |
| `RATE_LIMIT_PER_MINUTE` | Requests per minute | `60` | No | `100` |
| `RATE_LIMIT_PER_HOUR` | Requests per hour | `1000` | No | `2000` |
| `RATE_LIMIT_PER_DAY` | Requests per day | `10000` | No | `50000` |

#### External Services

| Variable | Description | Default | Required | Example |
|----------|-------------|---------|----------|---------|
| `ANALYSIS_ENGINE_URL` | Analysis engine URL | `http://localhost:8001` | No | `http://analysis-engine:8001` |
| `PATTERN_MINING_URL` | Pattern mining URL | `http://localhost:8003` | No | `http://pattern-mining:8003` |
| `FIREBASE_PROJECT_ID` | Firebase project ID | - | No | `your-firebase-project` |
| `FIREBASE_CREDENTIALS_PATH` | Firebase credentials path | - | No | `/path/to/credentials.json` |

#### Performance Configuration

| Variable | Description | Default | Required | Example |
|----------|-------------|---------|----------|---------|
| `MAX_QUERY_LENGTH` | Maximum query length | `10000` | No | `5000` |
| `MAX_CONCURRENT_QUERIES` | Max concurrent queries | `100` | No | `50` |
| `QUERY_TIMEOUT` | Query timeout in seconds | `30` | No | `60` |
| `RESPONSE_STREAM_CHUNK_SIZE` | Stream chunk size | `1024` | No | `512` |
| `WORKERS` | Number of workers | `4` | No | `8` |

#### Monitoring & Observability

| Variable | Description | Default | Required | Example |
|----------|-------------|---------|----------|---------|
| `METRICS_ENABLED` | Enable Prometheus metrics | `true` | No | `false` |
| `METRICS_PORT` | Metrics port | `9090` | No | `8080` |
| `TRACING_ENABLED` | Enable distributed tracing | `true` | No | `false` |
| `TRACING_SAMPLE_RATE` | Tracing sample rate | `0.1` | No | `1.0` |
| `STRUCTURED_LOGGING` | Enable structured logging | `true` | No | `false` |

### Configuration Files

#### Development Configuration (`.env.development`)

```bash
# Development Environment
ENVIRONMENT=development
DEBUG=True
LOG_LEVEL=DEBUG

# API Keys (for local development)
GOOGLE_API_KEY=your-development-api-key
GCP_PROJECT_ID=your-dev-project

# Local Services
REDIS_URL=redis://localhost:6379
ANALYSIS_ENGINE_URL=http://localhost:8001

# Security (development only)
JWT_SECRET=dev-secret-key
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080"]

# Rate Limiting (relaxed for development)
RATE_LIMIT_PER_MINUTE=1000
RATE_LIMIT_PER_HOUR=10000
```

#### Production Configuration (`.env.production`)

```bash
# Production Environment
ENVIRONMENT=production
DEBUG=False
LOG_LEVEL=INFO

# Vertex AI (preferred for production)
USE_VERTEX_AI=true
GCP_PROJECT_ID=vibe-match-463114
GEMINI_MODEL_NAME=gemini-2.5-flash

# Production Services
REDIS_URL=redis://redis.internal:6379
ANALYSIS_ENGINE_URL=http://analysis-engine.internal:8001

# Security (production)
JWT_SECRET=${JWT_SECRET}
CORS_ORIGINS=["https://app.episteme.ai"]
ENABLE_WEBSOCKET_AUTH=true

# Rate Limiting (production)
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_PER_HOUR=1000
RATE_LIMIT_PER_DAY=10000

# Performance
MAX_CONCURRENT_QUERIES=50
QUERY_TIMEOUT=30
WORKERS=4

# Monitoring
METRICS_ENABLED=true
TRACING_ENABLED=true
STRUCTURED_LOGGING=true
```

### Docker Configuration

#### Dockerfile Environment

```dockerfile
# Runtime configuration
ENV ENVIRONMENT=production
ENV PORT=8002
ENV WORKERS=4

# Security
ENV JWT_ALGORITHM=HS256
ENV CORS_ORIGINS='["https://app.episteme.ai"]'

# Performance
ENV MAX_CONCURRENT_QUERIES=50
ENV QUERY_TIMEOUT=30
```

#### Docker Compose

```yaml
version: '3.8'
services:
  query-intelligence:
    image: query-intelligence:latest
    environment:
      - ENVIRONMENT=production
      - GCP_PROJECT_ID=vibe-match-463114
      - REDIS_URL=redis://redis:6379
      - ANALYSIS_ENGINE_URL=http://analysis-engine:8001
      - JWT_SECRET=${JWT_SECRET}
      - RATE_LIMIT_PER_HOUR=1000
      - METRICS_ENABLED=true
    ports:
      - "8002:8002"
    depends_on:
      - redis
      - analysis-engine
```

### Cloud Run Configuration

#### Cloud Run Service Configuration

```yaml
apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: query-intelligence
  annotations:
    run.googleapis.com/ingress: all
    run.googleapis.com/execution-environment: gen2
spec:
  template:
    metadata:
      annotations:
        run.googleapis.com/cpu-throttling: "false"
        run.googleapis.com/memory: "2Gi"
        run.googleapis.com/cpu: "2"
    spec:
      containerConcurrency: 100
      timeoutSeconds: 300
      containers:
      - image: gcr.io/vibe-match-463114/query-intelligence:latest
        env:
        - name: ENVIRONMENT
          value: "production"
        - name: GCP_PROJECT_ID
          value: "vibe-match-463114"
        - name: USE_VERTEX_AI
          value: "true"
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: redis-config
              key: url
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: jwt-config
              key: secret
        resources:
          limits:
            cpu: "2"
            memory: "2Gi"
```

### Validation & Testing

#### Configuration Validation

```python
import os
from typing import Optional

class Config:
    def __init__(self):
        self.validate_required_config()
    
    def validate_required_config(self):
        required_vars = ['GCP_PROJECT_ID', 'JWT_SECRET']
        
        for var in required_vars:
            if not os.getenv(var):
                raise ValueError(f"Required environment variable {var} is not set")
    
    @property
    def environment(self) -> str:
        return os.getenv('ENVIRONMENT', 'development')
    
    @property
    def debug(self) -> bool:
        return os.getenv('DEBUG', 'False').lower() == 'true'
    
    @property
    def redis_url(self) -> Optional[str]:
        return os.getenv('REDIS_URL')
```

#### Health Check Configuration

```python
async def health_check():
    """Health check endpoint configuration"""
    checks = {
        'redis': await check_redis_connection(),
        'analysis_engine': await check_analysis_engine(),
        'vertex_ai': await check_vertex_ai_connection(),
        'config': validate_configuration()
    }
    
    return {
        'status': 'healthy' if all(checks.values()) else 'unhealthy',
        'checks': checks
    }
```

---

## Related Documentation

- **[Production Deployment Guide](production-deployment.md)** - Complete deployment procedures
- **[Operations Runbook](../operations/runbook.md)** - Operational procedures and monitoring
- **[Developer Guide](../guides/developer-guide.md)** - Local development setup
- **[Architecture Documentation](../architecture/README.md)** - System design and configuration patterns

---

**🏠 [Query Intelligence Home](../README.md)** | **📚 [All Documentation](../README.md#documentation)**