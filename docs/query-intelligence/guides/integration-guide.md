# Integration Guide

**🔗 Navigation**: [← Developer Guide](developer-guide.md) | [↑ Query Intelligence](../README.md) | [↗ API Reference](../api/README.md)

---

## Integration Guide

This guide covers how to integrate the Query Intelligence service into your applications and workflows.

### Quick Integration

#### Python Client
```python
import requests
import json

class QueryIntelligenceClient:
    def __init__(self, base_url, jwt_token):
        self.base_url = base_url
        self.headers = {
            'Authorization': f'Bearer {jwt_token}',
            'Content-Type': 'application/json'
        }
    
    def query(self, query_text, repository_id, include_context=True):
        payload = {
            'query': query_text,
            'repository_id': repository_id,
            'include_context': include_context
        }
        
        response = requests.post(
            f'{self.base_url}/api/v1/query',
            headers=self.headers,
            data=json.dumps(payload)
        )
        
        return response.json()

# Usage
client = QueryIntelligenceClient(
    'https://query-intelligence-l3nxty7oka-uc.a.run.app',
    'YOUR_JWT_TOKEN'
)

result = client.query(
    'How does authentication work?',
    'repo-123'
)
```

#### JavaScript/TypeScript Client
```typescript
class QueryIntelligenceClient {
  private baseUrl: string;
  private headers: HeadersInit;

  constructor(baseUrl: string, jwtToken: string) {
    this.baseUrl = baseUrl;
    this.headers = {
      'Authorization': `Bearer ${jwtToken}`,
      'Content-Type': 'application/json'
    };
  }

  async query(queryText: string, repositoryId: string, includeContext: boolean = true) {
    const response = await fetch(`${this.baseUrl}/api/v1/query`, {
      method: 'POST',
      headers: this.headers,
      body: JSON.stringify({
        query: queryText,
        repository_id: repositoryId,
        include_context: includeContext
      })
    });

    return response.json();
  }
}

// Usage
const client = new QueryIntelligenceClient(
  'https://query-intelligence-l3nxty7oka-uc.a.run.app',
  'YOUR_JWT_TOKEN'
);

const result = await client.query(
  'Explain the database schema',
  'repo-123'
);
```

### Integration Patterns

#### Microservices Integration
For microservices architectures, integrate Query Intelligence as a shared service:

```yaml
# docker-compose.yml
version: '3.8'
services:
  query-intelligence:
    image: gcr.io/vibe-match-463114/query-intelligence:latest
    environment:
      - ENVIRONMENT=production
      - REDIS_URL=redis://redis:6379
      - GCP_PROJECT_ID=vibe-match-463114
    depends_on:
      - redis
      - analysis-engine
    
  your-app:
    image: your-app:latest
    environment:
      - QUERY_INTELLIGENCE_URL=http://query-intelligence:8002
```

#### Event-Driven Integration
```python
# Event-driven query processing
import asyncio
from query_intelligence_client import QueryIntelligenceClient

async def process_query_event(event):
    client = QueryIntelligenceClient(
        os.getenv('QUERY_INTELLIGENCE_URL'),
        os.getenv('JWT_TOKEN')
    )
    
    result = await client.query(
        event['query'],
        event['repository_id']
    )
    
    # Process and forward result
    await forward_result(result)
```

### Authentication Integration

#### JWT Token Management
```python
import jwt
import time

class TokenManager:
    def __init__(self, secret_key):
        self.secret_key = secret_key
        self.token = None
        self.expires_at = 0
    
    def get_token(self):
        if time.time() >= self.expires_at:
            self.refresh_token()
        return self.token
    
    def refresh_token(self):
        payload = {
            'sub': 'user_id',
            'iat': time.time(),
            'exp': time.time() + 3600  # 1 hour
        }
        self.token = jwt.encode(payload, self.secret_key, algorithm='HS256')
        self.expires_at = payload['exp']
```

### Error Handling

#### Retry Logic
```python
import time
import random

class RetryableClient:
    def __init__(self, client, max_retries=3):
        self.client = client
        self.max_retries = max_retries
    
    def query_with_retry(self, query_text, repository_id):
        for attempt in range(self.max_retries):
            try:
                return self.client.query(query_text, repository_id)
            except Exception as e:
                if attempt == self.max_retries - 1:
                    raise e
                
                # Exponential backoff with jitter
                wait_time = (2 ** attempt) + random.uniform(0, 1)
                time.sleep(wait_time)
```

### Performance Optimization

#### Connection Pooling
```python
import aiohttp
import asyncio

class AsyncQueryClient:
    def __init__(self, base_url, jwt_token):
        self.base_url = base_url
        self.headers = {'Authorization': f'Bearer {jwt_token}'}
        self.session = None
    
    async def __aenter__(self):
        connector = aiohttp.TCPConnector(
            limit=100,
            limit_per_host=30,
            ttl_dns_cache=300
        )
        self.session = aiohttp.ClientSession(
            connector=connector,
            headers=self.headers
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.session.close()
    
    async def query(self, query_text, repository_id):
        async with self.session.post(
            f'{self.base_url}/api/v1/query',
            json={
                'query': query_text,
                'repository_id': repository_id
            }
        ) as response:
            return await response.json()
```

### Best Practices

1. **Authentication**: Always use JWT tokens and refresh them before expiration
2. **Error Handling**: Implement proper retry logic with exponential backoff
3. **Connection Management**: Use connection pooling for high-throughput scenarios
4. **Caching**: Cache responses when appropriate to reduce API calls
5. **Monitoring**: Track API usage and performance metrics

---

## Related Documentation

- **[REST API Documentation](../api/README.md)** - Complete API reference
- **[WebSocket API Documentation](../api/websocket-api.md)** - Real-time streaming API
- **[Developer Guide](developer-guide.md)** - Local development setup
- **[Architecture Documentation](../architecture/README.md)** - System design and integration patterns

---

**🏠 [Query Intelligence Home](../README.md)** | **📚 [All Documentation](../README.md#documentation)**