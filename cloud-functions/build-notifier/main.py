"""
Cloud Function to handle build notifications and automation.
Triggers on Cloud Build pub/sub events to send notifications and perform post-build actions.
"""

import json
import base64
import logging
from typing import Dict, Any
import requests
from google.cloud import secretmanager
import functions_framework

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Secret Manager client
secret_client = secretmanager.SecretManagerServiceClient()

def get_secret(secret_id: str, project_id: str) -> str:
    """Retrieve secret from Secret Manager."""
    try:
        name = f"projects/{project_id}/secrets/{secret_id}/versions/latest"
        response = secret_client.access_secret_version(request={"name": name})
        return response.payload.data.decode("UTF-8")
    except Exception as e:
        logger.error(f"Failed to get secret {secret_id}: {e}")
        return ""

def send_slack_notification(webhook_url: str, message: Dict[str, Any]) -> bool:
    """Send notification to Slack."""
    try:
        response = requests.post(webhook_url, json=message, timeout=10)
        response.raise_for_status()
        logger.info("Slack notification sent successfully")
        return True
    except Exception as e:
        logger.error(f"Failed to send Slack notification: {e}")
        return False

def format_build_message(build_data: Dict[str, Any]) -> Dict[str, Any]:
    """Format build data for Slack message."""
    build_id = build_data.get('id', 'Unknown')
    status = build_data.get('status', 'Unknown')
    source = build_data.get('source', {})
    repo_name = source.get('repoSource', {}).get('repoName', 'Unknown')
    branch = source.get('repoSource', {}).get('branchName', 'Unknown')
    log_url = build_data.get('logUrl', '')
    
    # Determine color based on status
    color_map = {
        'SUCCESS': 'good',
        'FAILURE': 'danger', 
        'TIMEOUT': 'warning',
        'CANCELLED': 'warning',
        'WORKING': '#439FE0'
    }
    color = color_map.get(status, '#439FE0')
    
    # Determine emoji
    emoji_map = {
        'SUCCESS': ':white_check_mark:',
        'FAILURE': ':x:',
        'TIMEOUT': ':warning:',
        'CANCELLED': ':no_entry_sign:',
        'WORKING': ':building_construction:'
    }
    emoji = emoji_map.get(status, ':question:')
    
    message = {
        "attachments": [
            {
                "color": color,
                "title": f"{emoji} Build {status}",
                "fields": [
                    {
                        "title": "Repository",
                        "value": repo_name,
                        "short": True
                    },
                    {
                        "title": "Branch", 
                        "value": branch,
                        "short": True
                    },
                    {
                        "title": "Build ID",
                        "value": build_id,
                        "short": True
                    },
                    {
                        "title": "Status",
                        "value": status,
                        "short": True
                    }
                ],
                "actions": [
                    {
                        "type": "button",
                        "text": "View Logs",
                        "url": log_url
                    }
                ] if log_url else []
            }
        ]
    }
    
    return message

def trigger_post_build_actions(build_data: Dict[str, Any]) -> None:
    """Trigger post-build actions based on build status."""
    status = build_data.get('status')
    build_id = build_data.get('id')
    
    logger.info(f"Processing post-build actions for build {build_id} with status {status}")
    
    if status == 'SUCCESS':
        # Successful build actions
        logger.info(f"Build {build_id} succeeded - triggering success actions")
        
        # Could trigger:
        # - Security scanning
        # - Integration tests  
        # - Deployment to next environment
        # - Updating documentation
        
    elif status == 'FAILURE':
        # Failed build actions
        logger.info(f"Build {build_id} failed - triggering failure actions")
        
        # Could trigger:
        # - Rollback previous deployment
        # - Create incident ticket
        # - Notify on-call engineer
        # - Generate failure report

@functions_framework.cloud_event
def build_notifier(cloud_event):
    """
    Cloud Function triggered by Cloud Build pub/sub events.
    
    Args:
        cloud_event: CloudEvent containing build information
    """
    try:
        # Decode the pub/sub message
        build_data = json.loads(base64.b64decode(cloud_event.data['message']['data']))
        
        logger.info(f"Processing build event: {build_data.get('id')}")
        logger.info(f"Build status: {build_data.get('status')}")
        
        # Get project ID from environment or build data
        project_id = build_data.get('projectId', 'ccl-platform')
        
        # Send Slack notification if webhook is configured
        slack_webhook = get_secret('slack-webhook-url', project_id)
        if slack_webhook:
            message = format_build_message(build_data)
            send_slack_notification(slack_webhook, message)
        else:
            logger.warning("Slack webhook not configured - skipping notification")
        
        # Trigger post-build actions
        trigger_post_build_actions(build_data)
        
        logger.info(f"Successfully processed build event {build_data.get('id')}")
        
    except Exception as e:
        logger.error(f"Error processing build event: {e}")
        raise

# Health check endpoint for Cloud Functions
@functions_framework.http  
def health_check(request):
    """Health check endpoint."""
    return {'status': 'healthy', 'service': 'build-notifier'}, 200