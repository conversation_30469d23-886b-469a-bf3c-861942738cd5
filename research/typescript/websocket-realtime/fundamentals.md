# TypeScript WebSocket Fundamentals

**Source**: Multiple official documentation sources  
**Scraped**: 2025-07-18  
**Trust Score**: 9.5 (Official documentation compilation)  

## Table of Contents

1. [WebSocket Protocol Overview](#websocket-protocol-overview)
2. [TypeScript Integration](#typescript-integration)
3. [Browser WebSocket API](#browser-websocket-api)
4. [Node.js WebSocket Implementation](#nodejs-websocket-implementation)
5. [Basic Client Implementation](#basic-client-implementation)
6. [Basic Server Implementation](#basic-server-implementation)
7. [Type Definitions](#type-definitions)
8. [Error Handling](#error-handling)
9. [Message Protocols](#message-protocols)
10. [Connection Lifecycle](#connection-lifecycle)

## WebSocket Protocol Overview

WebSockets provide a persistent, bidirectional communication channel between client and server. Unlike HTTP, WebSocket connections remain open, allowing both parties to send data at any time.

### Key Characteristics

- **Full-duplex communication**: Both client and server can send messages independently
- **Low latency**: No HTTP overhead after initial handshake
- **Persistent connection**: Remains open until explicitly closed
- **Protocol upgrade**: Starts as HTTP, upgrades to WebSocket

### Protocol Handshake

```
GET /ws HTTP/1.1
Host: example.com
Upgrade: websocket
Connection: Upgrade
Sec-WebSocket-Key: dGhlIHNhbXBsZSBub25jZQ==
Sec-WebSocket-Version: 13
```

Response:
```
HTTP/1.1 101 Switching Protocols
Upgrade: websocket
Connection: Upgrade
Sec-WebSocket-Accept: s3pPLMBiTxaQ9kYGzzhZRbK+xOo=
```

## TypeScript Integration

TypeScript provides excellent support for WebSocket development through:

### Type Safety Benefits

- **Compile-time error detection**: Catch WebSocket API misuse early
- **IntelliSense support**: Auto-completion for WebSocket methods and properties
- **Interface definitions**: Strong typing for message protocols
- **Generic types**: Type-safe message handling

### Development Environment

```bash
# Install TypeScript and Node.js types
npm install typescript @types/node

# For browser development
npm install @types/dom

# For WebSocket library
npm install ws @types/ws
```

## Browser WebSocket API

### Basic WebSocket Client

```typescript
interface WebSocketMessage {
    type: string;
    data: any;
    timestamp: number;
}

class WebSocketClient {
    private socket: WebSocket | null = null;
    private reconnectAttempts: number = 0;
    private maxReconnectAttempts: number = 5;
    private reconnectDelay: number = 1000;

    constructor(private url: string) {}

    connect(): Promise<void> {
        return new Promise((resolve, reject) => {
            try {
                this.socket = new WebSocket(this.url);
                this.setupEventHandlers();
                
                this.socket.onopen = () => {
                    this.reconnectAttempts = 0;
                    resolve();
                };
                
                this.socket.onerror = (error) => {
                    reject(error);
                };
            } catch (error) {
                reject(error);
            }
        });
    }

    private setupEventHandlers(): void {
        if (!this.socket) return;

        this.socket.onmessage = (event) => {
            try {
                const message: WebSocketMessage = JSON.parse(event.data);
                this.handleMessage(message);
            } catch (error) {
                console.error('Failed to parse WebSocket message:', error);
            }
        };

        this.socket.onclose = (event) => {
            this.handleClose(event);
        };

        this.socket.onerror = (error) => {
            this.handleError(error);
        };
    }

    private handleMessage(message: WebSocketMessage): void {
        switch (message.type) {
            case 'ping':
                this.send({ type: 'pong', data: null, timestamp: Date.now() });
                break;
            case 'data':
                this.processData(message.data);
                break;
            default:
                console.warn('Unknown message type:', message.type);
        }
    }

    private handleClose(event: CloseEvent): void {
        console.log('WebSocket closed:', event.code, event.reason);
        
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            setTimeout(() => {
                this.connect().catch(console.error);
            }, this.reconnectDelay * this.reconnectAttempts);
        }
    }

    private handleError(error: Event): void {
        console.error('WebSocket error:', error);
    }

    send(message: WebSocketMessage): void {
        if (this.socket && this.socket.readyState === WebSocket.OPEN) {
            this.socket.send(JSON.stringify(message));
        } else {
            console.warn('WebSocket is not open');
        }
    }

    private processData(data: any): void {
        // Process incoming data
        console.log('Received data:', data);
    }

    disconnect(): void {
        if (this.socket) {
            this.socket.close();
            this.socket = null;
        }
    }
}
```

### React Hook for WebSocket

```typescript
import { useEffect, useState, useCallback, useRef } from 'react';

interface UseWebSocketOptions {
    onMessage?: (data: any) => void;
    onError?: (error: Event) => void;
    onClose?: (event: CloseEvent) => void;
    reconnectAttempts?: number;
    reconnectDelay?: number;
}

interface UseWebSocketReturn {
    socket: WebSocket | null;
    isConnected: boolean;
    sendMessage: (data: any) => void;
    disconnect: () => void;
}

export const useWebSocket = (
    url: string,
    options: UseWebSocketOptions = {}
): UseWebSocketReturn => {
    const [socket, setSocket] = useState<WebSocket | null>(null);
    const [isConnected, setIsConnected] = useState(false);
    const reconnectAttempts = useRef(0);
    const maxReconnectAttempts = options.reconnectAttempts || 5;
    const reconnectDelay = options.reconnectDelay || 1000;

    const connect = useCallback(() => {
        try {
            const ws = new WebSocket(url);
            
            ws.onopen = () => {
                setIsConnected(true);
                reconnectAttempts.current = 0;
                console.log('WebSocket connected');
            };

            ws.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    options.onMessage?.(data);
                } catch (error) {
                    console.error('Failed to parse message:', error);
                }
            };

            ws.onclose = (event) => {
                setIsConnected(false);
                options.onClose?.(event);
                
                if (reconnectAttempts.current < maxReconnectAttempts) {
                    reconnectAttempts.current++;
                    setTimeout(() => {
                        connect();
                    }, reconnectDelay * reconnectAttempts.current);
                }
            };

            ws.onerror = (error) => {
                options.onError?.(error);
            };

            setSocket(ws);
        } catch (error) {
            console.error('Failed to create WebSocket:', error);
        }
    }, [url, options, maxReconnectAttempts, reconnectDelay]);

    const sendMessage = useCallback((data: any) => {
        if (socket && socket.readyState === WebSocket.OPEN) {
            socket.send(JSON.stringify(data));
        } else {
            console.warn('WebSocket is not connected');
        }
    }, [socket]);

    const disconnect = useCallback(() => {
        if (socket) {
            socket.close();
            setSocket(null);
            setIsConnected(false);
        }
    }, [socket]);

    useEffect(() => {
        connect();
        return () => disconnect();
    }, [connect, disconnect]);

    return {
        socket,
        isConnected,
        sendMessage,
        disconnect
    };
};
```

## Node.js WebSocket Implementation

### Using ws Library

```typescript
import WebSocket from 'ws';
import { IncomingMessage } from 'http';

interface ClientConnection {
    id: string;
    socket: WebSocket;
    authenticated: boolean;
    userId?: string;
    metadata: Map<string, any>;
}

class WebSocketServer {
    private server: WebSocket.Server;
    private clients: Map<string, ClientConnection> = new Map();
    private heartbeatInterval: NodeJS.Timeout;

    constructor(port: number) {
        this.server = new WebSocket.Server({
            port,
            verifyClient: this.verifyClient.bind(this)
        });

        this.setupEventHandlers();
        this.startHeartbeat();
    }

    private verifyClient(info: {
        origin: string;
        secure: boolean;
        req: IncomingMessage;
    }): boolean {
        // Implement authentication logic
        const token = info.req.headers.authorization?.split(' ')[1];
        return this.validateToken(token);
    }

    private validateToken(token?: string): boolean {
        // Implement token validation
        return token !== undefined;
    }

    private setupEventHandlers(): void {
        this.server.on('connection', (socket: WebSocket, request: IncomingMessage) => {
            const clientId = this.generateClientId();
            const client: ClientConnection = {
                id: clientId,
                socket,
                authenticated: false,
                metadata: new Map()
            };

            this.clients.set(clientId, client);
            this.handleClientConnection(client, request);
        });

        this.server.on('error', (error) => {
            console.error('WebSocket Server error:', error);
        });
    }

    private handleClientConnection(client: ClientConnection, request: IncomingMessage): void {
        console.log(`Client connected: ${client.id}`);

        client.socket.on('message', (data: WebSocket.Data) => {
            try {
                const message = JSON.parse(data.toString());
                this.handleMessage(client, message);
            } catch (error) {
                console.error('Failed to parse message:', error);
                this.sendError(client, 'Invalid message format');
            }
        });

        client.socket.on('close', (code: number, reason: string) => {
            console.log(`Client disconnected: ${client.id}`, code, reason);
            this.clients.delete(client.id);
        });

        client.socket.on('error', (error) => {
            console.error(`Client error: ${client.id}`, error);
        });

        client.socket.on('pong', () => {
            client.metadata.set('lastPong', Date.now());
        });

        // Send welcome message
        this.send(client, {
            type: 'welcome',
            data: { clientId: client.id },
            timestamp: Date.now()
        });
    }

    private handleMessage(client: ClientConnection, message: any): void {
        switch (message.type) {
            case 'authenticate':
                this.handleAuthentication(client, message.data);
                break;
            case 'ping':
                this.send(client, { type: 'pong', data: null, timestamp: Date.now() });
                break;
            case 'subscribe':
                this.handleSubscription(client, message.data);
                break;
            case 'unsubscribe':
                this.handleUnsubscription(client, message.data);
                break;
            case 'data':
                this.handleData(client, message.data);
                break;
            default:
                this.sendError(client, `Unknown message type: ${message.type}`);
        }
    }

    private handleAuthentication(client: ClientConnection, data: any): void {
        const { token, userId } = data;
        
        if (this.validateToken(token)) {
            client.authenticated = true;
            client.userId = userId;
            
            this.send(client, {
                type: 'authenticated',
                data: { success: true },
                timestamp: Date.now()
            });
        } else {
            this.sendError(client, 'Authentication failed');
        }
    }

    private handleSubscription(client: ClientConnection, data: any): void {
        if (!client.authenticated) {
            this.sendError(client, 'Authentication required');
            return;
        }

        const { channel } = data;
        const subscriptions = client.metadata.get('subscriptions') || new Set();
        subscriptions.add(channel);
        client.metadata.set('subscriptions', subscriptions);

        this.send(client, {
            type: 'subscribed',
            data: { channel },
            timestamp: Date.now()
        });
    }

    private handleUnsubscription(client: ClientConnection, data: any): void {
        const { channel } = data;
        const subscriptions = client.metadata.get('subscriptions') || new Set();
        subscriptions.delete(channel);
        client.metadata.set('subscriptions', subscriptions);

        this.send(client, {
            type: 'unsubscribed',
            data: { channel },
            timestamp: Date.now()
        });
    }

    private handleData(client: ClientConnection, data: any): void {
        // Process incoming data
        console.log(`Data from ${client.id}:`, data);
    }

    private send(client: ClientConnection, message: any): void {
        if (client.socket.readyState === WebSocket.OPEN) {
            client.socket.send(JSON.stringify(message));
        }
    }

    private sendError(client: ClientConnection, error: string): void {
        this.send(client, {
            type: 'error',
            data: { message: error },
            timestamp: Date.now()
        });
    }

    private broadcast(message: any, filter?: (client: ClientConnection) => boolean): void {
        for (const client of this.clients.values()) {
            if (!filter || filter(client)) {
                this.send(client, message);
            }
        }
    }

    private broadcastToChannel(channel: string, message: any): void {
        this.broadcast(message, (client) => {
            const subscriptions = client.metadata.get('subscriptions') || new Set();
            return subscriptions.has(channel);
        });
    }

    private startHeartbeat(): void {
        this.heartbeatInterval = setInterval(() => {
            for (const client of this.clients.values()) {
                if (client.socket.readyState === WebSocket.OPEN) {
                    client.socket.ping();
                } else {
                    this.clients.delete(client.id);
                }
            }
        }, 30000); // 30 seconds
    }

    private generateClientId(): string {
        return `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    close(): void {
        clearInterval(this.heartbeatInterval);
        this.server.close();
    }
}
```

## Type Definitions

### Message Protocol Types

```typescript
// Base message interface
interface BaseMessage {
    type: string;
    timestamp: number;
    id?: string;
}

// Specific message types
interface PingMessage extends BaseMessage {
    type: 'ping';
    data: null;
}

interface PongMessage extends BaseMessage {
    type: 'pong';
    data: null;
}

interface AuthenticationMessage extends BaseMessage {
    type: 'authenticate';
    data: {
        token: string;
        userId: string;
    };
}

interface DataMessage extends BaseMessage {
    type: 'data';
    data: any;
}

interface ErrorMessage extends BaseMessage {
    type: 'error';
    data: {
        message: string;
        code?: number;
    };
}

interface SubscriptionMessage extends BaseMessage {
    type: 'subscribe' | 'unsubscribe';
    data: {
        channel: string;
    };
}

// Union type for all messages
type WebSocketMessage = 
    | PingMessage
    | PongMessage
    | AuthenticationMessage
    | DataMessage
    | ErrorMessage
    | SubscriptionMessage;
```

### Connection State Types

```typescript
enum ConnectionState {
    CONNECTING = 'connecting',
    OPEN = 'open',
    CLOSING = 'closing',
    CLOSED = 'closed'
}

interface ConnectionMetadata {
    connectedAt: number;
    lastActivity: number;
    messagesSent: number;
    messagesReceived: number;
    subscriptions: Set<string>;
}
```

## Error Handling

### Comprehensive Error Handling

```typescript
class WebSocketError extends Error {
    constructor(
        message: string,
        public code: number,
        public originalError?: Error
    ) {
        super(message);
        this.name = 'WebSocketError';
    }
}

enum ErrorCode {
    CONNECTION_FAILED = 1000,
    AUTHENTICATION_FAILED = 1001,
    INVALID_MESSAGE = 1002,
    RATE_LIMIT_EXCEEDED = 1003,
    INTERNAL_ERROR = 1004
}

class RobustWebSocketClient {
    private reconnectAttempts: number = 0;
    private maxReconnectAttempts: number = 5;
    private reconnectDelay: number = 1000;

    async connect(url: string): Promise<void> {
        try {
            await this.attemptConnection(url);
        } catch (error) {
            this.handleConnectionError(error);
        }
    }

    private async attemptConnection(url: string): Promise<void> {
        return new Promise((resolve, reject) => {
            const socket = new WebSocket(url);

            const connectionTimeout = setTimeout(() => {
                socket.close();
                reject(new WebSocketError(
                    'Connection timeout',
                    ErrorCode.CONNECTION_FAILED
                ));
            }, 10000);

            socket.onopen = () => {
                clearTimeout(connectionTimeout);
                this.reconnectAttempts = 0;
                resolve();
            };

            socket.onerror = (error) => {
                clearTimeout(connectionTimeout);
                reject(new WebSocketError(
                    'Connection failed',
                    ErrorCode.CONNECTION_FAILED,
                    error as Error
                ));
            };
        });
    }

    private handleConnectionError(error: Error): void {
        console.error('WebSocket connection error:', error);

        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
            
            setTimeout(() => {
                this.connect(this.url).catch(console.error);
            }, delay);
        } else {
            console.error('Max reconnection attempts reached');
        }
    }
}
```

## Message Protocols

### JSON-RPC Protocol

```typescript
interface JSONRPCRequest {
    jsonrpc: '2.0';
    method: string;
    params?: any;
    id: string | number;
}

interface JSONRPCResponse {
    jsonrpc: '2.0';
    result?: any;
    error?: {
        code: number;
        message: string;
        data?: any;
    };
    id: string | number;
}

class JSONRPCWebSocket {
    private pendingRequests: Map<string | number, {
        resolve: (value: any) => void;
        reject: (error: Error) => void;
        timeout: NodeJS.Timeout;
    }> = new Map();

    async call(method: string, params?: any): Promise<any> {
        const id = this.generateRequestId();
        const request: JSONRPCRequest = {
            jsonrpc: '2.0',
            method,
            params,
            id
        };

        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                this.pendingRequests.delete(id);
                reject(new Error('Request timeout'));
            }, 30000);

            this.pendingRequests.set(id, { resolve, reject, timeout });
            this.send(request);
        });
    }

    private handleResponse(response: JSONRPCResponse): void {
        const pending = this.pendingRequests.get(response.id);
        if (!pending) return;

        clearTimeout(pending.timeout);
        this.pendingRequests.delete(response.id);

        if (response.error) {
            pending.reject(new Error(response.error.message));
        } else {
            pending.resolve(response.result);
        }
    }

    private generateRequestId(): string {
        return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
}
```

## Connection Lifecycle

### State Management

```typescript
class ConnectionLifecycleManager {
    private state: ConnectionState = ConnectionState.CLOSED;
    private listeners: Map<string, Function[]> = new Map();

    constructor(private socket: WebSocket) {
        this.setupEventHandlers();
    }

    private setupEventHandlers(): void {
        this.socket.addEventListener('open', () => {
            this.setState(ConnectionState.OPEN);
            this.emit('open');
        });

        this.socket.addEventListener('close', (event) => {
            this.setState(ConnectionState.CLOSED);
            this.emit('close', event);
        });

        this.socket.addEventListener('error', (error) => {
            this.emit('error', error);
        });

        this.socket.addEventListener('message', (event) => {
            this.emit('message', event.data);
        });
    }

    private setState(newState: ConnectionState): void {
        const oldState = this.state;
        this.state = newState;
        this.emit('stateChange', { oldState, newState });
    }

    on(event: string, listener: Function): void {
        if (!this.listeners.has(event)) {
            this.listeners.set(event, []);
        }
        this.listeners.get(event)!.push(listener);
    }

    off(event: string, listener: Function): void {
        const listeners = this.listeners.get(event);
        if (listeners) {
            const index = listeners.indexOf(listener);
            if (index !== -1) {
                listeners.splice(index, 1);
            }
        }
    }

    private emit(event: string, ...args: any[]): void {
        const listeners = this.listeners.get(event);
        if (listeners) {
            listeners.forEach(listener => listener(...args));
        }
    }

    getState(): ConnectionState {
        return this.state;
    }

    isConnected(): boolean {
        return this.state === ConnectionState.OPEN;
    }
}
```

## Best Practices

1. **Always validate messages**: Use JSON schema validation for incoming messages
2. **Implement heartbeat**: Use ping/pong to detect broken connections
3. **Handle reconnection**: Implement exponential backoff for reconnection attempts
4. **Type safety**: Use TypeScript interfaces for all message types
5. **Error boundaries**: Wrap WebSocket operations in try-catch blocks
6. **Resource cleanup**: Always close connections and clear timers
7. **Security**: Validate and sanitize all incoming data
8. **Rate limiting**: Implement rate limiting to prevent abuse

This comprehensive guide provides the foundation for building robust TypeScript WebSocket applications with proper error handling, type safety, and production-ready patterns.