# TypeScript WebSocket Production Patterns

**Source**: Production WebSocket scaling research compilation  
**Scraped**: 2025-07-18  
**Backend Focus**: Production-ready server implementations  
**Trust Score**: 9.4 (Industry best practices compilation)  

## Table of Contents

1. [Production Architecture Overview](#production-architecture-overview)
2. [Load Balancing Strategies](#load-balancing-strategies)
3. [Horizontal Scaling Patterns](#horizontal-scaling-patterns)
4. [Session Management](#session-management)
5. [Message Broker Integration](#message-broker-integration)
6. [Connection Management](#connection-management)
7. [Health Monitoring](#health-monitoring)
8. [Rate Limiting and Backpressure](#rate-limiting-and-backpressure)
9. [Graceful Shutdown](#graceful-shutdown)
10. [Performance Optimization](#performance-optimization)
11. [Monitoring and Observability](#monitoring-and-observability)
12. [Deployment Strategies](#deployment-strategies)

## Production Architecture Overview

### Microservices Architecture

```typescript
// Service interface for WebSocket microservices
interface WebSocketService {
    id: string;
    name: string;
    version: string;
    health: HealthStatus;
    connections: ConnectionPool;
    messageHandler: MessageHandler;
    
    start(): Promise<void>;
    stop(): Promise<void>;
    healthCheck(): Promise<HealthStatus>;
}

// Production WebSocket server implementation
class ProductionWebSocketServer implements WebSocketService {
    id: string;
    name: string;
    version: string;
    
    private server: WebSocket.Server;
    private connectionPool: ConnectionPool;
    private messageHandler: MessageHandler;
    private healthMonitor: HealthMonitor;
    private rateLimiter: RateLimiter;
    private messageBroker: MessageBroker;
    
    constructor(config: ServerConfig) {
        this.id = config.id;
        this.name = config.name;
        this.version = config.version;
        
        this.connectionPool = new ConnectionPool(config.connectionPoolConfig);
        this.messageHandler = new MessageHandler(config.messageHandlerConfig);
        this.healthMonitor = new HealthMonitor(config.healthConfig);
        this.rateLimiter = new RateLimiter(config.rateLimitConfig);
        this.messageBroker = new MessageBroker(config.messageBrokerConfig);
    }
    
    async start(): Promise<void> {
        await this.messageBroker.connect();
        await this.startWebSocketServer();
        await this.healthMonitor.start();
        
        console.log(`WebSocket service ${this.name} started on port ${this.config.port}`);
    }
    
    async stop(): Promise<void> {
        await this.gracefulShutdown();
        console.log(`WebSocket service ${this.name} stopped`);
    }
    
    async healthCheck(): Promise<HealthStatus> {
        return await this.healthMonitor.getStatus();
    }
}
```

### Service Configuration

```typescript
interface ServerConfig {
    id: string;
    name: string;
    version: string;
    port: number;
    host: string;
    
    // Connection configuration
    connectionPoolConfig: ConnectionPoolConfig;
    messageHandlerConfig: MessageHandlerConfig;
    healthConfig: HealthConfig;
    rateLimitConfig: RateLimitConfig;
    messageBrokerConfig: MessageBrokerConfig;
    
    // Security configuration
    cors: CorsConfig;
    authentication: AuthConfig;
    
    // Performance configuration
    maxConnections: number;
    maxMessageSize: number;
    heartbeatInterval: number;
    
    // Logging configuration
    logging: LoggingConfig;
}

interface ConnectionPoolConfig {
    maxConnections: number;
    connectionTimeout: number;
    idleTimeout: number;
    cleanupInterval: number;
}
```

## Load Balancing Strategies

### TCP-Aware Load Balancing

```typescript
// Load balancer configuration for WebSocket servers
interface LoadBalancerConfig {
    strategy: 'round_robin' | 'least_connections' | 'ip_hash' | 'weighted';
    healthCheck: HealthCheckConfig;
    stickySession: boolean;
    timeout: number;
}

class WebSocketLoadBalancer {
    private servers: Map<string, ServerInfo> = new Map();
    private strategy: LoadBalancingStrategy;
    
    constructor(private config: LoadBalancerConfig) {
        this.strategy = this.createStrategy(config.strategy);
    }
    
    private createStrategy(type: string): LoadBalancingStrategy {
        switch (type) {
            case 'round_robin':
                return new RoundRobinStrategy();
            case 'least_connections':
                return new LeastConnectionsStrategy();
            case 'ip_hash':
                return new IpHashStrategy();
            case 'weighted':
                return new WeightedStrategy();
            default:
                throw new Error(`Unknown load balancing strategy: ${type}`);
        }
    }
    
    selectServer(request: IncomingMessage): ServerInfo | null {
        const healthyServers = Array.from(this.servers.values())
            .filter(server => server.health.status === 'healthy');
            
        if (healthyServers.length === 0) {
            return null;
        }
        
        return this.strategy.select(healthyServers, request);
    }
    
    addServer(server: ServerInfo): void {
        this.servers.set(server.id, server);
    }
    
    removeServer(serverId: string): void {
        this.servers.delete(serverId);
    }
    
    async healthCheck(): Promise<void> {
        const promises = Array.from(this.servers.values()).map(async (server) => {
            try {
                const response = await fetch(`http://${server.host}:${server.port}/health`);
                server.health.status = response.ok ? 'healthy' : 'unhealthy';
                server.health.lastCheck = Date.now();
            } catch (error) {
                server.health.status = 'unhealthy';
                server.health.lastCheck = Date.now();
                server.health.error = error.message;
            }
        });
        
        await Promise.all(promises);
    }
}

// Least connections strategy implementation
class LeastConnectionsStrategy implements LoadBalancingStrategy {
    select(servers: ServerInfo[], request: IncomingMessage): ServerInfo {
        return servers.reduce((prev, current) => 
            current.connectionCount < prev.connectionCount ? current : prev
        );
    }
}
```

### Nginx Configuration for WebSocket Load Balancing

```nginx
# nginx.conf for WebSocket load balancing
upstream websocket_backend {
    least_conn;
    server 127.0.0.1:8001 weight=1 max_fails=3 fail_timeout=30s;
    server 127.0.0.1:8002 weight=1 max_fails=3 fail_timeout=30s;
    server 127.0.0.1:8003 weight=1 max_fails=3 fail_timeout=30s;
}

server {
    listen 80;
    server_name websocket.example.com;
    
    location /ws {
        proxy_pass http://websocket_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket specific settings
        proxy_read_timeout 86400;
        proxy_connect_timeout 60;
        proxy_send_timeout 60;
        
        # Disable buffering for real-time communication
        proxy_buffering off;
        proxy_cache off;
    }
    
    location /health {
        proxy_pass http://websocket_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
```

## Horizontal Scaling Patterns

### Redis Pub/Sub Integration

```typescript
import Redis from 'ioredis';

class RedisMessageBroker implements MessageBroker {
    private publisher: Redis;
    private subscriber: Redis;
    private messageHandlers: Map<string, MessageHandler[]> = new Map();
    
    constructor(private config: RedisConfig) {
        this.publisher = new Redis(config.connectionString);
        this.subscriber = new Redis(config.connectionString);
        this.setupSubscriber();
    }
    
    private setupSubscriber(): void {
        this.subscriber.on('message', (channel, message) => {
            try {
                const parsedMessage = JSON.parse(message);
                this.handleMessage(channel, parsedMessage);
            } catch (error) {
                console.error('Failed to parse Redis message:', error);
            }
        });
    }
    
    async publish(channel: string, message: any): Promise<void> {
        await this.publisher.publish(channel, JSON.stringify(message));
    }
    
    async subscribe(channel: string, handler: MessageHandler): Promise<void> {
        if (!this.messageHandlers.has(channel)) {
            this.messageHandlers.set(channel, []);
            await this.subscriber.subscribe(channel);
        }
        
        this.messageHandlers.get(channel)!.push(handler);
    }
    
    async unsubscribe(channel: string, handler: MessageHandler): Promise<void> {
        const handlers = this.messageHandlers.get(channel);
        if (handlers) {
            const index = handlers.indexOf(handler);
            if (index !== -1) {
                handlers.splice(index, 1);
            }
            
            if (handlers.length === 0) {
                this.messageHandlers.delete(channel);
                await this.subscriber.unsubscribe(channel);
            }
        }
    }
    
    private handleMessage(channel: string, message: any): void {
        const handlers = this.messageHandlers.get(channel);
        if (handlers) {
            handlers.forEach(handler => handler(message));
        }
    }
}

// Message distribution across multiple servers
class DistributedMessageHandler {
    constructor(
        private messageBroker: MessageBroker,
        private localConnections: ConnectionPool
    ) {}
    
    async handleIncomingMessage(clientId: string, message: any): Promise<void> {
        // Process message locally
        await this.processMessage(clientId, message);
        
        // Distribute to other servers if needed
        if (this.shouldDistribute(message)) {
            await this.messageBroker.publish('global_messages', {
                serverId: process.env.SERVER_ID,
                clientId,
                message,
                timestamp: Date.now()
            });
        }
    }
    
    private shouldDistribute(message: any): boolean {
        // Determine if message should be distributed to other servers
        return message.type === 'broadcast' || message.type === 'room_message';
    }
    
    private async processMessage(clientId: string, message: any): Promise<void> {
        const connection = this.localConnections.get(clientId);
        if (connection) {
            await connection.send(message);
        }
    }
}
```

### Shared State Management

```typescript
// Distributed session store
class DistributedSessionStore {
    private redis: Redis;
    private localCache: Map<string, SessionData> = new Map();
    
    constructor(redisConfig: RedisConfig) {
        this.redis = new Redis(redisConfig.connectionString);
        this.setupCacheInvalidation();
    }
    
    private setupCacheInvalidation(): void {
        const subscriber = new Redis(this.redis.options);
        subscriber.subscribe('session_invalidation');
        
        subscriber.on('message', (channel, sessionId) => {
            if (channel === 'session_invalidation') {
                this.localCache.delete(sessionId);
            }
        });
    }
    
    async set(sessionId: string, data: SessionData): Promise<void> {
        // Update Redis
        await this.redis.setex(
            `session:${sessionId}`,
            data.ttl || 3600,
            JSON.stringify(data)
        );
        
        // Update local cache
        this.localCache.set(sessionId, data);
        
        // Invalidate cache on other servers
        await this.redis.publish('session_invalidation', sessionId);
    }
    
    async get(sessionId: string): Promise<SessionData | null> {
        // Check local cache first
        let sessionData = this.localCache.get(sessionId);
        if (sessionData) {
            return sessionData;
        }
        
        // Fetch from Redis
        const data = await this.redis.get(`session:${sessionId}`);
        if (data) {
            sessionData = JSON.parse(data);
            this.localCache.set(sessionId, sessionData);
            return sessionData;
        }
        
        return null;
    }
    
    async delete(sessionId: string): Promise<void> {
        await this.redis.del(`session:${sessionId}`);
        this.localCache.delete(sessionId);
        await this.redis.publish('session_invalidation', sessionId);
    }
}
```

## Session Management

### Sticky vs Non-Sticky Sessions

```typescript
// Non-sticky session implementation
class NonStickySessionManager {
    private sessionStore: DistributedSessionStore;
    private connectionRegistry: ConnectionRegistry;
    
    constructor(
        sessionStore: DistributedSessionStore,
        connectionRegistry: ConnectionRegistry
    ) {
        this.sessionStore = sessionStore;
        this.connectionRegistry = connectionRegistry;
    }
    
    async handleConnection(socket: WebSocket, sessionId: string): Promise<void> {
        // Load session data from distributed store
        const sessionData = await this.sessionStore.get(sessionId);
        if (!sessionData) {
            socket.close(1002, 'Invalid session');
            return;
        }
        
        // Register connection with any available server
        await this.connectionRegistry.register(sessionId, {
            serverId: process.env.SERVER_ID!,
            socket,
            sessionData
        });
        
        // Send session restoration data
        await this.sendSessionRestoration(socket, sessionData);
    }
    
    private async sendSessionRestoration(socket: WebSocket, sessionData: SessionData): Promise<void> {
        const restorationMessage = {
            type: 'session_restored',
            data: {
                userId: sessionData.userId,
                subscriptions: sessionData.subscriptions,
                lastActivity: sessionData.lastActivity
            },
            timestamp: Date.now()
        };
        
        socket.send(JSON.stringify(restorationMessage));
    }
}

// Connection registry for tracking connections across servers
class ConnectionRegistry {
    private redis: Redis;
    
    constructor(redisConfig: RedisConfig) {
        this.redis = new Redis(redisConfig.connectionString);
    }
    
    async register(sessionId: string, connection: ConnectionInfo): Promise<void> {
        await this.redis.hset(
            'connections',
            sessionId,
            JSON.stringify({
                serverId: connection.serverId,
                connectedAt: Date.now()
            })
        );
    }
    
    async unregister(sessionId: string): Promise<void> {
        await this.redis.hdel('connections', sessionId);
    }
    
    async getConnection(sessionId: string): Promise<ConnectionInfo | null> {
        const data = await this.redis.hget('connections', sessionId);
        return data ? JSON.parse(data) : null;
    }
    
    async getServerConnections(serverId: string): Promise<string[]> {
        const connections = await this.redis.hgetall('connections');
        return Object.keys(connections).filter(sessionId => {
            const info = JSON.parse(connections[sessionId]);
            return info.serverId === serverId;
        });
    }
}
```

## Message Broker Integration

### Kafka Integration

```typescript
import { Kafka, Producer, Consumer } from 'kafkajs';

class KafkaMessageBroker implements MessageBroker {
    private kafka: Kafka;
    private producer: Producer;
    private consumer: Consumer;
    private topicHandlers: Map<string, MessageHandler[]> = new Map();
    
    constructor(private config: KafkaConfig) {
        this.kafka = new Kafka({
            clientId: config.clientId,
            brokers: config.brokers
        });
        
        this.producer = this.kafka.producer();
        this.consumer = this.kafka.consumer({ groupId: config.groupId });
    }
    
    async connect(): Promise<void> {
        await this.producer.connect();
        await this.consumer.connect();
        
        this.consumer.on('consumer.crash', (error) => {
            console.error('Kafka consumer crashed:', error);
        });
        
        await this.consumer.run({
            eachMessage: async ({ topic, partition, message }) => {
                try {
                    const parsedMessage = JSON.parse(message.value!.toString());
                    await this.handleMessage(topic, parsedMessage);
                } catch (error) {
                    console.error('Failed to process Kafka message:', error);
                }
            }
        });
    }
    
    async publish(topic: string, message: any): Promise<void> {
        await this.producer.send({
            topic,
            messages: [{
                value: JSON.stringify(message),
                timestamp: Date.now().toString()
            }]
        });
    }
    
    async subscribe(topic: string, handler: MessageHandler): Promise<void> {
        if (!this.topicHandlers.has(topic)) {
            this.topicHandlers.set(topic, []);
            await this.consumer.subscribe({ topic });
        }
        
        this.topicHandlers.get(topic)!.push(handler);
    }
    
    private async handleMessage(topic: string, message: any): Promise<void> {
        const handlers = this.topicHandlers.get(topic);
        if (handlers) {
            await Promise.all(handlers.map(handler => handler(message)));
        }
    }
}
```

## Connection Management

### Connection Pool Implementation

```typescript
class ConnectionPool {
    private connections: Map<string, ConnectionInfo> = new Map();
    private stats: ConnectionStats = {
        total: 0,
        active: 0,
        idle: 0,
        errors: 0
    };
    
    constructor(private config: ConnectionPoolConfig) {
        this.startCleanupInterval();
    }
    
    add(id: string, socket: WebSocket, metadata: ConnectionMetadata): void {
        const connection: ConnectionInfo = {
            id,
            socket,
            metadata,
            createdAt: Date.now(),
            lastActivity: Date.now(),
            messageCount: 0,
            isActive: true
        };
        
        this.connections.set(id, connection);
        this.stats.total++;
        this.stats.active++;
        
        this.setupSocketHandlers(connection);
    }
    
    private setupSocketHandlers(connection: ConnectionInfo): void {
        connection.socket.on('message', () => {
            connection.lastActivity = Date.now();
            connection.messageCount++;
        });
        
        connection.socket.on('close', () => {
            this.remove(connection.id);
        });
        
        connection.socket.on('error', (error) => {
            console.error(`Connection error for ${connection.id}:`, error);
            this.stats.errors++;
            this.remove(connection.id);
        });
    }
    
    remove(id: string): void {
        const connection = this.connections.get(id);
        if (connection) {
            this.connections.delete(id);
            this.stats.total--;
            if (connection.isActive) {
                this.stats.active--;
            } else {
                this.stats.idle--;
            }
        }
    }
    
    get(id: string): ConnectionInfo | undefined {
        return this.connections.get(id);
    }
    
    getAll(): ConnectionInfo[] {
        return Array.from(this.connections.values());
    }
    
    getStats(): ConnectionStats {
        return { ...this.stats };
    }
    
    private startCleanupInterval(): void {
        setInterval(() => {
            this.cleanup();
        }, this.config.cleanupInterval);
    }
    
    private cleanup(): void {
        const now = Date.now();
        const connectionsToRemove: string[] = [];
        
        for (const [id, connection] of this.connections) {
            // Remove idle connections
            if (now - connection.lastActivity > this.config.idleTimeout) {
                connectionsToRemove.push(id);
                continue;
            }
            
            // Update active/idle status
            const wasActive = connection.isActive;
            connection.isActive = now - connection.lastActivity < 30000; // 30 seconds
            
            if (wasActive && !connection.isActive) {
                this.stats.active--;
                this.stats.idle++;
            } else if (!wasActive && connection.isActive) {
                this.stats.idle--;
                this.stats.active++;
            }
        }
        
        // Remove idle connections
        connectionsToRemove.forEach(id => this.remove(id));
    }
}
```

## Health Monitoring

### Comprehensive Health Checks

```typescript
interface HealthStatus {
    status: 'healthy' | 'degraded' | 'unhealthy';
    timestamp: number;
    checks: HealthCheck[];
    metrics: HealthMetrics;
}

interface HealthCheck {
    name: string;
    status: 'pass' | 'fail' | 'warn';
    duration: number;
    error?: string;
}

class HealthMonitor {
    private checks: Map<string, HealthCheckFunction> = new Map();
    private lastStatus: HealthStatus | null = null;
    
    constructor(private config: HealthConfig) {
        this.registerDefaultChecks();
    }
    
    private registerDefaultChecks(): void {
        this.checks.set('database', this.checkDatabase.bind(this));
        this.checks.set('redis', this.checkRedis.bind(this));
        this.checks.set('memory', this.checkMemory.bind(this));
        this.checks.set('connections', this.checkConnections.bind(this));
        this.checks.set('message_broker', this.checkMessageBroker.bind(this));
    }
    
    async getStatus(): Promise<HealthStatus> {
        const checkPromises = Array.from(this.checks.entries()).map(async ([name, checkFn]) => {
            const startTime = Date.now();
            try {
                await checkFn();
                return {
                    name,
                    status: 'pass' as const,
                    duration: Date.now() - startTime
                };
            } catch (error) {
                return {
                    name,
                    status: 'fail' as const,
                    duration: Date.now() - startTime,
                    error: error.message
                };
            }
        });
        
        const checks = await Promise.all(checkPromises);
        const failedChecks = checks.filter(check => check.status === 'fail');
        
        let status: 'healthy' | 'degraded' | 'unhealthy';
        if (failedChecks.length === 0) {
            status = 'healthy';
        } else if (failedChecks.length <= 1) {
            status = 'degraded';
        } else {
            status = 'unhealthy';
        }
        
        const healthStatus: HealthStatus = {
            status,
            timestamp: Date.now(),
            checks,
            metrics: await this.collectMetrics()
        };
        
        this.lastStatus = healthStatus;
        return healthStatus;
    }
    
    private async checkDatabase(): Promise<void> {
        // Implement database health check
        // e.g., simple query to verify connectivity
    }
    
    private async checkRedis(): Promise<void> {
        // Implement Redis health check
        // e.g., ping command
    }
    
    private async checkMemory(): Promise<void> {
        const memoryUsage = process.memoryUsage();
        const maxMemory = 1024 * 1024 * 1024; // 1GB
        
        if (memoryUsage.heapUsed > maxMemory * 0.9) {
            throw new Error('Memory usage too high');
        }
    }
    
    private async checkConnections(): Promise<void> {
        // Check connection pool health
        const stats = this.connectionPool.getStats();
        
        if (stats.errors > 100) {
            throw new Error('Too many connection errors');
        }
    }
    
    private async checkMessageBroker(): Promise<void> {
        // Check message broker connectivity
        // Implementation depends on broker type
    }
    
    private async collectMetrics(): Promise<HealthMetrics> {
        return {
            uptime: process.uptime(),
            memory: process.memoryUsage(),
            cpu: process.cpuUsage(),
            connections: this.connectionPool.getStats(),
            timestamp: Date.now()
        };
    }
}
```

## Rate Limiting and Backpressure

### Token Bucket Rate Limiter

```typescript
class TokenBucketRateLimiter {
    private buckets: Map<string, TokenBucket> = new Map();
    
    constructor(private config: RateLimitConfig) {}
    
    async checkLimit(clientId: string): Promise<boolean> {
        const bucket = this.getBucket(clientId);
        return bucket.consume();
    }
    
    private getBucket(clientId: string): TokenBucket {
        let bucket = this.buckets.get(clientId);
        if (!bucket) {
            bucket = new TokenBucket(
                this.config.maxTokens,
                this.config.refillRate,
                this.config.refillInterval
            );
            this.buckets.set(clientId, bucket);
        }
        return bucket;
    }
    
    cleanup(): void {
        const now = Date.now();
        for (const [clientId, bucket] of this.buckets) {
            if (now - bucket.lastActivity > this.config.cleanupInterval) {
                this.buckets.delete(clientId);
            }
        }
    }
}

class TokenBucket {
    private tokens: number;
    private lastRefill: number;
    public lastActivity: number;
    
    constructor(
        private maxTokens: number,
        private refillRate: number,
        private refillInterval: number
    ) {
        this.tokens = maxTokens;
        this.lastRefill = Date.now();
        this.lastActivity = Date.now();
    }
    
    consume(tokens: number = 1): boolean {
        this.refill();
        this.lastActivity = Date.now();
        
        if (this.tokens >= tokens) {
            this.tokens -= tokens;
            return true;
        }
        
        return false;
    }
    
    private refill(): void {
        const now = Date.now();
        const timePassed = now - this.lastRefill;
        const tokensToAdd = Math.floor(timePassed / this.refillInterval) * this.refillRate;
        
        this.tokens = Math.min(this.maxTokens, this.tokens + tokensToAdd);
        this.lastRefill = now;
    }
}
```

### Backpressure Management

```typescript
class BackpressureManager {
    private queues: Map<string, MessageQueue> = new Map();
    private metrics: BackpressureMetrics = {
        queueSizes: new Map(),
        droppedMessages: 0,
        slowClients: new Set()
    };
    
    constructor(private config: BackpressureConfig) {}
    
    async enqueue(clientId: string, message: any): Promise<boolean> {
        const queue = this.getQueue(clientId);
        
        if (queue.size >= this.config.maxQueueSize) {
            // Handle backpressure
            return this.handleBackpressure(clientId, message);
        }
        
        queue.enqueue(message);
        this.metrics.queueSizes.set(clientId, queue.size);
        return true;
    }
    
    private handleBackpressure(clientId: string, message: any): boolean {
        switch (this.config.strategy) {
            case 'drop_oldest':
                const queue = this.getQueue(clientId);
                queue.dequeue(); // Remove oldest message
                queue.enqueue(message);
                return true;
                
            case 'drop_new':
                this.metrics.droppedMessages++;
                return false;
                
            case 'slow_client':
                this.metrics.slowClients.add(clientId);
                return false;
                
            default:
                return false;
        }
    }
    
    private getQueue(clientId: string): MessageQueue {
        let queue = this.queues.get(clientId);
        if (!queue) {
            queue = new MessageQueue();
            this.queues.set(clientId, queue);
        }
        return queue;
    }
    
    getMetrics(): BackpressureMetrics {
        return {
            queueSizes: new Map(this.metrics.queueSizes),
            droppedMessages: this.metrics.droppedMessages,
            slowClients: new Set(this.metrics.slowClients)
        };
    }
}
```

## Graceful Shutdown

### Shutdown Manager

```typescript
class GracefulShutdownManager {
    private shutdownInProgress = false;
    private shutdownTimeout: NodeJS.Timeout | null = null;
    private connections: ConnectionPool;
    private server: WebSocket.Server;
    
    constructor(
        connections: ConnectionPool,
        server: WebSocket.Server,
        private config: ShutdownConfig
    ) {
        this.connections = connections;
        this.server = server;
        this.setupSignalHandlers();
    }
    
    private setupSignalHandlers(): void {
        process.on('SIGTERM', () => this.initiateShutdown('SIGTERM'));
        process.on('SIGINT', () => this.initiateShutdown('SIGINT'));
        process.on('SIGQUIT', () => this.initiateShutdown('SIGQUIT'));
    }
    
    private async initiateShutdown(signal: string): Promise<void> {
        if (this.shutdownInProgress) {
            console.log('Shutdown already in progress');
            return;
        }
        
        this.shutdownInProgress = true;
        console.log(`Received ${signal}, starting graceful shutdown...`);
        
        // Set a timeout for forced shutdown
        this.shutdownTimeout = setTimeout(() => {
            console.log('Graceful shutdown timeout, forcing exit');
            process.exit(1);
        }, this.config.timeout);
        
        try {
            await this.performShutdown();
            console.log('Graceful shutdown completed');
            process.exit(0);
        } catch (error) {
            console.error('Error during shutdown:', error);
            process.exit(1);
        }
    }
    
    private async performShutdown(): Promise<void> {
        // 1. Stop accepting new connections
        this.server.close();
        
        // 2. Send shutdown notice to all clients
        await this.notifyClientsOfShutdown();
        
        // 3. Wait for clients to disconnect gracefully
        await this.waitForClientDisconnection();
        
        // 4. Force close remaining connections
        await this.forceCloseConnections();
        
        // 5. Clean up resources
        await this.cleanup();
        
        if (this.shutdownTimeout) {
            clearTimeout(this.shutdownTimeout);
        }
    }
    
    private async notifyClientsOfShutdown(): Promise<void> {
        const shutdownMessage = {
            type: 'server_shutdown',
            data: {
                reason: 'Server maintenance',
                gracePeriod: this.config.gracePeriod
            },
            timestamp: Date.now()
        };
        
        const connections = this.connections.getAll();
        const notifications = connections.map(connection => {
            return new Promise<void>((resolve) => {
                try {
                    connection.socket.send(JSON.stringify(shutdownMessage));
                    resolve();
                } catch (error) {
                    resolve(); // Continue even if send fails
                }
            });
        });
        
        await Promise.all(notifications);
    }
    
    private async waitForClientDisconnection(): Promise<void> {
        const startTime = Date.now();
        
        while (this.connections.getStats().total > 0) {
            if (Date.now() - startTime > this.config.gracePeriod) {
                console.log('Grace period expired, forcing disconnection');
                break;
            }
            
            await new Promise(resolve => setTimeout(resolve, 100));
        }
    }
    
    private async forceCloseConnections(): Promise<void> {
        const connections = this.connections.getAll();
        const closePromises = connections.map(connection => {
            return new Promise<void>((resolve) => {
                connection.socket.close();
                resolve();
            });
        });
        
        await Promise.all(closePromises);
    }
    
    private async cleanup(): Promise<void> {
        // Close database connections, message brokers, etc.
        // Implementation depends on specific resources
    }
}
```

## Performance Optimization

### Message Batching

```typescript
class MessageBatcher {
    private batches: Map<string, MessageBatch> = new Map();
    private flushInterval: NodeJS.Timeout;
    
    constructor(private config: BatchingConfig) {
        this.flushInterval = setInterval(() => {
            this.flushAll();
        }, config.flushInterval);
    }
    
    addMessage(clientId: string, message: any): void {
        let batch = this.batches.get(clientId);
        if (!batch) {
            batch = {
                messages: [],
                createdAt: Date.now(),
                size: 0
            };
            this.batches.set(clientId, batch);
        }
        
        batch.messages.push(message);
        batch.size += this.estimateMessageSize(message);
        
        // Flush if batch is full
        if (batch.messages.length >= this.config.maxBatchSize ||
            batch.size >= this.config.maxBatchBytes) {
            this.flushBatch(clientId);
        }
    }
    
    private flushBatch(clientId: string): void {
        const batch = this.batches.get(clientId);
        if (!batch || batch.messages.length === 0) return;
        
        const connection = this.connections.get(clientId);
        if (connection && connection.socket.readyState === WebSocket.OPEN) {
            const batchMessage = {
                type: 'batch',
                data: batch.messages,
                timestamp: Date.now()
            };
            
            connection.socket.send(JSON.stringify(batchMessage));
        }
        
        this.batches.delete(clientId);
    }
    
    private flushAll(): void {
        const now = Date.now();
        
        for (const [clientId, batch] of this.batches) {
            if (now - batch.createdAt >= this.config.maxBatchAge) {
                this.flushBatch(clientId);
            }
        }
    }
    
    private estimateMessageSize(message: any): number {
        return JSON.stringify(message).length;
    }
}
```

This comprehensive production patterns document provides the foundation for building scalable, reliable TypeScript WebSocket applications that can handle production workloads with proper error handling, monitoring, and resource management.