# TypeScript WebSocket Authentication & Security

**Source**: Security best practices compilation for WebSocket implementations  
**Scraped**: 2025-07-18  
**Backend Focus**: Production-ready security patterns  
**Trust Score**: 9.6 (Security industry standards)  

## Table of Contents

1. [Security Architecture Overview](#security-architecture-overview)
2. [Authentication Patterns](#authentication-patterns)
3. [Authorization Models](#authorization-models)
4. [Token Management](#token-management)
5. [Connection Security](#connection-security)
6. [Input Validation](#input-validation)
7. [Rate Limiting](#rate-limiting)
8. [CSRF Protection](#csrf-protection)
9. [TLS/SSL Implementation](#tlsssl-implementation)
10. [Security Headers](#security-headers)
11. [Audit Logging](#audit-logging)
12. [Threat Protection](#threat-protection)

## Security Architecture Overview

### Defense in Depth Strategy

```typescript
interface SecurityLayer {
    name: string;
    priority: number;
    enabled: boolean;
    validate(request: SecurityRequest): Promise<SecurityResult>;
}

class SecurityManager {
    private layers: SecurityLayer[] = [];
    private auditLogger: AuditLogger;
    
    constructor(config: SecurityConfig) {
        this.auditLogger = new AuditLogger(config.auditConfig);
        this.initializeSecurityLayers(config);
    }
    
    private initializeSecurityLayers(config: SecurityConfig): void {
        this.layers = [
            new RateLimitingLayer(config.rateLimiting),
            new AuthenticationLayer(config.authentication),
            new AuthorizationLayer(config.authorization),
            new InputValidationLayer(config.validation),
            new CSRFProtectionLayer(config.csrf),
            new ThreatDetectionLayer(config.threatDetection)
        ].sort((a, b) => a.priority - b.priority);
    }
    
    async validateRequest(request: SecurityRequest): Promise<SecurityResult> {
        const results: SecurityResult[] = [];
        
        for (const layer of this.layers) {
            if (!layer.enabled) continue;
            
            const result = await layer.validate(request);
            results.push(result);
            
            if (!result.passed) {
                await this.auditLogger.logSecurityEvent({
                    type: 'security_violation',
                    layer: layer.name,
                    request: request.metadata,
                    result: result,
                    timestamp: Date.now()
                });
                
                return result;
            }
        }
        
        return { passed: true, message: 'Security validation passed' };
    }
}
```

### Security Configuration

```typescript
interface SecurityConfig {
    authentication: AuthenticationConfig;
    authorization: AuthorizationConfig;
    rateLimiting: RateLimitingConfig;
    validation: ValidationConfig;
    csrf: CSRFConfig;
    threatDetection: ThreatDetectionConfig;
    auditConfig: AuditConfig;
    tls: TLSConfig;
}

interface AuthenticationConfig {
    required: boolean;
    methods: AuthMethod[];
    tokenExpiry: number;
    refreshTokenExpiry: number;
    secretKey: string;
    algorithms: string[];
}

interface AuthorizationConfig {
    rbac: RBACConfig;
    resources: ResourceConfig[];
    defaultPermissions: Permission[];
}
```

## Authentication Patterns

### JWT-Based Authentication

```typescript
import jwt from 'jsonwebtoken';
import bcrypt from 'bcrypt';

interface TokenPayload {
    userId: string;
    username: string;
    roles: string[];
    permissions: string[];
    sessionId: string;
    iat: number;
    exp: number;
}

class JWTAuthenticationService {
    private secretKey: string;
    private refreshSecretKey: string;
    private tokenExpiry: number;
    private refreshTokenExpiry: number;
    
    constructor(config: AuthenticationConfig) {
        this.secretKey = config.secretKey;
        this.refreshSecretKey = config.refreshSecretKey;
        this.tokenExpiry = config.tokenExpiry;
        this.refreshTokenExpiry = config.refreshTokenExpiry;
    }
    
    async authenticate(token: string): Promise<TokenPayload | null> {
        try {
            const payload = jwt.verify(token, this.secretKey) as TokenPayload;
            
            // Additional validation
            if (!payload.userId || !payload.sessionId) {
                return null;
            }
            
            // Check if token is blacklisted
            if (await this.isTokenBlacklisted(payload.sessionId)) {
                return null;
            }
            
            return payload;
        } catch (error) {
            console.error('JWT verification failed:', error);
            return null;
        }
    }
    
    generateTokens(user: UserInfo): TokenPair {
        const payload: Omit<TokenPayload, 'iat' | 'exp'> = {
            userId: user.id,
            username: user.username,
            roles: user.roles,
            permissions: user.permissions,
            sessionId: this.generateSessionId()
        };
        
        const accessToken = jwt.sign(payload, this.secretKey, {
            expiresIn: this.tokenExpiry
        });
        
        const refreshToken = jwt.sign(
            { sessionId: payload.sessionId },
            this.refreshSecretKey,
            { expiresIn: this.refreshTokenExpiry }
        );
        
        return { accessToken, refreshToken };
    }
    
    async refreshToken(refreshToken: string): Promise<TokenPair | null> {
        try {
            const payload = jwt.verify(refreshToken, this.refreshSecretKey) as { sessionId: string };
            
            // Check if refresh token is blacklisted
            if (await this.isTokenBlacklisted(payload.sessionId)) {
                return null;
            }
            
            // Get user info from session
            const user = await this.getUserFromSession(payload.sessionId);
            if (!user) {
                return null;
            }
            
            // Generate new tokens
            return this.generateTokens(user);
        } catch (error) {
            console.error('Refresh token verification failed:', error);
            return null;
        }
    }
    
    async revokeToken(sessionId: string): Promise<void> {
        await this.blacklistToken(sessionId);
    }
    
    private generateSessionId(): string {
        return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    private async isTokenBlacklisted(sessionId: string): Promise<boolean> {
        // Implementation depends on storage backend (Redis, database, etc.)
        return false;
    }
    
    private async blacklistToken(sessionId: string): Promise<void> {
        // Implementation depends on storage backend
    }
    
    private async getUserFromSession(sessionId: string): Promise<UserInfo | null> {
        // Implementation depends on storage backend
        return null;
    }
}
```

### WebSocket Authentication Middleware

```typescript
class WebSocketAuthenticationMiddleware {
    private authService: JWTAuthenticationService;
    private sessionStore: SessionStore;
    
    constructor(
        authService: JWTAuthenticationService,
        sessionStore: SessionStore
    ) {
        this.authService = authService;
        this.sessionStore = sessionStore;
    }
    
    async authenticateConnection(
        socket: WebSocket,
        request: IncomingMessage
    ): Promise<AuthenticatedConnection | null> {
        try {
            // Extract token from various sources
            const token = this.extractToken(request);
            if (!token) {
                return null;
            }
            
            // Verify token
            const payload = await this.authService.authenticate(token);
            if (!payload) {
                return null;
            }
            
            // Create authenticated connection
            const connection: AuthenticatedConnection = {
                socket,
                user: {
                    id: payload.userId,
                    username: payload.username,
                    roles: payload.roles,
                    permissions: payload.permissions
                },
                sessionId: payload.sessionId,
                authenticatedAt: Date.now()
            };
            
            // Store session
            await this.sessionStore.set(payload.sessionId, connection.user);
            
            return connection;
        } catch (error) {
            console.error('WebSocket authentication failed:', error);
            return null;
        }
    }
    
    private extractToken(request: IncomingMessage): string | null {
        // Try Authorization header
        const authHeader = request.headers.authorization;
        if (authHeader && authHeader.startsWith('Bearer ')) {
            return authHeader.substring(7);
        }
        
        // Try query parameter
        const url = new URL(request.url!, `http://${request.headers.host}`);
        const token = url.searchParams.get('token');
        if (token) {
            return token;
        }
        
        // Try cookie
        const cookies = this.parseCookies(request.headers.cookie);
        if (cookies.access_token) {
            return cookies.access_token;
        }
        
        return null;
    }
    
    private parseCookies(cookieHeader?: string): Record<string, string> {
        const cookies: Record<string, string> = {};
        
        if (cookieHeader) {
            cookieHeader.split(';').forEach(cookie => {
                const [name, value] = cookie.trim().split('=');
                if (name && value) {
                    cookies[name] = decodeURIComponent(value);
                }
            });
        }
        
        return cookies;
    }
}
```

## Authorization Models

### Role-Based Access Control (RBAC)

```typescript
interface Role {
    id: string;
    name: string;
    permissions: Permission[];
    inherits?: string[];
}

interface Permission {
    resource: string;
    action: string;
    conditions?: PermissionCondition[];
}

interface PermissionCondition {
    field: string;
    operator: 'equals' | 'contains' | 'startsWith' | 'regex';
    value: string;
}

class RBACAuthorizationService {
    private roles: Map<string, Role> = new Map();
    private userRoles: Map<string, string[]> = new Map();
    
    constructor(config: RBACConfig) {
        this.loadRoles(config.roles);
    }
    
    private loadRoles(roles: Role[]): void {
        roles.forEach(role => {
            this.roles.set(role.id, role);
        });
    }
    
    async authorize(
        userId: string,
        resource: string,
        action: string,
        context?: any
    ): Promise<boolean> {
        const userRoles = this.userRoles.get(userId) || [];
        
        for (const roleId of userRoles) {
            const role = this.roles.get(roleId);
            if (!role) continue;
            
            // Check direct permissions
            if (await this.hasPermission(role, resource, action, context)) {
                return true;
            }
            
            // Check inherited permissions
            if (role.inherits) {
                for (const inheritedRoleId of role.inherits) {
                    const inheritedRole = this.roles.get(inheritedRoleId);
                    if (inheritedRole && await this.hasPermission(inheritedRole, resource, action, context)) {
                        return true;
                    }
                }
            }
        }
        
        return false;
    }
    
    private async hasPermission(
        role: Role,
        resource: string,
        action: string,
        context?: any
    ): Promise<boolean> {
        for (const permission of role.permissions) {
            if (permission.resource === resource && permission.action === action) {
                // Check conditions if present
                if (permission.conditions) {
                    return await this.evaluateConditions(permission.conditions, context);
                }
                return true;
            }
        }
        return false;
    }
    
    private async evaluateConditions(
        conditions: PermissionCondition[],
        context?: any
    ): Promise<boolean> {
        if (!context) return false;
        
        for (const condition of conditions) {
            const value = context[condition.field];
            if (!value) return false;
            
            switch (condition.operator) {
                case 'equals':
                    if (value !== condition.value) return false;
                    break;
                case 'contains':
                    if (!value.includes(condition.value)) return false;
                    break;
                case 'startsWith':
                    if (!value.startsWith(condition.value)) return false;
                    break;
                case 'regex':
                    if (!new RegExp(condition.value).test(value)) return false;
                    break;
            }
        }
        
        return true;
    }
}
```

### WebSocket Authorization Middleware

```typescript
class WebSocketAuthorizationMiddleware {
    private authzService: RBACAuthorizationService;
    
    constructor(authzService: RBACAuthorizationService) {
        this.authzService = authzService;
    }
    
    async authorizeMessage(
        connection: AuthenticatedConnection,
        message: WebSocketMessage
    ): Promise<boolean> {
        const resource = this.extractResource(message);
        const action = this.extractAction(message);
        
        if (!resource || !action) {
            return false;
        }
        
        return await this.authzService.authorize(
            connection.user.id,
            resource,
            action,
            {
                message: message.data,
                connection: connection,
                timestamp: Date.now()
            }
        );
    }
    
    private extractResource(message: WebSocketMessage): string | null {
        // Extract resource from message type or data
        switch (message.type) {
            case 'join_room':
                return `room:${message.data.roomId}`;
            case 'send_message':
                return `room:${message.data.roomId}`;
            case 'get_user_data':
                return `user:${message.data.userId}`;
            default:
                return null;
        }
    }
    
    private extractAction(message: WebSocketMessage): string | null {
        // Extract action from message type
        switch (message.type) {
            case 'join_room':
                return 'join';
            case 'send_message':
                return 'send';
            case 'get_user_data':
                return 'read';
            default:
                return null;
        }
    }
}
```

## Token Management

### Token Refresh Strategy

```typescript
class TokenManager {
    private refreshThreshold: number = 300000; // 5 minutes before expiry
    private refreshTokens: Map<string, RefreshTokenInfo> = new Map();
    
    constructor(private authService: JWTAuthenticationService) {}
    
    async manageTokenRefresh(connection: AuthenticatedConnection): Promise<void> {
        const tokenExpiry = this.extractTokenExpiry(connection.sessionId);
        const timeUntilExpiry = tokenExpiry - Date.now();
        
        if (timeUntilExpiry <= this.refreshThreshold) {
            await this.refreshConnectionToken(connection);
        }
        
        // Schedule next refresh check
        setTimeout(() => {
            this.manageTokenRefresh(connection);
        }, Math.min(timeUntilExpiry / 2, 60000)); // Check every minute or halfway to expiry
    }
    
    private async refreshConnectionToken(connection: AuthenticatedConnection): Promise<void> {
        try {
            const refreshToken = this.refreshTokens.get(connection.sessionId);
            if (!refreshToken) {
                throw new Error('No refresh token available');
            }
            
            const newTokens = await this.authService.refreshToken(refreshToken.token);
            if (!newTokens) {
                throw new Error('Token refresh failed');
            }
            
            // Send new token to client
            const tokenRefreshMessage = {
                type: 'token_refresh',
                data: {
                    accessToken: newTokens.accessToken,
                    expiresIn: this.extractTokenExpiry(newTokens.accessToken) - Date.now()
                },
                timestamp: Date.now()
            };
            
            connection.socket.send(JSON.stringify(tokenRefreshMessage));
            
            // Update refresh token
            this.refreshTokens.set(connection.sessionId, {
                token: newTokens.refreshToken,
                expiresAt: Date.now() + this.authService.refreshTokenExpiry
            });
            
        } catch (error) {
            console.error('Token refresh failed:', error);
            // Close connection if refresh fails
            connection.socket.close(1008, 'Token refresh failed');
        }
    }
    
    private extractTokenExpiry(token: string): number {
        try {
            const payload = jwt.decode(token) as TokenPayload;
            return payload.exp * 1000;
        } catch (error) {
            return 0;
        }
    }
}
```

## Connection Security

### Secure WebSocket Server

```typescript
import https from 'https';
import fs from 'fs';

class SecureWebSocketServer {
    private server: https.Server;
    private wss: WebSocket.Server;
    
    constructor(private config: SecureServerConfig) {
        this.createSecureServer();
        this.setupWebSocketServer();
    }
    
    private createSecureServer(): void {
        const httpsOptions = {
            key: fs.readFileSync(this.config.tls.keyPath),
            cert: fs.readFileSync(this.config.tls.certPath),
            ca: this.config.tls.caPath ? fs.readFileSync(this.config.tls.caPath) : undefined,
            requestCert: this.config.tls.requestCert,
            rejectUnauthorized: this.config.tls.rejectUnauthorized,
            secureProtocol: 'TLSv1_2_method',
            ciphers: this.config.tls.ciphers,
            honorCipherOrder: true
        };
        
        this.server = https.createServer(httpsOptions);
    }
    
    private setupWebSocketServer(): void {
        this.wss = new WebSocket.Server({
            server: this.server,
            verifyClient: this.verifyClient.bind(this),
            handleProtocols: this.handleProtocols.bind(this),
            clientTracking: true,
            perMessageDeflate: {
                threshold: 1024,
                concurrencyLimit: 10,
                serverMaxWindow: 15
            }
        });
        
        this.wss.on('connection', this.handleConnection.bind(this));
    }
    
    private verifyClient(info: VerifyClientInfo): boolean {
        // Verify origin
        if (!this.isOriginAllowed(info.origin)) {
            return false;
        }
        
        // Check IP whitelist/blacklist
        if (!this.isIPAllowed(info.req.connection.remoteAddress)) {
            return false;
        }
        
        // Rate limiting by IP
        if (!this.checkConnectionRateLimit(info.req.connection.remoteAddress)) {
            return false;
        }
        
        return true;
    }
    
    private isOriginAllowed(origin: string): boolean {
        const allowedOrigins = this.config.security.allowedOrigins;
        return allowedOrigins.includes('*') || allowedOrigins.includes(origin);
    }
    
    private isIPAllowed(ip: string): boolean {
        const { whitelist, blacklist } = this.config.security.ipFiltering;
        
        if (blacklist.includes(ip)) {
            return false;
        }
        
        if (whitelist.length > 0 && !whitelist.includes(ip)) {
            return false;
        }
        
        return true;
    }
    
    private checkConnectionRateLimit(ip: string): boolean {
        // Implementation depends on rate limiting strategy
        return true;
    }
    
    private handleProtocols(protocols: string[]): string | false {
        const allowedProtocols = this.config.security.allowedProtocols;
        
        for (const protocol of protocols) {
            if (allowedProtocols.includes(protocol)) {
                return protocol;
            }
        }
        
        return false;
    }
    
    private async handleConnection(socket: WebSocket, request: IncomingMessage): Promise<void> {
        try {
            // Authenticate connection
            const connection = await this.authenticateConnection(socket, request);
            if (!connection) {
                socket.close(1008, 'Authentication failed');
                return;
            }
            
            // Setup secure message handling
            this.setupSecureMessageHandling(connection);
            
        } catch (error) {
            console.error('Connection handling failed:', error);
            socket.close(1011, 'Internal server error');
        }
    }
    
    private setupSecureMessageHandling(connection: AuthenticatedConnection): void {
        connection.socket.on('message', async (data: WebSocket.Data) => {
            try {
                // Validate message size
                if (data.length > this.config.security.maxMessageSize) {
                    throw new Error('Message too large');
                }
                
                // Parse and validate message
                const message = this.parseMessage(data);
                if (!message) {
                    throw new Error('Invalid message format');
                }
                
                // Authorize message
                const authorized = await this.authorizeMessage(connection, message);
                if (!authorized) {
                    throw new Error('Unauthorized message');
                }
                
                // Process message
                await this.processMessage(connection, message);
                
            } catch (error) {
                console.error('Message handling error:', error);
                connection.socket.send(JSON.stringify({
                    type: 'error',
                    data: { message: error.message },
                    timestamp: Date.now()
                }));
            }
        });
    }
}
```

## Input Validation

### Message Validation

```typescript
import Joi from 'joi';

class MessageValidator {
    private schemas: Map<string, Joi.Schema> = new Map();
    
    constructor() {
        this.setupValidationSchemas();
    }
    
    private setupValidationSchemas(): void {
        this.schemas.set('join_room', Joi.object({
            type: Joi.string().valid('join_room').required(),
            data: Joi.object({
                roomId: Joi.string().alphanum().min(1).max(50).required(),
                password: Joi.string().min(4).max(100).optional()
            }).required(),
            timestamp: Joi.number().integer().positive().required()
        }));
        
        this.schemas.set('send_message', Joi.object({
            type: Joi.string().valid('send_message').required(),
            data: Joi.object({
                roomId: Joi.string().alphanum().min(1).max(50).required(),
                content: Joi.string().min(1).max(1000).required(),
                messageType: Joi.string().valid('text', 'image', 'file').default('text')
            }).required(),
            timestamp: Joi.number().integer().positive().required()
        }));
        
        this.schemas.set('authenticate', Joi.object({
            type: Joi.string().valid('authenticate').required(),
            data: Joi.object({
                token: Joi.string().required()
            }).required(),
            timestamp: Joi.number().integer().positive().required()
        }));
    }
    
    validate(message: any): ValidationResult {
        try {
            // Basic structure validation
            if (!message || typeof message !== 'object') {
                return { valid: false, error: 'Invalid message format' };
            }
            
            if (!message.type || !message.data || !message.timestamp) {
                return { valid: false, error: 'Missing required fields' };
            }
            
            // Schema validation
            const schema = this.schemas.get(message.type);
            if (!schema) {
                return { valid: false, error: `Unknown message type: ${message.type}` };
            }
            
            const { error, value } = schema.validate(message);
            if (error) {
                return { valid: false, error: error.details[0].message };
            }
            
            // Additional security checks
            const securityCheck = this.performSecurityChecks(value);
            if (!securityCheck.valid) {
                return securityCheck;
            }
            
            return { valid: true, data: value };
            
        } catch (error) {
            return { valid: false, error: 'Validation error' };
        }
    }
    
    private performSecurityChecks(message: any): ValidationResult {
        // Check for potential XSS
        if (this.containsXSS(message)) {
            return { valid: false, error: 'Potential XSS detected' };
        }
        
        // Check for SQL injection patterns
        if (this.containsSQLInjection(message)) {
            return { valid: false, error: 'Potential SQL injection detected' };
        }
        
        // Check for path traversal
        if (this.containsPathTraversal(message)) {
            return { valid: false, error: 'Path traversal detected' };
        }
        
        return { valid: true };
    }
    
    private containsXSS(obj: any): boolean {
        const xssPatterns = [
            /<script[^>]*>.*?<\/script>/gi,
            /javascript:/gi,
            /on\w+\s*=/gi,
            /<iframe[^>]*>.*?<\/iframe>/gi
        ];
        
        const jsonStr = JSON.stringify(obj);
        return xssPatterns.some(pattern => pattern.test(jsonStr));
    }
    
    private containsSQLInjection(obj: any): boolean {
        const sqlPatterns = [
            /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/gi,
            /(\b(OR|AND)\b.*?[=<>])/gi,
            /[';"].*?[';"].*?[=<>]/gi
        ];
        
        const jsonStr = JSON.stringify(obj);
        return sqlPatterns.some(pattern => pattern.test(jsonStr));
    }
    
    private containsPathTraversal(obj: any): boolean {
        const pathPatterns = [
            /\.\.\//g,
            /\.\.\\\/g,
            /%2e%2e%2f/gi,
            /%2e%2e\\\/gi
        ];
        
        const jsonStr = JSON.stringify(obj);
        return pathPatterns.some(pattern => pattern.test(jsonStr));
    }
}
```

## Rate Limiting

### Advanced Rate Limiting

```typescript
class AdvancedRateLimiter {
    private limiters: Map<string, RateLimiter> = new Map();
    private violations: Map<string, ViolationTracker> = new Map();
    
    constructor(private config: RateLimitConfig) {}
    
    async checkLimit(
        clientId: string,
        messageType: string,
        clientIP: string
    ): Promise<RateLimitResult> {
        const limits = this.getLimitsForMessageType(messageType);
        
        for (const limit of limits) {
            const limiterId = `${clientId}:${limit.type}:${limit.window}`;
            let limiter = this.limiters.get(limiterId);
            
            if (!limiter) {
                limiter = new SlidingWindowRateLimiter(limit);
                this.limiters.set(limiterId, limiter);
            }
            
            const allowed = await limiter.checkLimit();
            if (!allowed) {
                await this.recordViolation(clientId, clientIP, messageType, limit);
                return {
                    allowed: false,
                    resetTime: limiter.getResetTime(),
                    remaining: limiter.getRemaining(),
                    limit: limit.maxRequests
                };
            }
        }
        
        return {
            allowed: true,
            resetTime: 0,
            remaining: 0,
            limit: 0
        };
    }
    
    private getLimitsForMessageType(messageType: string): RateLimit[] {
        const defaults = [
            { type: 'per_second', maxRequests: 10, window: 1000 },
            { type: 'per_minute', maxRequests: 100, window: 60000 },
            { type: 'per_hour', maxRequests: 1000, window: 3600000 }
        ];
        
        return this.config.messageTypeRules[messageType] || defaults;
    }
    
    private async recordViolation(
        clientId: string,
        clientIP: string,
        messageType: string,
        limit: RateLimit
    ): Promise<void> {
        const key = `${clientId}:${clientIP}`;
        let tracker = this.violations.get(key);
        
        if (!tracker) {
            tracker = {
                count: 0,
                firstViolation: Date.now(),
                lastViolation: Date.now(),
                violations: []
            };
            this.violations.set(key, tracker);
        }
        
        tracker.count++;
        tracker.lastViolation = Date.now();
        tracker.violations.push({
            timestamp: Date.now(),
            messageType,
            limit: limit.type
        });
        
        // Check if client should be temporarily banned
        if (tracker.count >= this.config.banThreshold) {
            await this.temporaryBan(clientId, clientIP, tracker);
        }
    }
    
    private async temporaryBan(
        clientId: string,
        clientIP: string,
        tracker: ViolationTracker
    ): Promise<void> {
        const banDuration = this.calculateBanDuration(tracker.count);
        
        // Store ban information
        await this.storeBan(clientId, clientIP, banDuration);
        
        console.log(`Temporarily banned client ${clientId} (${clientIP}) for ${banDuration}ms`);
    }
    
    private calculateBanDuration(violationCount: number): number {
        // Exponential backoff: 1 minute, 5 minutes, 15 minutes, 1 hour
        const baseDuration = 60000; // 1 minute
        return baseDuration * Math.pow(2, Math.min(violationCount - 1, 6));
    }
}
```

## CSRF Protection

### WebSocket CSRF Protection

```typescript
class CSRFProtection {
    private validTokens: Map<string, CSRFToken> = new Map();
    private secretKey: string;
    
    constructor(config: CSRFConfig) {
        this.secretKey = config.secretKey;
    }
    
    generateToken(sessionId: string): string {
        const token = this.createSecureToken();
        const expiry = Date.now() + 3600000; // 1 hour
        
        this.validTokens.set(token, {
            sessionId,
            createdAt: Date.now(),
            expiresAt: expiry
        });
        
        return token;
    }
    
    validateToken(token: string, sessionId: string): boolean {
        const tokenData = this.validTokens.get(token);
        
        if (!tokenData) {
            return false;
        }
        
        if (tokenData.expiresAt < Date.now()) {
            this.validTokens.delete(token);
            return false;
        }
        
        if (tokenData.sessionId !== sessionId) {
            return false;
        }
        
        return true;
    }
    
    private createSecureToken(): string {
        const timestamp = Date.now().toString();
        const randomBytes = crypto.randomBytes(16).toString('hex');
        const data = `${timestamp}:${randomBytes}`;
        
        const hmac = crypto.createHmac('sha256', this.secretKey);
        hmac.update(data);
        const signature = hmac.digest('hex');
        
        return `${Buffer.from(data).toString('base64')}.${signature}`;
    }
}
```

## Audit Logging

### Comprehensive Audit System

```typescript
class AuditLogger {
    private logger: winston.Logger;
    private logBuffer: AuditEvent[] = [];
    private flushInterval: NodeJS.Timeout;
    
    constructor(config: AuditConfig) {
        this.logger = winston.createLogger({
            level: config.logLevel,
            format: winston.format.combine(
                winston.format.timestamp(),
                winston.format.errors({ stack: true }),
                winston.format.json()
            ),
            transports: [
                new winston.transports.File({ filename: config.auditLogPath }),
                new winston.transports.Console()
            ]
        });
        
        this.flushInterval = setInterval(() => {
            this.flushLogs();
        }, config.flushInterval);
    }
    
    async logSecurityEvent(event: SecurityEvent): Promise<void> {
        const auditEvent: AuditEvent = {
            type: 'security',
            timestamp: Date.now(),
            event,
            severity: this.calculateSeverity(event),
            id: this.generateEventId()
        };
        
        this.logBuffer.push(auditEvent);
        
        if (auditEvent.severity === 'critical') {
            await this.flushLogs();
        }
    }
    
    async logAuthenticationEvent(event: AuthenticationEvent): Promise<void> {
        const auditEvent: AuditEvent = {
            type: 'authentication',
            timestamp: Date.now(),
            event,
            severity: event.success ? 'info' : 'warning',
            id: this.generateEventId()
        };
        
        this.logBuffer.push(auditEvent);
    }
    
    async logAuthorizationEvent(event: AuthorizationEvent): Promise<void> {
        const auditEvent: AuditEvent = {
            type: 'authorization',
            timestamp: Date.now(),
            event,
            severity: event.allowed ? 'info' : 'warning',
            id: this.generateEventId()
        };
        
        this.logBuffer.push(auditEvent);
    }
    
    private async flushLogs(): Promise<void> {
        if (this.logBuffer.length === 0) return;
        
        const logsToFlush = [...this.logBuffer];
        this.logBuffer = [];
        
        for (const event of logsToFlush) {
            this.logger.log(event.severity, 'Audit Event', event);
        }
    }
    
    private calculateSeverity(event: SecurityEvent): 'info' | 'warning' | 'error' | 'critical' {
        switch (event.type) {
            case 'rate_limit_violation':
                return 'warning';
            case 'authentication_failure':
                return 'warning';
            case 'authorization_failure':
                return 'warning';
            case 'malicious_input':
                return 'error';
            case 'brute_force_attack':
                return 'critical';
            case 'data_breach_attempt':
                return 'critical';
            default:
                return 'info';
        }
    }
    
    private generateEventId(): string {
        return `audit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
}
```

This comprehensive security guide provides the foundation for building secure TypeScript WebSocket applications with proper authentication, authorization, input validation, and threat protection mechanisms.