# Socket.IO Redis Scaling - Quick Reference Guide

**For Development Team**: Immediate implementation patterns and configuration examples

## 🚀 Quick Start Implementation

### 1. Redis Streams Adapter Setup (Recommended)

```bash
# Install dependencies
npm install socket.io @socket.io/redis-streams-adapter redis
```

```javascript
// server.js
import { createServer } from "http";
import { Server } from "socket.io";
import { createClient } from "redis";
import { createAdapter } from "@socket.io/redis-streams-adapter";

const httpServer = createServer();
const redisClient = createClient({ url: "redis://localhost:6379" });
await redisClient.connect();

const io = new Server(httpServer, {
  adapter: createAdapter(redisClient, {
    streamName: "socket.io",
    maxLen: 10000,
    readCount: 100,
    heartbeatInterval: 5000,
    heartbeatTimeout: 10000
  })
});

const PORT = process.env.PORT || 3000;
httpServer.listen(PORT);
```

### 2. Node.js Cluster Configuration

```javascript
// cluster.js
import cluster from "cluster";
import { availableParallelism } from "os";

if (cluster.isPrimary) {
  const numCPUs = availableParallelism();
  
  for (let i = 0; i < numCPUs; i++) {
    cluster.fork();
  }
  
  cluster.on('exit', (worker) => {
    console.log(`Worker ${worker.process.pid} died`);
    cluster.fork();
  });
} else {
  require('./server.js');
}
```

## 🔧 Configuration Examples

### Redis Cluster Configuration

```javascript
import { Cluster } from "ioredis";

const cluster = new Cluster([
  { host: "127.0.0.1", port: 7000 },
  { host: "127.0.0.1", port: 7001 },
  { host: "127.0.0.1", port: 7002 }
], {
  maxRetriesPerRequest: 3,
  retryDelayOnFailover: 100,
  enableAutoPipelining: true
});
```

### Nginx Load Balancer

```nginx
upstream socket_io_nodes {
    ip_hash;  # Sticky sessions
    server app01:3000;
    server app02:3000;
    server app03:3000;
}

server {
    listen 80;
    location /socket.io/ {
        proxy_pass http://socket_io_nodes;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_buffering off;
    }
}
```

## 🛡️ Security Implementation

### JWT Authentication

```javascript
import jwt from 'jsonwebtoken';

io.use(async (socket, next) => {
  try {
    const token = socket.handshake.auth.token;
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    socket.user = decoded;
    next();
  } catch (error) {
    next(new Error('Authentication failed'));
  }
});
```

### Rate Limiting

```javascript
import { RateLimiterRedis } from 'rate-limiter-flexible';

const rateLimiter = new RateLimiterRedis({
  storeClient: redisClient,
  points: 100,      // Requests
  duration: 60,     // Per 60 seconds
  blockDuration: 60 // Block for 60 seconds
});

io.use(async (socket, next) => {
  try {
    await rateLimiter.consume(socket.handshake.address);
    next();
  } catch (error) {
    next(new Error('Rate limit exceeded'));
  }
});
```

## 📊 Monitoring Setup

### Health Check Endpoint

```javascript
app.get('/health', async (req, res) => {
  const health = {
    status: 'healthy',
    connections: io.engine.clientsCount,
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    redis: await redisClient.ping() ? 'connected' : 'disconnected'
  };
  res.json(health);
});
```

### Prometheus Metrics

```javascript
import prometheus from 'prom-client';

const connectionsGauge = new prometheus.Gauge({
  name: 'socketio_connections_total',
  help: 'Total Socket.IO connections'
});

setInterval(() => {
  connectionsGauge.set(io.engine.clientsCount);
}, 5000);
```

## 🚢 Docker Deployment

### Dockerfile

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 3000
CMD ["node", "cluster.js"]
```

### Docker Compose

```yaml
version: '3.8'
services:
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    
  socket-app:
    build: .
    ports:
      - "3000-3003:3000"
    depends_on:
      - redis
    environment:
      - REDIS_URL=redis://redis:6379
    deploy:
      replicas: 4
```

## ⚡ Performance Optimization

### OS-Level Optimization

```bash
# Increase file descriptor limits
echo "* soft nofile 1048576" >> /etc/security/limits.conf
echo "* hard nofile 1048576" >> /etc/security/limits.conf

# Expand port range
echo "net.ipv4.ip_local_port_range = 10000 65535" >> /etc/sysctl.conf
sysctl -p
```

### WebSocket Optimization

```javascript
// Install native modules for better performance
// npm install --save-optional bufferutil utf-8-validate

const io = new Server(httpServer, {
  wsEngine: require("eiows").Server,  // Faster WebSocket engine
  compression: false,                  // Disable for better performance
  maxHttpBufferSize: 1e6,             // 1MB buffer
  pingTimeout: 60000,                 // 60 seconds
  pingInterval: 25000,                // 25 seconds
  transports: ["websocket", "polling"]
});
```

## 📈 Performance Targets

| Metric | Target | Notes |
|--------|--------|-------|
| Concurrent Connections | 10,000+ | Per server instance |
| Message Throughput | 50,000+ msg/s | With proper optimization |
| Latency | <100ms | Real-time operations |
| Memory Usage | <1GB | Per 10,000 connections |
| CPU Usage | <70% | Under peak load |

## 🔍 Troubleshooting

### Common Issues

1. **"Session ID unknown" errors**
   - Solution: Ensure sticky sessions are configured
   - Check load balancer configuration

2. **High Redis latency**
   - Solution: Use connection pooling
   - Consider Redis cluster for scale

3. **Memory leaks**
   - Solution: Implement connection cleanup
   - Set `rawSocket.request = null` for memory optimization

4. **Connection drops**
   - Solution: Implement proper error handling
   - Use Redis Streams adapter for reliability

### Debug Commands

```bash
# Check Redis connection
redis-cli ping

# Monitor Redis commands
redis-cli monitor

# Check Socket.IO connections
curl http://localhost:3000/health

# Load test
npx artillery run artillery-config.yml
```

## 🎯 Implementation Priority

1. **Phase 1**: Basic Redis Streams adapter setup
2. **Phase 2**: Add authentication and rate limiting
3. **Phase 3**: Implement monitoring and health checks
4. **Phase 4**: Configure load balancer and scaling
5. **Phase 5**: Performance optimization and tuning

## 📚 Reference Links

- [Main Research Document](./socket-io-scaling-redis.md)
- [WebSocket Patterns](./websocket-realtime/)
- [Socket.IO Documentation](https://socket.io/docs/v4/)
- [Redis Scaling Guide](https://redis.io/docs/manual/scaling/)
- [Node.js Cluster](https://nodejs.org/api/cluster.html)

---

**Note**: This quick reference provides immediate implementation patterns. Refer to the comprehensive research document for detailed explanations and advanced configurations.