# Container Security Best Practices

## Overview

Container security involves protecting the entire container lifecycle from build to runtime. This includes securing container images, the container runtime environment, and the orchestration platform.

## Container Image Security

### Base Image Security
- Use minimal base images (distroless, scratch, or alpine)
- Keep base images updated with latest security patches
- Scan base images for known vulnerabilities
- Use official images from trusted registries

### Dockerfile Security Best Practices

```dockerfile
# Use specific image tags, not 'latest'
FROM node:18.17.0-alpine3.18

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001

# Don't run as root
USER nextjs

# Use COPY instead of ADD
COPY package*.json ./
COPY --chown=nextjs:nodejs . .

# Remove unnecessary packages
RUN apk del .gyp

# Set read-only filesystem
USER 1001
```

### Multi-Stage Builds for Security

```dockerfile
# Build stage
FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

# Production stage
FROM node:18-alpine AS production
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001
WORKDIR /app
COPY --from=builder --chown=nextjs:nodejs /app/node_modules ./node_modules
COPY --chown=nextjs:nodejs . .
USER nextjs
EXPOSE 3000
CMD ["npm", "start"]
```

## Image Vulnerability Scanning

### Container Analysis API Integration

```bash
# Enable Container Analysis API
gcloud services enable containeranalysis.googleapis.com

# Scan image for vulnerabilities
gcloud beta container images scan IMAGE_URL

# Get scan results
gcloud beta container images list-tags IMAGE_URL \
  --show-occurrences \
  --format="table(digest,occurrences.vulnerabilities:label=VULNERABILITIES)"
```

### Trivy Integration

```yaml
# Add to CI/CD pipeline
- name: Run Trivy vulnerability scanner
  uses: aquasecurity/trivy-action@master
  with:
    image-ref: '${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ env.IMAGE_TAG }}'
    format: 'sarif'
    output: 'trivy-results.sarif'

- name: Upload Trivy scan results to GitHub Security tab
  uses: github/codeql-action/upload-sarif@v2
  with:
    sarif_file: 'trivy-results.sarif'
```

## Binary Authorization

### Policy Configuration

```yaml
# Binary Authorization policy for production
defaultAdmissionRule:
  requireAttestationsBy:
  - projects/PROJECT_ID/attestors/production-attestor
  - projects/PROJECT_ID/attestors/security-attestor
  enforcementMode: ENFORCED_BLOCK_AND_AUDIT_LOG

# Allow Google-managed images
admissionWhitelistPatterns:
- namePattern: gcr.io/google-containers/*
- namePattern: gcr.io/gke-release/*

# Stricter rules for production namespace
kubernetesNamespaceAdmissionRules:
  production:
    requireAttestationsBy:
    - projects/PROJECT_ID/attestors/production-attestor
    - projects/PROJECT_ID/attestors/security-attestor
    - projects/PROJECT_ID/attestors/compliance-attestor
    enforcementMode: ENFORCED_BLOCK_AND_AUDIT_LOG
```

### Attestation Workflow

```bash
# 1. Build and push image
docker build -t gcr.io/PROJECT_ID/app:latest .
docker push gcr.io/PROJECT_ID/app:latest

# 2. Scan for vulnerabilities
gcloud beta container images scan gcr.io/PROJECT_ID/app:latest

# 3. Create security attestation
gcloud beta container binauthz attestations sign-and-create \
  --artifact-url=gcr.io/PROJECT_ID/app:latest \
  --attestor=projects/PROJECT_ID/attestors/security-attestor \
  --attestor-project=PROJECT_ID

# 4. Create production attestation
gcloud beta container binauthz attestations sign-and-create \
  --artifact-url=gcr.io/PROJECT_ID/app:latest \
  --attestor=projects/PROJECT_ID/attestors/production-attestor \
  --attestor-project=PROJECT_ID
```

## Runtime Security

### Security Contexts

```yaml
apiVersion: v1
kind: Pod
spec:
  securityContext:
    runAsNonRoot: true
    runAsUser: 1001
    runAsGroup: 1001
    fsGroup: 1001
    seccompProfile:
      type: RuntimeDefault
  containers:
  - name: app
    securityContext:
      allowPrivilegeEscalation: false
      readOnlyRootFilesystem: true
      capabilities:
        drop:
        - ALL
      runAsNonRoot: true
      runAsUser: 1001
```

### Resource Limits

```yaml
apiVersion: v1
kind: Pod
spec:
  containers:
  - name: app
    resources:
      limits:
        memory: "128Mi"
        cpu: "100m"
        ephemeral-storage: "1Gi"
      requests:
        memory: "64Mi"
        cpu: "50m"
        ephemeral-storage: "500Mi"
```

## Network Security

### Network Policies

```yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: deny-all
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: allow-app-traffic
spec:
  podSelector:
    matchLabels:
      app: myapp
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          app: frontend
    ports:
    - protocol: TCP
      port: 8080
  egress:
  - to:
    - podSelector:
        matchLabels:
          app: database
    ports:
    - protocol: TCP
      port: 5432
```

### Service Mesh Security (Istio)

```yaml
apiVersion: security.istio.io/v1beta1
kind: PeerAuthentication
metadata:
  name: default
spec:
  mtls:
    mode: STRICT
---
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: app-policy
spec:
  selector:
    matchLabels:
      app: myapp
  rules:
  - from:
    - source:
        principals: ["cluster.local/ns/default/sa/frontend"]
    to:
    - operation:
        methods: ["GET", "POST"]
```

## Secret Management

### Kubernetes Secrets

```yaml
apiVersion: v1
kind: Secret
metadata:
  name: app-secrets
type: Opaque
data:
  database-url: <base64-encoded-value>
  api-key: <base64-encoded-value>
---
apiVersion: apps/v1
kind: Deployment
spec:
  template:
    spec:
      containers:
      - name: app
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: database-url
```

### External Secret Management

```yaml
# Using External Secrets Operator with Google Secret Manager
apiVersion: external-secrets.io/v1beta1
kind: SecretStore
metadata:
  name: gcp-secret-store
spec:
  provider:
    gcpsm:
      projectId: "my-project"
      auth:
        workloadIdentity:
          clusterLocation: us-central1
          clusterName: my-cluster
          serviceAccountRef:
            name: external-secrets-sa
---
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: app-secret
spec:
  refreshInterval: 1h
  secretStoreRef:
    name: gcp-secret-store
    kind: SecretStore
  target:
    name: app-secret
    creationPolicy: Owner
  data:
  - secretKey: database-url
    remoteRef:
      key: database-connection-string
```

## Compliance and Auditing

### Pod Security Standards

```yaml
apiVersion: v1
kind: Namespace
metadata:
  name: production
  labels:
    pod-security.kubernetes.io/enforce: restricted
    pod-security.kubernetes.io/audit: restricted
    pod-security.kubernetes.io/warn: restricted
```

### OPA Gatekeeper Policies

```yaml
apiVersion: templates.gatekeeper.sh/v1beta1
kind: ConstraintTemplate
metadata:
  name: k8srequiredsecuritycontext
spec:
  crd:
    spec:
      names:
        kind: K8sRequiredSecurityContext
      validation:
        properties:
          runAsNonRoot:
            type: boolean
  targets:
    - target: admission.k8s.gatekeeper.sh
      rego: |
        package k8srequiredsecuritycontext
        
        violation[{"msg": msg}] {
          container := input.review.object.spec.containers[_]
          not container.securityContext.runAsNonRoot
          msg := "Container must run as non-root user"
        }
```

## Monitoring and Alerting

### Security Monitoring

```yaml
# Falco rules for container security
- rule: Unauthorized Container Activity
  desc: Detect unauthorized container operations
  condition: >
    spawned_process and container and
    (proc.name in (shell_binaries) or
     proc.name in (network_tools) or
     proc.name in (system_tools))
  output: >
    Unauthorized activity in container
    (user=%user.name command=%proc.cmdline container=%container.name)
  priority: WARNING
```

### Prometheus Monitoring

```yaml
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: container-security-metrics
spec:
  selector:
    matchLabels:
      app: security-scanner
  endpoints:
  - port: metrics
    interval: 30s
    path: /metrics
---
# Alert rules
groups:
- name: container-security
  rules:
  - alert: HighVulnerabilityCount
    expr: vulnerability_count{severity="high"} > 5
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High number of vulnerabilities detected"
      description: "{{ $labels.image }} has {{ $value }} high severity vulnerabilities"
```

## CI/CD Security Integration

### GitHub Actions Security Workflow

```yaml
name: Container Security Scan

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  security-scan:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Build Docker image
      run: docker build -t myapp:${{ github.sha }} .
    
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: 'myapp:${{ github.sha }}'
        format: 'table'
        exit-code: '1'
        severity: 'CRITICAL,HIGH'
    
    - name: Run Hadolint Dockerfile linter
      uses: hadolint/hadolint-action@v2.0.0
      with:
        dockerfile: Dockerfile
        failure-threshold: error
    
    - name: Run container structure test
      run: |
        curl -LO https://storage.googleapis.com/container-structure-test/latest/container-structure-test-linux-amd64
        chmod +x container-structure-test-linux-amd64
        ./container-structure-test-linux-amd64 test \
          --image myapp:${{ github.sha }} \
          --config container-tests.yaml
```

### Supply Chain Security

```yaml
# SLSA (Supply Chain Levels for Software Artifacts) provenance
name: Build with SLSA Provenance

on:
  release:
    types: [published]

jobs:
  build:
    runs-on: ubuntu-latest
    outputs:
      digest: ${{ steps.build.outputs.digest }}
    steps:
    - uses: actions/checkout@v3
    
    - name: Build and push Docker image
      id: build
      uses: docker/build-push-action@v4
      with:
        context: .
        push: true
        tags: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.ref_name }}
        platforms: linux/amd64,linux/arm64
        provenance: true
        sbom: true

  provenance:
    needs: [build]
    uses: slsa-framework/slsa-github-generator/.github/workflows/generator_container_slsa3.yml@v1.7.0
    with:
      image: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
      digest: ${{ needs.build.outputs.digest }}
    secrets:
      registry-username: ${{ github.actor }}
      registry-password: ${{ secrets.GITHUB_TOKEN }}
```

## Incident Response

### Security Incident Runbook

1. **Detection**: Monitor for security alerts and policy violations
2. **Assessment**: Evaluate the severity and scope of the incident
3. **Containment**: Isolate affected containers and services
4. **Eradication**: Remove malicious components and patch vulnerabilities
5. **Recovery**: Restore services with updated, secure images
6. **Lessons Learned**: Update security policies and procedures

### Emergency Procedures

```bash
# Quarantine compromised container
kubectl patch deployment myapp -p '{"spec":{"replicas":0}}'

# Block image in Binary Authorization
# Create emergency policy blocking the compromised image
cat > emergency-policy.yaml << EOF
defaultAdmissionRule:
  enforcementMode: ENFORCED_BLOCK_AND_AUDIT_LOG
  requireAttestationsBy:
  - projects/PROJECT_ID/attestors/emergency-attestor
admissionWhitelistPatterns: []
clusterAdmissionRules: {}
name: projects/PROJECT_ID/policy
EOF

gcloud container binauthz policy import emergency-policy.yaml

# Forensic analysis
kubectl logs deployment/myapp --previous > incident-logs.txt
kubectl describe pod POD_NAME > pod-details.txt
```

## Compliance Frameworks

### CIS Kubernetes Benchmark

Key controls for container security:
- 4.1.1: Ensure Image Vulnerability Scanning is in place
- 4.1.2: Minimize the admission of containers with allowPrivilegeEscalation
- 4.2.1: Minimize the admission of privileged containers
- 4.2.2: Minimize the admission of containers with capabilities
- 4.2.3: Minimize the admission of containers with hostNetwork
- 4.2.4: Minimize the admission of containers with hostPID
- 4.2.5: Minimize the admission of containers with hostIPC

### NIST Cybersecurity Framework

Controls mapping:
- **Identify**: Asset inventory, vulnerability assessment
- **Protect**: Access controls, security training, protective technology
- **Detect**: Security monitoring, anomaly detection
- **Respond**: Incident response planning, communication
- **Recover**: Recovery planning, improvements

## Tools and Resources

### Security Tools
- **Trivy**: Vulnerability scanner for containers and filesystems
- **Clair**: Open source vulnerability scanner
- **Anchore**: Container security and compliance platform
- **Falco**: Runtime security monitoring
- **OPA Gatekeeper**: Policy enforcement for Kubernetes

### Best Practices Checklist
- [ ] Use minimal base images
- [ ] Scan images for vulnerabilities
- [ ] Implement Binary Authorization
- [ ] Configure security contexts
- [ ] Set resource limits
- [ ] Use network policies
- [ ] Implement proper secret management
- [ ] Monitor runtime security
- [ ] Maintain incident response procedures
- [ ] Regular security audits and updates

## References

- [NIST Container Security Guide](https://csrc.nist.gov/publications/detail/sp/800-190/final)
- [CIS Kubernetes Benchmark](https://www.cisecurity.org/benchmark/kubernetes)
- [OWASP Container Security](https://owasp.org/www-project-docker-security/)
- [Google Cloud Container Security](https://cloud.google.com/security/container-security)
- [SLSA Framework](https://slsa.dev/)