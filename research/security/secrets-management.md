# Secrets Management Best Practices

## Overview

This document outlines comprehensive security best practices for managing secrets in the Episteme platform, covering local development, staging, and production environments.

## Core Principles

### 1. Never Store Secrets in Code
- **NO hardcoded passwords, API keys, or tokens in source code**
- **NO secrets in environment variable defaults**
- **NO secrets in configuration files committed to version control**
- **NO secrets in Docker images or container layers**

### 2. Principle of Least Privilege
- Services only access secrets they require
- Use service-specific secret scoping
- Regular access reviews and rotation

### 3. Defense in Depth
- Multiple layers of secret protection
- Encryption at rest and in transit
- Audit logging for all secret access
- Automated monitoring and alerting

## Secret Classification

### High Sensitivity (Immediate rotation if exposed)
- Database root passwords
- JWT signing keys
- API keys for critical services
- Service account private keys
- Encryption keys

### Medium Sensitivity (Rotation within 24 hours)
- Application database passwords
- Redis passwords
- Webhook secrets
- Third-party service tokens

### Low Sensitivity (Rotation within 7 days)
- Development API keys
- Non-critical service tokens
- Monitoring credentials

## Environment-Specific Strategies

### Development Environment

#### Local Development Setup
```bash
# Create local secrets directory (not committed)
mkdir -p ./secrets
echo "secrets/" >> .gitignore

# Generate development secrets
./scripts/security/generate-dev-secrets.sh
```

#### Docker Compose Secrets
```yaml
# docker-compose.secrets.yml
version: '3.8'

services:
  postgres:
    environment:
      POSTGRES_PASSWORD_FILE: /run/secrets/postgres_password
    secrets:
      - postgres_password

secrets:
  postgres_password:
    file: ./secrets/dev-postgres-password.txt
  redis_password:
    file: ./secrets/dev-redis-password.txt
```

#### Development Secret Rotation
- **Frequency**: Weekly or on developer request
- **Method**: Automated script with local file updates
- **Validation**: Service restart and health checks

### Staging Environment

#### Secret Management
- Mirror production secret structure
- Use staging-specific values
- Automated secret provisioning from templates

#### Configuration
```yaml
# staging environment
secrets:
  prefix: "staging-"
  rotation_period: "7d"
  backup_retention: "30d"
```

### Production Environment

#### Google Cloud Secret Manager
```bash
# Create production secrets with proper versioning
gcloud secrets create prod-postgres-password \
  --replication-policy="automatic" \
  --labels="environment=production,service=database,sensitivity=high"

# Set IAM permissions
gcloud secrets add-iam-policy-binding prod-postgres-password \
  --member="serviceAccount:<EMAIL>" \
  --role="roles/secretmanager.secretAccessor"
```

#### Automated Rotation
- **High sensitivity**: 30 days or on-demand
- **Medium sensitivity**: 60 days
- **Low sensitivity**: 90 days

## Secret Rotation Procedures

### Automated Rotation Script

Our existing `scripts/security/rotate-secrets.sh` provides:

```bash
# Rotate all production secrets
./scripts/security/rotate-secrets.sh --environment production all

# Rotate specific secret type
./scripts/security/rotate-secrets.sh --environment production database

# Dry run to preview changes
./scripts/security/rotate-secrets.sh --dry-run --environment production
```

### Zero-Downtime Rotation

1. **Generate new secret version**
2. **Deploy to Secret Manager**
3. **Update service configuration**
4. **Rolling restart services**
5. **Validate service health**
6. **Deactivate old secret version**
7. **Clean up after grace period**

### Emergency Rotation

```bash
# Immediate emergency rotation
./scripts/security/rotate-secrets.sh --force --environment production api-keys

# Validate all services after emergency rotation
./scripts/security/validate-services.sh --environment production
```

## Access Control and Auditing

### Service Account Management

#### Production Service Accounts
```yaml
# Dedicated service accounts per service
services:
  analysis-engine:
    service_account: "<EMAIL>"
    secrets:
      - prod-postgres-password
      - prod-jwt-secret
  
  query-intelligence:
    service_account: "<EMAIL>"
    secrets:
      - prod-gemini-api-key
      - prod-jwt-secret
```

#### IAM Policy Examples
```bash
# Grant specific secret access
gcloud secrets add-iam-policy-binding prod-postgres-password \
  --member="serviceAccount:<EMAIL>" \
  --role="roles/secretmanager.secretAccessor"

# Deny access to unauthorized services
gcloud secrets remove-iam-policy-binding prod-postgres-password \
  --member="serviceAccount:<EMAIL>" \
  --role="roles/secretmanager.secretAccessor"
```

### Audit Logging

#### Enable Comprehensive Logging
```bash
# Enable Secret Manager audit logs
gcloud logging sinks create secret-manager-audit \
  bigquery.googleapis.com/projects/$PROJECT_ID/datasets/security_audit \
  --log-filter='protoPayload.serviceName="secretmanager.googleapis.com"'

# Enable IAM audit logs
gcloud logging sinks create iam-audit \
  bigquery.googleapis.com/projects/$PROJECT_ID/datasets/security_audit \
  --log-filter='protoPayload.serviceName="iam.googleapis.com"'
```

#### Monitoring and Alerting
```yaml
# Monitoring policy for secret access
alert_policies:
  - name: "unusual_secret_access"
    condition: "access outside business hours"
    notification: "<EMAIL>"
  
  - name: "failed_secret_access"
    condition: "repeated access failures"
    notification: "<EMAIL>"
  
  - name: "secret_rotation_failure" 
    condition: "rotation script failure"
    notification: "<EMAIL>"
```

## Secret Storage Security

### Encryption Standards

#### At Rest
- **Secret Manager**: Google-managed encryption keys
- **Local Development**: OS-level file encryption recommended
- **Backup Storage**: Client-side encryption before storage

#### In Transit
- **TLS 1.3** for all secret transmission
- **mTLS** for service-to-service communication
- **Certificate pinning** where applicable

### Secret Formats

#### Database Passwords
```bash
# Generate strong database passwords
openssl rand -base64 32 | tr -d "=+/" | cut -c1-32
```

#### JWT Secrets
```bash
# Generate JWT signing keys
openssl rand -hex 64
```

#### API Keys
```bash
# Format: prefix_date_random
echo "episteme_$(date +%Y%m%d)_$(openssl rand -hex 16)"
```

## Incident Response

### Secret Compromise Response

#### Immediate Actions (0-15 minutes)
1. **Rotate compromised secret immediately**
2. **Revoke access to compromised secret**
3. **Alert security team**
4. **Monitor for unauthorized access**

#### Short-term Actions (15 minutes - 4 hours)
1. **Investigate scope of compromise**
2. **Rotate related secrets**
3. **Review access logs**
4. **Update security monitoring**

#### Long-term Actions (4+ hours)
1. **Complete forensic analysis**
2. **Update security procedures**
3. **Implement additional controls**
4. **Conduct security training**

### Incident Playbook

```bash
# Emergency secret rotation playbook
./scripts/security/incident-response.sh \
  --compromised-secret="prod-postgres-password" \
  --severity="high" \
  --notify="<EMAIL>"
```

## Compliance and Governance

### Secret Lifecycle Management

#### Creation
- Automated generation with strong entropy
- Proper classification and labeling
- IAM policy assignment
- Audit log activation

#### Usage
- Access only through authorized channels
- No logging of secret values
- Regular access pattern analysis
- Automated anomaly detection

#### Rotation
- Scheduled rotation based on classification
- Zero-downtime rotation procedures
- Validation of service functionality
- Cleanup of old versions

#### Retirement
- Secure deletion from all systems
- Verification of no remaining references
- Audit trail of retirement process
- Update documentation

### Compliance Requirements

#### SOC 2 Type II
- Comprehensive audit logging
- Access control documentation
- Regular security assessments
- Incident response procedures

#### GDPR
- Data minimization for secrets
- Right to deletion procedures
- Cross-border transfer controls
- Privacy impact assessments

## Developer Guidelines

### Secure Development Practices

#### Code Review Checklist
- [ ] No hardcoded secrets in source code
- [ ] Proper use of environment variables
- [ ] Secret references use file paths or environment variables
- [ ] No secrets in log output
- [ ] Proper error handling without exposing secrets

#### Testing with Secrets
```bash
# Use test-specific secrets
export TEST_JWT_SECRET="test-only-secret-$(date +%s)"
export TEST_DATABASE_URL="postgresql://test:test@localhost:5433/test_db"

# Never use production secrets in tests
if [[ "$ENVIRONMENT" != "test" ]]; then
  echo "ERROR: Production secrets detected in test environment"
  exit 1
fi
```

### Local Development Setup

#### Initial Setup
```bash
# Clone repository
git clone https://github.com/star-boy-95/episteme.git
cd episteme

# Generate development secrets
./scripts/security/setup-dev-secrets.sh

# Verify setup
./scripts/security/validate-dev-setup.sh
```

#### Daily Development
```bash
# Start services with secrets
docker-compose -f docker-compose.base.yml -f docker-compose.secrets.yml up

# Rotate development secrets weekly
./scripts/security/rotate-secrets.sh --environment development
```

## Tools and Automation

### Secret Scanning Tools

#### Pre-commit Hooks
```yaml
# .pre-commit-config.yaml
repos:
  - repo: https://github.com/Yelp/detect-secrets
    rev: v1.4.0
    hooks:
      - id: detect-secrets
        args: ['--baseline', '.secrets.baseline']
```

#### CI/CD Integration
```yaml
# GitHub Actions secret scanning
name: Security Scan
on: [push, pull_request]
jobs:
  secret-scan:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Run secret scan
        run: |
          pip install detect-secrets
          detect-secrets scan --all-files
```

### Monitoring Tools

#### Secret Access Monitoring
```bash
# Monitor secret access patterns
gcloud logging read 'protoPayload.serviceName="secretmanager.googleapis.com"' \
  --limit=100 \
  --format="table(timestamp,protoPayload.authenticationInfo.principalEmail,protoPayload.resourceName)"
```

#### Health Check Integration
```bash
# Validate secret accessibility in health checks
curl -f https://analysis-engine.episteme.com/health/secrets
```

## Related Documentation

- [Google Cloud Secret Manager Documentation](https://cloud.google.com/secret-manager/docs)
- [OWASP Secret Management Cheat Sheet](https://cheatsheetseries.owasp.org/cheatsheets/Secrets_Management_Cheat_Sheet.html)
- [NIST Cryptographic Standards](https://csrc.nist.gov/publications/detail/sp/800-57-part-1/rev-5/final)
- [Episteme Security Policies](./secure-coding-practices.md)

## Change Log

| Date | Change | Author |
|------|--------|---------|
| 2025-01-22 | Initial secrets management documentation | AI Assistant |
| | Comprehensive best practices guide | |
| | Environment-specific security procedures | |