# Binary Authorization for Cloud Run - Setup Guide

## Overview

Binary Authorization is a deployment-time security control that ensures only trusted container images are deployed on Google Cloud. It works by verifying cryptographic signatures on container images before deployment to Cloud Run, GKE, or other Google Cloud services.

## Architecture Components

### 1. Binary Authorization Policy
- Defines rules for which container images can be deployed
- Specifies required attestations and signers
- Can be project-wide or resource-specific

### 2. Attestation Authority
- Entity that creates attestations (signatures) for container images
- Uses cryptographic keys to sign attestations
- Represents a trusted source in the deployment pipeline

### 3. Attestors
- References to attestation authorities in Binary Authorization policies
- Links policies to specific signing keys
- Allows policy-based control over trusted signers

## Setup Process

### Step 1: Enable Binary Authorization API

```bash
# Enable the Binary Authorization API
gcloud services enable binaryauthorization.googleapis.com

# Enable Container Analysis API (required for attestations)
gcloud services enable containeranalysis.googleapis.com

# Enable Artifact Registry API (if not already enabled)
gcloud services enable artifactregistry.googleapis.com
```

### Step 2: Create Attestation Authority

```bash
# Create a note to represent the attestation authority
export PROJECT_ID="your-project-id"
export ATTESTOR_NAME="production-attestor"
export NOTE_ID="prod-attestation-note"

# Create the note
cat > /tmp/note_payload.json << EOF
{
  "name": "projects/${PROJECT_ID}/notes/${NOTE_ID}",
  "attestation": {
    "hint": {
      "human_readable_name": "Production attestation authority"
    }
  }
}
EOF

curl -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $(gcloud auth print-access-token)" \
  -d @/tmp/note_payload.json \
  "https://containeranalysis.googleapis.com/v1/projects/${PROJECT_ID}/notes?noteId=${NOTE_ID}"
```

### Step 3: Generate Signing Keys

```bash
# Create directory for keys
mkdir -p /tmp/binauthz-keys
cd /tmp/binauthz-keys

# Generate a PGP key pair for signing
gpg --quick-generate-key \
  --batch \
  --passphrase "" \
  "Binary Authorization Production <<EMAIL>>"

# Export the public key
gpg --armor --export "Binary Authorization Production <<EMAIL>>" > publickey.pgp

# Export the private key (store securely!)
gpg --armor --export-secret-keys "Binary Authorization Production <<EMAIL>>" > privatekey.pgp

# Get the key fingerprint
export KEY_FINGERPRINT=$(gpg --with-colons --fingerprint "Binary Authorization Production <<EMAIL>>" | grep fpr | head -1 | cut -d: -f10)
echo "Key fingerprint: ${KEY_FINGERPRINT}"
```

### Step 4: Create Attestor

```bash
# Create the attestor
gcloud container binauthz attestors create ${ATTESTOR_NAME} \
  --attestation-authority-note="projects/${PROJECT_ID}/notes/${NOTE_ID}" \
  --attestation-authority-note-public-key-file=publickey.pgp \
  --attestation-authority-note-public-key-id="${KEY_FINGERPRINT}"
```

### Step 5: Create Binary Authorization Policy

```bash
# Create policy file
cat > /tmp/policy.yaml << EOF
defaultAdmissionRule:
  requireAttestationsBy:
  - projects/${PROJECT_ID}/attestors/${ATTESTOR_NAME}
  enforcementMode: ENFORCED_BLOCK_AND_AUDIT_LOG
admissionWhitelistPatterns:
- namePattern: gcr.io/google-containers/*
- namePattern: gcr.io/google_containers/*
- namePattern: k8s.gcr.io/*
- namePattern: gcr.io/gke-release/*
clusterAdmissionRules: {}
kubernetesNamespaceAdmissionRules: {}
kubernetesServiceAccountAdmissionRules: {}
istioServiceIdentityAdmissionRules: {}
name: projects/${PROJECT_ID}/policy
EOF

# Import the policy
gcloud container binauthz policy import /tmp/policy.yaml
```

### Step 6: Configure Cloud Run to Use Binary Authorization

```bash
# Enable Binary Authorization for Cloud Run in the project
gcloud run services update YOUR_SERVICE_NAME \
  --region=us-central1 \
  --binary-authorization=default

# Or set as default for all new services
gcloud config set run/binary_authorization default
```

## Image Signing in CI/CD Pipeline

### Using Cloud Build

Add this step to your `cloudbuild.yaml`:

```yaml
# Step: Sign the container image
- name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
  id: 'sign-image'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      # Import signing key from Secret Manager
      gcloud secrets versions access latest --secret="binauthz-signing-key" > /tmp/privatekey.pgp
      gpg --import /tmp/privatekey.pgp
      
      # Sign the image
      gcloud beta container binauthz attestations sign-and-create \
        --artifact-url="${_REGION}-docker.pkg.dev/${PROJECT_ID}/${_REPOSITORY}/${_SERVICE_NAME}:${COMMIT_SHA}" \
        --attestor="projects/${PROJECT_ID}/attestors/${_ATTESTOR_NAME}" \
        --attestor-project="${PROJECT_ID}" \
        --keyversion-project="${PROJECT_ID}" \
        --keyversion-location="global" \
        --keyversion-keyring="binauthz-keys" \
        --keyversion-key="signing-key" \
        --keyversion="1"
  waitFor: ['push-image']
```

### Using GitHub Actions

```yaml
- name: Sign container image
  env:
    GOOGLE_APPLICATION_CREDENTIALS: ${{ secrets.GCP_SA_KEY_FILE }}
  run: |
    # Authenticate with gcloud
    gcloud auth activate-service-account --key-file="${GOOGLE_APPLICATION_CREDENTIALS}"
    
    # Import signing key
    echo "${{ secrets.BINAUTHZ_PRIVATE_KEY }}" | gpg --import
    
    # Sign the image
    gcloud beta container binauthz attestations sign-and-create \
      --artifact-url="${GAR_REGISTRY}/${GCP_PROJECT_ID}/ccl-images/${SERVICE_NAME}:${GITHUB_SHA}" \
      --attestor="projects/${GCP_PROJECT_ID}/attestors/production-attestor" \
      --attestor-project="${GCP_PROJECT_ID}"
```

## Validation and Testing

### Test Binary Authorization Policy

```bash
# Try to deploy an unsigned image (should fail)
gcloud run deploy test-unsigned \
  --image=gcr.io/google-containers/pause:latest \
  --region=us-central1 \
  --allow-unauthenticated

# Expected: Deployment should be blocked by Binary Authorization

# Deploy a properly signed image (should succeed)
gcloud run deploy test-signed \
  --image="${_REGION}-docker.pkg.dev/${PROJECT_ID}/${_REPOSITORY}/${_SERVICE_NAME}:${COMMIT_SHA}" \
  --region=us-central1 \
  --allow-unauthenticated
```

### Verify Attestations

```bash
# List attestations for an image
gcloud beta container binauthz attestations list \
  --artifact-url="${_REGION}-docker.pkg.dev/${PROJECT_ID}/${_REPOSITORY}/${_SERVICE_NAME}:${COMMIT_SHA}" \
  --attestor="projects/${PROJECT_ID}/attestors/${ATTESTOR_NAME}"

# Check policy compliance
gcloud container binauthz policy evaluate \
  --image-url="${_REGION}-docker.pkg.dev/${PROJECT_ID}/${_REPOSITORY}/${_SERVICE_NAME}:${COMMIT_SHA}"
```

## Security Best Practices

### Key Management
- Store private signing keys in Google Secret Manager or Cloud KMS
- Use different keys for different environments (dev, staging, prod)
- Implement key rotation policies
- Restrict access to signing keys to CI/CD service accounts only

### Policy Configuration
- Use `ENFORCED_BLOCK_AND_AUDIT_LOG` mode for production
- Start with `DRYRUN_AUDIT_LOG_ONLY` for testing
- Maintain separate policies for different environments
- Whitelist only essential base images (Google-provided images)

### Operational Considerations
- Monitor Binary Authorization logs in Cloud Logging
- Set up alerts for policy violations
- Implement emergency break-glass procedures
- Document key recovery procedures

## Emergency Procedures

### Temporarily Disable Binary Authorization

```bash
# Switch to permissive mode (emergency only)
cat > /tmp/emergency_policy.yaml << EOF
defaultAdmissionRule:
  enforcementMode: DRYRUN_AUDIT_LOG_ONLY
name: projects/${PROJECT_ID}/policy
EOF

gcloud container binauthz policy import /tmp/emergency_policy.yaml
```

### Recover from Lost Signing Keys

```bash
# Create new attestor with new keys
gcloud container binauthz attestors create ${NEW_ATTESTOR_NAME} \
  --attestation-authority-note="projects/${PROJECT_ID}/notes/${NOTE_ID}" \
  --attestation-authority-note-public-key-file=new_publickey.pgp \
  --attestation-authority-note-public-key-id="${NEW_KEY_FINGERPRINT}"

# Update policy to use new attestor
# Update CI/CD pipeline with new signing key
```

## Troubleshooting

### Common Issues

1. **Deployment blocked**: Check if image has required attestations
2. **Attestation creation fails**: Verify attestor configuration and key access
3. **Policy evaluation errors**: Check policy syntax and attestor references
4. **Key import issues**: Verify key format and Secret Manager access

### Debug Commands

```bash
# Check current policy
gcloud container binauthz policy export

# List attestors
gcloud container binauthz attestors list

# Check service account permissions
gcloud projects get-iam-policy ${PROJECT_ID}

# View Binary Authorization logs
gcloud logging read 'resource.type="cloud_run_revision" AND protoPayload.serviceName="binaryauthorization.googleapis.com"'
```

## References

- [Binary Authorization Documentation](https://cloud.google.com/binary-authorization/docs)
- [Container Analysis API](https://cloud.google.com/container-analysis/docs)
- [Cloud Run Binary Authorization](https://cloud.google.com/run/docs/securing/binary-authorization)
- [Attestation and Signature Formats](https://cloud.google.com/binary-authorization/docs/attestation-format)