# Google Cloud Secret Manager Integration Guide

## Overview

This guide provides comprehensive instructions for integrating Google Cloud Secret Manager with the Episteme platform services, replacing hardcoded secrets in Docker Compose files with secure secret references.

## Architecture

### Secret Management Strategy

1. **Development Environment**: Docker Compose secrets for local development
2. **Production Environment**: Google Cloud Secret Manager with Cloud Run integration
3. **Secret Rotation**: Automated rotation using existing `scripts/security/rotate-secrets.sh`

### Secret Organization

```
Secret Naming Convention: {environment}-{service}-{secret-type}
Examples:
- prod-postgres-password
- prod-redis-password  
- prod-jwt-secret
- dev-postgres-password
```

## Implementation Steps

### 1. Create Secrets in Secret Manager

```bash
# Database secrets
echo -n "$(openssl rand -base64 32)" | gcloud secrets create prod-postgres-password --data-file=-
echo -n "$(openssl rand -base64 32)" | gcloud secrets create prod-redis-password --data-file=-

# Application secrets
echo -n "$(openssl rand -hex 64)" | gcloud secrets create prod-jwt-secret --data-file=-
echo -n "$(openssl rand -base64 32)" | gcloud secrets create prod-session-secret --data-file=-

# API keys (set manually with real values)
gcloud secrets create prod-gemini-api-key --data-file=-
gcloud secrets create prod-openai-api-key --data-file=-
```

### 2. Configure Service Account Permissions

```bash
# Grant Secret Manager access to Cloud Run service account
gcloud projects add-iam-policy-binding $PROJECT_ID \
  --member="serviceAccount:$SERVICE_ACCOUNT" \
  --role="roles/secretmanager.secretAccessor"
```

### 3. Docker Compose Secret References

#### Development (Local) Configuration

```yaml
# docker-compose.secrets.yml
version: '3.8'

services:
  postgres:
    environment:
      POSTGRES_PASSWORD_FILE: /run/secrets/postgres_password
    secrets:
      - postgres_password

secrets:
  postgres_password:
    file: ./secrets/dev-postgres-password.txt
  redis_password:
    file: ./secrets/dev-redis-password.txt
  jwt_secret:
    file: ./secrets/dev-jwt-secret.txt
```

#### Production (Cloud Run) Configuration

```yaml
# Cloud Run deployment with Secret Manager
apiVersion: serving.knative.dev/v1
kind: Service
spec:
  template:
    metadata:
      annotations:
        run.googleapis.com/secrets: |
          prod-postgres-password:latest,
          prod-redis-password:latest,
          prod-jwt-secret:latest
    spec:
      serviceAccountName: episteme-service-account
      containers:
      - name: analysis-engine
        env:
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: prod-postgres-password
              key: latest
```

## Secret Types and Configurations

### Database Secrets

| Secret Name | Description | Rotation Period | Usage |
|-------------|-------------|-----------------|-------|
| `{env}-postgres-password` | PostgreSQL password | 90 days | Database authentication |
| `{env}-redis-password` | Redis password | 90 days | Cache authentication |
| `{env}-spanner-key` | Spanner service account key | 90 days | Primary database |

### Application Secrets

| Secret Name | Description | Rotation Period | Usage |
|-------------|-------------|-----------------|-------|
| `{env}-jwt-secret` | JWT signing key | 30 days | Authentication tokens |
| `{env}-session-secret` | Session encryption | 30 days | User sessions |
| `{env}-webhook-secret` | Webhook signatures | 30 days | External integrations |

### API Keys

| Secret Name | Description | Rotation Period | Usage |
|-------------|-------------|-----------------|-------|
| `{env}-gemini-api-key` | Google Gemini API | Manual | AI/ML processing |
| `{env}-openai-api-key` | OpenAI API | Manual | Alternative AI processing |
| `{env}-github-token` | GitHub API token | 90 days | Repository access |

## Environment Variables and Secret Mapping

### Current Hardcoded Values (TO BE REMOVED)

```yaml
# ❌ REMOVE THESE
environment:
  POSTGRES_PASSWORD: dev_password
  DATABASE_URL: ***********************************************/ccl_local
```

### Secure Secret References (NEW IMPLEMENTATION)

```yaml
# ✅ USE THESE
environment:
  POSTGRES_PASSWORD_FILE: /run/secrets/postgres_password
  DATABASE_URL: postgresql://ccl_dev:$(cat /run/secrets/postgres_password)@postgres:5432/ccl_local
```

## Development Setup

### 1. Create Local Secret Files

```bash
mkdir -p ./secrets
echo "dev_secure_password_$(date +%s)" > ./secrets/dev-postgres-password.txt
echo "dev_redis_pass_$(date +%s)" > ./secrets/dev-redis-password.txt
echo "$(openssl rand -hex 64)" > ./secrets/dev-jwt-secret.txt
```

### 2. Update .gitignore

```gitignore
# Secrets directory
secrets/
*.secret
*.key
.env.local
.env.production
```

### 3. Use Docker Compose Override

```bash
# Use secrets-enabled compose
docker-compose -f docker-compose.base.yml -f docker-compose.secrets.yml up
```

## Production Deployment

### 1. Cloud Run Environment Variables

```bash
# Set environment variables that reference secrets
gcloud run services update $SERVICE_NAME \
  --update-env-vars="JWT_SECRET_PATH=/secrets/jwt-secret" \
  --update-env-vars="POSTGRES_PASSWORD_PATH=/secrets/postgres-password"
```

### 2. Secret Mount Configuration

```yaml
# In Cloud Run service spec
spec:
  template:
    spec:
      volumes:
      - name: secret-volume
        secret:
          secretName: prod-secrets
          items:
          - key: postgres-password
            path: postgres-password
          - key: jwt-secret  
            path: jwt-secret
```

## Security Best Practices

### 1. Least Privilege Access

- Service accounts only have access to required secrets
- Use secret-specific IAM bindings
- Regular access reviews

### 2. Secret Rotation

```bash
# Automated rotation with existing script
./scripts/security/rotate-secrets.sh --environment production database
./scripts/security/rotate-secrets.sh --environment production api-keys
```

### 3. Audit Logging

```bash
# Enable Secret Manager audit logs
gcloud logging sinks create secret-manager-audit \
  bigquery.googleapis.com/projects/$PROJECT_ID/datasets/security_audit \
  --log-filter='protoPayload.serviceName="secretmanager.googleapis.com"'
```

## Migration Process

### Phase 1: Create Secret Infrastructure
1. Create secrets in Secret Manager
2. Configure service account permissions
3. Update deployment scripts

### Phase 2: Update Docker Compose Files
1. Create secrets-enabled compose files
2. Update environment variable references
3. Test local development setup

### Phase 3: Update Production Deployment
1. Deploy services with secret references
2. Validate secret access
3. Remove hardcoded values

### Phase 4: Cleanup and Validation
1. Verify no hardcoded secrets remain
2. Test secret rotation
3. Update documentation

## Testing and Validation

### Local Development Test

```bash
# Test Docker Compose with secrets
docker-compose -f docker-compose.base.yml -f docker-compose.secrets.yml config

# Verify no hardcoded secrets
grep -r "password\|secret\|key" docker-compose*.yml | grep -v "_FILE\|_PATH"
```

### Production Validation

```bash
# Test secret access in Cloud Run
gcloud run services describe $SERVICE_NAME --region=$REGION --format="export" | grep -i secret

# Validate service health with secrets
curl -f https://$SERVICE_URL/health
```

## Troubleshooting

### Common Issues

1. **Secret not accessible**: Check service account IAM permissions
2. **Container startup fails**: Verify secret paths and environment variables  
3. **Local development issues**: Ensure secret files exist in ./secrets/

### Debug Commands

```bash
# Check secret manager access
gcloud secrets versions access latest --secret="prod-postgres-password"

# Test service account permissions  
gcloud auth activate-service-account --key-file=$SERVICE_ACCOUNT_KEY
gcloud secrets list

# Validate Docker Compose configuration
docker-compose config --services
```

## Related Documentation

- [Secret Manager API Reference](https://cloud.google.com/secret-manager/docs/reference/rest)
- [Cloud Run Secret Management](https://cloud.google.com/run/docs/configuring/secrets)
- [Episteme Security Guidelines](../../security/secure-coding-practices.md)
- [Secret Rotation Scripts](../../../scripts/security/README.md)

## Change Log

| Date | Change | Author |
|------|--------|---------|
| 2025-01-22 | Initial documentation | AI Assistant |
| | Created secret management integration guide | |
| | Defined migration process and best practices | |