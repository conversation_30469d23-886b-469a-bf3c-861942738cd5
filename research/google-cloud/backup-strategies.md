# Google Cloud Backup Strategies

## Overview
This document outlines comprehensive backup strategies for Google Cloud Platform services, specifically tailored for microservices architectures.

## Cloud Spanner Backup Strategies

### Automated Backups
```bash
# Create automatic backup schedule
gcloud spanner backups create-schedule backup-schedule-name \
    --database=database-name \
    --instance=instance-name \
    --cron-schedule="0 2 * * *" \  # Daily at 2 AM
    --retention-period=30d

# List existing backups
gcloud spanner backups list --instance=instance-name

# Restore from backup
gcloud spanner databases restore database-name-restored \
    --source-backup=backup-name \
    --source-instance=instance-name \
    --target-instance=target-instance-name
```

### Point-in-Time Recovery
```bash
# Create database with point-in-time recovery
gcloud spanner databases restore database-name-pitr \
    --source-database=source-database \
    --source-instance=source-instance \
    --restore-time=2025-01-15T10:30:00Z
```

### Cross-Region Backup Replication
```bash
# Create backup in secondary region
gcloud spanner backups copy backup-name-copy \
    --source-backup=backup-name \
    --source-instance=source-instance \
    --destination-instance=dr-instance \
    --destination-region=us-east1
```

## BigQuery Backup Strategies

### Dataset Export
```bash
# Export entire dataset
bq extract --destination_format=AVRO \
    --compression=SNAPPY \
    'project:dataset.*' \
    'gs://backup-bucket/dataset/export_$(date +%Y%m%d)/*.avro'

# Export specific table with partitioning
bq extract --destination_format=PARQUET \
    'project:dataset.table$20250115' \
    'gs://backup-bucket/table/partition_20250115.parquet'

# Scheduled export using cron
# 0 3 * * * /path/to/bigquery_backup.sh
```

### Cross-Project Dataset Copy
```bash
# Copy dataset to backup project
bq cp \
    --source_project_id=source-project \
    --destination_project_id=backup-project \
    dataset:table \
    backup_dataset:table_$(date +%Y%m%d)
```

### Table Snapshots
```bash
# Create table snapshot
bq cp dataset.table dataset.table_snapshot_$(date +%Y%m%d_%H%M%S)

# Restore from snapshot
bq cp dataset.table_snapshot_20250115_140000 dataset.table_restored
```

## Cloud Storage Backup

### Cross-Region Replication
```bash
# Enable dual-region bucket
gsutil mb -c STANDARD -l us gs://primary-bucket
gsutil mb -c STANDARD -l europe gs://backup-bucket-eu

# Sync buckets
gsutil -m rsync -r -d gs://primary-bucket gs://backup-bucket-eu

# Automated sync with Cloud Scheduler
gcloud scheduler jobs create http backup-sync-job \
    --schedule="0 1 * * *" \
    --uri="https://cloudfunctions.googleapis.com/backup-sync" \
    --http-method=POST
```

### Lifecycle Management
```bash
# Create lifecycle configuration
cat > lifecycle.json << EOF
{
  "lifecycle": {
    "rule": [
      {
        "action": {"type": "SetStorageClass", "storageClass": "NEARLINE"},
        "condition": {"age": 30}
      },
      {
        "action": {"type": "SetStorageClass", "storageClass": "COLDLINE"},
        "condition": {"age": 90}
      },
      {
        "action": {"type": "Delete"},
        "condition": {"age": 2555}  # 7 years
      }
    ]
  }
}
EOF

gsutil lifecycle set lifecycle.json gs://backup-bucket
```

## Cloud Run Application Backup

### Container Image Backup
```bash
# List all service revisions
gcloud run revisions list --service=service-name --region=us-central1

# Backup current container image
CURRENT_IMAGE=$(gcloud run services describe service-name \
    --region=us-central1 \
    --format="value(spec.template.spec.containers[0].image)")

docker pull $CURRENT_IMAGE
docker tag $CURRENT_IMAGE gcr.io/backup-project/service-name:backup-$(date +%Y%m%d)
docker push gcr.io/backup-project/service-name:backup-$(date +%Y%m%d)
```

### Service Configuration Backup
```bash
# Export service configuration
gcloud run services describe service-name \
    --region=us-central1 \
    --format=export > service-backup-$(date +%Y%m%d).yaml

# Store in Cloud Storage
gsutil cp service-backup-$(date +%Y%m%d).yaml \
    gs://config-backups/cloud-run/
```

## Redis/Memorystore Backup

### RDB Snapshots
```bash
# Create manual backup
gcloud redis instances export redis-instance \
    --destination=gs://redis-backups/backup-$(date +%Y%m%d_%H%M%S).rdb \
    --region=us-central1

# Schedule automated backups
gcloud scheduler jobs create app-engine redis-backup-job \
    --schedule="0 */6 * * *" \  # Every 6 hours
    --service=backup-service \
    --version=v1 \
    --relative-url=/backup-redis
```

### Point-in-Time Recovery
```bash
# Enable backup for new instance
gcloud redis instances create redis-instance-with-backup \
    --size=5 \
    --region=us-central1 \
    --redis-version=redis_6_x \
    --redis-config="save 900 1,save 300 10,save 60 10000"
```

## Database Backup Automation

### Spanner Backup Script
```bash
#!/bin/bash
# spanner_backup.sh

PROJECT_ID="your-project-id"
INSTANCE_ID="your-instance-id"
DATABASE_ID="your-database-id"
BACKUP_BUCKET="gs://your-backup-bucket"
RETENTION_DAYS=30

# Create backup
BACKUP_ID="backup-$(date +%Y%m%d_%H%M%S)"
EXPIRATION_DATE=$(date -d "+${RETENTION_DAYS} days" '+%Y-%m-%dT%H:%M:%SZ')

gcloud spanner backups create $BACKUP_ID \
    --database=$DATABASE_ID \
    --instance=$INSTANCE_ID \
    --expiration-date=$EXPIRATION_DATE \
    --project=$PROJECT_ID

# Verify backup
gcloud spanner backups describe $BACKUP_ID \
    --instance=$INSTANCE_ID \
    --project=$PROJECT_ID

# Log backup completion
echo "$(date): Backup $BACKUP_ID created successfully" >> /var/log/spanner-backup.log
```

### BigQuery Backup Script
```bash
#!/bin/bash
# bigquery_backup.sh

PROJECT_ID="your-project-id"
DATASET_ID="your-dataset"
BACKUP_BUCKET="gs://your-backup-bucket"
DATE_SUFFIX=$(date +%Y%m%d)

# Get all tables in dataset
TABLES=$(bq ls -n 1000 $PROJECT_ID:$DATASET_ID | grep TABLE | awk '{print $1}')

for table in $TABLES; do
    echo "Backing up table: $table"
    
    # Export table
    bq extract \
        --destination_format=AVRO \
        --compression=SNAPPY \
        "$PROJECT_ID:$DATASET_ID.$table" \
        "$BACKUP_BUCKET/dataset_backup/$DATE_SUFFIX/$table/*.avro"
        
    if [ $? -eq 0 ]; then
        echo "$(date): Table $table backup completed successfully"
    else
        echo "$(date): Table $table backup failed" >&2
    fi
done
```

## Backup Monitoring and Alerting

### Cloud Monitoring Metrics
```bash
# Create backup success metric
gcloud logging metrics create backup_success_count \
    --description="Count of successful backups" \
    --log-filter='resource.type="cloud_function"
                  AND jsonPayload.message="Backup completed successfully"'

# Create backup failure alert
gcloud alpha monitoring policies create \
    --policy-from-file=backup-failure-alert.yaml
```

### Backup Failure Alert Policy
```yaml
# backup-failure-alert.yaml
displayName: "Backup Failure Alert"
conditions:
  - displayName: "Backup failure detected"
    conditionThreshold:
      filter: 'resource.type="cloud_function" AND log_name="backup-function"'
      comparison: COMPARISON_GREATER_THAN
      thresholdValue: 0
      duration: 60s
alertStrategy:
  notificationRateLimit:
    period: 300s
notificationChannels:
  - "projects/PROJECT_ID/notificationChannels/CHANNEL_ID"
```

## Backup Validation and Testing

### Restore Testing Script
```bash
#!/bin/bash
# restore_test.sh

# Test Spanner restore
TEST_DATABASE="test-restore-$(date +%Y%m%d)"
LATEST_BACKUP=$(gcloud spanner backups list \
    --instance=$INSTANCE_ID \
    --format="value(name)" \
    --sort-by="~createTime" \
    --limit=1)

echo "Testing restore from backup: $LATEST_BACKUP"

gcloud spanner databases restore $TEST_DATABASE \
    --source-backup=$LATEST_BACKUP \
    --instance=$INSTANCE_ID

# Validate data integrity
gcloud spanner databases execute-sql $TEST_DATABASE \
    --instance=$INSTANCE_ID \
    --sql="SELECT COUNT(*) as row_count FROM information_schema.tables"

# Cleanup test database
gcloud spanner databases delete $TEST_DATABASE --instance=$INSTANCE_ID --quiet
```

### Backup Verification
```bash
# Verify backup integrity
gsutil stat gs://backup-bucket/latest/backup.tar.gz
gsutil hash gs://backup-bucket/latest/backup.tar.gz

# Test backup file extraction
gsutil cp gs://backup-bucket/latest/backup.tar.gz /tmp/
tar -tzf /tmp/backup.tar.gz | head -10
```

## Security and Access Control

### Backup Access Control
```bash
# Create backup service account
gcloud iam service-accounts create backup-service-account \
    --display-name="Backup Service Account"

# Grant minimal required permissions
gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:backup-service-account@$PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/spanner.backupAdmin"

gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:backup-service-account@$PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/bigquery.dataEditor"

gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:backup-service-account@$PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/storage.admin"
```

### Backup Encryption
```bash
# Create KMS key for backup encryption
gcloud kms keyrings create backup-keyring --location=us-central1

gcloud kms keys create backup-key \
    --location=us-central1 \
    --keyring=backup-keyring \
    --purpose=encryption

# Use customer-managed encryption for backups
gsutil kms encryption -k projects/$PROJECT_ID/locations/us-central1/keyRings/backup-keyring/cryptoKeys/backup-key gs://backup-bucket
```

## Compliance and Retention

### Retention Policies
```bash
# Set bucket retention policy
gsutil retention set 2555d gs://backup-bucket  # 7 years

# Lock retention policy (irreversible)
gsutil retention lock gs://backup-bucket
```

### Audit Logging
```bash
# Enable audit logging for backup operations
gcloud logging sinks create backup-audit-sink \
    bigquery.googleapis.com/projects/$PROJECT_ID/datasets/audit_logs \
    --log-filter='protoPayload.serviceName="spanner.googleapis.com"
                  AND protoPayload.methodName="google.spanner.admin.database.v1.DatabaseAdmin.CreateBackup"'
```

## Cost Optimization

### Storage Class Optimization
```bash
# Move old backups to cheaper storage
gsutil -m cp -r gs://backup-bucket/2024* gs://coldline-backup-bucket/
gsutil -m rm -r gs://backup-bucket/2024*
```

### Backup Lifecycle Management
```json
{
  "lifecycle": {
    "rule": [
      {
        "action": {"type": "SetStorageClass", "storageClass": "NEARLINE"},
        "condition": {"age": 7, "matchesPrefix": ["daily-backup/"]}
      },
      {
        "action": {"type": "SetStorageClass", "storageClass": "COLDLINE"},
        "condition": {"age": 30, "matchesPrefix": ["daily-backup/"]}
      },
      {
        "action": {"type": "Delete"},
        "condition": {"age": 2555, "matchesPrefix": ["daily-backup/"]}
      }
    ]
  }
}
```

## References

- [Cloud Spanner Backup Documentation](https://cloud.google.com/spanner/docs/backup)
- [BigQuery Data Export](https://cloud.google.com/bigquery/docs/exporting-data)
- [Cloud Storage Lifecycle Management](https://cloud.google.com/storage/docs/lifecycle)
- [Memorystore Backup and Recovery](https://cloud.google.com/memorystore/docs/redis/backup-and-recovery)