#!/bin/bash
# Test script to validate production runbook procedures
# This script tests the accessibility and basic functionality of runbook commands

set -e

echo "🔍 Testing Production Runbook Procedures..."

# Test 1: Check if all referenced files exist
echo "✅ Testing file references..."

RUNBOOK_FILES=(
    "docs/PRODUCTION_RUNBOOK.md"
    "docs/operations/escalation-procedures.md"
    "docs/operations/troubleshooting-guide.md"
    "docs/operations/recovery-procedures.md"
    "research/operations/incident-response.md"
    "research/documentation/runbook-templates.md"
)

for file in "${RUNBOOK_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "  ✓ $file exists"
    else
        echo "  ❌ $file missing"
        exit 1
    fi
done

# Test 2: Validate markdown structure
echo "✅ Testing markdown structure..."
for file in "${RUNBOOK_FILES[@]}"; do
    if grep -q "^# " "$file"; then
        echo "  ✓ $file has proper heading structure"
    else
        echo "  ❌ $file missing main heading"
        exit 1
    fi
done

# Test 3: Check for required sections in main runbook
echo "✅ Testing required sections in production runbook..."
REQUIRED_SECTIONS=(
    "Platform Overview"
    "Service Health Dashboard"
    "Cross-Service Incident Response"
    "Platform-Wide Troubleshooting"
    "Escalation Procedures"
    "Disaster Recovery"
    "Emergency Contacts"
)

for section in "${REQUIRED_SECTIONS[@]}"; do
    if grep -q "$section" "docs/PRODUCTION_RUNBOOK.md"; then
        echo "  ✓ Found section: $section"
    else
        echo "  ❌ Missing section: $section"
        exit 1
    fi
done

# Test 4: Verify cross-references between documents
echo "✅ Testing cross-references..."
if grep -q "escalation-procedures.md" "docs/PRODUCTION_RUNBOOK.md"; then
    echo "  ✓ Production runbook references escalation procedures"
else
    echo "  ⚠️ Production runbook should reference escalation procedures"
fi

if grep -q "troubleshooting-guide.md" "docs/PRODUCTION_RUNBOOK.md"; then
    echo "  ✓ Production runbook references troubleshooting guide"
else
    echo "  ⚠️ Production runbook should reference troubleshooting guide"
fi

# Test 5: Check for emergency contact information
echo "✅ Testing emergency contact information..."
if grep -q "Emergency Contacts" "docs/PRODUCTION_RUNBOOK.md"; then
    echo "  ✓ Emergency contacts section found"
else
    echo "  ❌ Emergency contacts section missing"
    exit 1
fi

# Test 6: Validate script references (check if they're documented)
echo "✅ Testing script references..."
SCRIPT_REFERENCES=$(grep -o '\./scripts/[^[:space:]]*\.sh' docs/PRODUCTION_RUNBOOK.md | head -5)
if [ -n "$SCRIPT_REFERENCES" ]; then
    echo "  ✓ Script references found in runbook"
    echo "  📝 Sample script references:"
    echo "$SCRIPT_REFERENCES" | head -3 | sed 's/^/    /'
else
    echo "  ⚠️ No script references found - this is expected for documentation"
fi

# Test 7: Check for severity levels and response times
echo "✅ Testing incident response structure..."
if grep -q "P0\|P1\|P2\|P3" "docs/PRODUCTION_RUNBOOK.md"; then
    echo "  ✓ Incident severity levels defined"
else
    echo "  ❌ Incident severity levels missing"
    exit 1
fi

if grep -q "minutes\|hours" "docs/PRODUCTION_RUNBOOK.md"; then
    echo "  ✓ Response time targets specified"
else
    echo "  ❌ Response time targets missing"
    exit 1
fi

echo ""
echo "🎉 All production runbook tests passed!"
echo "📊 Summary:"
echo "  - ${#RUNBOOK_FILES[@]} documentation files created"
echo "  - ${#REQUIRED_SECTIONS[@]} required sections verified"
echo "  - Cross-references validated"
echo "  - Emergency procedures documented"
echo ""
echo "✅ Production runbook is ready for use!"