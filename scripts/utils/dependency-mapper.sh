#!/bin/bash
# Episteme Dependency Mapping Script
#
# This script provides easy-to-use commands for dependency analysis and mapping.

set -e

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
TOOLS_DIR="$PROJECT_ROOT/tools/dependency-analyzer"
VENV_DIR="$TOOLS_DIR/.venv"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

echo_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

echo_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

echo_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Setup virtual environment and install dependencies
setup_env() {
    echo_info "Setting up dependency analyzer environment..."
    
    cd "$TOOLS_DIR"
    
    if [ ! -d "$VENV_DIR" ]; then
        echo_info "Creating virtual environment..."
        python3 -m venv "$VENV_DIR"
    fi
    
    source "$VENV_DIR/bin/activate"
    
    echo_info "Installing dependencies..."
    pip install -r requirements.txt
    
    echo_success "Environment setup complete"
}

# Run full dependency analysis
analyze_all() {
    echo_info "Running comprehensive dependency analysis..."
    
    cd "$TOOLS_DIR"
    source "$VENV_DIR/bin/activate"
    
    python dependency_analyzer.py \
        --analyze-all \
        --validate-dependencies \
        --export "$PROJECT_ROOT/docs/architecture/dependency-analysis.json" \
        --project-root "$PROJECT_ROOT"
    
    echo_success "Analysis complete. Results saved to docs/architecture/dependency-analysis.json"
}

# Generate dependency graph visualization
generate_graph() {
    echo_info "Generating dependency graph visualization..."
    
    cd "$TOOLS_DIR"
    source "$VENV_DIR/bin/activate"
    
    python dependency_analyzer.py \
        --generate-graph \
        --project-root "$PROJECT_ROOT"
    
    if [ -f "dependency_graph.png" ]; then
        mv dependency_graph.png "$PROJECT_ROOT/docs/architecture/"
        echo_success "Dependency graph saved to docs/architecture/dependency_graph.png"
    fi
}

# Run impact analysis for a specific service
impact_analysis() {
    local service="$1"
    
    if [ -z "$service" ]; then
        echo_error "Service name required for impact analysis"
        echo "Usage: $0 impact <service-name>"
        echo "Available services: analysis-engine, collaboration, web, marketplace, pattern-mining, query-intelligence"
        exit 1
    fi
    
    echo_info "Running impact analysis for service: $service"
    
    cd "$TOOLS_DIR"
    source "$VENV_DIR/bin/activate"
    
    python dependency_analyzer.py \
        --impact-analysis "$service" \
        --project-root "$PROJECT_ROOT"
}

# Validate dependencies
validate() {
    echo_info "Validating service dependencies..."
    
    cd "$TOOLS_DIR"
    source "$VENV_DIR/bin/activate"
    
    python dependency_analyzer.py \
        --validate-dependencies \
        --project-root "$PROJECT_ROOT"
}

# Generate comprehensive dependency report
generate_report() {
    echo_info "Generating comprehensive dependency report..."
    
    # Ensure output directory exists
    mkdir -p "$PROJECT_ROOT/docs/architecture/reports"
    
    cd "$TOOLS_DIR"
    source "$VENV_DIR/bin/activate"
    
    # Run analysis and export to JSON
    python dependency_analyzer.py \
        --analyze-all \
        --validate-dependencies \
        --export "$PROJECT_ROOT/docs/architecture/reports/dependency-report-$(date +%Y%m%d).json" \
        --project-root "$PROJECT_ROOT"
    
    # Generate graph
    python dependency_analyzer.py \
        --generate-graph \
        --project-root "$PROJECT_ROOT"
    
    if [ -f "dependency_graph.png" ]; then
        mv dependency_graph.png "$PROJECT_ROOT/docs/architecture/reports/dependency-graph-$(date +%Y%m%d).png"
    fi
    
    echo_success "Comprehensive report generated in docs/architecture/reports/"
}

# Update dependency documentation
update_docs() {
    echo_info "Updating dependency documentation..."
    
    # Run analysis to get latest data
    analyze_all
    
    # Generate updated graph
    generate_graph
    
    # Create a summary report
    cat > "$PROJECT_ROOT/docs/architecture/dependency-summary.md" << EOF
# Dependency Analysis Summary

**Generated**: $(date)  
**Script**: scripts/dependency-mapper.sh  

## Quick Stats

This summary is automatically generated from the dependency analyzer.

### Services Discovered
- analysis-engine (Rust/Axum)
- collaboration (TypeScript/Express+SocketIO)
- web (TypeScript/Next.js)
- marketplace (Go/Gin)
- pattern-mining (Python/FastAPI)
- query-intelligence (Python/FastAPI)

### Critical Dependencies
- Google Cloud Spanner (Primary database)
- Redis (Caching layer)
- Analysis Engine (Core service)

### External Services
- Google Cloud Platform services
- Pinecone (Vector search)
- OpenAI API (Language models)

## Files Updated
- [Service Dependencies](./service-dependencies.md)
- [Dependency Graph](./dependency-graph.md)
- [Data Flows](./data-flows.md)
- [Analysis JSON](./dependency-analysis.json)
- [Visual Graph](./dependency_graph.png)

## Next Steps
1. Review dependency accuracy
2. Update service documentation
3. Validate health check endpoints
4. Test failover procedures

For detailed analysis, see the complete documentation files above.
EOF
    
    echo_success "Documentation updated successfully"
}

# Show usage information
usage() {
    echo "Episteme Dependency Mapping Tool"
    echo ""
    echo "Usage: $0 <command> [options]"
    echo ""
    echo "Commands:"
    echo "  setup           Set up the analysis environment"
    echo "  analyze         Run comprehensive dependency analysis"
    echo "  graph           Generate dependency graph visualization"
    echo "  impact <service> Run impact analysis for specific service"
    echo "  validate        Validate dependency configurations"
    echo "  report          Generate comprehensive dependency report"
    echo "  update-docs     Update all dependency documentation"
    echo "  help            Show this usage information"
    echo ""
    echo "Examples:"
    echo "  $0 setup"
    echo "  $0 analyze"
    echo "  $0 graph"
    echo "  $0 impact analysis-engine"
    echo "  $0 validate"
    echo "  $0 report"
    echo "  $0 update-docs"
    echo ""
}

# Main command dispatcher
main() {
    case "${1:-help}" in
        setup)
            setup_env
            ;;
        analyze)
            setup_env
            analyze_all
            ;;
        graph)
            setup_env
            generate_graph
            ;;
        impact)
            setup_env
            impact_analysis "$2"
            ;;
        validate)
            setup_env
            validate
            ;;
        report)
            setup_env
            generate_report
            ;;
        update-docs)
            setup_env
            update_docs
            ;;
        help|--help|-h)
            usage
            ;;
        *)
            echo_error "Unknown command: $1"
            usage
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"