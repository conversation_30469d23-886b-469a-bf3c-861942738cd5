#!/bin/bash

# SQL Injection Prevention Tests
# 
# This script validates that all database queries in the analysis engine
# are properly protected against SQL injection attacks.

set -e

echo "🔍 Starting SQL Injection Prevention Tests..."

# Change to the analysis engine directory
cd "$(dirname "$0")/../services/analysis-engine"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Test counters
TESTS_PASSED=0
TESTS_FAILED=0
TESTS_TOTAL=0

# Function to print test results
print_test_result() {
    local test_name="$1"
    local result="$2"
    local details="$3"
    
    TESTS_TOTAL=$((TESTS_TOTAL + 1))
    
    if [ "$result" = "PASS" ]; then
        echo -e "${GREEN}✅ PASS${NC}: $test_name"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        echo -e "${RED}❌ FAIL${NC}: $test_name"
        if [ -n "$details" ]; then
            echo -e "   ${YELLOW}Details: $details${NC}"
        fi
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
}

# Function to check for dangerous SQL patterns in source code
check_source_code_patterns() {
    echo
    echo "📁 Checking source code for unsafe SQL patterns..."
    
    # Check for string concatenation in SQL queries
    if grep -r "format!" src/ | grep -i -E "(select|insert|update|delete|where|limit|offset)" | grep -v test; then
        print_test_result "Source code SQL concatenation check" "FAIL" "Found format! usage in SQL contexts"
    else
        print_test_result "Source code SQL concatenation check" "PASS"
    fi
    
    # Check for direct string interpolation in SQL
    if grep -r -E "\$\{.*\}" src/ | grep -i -E "(select|insert|update|delete|where)"; then
        print_test_result "Source code string interpolation check" "FAIL" "Found string interpolation in SQL contexts"
    else
        print_test_result "Source code string interpolation check" "PASS"
    fi
    
    # Check for unparameterized LIMIT/OFFSET (should use @limit/@offset)
    if grep -r "LIMIT [0-9]" src/ | grep -v test | grep -v "LIMIT @"; then
        print_test_result "Parameterized LIMIT check" "FAIL" "Found unparameterized LIMIT clauses"
    else
        print_test_result "Parameterized LIMIT check" "PASS"
    fi
    
    # Check that all SQL queries use Statement::new with proper parameters
    sql_statements=$(grep -r "Statement::new" src/ | wc -l)
    if [ "$sql_statements" -lt 20 ]; then
        print_test_result "Parameterized statements usage" "FAIL" "Expected at least 20 parameterized statements, found $sql_statements"
    else
        print_test_result "Parameterized statements usage" "PASS" "Found $sql_statements parameterized statements"
    fi
}

# Function to run unit tests for SQL injection prevention
run_unit_tests() {
    echo
    echo "🧪 Running SQL injection prevention unit tests..."
    
    # Run the specific SQL injection tests
    if cargo test sql_injection --lib -- --nocapture; then
        print_test_result "SQL injection unit tests" "PASS"
    else
        print_test_result "SQL injection unit tests" "FAIL" "Some unit tests failed"
    fi
    
    # Run security module tests
    if cargo test security::sql_injection_tests --lib -- --nocapture; then
        print_test_result "Security module SQL injection tests" "PASS"
    else
        print_test_result "Security module SQL injection tests" "FAIL" "Security tests failed"
    fi
}

# Function to test query validation
test_query_validation() {
    echo
    echo "🔍 Testing query validation logic..."
    
    # Test that the validator catches unsafe patterns
    if cargo test test_detect_unsafe_query_patterns --lib -- --nocapture; then
        print_test_result "Unsafe pattern detection" "PASS"
    else
        print_test_result "Unsafe pattern detection" "FAIL"
    fi
    
    # Test that the validator allows safe patterns
    if cargo test test_allow_safe_query_patterns --lib -- --nocapture; then
        print_test_result "Safe pattern validation" "PASS"
    else
        print_test_result "Safe pattern validation" "FAIL"
    fi
    
    # Test parameter binding validation
    if cargo test test_parameter_binding_validation --lib -- --nocapture; then
        print_test_result "Parameter binding validation" "PASS"
    else
        print_test_result "Parameter binding validation" "FAIL"
    fi
}

# Function to test injection resistance
test_injection_resistance() {
    echo
    echo "🛡️ Testing SQL injection resistance..."
    
    # Test with malicious inputs
    if cargo test test_sql_injection_resistance --lib -- --nocapture; then
        print_test_result "SQL injection resistance" "PASS"
    else
        print_test_result "SQL injection resistance" "FAIL"
    fi
    
    # Test numeric injection patterns
    if cargo test test_numeric_injection_patterns --lib -- --nocapture; then
        print_test_result "Numeric injection resistance" "PASS"
    else
        print_test_result "Numeric injection resistance" "FAIL"
    fi
}

# Function to check database dependencies
check_database_dependencies() {
    echo
    echo "📦 Checking database security dependencies..."
    
    # Check that we're using secure database clients
    if grep -q "google-cloud-spanner" Cargo.toml; then
        print_test_result "Secure database client dependency" "PASS" "Using google-cloud-spanner"
    else
        print_test_result "Secure database client dependency" "FAIL" "Not using recommended secure client"
    fi
    
    # Check for SQL injection testing dependencies
    if grep -q "regex" Cargo.toml; then
        print_test_result "SQL validation dependencies" "PASS"
    else
        print_test_result "SQL validation dependencies" "FAIL" "Missing regex dependency for validation"
    fi
}

# Function to validate logging is in place
check_security_logging() {
    echo
    echo "📝 Checking database security logging..."
    
    # Check for security-related logging in database operations
    if grep -r "tracing::" src/storage/ | grep -E "(warn|error|info)" | wc -l | awk '{if($1 >= 10) print "PASS"; else print "FAIL"}' | grep -q "PASS"; then
        print_test_result "Database security logging" "PASS"
    else
        print_test_result "Database security logging" "FAIL" "Insufficient security logging in database operations"
    fi
}

# Function to check for proper error handling
check_error_handling() {
    echo
    echo "⚠️ Checking SQL error handling..."
    
    # Check that database errors don't leak sensitive information
    if grep -r "map_err" src/storage/ | wc -l | awk '{if($1 >= 5) print "PASS"; else print "FAIL"}' | grep -q "PASS"; then
        print_test_result "SQL error handling" "PASS"
    else
        print_test_result "SQL error handling" "FAIL" "Insufficient error handling in database operations"
    fi
}

# Main test execution
main() {
    echo "🚀 SQL Injection Prevention Test Suite"
    echo "======================================"
    
    # Ensure we can build the project first
    echo "🔨 Building project..."
    if ! cargo build --lib; then
        echo -e "${RED}❌ Build failed. Cannot run SQL injection tests.${NC}"
        exit 1
    fi
    
    # Run all test categories
    check_source_code_patterns
    check_database_dependencies
    run_unit_tests
    test_query_validation
    test_injection_resistance
    check_security_logging
    check_error_handling
    
    # Summary
    echo
    echo "📊 Test Summary"
    echo "==============="
    echo -e "Total tests: $TESTS_TOTAL"
    echo -e "${GREEN}Passed: $TESTS_PASSED${NC}"
    echo -e "${RED}Failed: $TESTS_FAILED${NC}"
    
    if [ $TESTS_FAILED -eq 0 ]; then
        echo
        echo -e "${GREEN}🎉 All SQL injection prevention tests passed!${NC}"
        echo "✅ Database operations are properly protected against SQL injection attacks."
        exit 0
    else
        echo
        echo -e "${RED}⚠️ Some SQL injection prevention tests failed.${NC}"
        echo "❌ Please review and fix the identified security issues."
        exit 1
    fi
}

# Run the tests
main "$@"