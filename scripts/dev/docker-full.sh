#!/bin/bash
# Convenience script for running full environment with monitoring

set -e

SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$( cd "${SCRIPT_DIR}/.." && pwd )"
DOCKER_DIR="${PROJECT_ROOT}/docker"

cd "${DOCKER_DIR}"

# Run with base + dev + monitoring configurations
docker-compose \
    -f docker-compose.base.yml \
    -f docker-compose.dev.yml \
    -f docker-compose.monitoring.yml \
    "$@"