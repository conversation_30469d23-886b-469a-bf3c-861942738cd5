#!/bin/bash

# Episteme Platform Backup Script
# Comprehensive backup automation for all platform components

set -euo pipefail

# Configuration
PROJECT_ID="${PROJECT_ID:-episteme-prod}"
BACKUP_BUCKET="${BACKUP_BUCKET:-episteme-backups}"
DATE_SUFFIX=$(date +%Y%m%d_%H%M%S)
LOG_FILE="/var/log/episteme-backup-${DATE_SUFFIX}.log"

# Services configuration
SERVICES=("analysis-engine" "query-intelligence" "pattern-mining" "marketplace")
SPANNER_INSTANCE="episteme-prod-instance"
SPANNER_DATABASE="episteme-prod"
REDIS_INSTANCE="episteme-cache"
BIGQUERY_DATASETS=("analytics" "monitoring" "audit")

# Logging function
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') [$1] $2" | tee -a "$LOG_FILE"
}

# Error handling
error_exit() {
    log "ERROR" "$1"
    exit 1
}

# Cleanup function
cleanup() {
    log "INFO" "Cleaning up temporary files..."
    rm -rf /tmp/episteme-backup-${DATE_SUFFIX} || true
}

trap cleanup EXIT

# Main backup function
main() {
    log "INFO" "Starting Episteme platform backup process"
    
    # Create working directory
    mkdir -p "/tmp/episteme-backup-${DATE_SUFFIX}"
    cd "/tmp/episteme-backup-${DATE_SUFFIX}"
    
    # Backup Spanner database
    backup_spanner
    
    # Backup BigQuery datasets
    backup_bigquery
    
    # Backup Redis cache
    backup_redis
    
    # Backup application configurations
    backup_configurations
    
    # Backup container images
    backup_container_images
    
    # Verify backups
    verify_backups
    
    # Cleanup old backups
    cleanup_old_backups
    
    log "INFO" "Backup process completed successfully"
}

# Spanner backup function
backup_spanner() {
    log "INFO" "Starting Spanner database backup"
    
    local backup_id="backup-${DATE_SUFFIX}"
    local expiration_date=$(date -d "+30 days" '+%Y-%m-%dT%H:%M:%SZ')
    
    # Create backup
    gcloud spanner backups create "$backup_id" \
        --database="$SPANNER_DATABASE" \
        --instance="$SPANNER_INSTANCE" \
        --expiration-date="$expiration_date" \
        --project="$PROJECT_ID" || error_exit "Spanner backup creation failed"
    
    # Wait for backup completion
    local backup_state=""
    local attempts=0
    while [ "$backup_state" != "READY" ] && [ $attempts -lt 60 ]; do
        backup_state=$(gcloud spanner backups describe "$backup_id" \
            --instance="$SPANNER_INSTANCE" \
            --project="$PROJECT_ID" \
            --format="value(state)")
        
        if [ "$backup_state" = "CREATING" ]; then
            log "INFO" "Spanner backup still creating... (attempt $((attempts + 1))/60)"
            sleep 30
            ((attempts++))
        elif [ "$backup_state" = "READY" ]; then
            log "INFO" "Spanner backup completed successfully"
            break
        else
            error_exit "Spanner backup failed with state: $backup_state"
        fi
    done
    
    if [ $attempts -eq 60 ]; then
        error_exit "Spanner backup timed out after 30 minutes"
    fi
    
    # Create cross-region backup copy
    log "INFO" "Creating cross-region backup copy"
    gcloud spanner backups copy "${backup_id}-dr" \
        --source-backup="$backup_id" \
        --source-instance="$SPANNER_INSTANCE" \
        --destination-instance="episteme-dr-instance" \
        --destination-region="us-east1" \
        --project="$PROJECT_ID" || log "WARN" "Cross-region backup copy failed"
    
    log "INFO" "Spanner backup process completed"
}

# BigQuery backup function
backup_bigquery() {
    log "INFO" "Starting BigQuery datasets backup"
    
    for dataset in "${BIGQUERY_DATASETS[@]}"; do
        log "INFO" "Backing up BigQuery dataset: $dataset"
        
        # Get list of tables in dataset
        local tables
        tables=$(bq ls -n 1000 "$PROJECT_ID:$dataset" | grep TABLE | awk '{print $1}' || true)
        
        if [ -z "$tables" ]; then
            log "WARN" "No tables found in dataset $dataset"
            continue
        fi
        
        # Create dataset backup directory
        mkdir -p "bigquery/$dataset"
        
        # Export each table
        while IFS= read -r table; do
            if [ -n "$table" ]; then
                log "INFO" "Exporting table: $dataset.$table"
                
                bq extract \
                    --destination_format=AVRO \
                    --compression=SNAPPY \
                    "$PROJECT_ID:$dataset.$table" \
                    "gs://$BACKUP_BUCKET/bigquery/$dataset/$DATE_SUFFIX/$table/*.avro" || \
                    log "ERROR" "Failed to export table $dataset.$table"
            fi
        done <<< "$tables"
        
        # Create dataset metadata backup
        bq show --format=prettyjson "$PROJECT_ID:$dataset" > "bigquery/$dataset/metadata.json"
    done
    
    log "INFO" "BigQuery backup process completed"
}

# Redis backup function
backup_redis() {
    log "INFO" "Starting Redis cache backup"
    
    # Export Redis instance
    gcloud redis instances export "$REDIS_INSTANCE" \
        --destination="gs://$BACKUP_BUCKET/redis/cache-${DATE_SUFFIX}.rdb" \
        --region="us-central1" \
        --project="$PROJECT_ID" || error_exit "Redis backup failed"
    
    # Wait for export completion
    local export_status=""
    local attempts=0
    while [ "$export_status" != "DONE" ] && [ $attempts -lt 30 ]; do
        export_status=$(gcloud redis operations list \
            --filter="target:$REDIS_INSTANCE AND operationType:EXPORT" \
            --format="value(status)" \
            --limit=1 \
            --project="$PROJECT_ID")
        
        if [ "$export_status" = "RUNNING" ]; then
            log "INFO" "Redis export still running... (attempt $((attempts + 1))/30)"
            sleep 60
            ((attempts++))
        elif [ "$export_status" = "DONE" ]; then
            log "INFO" "Redis backup completed successfully"
            break
        else
            error_exit "Redis backup failed with status: $export_status"
        fi
    done
    
    if [ $attempts -eq 30 ]; then
        error_exit "Redis backup timed out after 30 minutes"
    fi
    
    log "INFO" "Redis backup process completed"
}

# Configuration backup function
backup_configurations() {
    log "INFO" "Starting application configurations backup"
    
    mkdir -p "configurations"
    
    # Backup Cloud Run service configurations
    for service in "${SERVICES[@]}"; do
        log "INFO" "Backing up configuration for service: $service"
        
        gcloud run services describe "$service" \
            --region="us-central1" \
            --project="$PROJECT_ID" \
            --format=export > "configurations/${service}-config.yaml" || \
            log "ERROR" "Failed to backup configuration for $service"
    done
    
    # Backup secrets list (metadata only)
    gcloud secrets list \
        --project="$PROJECT_ID" \
        --format="table(name,createTime,labels)" > "configurations/secrets-list.txt"
    
    # Backup IAM policies
    gcloud projects get-iam-policy "$PROJECT_ID" \
        --format=json > "configurations/iam-policy.json"
    
    # Backup network configurations
    gcloud compute networks list \
        --project="$PROJECT_ID" \
        --format=json > "configurations/networks.json"
    
    gcloud compute firewall-rules list \
        --project="$PROJECT_ID" \
        --format=json > "configurations/firewall-rules.json"
    
    # Upload configurations to secure storage
    gsutil -m cp -r configurations "gs://$BACKUP_BUCKET/config/$DATE_SUFFIX/"
    
    log "INFO" "Configuration backup process completed"
}

# Container images backup function
backup_container_images() {
    log "INFO" "Starting container images backup"
    
    mkdir -p "images"
    
    for service in "${SERVICES[@]}"; do
        log "INFO" "Backing up container image for service: $service"
        
        # Get current image
        local current_image
        current_image=$(gcloud run services describe "$service" \
            --region="us-central1" \
            --project="$PROJECT_ID" \
            --format="value(spec.template.spec.containers[0].image)")
        
        if [ -n "$current_image" ]; then
            # Pull and tag image
            docker pull "$current_image" || log "ERROR" "Failed to pull image for $service"
            
            # Tag for backup
            local backup_tag="gcr.io/$PROJECT_ID/$service:backup-$DATE_SUFFIX"
            docker tag "$current_image" "$backup_tag"
            
            # Push backup image
            docker push "$backup_tag" || log "ERROR" "Failed to push backup image for $service"
            
            # Record image information
            echo "$service,$current_image,$backup_tag,$(date)" >> "images/image-manifest.csv"
        else
            log "ERROR" "Could not determine current image for $service"
        fi
    done
    
    # Upload image manifest
    gsutil cp "images/image-manifest.csv" "gs://$BACKUP_BUCKET/images/$DATE_SUFFIX/"
    
    log "INFO" "Container images backup process completed"
}

# Backup verification function
verify_backups() {
    log "INFO" "Starting backup verification"
    
    # Verify Spanner backup
    local backup_id="backup-${DATE_SUFFIX}"
    local backup_size
    backup_size=$(gcloud spanner backups describe "$backup_id" \
        --instance="$SPANNER_INSTANCE" \
        --project="$PROJECT_ID" \
        --format="value(sizeBytes)")
    
    if [ "$backup_size" -gt 0 ]; then
        log "INFO" "Spanner backup verified: $backup_size bytes"
    else
        log "ERROR" "Spanner backup verification failed"
    fi
    
    # Verify BigQuery exports
    for dataset in "${BIGQUERY_DATASETS[@]}"; do
        local file_count
        file_count=$(gsutil ls "gs://$BACKUP_BUCKET/bigquery/$dataset/$DATE_SUFFIX/**" 2>/dev/null | wc -l || echo "0")
        
        if [ "$file_count" -gt 0 ]; then
            log "INFO" "BigQuery dataset $dataset verified: $file_count files"
        else
            log "ERROR" "BigQuery dataset $dataset verification failed"
        fi
    done
    
    # Verify Redis backup
    if gsutil stat "gs://$BACKUP_BUCKET/redis/cache-${DATE_SUFFIX}.rdb" >/dev/null 2>&1; then
        log "INFO" "Redis backup verified"
    else
        log "ERROR" "Redis backup verification failed"
    fi
    
    # Verify configurations
    local config_count
    config_count=$(gsutil ls "gs://$BACKUP_BUCKET/config/$DATE_SUFFIX/**" 2>/dev/null | wc -l || echo "0")
    
    if [ "$config_count" -gt 0 ]; then
        log "INFO" "Configuration backup verified: $config_count files"
    else
        log "ERROR" "Configuration backup verification failed"
    fi
    
    log "INFO" "Backup verification completed"
}

# Cleanup old backups function
cleanup_old_backups() {
    log "INFO" "Starting cleanup of old backups"
    
    # Clean up Spanner backups older than 30 days
    local old_backups
    old_backups=$(gcloud spanner backups list \
        --instance="$SPANNER_INSTANCE" \
        --project="$PROJECT_ID" \
        --filter="createTime < $(date -d '30 days ago' '+%Y-%m-%d')" \
        --format="value(name)")
    
    while IFS= read -r backup; do
        if [ -n "$backup" ]; then
            log "INFO" "Deleting old Spanner backup: $backup"
            gcloud spanner backups delete "$backup" \
                --instance="$SPANNER_INSTANCE" \
                --project="$PROJECT_ID" \
                --quiet || log "ERROR" "Failed to delete backup $backup"
        fi
    done <<< "$old_backups"
    
    # Clean up Cloud Storage backups older than 90 days
    gsutil -m rm -r "gs://$BACKUP_BUCKET/**/$(date -d '90 days ago' '+%Y%m%d')*" 2>/dev/null || true
    
    log "INFO" "Old backups cleanup completed"
}

# Run main function
main "$@"