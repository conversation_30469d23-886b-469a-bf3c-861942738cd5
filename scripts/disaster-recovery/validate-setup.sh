#!/bin/bash

# Episteme Disaster Recovery Validation Test
# Quick validation that DR infrastructure is properly configured

set -euo pipefail

PROJECT_ID="${PROJECT_ID:-episteme-prod}"
FAILED_CHECKS=0

echo "🔧 Episteme Disaster Recovery Configuration Validation"
echo "=================================================="
echo

# Function to check and report
check() {
    local description="$1"
    local command="$2"
    
    printf "%-50s " "$description..."
    
    if eval "$command" >/dev/null 2>&1; then
        echo "✅ PASS"
    else
        echo "❌ FAIL"
        ((FAILED_CHECKS++))
    fi
}

echo "📋 Checking Infrastructure Components:"
echo "-----------------------------------"

# Check GCP project access
check "GCP project access" "gcloud projects describe $PROJECT_ID"

# Check Spanner instance
check "Spanner instance exists" "gcloud spanner instances describe episteme-prod-instance --project=$PROJECT_ID"

# Check BigQuery datasets
check "BigQuery analytics dataset" "bq ls $PROJECT_ID:analytics"

# Check Redis instance
check "Redis cache instance" "gcloud redis instances describe episteme-cache --region=us-central1 --project=$PROJECT_ID"

# Check Cloud Run services
check "Analysis Engine service" "gcloud run services describe analysis-engine --region=us-central1 --project=$PROJECT_ID"
check "Query Intelligence service" "gcloud run services describe query-intelligence --region=us-central1 --project=$PROJECT_ID"

# Check backup storage
check "Backup storage bucket" "gsutil ls gs://episteme-backups/"

echo
echo "🔑 Checking Permissions and Access:"
echo "--------------------------------"

# Check service account
check "Backup service account" "gcloud iam service-accounts describe backup-service-account@$PROJECT_ID.iam.gserviceaccount.com --project=$PROJECT_ID"

# Check secret access
check "Secret Manager access" "gcloud secrets list --project=$PROJECT_ID"

# Check monitoring
check "Cloud Logging access" "gcloud logging read 'resource.type=\"cloud_run_revision\"' --limit=1 --project=$PROJECT_ID"

echo
echo "📊 Checking DR Configuration:"
echo "----------------------------"

# Check DR region setup
check "DR project exists" "gcloud projects describe episteme-dr"

# Check cross-region backups
check "Cross-region backup capability" "gcloud spanner instances list --filter='name:dr' --project=$PROJECT_ID"

# Check monitoring metrics
check "Backup monitoring metrics" "gcloud logging metrics describe backup_success_count --project=$PROJECT_ID"

echo
echo "📁 Checking Script Configuration:"
echo "-------------------------------"

# Check script permissions
check "Backup script executable" "test -x ./scripts/disaster-recovery/backup-platform.sh"
check "Failover script executable" "test -x ./scripts/disaster-recovery/regional-failover.sh"
check "Test script executable" "test -x ./scripts/disaster-recovery/test-dr-procedures.sh"

# Check documentation
check "DR plan documentation" "test -f ./docs/disaster-recovery-plan.md"
check "Backup strategies research" "test -f ./research/google-cloud/backup-strategies.md"

echo
echo "📝 Validation Summary:"
echo "======================"

if [ $FAILED_CHECKS -eq 0 ]; then
    echo "✅ All checks passed! Disaster recovery infrastructure is properly configured."
    echo
    echo "🚀 Next Steps:"
    echo "1. Schedule automated backups: crontab -e"
    echo "2. Configure monitoring alerts: gcloud alpha monitoring policies create"
    echo "3. Run first DR test: ./scripts/disaster-recovery/test-dr-procedures.sh"
    exit 0
else
    echo "❌ $FAILED_CHECKS check(s) failed. Please review and fix the issues above."
    echo
    echo "🔧 Common fixes:"
    echo "1. Ensure GCP authentication: gcloud auth login"
    echo "2. Set correct project: gcloud config set project $PROJECT_ID"
    echo "3. Enable required APIs: gcloud services enable spanner.googleapis.com"
    echo "4. Create missing resources according to docs/disaster-recovery-plan.md"
    exit 1
fi