#!/bin/bash

# Episteme Platform Regional Failover Script
# Automated disaster recovery failover to secondary region

set -euo pipefail

# Configuration
PROJECT_ID="${PROJECT_ID:-episteme-prod}"
DR_PROJECT_ID="${DR_PROJECT_ID:-episteme-dr}"
PRIMARY_REGION="us-central1"
DR_REGION="us-east1"
DATE_SUFFIX=$(date +%Y%m%d_%H%M%S)
LOG_FILE="/var/log/episteme-failover-${DATE_SUFFIX}.log"

# Services configuration
SERVICES=("analysis-engine" "query-intelligence" "pattern-mining" "marketplace")
SPANNER_INSTANCE="episteme-prod-instance"
DR_SPANNER_INSTANCE="episteme-dr-instance"

# Notification configuration
SLACK_WEBHOOK_URL="${SLACK_WEBHOOK_URL:-}"
PAGERDUTY_INTEGRATION_KEY="${PAGERDUTY_INTEGRATION_KEY:-}"

# Logging function
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') [$1] $2" | tee -a "$LOG_FILE"
}

# Error handling
error_exit() {
    log "ERROR" "$1"
    notify_stakeholders "FAILOVER_FAILED" "$1"
    exit 1
}

# Notification function
notify_stakeholders() {
    local event_type="$1"
    local message="$2"
    
    # Slack notification
    if [ -n "$SLACK_WEBHOOK_URL" ]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{
                \"text\":\"🚨 Episteme DR Event: $event_type\",
                \"attachments\":[{
                    \"color\":\"danger\",
                    \"fields\":[{
                        \"title\":\"Details\",
                        \"value\":\"$message\",
                        \"short\":false
                    },{
                        \"title\":\"Timestamp\",
                        \"value\":\"$(date)\",
                        \"short\":true
                    }]
                }]
            }" \
            "$SLACK_WEBHOOK_URL" 2>/dev/null || log "WARN" "Failed to send Slack notification"
    fi
    
    # PagerDuty notification
    if [ -n "$PAGERDUTY_INTEGRATION_KEY" ]; then
        curl -X POST "https://events.pagerduty.com/v2/enqueue" \
            -H "Content-Type: application/json" \
            -d "{
                \"routing_key\": \"$PAGERDUTY_INTEGRATION_KEY\",
                \"event_action\": \"trigger\",
                \"payload\": {
                    \"summary\": \"Episteme DR Event: $event_type\",
                    \"source\": \"disaster-recovery-system\",
                    \"severity\": \"critical\",
                    \"custom_details\": {
                        \"message\": \"$message\",
                        \"timestamp\": \"$(date)\"
                    }
                }
            }" 2>/dev/null || log "WARN" "Failed to send PagerDuty notification"
    fi
}

# Health check function
check_region_health() {
    local region="$1"
    local endpoint=""
    
    if [ "$region" = "$PRIMARY_REGION" ]; then
        endpoint="https://api.episteme.dev/health"
    else
        endpoint="https://api-dr.episteme.dev/health"
    fi
    
    local http_code
    http_code=$(curl -s -o /dev/null -w "%{http_code}" "$endpoint" --max-time 10 || echo "000")
    
    if [ "$http_code" = "200" ]; then
        return 0
    else
        return 1
    fi
}

# Verify primary region failure
verify_primary_failure() {
    log "INFO" "Verifying primary region failure"
    
    local failed_checks=0
    local total_checks=3
    
    for i in $(seq 1 $total_checks); do
        if ! check_region_health "$PRIMARY_REGION"; then
            ((failed_checks++))
            log "WARN" "Primary region health check failed ($i/$total_checks)"
        else
            log "INFO" "Primary region health check passed ($i/$total_checks)"
        fi
        
        if [ $i -lt $total_checks ]; then
            sleep 30
        fi
    done
    
    if [ $failed_checks -eq $total_checks ]; then
        log "ERROR" "Primary region confirmed unhealthy after $total_checks checks"
        return 0
    else
        log "INFO" "Primary region appears healthy ($((total_checks - failed_checks))/$total_checks checks passed)"
        return 1
    fi
}

# Scale up DR region services
scale_up_dr_services() {
    log "INFO" "Scaling up services in DR region"
    
    for service in "${SERVICES[@]}"; do
        log "INFO" "Scaling up $service in DR region"
        
        # Check if service exists in DR region
        if gcloud run services describe "$service" \
            --region="$DR_REGION" \
            --project="$DR_PROJECT_ID" \
            --quiet >/dev/null 2>&1; then
            
            # Scale up the service
            gcloud run services update "$service" \
                --region="$DR_REGION" \
                --project="$DR_PROJECT_ID" \
                --min-instances=2 \
                --max-instances=20 \
                --concurrency=100 \
                --cpu=2 \
                --memory=4Gi \
                --timeout=300 || log "ERROR" "Failed to scale up $service"
            
            log "INFO" "$service scaled up successfully"
        else
            log "ERROR" "Service $service not found in DR region"
        fi
    done
    
    log "INFO" "DR services scaling completed"
}

# Update DNS to point to DR region
update_dns_failover() {
    log "INFO" "Updating DNS to point to DR region"
    
    # Update main API endpoint
    gcloud dns record-sets update api.episteme.dev \
        --type=A \
        --zone=episteme-zone \
        --project="$PROJECT_ID" \
        --rrdatas=35.186.100.100 || log "ERROR" "Failed to update DNS for main API"
    
    # Update service-specific endpoints
    for service in "${SERVICES[@]}"; do
        gcloud dns record-sets update "${service}.episteme.dev" \
            --type=A \
            --zone=episteme-zone \
            --project="$PROJECT_ID" \
            --rrdatas=35.186.100.101 || log "WARN" "Failed to update DNS for $service"
    done
    
    log "INFO" "DNS failover completed"
}

# Verify DR region services
verify_dr_services() {
    log "INFO" "Verifying DR region services"
    
    # Wait for DNS propagation
    sleep 120
    
    local healthy_services=0
    local total_services=${#SERVICES[@]}
    
    for service in "${SERVICES[@]}"; do
        log "INFO" "Checking health of $service in DR region"
        
        if check_region_health "$DR_REGION"; then
            log "INFO" "$service is healthy in DR region"
            ((healthy_services++))
        else
            log "ERROR" "$service is unhealthy in DR region"
        fi
    done
    
    if [ $healthy_services -eq $total_services ]; then
        log "INFO" "All services are healthy in DR region"
        return 0
    else
        log "ERROR" "Only $healthy_services/$total_services services are healthy in DR region"
        return 1
    fi
}

# Spanner failover (automatic with multi-region setup)
spanner_failover() {
    log "INFO" "Verifying Spanner database availability"
    
    # Spanner multi-region automatically handles failover
    # We just need to verify connectivity
    local query_result
    query_result=$(gcloud spanner databases execute-sql episteme-prod \
        --instance="$SPANNER_INSTANCE" \
        --project="$PROJECT_ID" \
        --sql="SELECT 1 as test" \
        --format="value(rows[0][0])" 2>/dev/null || echo "failed")
    
    if [ "$query_result" = "1" ]; then
        log "INFO" "Spanner database is accessible"
    else
        log "ERROR" "Spanner database is not accessible"
        return 1
    fi
}

# Update load balancer configuration
update_load_balancer() {
    log "INFO" "Updating load balancer configuration"
    
    # Update backend service to point to DR region
    gcloud compute backend-services update episteme-backend \
        --global \
        --project="$PROJECT_ID" \
        --health-checks=episteme-health-check-dr || log "ERROR" "Failed to update backend service"
    
    # Update URL map if needed
    gcloud compute url-maps update episteme-lb \
        --project="$PROJECT_ID" \
        --default-service=episteme-backend-dr || log "ERROR" "Failed to update URL map"
    
    log "INFO" "Load balancer configuration updated"
}

# Create incident ticket
create_incident_ticket() {
    log "INFO" "Creating incident ticket"
    
    # This would integrate with your ticketing system
    # Example using a REST API
    local incident_data="{
        \"title\": \"Episteme Platform Regional Failover\",
        \"description\": \"Automatic failover to DR region due to primary region failure\",
        \"severity\": \"critical\",
        \"status\": \"investigating\",
        \"created_at\": \"$(date -u +%Y-%m-%dT%H:%M:%SZ)\",
        \"tags\": [\"disaster-recovery\", \"failover\", \"production\"]
    }"
    
    # Replace with your actual incident management API
    # curl -X POST "https://your-incident-api.com/incidents" \
    #     -H "Content-Type: application/json" \
    #     -H "Authorization: Bearer $INCIDENT_API_TOKEN" \
    #     -d "$incident_data"
    
    log "INFO" "Incident ticket creation completed"
}

# Main failover function
main() {
    log "INFO" "Starting Episteme platform regional failover"
    
    # Step 1: Verify primary region failure
    if ! verify_primary_failure; then
        log "INFO" "Primary region appears healthy. Manual verification required."
        echo "Primary region health checks passed. Failover not initiated."
        echo "Use --force flag to bypass health checks if needed."
        exit 0
    fi
    
    # Notify stakeholders about failover start
    notify_stakeholders "FAILOVER_STARTED" "Primary region failure detected. Initiating failover to DR region."
    
    # Step 2: Create incident ticket
    create_incident_ticket
    
    # Step 3: Scale up DR region services
    scale_up_dr_services
    
    # Step 4: Verify Spanner database availability
    spanner_failover
    
    # Step 5: Update load balancer configuration
    update_load_balancer
    
    # Step 6: Update DNS failover
    update_dns_failover
    
    # Step 7: Verify DR region services
    if verify_dr_services; then
        log "INFO" "Regional failover completed successfully"
        notify_stakeholders "FAILOVER_COMPLETED" "Services are now running in DR region (${DR_REGION}). All health checks passed."
    else
        error_exit "DR region services verification failed"
    fi
    
    # Step 8: Final status update
    log "INFO" "Failover process completed. Monitor services and prepare for primary region recovery."
    
    # Provide next steps
    cat << EOF

Failover Completed Successfully
==============================

Services are now running in DR region: $DR_REGION

Next Steps:
1. Monitor service health and performance
2. Investigate primary region failure
3. Prepare for primary region recovery when ready
4. Run: ./recovery-primary-region.sh when primary is restored

Current Status:
- All services scaled up in DR region
- DNS updated to point to DR endpoints
- Load balancer configured for DR region
- Incident ticket created and stakeholders notified

Log file: $LOG_FILE
EOF
}

# Handle command line arguments
case "${1:-}" in
    --force)
        log "WARN" "Forcing failover without health checks"
        # Skip health checks and proceed with failover
        log "INFO" "Starting forced failover"
        notify_stakeholders "FORCED_FAILOVER_STARTED" "Manual failover initiated with --force flag"
        scale_up_dr_services
        spanner_failover
        update_load_balancer
        update_dns_failover
        verify_dr_services
        notify_stakeholders "FORCED_FAILOVER_COMPLETED" "Forced failover completed"
        ;;
    --help|-h)
        cat << EOF
Episteme Platform Regional Failover Script

Usage: $0 [OPTIONS]

Options:
    --force     Force failover without health checks
    --help      Show this help message

Environment Variables:
    PROJECT_ID                   Primary project ID (default: episteme-prod)
    DR_PROJECT_ID               DR project ID (default: episteme-dr)
    SLACK_WEBHOOK_URL           Slack webhook for notifications
    PAGERDUTY_INTEGRATION_KEY   PagerDuty integration key

The script will:
1. Verify primary region failure (unless --force is used)
2. Scale up services in DR region
3. Update load balancer and DNS
4. Verify DR region functionality
5. Notify stakeholders

EOF
        exit 0
        ;;
    "")
        main
        ;;
    *)
        echo "Unknown option: $1"
        echo "Use --help for usage information"
        exit 1
        ;;
esac