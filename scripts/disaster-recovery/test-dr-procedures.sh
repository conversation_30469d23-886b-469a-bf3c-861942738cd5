#!/bin/bash

# Episteme Platform Disaster Recovery Testing Script
# Automated testing of disaster recovery procedures

set -euo pipefail

# Configuration
PROJECT_ID="${PROJECT_ID:-episteme-prod}"
DR_PROJECT_ID="${DR_PROJECT_ID:-episteme-dr}"
TEST_DATE=$(date +%Y%m%d_%H%M%S)
LOG_FILE="/var/log/episteme-dr-test-${TEST_DATE}.log"
RESULTS_FILE="/tmp/dr-test-results-${TEST_DATE}.json"

# Test configuration
SERVICES=("analysis-engine" "query-intelligence" "pattern-mining" "marketplace")
TEST_TIMEOUT=300  # 5 minutes

# Test results tracking
declare -A test_results

# Logging function
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') [$1] $2" | tee -a "$LOG_FILE"
}

# Record test result
record_result() {
    local test_name="$1"
    local result="$2"
    local details="$3"
    
    test_results["$test_name"]="$result"
    
    cat >> "$RESULTS_FILE" << EOF
{
  "test": "$test_name",
  "result": "$result",
  "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "details": "$details"
},
EOF
    
    if [ "$result" = "PASS" ]; then
        log "INFO" "✅ $test_name: PASSED - $details"
    else
        log "ERROR" "❌ $test_name: FAILED - $details"
    fi
}

# Test backup creation
test_backup_creation() {
    log "INFO" "Testing backup creation procedures"
    
    # Test Spanner backup
    local backup_id="test-backup-${TEST_DATE}"
    local expiration_date=$(date -d "+1 day" '+%Y-%m-%dT%H:%M:%SZ')
    
    if gcloud spanner backups create "$backup_id" \
        --database=episteme-prod \
        --instance=episteme-prod-instance \
        --expiration-date="$expiration_date" \
        --project="$PROJECT_ID" >/dev/null 2>&1; then
        
        # Wait for backup completion
        local backup_state=""
        local attempts=0
        while [ "$backup_state" != "READY" ] && [ $attempts -lt 20 ]; do
            backup_state=$(gcloud spanner backups describe "$backup_id" \
                --instance=episteme-prod-instance \
                --project="$PROJECT_ID" \
                --format="value(state)" 2>/dev/null || echo "FAILED")
            
            if [ "$backup_state" = "CREATING" ]; then
                sleep 15
                ((attempts++))
            elif [ "$backup_state" = "READY" ]; then
                break
            else
                break
            fi
        done
        
        if [ "$backup_state" = "READY" ]; then
            record_result "spanner_backup_creation" "PASS" "Backup created and ready in $((attempts * 15)) seconds"
            
            # Cleanup test backup
            gcloud spanner backups delete "$backup_id" \
                --instance=episteme-prod-instance \
                --project="$PROJECT_ID" \
                --quiet >/dev/null 2>&1 || true
        else
            record_result "spanner_backup_creation" "FAIL" "Backup did not complete within timeout"
        fi
    else
        record_result "spanner_backup_creation" "FAIL" "Failed to initiate backup creation"
    fi
    
    # Test BigQuery export
    if bq extract \
        --destination_format=AVRO \
        --compression=SNAPPY \
        'episteme-prod:analytics.repositories' \
        "gs://episteme-test-backups/dr-test-${TEST_DATE}/repositories.avro" >/dev/null 2>&1; then
        record_result "bigquery_export" "PASS" "BigQuery table exported successfully"
        
        # Cleanup test export
        gsutil rm "gs://episteme-test-backups/dr-test-${TEST_DATE}/repositories.avro" 2>/dev/null || true
    else
        record_result "bigquery_export" "FAIL" "BigQuery export failed"
    fi
    
    # Test Redis export
    if gcloud redis instances export episteme-cache \
        --destination="gs://episteme-test-backups/redis-test-${TEST_DATE}.rdb" \
        --region=us-central1 \
        --project="$PROJECT_ID" >/dev/null 2>&1; then
        record_result "redis_export" "PASS" "Redis export initiated successfully"
        
        # Cleanup test export (after a delay)
        sleep 30
        gsutil rm "gs://episteme-test-backups/redis-test-${TEST_DATE}.rdb" 2>/dev/null || true
    else
        record_result "redis_export" "FAIL" "Redis export failed"
    fi
}

# Test backup restoration
test_backup_restoration() {
    log "INFO" "Testing backup restoration procedures"
    
    # Test Spanner restore from latest backup
    local latest_backup
    latest_backup=$(gcloud spanner backups list \
        --instance=episteme-prod-instance \
        --project="$PROJECT_ID" \
        --format="value(name)" \
        --sort-by="~createTime" \
        --limit=1)
    
    if [ -n "$latest_backup" ]; then
        local test_db="test-restore-${TEST_DATE}"
        
        if gcloud spanner databases restore "$test_db" \
            --source-backup="$latest_backup" \
            --instance=episteme-prod-instance \
            --project="$PROJECT_ID" >/dev/null 2>&1; then
            
            # Wait for restore completion
            local restore_state=""
            local attempts=0
            while [ "$restore_state" != "READY" ] && [ $attempts -lt 20 ]; do
                restore_state=$(gcloud spanner databases describe "$test_db" \
                    --instance=episteme-prod-instance \
                    --project="$PROJECT_ID" \
                    --format="value(state)" 2>/dev/null || echo "FAILED")
                
                if [ "$restore_state" = "CREATING" ]; then
                    sleep 15
                    ((attempts++))
                elif [ "$restore_state" = "READY" ]; then
                    break
                else
                    break
                fi
            done
            
            if [ "$restore_state" = "READY" ]; then
                # Validate restored data
                local repo_count
                repo_count=$(gcloud spanner databases execute-sql "$test_db" \
                    --instance=episteme-prod-instance \
                    --project="$PROJECT_ID" \
                    --sql="SELECT COUNT(*) as count FROM repositories" \
                    --format="value(rows[0][0])" 2>/dev/null || echo "0")
                
                if [ "$repo_count" -gt 0 ]; then
                    record_result "spanner_restore" "PASS" "Database restored with $repo_count repositories"
                else
                    record_result "spanner_restore" "FAIL" "Restored database contains no data"
                fi
                
                # Cleanup test database
                gcloud spanner databases delete "$test_db" \
                    --instance=episteme-prod-instance \
                    --project="$PROJECT_ID" \
                    --quiet >/dev/null 2>&1 || true
            else
                record_result "spanner_restore" "FAIL" "Database restore did not complete within timeout"
            fi
        else
            record_result "spanner_restore" "FAIL" "Failed to initiate database restore"
        fi
    else
        record_result "spanner_restore" "FAIL" "No backup available for restore test"
    fi
}

# Test service failover
test_service_failover() {
    log "INFO" "Testing service failover procedures"
    
    for service in "${SERVICES[@]}"; do
        log "INFO" "Testing failover for service: $service"
        
        # Get current revision
        local current_revision
        current_revision=$(gcloud run services describe "$service" \
            --region=us-central1 \
            --project="$PROJECT_ID" \
            --format="value(status.latestReadyRevisionName)" 2>/dev/null || echo "")
        
        if [ -z "$current_revision" ]; then
            record_result "${service}_failover" "FAIL" "Could not determine current revision"
            continue
        fi
        
        # Get previous revision
        local previous_revision
        previous_revision=$(gcloud run revisions list \
            --service="$service" \
            --region=us-central1 \
            --project="$PROJECT_ID" \
            --format="value(metadata.name)" \
            --limit=2 | tail -1)
        
        if [ -z "$previous_revision" ] || [ "$previous_revision" = "$current_revision" ]; then
            record_result "${service}_failover" "SKIP" "No previous revision available for failover test"
            continue
        fi
        
        # Test health before failover
        local health_before
        health_before=$(curl -s -o /dev/null -w "%{http_code}" \
            "https://${service}-url/health" --max-time 10 || echo "000")
        
        if [ "$health_before" != "200" ]; then
            record_result "${service}_failover" "FAIL" "Service unhealthy before failover test"
            continue
        fi
        
        # Perform failover to previous revision
        if gcloud run services update-traffic "$service" \
            --to-revisions="$previous_revision=100" \
            --region=us-central1 \
            --project="$PROJECT_ID" >/dev/null 2>&1; then
            
            # Wait for traffic to switch
            sleep 30
            
            # Test health after failover
            local health_after
            health_after=$(curl -s -o /dev/null -w "%{http_code}" \
                "https://${service}-url/health" --max-time 10 || echo "000")
            
            if [ "$health_after" = "200" ]; then
                # Rollback to current revision
                gcloud run services update-traffic "$service" \
                    --to-revisions="$current_revision=100" \
                    --region=us-central1 \
                    --project="$PROJECT_ID" >/dev/null 2>&1
                
                record_result "${service}_failover" "PASS" "Failover and rollback successful"
            else
                # Rollback anyway
                gcloud run services update-traffic "$service" \
                    --to-revisions="$current_revision=100" \
                    --region=us-central1 \
                    --project="$PROJECT_ID" >/dev/null 2>&1
                
                record_result "${service}_failover" "FAIL" "Service unhealthy after failover"
            fi
        else
            record_result "${service}_failover" "FAIL" "Failed to initiate failover"
        fi
    done
}

# Test monitoring and alerting
test_monitoring_alerting() {
    log "INFO" "Testing monitoring and alerting systems"
    
    # Test Cloud Monitoring API
    if gcloud logging read \
        'resource.type="cloud_run_revision" AND severity>=ERROR' \
        --limit=1 \
        --project="$PROJECT_ID" >/dev/null 2>&1; then
        record_result "cloud_logging" "PASS" "Cloud Logging API accessible"
    else
        record_result "cloud_logging" "FAIL" "Cloud Logging API not accessible"
    fi
    
    # Test metric creation
    if gcloud logging metrics describe backup_success_count \
        --project="$PROJECT_ID" >/dev/null 2>&1; then
        record_result "backup_metrics" "PASS" "Backup monitoring metrics exist"
    else
        record_result "backup_metrics" "FAIL" "Backup monitoring metrics missing"
    fi
    
    # Test alert policies
    local alert_count
    alert_count=$(gcloud alpha monitoring policies list \
        --project="$PROJECT_ID" \
        --format="value(name)" | wc -l)
    
    if [ "$alert_count" -gt 0 ]; then
        record_result "alert_policies" "PASS" "$alert_count alert policies configured"
    else
        record_result "alert_policies" "FAIL" "No alert policies found"
    fi
}

# Test cross-region connectivity
test_cross_region_connectivity() {
    log "INFO" "Testing cross-region connectivity"
    
    # Test DR region services
    for service in "${SERVICES[@]}"; do
        if gcloud run services describe "$service" \
            --region=us-east1 \
            --project="$DR_PROJECT_ID" >/dev/null 2>&1; then
            record_result "${service}_dr_exists" "PASS" "DR service exists in secondary region"
        else
            record_result "${service}_dr_exists" "FAIL" "DR service missing in secondary region"
        fi
    done
    
    # Test Spanner multi-region access
    if gcloud spanner databases execute-sql episteme-prod \
        --instance=episteme-prod-instance \
        --project="$PROJECT_ID" \
        --sql="SELECT 1" >/dev/null 2>&1; then
        record_result "spanner_connectivity" "PASS" "Spanner database accessible"
    else
        record_result "spanner_connectivity" "FAIL" "Spanner database not accessible"
    fi
    
    # Test cross-region storage access
    if gsutil ls gs://episteme-backups/ >/dev/null 2>&1; then
        record_result "storage_connectivity" "PASS" "Backup storage accessible"
    else
        record_result "storage_connectivity" "FAIL" "Backup storage not accessible"
    fi
}

# Test security and access controls
test_security_access() {
    log "INFO" "Testing security and access controls"
    
    # Test service account permissions
    if gcloud iam service-accounts describe \
        backup-service-account@"$PROJECT_ID".iam.gserviceaccount.com \
        --project="$PROJECT_ID" >/dev/null 2>&1; then
        record_result "backup_service_account" "PASS" "Backup service account exists"
    else
        record_result "backup_service_account" "FAIL" "Backup service account missing"
    fi
    
    # Test secret access
    if gcloud secrets versions access latest \
        --secret=database-url \
        --project="$PROJECT_ID" >/dev/null 2>&1; then
        record_result "secret_access" "PASS" "Secrets accessible"
    else
        record_result "secret_access" "FAIL" "Secrets not accessible"
    fi
    
    # Test IAM policy
    local iam_policy
    iam_policy=$(gcloud projects get-iam-policy "$PROJECT_ID" \
        --format="value(bindings[].members)" 2>/dev/null | wc -l)
    
    if [ "$iam_policy" -gt 0 ]; then
        record_result "iam_policy" "PASS" "IAM policy configured"
    else
        record_result "iam_policy" "FAIL" "IAM policy missing or inaccessible"
    fi
}

# Generate test report
generate_report() {
    log "INFO" "Generating test report"
    
    # Count results
    local total_tests=0
    local passed_tests=0
    local failed_tests=0
    local skipped_tests=0
    
    for test in "${!test_results[@]}"; do
        ((total_tests++))
        case "${test_results[$test]}" in
            "PASS") ((passed_tests++)) ;;
            "FAIL") ((failed_tests++)) ;;
            "SKIP") ((skipped_tests++)) ;;
        esac
    done
    
    # Create summary report
    cat > "/tmp/dr-test-summary-${TEST_DATE}.md" << EOF
# Disaster Recovery Test Report

**Test Date**: $(date)  
**Test Duration**: $((SECONDS / 60)) minutes  
**Log File**: $LOG_FILE

## Summary

- **Total Tests**: $total_tests
- **Passed**: $passed_tests ($(( passed_tests * 100 / total_tests ))%)
- **Failed**: $failed_tests ($(( failed_tests * 100 / total_tests ))%)
- **Skipped**: $skipped_tests ($(( skipped_tests * 100 / total_tests ))%)

## Test Results

| Test | Result | Details |
|------|--------|---------|
EOF
    
    # Add individual test results
    for test in "${!test_results[@]}"; do
        local result="${test_results[$test]}"
        local icon="❓"
        case "$result" in
            "PASS") icon="✅" ;;
            "FAIL") icon="❌" ;;
            "SKIP") icon="⏭️" ;;
        esac
        
        echo "| $test | $icon $result | See log for details |" >> "/tmp/dr-test-summary-${TEST_DATE}.md"
    done
    
    cat >> "/tmp/dr-test-summary-${TEST_DATE}.md" << EOF

## Recommendations

EOF
    
    if [ $failed_tests -gt 0 ]; then
        cat >> "/tmp/dr-test-summary-${TEST_DATE}.md" << EOF
⚠️ **Action Required**: $failed_tests test(s) failed. Review the log file and address the issues before the next scheduled DR test.

EOF
    fi
    
    if [ $passed_tests -eq $total_tests ]; then
        cat >> "/tmp/dr-test-summary-${TEST_DATE}.md" << EOF
✅ **All tests passed**: The disaster recovery system is functioning correctly.

EOF
    fi
    
    cat >> "/tmp/dr-test-summary-${TEST_DATE}.md" << EOF
## Next Steps

1. Review failed tests and implement fixes
2. Update DR procedures based on test results
3. Schedule next DR test
4. Train team on any procedure changes

## Files Generated

- Test Summary: /tmp/dr-test-summary-${TEST_DATE}.md
- Detailed Results: $RESULTS_FILE
- Full Log: $LOG_FILE
EOF
    
    log "INFO" "Test report generated: /tmp/dr-test-summary-${TEST_DATE}.md"
    echo "Test report: /tmp/dr-test-summary-${TEST_DATE}.md"
}

# Main test function
main() {
    log "INFO" "Starting disaster recovery testing suite"
    
    # Initialize results file
    echo "{" > "$RESULTS_FILE"
    echo "  \"test_run\": {" >> "$RESULTS_FILE"
    echo "    \"timestamp\": \"$(date -u +%Y-%m-%dT%H:%M:%SZ)\"," >> "$RESULTS_FILE"
    echo "    \"test_id\": \"dr-test-$TEST_DATE\"" >> "$RESULTS_FILE"
    echo "  }," >> "$RESULTS_FILE"
    echo "  \"results\": [" >> "$RESULTS_FILE"
    
    # Run test suites
    test_backup_creation
    test_backup_restoration
    test_service_failover
    test_monitoring_alerting
    test_cross_region_connectivity
    test_security_access
    
    # Close results file
    echo "  ]" >> "$RESULTS_FILE"
    echo "}" >> "$RESULTS_FILE"
    
    # Generate report
    generate_report
    
    log "INFO" "Disaster recovery testing completed"
    
    # Exit with error code if any tests failed
    if [ $failed_tests -gt 0 ]; then
        exit 1
    fi
}

# Handle command line arguments
case "${1:-}" in
    --help|-h)
        cat << EOF
Episteme Platform Disaster Recovery Testing Script

Usage: $0 [OPTIONS]

Options:
    --help      Show this help message

Environment Variables:
    PROJECT_ID       Primary project ID (default: episteme-prod)
    DR_PROJECT_ID    DR project ID (default: episteme-dr)

The script will test:
1. Backup creation procedures
2. Backup restoration procedures
3. Service failover capabilities
4. Monitoring and alerting systems
5. Cross-region connectivity
6. Security and access controls

Results are saved to:
- Test summary: /tmp/dr-test-summary-[timestamp].md
- Detailed results: /tmp/dr-test-results-[timestamp].json
- Full log: /var/log/episteme-dr-test-[timestamp].log

EOF
        exit 0
        ;;
    "")
        main
        ;;
    *)
        echo "Unknown option: $1"
        echo "Use --help for usage information"
        exit 1
        ;;
esac