# Episteme Disaster Recovery Scripts

This directory contains automated scripts for disaster recovery procedures for the Episteme platform.

## Overview

The disaster recovery automation consists of three main scripts that handle backup, failover, and testing procedures:

1. **`backup-platform.sh`** - Comprehensive backup automation
2. **`regional-failover.sh`** - Automated regional failover 
3. **`test-dr-procedures.sh`** - DR testing and validation

## Quick Start

### Daily Backup (Automated)
```bash
# Run daily backup (typically scheduled via cron)
./backup-platform.sh
```

### Emergency Regional Failover
```bash
# Check primary region and failover if needed
./regional-failover.sh

# Force failover without health checks (emergency)
./regional-failover.sh --force
```

### Monthly DR Testing
```bash
# Run comprehensive DR test suite
./test-dr-procedures.sh
```

## Script Details

### backup-platform.sh

Performs comprehensive backup of all platform components:

- **Spanner Database**: Creates backups with cross-region replication
- **BigQuery Datasets**: Exports analytics and monitoring data
- **Redis Cache**: Creates RDB exports
- **Configurations**: Backs up Cloud Run service configs and secrets metadata
- **Container Images**: Tags and stores current production images

**Features:**
- Automated backup verification
- Cleanup of old backups
- Comprehensive logging
- Error handling and notifications

**Schedule**: Daily at 2 AM UTC (recommended)

### regional-failover.sh

Automates failover to disaster recovery region:

- **Health Verification**: Checks primary region health before failover
- **Service Scaling**: Scales up DR region services
- **DNS Failover**: Updates DNS to point to DR region
- **Load Balancer**: Reconfigures traffic routing
- **Notifications**: Alerts stakeholders via Slack/PagerDuty

**Features:**
- Automatic health checks with manual override
- Stakeholder notifications
- Comprehensive logging
- Rollback capabilities

**Usage**: On-demand during incidents

### test-dr-procedures.sh

Validates disaster recovery capabilities:

- **Backup Testing**: Tests backup creation and restoration
- **Service Failover**: Tests service revision rollbacks
- **Monitoring**: Validates alerting and metrics
- **Cross-Region**: Tests DR region connectivity
- **Security**: Validates access controls

**Features:**
- Comprehensive test coverage
- Automated report generation
- Non-destructive testing
- Pass/fail tracking

**Schedule**: Monthly on first Sunday

## Configuration

### Environment Variables

```bash
# Primary configuration
export PROJECT_ID="episteme-prod"
export DR_PROJECT_ID="episteme-dr"
export BACKUP_BUCKET="episteme-backups"

# Notification configuration
export SLACK_WEBHOOK_URL="https://hooks.slack.com/..."
export PAGERDUTY_INTEGRATION_KEY="your-pagerduty-key"

# Service configuration
export SERVICES=("analysis-engine" "query-intelligence" "pattern-mining" "marketplace")
export SPANNER_INSTANCE="episteme-prod-instance"
export REDIS_INSTANCE="episteme-cache"
```

### Required Permissions

The scripts require a service account with the following IAM roles:

```bash
# Backup permissions
roles/spanner.backupAdmin
roles/bigquery.dataEditor
roles/storage.admin
roles/redis.admin

# Failover permissions
roles/run.admin
roles/compute.networkAdmin
roles/dns.admin

# Monitoring permissions
roles/monitoring.viewer
roles/logging.viewer
```

## Scheduling

### Cron Configuration

```bash
# Add to crontab
# Daily backup at 2 AM UTC
0 2 * * * /path/to/scripts/disaster-recovery/backup-platform.sh

# Monthly DR test on first Sunday at 3 AM UTC
0 3 1-7 * 0 /path/to/scripts/disaster-recovery/test-dr-procedures.sh

# Weekly backup verification on Mondays at 4 AM UTC
0 4 * * 1 /path/to/scripts/disaster-recovery/backup-platform.sh --verify-only
```

### Cloud Scheduler Configuration

```bash
# Create backup job
gcloud scheduler jobs create http backup-daily \
    --schedule="0 2 * * *" \
    --uri="https://cloud-functions-endpoint/backup-platform" \
    --http-method=POST \
    --time-zone="UTC"

# Create DR test job
gcloud scheduler jobs create http dr-test-monthly \
    --schedule="0 3 1 * *" \
    --uri="https://cloud-functions-endpoint/test-dr" \
    --http-method=POST \
    --time-zone="UTC"
```

## Monitoring and Alerting

### Key Metrics

- **Backup Success Rate**: Should be 100%
- **Backup Duration**: Should complete within 2 hours
- **Cross-Region Latency**: Should be < 100ms
- **DR Test Pass Rate**: Should be > 95%

### Alert Thresholds

```yaml
# Cloud Monitoring alert policies
backup_failure:
  condition: backup_failure_count > 0
  notification: PagerDuty Critical

backup_duration:
  condition: backup_duration > 7200  # 2 hours
  notification: Slack Warning

dr_test_failure:
  condition: dr_test_pass_rate < 0.95
  notification: Email + Slack
```

## Logging

All scripts generate comprehensive logs:

- **Location**: `/var/log/episteme-*-[timestamp].log`
- **Format**: Structured logging with timestamps and severity levels
- **Retention**: 90 days
- **Aggregation**: Sent to Cloud Logging for analysis

### Log Analysis

```bash
# View recent backup logs
gcloud logging read 'jsonPayload.component="backup-platform"' --limit=50

# Check for backup failures
gcloud logging read 'severity>=ERROR AND jsonPayload.component="backup-platform"' --limit=20

# Monitor DR test results
gcloud logging read 'jsonPayload.component="dr-testing"' --limit=30
```

## Troubleshooting

### Common Issues

#### Backup Failures

```bash
# Check Spanner backup status
gcloud spanner backups list --instance=episteme-prod-instance

# Verify BigQuery export permissions
bq ls episteme-prod:analytics

# Check Redis export status
gcloud redis operations list --filter="target:episteme-cache"
```

#### Failover Issues

```bash
# Check service health in DR region
curl -f https://api-dr.episteme.dev/health

# Verify DNS propagation
nslookup api.episteme.dev

# Check load balancer configuration
gcloud compute url-maps describe episteme-lb
```

#### Test Failures

```bash
# Review detailed test results
cat /tmp/dr-test-results-[timestamp].json

# Check service permissions
gcloud iam service-accounts get-iam-policy <EMAIL>

# Validate cross-region connectivity
curl -f https://service-dr.episteme.dev/health
```

### Recovery Procedures

#### Failed Backup Recovery

```bash
# Retry specific component backup
./backup-platform.sh --component=spanner
./backup-platform.sh --component=bigquery
./backup-platform.sh --component=redis
```

#### Failed Failover Recovery

```bash
# Rollback to primary region
./regional-recovery.sh --from-dr

# Check service health
./test-dr-procedures.sh --quick-health-check
```

## Integration

### Operations Runbooks

These scripts integrate with existing operations runbooks:

- **Pattern Mining**: `docs/pattern-mining/operations-runbook.md`
- **Analysis Engine**: `docs/analysis-engine/operations/runbook.md`
- **Query Intelligence**: `docs/query-intelligence/operations/runbook.md`

### Monitoring Dashboards

- **Grafana Dashboard**: Disaster Recovery Overview
- **Cloud Monitoring**: Backup and DR Metrics
- **StatusPage**: Automated incident updates

### Incident Management

- **PagerDuty**: Automatic escalation for critical failures
- **Slack**: Real-time notifications for all DR events
- **Email**: Executive notifications for major incidents

## Security

### Secret Management

- Database credentials stored in Secret Manager
- API keys rotated automatically every 30 days
- Service account keys managed through IAM

### Audit Logging

- All DR operations logged to BigQuery audit dataset
- Access to DR scripts logged and monitored
- Compliance reporting generated monthly

### Access Control

- DR scripts require specific IAM roles
- Production access requires MFA
- Emergency access procedures documented

## Compliance

### Regulatory Requirements

- **SOC 2**: Annual DR testing and documentation
- **ISO 27001**: Quarterly DR procedure reviews
- **GDPR**: 72-hour incident notification procedures

### Documentation

- DR procedures reviewed quarterly
- Test results archived for audit purposes
- Incident post-mortems included in compliance reports

## Support

### Escalation Path

1. **On-call Engineer**: First responder for DR events
2. **Platform Team Lead**: Technical escalation
3. **Engineering Director**: Business decision authority
4. **CTO**: Executive escalation for major incidents

### Contact Information

- **Platform Team**: <EMAIL>
- **On-call**: +1-555-DR-ONCALL
- **Emergency Escalation**: <EMAIL>

### Documentation

- **Main DR Plan**: `docs/disaster-recovery-plan.md`
- **Research Documentation**: `research/operations/disaster-recovery.md`
- **Backup Strategies**: `research/google-cloud/backup-strategies.md`

---

**Last Updated**: January 2025  
**Review Schedule**: Quarterly  
**Owner**: Platform Engineering Team