#!/bin/bash
# Setup dedicated service accounts for Cloud Run services
# This script implements proper service account assignment to replace default credentials

set -euo pipefail

# Configuration
PROJECT_ID="${GCP_PROJECT_ID:-vibe-match-463114}"
ENVIRONMENT="${ENVIRONMENT:-production}"

# Color output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
echo_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
echo_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
echo_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Check prerequisites
check_prerequisites() {
    echo_info "Checking prerequisites..."
    
    if ! command -v gcloud &> /dev/null; then
        echo_error "gcloud CLI is required but not installed"
        exit 1
    fi
    
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | head -n1 &> /dev/null; then
        echo_error "Please authenticate with gcloud: gcloud auth login"
        exit 1
    fi
    
    # Set project
    gcloud config set project "$PROJECT_ID"
    echo_success "Using project: $PROJECT_ID"
}

# Create service account with specified roles
create_service_account() {
    local service_name="$1"
    local sa_name="$2"
    local display_name="$3"
    local description="$4"
    shift 4
    local roles=("$@")
    
    echo_info "Setting up service account for $service_name..."
    
    # Create service account if it doesn't exist
    if ! gcloud iam service-accounts describe "${sa_name}@${PROJECT_ID}.iam.gserviceaccount.com" &> /dev/null; then
        echo_info "Creating service account: $sa_name"
        gcloud iam service-accounts create "$sa_name" \
            --display-name="$display_name" \
            --description="$description"
        echo_success "Service account created: $sa_name"
    else
        echo_warning "Service account already exists: $sa_name"
    fi
    
    # Assign roles
    for role in "${roles[@]}"; do
        echo_info "Assigning role $role to $sa_name"
        gcloud projects add-iam-policy-binding "$PROJECT_ID" \
            --member="serviceAccount:${sa_name}@${PROJECT_ID}.iam.gserviceaccount.com" \
            --role="$role" \
            --quiet
    done
    
    echo_success "Service account $sa_name configured with ${#roles[@]} roles"
}

# Configure service-to-service permissions
configure_service_communication() {
    local service_name="$1"
    shift
    local allowed_callers=("$@")
    
    echo_info "Configuring service-to-service communication for $service_name..."
    
    # Get the Cloud Run service URL (this would be set during deployment)
    # For now, we'll configure the IAM policy for Cloud Run Invoker role
    
    for caller_sa in "${allowed_callers[@]}"; do
        echo_info "Granting Cloud Run Invoker role to $caller_sa for $service_name"
        # Note: This would be applied to the actual Cloud Run service during deployment
        # The exact command would be: gcloud run services add-iam-policy-binding $service_name
        # --member="serviceAccount:${caller_sa}@${PROJECT_ID}.iam.gserviceaccount.com"
        # --role="roles/run.invoker"
        echo "  - $caller_sa can invoke $service_name"
    done
}

# Main setup function
setup_service_accounts() {
    echo_info "Setting up service accounts for Cloud Run services..."
    
    # Analysis Engine Service Account
    create_service_account "analysis-engine" "ccl-analysis-engine-sa" \
        "Analysis Engine Service Account" \
        "Service account for Analysis Engine with minimal permissions" \
        "roles/spanner.databaseUser" \
        "roles/storage.objectViewer" \
        "roles/monitoring.metricWriter" \
        "roles/logging.logWriter" \
        "roles/cloudtrace.agent"
    
    # Query Intelligence Service Account
    create_service_account "query-intelligence" "ccl-query-intelligence-sa" \
        "Query Intelligence Service Account" \
        "Service account for Query Intelligence with AI/ML permissions" \
        "roles/spanner.databaseUser" \
        "roles/aiplatform.user" \
        "roles/monitoring.metricWriter" \
        "roles/logging.logWriter" \
        "roles/cloudtrace.agent" \
        "roles/storage.objectViewer"
    
    # Pattern Mining Service Account
    create_service_account "pattern-mining" "ccl-pattern-mining-sa" \
        "Pattern Mining Service Account" \
        "Service account for Pattern Mining with ML training permissions" \
        "roles/spanner.databaseUser" \
        "roles/aiplatform.user" \
        "roles/ml.developer" \
        "roles/storage.objectAdmin" \
        "roles/monitoring.metricWriter" \
        "roles/logging.logWriter" \
        "roles/cloudtrace.agent" \
        "roles/bigquery.dataEditor"
    
    # Marketplace Service Account
    create_service_account "marketplace" "ccl-marketplace-sa" \
        "Marketplace Service Account" \
        "Service account for Marketplace with transaction permissions" \
        "roles/spanner.databaseUser" \
        "roles/pubsub.publisher" \
        "roles/monitoring.metricWriter" \
        "roles/logging.logWriter" \
        "roles/cloudtrace.agent" \
        "roles/storage.objectViewer"
    
    # Collaboration Service Account
    create_service_account "collaboration" "ccl-collaboration-sa" \
        "Collaboration Service Account" \
        "Service account for Collaboration with real-time permissions" \
        "roles/spanner.databaseUser" \
        "roles/pubsub.editor" \
        "roles/monitoring.metricWriter" \
        "roles/logging.logWriter" \
        "roles/cloudtrace.agent"
    
    # Web Frontend Service Account
    create_service_account "web" "ccl-web-sa" \
        "Web Frontend Service Account" \
        "Service account for Web Frontend with minimal permissions" \
        "roles/monitoring.metricWriter" \
        "roles/logging.logWriter" \
        "roles/cloudtrace.agent"
}

# Configure service-to-service communication
setup_service_communication() {
    echo_info "Configuring service-to-service communication permissions..."
    
    configure_service_communication "analysis-engine" \
        "ccl-query-intelligence-sa" "ccl-pattern-mining-sa" "ccl-web-sa"
    
    configure_service_communication "query-intelligence" \
        "ccl-web-sa" "ccl-collaboration-sa"
    
    configure_service_communication "pattern-mining" \
        "ccl-query-intelligence-sa" "ccl-marketplace-sa" "ccl-web-sa"
    
    configure_service_communication "marketplace" \
        "ccl-web-sa"
    
    configure_service_communication "collaboration" \
        "ccl-web-sa"
}

# Disable default service account (optional - be careful with this)
disable_default_service_account() {
    echo_warning "Disabling default Compute Engine service account..."
    echo_warning "This is optional and should be done carefully"
    
    # Get default service account
    local default_sa="${PROJECT_ID}-<EMAIL>"
    
    echo_info "Default service account: $default_sa"
    echo_warning "Consider removing Editor role from default service account manually"
    echo_warning "Command: gcloud projects remove-iam-policy-binding $PROJECT_ID --member=serviceAccount:$default_sa --role=roles/editor"
}

# Verify setup
verify_setup() {
    echo_info "Verifying service account setup..."
    
    local service_accounts=(
        "ccl-analysis-engine-sa"
        "ccl-query-intelligence-sa"
        "ccl-pattern-mining-sa"
        "ccl-marketplace-sa"
        "ccl-collaboration-sa"
        "ccl-web-sa"
    )
    
    for sa in "${service_accounts[@]}"; do
        if gcloud iam service-accounts describe "${sa}@${PROJECT_ID}.iam.gserviceaccount.com" &> /dev/null; then
            echo_success "✓ Service account exists: $sa"
        else
            echo_error "✗ Service account missing: $sa"
        fi
    done
}

# Generate deployment configuration
generate_deployment_config() {
    echo_info "Generating deployment configuration with service accounts..."
    
    cat > "/tmp/cloud-run-service-accounts.yml" << EOF
# Cloud Run Service Account Configuration
# Use this configuration in your deployment scripts

services:
  analysis-engine:
    service_account: "ccl-analysis-engine-sa@${PROJECT_ID}.iam.gserviceaccount.com"
    
  query-intelligence:
    service_account: "ccl-query-intelligence-sa@${PROJECT_ID}.iam.gserviceaccount.com"
    
  pattern-mining:
    service_account: "ccl-pattern-mining-sa@${PROJECT_ID}.iam.gserviceaccount.com"
    
  marketplace:
    service_account: "ccl-marketplace-sa@${PROJECT_ID}.iam.gserviceaccount.com"
    
  collaboration:
    service_account: "ccl-collaboration-sa@${PROJECT_ID}.iam.gserviceaccount.com"
    
  web:
    service_account: "ccl-web-sa@${PROJECT_ID}.iam.gserviceaccount.com"

# Example gcloud run deploy command:
# gcloud run deploy analysis-engine \\
#   --image=gcr.io/${PROJECT_ID}/analysis-engine:latest \\
#   --service-account=ccl-analysis-engine-sa@${PROJECT_ID}.iam.gserviceaccount.com \\
#   --region=us-central1 \\
#   --project=${PROJECT_ID}
EOF
    
    echo_success "Deployment configuration generated: /tmp/cloud-run-service-accounts.yml"
}

# Main execution
main() {
    echo_info "=== Cloud Run Service Account Setup ==="
    echo_info "Project: $PROJECT_ID"
    echo_info "Environment: $ENVIRONMENT"
    echo ""
    
    check_prerequisites
    setup_service_accounts
    setup_service_communication
    verify_setup
    generate_deployment_config
    
    echo ""
    echo_success "=== Service Account Setup Complete ==="
    echo_info "Next steps:"
    echo "1. Update your deployment scripts to use the service accounts"
    echo "2. Update application code to remove hardcoded credentials"
    echo "3. Test service-to-service authentication"
    echo "4. Monitor for 'default credentials' warnings in logs"
    echo ""
    echo_warning "Important: Update your deployment configurations to use:"
    echo "  --service-account=SERVICE_ACCOUNT_EMAIL"
    echo ""
    echo_info "Configuration file: /tmp/cloud-run-service-accounts.yml"
}

# Run if called directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi