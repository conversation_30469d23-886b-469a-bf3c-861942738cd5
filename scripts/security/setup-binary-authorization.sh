#!/bin/bash
set -euo pipefail

# Binary Authorization Setup Script for Episteme Platform
# This script sets up Binary Authorization for Cloud Run services

# Configuration
PROJECT_ID="${PROJECT_ID:-vibe-match-463114}"
REGION="${REGION:-us-central1}"
ATTESTOR_NAME="${ATTESTOR_NAME:-episteme-production-attestor}"
NOTE_ID="${NOTE_ID:-episteme-attestation-note}"
KEY_RING_NAME="${KEY_RING_NAME:-episteme-binauthz-keys}"
KEY_NAME="${KEY_NAME:-signing-key}"
SECRET_NAME="${SECRET_NAME:-episteme-binauthz-signing-key}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if gcloud is installed and authenticated
    if ! command -v gcloud &> /dev/null; then
        log_error "gcloud CLI is not installed"
        exit 1
    fi
    
    # Check if user is authenticated
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
        log_error "No active gcloud authentication found. Please run: gcloud auth login"
        exit 1
    fi
    
    # Check if gpg is installed
    if ! command -v gpg &> /dev/null; then
        log_error "GPG is not installed"
        exit 1
    fi
    
    # Set the project
    gcloud config set project "${PROJECT_ID}"
    log_success "Prerequisites checked"
}

# Enable required APIs
enable_apis() {
    log_info "Enabling required APIs..."
    
    local apis=(
        "binaryauthorization.googleapis.com"
        "containeranalysis.googleapis.com"
        "cloudbuild.googleapis.com"
        "run.googleapis.com"
        "secretmanager.googleapis.com"
        "cloudkms.googleapis.com"
    )
    
    for api in "${apis[@]}"; do
        log_info "Enabling ${api}..."
        gcloud services enable "${api}"
    done
    
    log_success "Required APIs enabled"
}

# Create Cloud KMS key ring and key
create_kms_key() {
    log_info "Creating Cloud KMS key ring and key..."
    
    # Create key ring
    if ! gcloud kms keyrings describe "${KEY_RING_NAME}" --location=global &> /dev/null; then
        gcloud kms keyrings create "${KEY_RING_NAME}" --location=global
        log_success "Created key ring: ${KEY_RING_NAME}"
    else
        log_info "Key ring ${KEY_RING_NAME} already exists"
    fi
    
    # Create key
    if ! gcloud kms keys describe "${KEY_NAME}" --keyring="${KEY_RING_NAME}" --location=global &> /dev/null; then
        gcloud kms keys create "${KEY_NAME}" \
            --keyring="${KEY_RING_NAME}" \
            --location=global \
            --purpose=asymmetric-signing \
            --default-algorithm=rsa-sign-pkcs1-4096-sha512
        log_success "Created signing key: ${KEY_NAME}"
    else
        log_info "Signing key ${KEY_NAME} already exists"
    fi
}

# Create attestation note
create_attestation_note() {
    log_info "Creating attestation note..."
    
    local note_payload=$(cat << EOF
{
  "name": "projects/${PROJECT_ID}/notes/${NOTE_ID}",
  "attestation": {
    "hint": {
      "human_readable_name": "Episteme Production Attestation Authority"
    }
  }
}
EOF
)
    
    # Check if note already exists
    if curl -s -H "Authorization: Bearer $(gcloud auth print-access-token)" \
       "https://containeranalysis.googleapis.com/v1/projects/${PROJECT_ID}/notes/${NOTE_ID}" | grep -q "name"; then
        log_info "Attestation note ${NOTE_ID} already exists"
    else
        # Create the note
        echo "${note_payload}" | curl -X POST \
            -H "Content-Type: application/json" \
            -H "Authorization: Bearer $(gcloud auth print-access-token)" \
            -d @- \
            "https://containeranalysis.googleapis.com/v1/projects/${PROJECT_ID}/notes?noteId=${NOTE_ID}"
        
        log_success "Created attestation note: ${NOTE_ID}"
    fi
}

# Get KMS public key and create attestor
create_attestor() {
    log_info "Creating Binary Authorization attestor..."
    
    # Get the public key from KMS
    local public_key_file="/tmp/kms-public-key.pem"
    gcloud kms keys versions get-public-key 1 \
        --key="${KEY_NAME}" \
        --keyring="${KEY_RING_NAME}" \
        --location=global \
        --output-file="${public_key_file}"
    
    # Check if attestor already exists
    if gcloud container binauthz attestors describe "${ATTESTOR_NAME}" &> /dev/null; then
        log_info "Attestor ${ATTESTOR_NAME} already exists"
    else
        # Create the attestor
        gcloud container binauthz attestors create "${ATTESTOR_NAME}" \
            --attestation-authority-note="projects/${PROJECT_ID}/notes/${NOTE_ID}" \
            --attestation-authority-note-public-key-file="${public_key_file}" \
            --attestation-authority-note-public-key-id="//cloudkms.googleapis.com/v1/projects/${PROJECT_ID}/locations/global/keyRings/${KEY_RING_NAME}/cryptoKeys/${KEY_NAME}/cryptoKeyVersions/1"
        
        log_success "Created attestor: ${ATTESTOR_NAME}"
    fi
    
    # Clean up temporary file
    rm -f "${public_key_file}"
}

# Create Binary Authorization policy
create_policy() {
    log_info "Creating Binary Authorization policy..."
    
    local policy_file="/tmp/binauthz-policy.yaml"
    
    cat > "${policy_file}" << EOF
defaultAdmissionRule:
  requireAttestationsBy:
  - projects/${PROJECT_ID}/attestors/${ATTESTOR_NAME}
  enforcementMode: ENFORCED_BLOCK_AND_AUDIT_LOG
admissionWhitelistPatterns:
- namePattern: gcr.io/google-containers/*
- namePattern: gcr.io/google_containers/*
- namePattern: k8s.gcr.io/*
- namePattern: gcr.io/gke-release/*
- namePattern: gcr.io/distroless/*
clusterAdmissionRules: {}
kubernetesNamespaceAdmissionRules: {}
kubernetesServiceAccountAdmissionRules: {}
istioServiceIdentityAdmissionRules: {}
name: projects/${PROJECT_ID}/policy
EOF
    
    # Import the policy
    gcloud container binauthz policy import "${policy_file}"
    log_success "Created Binary Authorization policy"
    
    # Clean up temporary file
    rm -f "${policy_file}"
}

# Create service account for CI/CD
create_service_account() {
    log_info "Creating service account for CI/CD..."
    
    local sa_name="episteme-binauthz-signer"
    local sa_email="${sa_name}@${PROJECT_ID}.iam.gserviceaccount.com"
    
    # Check if service account exists
    if gcloud iam service-accounts describe "${sa_email}" &> /dev/null; then
        log_info "Service account ${sa_email} already exists"
    else
        # Create service account
        gcloud iam service-accounts create "${sa_name}" \
            --display-name="Episteme Binary Authorization Signer" \
            --description="Service account for signing container images in CI/CD pipeline"
        
        log_success "Created service account: ${sa_email}"
    fi
    
    # Grant necessary permissions
    local roles=(
        "roles/containeranalysis.occurrences.editor"
        "roles/containeranalysis.notes.viewer"
        "roles/cloudkms.signerVerifier"
        "roles/binaryauthorization.attestorsViewer"
    )
    
    for role in "${roles[@]}"; do
        gcloud projects add-iam-policy-binding "${PROJECT_ID}" \
            --member="serviceAccount:${sa_email}" \
            --role="${role}" \
            --quiet
    done
    
    log_success "Granted permissions to service account"
    
    # Create and store service account key in Secret Manager
    local key_file="/tmp/sa-key.json"
    
    if gcloud secrets describe "${SECRET_NAME}-sa-key" &> /dev/null; then
        log_info "Service account key secret already exists"
    else
        gcloud iam service-accounts keys create "${key_file}" \
            --iam-account="${sa_email}"
        
        gcloud secrets create "${SECRET_NAME}-sa-key" \
            --data-file="${key_file}"
        
        log_success "Created and stored service account key in Secret Manager"
        rm -f "${key_file}"
    fi
}

# Test the setup
test_setup() {
    log_info "Testing Binary Authorization setup..."
    
    # Test policy evaluation with a known image
    local test_image="gcr.io/google-containers/pause:latest"
    
    log_info "Testing policy evaluation with whitelisted image: ${test_image}"
    if gcloud container binauthz policy evaluate --image-url="${test_image}"; then
        log_success "Whitelisted image passed policy evaluation"
    else
        log_warning "Whitelisted image failed policy evaluation - this may be expected"
    fi
    
    # List attestors
    log_info "Listing created attestors:"
    gcloud container binauthz attestors list
    
    # Show current policy
    log_info "Current Binary Authorization policy:"
    gcloud container binauthz policy export
}

# Generate GitHub Actions configuration
generate_github_actions_config() {
    log_info "Generating GitHub Actions configuration..."
    
    local config_dir="/tmp/binauthz-config"
    mkdir -p "${config_dir}"
    
    cat > "${config_dir}/sign-image-action.yml" << 'EOF'
# Add this step to your GitHub Actions workflow after building and pushing the image
- name: Sign container image with Binary Authorization
  env:
    GOOGLE_APPLICATION_CREDENTIALS: /tmp/sa-key.json
    PROJECT_ID: ${{ env.PROJECT_ID }}
    ATTESTOR_NAME: ${{ env.ATTESTOR_NAME }}
    IMAGE_URL: ${{ env.IMAGE_URL }}
  run: |
    # Create credentials file from secret
    echo '${{ secrets.EPISTEME_BINAUTHZ_SA_KEY }}' > /tmp/sa-key.json
    
    # Authenticate with gcloud
    gcloud auth activate-service-account --key-file=/tmp/sa-key.json
    gcloud config set project ${PROJECT_ID}
    
    # Sign the image
    gcloud beta container binauthz attestations sign-and-create \
      --artifact-url="${IMAGE_URL}" \
      --attestor="projects/${PROJECT_ID}/attestors/${ATTESTOR_NAME}" \
      --attestor-project="${PROJECT_ID}" \
      --keyversion-project="${PROJECT_ID}" \
      --keyversion-location="global" \
      --keyversion-keyring="episteme-binauthz-keys" \
      --keyversion-key="signing-key" \
      --keyversion="1"
    
    # Clean up credentials file
    rm -f /tmp/sa-key.json
EOF
    
    cat > "${config_dir}/cloudbuild-sign-step.yml" << 'EOF'
# Add this step to your cloudbuild.yaml after the push-image step
- name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
  id: 'sign-image'
  entrypoint: 'bash'
  args:
    - '-c'
    - |
      # Sign the image using Cloud KMS
      gcloud beta container binauthz attestations sign-and-create \
        --artifact-url="${_REGION}-docker.pkg.dev/${PROJECT_ID}/${_REPOSITORY}/${_SERVICE_NAME}:${COMMIT_SHA}" \
        --attestor="projects/${PROJECT_ID}/attestors/${SUBSTITUTIONS._ATTESTOR_NAME}" \
        --attestor-project="${PROJECT_ID}" \
        --keyversion-project="${PROJECT_ID}" \
        --keyversion-location="global" \
        --keyversion-keyring="${SUBSTITUTIONS._KEY_RING_NAME}" \
        --keyversion-key="${SUBSTITUTIONS._KEY_NAME}" \
        --keyversion="1"
  waitFor: ['push-image']

# Add these substitutions to your cloudbuild.yaml
substitutions:
  _ATTESTOR_NAME: episteme-production-attestor
  _KEY_RING_NAME: episteme-binauthz-keys
  _KEY_NAME: signing-key
EOF
    
    log_success "Generated configuration files in ${config_dir}/"
    log_info "Files created:"
    ls -la "${config_dir}/"
}

# Create validation script
create_validation_script() {
    log_info "Creating validation script..."
    
    local script_path="/tmp/validate-binary-authorization.sh"
    
    cat > "${script_path}" << 'EOF'
#!/bin/bash
# Validation script for Binary Authorization
set -euo pipefail

PROJECT_ID="${PROJECT_ID:-vibe-match-463114}"
REGION="${REGION:-us-central1}"
TEST_SERVICE_NAME="binauthz-test-$(date +%s)"

echo "Testing Binary Authorization enforcement..."

# Test 1: Try to deploy an unsigned image (should fail)
echo "Test 1: Attempting to deploy unsigned image (should fail)..."
if gcloud run deploy "${TEST_SERVICE_NAME}-unsigned" \
    --image=gcr.io/google-containers/pause:latest \
    --region="${REGION}" \
    --allow-unauthenticated \
    --quiet 2>/dev/null; then
    echo "❌ FAIL: Unsigned image was allowed to deploy"
    # Clean up
    gcloud run services delete "${TEST_SERVICE_NAME}-unsigned" --region="${REGION}" --quiet
    exit 1
else
    echo "✅ PASS: Unsigned image was correctly blocked"
fi

# Test 2: Check if whitelisted images can deploy
echo "Test 2: Attempting to deploy whitelisted image (should succeed)..."
if gcloud run deploy "${TEST_SERVICE_NAME}-whitelisted" \
    --image=gcr.io/distroless/base:latest \
    --region="${REGION}" \
    --allow-unauthenticated \
    --quiet; then
    echo "✅ PASS: Whitelisted image was allowed to deploy"
    # Clean up
    gcloud run services delete "${TEST_SERVICE_NAME}-whitelisted" --region="${REGION}" --quiet
else
    echo "❌ FAIL: Whitelisted image was blocked"
    exit 1
fi

echo "✅ Binary Authorization validation completed successfully"
EOF
    
    chmod +x "${script_path}"
    log_success "Created validation script: ${script_path}"
}

# Main setup function
main() {
    log_info "Starting Binary Authorization setup for Episteme platform..."
    log_info "Project ID: ${PROJECT_ID}"
    log_info "Region: ${REGION}"
    log_info "Attestor Name: ${ATTESTOR_NAME}"
    
    check_prerequisites
    enable_apis
    create_kms_key
    create_attestation_note
    create_attestor
    create_policy
    create_service_account
    test_setup
    generate_github_actions_config
    create_validation_script
    
    log_success "Binary Authorization setup completed successfully!"
    log_info ""
    log_info "Next steps:"
    log_info "1. Add the service account key to GitHub Secrets as 'EPISTEME_BINAUTHZ_SA_KEY'"
    log_info "2. Update your CI/CD pipelines with the signing steps"
    log_info "3. Run the validation script: /tmp/validate-binary-authorization.sh"
    log_info "4. Update Cloud Run services to enforce Binary Authorization"
}

# Run main function if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
EOF