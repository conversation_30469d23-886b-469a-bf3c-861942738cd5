#!/bin/bash

# Production secrets deployment script
# This script creates secrets in Google Cloud Secret Manager
# for secure production deployment

set -e

# Configuration
PROJECT_ID="${GCP_PROJECT_ID:-}"
REGION="${GCP_REGION:-us-central1}"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

echo "🔒 Deploying production secrets to Google Cloud Secret Manager"
echo "============================================================"

# Check prerequisites
if [ -z "$PROJECT_ID" ]; then
    echo "❌ Error: GCP_PROJECT_ID environment variable not set"
    echo "Usage: GCP_PROJECT_ID=your-project-id $0"
    exit 1
fi

if ! command -v gcloud &> /dev/null; then
    echo "❌ Error: gcloud CLI not installed"
    echo "Install from: https://cloud.google.com/sdk/docs/install"
    exit 1
fi

# Verify authentication
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q "@"; then
    echo "❌ Error: Not authenticated with gcloud"
    echo "Run: gcloud auth login"
    exit 1
fi

# Set project
echo "🏗️  Setting project to $PROJECT_ID..."
gcloud config set project "$PROJECT_ID"

# Enable required APIs
echo "🔌 Enabling required APIs..."
gcloud services enable secretmanager.googleapis.com
gcloud services enable cloudrun.googleapis.com
gcloud services enable cloudbuild.googleapis.com

# Function to create or update secret
create_secret() {
    local secret_name="$1"
    local secret_value="$2"
    local description="$3"
    
    echo "Creating secret: $secret_name"
    
    # Check if secret exists
    if gcloud secrets describe "$secret_name" --project="$PROJECT_ID" &>/dev/null; then
        echo "  Secret $secret_name already exists, adding new version..."
        echo -n "$secret_value" | gcloud secrets versions add "$secret_name" --data-file=- --project="$PROJECT_ID"
    else
        echo "  Creating new secret $secret_name..."
        echo -n "$secret_value" | gcloud secrets create "$secret_name" \
            --data-file=- \
            --project="$PROJECT_ID" \
            --labels=environment=production,service=episteme \
            --replication-policy=automatic
    fi
}

# Generate secure production secrets
echo "🔐 Generating production secrets..."

# Database secrets
POSTGRES_PASSWORD=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-25)
create_secret "prod-postgres-password" "$POSTGRES_PASSWORD" "PostgreSQL password for production"

# Redis secrets
REDIS_PASSWORD=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-25)
create_secret "prod-redis-password" "$REDIS_PASSWORD" "Redis password for production"

# Application secrets
JWT_SECRET=$(openssl rand -hex 32)
create_secret "prod-jwt-secret" "$JWT_SECRET" "JWT secret key for production"

SESSION_SECRET=$(openssl rand -hex 32)
create_secret "prod-session-secret" "$SESSION_SECRET" "Session secret key for production"

# API keys (placeholders - replace with real values)
GEMINI_API_KEY="${GEMINI_API_KEY:-REPLACE_WITH_REAL_GEMINI_API_KEY}"
create_secret "prod-gemini-api-key" "$GEMINI_API_KEY" "Google Gemini API key for production"

OPENAI_API_KEY="${OPENAI_API_KEY:-REPLACE_WITH_REAL_OPENAI_API_KEY}"
create_secret "prod-openai-api-key" "$OPENAI_API_KEY" "OpenAI API key for production"

# Database URL with secret reference
DATABASE_URL="postgresql://ccl_prod:$POSTGRES_PASSWORD@$PROJECT_ID:$REGION:episteme-db/episteme_prod"
create_secret "prod-database-url" "$DATABASE_URL" "Complete database URL for production"

# Redis URL with secret reference
REDIS_URL="rediss://:$REDIS_PASSWORD@$PROJECT_ID-redis:6380/0"
create_secret "prod-redis-url" "$REDIS_URL" "Complete Redis URL for production"

# Create service accounts
echo "🔐 Creating service accounts..."

# Analysis Engine service account
ANALYSIS_SA="analysis-engine@$PROJECT_ID.iam.gserviceaccount.com"
if ! gcloud iam service-accounts describe "$ANALYSIS_SA" --project="$PROJECT_ID" &>/dev/null; then
    gcloud iam service-accounts create analysis-engine \
        --display-name="Analysis Engine Service Account" \
        --description="Service account for analysis-engine service" \
        --project="$PROJECT_ID"
fi

# Grant necessary permissions
echo "🔑 Granting IAM permissions..."

# Secret Manager permissions
gcloud projects add-iam-policy-binding "$PROJECT_ID" \
    --member="serviceAccount:$ANALYSIS_SA" \
    --role="roles/secretmanager.secretAccessor"

# Cloud SQL permissions  
gcloud projects add-iam-policy-binding "$PROJECT_ID" \
    --member="serviceAccount:$ANALYSIS_SA" \
    --role="roles/cloudsql.client"

# Cloud Storage permissions
gcloud projects add-iam-policy-binding "$PROJECT_ID" \
    --member="serviceAccount:$ANALYSIS_SA" \
    --role="roles/storage.objectAdmin"

# BigQuery permissions
gcloud projects add-iam-policy-binding "$PROJECT_ID" \
    --member="serviceAccount:$ANALYSIS_SA" \
    --role="roles/bigquery.dataEditor"

# Cloud Run permissions
gcloud projects add-iam-policy-binding "$PROJECT_ID" \
    --member="serviceAccount:$ANALYSIS_SA" \
    --role="roles/run.developer"

echo "✅ Production secrets deployment complete!"
echo ""
echo "📋 Created secrets in Google Cloud Secret Manager:"
echo "  - prod-postgres-password"
echo "  - prod-redis-password" 
echo "  - prod-jwt-secret"
echo "  - prod-session-secret"
echo "  - prod-gemini-api-key (update with real value)"
echo "  - prod-openai-api-key (update with real value)"
echo "  - prod-database-url"
echo "  - prod-redis-url"
echo ""
echo "🔐 Created service account:"
echo "  - analysis-engine@$PROJECT_ID.iam.gserviceaccount.com"
echo ""
echo "🚀 Next steps:"
echo "1. Update API keys with real values:"
echo "   echo 'REAL_API_KEY' | gcloud secrets versions add prod-gemini-api-key --data-file=-"
echo "   echo 'REAL_API_KEY' | gcloud secrets versions add prod-openai-api-key --data-file=-"
echo ""
echo "2. Deploy with Cloud Run using secrets:"
echo "   gcloud run deploy analysis-engine \\"
echo "     --image=gcr.io/$PROJECT_ID/analysis-engine:latest \\"
echo "     --service-account=$ANALYSIS_SA \\"
echo "     --set-secrets=JWT_SECRET=prod-jwt-secret:latest \\"
echo "     --set-secrets=DATABASE_URL=prod-database-url:latest \\"
echo "     --region=$REGION \\"
echo "     --project=$PROJECT_ID"
echo ""
echo "3. Use secrets in docker-compose.prod.yml with external references"