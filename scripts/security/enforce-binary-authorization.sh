#!/bin/bash
# Binary Authorization Enforcement Script
# Creates Binary Authorization policy and attestor for production security

set -euo pipefail

PROJECT_ID=${GCP_PROJECT_ID:-vibe-match-463114}
ATTESTOR_NAME="episteme-production-attestor"
KEY_LOCATION="global"
KEY_RING="episteme-binauthz-keys"
KEY_NAME="signing-key"
POLICY_FILE="binauthz-policy.yaml"

echo "🔐 Setting up Binary Authorization for project: $PROJECT_ID"

# Function to create KMS key ring and key
setup_kms() {
    echo "Setting up KMS key ring and signing key..."
    
    # Create key ring (ignore error if exists)
    gcloud kms keyrings create "$KEY_RING" \
        --location="$KEY_LOCATION" \
        --project="$PROJECT_ID" 2>/dev/null || echo "Key ring already exists"
    
    # Create signing key (ignore error if exists)
    gcloud kms keys create "$KEY_NAME" \
        --keyring="$KEY_RING" \
        --location="$KEY_LOCATION" \
        --purpose=asymmetric-signing \
        --default-algorithm=rsa-sign-pkcs1-4096-sha512 \
        --project="$PROJECT_ID" 2>/dev/null || echo "Signing key already exists"
    
    echo "✅ KMS setup complete"
}

# Function to create attestor
setup_attestor() {
    echo "Setting up Binary Authorization attestor..."
    
    # Get the public key
    PUBLIC_KEY_FILE="/tmp/public_key.pem"
    gcloud kms keys versions get-public-key 1 \
        --key="$KEY_NAME" \
        --keyring="$KEY_RING" \
        --location="$KEY_LOCATION" \
        --output-file="$PUBLIC_KEY_FILE" \
        --project="$PROJECT_ID"
    
    # Create attestor (ignore error if exists)
    gcloud container binauthz attestors create "$ATTESTOR_NAME" \
        --attestation-authority-note-project="$PROJECT_ID" \
        --project="$PROJECT_ID" 2>/dev/null || echo "Attestor already exists"
    
    # Add public key to attestor
    gcloud container binauthz attestors public-keys add \
        --attestor="$ATTESTOR_NAME" \
        --public-key-file="$PUBLIC_KEY_FILE" \
        --project="$PROJECT_ID" 2>/dev/null || echo "Public key already added"
    
    # Clean up temp file
    rm -f "$PUBLIC_KEY_FILE"
    
    echo "✅ Attestor setup complete"
}

# Function to create Binary Authorization policy
setup_policy() {
    echo "Creating Binary Authorization policy..."
    
    cat > "$POLICY_FILE" << EOF
defaultAdmissionRule:
  requireAttestationsBy:
  - projects/$PROJECT_ID/attestors/$ATTESTOR_NAME
  enforcementMode: ENFORCED_BLOCK_AND_AUDIT_LOG
clusterAdmissionRules:
  us-central1.episteme-production:
    requireAttestationsBy:
    - projects/$PROJECT_ID/attestors/$ATTESTOR_NAME
    enforcementMode: ENFORCED_BLOCK_AND_AUDIT_LOG
    evaluationMode: REQUIRE_ATTESTATION
admissionWhitelistPatterns:
# Allow Google-managed base images
- namePattern: gcr.io/distroless/*
- namePattern: gcr.io/gke-release/*
- namePattern: us.gcr.io/k8s-artifacts-prod/*
# Allow Cloud Build images
- namePattern: gcr.io/cloud-builders/*
# Allow our staging environment for testing
- namePattern: ${PROJECT_ID}/episteme-*:staging-*
EOF

    # Apply the policy
    gcloud container binauthz policy import "$POLICY_FILE" \
        --project="$PROJECT_ID"
    
    echo "✅ Binary Authorization policy applied"
    rm -f "$POLICY_FILE"
}

# Function to sign an image
sign_image() {
    local IMAGE_URL=$1
    local IMAGE_DIGEST=$2
    
    echo "Signing image: ${IMAGE_URL}@${IMAGE_DIGEST}"
    
    gcloud beta container binauthz attestations sign-and-create \
        --artifact-url="$IMAGE_URL@$IMAGE_DIGEST" \
        --attestor="$ATTESTOR_NAME" \
        --attestor-project="$PROJECT_ID" \
        --keyversion-project="$PROJECT_ID" \
        --keyversion-location="$KEY_LOCATION" \
        --keyversion-keyring="$KEY_RING" \
        --keyversion-key="$KEY_NAME" \
        --keyversion="1"
    
    echo "✅ Image signed and attested successfully"
}

# Function to validate setup
validate_setup() {
    echo "Validating Binary Authorization setup..."
    
    # List attestors
    echo "Attestors:"
    gcloud container binauthz attestors list --project="$PROJECT_ID"
    
    # Show policy
    echo "Current policy:"
    gcloud container binauthz policy export --project="$PROJECT_ID"
    
    echo "✅ Setup validation complete"
}

# Main execution
main() {
    case "${1:-setup}" in
        "setup")
            echo "🚀 Setting up Binary Authorization..."
            setup_kms
            setup_attestor
            setup_policy
            validate_setup
            echo "✅ Binary Authorization setup complete!"
            ;;
        "sign")
            if [ $# -ne 3 ]; then
                echo "Usage: $0 sign <image-url> <image-digest>"
                exit 1
            fi
            sign_image "$2" "$3"
            ;;
        "validate")
            validate_setup
            ;;
        *)
            echo "Usage: $0 {setup|sign|validate}"
            echo "  setup    - Set up Binary Authorization"
            echo "  sign     - Sign an image (requires image-url and digest)"
            echo "  validate - Validate current setup"
            exit 1
            ;;
    esac
}

main "$@"