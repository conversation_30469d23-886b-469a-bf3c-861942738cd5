#!/bin/bash

# Setup script for secure secret management
# This script creates the necessary secret files for development
# and provides instructions for production deployment

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
SECRETS_DIR="$PROJECT_ROOT/secrets"

echo "🔒 Setting up secure secret management for Episteme"
echo "=================================================="

# Create secrets directory if it doesn't exist
if [ ! -d "$SECRETS_DIR" ]; then
    echo "📁 Creating secrets directory..."
    mkdir -p "$SECRETS_DIR"
    chmod 700 "$SECRETS_DIR"
fi

# Function to generate secure password
generate_password() {
    openssl rand -base64 32 | tr -d "=+/" | cut -c1-25
}

# Function to generate JWT secret
generate_jwt_secret() {
    openssl rand -hex 32
}

# Development secrets setup
echo "🛠️  Setting up development secrets..."

# PostgreSQL password
if [ ! -f "$SECRETS_DIR/dev-postgres-password.txt" ]; then
    echo "Generating PostgreSQL password..."
    generate_password > "$SECRETS_DIR/dev-postgres-password.txt"
    chmod 600 "$SECRETS_DIR/dev-postgres-password.txt"
fi

# Redis password
if [ ! -f "$SECRETS_DIR/dev-redis-password.txt" ]; then
    echo "Generating Redis password..."
    generate_password > "$SECRETS_DIR/dev-redis-password.txt"
    chmod 600 "$SECRETS_DIR/dev-redis-password.txt"
fi

# JWT secret
if [ ! -f "$SECRETS_DIR/dev-jwt-secret.txt" ]; then
    echo "Generating JWT secret..."
    generate_jwt_secret > "$SECRETS_DIR/dev-jwt-secret.txt"
    chmod 600 "$SECRETS_DIR/dev-jwt-secret.txt"
fi

# Session secret
if [ ! -f "$SECRETS_DIR/dev-session-secret.txt" ]; then
    echo "Generating session secret..."
    generate_jwt_secret > "$SECRETS_DIR/dev-session-secret.txt"
    chmod 600 "$SECRETS_DIR/dev-session-secret.txt"
fi

# GCP Service Account placeholder
if [ ! -f "$SECRETS_DIR/dev-gcp-service-account-key.json" ]; then
    echo "Creating GCP service account key placeholder..."
    cat > "$SECRETS_DIR/dev-gcp-service-account-key.json" << 'EOF'
********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
EOF
    chmod 600 "$SECRETS_DIR/dev-gcp-service-account-key.json"
    echo "⚠️  IMPORTANT: Replace placeholder GCP service account key with real credentials"
fi

# Create environment file for development
echo "📄 Creating development environment file..."
cat > "$PROJECT_ROOT/.env.local" << EOF
# Development environment variables
# Generated by setup-secrets.sh on $(date)

DATABASE_URL=postgresql://ccl_dev:\$(cat $SECRETS_DIR/dev-postgres-password.txt)@postgres:5432/ccl_local
POSTGRES_PASSWORD=\$(cat $SECRETS_DIR/dev-postgres-password.txt)
REDIS_PASSWORD=\$(cat $SECRETS_DIR/dev-redis-password.txt)
JWT_SECRET=\$(cat $SECRETS_DIR/dev-jwt-secret.txt)
SESSION_SECRET=\$(cat $SECRETS_DIR/dev-session-secret.txt)
GOOGLE_APPLICATION_CREDENTIALS=$SECRETS_DIR/dev-gcp-service-account-key.json

# MinIO (local S3-compatible storage)
MINIO_ROOT_USER=minioadmin
MINIO_ROOT_PASSWORD=\$(cat $SECRETS_DIR/dev-postgres-password.txt)

# Grafana
GRAFANA_ADMIN_USER=admin
GRAFANA_ADMIN_PASSWORD=\$(cat $SECRETS_DIR/dev-postgres-password.txt)

# Application
ENVIRONMENT=development
LOG_LEVEL=DEBUG
GCP_PROJECT_ID=ccl-local
EOF

chmod 600 "$PROJECT_ROOT/.env.local"

echo "✅ Development secrets setup complete!"
echo ""
echo "📋 Next steps:"
echo "1. Replace the placeholder GCP service account key with real credentials"
echo "2. Source the environment: source .env.local"
echo "3. Use docker-compose.secrets.yml for secure development:"
echo "   docker-compose -f docker/docker-compose.base.yml -f docker/docker-compose.secrets.yml up"
echo ""
echo "🏭 For production deployment:"
echo "1. Store all secrets in Google Cloud Secret Manager:"
echo "   ./scripts/security/deploy-production-secrets.sh"
echo "2. Use Cloud Run with Workload Identity"
echo "3. Never use .env files in production containers"
echo ""
echo "🔒 Security reminders:"
echo "- secrets/ directory is in .gitignore"
echo "- .env.local is in .gitignore"
echo "- All secret files have restrictive permissions (600)"
echo "- Use different secrets for each environment"