#!/bin/bash

# Development Secret Setup Script
# Generates secure development secrets for local Docker Compose environment
# This script creates the secrets/ directory and generates secure random secrets

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" &> /dev/null && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
SECRETS_DIR="$PROJECT_ROOT/secrets"

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if openssl is available
    if ! command -v openssl &> /dev/null; then
        log_error "openssl is required but not installed"
        exit 1
    fi
    
    # Check if we're in the right directory
    if [[ ! -f "$PROJECT_ROOT/docker-compose.dev.yml" ]] && [[ ! -f "$PROJECT_ROOT/docker/docker-compose.base.yml" ]]; then
        log_error "This script must be run from the project root or scripts/security directory"
        log_error "Current directory: $(pwd)"
        log_error "Project root: $PROJECT_ROOT"
        exit 1
    fi
    
    log_info "Prerequisites check passed"
}

# Create secrets directory
create_secrets_directory() {
    log_info "Creating secrets directory..."
    
    if [[ -d "$SECRETS_DIR" ]]; then
        log_warn "Secrets directory already exists: $SECRETS_DIR"
        
        # Ask for confirmation to overwrite
        read -p "Do you want to regenerate all secrets? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "Keeping existing secrets"
            return 0
        fi
        
        log_warn "Backing up existing secrets..."
        mv "$SECRETS_DIR" "${SECRETS_DIR}.backup.$(date +%Y%m%d_%H%M%S)"
    fi
    
    mkdir -p "$SECRETS_DIR"
    chmod 700 "$SECRETS_DIR"
    
    log_info "Created secrets directory: $SECRETS_DIR"
}

# Generate secure password
generate_password() {
    local length=${1:-32}
    openssl rand -base64 $length | tr -d "=+/" | cut -c1-$length
}

# Generate secure hex key
generate_hex_key() {
    local length=${1:-64}
    openssl rand -hex $length
}

# Generate database secrets
generate_database_secrets() {
    log_info "Generating database secrets..."
    
    # PostgreSQL password
    local postgres_password=$(generate_password 32)
    echo -n "$postgres_password" > "$SECRETS_DIR/dev-postgres-password.txt"
    log_info "Generated PostgreSQL password"
    
    # Redis password
    local redis_password=$(generate_password 32)
    echo -n "$redis_password" > "$SECRETS_DIR/dev-redis-password.txt"
    log_info "Generated Redis password"
}

# Generate application secrets
generate_application_secrets() {
    log_info "Generating application secrets..."
    
    # JWT secret (longer for security)
    local jwt_secret=$(generate_hex_key 64)
    echo -n "$jwt_secret" > "$SECRETS_DIR/dev-jwt-secret.txt"
    log_info "Generated JWT secret"
    
    # Session secret
    local session_secret=$(generate_hex_key 32)
    echo -n "$session_secret" > "$SECRETS_DIR/dev-session-secret.txt"
    log_info "Generated session secret"
    
    # Webhook secret
    local webhook_secret=$(generate_hex_key 32)
    echo -n "$webhook_secret" > "$SECRETS_DIR/dev-webhook-secret.txt"
    log_info "Generated webhook secret"
}

# Generate GCP service account key (placeholder for development)
generate_gcp_secrets() {
    log_info "Generating GCP development credentials..."
    
    # Create a placeholder service account key for development
    # In production, this would be a real service account key
    cat > "$SECRETS_DIR/dev-gcp-service-account-key.json" << EOF
****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
EOF
    
    log_warn "Generated placeholder GCP service account key for development"
    log_warn "Replace with real service account key for staging/production"
}

# Generate API keys (placeholders for development)
generate_api_secrets() {
    log_info "Generating API key placeholders..."
    
    # Gemini API key placeholder
    echo -n "dev-gemini-key-$(date +%s)" > "$SECRETS_DIR/dev-gemini-api-key.txt"
    
    # OpenAI API key placeholder
    echo -n "dev-openai-key-$(date +%s)" > "$SECRETS_DIR/dev-openai-api-key.txt"
    
    # GitHub token placeholder
    echo -n "dev-github-token-$(date +%s)" > "$SECRETS_DIR/dev-github-token.txt"
    
    log_info "Generated API key placeholders"
    log_warn "Replace with real API keys for actual functionality"
}

# Set proper file permissions
set_file_permissions() {
    log_info "Setting secure file permissions..."
    
    # Set directory permissions
    chmod 700 "$SECRETS_DIR"
    
    # Set file permissions (read-only for owner)
    find "$SECRETS_DIR" -type f -exec chmod 600 {} \;
    
    log_info "Set secure permissions on all secret files"
}

# Update .gitignore
update_gitignore() {
    log_info "Updating .gitignore to exclude secrets..."
    
    local gitignore_file="$PROJECT_ROOT/.gitignore"
    
    # Check if secrets/ is already in .gitignore
    if grep -q "^secrets/" "$gitignore_file" 2>/dev/null; then
        log_info "secrets/ already in .gitignore"
    else
        echo "" >> "$gitignore_file"
        echo "# Development secrets (generated by scripts/security/setup-dev-secrets.sh)" >> "$gitignore_file"
        echo "secrets/" >> "$gitignore_file"
        echo "*.secret" >> "$gitignore_file"
        echo "*.key" >> "$gitignore_file"
        echo ".env.local" >> "$gitignore_file"
        echo ".env.production" >> "$gitignore_file"
        log_info "Added secrets exclusions to .gitignore"
    fi
}

# Create environment file template
create_env_template() {
    log_info "Creating environment template..."
    
    cat > "$PROJECT_ROOT/.env.secrets.example" << EOF
# Environment variables for secret-based configuration
# Copy to .env.secrets and customize for your environment

# Database Configuration
DATABASE_HOST=postgres
DATABASE_PORT=5432
DATABASE_NAME=ccl_local
DATABASE_USER=ccl_dev
# Password loaded from secrets/dev-postgres-password.txt

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379
# Password loaded from secrets/dev-redis-password.txt

# Application Configuration
# JWT secret loaded from secrets/dev-jwt-secret.txt
# Session secret loaded from secrets/dev-session-secret.txt

# GCP Configuration
# Service account key loaded from secrets/dev-gcp-service-account-key.json
BIGQUERY_PROJECT=ccl-local
BIGQUERY_DATASET=patterns

# Development-specific settings
ENVIRONMENT=development
LOG_LEVEL=DEBUG
DEBUG=true
EOF
    
    log_info "Created .env.secrets.example template"
}

# Validate secret generation
validate_secrets() {
    log_info "Validating generated secrets..."
    
    local validation_failed=false
    
    # List of required secret files
    local required_secrets=(
        "dev-postgres-password.txt"
        "dev-redis-password.txt"
        "dev-jwt-secret.txt"
        "dev-session-secret.txt"
        "dev-gcp-service-account-key.json"
    )
    
    for secret_file in "${required_secrets[@]}"; do
        local secret_path="$SECRETS_DIR/$secret_file"
        
        if [[ ! -f "$secret_path" ]]; then
            log_error "Missing secret file: $secret_file"
            validation_failed=true
            continue
        fi
        
        # Check file is not empty
        if [[ ! -s "$secret_path" ]]; then
            log_error "Empty secret file: $secret_file"
            validation_failed=true
            continue
        fi
        
        # Check file permissions
        local perms=$(stat -c "%a" "$secret_path" 2>/dev/null || stat -f "%A" "$secret_path" 2>/dev/null)
        if [[ "$perms" != "600" ]]; then
            log_warn "Incorrect permissions on $secret_file: $perms (should be 600)"
        fi
        
        log_info "✓ Validated $secret_file"
    done
    
    if [[ "$validation_failed" == "true" ]]; then
        log_error "Secret validation failed"
        exit 1
    fi
    
    log_info "All secrets validated successfully"
}

# Print usage instructions
print_usage_instructions() {
    log_info "Development secrets setup completed successfully!"
    echo
    echo "Usage Instructions:"
    echo "=================="
    echo
    echo "1. Start services with secrets:"
    echo "   docker-compose -f docker-compose.base.yml -f docker-compose.secrets.yml up"
    echo
    echo "2. Validate configuration:"
    echo "   docker-compose -f docker-compose.base.yml -f docker-compose.secrets.yml config"
    echo
    echo "3. View generated secrets (for debugging):"
    echo "   ls -la secrets/"
    echo
    echo "4. Regenerate secrets anytime by running this script again"
    echo
    log_warn "Important: Never commit the secrets/ directory to version control"
    log_warn "For production, use Google Cloud Secret Manager instead"
}

# Main execution
main() {
    echo "========================================"
    echo "Episteme Development Secrets Setup"
    echo "========================================"
    echo
    
    check_prerequisites
    create_secrets_directory
    generate_database_secrets
    generate_application_secrets
    generate_gcp_secrets
    generate_api_secrets
    set_file_permissions
    update_gitignore
    create_env_template
    validate_secrets
    print_usage_instructions
}

# Run main function
main "$@"