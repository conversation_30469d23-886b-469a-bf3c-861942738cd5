#!/bin/bash

# Secret Manager Integration Script for Deployment
# Updates deployment scripts to work with Google Cloud Secret Manager
# This script helps migrate from hardcoded secrets to Secret Manager

set -euo pipefail

# Configuration
PROJECT_ID="${PROJECT_ID:-vibe-match-463114}"
ENVIRONMENT="${ENVIRONMENT:-production}"
REGION="${REGION:-us-central1}"

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# Help function
show_help() {
    cat << EOF
Secret Manager Integration Script for Deployment

USAGE:
    $0 [OPTIONS] COMMAND

COMMANDS:
    create-secrets      Create secrets in Google Cloud Secret Manager
    update-deployment   Update deployment scripts to use Secret Manager
    validate           Validate secret manager integration
    help               Show this help message

OPTIONS:
    --project PROJECT   GCP project ID (default: $PROJECT_ID)
    --environment ENV   Environment (default: $ENVIRONMENT)
    --region REGION     GCP region (default: $REGION)
    --dry-run          Show what would be done without executing

EXAMPLES:
    $0 create-secrets                    # Create all production secrets
    $0 --environment staging create-secrets  # Create staging secrets
    $0 update-deployment                 # Update deployment scripts
    $0 validate                          # Validate integration

EOF
}

# Parse command line arguments
COMMAND=""
DRY_RUN=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --project)
            PROJECT_ID="$2"
            shift 2
            ;;
        --environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        --region)
            REGION="$2"
            shift 2
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        create-secrets|update-deployment|validate|help)
            COMMAND="$1"
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

if [[ -z "$COMMAND" ]]; then
    log_error "No command specified"
    show_help
    exit 1
fi

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if gcloud is installed and authenticated
    if ! command -v gcloud &> /dev/null; then
        log_error "gcloud CLI is not installed"
        exit 1
    fi
    
    # Check if user is authenticated
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" &> /dev/null; then
        log_error "Not authenticated with gcloud. Run: gcloud auth login"
        exit 1
    fi
    
    # Set project
    gcloud config set project "$PROJECT_ID"
    
    # Enable Secret Manager API
    if [[ "$DRY_RUN" == "false" ]]; then
        log_info "Enabling Secret Manager API..."
        gcloud services enable secretmanager.googleapis.com
    fi
    
    log_info "Prerequisites check passed"
}

# Generate secure password
generate_password() {
    local length=${1:-32}
    openssl rand -base64 $length | tr -d "=+/" | cut -c1-$length
}

# Generate secure hex key
generate_hex_key() {
    local length=${1:-64}
    openssl rand -hex $length
}

# Create secret in Secret Manager
create_secret() {
    local secret_name=$1
    local secret_value=$2
    local description=${3:-""}
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] Would create secret: $secret_name"
        return 0
    fi
    
    # Check if secret already exists
    if gcloud secrets describe "$secret_name" &> /dev/null; then
        log_warn "Secret $secret_name already exists, adding new version"
        echo -n "$secret_value" | gcloud secrets versions add "$secret_name" --data-file=-
    else
        log_info "Creating secret: $secret_name"
        echo -n "$secret_value" | gcloud secrets create "$secret_name" \
            --data-file=- \
            --replication-policy="automatic"
        
        # Add description if provided
        if [[ -n "$description" ]]; then
            gcloud secrets update "$secret_name" --update-labels="description=$description"
        fi
    fi
    
    log_info "✓ Created/updated secret: $secret_name"
}

# Create all required secrets
create_secrets() {
    log_info "Creating secrets for environment: $ENVIRONMENT"
    
    # Database secrets
    log_info "Creating database secrets..."
    create_secret "${ENVIRONMENT}-postgres-password" "$(generate_password 32)" "PostgreSQL password for $ENVIRONMENT"
    create_secret "${ENVIRONMENT}-redis-password" "$(generate_password 32)" "Redis password for $ENVIRONMENT"
    
    # Application secrets
    log_info "Creating application secrets..."
    create_secret "${ENVIRONMENT}-jwt-secret" "$(generate_hex_key 64)" "JWT signing key for $ENVIRONMENT"
    create_secret "${ENVIRONMENT}-session-secret" "$(generate_hex_key 32)" "Session encryption key for $ENVIRONMENT"
    create_secret "${ENVIRONMENT}-webhook-secret" "$(generate_hex_key 32)" "Webhook signature key for $ENVIRONMENT"
    
    # API key placeholders (to be updated manually with real values)
    log_info "Creating API key placeholders..."
    create_secret "${ENVIRONMENT}-gemini-api-key" "REPLACE_WITH_REAL_GEMINI_API_KEY" "Google Gemini API key for $ENVIRONMENT"
    create_secret "${ENVIRONMENT}-openai-api-key" "REPLACE_WITH_REAL_OPENAI_API_KEY" "OpenAI API key for $ENVIRONMENT"
    create_secret "${ENVIRONMENT}-github-token" "REPLACE_WITH_REAL_GITHUB_TOKEN" "GitHub API token for $ENVIRONMENT"
    
    # Additional service-specific secrets
    if [[ "$ENVIRONMENT" == "production" ]]; then
        log_info "Creating production-specific secrets..."
        create_secret "${ENVIRONMENT}-stripe-secret-key" "REPLACE_WITH_REAL_STRIPE_KEY" "Stripe secret key for production"
        create_secret "${ENVIRONMENT}-stripe-webhook-secret" "REPLACE_WITH_REAL_STRIPE_WEBHOOK_SECRET" "Stripe webhook secret for production"
    fi
    
    log_info "✅ All secrets created successfully"
    log_warn "Remember to update API key placeholders with real values:"
    log_warn "  gcloud secrets versions add ${ENVIRONMENT}-gemini-api-key --data-file=-"
    log_warn "  gcloud secrets versions add ${ENVIRONMENT}-openai-api-key --data-file=-"
    log_warn "  gcloud secrets versions add ${ENVIRONMENT}-github-token --data-file=-"
}

# Update deployment scripts
update_deployment() {
    log_info "Updating deployment scripts for Secret Manager integration..."
    
    # Update analysis-engine deployment script
    local analysis_engine_deploy="/home/<USER>/work/episteme/episteme/services/analysis-engine/scripts/deployment/deploy.sh"
    
    if [[ -f "$analysis_engine_deploy" ]]; then
        log_info "✓ Analysis engine deployment script already has Secret Manager integration"
    else
        log_warn "Analysis engine deployment script not found: $analysis_engine_deploy"
    fi
    
    # Create Cloud Run deployment helper
    create_cloud_run_helper
    
    log_info "✅ Deployment scripts updated for Secret Manager"
}

# Create Cloud Run deployment helper
create_cloud_run_helper() {
    local helper_script="/home/<USER>/work/episteme/episteme/scripts/deploy-with-secrets.sh"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] Would create Cloud Run deployment helper: $helper_script"
        return 0
    fi
    
    cat > "$helper_script" << 'EOF'
#!/bin/bash

# Cloud Run Deployment with Secret Manager Integration
# This script deploys services to Cloud Run with proper secret configuration

set -euo pipefail

SERVICE_NAME="${1:-analysis-engine}"
IMAGE_URL="${2}"
PROJECT_ID="${3:-vibe-match-463114}"
REGION="${4:-us-central1}"
ENVIRONMENT="${5:-production}"

if [[ -z "$IMAGE_URL" ]]; then
    echo "Usage: $0 SERVICE_NAME IMAGE_URL [PROJECT_ID] [REGION] [ENVIRONMENT]"
    exit 1
fi

echo "Deploying $SERVICE_NAME to Cloud Run with Secret Manager integration..."

# Deploy to Cloud Run with secret manager integration
gcloud run deploy "$SERVICE_NAME" \
    --image="$IMAGE_URL" \
    --project="$PROJECT_ID" \
    --region="$REGION" \
    --platform=managed \
    --allow-unauthenticated \
    --set-env-vars="ENVIRONMENT=$ENVIRONMENT" \
    --set-secrets="POSTGRES_PASSWORD=${ENVIRONMENT}-postgres-password:latest" \
    --set-secrets="REDIS_PASSWORD=${ENVIRONMENT}-redis-password:latest" \
    --set-secrets="JWT_SECRET=${ENVIRONMENT}-jwt-secret:latest" \
    --set-secrets="SESSION_SECRET=${ENVIRONMENT}-session-secret:latest" \
    --set-secrets="GEMINI_API_KEY=${ENVIRONMENT}-gemini-api-key:latest" \
    --memory=4Gi \
    --cpu=4 \
    --min-instances=1 \
    --max-instances=100 \
    --timeout=300

echo "✅ $SERVICE_NAME deployed successfully with Secret Manager integration"
EOF
    
    chmod +x "$helper_script"
    log_info "✓ Created Cloud Run deployment helper: $helper_script"
}

# Validate Secret Manager integration
validate_integration() {
    log_info "Validating Secret Manager integration..."
    
    local validation_failed=false
    
    # Required secrets for the environment
    local required_secrets=(
        "${ENVIRONMENT}-postgres-password"
        "${ENVIRONMENT}-redis-password"
        "${ENVIRONMENT}-jwt-secret"
        "${ENVIRONMENT}-session-secret"
        "${ENVIRONMENT}-webhook-secret"
    )
    
    # Check if all required secrets exist
    for secret_name in "${required_secrets[@]}"; do
        if gcloud secrets describe "$secret_name" &> /dev/null; then
            # Check if secret has a value
            local secret_value
            secret_value=$(gcloud secrets versions access latest --secret="$secret_name" 2>/dev/null || echo "")
            
            if [[ -n "$secret_value" && "$secret_value" != "REPLACE_WITH_REAL_"* ]]; then
                log_info "✓ Secret $secret_name exists and has a value"
            else
                log_warn "⚠️  Secret $secret_name exists but needs real value"
            fi
        else
            log_error "❌ Secret $secret_name does not exist"
            validation_failed=true
        fi
    done
    
    # Check Docker Compose files for hardcoded secrets
    log_info "Checking Docker Compose files for hardcoded secrets..."
    local compose_dir="/home/<USER>/work/episteme/episteme"
    
    # This should return no results if successful
    local hardcoded_secrets
    hardcoded_secrets=$(grep -r "password.*:" "$compose_dir/docker-compose*.yml" "$compose_dir/docker/docker-compose*.yml" 2>/dev/null | grep -v "_FILE\|_PATH\|CHANGE_ME_INSECURE_DEFAULT\|comment\|description\|example\|⚠️\|#" || true)
    
    if [[ -z "$hardcoded_secrets" ]]; then
        log_info "✅ No hardcoded secrets found in Docker Compose files"
    else
        log_error "❌ Found potential hardcoded secrets:"
        echo "$hardcoded_secrets"
        validation_failed=true
    fi
    
    # Check if deployment scripts exist
    local deploy_script="/home/<USER>/work/episteme/episteme/scripts/deploy-with-secrets.sh"
    if [[ -f "$deploy_script" ]]; then
        log_info "✓ Cloud Run deployment helper exists"
    else
        log_warn "⚠️  Cloud Run deployment helper not found"
    fi
    
    if [[ "$validation_failed" == "true" ]]; then
        log_error "❌ Validation failed"
        return 1
    else
        log_info "✅ Validation passed"
        return 0
    fi
}

# Main execution
main() {
    log_info "Secret Manager Integration Script"
    log_info "Project: $PROJECT_ID"
    log_info "Environment: $ENVIRONMENT"
    log_info "Region: $REGION"
    log_info "Command: $COMMAND"
    
    check_prerequisites
    
    case "$COMMAND" in
        "create-secrets")
            create_secrets
            ;;
        "update-deployment")
            update_deployment
            ;;
        "validate")
            validate_integration
            ;;
        "help")
            show_help
            ;;
        *)
            log_error "Unknown command: $COMMAND"
            exit 1
            ;;
    esac
    
    log_info "✅ Secret Manager integration completed successfully"
}

# Run main function
main "$@"