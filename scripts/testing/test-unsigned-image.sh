#!/bin/bash
# Test script to verify Binary Authorization blocks unsigned images
set -euo pipefail

PROJECT_ID="${PROJECT_ID:-vibe-match-463114}"
REGION="${REGION:-us-central1}"
TEST_SERVICE_NAME="test-unsigned-$(date +%s)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Testing Binary Authorization enforcement...${NC}"
echo "Project ID: $PROJECT_ID"
echo "Region: $REGION"
echo "Test service: $TEST_SERVICE_NAME"
echo ""

# Test: Try to deploy an unsigned image (should fail)
echo -e "${YELLOW}Attempting to deploy unsigned image (should fail)...${NC}"

if gcloud run deploy "$TEST_SERVICE_NAME" \
    --image=gcr.io/cloudrun/hello \
    --region="$REGION" \
    --allow-unauthenticated \
    --quiet \
    --no-traffic 2>&1; then
    
    echo -e "${RED}❌ FAIL: Unsigned image was allowed to deploy${NC}"
    echo "Binary Authorization is not properly enforced!"
    
    # Clean up the deployed service
    echo "Cleaning up deployed service..."
    gcloud run services delete "$TEST_SERVICE_NAME" --region="$REGION" --quiet || true
    
    exit 1
else
    echo -e "${GREEN}✅ PASS: Unsigned image was correctly blocked${NC}"
    echo "Binary Authorization is working correctly!"
fi

echo ""
echo -e "${GREEN}Test completed successfully!${NC}"
echo "Binary Authorization is properly configured and enforcing policies."