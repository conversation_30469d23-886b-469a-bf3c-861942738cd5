#!/bin/bash
# Binary Authorization Validation Script
# Tests Binary Authorization setup and enforcement

set -euo pipefail

PROJECT_ID=${GCP_PROJECT_ID:-vibe-match-463114}
ATTESTOR_NAME="episteme-production-attestor"

echo "🔍 Validating Binary Authorization setup..."

# Function to check if attestor exists
check_attestor() {
    echo "Checking attestor existence..."
    
    if gcloud container binauthz attestors describe "$ATTESTOR_NAME" \
        --project="$PROJECT_ID" >/dev/null 2>&1; then
        echo "✅ Attestor '$ATTESTOR_NAME' exists"
        
        # Show attestor details
        gcloud container binauthz attestors describe "$ATTESTOR_NAME" \
            --project="$PROJECT_ID" \
            --format="yaml(name,userOwnedGrafeasNote.publicKeys[].asciiArmoredPgpPublicKey)"
        
        return 0
    else
        echo "❌ Attestor '$ATTESTOR_NAME' not found"
        return 1
    fi
}

# Function to check Binary Authorization policy
check_policy() {
    echo "Checking Binary Authorization policy..."
    
    local policy_output
    if policy_output=$(gcloud container binauthz policy export --project="$PROJECT_ID" 2>/dev/null); then
        echo "✅ Binary Authorization policy exists"
        
        # Check if our attestor is in the policy
        if echo "$policy_output" | grep -q "$ATTESTOR_NAME"; then
            echo "✅ Attestor is configured in policy"
        else
            echo "⚠️  Attestor not found in policy"
        fi
        
        # Show enforcement mode
        echo "Policy enforcement mode:"
        echo "$policy_output" | grep -A2 -B2 "enforcementMode" || echo "No enforcement mode found"
        
        return 0
    else
        echo "❌ Binary Authorization policy not found"
        return 1
    fi
}

# Function to check KMS key
check_kms_key() {
    echo "Checking KMS signing key..."
    
    local key_ring="episteme-binauthz-keys"
    local key_name="signing-key"
    local location="global"
    
    if gcloud kms keys describe "$key_name" \
        --keyring="$key_ring" \
        --location="$location" \
        --project="$PROJECT_ID" >/dev/null 2>&1; then
        echo "✅ KMS signing key exists"
        
        # Show key details
        gcloud kms keys describe "$key_name" \
            --keyring="$key_ring" \
            --location="$location" \
            --project="$PROJECT_ID" \
            --format="table(name,purpose,algorithm,createTime)"
        
        return 0
    else
        echo "❌ KMS signing key not found"
        return 1
    fi
}

# Function to test attestation creation (dry run)
test_attestation() {
    echo "Testing attestation creation (dry run)..."
    
    # Use a test image (doesn't need to exist for validation)
    local test_image="gcr.io/$PROJECT_ID/test-validation:latest"
    local test_digest="sha256:0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef"
    
    echo "Test image: $test_image@$test_digest"
    
    # This will fail because the image doesn't exist, but will validate the setup
    if gcloud beta container binauthz attestations sign-and-create \
        --artifact-url="$test_image@$test_digest" \
        --attestor="$ATTESTOR_NAME" \
        --attestor-project="$PROJECT_ID" \
        --keyversion-project="$PROJECT_ID" \
        --keyversion-location="global" \
        --keyversion-keyring="episteme-binauthz-keys" \
        --keyversion-key="signing-key" \
        --keyversion="1" \
        --dry-run 2>&1; then
        echo "✅ Attestation creation validation passed"
        return 0
    else
        echo "⚠️  Attestation validation completed (expected for non-existent image)"
        return 0
    fi
}

# Function to show API status
check_apis() {
    echo "Checking required APIs..."
    
    local required_apis=(
        "binaryauthorization.googleapis.com"
        "containeranalysis.googleapis.com" 
        "cloudkms.googleapis.com"
    )
    
    for api in "${required_apis[@]}"; do
        if gcloud services list --enabled \
            --filter="name:$api" \
            --project="$PROJECT_ID" \
            --format="value(name)" | grep -q "$api"; then
            echo "✅ $api is enabled"
        else
            echo "❌ $api is not enabled"
            echo "   Run: gcloud services enable $api --project=$PROJECT_ID"
        fi
    done
}

# Function to generate validation report
generate_report() {
    echo ""
    echo "📊 Binary Authorization Validation Report"
    echo "========================================"
    echo "Project: $PROJECT_ID"
    echo "Attestor: $ATTESTOR_NAME"
    echo "Timestamp: $(date -u +"%Y-%m-%d %H:%M:%S UTC")"
    echo ""
    
    local status="PASS"
    
    # Run all checks
    check_apis || status="FAIL"
    echo ""
    check_kms_key || status="FAIL"
    echo ""
    check_attestor || status="FAIL"
    echo ""
    check_policy || status="FAIL"
    echo ""
    test_attestation || true  # Don't fail on this
    echo ""
    
    echo "Overall Status: $status"
    
    if [ "$status" = "PASS" ]; then
        echo "✅ Binary Authorization is properly configured and ready for use"
        return 0
    else
        echo "❌ Binary Authorization setup has issues that need to be resolved"
        return 1
    fi
}

# Main execution
main() {
    case "${1:-report}" in
        "report")
            generate_report
            ;;
        "attestor")
            check_attestor
            ;;
        "policy")
            check_policy
            ;;
        "kms")
            check_kms_key
            ;;
        "apis")
            check_apis
            ;;
        *)
            echo "Usage: $0 {report|attestor|policy|kms|apis}"
            echo "  report   - Generate full validation report (default)"
            echo "  attestor - Check attestor configuration"
            echo "  policy   - Check Binary Authorization policy"
            echo "  kms      - Check KMS signing key"
            echo "  apis     - Check required API enablement"
            exit 1
            ;;
    esac
}

main "$@"