#!/bin/bash
# PagerDuty Integration Testing Script
# Tests alert routing and PagerDuty incident creation

set -euo pipefail

ALERTMANAGER_URL=${ALERTMANAGER_URL:-http://localhost:9093}
PAGERDUTY_INTEGRATION_KEY=${PAGERDUTY_INTEGRATION_KEY:-""}

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to log with colors
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }

echo "🚨 PagerDuty Integration Testing"
echo "================================"
echo "Alertmanager URL: $ALERTMANAGER_URL"
echo "Timestamp: $(date -u +"%Y-%m-%d %H:%M:%S UTC")"
echo ""

# Function to check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if curl is available
    if ! command -v curl &> /dev/null; then
        log_error "curl is required but not installed"
        exit 1
    fi
    
    # Check if jq is available for JSON parsing
    if ! command -v jq &> /dev/null; then
        log_warning "jq not installed - JSON parsing will be limited"
    fi
    
    # Check if Alertmanager is accessible
    if ! curl -sf "$ALERTMANAGER_URL/api/v1/status" >/dev/null 2>&1; then
        log_error "Alertmanager not accessible at $ALERTMANAGER_URL"
        log_info "Start Alertmanager with: docker-compose -f docker/docker-compose.monitoring.yml up -d alertmanager"
        exit 1
    fi
    
    log_success "Prerequisites verified"
}

# Function to test alertmanager configuration
test_alertmanager_config() {
    log_info "Testing Alertmanager configuration..."
    
    # Get configuration from Alertmanager
    local config_url="$ALERTMANAGER_URL/api/v1/status"
    local config_response
    
    if config_response=$(curl -sf "$config_url" 2>/dev/null); then
        log_success "Alertmanager configuration accessible"
        
        # Check if PagerDuty is configured (if jq is available)
        if command -v jq &> /dev/null; then
            if echo "$config_response" | jq -r '.data.configYAML' | grep -q "pagerduty"; then
                log_success "PagerDuty configuration found in Alertmanager"
            else
                log_warning "PagerDuty configuration not found in Alertmanager config"
            fi
        fi
    else
        log_error "Failed to fetch Alertmanager configuration"
        return 1
    fi
}

# Function to send test alert
send_test_alert() {
    local severity="$1"
    local service="$2"
    local description="$3"
    
    log_info "Sending $severity test alert for $service..."
    
    local alert_payload
    alert_payload=$(cat <<EOF
[
  {
    "labels": {
      "alertname": "TestAlert_${service}_${severity}",
      "service": "$service",
      "instance": "test-instance",
      "severity": "$severity",
      "category": "test"
    },
    "annotations": {
      "summary": "$description",
      "description": "Test alert sent from PagerDuty integration test script",
      "runbook_url": "https://docs.episteme.io/runbooks/$service"
    },
    "startsAt": "$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)",
    "endsAt": "$(date -u -d '+5 minutes' +%Y-%m-%dT%H:%M:%S.%3NZ)",
    "generatorURL": "https://test.episteme.io/generator"
  }
]
EOF
)

    # Send alert to Alertmanager
    local alerts_url="$ALERTMANAGER_URL/api/v1/alerts"
    local response
    
    if response=$(curl -sf -X POST "$alerts_url" \
        -H "Content-Type: application/json" \
        -d "$alert_payload" 2>&1); then
        log_success "$severity alert sent successfully"
        return 0
    else
        log_error "Failed to send $severity alert: $response"
        return 1
    fi
}

# Function to verify alert receipt
verify_alert_receipt() {
    local alertname="$1"
    local max_wait="$2"
    
    log_info "Verifying alert receipt: $alertname (waiting up to ${max_wait}s)..."
    
    local alerts_url="$ALERTMANAGER_URL/api/v1/alerts"
    local elapsed=0
    
    while [ $elapsed -lt $max_wait ]; do
        local alerts_response
        if alerts_response=$(curl -sf "$alerts_url" 2>/dev/null); then
            if command -v jq &> /dev/null; then
                # Use jq for precise filtering
                if echo "$alerts_response" | jq -r '.data[].labels.alertname' | grep -q "$alertname"; then
                    log_success "Alert $alertname received by Alertmanager"
                    return 0
                fi
            else
                # Fallback to grep
                if echo "$alerts_response" | grep -q "$alertname"; then
                    log_success "Alert $alertname received by Alertmanager"
                    return 0
                fi
            fi
        fi
        
        sleep 5
        elapsed=$((elapsed + 5))
    done
    
    log_warning "Alert $alertname not found in Alertmanager after ${max_wait}s"
    return 1
}

# Function to test direct PagerDuty integration
test_direct_pagerduty() {
    log_info "Testing direct PagerDuty integration..."
    
    if [ -z "$PAGERDUTY_INTEGRATION_KEY" ]; then
        log_warning "PAGERDUTY_INTEGRATION_KEY not set - skipping direct PagerDuty test"
        log_info "Set integration key with: export PAGERDUTY_INTEGRATION_KEY=your_key_here"
        return 0
    fi
    
    local pagerduty_url="https://events.pagerduty.com/v2/enqueue"
    local payload
    payload=$(cat <<EOF
{
  "routing_key": "$PAGERDUTY_INTEGRATION_KEY",
  "event_action": "trigger",
  "payload": {
    "summary": "Episteme PagerDuty Integration Test",
    "source": "episteme-test-script",
    "severity": "critical",
    "component": "monitoring",
    "group": "episteme-platform",
    "class": "integration-test",
    "custom_details": {
      "test_timestamp": "$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)",
      "test_description": "This is a test alert from the Episteme PagerDuty integration validation script",
      "dashboard": "https://grafana.episteme.io/d/episteme-overview",
      "runbook": "https://docs.episteme.io/runbooks/monitoring"
    }
  },
  "client": "Episteme Test Script",
  "client_url": "https://github.com/episteme/platform"
}
EOF
)

    local response
    if response=$(curl -sf -X POST "$pagerduty_url" \
        -H "Content-Type: application/json" \
        -d "$payload" 2>&1); then
        
        log_success "Direct PagerDuty test alert sent successfully"
        
        # Try to extract dedup_key if jq is available
        if command -v jq &> /dev/null; then
            local dedup_key
            dedup_key=$(echo "$response" | jq -r '.dedup_key // "unknown"')
            log_info "PagerDuty incident dedup_key: $dedup_key"
        fi
        
        log_info "Check PagerDuty dashboard for the test incident"
        return 0
    else
        log_error "Direct PagerDuty test failed: $response"
        return 1
    fi
}

# Function to test alert silencing
test_alert_silencing() {
    log_info "Testing alert silencing..."
    
    local silence_url="$ALERTMANAGER_URL/api/v1/silences"
    local silence_payload
    silence_payload=$(cat <<EOF
{
  "matchers": [
    {
      "name": "alertname",
      "value": "TestAlert_.*",
      "isRegex": true
    }
  ],
  "startsAt": "$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)",
  "endsAt": "$(date -u -d '+10 minutes' +%Y-%m-%dT%H:%M:%S.%3NZ)",
  "createdBy": "test-script",
  "comment": "Silencing test alerts from PagerDuty integration test"
}
EOF
)

    local response
    if response=$(curl -sf -X POST "$silence_url" \
        -H "Content-Type: application/json" \
        -d "$silence_payload" 2>&1); then
        
        log_success "Test alert silence created successfully"
        
        # Extract silence ID if jq is available
        if command -v jq &> /dev/null; then
            local silence_id
            silence_id=$(echo "$response" | jq -r '.silenceID // "unknown"')
            log_info "Silence ID: $silence_id"
        fi
        
        return 0
    else
        log_error "Failed to create silence: $response"
        return 1
    fi
}

# Function to run comprehensive integration test
run_integration_test() {
    log_info "Running comprehensive PagerDuty integration test..."
    
    local test_results=()
    local tests_passed=0
    local total_tests=0
    
    # Test 1: Configuration validation
    ((total_tests++))
    if test_alertmanager_config; then
        ((tests_passed++))
        test_results+=("✅ Configuration validation")
    else
        test_results+=("❌ Configuration validation")
    fi
    
    # Test 2: Critical alert (should trigger PagerDuty)
    ((total_tests++))
    if send_test_alert "critical" "analysis-engine" "Critical test alert for PagerDuty integration"; then
        if verify_alert_receipt "TestAlert_analysis-engine_critical" 30; then
            ((tests_passed++))
            test_results+=("✅ Critical alert routing")
        else
            test_results+=("❌ Critical alert routing")
        fi
    else
        test_results+=("❌ Critical alert routing")
    fi
    
    # Test 3: High priority alert (should NOT trigger PagerDuty)
    ((total_tests++))
    if send_test_alert "high" "collaboration" "High priority test alert"; then
        if verify_alert_receipt "TestAlert_collaboration_high" 15; then
            ((tests_passed++))
            test_results+=("✅ High priority alert routing")
        else
            test_results+=("❌ High priority alert routing")
        fi
    else
        test_results+=("❌ High priority alert routing")
    fi
    
    # Test 4: Direct PagerDuty integration
    ((total_tests++))
    if test_direct_pagerduty; then
        ((tests_passed++))
        test_results+=("✅ Direct PagerDuty integration")
    else
        test_results+=("❌ Direct PagerDuty integration")
    fi
    
    # Test 5: Alert silencing
    ((total_tests++))
    if test_alert_silencing; then
        ((tests_passed++))
        test_results+=("✅ Alert silencing")
    else
        test_results+=("❌ Alert silencing")
    fi
    
    # Display results
    echo ""
    log_info "📊 Test Results Summary:"
    for result in "${test_results[@]}"; do
        echo "   $result"
    done
    
    echo ""
    log_info "Tests Passed: $tests_passed/$total_tests"
    
    if [ $tests_passed -eq $total_tests ]; then
        log_success "🎉 All PagerDuty integration tests PASSED!"
        return 0
    else
        log_warning "⚠️  Some PagerDuty integration tests failed"
        return 1
    fi
}

# Function to show manual verification steps
show_manual_steps() {
    echo ""
    log_info "📋 Manual Verification Steps:"
    echo ""
    echo "1. Check PagerDuty Dashboard:"
    echo "   - Look for test incidents created during this test"
    echo "   - Verify incident details match the test alert payload"
    echo ""
    echo "2. Verify Alertmanager Web UI:"
    echo "   - Visit: $ALERTMANAGER_URL"
    echo "   - Check active alerts and silences"
    echo ""
    echo "3. Test Production Integration:"
    echo "   - Set PAGERDUTY_INTEGRATION_KEY environment variable"
    echo "   - Re-run with: $0 --with-pagerduty"
    echo ""
    echo "4. Cleanup Test Alerts:"
    echo "   - Resolve any test incidents in PagerDuty"
    echo "   - Remove silences from Alertmanager if needed"
}

# Main execution
main() {
    case "${1:-test}" in
        "test"|"")
            check_prerequisites
            run_integration_test
            show_manual_steps
            ;;
        "--with-pagerduty")
            if [ -z "$PAGERDUTY_INTEGRATION_KEY" ]; then
                log_error "PAGERDUTY_INTEGRATION_KEY must be set for direct PagerDuty testing"
                echo "export PAGERDUTY_INTEGRATION_KEY=your_integration_key_here"
                exit 1
            fi
            check_prerequisites
            run_integration_test
            show_manual_steps
            ;;
        "--config-only")
            check_prerequisites
            test_alertmanager_config
            ;;
        "--direct-only")
            if [ -z "$PAGERDUTY_INTEGRATION_KEY" ]; then
                log_error "PAGERDUTY_INTEGRATION_KEY must be set for direct testing"
                exit 1
            fi
            test_direct_pagerduty
            ;;
        *)
            echo "Usage: $0 [test|--with-pagerduty|--config-only|--direct-only]"
            echo "  test              - Run integration test without direct PagerDuty (default)"
            echo "  --with-pagerduty  - Run full test including direct PagerDuty integration"
            echo "  --config-only     - Test only Alertmanager configuration"
            echo "  --direct-only     - Test only direct PagerDuty integration"
            echo ""
            echo "Environment Variables:"
            echo "  ALERTMANAGER_URL           - Alertmanager URL (default: http://localhost:9093)"
            echo "  PAGERDUTY_INTEGRATION_KEY  - PagerDuty integration key for direct testing"
            exit 1
            ;;
    esac
}

main "$@"