#!/bin/bash

# SOC 2 Compliance Automation Test Script
# Tests the comprehensive SOC 2 compliance monitoring and reporting system

set -e

echo "🔒 SOC 2 Compliance Automation Test Suite"
echo "========================================"

# Configuration
API_BASE_URL="${API_BASE_URL:-http://localhost:8001}"
TEST_RESULTS_DIR="test-results/soc2-compliance"
mkdir -p "$TEST_RESULTS_DIR"

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Test counter
TESTS_PASSED=0
TESTS_FAILED=0

# Helper function to make API calls
api_call() {
    local method=$1
    local endpoint=$2
    local data=$3
    local expected_status=$4
    
    echo -n "Testing $method $endpoint... "
    
    if [ -z "$data" ]; then
        response=$(curl -s -w "\n%{http_code}" -X "$method" "$API_BASE_URL$endpoint" -H "Content-Type: application/json")
    else
        response=$(curl -s -w "\n%{http_code}" -X "$method" "$API_BASE_URL$endpoint" -H "Content-Type: application/json" -d "$data")
    fi
    
    status_code=$(echo "$response" | tail -n1)
    body=$(echo "$response" | sed '$d')
    
    if [ "$status_code" = "$expected_status" ]; then
        echo -e "${GREEN}✓${NC} (Status: $status_code)"
        ((TESTS_PASSED++))
        return 0
    else
        echo -e "${RED}✗${NC} (Expected: $expected_status, Got: $status_code)"
        echo "Response: $body"
        ((TESTS_FAILED++))
        return 1
    fi
}

# Test helper to verify JSON structure
check_json_field() {
    local json=$1
    local field=$2
    local test_name=$3
    
    echo -n "Checking $test_name... "
    
    if echo "$json" | jq -e ".$field" > /dev/null 2>&1; then
        echo -e "${GREEN}✓${NC}"
        ((TESTS_PASSED++))
        return 0
    else
        echo -e "${RED}✗${NC} (Field '$field' not found)"
        ((TESTS_FAILED++))
        return 1
    fi
}

echo ""
echo "1. Testing Health Check"
echo "-----------------------"
api_call GET "/api/compliance/soc2/health" "" "200"

echo ""
echo "2. Testing Dashboard Endpoints"
echo "------------------------------"
api_call GET "/api/compliance/soc2/dashboard" "" "200"
api_call GET "/api/compliance/soc2/dashboard?time_range=last_7_days" "" "200"
api_call GET "/api/compliance/soc2/dashboard/executive" "" "200"
api_call GET "/api/compliance/soc2/dashboard/trends?days=30" "" "200"

echo ""
echo "3. Testing Metrics Endpoint"
echo "---------------------------"
echo -n "Testing Prometheus metrics endpoint... "
response=$(curl -s -w "\n%{http_code}" "$API_BASE_URL/api/compliance/soc2/metrics")
status_code=$(echo "$response" | tail -n1)
body=$(echo "$response" | sed '$d')

if [ "$status_code" = "200" ] && echo "$body" | grep -q "soc2_compliance_score"; then
    echo -e "${GREEN}✓${NC} (Metrics found)"
    ((TESTS_PASSED++))
else
    echo -e "${RED}✗${NC} (Metrics not found or wrong status)"
    ((TESTS_FAILED++))
fi

echo ""
echo "4. Testing Trust Principles"
echo "---------------------------"
api_call GET "/api/compliance/soc2/principles" "" "200"
api_call GET "/api/compliance/soc2/principles/security" "" "200"
api_call GET "/api/compliance/soc2/principles/availability" "" "200"
api_call GET "/api/compliance/soc2/principles/processing_integrity" "" "200"
api_call GET "/api/compliance/soc2/principles/confidentiality" "" "200"
api_call GET "/api/compliance/soc2/principles/privacy" "" "200"

echo ""
echo "5. Testing Report Generation"
echo "----------------------------"
api_call GET "/api/compliance/soc2/reports" "" "200"
api_call GET "/api/compliance/soc2/reports/daily" "" "200"
api_call GET "/api/compliance/soc2/reports/monthly" "" "200"

echo ""
echo "6. Testing Evidence Collection"
echo "------------------------------"
api_call GET "/api/compliance/soc2/evidence" "" "200"
api_call GET "/api/compliance/soc2/evidence/CC6.1" "" "200"

# Test adding evidence
evidence_data='{
  "id": "test-evidence-001",
  "control_id": "CC6.1",
  "evidence_type": "SystemGenerated",
  "title": "Test Evidence",
  "description": "Automated test evidence",
  "collected_at": "2024-01-15T10:00:00Z",
  "collector": "test-script",
  "data": {
    "Text": "Test evidence data"
  },
  "metadata": {},
  "retention_until": "2025-01-15T10:00:00Z"
}'

api_call POST "/api/compliance/soc2/evidence" "$evidence_data" "201"

echo ""
echo "7. Testing Dashboard Data Structure"
echo "-----------------------------------"
dashboard_response=$(curl -s "$API_BASE_URL/api/compliance/soc2/dashboard")

check_json_field "$dashboard_response" "compliance_score" "compliance_score field"
check_json_field "$dashboard_response" "trust_principles" "trust_principles field"
check_json_field "$dashboard_response" "alerts" "alerts field"
check_json_field "$dashboard_response" "evidence_summary" "evidence_summary field"
check_json_field "$dashboard_response" "historical_scores" "historical_scores field"

echo ""
echo "8. Testing Compliance Score Structure"
echo "-------------------------------------"
check_json_field "$dashboard_response" "compliance_score.overall" "overall score"
check_json_field "$dashboard_response" "compliance_score.security" "security score"
check_json_field "$dashboard_response" "compliance_score.availability" "availability score"
check_json_field "$dashboard_response" "compliance_score.processing_integrity" "processing integrity score"
check_json_field "$dashboard_response" "compliance_score.confidentiality" "confidentiality score"
check_json_field "$dashboard_response" "compliance_score.privacy" "privacy score"

echo ""
echo "9. Testing Report Export"
echo "------------------------"
# Test different export formats
api_call GET "/api/compliance/soc2/reports/daily-2024-01-15/export?format=json" "" "200"
api_call GET "/api/compliance/soc2/reports/daily-2024-01-15/export?format=html" "" "200"
api_call GET "/api/compliance/soc2/reports/daily-2024-01-15/export?format=pdf" "" "200"

echo ""
echo "10. Testing Evidence Package Generation"
echo "---------------------------------------"
package_request='{
  "control_ids": ["CC6.1", "CC6.6", "A1.1"],
  "start_date": "2024-01-01T00:00:00Z",
  "end_date": "2024-01-31T23:59:59Z"
}'

api_call POST "/api/compliance/soc2/evidence/package" "$package_request" "200"

echo ""
echo "11. Testing Metric Values"
echo "-------------------------"
echo -n "Checking metric values... "
metrics_response=$(curl -s "$API_BASE_URL/api/compliance/soc2/metrics")

if echo "$metrics_response" | grep -q "soc2_compliance_score.*security"; then
    echo -e "${GREEN}✓${NC} (Security metrics found)"
    ((TESTS_PASSED++))
else
    echo -e "${RED}✗${NC} (Security metrics not found)"
    ((TESTS_FAILED++))
fi

echo ""
echo "12. Testing Invalid Requests"
echo "-----------------------------"
api_call GET "/api/compliance/soc2/principles/invalid" "" "400"
api_call GET "/api/compliance/soc2/reports/invalid" "" "400"
api_call GET "/api/compliance/soc2/dashboard?time_range=invalid" "" "400"

echo ""
echo "13. Testing Report Attestation"
echo "-------------------------------"
attestation_data='{
  "attester_name": "John Doe",
  "attester_role": "Compliance Officer",
  "statement": "I attest that this report accurately reflects our SOC 2 compliance status."
}'

api_call POST "/api/compliance/soc2/reports/daily-2024-01-15/attest" "$attestation_data" "204"

echo ""
echo "14. Performance Tests"
echo "---------------------"
echo -n "Testing dashboard response time... "
start_time=$(date +%s%N)
curl -s "$API_BASE_URL/api/compliance/soc2/dashboard" > /dev/null
end_time=$(date +%s%N)
response_time=$(( (end_time - start_time) / 1000000 ))

if [ "$response_time" -lt 1000 ]; then
    echo -e "${GREEN}✓${NC} (${response_time}ms)"
    ((TESTS_PASSED++))
else
    echo -e "${YELLOW}⚠${NC} (${response_time}ms - consider optimization)"
    ((TESTS_PASSED++))
fi

echo ""
echo "15. Concurrent Request Test"
echo "---------------------------"
echo -n "Testing concurrent requests... "
for i in {1..10}; do
    curl -s "$API_BASE_URL/api/compliance/soc2/dashboard" > /dev/null &
done
wait

echo -e "${GREEN}✓${NC} (All concurrent requests completed)"
((TESTS_PASSED++))

# Generate test report
echo ""
echo "Generating Test Report"
echo "----------------------"
cat > "$TEST_RESULTS_DIR/soc2-test-report.json" <<EOF
{
  "test_suite": "SOC 2 Compliance Automation",
  "timestamp": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
  "total_tests": $((TESTS_PASSED + TESTS_FAILED)),
  "passed": $TESTS_PASSED,
  "failed": $TESTS_FAILED,
  "success_rate": $(echo "scale=2; $TESTS_PASSED * 100 / ($TESTS_PASSED + $TESTS_FAILED)" | bc)%,
  "api_base_url": "$API_BASE_URL"
}
EOF

echo "Test report saved to: $TEST_RESULTS_DIR/soc2-test-report.json"

# Summary
echo ""
echo "========================================"
echo "Test Summary"
echo "========================================"
echo -e "Total Tests: $((TESTS_PASSED + TESTS_FAILED))"
echo -e "Passed: ${GREEN}$TESTS_PASSED${NC}"
echo -e "Failed: ${RED}$TESTS_FAILED${NC}"
echo -e "Success Rate: $(echo "scale=2; $TESTS_PASSED * 100 / ($TESTS_PASSED + $TESTS_FAILED)" | bc)%"

if [ $TESTS_FAILED -eq 0 ]; then
    echo ""
    echo -e "${GREEN}✓ All tests passed! SOC 2 compliance automation is working correctly.${NC}"
    exit 0
else
    echo ""
    echo -e "${RED}✗ Some tests failed. Please check the implementation.${NC}"
    exit 1
fi