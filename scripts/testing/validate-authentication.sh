#!/bin/bash
# Validate service-to-service authentication setup
# This script checks that services are not using default credentials

set -euo pipefail

# Configuration
PROJECT_ID="${GCP_PROJECT_ID:-vibe-match-463114}"
REGION="${GCP_REGION:-us-central1}"

# Color output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
echo_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
echo_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
echo_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Services and their expected service accounts
declare -A SERVICE_ACCOUNTS=(
    ["analysis-engine"]="ccl-analysis-engine-sa"
    ["query-intelligence"]="ccl-query-intelligence-sa"
    ["pattern-mining"]="ccl-pattern-mining-sa"
    ["marketplace"]="ccl-marketplace-sa"
    ["collaboration-hub"]="ccl-collaboration-sa"
    ["web"]="ccl-web-sa"
)

# Check if service account exists
check_service_account_exists() {
    local sa_name="$1"
    local sa_email="${sa_name}@${PROJECT_ID}.iam.gserviceaccount.com"
    
    if gcloud iam service-accounts describe "$sa_email" --project="$PROJECT_ID" &> /dev/null; then
        echo_success "✓ Service account exists: $sa_name"
        return 0
    else
        echo_error "✗ Service account missing: $sa_name"
        return 1
    fi
}

# Check if Cloud Run service is using the correct service account
check_cloud_run_service_account() {
    local service_name="$1"
    local expected_sa="$2"
    local expected_sa_email="${expected_sa}@${PROJECT_ID}.iam.gserviceaccount.com"
    
    echo_info "Checking Cloud Run service: $service_name"
    
    # Get the current service account for the Cloud Run service
    local current_sa
    current_sa=$(gcloud run services describe "$service_name" \
        --region="$REGION" \
        --project="$PROJECT_ID" \
        --format="value(spec.template.spec.serviceAccountName)" 2>/dev/null || echo "")
    
    if [[ -z "$current_sa" ]]; then
        echo_warning "⚠️  Service $service_name not found or no service account assigned (using default)"
        return 1
    elif [[ "$current_sa" == "$expected_sa_email" ]]; then
        echo_success "✓ Service $service_name using correct service account: $expected_sa"
        return 0
    else
        echo_error "✗ Service $service_name using wrong service account:"
        echo "    Expected: $expected_sa_email"
        echo "    Actual:   $current_sa"
        return 1
    fi
}

# Check service health endpoints for authentication status
check_service_auth_status() {
    local service_name="$1"
    
    echo_info "Checking authentication status for $service_name..."
    
    # Get service URL
    local service_url
    service_url=$(gcloud run services describe "$service_name" \
        --region="$REGION" \
        --project="$PROJECT_ID" \
        --format="value(status.url)" 2>/dev/null || echo "")
    
    if [[ -z "$service_url" ]]; then
        echo_warning "⚠️  Could not get URL for service $service_name"
        return 1
    fi
    
    # Check if service has auth status endpoint
    local auth_endpoint="${service_url}/health/auth"
    local health_endpoint="${service_url}/health"
    
    echo_info "Testing endpoint: $health_endpoint"
    
    # Test basic health first
    local http_code
    http_code=$(curl -s -o /dev/null -w "%{http_code}" "$health_endpoint" || echo "000")
    
    if [[ "$http_code" == "200" ]]; then
        echo_success "✓ Service $service_name is healthy"
        
        # Check auth status if available
        local auth_response
        auth_response=$(curl -s "$auth_endpoint" 2>/dev/null || echo "{}")
        
        if [[ "$auth_response" != "{}" ]]; then
            # Parse the response to check for default credentials warning
            local auth_method
            auth_method=$(echo "$auth_response" | jq -r '.auth_method // "unknown"' 2>/dev/null || echo "unknown")
            
            local security_status
            security_status=$(echo "$auth_response" | jq -r '.debug_info.auth_security_status // "unknown"' 2>/dev/null || echo "unknown")
            
            echo_info "  Auth method: $auth_method"
            echo_info "  Security status: $security_status"
            
            if [[ "$auth_method" == "default_credentials" ]] || [[ "$security_status" == "using_default_credentials" ]]; then
                echo_error "✗ Service $service_name is using default credentials (SECURITY RISK)"
                return 1
            else
                echo_success "✓ Service $service_name using secure authentication"
                return 0
            fi
        else
            echo_info "  No auth status endpoint available"
            return 0
        fi
    else
        echo_error "✗ Service $service_name health check failed (HTTP $http_code)"
        return 1
    fi
}

# Check service logs for default credentials warnings
check_service_logs() {
    local service_name="$1"
    
    echo_info "Checking recent logs for $service_name..."
    
    # Check for "default credentials" warnings in recent logs
    local log_filter="resource.type=\"cloud_run_revision\" AND resource.labels.service_name=\"$service_name\" AND (\"default credentials\" OR \"Application Default Credentials\" OR \"default_credentials\")"
    
    local log_entries
    log_entries=$(gcloud logging read "$log_filter" \
        --project="$PROJECT_ID" \
        --limit=10 \
        --format="value(timestamp,jsonPayload.message,textPayload)" 2>/dev/null || echo "")
    
    if [[ -n "$log_entries" ]]; then
        echo_warning "⚠️  Found 'default credentials' mentions in $service_name logs:"
        echo "$log_entries" | head -3
        return 1
    else
        echo_success "✓ No 'default credentials' warnings found in $service_name logs"
        return 0
    fi
}

# Main validation function
validate_authentication() {
    echo_info "=== Service-to-Service Authentication Validation ==="
    echo_info "Project: $PROJECT_ID"
    echo_info "Region: $REGION"
    echo ""
    
    local total_checks=0
    local passed_checks=0
    local failed_services=()
    
    # Check each service
    for service_name in "${!SERVICE_ACCOUNTS[@]}"; do
        local sa_name="${SERVICE_ACCOUNTS[$service_name]}"
        
        echo_info "=== Validating $service_name ==="
        
        # Check 1: Service account exists
        total_checks=$((total_checks + 1))
        if check_service_account_exists "$sa_name"; then
            passed_checks=$((passed_checks + 1))
        else
            failed_services+=("$service_name: SA missing")
        fi
        
        # Check 2: Cloud Run service using correct SA
        total_checks=$((total_checks + 1))
        if check_cloud_run_service_account "$service_name" "$sa_name"; then
            passed_checks=$((passed_checks + 1))
        else
            failed_services+=("$service_name: Wrong SA assigned")
        fi
        
        # Check 3: Service auth status
        total_checks=$((total_checks + 1))
        if check_service_auth_status "$service_name"; then
            passed_checks=$((passed_checks + 1))
        else
            failed_services+=("$service_name: Auth status failed")
        fi
        
        # Check 4: Service logs
        total_checks=$((total_checks + 1))
        if check_service_logs "$service_name"; then
            passed_checks=$((passed_checks + 1))
        else
            failed_services+=("$service_name: Default creds in logs")
        fi
        
        echo ""
    done
    
    # Summary
    echo_info "=== Validation Summary ==="
    echo_info "Total checks: $total_checks"
    echo_info "Passed: $passed_checks"
    echo_info "Failed: $((total_checks - passed_checks))"
    
    if [[ ${#failed_services[@]} -gt 0 ]]; then
        echo_error "Failed services:"
        for failure in "${failed_services[@]}"; do
            echo "  - $failure"
        done
        echo ""
        echo_error "❌ VALIDATION FAILED - Services are using insecure authentication"
        echo_info "Next steps:"
        echo "1. Run: ./scripts/security/setup-service-accounts.sh"
        echo "2. Redeploy services with: ./scripts/ccl-deploy"
        echo "3. Monitor logs for default credentials warnings"
        return 1
    else
        echo_success "✅ ALL CHECKS PASSED - Service-to-service authentication is secure"
        echo_info "Services are properly configured with dedicated service accounts"
        return 0
    fi
}

# Show help
show_help() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --help, -h          Show this help message"
    echo "  --project PROJECT   GCP project ID (default: $PROJECT_ID)"
    echo "  --region REGION     GCP region (default: $REGION)" 
    echo "  --service SERVICE   Check specific service only"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Validate all services"
    echo "  $0 --service analysis-engine         # Validate specific service"
    echo "  $0 --project my-project --region us-west1"
}

# Parse command line arguments
main() {
    local specific_service=""
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --help|-h)
                show_help
                exit 0
                ;;
            --project)
                PROJECT_ID="$2"
                shift 2
                ;;
            --region)
                REGION="$2"
                shift 2
                ;;
            --service)
                specific_service="$2"
                shift 2
                ;;
            *)
                echo_error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # Validate specific service if requested
    if [[ -n "$specific_service" ]]; then
        if [[ -n "${SERVICE_ACCOUNTS[$specific_service]:-}" ]]; then
            local sa_name="${SERVICE_ACCOUNTS[$specific_service]}"
            echo_info "Validating single service: $specific_service"
            
            check_service_account_exists "$sa_name" && \
            check_cloud_run_service_account "$specific_service" "$sa_name" && \
            check_service_auth_status "$specific_service" && \
            check_service_logs "$specific_service"
        else
            echo_error "Unknown service: $specific_service"
            echo_info "Available services: ${!SERVICE_ACCOUNTS[*]}"
            exit 1
        fi
    else
        # Validate all services
        validate_authentication
    fi
}

# Run if called directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi