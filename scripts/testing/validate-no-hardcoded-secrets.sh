#!/bin/bash

# Hardcoded Secrets Validation Script
# EPIS-004: Validates that no hardcoded secrets exist in configuration files
#
# Usage: ./scripts/security/validate-no-hardcoded-secrets.sh
# Returns: 0 if no secrets found, 1 if secrets detected

set -euo pipefail

# Color codes for output
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly NC='\033[0m' # No Color

echo -e "${GREEN}🔍 Hardcoded Secrets Validation - EPIS-004${NC}"
echo "========================================"
echo

# Configuration
readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../.." && pwd)"
readonly VALIDATION_LOG="${PROJECT_ROOT}/validation-results/secrets-validation.log"

# Create validation results directory
mkdir -p "$(dirname "$VALIDATION_LOG")"

# Initialize validation log
cat > "$VALIDATION_LOG" <<EOF
Hardcoded Secrets Validation Report
Generated: $(date -u '+%Y-%m-%d %H:%M:%S UTC')
Project: Episteme
Validation: EPIS-004 - No hardcoded secrets in configuration files

EOF

echo "1. Scanning for hardcoded secrets in configuration files..."

# Function to scan for specific patterns
scan_for_pattern() {
    local pattern="$1"
    local description="$2"
    local matches_found=false
    
    echo "   Checking for: $description"
    
    if matches=$(grep -rn "$pattern" "$PROJECT_ROOT/docker/" "$PROJECT_ROOT"/*.yml "$PROJECT_ROOT/Makefile" 2>/dev/null || true); then
        if [[ -n "$matches" ]]; then
            echo -e "   ${RED}❌ Found: $description${NC}"
            echo "$matches" | while IFS= read -r line; do
                echo "      $line"
                echo "      $line" >> "$VALIDATION_LOG"
            done
            matches_found=true
        fi
    fi
    
    # Return 0 (true) when matches are found, 1 (false) when no matches
    if [[ "$matches_found" == "true" ]]; then
        return 0
    else
        return 1
    fi
}

# Scan for critical patterns
secrets_found=0

# Check for specific EPIS-004 hardcoded secrets
echo "2. Checking for specific EPIS-004 critical issues..."

# Check docker-compose.base.yml for insecure postgres default
if [[ -f "$PROJECT_ROOT/docker/docker-compose.base.yml" ]]; then
    echo "   Checking PostgreSQL configuration..."
    if grep -q "CHANGE_ME_INSECURE_DEFAULT" "$PROJECT_ROOT/docker/docker-compose.base.yml" 2>/dev/null; then
        echo -e "   ${RED}❌ Found insecure PostgreSQL default in docker-compose.base.yml${NC}"
        secrets_found=$((secrets_found + 1))
        echo "CRITICAL: Insecure PostgreSQL default found" >> "$VALIDATION_LOG"
    else
        echo -e "   ✅ PostgreSQL password properly secured"
    fi
fi

# Check docker-compose.dev.yml for dummy stripe key  
if [[ -f "$PROJECT_ROOT/docker/docker-compose.dev.yml" ]]; then
    echo "   Checking Stripe configuration..."
    if grep -q "sk_test_dummy" "$PROJECT_ROOT/docker/docker-compose.dev.yml" 2>/dev/null; then
        echo -e "   ${RED}❌ Found dummy Stripe key in docker-compose.dev.yml${NC}"
        secrets_found=$((secrets_found + 1))
        echo "CRITICAL: Dummy Stripe key found" >> "$VALIDATION_LOG"
    else
        echo -e "   ✅ Stripe key properly secured"
    fi
fi

# Check Makefile for exposed credentials
if [[ -f "$PROJECT_ROOT/Makefile" ]]; then
    echo "   Checking Makefile credentials..." 
    if grep -q "PGPASSWORD=dev_password" "$PROJECT_ROOT/Makefile" 2>/dev/null; then
        echo -e "   ${YELLOW}⚠️ Found exposed dev password in Makefile${NC}"
        secrets_found=$((secrets_found + 1))
        echo "WARNING: Dev password exposed in Makefile" >> "$VALIDATION_LOG"
    else
        echo -e "   ✅ Makefile credentials secured"
    fi
fi

# Check for general insecure patterns
echo "3. Checking for general hardcoded secret patterns..."

# Common insecure patterns
if scan_for_pattern "CHANGE_ME" "Insecure default placeholders"; then
    secrets_found=$((secrets_found + 1))
fi

if scan_for_pattern "sk_test_dummy" "Dummy test keys"; then
    secrets_found=$((secrets_found + 1))  
fi

if scan_for_pattern "dev_password" "Development passwords"; then
    secrets_found=$((secrets_found + 1))
fi

if scan_for_pattern "password.*=.*['\"][^'\"]{3,}['\"]" "Hardcoded passwords"; then
    secrets_found=$((secrets_found + 1))
fi

echo

# Generate final report
cat >> "$VALIDATION_LOG" <<EOF

VALIDATION SUMMARY
==================
Secret patterns found: $secrets_found
Validation result: $([ $secrets_found -eq 0 ] && echo "PASS" || echo "FAIL")

EOF

# Display results
echo "========================================"
echo "Hardcoded Secrets Validation Results"
echo "========================================"
echo "🔍 Secret patterns found: $secrets_found"

if [[ $secrets_found -eq 0 ]]; then
    echo -e "✅ ${GREEN}VALIDATION PASSED${NC}"
    echo "🎉 No hardcoded secrets detected!"
    echo
    echo "Security Status:"
    echo "  ✅ No insecure password defaults found"
    echo "  ✅ No dummy API keys detected" 
    echo "  ✅ No hardcoded credentials in configuration"
    echo
    echo "✅ EPIS-004: Hardcoded secrets successfully removed"
    
    exit 0
else
    echo -e "❌ ${RED}VALIDATION FAILED${NC}"
    echo "🚨 Hardcoded secrets detected in configuration files!"
    echo
    echo "Required Actions:"
    echo "  1. Remove or replace all hardcoded secrets with environment variables"
    echo "  2. Use Docker secrets for sensitive data in docker-compose.secrets.yml"
    echo "  3. Update documentation to reference secure configuration patterns"  
    echo "  4. Ensure environment variables are properly configured"
    echo
    echo "❌ EPIS-004: Hardcoded secrets still present - requires immediate attention"
    
    exit 1
fi