#!/bin/bash

# Security Drill Validation Script
# This script validates that security procedures can be executed successfully
# Based on EPIS-026 requirements for documented security procedures

set -e

echo "========================================"
echo "Security Procedures Validation Drill"
echo "========================================"
echo "Testing comprehensive security procedures implementation"
echo "Issue: EPIS-026 - Security procedures undocumented"
echo ""

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Test counter
TESTS_PASSED=0
TESTS_FAILED=0

# Function to print test results
print_result() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✓ PASS${NC}: $2"
        ((TESTS_PASSED++))
    else
        echo -e "${RED}✗ FAIL${NC}: $2"
        ((TESTS_FAILED++))
    fi
}

# Function to check if file exists and has content
check_file() {
    local file="$1"
    local description="$2"
    
    if [ -f "$file" ] && [ -s "$file" ]; then
        print_result 0 "$description exists and has content"
        return 0
    else
        print_result 1 "$description missing or empty"
        return 1
    fi
}

# Function to check if file contains required sections
check_sections() {
    local file="$1"
    local sections="$2"
    local description="$3"
    
    local missing_sections=""
    IFS=',' read -ra SECTION_ARRAY <<< "$sections"
    
    for section in "${SECTION_ARRAY[@]}"; do
        if ! grep -q "$section" "$file" 2>/dev/null; then
            missing_sections="$missing_sections $section"
        fi
    done
    
    if [ -z "$missing_sections" ]; then
        print_result 0 "$description contains all required sections"
        return 0
    else
        print_result 1 "$description missing sections:$missing_sections"
        return 1
    fi
}

echo "1. Testing Research Documentation Files"
echo "----------------------------------------"

# Check research documentation
check_file "research/security/incident-response.md" "Incident Response Research Documentation"
check_file "research/security/security-procedures.md" "Security Procedures Research Documentation"

# Check research documentation contains required content
if [ -f "research/security/incident-response.md" ]; then
    check_sections "research/security/incident-response.md" "NIST Incident Response Lifecycle,Incident Classification,Communication Procedures,Technical Response Procedures" "Incident Response Research"
fi

if [ -f "research/security/security-procedures.md" ]; then
    check_sections "research/security/security-procedures.md" "Security Operations Framework,Access Control Procedures,Escalation Paths and Contacts,Security Checklists" "Security Procedures Research"
fi

echo ""
echo "2. Testing Core Security Procedure Documentation"
echo "------------------------------------------------"

# Check core security documentation
check_file "docs/security-procedures.md" "Security Procedures Documentation"
check_file "docs/incident-response-playbooks.md" "Incident Response Playbooks"
check_file "docs/security-checklists.md" "Security Checklists"
check_file "docs/security-contacts.md" "Security Contacts and Escalation"

echo ""
echo "3. Testing Incident Response Procedures"
echo "---------------------------------------"

if [ -f "docs/incident-response-playbooks.md" ]; then
    check_sections "docs/incident-response-playbooks.md" "Data Breach Response Playbook,Ransomware Attack Response Playbook,Malware Infection Response Playbook,Unauthorized Access Response Playbook" "Incident Response Playbooks"
fi

if [ -f "docs/security-procedures.md" ]; then
    check_sections "docs/security-procedures.md" "Incident Response Procedures,P0 - Critical,P1 - High,P2 - Medium,P3 - Low" "Incident Response Severity Classification"
fi

echo ""
echo "4. Testing Security Checklists"
echo "------------------------------"

if [ -f "docs/security-checklists.md" ]; then
    check_sections "docs/security-checklists.md" "Employee Lifecycle Security Checklists,New Employee Onboarding Security Checklist,Employee Termination Security Checklist,Security Incident Response Checklist" "Security Checklists"
fi

echo ""
echo "5. Testing Escalation Paths"
echo "---------------------------"

if [ -f "docs/security-contacts.md" ]; then
    check_sections "docs/security-contacts.md" "Emergency Security Contacts,Security Team Structure and Contacts,Escalation Matrix by Incident Severity,Internal Stakeholder Contacts,External Emergency Contacts" "Security Contacts and Escalation"
fi

echo ""
echo "6. Testing Security Contacts"
echo "----------------------------"

if [ -f "docs/security-contacts.md" ]; then
    # Check for required contact types
    check_sections "docs/security-contacts.md" "SOC Team Lead,Security Manager,Chief Information Security Officer,Legal Counsel,Public Relations" "Security Contact Information"
    
    # Check for emergency contacts
    if grep -q "+1 (555)" "docs/security-contacts.md" 2>/dev/null; then
        print_result 0 "Emergency phone numbers are documented"
    else
        print_result 1 "Emergency phone numbers missing"
    fi
    
    # Check for email contacts
    if grep -q "@ccl-platform.com" "docs/security-contacts.md" 2>/dev/null; then
        print_result 0 "Email contacts are documented"
    else
        print_result 1 "Email contacts missing"
    fi
fi

echo ""
echo "7. Testing Integration with Main Security Policy"
echo "------------------------------------------------"

if [ -f "SECURITY.md" ]; then
    # Check if main SECURITY.md references the new procedures
    if grep -q "docs/security-procedures.md" "SECURITY.md" 2>/dev/null; then
        print_result 0 "Main SECURITY.md references security procedures"
    else
        print_result 1 "Main SECURITY.md missing reference to security procedures"
    fi
    
    if grep -q "<EMAIL>" "SECURITY.md" 2>/dev/null; then
        print_result 0 "Main SECURITY.md contains contact information"
    else
        print_result 1 "Main SECURITY.md missing contact information"
    fi
fi

echo ""
echo "8. Testing Procedure Completeness"
echo "---------------------------------"

# Test that procedures can be followed by checking for step-by-step instructions
if [ -f "docs/incident-response-playbooks.md" ]; then
    if grep -q "Step 1:" "docs/incident-response-playbooks.md" 2>/dev/null; then
        print_result 0 "Incident response procedures contain step-by-step instructions"
    else
        print_result 1 "Incident response procedures missing step-by-step instructions"
    fi
fi

if [ -f "docs/security-checklists.md" ]; then
    if grep -q "\- \[ \]" "docs/security-checklists.md" 2>/dev/null; then
        print_result 0 "Security checklists contain actionable checkboxes"
    else
        print_result 1 "Security checklists missing actionable checkboxes"
    fi
fi

echo ""
echo "9. Testing Drill Execution Capability"
echo "-------------------------------------"

# Check if procedures support drill execution
if [ -f "docs/security-procedures.md" ]; then
    if grep -q "drill\|exercise\|test" "docs/security-procedures.md" 2>/dev/null; then
        print_result 0 "Security procedures support drill execution"
    else
        print_result 1 "Security procedures missing drill execution guidance"
    fi
fi

if [ -f "docs/incident-response-playbooks.md" ]; then
    if grep -q "validation\|testing" "docs/incident-response-playbooks.md" 2>/dev/null; then
        print_result 0 "Incident response playbooks support validation testing"
    else
        print_result 1 "Incident response playbooks missing validation testing"
    fi
fi

echo ""
echo "10. Testing Documentation Quality"
echo "--------------------------------"

# Check documentation quality markers
for doc in "docs/security-procedures.md" "docs/incident-response-playbooks.md" "docs/security-checklists.md" "docs/security-contacts.md"; do
    if [ -f "$doc" ]; then
        if grep -q "Last Updated: 2025-01-16" "$doc" 2>/dev/null; then
            print_result 0 "$(basename $doc) has current update date"
        else
            print_result 1 "$(basename $doc) missing or outdated update date"
        fi
        
        if grep -q "Document Classification" "$doc" 2>/dev/null; then
            print_result 0 "$(basename $doc) has proper classification"
        else
            print_result 1 "$(basename $doc) missing document classification"
        fi
    fi
done

echo ""
echo "========================================"
echo "Security Drill Validation Results"
echo "========================================"
echo -e "Tests Passed: ${GREEN}$TESTS_PASSED${NC}"
echo -e "Tests Failed: ${RED}$TESTS_FAILED${NC}"
echo "Total Tests: $((TESTS_PASSED + TESTS_FAILED))"

if [ $TESTS_FAILED -eq 0 ]; then
    echo -e "\n${GREEN}✓ SUCCESS${NC}: All security procedures are documented and can be executed"
    echo "Security drill can be executed successfully using the documented procedures"
    echo ""
    echo "EPIS-026 Validation: PASSED"
    echo "- ✓ Incident response procedures documented"
    echo "- ✓ Security checklists created"
    echo "- ✓ Escalation paths defined"
    echo "- ✓ Security contacts documented"
    echo "- ✓ Procedures support drill execution"
    exit 0
else
    echo -e "\n${RED}✗ FAILURE${NC}: Some security procedures are missing or incomplete"
    echo "Please review failed tests and complete missing documentation"
    echo ""
    echo "EPIS-026 Validation: FAILED"
    exit 1
fi