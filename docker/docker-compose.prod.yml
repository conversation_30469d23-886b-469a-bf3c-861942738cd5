# Production Docker Compose Configuration
# This file demonstrates how secrets should be handled in production
# 
# ⚠️  DO NOT USE THIS FILE DIRECTLY FOR PRODUCTION DEPLOYMENT
# ⚠️  This is for reference only - production uses Cloud Run with Secret Manager
#
# For actual production deployment, use:
#   - Google Cloud Secret Manager for secret storage
#   - Cloud Run services with secret manager integration
#   - The deployment scripts in services/*/scripts/deployment/

version: '3.8'

services:
  # Production PostgreSQL with Secret Manager integration
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_DB: ${POSTGRES_DB}
      # In production, password comes from Secret Manager
      POSTGRES_PASSWORD_FILE: /run/secrets/postgres_password
    secrets:
      - postgres_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts/postgres:/docker-entrypoint-initdb.d
    networks:
      - episteme-production
    deploy:
      replicas: 1
      resources:
        limits:
          memory: 4G
          cpus: '2'
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER}"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Production Redis with authentication
  redis:
    image: redis:7-alpine
    environment:
      REDIS_PASSWORD_FILE: /run/secrets/redis_password
    secrets:
      - redis_password
    command: >
      sh -c "
        REDIS_PASSWORD=$$(cat /run/secrets/redis_password)
        redis-server --requirepass $$REDIS_PASSWORD --appendonly yes --maxmemory 2gb --maxmemory-policy allkeys-lru
      "
    volumes:
      - redis_data:/data
    networks:
      - episteme-production
    deploy:
      replicas: 1
      resources:
        limits:
          memory: 2G
          cpus: '1'
    healthcheck:
      test: ["CMD", "redis-cli", "--no-auth-warning", "-a", "$$(cat /run/secrets/redis_password)", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

# Production Secret References
# In actual production, these are managed by Google Cloud Secret Manager
secrets:
  postgres_password:
    external: true
    name: prod-postgres-password
  redis_password:
    external: true
    name: prod-redis-password
  jwt_secret:
    external: true
    name: prod-jwt-secret
  session_secret:
    external: true
    name: prod-session-secret
  gemini_api_key:
    external: true
    name: prod-gemini-api-key
  openai_api_key:
    external: true
    name: prod-openai-api-key

volumes:
  postgres_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /var/lib/episteme/postgres
  redis_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /var/lib/episteme/redis

networks:
  episteme-production:
    driver: bridge
    driver_opts:
      com.docker.network.bridge.name: episteme-prod
    ipam:
      config:
        - subnet: **********/16

# Production deployment configuration
deploy:
  # Resource constraints for production
  resources:
    limits:
      memory: 16G
      cpus: '8'
    reservations:
      memory: 8G
      cpus: '4'
  
  # Rolling update strategy
  update_config:
    parallelism: 1
    delay: 30s
    failure_action: rollback
    monitor: 60s
    max_failure_ratio: 0.1
  
  # Restart policy
  restart_policy:
    condition: on-failure
    delay: 10s
    max_attempts: 3
    window: 60s

# Production-specific configurations
x-production-config:
  logging:
    driver: "json-file"
    options:
      max-size: "100m"
      max-file: "5"
      labels: "environment=production,service={{.Name}}"
  
  security:
    - seccomp:unconfined
    - apparmor:unconfined
  
  healthcheck:
    interval: 30s
    timeout: 10s
    retries: 3
    start_period: 60s

# Environment-specific overrides
x-environment: &production-environment
  # Common production environment variables
  NODE_ENV: production
  RAILS_ENV: production
  PYTHONUNBUFFERED: 1
  LOG_LEVEL: WARN
  ENVIRONMENT: production
  
  # Security settings
  SECURE_SSL_REDIRECT: "true"
  SESSION_COOKIE_SECURE: "true"
  CSRF_COOKIE_SECURE: "true"
  
  # Performance settings
  WEB_CONCURRENCY: 4
  MAX_THREADS: 5
  
  # Monitoring
  ENABLE_METRICS: "true"
  METRICS_PORT: 9090
  HEALTH_CHECK_PORT: 8080

# Production service template
x-service-production: &service-production
  environment: *production-environment
  networks:
    - episteme-production
  logging:
    driver: "json-file"
    options:
      max-size: "100m"
      max-file: "5"
  deploy:
    restart_policy:
      condition: on-failure
      delay: 10s
      max_attempts: 3
  healthcheck:
    interval: 30s
    timeout: 10s
    retries: 3

# Production monitoring stack (referenced services)
x-monitoring-stack:
  prometheus:
    image: prom/prometheus:latest
    <<: *service-production
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
    ports:
      - "9090:9090"
  
  grafana:
    image: grafana/grafana:latest
    <<: *service-production
    environment:
      - GF_SECURITY_ADMIN_PASSWORD_FILE=/run/secrets/grafana_admin_password
    secrets:
      - grafana_admin_password
    volumes:
      - grafana_data:/var/lib/grafana
    ports:
      - "3000:3000"

# Production security configurations
x-security-config:
  # Container security
  security_opt:
    - no-new-privileges:true
  
  # Read-only root filesystem where possible
  read_only: true
  
  # Temporary filesystem for writable areas
  tmpfs:
    - /tmp:noexec,nosuid,size=100m
    - /run:noexec,nosuid,size=100m
  
  # User namespace remapping
  user: "1000:1000"
  
  # Capability dropping
  cap_drop:
    - ALL
  cap_add:
    - NET_BIND_SERVICE

# Production backup configuration
x-backup-config:
  postgres_backup:
    image: postgres:15-alpine
    environment:
      PGPASSWORD_FILE: /run/secrets/postgres_password
    secrets:
      - postgres_password
    volumes:
      - backup_data:/backup
    command: >
      sh -c "
        PGPASSWORD=$$(cat /run/secrets/postgres_password)
        pg_dump -h postgres -U ${POSTGRES_USER} ${POSTGRES_DB} | gzip > /backup/postgres_backup_$$(date +%Y%m%d_%H%M%S).sql.gz
      "
    depends_on:
      - postgres
    deploy:
      restart_policy:
        condition: none

# Additional production volumes
volumes:
  prometheus_data:
  grafana_data:
  backup_data:

# Production-specific secrets (external references)
secrets:
  grafana_admin_password:
    external: true
    name: prod-grafana-admin-password