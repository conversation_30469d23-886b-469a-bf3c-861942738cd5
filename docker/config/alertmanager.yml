# Alertmanager configuration for Episteme platform
global:
  smtp_smarthost: 'localhost:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: '<EMAIL>'
  smtp_auth_password: 'SMTP_PASSWORD_HERE'

# Templates for notifications
templates:
  - '/etc/alertmanager/templates/*.tmpl'

# Routing tree
route:
  group_by: ['alertname', 'cluster', 'service']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 12h
  receiver: 'web.hook.default'
  routes:
    # Critical alerts go to on-call immediately
    - match:
        severity: critical
      receiver: 'critical-alerts'
      group_wait: 0s
      repeat_interval: 5m

    # High severity alerts
    - match:
        severity: high
      receiver: 'high-priority-alerts'
      group_wait: 30s
      repeat_interval: 30m

    # Security alerts
    - match:
        category: security
      receiver: 'security-team'
      group_wait: 0s
      repeat_interval: 10m

    # Performance alerts
    - match:
        category: performance
      receiver: 'engineering-team'
      group_wait: 1m
      repeat_interval: 1h

    # Service-specific routing
    - match:
        service: analysis-engine
      receiver: 'backend-team'

    - match:
        service: collaboration
      receiver: 'frontend-team'

    - match:
        service: marketplace
      receiver: 'product-team'

# Inhibit rules to reduce noise
inhibit_rules:
  # Inhibit lower severity alerts when critical is firing
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'service', 'instance']

  # Inhibit instance down alerts when entire service is down
  - source_match:
      alertname: 'ServiceDown'
    target_match:
      alertname: 'InstanceDown'
    equal: ['service']

# Receivers define how to send notifications
receivers:
  # Default webhook (for development)
  - name: 'web.hook.default'
    webhook_configs:
      - url: 'http://localhost:8080/webhook'
        send_resolved: true

  # Critical alerts - immediate notification
  - name: 'critical-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '=� CRITICAL: {{ .GroupLabels.alertname }} on {{ .GroupLabels.service }}'
        body: |
          {{ range .Alerts -}}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Service: {{ .Labels.service }}
          Instance: {{ .Labels.instance }}
          Severity: {{ .Labels.severity }}
          Started: {{ .StartsAt }}
          {{ end }}
    slack_configs:
      - api_url: 'SLACK_WEBHOOK_URL_HERE'
        channel: '#alerts-critical'
        title: '=� Critical Alert: {{ .GroupLabels.alertname }}'
        text: |
          {{ range .Alerts -}}
          *Service:* {{ .Labels.service }}
          *Alert:* {{ .Annotations.summary }}
          *Description:* {{ .Annotations.description }}
          *Severity:* {{ .Labels.severity }}
          {{ end }}
    pagerduty_configs:
      - service_key: 'PAGERDUTY_INTEGRATION_KEY_HERE'
        description: '🚨 CRITICAL: {{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
        details: |
          {{ range .Alerts -}}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Service: {{ .Labels.service }}
          Instance: {{ .Labels.instance }}
          Severity: {{ .Labels.severity }}
          Started: {{ .StartsAt }}
          Runbook: https://docs.episteme.io/runbooks/{{ .Labels.service }}
          Dashboard: https://grafana.episteme.io/d/episteme-overview
          {{ end }}
        client: 'Episteme Alertmanager'
        client_url: 'https://alertmanager.episteme.io'
        links:
          - href: 'https://grafana.episteme.io/d/episteme-overview'
            text: 'Service Overview Dashboard'
          - href: 'https://grafana.episteme.io/d/episteme-security'
            text: 'Security Dashboard'

  # High priority alerts
  - name: 'high-priority-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '� HIGH: {{ .GroupLabels.alertname }} on {{ .GroupLabels.service }}'
        body: |
          {{ range .Alerts -}}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Service: {{ .Labels.service }}
          Instance: {{ .Labels.instance }}
          Severity: {{ .Labels.severity }}
          Started: {{ .StartsAt }}
          {{ end }}
    slack_configs:
      - api_url: 'SLACK_WEBHOOK_URL_HERE'
        channel: '#alerts-high'
        title: '� High Priority Alert: {{ .GroupLabels.alertname }}'

  # Security team notifications
  - name: 'security-team'
    email_configs:
      - to: '<EMAIL>'
        subject: '= SECURITY: {{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts -}}
          Security Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Service: {{ .Labels.service }}
          Instance: {{ .Labels.instance }}
          Severity: {{ .Labels.severity }}
          Started: {{ .StartsAt }}
          
          This may indicate a security incident. Please investigate immediately.
          {{ end }}
    slack_configs:
      - api_url: 'SLACK_WEBHOOK_URL_HERE'
        channel: '#security-alerts'
        title: '= Security Alert: {{ .GroupLabels.alertname }}'

  # Engineering team for performance issues
  - name: 'engineering-team'
    email_configs:
      - to: '<EMAIL>'
        subject: '=� PERFORMANCE: {{ .GroupLabels.alertname }}'
    slack_configs:
      - api_url: 'SLACK_WEBHOOK_URL_HERE'
        channel: '#engineering'
        title: '=� Performance Alert: {{ .GroupLabels.alertname }}'

  # Backend team
  - name: 'backend-team'
    slack_configs:
      - api_url: 'SLACK_WEBHOOK_URL_HERE'
        channel: '#backend-alerts'
        title: '=' Backend Alert: {{ .GroupLabels.alertname }}'

  # Frontend team
  - name: 'frontend-team'
    slack_configs:
      - api_url: 'SLACK_WEBHOOK_URL_HERE'
        channel: '#frontend-alerts'
        title: '<� Frontend Alert: {{ .GroupLabels.alertname }}'

  # Product team
  - name: 'product-team'
    slack_configs:
      - api_url: 'SLACK_WEBHOOK_URL_HERE'
        channel: '#product-alerts'
        title: '=� Product Alert: {{ .GroupLabels.alertname }}'