# Docker Compose file with secure secret management
# This file replaces hardcoded secrets with external secret references
# 
# Usage:
#   docker-compose -f docker-compose.base.yml -f docker-compose.secrets.yml up
#
# Prerequisites:
#   1. Run: ./scripts/security/setup-dev-secrets.sh
#   2. Ensure secrets/ directory exists with required secret files

version: '3.8'

services:
  # Core Infrastructure with Secret References
  postgres:
    environment:
      # Remove hardcoded password, use secret reference
      POSTGRES_USER: ccl_dev
      POSTGRES_DB: ccl_local
      # Use Docker secret for password
      POSTGRES_PASSWORD_FILE: /run/secrets/postgres_password
    secrets:
      - postgres_password
    # Override hardcoded values from base configuration
    command: >
      sh -c "
        export POSTGRES_PASSWORD=$$(cat /run/secrets/postgres_password)
        docker-entrypoint.sh postgres
      "

  redis:
    environment:
      # Redis with authentication enabled
      REDIS_PASSWORD_FILE: /run/secrets/redis_password
    secrets:
      - redis_password
    command: >
      sh -c "
        REDIS_PASSWORD=$$(cat /run/secrets/redis_password)
        redis-server --requirepass $$REDIS_PASSWORD --appendonly yes
      "

  # Essential CCL Service with Secure Configuration
  pattern-mining:
    environment:
      PYTHONUNBUFFERED: 1
      # Use secret-based database URL construction
      DATABASE_HOST: postgres
      DATABASE_PORT: 5432
      DATABASE_NAME: ccl_local
      DATABASE_USER: ccl_dev
      DATABASE_PASSWORD_FILE: /run/secrets/postgres_password
      
      # Redis connection with authentication
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD_FILE: /run/secrets/redis_password
      
      # Application secrets
      JWT_SECRET_FILE: /run/secrets/jwt_secret
      SESSION_SECRET_FILE: /run/secrets/session_secret
      
      # GCP configuration (using service account key file)
      BIGQUERY_PROJECT: ccl-local
      BIGQUERY_DATASET: patterns
      GOOGLE_APPLICATION_CREDENTIALS: /run/secrets/gcp_service_account_key
      
      # Other configuration
      OTEL_EXPORTER_OTLP_ENDPOINT: http://otel-collector:4317
      SPANNER_EMULATOR_HOST: spanner-emulator:9010
      STORAGE_EMULATOR_HOST: http://storage-emulator:4443
    secrets:
      - postgres_password
      - redis_password
      - jwt_secret
      - session_secret
      - gcp_service_account_key
    # Initialize with secret-based database URL
    command: >
      sh -c "
        export DATABASE_PASSWORD=$$(cat /run/secrets/postgres_password)
        export REDIS_PASSWORD=$$(cat /run/secrets/redis_password)
        export JWT_SECRET=$$(cat /run/secrets/jwt_secret)
        export DATABASE_URL=******************************************************/ccl_local
        export REDIS_URL=redis://:$$REDIS_PASSWORD@redis:6379
        python -m uvicorn main:app --reload --host 0.0.0.0 --port 8003
      "

# Docker Secrets Configuration
secrets:
  postgres_password:
    file: ./secrets/dev-postgres-password.txt
  redis_password:
    file: ./secrets/dev-redis-password.txt
  jwt_secret:
    file: ./secrets/dev-jwt-secret.txt
  session_secret:
    file: ./secrets/dev-session-secret.txt
  gcp_service_account_key:
    file: ./secrets/dev-gcp-service-account-key.json

# Additional services that may be added in development
# All following the same pattern of external secret references