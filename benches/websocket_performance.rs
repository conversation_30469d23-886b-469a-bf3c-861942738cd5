use criterion::{black_box, criterion_group, criterion_main, Criterion, Throughput};
use collaboration_engine::{
    models::{WebSocketMessage, UserId, UserInfo},
    websocket::performance::{FastSerializer, MessageCompressor},
};
use std::time::Duration;

fn benchmark_serialization(c: &mut Criterion) {
    let mut group = c.benchmark_group("websocket_serialization");
    
    // Small message (cursor movement)
    let small_msg = WebSocketMessage::CursorMove {
        session_id: "test-session".to_string(),
        user_id: UserId::from_string("test-user".to_string()),
        position: collaboration_engine::models::CursorPosition {
            x: 100.0,
            y: 200.0,
            element_id: Some("editor".to_string()),
        },
    };
    
    group.throughput(Throughput::Elements(1));
    group.bench_function("small_message_json", |b| {
        b.iter(|| {
            let json = serde_json::to_vec(black_box(&small_msg)).unwrap();
            black_box(json);
        })
    });
    
    // Large message (with content)
    let large_msg = WebSocketMessage::MessageSent {
        message: collaboration_engine::models::Message {
            id: "msg-123".to_string(),
            session_id: "session-123".to_string(),
            user_id: UserId::from_string("user-123".to_string()),
            content: collaboration_engine::models::MessageContent::Text {
                text: "x".repeat(1000), // 1KB of text
                mentions: vec![],
            },
            created_at: collaboration_engine::models::Timestamp::now(),
            updated_at: None,
            edited: false,
            deleted: false,
            metadata: Default::default(),
        },
    };
    
    group.bench_function("large_message_json", |b| {
        b.iter(|| {
            let json = serde_json::to_vec(black_box(&large_msg)).unwrap();
            black_box(json);
        })
    });
    
    group.finish();
}

fn benchmark_compression(c: &mut Criterion) {
    let mut group = c.benchmark_group("websocket_compression");
    let compressor = MessageCompressor::new(512); // 512 byte threshold
    
    // Test compression of different message sizes
    let sizes = vec![100, 500, 1000, 5000, 10000];
    
    for size in sizes {
        let data = "x".repeat(size).into_bytes();
        
        group.throughput(Throughput::Bytes(size as u64));
        group.bench_function(format!("compress_{}b", size), |b| {
            b.iter(|| {
                let compressed = compressor.compress(black_box(&data)).unwrap();
                black_box(compressed);
            })
        });
    }
    
    group.finish();
}

fn benchmark_roundtrip_latency(c: &mut Criterion) {
    let mut group = c.benchmark_group("websocket_roundtrip");
    group.measurement_time(Duration::from_secs(10));
    
    // Simulate end-to-end message processing
    let msg = WebSocketMessage::Ping;
    
    group.bench_function("message_roundtrip", |b| {
        b.iter(|| {
            // Serialize
            let json = serde_json::to_vec(black_box(&msg)).unwrap();
            
            // Simulate network transfer (just copy)
            let received = json.clone();
            
            // Deserialize
            let _parsed: WebSocketMessage = serde_json::from_slice(&received).unwrap();
        })
    });
    
    group.finish();
}

criterion_group!(
    benches,
    benchmark_serialization,
    benchmark_compression,
    benchmark_roundtrip_latency
);
criterion_main!(benches);