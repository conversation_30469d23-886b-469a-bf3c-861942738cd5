# Google Cloud Deploy configuration for progressive deployment
apiVersion: deploy.cloud.google.com/v1
kind: DeliveryPipeline
metadata:
  name: episteme-delivery-pipeline
  labels:
    app: episteme
    team: platform-engineering
description: |
  Progressive deployment pipeline for Episteme microservices platform.
  Provides automated canary deployments, rollback capabilities, and approval gates.
serialPipeline:
  stages:
  # Development stage - automatic deployment
  - targetId: development
    profiles: [development]
    strategy:
      standard:
        verify: true
        predeploy:
          actions:
          - name: pre-deployment-tests
            description: Run smoke tests before deployment
        postdeploy:
          actions:
          - name: post-deployment-validation
            description: Validate deployment health

  # Staging stage - automatic with approval gate
  - targetId: staging  
    profiles: [staging]
    strategy:
      canary:
        runtimeConfig:
          cloudRun:
            automaticTrafficControl: true
        canaryDeployment:
          percentages: [25, 50, 100]
          verify: true
          postdeploy:
            actions:
            - name: load-testing
              description: Execute performance and load tests
        customCanaryDeployment:
          phaseConfigs:
          - phaseId: "25-percent"
            percentage: 25
            verify: true
            postdeploy:
              actions:
              - name: canary-analysis-25
                description: Analyze metrics at 25% traffic
          - phaseId: "50-percent" 
            percentage: 50
            verify: true
            postdeploy:
              actions:
              - name: canary-analysis-50
                description: Analyze metrics at 50% traffic
          - phaseId: "100-percent"
            percentage: 100
            verify: true
    deployParameters:
    - name: "approval-required"
      description: "Manual approval required for staging"
      required: true

  # Production stage - manual approval required
  - targetId: production
    profiles: [production]
    requireApproval: true
    strategy:
      canary:
        runtimeConfig:
          cloudRun:
            automaticTrafficControl: true
        canaryDeployment:
          percentages: [10, 25, 50, 100]
          verify: true
          postdeploy:
            actions:
            - name: production-monitoring
              description: Enhanced monitoring and alerting
        customCanaryDeployment:
          phaseConfigs:
          - phaseId: "10-percent"
            percentage: 10
            verify: true
            postdeploy:
              actions:
              - name: production-canary-10
                description: Monitor production metrics at 10%
          - phaseId: "25-percent"
            percentage: 25  
            verify: true
            postdeploy:
              actions:
              - name: production-canary-25
                description: Monitor production metrics at 25%
          - phaseId: "50-percent"
            percentage: 50
            verify: true
            postdeploy:
              actions:
              - name: production-canary-50
                description: Monitor production metrics at 50%
          - phaseId: "100-percent"
            percentage: 100
            verify: true
    deployParameters:
    - name: "production-approval"
      description: "Production deployment requires manual approval"
      required: true

---
# Development target
apiVersion: deploy.cloud.google.com/v1
kind: Target
metadata:
  name: development
  labels:
    env: development
description: Development environment for testing and integration
run:
  location: projects/vibe-match-463114/locations/us-central1
deployParameters:
  memory: "2Gi"
  cpu: "2"
  maxInstances: "10"
  minInstances: "0"
  environmentVariables:
    ENVIRONMENT: "development"
    LOG_LEVEL: "debug"

---
# Staging target  
apiVersion: deploy.cloud.google.com/v1
kind: Target
metadata:
  name: staging
  labels:
    env: staging
description: Staging environment for pre-production testing
run:
  location: projects/vibe-match-463114/locations/us-central1
deployParameters:
  memory: "4Gi"
  cpu: "2" 
  maxInstances: "50"
  minInstances: "1"
  environmentVariables:
    ENVIRONMENT: "staging"
    LOG_LEVEL: "info"

---
# Production target
apiVersion: deploy.cloud.google.com/v1
kind: Target  
metadata:
  name: production
  labels:
    env: production
description: Production environment with high availability
run:
  location: projects/vibe-match-463114/locations/us-central1
requireApproval: true
deployParameters:
  memory: "8Gi"
  cpu: "4"
  maxInstances: "1000" 
  minInstances: "3"
  environmentVariables:
    ENVIRONMENT: "production"
    LOG_LEVEL: "warn"

---
# Automation rules for Cloud Deploy
apiVersion: deploy.cloud.google.com/v1
kind: Automation
metadata:
  name: episteme-promote-automation
description: Automatic promotion rules for successful deployments
serviceAccount: <EMAIL>
selector:
- target:
    id: development
rules:
- promoteRelease:
    name: "auto-promote-to-staging"
    targetId: "staging" 
    wait: 300s  # Wait 5 minutes after successful dev deployment
    condition:
      targets:
      - id: "development"
        advanceCondition:
          succeeded: {}

---
# Custom verification configurations
apiVersion: deploy.cloud.google.com/v1
kind: CustomTargetType
metadata:
  name: episteme-verification
description: Custom verification steps for Episteme deployments
customActions:
  render:
    image: gcr.io/vibe-match-463114/deployment-verifier:latest
  deploy:
    image: gcr.io/vibe-match-463114/deployment-verifier:latest