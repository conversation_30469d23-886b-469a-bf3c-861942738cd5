# Cloud Run service configuration for marketplace service
# Following Context Engineering principles for production deployment
apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: marketplace-service
  namespace: default
  labels:
    app: marketplace
    environment: production
    version: v1.0.0
  annotations:
    run.googleapis.com/ingress: all
    run.googleapis.com/execution-environment: gen2
    run.googleapis.com/cpu-throttling: "false"
spec:
  template:
    metadata:
      labels:
        app: marketplace
        environment: production
      annotations:
        # Scaling configuration
        autoscaling.knative.dev/minScale: "2"
        autoscaling.knative.dev/maxScale: "100"
        autoscaling.knative.dev/target: "80"
        
        # Resource allocation
        run.googleapis.com/cpu: "2"
        run.googleapis.com/memory: "4Gi"
        run.googleapis.com/execution-environment: gen2
        
        # Network configuration
        run.googleapis.com/vpc-access-connector: projects/vibe-match-463114/locations/us-central1/connectors/marketplace-connector
        run.googleapis.com/vpc-access-egress: all-traffic
        
        # Security configuration
        run.googleapis.com/service-account: <EMAIL>
    spec:
      serviceAccountName: <EMAIL>
      containerConcurrency: 1000
      timeoutSeconds: 300
      containers:
      - name: marketplace
        image: gcr.io/vibe-match-463114/marketplace:latest
        ports:
        - name: http1
          containerPort: 8004
          protocol: TCP
        env:
        # Application configuration
        - name: PORT
          value: "8004"
        - name: ENVIRONMENT
          value: "production"
        - name: LOG_LEVEL
          value: "info"
        - name: APP_VERSION
          value: "v1.0.0"
        
        # Database configuration
        - name: SPANNER_PROJECT_ID
          value: "vibe-match-463114"
        - name: SPANNER_INSTANCE_ID
          value: "marketplace-instance"
        - name: SPANNER_DATABASE_ID
          value: "marketplace-db"
        
        # Redis configuration
        - name: REDIS_HOST
          valueFrom:
            secretKeyRef:
              name: marketplace-secrets
              key: redis-host
        - name: REDIS_PORT
          value: "6379"
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: marketplace-secrets
              key: redis-password
        
        # Stripe configuration
        - name: STRIPE_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: marketplace-secrets
              key: stripe-secret-key
        - name: STRIPE_WEBHOOK_SECRET
          valueFrom:
            secretKeyRef:
              name: marketplace-secrets
              key: stripe-webhook-secret
        
        # JWT configuration
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: marketplace-secrets
              key: jwt-secret
        
        # Google Cloud Storage
        - name: GCS_BUCKET_NAME
          value: "marketplace-patterns-vibe-match-463114"
        
        # Monitoring configuration
        - name: PROMETHEUS_ENABLED
          value: "true"
        - name: METRICS_PORT
          value: "9090"
        
        # Performance configuration
        - name: MAX_REQUEST_SIZE
          value: "10485760"  # 10MB
        - name: READ_TIMEOUT
          value: "30s"
        - name: WRITE_TIMEOUT
          value: "30s"
        - name: IDLE_TIMEOUT
          value: "120s"
        
        resources:
          requests:
            cpu: "1000m"
            memory: "2Gi"
          limits:
            cpu: "2000m"
            memory: "4Gi"
        
        # Health checks
        livenessProbe:
          httpGet:
            path: /health
            port: 8004
            httpHeaders:
            - name: User-Agent
              value: "Cloud-Run-Health-Check"
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
          successThreshold: 1
        
        readinessProbe:
          httpGet:
            path: /health
            port: 8004
            httpHeaders:
            - name: User-Agent
              value: "Cloud-Run-Health-Check"
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
          successThreshold: 1
        
        # Startup probe for slower initialization
        startupProbe:
          httpGet:
            path: /health
            port: 8004
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 30
          successThreshold: 1
  
  traffic:
  - percent: 100
    latestRevision: true
