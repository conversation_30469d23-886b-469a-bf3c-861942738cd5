// Package main provides the entry point for the marketplace service
// Following Context Engineering principles with research-first approach
package main

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"go.uber.org/zap"

	"github.com/episteme/marketplace/internal/api"
	"github.com/episteme/marketplace/internal/audit"
	"github.com/episteme/marketplace/internal/clients"
	"github.com/episteme/marketplace/internal/config"
	"github.com/episteme/marketplace/internal/monitoring"
	"github.com/episteme/marketplace/internal/services"
	"github.com/episteme/marketplace/pkg/logger"
	"github.com/episteme/marketplace/pkg/metrics"
)

func main() {
	// Initialize logger following Context Engineering standards
	logger := logger.NewLogger()
	defer logger.Sync()

	// Load configuration with environment validation
	cfg, err := config.Load()
	if err != nil {
		logger.Fatal("Failed to load configuration", zap.Error(err))
	}

	// Initialize metrics
	metrics.Init()

	// Initialize database clients
	spannerClient, err := clients.NewSpannerClient(cfg.Database.Spanner)
	if err != nil {
		logger.Fatal("Failed to initialize Spanner client", zap.Error(err))
	}
	defer spannerClient.Close()

	redisClient, err := clients.NewRedisClient(cfg.Cache.Redis)
	if err != nil {
		logger.Fatal("Failed to initialize Redis client", zap.Error(err))
	}
	defer redisClient.Close()

	// Initialize external service clients
	stripeClient := clients.NewStripeClient(cfg.Payment.Stripe)
	storageClient, err := clients.NewStorageClient(cfg.Storage.GCS)
	if err != nil {
		logger.Fatal("Failed to initialize Storage client", zap.Error(err))
	}
	defer storageClient.Close()

	// Initialize services following dependency injection pattern
	patternService := services.NewPatternService(spannerClient, storageClient, redisClient)
	paymentService := services.NewPaymentService(stripeClient, spannerClient)
	userService := services.NewUserService(spannerClient, redisClient)
	notificationService := services.NewNotificationService(redisClient)
	communityService := services.NewCommunityService(spannerClient, redisClient)
	searchService := services.NewSearchService(spannerClient, redisClient)

	// Initialize audit logger
	auditLogger := audit.NewLogger(spannerClient, logger)

	// Initialize monitoring and analytics
	metricsCollector := monitoring.NewMetricsCollector(logger)
	dashboardService := monitoring.NewDashboardService(metricsCollector, spannerClient, redisClient, logger)

	// Initialize API router with middleware
	router := api.NewRouter(&api.Services{
		Pattern:      patternService,
		Payment:      paymentService,
		User:         userService,
		Notification: notificationService,
		Community:    communityService,
		Search:       searchService,
	}, &api.Dependencies{
		Config:           cfg,
		SpannerClient:    spannerClient,
		RedisClient:      redisClient,
		AuditLogger:      auditLogger,
		MetricsCollector: metricsCollector,
		DashboardService: dashboardService,
	}, logger)

	// Setup metrics endpoint
	router.GET("/metrics", gin.WrapH(promhttp.Handler()))

	// Setup health check endpoint
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":    "healthy",
			"timestamp": time.Now().UTC(),
			"version":   cfg.App.Version,
		})
	})

	// Create HTTP server with production settings
	server := &http.Server{
		Addr:         fmt.Sprintf(":%d", cfg.Server.Port),
		Handler:      router,
		ReadTimeout:  cfg.Server.ReadTimeout,
		WriteTimeout: cfg.Server.WriteTimeout,
		IdleTimeout:  cfg.Server.IdleTimeout,
	}

	// Start server in goroutine
	go func() {
		logger.Info("Starting marketplace service",
			zap.String("address", server.Addr),
			zap.String("environment", cfg.App.Environment),
		)

		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Fatal("Failed to start server", zap.Error(err))
		}
	}()

	// Wait for interrupt signal for graceful shutdown
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("Shutting down marketplace service...")

	// Graceful shutdown with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		logger.Fatal("Server forced to shutdown", zap.Error(err))
	}

	logger.Info("Marketplace service stopped")
}
