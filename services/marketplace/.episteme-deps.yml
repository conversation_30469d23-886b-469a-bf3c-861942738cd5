# Marketplace Service Dependencies Configuration  
# This file defines the critical dependencies for the marketplace service

service:
  name: marketplace
  language: go
  framework: gin
  port: 8005
  criticality: medium

# Critical external dependencies
dependencies:
  databases:
    - name: postgresql
      type: database
      criticality: critical
      protocol: postgres
      port: 5432
      description: "Primary database for marketplace data"
      health_check: "SELECT 1"
      timeout_ms: 3000
      retry_policy: "exponential_backoff"
      
  caches:
    - name: redis
      type: cache
      criticality: high
      protocol: redis
      port: 6379
      description: "Caching layer for product data and sessions"
      health_check: "PING"
      timeout_ms: 1000
      retry_policy: "linear_backoff"

  internal_services:
    - name: analysis-engine
      type: http_api
      criticality: high
      protocol: http
      port: 8001
      description: "Analysis engine for code pattern validation"
      health_check: "/health"
      timeout_ms: 5000
      retry_policy: "circuit_breaker"
      
  monitoring:
    - name: prometheus
      type: monitoring
      criticality: medium
      protocol: http
      port: 9090
      description: "Metrics collection and monitoring"
      health_check: "/api/v1/status" 
      timeout_ms: 2000
      retry_policy: "none"

# Service APIs exposed
exposed_apis:
  - "/health"
  - "/metrics"
  - "/products/search"
  - "/products/recommend"
  - "/marketplace/publish"
  - "/marketplace/browse"

# Health check configuration
health_check:
  endpoint: "/health"
  interval_seconds: 30
  timeout_ms: 3000
  
# Metrics configuration
metrics:
  endpoint: "/metrics"
  port: 8005