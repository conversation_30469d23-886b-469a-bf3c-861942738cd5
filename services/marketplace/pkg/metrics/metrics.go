// Package metrics provides Prometheus metrics for the marketplace service
// Following Context Engineering standards with comprehensive monitoring
package metrics

import (
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
)

var (
	// HTTP metrics
	HTTPRequestsTotal = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "marketplace_http_requests_total",
			Help: "Total number of HTTP requests",
		},
		[]string{"method", "endpoint", "status_code"},
	)

	HTTPRequestDuration = promauto.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "marketplace_http_request_duration_seconds",
			Help:    "HTTP request duration in seconds",
			Buckets: prometheus.DefBuckets,
		},
		[]string{"method", "endpoint"},
	)

	// Pattern metrics
	PatternsTotal = promauto.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "marketplace_patterns_total",
			Help: "Total number of patterns",
		},
		[]string{"status", "category", "language"},
	)

	PatternDownloads = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "marketplace_pattern_downloads_total",
			Help: "Total number of pattern downloads",
		},
		[]string{"pattern_id", "category", "language"},
	)

	PatternValidationDuration = promauto.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "marketplace_pattern_validation_duration_seconds",
			Help:    "Pattern validation duration in seconds",
			Buckets: []float64{0.1, 0.5, 1.0, 2.0, 5.0, 10.0, 30.0, 60.0},
		},
		[]string{"validation_type"},
	)

	PatternValidationScore = promauto.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "marketplace_pattern_validation_score",
			Help:    "Pattern validation scores",
			Buckets: []float64{0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0},
		},
		[]string{"category", "language"},
	)

	// Transaction metrics
	TransactionsTotal = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "marketplace_transactions_total",
			Help: "Total number of transactions",
		},
		[]string{"status", "currency"},
	)

	TransactionAmount = promauto.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "marketplace_transaction_amount_cents",
			Help:    "Transaction amounts in cents",
			Buckets: []float64{100, 500, 1000, 2500, 5000, 10000, 25000, 50000, 100000},
		},
		[]string{"currency"},
	)

	PlatformRevenue = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "marketplace_platform_revenue_cents",
			Help: "Platform revenue in cents",
		},
		[]string{"currency"},
	)

	PaymentProcessingDuration = promauto.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "marketplace_payment_processing_duration_seconds",
			Help:    "Payment processing duration in seconds",
			Buckets: []float64{0.1, 0.5, 1.0, 2.0, 5.0, 10.0, 30.0},
		},
		[]string{"payment_method"},
	)

	// User metrics
	UsersTotal = promauto.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "marketplace_users_total",
			Help: "Total number of users",
		},
		[]string{"role", "status"},
	)

	UserRegistrations = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "marketplace_user_registrations_total",
			Help: "Total number of user registrations",
		},
		[]string{"source"},
	)

	ActiveSellers = promauto.NewGauge(
		prometheus.GaugeOpts{
			Name: "marketplace_active_sellers_total",
			Help: "Total number of active sellers",
		},
	)

	// Database metrics
	DatabaseConnections = promauto.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "marketplace_database_connections",
			Help: "Number of database connections",
		},
		[]string{"database", "status"},
	)

	DatabaseQueryDuration = promauto.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "marketplace_database_query_duration_seconds",
			Help:    "Database query duration in seconds",
			Buckets: []float64{0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0, 5.0},
		},
		[]string{"database", "operation"},
	)

	DatabaseErrors = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "marketplace_database_errors_total",
			Help: "Total number of database errors",
		},
		[]string{"database", "error_type"},
	)

	// Cache metrics
	CacheHits = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "marketplace_cache_hits_total",
			Help: "Total number of cache hits",
		},
		[]string{"cache_type"},
	)

	CacheMisses = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "marketplace_cache_misses_total",
			Help: "Total number of cache misses",
		},
		[]string{"cache_type"},
	)

	CacheOperationDuration = promauto.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "marketplace_cache_operation_duration_seconds",
			Help:    "Cache operation duration in seconds",
			Buckets: []float64{0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0},
		},
		[]string{"cache_type", "operation"},
	)

	// External service metrics
	ExternalServiceRequests = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "marketplace_external_service_requests_total",
			Help: "Total number of external service requests",
		},
		[]string{"service", "status_code"},
	)

	ExternalServiceDuration = promauto.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "marketplace_external_service_duration_seconds",
			Help:    "External service request duration in seconds",
			Buckets: []float64{0.1, 0.5, 1.0, 2.0, 5.0, 10.0, 30.0},
		},
		[]string{"service"},
	)

	// Business metrics
	RevenuePerDay = promauto.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "marketplace_revenue_per_day_cents",
			Help: "Revenue per day in cents",
		},
		[]string{"currency"},
	)

	AveragePatternPrice = promauto.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "marketplace_average_pattern_price_cents",
			Help: "Average pattern price in cents",
		},
		[]string{"category", "currency"},
	)

	PatternConversionRate = promauto.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "marketplace_pattern_conversion_rate",
			Help: "Pattern view to purchase conversion rate",
		},
		[]string{"category"},
	)

	// System metrics
	GoroutinesActive = promauto.NewGauge(
		prometheus.GaugeOpts{
			Name: "marketplace_goroutines_active",
			Help: "Number of active goroutines",
		},
	)

	MemoryUsage = promauto.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "marketplace_memory_usage_bytes",
			Help: "Memory usage in bytes",
		},
		[]string{"type"},
	)
)

// Init initializes the metrics system
func Init() {
	// Register custom collectors if needed
	// This function can be extended to set up custom metrics collection
}

// Helper functions for common metric operations

// RecordHTTPRequest records an HTTP request metric
func RecordHTTPRequest(method, endpoint, statusCode string, duration float64) {
	HTTPRequestsTotal.WithLabelValues(method, endpoint, statusCode).Inc()
	HTTPRequestDuration.WithLabelValues(method, endpoint).Observe(duration)
}

// RecordPatternDownload records a pattern download
func RecordPatternDownload(patternID, category, language string) {
	PatternDownloads.WithLabelValues(patternID, category, language).Inc()
}

// RecordTransaction records a transaction
func RecordTransaction(status, currency string, amountCents float64) {
	TransactionsTotal.WithLabelValues(status, currency).Inc()
	TransactionAmount.WithLabelValues(currency).Observe(amountCents)
	
	if status == "completed" {
		// Assuming 20% platform fee
		platformFee := amountCents * 0.20
		PlatformRevenue.WithLabelValues(currency).Add(platformFee)
	}
}

// RecordPaymentProcessing records payment processing metrics
func RecordPaymentProcessing(paymentMethod string, duration float64) {
	PaymentProcessingDuration.WithLabelValues(paymentMethod).Observe(duration)
}

// RecordDatabaseQuery records database query metrics
func RecordDatabaseQuery(database, operation string, duration float64, err error) {
	DatabaseQueryDuration.WithLabelValues(database, operation).Observe(duration)
	
	if err != nil {
		DatabaseErrors.WithLabelValues(database, "query_error").Inc()
	}
}

// RecordCacheOperation records cache operation metrics
func RecordCacheOperation(cacheType, operation string, duration float64, hit bool) {
	CacheOperationDuration.WithLabelValues(cacheType, operation).Observe(duration)
	
	if operation == "get" {
		if hit {
			CacheHits.WithLabelValues(cacheType).Inc()
		} else {
			CacheMisses.WithLabelValues(cacheType).Inc()
		}
	}
}

// RecordExternalServiceCall records external service call metrics
func RecordExternalServiceCall(service, statusCode string, duration float64) {
	ExternalServiceRequests.WithLabelValues(service, statusCode).Inc()
	ExternalServiceDuration.WithLabelValues(service).Observe(duration)
}

// RecordPatternValidation records pattern validation metrics
func RecordPatternValidation(validationType string, duration float64, score float64, category, language string) {
	PatternValidationDuration.WithLabelValues(validationType).Observe(duration)
	PatternValidationScore.WithLabelValues(category, language).Observe(score)
}

// UpdateUserCounts updates user count metrics
func UpdateUserCounts(totalUsers, activeUsers, activeSellers int) {
	UsersTotal.WithLabelValues("user", "active").Set(float64(activeUsers))
	UsersTotal.WithLabelValues("user", "total").Set(float64(totalUsers))
	ActiveSellers.Set(float64(activeSellers))
}

// UpdatePatternCounts updates pattern count metrics
func UpdatePatternCounts(status, category, language string, count int) {
	PatternsTotal.WithLabelValues(status, category, language).Set(float64(count))
}

// UpdateBusinessMetrics updates business metrics
func UpdateBusinessMetrics(dailyRevenue float64, currency string, avgPrice float64, category string, conversionRate float64) {
	RevenuePerDay.WithLabelValues(currency).Set(dailyRevenue)
	AveragePatternPrice.WithLabelValues(category, currency).Set(avgPrice)
	PatternConversionRate.WithLabelValues(category).Set(conversionRate)
}

// UpdateSystemMetrics updates system metrics
func UpdateSystemMetrics(goroutines int, memoryHeap, memoryStack, memoryOther uint64) {
	GoroutinesActive.Set(float64(goroutines))
	MemoryUsage.WithLabelValues("heap").Set(float64(memoryHeap))
	MemoryUsage.WithLabelValues("stack").Set(float64(memoryStack))
	MemoryUsage.WithLabelValues("other").Set(float64(memoryOther))
}
