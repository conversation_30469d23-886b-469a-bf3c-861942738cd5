// Package logger provides structured logging for the marketplace service
// Following Context Engineering standards with production-ready logging
package logger

import (
	"os"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

// NewLogger creates a new structured logger with production configuration
func NewLogger() *zap.Logger {
	// Determine log level from environment
	logLevel := getLogLevel()
	
	// Create encoder config for production
	encoderConfig := zapcore.EncoderConfig{
		TimeKey:        "timestamp",
		LevelKey:       "level",
		NameKey:        "logger",
		Caller<PERSON>ey:      "caller",
		FunctionKey:    zapcore.OmitKey,
		MessageKey:     "message",
		StacktraceKey:  "stacktrace",
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeLevel:    zapcore.LowercaseLevelEncoder,
		EncodeTime:     zapcore.ISO8601TimeEncoder,
		EncodeDuration: zapcore.SecondsDurationEncoder,
		EncodeCaller:   zapcore.ShortCallerEncoder,
	}

	// Create core with appropriate encoder
	var encoder zapcore.Encoder
	if isProduction() {
		encoder = zapcore.NewJSONEncoder(encoderConfig)
	} else {
		encoder = zapcore.NewConsoleEncoder(encoderConfig)
	}

	core := zapcore.NewCore(
		encoder,
		zapcore.AddSync(os.Stdout),
		logLevel,
	)

	// Add caller information and stack traces for errors
	logger := zap.New(core, zap.AddCaller(), zap.AddStacktrace(zapcore.ErrorLevel))

	// Add service-specific fields
	logger = logger.With(
		zap.String("service", "marketplace"),
		zap.String("version", getVersion()),
	)

	return logger
}

// NewLoggerWithFields creates a logger with additional fields
func NewLoggerWithFields(fields ...zap.Field) *zap.Logger {
	logger := NewLogger()
	return logger.With(fields...)
}

// getLogLevel determines the log level from environment
func getLogLevel() zapcore.Level {
	switch os.Getenv("LOG_LEVEL") {
	case "debug", "DEBUG":
		return zapcore.DebugLevel
	case "info", "INFO":
		return zapcore.InfoLevel
	case "warn", "WARN", "warning", "WARNING":
		return zapcore.WarnLevel
	case "error", "ERROR":
		return zapcore.ErrorLevel
	case "fatal", "FATAL":
		return zapcore.FatalLevel
	default:
		if isProduction() {
			return zapcore.InfoLevel
		}
		return zapcore.DebugLevel
	}
}

// isProduction checks if running in production environment
func isProduction() bool {
	env := os.Getenv("ENVIRONMENT")
	return env == "production" || env == "prod"
}

// getVersion returns the service version
func getVersion() string {
	if version := os.Getenv("APP_VERSION"); version != "" {
		return version
	}
	return "unknown"
}

// Middleware logger for HTTP requests
func NewHTTPLogger() *zap.Logger {
	logger := NewLogger()
	return logger.Named("http")
}

// Database logger
func NewDatabaseLogger() *zap.Logger {
	logger := NewLogger()
	return logger.Named("database")
}

// Payment logger with sensitive data filtering
func NewPaymentLogger() *zap.Logger {
	logger := NewLogger()
	return logger.Named("payment")
}

// Pattern validation logger
func NewValidationLogger() *zap.Logger {
	logger := NewLogger()
	return logger.Named("validation")
}

// Background job logger
func NewJobLogger() *zap.Logger {
	logger := NewLogger()
	return logger.Named("job")
}

// Audit logger for security events
func NewAuditLogger() *zap.Logger {
	// Audit logs should always be at info level or higher
	encoderConfig := zapcore.EncoderConfig{
		TimeKey:        "timestamp",
		LevelKey:       "level",
		NameKey:        "logger",
		CallerKey:      "caller",
		MessageKey:     "message",
		StacktraceKey:  "stacktrace",
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeLevel:    zapcore.LowercaseLevelEncoder,
		EncodeTime:     zapcore.ISO8601TimeEncoder,
		EncodeDuration: zapcore.SecondsDurationEncoder,
		EncodeCaller:   zapcore.ShortCallerEncoder,
	}

	core := zapcore.NewCore(
		zapcore.NewJSONEncoder(encoderConfig),
		zapcore.AddSync(os.Stdout),
		zapcore.InfoLevel, // Always info level for audit logs
	)

	logger := zap.New(core, zap.AddCaller())
	return logger.Named("audit").With(
		zap.String("service", "marketplace"),
		zap.String("log_type", "audit"),
	)
}

// Helper functions for common log patterns

// LogError logs an error with context
func LogError(logger *zap.Logger, message string, err error, fields ...zap.Field) {
	allFields := append(fields, zap.Error(err))
	logger.Error(message, allFields...)
}

// LogRequest logs an HTTP request
func LogRequest(logger *zap.Logger, method, path, userID string, duration int64, statusCode int) {
	logger.Info("HTTP request",
		zap.String("method", method),
		zap.String("path", path),
		zap.String("user_id", userID),
		zap.Int64("duration_ms", duration),
		zap.Int("status_code", statusCode),
	)
}

// LogPayment logs a payment event (with sensitive data filtering)
func LogPayment(logger *zap.Logger, event string, paymentID, userID string, amountCents int64, fields ...zap.Field) {
	baseFields := []zap.Field{
		zap.String("event", event),
		zap.String("payment_id", paymentID),
		zap.String("user_id", userID),
		zap.Int64("amount_cents", amountCents),
	}
	
	allFields := append(baseFields, fields...)
	logger.Info("Payment event", allFields...)
}

// LogPatternEvent logs a pattern-related event
func LogPatternEvent(logger *zap.Logger, event string, patternID, userID string, fields ...zap.Field) {
	baseFields := []zap.Field{
		zap.String("event", event),
		zap.String("pattern_id", patternID),
		zap.String("user_id", userID),
	}
	
	allFields := append(baseFields, fields...)
	logger.Info("Pattern event", allFields...)
}

// LogSecurityEvent logs a security-related event
func LogSecurityEvent(logger *zap.Logger, event string, userID, ipAddress string, fields ...zap.Field) {
	baseFields := []zap.Field{
		zap.String("event", event),
		zap.String("user_id", userID),
		zap.String("ip_address", ipAddress),
	}
	
	allFields := append(baseFields, fields...)
	logger.Warn("Security event", allFields...)
}

// LogAuditEvent logs an audit event
func LogAuditEvent(logger *zap.Logger, action string, userID, resourceID, resourceType string, fields ...zap.Field) {
	baseFields := []zap.Field{
		zap.String("action", action),
		zap.String("user_id", userID),
		zap.String("resource_id", resourceID),
		zap.String("resource_type", resourceType),
	}
	
	allFields := append(baseFields, fields...)
	logger.Info("Audit event", allFields...)
}

// Performance logging

// LogSlowQuery logs a slow database query
func LogSlowQuery(logger *zap.Logger, query string, duration int64, fields ...zap.Field) {
	baseFields := []zap.Field{
		zap.String("query", query),
		zap.Int64("duration_ms", duration),
	}
	
	allFields := append(baseFields, fields...)
	logger.Warn("Slow query detected", allFields...)
}

// LogCacheEvent logs cache hit/miss events
func LogCacheEvent(logger *zap.Logger, event string, key string, fields ...zap.Field) {
	baseFields := []zap.Field{
		zap.String("event", event),
		zap.String("cache_key", key),
	}
	
	allFields := append(baseFields, fields...)
	logger.Debug("Cache event", allFields...)
}

// Business logic logging

// LogPatternValidation logs pattern validation results
func LogPatternValidation(logger *zap.Logger, patternID string, isValid bool, score float64, issues int, fields ...zap.Field) {
	baseFields := []zap.Field{
		zap.String("pattern_id", patternID),
		zap.Bool("is_valid", isValid),
		zap.Float64("score", score),
		zap.Int("issues", issues),
	}
	
	allFields := append(baseFields, fields...)
	logger.Info("Pattern validation completed", allFields...)
}

// LogMarketplaceMetrics logs marketplace business metrics
func LogMarketplaceMetrics(logger *zap.Logger, metric string, value float64, fields ...zap.Field) {
	baseFields := []zap.Field{
		zap.String("metric", metric),
		zap.Float64("value", value),
	}
	
	allFields := append(baseFields, fields...)
	logger.Info("Marketplace metric", allFields...)
}
