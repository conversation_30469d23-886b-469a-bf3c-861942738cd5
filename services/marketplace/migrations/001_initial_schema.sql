-- Initial schema for marketplace service
-- Following Context Engineering standards with comprehensive data modeling

-- Users table
CREATE TABLE users (
    user_id STRING(36) NOT NULL,
    email STRING(255) NOT NULL,
    username STRING(50) NOT NULL,
    display_name STRING(100) NOT NULL,
    avatar_url STRING(500),
    bio STRING(500),
    website STRING(255),
    github_username STRING(39),
    twitter_username STRING(15),
    location STRING(100),
    is_verified BOOL NOT NULL DEFAULT FALSE,
    is_active BOOL NOT NULL DEFAULT TRUE,
    role STRING(20) NOT NULL DEFAULT 'user',
    metadata JSON,
    created_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    updated_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    last_login_at TIMESTAMP,
) PRIMARY KEY (user_id);

-- Create unique indexes for users
CREATE UNIQUE INDEX idx_users_email ON users(email);
CREATE UNIQUE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_created_at ON users(created_at);

-- Marketplace profiles table
CREATE TABLE marketplace_profiles (
    user_id STRING(36) NOT NULL,
    seller_status STRING(20) NOT NULL DEFAULT 'inactive',
    stripe_account_id STRING(100),
    total_sales INT64 NOT NULL DEFAULT 0,
    total_revenue_cents INT64 NOT NULL DEFAULT 0,
    total_purchases INT64 NOT NULL DEFAULT 0,
    reputation_score FLOAT64 NOT NULL DEFAULT 0.0,
    bio STRING(1000),
    website STRING(255),
    github_username STRING(39),
    created_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    updated_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    FOREIGN KEY (user_id) REFERENCES users (user_id) ON DELETE CASCADE,
) PRIMARY KEY (user_id);

-- Create indexes for marketplace profiles
CREATE INDEX idx_marketplace_profiles_seller_status ON marketplace_profiles(seller_status);
CREATE INDEX idx_marketplace_profiles_reputation_score ON marketplace_profiles(reputation_score DESC);

-- Patterns table
CREATE TABLE marketplace_patterns (
    pattern_id STRING(36) NOT NULL,
    name STRING(255) NOT NULL,
    description STRING(2000) NOT NULL,
    category STRING(50) NOT NULL,
    language STRING(50) NOT NULL,
    author_id STRING(36) NOT NULL,
    price_cents INT64 NOT NULL DEFAULT 0,
    currency STRING(3) NOT NULL DEFAULT 'USD',
    license_type STRING(20) NOT NULL,
    version STRING(20) NOT NULL,
    downloads INT64 NOT NULL DEFAULT 0,
    rating FLOAT64,
    rating_count INT64 NOT NULL DEFAULT 0,
    status STRING(20) NOT NULL DEFAULT 'draft',
    validation_status STRING(20),
    validation_score FLOAT64,
    pattern_hash STRING(64),
    storage_url STRING(500),
    metadata JSON,
    tags ARRAY<STRING(30)>,
    created_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    updated_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    published_at TIMESTAMP,
    FOREIGN KEY (author_id) REFERENCES users (user_id) ON DELETE CASCADE,
) PRIMARY KEY (pattern_id);

-- Create indexes for patterns
CREATE INDEX idx_patterns_author_id ON marketplace_patterns(author_id);
CREATE INDEX idx_patterns_category ON marketplace_patterns(category);
CREATE INDEX idx_patterns_language ON marketplace_patterns(language);
CREATE INDEX idx_patterns_status ON marketplace_patterns(status);
CREATE INDEX idx_patterns_price_cents ON marketplace_patterns(price_cents);
CREATE INDEX idx_patterns_rating ON marketplace_patterns(rating DESC);
CREATE INDEX idx_patterns_downloads ON marketplace_patterns(downloads DESC);
CREATE INDEX idx_patterns_created_at ON marketplace_patterns(created_at DESC);
CREATE INDEX idx_patterns_published_at ON marketplace_patterns(published_at DESC);

-- Transactions table
CREATE TABLE marketplace_transactions (
    transaction_id STRING(36) NOT NULL,
    pattern_id STRING(36) NOT NULL,
    buyer_id STRING(36) NOT NULL,
    seller_id STRING(36) NOT NULL,
    amount_cents INT64 NOT NULL,
    currency STRING(3) NOT NULL,
    status STRING(20) NOT NULL DEFAULT 'pending',
    stripe_payment_intent_id STRING(100),
    stripe_charge_id STRING(100),
    platform_fee_cents INT64 NOT NULL,
    seller_amount_cents INT64 NOT NULL,
    metadata JSON,
    created_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    completed_at TIMESTAMP,
    FOREIGN KEY (pattern_id) REFERENCES marketplace_patterns (pattern_id) ON DELETE CASCADE,
    FOREIGN KEY (buyer_id) REFERENCES users (user_id) ON DELETE CASCADE,
    FOREIGN KEY (seller_id) REFERENCES users (user_id) ON DELETE CASCADE,
) PRIMARY KEY (transaction_id);

-- Create indexes for transactions
CREATE INDEX idx_transactions_pattern_id ON marketplace_transactions(pattern_id);
CREATE INDEX idx_transactions_buyer_id ON marketplace_transactions(buyer_id);
CREATE INDEX idx_transactions_seller_id ON marketplace_transactions(seller_id);
CREATE INDEX idx_transactions_status ON marketplace_transactions(status);
CREATE INDEX idx_transactions_created_at ON marketplace_transactions(created_at DESC);
CREATE INDEX idx_transactions_completed_at ON marketplace_transactions(completed_at DESC);
CREATE UNIQUE INDEX idx_transactions_stripe_payment_intent ON marketplace_transactions(stripe_payment_intent_id);

-- Reviews table
CREATE TABLE marketplace_reviews (
    review_id STRING(36) NOT NULL,
    pattern_id STRING(36) NOT NULL,
    user_id STRING(36) NOT NULL,
    rating INT64 NOT NULL,
    title STRING(100),
    comment STRING(2000),
    helpful_count INT64 NOT NULL DEFAULT 0,
    verified_purchase BOOL NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    updated_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    FOREIGN KEY (pattern_id) REFERENCES marketplace_patterns (pattern_id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users (user_id) ON DELETE CASCADE,
) PRIMARY KEY (review_id);

-- Create indexes for reviews
CREATE INDEX idx_reviews_pattern_id ON marketplace_reviews(pattern_id);
CREATE INDEX idx_reviews_user_id ON marketplace_reviews(user_id);
CREATE INDEX idx_reviews_rating ON marketplace_reviews(rating DESC);
CREATE INDEX idx_reviews_created_at ON marketplace_reviews(created_at DESC);
CREATE UNIQUE INDEX idx_reviews_pattern_user ON marketplace_reviews(pattern_id, user_id);

-- Pattern purchases table (for access control)
CREATE TABLE marketplace_pattern_purchases (
    purchase_id STRING(36) NOT NULL,
    pattern_id STRING(36) NOT NULL,
    user_id STRING(36) NOT NULL,
    transaction_id STRING(36) NOT NULL,
    license_type STRING(20) NOT NULL,
    purchased_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    expires_at TIMESTAMP,
    FOREIGN KEY (pattern_id) REFERENCES marketplace_patterns (pattern_id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users (user_id) ON DELETE CASCADE,
    FOREIGN KEY (transaction_id) REFERENCES marketplace_transactions (transaction_id) ON DELETE CASCADE,
) PRIMARY KEY (purchase_id);

-- Create indexes for pattern purchases
CREATE INDEX idx_pattern_purchases_pattern_id ON marketplace_pattern_purchases(pattern_id);
CREATE INDEX idx_pattern_purchases_user_id ON marketplace_pattern_purchases(user_id);
CREATE INDEX idx_pattern_purchases_purchased_at ON marketplace_pattern_purchases(purchased_at DESC);
CREATE UNIQUE INDEX idx_pattern_purchases_pattern_user ON marketplace_pattern_purchases(pattern_id, user_id);

-- Pattern views table (for analytics)
CREATE TABLE marketplace_pattern_views (
    view_id STRING(36) NOT NULL,
    pattern_id STRING(36) NOT NULL,
    user_id STRING(36),
    ip_address STRING(45),
    user_agent STRING(500),
    referrer STRING(500),
    viewed_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    FOREIGN KEY (pattern_id) REFERENCES marketplace_patterns (pattern_id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users (user_id) ON DELETE SET NULL,
) PRIMARY KEY (view_id);

-- Create indexes for pattern views
CREATE INDEX idx_pattern_views_pattern_id ON marketplace_pattern_views(pattern_id);
CREATE INDEX idx_pattern_views_user_id ON marketplace_pattern_views(user_id);
CREATE INDEX idx_pattern_views_viewed_at ON marketplace_pattern_views(viewed_at DESC);

-- Seller badges table
CREATE TABLE marketplace_seller_badges (
    badge_id STRING(36) NOT NULL,
    user_id STRING(36) NOT NULL,
    badge_type STRING(30) NOT NULL,
    name STRING(100) NOT NULL,
    description STRING(255) NOT NULL,
    earned_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    FOREIGN KEY (user_id) REFERENCES users (user_id) ON DELETE CASCADE,
) PRIMARY KEY (badge_id);

-- Create indexes for seller badges
CREATE INDEX idx_seller_badges_user_id ON marketplace_seller_badges(user_id);
CREATE INDEX idx_seller_badges_badge_type ON marketplace_seller_badges(badge_type);
CREATE INDEX idx_seller_badges_earned_at ON marketplace_seller_badges(earned_at DESC);
CREATE UNIQUE INDEX idx_seller_badges_user_type ON marketplace_seller_badges(user_id, badge_type);

-- Pattern collections table (for curated lists)
CREATE TABLE marketplace_pattern_collections (
    collection_id STRING(36) NOT NULL,
    name STRING(255) NOT NULL,
    description STRING(1000),
    curator_id STRING(36) NOT NULL,
    is_public BOOL NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    updated_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    FOREIGN KEY (curator_id) REFERENCES users (user_id) ON DELETE CASCADE,
) PRIMARY KEY (collection_id);

-- Create indexes for pattern collections
CREATE INDEX idx_pattern_collections_curator_id ON marketplace_pattern_collections(curator_id);
CREATE INDEX idx_pattern_collections_is_public ON marketplace_pattern_collections(is_public);
CREATE INDEX idx_pattern_collections_created_at ON marketplace_pattern_collections(created_at DESC);

-- Pattern collection items table
CREATE TABLE marketplace_pattern_collection_items (
    collection_id STRING(36) NOT NULL,
    pattern_id STRING(36) NOT NULL,
    added_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    FOREIGN KEY (collection_id) REFERENCES marketplace_pattern_collections (collection_id) ON DELETE CASCADE,
    FOREIGN KEY (pattern_id) REFERENCES marketplace_patterns (pattern_id) ON DELETE CASCADE,
) PRIMARY KEY (collection_id, pattern_id);

-- Create indexes for pattern collection items
CREATE INDEX idx_pattern_collection_items_pattern_id ON marketplace_pattern_collection_items(pattern_id);
CREATE INDEX idx_pattern_collection_items_added_at ON marketplace_pattern_collection_items(added_at DESC);
