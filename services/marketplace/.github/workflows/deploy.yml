# CI/CD Pipeline for Marketplace Service
# Following Context Engineering principles with production-ready deployment

name: Deploy Marketplace Service

on:
  push:
    branches:
      - main
      - production
    paths:
      - 'services/marketplace/**'
  pull_request:
    branches:
      - main
    paths:
      - 'services/marketplace/**'

env:
  PROJECT_ID: vibe-match-463114
  SERVICE_NAME: marketplace-service
  REGION: us-central1
  REGISTRY: gcr.io

jobs:
  # Test and validation job
  test:
    name: Test and Validate
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: services/marketplace
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: '1.21'
        cache-dependency-path: services/marketplace/go.sum
    
    - name: Cache Go modules
      uses: actions/cache@v3
      with:
        path: |
          ~/.cache/go-build
          ~/go/pkg/mod
        key: ${{ runner.os }}-go-${{ hashFiles('services/marketplace/go.sum') }}
        restore-keys: |
          ${{ runner.os }}-go-
    
    - name: Download dependencies
      run: go mod download
    
    - name: Run tests
      run: |
        go test -v -race -coverprofile=coverage.out ./...
        go tool cover -html=coverage.out -o coverage.html
    
    - name: Check test coverage
      run: |
        COVERAGE=$(go tool cover -func=coverage.out | grep total | awk '{print $3}' | sed 's/%//')
        echo "Test coverage: ${COVERAGE}%"
        if (( $(echo "$COVERAGE < 80" | bc -l) )); then
          echo "Test coverage is below 80%"
          exit 1
        fi
    
    - name: Run linting
      uses: golangci/golangci-lint-action@v3
      with:
        version: latest
        working-directory: services/marketplace
        args: --timeout=5m
    
    - name: Run security scan
      uses: securecodewarrior/github-action-add-sarif@v1
      with:
        sarif-file: 'gosec-report.sarif'
      continue-on-error: true
    
    - name: Build application
      run: |
        CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build \
          -ldflags='-w -s -extldflags "-static"' \
          -a -installsuffix cgo \
          -o marketplace \
          ./cmd/server
    
    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: services/marketplace/coverage.out
        flags: marketplace
        name: marketplace-coverage

  # Build and deploy job (only on main/production branches)
  deploy:
    name: Build and Deploy
    runs-on: ubuntu-latest
    needs: test
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/production'
    defaults:
      run:
        working-directory: services/marketplace
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Cloud SDK
      uses: google-github-actions/setup-gcloud@v1
      with:
        project_id: ${{ env.PROJECT_ID }}
        service_account_key: ${{ secrets.GCP_SA_KEY }}
        export_default_credentials: true
    
    - name: Configure Docker for GCR
      run: gcloud auth configure-docker
    
    - name: Set environment variables
      run: |
        if [[ "${{ github.ref }}" == "refs/heads/production" ]]; then
          echo "ENVIRONMENT=production" >> $GITHUB_ENV
          echo "IMAGE_TAG=production-$(date +%Y%m%d-%H%M%S)" >> $GITHUB_ENV
        else
          echo "ENVIRONMENT=staging" >> $GITHUB_ENV
          echo "IMAGE_TAG=staging-$(date +%Y%m%d-%H%M%S)" >> $GITHUB_ENV
        fi
        echo "FULL_IMAGE_NAME=${{ env.REGISTRY }}/${{ env.PROJECT_ID }}/${{ env.SERVICE_NAME }}:${IMAGE_TAG}" >> $GITHUB_ENV
    
    - name: Build Docker image
      run: |
        docker build \
          --build-arg ENVIRONMENT=${{ env.ENVIRONMENT }} \
          --build-arg VERSION=${{ env.IMAGE_TAG }} \
          -t ${{ env.FULL_IMAGE_NAME }} \
          -t ${{ env.REGISTRY }}/${{ env.PROJECT_ID }}/${{ env.SERVICE_NAME }}:latest \
          .
    
    - name: Run security scan on image
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: ${{ env.FULL_IMAGE_NAME }}
        format: 'sarif'
        output: 'trivy-results.sarif'
      continue-on-error: true
    
    - name: Push Docker image
      run: |
        docker push ${{ env.FULL_IMAGE_NAME }}
        docker push ${{ env.REGISTRY }}/${{ env.PROJECT_ID }}/${{ env.SERVICE_NAME }}:latest
    
    - name: Deploy to Cloud Run
      run: |
        gcloud run deploy ${{ env.SERVICE_NAME }} \
          --image=${{ env.FULL_IMAGE_NAME }} \
          --region=${{ env.REGION }} \
          --platform=managed \
          --allow-unauthenticated \
          --set-env-vars="ENVIRONMENT=${{ env.ENVIRONMENT }}" \
          --set-env-vars="VERSION=${{ env.IMAGE_TAG }}" \
          --cpu=2 \
          --memory=4Gi \
          --concurrency=1000 \
          --max-instances=100 \
          --min-instances=2 \
          --timeout=300 \
          --service-account=marketplace-service@${{ env.PROJECT_ID }}.iam.gserviceaccount.com
    
    - name: Get service URL
      run: |
        SERVICE_URL=$(gcloud run services describe ${{ env.SERVICE_NAME }} \
          --region=${{ env.REGION }} \
          --format='value(status.url)')
        echo "Service deployed at: $SERVICE_URL"
        echo "SERVICE_URL=$SERVICE_URL" >> $GITHUB_ENV
    
    - name: Run health check
      run: |
        sleep 30  # Wait for service to be ready
        curl -f ${{ env.SERVICE_URL }}/health || exit 1
        echo "Health check passed"
    
    - name: Run load test (production only)
      if: github.ref == 'refs/heads/production'
      run: |
        # Install k6 for load testing
        sudo apt-key adv --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys C5AD17C747E3415A3642D57D77C6C491D6AC1D69
        echo "deb https://dl.k6.io/deb stable main" | sudo tee /etc/apt/sources.list.d/k6.list
        sudo apt-get update
        sudo apt-get install k6
        
        # Run load test
        k6 run --vus 50 --duration 2m scripts/load-test.js
    
    - name: Notify deployment
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        channel: '#deployments'
        text: |
          Marketplace service deployed successfully!
          Environment: ${{ env.ENVIRONMENT }}
          Version: ${{ env.IMAGE_TAG }}
          URL: ${{ env.SERVICE_URL }}
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
      if: always()
