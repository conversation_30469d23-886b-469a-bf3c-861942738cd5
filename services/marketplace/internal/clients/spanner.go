// Package clients provides database and external service clients
// Following Context Engineering standards with production-ready patterns
package clients

import (
	"context"
	"fmt"
	"sync"
	"time"

	"cloud.google.com/go/spanner"
	"go.uber.org/zap"

	"github.com/episteme/marketplace/internal/config"
	"github.com/episteme/marketplace/internal/models"
	"github.com/episteme/marketplace/pkg/logger"
)

// QueryStats tracks query performance metrics
type QueryStats struct {
	QueryCount    int64         `json:"query_count"`
	TotalDuration time.Duration `json:"total_duration"`
	AvgDuration   time.Duration `json:"avg_duration"`
	SlowQueries   int64         `json:"slow_queries"`
}

// SpannerClient wraps Google Cloud Spanner client with marketplace-specific methods
type SpannerClient struct {
	client      *spanner.Client
	database    string
	logger      *zap.Logger
	queryStats  map[string]*QueryStats
	statsMutex  sync.RWMutex
	perfMonitor func(operation, table string, duration time.Duration)
}

// NewSpannerClient creates a new Spanner client
func NewSpannerClient(cfg config.SpannerConfig) (*SpannerClient, error) {
	ctx := context.Background()

	// Construct database path
	database := fmt.Sprintf("projects/%s/instances/%s/databases/%s",
		cfg.ProjectID, cfg.InstanceID, cfg.DatabaseID)

	// Create Spanner client with production settings
	client, err := spanner.NewClient(ctx, database)
	if err != nil {
		return nil, fmt.Errorf("failed to create Spanner client: %w", err)
	}

	return &SpannerClient{
		client:     client,
		database:   database,
		logger:     logger.NewLogger(),
		queryStats: make(map[string]*QueryStats),
	}, nil
}

// Close closes the Spanner client
func (s *SpannerClient) Close() {
	s.client.Close()
}

// SetPerformanceMonitor sets the performance monitoring callback
func (s *SpannerClient) SetPerformanceMonitor(monitor func(operation, table string, duration time.Duration)) {
	s.perfMonitor = monitor
}

// GetQueryStats returns query performance statistics
func (s *SpannerClient) GetQueryStats() map[string]*QueryStats {
	s.statsMutex.RLock()
	defer s.statsMutex.RUnlock()

	// Create a copy to avoid race conditions
	stats := make(map[string]*QueryStats)
	for key, value := range s.queryStats {
		statsCopy := *value
		if statsCopy.QueryCount > 0 {
			statsCopy.AvgDuration = statsCopy.TotalDuration / time.Duration(statsCopy.QueryCount)
		}
		stats[key] = &statsCopy
	}
	return stats
}

// ClearQueryStats resets query statistics
func (s *SpannerClient) ClearQueryStats() {
	s.statsMutex.Lock()
	defer s.statsMutex.Unlock()

	s.queryStats = make(map[string]*QueryStats)
}

// recordQueryStats records performance metrics for a query
func (s *SpannerClient) recordQueryStats(operation string, duration time.Duration) {
	s.statsMutex.Lock()
	defer s.statsMutex.Unlock()

	if s.queryStats[operation] == nil {
		s.queryStats[operation] = &QueryStats{}
	}

	stats := s.queryStats[operation]
	stats.QueryCount++
	stats.TotalDuration += duration

	// Track slow queries (>100ms)
	if duration > 100*time.Millisecond {
		stats.SlowQueries++
	}

	// Call performance monitor if set
	if s.perfMonitor != nil {
		s.perfMonitor(operation, "spanner", duration)
	}
}

// Client returns the underlying Spanner client
func (s *SpannerClient) Client() *spanner.Client {
	return s.client
}

// ReadOnlyTransaction creates a read-only transaction
func (s *SpannerClient) ReadOnlyTransaction() *spanner.ReadOnlyTransaction {
	return s.client.ReadOnlyTransaction()
}

// ReadWriteTransaction executes a read-write transaction
func (s *SpannerClient) ReadWriteTransaction(ctx context.Context, f func(context.Context, *spanner.ReadWriteTransaction) error) (time.Time, error) {
	return s.client.ReadWriteTransaction(ctx, f)
}

// Single executes a single read operation
func (s *SpannerClient) Single() *spanner.ReadOnlyTransaction {
	return s.client.Single()
}

// Apply applies mutations to the database
func (s *SpannerClient) Apply(ctx context.Context, ms []*spanner.Mutation, opts ...spanner.ApplyOption) (time.Time, error) {
	return s.client.Apply(ctx, ms, opts...)
}

// Query executes a SQL query
func (s *SpannerClient) Query(ctx context.Context, statement spanner.Statement) *spanner.RowIterator {
	return s.client.Single().Query(ctx, statement)
}

// Read reads rows from a table
func (s *SpannerClient) Read(ctx context.Context, table string, keys spanner.KeySet, columns []string) *spanner.RowIterator {
	return s.client.Single().Read(ctx, table, keys, columns)
}

// BatchReadOnlyTransaction creates a batch read-only transaction
func (s *SpannerClient) BatchReadOnlyTransaction(ctx context.Context, tb spanner.TimestampBound) (*spanner.BatchReadOnlyTransaction, error) {
	return s.client.BatchReadOnlyTransaction(ctx, tb)
}

// PartitionQuery partitions a query for parallel execution
func (s *SpannerClient) PartitionQuery(ctx context.Context, statement spanner.Statement, partitionOptions spanner.PartitionOptions) ([]*spanner.Partition, error) {
	txn, err := s.client.BatchReadOnlyTransaction(ctx, spanner.StrongRead())
	if err != nil {
		return nil, fmt.Errorf("failed to create batch transaction: %w", err)
	}
	defer txn.Close()

	return txn.PartitionQuery(ctx, statement, partitionOptions)
}

// HealthCheck performs a health check on the Spanner connection
func (s *SpannerClient) HealthCheck(ctx context.Context) error {
	// Simple query to test connection
	stmt := spanner.Statement{
		SQL: "SELECT 1",
	}

	iter := s.client.Single().Query(ctx, stmt)
	defer iter.Stop()

	_, err := iter.Next()
	if err != nil {
		return fmt.Errorf("Spanner health check failed: %w", err)
	}

	return nil
}

// GetDatabaseInfo returns information about the database
func (s *SpannerClient) GetDatabaseInfo(ctx context.Context) (map[string]interface{}, error) {
	info := map[string]interface{}{
		"database": s.database,
		"status":   "connected",
	}

	// Test connection
	if err := s.HealthCheck(ctx); err != nil {
		info["status"] = "error"
		info["error"] = err.Error()
		return info, err
	}

	return info, nil
}

// ExecuteQuery executes a parameterized query with logging
func (s *SpannerClient) ExecuteQuery(ctx context.Context, sql string, params map[string]interface{}) *spanner.RowIterator {
	stmt := spanner.Statement{
		SQL:    sql,
		Params: params,
	}

	s.logger.Debug("Executing Spanner query",
		zap.String("sql", sql),
		zap.Any("params", params),
	)

	return s.client.Single().Query(ctx, stmt)
}

// ExecuteUpdate executes an update statement
func (s *SpannerClient) ExecuteUpdate(ctx context.Context, sql string, params map[string]interface{}) (int64, error) {
	stmt := spanner.Statement{
		SQL:    sql,
		Params: params,
	}

	s.logger.Debug("Executing Spanner update",
		zap.String("sql", sql),
		zap.Any("params", params),
	)

	return s.client.PartitionedUpdate(ctx, stmt)
}

// BatchInsert performs a batch insert operation
func (s *SpannerClient) BatchInsert(ctx context.Context, table string, columns []string, values [][]interface{}) error {
	var mutations []*spanner.Mutation

	for _, row := range values {
		mutation := spanner.Insert(table, columns, row)
		mutations = append(mutations, mutation)
	}

	_, err := s.client.Apply(ctx, mutations)
	if err != nil {
		return fmt.Errorf("batch insert failed: %w", err)
	}

	s.logger.Debug("Batch insert completed",
		zap.String("table", table),
		zap.Int("rows", len(values)),
	)

	return nil
}

// BatchUpdate performs a batch update operation
func (s *SpannerClient) BatchUpdate(ctx context.Context, table string, columns []string, values [][]interface{}) error {
	var mutations []*spanner.Mutation

	for _, row := range values {
		mutation := spanner.Update(table, columns, row)
		mutations = append(mutations, mutation)
	}

	_, err := s.client.Apply(ctx, mutations)
	if err != nil {
		return fmt.Errorf("batch update failed: %w", err)
	}

	s.logger.Debug("Batch update completed",
		zap.String("table", table),
		zap.Int("rows", len(values)),
	)

	return nil
}

// Transaction helper methods

// InsertPattern inserts a pattern record
func (s *SpannerClient) InsertPattern(ctx context.Context, pattern *models.Pattern) error {
	mutation := spanner.Insert("marketplace_patterns",
		[]string{
			"pattern_id", "name", "description", "category", "language",
			"author_id", "price_cents", "currency", "license_type", "version",
			"downloads", "rating_count", "status", "pattern_hash", "storage_url",
			"metadata", "tags", "created_at", "updated_at",
		},
		[]interface{}{
			pattern.ID, pattern.Name, pattern.Description, pattern.Category, pattern.Language,
			pattern.AuthorID, pattern.PriceCents, pattern.Currency, pattern.LicenseType, pattern.Version,
			pattern.Downloads, pattern.RatingCount, string(pattern.Status), pattern.PatternHash, pattern.StorageURL,
			pattern.Metadata, pattern.Tags, pattern.CreatedAt, pattern.UpdatedAt,
		})

	_, err := s.client.Apply(ctx, []*spanner.Mutation{mutation})
	if err != nil {
		s.logger.Error("Failed to insert pattern", zap.Error(err), zap.String("pattern_id", pattern.ID))
		return fmt.Errorf("failed to insert pattern: %w", err)
	}

	return nil
}

// UpdatePattern updates a pattern record
func (s *SpannerClient) UpdatePattern(ctx context.Context, patternID string, updates map[string]interface{}) error {
	// This would be implemented with proper pattern model mapping
	// For now, returning a placeholder
	return fmt.Errorf("not implemented")
}

// GetPattern retrieves a pattern by ID
func (s *SpannerClient) GetPattern(ctx context.Context, patternID string) (*models.Pattern, error) {
	stmt := spanner.Statement{
		SQL: `SELECT pattern_id, name, description, category, language, author_id,
			  price_cents, currency, license_type, version, downloads, rating,
			  rating_count, status, validation_status, validation_score,
			  pattern_hash, storage_url, metadata, tags, created_at, updated_at, published_at
			  FROM marketplace_patterns WHERE pattern_id = @pattern_id`,
		Params: map[string]interface{}{
			"pattern_id": patternID,
		},
	}

	iter := s.client.Single().Query(ctx, stmt)
	defer iter.Stop()

	row, err := iter.Next()
	if err != nil {
		return nil, fmt.Errorf("failed to query pattern: %w", err)
	}

	pattern := &models.Pattern{}
	var validationStatus *string
	var rating *float64
	var publishedAt *time.Time

	err = row.Columns(
		&pattern.ID, &pattern.Name, &pattern.Description, &pattern.Category, &pattern.Language,
		&pattern.AuthorID, &pattern.PriceCents, &pattern.Currency, &pattern.LicenseType, &pattern.Version,
		&pattern.Downloads, &rating, &pattern.RatingCount, &pattern.Status, &validationStatus,
		&pattern.ValidationScore, &pattern.PatternHash, &pattern.StorageURL, &pattern.Metadata,
		&pattern.Tags, &pattern.CreatedAt, &pattern.UpdatedAt, &publishedAt,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to scan pattern: %w", err)
	}

	// Handle nullable fields
	pattern.Rating = rating
	pattern.PublishedAt = publishedAt
	if validationStatus != nil {
		vs := models.ValidationStatus(*validationStatus)
		pattern.ValidationStatus = &vs
	}

	return pattern, nil
}

// ListPatterns retrieves patterns with pagination
func (s *SpannerClient) ListPatterns(ctx context.Context, params *models.ListPatternsParams) ([]*models.Pattern, int64, error) {
	// Build WHERE clause based on filters
	whereClause := "WHERE 1=1"
	queryParams := make(map[string]interface{})

	if params.Category != "" {
		whereClause += " AND category = @category"
		queryParams["category"] = params.Category
	}
	if params.Language != "" {
		whereClause += " AND language = @language"
		queryParams["language"] = params.Language
	}
	if params.AuthorID != "" {
		whereClause += " AND author_id = @author_id"
		queryParams["author_id"] = params.AuthorID
	}
	if params.Status != "" {
		whereClause += " AND status = @status"
		queryParams["status"] = string(params.Status)
	} else {
		// Default to published patterns only
		whereClause += " AND status = @status"
		queryParams["status"] = string(models.PatternStatusPublished)
	}

	// Build ORDER BY clause
	orderBy := "ORDER BY created_at DESC"
	if params.Sort != "" {
		direction := "DESC"
		if params.Order == "asc" {
			direction = "ASC"
		}
		orderBy = fmt.Sprintf("ORDER BY %s %s", params.Sort, direction)
	}

	// Calculate offset
	offset := (params.Page - 1) * params.PageSize

	// Query for patterns
	stmt := spanner.Statement{
		SQL: fmt.Sprintf(`SELECT pattern_id, name, description, category, language, author_id,
			  price_cents, currency, license_type, version, downloads, rating,
			  rating_count, status, validation_status, validation_score,
			  pattern_hash, storage_url, metadata, tags, created_at, updated_at, published_at
			  FROM marketplace_patterns %s %s LIMIT @limit OFFSET @offset`, whereClause, orderBy),
		Params: map[string]interface{}{
			"limit":  params.PageSize,
			"offset": offset,
		},
	}

	// Add filter params
	for k, v := range queryParams {
		stmt.Params[k] = v
	}

	iter := s.client.Single().Query(ctx, stmt)
	defer iter.Stop()

	var patterns []*models.Pattern
	for {
		row, err := iter.Next()
		if err != nil {
			break
		}

		pattern := &models.Pattern{}
		var validationStatus *string
		var rating *float64
		var publishedAt *time.Time

		err = row.Columns(
			&pattern.ID, &pattern.Name, &pattern.Description, &pattern.Category, &pattern.Language,
			&pattern.AuthorID, &pattern.PriceCents, &pattern.Currency, &pattern.LicenseType, &pattern.Version,
			&pattern.Downloads, &rating, &pattern.RatingCount, &pattern.Status, &validationStatus,
			&pattern.ValidationScore, &pattern.PatternHash, &pattern.StorageURL, &pattern.Metadata,
			&pattern.Tags, &pattern.CreatedAt, &pattern.UpdatedAt, &publishedAt,
		)
		if err != nil {
			return nil, 0, fmt.Errorf("failed to scan pattern: %w", err)
		}

		// Handle nullable fields
		pattern.Rating = rating
		pattern.PublishedAt = publishedAt
		if validationStatus != nil {
			vs := models.ValidationStatus(*validationStatus)
			pattern.ValidationStatus = &vs
		}

		patterns = append(patterns, pattern)
	}

	// Get total count
	countStmt := spanner.Statement{
		SQL:    fmt.Sprintf("SELECT COUNT(*) FROM marketplace_patterns %s", whereClause),
		Params: queryParams,
	}

	countIter := s.client.Single().Query(ctx, countStmt)
	defer countIter.Stop()

	var total int64
	if row, err := countIter.Next(); err == nil {
		row.Columns(&total)
	}

	return patterns, total, nil
}

// InsertTransaction inserts a new transaction
func (s *SpannerClient) InsertTransaction(ctx context.Context, transaction *models.Transaction) error {
	mutation := spanner.Insert("marketplace_transactions",
		[]string{"transaction_id", "pattern_id", "buyer_id", "seller_id", "amount_cents", "currency", "status", "stripe_payment_intent_id", "stripe_charge_id", "platform_fee_cents", "seller_amount_cents", "metadata", "created_at", "completed_at"},
		[]interface{}{transaction.ID, transaction.PatternID, transaction.BuyerID, transaction.SellerID, transaction.AmountCents, transaction.Currency, string(transaction.Status), transaction.StripePaymentIntentID, transaction.StripeChargeID, transaction.PlatformFeeCents, transaction.SellerAmountCents, transaction.Metadata, transaction.CreatedAt, transaction.CompletedAt})

	_, err := s.client.Apply(ctx, []*spanner.Mutation{mutation})
	return err
}

// GetTransactionByPaymentIntent retrieves a transaction by Stripe payment intent ID
func (s *SpannerClient) GetTransactionByPaymentIntent(ctx context.Context, paymentIntentID string) (*models.Transaction, error) {
	stmt := spanner.Statement{
		SQL: `SELECT transaction_id, pattern_id, buyer_id, seller_id, amount_cents, currency,
			  status, stripe_payment_intent_id, stripe_charge_id, platform_fee_cents,
			  seller_amount_cents, metadata, created_at, completed_at
			  FROM marketplace_transactions
			  WHERE stripe_payment_intent_id = @payment_intent_id`,
		Params: map[string]interface{}{
			"payment_intent_id": paymentIntentID,
		},
	}

	iter := s.client.Single().Query(ctx, stmt)
	defer iter.Stop()

	row, err := iter.Next()
	if err != nil {
		return nil, fmt.Errorf("transaction not found: %w", err)
	}

	transaction := &models.Transaction{}
	err = row.Columns(
		&transaction.ID, &transaction.PatternID, &transaction.BuyerID, &transaction.SellerID,
		&transaction.AmountCents, &transaction.Currency, &transaction.Status,
		&transaction.StripePaymentIntentID, &transaction.StripeChargeID,
		&transaction.PlatformFeeCents, &transaction.SellerAmountCents,
		&transaction.Metadata, &transaction.CreatedAt, &transaction.CompletedAt,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to scan transaction: %w", err)
	}

	return transaction, nil
}

// UpdateTransaction updates an existing transaction
func (s *SpannerClient) UpdateTransaction(ctx context.Context, transaction *models.Transaction) error {
	mutation := spanner.Update("marketplace_transactions",
		[]string{"transaction_id", "status", "stripe_charge_id", "completed_at", "metadata"},
		[]interface{}{transaction.ID, string(transaction.Status), transaction.StripeChargeID, transaction.CompletedAt, transaction.Metadata})

	_, err := s.client.Apply(ctx, []*spanner.Mutation{mutation})
	return err
}

// Metrics and monitoring

// GetConnectionStats returns connection statistics
func (s *SpannerClient) GetConnectionStats() map[string]interface{} {
	return map[string]interface{}{
		"database":  s.database,
		"status":    "connected",
		"client_id": fmt.Sprintf("%p", s.client),
	}
}
