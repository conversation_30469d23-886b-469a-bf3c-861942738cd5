// Package clients provides Redis client for caching and session management
package clients

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"

	"github.com/episteme/marketplace/internal/config"
	"github.com/episteme/marketplace/pkg/logger"
)

// RedisClient wraps Redis client with marketplace-specific methods
type RedisClient struct {
	client *redis.Client
	logger *zap.Logger
}

// NewRedisClient creates a new Redis client
func NewRedisClient(cfg config.RedisConfig) (*RedisClient, error) {
	// Create Redis client with production settings
	client := redis.NewClient(&redis.Options{
		Addr:            cfg.Address,
		Password:        cfg.Password,
		DB:              cfg.DB,
		PoolSize:        10,
		MinIdleConns:    5,
		MaxRetries:      3,
		DialTimeout:     5 * time.Second,
		ReadTimeout:     3 * time.Second,
		WriteTimeout:    3 * time.Second,
		PoolTimeout:     4 * time.Second,
		ConnMaxIdleTime: 5 * time.Minute,
	})

	// Test connection
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := client.Ping(ctx).Err(); err != nil {
		return nil, fmt.Errorf("failed to connect to Redis: %w", err)
	}

	return &RedisClient{
		client: client,
		logger: logger.NewLogger(),
	}, nil
}

// Close closes the Redis client
func (r *RedisClient) Close() error {
	return r.client.Close()
}

// Client returns the underlying Redis client
func (r *RedisClient) Client() *redis.Client {
	return r.client
}

// Cache operations

// Set stores a value with expiration
func (r *RedisClient) Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
	data, err := json.Marshal(value)
	if err != nil {
		return fmt.Errorf("failed to marshal value: %w", err)
	}

	if err := r.client.Set(ctx, key, data, expiration).Err(); err != nil {
		r.logger.Error("Failed to set cache key",
			zap.String("key", key),
			zap.Error(err),
		)
		return fmt.Errorf("failed to set cache key: %w", err)
	}

	r.logger.Debug("Cache key set",
		zap.String("key", key),
		zap.Duration("expiration", expiration),
	)

	return nil
}

// Get retrieves a value from cache
func (r *RedisClient) Get(ctx context.Context, key string, dest interface{}) error {
	data, err := r.client.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return fmt.Errorf("cache miss for key: %s", key)
		}
		return fmt.Errorf("failed to get cache key: %w", err)
	}

	if err := json.Unmarshal([]byte(data), dest); err != nil {
		return fmt.Errorf("failed to unmarshal cached value: %w", err)
	}

	r.logger.Debug("Cache hit",
		zap.String("key", key),
	)

	return nil
}

// Delete removes a key from cache
func (r *RedisClient) Delete(ctx context.Context, keys ...string) error {
	if len(keys) == 0 {
		return nil
	}

	deleted, err := r.client.Del(ctx, keys...).Result()
	if err != nil {
		return fmt.Errorf("failed to delete cache keys: %w", err)
	}

	r.logger.Debug("Cache keys deleted",
		zap.Strings("keys", keys),
		zap.Int64("deleted", deleted),
	)

	return nil
}

// Exists checks if a key exists
func (r *RedisClient) Exists(ctx context.Context, key string) (bool, error) {
	count, err := r.client.Exists(ctx, key).Result()
	if err != nil {
		return false, fmt.Errorf("failed to check key existence: %w", err)
	}

	return count > 0, nil
}

// Expire sets expiration for a key
func (r *RedisClient) Expire(ctx context.Context, key string, expiration time.Duration) error {
	if err := r.client.Expire(ctx, key, expiration).Err(); err != nil {
		return fmt.Errorf("failed to set expiration: %w", err)
	}

	return nil
}

// TTL returns the time to live for a key
func (r *RedisClient) TTL(ctx context.Context, key string) (time.Duration, error) {
	ttl, err := r.client.TTL(ctx, key).Result()
	if err != nil {
		return 0, fmt.Errorf("failed to get TTL: %w", err)
	}

	return ttl, nil
}

// Pattern-specific cache operations

// CachePattern caches a pattern with 5-minute expiration
func (r *RedisClient) CachePattern(ctx context.Context, patternID string, pattern interface{}) error {
	key := fmt.Sprintf("pattern:detail:%s", patternID)
	return r.Set(ctx, key, pattern, 5*time.Minute)
}

// GetCachedPattern retrieves a cached pattern
func (r *RedisClient) GetCachedPattern(ctx context.Context, patternID string, dest interface{}) error {
	key := fmt.Sprintf("pattern:detail:%s", patternID)
	return r.Get(ctx, key, dest)
}

// CachePatternList caches a pattern list with 1-minute expiration
func (r *RedisClient) CachePatternList(ctx context.Context, cacheKey string, patterns interface{}) error {
	key := fmt.Sprintf("pattern:list:%s", cacheKey)
	return r.Set(ctx, key, patterns, 1*time.Minute)
}

// GetCachedPatternList retrieves a cached pattern list
func (r *RedisClient) GetCachedPatternList(ctx context.Context, cacheKey string, dest interface{}) error {
	key := fmt.Sprintf("pattern:list:%s", cacheKey)
	return r.Get(ctx, key, dest)
}

// CacheUserProfile caches a user profile with 10-minute expiration
func (r *RedisClient) CacheUserProfile(ctx context.Context, userID string, profile interface{}) error {
	key := fmt.Sprintf("user:profile:%s", userID)
	return r.Set(ctx, key, profile, 10*time.Minute)
}

// GetCachedUserProfile retrieves a cached user profile
func (r *RedisClient) GetCachedUserProfile(ctx context.Context, userID string, dest interface{}) error {
	key := fmt.Sprintf("user:profile:%s", userID)
	return r.Get(ctx, key, dest)
}

// Session management

// SetSession stores a user session
func (r *RedisClient) SetSession(ctx context.Context, sessionID string, userID string, expiration time.Duration) error {
	key := fmt.Sprintf("session:%s", sessionID)
	sessionData := map[string]interface{}{
		"user_id":    userID,
		"created_at": time.Now().UTC(),
	}
	return r.Set(ctx, key, sessionData, expiration)
}

// GetSession retrieves a user session
func (r *RedisClient) GetSession(ctx context.Context, sessionID string) (string, error) {
	key := fmt.Sprintf("session:%s", sessionID)
	var sessionData map[string]interface{}

	if err := r.Get(ctx, key, &sessionData); err != nil {
		return "", err
	}

	userID, ok := sessionData["user_id"].(string)
	if !ok {
		return "", fmt.Errorf("invalid session data")
	}

	return userID, nil
}

// DeleteSession removes a user session
func (r *RedisClient) DeleteSession(ctx context.Context, sessionID string) error {
	key := fmt.Sprintf("session:%s", sessionID)
	return r.Delete(ctx, key)
}

// Rate limiting

// CheckRateLimit checks if a rate limit is exceeded
func (r *RedisClient) CheckRateLimit(ctx context.Context, key string, limit int64, window time.Duration) (bool, error) {
	pipe := r.client.Pipeline()

	// Increment counter
	incr := pipe.Incr(ctx, key)
	pipe.Expire(ctx, key, window)

	_, err := pipe.Exec(ctx)
	if err != nil {
		return false, fmt.Errorf("failed to check rate limit: %w", err)
	}

	count := incr.Val()
	return count <= limit, nil
}

// Real-time features

// PublishNotification publishes a notification to a channel
func (r *RedisClient) PublishNotification(ctx context.Context, channel string, message interface{}) error {
	data, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("failed to marshal notification: %w", err)
	}

	if err := r.client.Publish(ctx, channel, data).Err(); err != nil {
		return fmt.Errorf("failed to publish notification: %w", err)
	}

	r.logger.Debug("Notification published",
		zap.String("channel", channel),
	)

	return nil
}

// Subscribe subscribes to a channel for notifications
func (r *RedisClient) Subscribe(ctx context.Context, channels ...string) *redis.PubSub {
	return r.client.Subscribe(ctx, channels...)
}

// Health check

// HealthCheck performs a health check on the Redis connection
func (r *RedisClient) HealthCheck(ctx context.Context) error {
	if err := r.client.Ping(ctx).Err(); err != nil {
		return fmt.Errorf("Redis health check failed: %w", err)
	}
	return nil
}

// GetConnectionStats returns connection statistics
func (r *RedisClient) GetConnectionStats() map[string]interface{} {
	stats := r.client.PoolStats()
	return map[string]interface{}{
		"hits":        stats.Hits,
		"misses":      stats.Misses,
		"timeouts":    stats.Timeouts,
		"total_conns": stats.TotalConns,
		"idle_conns":  stats.IdleConns,
		"stale_conns": stats.StaleConns,
	}
}
