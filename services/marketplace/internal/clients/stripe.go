// Package clients provides Stripe client for payment processing
package clients

import (
	"fmt"

	"github.com/stripe/stripe-go/v75"
	"github.com/stripe/stripe-go/v75/account"
	"github.com/stripe/stripe-go/v75/customer"
	"github.com/stripe/stripe-go/v75/paymentintent"
	"github.com/stripe/stripe-go/v75/paymentmethod"
	"github.com/stripe/stripe-go/v75/refund"
	"github.com/stripe/stripe-go/v75/transfer"
	"go.uber.org/zap"

	"github.com/episteme/marketplace/internal/config"
	"github.com/episteme/marketplace/pkg/logger"
)

// StripeClient wraps Stripe client with marketplace-specific methods
type StripeClient struct {
	secretKey       string
	webhookSecret   string
	platformFeeRate float64
	logger          *zap.Logger
}

// NewStripeClient creates a new Stripe client
func NewStripeClient(cfg config.StripeConfig) *StripeClient {
	// Set Stripe API key
	stripe.Key = cfg.SecretKey

	return &StripeClient{
		secretKey:       cfg.SecretKey,
		webhookSecret:   cfg.WebhookSecret,
		platformFeeRate: cfg.PlatformFeeRate,
		logger:          logger.NewLogger(),
	}
}

// Payment Intent operations

// CreatePaymentIntent creates a payment intent for a pattern purchase
func (s *StripeClient) CreatePaymentIntent(customerID string, amountCents int64, currency string, metadata map[string]string) (*stripe.PaymentIntent, error) {
	params := &stripe.PaymentIntentParams{
		Amount:   stripe.Int64(amountCents),
		Currency: stripe.String(currency),
		Customer: stripe.String(customerID),
		Metadata: metadata,
		AutomaticPaymentMethods: &stripe.PaymentIntentAutomaticPaymentMethodsParams{
			Enabled: stripe.Bool(true),
		},
	}

	pi, err := paymentintent.New(params)
	if err != nil {
		s.logger.Error("Failed to create payment intent",
			zap.String("customer_id", customerID),
			zap.Int64("amount_cents", amountCents),
			zap.Error(err),
		)
		return nil, fmt.Errorf("failed to create payment intent: %w", err)
	}

	s.logger.Info("Payment intent created",
		zap.String("payment_intent_id", pi.ID),
		zap.String("customer_id", customerID),
		zap.Int64("amount_cents", amountCents),
	)

	return pi, nil
}

// ConfirmPaymentIntent confirms a payment intent
func (s *StripeClient) ConfirmPaymentIntent(paymentIntentID string, paymentMethodID string) (*stripe.PaymentIntent, error) {
	params := &stripe.PaymentIntentConfirmParams{
		PaymentMethod: stripe.String(paymentMethodID),
	}

	pi, err := paymentintent.Confirm(paymentIntentID, params)
	if err != nil {
		s.logger.Error("Failed to confirm payment intent",
			zap.String("payment_intent_id", paymentIntentID),
			zap.Error(err),
		)
		return nil, fmt.Errorf("failed to confirm payment intent: %w", err)
	}

	s.logger.Info("Payment intent confirmed",
		zap.String("payment_intent_id", paymentIntentID),
		zap.String("status", string(pi.Status)),
	)

	return pi, nil
}

// GetPaymentIntent retrieves a payment intent
func (s *StripeClient) GetPaymentIntent(paymentIntentID string) (*stripe.PaymentIntent, error) {
	pi, err := paymentintent.Get(paymentIntentID, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to get payment intent: %w", err)
	}

	return pi, nil
}

// Customer operations

// CreateCustomer creates a Stripe customer
func (s *StripeClient) CreateCustomer(email, name string, metadata map[string]string) (*stripe.Customer, error) {
	params := &stripe.CustomerParams{
		Email:    stripe.String(email),
		Name:     stripe.String(name),
		Metadata: metadata,
	}

	cust, err := customer.New(params)
	if err != nil {
		s.logger.Error("Failed to create customer",
			zap.String("email", email),
			zap.Error(err),
		)
		return nil, fmt.Errorf("failed to create customer: %w", err)
	}

	s.logger.Info("Customer created",
		zap.String("customer_id", cust.ID),
		zap.String("email", email),
	)

	return cust, nil
}

// GetCustomer retrieves a Stripe customer
func (s *StripeClient) GetCustomer(customerID string) (*stripe.Customer, error) {
	cust, err := customer.Get(customerID, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to get customer: %w", err)
	}

	return cust, nil
}

// UpdateCustomer updates a Stripe customer
func (s *StripeClient) UpdateCustomer(customerID string, params *stripe.CustomerParams) (*stripe.Customer, error) {
	cust, err := customer.Update(customerID, params)
	if err != nil {
		s.logger.Error("Failed to update customer",
			zap.String("customer_id", customerID),
			zap.Error(err),
		)
		return nil, fmt.Errorf("failed to update customer: %w", err)
	}

	return cust, nil
}

// Payment Method operations

// AttachPaymentMethod attaches a payment method to a customer
func (s *StripeClient) AttachPaymentMethod(paymentMethodID, customerID string) (*stripe.PaymentMethod, error) {
	params := &stripe.PaymentMethodAttachParams{
		Customer: stripe.String(customerID),
	}

	pm, err := paymentmethod.Attach(paymentMethodID, params)
	if err != nil {
		s.logger.Error("Failed to attach payment method",
			zap.String("payment_method_id", paymentMethodID),
			zap.String("customer_id", customerID),
			zap.Error(err),
		)
		return nil, fmt.Errorf("failed to attach payment method: %w", err)
	}

	return pm, nil
}

// DetachPaymentMethod detaches a payment method from a customer
func (s *StripeClient) DetachPaymentMethod(paymentMethodID string) (*stripe.PaymentMethod, error) {
	pm, err := paymentmethod.Detach(paymentMethodID, nil)
	if err != nil {
		s.logger.Error("Failed to detach payment method",
			zap.String("payment_method_id", paymentMethodID),
			zap.Error(err),
		)
		return nil, fmt.Errorf("failed to detach payment method: %w", err)
	}

	return pm, nil
}

// Connected Account operations (for sellers)

// CreateConnectedAccount creates a connected account for a seller
func (s *StripeClient) CreateConnectedAccount(email string, metadata map[string]string) (*stripe.Account, error) {
	params := &stripe.AccountParams{
		Type:     stripe.String("express"),
		Email:    stripe.String(email),
		Metadata: metadata,
	}

	acc, err := account.New(params)
	if err != nil {
		s.logger.Error("Failed to create connected account",
			zap.String("email", email),
			zap.Error(err),
		)
		return nil, fmt.Errorf("failed to create connected account: %w", err)
	}

	s.logger.Info("Connected account created",
		zap.String("account_id", acc.ID),
		zap.String("email", email),
	)

	return acc, nil
}

// GetConnectedAccount retrieves a connected account
func (s *StripeClient) GetConnectedAccount(accountID string) (*stripe.Account, error) {
	acc, err := account.GetByID(accountID, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to get connected account: %w", err)
	}

	return acc, nil
}

// Transfer operations

// CreateTransfer creates a transfer to a connected account
func (s *StripeClient) CreateTransfer(amountCents int64, currency, destinationAccountID string, metadata map[string]string) (*stripe.Transfer, error) {
	params := &stripe.TransferParams{
		Amount:      stripe.Int64(amountCents),
		Currency:    stripe.String(currency),
		Destination: stripe.String(destinationAccountID),
		Metadata:    metadata,
	}

	transfer, err := transfer.New(params)
	if err != nil {
		s.logger.Error("Failed to create transfer",
			zap.Int64("amount_cents", amountCents),
			zap.String("destination", destinationAccountID),
			zap.Error(err),
		)
		return nil, fmt.Errorf("failed to create transfer: %w", err)
	}

	s.logger.Info("Transfer created",
		zap.String("transfer_id", transfer.ID),
		zap.Int64("amount_cents", amountCents),
		zap.String("destination", destinationAccountID),
	)

	return transfer, nil
}

// Refund operations

// CreateRefund creates a refund for a charge
func (s *StripeClient) CreateRefund(chargeID string, amountCents *int64, reason string, metadata map[string]string) (*stripe.Refund, error) {
	params := &stripe.RefundParams{
		Charge:   stripe.String(chargeID),
		Reason:   stripe.String(reason),
		Metadata: metadata,
	}

	if amountCents != nil {
		params.Amount = stripe.Int64(*amountCents)
	}

	ref, err := refund.New(params)
	if err != nil {
		s.logger.Error("Failed to create refund",
			zap.String("charge_id", chargeID),
			zap.Error(err),
		)
		return nil, fmt.Errorf("failed to create refund: %w", err)
	}

	s.logger.Info("Refund created",
		zap.String("refund_id", ref.ID),
		zap.String("charge_id", chargeID),
	)

	return ref, nil
}

// Marketplace-specific operations

// ProcessMarketplacePayment processes a payment with platform fee
func (s *StripeClient) ProcessMarketplacePayment(customerID string, amountCents int64, currency string, sellerAccountID string, metadata map[string]string) (*stripe.PaymentIntent, error) {
	// Calculate platform fee
	platformFeeCents := int64(float64(amountCents) * s.platformFeeRate)

	params := &stripe.PaymentIntentParams{
		Amount:               stripe.Int64(amountCents),
		Currency:             stripe.String(currency),
		Customer:             stripe.String(customerID),
		Metadata:             metadata,
		ApplicationFeeAmount: stripe.Int64(platformFeeCents),
		TransferData: &stripe.PaymentIntentTransferDataParams{
			Destination: stripe.String(sellerAccountID),
		},
		AutomaticPaymentMethods: &stripe.PaymentIntentAutomaticPaymentMethodsParams{
			Enabled: stripe.Bool(true),
		},
	}

	pi, err := paymentintent.New(params)
	if err != nil {
		s.logger.Error("Failed to create marketplace payment intent",
			zap.String("customer_id", customerID),
			zap.Int64("amount_cents", amountCents),
			zap.Int64("platform_fee_cents", platformFeeCents),
			zap.String("seller_account_id", sellerAccountID),
			zap.Error(err),
		)
		return nil, fmt.Errorf("failed to create marketplace payment intent: %w", err)
	}

	s.logger.Info("Marketplace payment intent created",
		zap.String("payment_intent_id", pi.ID),
		zap.String("customer_id", customerID),
		zap.Int64("amount_cents", amountCents),
		zap.Int64("platform_fee_cents", platformFeeCents),
		zap.String("seller_account_id", sellerAccountID),
	)

	return pi, nil
}

// GetPlatformFeeRate returns the platform fee rate
func (s *StripeClient) GetPlatformFeeRate() float64 {
	return s.platformFeeRate
}

// GetWebhookSecret returns the webhook secret
func (s *StripeClient) GetWebhookSecret() string {
	return s.webhookSecret
}

// Utility methods

// CalculatePlatformFee calculates the platform fee for an amount
func (s *StripeClient) CalculatePlatformFee(amountCents int64) int64 {
	return int64(float64(amountCents) * s.platformFeeRate)
}

// CalculateSellerAmount calculates the seller amount after platform fee
func (s *StripeClient) CalculateSellerAmount(amountCents int64) int64 {
	platformFee := s.CalculatePlatformFee(amountCents)
	return amountCents - platformFee
}
