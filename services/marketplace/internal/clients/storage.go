// Package clients provides Google Cloud Storage client for pattern file management
package clients

import (
	"context"
	"fmt"
	"io"
	"time"

	"cloud.google.com/go/storage"
	"go.uber.org/zap"
	"google.golang.org/api/option"

	"github.com/episteme/marketplace/internal/config"
	"github.com/episteme/marketplace/pkg/logger"
)

// StorageClient wraps Google Cloud Storage client with marketplace-specific methods
type StorageClient struct {
	client     *storage.Client
	bucketName string
	logger     *zap.Logger
}

// NewStorageClient creates a new Google Cloud Storage client
func NewStorageClient(cfg config.GCSConfig) (*StorageClient, error) {
	ctx := context.Background()

	// Create storage client
	client, err := storage.NewClient(ctx, option.WithScopes(storage.ScopeFullControl))
	if err != nil {
		return nil, fmt.Errorf("failed to create storage client: %w", err)
	}

	return &StorageClient{
		client:     client,
		bucketName: cfg.BucketName,
		logger:     logger.NewLogger(),
	}, nil
}

// Close closes the storage client
func (s *StorageClient) Close() error {
	return s.client.Close()
}

// Client returns the underlying storage client
func (s *StorageClient) Client() *storage.Client {
	return s.client
}

// Pattern file operations

// UploadPatternFile uploads a pattern file to storage
func (s *StorageClient) UploadPatternFile(ctx context.Context, patternID string, filename string, content io.Reader, contentType string) (string, error) {
	objectName := fmt.Sprintf("patterns/%s/%s", patternID, filename)

	obj := s.client.Bucket(s.bucketName).Object(objectName)
	writer := obj.NewWriter(ctx)

	// Set content type and metadata
	writer.ContentType = contentType
	writer.Metadata = map[string]string{
		"pattern_id":  patternID,
		"uploaded_at": time.Now().UTC().Format(time.RFC3339),
	}

	// Copy content to storage
	if _, err := io.Copy(writer, content); err != nil {
		writer.Close()
		s.logger.Error("Failed to upload pattern file",
			zap.String("pattern_id", patternID),
			zap.String("filename", filename),
			zap.Error(err),
		)
		return "", fmt.Errorf("failed to upload pattern file: %w", err)
	}

	if err := writer.Close(); err != nil {
		s.logger.Error("Failed to close storage writer",
			zap.String("pattern_id", patternID),
			zap.String("filename", filename),
			zap.Error(err),
		)
		return "", fmt.Errorf("failed to close storage writer: %w", err)
	}

	// Generate public URL
	url := fmt.Sprintf("gs://%s/%s", s.bucketName, objectName)

	s.logger.Info("Pattern file uploaded",
		zap.String("pattern_id", patternID),
		zap.String("filename", filename),
		zap.String("url", url),
	)

	return url, nil
}

// DownloadPatternFile downloads a pattern file from storage
func (s *StorageClient) DownloadPatternFile(ctx context.Context, patternID string, filename string) (io.ReadCloser, error) {
	objectName := fmt.Sprintf("patterns/%s/%s", patternID, filename)

	obj := s.client.Bucket(s.bucketName).Object(objectName)
	reader, err := obj.NewReader(ctx)
	if err != nil {
		s.logger.Error("Failed to download pattern file",
			zap.String("pattern_id", patternID),
			zap.String("filename", filename),
			zap.Error(err),
		)
		return nil, fmt.Errorf("failed to download pattern file: %w", err)
	}

	s.logger.Debug("Pattern file download started",
		zap.String("pattern_id", patternID),
		zap.String("filename", filename),
	)

	return reader, nil
}

// DeletePatternFile deletes a pattern file from storage
func (s *StorageClient) DeletePatternFile(ctx context.Context, patternID string, filename string) error {
	objectName := fmt.Sprintf("patterns/%s/%s", patternID, filename)

	obj := s.client.Bucket(s.bucketName).Object(objectName)
	if err := obj.Delete(ctx); err != nil {
		s.logger.Error("Failed to delete pattern file",
			zap.String("pattern_id", patternID),
			zap.String("filename", filename),
			zap.Error(err),
		)
		return fmt.Errorf("failed to delete pattern file: %w", err)
	}

	s.logger.Info("Pattern file deleted",
		zap.String("pattern_id", patternID),
		zap.String("filename", filename),
	)

	return nil
}

// ListPatternFiles lists all files for a pattern
func (s *StorageClient) ListPatternFiles(ctx context.Context, patternID string) ([]*storage.ObjectAttrs, error) {
	prefix := fmt.Sprintf("patterns/%s/", patternID)

	query := &storage.Query{
		Prefix: prefix,
	}

	it := s.client.Bucket(s.bucketName).Objects(ctx, query)

	var objects []*storage.ObjectAttrs
	for {
		attrs, err := it.Next()
		if err == storage.ErrObjectNotExist {
			break
		}
		if err != nil {
			s.logger.Error("Failed to list pattern files",
				zap.String("pattern_id", patternID),
				zap.Error(err),
			)
			return nil, fmt.Errorf("failed to list pattern files: %w", err)
		}

		objects = append(objects, attrs)
	}

	s.logger.Debug("Pattern files listed",
		zap.String("pattern_id", patternID),
		zap.Int("count", len(objects)),
	)

	return objects, nil
}

// GetPatternFileInfo gets information about a pattern file
func (s *StorageClient) GetPatternFileInfo(ctx context.Context, patternID string, filename string) (*storage.ObjectAttrs, error) {
	objectName := fmt.Sprintf("patterns/%s/%s", patternID, filename)

	obj := s.client.Bucket(s.bucketName).Object(objectName)
	attrs, err := obj.Attrs(ctx)
	if err != nil {
		if err == storage.ErrObjectNotExist {
			return nil, fmt.Errorf("pattern file not found")
		}
		s.logger.Error("Failed to get pattern file info",
			zap.String("pattern_id", patternID),
			zap.String("filename", filename),
			zap.Error(err),
		)
		return nil, fmt.Errorf("failed to get pattern file info: %w", err)
	}

	return attrs, nil
}

// GenerateSignedURL generates a signed URL for temporary access to a pattern file
func (s *StorageClient) GenerateSignedURL(ctx context.Context, patternID string, filename string, expiration time.Duration) (string, error) {
	objectName := fmt.Sprintf("patterns/%s/%s", patternID, filename)

	opts := &storage.SignedURLOptions{
		Scheme:  storage.SigningSchemeV4,
		Method:  "GET",
		Expires: time.Now().Add(expiration),
	}

	url, err := storage.SignedURL(s.bucketName, objectName, opts)
	if err != nil {
		s.logger.Error("Failed to generate signed URL",
			zap.String("pattern_id", patternID),
			zap.String("filename", filename),
			zap.Error(err),
		)
		return "", fmt.Errorf("failed to generate signed URL: %w", err)
	}

	s.logger.Debug("Signed URL generated",
		zap.String("pattern_id", patternID),
		zap.String("filename", filename),
		zap.Duration("expiration", expiration),
	)

	return url, nil
}

// Avatar and media operations

// UploadUserAvatar uploads a user avatar
func (s *StorageClient) UploadUserAvatar(ctx context.Context, userID string, content io.Reader, contentType string) (string, error) {
	objectName := fmt.Sprintf("avatars/%s/avatar", userID)

	obj := s.client.Bucket(s.bucketName).Object(objectName)
	writer := obj.NewWriter(ctx)

	writer.ContentType = contentType
	writer.Metadata = map[string]string{
		"user_id":     userID,
		"uploaded_at": time.Now().UTC().Format(time.RFC3339),
	}

	if _, err := io.Copy(writer, content); err != nil {
		writer.Close()
		return "", fmt.Errorf("failed to upload avatar: %w", err)
	}

	if err := writer.Close(); err != nil {
		return "", fmt.Errorf("failed to close avatar writer: %w", err)
	}

	url := fmt.Sprintf("gs://%s/%s", s.bucketName, objectName)

	s.logger.Info("User avatar uploaded",
		zap.String("user_id", userID),
		zap.String("url", url),
	)

	return url, nil
}

// DeleteUserAvatar deletes a user avatar
func (s *StorageClient) DeleteUserAvatar(ctx context.Context, userID string) error {
	objectName := fmt.Sprintf("avatars/%s/avatar", userID)

	obj := s.client.Bucket(s.bucketName).Object(objectName)
	if err := obj.Delete(ctx); err != nil {
		if err == storage.ErrObjectNotExist {
			return nil // Already deleted
		}
		return fmt.Errorf("failed to delete avatar: %w", err)
	}

	s.logger.Info("User avatar deleted",
		zap.String("user_id", userID),
	)

	return nil
}

// Backup and maintenance operations

// BackupPattern creates a backup of all pattern files
func (s *StorageClient) BackupPattern(ctx context.Context, patternID string, backupBucket string) error {
	// List all pattern files
	files, err := s.ListPatternFiles(ctx, patternID)
	if err != nil {
		return fmt.Errorf("failed to list pattern files for backup: %w", err)
	}

	// Copy each file to backup bucket
	for _, file := range files {
		srcObj := s.client.Bucket(s.bucketName).Object(file.Name)
		dstObj := s.client.Bucket(backupBucket).Object(file.Name)

		if _, err := dstObj.CopierFrom(srcObj).Run(ctx); err != nil {
			s.logger.Error("Failed to backup pattern file",
				zap.String("pattern_id", patternID),
				zap.String("filename", file.Name),
				zap.Error(err),
			)
			return fmt.Errorf("failed to backup pattern file %s: %w", file.Name, err)
		}
	}

	s.logger.Info("Pattern backup completed",
		zap.String("pattern_id", patternID),
		zap.String("backup_bucket", backupBucket),
		zap.Int("files_backed_up", len(files)),
	)

	return nil
}

// Health check

// HealthCheck performs a health check on the storage connection
func (s *StorageClient) HealthCheck(ctx context.Context) error {
	// Try to get bucket attributes
	bucket := s.client.Bucket(s.bucketName)
	_, err := bucket.Attrs(ctx)
	if err != nil {
		return fmt.Errorf("storage health check failed: %w", err)
	}

	return nil
}

// GetBucketInfo returns information about the storage bucket
func (s *StorageClient) GetBucketInfo(ctx context.Context) (map[string]interface{}, error) {
	bucket := s.client.Bucket(s.bucketName)
	attrs, err := bucket.Attrs(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get bucket info: %w", err)
	}

	return map[string]interface{}{
		"name":          attrs.Name,
		"location":      attrs.Location,
		"storage_class": attrs.StorageClass,
		"created":       attrs.Created,
	}, nil
}
