// Package models provides search-related data structures for the marketplace service
package models

import (
	"time"
)

// SearchRequest represents a search query request
type SearchRequest struct {
	Query         string                 `json:"query" form:"q"`
	EntityType    string                 `json:"entity_type" form:"entity_type"` // patterns, users, collections
	Filters       map[string]interface{} `json:"filters" form:"-"`
	Sort          string                 `json:"sort" form:"sort"`
	Page          int                    `json:"page" form:"page"`
	PageSize      int                    `json:"page_size" form:"page_size"`
	IncludeFacets bool                   `json:"include_facets" form:"include_facets"`
	UserID        *string                `json:"-"` // For personalized results
}

// SearchResponse represents a search query response
type SearchResponse struct {
	Results    []interface{} `json:"results"`
	Facets     *SearchFacets `json:"facets,omitempty"`
	Pagination Pagination    `json:"pagination"`
	SearchTime int64         `json:"search_time_ms"`
	Total      int64         `json:"total"`
	Query      string        `json:"query"`
}

// SearchFacets represents faceted search results
type SearchFacets struct {
	Categories  []FacetValue `json:"categories,omitempty"`
	Languages   []FacetValue `json:"languages,omitempty"`
	Authors     []FacetValue `json:"authors,omitempty"`
	PriceRanges []FacetValue `json:"price_ranges,omitempty"`
	Ratings     []FacetValue `json:"ratings,omitempty"`
	Tags        []FacetValue `json:"tags,omitempty"`
}

// FacetValue represents a single facet value with count
type FacetValue struct {
	Value string `json:"value"`
	Count int64  `json:"count"`
	Label string `json:"label,omitempty"`
}

// SearchAnalytics represents search analytics data
type SearchAnalytics struct {
	TotalSearches      int64            `json:"total_searches"`
	UniqueUsers        int64            `json:"unique_users"`
	AverageResultCount float64          `json:"average_result_count"`
	TopQueries         []*SearchTerm    `json:"top_queries"`
	SearchesByCategory map[string]int64 `json:"searches_by_category"`
	SearchesByLanguage map[string]int64 `json:"searches_by_language"`
	ConversionRate     float64          `json:"conversion_rate"` // Searches that led to purchases
	Period             string           `json:"period"`
}

// SearchTerm represents a search term with analytics
type SearchTerm struct {
	Term           string    `json:"term"`
	Count          int64     `json:"count"`
	ResultCount    int64     `json:"result_count"`
	ClickRate      float64   `json:"click_rate"`
	ConversionRate float64   `json:"conversion_rate"`
	LastSearched   time.Time `json:"last_searched"`
}

// RecommendationRequest represents a recommendation request
type RecommendationRequest struct {
	UserID    string                 `json:"user_id"`
	PatternID *string                `json:"pattern_id,omitempty"` // For similar patterns
	Category  *string                `json:"category,omitempty"`
	Language  *string                `json:"language,omitempty"`
	Limit     int                    `json:"limit"`
	Filters   map[string]interface{} `json:"filters,omitempty"`
	Context   string                 `json:"context"` // browse, purchase, view, etc.
}

// RecommendationResponse represents a recommendation response
type RecommendationResponse struct {
	Patterns    []*Pattern `json:"patterns"`
	Algorithm   string     `json:"algorithm"`
	Confidence  float64    `json:"confidence"`
	Explanation string     `json:"explanation,omitempty"`
	GeneratedAt time.Time  `json:"generated_at"`
}

// TrendingRequest represents a trending patterns request
type TrendingRequest struct {
	Timeframe string  `json:"timeframe"` // 1h, 24h, 7d, 30d
	Category  *string `json:"category,omitempty"`
	Language  *string `json:"language,omitempty"`
	Limit     int     `json:"limit"`
}

// TrendingResponse represents a trending patterns response
type TrendingResponse struct {
	Patterns    []*Pattern `json:"patterns"`
	Timeframe   string     `json:"timeframe"`
	GeneratedAt time.Time  `json:"generated_at"`
	Algorithm   string     `json:"algorithm"`
}

// SearchIndex represents a search index entry
type SearchIndex struct {
	ID          string                 `json:"id"`
	Type        string                 `json:"type"` // pattern, user, collection
	Title       string                 `json:"title"`
	Description string                 `json:"description"`
	Content     string                 `json:"content"`
	Tags        []string               `json:"tags"`
	Category    string                 `json:"category"`
	Language    string                 `json:"language"`
	Author      string                 `json:"author"`
	AuthorID    string                 `json:"author_id"`
	Price       float64                `json:"price"`
	Rating      float64                `json:"rating"`
	Downloads   int64                  `json:"downloads"`
	Views       int64                  `json:"views"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
	Status      string                 `json:"status"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// SearchFilters represents common search filters
type SearchFilters struct {
	Category   *string    `json:"category" form:"category"`
	Language   *string    `json:"language" form:"language"`
	Author     *string    `json:"author" form:"author"`
	Tags       []string   `json:"tags" form:"tags"`
	MinPrice   *float64   `json:"min_price" form:"min_price"`
	MaxPrice   *float64   `json:"max_price" form:"max_price"`
	MinRating  *float64   `json:"min_rating" form:"min_rating"`
	PriceRange *string    `json:"price_range" form:"price_range"` // free, paid, 0-10, 10-50, 50+
	Status     *string    `json:"status" form:"status"`
	DateFrom   *time.Time `json:"date_from" form:"date_from"`
	DateTo     *time.Time `json:"date_to" form:"date_to"`
}

// SearchQuery represents a logged search query
type SearchQuery struct {
	ID               string                 `json:"id"`
	UserID           *string                `json:"user_id"`
	Query            string                 `json:"query"`
	Filters          map[string]interface{} `json:"filters"`
	ResultCount      int64                  `json:"result_count"`
	ClickedResults   []string               `json:"clicked_results"`
	ConvertedResults []string               `json:"converted_results"` // Patterns that were purchased
	SearchTime       int64                  `json:"search_time_ms"`
	IPAddress        string                 `json:"ip_address"`
	UserAgent        string                 `json:"user_agent"`
	CreatedAt        time.Time              `json:"created_at"`
}

// AutocompleteRequest represents an autocomplete request
type AutocompleteRequest struct {
	Query string `json:"query" form:"q"`
	Type  string `json:"type" form:"type"` // patterns, users, tags
	Limit int    `json:"limit" form:"limit"`
}

// AutocompleteResponse represents an autocomplete response
type AutocompleteResponse struct {
	Suggestions []AutocompleteSuggestion `json:"suggestions"`
	Query       string                   `json:"query"`
}

// AutocompleteSuggestion represents a single autocomplete suggestion
type AutocompleteSuggestion struct {
	Text     string                 `json:"text"`
	Type     string                 `json:"type"`
	Score    float64                `json:"score"`
	Metadata map[string]interface{} `json:"metadata,omitempty"`
}

// NewSearchRequest creates a new search request with defaults
func NewSearchRequest(query string) *SearchRequest {
	return &SearchRequest{
		Query:         query,
		EntityType:    "patterns",
		Filters:       make(map[string]interface{}),
		Sort:          "relevance",
		Page:          1,
		PageSize:      20,
		IncludeFacets: false,
	}
}

// NewSearchResponse creates a new search response
func NewSearchResponse(query string) *SearchResponse {
	return &SearchResponse{
		Results:    make([]interface{}, 0),
		Pagination: Pagination{},
		SearchTime: 0,
		Total:      0,
		Query:      query,
	}
}

// NewSearchIndex creates a new search index entry from a pattern
func NewSearchIndexFromPattern(pattern *Pattern) *SearchIndex {
	authorName := pattern.AuthorID
	if pattern.Author != nil && pattern.Author.DisplayName != "" {
		authorName = pattern.Author.DisplayName
	}

	rating := 0.0
	if pattern.Rating != nil {
		rating = *pattern.Rating
	}

	return &SearchIndex{
		ID:          pattern.ID,
		Type:        "pattern",
		Title:       pattern.Name,
		Description: pattern.Description,
		Content:     pattern.Description, // Could include code content
		Tags:        pattern.Tags,
		Category:    pattern.Category,
		Language:    pattern.Language,
		Author:      authorName,
		AuthorID:    pattern.AuthorID,
		Price:       float64(pattern.PriceCents) / 100.0, // Convert cents to dollars
		Rating:      rating,
		Downloads:   pattern.Downloads,
		Views:       0, // TODO: Add view count tracking
		CreatedAt:   pattern.CreatedAt,
		UpdatedAt:   pattern.UpdatedAt,
		Status:      string(pattern.Status),
		Metadata:    make(map[string]interface{}),
	}
}
