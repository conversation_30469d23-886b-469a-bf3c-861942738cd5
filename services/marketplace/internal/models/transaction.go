// Package models provides transaction models for the marketplace service
package models

import (
	"time"

	"github.com/google/uuid"
)

// Transaction represents a marketplace transaction
type Transaction struct {
	ID                     string                 `json:"id" spanner:"transaction_id"`
	PatternID              string                 `json:"pattern_id" spanner:"pattern_id"`
	BuyerID                string                 `json:"buyer_id" spanner:"buyer_id"`
	SellerID               string                 `json:"seller_id" spanner:"seller_id"`
	AmountCents            int64                  `json:"amount_cents" spanner:"amount_cents"`
	Currency               string                 `json:"currency" spanner:"currency"`
	Status                 TransactionStatus      `json:"status" spanner:"status"`
	StripePaymentIntentID  *string                `json:"stripe_payment_intent_id" spanner:"stripe_payment_intent_id"`
	StripeChargeID         *string                `json:"stripe_charge_id" spanner:"stripe_charge_id"`
	PlatformFeeCents       int64                  `json:"platform_fee_cents" spanner:"platform_fee_cents"`
	SellerAmountCents      int64                  `json:"seller_amount_cents" spanner:"seller_amount_cents"`
	Metadata               map[string]interface{} `json:"metadata" spanner:"metadata"`
	CreatedAt              time.Time              `json:"created_at" spanner:"created_at"`
	CompletedAt            *time.Time             `json:"completed_at" spanner:"completed_at"`
	
	// Computed fields
	Pattern                *Pattern               `json:"pattern,omitempty" spanner:"-"`
	Buyer                  *User                  `json:"buyer,omitempty" spanner:"-"`
	Seller                 *User                  `json:"seller,omitempty" spanner:"-"`
}

// TransactionStatus represents the status of a transaction
type TransactionStatus string

const (
	TransactionStatusPending   TransactionStatus = "pending"
	TransactionStatusCompleted TransactionStatus = "completed"
	TransactionStatusFailed    TransactionStatus = "failed"
	TransactionStatusRefunded  TransactionStatus = "refunded"
	TransactionStatusCancelled TransactionStatus = "cancelled"
)

// PurchaseRequest represents a request to purchase a pattern
type PurchaseRequest struct {
	PaymentMethodID string                 `json:"payment_method_id" validate:"required"`
	LicenseType     string                 `json:"license_type" validate:"required,oneof=individual team enterprise"`
	Metadata        map[string]interface{} `json:"metadata"`
}

// RefundRequest represents a request to refund a transaction
type RefundRequest struct {
	Reason   string `json:"reason" validate:"required,min=10,max=500"`
	Metadata map[string]interface{} `json:"metadata"`
}

// ListTransactionsParams represents parameters for listing transactions
type ListTransactionsParams struct {
	Page       int               `json:"page" validate:"min=1"`
	PageSize   int               `json:"page_size" validate:"min=1,max=100"`
	Sort       string            `json:"sort" validate:"omitempty,oneof=created_at completed_at amount"`
	Order      string            `json:"order" validate:"omitempty,oneof=asc desc"`
	Status     TransactionStatus `json:"status,omitempty"`
	BuyerID    string            `json:"buyer_id,omitempty"`
	SellerID   string            `json:"seller_id,omitempty"`
	PatternID  string            `json:"pattern_id,omitempty"`
	StartDate  *time.Time        `json:"start_date,omitempty"`
	EndDate    *time.Time        `json:"end_date,omitempty"`
}

// ListTransactionsResponse represents the response for listing transactions
type ListTransactionsResponse struct {
	Transactions []*Transaction `json:"transactions"`
	Pagination   Pagination     `json:"pagination"`
	Summary      *TransactionSummary `json:"summary,omitempty"`
}

// TransactionSummary provides summary statistics for transactions
type TransactionSummary struct {
	TotalCount        int64   `json:"total_count"`
	TotalAmountCents  int64   `json:"total_amount_cents"`
	TotalFeeCents     int64   `json:"total_fee_cents"`
	AverageAmountCents int64  `json:"average_amount_cents"`
	CompletionRate    float64 `json:"completion_rate"`
}

// Review represents a pattern review
type Review struct {
	ID               string                 `json:"id" spanner:"review_id"`
	PatternID        string                 `json:"pattern_id" spanner:"pattern_id"`
	UserID           string                 `json:"user_id" spanner:"user_id"`
	Rating           int                    `json:"rating" spanner:"rating" validate:"min=1,max=5"`
	Title            *string                `json:"title" spanner:"title"`
	Comment          *string                `json:"comment" spanner:"comment"`
	HelpfulCount     int64                  `json:"helpful_count" spanner:"helpful_count"`
	VerifiedPurchase bool                   `json:"verified_purchase" spanner:"verified_purchase"`
	CreatedAt        time.Time              `json:"created_at" spanner:"created_at"`
	UpdatedAt        time.Time              `json:"updated_at" spanner:"updated_at"`
	
	// Computed fields
	User             *User                  `json:"user,omitempty" spanner:"-"`
	IsHelpful        bool                   `json:"is_helpful,omitempty" spanner:"-"`
}

// CreateReviewRequest represents a request to create a review
type CreateReviewRequest struct {
	Rating  int     `json:"rating" validate:"required,min=1,max=5"`
	Title   *string `json:"title,omitempty" validate:"omitempty,min=5,max=100"`
	Comment *string `json:"comment,omitempty" validate:"omitempty,min=10,max=2000"`
}

// UpdateReviewRequest represents a request to update a review
type UpdateReviewRequest struct {
	Rating  *int    `json:"rating,omitempty" validate:"omitempty,min=1,max=5"`
	Title   *string `json:"title,omitempty" validate:"omitempty,min=5,max=100"`
	Comment *string `json:"comment,omitempty" validate:"omitempty,min=10,max=2000"`
}

// NewTransaction creates a new transaction
func NewTransaction(patternID, buyerID, sellerID string, amountCents int64, currency string, platformFeeRate float64) *Transaction {
	now := time.Now().UTC()
	platformFeeCents := int64(float64(amountCents) * platformFeeRate)
	sellerAmountCents := amountCents - platformFeeCents
	
	return &Transaction{
		ID:                uuid.New().String(),
		PatternID:         patternID,
		BuyerID:           buyerID,
		SellerID:          sellerID,
		AmountCents:       amountCents,
		Currency:          currency,
		Status:            TransactionStatusPending,
		PlatformFeeCents:  platformFeeCents,
		SellerAmountCents: sellerAmountCents,
		CreatedAt:         now,
	}
}

// NewReview creates a new review
func NewReview(patternID, userID string, req *CreateReviewRequest, verifiedPurchase bool) *Review {
	now := time.Now().UTC()
	
	return &Review{
		ID:               uuid.New().String(),
		PatternID:        patternID,
		UserID:           userID,
		Rating:           req.Rating,
		Title:            req.Title,
		Comment:          req.Comment,
		HelpfulCount:     0,
		VerifiedPurchase: verifiedPurchase,
		CreatedAt:        now,
		UpdatedAt:        now,
	}
}

// IsCompleted returns true if the transaction is completed
func (t *Transaction) IsCompleted() bool {
	return t.Status == TransactionStatusCompleted
}

// IsPending returns true if the transaction is pending
func (t *Transaction) IsPending() bool {
	return t.Status == TransactionStatusPending
}

// GetAmountInDollars returns the amount in dollars
func (t *Transaction) GetAmountInDollars() float64 {
	return float64(t.AmountCents) / 100.0
}

// GetPlatformFeeInDollars returns the platform fee in dollars
func (t *Transaction) GetPlatformFeeInDollars() float64 {
	return float64(t.PlatformFeeCents) / 100.0
}

// GetSellerAmountInDollars returns the seller amount in dollars
func (t *Transaction) GetSellerAmountInDollars() float64 {
	return float64(t.SellerAmountCents) / 100.0
}

// Complete marks the transaction as completed
func (t *Transaction) Complete() {
	t.Status = TransactionStatusCompleted
	now := time.Now().UTC()
	t.CompletedAt = &now
}

// Fail marks the transaction as failed
func (t *Transaction) Fail() {
	t.Status = TransactionStatusFailed
}

// Refund marks the transaction as refunded
func (t *Transaction) Refund() {
	t.Status = TransactionStatusRefunded
}

// Update updates the review with the provided request
func (r *Review) Update(req *UpdateReviewRequest) {
	if req.Rating != nil {
		r.Rating = *req.Rating
	}
	if req.Title != nil {
		r.Title = req.Title
	}
	if req.Comment != nil {
		r.Comment = req.Comment
	}
	
	r.UpdatedAt = time.Now().UTC()
}
