// Package models provides data models for the marketplace service
// Following Context Engineering standards with comprehensive validation
package models

import (
	"time"

	"github.com/google/uuid"
)

// <PERSON><PERSON> represents a code pattern in the marketplace
type Pattern struct {
	ID                   string                 `json:"id" spanner:"pattern_id"`
	Name                 string                 `json:"name" spanner:"name" validate:"required,min=3,max=255"`
	Description          string                 `json:"description" spanner:"description" validate:"required,min=10,max=2000"`
	Category             string                 `json:"category" spanner:"category" validate:"required,oneof=architecture design security performance testing"`
	Language             string                 `json:"language" spanner:"language" validate:"required"`
	AuthorID             string                 `json:"author_id" spanner:"author_id"`
	PriceCents           int64                  `json:"price_cents" spanner:"price_cents" validate:"min=0"`
	Currency             string                 `json:"currency" spanner:"currency" validate:"required,len=3"`
	LicenseType          string                 `json:"license_type" spanner:"license_type" validate:"required,oneof=individual team enterprise open_source"`
	Version              string                 `json:"version" spanner:"version" validate:"required,semver"`
	Downloads            int64                  `json:"downloads" spanner:"downloads"`
	Rating               *float64               `json:"rating" spanner:"rating"`
	RatingCount          int64                  `json:"rating_count" spanner:"rating_count"`
	Status               PatternStatus          `json:"status" spanner:"status"`
	ValidationStatus     *ValidationStatus      `json:"validation_status" spanner:"validation_status"`
	ValidationScore      *float64               `json:"validation_score" spanner:"validation_score"`
	PatternHash          string                 `json:"pattern_hash" spanner:"pattern_hash"`
	StorageURL           string                 `json:"storage_url" spanner:"storage_url"`
	Metadata             map[string]interface{} `json:"metadata" spanner:"metadata"`
	Tags                 []string               `json:"tags" spanner:"tags" validate:"max=10"`
	CreatedAt            time.Time              `json:"created_at" spanner:"created_at"`
	UpdatedAt            time.Time              `json:"updated_at" spanner:"updated_at"`
	PublishedAt          *time.Time             `json:"published_at" spanner:"published_at"`
	
	// Computed fields (not stored in database)
	Author               *User                  `json:"author,omitempty" spanner:"-"`
	CanAccess            bool                   `json:"can_access,omitempty" spanner:"-"`
	IsPurchased          bool                   `json:"is_purchased,omitempty" spanner:"-"`
}

// PatternStatus represents the status of a pattern
type PatternStatus string

const (
	PatternStatusDraft          PatternStatus = "draft"
	PatternStatusPendingReview  PatternStatus = "pending_review"
	PatternStatusPublished      PatternStatus = "published"
	PatternStatusArchived       PatternStatus = "archived"
	PatternStatusSuspended      PatternStatus = "suspended"
)

// ValidationStatus represents the validation status of a pattern
type ValidationStatus string

const (
	ValidationStatusPending ValidationStatus = "pending"
	ValidationStatusPassed  ValidationStatus = "passed"
	ValidationStatusFailed  ValidationStatus = "failed"
)

// CreatePatternRequest represents a request to create a new pattern
type CreatePatternRequest struct {
	Name         string                 `json:"name" validate:"required,min=3,max=255"`
	Description  string                 `json:"description" validate:"required,min=10,max=2000"`
	Category     string                 `json:"category" validate:"required,oneof=architecture design security performance testing"`
	Language     string                 `json:"language" validate:"required"`
	PriceCents   int64                  `json:"price_cents" validate:"min=0,max=100000000"` // Max $1M
	Currency     string                 `json:"currency" validate:"required,len=3"`
	LicenseType  string                 `json:"license_type" validate:"required,oneof=individual team enterprise open_source"`
	Version      string                 `json:"version" validate:"required,semver"`
	PatternData  string                 `json:"pattern_data" validate:"required,min=50"`
	Tags         []string               `json:"tags" validate:"max=10,dive,min=2,max=30"`
	Metadata     map[string]interface{} `json:"metadata"`
}

// UpdatePatternRequest represents a request to update a pattern
type UpdatePatternRequest struct {
	Name        *string                `json:"name,omitempty" validate:"omitempty,min=3,max=255"`
	Description *string                `json:"description,omitempty" validate:"omitempty,min=10,max=2000"`
	Category    *string                `json:"category,omitempty" validate:"omitempty,oneof=architecture design security performance testing"`
	PriceCents  *int64                 `json:"price_cents,omitempty" validate:"omitempty,min=0,max=100000000"`
	LicenseType *string                `json:"license_type,omitempty" validate:"omitempty,oneof=individual team enterprise open_source"`
	Tags        []string               `json:"tags,omitempty" validate:"omitempty,max=10,dive,min=2,max=30"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// ListPatternsParams represents parameters for listing patterns
type ListPatternsParams struct {
	Page       int      `json:"page" validate:"min=1"`
	PageSize   int      `json:"page_size" validate:"min=1,max=100"`
	Sort       string   `json:"sort" validate:"omitempty,oneof=created_at updated_at downloads rating price name"`
	Order      string   `json:"order" validate:"omitempty,oneof=asc desc"`
	Category   string   `json:"category,omitempty"`
	Language   string   `json:"language,omitempty"`
	Tags       []string `json:"tags,omitempty"`
	MinPrice   *int64   `json:"min_price,omitempty" validate:"omitempty,min=0"`
	MaxPrice   *int64   `json:"max_price,omitempty" validate:"omitempty,min=0"`
	MinRating  *float64 `json:"min_rating,omitempty" validate:"omitempty,min=0,max=5"`
	AuthorID   string   `json:"author_id,omitempty"`
	Status     string   `json:"status,omitempty"`
	Search     string   `json:"search,omitempty" validate:"omitempty,min=2,max=100"`
}

// ListPatternsResponse represents the response for listing patterns
type ListPatternsResponse struct {
	Patterns   []*Pattern `json:"patterns"`
	Pagination Pagination `json:"pagination"`
}

// Pagination represents pagination information
type Pagination struct {
	Page       int   `json:"page"`
	PageSize   int   `json:"page_size"`
	Total      int64 `json:"total"`
	TotalPages int   `json:"total_pages"`
	HasNext    bool  `json:"has_next"`
	HasPrev    bool  `json:"has_prev"`
}

// PatternValidationResult represents the result of pattern validation
type PatternValidationResult struct {
	IsValid      bool                   `json:"is_valid"`
	Score        float64                `json:"score"`
	Issues       []ValidationIssue      `json:"issues"`
	Metrics      map[string]float64     `json:"metrics"`
	Suggestions  []string               `json:"suggestions"`
	ProcessedAt  time.Time              `json:"processed_at"`
}

// ValidationIssue represents a validation issue found in a pattern
type ValidationIssue struct {
	Type        string `json:"type"`
	Severity    string `json:"severity"`
	Message     string `json:"message"`
	Line        *int   `json:"line,omitempty"`
	Column      *int   `json:"column,omitempty"`
	Suggestion  string `json:"suggestion,omitempty"`
}

// NewPattern creates a new pattern with default values
func NewPattern(req *CreatePatternRequest, authorID string) *Pattern {
	now := time.Now().UTC()
	
	return &Pattern{
		ID:               uuid.New().String(),
		Name:             req.Name,
		Description:      req.Description,
		Category:         req.Category,
		Language:         req.Language,
		AuthorID:         authorID,
		PriceCents:       req.PriceCents,
		Currency:         req.Currency,
		LicenseType:      req.LicenseType,
		Version:          req.Version,
		Downloads:        0,
		RatingCount:      0,
		Status:           PatternStatusDraft,
		ValidationStatus: nil, // Will be set after validation
		Tags:             req.Tags,
		Metadata:         req.Metadata,
		CreatedAt:        now,
		UpdatedAt:        now,
	}
}

// IsPublished returns true if the pattern is published
func (p *Pattern) IsPublished() bool {
	return p.Status == PatternStatusPublished
}

// IsFree returns true if the pattern is free
func (p *Pattern) IsFree() bool {
	return p.PriceCents == 0
}

// GetPriceInDollars returns the price in dollars
func (p *Pattern) GetPriceInDollars() float64 {
	return float64(p.PriceCents) / 100.0
}

// CanBePublished returns true if the pattern can be published
func (p *Pattern) CanBePublished() bool {
	return p.Status == PatternStatusDraft && 
		   p.ValidationStatus != nil && 
		   *p.ValidationStatus == ValidationStatusPassed
}

// Update updates the pattern with the provided request
func (p *Pattern) Update(req *UpdatePatternRequest) {
	if req.Name != nil {
		p.Name = *req.Name
	}
	if req.Description != nil {
		p.Description = *req.Description
	}
	if req.Category != nil {
		p.Category = *req.Category
	}
	if req.PriceCents != nil {
		p.PriceCents = *req.PriceCents
	}
	if req.LicenseType != nil {
		p.LicenseType = *req.LicenseType
	}
	if req.Tags != nil {
		p.Tags = req.Tags
	}
	if req.Metadata != nil {
		p.Metadata = req.Metadata
	}
	
	p.UpdatedAt = time.Now().UTC()
}
