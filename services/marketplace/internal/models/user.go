// Package models provides user models for the marketplace service
package models

import (
	"time"

	"github.com/google/uuid"
)

// User represents a marketplace user
type User struct {
	ID                string                 `json:"id" spanner:"user_id"`
	Email             string                 `json:"email" spanner:"email"`
	Username          string                 `json:"username" spanner:"username"`
	DisplayName       string                 `json:"display_name" spanner:"display_name"`
	AvatarURL         *string                `json:"avatar_url" spanner:"avatar_url"`
	Bio               *string                `json:"bio" spanner:"bio"`
	Website           *string                `json:"website" spanner:"website"`
	GitHubUsername    *string                `json:"github_username" spanner:"github_username"`
	TwitterUsername   *string                `json:"twitter_username" spanner:"twitter_username"`
	Location          *string                `json:"location" spanner:"location"`
	IsVerified        bool                   `json:"is_verified" spanner:"is_verified"`
	IsActive          bool                   `json:"is_active" spanner:"is_active"`
	Role              UserRole               `json:"role" spanner:"role"`
	Metadata          map[string]interface{} `json:"metadata" spanner:"metadata"`
	CreatedAt         time.Time              `json:"created_at" spanner:"created_at"`
	UpdatedAt         time.Time              `json:"updated_at" spanner:"updated_at"`
	LastLoginAt       *time.Time             `json:"last_login_at" spanner:"last_login_at"`
	
	// Marketplace-specific fields
	MarketplaceProfile *MarketplaceProfile   `json:"marketplace_profile,omitempty" spanner:"-"`
}

// UserRole represents the role of a user
type UserRole string

const (
	UserRoleUser      UserRole = "user"
	UserRoleModerator UserRole = "moderator"
	UserRoleAdmin     UserRole = "admin"
)

// MarketplaceProfile represents marketplace-specific user data
type MarketplaceProfile struct {
	UserID              string                 `json:"user_id" spanner:"user_id"`
	SellerStatus        SellerStatus           `json:"seller_status" spanner:"seller_status"`
	StripeAccountID     *string                `json:"stripe_account_id" spanner:"stripe_account_id"`
	TotalSales          int64                  `json:"total_sales" spanner:"total_sales"`
	TotalRevenueCents   int64                  `json:"total_revenue_cents" spanner:"total_revenue_cents"`
	TotalPurchases      int64                  `json:"total_purchases" spanner:"total_purchases"`
	ReputationScore     float64                `json:"reputation_score" spanner:"reputation_score"`
	Bio                 *string                `json:"bio" spanner:"bio"`
	Website             *string                `json:"website" spanner:"website"`
	GitHubUsername      *string                `json:"github_username" spanner:"github_username"`
	CreatedAt           time.Time              `json:"created_at" spanner:"created_at"`
	UpdatedAt           time.Time              `json:"updated_at" spanner:"updated_at"`
	
	// Computed fields
	Badges              []SellerBadge          `json:"badges,omitempty" spanner:"-"`
	AverageRating       *float64               `json:"average_rating,omitempty" spanner:"-"`
	TotalReviews        int64                  `json:"total_reviews,omitempty" spanner:"-"`
}

// SellerStatus represents the status of a seller
type SellerStatus string

const (
	SellerStatusInactive  SellerStatus = "inactive"
	SellerStatusActive    SellerStatus = "active"
	SellerStatusSuspended SellerStatus = "suspended"
	SellerStatusBanned    SellerStatus = "banned"
)

// SellerBadge represents a seller badge
type SellerBadge struct {
	Type        BadgeType `json:"type"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	EarnedAt    time.Time `json:"earned_at"`
}

// BadgeType represents the type of a seller badge
type BadgeType string

const (
	BadgeTypeVerifiedSeller   BadgeType = "verified_seller"
	BadgeTypeTopSeller        BadgeType = "top_seller"
	BadgeTypeQualityCreator   BadgeType = "quality_creator"
	BadgeTypeCommunityLeader  BadgeType = "community_leader"
	BadgeTypeEarlyAdopter     BadgeType = "early_adopter"
	BadgeTypePowerSeller      BadgeType = "power_seller"
)

// CreateUserRequest represents a request to create a new user
type CreateUserRequest struct {
	Email           string                 `json:"email" validate:"required,email"`
	Username        string                 `json:"username" validate:"required,min=3,max=30,alphanum"`
	DisplayName     string                 `json:"display_name" validate:"required,min=2,max=100"`
	Password        string                 `json:"password" validate:"required,min=8,max=128"`
	AvatarURL       *string                `json:"avatar_url,omitempty" validate:"omitempty,url"`
	Bio             *string                `json:"bio,omitempty" validate:"omitempty,max=500"`
	Website         *string                `json:"website,omitempty" validate:"omitempty,url"`
	GitHubUsername  *string                `json:"github_username,omitempty" validate:"omitempty,min=1,max=39"`
	TwitterUsername *string                `json:"twitter_username,omitempty" validate:"omitempty,min=1,max=15"`
	Location        *string                `json:"location,omitempty" validate:"omitempty,max=100"`
	Metadata        map[string]interface{} `json:"metadata,omitempty"`
}

// UpdateUserRequest represents a request to update a user
type UpdateUserRequest struct {
	DisplayName     *string                `json:"display_name,omitempty" validate:"omitempty,min=2,max=100"`
	AvatarURL       *string                `json:"avatar_url,omitempty" validate:"omitempty,url"`
	Bio             *string                `json:"bio,omitempty" validate:"omitempty,max=500"`
	Website         *string                `json:"website,omitempty" validate:"omitempty,url"`
	GitHubUsername  *string                `json:"github_username,omitempty" validate:"omitempty,min=1,max=39"`
	TwitterUsername *string                `json:"twitter_username,omitempty" validate:"omitempty,min=1,max=15"`
	Location        *string                `json:"location,omitempty" validate:"omitempty,max=100"`
	Metadata        map[string]interface{} `json:"metadata,omitempty"`
}

// UpdateMarketplaceProfileRequest represents a request to update marketplace profile
type UpdateMarketplaceProfileRequest struct {
	Bio            *string `json:"bio,omitempty" validate:"omitempty,max=1000"`
	Website        *string `json:"website,omitempty" validate:"omitempty,url"`
	GitHubUsername *string `json:"github_username,omitempty" validate:"omitempty,min=1,max=39"`
}

// LoginRequest represents a login request
type LoginRequest struct {
	Email    string `json:"email" validate:"required,email"`
	Password string `json:"password" validate:"required"`
}

// LoginResponse represents a login response
type LoginResponse struct {
	User         *User  `json:"user"`
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	ExpiresIn    int64  `json:"expires_in"`
}

// NewUser creates a new user with default values
func NewUser(req *CreateUserRequest) *User {
	now := time.Now().UTC()
	
	return &User{
		ID:              uuid.New().String(),
		Email:           req.Email,
		Username:        req.Username,
		DisplayName:     req.DisplayName,
		AvatarURL:       req.AvatarURL,
		Bio:             req.Bio,
		Website:         req.Website,
		GitHubUsername:  req.GitHubUsername,
		TwitterUsername: req.TwitterUsername,
		Location:        req.Location,
		IsVerified:      false,
		IsActive:        true,
		Role:            UserRoleUser,
		Metadata:        req.Metadata,
		CreatedAt:       now,
		UpdatedAt:       now,
	}
}

// NewMarketplaceProfile creates a new marketplace profile
func NewMarketplaceProfile(userID string) *MarketplaceProfile {
	now := time.Now().UTC()
	
	return &MarketplaceProfile{
		UserID:            userID,
		SellerStatus:      SellerStatusInactive,
		TotalSales:        0,
		TotalRevenueCents: 0,
		TotalPurchases:    0,
		ReputationScore:   0.0,
		CreatedAt:         now,
		UpdatedAt:         now,
	}
}

// IsAdmin returns true if the user is an admin
func (u *User) IsAdmin() bool {
	return u.Role == UserRoleAdmin
}

// IsModerator returns true if the user is a moderator or admin
func (u *User) IsModerator() bool {
	return u.Role == UserRoleModerator || u.Role == UserRoleAdmin
}

// CanSell returns true if the user can sell patterns
func (u *User) CanSell() bool {
	return u.IsActive && u.MarketplaceProfile != nil && 
		   u.MarketplaceProfile.SellerStatus == SellerStatusActive
}

// GetTotalRevenueInDollars returns the total revenue in dollars
func (mp *MarketplaceProfile) GetTotalRevenueInDollars() float64 {
	return float64(mp.TotalRevenueCents) / 100.0
}

// IsActiveSeller returns true if the user is an active seller
func (mp *MarketplaceProfile) IsActiveSeller() bool {
	return mp.SellerStatus == SellerStatusActive
}

// Update updates the user with the provided request
func (u *User) Update(req *UpdateUserRequest) {
	if req.DisplayName != nil {
		u.DisplayName = *req.DisplayName
	}
	if req.AvatarURL != nil {
		u.AvatarURL = req.AvatarURL
	}
	if req.Bio != nil {
		u.Bio = req.Bio
	}
	if req.Website != nil {
		u.Website = req.Website
	}
	if req.GitHubUsername != nil {
		u.GitHubUsername = req.GitHubUsername
	}
	if req.TwitterUsername != nil {
		u.TwitterUsername = req.TwitterUsername
	}
	if req.Location != nil {
		u.Location = req.Location
	}
	if req.Metadata != nil {
		u.Metadata = req.Metadata
	}
	
	u.UpdatedAt = time.Now().UTC()
}

// UpdateMarketplaceProfile updates the marketplace profile
func (mp *MarketplaceProfile) Update(req *UpdateMarketplaceProfileRequest) {
	if req.Bio != nil {
		mp.Bio = req.Bio
	}
	if req.Website != nil {
		mp.Website = req.Website
	}
	if req.GitHubUsername != nil {
		mp.GitHubUsername = req.GitHubUsername
	}
	
	mp.UpdatedAt = time.Now().UTC()
}
