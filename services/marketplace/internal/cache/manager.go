// Package cache provides multi-layer caching for performance optimization
package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"

	"github.com/episteme/marketplace/internal/clients"
)

// CacheLevel represents different cache levels
type CacheLevel int

const (
	CacheLevelMemory CacheLevel = iota
	CacheLevelRedis
	CacheLevelDatabase
)

// CacheEntry represents a cached item with metadata
type CacheEntry struct {
	Data      interface{} `json:"data"`
	ExpiresAt time.Time   `json:"expires_at"`
	Level     CacheLevel  `json:"level"`
	HitCount  int64       `json:"hit_count"`
	CreatedAt time.Time   `json:"created_at"`
}

// CacheStats represents cache performance statistics
type CacheStats struct {
	MemoryHits   int64   `json:"memory_hits"`
	MemoryMisses int64   `json:"memory_misses"`
	RedisHits    int64   `json:"redis_hits"`
	RedisMisses  int64   `json:"redis_misses"`
	TotalHits    int64   `json:"total_hits"`
	TotalMisses  int64   `json:"total_misses"`
	HitRatio     float64 `json:"hit_ratio"`
}

// Manager provides multi-layer caching with performance optimization
type Manager struct {
	memoryCache map[string]*CacheEntry
	memoryMutex sync.RWMutex
	redisClient *clients.RedisClient
	logger      *zap.Logger
	stats       *CacheStats
	statsMutex  sync.RWMutex

	// Configuration
	maxMemoryItems int
	defaultTTL     time.Duration
}

// NewManager creates a new cache manager
func NewManager(redisClient *clients.RedisClient, logger *zap.Logger) *Manager {
	return &Manager{
		memoryCache:    make(map[string]*CacheEntry),
		redisClient:    redisClient,
		logger:         logger,
		stats:          &CacheStats{},
		maxMemoryItems: 1000, // Configurable
		defaultTTL:     5 * time.Minute,
	}
}

// Get retrieves a value from cache with multi-layer fallback
func (m *Manager) Get(ctx context.Context, key string, dest interface{}) error {
	// Try memory cache first
	if entry, found := m.getFromMemory(key); found {
		if !entry.ExpiresAt.Before(time.Now()) {
			m.recordHit(CacheLevelMemory)
			entry.HitCount++
			return m.deserialize(entry.Data, dest)
		}
		// Remove expired entry
		m.removeFromMemory(key)
	}

	// Try Redis cache
	if err := m.redisClient.Get(ctx, key, dest); err == nil {
		m.recordHit(CacheLevelRedis)
		// Promote to memory cache for hot data
		m.setInMemory(key, dest, m.defaultTTL)
		return nil
	}

	m.recordMiss()
	return fmt.Errorf("cache miss for key: %s", key)
}

// Set stores a value in cache with TTL
func (m *Manager) Set(ctx context.Context, key string, value interface{}, ttl time.Duration) error {
	// Store in memory cache
	m.setInMemory(key, value, ttl)

	// Store in Redis cache
	if err := m.redisClient.Set(ctx, key, value, ttl); err != nil {
		m.logger.Warn("Failed to set Redis cache", zap.String("key", key), zap.Error(err))
		// Continue with memory cache only
	}

	return nil
}

// Delete removes a key from all cache levels
func (m *Manager) Delete(ctx context.Context, key string) error {
	// Remove from memory
	m.removeFromMemory(key)

	// Remove from Redis
	if err := m.redisClient.Delete(ctx, key); err != nil {
		m.logger.Warn("Failed to delete from Redis cache", zap.String("key", key), zap.Error(err))
	}

	return nil
}

// GetStats returns cache performance statistics
func (m *Manager) GetStats() CacheStats {
	m.statsMutex.RLock()
	defer m.statsMutex.RUnlock()

	stats := *m.stats
	if stats.TotalHits+stats.TotalMisses > 0 {
		stats.HitRatio = float64(stats.TotalHits) / float64(stats.TotalHits+stats.TotalMisses)
	}

	return stats
}

// ClearStats resets cache statistics
func (m *Manager) ClearStats() {
	m.statsMutex.Lock()
	defer m.statsMutex.Unlock()

	m.stats = &CacheStats{}
}

// Warm preloads frequently accessed data into cache
func (m *Manager) Warm(ctx context.Context, keys []string, loader func(string) (interface{}, error)) error {
	for _, key := range keys {
		var temp map[string]interface{}
		if err := m.Get(ctx, key, &temp); err != nil {
			// Cache miss, load data
			if data, err := loader(key); err == nil {
				m.Set(ctx, key, data, m.defaultTTL)
			}
		}
	}
	return nil
}

// Cleanup removes expired entries from memory cache
func (m *Manager) Cleanup() {
	m.memoryMutex.Lock()
	defer m.memoryMutex.Unlock()

	now := time.Now()
	for key, entry := range m.memoryCache {
		if entry.ExpiresAt.Before(now) {
			delete(m.memoryCache, key)
		}
	}

	// Enforce memory limit
	if len(m.memoryCache) > m.maxMemoryItems {
		// Remove least recently used items
		// Simple implementation: remove oldest items
		oldestTime := now
		oldestKey := ""

		for key, entry := range m.memoryCache {
			if entry.CreatedAt.Before(oldestTime) {
				oldestTime = entry.CreatedAt
				oldestKey = key
			}
		}

		if oldestKey != "" {
			delete(m.memoryCache, oldestKey)
		}
	}
}

// StartCleanupRoutine starts a background cleanup routine
func (m *Manager) StartCleanupRoutine(interval time.Duration) {
	ticker := time.NewTicker(interval)
	go func() {
		for range ticker.C {
			m.Cleanup()
		}
	}()
}

// Private methods

func (m *Manager) getFromMemory(key string) (*CacheEntry, bool) {
	m.memoryMutex.RLock()
	defer m.memoryMutex.RUnlock()

	entry, found := m.memoryCache[key]
	return entry, found
}

func (m *Manager) setInMemory(key string, value interface{}, ttl time.Duration) {
	m.memoryMutex.Lock()
	defer m.memoryMutex.Unlock()

	m.memoryCache[key] = &CacheEntry{
		Data:      value,
		ExpiresAt: time.Now().Add(ttl),
		Level:     CacheLevelMemory,
		HitCount:  0,
		CreatedAt: time.Now(),
	}
}

func (m *Manager) removeFromMemory(key string) {
	m.memoryMutex.Lock()
	defer m.memoryMutex.Unlock()

	delete(m.memoryCache, key)
}

func (m *Manager) recordHit(level CacheLevel) {
	m.statsMutex.Lock()
	defer m.statsMutex.Unlock()

	switch level {
	case CacheLevelMemory:
		m.stats.MemoryHits++
	case CacheLevelRedis:
		m.stats.RedisHits++
	}
	m.stats.TotalHits++
}

func (m *Manager) recordMiss() {
	m.statsMutex.Lock()
	defer m.statsMutex.Unlock()

	m.stats.TotalMisses++
}

func (m *Manager) deserialize(data interface{}, dest interface{}) error {
	// Simple serialization for now
	jsonData, err := json.Marshal(data)
	if err != nil {
		return err
	}
	return json.Unmarshal(jsonData, dest)
}
