// Package monitoring provides comprehensive monitoring and analytics for the marketplace
package monitoring

import (
	"context"
	"sync"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	"go.uber.org/zap"

	"github.com/episteme/marketplace/internal/models"
)

// BusinessMetrics tracks business-specific metrics
type BusinessMetrics struct {
	// Revenue metrics
	TotalRevenue     prometheus.Gauge
	RevenueByPeriod  *prometheus.GaugeVec
	TransactionValue *prometheus.HistogramVec

	// Pattern metrics
	PatternsPublished *prometheus.CounterVec
	PatternDownloads  *prometheus.CounterVec
	PatternViews      *prometheus.CounterVec
	PatternRatings    *prometheus.HistogramVec

	// User metrics
	UserRegistrations *prometheus.CounterVec
	ActiveUsers       *prometheus.GaugeVec
	UserRetention     *prometheus.GaugeVec

	// Marketplace metrics
	SearchQueries   *prometheus.CounterVec
	ConversionRate  *prometheus.GaugeVec
	CartAbandonment *prometheus.CounterVec

	// Performance metrics
	APILatency      *prometheus.HistogramVec
	DatabaseLatency *prometheus.HistogramVec
	CacheHitRate    *prometheus.GaugeVec
	ErrorRate       *prometheus.GaugeVec
}

// NewBusinessMetrics creates new business metrics
func NewBusinessMetrics() *BusinessMetrics {
	return &BusinessMetrics{
		TotalRevenue: promauto.NewGauge(prometheus.GaugeOpts{
			Name: "marketplace_total_revenue_usd",
			Help: "Total revenue in USD",
		}),
		RevenueByPeriod: promauto.NewGaugeVec(
			prometheus.GaugeOpts{
				Name: "marketplace_revenue_by_period_usd",
				Help: "Revenue by time period in USD",
			},
			[]string{"period", "category"},
		),
		TransactionValue: promauto.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "marketplace_transaction_value_usd",
				Help:    "Transaction value distribution in USD",
				Buckets: []float64{1, 5, 10, 25, 50, 100, 250, 500, 1000},
			},
			[]string{"payment_method", "category"},
		),
		PatternsPublished: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "marketplace_patterns_published_total",
				Help: "Total number of patterns published",
			},
			[]string{"category", "language", "author_tier"},
		),
		PatternDownloads: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "marketplace_pattern_downloads_total",
				Help: "Total number of pattern downloads",
			},
			[]string{"pattern_id", "category", "payment_type"},
		),
		PatternViews: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "marketplace_pattern_views_total",
				Help: "Total number of pattern views",
			},
			[]string{"pattern_id", "category", "source"},
		),
		PatternRatings: promauto.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "marketplace_pattern_ratings",
				Help:    "Pattern rating distribution",
				Buckets: []float64{1, 2, 3, 4, 5},
			},
			[]string{"pattern_id", "category"},
		),
		UserRegistrations: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "marketplace_user_registrations_total",
				Help: "Total number of user registrations",
			},
			[]string{"source", "user_type"},
		),
		ActiveUsers: promauto.NewGaugeVec(
			prometheus.GaugeOpts{
				Name: "marketplace_active_users",
				Help: "Number of active users by time period",
			},
			[]string{"period", "user_type"},
		),
		UserRetention: promauto.NewGaugeVec(
			prometheus.GaugeOpts{
				Name: "marketplace_user_retention_rate",
				Help: "User retention rate by cohort",
			},
			[]string{"cohort", "period"},
		),
		SearchQueries: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "marketplace_search_queries_total",
				Help: "Total number of search queries",
			},
			[]string{"query_type", "has_results"},
		),
		ConversionRate: promauto.NewGaugeVec(
			prometheus.GaugeOpts{
				Name: "marketplace_conversion_rate",
				Help: "Conversion rate by funnel stage",
			},
			[]string{"stage", "category"},
		),
		CartAbandonment: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "marketplace_cart_abandonment_total",
				Help: "Total number of cart abandonments",
			},
			[]string{"stage", "reason"},
		),
		APILatency: promauto.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "marketplace_api_latency_seconds",
				Help:    "API endpoint latency in seconds",
				Buckets: []float64{0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1, 2.5, 5},
			},
			[]string{"endpoint", "method", "status"},
		),
		DatabaseLatency: promauto.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "marketplace_database_latency_seconds",
				Help:    "Database operation latency in seconds",
				Buckets: []float64{0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1},
			},
			[]string{"operation", "table"},
		),
		CacheHitRate: promauto.NewGaugeVec(
			prometheus.GaugeOpts{
				Name: "marketplace_cache_hit_rate",
				Help: "Cache hit rate by cache type",
			},
			[]string{"cache_type", "operation"},
		),
		ErrorRate: promauto.NewGaugeVec(
			prometheus.GaugeOpts{
				Name: "marketplace_error_rate",
				Help: "Error rate by service and error type",
			},
			[]string{"service", "error_type"},
		),
	}
}

// MetricsCollector collects and aggregates business metrics
type MetricsCollector struct {
	metrics *BusinessMetrics
	logger  *zap.Logger

	// Internal state for calculations
	revenueData  map[string]float64
	userActivity map[string]int64
	patternStats map[string]*PatternStats
	mutex        sync.RWMutex
}

// PatternStats tracks statistics for individual patterns
type PatternStats struct {
	Views     int64   `json:"views"`
	Downloads int64   `json:"downloads"`
	Revenue   float64 `json:"revenue"`
	Rating    float64 `json:"rating"`
	Reviews   int64   `json:"reviews"`
}

// NewMetricsCollector creates a new metrics collector
func NewMetricsCollector(logger *zap.Logger) *MetricsCollector {
	return &MetricsCollector{
		metrics:      NewBusinessMetrics(),
		logger:       logger,
		revenueData:  make(map[string]float64),
		userActivity: make(map[string]int64),
		patternStats: make(map[string]*PatternStats),
	}
}

// RecordTransaction records a transaction for metrics
func (m *MetricsCollector) RecordTransaction(ctx context.Context, transaction *models.Transaction) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// Determine payment method from Stripe fields
	paymentMethod := "unknown"
	if transaction.StripePaymentIntentID != nil {
		paymentMethod = "stripe"
	}

	// Determine category from pattern if available
	category := "unknown"
	if transaction.Pattern != nil {
		category = transaction.Pattern.Category
	}

	// Record transaction value
	m.metrics.TransactionValue.WithLabelValues(
		paymentMethod,
		category,
	).Observe(float64(transaction.AmountCents) / 100.0)

	// Update total revenue
	m.revenueData["total"] += float64(transaction.AmountCents) / 100.0
	m.metrics.TotalRevenue.Set(m.revenueData["total"])

	// Update period revenue
	period := time.Now().Format("2006-01")
	periodKey := period + ":" + category
	m.revenueData[periodKey] += float64(transaction.AmountCents) / 100.0
	m.metrics.RevenueByPeriod.WithLabelValues(period, category).Set(m.revenueData[periodKey])
}

// RecordPatternActivity records pattern-related activity
func (m *MetricsCollector) RecordPatternActivity(ctx context.Context, activity *PatternActivity) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// Initialize pattern stats if not exists
	if m.patternStats[activity.PatternID] == nil {
		m.patternStats[activity.PatternID] = &PatternStats{}
	}

	stats := m.patternStats[activity.PatternID]

	switch activity.Type {
	case "view":
		stats.Views++
		m.metrics.PatternViews.WithLabelValues(
			activity.PatternID,
			activity.Category,
			activity.Source,
		).Inc()

	case "download":
		stats.Downloads++
		m.metrics.PatternDownloads.WithLabelValues(
			activity.PatternID,
			activity.Category,
			activity.PaymentType,
		).Inc()

	case "rating":
		stats.Reviews++
		// Update average rating
		stats.Rating = ((stats.Rating * float64(stats.Reviews-1)) + activity.Rating) / float64(stats.Reviews)
		m.metrics.PatternRatings.WithLabelValues(
			activity.PatternID,
			activity.Category,
		).Observe(activity.Rating)
	}
}

// RecordUserActivity records user-related activity
func (m *MetricsCollector) RecordUserActivity(ctx context.Context, activity *UserActivity) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	switch activity.Type {
	case "registration":
		m.metrics.UserRegistrations.WithLabelValues(
			activity.Source,
			activity.UserType,
		).Inc()

	case "login":
		// Track active users
		period := time.Now().Format("2006-01-02")
		key := period + ":" + activity.UserType
		m.userActivity[key]++
		m.metrics.ActiveUsers.WithLabelValues("daily", activity.UserType).Set(float64(m.userActivity[key]))
	}
}

// RecordSearchActivity records search-related metrics
func (m *MetricsCollector) RecordSearchActivity(ctx context.Context, query string, hasResults bool, queryType string) {
	hasResultsStr := "false"
	if hasResults {
		hasResultsStr = "true"
	}

	m.metrics.SearchQueries.WithLabelValues(queryType, hasResultsStr).Inc()
}

// RecordAPILatency records API endpoint latency
func (m *MetricsCollector) RecordAPILatency(endpoint, method, status string, duration time.Duration) {
	m.metrics.APILatency.WithLabelValues(endpoint, method, status).Observe(duration.Seconds())
}

// RecordDatabaseLatency records database operation latency
func (m *MetricsCollector) RecordDatabaseLatency(operation, table string, duration time.Duration) {
	m.metrics.DatabaseLatency.WithLabelValues(operation, table).Observe(duration.Seconds())
}

// UpdateCacheMetrics updates cache performance metrics
func (m *MetricsCollector) UpdateCacheMetrics(cacheType, operation string, hitRate float64) {
	m.metrics.CacheHitRate.WithLabelValues(cacheType, operation).Set(hitRate)
}

// UpdateErrorRate updates error rate metrics
func (m *MetricsCollector) UpdateErrorRate(service, errorType string, rate float64) {
	m.metrics.ErrorRate.WithLabelValues(service, errorType).Set(rate)
}

// GetMetrics returns the metrics instance
func (m *MetricsCollector) GetMetrics() *BusinessMetrics {
	return m.metrics
}

// Activity types for metrics collection

// PatternActivity represents pattern-related activity
type PatternActivity struct {
	PatternID   string    `json:"pattern_id"`
	Type        string    `json:"type"` // view, download, rating, purchase
	Category    string    `json:"category"`
	Source      string    `json:"source"`
	PaymentType string    `json:"payment_type"`
	Rating      float64   `json:"rating"`
	UserID      string    `json:"user_id"`
	Timestamp   time.Time `json:"timestamp"`
}

// UserActivity represents user-related activity
type UserActivity struct {
	UserID    string    `json:"user_id"`
	Type      string    `json:"type"` // registration, login, purchase, etc.
	Source    string    `json:"source"`
	UserType  string    `json:"user_type"`
	Timestamp time.Time `json:"timestamp"`
}
