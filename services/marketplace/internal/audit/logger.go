// Package audit provides audit logging for compliance and security monitoring
package audit

import (
	"context"
	"encoding/json"
	"time"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"

	"github.com/episteme/marketplace/internal/clients"
)

// EventType represents the type of audit event
type EventType string

const (
	EventTypeAuthentication EventType = "authentication"
	EventTypeAuthorization  EventType = "authorization"
	EventTypeTransaction    EventType = "transaction"
	EventTypePatternAccess  EventType = "pattern_access"
	EventTypeDataAccess     EventType = "data_access"
	EventTypeSecurityEvent  EventType = "security_event"
	EventTypeAdminAction    EventType = "admin_action"
	EventTypeUserAction     EventType = "user_action"
	EventTypeSystemEvent    EventType = "system_event"
)

// AuditEvent represents an audit log entry
type AuditEvent struct {
	ID              string                 `json:"id" spanner:"id"`
	Timestamp       time.Time              `json:"timestamp" spanner:"timestamp"`
	EventType       EventType              `json:"event_type" spanner:"event_type"`
	UserID          string                 `json:"user_id,omitempty" spanner:"user_id"`
	SessionID       string                 `json:"session_id,omitempty" spanner:"session_id"`
	IPAddress       string                 `json:"ip_address,omitempty" spanner:"ip_address"`
	UserAgent       string                 `json:"user_agent,omitempty" spanner:"user_agent"`
	Action          string                 `json:"action" spanner:"action"`
	Resource        string                 `json:"resource,omitempty" spanner:"resource"`
	ResourceID      string                 `json:"resource_id,omitempty" spanner:"resource_id"`
	Status          string                 `json:"status" spanner:"status"`
	Details         map[string]interface{} `json:"details,omitempty" spanner:"details"`
	RequestID       string                 `json:"request_id,omitempty" spanner:"request_id"`
	Severity        string                 `json:"severity" spanner:"severity"`
	ComplianceFlags []string               `json:"compliance_flags,omitempty" spanner:"compliance_flags"`
}

// Logger provides audit logging functionality
type Logger struct {
	spannerClient *clients.SpannerClient
	logger        *zap.Logger
	auditLogger   *zap.Logger
}

// NewLogger creates a new audit logger
func NewLogger(spannerClient *clients.SpannerClient, logger *zap.Logger) *Logger {
	// Create dedicated audit logger with structured output
	auditConfig := zap.NewProductionConfig()
	auditConfig.OutputPaths = []string{"stdout", "/var/log/marketplace/audit.log"}
	auditConfig.EncoderConfig.TimeKey = "timestamp"
	auditConfig.EncoderConfig.LevelKey = "severity"
	auditConfig.EncoderConfig.MessageKey = "message"
	auditConfig.EncoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder

	auditLogger, err := auditConfig.Build()
	if err != nil {
		logger.Error("Failed to create audit logger", zap.Error(err))
		auditLogger = logger // Fallback to main logger
	}

	return &Logger{
		spannerClient: spannerClient,
		logger:        logger,
		auditLogger:   auditLogger,
	}
}

// LogEvent logs an audit event
func (l *Logger) LogEvent(ctx context.Context, event *AuditEvent) error {
	// Set default values
	if event.ID == "" {
		event.ID = generateEventID()
	}
	if event.Timestamp.IsZero() {
		event.Timestamp = time.Now().UTC()
	}
	if event.Severity == "" {
		event.Severity = "INFO"
	}

	// Log to structured logger
	l.auditLogger.Info("Audit Event",
		zap.String("event_id", event.ID),
		zap.String("event_type", string(event.EventType)),
		zap.String("user_id", event.UserID),
		zap.String("action", event.Action),
		zap.String("resource", event.Resource),
		zap.String("status", event.Status),
		zap.String("ip_address", event.IPAddress),
		zap.String("request_id", event.RequestID),
		zap.Any("details", event.Details),
	)

	// Store in database for compliance
	return l.storeEvent(ctx, event)
}

// LogAuthentication logs authentication events
func (l *Logger) LogAuthentication(ctx context.Context, userID, action, status, ipAddress, userAgent, requestID string, details map[string]interface{}) error {
	event := &AuditEvent{
		EventType:       EventTypeAuthentication,
		UserID:          userID,
		IPAddress:       ipAddress,
		UserAgent:       userAgent,
		Action:          action,
		Status:          status,
		Details:         details,
		RequestID:       requestID,
		Severity:        getSeverityForAuthEvent(status),
		ComplianceFlags: []string{"SOX", "PCI-DSS"},
	}

	return l.LogEvent(ctx, event)
}

// LogTransaction logs payment and transaction events
func (l *Logger) LogTransaction(ctx context.Context, userID, transactionID, action, status string, amount int64, details map[string]interface{}) error {
	if details == nil {
		details = make(map[string]interface{})
	}
	details["amount_cents"] = amount

	event := &AuditEvent{
		EventType:       EventTypeTransaction,
		UserID:          userID,
		Action:          action,
		Resource:        "transaction",
		ResourceID:      transactionID,
		Status:          status,
		Details:         details,
		Severity:        getSeverityForTransactionEvent(status),
		ComplianceFlags: []string{"SOX", "PCI-DSS", "GDPR"},
	}

	return l.LogEvent(ctx, event)
}

// LogPatternAccess logs pattern access events
func (l *Logger) LogPatternAccess(ctx context.Context, userID, patternID, action, ipAddress, requestID string) error {
	event := &AuditEvent{
		EventType:       EventTypePatternAccess,
		UserID:          userID,
		IPAddress:       ipAddress,
		Action:          action,
		Resource:        "pattern",
		ResourceID:      patternID,
		Status:          "success",
		RequestID:       requestID,
		Severity:        "INFO",
		ComplianceFlags: []string{"GDPR"},
	}

	return l.LogEvent(ctx, event)
}

// LogSecurityEvent logs security-related events
func (l *Logger) LogSecurityEvent(ctx context.Context, eventType, action, status, ipAddress, userAgent, requestID string, details map[string]interface{}) error {
	event := &AuditEvent{
		EventType:       EventTypeSecurityEvent,
		IPAddress:       ipAddress,
		UserAgent:       userAgent,
		Action:          action,
		Status:          status,
		Details:         details,
		RequestID:       requestID,
		Severity:        getSeverityForSecurityEvent(status),
		ComplianceFlags: []string{"SOX", "PCI-DSS", "GDPR"},
	}

	return l.LogEvent(ctx, event)
}

// LogAdminAction logs administrative actions
func (l *Logger) LogAdminAction(ctx context.Context, adminUserID, action, resource, resourceID, status, requestID string, details map[string]interface{}) error {
	event := &AuditEvent{
		EventType:       EventTypeAdminAction,
		UserID:          adminUserID,
		Action:          action,
		Resource:        resource,
		ResourceID:      resourceID,
		Status:          status,
		Details:         details,
		RequestID:       requestID,
		Severity:        "HIGH",
		ComplianceFlags: []string{"SOX", "GDPR"},
	}

	return l.LogEvent(ctx, event)
}

// storeEvent stores the audit event in the database
func (l *Logger) storeEvent(ctx context.Context, event *AuditEvent) error {
	// Convert details to JSON string for Spanner storage
	_, err := json.Marshal(event.Details)
	if err != nil {
		l.logger.Error("Failed to marshal audit event details", zap.Error(err))
	}

	// TODO: Implement Spanner storage
	// For now, just log the event
	l.logger.Debug("Audit event stored",
		zap.String("event_id", event.ID),
		zap.String("event_type", string(event.EventType)),
	)

	return nil
}

// Helper functions

func generateEventID() string {
	return time.Now().Format("20060102150405") + "-" + randomString(12)
}

func randomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[time.Now().UnixNano()%int64(len(charset))]
	}
	return string(b)
}

func getSeverityForAuthEvent(status string) string {
	switch status {
	case "success":
		return "INFO"
	case "failed", "blocked":
		return "WARN"
	case "suspicious":
		return "HIGH"
	default:
		return "INFO"
	}
}

func getSeverityForTransactionEvent(status string) string {
	switch status {
	case "completed":
		return "INFO"
	case "failed", "disputed":
		return "WARN"
	case "fraudulent":
		return "CRITICAL"
	default:
		return "INFO"
	}
}

func getSeverityForSecurityEvent(status string) string {
	switch status {
	case "blocked", "prevented":
		return "HIGH"
	case "detected":
		return "CRITICAL"
	default:
		return "WARN"
	}
}
