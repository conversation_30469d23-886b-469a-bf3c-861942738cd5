// Package services provides caching service for performance optimization
package services

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"

	"github.com/episteme/marketplace/internal/cache"
	"github.com/episteme/marketplace/internal/clients"
	"github.com/episteme/marketplace/internal/models"
)

// CacheService provides caching functionality for marketplace data
type CacheService interface {
	// Pattern caching
	GetPattern(ctx context.Context, id string) (*models.Pattern, error)
	SetPattern(ctx context.Context, pattern *models.Pattern) error
	InvalidatePattern(ctx context.Context, id string) error
	
	// User caching
	GetUser(ctx context.Context, id string) (*models.User, error)
	SetUser(ctx context.Context, user *models.User) error
	InvalidateUser(ctx context.Context, id string) error
	
	// Search result caching
	GetSearchResults(ctx context.Context, query string, filters map[string]interface{}) ([]models.Pattern, error)
	SetSearchResults(ctx context.Context, query string, filters map[string]interface{}, results []models.Pattern) error
	
	// Popular patterns caching
	GetPopularPatterns(ctx context.Context, limit int) ([]models.Pattern, error)
	SetPopularPatterns(ctx context.Context, patterns []models.Pattern) error
	
	// Cache management
	WarmCache(ctx context.Context) error
	GetCacheStats() cache.CacheStats
	ClearCache(ctx context.Context) error
}

// cacheService implements CacheService
type cacheService struct {
	cacheManager *cache.Manager
	logger       *zap.Logger
	
	// Cache TTL configurations
	patternTTL       time.Duration
	userTTL          time.Duration
	searchResultTTL  time.Duration
	popularTTL       time.Duration
}

// NewCacheService creates a new cache service
func NewCacheService(redisClient *clients.RedisClient, logger *zap.Logger) CacheService {
	cacheManager := cache.NewManager(redisClient, logger)
	
	// Start cleanup routine
	cacheManager.StartCleanupRoutine(5 * time.Minute)
	
	return &cacheService{
		cacheManager:    cacheManager,
		logger:          logger,
		patternTTL:      15 * time.Minute,
		userTTL:         10 * time.Minute,
		searchResultTTL: 5 * time.Minute,
		popularTTL:      30 * time.Minute,
	}
}

// Pattern caching methods

func (c *cacheService) GetPattern(ctx context.Context, id string) (*models.Pattern, error) {
	key := fmt.Sprintf("pattern:%s", id)
	var pattern models.Pattern
	
	if err := c.cacheManager.Get(ctx, key, &pattern); err != nil {
		return nil, err
	}
	
	return &pattern, nil
}

func (c *cacheService) SetPattern(ctx context.Context, pattern *models.Pattern) error {
	key := fmt.Sprintf("pattern:%s", pattern.ID)
	return c.cacheManager.Set(ctx, key, pattern, c.patternTTL)
}

func (c *cacheService) InvalidatePattern(ctx context.Context, id string) error {
	key := fmt.Sprintf("pattern:%s", id)
	return c.cacheManager.Delete(ctx, key)
}

// User caching methods

func (c *cacheService) GetUser(ctx context.Context, id string) (*models.User, error) {
	key := fmt.Sprintf("user:%s", id)
	var user models.User
	
	if err := c.cacheManager.Get(ctx, key, &user); err != nil {
		return nil, err
	}
	
	return &user, nil
}

func (c *cacheService) SetUser(ctx context.Context, user *models.User) error {
	key := fmt.Sprintf("user:%s", user.ID)
	return c.cacheManager.Set(ctx, key, user, c.userTTL)
}

func (c *cacheService) InvalidateUser(ctx context.Context, id string) error {
	key := fmt.Sprintf("user:%s", id)
	return c.cacheManager.Delete(ctx, key)
}

// Search result caching methods

func (c *cacheService) GetSearchResults(ctx context.Context, query string, filters map[string]interface{}) ([]models.Pattern, error) {
	key := c.generateSearchKey(query, filters)
	var results []models.Pattern
	
	if err := c.cacheManager.Get(ctx, key, &results); err != nil {
		return nil, err
	}
	
	return results, nil
}

func (c *cacheService) SetSearchResults(ctx context.Context, query string, filters map[string]interface{}, results []models.Pattern) error {
	key := c.generateSearchKey(query, filters)
	return c.cacheManager.Set(ctx, key, results, c.searchResultTTL)
}

// Popular patterns caching

func (c *cacheService) GetPopularPatterns(ctx context.Context, limit int) ([]models.Pattern, error) {
	key := fmt.Sprintf("popular_patterns:%d", limit)
	var patterns []models.Pattern
	
	if err := c.cacheManager.Get(ctx, key, &patterns); err != nil {
		return nil, err
	}
	
	return patterns, nil
}

func (c *cacheService) SetPopularPatterns(ctx context.Context, patterns []models.Pattern) error {
	key := "popular_patterns"
	return c.cacheManager.Set(ctx, key, patterns, c.popularTTL)
}

// Cache management methods

func (c *cacheService) WarmCache(ctx context.Context) error {
	c.logger.Info("Starting cache warm-up")
	
	// Define frequently accessed keys
	popularKeys := []string{
		"popular_patterns:10",
		"popular_patterns:20",
		"trending_patterns",
		"featured_patterns",
	}
	
	// Warm cache with popular data
	err := c.cacheManager.Warm(ctx, popularKeys, func(key string) (interface{}, error) {
		// This would typically load data from database
		// For now, return empty data
		switch key {
		case "popular_patterns:10", "popular_patterns:20":
			return []models.Pattern{}, nil
		case "trending_patterns", "featured_patterns":
			return []models.Pattern{}, nil
		default:
			return nil, fmt.Errorf("unknown key: %s", key)
		}
	})
	
	if err != nil {
		c.logger.Error("Cache warm-up failed", zap.Error(err))
		return err
	}
	
	c.logger.Info("Cache warm-up completed")
	return nil
}

func (c *cacheService) GetCacheStats() cache.CacheStats {
	return c.cacheManager.GetStats()
}

func (c *cacheService) ClearCache(ctx context.Context) error {
	// Clear statistics
	c.cacheManager.ClearStats()
	
	// Cleanup expired entries
	c.cacheManager.Cleanup()
	
	c.logger.Info("Cache cleared")
	return nil
}

// Helper methods

func (c *cacheService) generateSearchKey(query string, filters map[string]interface{}) string {
	// Simple key generation - could be more sophisticated
	key := fmt.Sprintf("search:%s", query)
	
	// Add filters to key
	for k, v := range filters {
		key += fmt.Sprintf(":%s=%v", k, v)
	}
	
	return key
}

// CacheMiddleware provides caching middleware for handlers
type CacheMiddleware struct {
	cacheService CacheService
	logger       *zap.Logger
}

// NewCacheMiddleware creates a new cache middleware
func NewCacheMiddleware(cacheService CacheService, logger *zap.Logger) *CacheMiddleware {
	return &CacheMiddleware{
		cacheService: cacheService,
		logger:       logger,
	}
}

// CachePattern caches pattern responses
func (cm *CacheMiddleware) CachePattern(next func(ctx context.Context, id string) (*models.Pattern, error)) func(ctx context.Context, id string) (*models.Pattern, error) {
	return func(ctx context.Context, id string) (*models.Pattern, error) {
		// Try cache first
		if pattern, err := cm.cacheService.GetPattern(ctx, id); err == nil {
			cm.logger.Debug("Pattern cache hit", zap.String("pattern_id", id))
			return pattern, nil
		}
		
		// Cache miss, call original function
		pattern, err := next(ctx, id)
		if err != nil {
			return nil, err
		}
		
		// Cache the result
		if err := cm.cacheService.SetPattern(ctx, pattern); err != nil {
			cm.logger.Warn("Failed to cache pattern", zap.String("pattern_id", id), zap.Error(err))
		}
		
		return pattern, nil
	}
}

// CacheUser caches user responses
func (cm *CacheMiddleware) CacheUser(next func(ctx context.Context, id string) (*models.User, error)) func(ctx context.Context, id string) (*models.User, error) {
	return func(ctx context.Context, id string) (*models.User, error) {
		// Try cache first
		if user, err := cm.cacheService.GetUser(ctx, id); err == nil {
			cm.logger.Debug("User cache hit", zap.String("user_id", id))
			return user, nil
		}
		
		// Cache miss, call original function
		user, err := next(ctx, id)
		if err != nil {
			return nil, err
		}
		
		// Cache the result
		if err := cm.cacheService.SetUser(ctx, user); err != nil {
			cm.logger.Warn("Failed to cache user", zap.String("user_id", id), zap.Error(err))
		}
		
		return user, nil
	}
}
