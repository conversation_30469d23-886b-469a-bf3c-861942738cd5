// Package services provides search and discovery functionality for the marketplace
package services

import (
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"strings"
	"time"

	"go.uber.org/zap"

	"github.com/episteme/marketplace/internal/clients"
	"github.com/episteme/marketplace/internal/models"
)

// searchService implements the SearchService interface
type searchService struct {
	spannerClient *clients.SpannerClient
	redisClient   *clients.RedisClient
	logger        *zap.Logger
}

// NewSearchService creates a new search service
func NewSearchService(spannerClient *clients.SpannerClient, redisClient *clients.RedisClient) SearchService {
	return &searchService{
		spannerClient: spannerClient,
		redisClient:   redisClient,
		logger:        zap.NewNop(),
	}
}

// SearchPatterns performs advanced pattern search
func (s *searchService) SearchPatterns(ctx context.Context, req *models.SearchRequest) (*models.SearchResponse, error) {
	startTime := time.Now()

	s.logger.Info("Searching patterns",
		zap.String("query", req.Query),
		zap.Any("filters", req.Filters),
		zap.String("sort", req.Sort))

	// Check cache first
	cacheKey := s.buildCacheKey("search:patterns", req)
	var cachedResponse models.SearchResponse
	if err := s.redisClient.Get(ctx, cacheKey, &cachedResponse); err == nil {
		s.logger.Debug("Search results retrieved from cache", zap.String("cache_key", cacheKey))
		return &cachedResponse, nil
	}

	// Build search parameters
	params := s.buildSearchParams(req)

	// Execute search
	patterns, total, err := s.searchPatternsInDatabase(ctx, params)
	if err != nil {
		s.logger.Error("Failed to search patterns", zap.Error(err))
		return nil, fmt.Errorf("search failed: %w", err)
	}

	// Convert patterns to interface slice
	results := make([]interface{}, len(patterns))
	for i, pattern := range patterns {
		results[i] = pattern
	}

	// Build response
	response := &models.SearchResponse{
		Results: results,
		Pagination: models.Pagination{
			Page:       req.Page,
			PageSize:   req.PageSize,
			Total:      total,
			TotalPages: int((total + int64(req.PageSize) - 1) / int64(req.PageSize)),
			HasNext:    req.Page < int((total+int64(req.PageSize)-1)/int64(req.PageSize)),
			HasPrev:    req.Page > 1,
		},
		SearchTime: time.Since(startTime).Milliseconds(),
		Total:      total,
		Query:      req.Query,
	}

	// Add facets if requested
	if req.IncludeFacets {
		facets, err := s.buildSearchFacets(ctx, req.Query, patterns)
		if err != nil {
			s.logger.Warn("Failed to build facets", zap.Error(err))
		} else {
			response.Facets = facets
		}
	}

	// Cache the response
	if err := s.redisClient.Set(ctx, cacheKey, response, 5*time.Minute); err != nil { // 5 minutes cache
		s.logger.Warn("Failed to cache search results", zap.Error(err))
	}

	// Record search analytics
	go s.recordSearchAnalytics(ctx, req, int(total))

	s.logger.Info("Pattern search completed",
		zap.Int64("total", total),
		zap.Int64("search_time_ms", response.SearchTime))

	return response, nil
}

// SearchUsers performs user search
func (s *searchService) SearchUsers(ctx context.Context, req *models.SearchRequest) (*models.SearchResponse, error) {
	// TODO: Implement user search
	s.logger.Info("Searching users", zap.String("query", req.Query))

	response := models.NewSearchResponse(req.Query)
	response.SearchTime = 1 // Placeholder

	return response, nil
}

// SearchCollections performs collection search
func (s *searchService) SearchCollections(ctx context.Context, req *models.SearchRequest) (*models.SearchResponse, error) {
	// TODO: Implement collection search
	s.logger.Info("Searching collections", zap.String("query", req.Query))

	response := models.NewSearchResponse(req.Query)
	response.SearchTime = 1 // Placeholder

	return response, nil
}

// GetSearchFacets returns faceted search results
func (s *searchService) GetSearchFacets(ctx context.Context, query string, entityType string) (*models.SearchFacets, error) {
	s.logger.Info("Getting search facets", zap.String("query", query), zap.String("entity_type", entityType))

	// TODO: Implement facet calculation from database
	facets := &models.SearchFacets{
		Categories: []models.FacetValue{
			{Value: "authentication", Count: 25, Label: "Authentication"},
			{Value: "database", Count: 18, Label: "Database"},
			{Value: "api", Count: 32, Label: "API"},
		},
		Languages: []models.FacetValue{
			{Value: "javascript", Count: 45, Label: "JavaScript"},
			{Value: "python", Count: 38, Label: "Python"},
			{Value: "go", Count: 22, Label: "Go"},
		},
		PriceRanges: []models.FacetValue{
			{Value: "free", Count: 67, Label: "Free"},
			{Value: "0-10", Count: 23, Label: "$0-$10"},
			{Value: "10-50", Count: 15, Label: "$10-$50"},
		},
	}

	return facets, nil
}

// GetPersonalizedRecommendations returns personalized pattern recommendations
func (s *searchService) GetPersonalizedRecommendations(ctx context.Context, userID string, limit int) ([]*models.Pattern, error) {
	s.logger.Info("Getting personalized recommendations", zap.String("user_id", userID), zap.Int("limit", limit))

	// Check cache first
	cacheKey := fmt.Sprintf("recommendations:user:%s:limit:%d", userID, limit)
	var cachedPatterns []*models.Pattern
	if err := s.redisClient.Get(ctx, cacheKey, &cachedPatterns); err == nil {
		s.logger.Debug("Recommendations retrieved from cache", zap.String("cache_key", cacheKey))
		return cachedPatterns, nil
	}

	// TODO: Implement ML-based recommendations
	// For now, return popular patterns in user's preferred categories
	patterns, err := s.getPopularPatternsForUser(ctx, userID, limit)
	if err != nil {
		s.logger.Error("Failed to get personalized recommendations", zap.Error(err))
		return nil, fmt.Errorf("failed to get recommendations: %w", err)
	}

	// Cache the recommendations
	if err := s.redisClient.Set(ctx, cacheKey, patterns, 30*time.Minute); err != nil { // 30 minutes cache
		s.logger.Warn("Failed to cache recommendations", zap.Error(err))
	}

	return patterns, nil
}

// GetSimilarPatterns returns patterns similar to the given pattern
func (s *searchService) GetSimilarPatterns(ctx context.Context, patternID string, limit int) ([]*models.Pattern, error) {
	s.logger.Info("Getting similar patterns", zap.String("pattern_id", patternID), zap.Int("limit", limit))

	// Get the source pattern
	pattern, err := s.spannerClient.GetPattern(ctx, patternID)
	if err != nil {
		return nil, fmt.Errorf("failed to get source pattern: %w", err)
	}

	// TODO: Implement similarity algorithm based on tags, category, language
	// For now, return patterns in the same category
	params := &models.ListPatternsParams{
		Category: pattern.Category,
		Language: pattern.Language,
		Page:     1,
		PageSize: limit + 1, // +1 to exclude the source pattern
	}

	patterns, _, err := s.spannerClient.ListPatterns(ctx, params)
	if err != nil {
		return nil, fmt.Errorf("failed to get similar patterns: %w", err)
	}

	// Filter out the source pattern
	var similarPatterns []*models.Pattern
	for _, p := range patterns {
		if p.ID != patternID {
			similarPatterns = append(similarPatterns, p)
		}
		if len(similarPatterns) >= limit {
			break
		}
	}

	return similarPatterns, nil
}

// GetTrendingPatterns returns trending patterns for a given timeframe
func (s *searchService) GetTrendingPatterns(ctx context.Context, timeframe string, limit int) ([]*models.Pattern, error) {
	s.logger.Info("Getting trending patterns", zap.String("timeframe", timeframe), zap.Int("limit", limit))

	// Check cache first
	cacheKey := fmt.Sprintf("trending:patterns:%s:limit:%d", timeframe, limit)
	var cachedPatterns []*models.Pattern
	if err := s.redisClient.Get(ctx, cacheKey, &cachedPatterns); err == nil {
		s.logger.Debug("Trending patterns retrieved from cache", zap.String("cache_key", cacheKey))
		return cachedPatterns, nil
	}

	// TODO: Implement trending algorithm based on views, downloads, purchases
	// For now, return most downloaded patterns
	params := &models.ListPatternsParams{
		Sort:     "downloads_desc",
		Page:     1,
		PageSize: limit,
	}

	patterns, _, err := s.spannerClient.ListPatterns(ctx, params)
	if err != nil {
		return nil, fmt.Errorf("failed to get trending patterns: %w", err)
	}

	// Cache the trending patterns
	cacheTTL := s.getTrendingCacheTTL(timeframe)
	if err := s.redisClient.Set(ctx, cacheKey, patterns, time.Duration(cacheTTL)*time.Second); err != nil {
		s.logger.Warn("Failed to cache trending patterns", zap.Error(err))
	}

	return patterns, nil
}

// GetPopularPatterns returns popular patterns in a category
func (s *searchService) GetPopularPatterns(ctx context.Context, category string, limit int) ([]*models.Pattern, error) {
	s.logger.Info("Getting popular patterns", zap.String("category", category), zap.Int("limit", limit))

	params := &models.ListPatternsParams{
		Category: category,
		Sort:     "popularity_desc",
		Page:     1,
		PageSize: limit,
	}

	patterns, _, err := s.spannerClient.ListPatterns(ctx, params)
	if err != nil {
		return nil, fmt.Errorf("failed to get popular patterns: %w", err)
	}

	return patterns, nil
}

// RecordSearchQuery records a search query for analytics
func (s *searchService) RecordSearchQuery(ctx context.Context, userID *string, query string, filters map[string]interface{}, resultCount int) error {
	searchQuery := &models.SearchQuery{
		ID:          fmt.Sprintf("search_%d", time.Now().UnixNano()),
		UserID:      userID,
		Query:       query,
		Filters:     filters,
		ResultCount: int64(resultCount),
		CreatedAt:   time.Now(),
	}

	// TODO: Store search query in database for analytics
	_ = searchQuery // Suppress unused variable warning

	// TODO: Store in database for analytics
	s.logger.Info("Recording search query",
		zap.String("query", query),
		zap.Int("result_count", resultCount))

	return nil
}

// Helper methods

func (s *searchService) buildCacheKey(prefix string, req *models.SearchRequest) string {
	// Create a deterministic cache key from the request
	key := fmt.Sprintf("%s:q:%s:sort:%s:page:%d:size:%d",
		prefix, req.Query, req.Sort, req.Page, req.PageSize)

	if len(req.Filters) > 0 {
		filtersJSON, _ := json.Marshal(req.Filters)
		key += fmt.Sprintf(":filters:%s", string(filtersJSON))
	}

	return key
}

func (s *searchService) buildSearchParams(req *models.SearchRequest) *models.ListPatternsParams {
	params := &models.ListPatternsParams{
		Page:     req.Page,
		PageSize: req.PageSize,
		Sort:     req.Sort,
	}

	// Apply filters
	if category, ok := req.Filters["category"].(string); ok {
		params.Category = category
	}
	if language, ok := req.Filters["language"].(string); ok {
		params.Language = language
	}
	if author, ok := req.Filters["author"].(string); ok {
		params.AuthorID = author
	}

	return params
}

func (s *searchService) searchPatternsInDatabase(ctx context.Context, params *models.ListPatternsParams) ([]*models.Pattern, int64, error) {
	// TODO: Implement full-text search in database
	// For now, use the existing ListPatterns method
	return s.spannerClient.ListPatterns(ctx, params)
}

func (s *searchService) buildSearchFacets(ctx context.Context, query string, patterns []*models.Pattern) (*models.SearchFacets, error) {
	facets := &models.SearchFacets{
		Categories:  make([]models.FacetValue, 0),
		Languages:   make([]models.FacetValue, 0),
		Authors: <AUTHORS>
		PriceRanges: make([]models.FacetValue, 0),
		Ratings:     make([]models.FacetValue, 0),
		Tags:        make([]models.FacetValue, 0),
	}

	// Count categories
	categoryCount := make(map[string]int64)
	languageCount := make(map[string]int64)
	authorCount := make(map[string]int64)

	for _, pattern := range patterns {
		categoryCount[pattern.Category]++
		languageCount[pattern.Language]++

		// Get author name from Author field if available, otherwise use AuthorID
		authorName := pattern.AuthorID
		if pattern.Author != nil && pattern.Author.DisplayName != "" {
			authorName = pattern.Author.DisplayName
		}
		authorCount[authorName]++
	}

	// Convert to facet values
	for category, count := range categoryCount {
		facets.Categories = append(facets.Categories, models.FacetValue{
			Value: category,
			Count: count,
			Label: strings.ToTitle(category),
		})
	}

	for language, count := range languageCount {
		facets.Languages = append(facets.Languages, models.FacetValue{
			Value: language,
			Count: count,
			Label: strings.ToTitle(language),
		})
	}

	for author, count := range authorCount {
		facets.Authors = append(facets.Authors, models.FacetValue{
			Value: author,
			Count: count,
			Label: author,
		})
	}

	// Sort facets by count
	sort.Slice(facets.Categories, func(i, j int) bool {
		return facets.Categories[i].Count > facets.Categories[j].Count
	})
	sort.Slice(facets.Languages, func(i, j int) bool {
		return facets.Languages[i].Count > facets.Languages[j].Count
	})
	sort.Slice(facets.Authors, func(i, j int) bool {
		return facets.Authors[i].Count > facets.Authors[j].Count
	})

	return facets, nil
}

func (s *searchService) recordSearchAnalytics(ctx context.Context, req *models.SearchRequest, resultCount int) {
	err := s.RecordSearchQuery(ctx, req.UserID, req.Query, req.Filters, resultCount)
	if err != nil {
		s.logger.Error("Failed to record search analytics", zap.Error(err))
	}
}

func (s *searchService) getPopularPatternsForUser(ctx context.Context, userID string, limit int) ([]*models.Pattern, error) {
	// TODO: Implement user preference analysis
	// For now, return most popular patterns
	params := &models.ListPatternsParams{
		Sort:     "popularity_desc",
		Page:     1,
		PageSize: limit,
	}

	patterns, _, err := s.spannerClient.ListPatterns(ctx, params)
	return patterns, err
}

func (s *searchService) getTrendingCacheTTL(timeframe string) int64 {
	switch timeframe {
	case "1h":
		return 300 // 5 minutes
	case "24h":
		return 1800 // 30 minutes
	case "7d":
		return 3600 // 1 hour
	case "30d":
		return 7200 // 2 hours
	default:
		return 1800 // 30 minutes default
	}
}

// GetSearchAnalytics returns search analytics for a given timeframe
func (s *searchService) GetSearchAnalytics(ctx context.Context, timeframe string) (*models.SearchAnalytics, error) {
	s.logger.Info("Getting search analytics", zap.String("timeframe", timeframe))

	// TODO: Implement analytics aggregation from database
	analytics := &models.SearchAnalytics{
		TotalSearches:      1250,
		UniqueUsers:        340,
		AverageResultCount: 12.5,
		TopQueries: []*models.SearchTerm{
			{Term: "authentication", Count: 125, ResultCount: 15, ClickRate: 0.75},
			{Term: "database", Count: 98, ResultCount: 22, ClickRate: 0.68},
			{Term: "api", Count: 87, ResultCount: 18, ClickRate: 0.72},
		},
		SearchesByCategory: map[string]int64{
			"authentication": 125,
			"database":       98,
			"api":            87,
		},
		SearchesByLanguage: map[string]int64{
			"javascript": 245,
			"python":     198,
			"go":         156,
		},
		ConversionRate: 0.15,
		Period:         timeframe,
	}

	return analytics, nil
}

// GetPopularSearchTerms returns the most popular search terms
func (s *searchService) GetPopularSearchTerms(ctx context.Context, limit int) ([]*models.SearchTerm, error) {
	s.logger.Info("Getting popular search terms", zap.Int("limit", limit))

	// TODO: Implement from database analytics
	terms := []*models.SearchTerm{
		{Term: "authentication", Count: 125, ResultCount: 15, ClickRate: 0.75, LastSearched: time.Now()},
		{Term: "database", Count: 98, ResultCount: 22, ClickRate: 0.68, LastSearched: time.Now()},
		{Term: "api", Count: 87, ResultCount: 18, ClickRate: 0.72, LastSearched: time.Now()},
		{Term: "security", Count: 76, ResultCount: 12, ClickRate: 0.69, LastSearched: time.Now()},
		{Term: "microservices", Count: 65, ResultCount: 8, ClickRate: 0.71, LastSearched: time.Now()},
	}

	if limit < len(terms) {
		terms = terms[:limit]
	}

	return terms, nil
}

// IndexPattern adds or updates a pattern in the search index
func (s *searchService) IndexPattern(ctx context.Context, pattern *models.Pattern) error {
	s.logger.Info("Indexing pattern", zap.String("pattern_id", pattern.ID))

	// Create search index entry
	index := models.NewSearchIndexFromPattern(pattern)

	// TODO: Store in search index (Elasticsearch, Algolia, etc.)
	// For now, just log the action
	s.logger.Debug("Pattern indexed", zap.String("pattern_id", pattern.ID), zap.String("title", index.Title))

	return nil
}

// UpdatePatternIndex updates a pattern in the search index
func (s *searchService) UpdatePatternIndex(ctx context.Context, pattern *models.Pattern) error {
	s.logger.Info("Updating pattern index", zap.String("pattern_id", pattern.ID))

	// Update search index entry
	return s.IndexPattern(ctx, pattern)
}

// RemovePatternFromIndex removes a pattern from the search index
func (s *searchService) RemovePatternFromIndex(ctx context.Context, patternID string) error {
	s.logger.Info("Removing pattern from index", zap.String("pattern_id", patternID))

	// TODO: Remove from search index
	// For now, just log the action
	s.logger.Debug("Pattern removed from index", zap.String("pattern_id", patternID))

	return nil
}

// RebuildIndex rebuilds the entire search index
func (s *searchService) RebuildIndex(ctx context.Context) error {
	s.logger.Info("Rebuilding search index")

	// TODO: Implement full index rebuild
	// This would typically:
	// 1. Get all patterns from database
	// 2. Clear existing index
	// 3. Re-index all patterns

	s.logger.Info("Search index rebuild completed")
	return nil
}
