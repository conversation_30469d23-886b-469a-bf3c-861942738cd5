// Package services provides validation service implementation
package services

import (
	"context"
	"fmt"
	"regexp"
	"strings"
	"time"

	"go.uber.org/zap"

	"github.com/episteme/marketplace/internal/clients"
	"github.com/episteme/marketplace/internal/models"
)

// validationService implements ValidationService interface
type validationService struct {
	spannerClient *clients.SpannerClient
	logger        *zap.Logger
}

// NewValidationService creates a new validation service
func NewValidationService(spannerClient *clients.SpannerClient) ValidationService {
	return &validationService{
		spannerClient: spannerClient,
		logger:        zap.NewNop(),
	}
}

// ValidatePatternCode validates pattern code for syntax and quality
func (s *validationService) ValidatePatternCode(ctx context.Context, patternID string, code string, language string) (*models.PatternValidationResult, error) {
	s.logger.Info("Validating pattern code",
		zap.String("pattern_id", patternID),
		zap.String("language", language))

	result := &models.PatternValidationResult{
		IsValid:     true,
		Score:       0.0,
		Issues:      []models.ValidationIssue{},
		Metrics:     make(map[string]float64),
		Suggestions: []string{},
		ProcessedAt: time.Now(),
	}

	// Basic code validation
	if err := s.validateCodeSyntax(code, language, result); err != nil {
		return result, err
	}

	// Security validation
	if err := s.validateCodeSecurity(code, language, result); err != nil {
		return result, err
	}

	// Quality metrics
	s.calculateCodeMetrics(code, language, result)

	// Calculate overall score
	result.Score = s.calculateOverallScore(result)

	s.logger.Info("Pattern code validation completed",
		zap.String("pattern_id", patternID),
		zap.Float64("score", result.Score),
		zap.Bool("is_valid", result.IsValid))

	return result, nil
}

// ValidatePatternMetadata validates pattern metadata
func (s *validationService) ValidatePatternMetadata(ctx context.Context, pattern *models.Pattern) (*models.PatternValidationResult, error) {
	s.logger.Info("Validating pattern metadata", zap.String("pattern_id", pattern.ID))

	result := &models.PatternValidationResult{
		IsValid:     true,
		Score:       0.0,
		Issues:      []models.ValidationIssue{},
		Metrics:     make(map[string]float64),
		Suggestions: []string{},
		ProcessedAt: time.Now(),
	}

	// Validate required fields
	if pattern.Name == "" {
		result.Issues = append(result.Issues, models.ValidationIssue{
			Type: "error",

			Message:    "Pattern name is required",
			Severity:   "high",
			Line:       nil,
			Suggestion: "Provide a descriptive name for your pattern",
		})
		result.IsValid = false
	}

	if pattern.Description == "" {
		result.Issues = append(result.Issues, models.ValidationIssue{
			Type: "error",

			Message:    "Pattern description is required",
			Severity:   "high",
			Line:       nil,
			Suggestion: "Provide a detailed description of your pattern",
		})
		result.IsValid = false
	}

	// Validate name length and quality
	if len(pattern.Name) < 3 {
		result.Issues = append(result.Issues, models.ValidationIssue{
			Type: "warning",

			Message:    "Pattern name is too short",
			Severity:   "medium",
			Line:       nil,
			Suggestion: "Use a more descriptive name (at least 3 characters)",
		})
	}

	// Validate description quality
	if len(pattern.Description) < 10 {
		result.Issues = append(result.Issues, models.ValidationIssue{
			Type: "warning",

			Message:    "Pattern description is too short",
			Severity:   "medium",
			Line:       nil,
			Suggestion: "Provide a more detailed description (at least 10 characters)",
		})
	}

	// Validate tags
	if len(pattern.Tags) == 0 {
		result.Issues = append(result.Issues, models.ValidationIssue{
			Type: "info",

			Message:    "No tags provided",
			Severity:   "low",
			Line:       nil,
			Suggestion: "Add relevant tags to improve discoverability",
		})
	}

	// Calculate metadata score
	result.Score = s.calculateMetadataScore(pattern, result)

	s.logger.Info("Pattern metadata validation completed",
		zap.String("pattern_id", pattern.ID),
		zap.Float64("score", result.Score),
		zap.Bool("is_valid", result.IsValid))

	return result, nil
}

// ValidatePatternSecurity performs security validation
func (s *validationService) ValidatePatternSecurity(ctx context.Context, patternID string, code string) (*models.PatternValidationResult, error) {
	s.logger.Info("Validating pattern security", zap.String("pattern_id", patternID))

	result := &models.PatternValidationResult{
		IsValid:     true,
		Score:       0.0,
		Issues:      []models.ValidationIssue{},
		Metrics:     make(map[string]float64),
		Suggestions: []string{},
		ProcessedAt: time.Now(),
	}

	// Security checks
	s.checkForSecurityVulnerabilities(code, result)
	s.checkForMaliciousPatterns(code, result)
	s.checkForSensitiveData(code, result)

	// Calculate security score
	result.Score = s.calculateSecurityScore(result)

	s.logger.Info("Pattern security validation completed",
		zap.String("pattern_id", patternID),
		zap.Float64("score", result.Score),
		zap.Bool("is_valid", result.IsValid))

	return result, nil
}

// CalculateQualityScore calculates overall quality score
func (s *validationService) CalculateQualityScore(ctx context.Context, pattern *models.Pattern, validationResults []*models.PatternValidationResult) (float64, error) {
	if len(validationResults) == 0 {
		return 0.0, fmt.Errorf("no validation results provided")
	}

	var totalScore float64
	var weights = map[string]float64{
		"code":     0.4, // 40% weight for code quality
		"metadata": 0.3, // 30% weight for metadata quality
		"security": 0.3, // 30% weight for security
	}

	for _, result := range validationResults {
		// Determine validation type based on metrics
		if _, hasCodeMetrics := result.Metrics["lines_of_code"]; hasCodeMetrics {
			totalScore += result.Score * weights["code"]
		} else if _, hasSecurityMetrics := result.Metrics["security_issues"]; hasSecurityMetrics {
			totalScore += result.Score * weights["security"]
		} else {
			totalScore += result.Score * weights["metadata"]
		}
	}

	// Normalize score to 0-100 range
	finalScore := totalScore * 100

	s.logger.Info("Quality score calculated",
		zap.String("pattern_id", pattern.ID),
		zap.Float64("final_score", finalScore))

	return finalScore, nil
}

// Helper methods for validation logic

func (s *validationService) validateCodeSyntax(code string, language string, result *models.PatternValidationResult) error {
	// Basic syntax validation based on language
	switch strings.ToLower(language) {
	case "go":
		return s.validateGoSyntax(code, result)
	case "python":
		return s.validatePythonSyntax(code, result)
	case "javascript", "typescript":
		return s.validateJavaScriptSyntax(code, result)
	case "rust":
		return s.validateRustSyntax(code, result)
	default:
		// Generic validation
		return s.validateGenericSyntax(code, result)
	}
}

func (s *validationService) validateGoSyntax(code string, result *models.PatternValidationResult) error {
	// Check for basic Go syntax patterns
	if !strings.Contains(code, "package ") {
		result.Issues = append(result.Issues, models.ValidationIssue{
			Type: "error",

			Message:    "Go code must have a package declaration",
			Severity:   "high",
			Line:       nil,
			Suggestion: "Add 'package main' or appropriate package name",
		})
		result.IsValid = false
	}

	// Check for balanced braces
	if strings.Count(code, "{") != strings.Count(code, "}") {
		result.Issues = append(result.Issues, models.ValidationIssue{
			Type: "error",

			Message:    "Unbalanced braces in Go code",
			Severity:   "high",
			Line:       nil,
			Suggestion: "Check for missing opening or closing braces",
		})
		result.IsValid = false
	}

	return nil
}

func (s *validationService) validatePythonSyntax(code string, result *models.PatternValidationResult) error {
	// Basic Python validation
	lines := strings.Split(code, "\n")
	for i, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		// Check for basic indentation issues
		if strings.HasSuffix(line, ":") && i+1 < len(lines) {
			nextLine := strings.TrimLeft(lines[i+1], " \t")
			if nextLine != "" && !strings.HasPrefix(nextLine, "#") &&
				len(lines[i+1])-len(nextLine) <= len(line)-len(strings.TrimLeft(line, " \t")) {
				result.Issues = append(result.Issues, models.ValidationIssue{
					Type: "warning",

					Message:    "Possible indentation issue",
					Severity:   "medium",
					Line:       &[]int{i + 1}[0],
					Suggestion: "Check indentation after colon",
				})
			}
		}
	}

	return nil
}

func (s *validationService) validateJavaScriptSyntax(code string, result *models.PatternValidationResult) error {
	// Check for balanced parentheses and braces
	if strings.Count(code, "(") != strings.Count(code, ")") {
		result.Issues = append(result.Issues, models.ValidationIssue{
			Type: "error",

			Message:    "Unbalanced parentheses in JavaScript code",
			Severity:   "high",
			Line:       nil,
			Suggestion: "Check for missing opening or closing parentheses",
		})
		result.IsValid = false
	}

	if strings.Count(code, "{") != strings.Count(code, "}") {
		result.Issues = append(result.Issues, models.ValidationIssue{
			Type: "error",

			Message:    "Unbalanced braces in JavaScript code",
			Severity:   "high",
			Line:       nil,
			Suggestion: "Check for missing opening or closing braces",
		})
		result.IsValid = false
	}

	return nil
}

func (s *validationService) validateRustSyntax(code string, result *models.PatternValidationResult) error {
	// Check for basic Rust patterns
	if !strings.Contains(code, "fn ") && !strings.Contains(code, "struct ") &&
		!strings.Contains(code, "enum ") && !strings.Contains(code, "impl ") {
		result.Issues = append(result.Issues, models.ValidationIssue{
			Type: "warning",

			Message:    "No recognizable Rust constructs found",
			Severity:   "medium",
			Line:       nil,
			Suggestion: "Ensure this is valid Rust code with functions, structs, or other constructs",
		})
	}

	return nil
}

func (s *validationService) validateGenericSyntax(code string, result *models.PatternValidationResult) error {
	// Generic validation for unknown languages
	if len(strings.TrimSpace(code)) == 0 {
		result.Issues = append(result.Issues, models.ValidationIssue{
			Type: "error",

			Message:    "Pattern code is empty",
			Severity:   "high",
			Line:       nil,
			Suggestion: "Provide actual code content",
		})
		result.IsValid = false
	}

	return nil
}

func (s *validationService) validateCodeSecurity(code string, language string, result *models.PatternValidationResult) error {
	s.checkForSecurityVulnerabilities(code, result)
	s.checkForMaliciousPatterns(code, result)
	s.checkForSensitiveData(code, result)
	return nil
}

func (s *validationService) calculateCodeMetrics(code string, language string, result *models.PatternValidationResult) {
	lines := strings.Split(code, "\n")
	result.Metrics["lines_of_code"] = float64(len(lines))

	// Count non-empty lines
	nonEmptyLines := 0
	commentLines := 0
	for _, line := range lines {
		trimmed := strings.TrimSpace(line)
		if trimmed != "" {
			nonEmptyLines++
			if strings.HasPrefix(trimmed, "//") || strings.HasPrefix(trimmed, "#") ||
				strings.HasPrefix(trimmed, "/*") || strings.HasPrefix(trimmed, "*") {
				commentLines++
			}
		}
	}

	result.Metrics["non_empty_lines"] = float64(nonEmptyLines)
	result.Metrics["comment_lines"] = float64(commentLines)

	if nonEmptyLines > 0 {
		result.Metrics["comment_ratio"] = float64(commentLines) / float64(nonEmptyLines)
	}

	// Calculate complexity (basic)
	complexity := strings.Count(code, "if ") + strings.Count(code, "for ") +
		strings.Count(code, "while ") + strings.Count(code, "switch ") +
		strings.Count(code, "case ")
	result.Metrics["cyclomatic_complexity"] = float64(complexity)
}

func (s *validationService) calculateOverallScore(result *models.PatternValidationResult) float64 {
	if !result.IsValid {
		return 0.0
	}

	score := 1.0 // Start with perfect score

	// Deduct points for issues
	for _, issue := range result.Issues {
		switch issue.Severity {
		case "high":
			score -= 0.3
		case "medium":
			score -= 0.1
		case "low":
			score -= 0.05
		}
	}

	// Bonus for good metrics
	if commentRatio, exists := result.Metrics["comment_ratio"]; exists && commentRatio > 0.2 {
		score += 0.1 // Bonus for good documentation
	}

	// Ensure score is between 0 and 1
	if score < 0 {
		score = 0
	}
	if score > 1 {
		score = 1
	}

	return score
}

// AnalyzePatternComplexity analyzes pattern complexity using pattern-mining service
func (s *validationService) AnalyzePatternComplexity(ctx context.Context, patternID string) (map[string]interface{}, error) {
	s.logger.Info("Analyzing pattern complexity", zap.String("pattern_id", patternID))

	// TODO: Integrate with pattern-mining service
	// For now, return mock complexity analysis
	complexity := map[string]interface{}{
		"cyclomatic_complexity": 5,
		"cognitive_complexity":  3,
		"maintainability_index": 75.5,
		"technical_debt_ratio":  0.15,
		"code_smells":           2,
		"duplicated_lines":      0,
		"coverage":              85.2,
		"analysis_timestamp":    time.Now().UTC(),
	}

	s.logger.Info("Pattern complexity analysis completed",
		zap.String("pattern_id", patternID),
		zap.Any("complexity", complexity))

	return complexity, nil
}

// DetectPatternSimilarity detects similar patterns using pattern-mining service
func (s *validationService) DetectPatternSimilarity(ctx context.Context, patternID string) ([]string, error) {
	s.logger.Info("Detecting pattern similarity", zap.String("pattern_id", patternID))

	// TODO: Integrate with pattern-mining service for similarity detection
	// For now, return mock similar pattern IDs
	similarPatterns := []string{
		// These would be actual pattern IDs from similarity analysis
		"pattern_123",
		"pattern_456",
		"pattern_789",
	}

	s.logger.Info("Pattern similarity detection completed",
		zap.String("pattern_id", patternID),
		zap.Int("similar_patterns_count", len(similarPatterns)))

	return similarPatterns, nil
}

func (s *validationService) calculateMetadataScore(pattern *models.Pattern, result *models.PatternValidationResult) float64 {
	score := 1.0

	// Deduct for issues
	for _, issue := range result.Issues {
		switch issue.Severity {
		case "high":
			score -= 0.4
		case "medium":
			score -= 0.2
		case "low":
			score -= 0.1
		}
	}

	// Bonus for good metadata
	if len(pattern.Tags) > 0 {
		score += 0.1
	}
	if len(pattern.Description) > 100 {
		score += 0.1
	}

	if score < 0 {
		score = 0
	}
	if score > 1 {
		score = 1
	}

	return score
}

func (s *validationService) calculateSecurityScore(result *models.PatternValidationResult) float64 {
	score := 1.0

	// Deduct heavily for security issues
	for _, issue := range result.Issues {
		switch issue.Severity {
		case "high":
			score -= 0.5
		case "medium":
			score -= 0.3
		case "low":
			score -= 0.1
		}
	}

	if score < 0 {
		score = 0
	}

	return score
}

func (s *validationService) checkForSecurityVulnerabilities(code string, result *models.PatternValidationResult) {
	// Check for common security anti-patterns
	securityPatterns := map[string]string{
		`eval\s*\(`:                     "Use of eval() function can lead to code injection",
		`exec\s*\(`:                     "Use of exec() function can lead to code injection",
		`system\s*\(`:                   "Use of system() function can lead to command injection",
		`shell_exec\s*\(`:               "Use of shell_exec() function can lead to command injection",
		`\$_GET\[.*\]`:                  "Direct use of $_GET without sanitization",
		`\$_POST\[.*\]`:                 "Direct use of $_POST without sanitization",
		`document\.write\s*\(`:          "Use of document.write() can lead to XSS",
		`innerHTML\s*=`:                 "Direct innerHTML assignment can lead to XSS",
		`password\s*=\s*["'][^"']+["']`: "Hardcoded password detected",
		`api_key\s*=\s*["'][^"']+["']`:  "Hardcoded API key detected",
	}

	for pattern, message := range securityPatterns {
		if matched, _ := regexp.MatchString(pattern, code); matched {
			result.Issues = append(result.Issues, models.ValidationIssue{
				Type:       "error",
				Message:    message,
				Severity:   "high",
				Line:       nil,
				Suggestion: "Review and fix security vulnerability",
			})
		}
	}
}

func (s *validationService) checkForMaliciousPatterns(code string, result *models.PatternValidationResult) {
	// Check for potentially malicious patterns
	maliciousPatterns := []string{
		"rm -rf",
		"format c:",
		"del /f /s /q",
		"DROP TABLE",
		"DELETE FROM",
		"TRUNCATE TABLE",
	}

	codeUpper := strings.ToUpper(code)
	for _, pattern := range maliciousPatterns {
		if strings.Contains(codeUpper, strings.ToUpper(pattern)) {
			result.Issues = append(result.Issues, models.ValidationIssue{
				Type:       "error",
				Message:    fmt.Sprintf("Potentially malicious pattern detected: %s", pattern),
				Severity:   "high",
				Line:       nil,
				Suggestion: "Remove malicious code patterns",
			})
			result.IsValid = false
		}
	}
}

func (s *validationService) checkForSensitiveData(code string, result *models.PatternValidationResult) {
	// Check for sensitive data patterns
	sensitivePatterns := map[string]string{
		`[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}`: "Email address detected",
		`\b\d{3}-\d{2}-\d{4}\b`:                           "SSN pattern detected",
		`\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b`:      "Credit card pattern detected",
		`sk_live_[a-zA-Z0-9]{24}`:                         "Stripe live secret key detected",
		`pk_live_[a-zA-Z0-9]{24}`:                         "Stripe live publishable key detected",
	}

	for pattern, message := range sensitivePatterns {
		if matched, _ := regexp.MatchString(pattern, code); matched {
			result.Issues = append(result.Issues, models.ValidationIssue{
				Type:       "warning",
				Message:    message,
				Severity:   "medium",
				Line:       nil,
				Suggestion: "Remove or mask sensitive data",
			})
		}
	}
}
