// Package services provides payment service implementation
package services

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"

	"github.com/episteme/marketplace/internal/clients"
	"github.com/episteme/marketplace/internal/models"
	"github.com/google/uuid"
)

// paymentService implements PaymentService interface
type paymentService struct {
	stripeClient  *clients.StripeClient
	spannerClient *clients.SpannerClient
	logger        *zap.Logger
}

// NewPaymentService creates a new payment service
func NewPaymentService(stripeClient *clients.StripeClient, spannerClient *clients.SpannerClient) PaymentService {
	return &paymentService{
		stripeClient:  stripeClient,
		spannerClient: spannerClient,
		logger:        zap.NewNop(),
	}
}

// PurchasePattern initiates a pattern purchase
func (s *paymentService) PurchasePattern(ctx context.Context, patternID string, req *models.PurchaseRequest, userID string) (*models.Transaction, error) {
	s.logger.Info("Initiating pattern purchase", zap.String("pattern_id", patternID), zap.String("user_id", userID))

	// Get pattern details to calculate pricing
	pattern, err := s.spannerClient.GetPattern(ctx, patternID)
	if err != nil {
		s.logger.Error("Failed to get pattern", zap.Error(err), zap.String("pattern_id", patternID))
		return nil, fmt.Errorf("pattern not found: %w", err)
	}

	// Check if pattern is available for purchase
	if pattern.Status != models.PatternStatusPublished {
		return nil, fmt.Errorf("pattern not available for purchase")
	}

	// Check if user already owns this pattern
	if pattern.AuthorID == userID {
		return nil, fmt.Errorf("cannot purchase your own pattern")
	}

	// Create transaction record
	transactionID := uuid.New().String()
	transaction := &models.Transaction{
		ID:                transactionID,
		PatternID:         patternID,
		BuyerID:           userID,
		SellerID:          pattern.AuthorID,
		AmountCents:       pattern.PriceCents,
		Currency:          pattern.Currency,
		PlatformFeeCents:  int64(float64(pattern.PriceCents) * 0.20), // 20% platform fee
		SellerAmountCents: int64(float64(pattern.PriceCents) * 0.80), // 80% to seller
		Status:            models.TransactionStatusPending,
		CreatedAt:         time.Now(),
		Metadata: map[string]interface{}{
			"payment_method_id": req.PaymentMethodID,
		},
	}

	// Create Stripe Payment Intent
	paymentIntent, err := s.stripeClient.CreatePaymentIntent(
		userID,
		pattern.PriceCents,
		pattern.Currency,
		map[string]string{
			"transaction_id": transactionID,
			"pattern_id":     patternID,
			"seller_id":      pattern.AuthorID,
		},
	)
	if err != nil {
		s.logger.Error("Failed to create payment intent", zap.Error(err))
		return nil, fmt.Errorf("failed to create payment intent: %w", err)
	}

	transaction.StripePaymentIntentID = &paymentIntent.ID
	// Store client secret in metadata for frontend use
	transaction.Metadata["client_secret"] = paymentIntent.ClientSecret

	// Save transaction to database
	if err := s.spannerClient.InsertTransaction(ctx, transaction); err != nil {
		s.logger.Error("Failed to save transaction", zap.Error(err))
		return nil, fmt.Errorf("failed to save transaction: %w", err)
	}

	s.logger.Info("Pattern purchase initiated successfully",
		zap.String("transaction_id", transactionID),
		zap.String("payment_intent_id", paymentIntent.ID))

	return transaction, nil
}

// ConfirmPayment confirms a payment
func (s *paymentService) ConfirmPayment(ctx context.Context, paymentIntentID string, userID string) (*models.Transaction, error) {
	s.logger.Info("Confirming payment", zap.String("payment_intent_id", paymentIntentID), zap.String("user_id", userID))

	// Get transaction by payment intent ID
	transaction, err := s.spannerClient.GetTransactionByPaymentIntent(ctx, paymentIntentID)
	if err != nil {
		s.logger.Error("Failed to get transaction", zap.Error(err))
		return nil, fmt.Errorf("transaction not found: %w", err)
	}

	// Verify user owns this transaction
	if transaction.BuyerID != userID {
		return nil, fmt.Errorf("unauthorized: transaction does not belong to user")
	}

	// Check if already completed
	if transaction.Status == models.TransactionStatusCompleted {
		return transaction, nil
	}

	// Get payment method ID from transaction metadata
	paymentMethodID, ok := transaction.Metadata["payment_method_id"].(string)
	if !ok {
		return nil, fmt.Errorf("payment method ID not found in transaction")
	}

	// Confirm payment with Stripe
	paymentIntent, err := s.stripeClient.ConfirmPaymentIntent(paymentIntentID, paymentMethodID)
	if err != nil {
		s.logger.Error("Failed to confirm payment intent", zap.Error(err))
		return nil, fmt.Errorf("failed to confirm payment: %w", err)
	}

	// Update transaction status
	now := time.Now()
	transaction.Status = models.TransactionStatusCompleted
	transaction.CompletedAt = &now
	if paymentIntent.LatestCharge != nil {
		chargeID := paymentIntent.LatestCharge.ID
		transaction.StripeChargeID = &chargeID
	}

	// Update transaction in database
	if err := s.spannerClient.UpdateTransaction(ctx, transaction); err != nil {
		s.logger.Error("Failed to update transaction", zap.Error(err))
		return nil, fmt.Errorf("failed to update transaction: %w", err)
	}

	// Process revenue sharing (transfer to seller)
	if err := s.processRevenueSharing(ctx, transaction); err != nil {
		s.logger.Error("Failed to process revenue sharing", zap.Error(err))
		// Don't fail the transaction, but log the error for manual processing
	}

	s.logger.Info("Payment confirmed successfully",
		zap.String("transaction_id", transaction.ID),
		zap.String("payment_intent_id", paymentIntentID))

	return transaction, nil
}

// processRevenueSharing handles the transfer of funds to the seller
func (s *paymentService) processRevenueSharing(ctx context.Context, transaction *models.Transaction) error {
	// Create transfer to seller's Stripe account
	// This would require the seller to have a connected Stripe account
	// For now, we'll just log the revenue sharing
	s.logger.Info("Processing revenue sharing",
		zap.String("transaction_id", transaction.ID),
		zap.String("seller_id", transaction.SellerID),
		zap.Int64("seller_amount_cents", transaction.SellerAmountCents),
		zap.Int64("platform_fee_cents", transaction.PlatformFeeCents))

	// TODO: Implement actual Stripe transfer to seller's connected account
	// transfer, err := s.stripeClient.CreateTransfer(transaction.SellerAmountCents, transaction.Currency, sellerStripeAccountID)

	return nil
}

// GetTransaction gets a transaction
func (s *paymentService) GetTransaction(ctx context.Context, transactionID string, userID string) (*models.Transaction, error) {
	return nil, nil
}

// ListTransactions lists transactions
func (s *paymentService) ListTransactions(ctx context.Context, params *models.ListTransactionsParams, userID string) (*models.ListTransactionsResponse, error) {
	return &models.ListTransactionsResponse{}, nil
}

// RequestRefund requests a refund
func (s *paymentService) RequestRefund(ctx context.Context, transactionID string, req *models.RefundRequest, userID string) error {
	return nil
}

// ProcessRefund processes a refund
func (s *paymentService) ProcessRefund(ctx context.Context, transactionID string, adminID string) error {
	return nil
}

// AddPaymentMethod adds a payment method
func (s *paymentService) AddPaymentMethod(ctx context.Context, paymentMethodID string, userID string) error {
	return nil
}

// RemovePaymentMethod removes a payment method
func (s *paymentService) RemovePaymentMethod(ctx context.Context, paymentMethodID string, userID string) error {
	return nil
}

// GetPaymentMethods gets payment methods
func (s *paymentService) GetPaymentMethods(ctx context.Context, userID string) ([]interface{}, error) {
	return []interface{}{}, nil
}

// GetRevenueStats gets revenue statistics
func (s *paymentService) GetRevenueStats(ctx context.Context, userID string, startDate, endDate *string) (map[string]interface{}, error) {
	return map[string]interface{}{}, nil
}

// GetSystemRevenue gets system revenue
func (s *paymentService) GetSystemRevenue(ctx context.Context, startDate, endDate *string) (map[string]interface{}, error) {
	return map[string]interface{}{}, nil
}

// HandleStripeWebhook handles Stripe webhooks
func (s *paymentService) HandleStripeWebhook(ctx context.Context, payload []byte, signature string) error {
	return nil
}

// CreateSellerAccount creates a seller account
func (s *paymentService) CreateSellerAccount(ctx context.Context, userID string) error {
	return nil
}

// GetSellerAccountStatus gets seller account status
func (s *paymentService) GetSellerAccountStatus(ctx context.Context, userID string) (map[string]interface{}, error) {
	return map[string]interface{}{}, nil
}
