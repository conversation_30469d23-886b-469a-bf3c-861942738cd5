// Package services provides business logic interfaces for the marketplace service
// Following Context Engineering standards with comprehensive service contracts
package services

import (
	"context"
	"io"

	"github.com/episteme/marketplace/internal/models"
)

// PatternService defines the interface for pattern-related business logic
type PatternService interface {
	// Pattern CRUD operations
	CreatePattern(ctx context.Context, req *models.CreatePatternRequest, authorID string) (*models.Pattern, error)
	GetPattern(ctx context.Context, patternID string, userID *string) (*models.Pattern, error)
	UpdatePattern(ctx context.Context, patternID string, req *models.UpdatePatternRequest, userID string) (*models.Pattern, error)
	DeletePattern(ctx context.Context, patternID string, userID string) error
	ListPatterns(ctx context.Context, params *models.ListPatternsParams) (*models.ListPatternsResponse, error)

	// Pattern publishing
	PublishPattern(ctx context.Context, patternID string, userID string) error
	UnpublishPattern(ctx context.Context, patternID string, userID string) error

	// Pattern validation
	ValidatePattern(ctx context.Context, patternID string) (*models.PatternValidationResult, error)

	// Pattern files
	UploadPatternFile(ctx context.Context, patternID string, filename string, content io.Reader, contentType string, userID string) error
	DownloadPatternFile(ctx context.Context, patternID string, filename string, userID string) (io.ReadCloser, error)
	ListPatternFiles(ctx context.Context, patternID string, userID string) ([]string, error)
	DeletePatternFile(ctx context.Context, patternID string, filename string, userID string) error

	// Pattern reviews
	CreateReview(ctx context.Context, patternID string, req *models.CreateReviewRequest, userID string) (*models.Review, error)
	GetPatternReviews(ctx context.Context, patternID string, page, pageSize int) ([]*models.Review, int64, error)
	UpdateReview(ctx context.Context, reviewID string, req *models.UpdateReviewRequest, userID string) (*models.Review, error)
	DeleteReview(ctx context.Context, reviewID string, userID string) error
	MarkReviewHelpful(ctx context.Context, reviewID string, userID string) error

	// Search and discovery
	SearchPatterns(ctx context.Context, query string, filters map[string]interface{}, page, pageSize int) (*models.ListPatternsResponse, error)
	GetCategories(ctx context.Context) ([]string, error)
	GetLanguages(ctx context.Context) ([]string, error)
	GetTrendingPatterns(ctx context.Context, limit int) ([]*models.Pattern, error)
	GetRecommendedPatterns(ctx context.Context, userID string, limit int) ([]*models.Pattern, error)

	// Analytics
	GetPatternStats(ctx context.Context, patternID string, userID string) (map[string]interface{}, error)
	GetSellerDashboard(ctx context.Context, userID string) (map[string]interface{}, error)
	RecordPatternView(ctx context.Context, patternID string, userID *string, ipAddress string) error
}

// PaymentService defines the interface for payment-related business logic
type PaymentService interface {
	// Purchase flow
	PurchasePattern(ctx context.Context, patternID string, req *models.PurchaseRequest, userID string) (*models.Transaction, error)
	ConfirmPayment(ctx context.Context, paymentIntentID string, userID string) (*models.Transaction, error)

	// Transaction management
	GetTransaction(ctx context.Context, transactionID string, userID string) (*models.Transaction, error)
	ListTransactions(ctx context.Context, params *models.ListTransactionsParams, userID string) (*models.ListTransactionsResponse, error)
	RequestRefund(ctx context.Context, transactionID string, req *models.RefundRequest, userID string) error
	ProcessRefund(ctx context.Context, transactionID string, adminID string) error

	// Payment methods
	AddPaymentMethod(ctx context.Context, paymentMethodID string, userID string) error
	RemovePaymentMethod(ctx context.Context, paymentMethodID string, userID string) error
	GetPaymentMethods(ctx context.Context, userID string) ([]interface{}, error)

	// Revenue and analytics
	GetRevenueStats(ctx context.Context, userID string, startDate, endDate *string) (map[string]interface{}, error)
	GetSystemRevenue(ctx context.Context, startDate, endDate *string) (map[string]interface{}, error)

	// Webhook handling
	HandleStripeWebhook(ctx context.Context, payload []byte, signature string) error

	// Seller onboarding
	CreateSellerAccount(ctx context.Context, userID string) error
	GetSellerAccountStatus(ctx context.Context, userID string) (map[string]interface{}, error)
}

// UserService defines the interface for user-related business logic
type UserService interface {
	// Authentication
	Register(ctx context.Context, req *models.CreateUserRequest) (*models.User, string, error)
	Login(ctx context.Context, req *models.LoginRequest) (*models.LoginResponse, error)
	RefreshToken(ctx context.Context, refreshToken string) (*models.LoginResponse, error)
	Logout(ctx context.Context, userID string) error

	// Password management
	ForgotPassword(ctx context.Context, email string) error
	ResetPassword(ctx context.Context, token string, newPassword string) error
	ChangePassword(ctx context.Context, userID string, currentPassword, newPassword string) error

	// User profile
	GetUser(ctx context.Context, userID string) (*models.User, error)
	GetPublicProfile(ctx context.Context, userID string) (*models.User, error)
	UpdateUser(ctx context.Context, userID string, req *models.UpdateUserRequest) (*models.User, error)
	DeleteUser(ctx context.Context, userID string) error
	UploadAvatar(ctx context.Context, userID string, content io.Reader, contentType string) (string, error)

	// Marketplace profile
	GetMarketplaceProfile(ctx context.Context, userID string) (*models.MarketplaceProfile, error)
	UpdateMarketplaceProfile(ctx context.Context, userID string, req *models.UpdateMarketplaceProfileRequest) (*models.MarketplaceProfile, error)
	BecomeSeller(ctx context.Context, userID string) error

	// User management (admin)
	ListUsers(ctx context.Context, page, pageSize int, filters map[string]interface{}) ([]*models.User, int64, error)
	VerifyUser(ctx context.Context, userID string, adminID string) error
	SuspendUser(ctx context.Context, userID string, adminID string, reason string) error
	UnsuspendUser(ctx context.Context, userID string, adminID string) error

	// Analytics
	GetUserAnalytics(ctx context.Context) (map[string]interface{}, error)

	// Badges and reputation
	AwardBadge(ctx context.Context, userID string, badgeType models.BadgeType) error
	GetUserBadges(ctx context.Context, userID string) ([]*models.SellerBadge, error)
	UpdateReputationScore(ctx context.Context, userID string) error
}

// NotificationService defines the interface for notification-related business logic
type NotificationService interface {
	// Real-time notifications
	SendNotification(ctx context.Context, userID string, notification interface{}) error
	GetNotifications(ctx context.Context, userID string, page, pageSize int) ([]interface{}, int64, error)
	MarkNotificationRead(ctx context.Context, notificationID string, userID string) error
	MarkAllNotificationsRead(ctx context.Context, userID string) error

	// Email notifications
	SendWelcomeEmail(ctx context.Context, userID string) error
	SendPurchaseConfirmation(ctx context.Context, transactionID string) error
	SendSaleNotification(ctx context.Context, transactionID string) error
	SendPatternApprovalEmail(ctx context.Context, patternID string) error
	SendPatternRejectionEmail(ctx context.Context, patternID string, reason string) error

	// Push notifications
	RegisterPushToken(ctx context.Context, userID string, token string, platform string) error
	SendPushNotification(ctx context.Context, userID string, title, body string, data map[string]interface{}) error

	// Subscription management
	Subscribe(ctx context.Context, userID string, channel string) error
	Unsubscribe(ctx context.Context, userID string, channel string) error
	GetSubscriptions(ctx context.Context, userID string) ([]string, error)
}

// ValidationService defines the interface for pattern validation
type ValidationService interface {
	// Pattern validation
	ValidatePatternCode(ctx context.Context, patternID string, code string, language string) (*models.PatternValidationResult, error)
	ValidatePatternMetadata(ctx context.Context, pattern *models.Pattern) (*models.PatternValidationResult, error)
	ValidatePatternSecurity(ctx context.Context, patternID string, code string) (*models.PatternValidationResult, error)

	// Quality scoring
	CalculateQualityScore(ctx context.Context, pattern *models.Pattern, validationResults []*models.PatternValidationResult) (float64, error)

	// Integration with pattern-mining service
	AnalyzePatternComplexity(ctx context.Context, patternID string) (map[string]interface{}, error)
	DetectPatternSimilarity(ctx context.Context, patternID string) ([]string, error)
}

// AnalyticsService defines the interface for analytics and reporting
type AnalyticsService interface {
	// System analytics
	GetSystemOverview(ctx context.Context) (map[string]interface{}, error)
	GetPatternAnalytics(ctx context.Context, startDate, endDate *string) (map[string]interface{}, error)
	GetUserGrowthAnalytics(ctx context.Context, startDate, endDate *string) (map[string]interface{}, error)
	GetRevenueAnalytics(ctx context.Context, startDate, endDate *string) (map[string]interface{}, error)

	// Performance metrics
	GetPerformanceMetrics(ctx context.Context) (map[string]interface{}, error)
	GetErrorRates(ctx context.Context, startDate, endDate *string) (map[string]interface{}, error)

	// Business intelligence
	GetTopPatterns(ctx context.Context, period string, limit int) ([]*models.Pattern, error)
	GetTopSellers(ctx context.Context, period string, limit int) ([]*models.User, error)
	GetMarketTrends(ctx context.Context, period string) (map[string]interface{}, error)

	// Custom reports
	GenerateReport(ctx context.Context, reportType string, parameters map[string]interface{}) (interface{}, error)
	ScheduleReport(ctx context.Context, reportType string, schedule string, recipients []string) error
}

// CommunityService defines the interface for community features
type CommunityService interface {
	// Following system
	FollowUser(ctx context.Context, followerID, followedID string) error
	UnfollowUser(ctx context.Context, followerID, followedID string) error
	GetFollowers(ctx context.Context, userID string, page, pageSize int) ([]*models.User, int64, error)
	GetFollowing(ctx context.Context, userID string, page, pageSize int) ([]*models.User, int64, error)
	IsFollowing(ctx context.Context, followerID, followedID string) (bool, error)

	// Pattern following
	FollowPattern(ctx context.Context, userID, patternID string) error
	UnfollowPattern(ctx context.Context, userID, patternID string) error
	GetPatternFollowers(ctx context.Context, patternID string, page, pageSize int) ([]*models.User, int64, error)
	GetFollowedPatterns(ctx context.Context, userID string, page, pageSize int) ([]*models.Pattern, int64, error)

	// Collections
	CreateCollection(ctx context.Context, userID string, req *models.CreateCollectionRequest) (*models.Collection, error)
	GetCollection(ctx context.Context, collectionID string) (*models.Collection, error)
	UpdateCollection(ctx context.Context, collectionID string, req *models.UpdateCollectionRequest, userID string) (*models.Collection, error)
	DeleteCollection(ctx context.Context, collectionID string, userID string) error
	AddPatternToCollection(ctx context.Context, collectionID, patternID, userID string) error
	RemovePatternFromCollection(ctx context.Context, collectionID, patternID, userID string) error
	GetUserCollections(ctx context.Context, userID string, page, pageSize int) ([]*models.Collection, int64, error)
	GetCollectionPatterns(ctx context.Context, collectionID string, page, pageSize int) ([]*models.Pattern, int64, error)

	// Discussions
	CreateDiscussion(ctx context.Context, patternID string, req *models.CreateDiscussionRequest, userID string) (*models.Discussion, error)
	GetDiscussion(ctx context.Context, discussionID string) (*models.Discussion, error)
	GetPatternDiscussions(ctx context.Context, patternID string, page, pageSize int) ([]*models.Discussion, int64, error)
	ReplyToDiscussion(ctx context.Context, discussionID string, req *models.CreateReplyRequest, userID string) (*models.DiscussionReply, error)
	GetDiscussionReplies(ctx context.Context, discussionID string, page, pageSize int) ([]*models.DiscussionReply, int64, error)

	// Activity feed
	GetUserActivityFeed(ctx context.Context, userID string, page, pageSize int) ([]*models.ActivityItem, int64, error)
	GetFollowingActivityFeed(ctx context.Context, userID string, page, pageSize int) ([]*models.ActivityItem, int64, error)
	RecordActivity(ctx context.Context, activity *models.ActivityItem) error
}

// SearchService defines the interface for search and discovery operations
type SearchService interface {
	// Advanced search
	SearchPatterns(ctx context.Context, req *models.SearchRequest) (*models.SearchResponse, error)
	SearchUsers(ctx context.Context, req *models.SearchRequest) (*models.SearchResponse, error)
	SearchCollections(ctx context.Context, req *models.SearchRequest) (*models.SearchResponse, error)

	// Faceted search
	GetSearchFacets(ctx context.Context, query string, entityType string) (*models.SearchFacets, error)

	// Recommendations
	GetPersonalizedRecommendations(ctx context.Context, userID string, limit int) ([]*models.Pattern, error)
	GetSimilarPatterns(ctx context.Context, patternID string, limit int) ([]*models.Pattern, error)
	GetTrendingPatterns(ctx context.Context, timeframe string, limit int) ([]*models.Pattern, error)
	GetPopularPatterns(ctx context.Context, category string, limit int) ([]*models.Pattern, error)

	// Search analytics
	RecordSearchQuery(ctx context.Context, userID *string, query string, filters map[string]interface{}, resultCount int) error
	GetSearchAnalytics(ctx context.Context, timeframe string) (*models.SearchAnalytics, error)
	GetPopularSearchTerms(ctx context.Context, limit int) ([]*models.SearchTerm, error)

	// Index management
	IndexPattern(ctx context.Context, pattern *models.Pattern) error
	UpdatePatternIndex(ctx context.Context, pattern *models.Pattern) error
	RemovePatternFromIndex(ctx context.Context, patternID string) error
	RebuildIndex(ctx context.Context) error
}
