// Package config provides configuration management for the marketplace service
// Following Context Engineering standards with environment validation
package config

import (
	"fmt"
	"os"
	"strconv"
	"time"
)

// Config holds all configuration for the marketplace service
type Config struct {
	App      AppConfig      `json:"app"`
	Server   ServerConfig   `json:"server"`
	Database DatabaseConfig `json:"database"`
	Cache    CacheConfig    `json:"cache"`
	Payment  PaymentConfig  `json:"payment"`
	Storage  StorageConfig  `json:"storage"`
	Auth     AuthConfig     `json:"auth"`
	Metrics  MetricsConfig  `json:"metrics"`
}

// AppConfig contains application-level configuration
type AppConfig struct {
	Name        string `json:"name"`
	Version     string `json:"version"`
	Environment string `json:"environment"`
	ProjectID   string `json:"project_id"`
}

// ServerConfig contains HTTP server configuration
type ServerConfig struct {
	Port         int           `json:"port"`
	ReadTimeout  time.Duration `json:"read_timeout"`
	WriteTimeout time.Duration `json:"write_timeout"`
	IdleTimeout  time.Duration `json:"idle_timeout"`
}

// DatabaseConfig contains database connection configuration
type DatabaseConfig struct {
	Spanner SpannerConfig `json:"spanner"`
}

// SpannerConfig contains Google Cloud Spanner configuration
type SpannerConfig struct {
	ProjectID  string `json:"project_id"`
	InstanceID string `json:"instance_id"`
	DatabaseID string `json:"database_id"`
}

// CacheConfig contains caching configuration
type CacheConfig struct {
	Redis RedisConfig `json:"redis"`
}

// RedisConfig contains Redis configuration
type RedisConfig struct {
	Address  string `json:"address"`
	Password string `json:"password"`
	DB       int    `json:"db"`
}

// PaymentConfig contains payment processing configuration
type PaymentConfig struct {
	Stripe StripeConfig `json:"stripe"`
}

// StripeConfig contains Stripe payment configuration
type StripeConfig struct {
	SecretKey      string  `json:"secret_key"`
	WebhookSecret  string  `json:"webhook_secret"`
	PlatformFeeRate float64 `json:"platform_fee_rate"`
}

// StorageConfig contains storage configuration
type StorageConfig struct {
	GCS GCSConfig `json:"gcs"`
}

// GCSConfig contains Google Cloud Storage configuration
type GCSConfig struct {
	ProjectID  string `json:"project_id"`
	BucketName string `json:"bucket_name"`
}

// AuthConfig contains authentication configuration
type AuthConfig struct {
	JWTSecret     string        `json:"jwt_secret"`
	TokenDuration time.Duration `json:"token_duration"`
}

// MetricsConfig contains metrics configuration
type MetricsConfig struct {
	Enabled bool   `json:"enabled"`
	Path    string `json:"path"`
}

// Load loads configuration from environment variables
func Load() (*Config, error) {
	cfg := &Config{
		App: AppConfig{
			Name:        getEnv("APP_NAME", "marketplace-service"),
			Version:     getEnv("APP_VERSION", "1.0.0"),
			Environment: getEnv("ENVIRONMENT", "development"),
			ProjectID:   getEnv("GOOGLE_CLOUD_PROJECT", ""),
		},
		Server: ServerConfig{
			Port:         getEnvInt("PORT", 8004),
			ReadTimeout:  getEnvDuration("SERVER_READ_TIMEOUT", 30*time.Second),
			WriteTimeout: getEnvDuration("SERVER_WRITE_TIMEOUT", 30*time.Second),
			IdleTimeout:  getEnvDuration("SERVER_IDLE_TIMEOUT", 120*time.Second),
		},
		Database: DatabaseConfig{
			Spanner: SpannerConfig{
				ProjectID:  getEnv("SPANNER_PROJECT_ID", getEnv("GOOGLE_CLOUD_PROJECT", "")),
				InstanceID: getEnv("SPANNER_INSTANCE_ID", "marketplace-prod"),
				DatabaseID: getEnv("SPANNER_DATABASE_ID", "marketplace"),
			},
		},
		Cache: CacheConfig{
			Redis: RedisConfig{
				Address:  getEnv("REDIS_ADDRESS", "localhost:6379"),
				Password: getEnv("REDIS_PASSWORD", ""),
				DB:       getEnvInt("REDIS_DB", 0),
			},
		},
		Payment: PaymentConfig{
			Stripe: StripeConfig{
				SecretKey:       getEnv("STRIPE_SECRET_KEY", ""),
				WebhookSecret:   getEnv("STRIPE_WEBHOOK_SECRET", ""),
				PlatformFeeRate: getEnvFloat("STRIPE_PLATFORM_FEE_RATE", 0.20), // 20% default
			},
		},
		Storage: StorageConfig{
			GCS: GCSConfig{
				ProjectID:  getEnv("GCS_PROJECT_ID", getEnv("GOOGLE_CLOUD_PROJECT", "")),
				BucketName: getEnv("GCS_BUCKET_NAME", "marketplace-patterns"),
			},
		},
		Auth: AuthConfig{
			JWTSecret:     getEnv("JWT_SECRET", ""),
			TokenDuration: getEnvDuration("JWT_TOKEN_DURATION", 24*time.Hour),
		},
		Metrics: MetricsConfig{
			Enabled: getEnvBool("METRICS_ENABLED", true),
			Path:    getEnv("METRICS_PATH", "/metrics"),
		},
	}

	// Validate required configuration
	if err := cfg.Validate(); err != nil {
		return nil, fmt.Errorf("configuration validation failed: %w", err)
	}

	return cfg, nil
}

// Validate validates the configuration
func (c *Config) Validate() error {
	if c.App.ProjectID == "" {
		return fmt.Errorf("GOOGLE_CLOUD_PROJECT is required")
	}

	if c.Payment.Stripe.SecretKey == "" {
		return fmt.Errorf("STRIPE_SECRET_KEY is required")
	}

	if c.Payment.Stripe.WebhookSecret == "" {
		return fmt.Errorf("STRIPE_WEBHOOK_SECRET is required")
	}

	if c.Auth.JWTSecret == "" {
		return fmt.Errorf("JWT_SECRET is required")
	}

	return nil
}

// Helper functions for environment variable parsing

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

func getEnvFloat(key string, defaultValue float64) float64 {
	if value := os.Getenv(key); value != "" {
		if floatValue, err := strconv.ParseFloat(value, 64); err == nil {
			return floatValue
		}
	}
	return defaultValue
}

func getEnvBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if boolValue, err := strconv.ParseBool(value); err == nil {
			return boolValue
		}
	}
	return defaultValue
}

func getEnvDuration(key string, defaultValue time.Duration) time.Duration {
	if value := os.Getenv(key); value != "" {
		if duration, err := time.ParseDuration(value); err == nil {
			return duration
		}
	}
	return defaultValue
}
