// Package handlers provides HTTP handlers for search and discovery features
package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"github.com/episteme/marketplace/internal/models"
	"github.com/episteme/marketplace/internal/services"
)

// SearchHandlers handles search-related HTTP requests
type SearchHandlers struct {
	searchService services.SearchService
	logger        *zap.Logger
}

// NewSearchHandlers creates new search handlers
func NewSearchHandlers(searchService services.SearchService) *SearchHandlers {
	return &SearchHandlers{
		searchService: searchService,
		logger:        zap.NewNop(),
	}
}

// SearchPatterns handles pattern search requests
func (h *SearchHandlers) SearchPatterns(c *gin.Context) {
	var req models.SearchRequest
	
	// Parse query parameters
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid query parameters"})
		return
	}

	// Set defaults
	if req.Page == 0 {
		req.Page = 1
	}
	if req.PageSize == 0 {
		req.PageSize = 20
	}
	if req.EntityType == "" {
		req.EntityType = "patterns"
	}
	if req.Sort == "" {
		req.Sort = "relevance"
	}

	// Parse filters from query parameters
	req.Filters = make(map[string]interface{})
	if category := c.Query("category"); category != "" {
		req.Filters["category"] = category
	}
	if language := c.Query("language"); language != "" {
		req.Filters["language"] = language
	}
	if author := c.Query("author"); author != "" {
		req.Filters["author"] = author
	}
	if minPrice := c.Query("min_price"); minPrice != "" {
		if price, err := strconv.ParseFloat(minPrice, 64); err == nil {
			req.Filters["min_price"] = price
		}
	}
	if maxPrice := c.Query("max_price"); maxPrice != "" {
		if price, err := strconv.ParseFloat(maxPrice, 64); err == nil {
			req.Filters["max_price"] = price
		}
	}
	if minRating := c.Query("min_rating"); minRating != "" {
		if rating, err := strconv.ParseFloat(minRating, 64); err == nil {
			req.Filters["min_rating"] = rating
		}
	}

	// Get user ID for personalized results
	if userID, exists := c.Get("user_id"); exists {
		userIDStr := userID.(string)
		req.UserID = &userIDStr
	}

	// Check if facets are requested
	req.IncludeFacets = c.Query("include_facets") == "true"

	response, err := h.searchService.SearchPatterns(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error("Failed to search patterns", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Search failed"})
		return
	}

	c.JSON(http.StatusOK, response)
}

// SearchUsers handles user search requests
func (h *SearchHandlers) SearchUsers(c *gin.Context) {
	var req models.SearchRequest
	
	// Parse query parameters
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid query parameters"})
		return
	}

	// Set defaults
	if req.Page == 0 {
		req.Page = 1
	}
	if req.PageSize == 0 {
		req.PageSize = 20
	}
	req.EntityType = "users"

	response, err := h.searchService.SearchUsers(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error("Failed to search users", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Search failed"})
		return
	}

	c.JSON(http.StatusOK, response)
}

// SearchCollections handles collection search requests
func (h *SearchHandlers) SearchCollections(c *gin.Context) {
	var req models.SearchRequest
	
	// Parse query parameters
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid query parameters"})
		return
	}

	// Set defaults
	if req.Page == 0 {
		req.Page = 1
	}
	if req.PageSize == 0 {
		req.PageSize = 20
	}
	req.EntityType = "collections"

	response, err := h.searchService.SearchCollections(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error("Failed to search collections", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Search failed"})
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetSearchFacets handles search facets requests
func (h *SearchHandlers) GetSearchFacets(c *gin.Context) {
	query := c.Query("q")
	entityType := c.DefaultQuery("entity_type", "patterns")

	facets, err := h.searchService.GetSearchFacets(c.Request.Context(), query, entityType)
	if err != nil {
		h.logger.Error("Failed to get search facets", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get facets"})
		return
	}

	c.JSON(http.StatusOK, facets)
}

// GetRecommendations handles personalized recommendation requests
func (h *SearchHandlers) GetRecommendations(c *gin.Context) {
	userID := c.GetString("user_id") // From auth middleware

	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication required"})
		return
	}

	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	if limit > 50 {
		limit = 50 // Cap at 50
	}

	patterns, err := h.searchService.GetPersonalizedRecommendations(c.Request.Context(), userID, limit)
	if err != nil {
		h.logger.Error("Failed to get recommendations", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get recommendations"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"patterns": patterns,
		"count":    len(patterns),
		"user_id":  userID,
	})
}

// GetSimilarPatterns handles similar pattern requests
func (h *SearchHandlers) GetSimilarPatterns(c *gin.Context) {
	patternID := c.Param("pattern_id")
	if patternID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Pattern ID is required"})
		return
	}

	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "5"))
	if limit > 20 {
		limit = 20 // Cap at 20
	}

	patterns, err := h.searchService.GetSimilarPatterns(c.Request.Context(), patternID, limit)
	if err != nil {
		h.logger.Error("Failed to get similar patterns", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get similar patterns"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"patterns":   patterns,
		"count":      len(patterns),
		"pattern_id": patternID,
	})
}

// GetTrendingPatterns handles trending pattern requests
func (h *SearchHandlers) GetTrendingPatterns(c *gin.Context) {
	timeframe := c.DefaultQuery("timeframe", "24h")
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	
	if limit > 50 {
		limit = 50 // Cap at 50
	}

	patterns, err := h.searchService.GetTrendingPatterns(c.Request.Context(), timeframe, limit)
	if err != nil {
		h.logger.Error("Failed to get trending patterns", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get trending patterns"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"patterns":  patterns,
		"count":     len(patterns),
		"timeframe": timeframe,
	})
}

// GetPopularPatterns handles popular pattern requests
func (h *SearchHandlers) GetPopularPatterns(c *gin.Context) {
	category := c.Query("category")
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	
	if limit > 50 {
		limit = 50 // Cap at 50
	}

	patterns, err := h.searchService.GetPopularPatterns(c.Request.Context(), category, limit)
	if err != nil {
		h.logger.Error("Failed to get popular patterns", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get popular patterns"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"patterns": patterns,
		"count":    len(patterns),
		"category": category,
	})
}

// GetSearchAnalytics handles search analytics requests (admin only)
func (h *SearchHandlers) GetSearchAnalytics(c *gin.Context) {
	timeframe := c.DefaultQuery("timeframe", "7d")

	analytics, err := h.searchService.GetSearchAnalytics(c.Request.Context(), timeframe)
	if err != nil {
		h.logger.Error("Failed to get search analytics", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get analytics"})
		return
	}

	c.JSON(http.StatusOK, analytics)
}

// GetPopularSearchTerms handles popular search terms requests
func (h *SearchHandlers) GetPopularSearchTerms(c *gin.Context) {
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	
	if limit > 100 {
		limit = 100 // Cap at 100
	}

	terms, err := h.searchService.GetPopularSearchTerms(c.Request.Context(), limit)
	if err != nil {
		h.logger.Error("Failed to get popular search terms", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get search terms"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"terms": terms,
		"count": len(terms),
	})
}
