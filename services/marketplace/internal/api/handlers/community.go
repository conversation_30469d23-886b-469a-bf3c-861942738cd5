// Package handlers provides HTTP handlers for community features
package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"github.com/episteme/marketplace/internal/models"
	"github.com/episteme/marketplace/internal/services"
)

// CommunityHandlers handles community-related HTTP requests
type CommunityHandlers struct {
	communityService services.CommunityService
	logger           *zap.Logger
}

// NewCommunityHandlers creates new community handlers
func NewCommunityHandlers(communityService services.CommunityService) *CommunityHandlers {
	return &CommunityHandlers{
		communityService: communityService,
		logger:           zap.NewNop(),
	}
}

// FollowUser handles user following requests
func (h *CommunityHandlers) FollowUser(c *gin.Context) {
	userID := c.GetString("user_id") // From auth middleware
	followedID := c.Param("user_id")

	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication required"})
		return
	}

	if followedID == "" {
		c.<PERSON><PERSON>(http.StatusBadRequest, gin.H{"error": "User ID is required"})
		return
	}

	err := h.communityService.FollowUser(c.Request.Context(), userID, followedID)
	if err != nil {
		h.logger.Error("Failed to follow user", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to follow user"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "User followed successfully"})
}

// UnfollowUser handles user unfollowing requests
func (h *CommunityHandlers) UnfollowUser(c *gin.Context) {
	userID := c.GetString("user_id") // From auth middleware
	followedID := c.Param("user_id")

	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication required"})
		return
	}

	if followedID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "User ID is required"})
		return
	}

	err := h.communityService.UnfollowUser(c.Request.Context(), userID, followedID)
	if err != nil {
		h.logger.Error("Failed to unfollow user", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to unfollow user"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "User unfollowed successfully"})
}

// GetFollowers handles getting user followers
func (h *CommunityHandlers) GetFollowers(c *gin.Context) {
	userID := c.Param("user_id")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "User ID is required"})
		return
	}

	followers, total, err := h.communityService.GetFollowers(c.Request.Context(), userID, page, pageSize)
	if err != nil {
		h.logger.Error("Failed to get followers", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get followers"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"followers": followers,
		"total":     total,
		"page":      page,
		"page_size": pageSize,
	})
}

// GetFollowing handles getting users being followed
func (h *CommunityHandlers) GetFollowing(c *gin.Context) {
	userID := c.Param("user_id")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "User ID is required"})
		return
	}

	following, total, err := h.communityService.GetFollowing(c.Request.Context(), userID, page, pageSize)
	if err != nil {
		h.logger.Error("Failed to get following", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get following"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"following": following,
		"total":     total,
		"page":      page,
		"page_size": pageSize,
	})
}

// FollowPattern handles pattern following requests
func (h *CommunityHandlers) FollowPattern(c *gin.Context) {
	userID := c.GetString("user_id") // From auth middleware
	patternID := c.Param("pattern_id")

	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication required"})
		return
	}

	if patternID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Pattern ID is required"})
		return
	}

	err := h.communityService.FollowPattern(c.Request.Context(), userID, patternID)
	if err != nil {
		h.logger.Error("Failed to follow pattern", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to follow pattern"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Pattern followed successfully"})
}

// UnfollowPattern handles pattern unfollowing requests
func (h *CommunityHandlers) UnfollowPattern(c *gin.Context) {
	userID := c.GetString("user_id") // From auth middleware
	patternID := c.Param("pattern_id")

	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication required"})
		return
	}

	if patternID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Pattern ID is required"})
		return
	}

	err := h.communityService.UnfollowPattern(c.Request.Context(), userID, patternID)
	if err != nil {
		h.logger.Error("Failed to unfollow pattern", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to unfollow pattern"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Pattern unfollowed successfully"})
}

// CreateCollection handles collection creation requests
func (h *CommunityHandlers) CreateCollection(c *gin.Context) {
	userID := c.GetString("user_id") // From auth middleware

	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication required"})
		return
	}

	var req models.CreateCollectionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	collection, err := h.communityService.CreateCollection(c.Request.Context(), userID, &req)
	if err != nil {
		h.logger.Error("Failed to create collection", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create collection"})
		return
	}

	c.JSON(http.StatusCreated, collection)
}

// GetCollection handles getting a collection by ID
func (h *CommunityHandlers) GetCollection(c *gin.Context) {
	collectionID := c.Param("collection_id")

	if collectionID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Collection ID is required"})
		return
	}

	collection, err := h.communityService.GetCollection(c.Request.Context(), collectionID)
	if err != nil {
		h.logger.Error("Failed to get collection", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get collection"})
		return
	}

	c.JSON(http.StatusOK, collection)
}

// UpdateCollection handles collection update requests
func (h *CommunityHandlers) UpdateCollection(c *gin.Context) {
	userID := c.GetString("user_id") // From auth middleware
	collectionID := c.Param("collection_id")

	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication required"})
		return
	}

	if collectionID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Collection ID is required"})
		return
	}

	var req models.UpdateCollectionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	collection, err := h.communityService.UpdateCollection(c.Request.Context(), collectionID, &req, userID)
	if err != nil {
		h.logger.Error("Failed to update collection", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update collection"})
		return
	}

	c.JSON(http.StatusOK, collection)
}

// DeleteCollection handles collection deletion requests
func (h *CommunityHandlers) DeleteCollection(c *gin.Context) {
	userID := c.GetString("user_id") // From auth middleware
	collectionID := c.Param("collection_id")

	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication required"})
		return
	}

	if collectionID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Collection ID is required"})
		return
	}

	err := h.communityService.DeleteCollection(c.Request.Context(), collectionID, userID)
	if err != nil {
		h.logger.Error("Failed to delete collection", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete collection"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Collection deleted successfully"})
}

// AddPatternToCollection handles adding patterns to collections
func (h *CommunityHandlers) AddPatternToCollection(c *gin.Context) {
	userID := c.GetString("user_id") // From auth middleware
	collectionID := c.Param("collection_id")
	patternID := c.Param("pattern_id")

	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication required"})
		return
	}

	if collectionID == "" || patternID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Collection ID and Pattern ID are required"})
		return
	}

	err := h.communityService.AddPatternToCollection(c.Request.Context(), collectionID, patternID, userID)
	if err != nil {
		h.logger.Error("Failed to add pattern to collection", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to add pattern to collection"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Pattern added to collection successfully"})
}

// RemovePatternFromCollection handles removing patterns from collections
func (h *CommunityHandlers) RemovePatternFromCollection(c *gin.Context) {
	userID := c.GetString("user_id") // From auth middleware
	collectionID := c.Param("collection_id")
	patternID := c.Param("pattern_id")

	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication required"})
		return
	}

	if collectionID == "" || patternID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Collection ID and Pattern ID are required"})
		return
	}

	err := h.communityService.RemovePatternFromCollection(c.Request.Context(), collectionID, patternID, userID)
	if err != nil {
		h.logger.Error("Failed to remove pattern from collection", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to remove pattern from collection"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Pattern removed from collection successfully"})
}

// GetUserCollections handles getting user collections
func (h *CommunityHandlers) GetUserCollections(c *gin.Context) {
	userID := c.Param("user_id")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "User ID is required"})
		return
	}

	collections, total, err := h.communityService.GetUserCollections(c.Request.Context(), userID, page, pageSize)
	if err != nil {
		h.logger.Error("Failed to get user collections", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user collections"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"collections": collections,
		"total":       total,
		"page":        page,
		"page_size":   pageSize,
	})
}

// CreateDiscussion handles discussion creation requests
func (h *CommunityHandlers) CreateDiscussion(c *gin.Context) {
	userID := c.GetString("user_id") // From auth middleware
	patternID := c.Param("pattern_id")

	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication required"})
		return
	}

	if patternID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Pattern ID is required"})
		return
	}

	var req models.CreateDiscussionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	discussion, err := h.communityService.CreateDiscussion(c.Request.Context(), patternID, &req, userID)
	if err != nil {
		h.logger.Error("Failed to create discussion", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create discussion"})
		return
	}

	c.JSON(http.StatusCreated, discussion)
}

// GetDiscussion handles getting a discussion by ID
func (h *CommunityHandlers) GetDiscussion(c *gin.Context) {
	discussionID := c.Param("discussion_id")

	if discussionID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Discussion ID is required"})
		return
	}

	discussion, err := h.communityService.GetDiscussion(c.Request.Context(), discussionID)
	if err != nil {
		h.logger.Error("Failed to get discussion", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get discussion"})
		return
	}

	c.JSON(http.StatusOK, discussion)
}

// GetPatternDiscussions handles getting discussions for a pattern
func (h *CommunityHandlers) GetPatternDiscussions(c *gin.Context) {
	patternID := c.Param("pattern_id")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	if patternID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Pattern ID is required"})
		return
	}

	discussions, total, err := h.communityService.GetPatternDiscussions(c.Request.Context(), patternID, page, pageSize)
	if err != nil {
		h.logger.Error("Failed to get pattern discussions", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get pattern discussions"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"discussions": discussions,
		"total":       total,
		"page":        page,
		"page_size":   pageSize,
	})
}

// ReplyToDiscussion handles discussion reply requests
func (h *CommunityHandlers) ReplyToDiscussion(c *gin.Context) {
	userID := c.GetString("user_id") // From auth middleware
	discussionID := c.Param("discussion_id")

	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication required"})
		return
	}

	if discussionID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Discussion ID is required"})
		return
	}

	var req models.CreateReplyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	reply, err := h.communityService.ReplyToDiscussion(c.Request.Context(), discussionID, &req, userID)
	if err != nil {
		h.logger.Error("Failed to create discussion reply", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create discussion reply"})
		return
	}

	c.JSON(http.StatusCreated, reply)
}

// GetUserActivityFeed handles getting user activity feed
func (h *CommunityHandlers) GetUserActivityFeed(c *gin.Context) {
	userID := c.Param("user_id")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "User ID is required"})
		return
	}

	activities, total, err := h.communityService.GetUserActivityFeed(c.Request.Context(), userID, page, pageSize)
	if err != nil {
		h.logger.Error("Failed to get user activity feed", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user activity feed"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"activities": activities,
		"total":      total,
		"page":       page,
		"page_size":  pageSize,
	})
}
