// Package handlers provides payment HTTP handlers
package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"github.com/episteme/marketplace/internal/models"
	"github.com/episteme/marketplace/internal/services"
)

// PaymentHandler handles payment-related HTTP requests
type PaymentHandler struct {
	paymentService services.PaymentService
	logger         *zap.Logger
}

// NewPaymentHandler creates a new payment handler
func NewPaymentHandler(paymentService services.PaymentService, logger *zap.Logger) *PaymentHandler {
	return &PaymentHandler{
		paymentService: paymentService,
		logger:         logger,
	}
}

// PurchasePattern handles pattern purchase requests
func (h *PaymentHandler) PurchasePattern(c *gin.Context) {
	patternID := c.Param("id")
	if patternID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Pattern ID is required"})
		return
	}

	var req models.PurchaseRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	transaction, err := h.paymentService.PurchasePattern(c.Request.Context(), patternID, &req, userID.(string))
	if err != nil {
		h.logger.Error("Failed to purchase pattern", zap.Error(err))
		if err.Error() == "pattern not found" || err.Error() == "pattern not available for purchase" || err.Error() == "cannot purchase your own pattern" {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to process purchase"})
		}
		return
	}

	c.JSON(http.StatusCreated, transaction)
}

// ConfirmPayment handles payment confirmation requests
func (h *PaymentHandler) ConfirmPayment(c *gin.Context) {
	paymentIntentID := c.Param("payment_intent_id")
	if paymentIntentID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Payment intent ID is required"})
		return
	}

	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	transaction, err := h.paymentService.ConfirmPayment(c.Request.Context(), paymentIntentID, userID.(string))
	if err != nil {
		h.logger.Error("Failed to confirm payment", zap.Error(err))
		if err.Error() == "transaction not found" || err.Error() == "unauthorized: transaction does not belong to user" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Transaction not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to confirm payment"})
		}
		return
	}

	c.JSON(http.StatusOK, transaction)
}

// GetTransactions handles transaction listing requests
func (h *PaymentHandler) GetTransactions(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, gin.H{"message": "Not implemented"})
}

// GetTransaction handles single transaction retrieval requests
func (h *PaymentHandler) GetTransaction(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, gin.H{"message": "Not implemented"})
}

// RequestRefund handles refund requests
func (h *PaymentHandler) RequestRefund(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, gin.H{"message": "Not implemented"})
}

// ProcessRefund handles refund processing (admin)
func (h *PaymentHandler) ProcessRefund(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, gin.H{"message": "Not implemented"})
}

// GetPaymentMethods handles payment method retrieval requests
func (h *PaymentHandler) GetPaymentMethods(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, gin.H{"message": "Not implemented"})
}

// AddPaymentMethod handles payment method addition requests
func (h *PaymentHandler) AddPaymentMethod(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, gin.H{"message": "Not implemented"})
}

// RemovePaymentMethod handles payment method removal requests
func (h *PaymentHandler) RemovePaymentMethod(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, gin.H{"message": "Not implemented"})
}

// GetMyPurchases handles user purchase history requests
func (h *PaymentHandler) GetMyPurchases(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, gin.H{"message": "Not implemented"})
}

// GetMySales handles user sales history requests
func (h *PaymentHandler) GetMySales(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, gin.H{"message": "Not implemented"})
}

// GetRevenueStats handles revenue statistics requests
func (h *PaymentHandler) GetRevenueStats(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, gin.H{"message": "Not implemented"})
}

// GetAllTransactions handles all transaction retrieval requests (admin)
func (h *PaymentHandler) GetAllTransactions(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, gin.H{"message": "Not implemented"})
}

// GetSystemRevenue handles system revenue requests (admin)
func (h *PaymentHandler) GetSystemRevenue(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, gin.H{"message": "Not implemented"})
}
