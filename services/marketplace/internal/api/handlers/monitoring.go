// Package handlers provides HTTP handlers for monitoring and analytics endpoints
package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"github.com/episteme/marketplace/internal/monitoring"
)

// MonitoringHandlers provides HTTP handlers for monitoring endpoints
type MonitoringHandlers struct {
	dashboardService monitoring.DashboardService
	metricsCollector *monitoring.MetricsCollector
	logger           *zap.Logger
}

// NewMonitoringHandlers creates new monitoring handlers
func NewMonitoringHandlers(
	dashboardService monitoring.DashboardService,
	metricsCollector *monitoring.MetricsCollector,
	logger *zap.Logger,
) *MonitoringHandlers {
	return &MonitoringHandlers{
		dashboardService: dashboardService,
		metricsCollector: metricsCollector,
		logger:           logger,
	}
}

// GetRealTimeMetrics returns real-time system metrics
// @Summary Get real-time metrics
// @Description Returns current system performance and business metrics
// @Tags monitoring
// @Produce json
// @Success 200 {object} monitoring.RealTimeMetrics
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/monitoring/realtime [get]
func (h *MonitoringHandlers) GetRealTimeMetrics(c *gin.Context) {
	metrics, err := h.dashboardService.GetRealTimeMetrics(c.Request.Context())
	if err != nil {
		h.logger.Error("Failed to get real-time metrics", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to retrieve real-time metrics",
			"code":    "METRICS_ERROR",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, metrics)
}

// GetBusinessMetrics returns business performance metrics
// @Summary Get business metrics
// @Description Returns business performance metrics for a specified period
// @Tags monitoring
// @Param period query string false "Time period (day, week, month, quarter, year)" default(month)
// @Produce json
// @Success 200 {object} monitoring.BusinessMetricsReport
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/monitoring/business [get]
func (h *MonitoringHandlers) GetBusinessMetrics(c *gin.Context) {
	period := c.DefaultQuery("period", "month")

	// Validate period
	validPeriods := map[string]bool{
		"day": true, "week": true, "month": true, "quarter": true, "year": true,
	}
	if !validPeriods[period] {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid period parameter",
			"code":    "INVALID_PERIOD",
			"details": "Period must be one of: day, week, month, quarter, year",
		})
		return
	}

	metrics, err := h.dashboardService.GetBusinessMetrics(c.Request.Context(), period)
	if err != nil {
		h.logger.Error("Failed to get business metrics", zap.Error(err), zap.String("period", period))
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to retrieve business metrics",
			"code":    "METRICS_ERROR",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, metrics)
}

// GetPerformanceReport returns system performance report
// @Summary Get performance report
// @Description Returns detailed system performance metrics
// @Tags monitoring
// @Param timeRange query string false "Time range (1h, 6h, 24h, 7d, 30d)" default(24h)
// @Produce json
// @Success 200 {object} monitoring.PerformanceReport
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/monitoring/performance [get]
func (h *MonitoringHandlers) GetPerformanceReport(c *gin.Context) {
	timeRange := c.DefaultQuery("timeRange", "24h")

	// Validate time range
	validRanges := map[string]bool{
		"1h": true, "6h": true, "24h": true, "7d": true, "30d": true,
	}
	if !validRanges[timeRange] {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid timeRange parameter",
			"code":    "INVALID_TIME_RANGE",
			"details": "Time range must be one of: 1h, 6h, 24h, 7d, 30d",
		})
		return
	}

	report, err := h.dashboardService.GetPerformanceReport(c.Request.Context(), timeRange)
	if err != nil {
		h.logger.Error("Failed to get performance report", zap.Error(err), zap.String("timeRange", timeRange))
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to retrieve performance report",
			"code":    "METRICS_ERROR",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, report)
}

// GetUserAnalytics returns user behavior analytics
// @Summary Get user analytics
// @Description Returns user behavior and engagement analytics
// @Tags monitoring
// @Param period query string false "Time period (day, week, month, quarter)" default(month)
// @Produce json
// @Success 200 {object} monitoring.UserAnalytics
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/monitoring/users [get]
func (h *MonitoringHandlers) GetUserAnalytics(c *gin.Context) {
	period := c.DefaultQuery("period", "month")

	analytics, err := h.dashboardService.GetUserAnalytics(c.Request.Context(), period)
	if err != nil {
		h.logger.Error("Failed to get user analytics", zap.Error(err), zap.String("period", period))
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to retrieve user analytics",
			"code":    "ANALYTICS_ERROR",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, analytics)
}

// GetPatternAnalytics returns pattern-specific analytics
// @Summary Get pattern analytics
// @Description Returns analytics for a specific pattern
// @Tags monitoring
// @Param patternId path string true "Pattern ID"
// @Param period query string false "Time period (day, week, month, quarter)" default(month)
// @Produce json
// @Success 200 {object} monitoring.PatternAnalytics
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/monitoring/patterns/{patternId} [get]
func (h *MonitoringHandlers) GetPatternAnalytics(c *gin.Context) {
	patternID := c.Param("patternId")
	if patternID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Pattern ID is required",
			"code":    "MISSING_PATTERN_ID",
			"details": "Pattern ID must be provided in the URL path",
		})
		return
	}

	period := c.DefaultQuery("period", "month")

	analytics, err := h.dashboardService.GetPatternAnalytics(c.Request.Context(), patternID, period)
	if err != nil {
		h.logger.Error("Failed to get pattern analytics",
			zap.Error(err),
			zap.String("patternId", patternID),
			zap.String("period", period))
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to retrieve pattern analytics",
			"code":    "ANALYTICS_ERROR",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, analytics)
}

// GetTrendingPatterns returns trending patterns
// @Summary Get trending patterns
// @Description Returns list of trending patterns based on recent activity
// @Tags monitoring
// @Param limit query int false "Number of patterns to return" default(10)
// @Produce json
// @Success 200 {array} monitoring.TrendingPattern
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/monitoring/trending [get]
func (h *MonitoringHandlers) GetTrendingPatterns(c *gin.Context) {
	limitStr := c.DefaultQuery("limit", "10")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit < 1 || limit > 100 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid limit parameter",
			"code":    "INVALID_LIMIT",
			"details": "Limit must be a number between 1 and 100",
		})
		return
	}

	patterns, err := h.dashboardService.GetTrendingPatterns(c.Request.Context(), limit)
	if err != nil {
		h.logger.Error("Failed to get trending patterns", zap.Error(err), zap.Int("limit", limit))
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to retrieve trending patterns",
			"code":    "ANALYTICS_ERROR",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, patterns)
}

// GetRevenueReport returns revenue analytics
// @Summary Get revenue report
// @Description Returns comprehensive revenue analytics
// @Tags monitoring
// @Param period query string false "Time period (day, week, month, quarter, year)" default(month)
// @Produce json
// @Success 200 {object} monitoring.RevenueReport
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/monitoring/revenue [get]
func (h *MonitoringHandlers) GetRevenueReport(c *gin.Context) {
	period := c.DefaultQuery("period", "month")

	report, err := h.dashboardService.GetRevenueReport(c.Request.Context(), period)
	if err != nil {
		h.logger.Error("Failed to get revenue report", zap.Error(err), zap.String("period", period))
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to retrieve revenue report",
			"code":    "ANALYTICS_ERROR",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, report)
}

// GetSellerAnalytics returns seller-specific analytics
// @Summary Get seller analytics
// @Description Returns analytics for a specific seller
// @Tags monitoring
// @Param sellerId path string true "Seller ID"
// @Param period query string false "Time period (day, week, month, quarter)" default(month)
// @Produce json
// @Success 200 {object} monitoring.SellerAnalytics
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/monitoring/sellers/{sellerId} [get]
func (h *MonitoringHandlers) GetSellerAnalytics(c *gin.Context) {
	sellerID := c.Param("sellerId")
	if sellerID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Seller ID is required",
			"code":    "MISSING_SELLER_ID",
			"details": "Seller ID must be provided in the URL path",
		})
		return
	}

	period := c.DefaultQuery("period", "month")

	analytics, err := h.dashboardService.GetSellerAnalytics(c.Request.Context(), sellerID, period)
	if err != nil {
		h.logger.Error("Failed to get seller analytics",
			zap.Error(err),
			zap.String("sellerId", sellerID),
			zap.String("period", period))
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to retrieve seller analytics",
			"code":    "ANALYTICS_ERROR",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, analytics)
}

// GenerateCustomReport generates a custom analytics report
// @Summary Generate custom report
// @Description Generates a custom analytics report based on provided configuration
// @Tags monitoring
// @Accept json
// @Produce json
// @Param config body monitoring.ReportConfig true "Report configuration"
// @Success 200 {object} monitoring.CustomReport
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/monitoring/reports/custom [post]
func (h *MonitoringHandlers) GenerateCustomReport(c *gin.Context) {
	var config monitoring.ReportConfig
	if err := c.ShouldBindJSON(&config); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request body",
			"code":    "INVALID_REQUEST",
			"details": err.Error(),
		})
		return
	}

	// Validate report configuration
	if config.Type == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Report type is required",
			"code":    "MISSING_REPORT_TYPE",
			"details": "Report type must be specified",
		})
		return
	}

	report, err := h.dashboardService.GenerateCustomReport(c.Request.Context(), &config)
	if err != nil {
		h.logger.Error("Failed to generate custom report", zap.Error(err), zap.Any("config", config))
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to generate custom report",
			"code":    "REPORT_GENERATION_ERROR",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, report)
}

// GetSystemHealth returns system health status
// @Summary Get system health
// @Description Returns comprehensive system health status
// @Tags monitoring
// @Produce json
// @Success 200 {object} map[string]interface{}
// @Router /api/v1/monitoring/health [get]
func (h *MonitoringHandlers) GetSystemHealth(c *gin.Context) {
	// This would typically check various system components
	health := map[string]interface{}{
		"status":    "healthy",
		"timestamp": "2024-01-01T00:00:00Z",
		"services": map[string]interface{}{
			"database": map[string]interface{}{
				"status":      "healthy",
				"latency_ms":  12.3,
				"connections": 23,
			},
			"cache": map[string]interface{}{
				"status":   "healthy",
				"hit_rate": 94.5,
			},
			"storage": map[string]interface{}{
				"status":        "healthy",
				"usage_percent": 45.2,
			},
		},
		"metrics": map[string]interface{}{
			"requests_per_minute": 3420,
			"error_rate":          0.12,
			"avg_response_time":   45.2,
		},
	}

	c.JSON(http.StatusOK, health)
}
