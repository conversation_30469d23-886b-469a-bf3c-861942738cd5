// Package api provides HTTP API routing and middleware for the marketplace service
// Following Context Engineering standards with production-ready API patterns
package api

import (
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"github.com/episteme/marketplace/internal/api/handlers"
	"github.com/episteme/marketplace/internal/api/middleware"
	"github.com/episteme/marketplace/internal/audit"
	"github.com/episteme/marketplace/internal/clients"
	"github.com/episteme/marketplace/internal/config"
	"github.com/episteme/marketplace/internal/monitoring"
	"github.com/episteme/marketplace/internal/services"
)

// Services holds all service dependencies
type Services struct {
	Pattern      services.PatternService
	Payment      services.PaymentService
	User         services.UserService
	Notification services.NotificationService
	Community    services.CommunityService
	Search       services.SearchService
}

// Dependencies holds all external dependencies for the router
type Dependencies struct {
	Config           *config.Config
	SpannerClient    *clients.SpannerClient
	RedisClient      *clients.RedisClient
	AuditLogger      *audit.Logger
	MetricsCollector *monitoring.MetricsCollector
	DashboardService monitoring.DashboardService
}

// NewRouter creates a new HTTP router with all routes and middleware
func NewRouter(services *Services, deps *Dependencies, logger *zap.Logger) *gin.Engine {
	// Set Gin mode based on environment
	gin.SetMode(gin.ReleaseMode)

	router := gin.New()

	// Initialize security middleware
	authMiddleware := middleware.NewAuthMiddleware(deps.Config, logger)
	securityMiddleware := middleware.NewSecurityMiddleware(deps.Config, logger)
	rateLimiter := middleware.NewRateLimiter(deps.RedisClient, logger)
	performanceMiddleware := middleware.NewPerformanceMiddleware(logger)

	// Set up performance monitoring for database client
	deps.SpannerClient.SetPerformanceMonitor(performanceMiddleware.RecordDatabaseQuery)

	// Global middleware
	router.Use(gin.Recovery())
	router.Use(middleware.Logger(logger))
	router.Use(performanceMiddleware.Monitor())
	router.Use(performanceMiddleware.ResponseCompression())
	router.Use(securityMiddleware.RequestID())
	router.Use(securityMiddleware.SecurityHeaders())
	router.Use(securityMiddleware.CORS())
	router.Use(securityMiddleware.InputValidation())
	router.Use(rateLimiter.RateLimit(middleware.RateLimitConfig{
		Requests: 1000,
		Window:   time.Hour,
		KeyFunc:  middleware.IPBasedKey,
	}))
	router.Use(middleware.Metrics())

	// Initialize handlers
	patternHandler := handlers.NewPatternHandler(services.Pattern, logger)
	paymentHandler := handlers.NewPaymentHandler(services.Payment, logger)
	userHandler := handlers.NewUserHandler(services.User, logger)
	webhookHandler := handlers.NewWebhookHandler(services.Payment, logger)
	communityHandler := handlers.NewCommunityHandlers(services.Community)
	searchHandler := handlers.NewSearchHandlers(services.Search)
	monitoringHandler := handlers.NewMonitoringHandlers(deps.DashboardService, deps.MetricsCollector, logger)

	// Health check endpoints (no auth required)
	router.GET("/health", handlers.HealthCheck)
	router.GET("/ready", handlers.ReadinessCheck)

	// API version 1
	v1 := router.Group("/api/v1")
	{
		// Public endpoints (no authentication required)
		public := v1.Group("/public")
		{
			// Pattern browsing
			public.GET("/patterns", patternHandler.ListPatterns)
			public.GET("/patterns/:id", patternHandler.GetPattern)
			public.GET("/patterns/:id/reviews", patternHandler.GetPatternReviews)

			// Categories and search
			public.GET("/categories", patternHandler.GetCategories)
			public.GET("/languages", patternHandler.GetLanguages)
			public.GET("/search", searchHandler.SearchPatterns)
			public.GET("/search/patterns", searchHandler.SearchPatterns)
			public.GET("/search/users", searchHandler.SearchUsers)
			public.GET("/search/collections", searchHandler.SearchCollections)
			public.GET("/search/facets", searchHandler.GetSearchFacets)

			// Discovery endpoints
			public.GET("/trending", searchHandler.GetTrendingPatterns)
			public.GET("/popular", searchHandler.GetPopularPatterns)
			public.GET("/patterns/:pattern_id/similar", searchHandler.GetSimilarPatterns)

			// User profiles (public info only)
			public.GET("/users/:id", userHandler.GetPublicProfile)
			public.GET("/users/:id/patterns", patternHandler.GetUserPatterns)
		}

		// Authentication endpoints
		auth := v1.Group("/auth")
		{
			auth.POST("/register", userHandler.Register)
			auth.POST("/login", userHandler.Login)
			auth.POST("/refresh", userHandler.RefreshToken)
			auth.POST("/logout", authMiddleware.Auth(), userHandler.Logout)
			auth.POST("/forgot-password", userHandler.ForgotPassword)
			auth.POST("/reset-password", userHandler.ResetPassword)
		}

		// Webhook endpoints (special authentication)
		webhooks := v1.Group("/webhooks")
		{
			webhooks.POST("/stripe", middleware.StripeWebhook(), webhookHandler.HandleStripeWebhook)
		}

		// Protected endpoints (authentication required)
		protected := v1.Group("/")
		protected.Use(authMiddleware.Auth())
		{
			// User management
			users := protected.Group("/users")
			{
				users.GET("/me", userHandler.GetCurrentUser)
				users.PUT("/me", userHandler.UpdateCurrentUser)
				users.DELETE("/me", userHandler.DeleteCurrentUser)
				users.POST("/me/avatar", userHandler.UploadAvatar)

				// Marketplace profile
				users.GET("/me/marketplace-profile", userHandler.GetMarketplaceProfile)
				users.PUT("/me/marketplace-profile", userHandler.UpdateMarketplaceProfile)
				users.POST("/me/become-seller", userHandler.BecomeSeller)

				// User's patterns and purchases
				users.GET("/me/patterns", patternHandler.GetMyPatterns)
				users.GET("/me/purchases", paymentHandler.GetMyPurchases)
				users.GET("/me/sales", paymentHandler.GetMySales)
				users.GET("/me/reviews", patternHandler.GetMyReviews)
			}

			// Pattern management
			patterns := protected.Group("/patterns")
			{
				patterns.POST("/", middleware.SellerOnly(), patternHandler.CreatePattern)
				patterns.PUT("/:id", middleware.PatternOwner(), patternHandler.UpdatePattern)
				patterns.DELETE("/:id", middleware.PatternOwner(), patternHandler.DeletePattern)
				patterns.POST("/:id/publish", middleware.PatternOwner(), patternHandler.PublishPattern)
				patterns.POST("/:id/unpublish", middleware.PatternOwner(), patternHandler.UnpublishPattern)

				// Pattern files
				patterns.POST("/:id/files", middleware.PatternOwner(), patternHandler.UploadPatternFile)
				patterns.GET("/:id/files", middleware.PatternAccess(), patternHandler.ListPatternFiles)
				patterns.GET("/:id/files/:filename", middleware.PatternAccess(), patternHandler.DownloadPatternFile)
				patterns.DELETE("/:id/files/:filename", middleware.PatternOwner(), patternHandler.DeletePatternFile)

				// Pattern reviews
				patterns.POST("/:id/reviews", middleware.PurchaseRequired(), patternHandler.CreateReview)
				patterns.PUT("/:id/reviews/:review_id", middleware.ReviewOwner(), patternHandler.UpdateReview)
				patterns.DELETE("/:id/reviews/:review_id", middleware.ReviewOwner(), patternHandler.DeleteReview)
				patterns.POST("/:id/reviews/:review_id/helpful", patternHandler.MarkReviewHelpful)
			}

			// Payment and purchasing
			payments := protected.Group("/payments")
			{
				payments.POST("/purchase/:pattern_id", paymentHandler.PurchasePattern)
				payments.POST("/payment-intents/:id/confirm", paymentHandler.ConfirmPayment)
				payments.GET("/transactions", paymentHandler.GetTransactions)
				payments.GET("/transactions/:id", paymentHandler.GetTransaction)
				payments.POST("/transactions/:id/refund", paymentHandler.RequestRefund)

				// Payment methods
				payments.GET("/payment-methods", paymentHandler.GetPaymentMethods)
				payments.POST("/payment-methods", paymentHandler.AddPaymentMethod)
				payments.DELETE("/payment-methods/:id", paymentHandler.RemovePaymentMethod)
			}

			// Collections
			collections := protected.Group("/collections")
			{
				collections.POST("/", communityHandler.CreateCollection)
				collections.GET("/:collection_id", communityHandler.GetCollection)
				collections.PUT("/:collection_id", communityHandler.UpdateCollection)
				collections.DELETE("/:collection_id", communityHandler.DeleteCollection)
				collections.POST("/:collection_id/patterns/:pattern_id", communityHandler.AddPatternToCollection)
				collections.DELETE("/:collection_id/patterns/:pattern_id", communityHandler.RemovePatternFromCollection)
			}

			// Community features
			community := protected.Group("/community")
			{
				// User following
				community.POST("/users/:user_id/follow", communityHandler.FollowUser)
				community.DELETE("/users/:user_id/follow", communityHandler.UnfollowUser)
				community.GET("/users/:user_id/followers", communityHandler.GetFollowers)
				community.GET("/users/:user_id/following", communityHandler.GetFollowing)

				// Pattern following
				community.POST("/patterns/:pattern_id/follow", communityHandler.FollowPattern)
				community.DELETE("/patterns/:pattern_id/follow", communityHandler.UnfollowPattern)

				// User collections
				community.GET("/users/:user_id/collections", communityHandler.GetUserCollections)

				// Discussions
				community.POST("/patterns/:pattern_id/discussions", communityHandler.CreateDiscussion)
				community.GET("/patterns/:pattern_id/discussions", communityHandler.GetPatternDiscussions)
				community.GET("/discussions/:discussion_id", communityHandler.GetDiscussion)
				community.POST("/discussions/:discussion_id/replies", communityHandler.ReplyToDiscussion)

				// Activity feeds
				community.GET("/users/:user_id/activity", communityHandler.GetUserActivityFeed)
			}

			// Search and discovery (personalized)
			search := protected.Group("/search")
			{
				search.GET("/recommendations", searchHandler.GetRecommendations)
			}

			// Analytics (for sellers)
			analytics := protected.Group("/analytics")
			analytics.Use(middleware.SellerOnly())
			{
				analytics.GET("/dashboard", patternHandler.GetSellerDashboard)
				analytics.GET("/patterns/:id/stats", middleware.PatternOwner(), patternHandler.GetPatternStats)
				analytics.GET("/revenue", paymentHandler.GetRevenueStats)
			}
		}

		// Admin endpoints (admin authentication required)
		admin := v1.Group("/admin")
		admin.Use(authMiddleware.Auth(), middleware.AdminOnly())
		{
			// User management
			admin.GET("/users", userHandler.ListUsers)
			admin.GET("/users/:id", userHandler.GetUser)
			admin.PUT("/users/:id", userHandler.UpdateUser)
			admin.POST("/users/:id/verify", userHandler.VerifyUser)
			admin.POST("/users/:id/suspend", userHandler.SuspendUser)
			admin.POST("/users/:id/unsuspend", userHandler.UnsuspendUser)

			// Pattern moderation
			admin.GET("/patterns/pending", patternHandler.GetPendingPatterns)
			admin.POST("/patterns/:id/approve", patternHandler.ApprovePattern)
			admin.POST("/patterns/:id/reject", patternHandler.RejectPattern)
			admin.POST("/patterns/:id/suspend", patternHandler.SuspendPattern)

			// Transaction management
			admin.GET("/transactions", paymentHandler.GetAllTransactions)
			admin.POST("/transactions/:id/refund", paymentHandler.ProcessRefund)

			// System analytics
			admin.GET("/analytics/overview", handlers.GetSystemAnalytics)
			admin.GET("/analytics/revenue", paymentHandler.GetSystemRevenue)
			admin.GET("/analytics/patterns", patternHandler.GetPatternAnalytics)
			admin.GET("/analytics/users", userHandler.GetUserAnalytics)
			admin.GET("/analytics/search", searchHandler.GetSearchAnalytics)
			admin.GET("/search/terms", searchHandler.GetPopularSearchTerms)
		}
	}

	// Monitoring and analytics endpoints
	monitoring := v1.Group("/monitoring")
	monitoring.Use(authMiddleware.Auth())
	{
		// Real-time metrics (accessible to all authenticated users)
		monitoring.GET("/realtime", monitoringHandler.GetRealTimeMetrics)
		monitoring.GET("/health", monitoringHandler.GetSystemHealth)

		// Business metrics (seller and admin access)
		monitoring.GET("/business", middleware.SellerOnly(), monitoringHandler.GetBusinessMetrics)
		monitoring.GET("/performance", middleware.SellerOnly(), monitoringHandler.GetPerformanceReport)
		monitoring.GET("/users", middleware.SellerOnly(), monitoringHandler.GetUserAnalytics)
		monitoring.GET("/revenue", middleware.SellerOnly(), monitoringHandler.GetRevenueReport)
		monitoring.GET("/trending", monitoringHandler.GetTrendingPatterns)

		// Pattern-specific analytics
		monitoring.GET("/patterns/:patternId", monitoringHandler.GetPatternAnalytics)

		// Seller analytics
		monitoring.GET("/sellers/:sellerId", middleware.SellerOnly(), monitoringHandler.GetSellerAnalytics)

		// Admin-only analytics
		adminMonitoring := monitoring.Group("")
		adminMonitoring.Use(middleware.AdminOnly())
		{
			adminMonitoring.POST("/reports/custom", monitoringHandler.GenerateCustomReport)
		}
	}

	// WebSocket endpoints for real-time features
	ws := router.Group("/ws")
	ws.Use(authMiddleware.Auth())
	{
		ws.GET("/notifications", handlers.HandleWebSocket)
	}

	return router
}

// SetupRoutes is an alternative function for setting up routes with more granular control
func SetupRoutes(router *gin.Engine, services *Services, deps *Dependencies, logger *zap.Logger) {
	// This function can be used for more complex routing setups
	// Currently delegates to NewRouter for consistency
	newRouter := NewRouter(services, deps, logger)

	// Copy routes from newRouter to the provided router
	for _, route := range newRouter.Routes() {
		router.Handle(route.Method, route.Path, route.HandlerFunc)
	}
}
