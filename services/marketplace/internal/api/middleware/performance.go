// Package middleware provides performance monitoring middleware
package middleware

import (
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	"go.uber.org/zap"
)

// PerformanceMetrics holds Prometheus metrics for performance monitoring
type PerformanceMetrics struct {
	RequestDuration *prometheus.HistogramVec
	RequestCount    *prometheus.CounterVec
	ActiveRequests  prometheus.Gauge
	ResponseSize    *prometheus.HistogramVec
	CacheHitRatio   *prometheus.GaugeVec
	DatabaseQueries *prometheus.HistogramVec
	ErrorRate       *prometheus.CounterVec
}

// NewPerformanceMetrics creates new performance metrics
func NewPerformanceMetrics() *PerformanceMetrics {
	return &PerformanceMetrics{
		RequestDuration: promauto.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "marketplace_http_request_duration_seconds",
				Help:    "Duration of HTTP requests in seconds",
				Buckets: []float64{0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1, 2.5, 5, 10},
			},
			[]string{"method", "endpoint", "status_code"},
		),
		RequestCount: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "marketplace_http_requests_total",
				Help: "Total number of HTTP requests",
			},
			[]string{"method", "endpoint", "status_code"},
		),
		ActiveRequests: promauto.NewGauge(
			prometheus.GaugeOpts{
				Name: "marketplace_http_requests_active",
				Help: "Number of active HTTP requests",
			},
		),
		ResponseSize: promauto.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "marketplace_http_response_size_bytes",
				Help:    "Size of HTTP responses in bytes",
				Buckets: []float64{100, 1000, 10000, 100000, 1000000, 10000000},
			},
			[]string{"method", "endpoint"},
		),
		CacheHitRatio: promauto.NewGaugeVec(
			prometheus.GaugeOpts{
				Name: "marketplace_cache_hit_ratio",
				Help: "Cache hit ratio by cache type",
			},
			[]string{"cache_type"},
		),
		DatabaseQueries: promauto.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "marketplace_database_query_duration_seconds",
				Help:    "Duration of database queries in seconds",
				Buckets: []float64{0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1, 2},
			},
			[]string{"operation", "table"},
		),
		ErrorRate: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "marketplace_errors_total",
				Help: "Total number of errors by type",
			},
			[]string{"error_type", "endpoint"},
		),
	}
}

// PerformanceMiddleware provides performance monitoring
type PerformanceMiddleware struct {
	metrics *PerformanceMetrics
	logger  *zap.Logger
}

// NewPerformanceMiddleware creates a new performance middleware
func NewPerformanceMiddleware(logger *zap.Logger) *PerformanceMiddleware {
	return &PerformanceMiddleware{
		metrics: NewPerformanceMetrics(),
		logger:  logger,
	}
}

// Monitor provides comprehensive performance monitoring
func (p *PerformanceMiddleware) Monitor() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		
		// Increment active requests
		p.metrics.ActiveRequests.Inc()
		defer p.metrics.ActiveRequests.Dec()

		// Process request
		c.Next()

		// Calculate metrics
		duration := time.Since(start)
		statusCode := strconv.Itoa(c.Writer.Status())
		endpoint := normalizeEndpoint(c.FullPath())
		method := c.Request.Method
		responseSize := float64(c.Writer.Size())

		// Record metrics
		p.metrics.RequestDuration.WithLabelValues(method, endpoint, statusCode).Observe(duration.Seconds())
		p.metrics.RequestCount.WithLabelValues(method, endpoint, statusCode).Inc()
		p.metrics.ResponseSize.WithLabelValues(method, endpoint).Observe(responseSize)

		// Log slow requests
		if duration > 200*time.Millisecond {
			p.logger.Warn("Slow request detected",
				zap.String("method", method),
				zap.String("endpoint", endpoint),
				zap.Duration("duration", duration),
				zap.Int("status", c.Writer.Status()),
				zap.String("user_agent", c.Request.UserAgent()),
				zap.String("remote_addr", c.ClientIP()),
			)
		}

		// Log errors
		if c.Writer.Status() >= 400 {
			errorType := getErrorType(c.Writer.Status())
			p.metrics.ErrorRate.WithLabelValues(errorType, endpoint).Inc()
			
			p.logger.Error("HTTP error",
				zap.String("method", method),
				zap.String("endpoint", endpoint),
				zap.Int("status", c.Writer.Status()),
				zap.Duration("duration", duration),
				zap.String("error_type", errorType),
			)
		}
	}
}

// ResponseCompression provides gzip compression for responses
func (p *PerformanceMiddleware) ResponseCompression() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Check if client accepts gzip
		if !shouldCompress(c) {
			c.Next()
			return
		}

		// Set compression headers
		c.Header("Content-Encoding", "gzip")
		c.Header("Vary", "Accept-Encoding")

		// TODO: Implement actual gzip compression
		// For now, just set headers
		c.Next()
	}
}

// DatabaseMetrics records database operation metrics
func (p *PerformanceMiddleware) RecordDatabaseQuery(operation, table string, duration time.Duration) {
	p.metrics.DatabaseQueries.WithLabelValues(operation, table).Observe(duration.Seconds())
	
	// Log slow database queries
	if duration > 100*time.Millisecond {
		p.logger.Warn("Slow database query",
			zap.String("operation", operation),
			zap.String("table", table),
			zap.Duration("duration", duration),
		)
	}
}

// RecordCacheMetrics records cache performance metrics
func (p *PerformanceMiddleware) RecordCacheMetrics(cacheType string, hitRatio float64) {
	p.metrics.CacheHitRatio.WithLabelValues(cacheType).Set(hitRatio)
}

// GetMetrics returns the metrics instance for external use
func (p *PerformanceMiddleware) GetMetrics() *PerformanceMetrics {
	return p.metrics
}

// Helper functions

func normalizeEndpoint(path string) string {
	if path == "" {
		return "unknown"
	}
	
	// Normalize dynamic paths
	// Replace IDs with placeholders for better grouping
	// This is a simple implementation - could be more sophisticated
	return path
}

func getErrorType(statusCode int) string {
	switch {
	case statusCode >= 400 && statusCode < 500:
		return "client_error"
	case statusCode >= 500:
		return "server_error"
	default:
		return "unknown"
	}
}

func shouldCompress(c *gin.Context) bool {
	// Check Accept-Encoding header
	acceptEncoding := c.GetHeader("Accept-Encoding")
	if acceptEncoding == "" {
		return false
	}

	// Simple check for gzip support
	return len(acceptEncoding) > 0 && 
		   (c.GetHeader("Content-Type") == "application/json" || 
		    c.GetHeader("Content-Type") == "text/html")
}

// HealthMetrics provides health check metrics
func (p *PerformanceMiddleware) HealthMetrics() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		
		// Basic health check
		c.JSON(200, gin.H{
			"status":    "healthy",
			"timestamp": start.Unix(),
			"uptime":    time.Since(start).String(),
		})
		
		// Record health check metrics
		duration := time.Since(start)
		p.metrics.RequestDuration.WithLabelValues("GET", "/health", "200").Observe(duration.Seconds())
		p.metrics.RequestCount.WithLabelValues("GET", "/health", "200").Inc()
	}
}

// PerformanceReport generates a performance report
type PerformanceReport struct {
	AverageResponseTime float64            `json:"average_response_time"`
	RequestsPerSecond   float64            `json:"requests_per_second"`
	ErrorRate           float64            `json:"error_rate"`
	CacheHitRatio       map[string]float64 `json:"cache_hit_ratio"`
	SlowQueries         int                `json:"slow_queries"`
	ActiveConnections   int                `json:"active_connections"`
	MemoryUsage         float64            `json:"memory_usage_mb"`
}

// GenerateReport creates a performance report
func (p *PerformanceMiddleware) GenerateReport() *PerformanceReport {
	// This would typically gather metrics from Prometheus
	// For now, return a placeholder
	return &PerformanceReport{
		AverageResponseTime: 0.05, // 50ms
		RequestsPerSecond:   100,
		ErrorRate:           0.01, // 1%
		CacheHitRatio: map[string]float64{
			"memory": 0.95,
			"redis":  0.85,
		},
		SlowQueries:       5,
		ActiveConnections: 50,
		MemoryUsage:       256.0,
	}
}
