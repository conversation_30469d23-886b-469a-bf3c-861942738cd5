// Package middleware provides HTTP middleware for the marketplace service
package middleware

import (
	"fmt"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"go.uber.org/zap"

	"github.com/episteme/marketplace/internal/config"
	"github.com/episteme/marketplace/internal/models"
)

// JWTClaims represents JWT token claims
type JWTClaims struct {
	UserID   string          `json:"user_id"`
	Email    string          `json:"email"`
	Role     models.UserRole `json:"role"`
	IsActive bool            `json:"is_active"`
	jwt.RegisteredClaims
}

// AuthMiddleware provides JWT authentication
type AuthMiddleware struct {
	jwtSecret []byte
	logger    *zap.Logger
}

// NewAuthMiddleware creates a new auth middleware
func NewAuthMiddleware(cfg *config.Config, logger *zap.Logger) *AuthMiddleware {
	return &AuthMiddleware{
		jwtSecret: []byte(cfg.Auth.JWTSecret),
		logger:    logger,
	}
}

// Auth provides authentication middleware
func (a *AuthMiddleware) Auth() gin.HandlerFunc {
	return func(c *gin.Context) {
		token, err := a.extractToken(c)
		if err != nil {
			a.logger.Warn("Authentication failed", zap.Error(err))
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Authentication required",
				"code":  "AUTH_REQUIRED",
			})
			c.Abort()
			return
		}

		claims, err := a.validateToken(token)
		if err != nil {
			a.logger.Warn("Token validation failed", zap.Error(err))
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Invalid token",
				"code":  "INVALID_TOKEN",
			})
			c.Abort()
			return
		}

		// Check if user is active
		if !claims.IsActive {
			a.logger.Warn("Inactive user attempted access", zap.String("user_id", claims.UserID))
			c.JSON(http.StatusForbidden, gin.H{
				"error": "Account is inactive",
				"code":  "ACCOUNT_INACTIVE",
			})
			c.Abort()
			return
		}

		// Set user context
		c.Set("user_id", claims.UserID)
		c.Set("user_email", claims.Email)
		c.Set("user_role", claims.Role)
		c.Set("user_claims", claims)

		c.Next()
	}
}

// extractToken extracts JWT token from request
func (a *AuthMiddleware) extractToken(c *gin.Context) (string, error) {
	// Check Authorization header
	authHeader := c.GetHeader("Authorization")
	if authHeader != "" {
		if token, found := strings.CutPrefix(authHeader, "Bearer "); found {
			return token, nil
		}
		return "", fmt.Errorf("invalid authorization header format")
	}

	// Check query parameter (for WebSocket connections)
	token := c.Query("token")
	if token != "" {
		return token, nil
	}

	return "", fmt.Errorf("no token provided")
}

// validateToken validates JWT token and returns claims
func (a *AuthMiddleware) validateToken(tokenString string) (*JWTClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &JWTClaims{}, func(token *jwt.Token) (any, error) {
		// Validate signing method
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return a.jwtSecret, nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to parse token: %w", err)
	}

	if !token.Valid {
		return nil, fmt.Errorf("invalid token")
	}

	claims, ok := token.Claims.(*JWTClaims)
	if !ok {
		return nil, fmt.Errorf("invalid token claims")
	}

	return claims, nil
}

// SellerOnly ensures only sellers can access the endpoint
func SellerOnly() gin.HandlerFunc {
	return func(c *gin.Context) {
		role, exists := c.Get("user_role")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Authentication required",
				"code":  "AUTH_REQUIRED",
			})
			c.Abort()
			return
		}

		userRole := role.(models.UserRole)
		if userRole != models.UserRoleAdmin && userRole != models.UserRoleModerator {
			// Check if user has seller status
			// TODO: Check marketplace profile for seller status
			c.JSON(http.StatusForbidden, gin.H{
				"error": "Seller access required",
				"code":  "SELLER_ACCESS_REQUIRED",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// AdminOnly ensures only admins can access the endpoint
func AdminOnly() gin.HandlerFunc {
	return func(c *gin.Context) {
		role, exists := c.Get("user_role")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Authentication required",
				"code":  "AUTH_REQUIRED",
			})
			c.Abort()
			return
		}

		userRole := role.(models.UserRole)
		if userRole != models.UserRoleAdmin {
			c.JSON(http.StatusForbidden, gin.H{
				"error": "Admin access required",
				"code":  "ADMIN_ACCESS_REQUIRED",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// PatternOwner ensures only pattern owners can access the endpoint
func PatternOwner() gin.HandlerFunc {
	return func(c *gin.Context) {
		// TODO: Implement pattern ownership verification
		c.Next()
	}
}

// PatternAccess ensures user has access to the pattern (purchased or owns)
func PatternAccess() gin.HandlerFunc {
	return func(c *gin.Context) {
		// TODO: Implement pattern access verification
		c.Next()
	}
}

// PurchaseRequired ensures user has purchased the pattern
func PurchaseRequired() gin.HandlerFunc {
	return func(c *gin.Context) {
		// TODO: Implement purchase verification
		c.Next()
	}
}

// ReviewOwner ensures only review owners can access the endpoint
func ReviewOwner() gin.HandlerFunc {
	return func(c *gin.Context) {
		// TODO: Implement review ownership verification
		c.Next()
	}
}

// CollectionOwner ensures only collection owners can access the endpoint
func CollectionOwner() gin.HandlerFunc {
	return func(c *gin.Context) {
		// TODO: Implement collection ownership verification
		c.Next()
	}
}

// StripeWebhook provides Stripe webhook signature verification
func StripeWebhook() gin.HandlerFunc {
	return func(c *gin.Context) {
		// TODO: Implement Stripe webhook signature verification
		c.Next()
	}
}
