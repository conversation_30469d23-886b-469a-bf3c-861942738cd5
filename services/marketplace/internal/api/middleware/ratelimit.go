// Package middleware provides rate limiting middleware
package middleware

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"github.com/episteme/marketplace/internal/clients"
)

// RateLimitConfig defines rate limiting configuration
type RateLimitConfig struct {
	Requests int                       // Number of requests allowed
	Window   time.Duration             // Time window
	KeyFunc  func(*gin.Context) string // Function to generate rate limit key
}

// RateLimiter provides Redis-based rate limiting
type RateLimiter struct {
	redisClient *clients.RedisClient
	logger      *zap.Logger
}

// NewRateLimiter creates a new rate limiter
func NewRateLimiter(redisClient *clients.RedisClient, logger *zap.Logger) *RateLimiter {
	return &RateLimiter{
		redisClient: redisClient,
		logger:      logger,
	}
}

// RateLimit creates a rate limiting middleware
func (rl *RateLimiter) RateLimit(config RateLimitConfig) gin.HandlerFunc {
	return func(c *gin.Context) {
		key := config.KeyFunc(c)
		allowed, remaining, resetTime, err := rl.checkRateLimit(c.Request.Context(), key, config)

		if err != nil {
			rl.logger.Error("Rate limit check failed", zap.Error(err), zap.String("key", key))
			// On error, allow the request but log it
			c.Next()
			return
		}

		// Set rate limit headers
		c.Header("X-RateLimit-Limit", strconv.Itoa(config.Requests))
		c.Header("X-RateLimit-Remaining", strconv.Itoa(remaining))
		c.Header("X-RateLimit-Reset", strconv.FormatInt(resetTime.Unix(), 10))

		if !allowed {
			rl.logger.Warn("Rate limit exceeded",
				zap.String("key", key),
				zap.String("ip", c.ClientIP()),
				zap.String("path", c.Request.URL.Path),
			)

			c.JSON(http.StatusTooManyRequests, gin.H{
				"error":       "Rate limit exceeded",
				"code":        "RATE_LIMIT_EXCEEDED",
				"retry_after": int(time.Until(resetTime).Seconds()),
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// checkRateLimit checks if request is within rate limit using sliding window
func (rl *RateLimiter) checkRateLimit(ctx context.Context, key string, config RateLimitConfig) (allowed bool, remaining int, resetTime time.Time, err error) {
	now := time.Now()

	// Redis key for this rate limit
	redisKey := fmt.Sprintf("ratelimit:%s", key)

	// Use simple counter-based rate limiting for now
	// TODO: Implement sliding window with sorted sets for more accurate rate limiting
	allowed, err = rl.redisClient.CheckRateLimit(ctx, redisKey, int64(config.Requests), config.Window)
	if err != nil {
		return false, 0, time.Time{}, fmt.Errorf("failed to check rate limit: %w", err)
	}

	// Calculate remaining (approximate)
	remaining = config.Requests - 1 // Simplified calculation
	if remaining < 0 {
		remaining = 0
	}

	resetTime = now.Add(config.Window)

	return allowed, remaining, resetTime, nil
}

// Common rate limit key functions

// IPBasedKey generates rate limit key based on IP address
func IPBasedKey(c *gin.Context) string {
	return fmt.Sprintf("ip:%s", c.ClientIP())
}

// UserBasedKey generates rate limit key based on user ID
func UserBasedKey(c *gin.Context) string {
	userID, exists := c.Get("user_id")
	if !exists {
		return IPBasedKey(c) // Fallback to IP-based
	}
	return fmt.Sprintf("user:%s", userID)
}

// EndpointBasedKey generates rate limit key based on endpoint and user
func EndpointBasedKey(c *gin.Context) string {
	userID, exists := c.Get("user_id")
	if !exists {
		return fmt.Sprintf("endpoint:%s:ip:%s", c.Request.URL.Path, c.ClientIP())
	}
	return fmt.Sprintf("endpoint:%s:user:%s", c.Request.URL.Path, userID)
}

// APIKeyBasedKey generates rate limit key based on API key
func APIKeyBasedKey(c *gin.Context) string {
	apiKey := c.GetHeader("X-API-Key")
	if apiKey == "" {
		return UserBasedKey(c) // Fallback to user-based
	}
	return fmt.Sprintf("apikey:%s", apiKey)
}

// Predefined rate limit configurations

// GlobalRateLimit applies to all requests from an IP
func GlobalRateLimit(rl *RateLimiter) gin.HandlerFunc {
	return rl.RateLimit(RateLimitConfig{
		Requests: 1000,
		Window:   time.Hour,
		KeyFunc:  IPBasedKey,
	})
}

// AuthRateLimit applies to authentication endpoints
func AuthRateLimit(rl *RateLimiter) gin.HandlerFunc {
	return rl.RateLimit(RateLimitConfig{
		Requests: 5,
		Window:   time.Minute * 15,
		KeyFunc:  IPBasedKey,
	})
}

// APIRateLimit applies to API endpoints per user
func APIRateLimit(rl *RateLimiter) gin.HandlerFunc {
	return rl.RateLimit(RateLimitConfig{
		Requests: 100,
		Window:   time.Minute,
		KeyFunc:  UserBasedKey,
	})
}

// PaymentRateLimit applies to payment endpoints
func PaymentRateLimit(rl *RateLimiter) gin.HandlerFunc {
	return rl.RateLimit(RateLimitConfig{
		Requests: 10,
		Window:   time.Minute,
		KeyFunc:  UserBasedKey,
	})
}

// SearchRateLimit applies to search endpoints
func SearchRateLimit(rl *RateLimiter) gin.HandlerFunc {
	return rl.RateLimit(RateLimitConfig{
		Requests: 50,
		Window:   time.Minute,
		KeyFunc: func(c *gin.Context) string {
			// More lenient for authenticated users
			if userID, exists := c.Get("user_id"); exists {
				return fmt.Sprintf("search:user:%s", userID)
			}
			return fmt.Sprintf("search:ip:%s", c.ClientIP())
		},
	})
}

// AdminRateLimit applies to admin endpoints
func AdminRateLimit(rl *RateLimiter) gin.HandlerFunc {
	return rl.RateLimit(RateLimitConfig{
		Requests: 200,
		Window:   time.Minute,
		KeyFunc:  UserBasedKey,
	})
}
