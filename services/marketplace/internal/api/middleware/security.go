// Package middleware provides security middleware for the marketplace service
package middleware

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"github.com/episteme/marketplace/internal/config"
)

// SecurityMiddleware provides security headers and protection
type SecurityMiddleware struct {
	config *config.Config
	logger *zap.Logger
}

// NewSecurityMiddleware creates a new security middleware
func NewSecurityMiddleware(cfg *config.Config, logger *zap.Logger) *SecurityMiddleware {
	return &SecurityMiddleware{
		config: cfg,
		logger: logger,
	}
}

// SecurityHeaders adds security headers to responses
func (s *SecurityMiddleware) SecurityHeaders() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Content Security Policy
		csp := "default-src 'self'; " +
			"script-src 'self' 'unsafe-inline' 'unsafe-eval' https://js.stripe.com; " +
			"style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; " +
			"font-src 'self' https://fonts.gstatic.com; " +
			"img-src 'self' data: https:; " +
			"connect-src 'self' https://api.stripe.com; " +
			"frame-src https://js.stripe.com https://hooks.stripe.com; " +
			"object-src 'none'; " +
			"base-uri 'self'; " +
			"form-action 'self'; " +
			"upgrade-insecure-requests"

		// Set security headers
		c.Header("Content-Security-Policy", csp)
		c.Header("X-Content-Type-Options", "nosniff")
		c.Header("X-Frame-Options", "DENY")
		c.Header("X-XSS-Protection", "1; mode=block")
		c.Header("Referrer-Policy", "strict-origin-when-cross-origin")
		c.Header("Permissions-Policy", "geolocation=(), microphone=(), camera=(), payment=(self)")
		
		// HSTS header (only for HTTPS)
		if c.Request.TLS != nil || c.GetHeader("X-Forwarded-Proto") == "https" {
			c.Header("Strict-Transport-Security", "max-age=31536000; includeSubDomains; preload")
		}

		// Remove server information
		c.Header("Server", "Marketplace-API")

		// Cache control for sensitive endpoints
		if isSecureEndpoint(c.Request.URL.Path) {
			c.Header("Cache-Control", "no-store, no-cache, must-revalidate, private")
			c.Header("Pragma", "no-cache")
			c.Header("Expires", "0")
		}

		c.Next()
	}
}

// CORS provides Cross-Origin Resource Sharing configuration
func (s *SecurityMiddleware) CORS() gin.HandlerFunc {
	return func(c *gin.Context) {
		origin := c.GetHeader("Origin")
		
		// Allow specific origins in production
		allowedOrigins := []string{
			"https://marketplace.episteme.com",
			"https://app.episteme.com",
		}
		
		// In development, allow localhost
		if s.config.App.Environment == "development" {
			allowedOrigins = append(allowedOrigins, 
				"http://localhost:3000",
				"http://localhost:3001",
				"http://127.0.0.1:3000",
			)
		}

		// Check if origin is allowed
		allowed := false
		for _, allowedOrigin := range allowedOrigins {
			if origin == allowedOrigin {
				allowed = true
				break
			}
		}

		if allowed {
			c.Header("Access-Control-Allow-Origin", origin)
		}

		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, PATCH, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Accept, Authorization, X-Requested-With, X-API-Key")
		c.Header("Access-Control-Allow-Credentials", "true")
		c.Header("Access-Control-Max-Age", "86400") // 24 hours

		// Handle preflight requests
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}

// RateLimit provides rate limiting middleware
func (s *SecurityMiddleware) RateLimit() gin.HandlerFunc {
	return func(c *gin.Context) {
		// TODO: Implement Redis-based rate limiting
		// For now, just pass through
		c.Next()
	}
}

// RequestID adds a unique request ID to each request
func (s *SecurityMiddleware) RequestID() gin.HandlerFunc {
	return func(c *gin.Context) {
		requestID := generateRequestID()
		c.Header("X-Request-ID", requestID)
		c.Set("request_id", requestID)
		c.Next()
	}
}

// InputValidation provides input validation and sanitization
func (s *SecurityMiddleware) InputValidation() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Validate content type for POST/PUT requests
		if c.Request.Method == "POST" || c.Request.Method == "PUT" || c.Request.Method == "PATCH" {
			contentType := c.GetHeader("Content-Type")
			if contentType != "" && !isAllowedContentType(contentType) {
				s.logger.Warn("Invalid content type", 
					zap.String("content_type", contentType),
					zap.String("path", c.Request.URL.Path),
				)
				c.JSON(http.StatusUnsupportedMediaType, gin.H{
					"error": "Unsupported content type",
					"code":  "INVALID_CONTENT_TYPE",
				})
				c.Abort()
				return
			}
		}

		// Validate request size
		if c.Request.ContentLength > 10*1024*1024 { // 10MB limit
			s.logger.Warn("Request too large", 
				zap.Int64("content_length", c.Request.ContentLength),
				zap.String("path", c.Request.URL.Path),
			)
			c.JSON(http.StatusRequestEntityTooLarge, gin.H{
				"error": "Request too large",
				"code":  "REQUEST_TOO_LARGE",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// Helper functions

func isSecureEndpoint(path string) bool {
	secureEndpoints := []string{
		"/api/v1/auth/",
		"/api/v1/users/me",
		"/api/v1/payments/",
		"/api/v1/admin/",
	}

	for _, endpoint := range secureEndpoints {
		if len(path) >= len(endpoint) && path[:len(endpoint)] == endpoint {
			return true
		}
	}
	return false
}

func isAllowedContentType(contentType string) bool {
	allowedTypes := []string{
		"application/json",
		"application/x-www-form-urlencoded",
		"multipart/form-data",
		"text/plain",
	}

	for _, allowedType := range allowedTypes {
		if len(contentType) >= len(allowedType) && contentType[:len(allowedType)] == allowedType {
			return true
		}
	}
	return false
}

func generateRequestID() string {
	// Simple request ID generation
	return time.Now().Format("20060102150405") + "-" + randomString(8)
}

func randomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[time.Now().UnixNano()%int64(len(charset))]
	}
	return string(b)
}
