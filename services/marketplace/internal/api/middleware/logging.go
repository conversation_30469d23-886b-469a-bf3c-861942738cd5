// Package middleware provides logging middleware
package middleware

import (
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// Logger provides structured logging middleware
func Logger(logger *zap.Logger) gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		logger.Info("HTTP Request",
			zap.String("method", param.Method),
			zap.String("path", param.Path),
			zap.Int("status", param.StatusCode),
			zap.Duration("latency", param.Latency),
			zap.String("client_ip", param.ClientIP),
			zap.String("user_agent", param.Request.UserAgent()),
		)
		return ""
	})
}

// RequestID adds a request ID to each request
func RequestID() gin.HandlerFunc {
	return func(c *gin.Context) {
		// TODO: Generate and add request ID
		c.Next()
	}
}

// Metrics provides metrics collection middleware
func Metrics() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		
		c.Next()
		
		// TODO: Record metrics using the metrics package
		duration := time.Since(start)
		_ = duration // Use duration for metrics recording
	}
}
