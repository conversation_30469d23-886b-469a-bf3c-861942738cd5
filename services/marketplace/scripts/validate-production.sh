#!/bin/bash

# Production validation script for marketplace service
# Following Context Engineering principles with comprehensive validation

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
SERVICE_URL="${SERVICE_URL:-http://localhost:8004}"
VALIDATION_ERRORS=0

# Increment error counter
increment_errors() {
    ((VALIDATION_ERRORS++))
}

# Validate build
validate_build() {
    log_info "Validating build..."
    
    if go build -o /tmp/marketplace-test ./cmd/server; then
        log_success "Build validation passed"
        rm -f /tmp/marketplace-test
    else
        log_error "Build validation failed"
        increment_errors
    fi
}

# Validate tests
validate_tests() {
    log_info "Validating tests..."
    
    if go test -v ./...; then
        log_success "Test validation passed"
    else
        log_error "Test validation failed"
        increment_errors
    fi
}

# Validate test coverage
validate_coverage() {
    log_info "Validating test coverage..."
    
    go test -coverprofile=coverage.out ./...
    COVERAGE=$(go tool cover -func=coverage.out | grep total | awk '{print $3}' | sed 's/%//')
    
    if (( $(echo "$COVERAGE >= 80" | bc -l) )); then
        log_success "Test coverage validation passed: ${COVERAGE}%"
    else
        log_warning "Test coverage below 80%: ${COVERAGE}%"
    fi
    
    rm -f coverage.out
}

# Validate linting
validate_linting() {
    log_info "Validating code quality..."
    
    if command -v golangci-lint &> /dev/null; then
        if golangci-lint run; then
            log_success "Linting validation passed"
        else
            log_error "Linting validation failed"
            increment_errors
        fi
    else
        log_warning "golangci-lint not installed, skipping linting validation"
    fi
}

# Validate Docker build
validate_docker() {
    log_info "Validating Docker build..."
    
    if docker build -t marketplace-validation-test .; then
        log_success "Docker build validation passed"
        docker rmi marketplace-validation-test
    else
        log_error "Docker build validation failed"
        increment_errors
    fi
}

# Validate service endpoints
validate_endpoints() {
    log_info "Validating service endpoints..."
    
    # Health check
    if curl -f -s "$SERVICE_URL/health" > /dev/null; then
        log_success "Health endpoint validation passed"
    else
        log_error "Health endpoint validation failed"
        increment_errors
    fi
    
    # Patterns API
    if curl -f -s "$SERVICE_URL/api/v1/patterns?page=1&page_size=10" > /dev/null; then
        log_success "Patterns API validation passed"
    else
        log_warning "Patterns API validation failed (may require authentication)"
    fi
    
    # Search API
    if curl -f -s "$SERVICE_URL/api/v1/search/patterns?q=test&page=1&page_size=5" > /dev/null; then
        log_success "Search API validation passed"
    else
        log_warning "Search API validation failed (may require authentication)"
    fi
    
    # Metrics endpoint
    if curl -f -s "$SERVICE_URL/metrics" > /dev/null; then
        log_success "Metrics endpoint validation passed"
    else
        log_warning "Metrics endpoint validation failed"
    fi
}

# Validate configuration files
validate_config() {
    log_info "Validating configuration files..."
    
    # Check Dockerfile
    if [[ -f "Dockerfile" ]]; then
        log_success "Dockerfile exists"
    else
        log_error "Dockerfile missing"
        increment_errors
    fi
    
    # Check Cloud Run config
    if [[ -f "deploy/cloudrun.yaml" ]]; then
        log_success "Cloud Run configuration exists"
    else
        log_error "Cloud Run configuration missing"
        increment_errors
    fi
    
    # Check CI/CD pipeline
    if [[ -f ".github/workflows/deploy.yml" ]]; then
        log_success "CI/CD pipeline configuration exists"
    else
        log_error "CI/CD pipeline configuration missing"
        increment_errors
    fi
    
    # Check deployment script
    if [[ -f "scripts/deploy.sh" && -x "scripts/deploy.sh" ]]; then
        log_success "Deployment script exists and is executable"
    else
        log_error "Deployment script missing or not executable"
        increment_errors
    fi
    
    # Check load test script
    if [[ -f "scripts/load-test.js" ]]; then
        log_success "Load test script exists"
    else
        log_error "Load test script missing"
        increment_errors
    fi
}

# Validate dependencies
validate_dependencies() {
    log_info "Validating dependencies..."
    
    # Check go.mod
    if go mod verify; then
        log_success "Go modules validation passed"
    else
        log_error "Go modules validation failed"
        increment_errors
    fi
    
    # Check for security vulnerabilities
    if command -v govulncheck &> /dev/null; then
        if govulncheck ./...; then
            log_success "Security vulnerability check passed"
        else
            log_warning "Security vulnerabilities found"
        fi
    else
        log_warning "govulncheck not installed, skipping vulnerability check"
    fi
}

# Validate performance requirements
validate_performance() {
    log_info "Validating performance requirements..."
    
    # This is a basic validation - full load testing should be done separately
    START_TIME=$(date +%s%N)
    
    if curl -f -s "$SERVICE_URL/health" > /dev/null; then
        END_TIME=$(date +%s%N)
        RESPONSE_TIME=$(( (END_TIME - START_TIME) / 1000000 )) # Convert to milliseconds
        
        if (( RESPONSE_TIME < 100 )); then
            log_success "Health endpoint response time: ${RESPONSE_TIME}ms"
        else
            log_warning "Health endpoint response time: ${RESPONSE_TIME}ms (>100ms)"
        fi
    else
        log_error "Performance validation failed - service not accessible"
        increment_errors
    fi
}

# Main validation function
main() {
    log_info "Starting production validation for marketplace service"
    log_info "Service URL: $SERVICE_URL"
    
    # Change to script directory
    cd "$(dirname "$0")/.."
    
    # Run all validations
    validate_build
    validate_tests
    validate_coverage
    validate_linting
    validate_docker
    validate_config
    validate_dependencies
    
    # Only validate endpoints if service is running
    if curl -f -s "$SERVICE_URL/health" > /dev/null 2>&1; then
        validate_endpoints
        validate_performance
    else
        log_warning "Service not running, skipping endpoint validation"
    fi
    
    # Summary
    echo
    log_info "Validation Summary:"
    if (( VALIDATION_ERRORS == 0 )); then
        log_success "All validations passed! Service is production-ready."
        exit 0
    else
        log_error "Validation failed with $VALIDATION_ERRORS errors"
        exit 1
    fi
}

# Run main function
main "$@"
