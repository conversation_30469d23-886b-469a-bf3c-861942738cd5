// Load testing script for marketplace service
// Following Context Engineering principles with comprehensive performance validation
// Target: 500+ TPS with <200ms p95 latency

import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate, Trend } from 'k6/metrics';

// Custom metrics
const errorRate = new Rate('errors');
const responseTime = new Trend('response_time');

// Test configuration
export const options = {
  stages: [
    { duration: '2m', target: 100 },   // Ramp up to 100 users
    { duration: '5m', target: 100 },   // Stay at 100 users
    { duration: '2m', target: 200 },   // Ramp up to 200 users
    { duration: '5m', target: 200 },   // Stay at 200 users
    { duration: '2m', target: 500 },   // Ramp up to 500 users
    { duration: '10m', target: 500 },  // Stay at 500 users (target load)
    { duration: '2m', target: 0 },     // Ramp down
  ],
  thresholds: {
    http_req_duration: ['p(95)<200'], // 95% of requests must be below 200ms
    http_req_failed: ['rate<0.01'],   // Error rate must be below 1%
    errors: ['rate<0.01'],            // Custom error rate below 1%
  },
};

// Base URL - will be set by environment variable in CI/CD
const BASE_URL = __ENV.SERVICE_URL || 'http://localhost:8004';

// Test data
const testPatterns = [
  {
    title: 'Load Test Pattern 1',
    description: 'A test pattern for load testing',
    category: 'backend',
    language: 'go',
    code: 'package main\n\nfunc main() {\n\tprintln("Hello, World!")\n}',
    price_cents: 1000,
    tags: ['test', 'load', 'performance'],
  },
  {
    title: 'Load Test Pattern 2',
    description: 'Another test pattern for load testing',
    category: 'frontend',
    language: 'javascript',
    code: 'console.log("Hello, World!");',
    price_cents: 1500,
    tags: ['test', 'frontend', 'js'],
  },
];

// Authentication token (mock for load testing)
const authToken = 'Bearer test-token-for-load-testing';

export default function () {
  const testScenario = Math.random();
  
  if (testScenario < 0.3) {
    // 30% - Health check
    testHealthCheck();
  } else if (testScenario < 0.6) {
    // 30% - List patterns
    testListPatterns();
  } else if (testScenario < 0.8) {
    // 20% - Search patterns
    testSearchPatterns();
  } else if (testScenario < 0.9) {
    // 10% - Get pattern details
    testGetPattern();
  } else {
    // 10% - Create pattern (authenticated)
    testCreatePattern();
  }
  
  // Random sleep between 1-3 seconds to simulate user behavior
  sleep(Math.random() * 2 + 1);
}

function testHealthCheck() {
  const response = http.get(`${BASE_URL}/health`);
  
  const success = check(response, {
    'health check status is 200': (r) => r.status === 200,
    'health check response time < 100ms': (r) => r.timings.duration < 100,
  });
  
  errorRate.add(!success);
  responseTime.add(response.timings.duration);
}

function testListPatterns() {
  const params = {
    page: Math.floor(Math.random() * 5) + 1,
    page_size: 20,
    category: ['backend', 'frontend', 'mobile'][Math.floor(Math.random() * 3)],
  };
  
  const url = `${BASE_URL}/api/v1/patterns?${new URLSearchParams(params).toString()}`;
  const response = http.get(url);
  
  const success = check(response, {
    'list patterns status is 200': (r) => r.status === 200,
    'list patterns response time < 200ms': (r) => r.timings.duration < 200,
    'list patterns has data': (r) => {
      try {
        const data = JSON.parse(r.body);
        return data.patterns && Array.isArray(data.patterns);
      } catch (e) {
        return false;
      }
    },
  });
  
  errorRate.add(!success);
  responseTime.add(response.timings.duration);
}

function testSearchPatterns() {
  const queries = ['authentication', 'database', 'api', 'frontend', 'backend'];
  const query = queries[Math.floor(Math.random() * queries.length)];
  
  const params = {
    q: query,
    page: 1,
    page_size: 10,
  };
  
  const url = `${BASE_URL}/api/v1/search/patterns?${new URLSearchParams(params).toString()}`;
  const response = http.get(url);
  
  const success = check(response, {
    'search patterns status is 200': (r) => r.status === 200,
    'search patterns response time < 300ms': (r) => r.timings.duration < 300,
    'search patterns has results': (r) => {
      try {
        const data = JSON.parse(r.body);
        return data.patterns && Array.isArray(data.patterns);
      } catch (e) {
        return false;
      }
    },
  });
  
  errorRate.add(!success);
  responseTime.add(response.timings.duration);
}

function testGetPattern() {
  // Use a mock pattern ID for testing
  const patternId = 'test-pattern-' + Math.floor(Math.random() * 100);
  const response = http.get(`${BASE_URL}/api/v1/patterns/${patternId}`);
  
  const success = check(response, {
    'get pattern response time < 150ms': (r) => r.timings.duration < 150,
    'get pattern status is 200 or 404': (r) => r.status === 200 || r.status === 404,
  });
  
  errorRate.add(!success);
  responseTime.add(response.timings.duration);
}

function testCreatePattern() {
  const pattern = testPatterns[Math.floor(Math.random() * testPatterns.length)];
  
  const payload = JSON.stringify({
    ...pattern,
    title: `${pattern.title} ${Date.now()}`, // Make title unique
  });
  
  const params = {
    headers: {
      'Content-Type': 'application/json',
      'Authorization': authToken,
    },
  };
  
  const response = http.post(`${BASE_URL}/api/v1/patterns`, payload, params);
  
  const success = check(response, {
    'create pattern response time < 500ms': (r) => r.timings.duration < 500,
    'create pattern status is 201 or 401': (r) => r.status === 201 || r.status === 401, // 401 expected for mock auth
  });
  
  errorRate.add(!success);
  responseTime.add(response.timings.duration);
}

// Setup function to run before the test
export function setup() {
  console.log(`Starting load test against: ${BASE_URL}`);
  
  // Verify service is accessible
  const response = http.get(`${BASE_URL}/health`);
  if (response.status !== 200) {
    throw new Error(`Service not accessible: ${response.status}`);
  }
  
  console.log('Service health check passed, starting load test...');
}

// Teardown function to run after the test
export function teardown(data) {
  console.log('Load test completed');
  
  // Log final metrics
  console.log(`Total requests: ${__VU * __ITER}`);
  console.log(`Error rate: ${errorRate.rate * 100}%`);
  console.log(`Average response time: ${responseTime.avg}ms`);
  console.log(`95th percentile response time: ${responseTime.p95}ms`);
}
