#!/bin/bash

# Production deployment script for marketplace service
# Following Context Engineering principles with comprehensive validation

set -euo pipefail

# Configuration
PROJECT_ID="vibe-match-463114"
SERVICE_NAME="marketplace-service"
REGION="us-central1"
REGISTRY="gcr.io"
ENVIRONMENT="${ENVIRONMENT:-production}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if gcloud is installed and authenticated
    if ! command -v gcloud &> /dev/null; then
        log_error "gcloud CLI is not installed"
        exit 1
    fi
    
    # Check if docker is installed
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed"
        exit 1
    fi
    
    # Check if authenticated with gcloud
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
        log_error "Not authenticated with gcloud. Run 'gcloud auth login'"
        exit 1
    fi
    
    # Set project
    gcloud config set project $PROJECT_ID
    
    log_success "Prerequisites check passed"
}

# Build and test
build_and_test() {
    log_info "Building and testing application..."
    
    # Run tests
    log_info "Running tests..."
    go test -v -race -coverprofile=coverage.out ./...
    
    # Check test coverage
    COVERAGE=$(go tool cover -func=coverage.out | grep total | awk '{print $3}' | sed 's/%//')
    log_info "Test coverage: ${COVERAGE}%"
    
    if (( $(echo "$COVERAGE < 80" | bc -l) )); then
        log_warning "Test coverage is below 80%"
    fi
    
    # Build application
    log_info "Building application..."
    CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build \
        -ldflags='-w -s -extldflags "-static"' \
        -a -installsuffix cgo \
        -o marketplace \
        ./cmd/server
    
    log_success "Build and test completed"
}

# Build Docker image
build_docker_image() {
    log_info "Building Docker image..."
    
    # Generate image tag
    TIMESTAMP=$(date +%Y%m%d-%H%M%S)
    IMAGE_TAG="${ENVIRONMENT}-${TIMESTAMP}"
    FULL_IMAGE_NAME="${REGISTRY}/${PROJECT_ID}/${SERVICE_NAME}:${IMAGE_TAG}"
    LATEST_IMAGE_NAME="${REGISTRY}/${PROJECT_ID}/${SERVICE_NAME}:latest"
    
    # Build image
    docker build \
        --build-arg ENVIRONMENT=$ENVIRONMENT \
        --build-arg VERSION=$IMAGE_TAG \
        -t $FULL_IMAGE_NAME \
        -t $LATEST_IMAGE_NAME \
        .
    
    log_success "Docker image built: $FULL_IMAGE_NAME"
    
    # Export for use in other functions
    export FULL_IMAGE_NAME
    export LATEST_IMAGE_NAME
    export IMAGE_TAG
}

# Push Docker image
push_docker_image() {
    log_info "Pushing Docker image to registry..."
    
    # Configure Docker for GCR
    gcloud auth configure-docker --quiet
    
    # Push images
    docker push $FULL_IMAGE_NAME
    docker push $LATEST_IMAGE_NAME
    
    log_success "Docker image pushed to registry"
}

# Deploy to Cloud Run
deploy_to_cloud_run() {
    log_info "Deploying to Cloud Run..."
    
    # Deploy service
    gcloud run deploy $SERVICE_NAME \
        --image=$FULL_IMAGE_NAME \
        --region=$REGION \
        --platform=managed \
        --allow-unauthenticated \
        --set-env-vars="ENVIRONMENT=$ENVIRONMENT" \
        --set-env-vars="VERSION=$IMAGE_TAG" \
        --cpu=2 \
        --memory=4Gi \
        --concurrency=1000 \
        --max-instances=100 \
        --min-instances=2 \
        --timeout=300 \
        --service-account=marketplace-service@${PROJECT_ID}.iam.gserviceaccount.com \
        --quiet
    
    # Get service URL
    SERVICE_URL=$(gcloud run services describe $SERVICE_NAME \
        --region=$REGION \
        --format='value(status.url)')
    
    log_success "Service deployed at: $SERVICE_URL"
    
    # Export for use in other functions
    export SERVICE_URL
}

# Run health checks
run_health_checks() {
    log_info "Running health checks..."
    
    # Wait for service to be ready
    log_info "Waiting for service to be ready..."
    sleep 30
    
    # Health check
    if curl -f -s "$SERVICE_URL/health" > /dev/null; then
        log_success "Health check passed"
    else
        log_error "Health check failed"
        exit 1
    fi
    
    # Additional endpoint checks
    log_info "Testing API endpoints..."
    
    # Test patterns endpoint
    if curl -f -s "$SERVICE_URL/api/v1/patterns?page=1&page_size=10" > /dev/null; then
        log_success "Patterns API endpoint working"
    else
        log_warning "Patterns API endpoint not accessible"
    fi
    
    # Test search endpoint
    if curl -f -s "$SERVICE_URL/api/v1/search/patterns?q=test&page=1&page_size=5" > /dev/null; then
        log_success "Search API endpoint working"
    else
        log_warning "Search API endpoint not accessible"
    fi
}

# Run load test (production only)
run_load_test() {
    if [[ "$ENVIRONMENT" != "production" ]]; then
        log_info "Skipping load test for non-production environment"
        return
    fi
    
    log_info "Running load test..."
    
    # Check if k6 is installed
    if ! command -v k6 &> /dev/null; then
        log_warning "k6 not installed, skipping load test"
        return
    fi
    
    # Run load test
    export SERVICE_URL
    k6 run --vus 50 --duration 2m scripts/load-test.js
    
    log_success "Load test completed"
}

# Cleanup
cleanup() {
    log_info "Cleaning up..."
    
    # Remove local binary
    rm -f marketplace
    
    # Remove coverage files
    rm -f coverage.out coverage.html
    
    log_success "Cleanup completed"
}

# Main deployment function
main() {
    log_info "Starting deployment process for $SERVICE_NAME ($ENVIRONMENT)"
    
    # Change to script directory
    cd "$(dirname "$0")/.."
    
    # Run deployment steps
    check_prerequisites
    build_and_test
    build_docker_image
    push_docker_image
    deploy_to_cloud_run
    run_health_checks
    run_load_test
    cleanup
    
    log_success "Deployment completed successfully!"
    log_info "Service URL: $SERVICE_URL"
    log_info "Image: $FULL_IMAGE_NAME"
    log_info "Version: $IMAGE_TAG"
}

# Handle script interruption
trap cleanup EXIT

# Run main function
main "$@"
