/**
 * Authentication Type Definitions
 * 
 * Types for authentication, authorization, and user context.
 */

import { Request } from 'express';

// User context attached to Express request
export interface AuthUser {
  id: string;
  email: string;
  name?: string;
  role?: string;
}

// Extended Express Request with user
export interface AuthRequest extends Request {
  user?: AuthUser;
}

// Session data
export interface SessionData {
  sessionId: string;
  userId: string;
  teamId?: string;
  createdAt: Date;
  expiresAt: Date;
}

// Authentication response
export interface AuthResponse {
  accessToken: string;
  refreshToken?: string;
  user: AuthUser;
  expiresIn: number;
}

// Registration data
export interface RegisterData {
  email: string;
  password: string;
  name: string;
  role?: string;
}

// Login data
export interface LoginData {
  email: string;
  password: string;
  remember?: boolean;
}

// Change password data
export interface ChangePasswordData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

// Reset password data
export interface ResetPasswordData {
  token: string;
  newPassword: string;
  confirmPassword: string;
}

// Token validation result
export interface TokenValidation {
  valid: boolean;
  user?: AuthUser;
  error?: string;
}

// Permission check result
export interface PermissionCheck {
  allowed: boolean;
  reason?: string;
}

// Team membership
export interface TeamMembership {
  teamId: string;
  role: string;
  joinedAt: Date;
}

// User profile
export interface UserProfile extends AuthUser {
  createdAt: Date;
  updatedAt: Date;
  lastLogin?: Date;
  teams: TeamMembership[];
  preferences?: Record<string, any>;
}