/**
 * Type Definitions Index
 * 
 * Central export point for all type definitions.
 */

// Re-export all types
export * from './auth';

// Express type augmentation
declare global {
  namespace Express {
    interface Request {
      user?: import('./auth').AuthUser;
      sessionId?: string;
      requestId?: string;
    }
  }
}

// Socket.IO type augmentation
declare module 'socket.io' {
  interface Socket {
    userId?: string;
    sessionId?: string;
    teamId?: string;
    authenticated?: boolean;
  }
}