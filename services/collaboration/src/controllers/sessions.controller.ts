/**
 * Sessions Controller
 * 
 * HTTP endpoints for collaboration session management.
 * Handles session creation, management, and participant operations.
 */

import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { asyncHandler } from '../middleware/error-handler.middleware';
import { spannerClient } from '../clients/spanner.client';
import { firestoreClient } from '../clients/firestore.client';
import { logger } from '../utils/logger';
import { v4 as uuidv4 } from 'uuid';

const router = Router();

// Validation schemas
const createSessionSchema = z.object({
  teamId: z.string().uuid(),
  name: z.string().min(1).max(100),
  type: z.enum(['analysis', 'pattern_development', 'code_review', 'discussion']).default('analysis'),
  description: z.string().max(500).optional(),
});

const updateSessionSchema = z.object({
  name: z.string().min(1).max(100).optional(),
  description: z.string().max(500).optional(),
});

/**
 * Create a new collaboration session
 */
router.post('/', asyncHandler(async (req: Request, res: Response) => {
  const { teamId, name, type, description } = createSessionSchema.parse(req.body);
  const userId = req.user!.id;

  try {
    // Check if user is a member of the team
    const team = await spannerClient.teams.get(teamId);
    if (!team) {
      return res.status(404).json({
        error: 'Team not found',
        message: 'The specified team does not exist',
      });
    }

    const members = await spannerClient.teams.getMembers(teamId);
    const isMember = members.some(member => member.user_id === userId);

    if (!isMember) {
      return res.status(403).json({
        error: 'Access denied',
        message: 'You are not a member of this team',
      });
    }

    const sessionId = uuidv4();

    // Create session in Spanner
    await spannerClient.sessions.create({
      id: sessionId,
      teamId,
      name,
      type,
      description,
      createdBy: userId,
    });

    // Create session in Firestore for real-time updates
    await firestoreClient.sessions.create({
      id: sessionId,
      teamId,
      name,
      type,
      description,
      createdBy: userId,
      participants: [userId],
    });

    logger.info('Collaboration session created', {
      sessionId,
      teamId,
      name,
      type,
      createdBy: userId,
    });

    res.status(201).json({
      success: true,
      data: {
        id: sessionId,
        teamId,
        name,
        type,
        description,
        createdBy: userId,
        status: 'active',
        participants: [userId],
        createdAt: new Date().toISOString(),
      },
    });
  } catch (error) {
    logger.error('Error creating session', { error, teamId, userId });
    res.status(500).json({
      error: 'Failed to create session',
      message: 'An error occurred while creating the session',
    });
  }
}));

/**
 * Get team sessions
 */
router.get('/team/:teamId', asyncHandler(async (req: Request, res: Response) => {
  const { teamId } = req.params;
  const userId = req.user!.id;

  try {
    // Check if user is a member of the team
    const members = await spannerClient.teams.getMembers(teamId);
    const isMember = members.some(member => member.user_id === userId);

    if (!isMember) {
      return res.status(403).json({
        error: 'Access denied',
        message: 'You are not a member of this team',
      });
    }

    const sessions = await spannerClient.sessions.listByTeam(teamId);

    res.json({
      success: true,
      data: sessions,
    });
  } catch (error) {
    logger.error('Error fetching team sessions', { error, teamId, userId });
    res.status(500).json({
      error: 'Failed to fetch sessions',
      message: 'An error occurred while fetching sessions',
    });
  }
}));

/**
 * Get session details
 */
router.get('/:sessionId', asyncHandler(async (req: Request, res: Response) => {
  const { sessionId } = req.params;
  const userId = req.user!.id;

  try {
    // Get session from Spanner
    const session = await spannerClient.sessions.get(sessionId);
    
    if (!session) {
      return res.status(404).json({
        error: 'Session not found',
        message: 'The requested session does not exist',
      });
    }

    // Check if user is a member of the team
    const members = await spannerClient.teams.getMembers(session.team_id);
    const isMember = members.some(member => member.user_id === userId);

    if (!isMember) {
      return res.status(403).json({
        error: 'Access denied',
        message: 'You are not a member of this team',
      });
    }

    // Get real-time session data from Firestore
    const firestoreSession = await firestoreClient.sessions.get(sessionId);

    res.json({
      success: true,
      data: {
        ...session,
        participants: firestoreSession?.participants || [],
        lastActivity: firestoreSession?.lastActivity || session.updated_at,
      },
    });
  } catch (error) {
    logger.error('Error fetching session details', { error, sessionId, userId });
    res.status(500).json({
      error: 'Failed to fetch session details',
      message: 'An error occurred while fetching session details',
    });
  }
}));

/**
 * Update session
 */
router.put('/:sessionId', asyncHandler(async (req: Request, res: Response) => {
  const { sessionId } = req.params;
  const updates = updateSessionSchema.parse(req.body);
  const userId = req.user!.id;

  try {
    const session = await spannerClient.sessions.get(sessionId);
    
    if (!session) {
      return res.status(404).json({
        error: 'Session not found',
        message: 'The requested session does not exist',
      });
    }

    // Check if user is session creator or team admin
    const members = await spannerClient.teams.getMembers(session.team_id);
    const userMembership = members.find(member => member.user_id === userId);

    if (session.created_by !== userId && (!userMembership || userMembership.role !== 'admin')) {
      return res.status(403).json({
        error: 'Access denied',
        message: 'You do not have permission to update this session',
      });
    }

    // Update in Spanner
    await spannerClient.sessions.update(sessionId, updates);

    // Update in Firestore
    await firestoreClient.sessions.update(sessionId, updates);

    logger.info('Session updated', { sessionId, updates, userId });

    res.json({
      success: true,
      data: {
        ...session,
        ...updates,
        updatedAt: new Date().toISOString(),
      },
    });
  } catch (error) {
    logger.error('Error updating session', { error, sessionId, userId });
    res.status(500).json({
      error: 'Failed to update session',
      message: 'An error occurred while updating the session',
    });
  }
}));

/**
 * End session
 */
router.post('/:sessionId/end', asyncHandler(async (req: Request, res: Response) => {
  const { sessionId } = req.params;
  const userId = req.user!.id;

  try {
    const session = await spannerClient.sessions.get(sessionId);
    
    if (!session) {
      return res.status(404).json({
        error: 'Session not found',
        message: 'The requested session does not exist',
      });
    }

    // Check if user is session creator or team admin
    const members = await spannerClient.teams.getMembers(session.team_id);
    const userMembership = members.find(member => member.user_id === userId);

    if (session.created_by !== userId && (!userMembership || userMembership.role !== 'admin')) {
      return res.status(403).json({
        error: 'Access denied',
        message: 'You do not have permission to end this session',
      });
    }

    // End session in Spanner
    await spannerClient.sessions.end(sessionId);

    // End session in Firestore
    await firestoreClient.sessions.end(sessionId);

    logger.info('Session ended', { sessionId, userId });

    res.json({
      success: true,
      message: 'Session ended successfully',
      data: {
        id: sessionId,
        status: 'ended',
        endedAt: new Date().toISOString(),
        endedBy: userId,
      },
    });
  } catch (error) {
    logger.error('Error ending session', { error, sessionId, userId });
    res.status(500).json({
      error: 'Failed to end session',
      message: 'An error occurred while ending the session',
    });
  }
}));

/**
 * Get session messages
 */
router.get('/:sessionId/messages', asyncHandler(async (req: Request, res: Response) => {
  const { sessionId } = req.params;
  const userId = req.user!.id;
  const limit = parseInt(req.query.limit as string) || 50;

  try {
    const session = await spannerClient.sessions.get(sessionId);
    
    if (!session) {
      return res.status(404).json({
        error: 'Session not found',
        message: 'The requested session does not exist',
      });
    }

    // Check if user is a member of the team
    const members = await spannerClient.teams.getMembers(session.team_id);
    const isMember = members.some(member => member.user_id === userId);

    if (!isMember) {
      return res.status(403).json({
        error: 'Access denied',
        message: 'You are not a member of this team',
      });
    }

    const messages = await firestoreClient.messages.list(sessionId, limit);

    res.json({
      success: true,
      data: messages,
    });
  } catch (error) {
    logger.error('Error fetching session messages', { error, sessionId, userId });
    res.status(500).json({
      error: 'Failed to fetch messages',
      message: 'An error occurred while fetching messages',
    });
  }
}));

export { router as sessionsController };