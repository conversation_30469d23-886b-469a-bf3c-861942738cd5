/**
 * Teams Controller
 * 
 * HTTP endpoints for team management operations.
 * Handles team creation, member management, and team settings.
 */

import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { asyncHandler } from '../middleware/error-handler.middleware';
import { spannerClient } from '../clients/spanner.client';
import { logger } from '../utils/logger';
import { v4 as uuidv4 } from 'uuid';

const router = Router();

// Validation schemas
const createTeamSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().max(500).optional(),
});

const updateTeamSchema = z.object({
  name: z.string().min(1).max(100).optional(),
  description: z.string().max(500).optional(),
});

const addMemberSchema = z.object({
  userId: z.string().uuid(),
  role: z.enum(['member', 'admin']).default('member'),
});

/**
 * Create a new team
 */
router.post('/', asyncHandler(async (req: Request, res: Response) => {
  const { name, description } = createTeamSchema.parse(req.body);
  const ownerId = req.user!.id;

  const teamId = uuidv4();
  
  try {
    await spannerClient.teams.create({
      id: teamId,
      name,
      description,
      ownerId,
    });

    // Add owner as admin member
    await spannerClient.teams.addMember(teamId, ownerId, 'admin');

    logger.info('Team created', {
      teamId,
      name,
      ownerId,
      userId: req.user!.id,
    });

    res.status(201).json({
      success: true,
      data: {
        id: teamId,
        name,
        description,
        ownerId,
        createdAt: new Date().toISOString(),
      },
    });
  } catch (error) {
    logger.error('Error creating team', { error, teamId, ownerId });
    res.status(500).json({
      error: 'Failed to create team',
      message: 'An error occurred while creating the team',
    });
  }
}));

/**
 * Get user's teams
 */
router.get('/', asyncHandler(async (req: Request, res: Response) => {
  const userId = req.user!.id;

  try {
    const teams = await spannerClient.teams.listByUser(userId);

    res.json({
      success: true,
      data: teams,
    });
  } catch (error) {
    logger.error('Error fetching user teams', { error, userId });
    res.status(500).json({
      error: 'Failed to fetch teams',
      message: 'An error occurred while fetching teams',
    });
  }
}));

/**
 * Get team details
 */
router.get('/:teamId', asyncHandler(async (req: Request, res: Response) => {
  const { teamId } = req.params;
  const userId = req.user!.id;

  try {
    const team = await spannerClient.teams.get(teamId);
    
    if (!team) {
      return res.status(404).json({
        error: 'Team not found',
        message: 'The requested team does not exist',
      });
    }

    // Check if user is a member
    const members = await spannerClient.teams.getMembers(teamId);
    const isMember = members.some(member => member.user_id === userId);

    if (!isMember) {
      return res.status(403).json({
        error: 'Access denied',
        message: 'You are not a member of this team',
      });
    }

    res.json({
      success: true,
      data: {
        ...team,
        members,
      },
    });
  } catch (error) {
    logger.error('Error fetching team details', { error, teamId, userId });
    res.status(500).json({
      error: 'Failed to fetch team details',
      message: 'An error occurred while fetching team details',
    });
  }
}));

/**
 * Update team
 */
router.put('/:teamId', asyncHandler(async (req: Request, res: Response) => {
  const { teamId } = req.params;
  const updates = updateTeamSchema.parse(req.body);
  const userId = req.user!.id;

  try {
    const team = await spannerClient.teams.get(teamId);
    
    if (!team) {
      return res.status(404).json({
        error: 'Team not found',
        message: 'The requested team does not exist',
      });
    }

    // Check if user is owner or admin
    const members = await spannerClient.teams.getMembers(teamId);
    const userMembership = members.find(member => member.user_id === userId);

    if (!userMembership || (userMembership.role !== 'admin' && team.owner_id !== userId)) {
      return res.status(403).json({
        error: 'Access denied',
        message: 'You do not have permission to update this team',
      });
    }

    await spannerClient.teams.update(teamId, updates);

    logger.info('Team updated', { teamId, updates, userId });

    res.json({
      success: true,
      data: {
        ...team,
        ...updates,
        updatedAt: new Date().toISOString(),
      },
    });
  } catch (error) {
    logger.error('Error updating team', { error, teamId, userId });
    res.status(500).json({
      error: 'Failed to update team',
      message: 'An error occurred while updating the team',
    });
  }
}));

/**
 * Add team member
 */
router.post('/:teamId/members', asyncHandler(async (req: Request, res: Response) => {
  const { teamId } = req.params;
  const { userId: newMemberId, role } = addMemberSchema.parse(req.body);
  const userId = req.user!.id;

  try {
    const team = await spannerClient.teams.get(teamId);
    
    if (!team) {
      return res.status(404).json({
        error: 'Team not found',
        message: 'The requested team does not exist',
      });
    }

    // Check if user is owner or admin
    const members = await spannerClient.teams.getMembers(teamId);
    const userMembership = members.find(member => member.user_id === userId);

    if (!userMembership || (userMembership.role !== 'admin' && team.owner_id !== userId)) {
      return res.status(403).json({
        error: 'Access denied',
        message: 'You do not have permission to add members to this team',
      });
    }

    // Check if user is already a member
    const existingMember = members.find(member => member.user_id === newMemberId);
    if (existingMember) {
      return res.status(409).json({
        error: 'User already a member',
        message: 'User is already a member of this team',
      });
    }

    await spannerClient.teams.addMember(teamId, newMemberId, role);

    logger.info('Team member added', { teamId, newMemberId, role, userId });

    res.status(201).json({
      success: true,
      data: {
        teamId,
        userId: newMemberId,
        role,
        joinedAt: new Date().toISOString(),
      },
    });
  } catch (error) {
    logger.error('Error adding team member', { error, teamId, newMemberId, userId });
    res.status(500).json({
      error: 'Failed to add team member',
      message: 'An error occurred while adding the team member',
    });
  }
}));

/**
 * Remove team member
 */
router.delete('/:teamId/members/:memberId', asyncHandler(async (req: Request, res: Response) => {
  const { teamId, memberId } = req.params;
  const userId = req.user!.id;

  try {
    const team = await spannerClient.teams.get(teamId);
    
    if (!team) {
      return res.status(404).json({
        error: 'Team not found',
        message: 'The requested team does not exist',
      });
    }

    // Check if user is owner or admin
    const members = await spannerClient.teams.getMembers(teamId);
    const userMembership = members.find(member => member.user_id === userId);

    if (!userMembership || (userMembership.role !== 'admin' && team.owner_id !== userId)) {
      return res.status(403).json({
        error: 'Access denied',
        message: 'You do not have permission to remove members from this team',
      });
    }

    // Cannot remove team owner
    if (memberId === team.owner_id) {
      return res.status(400).json({
        error: 'Cannot remove owner',
        message: 'Team owner cannot be removed',
      });
    }

    await spannerClient.teams.removeMember(teamId, memberId);

    logger.info('Team member removed', { teamId, memberId, userId });

    res.json({
      success: true,
      message: 'Team member removed successfully',
    });
  } catch (error) {
    logger.error('Error removing team member', { error, teamId, memberId, userId });
    res.status(500).json({
      error: 'Failed to remove team member',
      message: 'An error occurred while removing the team member',
    });
  }
}));

export { router as teamsController };