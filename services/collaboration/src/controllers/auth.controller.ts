/**
 * Authentication Controller
 * 
 * Secure authentication endpoints with comprehensive logging,
 * token management, and security controls.
 */

import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { asyncHandler } from '../middleware/error-handler.middleware';
import { 
  createRateLimit, 
  validateInput, 
  securityEventLogger 
} from '../middleware/security.middleware';
import { 
  jwtManager, 
  JWTPayload, 
  TokenError, 
  TokenExpiredError, 
  TokenInvalidError 
} from '../utils/jwt';
import { 
  auditService, 
  AuditEventType, 
  AuditSeverity 
} from '../services/audit.service';
import { spannerClient } from '../clients/spanner.client';
import { redisClient } from '../clients/redis.client';
import { logger } from '../utils/logger';
import { body, header } from 'express-validator';
import bcrypt from 'bcrypt';
import crypto from 'crypto';

const router = Router();

// Apply rate limiting and security middleware
router.use(createRateLimit('auth'));
router.use(securityEventLogger);

// Validation schemas
const loginSchema = z.object({
  email: z.string().email(),
  password: z.string().min(8),
  deviceId: z.string().optional(),
  rememberMe: z.boolean().default(false),
});

const refreshTokenSchema = z.object({
  refreshToken: z.string(),
  deviceId: z.string().optional(),
});

const changePasswordSchema = z.object({
  currentPassword: z.string(),
  newPassword: z.string().min(8).regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/),
  confirmPassword: z.string(),
});

const forgotPasswordSchema = z.object({
  email: z.string().email(),
});

const resetPasswordSchema = z.object({
  token: z.string(),
  newPassword: z.string().min(8).regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/),
  confirmPassword: z.string(),
});

// Validation middleware
const loginValidation = [
  body('email').isEmail().normalizeEmail(),
  body('password').isLength({ min: 8 }),
  body('deviceId').optional().isString(),
  body('rememberMe').optional().isBoolean(),
];

const refreshTokenValidation = [
  body('refreshToken').isString().notEmpty(),
  body('deviceId').optional().isString(),
];

const changePasswordValidation = [
  body('currentPassword').isString().notEmpty(),
  body('newPassword').isStrongPassword({
    minLength: 8,
    minLowercase: 1,
    minUppercase: 1,
    minNumbers: 1,
    minSymbols: 1,
  }),
  body('confirmPassword').isString().notEmpty(),
];

/**
 * Login endpoint
 */
router.post('/login', 
  loginValidation,
  validateInput,
  asyncHandler(async (req: Request, res: Response) => {
    const { email, password, deviceId, rememberMe } = loginSchema.parse(req.body);
    const ipAddress = req.ip || 'unknown';
    const userAgent = req.get('User-Agent') || 'unknown';
    const requestId = res.getHeader('X-Request-ID') as string;

    try {
      // Check for recent failed attempts
      const failedAttempts = await getFailedAttempts(email, ipAddress);
      if (failedAttempts >= 5) {
        await auditService.logSecurityEvent(
          AuditEventType.SUSPICIOUS_ACTIVITY,
          AuditSeverity.HIGH,
          'Login blocked due to repeated failures',
          {
            email,
            failedAttempts,
            ipAddress,
            userAgent,
          },
          { ipAddress, userAgent, requestId }
        );

        return res.status(429).json({
          error: 'Too Many Attempts',
          message: 'Account temporarily locked due to repeated failed login attempts',
          retryAfter: 900, // 15 minutes
        });
      }

      // Get user from database
      const user = await spannerClient.users.getByEmail(email);
      if (!user) {
        await recordFailedAttempt(email, ipAddress);
        await auditService.logAuthEvent(
          AuditEventType.LOGIN_FAILURE,
          'unknown',
          email,
          false,
          { reason: 'User not found' },
          { ipAddress, userAgent, requestId }
        );

        return res.status(401).json({
          error: 'Invalid Credentials',
          message: 'Invalid email or password',
        });
      }

      // Verify password
      const isPasswordValid = await bcrypt.compare(password, user.password_hash);
      if (!isPasswordValid) {
        await recordFailedAttempt(email, ipAddress);
        await auditService.logAuthEvent(
          AuditEventType.LOGIN_FAILURE,
          user.id,
          email,
          false,
          { reason: 'Invalid password' },
          { ipAddress, userAgent, requestId }
        );

        return res.status(401).json({
          error: 'Invalid Credentials',
          message: 'Invalid email or password',
        });
      }

      // Check if account is locked
      if (user.locked_until && user.locked_until > new Date()) {
        await auditService.logSecurityEvent(
          AuditEventType.ACCESS_DENIED,
          AuditSeverity.MEDIUM,
          'Login attempt on locked account',
          {
            userId: user.id,
            lockedUntil: user.locked_until,
          },
          { 
            userId: user.id, 
            userEmail: email, 
            ipAddress, 
            userAgent, 
            requestId 
          }
        );

        return res.status(423).json({
          error: 'Account Locked',
          message: 'Account is temporarily locked',
          unlocksAt: user.locked_until.toISOString(),
        });
      }

      // Clear failed attempts
      await clearFailedAttempts(email, ipAddress);

      // Get user teams and permissions
      const userTeams = await spannerClient.teams.listByUser(user.id);
      const teamIds = userTeams.map(team => team.id);

      // Generate token pair
      const tokenPair = jwtManager.generateTokenPair(
        user.id,
        user.email,
        user.name,
        user.role || 'member',
        user.permissions || ['read', 'write'],
        teamIds,
        deviceId,
        ipAddress
      );

      // Store session information
      await storeUserSession(user.id, deviceId, ipAddress, userAgent, rememberMe);

      // Update last login
      await spannerClient.users.update(user.id, {
        last_login: new Date().toISOString(),
        last_ip: ipAddress,
      });

      // Log successful login
      await auditService.logAuthEvent(
        AuditEventType.LOGIN_SUCCESS,
        user.id,
        email,
        true,
        { 
          deviceId, 
          rememberMe,
          teamsCount: teamIds.length,
        },
        { ipAddress, userAgent, requestId }
      );

      res.json({
        success: true,
        data: {
          user: {
            id: user.id,
            email: user.email,
            name: user.name,
            role: user.role || 'member',
            teams: userTeams,
          },
          tokens: tokenPair,
        },
      });
    } catch (error) {
      await auditService.logSecurityEvent(
        AuditEventType.SYSTEM_ERROR,
        AuditSeverity.HIGH,
        'Login endpoint error',
        { error: error instanceof Error ? error.message : String(error) },
        { ipAddress, userAgent, requestId }
      );

      logger.error('Login error', { error, email });
      res.status(500).json({
        error: 'Internal Server Error',
        message: 'An error occurred during login',
      });
    }
  })
);

/**
 * Refresh token endpoint
 */
router.post('/refresh',
  refreshTokenValidation,
  validateInput,
  asyncHandler(async (req: Request, res: Response) => {
    const { refreshToken, deviceId } = refreshTokenSchema.parse(req.body);
    const ipAddress = req.ip || 'unknown';
    const userAgent = req.get('User-Agent') || 'unknown';
    const requestId = res.getHeader('X-Request-ID') as string;

    try {
      const tokenPair = await jwtManager.refreshAccessToken(refreshToken);

      // Verify token to get user info
      const payload = jwtManager.verifyToken(tokenPair.accessToken);

      await auditService.logAuthEvent(
        AuditEventType.TOKEN_REFRESH,
        payload.userId,
        payload.email,
        true,
        { deviceId },
        { ipAddress, userAgent, requestId }
      );

      res.json({
        success: true,
        data: {
          tokens: tokenPair,
        },
      });
    } catch (error) {
      if (error instanceof TokenError) {
        await auditService.logSecurityEvent(
          AuditEventType.INVALID_TOKEN,
          AuditSeverity.MEDIUM,
          'Invalid refresh token',
          { error: error instanceof Error ? error.message : String(error) },
          { ipAddress, userAgent, requestId }
        );

        return res.status(401).json({
          error: 'Invalid Token',
          message: 'Refresh token is invalid or expired',
        });
      }

      logger.error('Token refresh error', { error });
      res.status(500).json({
        error: 'Internal Server Error',
        message: 'An error occurred during token refresh',
      });
    }
  })
);

/**
 * Logout endpoint
 */
router.post('/logout', asyncHandler(async (req: Request, res: Response) => {
  const token = req.get('Authorization')?.replace('Bearer ', '');
  const ipAddress = req.ip;
  const userAgent = req.get('User-Agent');
  const requestId = res.getHeader('X-Request-ID') as string;

  try {
    if (token) {
      // Blacklist the token
      jwtManager.blacklistToken(token);

      // Get user info from token
      try {
        const payload = jwtManager.verifyToken(token);
        
        // Remove user session
        await removeUserSession(payload.userId, req.body.deviceId);

        await auditService.logAuthEvent(
          AuditEventType.LOGOUT,
          payload.userId,
          payload.email,
          true,
          { deviceId: req.body.deviceId },
          { ipAddress, userAgent, requestId }
        );
      } catch (error) {
        // Token might be expired, that's ok for logout
      }
    }

    res.json({
      success: true,
      message: 'Logged out successfully',
    });
  } catch (error) {
    logger.error('Logout error', { error });
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'An error occurred during logout',
    });
  }
}));

/**
 * Change password endpoint
 */
router.post('/change-password',
  changePasswordValidation,
  validateInput,
  asyncHandler(async (req: Request, res: Response) => {
    if (!req.user) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required',
      });
    }

    const { currentPassword, newPassword, confirmPassword } = changePasswordSchema.parse(req.body);
    const user = req.user!;
    const ipAddress = req.ip || 'unknown';
    const userAgent = req.get('User-Agent') || 'unknown';
    const requestId = res.getHeader('X-Request-ID') as string;

    try {
      // Validate passwords match
      if (newPassword !== confirmPassword) {
        return res.status(400).json({
          error: 'Validation Error',
          message: 'New passwords do not match',
        });
      }

      // Get user from database
      const userData = await spannerClient.users.get(user.id);
      if (!userData) {
        return res.status(404).json({
          error: 'User Not Found',
          message: 'User not found',
        });
      }

      // Verify current password
      const isCurrentPasswordValid = await bcrypt.compare(currentPassword, userData.password_hash);
      if (!isCurrentPasswordValid) {
        await auditService.logSecurityEvent(
          AuditEventType.SUSPICIOUS_ACTIVITY,
          AuditSeverity.MEDIUM,
          'Invalid current password on password change',
          { userId: user.id },
          { 
            userId: user.id, 
            userEmail: user.email, 
            ipAddress, 
            userAgent, 
            requestId 
          }
        );

        return res.status(401).json({
          error: 'Invalid Password',
          message: 'Current password is incorrect',
        });
      }

      // Hash new password
      const saltRounds = 12;
      const hashedNewPassword = await bcrypt.hash(newPassword, saltRounds);

      // Update password
      await spannerClient.users.update(user.id, {
        password_hash: hashedNewPassword,
        password_changed_at: new Date().toISOString(),
      });

      // Revoke all existing tokens for security
      jwtManager.revokeAllUserTokens(user.id);

      await auditService.logSecurityEvent(
        AuditEventType.PERMISSION_ELEVATED,
        AuditSeverity.MEDIUM,
        'Password changed successfully',
        { userId: user.id },
        { 
          userId: user.id, 
          userEmail: user.email, 
          ipAddress, 
          userAgent, 
          requestId 
        }
      );

      res.json({
        success: true,
        message: 'Password changed successfully. Please log in again.',
      });
    } catch (error) {
      logger.error('Change password error', { error, userId: user.id });
      res.status(500).json({
        error: 'Internal Server Error',
        message: 'An error occurred while changing password',
      });
    }
  })
);

/**
 * Get current user info
 */
router.get('/me', asyncHandler(async (req: Request, res: Response) => {
  if (!req.user) {
    return res.status(401).json({
      error: 'Unauthorized',
      message: 'Authentication required',
    });
  }

  const user = req.user!;

  try {
    const userData = await spannerClient.users.get(user.id);
    const userTeams = await spannerClient.teams.listByUser(user.id);

    res.json({
      success: true,
      data: {
        id: userData.id,
        email: userData.email,
        name: userData.name,
        role: userData.role || 'member',
        teams: userTeams,
        lastLogin: userData.last_login,
        createdAt: userData.created_at,
      },
    });
  } catch (error) {
    logger.error('Get user info error', { error, userId: user.id });
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'An error occurred while fetching user information',
    });
  }
}));

/**
 * Get user sessions
 */
router.get('/sessions', asyncHandler(async (req: Request, res: Response) => {
  if (!req.user) {
    return res.status(401).json({
      error: 'Unauthorized',
      message: 'Authentication required',
    });
  }

  const user = req.user!;

  try {
    const sessions = await getUserSessions(user.id);
    res.json({
      success: true,
      data: sessions,
    });
  } catch (error) {
    logger.error('Get user sessions error', { error, userId: user.id });
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'An error occurred while fetching sessions',
    });
  }
}));

/**
 * Revoke session
 */
router.delete('/sessions/:sessionId', asyncHandler(async (req: Request, res: Response) => {
  if (!req.user) {
    return res.status(401).json({
      error: 'Unauthorized',
      message: 'Authentication required',
    });
  }

  const user = req.user!;
  const { sessionId } = req.params;

  try {
    await removeUserSession(user.id, sessionId);
    
    await auditService.logSecurityEvent(
      AuditEventType.TOKEN_REVOKE,
      AuditSeverity.LOW,
      'Session revoked by user',
      { sessionId },
      { 
        userId: user.id, 
        userEmail: user.email, 
        ipAddress: req.ip, 
        userAgent: req.get('User-Agent'), 
        requestId: res.getHeader('X-Request-ID') as string 
      }
    );

    res.json({
      success: true,
      message: 'Session revoked successfully',
    });
  } catch (error) {
    logger.error('Revoke session error', { error, userId: user.id, sessionId });
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'An error occurred while revoking session',
    });
  }
}));

// Helper functions
async function getFailedAttempts(email: string, ipAddress: string): Promise<number> {
  const key = `failed_attempts:${email}:${ipAddress}`;
  const attempts = await redisClient.get(key);
  return parseInt(attempts || '0', 10);
}

async function recordFailedAttempt(email: string, ipAddress: string): Promise<void> {
  const key = `failed_attempts:${email}:${ipAddress}`;
  const attempts = await getFailedAttempts(email, ipAddress);
  await redisClient.set(key, (attempts + 1).toString(), 900); // 15 minutes TTL
}

async function clearFailedAttempts(email: string, ipAddress: string): Promise<void> {
  const key = `failed_attempts:${email}:${ipAddress}`;
  await redisClient.del(key);
}

async function storeUserSession(
  userId: string,
  deviceId: string | undefined,
  ipAddress: string,
  userAgent: string | undefined,
  rememberMe: boolean
): Promise<void> {
  const sessionId = deviceId || crypto.randomUUID();
  const sessionData = {
    userId,
    deviceId: sessionId,
    ipAddress,
    userAgent,
    rememberMe,
    createdAt: new Date().toISOString(),
    lastActive: new Date().toISOString(),
  };

  const key = `session:${userId}:${sessionId}`;
  const ttl = rememberMe ? 30 * 24 * 60 * 60 : 24 * 60 * 60; // 30 days or 1 day
  await redisClient.set(key, JSON.stringify(sessionData), ttl);
}

async function removeUserSession(userId: string, sessionId: string): Promise<void> {
  const key = `session:${userId}:${sessionId}`;
  await redisClient.del(key);
}

async function getUserSessions(userId: string): Promise<any[]> {
  const pattern = `session:${userId}:*`;
  const keys = await redisClient.getRawClient().keys(pattern);
  
  const sessions = [];
  for (const key of keys) {
    const sessionData = await redisClient.get(key);
    if (sessionData) {
      sessions.push(JSON.parse(sessionData));
    }
  }
  
  return sessions;
}

export { router as authController };