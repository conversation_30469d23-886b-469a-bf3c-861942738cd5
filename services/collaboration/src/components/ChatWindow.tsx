/**
 * Chat Window Component
 * 
 * Real-time chat interface for collaboration sessions
 * with message history, typing indicators, and emoji support.
 */

import React, { useState, useRef, useEffect, KeyboardEvent } from 'react';
import { useCollaborationContext } from './CollaborationProvider';
import { useSessionMessages, useTypingIndicator } from '../hooks/useCollaboration';

// Message types
interface Message {
  id: string;
  userId: string;
  userEmail: string;
  userName?: string;
  message: string;
  type: 'text' | 'system' | 'analysis_share';
  timestamp: string;
  metadata?: Record<string, any>;
}

interface ChatWindowProps {
  sessionId: string;
  height?: string;
  className?: string;
}

/**
 * Chat Window Component
 */
export function ChatWindow({ sessionId, height = 'h-96', className = '' }: ChatWindowProps) {
  const [inputMessage, setInputMessage] = useState('');
  const [isMinimized, setIsMinimized] = useState(false);
  const messageEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  
  const { messages, participants, sendMessage, isConnected } = useCollaborationContext();
  const { startTyping, stopTyping, typingUsers } = useTypingIndicator(sessionId);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messageEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Handle message sending
  const handleSendMessage = () => {
    if (!inputMessage.trim() || !isConnected) return;

    sendMessage(sessionId, inputMessage.trim());
    setInputMessage('');
    stopTyping();
  };

  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputMessage(e.target.value);
    startTyping();
  };

  // Handle key presses
  const handleKeyPress = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Format timestamp
  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Get user display name
  const getUserName = (userId: string, userEmail: string, userName?: string) => {
    if (userName) return userName;
    return userEmail.split('@')[0];
  };

  if (isMinimized) {
    return (
      <div className={`bg-white border border-gray-300 rounded-lg shadow-lg ${className}`}>
        <div 
          className="p-3 border-b border-gray-200 flex items-center justify-between cursor-pointer"
          onClick={() => setIsMinimized(false)}
        >
          <h3 className="font-medium text-gray-800">Chat</h3>
          <button className="text-gray-400 hover:text-gray-600">
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white border border-gray-300 rounded-lg shadow-lg ${className}`}>
      {/* Header */}
      <div className="p-3 border-b border-gray-200 flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <h3 className="font-medium text-gray-800">Chat</h3>
          <span className="text-xs text-gray-500">
            {participants.length} participant{participants.length !== 1 ? 's' : ''}
          </span>
        </div>
        <div className="flex items-center space-x-2">
          <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
          <button
            onClick={() => setIsMinimized(true)}
            className="text-gray-400 hover:text-gray-600"
          >
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clipRule="evenodd" />
            </svg>
          </button>
        </div>
      </div>

      {/* Messages */}
      <div className={`overflow-y-auto p-3 space-y-3 ${height}`}>
        {messages.length === 0 ? (
          <div className="text-center text-gray-500 py-8">
            <p>No messages yet. Start the conversation!</p>
          </div>
        ) : (
          messages.map((message) => (
            <MessageBubble key={message.id} message={message} />
          ))
        )}
        
        {/* Typing indicator */}
        {typingUsers.length > 0 && (
          <div className="flex items-center space-x-2 text-gray-500 text-sm">
            <div className="flex space-x-1">
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" />
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
            </div>
            <span>
              {typingUsers.length === 1 
                ? `${typingUsers[0]} is typing...`
                : `${typingUsers.length} people are typing...`
              }
            </span>
          </div>
        )}
        
        <div ref={messageEndRef} />
      </div>

      {/* Input */}
      <div className="p-3 border-t border-gray-200">
        <div className="flex space-x-2">
          <input
            ref={inputRef}
            type="text"
            value={inputMessage}
            onChange={handleInputChange}
            onKeyPress={handleKeyPress}
            placeholder={isConnected ? "Type a message..." : "Connecting..."}
            disabled={!isConnected}
            className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100"
          />
          <button
            onClick={handleSendMessage}
            disabled={!inputMessage.trim() || !isConnected}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:bg-gray-400 disabled:cursor-not-allowed"
          >
            Send
          </button>
        </div>
      </div>
    </div>
  );
}

/**
 * Message Bubble Component
 */
interface MessageBubbleProps {
  message: Message;
}

function MessageBubble({ message }: MessageBubbleProps) {
  const isSystem = message.type === 'system';
  const isAnalysis = message.type === 'analysis_share';
  
  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getUserName = (userId: string, userEmail: string, userName?: string) => {
    if (userName) return userName;
    return userEmail.split('@')[0];
  };

  if (isSystem) {
    return (
      <div className="text-center">
        <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
          {message.message}
        </span>
      </div>
    );
  }

  if (isAnalysis) {
    return (
      <div className="border border-blue-200 rounded-lg p-3 bg-blue-50">
        <div className="flex items-center space-x-2 mb-2">
          <div className="w-2 h-2 bg-blue-500 rounded-full" />
          <span className="text-sm font-medium text-blue-800">
            {getUserName(message.userId, message.userEmail, message.userName)}
          </span>
          <span className="text-xs text-blue-600">
            shared an analysis
          </span>
          <span className="text-xs text-gray-500 ml-auto">
            {formatTime(message.timestamp)}
          </span>
        </div>
        <div className="text-sm text-blue-700">
          {message.message}
        </div>
        {message.metadata && (
          <div className="mt-2 text-xs text-blue-600">
            <details>
              <summary className="cursor-pointer">View details</summary>
              <pre className="mt-1 text-xs bg-blue-100 p-2 rounded overflow-auto">
                {JSON.stringify(message.metadata, null, 2)}
              </pre>
            </details>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="flex items-start space-x-3">
      <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center text-xs font-medium">
        {getUserName(message.userId, message.userEmail, message.userName).charAt(0).toUpperCase()}
      </div>
      <div className="flex-1 min-w-0">
        <div className="flex items-center space-x-2 mb-1">
          <span className="text-sm font-medium text-gray-800">
            {getUserName(message.userId, message.userEmail, message.userName)}
          </span>
          <span className="text-xs text-gray-500">
            {formatTime(message.timestamp)}
          </span>
        </div>
        <div className="text-sm text-gray-700 break-words">
          {message.message}
        </div>
      </div>
    </div>
  );
}

export default ChatWindow;