/**
 * Cursor Overlay Component
 * 
 * Displays real-time cursor positions of other users
 * with user names and smooth animations.
 */

import React, { useEffect, useState } from 'react';
import { useCollaborationContext } from './CollaborationProvider';
import { useCursorTracking, usePresence } from '../hooks/useCollaboration';

interface CursorPosition {
  userId: string;
  userEmail: string;
  userName?: string;
  position: {
    x: number;
    y: number;
    elementId?: string;
  };
  timestamp: string;
}

interface CursorOverlayProps {
  sessionId: string;
  enabled?: boolean;
  className?: string;
}

/**
 * Cursor Overlay Component
 */
export function CursorOverlay({ sessionId, enabled = true, className = '' }: CursorOverlayProps) {
  const { cursors, updateCursor, participants } = useCollaborationContext();
  const { getUserColor } = usePresence(participants);
  const { isTracking, startTracking, stopTracking } = useCursorTracking(sessionId, updateCursor);

  // Auto-start tracking when enabled
  useEffect(() => {
    if (enabled && !isTracking) {
      const cleanup = startTracking();
      return cleanup;
    } else if (!enabled && isTracking) {
      stopTracking();
    }
  }, [enabled, isTracking, startTracking, stopTracking]);

  if (!enabled) {
    return null;
  }

  return (
    <div className={`fixed inset-0 pointer-events-none z-50 ${className}`}>
      {Object.entries(cursors).map(([userId, cursor]) => (
        <RemoteCursor
          key={userId}
          cursor={cursor}
          color={getUserColor(userId)}
        />
      ))}
    </div>
  );
}

/**
 * Remote Cursor Component
 */
interface RemoteCursorProps {
  cursor: CursorPosition;
  color: string;
}

function RemoteCursor({ cursor, color }: RemoteCursorProps) {
  const [isVisible, setIsVisible] = useState(true);
  const [lastMovement, setLastMovement] = useState(Date.now());

  // Hide cursor after inactivity
  useEffect(() => {
    setLastMovement(Date.now());
    setIsVisible(true);

    const timer = setTimeout(() => {
      setIsVisible(false);
    }, 10000); // Hide after 10 seconds of inactivity

    return () => clearTimeout(timer);
  }, [cursor.position.x, cursor.position.y]);

  const getUserName = (userId: string, userEmail: string, userName?: string) => {
    if (userName) return userName;
    return userEmail.split('@')[0];
  };

  if (!isVisible) {
    return null;
  }

  return (
    <div
      className="absolute transition-all duration-200 ease-out"
      style={{
        left: cursor.position.x,
        top: cursor.position.y,
        transform: 'translate(-2px, -2px)',
      }}
    >
      {/* Cursor pointer */}
      <div className="relative">
        <svg
          width="20"
          height="20"
          viewBox="0 0 20 20"
          fill="none"
          className="drop-shadow-lg"
        >
          <path
            d="M2 2L18 8L10 10L8 18L2 2Z"
            fill={color}
            stroke="white"
            strokeWidth="1"
          />
        </svg>
        
        {/* User name label */}
        <div
          className="absolute left-5 top-2 px-2 py-1 rounded text-xs font-medium text-white shadow-lg whitespace-nowrap"
          style={{ backgroundColor: color }}
        >
          {getUserName(cursor.userId, cursor.userEmail, cursor.userName)}
        </div>
      </div>
    </div>
  );
}

/**
 * Cursor Trail Component (optional visual effect)
 */
interface CursorTrailProps {
  userId: string;
  positions: Array<{ x: number; y: number; timestamp: number }>;
  color: string;
}

export function CursorTrail({ userId, positions, color }: CursorTrailProps) {
  const [trailPositions, setTrailPositions] = useState<Array<{ x: number; y: number; opacity: number }>>([]);

  useEffect(() => {
    const maxTrailLength = 10;
    const now = Date.now();
    
    const newTrail = positions
      .filter(pos => now - pos.timestamp < 2000) // Only show trail for 2 seconds
      .slice(-maxTrailLength)
      .map((pos, index, arr) => ({
        x: pos.x,
        y: pos.y,
        opacity: (index + 1) / arr.length * 0.5, // Fade out older positions
      }));

    setTrailPositions(newTrail);
  }, [positions]);

  return (
    <div className="fixed inset-0 pointer-events-none z-40">
      {trailPositions.map((pos, index) => (
        <div
          key={index}
          className="absolute w-2 h-2 rounded-full transition-opacity duration-300"
          style={{
            left: pos.x,
            top: pos.y,
            backgroundColor: color,
            opacity: pos.opacity,
            transform: 'translate(-50%, -50%)',
          }}
        />
      ))}
    </div>
  );
}

/**
 * Cursor Settings Component
 */
interface CursorSettingsProps {
  enabled: boolean;
  onToggle: (enabled: boolean) => void;
  showTrail?: boolean;
  onTrailToggle?: (enabled: boolean) => void;
}

export function CursorSettings({ 
  enabled, 
  onToggle, 
  showTrail = false, 
  onTrailToggle 
}: CursorSettingsProps) {
  return (
    <div className="p-4 bg-white border border-gray-200 rounded-lg shadow-sm">
      <h3 className="font-medium text-gray-800 mb-3">Cursor Settings</h3>
      
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600">Show cursors</span>
          <button
            onClick={() => onToggle(!enabled)}
            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
              enabled ? 'bg-blue-600' : 'bg-gray-200'
            }`}
          >
            <span
              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                enabled ? 'translate-x-6' : 'translate-x-1'
              }`}
            />
          </button>
        </div>

        {onTrailToggle && (
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Cursor trail</span>
            <button
              onClick={() => onTrailToggle(!showTrail)}
              disabled={!enabled}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                showTrail && enabled ? 'bg-blue-600' : 'bg-gray-200'
              } ${!enabled ? 'opacity-50' : ''}`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  showTrail && enabled ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
          </div>
        )}
      </div>
    </div>
  );
}

/**
 * Cursor Analytics Component
 */
interface CursorAnalyticsProps {
  sessionId: string;
  cursors: Record<string, CursorPosition>;
}

export function CursorAnalytics({ sessionId, cursors }: CursorAnalyticsProps) {
  const [heatmapData, setHeatmapData] = useState<Array<{ x: number; y: number; intensity: number }>>([]);

  useEffect(() => {
    // Simple heatmap calculation
    const positions = Object.values(cursors).map(cursor => ({
      x: cursor.position.x,
      y: cursor.position.y,
    }));

    // Group positions into grid cells
    const gridSize = 50;
    const grid: Record<string, number> = {};

    positions.forEach(pos => {
      const gridX = Math.floor(pos.x / gridSize);
      const gridY = Math.floor(pos.y / gridSize);
      const key = `${gridX},${gridY}`;
      grid[key] = (grid[key] || 0) + 1;
    });

    // Convert to heatmap data
    const heatmap = Object.entries(grid).map(([key, count]) => {
      const [x, y] = key.split(',').map(Number);
      return {
        x: x * gridSize,
        y: y * gridSize,
        intensity: count,
      };
    });

    setHeatmapData(heatmap);
  }, [cursors]);

  return (
    <div className="p-4 bg-white border border-gray-200 rounded-lg shadow-sm">
      <h3 className="font-medium text-gray-800 mb-3">Cursor Analytics</h3>
      
      <div className="space-y-2 text-sm">
        <div className="flex justify-between">
          <span className="text-gray-600">Active cursors:</span>
          <span className="font-medium">{Object.keys(cursors).length}</span>
        </div>
        
        <div className="flex justify-between">
          <span className="text-gray-600">Hotspots:</span>
          <span className="font-medium">{heatmapData.length}</span>
        </div>
      </div>
    </div>
  );
}

export default CursorOverlay;