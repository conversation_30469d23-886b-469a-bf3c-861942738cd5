/**
 * Collaboration Demo Component
 * 
 * Complete example demonstrating all collaboration features
 * including real-time chat, cursor tracking, and session management.
 */

import React, { useState, useEffect } from 'react';
import { CollaborationProvider, useCollaborationContext } from './CollaborationProvider';
import { ChatWindow } from './ChatWindow';
import { CursorOverlay } from './CursorOverlay';
import { ConnectionStatus, ParticipantsList, SessionGuard } from './CollaborationProvider';

// Example usage component
interface CollaborationDemoProps {
  serverUrl: string;
  token: string;
  teamId: string;
  sessionId: string;
}

/**
 * Main Demo Component
 */
export function CollaborationDemo({ 
  serverUrl, 
  token, 
  teamId, 
  sessionId 
}: CollaborationDemoProps) {
  return (
    <CollaborationProvider serverUrl={serverUrl} token={token}>
      <div className="min-h-screen bg-gray-50">
        <CollaborationDemoContent teamId={teamId} sessionId={sessionId} />
      </div>
    </CollaborationProvider>
  );
}

/**
 * Demo Content Component
 */
function CollaborationDemoContent({ teamId, sessionId }: { teamId: string; sessionId: string }) {
  const [cursorEnabled, setCursorEnabled] = useState(true);
  const [chatMinimized, setChatMinimized] = useState(false);
  const [activeTab, setActiveTab] = useState<'analysis' | 'chat' | 'settings'>('analysis');
  
  const { 
    isConnected, 
    joinSession, 
    leaveSession, 
    currentSessionId,
    participants,
    shareAnalysis
  } = useCollaborationContext();

  // Join session on mount
  useEffect(() => {
    if (isConnected && sessionId && currentSessionId !== sessionId) {
      joinSession(sessionId);
    }
  }, [isConnected, sessionId, currentSessionId, joinSession]);

  // Example analysis data
  const handleShareAnalysis = () => {
    const exampleAnalysis = {
      id: `analysis-${Date.now()}`,
      type: 'code_analysis' as const,
      title: 'Code Quality Analysis',
      results: {
        complexity: 'medium',
        maintainability: 85,
        testCoverage: 72,
        issues: [
          'Unused variable on line 42',
          'Consider extracting method for better readability',
          'Missing error handling in async function'
        ]
      },
      metadata: {
        language: 'TypeScript',
        linesOfCode: 1250,
        executionTime: '2.3s'
      }
    };

    shareAnalysis(sessionId, exampleAnalysis);
  };

  return (
    <div className="flex h-screen">
      {/* Cursor overlay */}
      <CursorOverlay sessionId={sessionId} enabled={cursorEnabled} />

      {/* Main content area */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <header className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <h1 className="text-xl font-semibold text-gray-800">
                Collaboration Demo
              </h1>
              <span className="text-sm text-gray-500">
                Session: {sessionId.slice(0, 8)}...
              </span>
            </div>
            <div className="flex items-center space-x-4">
              <ConnectionStatus />
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-600">
                  {participants.length} online
                </span>
                <div className="flex -space-x-2">
                  {participants.slice(0, 3).map((participant) => (
                    <div
                      key={participant.id}
                      className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs font-medium border-2 border-white"
                      title={participant.name || participant.email}
                    >
                      {(participant.name || participant.email).charAt(0).toUpperCase()}
                    </div>
                  ))}
                  {participants.length > 3 && (
                    <div className="w-8 h-8 bg-gray-500 rounded-full flex items-center justify-center text-white text-xs font-medium border-2 border-white">
                      +{participants.length - 3}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </header>

        {/* Navigation tabs */}
        <nav className="bg-white border-b border-gray-200 px-6">
          <div className="flex space-x-8">
            {['analysis', 'chat', 'settings'].map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab as any)}
                className={`py-3 px-1 border-b-2 font-medium text-sm capitalize ${
                  activeTab === tab
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                {tab}
              </button>
            ))}
          </div>
        </nav>

        {/* Main content */}
        <main className="flex-1 overflow-hidden">
          <SessionGuard sessionId={sessionId}>
            {activeTab === 'analysis' && (
              <AnalysisView onShareAnalysis={handleShareAnalysis} />
            )}
            {activeTab === 'chat' && (
              <div className="h-full p-6">
                <ChatWindow sessionId={sessionId} height="h-full" />
              </div>
            )}
            {activeTab === 'settings' && (
              <SettingsView 
                cursorEnabled={cursorEnabled}
                onCursorToggle={setCursorEnabled}
              />
            )}
          </SessionGuard>
        </main>
      </div>

      {/* Sidebar */}
      <aside className="w-80 bg-white border-l border-gray-200 p-6">
        <div className="space-y-6">
          <ParticipantsList />
          
          <div className="space-y-2">
            <h3 className="font-medium text-sm text-gray-700">Quick Actions</h3>
            <div className="space-y-2">
              <button
                onClick={handleShareAnalysis}
                className="w-full px-3 py-2 text-left text-sm bg-blue-50 text-blue-700 rounded-lg hover:bg-blue-100"
              >
                Share Analysis Results
              </button>
              <button
                onClick={() => setCursorEnabled(!cursorEnabled)}
                className="w-full px-3 py-2 text-left text-sm bg-gray-50 text-gray-700 rounded-lg hover:bg-gray-100"
              >
                {cursorEnabled ? 'Hide' : 'Show'} Cursors
              </button>
            </div>
          </div>

          <div className="space-y-2">
            <h3 className="font-medium text-sm text-gray-700">Session Info</h3>
            <div className="text-sm text-gray-600 space-y-1">
              <div>ID: {sessionId.slice(0, 8)}...</div>
              <div>Team: {teamId.slice(0, 8)}...</div>
              <div>Status: {isConnected ? 'Connected' : 'Disconnected'}</div>
            </div>
          </div>
        </div>
      </aside>
    </div>
  );
}

/**
 * Analysis View Component
 */
interface AnalysisViewProps {
  onShareAnalysis: () => void;
}

function AnalysisView({ onShareAnalysis }: AnalysisViewProps) {
  const [code, setCode] = useState(`// Example TypeScript code for analysis
import { useState, useEffect } from 'react';

interface User {
  id: string;
  name: string;
  email: string;
}

export function UserProfile({ userId }: { userId: string }) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchUser(userId).then(user => {
      setUser(user);
      setLoading(false);
    });
  }, [userId]);

  if (loading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="user-profile">
      <h1>{user?.name}</h1>
      <p>{user?.email}</p>
    </div>
  );
}

async function fetchUser(id: string): Promise<User> {
  const response = await fetch(\`/api/users/\${id}\`);
  return response.json();
}`);

  return (
    <div className="h-full flex">
      {/* Code editor area */}
      <div className="flex-1 p-6">
        <div className="h-full bg-gray-900 rounded-lg p-4">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-white font-medium">Code Analysis</h2>
            <button
              onClick={onShareAnalysis}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              Share Analysis
            </button>
          </div>
          <textarea
            value={code}
            onChange={(e) => setCode(e.target.value)}
            className="w-full h-full bg-gray-800 text-gray-100 font-mono text-sm p-4 rounded border-none resize-none focus:outline-none"
            placeholder="Enter your code here..."
          />
        </div>
      </div>

      {/* Analysis results */}
      <div className="w-80 p-6 bg-gray-50">
        <h3 className="font-medium text-gray-800 mb-4">Analysis Results</h3>
        <div className="space-y-4">
          <div className="bg-white p-4 rounded-lg border">
            <h4 className="font-medium text-green-600 mb-2">✓ Code Quality</h4>
            <p className="text-sm text-gray-600">
              Overall quality score: 85/100
            </p>
          </div>
          
          <div className="bg-white p-4 rounded-lg border">
            <h4 className="font-medium text-yellow-600 mb-2">⚠ Suggestions</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Add error handling for async operations</li>
              <li>• Consider memoizing expensive calculations</li>
              <li>• Add loading states for better UX</li>
            </ul>
          </div>
          
          <div className="bg-white p-4 rounded-lg border">
            <h4 className="font-medium text-blue-600 mb-2">ℹ Stats</h4>
            <div className="text-sm text-gray-600 space-y-1">
              <div>Lines of code: 35</div>
              <div>Complexity: Medium</div>
              <div>Test coverage: 72%</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

/**
 * Settings View Component
 */
interface SettingsViewProps {
  cursorEnabled: boolean;
  onCursorToggle: (enabled: boolean) => void;
}

function SettingsView({ cursorEnabled, onCursorToggle }: SettingsViewProps) {
  const [settings, setSettings] = useState({
    notifications: true,
    sounds: false,
    autoJoin: true,
    cursorTrail: false,
  });

  const handleSettingChange = (key: string, value: boolean) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  return (
    <div className="p-6 max-w-2xl">
      <h2 className="text-xl font-semibold text-gray-800 mb-6">
        Collaboration Settings
      </h2>
      
      <div className="space-y-6">
        {/* Cursor settings */}
        <div className="bg-white p-6 rounded-lg border">
          <h3 className="font-medium text-gray-800 mb-4">Cursor & Presence</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">
                  Show other users' cursors
                </label>
                <p className="text-sm text-gray-500">
                  Display real-time cursor positions of other participants
                </p>
              </div>
              <ToggleSwitch
                enabled={cursorEnabled}
                onChange={onCursorToggle}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">
                  Cursor trail effect
                </label>
                <p className="text-sm text-gray-500">
                  Show a trail behind cursor movements
                </p>
              </div>
              <ToggleSwitch
                enabled={settings.cursorTrail}
                onChange={(enabled) => handleSettingChange('cursorTrail', enabled)}
              />
            </div>
          </div>
        </div>

        {/* Notification settings */}
        <div className="bg-white p-6 rounded-lg border">
          <h3 className="font-medium text-gray-800 mb-4">Notifications</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">
                  Desktop notifications
                </label>
                <p className="text-sm text-gray-500">
                  Show notifications for messages and activities
                </p>
              </div>
              <ToggleSwitch
                enabled={settings.notifications}
                onChange={(enabled) => handleSettingChange('notifications', enabled)}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">
                  Sound notifications
                </label>
                <p className="text-sm text-gray-500">
                  Play sounds for incoming messages
                </p>
              </div>
              <ToggleSwitch
                enabled={settings.sounds}
                onChange={(enabled) => handleSettingChange('sounds', enabled)}
              />
            </div>
          </div>
        </div>

        {/* Session settings */}
        <div className="bg-white p-6 rounded-lg border">
          <h3 className="font-medium text-gray-800 mb-4">Session</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">
                  Auto-join sessions
                </label>
                <p className="text-sm text-gray-500">
                  Automatically join when invited to a session
                </p>
              </div>
              <ToggleSwitch
                enabled={settings.autoJoin}
                onChange={(enabled) => handleSettingChange('autoJoin', enabled)}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

/**
 * Toggle Switch Component
 */
interface ToggleSwitchProps {
  enabled: boolean;
  onChange: (enabled: boolean) => void;
}

function ToggleSwitch({ enabled, onChange }: ToggleSwitchProps) {
  return (
    <button
      onClick={() => onChange(!enabled)}
      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
        enabled ? 'bg-blue-600' : 'bg-gray-200'
      }`}
    >
      <span
        className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
          enabled ? 'translate-x-6' : 'translate-x-1'
        }`}
      />
    </button>
  );
}

export default CollaborationDemo;