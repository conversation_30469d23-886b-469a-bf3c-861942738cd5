/**
 * Collaboration Provider Component
 * 
 * React context provider for managing collaboration state
 * and providing collaboration features to child components.
 */

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { useCollaboration } from '../hooks/useCollaboration';
import { ConnectionState } from '../clients/websocket-client';

// Context types
interface CollaborationContextType {
  // Connection state
  connectionState: ConnectionState;
  isConnected: boolean;
  error: string | null;
  
  // Session management
  currentSessionId: string | null;
  participants: any[];
  messages: any[];
  cursors: Record<string, any>;
  
  // Actions
  connect: () => Promise<void>;
  disconnect: () => void;
  joinSession: (sessionId: string) => Promise<void>;
  leaveSession: (sessionId: string) => Promise<void>;
  sendMessage: (sessionId: string, message: string) => void;
  updateCursor: (sessionId: string, position: any) => void;
  shareAnalysis: (sessionId: string, analysis: any) => void;
}

const CollaborationContext = createContext<CollaborationContextType | null>(null);

// Provider props
interface CollaborationProviderProps {
  children: ReactNode;
  serverUrl: string;
  token: string;
  autoConnect?: boolean;
}

/**
 * Collaboration Provider Component
 */
export function CollaborationProvider({ 
  children, 
  serverUrl, 
  token, 
  autoConnect = true 
}: CollaborationProviderProps) {
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);
  
  const collaboration = useCollaboration(serverUrl, token);

  // Auto-connect on mount
  useEffect(() => {
    if (autoConnect && !collaboration.isConnected) {
      collaboration.connect();
    }
  }, [autoConnect, collaboration]);

  // Enhanced join session with state tracking
  const joinSession = async (sessionId: string) => {
    try {
      await collaboration.joinSession(sessionId);
      setCurrentSessionId(sessionId);
    } catch (error) {
      console.error('Failed to join session:', error);
      throw error;
    }
  };

  // Enhanced leave session with state tracking
  const leaveSession = async (sessionId: string) => {
    try {
      await collaboration.leaveSession(sessionId);
      if (currentSessionId === sessionId) {
        setCurrentSessionId(null);
      }
    } catch (error) {
      console.error('Failed to leave session:', error);
      throw error;
    }
  };

  const contextValue: CollaborationContextType = {
    // Connection state
    connectionState: collaboration.connectionState,
    isConnected: collaboration.isConnected,
    error: collaboration.error,
    
    // Session state
    currentSessionId,
    participants: collaboration.participants,
    messages: collaboration.messages,
    cursors: collaboration.cursors,
    
    // Actions
    connect: collaboration.connect,
    disconnect: collaboration.disconnect,
    joinSession,
    leaveSession,
    sendMessage: collaboration.sendMessage,
    updateCursor: collaboration.updateCursor,
    shareAnalysis: collaboration.shareAnalysis,
  };

  return (
    <CollaborationContext.Provider value={contextValue}>
      {children}
    </CollaborationContext.Provider>
  );
}

/**
 * Hook to use collaboration context
 */
export function useCollaborationContext(): CollaborationContextType {
  const context = useContext(CollaborationContext);
  if (!context) {
    throw new Error('useCollaborationContext must be used within a CollaborationProvider');
  }
  return context;
}

/**
 * Connection Status Component
 */
export function ConnectionStatus() {
  const { connectionState, isConnected, error } = useCollaborationContext();

  const getStatusColor = () => {
    switch (connectionState) {
      case ConnectionState.CONNECTED:
        return 'bg-green-500';
      case ConnectionState.CONNECTING:
      case ConnectionState.RECONNECTING:
        return 'bg-yellow-500';
      case ConnectionState.ERROR:
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getStatusText = () => {
    switch (connectionState) {
      case ConnectionState.CONNECTED:
        return 'Connected';
      case ConnectionState.CONNECTING:
        return 'Connecting...';
      case ConnectionState.RECONNECTING:
        return 'Reconnecting...';
      case ConnectionState.ERROR:
        return 'Connection Error';
      default:
        return 'Disconnected';
    }
  };

  return (
    <div className="flex items-center space-x-2">
      <div className={`w-3 h-3 rounded-full ${getStatusColor()}`} />
      <span className="text-sm font-medium">{getStatusText()}</span>
      {error && (
        <span className="text-xs text-red-600" title={error}>
          ⚠️
        </span>
      )}
    </div>
  );
}

/**
 * Participants List Component
 */
export function ParticipantsList() {
  const { participants } = useCollaborationContext();

  if (participants.length === 0) {
    return (
      <div className="text-sm text-gray-500 p-4">
        No participants in this session
      </div>
    );
  }

  return (
    <div className="space-y-2">
      <h3 className="font-medium text-sm text-gray-700">
        Participants ({participants.length})
      </h3>
      <div className="space-y-1">
        {participants.map((participant) => (
          <div
            key={participant.id}
            className="flex items-center space-x-2 p-2 rounded-lg bg-gray-50"
          >
            <div className="w-2 h-2 bg-green-500 rounded-full" />
            <span className="text-sm">
              {participant.name || participant.email}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
}

/**
 * Session Guard Component
 */
interface SessionGuardProps {
  children: ReactNode;
  sessionId: string;
  fallback?: ReactNode;
}

export function SessionGuard({ children, sessionId, fallback }: SessionGuardProps) {
  const { currentSessionId, isConnected } = useCollaborationContext();

  if (!isConnected) {
    return fallback || (
      <div className="text-center p-8 text-gray-500">
        Connecting to collaboration service...
      </div>
    );
  }

  if (currentSessionId !== sessionId) {
    return fallback || (
      <div className="text-center p-8 text-gray-500">
        Not connected to this session
      </div>
    );
  }

  return <>{children}</>;
}

/**
 * Error Boundary for Collaboration
 */
interface CollaborationErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
}

export class CollaborationErrorBoundary extends React.Component<
  { children: ReactNode },
  CollaborationErrorBoundaryState
> {
  constructor(props: { children: ReactNode }) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error): CollaborationErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Collaboration error boundary caught an error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="p-4 border border-red-200 rounded-lg bg-red-50">
          <h3 className="font-medium text-red-800">
            Collaboration Error
          </h3>
          <p className="text-sm text-red-600 mt-1">
            Something went wrong with the collaboration features.
          </p>
          {this.state.error && (
            <details className="mt-2">
              <summary className="text-xs text-red-500 cursor-pointer">
                Error details
              </summary>
              <pre className="text-xs text-red-500 mt-1 overflow-auto">
                {this.state.error.message}
              </pre>
            </details>
          )}
          <button
            onClick={() => this.setState({ hasError: false, error: null })}
            className="mt-2 px-3 py-1 text-xs bg-red-600 text-white rounded hover:bg-red-700"
          >
            Try Again
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}