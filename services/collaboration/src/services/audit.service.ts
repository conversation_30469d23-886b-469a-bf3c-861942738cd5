/**
 * Audit Service
 * 
 * Comprehensive audit logging for security events,
 * user actions, and system activities with tamper-proof storage.
 */

import { createHash } from 'crypto';
import { firestoreClient } from '../clients/firestore.client';
import { redisClient } from '../clients/redis.client';
import { logger } from '../utils/logger';
import { JWTPayload } from '../utils/jwt';

// Audit event types
export enum AuditEventType {
  // Authentication events
  LOGIN_SUCCESS = 'auth.login.success',
  LOGIN_FAILURE = 'auth.login.failure',
  LOGOUT = 'auth.logout',
  TOKEN_REFRESH = 'auth.token.refresh',
  TOKEN_REVOKE = 'auth.token.revoke',
  
  // Authorization events
  ACCESS_GRANTED = 'auth.access.granted',
  ACCESS_DENIED = 'auth.access.denied',
  PERMISSION_ELEVATED = 'auth.permission.elevated',
  
  // Team events
  TEAM_CREATED = 'team.created',
  TEAM_UPDATED = 'team.updated',
  TEAM_DELETED = 'team.deleted',
  TEAM_MEMBER_ADDED = 'team.member.added',
  TEAM_MEMBER_REMOVED = 'team.member.removed',
  TEAM_MEMBER_ROLE_CHANGED = 'team.member.role.changed',
  
  // Session events
  SESSION_CREATED = 'session.created',
  SESSION_JOINED = 'session.joined',
  SESSION_LEFT = 'session.left',
  SESSION_ENDED = 'session.ended',
  SESSION_UPDATED = 'session.updated',
  
  // Analysis events
  ANALYSIS_SHARED = 'analysis.shared',
  ANALYSIS_ACCESSED = 'analysis.accessed',
  ANALYSIS_MODIFIED = 'analysis.modified',
  ANALYSIS_DELETED = 'analysis.deleted',
  
  // Security events
  RATE_LIMIT_EXCEEDED = 'security.rate_limit.exceeded',
  INVALID_TOKEN = 'security.token.invalid',
  CSRF_VIOLATION = 'security.csrf.violation',
  SUSPICIOUS_ACTIVITY = 'security.suspicious.activity',
  
  // System events
  SYSTEM_ERROR = 'system.error',
  SYSTEM_WARNING = 'system.warning',
  SYSTEM_MAINTENANCE = 'system.maintenance',
}

// Audit event severity levels
export enum AuditSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

// Audit event interface
export interface AuditEvent {
  id: string;
  type: AuditEventType;
  severity: AuditSeverity;
  timestamp: Date;
  userId?: string;
  userEmail?: string;
  sessionId?: string;
  teamId?: string;
  resource?: string;
  action: string;
  details: Record<string, any>;
  metadata: {
    ipAddress?: string;
    userAgent?: string;
    requestId?: string;
    sourceService: string;
    version: string;
  };
  integrity: {
    hash: string;
    previousHash?: string;
  };
}

// Audit query interface
export interface AuditQuery {
  userId?: string;
  teamId?: string;
  sessionId?: string;
  type?: AuditEventType;
  severity?: AuditSeverity;
  startDate?: Date;
  endDate?: Date;
  resource?: string;
  limit?: number;
  offset?: number;
}

/**
 * Audit Service Class
 */
export class AuditService {
  private static instance: AuditService;
  private lastHash: string | null = null;
  private eventBuffer: AuditEvent[] = [];
  private flushInterval: NodeJS.Timeout | null = null;

  private constructor() {
    this.initializeService();
  }

  static getInstance(): AuditService {
    if (!AuditService.instance) {
      AuditService.instance = new AuditService();
    }
    return AuditService.instance;
  }

  /**
   * Initialize audit service
   */
  private initializeService(): void {
    // Load last hash from storage
    this.loadLastHash();
    
    // Start buffer flush interval
    this.flushInterval = setInterval(() => {
      this.flushBuffer();
    }, 10000); // Flush every 10 seconds
  }

  /**
   * Log audit event
   */
  async logEvent(
    type: AuditEventType,
    severity: AuditSeverity,
    action: string,
    details: Record<string, any>,
    context?: {
      userId?: string;
      userEmail?: string;
      sessionId?: string;
      teamId?: string;
      resource?: string;
      ipAddress?: string;
      userAgent?: string;
      requestId?: string;
    }
  ): Promise<void> {
    try {
      const event: AuditEvent = {
        id: this.generateEventId(),
        type,
        severity,
        timestamp: new Date(),
        userId: context?.userId,
        userEmail: context?.userEmail,
        sessionId: context?.sessionId,
        teamId: context?.teamId,
        resource: context?.resource,
        action,
        details,
        metadata: {
          ipAddress: context?.ipAddress,
          userAgent: context?.userAgent,
          requestId: context?.requestId,
          sourceService: 'collaboration',
          version: process.env.npm_package_version || '0.1.0',
        },
        integrity: {
          hash: '',
          previousHash: this.lastHash || undefined,
        },
      };

      // Calculate integrity hash
      event.integrity.hash = this.calculateEventHash(event);
      this.lastHash = event.integrity.hash;

      // Add to buffer
      this.eventBuffer.push(event);

      // Flush immediately for critical events
      if (severity === AuditSeverity.CRITICAL) {
        await this.flushBuffer();
      }

      logger.debug('Audit event logged', {
        eventId: event.id,
        type,
        severity,
        action,
      });
    } catch (error) {
      logger.error('Failed to log audit event', {
        error,
        type,
        severity,
        action,
      });
    }
  }

  /**
   * Log authentication event
   */
  async logAuthEvent(
    type: AuditEventType,
    userId: string,
    userEmail: string,
    success: boolean,
    details: Record<string, any>,
    context?: {
      ipAddress?: string;
      userAgent?: string;
      requestId?: string;
    }
  ): Promise<void> {
    await this.logEvent(
      type,
      success ? AuditSeverity.LOW : AuditSeverity.MEDIUM,
      success ? 'Authentication succeeded' : 'Authentication failed',
      {
        success,
        ...details,
      },
      {
        userId,
        userEmail,
        ...context,
      }
    );
  }

  /**
   * Log authorization event
   */
  async logAuthorizationEvent(
    granted: boolean,
    userId: string,
    userEmail: string,
    resource: string,
    permission: string,
    context?: {
      teamId?: string;
      sessionId?: string;
      ipAddress?: string;
      userAgent?: string;
      requestId?: string;
    }
  ): Promise<void> {
    await this.logEvent(
      granted ? AuditEventType.ACCESS_GRANTED : AuditEventType.ACCESS_DENIED,
      granted ? AuditSeverity.LOW : AuditSeverity.MEDIUM,
      granted ? 'Access granted' : 'Access denied',
      {
        granted,
        resource,
        permission,
      },
      {
        userId,
        userEmail,
        resource,
        ...context,
      }
    );
  }

  /**
   * Log security event
   */
  async logSecurityEvent(
    type: AuditEventType,
    severity: AuditSeverity,
    action: string,
    details: Record<string, any>,
    context?: {
      userId?: string;
      userEmail?: string;
      ipAddress?: string;
      userAgent?: string;
      requestId?: string;
    }
  ): Promise<void> {
    await this.logEvent(
      type,
      severity,
      action,
      details,
      context
    );
  }

  /**
   * Log team event
   */
  async logTeamEvent(
    type: AuditEventType,
    teamId: string,
    userId: string,
    userEmail: string,
    action: string,
    details: Record<string, any>,
    context?: {
      ipAddress?: string;
      userAgent?: string;
      requestId?: string;
    }
  ): Promise<void> {
    await this.logEvent(
      type,
      AuditSeverity.MEDIUM,
      action,
      details,
      {
        teamId,
        userId,
        userEmail,
        resource: `team:${teamId}`,
        ...context,
      }
    );
  }

  /**
   * Log session event
   */
  async logSessionEvent(
    type: AuditEventType,
    sessionId: string,
    userId: string,
    userEmail: string,
    action: string,
    details: Record<string, any>,
    context?: {
      teamId?: string;
      ipAddress?: string;
      userAgent?: string;
      requestId?: string;
    }
  ): Promise<void> {
    await this.logEvent(
      type,
      AuditSeverity.LOW,
      action,
      details,
      {
        sessionId,
        userId,
        userEmail,
        resource: `session:${sessionId}`,
        ...context,
      }
    );
  }

  /**
   * Query audit events
   */
  async queryEvents(query: AuditQuery): Promise<AuditEvent[]> {
    try {
      // Flush buffer to ensure latest events are included
      await this.flushBuffer();

      let firestoreQuery: any = firestoreClient.collection('audit_events');

      // Apply filters
      if (query.userId) {
        firestoreQuery = firestoreQuery.where('userId', '==', query.userId);
      }
      if (query.teamId) {
        firestoreQuery = firestoreQuery.where('teamId', '==', query.teamId);
      }
      if (query.sessionId) {
        firestoreQuery = firestoreQuery.where('sessionId', '==', query.sessionId);
      }
      if (query.type) {
        firestoreQuery = firestoreQuery.where('type', '==', query.type);
      }
      if (query.severity) {
        firestoreQuery = firestoreQuery.where('severity', '==', query.severity);
      }
      if (query.startDate) {
        firestoreQuery = firestoreQuery.where('timestamp', '>=', query.startDate);
      }
      if (query.endDate) {
        firestoreQuery = firestoreQuery.where('timestamp', '<=', query.endDate);
      }
      if (query.resource) {
        firestoreQuery = firestoreQuery.where('resource', '==', query.resource);
      }

      // Apply ordering and limits
      firestoreQuery = firestoreQuery.orderBy('timestamp', 'desc');
      
      if (query.limit) {
        firestoreQuery = firestoreQuery.limit(query.limit);
      }
      if (query.offset) {
        firestoreQuery = firestoreQuery.offset(query.offset);
      }

      const snapshot = await firestoreQuery.get();
      return snapshot.docs.map((doc: any) => doc.data() as AuditEvent);
    } catch (error) {
      logger.error('Failed to query audit events', { error, query });
      return [];
    }
  }

  /**
   * Verify event integrity
   */
  verifyEventIntegrity(event: AuditEvent): boolean {
    const calculatedHash = this.calculateEventHash(event);
    return calculatedHash === event.integrity.hash;
  }

  /**
   * Get audit statistics
   */
  async getAuditStats(
    startDate?: Date,
    endDate?: Date
  ): Promise<{
    totalEvents: number;
    eventsByType: Record<string, number>;
    eventsBySeverity: Record<string, number>;
    topUsers: Array<{ userId: string; count: number }>;
    topResources: Array<{ resource: string; count: number }>;
  }> {
    try {
      const query: AuditQuery = {
        startDate,
        endDate,
        limit: 10000, // Reasonable limit for stats
      };

      const events = await this.queryEvents(query);

      const eventsByType: Record<string, number> = {};
      const eventsBySeverity: Record<string, number> = {};
      const userCounts: Record<string, number> = {};
      const resourceCounts: Record<string, number> = {};

      for (const event of events) {
        // Count by type
        eventsByType[event.type] = (eventsByType[event.type] || 0) + 1;

        // Count by severity
        eventsBySeverity[event.severity] = (eventsBySeverity[event.severity] || 0) + 1;

        // Count by user
        if (event.userId) {
          userCounts[event.userId] = (userCounts[event.userId] || 0) + 1;
        }

        // Count by resource
        if (event.resource) {
          resourceCounts[event.resource] = (resourceCounts[event.resource] || 0) + 1;
        }
      }

      // Sort and get top users
      const topUsers = Object.entries(userCounts)
        .sort(([, a], [, b]) => b - a)
        .slice(0, 10)
        .map(([userId, count]) => ({ userId, count }));

      // Sort and get top resources
      const topResources = Object.entries(resourceCounts)
        .sort(([, a], [, b]) => b - a)
        .slice(0, 10)
        .map(([resource, count]) => ({ resource, count }));

      return {
        totalEvents: events.length,
        eventsByType,
        eventsBySeverity,
        topUsers,
        topResources,
      };
    } catch (error) {
      logger.error('Failed to get audit stats', { error });
      return {
        totalEvents: 0,
        eventsByType: {},
        eventsBySeverity: {},
        topUsers: [],
        topResources: [],
      };
    }
  }

  /**
   * Flush event buffer to storage
   */
  private async flushBuffer(): Promise<void> {
    if (this.eventBuffer.length === 0) return;

    try {
      const events = [...this.eventBuffer];
      this.eventBuffer = [];

      // Store in Firestore
      const batch = firestoreClient.batch();
      
      for (const event of events) {
        const docRef = firestoreClient.collection('audit_events').doc(event.id);
        batch.set(docRef, event);
      }

      await batch.commit();

      // Store in Redis for quick access
      for (const event of events) {
        await redisClient.set(
          `audit:${event.id}`,
          JSON.stringify(event),
          24 * 60 * 60 // 24 hours TTL
        );
      }

      // Update last hash
      if (events.length > 0) {
        await this.saveLastHash();
      }

      logger.debug('Audit buffer flushed', {
        eventCount: events.length,
      });
    } catch (error) {
      logger.error('Failed to flush audit buffer', { error });
      // Re-add events to buffer for retry
      this.eventBuffer.unshift(...this.eventBuffer);
    }
  }

  /**
   * Calculate event hash for integrity
   */
  private calculateEventHash(event: AuditEvent): string {
    const hashData = {
      id: event.id,
      type: event.type,
      severity: event.severity,
      timestamp: event.timestamp.toISOString(),
      userId: event.userId,
      userEmail: event.userEmail,
      sessionId: event.sessionId,
      teamId: event.teamId,
      resource: event.resource,
      action: event.action,
      details: event.details,
      metadata: event.metadata,
      previousHash: event.integrity.previousHash,
    };

    return createHash('sha256')
      .update(JSON.stringify(hashData))
      .digest('hex');
  }

  /**
   * Generate unique event ID
   */
  private generateEventId(): string {
    return `audit_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
  }

  /**
   * Load last hash from storage
   */
  private async loadLastHash(): Promise<void> {
    try {
      this.lastHash = await redisClient.get('audit:last_hash');
    } catch (error) {
      logger.error('Failed to load last hash', { error });
    }
  }

  /**
   * Save last hash to storage
   */
  private async saveLastHash(): Promise<void> {
    try {
      if (this.lastHash) {
        await redisClient.set('audit:last_hash', this.lastHash);
      }
    } catch (error) {
      logger.error('Failed to save last hash', { error });
    }
  }

  /**
   * Cleanup old events
   */
  async cleanup(olderThanDays: number = 90): Promise<void> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

      const query = firestoreClient
        .collection('audit_events')
        .where('timestamp', '<', cutoffDate);

      const snapshot = await query.get();
      const batch = firestoreClient.batch();

      snapshot.docs.forEach(doc => {
        batch.delete(doc.ref);
      });

      await batch.commit();

      logger.info('Audit cleanup completed', {
        deletedEvents: snapshot.docs.length,
        olderThanDays,
      });
    } catch (error) {
      logger.error('Failed to cleanup audit events', { error });
    }
  }

  /**
   * Shutdown service
   */
  shutdown(): void {
    if (this.flushInterval) {
      clearInterval(this.flushInterval);
      this.flushInterval = null;
    }
    
    // Final flush
    this.flushBuffer();
  }
}

// Export singleton instance
export const auditService = AuditService.getInstance();

// Convenience functions
export const logAuthEvent = auditService.logAuthEvent.bind(auditService);
export const logAuthorizationEvent = auditService.logAuthorizationEvent.bind(auditService);
export const logSecurityEvent = auditService.logSecurityEvent.bind(auditService);
export const logTeamEvent = auditService.logTeamEvent.bind(auditService);
export const logSessionEvent = auditService.logSessionEvent.bind(auditService);