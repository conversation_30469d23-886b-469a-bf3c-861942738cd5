#!/usr/bin/env node

/**
 * Collaboration Service Entry Point
 * 
 * Production-ready TypeScript real-time collaboration service
 * for the CCL platform enabling shared code analysis sessions,
 * team pattern development, and collaborative workspaces.
 */

import 'dotenv/config';
import { createServer } from 'http';
import { AddressInfo } from 'net';
import { app } from './app';
import { initializeSocketIO } from './websocket';
import { logger } from './utils/logger';
import { initializeClients } from './clients';
import { gracefulShutdown } from './utils/graceful-shutdown';

/**
 * Main application bootstrap
 */
async function bootstrap(): Promise<void> {
  try {
    // Initialize external clients
    await initializeClients();
    logger.info('External clients initialized successfully');

    // Create HTTP server
    const server = createServer(app);

    // Initialize WebSocket server
    const io = await initializeSocketIO(server);
    logger.info('WebSocket server initialized');

    // Start server
    const PORT = process.env.PORT || 8005;
    server.listen(PORT, () => {
      const address = server.address() as AddressInfo;
      logger.info(`Collaboration service listening on port ${address.port}`, {
        port: address.port,
        environment: process.env.NODE_ENV || 'development',
        version: process.env.npm_package_version || '0.1.0'
      });
    });

    // Setup graceful shutdown
    process.on('SIGTERM', () => gracefulShutdown(server, io));
    process.on('SIGINT', () => gracefulShutdown(server, io));

  } catch (error) {
    logger.error('Failed to start collaboration service', { error });
    process.exit(1);
  }
}

// Handle uncaught errors
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception', { error });
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at Promise', { reason, promise });
  process.exit(1);
});

// Start the application
bootstrap().catch((error) => {
  logger.error('Bootstrap failed', { error });
  process.exit(1);
});