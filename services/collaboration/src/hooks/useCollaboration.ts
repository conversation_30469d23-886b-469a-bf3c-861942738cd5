/**
 * React Hooks for Collaboration Features
 * 
 * Custom hooks for integrating real-time collaboration
 * into React applications.
 */

import { useCallback, useEffect, useRef, useState } from 'react';
import { CollaborationClient, ConnectionState, EventHandlers } from '../clients/websocket-client';

// Types
export interface User {
  id: string;
  email: string;
  name?: string;
}

export interface Message {
  id: string;
  userId: string;
  userEmail: string;
  userName?: string;
  message: string;
  type: 'text' | 'system' | 'analysis_share';
  timestamp: string;
  metadata?: Record<string, any>;
}

export interface CursorPosition {
  userId: string;
  userEmail: string;
  userName?: string;
  position: {
    x: number;
    y: number;
    elementId?: string;
  };
  timestamp: string;
}

export interface CollaborationState {
  connectionState: ConnectionState;
  participants: User[];
  messages: Message[];
  cursors: Record<string, CursorPosition>;
  isConnected: boolean;
  error: string | null;
}

/**
 * Main collaboration hook
 */
export function useCollaboration(serverUrl: string, token: string) {
  const [state, setState] = useState<CollaborationState>({
    connectionState: ConnectionState.DISCONNECTED,
    participants: [],
    messages: [],
    cursors: {},
    isConnected: false,
    error: null,
  });

  const clientRef = useRef<CollaborationClient | null>(null);

  // Initialize client
  useEffect(() => {
    if (!serverUrl || !token) return;

    const client = new CollaborationClient({
      url: serverUrl,
      token,
      autoReconnect: true,
      maxReconnectAttempts: 5,
    });

    const handlers: EventHandlers = {
      onConnect: () => {
        setState(prev => ({
          ...prev,
          isConnected: true,
          error: null,
        }));
      },

      onDisconnect: (reason) => {
        setState(prev => ({
          ...prev,
          isConnected: false,
          participants: [],
          cursors: {},
          error: `Disconnected: ${reason}`,
        }));
      },

      onError: (error) => {
        setState(prev => ({
          ...prev,
          error: error.message,
        }));
      },

      onConnectionStateChange: (connectionState) => {
        setState(prev => ({
          ...prev,
          connectionState,
        }));
      },

      onUserJoined: (event) => {
        setState(prev => ({
          ...prev,
          participants: [
            ...prev.participants.filter(p => p.id !== event.userId),
            {
              id: event.userId,
              email: event.userEmail,
              name: event.userName,
            },
          ],
        }));
      },

      onUserLeft: (event) => {
        setState(prev => ({
          ...prev,
          participants: prev.participants.filter(p => p.id !== event.userId),
          cursors: Object.fromEntries(
            Object.entries(prev.cursors).filter(([userId]) => userId !== event.userId)
          ),
        }));
      },

      onMessageReceived: (event) => {
        setState(prev => ({
          ...prev,
          messages: [...prev.messages, event],
        }));
      },

      onCursorUpdate: (event) => {
        setState(prev => ({
          ...prev,
          cursors: {
            ...prev.cursors,
            [event.userId]: event,
          },
        }));
      },
    };

    client.on(handlers);
    clientRef.current = client;

    return () => {
      client.disconnect();
      clientRef.current = null;
    };
  }, [serverUrl, token]);

  // Connect to server
  const connect = useCallback(async () => {
    if (!clientRef.current) return;
    
    try {
      await clientRef.current.connect();
    } catch (error) {
      console.error('Failed to connect:', error);
    }
  }, []);

  // Disconnect from server
  const disconnect = useCallback(() => {
    if (!clientRef.current) return;
    clientRef.current.disconnect();
  }, []);

  // Join session
  const joinSession = useCallback(async (sessionId: string) => {
    if (!clientRef.current) return;
    
    try {
      await clientRef.current.joinSession(sessionId);
    } catch (error) {
      console.error('Failed to join session:', error);
    }
  }, []);

  // Leave session
  const leaveSession = useCallback(async (sessionId: string) => {
    if (!clientRef.current) return;
    
    try {
      await clientRef.current.leaveSession(sessionId);
    } catch (error) {
      console.error('Failed to leave session:', error);
    }
  }, []);

  // Send message
  const sendMessage = useCallback((sessionId: string, message: string) => {
    if (!clientRef.current) return;
    
    try {
      clientRef.current.sendMessage(sessionId, message);
    } catch (error) {
      console.error('Failed to send message:', error);
    }
  }, []);

  // Update cursor position
  const updateCursor = useCallback((sessionId: string, position: { x: number; y: number; elementId?: string }) => {
    if (!clientRef.current) return;
    
    clientRef.current.updateCursor(sessionId, position);
  }, []);

  // Share analysis
  const shareAnalysis = useCallback((sessionId: string, analysis: any) => {
    if (!clientRef.current) return;
    
    try {
      clientRef.current.shareAnalysis(sessionId, analysis);
    } catch (error) {
      console.error('Failed to share analysis:', error);
    }
  }, []);

  return {
    ...state,
    connect,
    disconnect,
    joinSession,
    leaveSession,
    sendMessage,
    updateCursor,
    shareAnalysis,
  };
}

/**
 * Hook for managing cursor tracking
 */
export function useCursorTracking(sessionId: string, updateCursor: (sessionId: string, position: any) => void) {
  const [isTracking, setIsTracking] = useState(false);
  const throttleRef = useRef<NodeJS.Timeout | null>(null);

  const startTracking = useCallback(() => {
    setIsTracking(true);
    
    const handleMouseMove = (event: MouseEvent) => {
      if (throttleRef.current) return;
      
      throttleRef.current = setTimeout(() => {
        updateCursor(sessionId, {
          x: event.clientX,
          y: event.clientY,
          elementId: (event.target as HTMLElement)?.id,
        });
        throttleRef.current = null;
      }, 100); // Throttle to 10 FPS
    };

    document.addEventListener('mousemove', handleMouseMove);
    
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      if (throttleRef.current) {
        clearTimeout(throttleRef.current);
        throttleRef.current = null;
      }
    };
  }, [sessionId, updateCursor]);

  const stopTracking = useCallback(() => {
    setIsTracking(false);
  }, []);

  return {
    isTracking,
    startTracking,
    stopTracking,
  };
}

/**
 * Hook for managing session messages
 */
export function useSessionMessages(initialMessages: Message[] = []) {
  const [messages, setMessages] = useState<Message[]>(initialMessages);
  const [isLoading, setIsLoading] = useState(false);

  const addMessage = useCallback((message: Message) => {
    setMessages(prev => [...prev, message]);
  }, []);

  const clearMessages = useCallback(() => {
    setMessages([]);
  }, []);

  const loadMoreMessages = useCallback(async (sessionId: string, offset: number = 0) => {
    setIsLoading(true);
    
    try {
      // This would typically fetch from an API
      const response = await fetch(`/api/sessions/${sessionId}/messages?offset=${offset}&limit=50`);
      const data = await response.json();
      
      if (data.success) {
        setMessages(prev => [...data.data, ...prev]);
      }
    } catch (error) {
      console.error('Failed to load messages:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    messages,
    isLoading,
    addMessage,
    clearMessages,
    loadMoreMessages,
  };
}

/**
 * Hook for managing presence/participants
 */
export function usePresence(participants: User[] = []) {
  const [onlineUsers, setOnlineUsers] = useState<User[]>([]);
  const [userColors, setUserColors] = useState<Record<string, string>>({});

  // Generate consistent colors for users
  const generateUserColor = useCallback((userId: string): string => {
    const colors = [
      '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
      '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9',
    ];
    
    let hash = 0;
    for (let i = 0; i < userId.length; i++) {
      hash = userId.charCodeAt(i) + ((hash << 5) - hash);
    }
    
    return colors[Math.abs(hash) % colors.length];
  }, []);

  // Update online users and assign colors
  useEffect(() => {
    setOnlineUsers(participants);
    
    const newColors = { ...userColors };
    participants.forEach(user => {
      if (!newColors[user.id]) {
        newColors[user.id] = generateUserColor(user.id);
      }
    });
    
    setUserColors(newColors);
  }, [participants, userColors, generateUserColor]);

  const getUserColor = useCallback((userId: string): string => {
    return userColors[userId] || generateUserColor(userId);
  }, [userColors, generateUserColor]);

  return {
    onlineUsers,
    userColors,
    getUserColor,
  };
}

/**
 * Hook for managing typing indicators
 */
export function useTypingIndicator(sessionId: string) {
  const [typingUsers, setTypingUsers] = useState<Set<string>>(new Set());
  const [isTyping, setIsTyping] = useState(false);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const startTyping = useCallback(() => {
    if (!isTyping) {
      setIsTyping(true);
      // Emit typing start event
    }
    
    // Reset timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }
    
    typingTimeoutRef.current = setTimeout(() => {
      setIsTyping(false);
      // Emit typing stop event
    }, 3000);
  }, [isTyping]);

  const stopTyping = useCallback(() => {
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
      typingTimeoutRef.current = null;
    }
    
    setIsTyping(false);
    // Emit typing stop event
  }, []);

  const addTypingUser = useCallback((userId: string) => {
    setTypingUsers(prev => new Set(prev).add(userId));
  }, []);

  const removeTypingUser = useCallback((userId: string) => {
    setTypingUsers(prev => {
      const newSet = new Set(prev);
      newSet.delete(userId);
      return newSet;
    });
  }, []);

  return {
    typingUsers: Array.from(typingUsers),
    isTyping,
    startTyping,
    stopTyping,
    addTypingUser,
    removeTypingUser,
  };
}