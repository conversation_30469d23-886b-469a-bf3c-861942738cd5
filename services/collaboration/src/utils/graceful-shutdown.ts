/**
 * Graceful Shutdown Handler
 * 
 * Handles graceful shutdown of the collaboration service,
 * ensuring all connections are properly closed and resources
 * are cleaned up before termination.
 */

import { Server } from 'http';
import { Server as SocketIOServer } from 'socket.io';
import { logger } from './logger';
import { closeClients } from '../clients';

let shutdownInProgress = false;

/**
 * Graceful shutdown handler
 */
export const gracefulShutdown = async (
  server: Server, 
  io: SocketIOServer
): Promise<void> => {
  if (shutdownInProgress) {
    logger.warn('Shutdown already in progress, ignoring signal');
    return;
  }

  shutdownInProgress = true;
  logger.info('Graceful shutdown initiated');

  const shutdownTimeout = setTimeout(() => {
    logger.error('Graceful shutdown timeout reached, forcing exit');
    process.exit(1);
  }, 30000); // 30 second timeout

  try {
    // 1. Stop accepting new connections
    logger.info('Stopping server from accepting new connections');
    server.close((err) => {
      if (err) {
        logger.error('Error closing HTTP server', { error: err });
      } else {
        logger.info('HTTP server closed successfully');
      }
    });

    // 2. Close all Socket.IO connections
    logger.info('Closing all WebSocket connections');
    await closeSocketIOConnections(io);

    // 3. Close external client connections
    logger.info('Closing external client connections');
    await closeClients();

    // 4. Clear the timeout
    clearTimeout(shutdownTimeout);

    logger.info('Graceful shutdown completed successfully');
    process.exit(0);

  } catch (error) {
    logger.error('Error during graceful shutdown', { error });
    clearTimeout(shutdownTimeout);
    process.exit(1);
  }
};

/**
 * Close all Socket.IO connections gracefully
 */
async function closeSocketIOConnections(io: SocketIOServer): Promise<void> {
  return new Promise((resolve) => {
    const sockets = io.sockets.sockets;
    const socketCount = sockets.size;

    if (socketCount === 0) {
      logger.info('No active WebSocket connections to close');
      resolve();
      return;
    }

    logger.info(`Closing ${socketCount} WebSocket connections`);

    let closedCount = 0;
    const closeTimeout = setTimeout(() => {
      logger.warn(`Timeout waiting for ${socketCount - closedCount} connections to close`);
      resolve();
    }, 10000); // 10 second timeout for socket closure

    sockets.forEach((socket) => {
      socket.emit('server_shutdown', { 
        message: 'Server is shutting down',
        timestamp: new Date().toISOString()
      });
      
      socket.on('disconnect', () => {
        closedCount++;
        if (closedCount === socketCount) {
          clearTimeout(closeTimeout);
          logger.info('All WebSocket connections closed');
          resolve();
        }
      });

      // Force disconnect after notification
      setTimeout(() => {
        socket.disconnect(true);
      }, 1000);
    });
  });
}

/**
 * Setup process signal handlers
 */
export const setupSignalHandlers = (server: Server, io: SocketIOServer): void => {
  process.on('SIGTERM', () => {
    logger.info('SIGTERM received, initiating graceful shutdown');
    gracefulShutdown(server, io);
  });

  process.on('SIGINT', () => {
    logger.info('SIGINT received, initiating graceful shutdown');
    gracefulShutdown(server, io);
  });

  process.on('SIGUSR2', () => {
    logger.info('SIGUSR2 received, initiating graceful shutdown');
    gracefulShutdown(server, io);
  });
};