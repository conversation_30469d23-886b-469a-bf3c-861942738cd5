/**
 * JWT Utilities
 * 
 * Comprehensive JWT token management with refresh tokens,
 * role-based access control, and security best practices.
 */

import jwt from 'jsonwebtoken';
import crypto from 'crypto';
import { appConfig } from '../config';
import { logger } from './logger';

// Token types
export interface JWTPayload {
  userId: string;
  email: string;
  name?: string;
  role: string;
  permissions: string[];
  teamIds: string[];
  sessionId?: string;
  tokenType: 'access' | 'refresh';
  iat?: number;
  exp?: number;
}

export interface TokenPair {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  tokenType: 'Bearer';
}

export interface RefreshTokenData {
  userId: string;
  email: string;
  tokenFamily: string;
  version: number;
  lastUsed: Date;
  deviceId?: string;
  ipAddress?: string;
}

// Security configuration
const ACCESS_TOKEN_EXPIRY = '15m';
const REFRESH_TOKEN_EXPIRY = '7d';
const TOKEN_ISSUER = 'collaboration-service';
const TOKEN_AUDIENCE = 'ccl-platform';

// Error classes
export class TokenError extends Error {
  constructor(message: string, public code: string) {
    super(message);
    this.name = 'TokenError';
  }
}

export class TokenExpiredError extends TokenError {
  constructor() {
    super('Token has expired', 'TOKEN_EXPIRED');
  }
}

export class TokenInvalidError extends TokenError {
  constructor() {
    super('Token is invalid', 'TOKEN_INVALID');
  }
}

/**
 * JWT Token Manager
 */
export class JWTManager {
  private static instance: JWTManager;
  private refreshTokenStore = new Map<string, RefreshTokenData>();
  private blacklistedTokens = new Set<string>();

  private constructor() {}

  static getInstance(): JWTManager {
    if (!JWTManager.instance) {
      JWTManager.instance = new JWTManager();
    }
    return JWTManager.instance;
  }

  /**
   * Generate access token
   */
  generateAccessToken(payload: Omit<JWTPayload, 'tokenType' | 'iat' | 'exp'>): string {
    const tokenPayload: JWTPayload = {
      ...payload,
      tokenType: 'access',
    };

    return jwt.sign(tokenPayload, appConfig.auth.jwtSecret, {
      expiresIn: ACCESS_TOKEN_EXPIRY,
      issuer: TOKEN_ISSUER,
      audience: TOKEN_AUDIENCE,
      algorithm: 'HS256',
    });
  }

  /**
   * Generate refresh token
   */
  generateRefreshToken(userId: string, email: string, deviceId?: string, ipAddress?: string): string {
    const tokenFamily = this.generateTokenFamily();
    const tokenPayload: Partial<JWTPayload> = {
      userId,
      email,
      tokenType: 'refresh',
    };

    const refreshToken = jwt.sign(tokenPayload, appConfig.auth.jwtSecret, {
      expiresIn: REFRESH_TOKEN_EXPIRY,
      issuer: TOKEN_ISSUER,
      audience: TOKEN_AUDIENCE,
      algorithm: 'HS256',
      jwtid: tokenFamily,
    });

    // Store refresh token data
    this.refreshTokenStore.set(tokenFamily, {
      userId,
      email,
      tokenFamily,
      version: 1,
      lastUsed: new Date(),
      deviceId,
      ipAddress,
    });

    return refreshToken;
  }

  /**
   * Generate complete token pair
   */
  generateTokenPair(
    userId: string,
    email: string,
    name: string,
    role: string,
    permissions: string[],
    teamIds: string[],
    deviceId?: string,
    ipAddress?: string
  ): TokenPair {
    const accessToken = this.generateAccessToken({
      userId,
      email,
      name,
      role,
      permissions,
      teamIds,
    });

    const refreshToken = this.generateRefreshToken(userId, email, deviceId, ipAddress);

    return {
      accessToken,
      refreshToken,
      expiresIn: 900, // 15 minutes in seconds
      tokenType: 'Bearer',
    };
  }

  /**
   * Verify and decode token
   */
  verifyToken(token: string): JWTPayload {
    try {
      // Check if token is blacklisted
      if (this.blacklistedTokens.has(token)) {
        throw new TokenInvalidError();
      }

      const decoded = jwt.verify(token, appConfig.auth.jwtSecret, {
        issuer: TOKEN_ISSUER,
        audience: TOKEN_AUDIENCE,
        algorithms: ['HS256'],
      }) as JWTPayload;

      return decoded;
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        throw new TokenExpiredError();
      } else if (error instanceof jwt.JsonWebTokenError) {
        throw new TokenInvalidError();
      } else {
        throw error;
      }
    }
  }

  /**
   * Refresh access token
   */
  async refreshAccessToken(refreshToken: string): Promise<TokenPair> {
    try {
      const decoded = jwt.verify(refreshToken, appConfig.auth.jwtSecret, {
        issuer: TOKEN_ISSUER,
        audience: TOKEN_AUDIENCE,
        algorithms: ['HS256'],
      }) as JWTPayload & { jti: string };

      const tokenFamily = decoded.jti;
      const refreshTokenData = this.refreshTokenStore.get(tokenFamily);

      if (!refreshTokenData) {
        throw new TokenInvalidError();
      }

      // Check if refresh token is still valid
      if (refreshTokenData.userId !== decoded.userId) {
        throw new TokenInvalidError();
      }

      // Update last used time
      refreshTokenData.lastUsed = new Date();
      refreshTokenData.version++;

      // Get user data (this would typically come from database)
      const userData = await this.getUserData(decoded.userId);
      
      if (!userData) {
        throw new TokenInvalidError();
      }

      // Generate new token pair
      return this.generateTokenPair(
        userData.userId,
        userData.email,
        userData.name,
        userData.role,
        userData.permissions,
        userData.teamIds,
        refreshTokenData.deviceId,
        refreshTokenData.ipAddress
      );
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        throw new TokenExpiredError();
      } else if (error instanceof jwt.JsonWebTokenError) {
        throw new TokenInvalidError();
      } else {
        throw error;
      }
    }
  }

  /**
   * Blacklist token
   */
  blacklistToken(token: string): void {
    this.blacklistedTokens.add(token);
    
    // Log security event
    logger.warn('Token blacklisted', {
      tokenHash: this.hashToken(token),
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Revoke refresh token
   */
  revokeRefreshToken(tokenFamily: string): void {
    this.refreshTokenStore.delete(tokenFamily);
    
    logger.info('Refresh token revoked', {
      tokenFamily,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Revoke all user tokens
   */
  revokeAllUserTokens(userId: string): void {
    for (const [tokenFamily, data] of this.refreshTokenStore.entries()) {
      if (data.userId === userId) {
        this.refreshTokenStore.delete(tokenFamily);
      }
    }
    
    logger.info('All user tokens revoked', {
      userId,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Check if user has permission
   */
  hasPermission(payload: JWTPayload, permission: string): boolean {
    return payload.permissions.includes(permission) || payload.role === 'admin';
  }

  /**
   * Check if user is team member
   */
  isTeamMember(payload: JWTPayload, teamId: string): boolean {
    return payload.teamIds.includes(teamId) || payload.role === 'admin';
  }

  /**
   * Check if user has team role
   */
  hasTeamRole(payload: JWTPayload, teamId: string, requiredRole: string): boolean {
    // This would typically check team-specific roles from database
    return this.isTeamMember(payload, teamId) && 
           (payload.role === requiredRole || payload.role === 'admin');
  }

  /**
   * Generate token family ID
   */
  private generateTokenFamily(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * Hash token for logging
   */
  private hashToken(token: string): string {
    return crypto.createHash('sha256').update(token).digest('hex').substring(0, 16);
  }

  /**
   * Get user data (mock implementation)
   */
  private async getUserData(userId: string): Promise<{
    userId: string;
    email: string;
    name: string;
    role: string;
    permissions: string[];
    teamIds: string[];
  } | null> {
    // This would typically fetch from database
    // For now, return mock data
    return {
      userId,
      email: '<EMAIL>',
      name: 'User Name',
      role: 'member',
      permissions: ['read', 'write'],
      teamIds: ['team1', 'team2'],
    };
  }

  /**
   * Clean up expired tokens
   */
  cleanup(): void {
    const now = new Date();
    const maxAge = 7 * 24 * 60 * 60 * 1000; // 7 days

    for (const [tokenFamily, data] of this.refreshTokenStore.entries()) {
      if (now.getTime() - data.lastUsed.getTime() > maxAge) {
        this.refreshTokenStore.delete(tokenFamily);
      }
    }

    logger.debug('Token cleanup completed', {
      remainingTokens: this.refreshTokenStore.size,
      blacklistedTokens: this.blacklistedTokens.size,
    });
  }
}

// Export singleton instance
export const jwtManager = JWTManager.getInstance();

// Permissions enum
export enum Permission {
  READ = 'read',
  WRITE = 'write',
  DELETE = 'delete',
  ADMIN = 'admin',
  TEAM_CREATE = 'team:create',
  TEAM_MANAGE = 'team:manage',
  TEAM_DELETE = 'team:delete',
  SESSION_CREATE = 'session:create',
  SESSION_MANAGE = 'session:manage',
  SESSION_DELETE = 'session:delete',
  ANALYSIS_SHARE = 'analysis:share',
  ANALYSIS_MANAGE = 'analysis:manage',
}

// Roles enum
export enum Role {
  ADMIN = 'admin',
  MEMBER = 'member',
  VIEWER = 'viewer',
}

// Team roles enum
export enum TeamRole {
  OWNER = 'owner',
  ADMIN = 'admin',
  MEMBER = 'member',
  VIEWER = 'viewer',
}

// Helper functions
export const hasPermission = (payload: JWTPayload, permission: string): boolean => {
  return jwtManager.hasPermission(payload, permission);
};

export const isTeamMember = (payload: JWTPayload, teamId: string): boolean => {
  return jwtManager.isTeamMember(payload, teamId);
};

export const hasTeamRole = (payload: JWTPayload, teamId: string, role: string): boolean => {
  return jwtManager.hasTeamRole(payload, teamId, role);
};