/**
 * Structured Logger Configuration
 * 
 * Winston-based logger with structured JSON output
 * for production environments and readable console
 * output for development.
 */

import winston from 'winston';
import { appConfig } from '../config';

// Define log format for different environments
const developmentFormat = winston.format.combine(
  winston.format.timestamp(),
  winston.format.colorize(),
  winston.format.printf(({ timestamp, level, message, service, ...meta }) => {
    const metaStr = Object.keys(meta).length ? JSON.stringify(meta, null, 2) : '';
    return `${timestamp} [${level}] ${service || 'collaboration'}: ${message} ${metaStr}`;
  })
);

const productionFormat = winston.format.combine(
  winston.format.timestamp(),
  winston.format.errors({ stack: true }),
  winston.format.json()
);

// Create logger instance
export const logger = winston.createLogger({
  level: appConfig.server.logLevel,
  format: appConfig.server.environment === 'production' ? productionFormat : developmentFormat,
  defaultMeta: {
    service: 'collaboration',
    version: process.env.npm_package_version || '0.1.0',
    environment: appConfig.server.environment,
  },
  transports: [
    new winston.transports.Console({
      handleExceptions: true,
      handleRejections: true,
    }),
  ],
});

// Add file transport for production
if (appConfig.server.environment === 'production') {
  logger.add(new winston.transports.File({
    filename: 'logs/error.log',
    level: 'error',
    maxsize: 5242880, // 5MB
    maxFiles: 5,
  }));
  
  logger.add(new winston.transports.File({
    filename: 'logs/combined.log',
    maxsize: 5242880, // 5MB
    maxFiles: 5,
  }));
}

// Export logger with typed methods
export interface Logger {
  error(message: string, meta?: Record<string, unknown>): void;
  warn(message: string, meta?: Record<string, unknown>): void;
  info(message: string, meta?: Record<string, unknown>): void;
  debug(message: string, meta?: Record<string, unknown>): void;
}

// Create structured logging methods
export const createLogger = (context: string): Logger => {
  return {
    error: (message: string, meta?: Record<string, unknown>) => 
      logger.error(message, { context, ...meta }),
    warn: (message: string, meta?: Record<string, unknown>) => 
      logger.warn(message, { context, ...meta }),
    info: (message: string, meta?: Record<string, unknown>) => 
      logger.info(message, { context, ...meta }),
    debug: (message: string, meta?: Record<string, unknown>) => 
      logger.debug(message, { context, ...meta }),
  };
};