/**
 * Redis Configuration
 * 
 * Advanced Redis configuration with cluster support, failover,
 * connection pooling, and performance optimization.
 */

import { RedisOptions, ClusterOptions } from 'ioredis';
import { appConfig } from './index';

// Redis connection pool configuration
export const REDIS_POOL_CONFIG = {
  min: 5,
  max: 50,
  acquireTimeoutMillis: 30000,
  createTimeoutMillis: 30000,
  destroyTimeoutMillis: 5000,
  idleTimeoutMillis: 30000,
  reapIntervalMillis: 1000,
  createRetryIntervalMillis: 200,
  propagateCreateError: false,
};

// Redis client configuration
export const REDIS_CONFIG: RedisOptions = {
  host: appConfig.redis.host,
  port: appConfig.redis.port,
  password: appConfig.redis.password,
  db: appConfig.redis.database,
  
  // Connection settings
  connectTimeout: 10000,
  commandTimeout: 5000,
  lazyConnect: true,
  keepAlive: 30000,
  
  // Retry configuration
  maxRetriesPerRequest: 3,
  
  // Performance optimization
  enableAutoPipelining: true,
  
  // Cluster-specific settings
  enableOfflineQueue: false,
  
  // Connection pool
  family: 4,
  
  // TLS configuration (if needed)
  tls: appConfig.redis.tls ? {
    rejectUnauthorized: false,
  } : undefined,
};

// Redis Cluster nodes
export const REDIS_CLUSTER_NODES = appConfig.redis.cluster?.nodes || [
  { host: appConfig.redis.host, port: appConfig.redis.port },
];

// Redis Cluster configuration
export const REDIS_CLUSTER_CONFIG: ClusterOptions = {
  // Cluster options
  enableOfflineQueue: false,
  redisOptions: {
    password: appConfig.redis.password,
    connectTimeout: 10000,
    commandTimeout: 5000,
    lazyConnect: true,
    keepAlive: 30000,
    family: 4,
    tls: appConfig.redis.tls ? {
      rejectUnauthorized: false,
    } : undefined,
  },
  
  // Cluster-specific settings
  retryDelayOnFailover: 100,
  retryDelayOnClusterDown: 300,
  enableReadyCheck: true,
  maxRedirections: 16,
  
  // Performance optimization
  enableAutoPipelining: true,
  
  // Scaling configuration
  scaleReads: 'slave',
  
  // Connection pool per node
  lazyConnect: true,
  
  // Cluster discovery
  slotsRefreshTimeout: 2000,
  slotsRefreshInterval: 5000,
  
};

// Cache configuration strategies
export const CACHE_STRATEGIES = {
  // Short-term cache (5 minutes)
  short: {
    ttl: 300,
    prefix: 'short:',
    maxKeys: 10000,
  },
  
  // Medium-term cache (30 minutes)
  medium: {
    ttl: 1800,
    prefix: 'medium:',
    maxKeys: 5000,
  },
  
  // Long-term cache (4 hours)
  long: {
    ttl: 14400,
    prefix: 'long:',
    maxKeys: 1000,
  },
  
  // Session cache (24 hours)
  session: {
    ttl: 86400,
    prefix: 'session:',
    maxKeys: 50000,
  },
  
  // User cache (1 hour)
  user: {
    ttl: 3600,
    prefix: 'user:',
    maxKeys: 20000,
  },
  
  // Team cache (2 hours)
  team: {
    ttl: 7200,
    prefix: 'team:',
    maxKeys: 5000,
  },
  
  // Analysis cache (6 hours)
  analysis: {
    ttl: 21600,
    prefix: 'analysis:',
    maxKeys: 2000,
  },
  
  // Rate limiting cache (15 minutes)
  rateLimit: {
    ttl: 900,
    prefix: 'rate:',
    maxKeys: 100000,
  },
  
  // WebSocket cache (1 minute)
  websocket: {
    ttl: 60,
    prefix: 'ws:',
    maxKeys: 50000,
  },
  
  // Metrics cache (5 minutes)
  metrics: {
    ttl: 300,
    prefix: 'metrics:',
    maxKeys: 10000,
  },
};

// Redis Streams configuration for WebSocket scaling
export const REDIS_STREAMS_CONFIG = {
  // Stream names
  streams: {
    collaboration: 'collaboration:events',
    notifications: 'notifications:events',
    analytics: 'analytics:events',
    system: 'system:events',
  },
  
  // Consumer groups
  consumerGroups: {
    collaboration: 'collaboration-service',
    notifications: 'notification-service',
    analytics: 'analytics-service',
    system: 'system-service',
  },
  
  // Stream configuration
  maxLength: 10000,
  trimStrategy: 'MAXLEN',
  
  // Consumer configuration
  blockTime: 1000,
  count: 10,
  
  // Acknowledgment timeout
  ackTimeout: 30000,
  
  // Retry configuration
  maxRetries: 3,
  retryDelay: 1000,
};

// Redis Pub/Sub configuration
export const REDIS_PUBSUB_CONFIG = {
  // Channel patterns
  channels: {
    teamEvents: 'team:*',
    sessionEvents: 'session:*',
    userEvents: 'user:*',
    systemEvents: 'system:*',
    analyticsEvents: 'analytics:*',
  },
  
  // Message configuration
  maxMessageSize: 1024 * 1024, // 1MB
  messageEncoding: 'utf8',
  
  // Subscription configuration
  subscriptionTimeout: 5000,
  reconnectDelay: 1000,
  
  // Pattern subscription
  patternSubscription: true,
};

// Memory optimization configuration
export const MEMORY_CONFIG = {
  // Memory usage limits
  maxMemoryUsage: 0.8, // 80% of available memory
  memoryCheckInterval: 30000, // 30 seconds
  
  // Key eviction policies
  evictionPolicies: {
    default: 'allkeys-lru',
    sessions: 'volatile-ttl',
    cache: 'allkeys-lfu',
    rateLimit: 'allkeys-lru',
  },
  
  // Compression configuration
  compression: {
    enabled: true,
    threshold: 1024, // Compress values larger than 1KB
    algorithm: 'gzip',
  },
  
  // Lazy expiration
  lazyExpiration: true,
  
  // Memory optimization
  hashMaxZiplistEntries: 512,
  hashMaxZiplistValue: 64,
  listMaxZiplistSize: -2,
  listCompressDepth: 0,
  setMaxIntsetEntries: 512,
  zsetMaxZiplistEntries: 128,
  zsetMaxZiplistValue: 64,
};

// Monitoring configuration
export const MONITORING_CONFIG = {
  // Metrics collection
  metricsEnabled: true,
  metricsInterval: 30000, // 30 seconds
  
  // Performance monitoring
  slowQueryThreshold: 100, // 100ms
  slowQueryLogging: true,
  
  // Connection monitoring
  connectionPoolMonitoring: true,
  connectionHealthChecks: true,
  healthCheckInterval: 10000, // 10 seconds
  
  // Latency monitoring
  latencyMonitoring: true,
  latencyHistogram: true,
  
  // Command monitoring
  commandMonitoring: true,
  commandStats: true,
  
  // Memory monitoring
  memoryMonitoring: true,
  memoryAlerts: true,
  memoryThreshold: 0.85, // 85% memory usage alert
  
  // Error monitoring
  errorTracking: true,
  errorRateThreshold: 0.05, // 5% error rate alert
  
  // Throughput monitoring
  throughputMonitoring: true,
  throughputAlerts: true,
  
  // Availability monitoring
  availabilityMonitoring: true,
  availabilityTarget: 0.999, // 99.9% availability
};

// Backup and recovery configuration
export const BACKUP_CONFIG = {
  // Automated backups
  autoBackup: true,
  backupInterval: 6 * 60 * 60 * 1000, // 6 hours
  
  // Backup retention
  backupRetention: 7, // 7 days
  
  // Backup compression
  backupCompression: true,
  
  // Point-in-time recovery
  pointInTimeRecovery: true,
  
  // Backup verification
  backupVerification: true,
  
  // Disaster recovery
  disasterRecoveryEnabled: true,
  replicationEnabled: true,
  
  // Backup storage
  backupStorage: {
    type: 'gcs', // Google Cloud Storage
    bucket: 'collaboration-backups',
    path: 'redis-backups',
  },
};

// Security configuration
export const SECURITY_CONFIG = {
  // Authentication
  auth: {
    enabled: true,
    password: appConfig.redis.password,
    users: {
      collaboration: {
        password: appConfig.redis.password,
        permissions: '+@all',
      },
    },
  },
  
  // TLS configuration
  tls: {
    enabled: appConfig.redis.tls,
    cert: appConfig.redis.cert,
    key: appConfig.redis.key,
    ca: appConfig.redis.ca,
    rejectUnauthorized: true,
  },
  
  // Network security
  network: {
    allowedIPs: appConfig.redis.allowedIPs || [],
    maxClients: 10000,
    tcpKeepAlive: 300,
    timeout: 0,
  },
  
  // Command security
  commands: {
    disabledCommands: ['FLUSHDB', 'FLUSHALL', 'KEYS', 'DEBUG'],
    renamedCommands: {
      CONFIG: 'CONFIG-PROTECTED',
      EVAL: 'EVAL-PROTECTED',
    },
  },
  
  // Data encryption
  encryption: {
    enabled: false, // Enable if required
    algorithm: 'aes-256-gcm',
    keyRotation: true,
    keyRotationInterval: 30 * 24 * 60 * 60 * 1000, // 30 days
  },
};

// Export all configurations
export const redisConfig = {
  client: REDIS_CONFIG,
  cluster: REDIS_CLUSTER_CONFIG,
  clusterNodes: REDIS_CLUSTER_NODES,
  pool: REDIS_POOL_CONFIG,
  cache: CACHE_STRATEGIES,
  streams: REDIS_STREAMS_CONFIG,
  pubsub: REDIS_PUBSUB_CONFIG,
  memory: MEMORY_CONFIG,
  monitoring: MONITORING_CONFIG,
  backup: BACKUP_CONFIG,
  security: SECURITY_CONFIG,
};

// Helper functions for configuration
export const getCacheConfig = (strategy: keyof typeof CACHE_STRATEGIES) => {
  return CACHE_STRATEGIES[strategy];
};

export const getStreamConfig = (streamName: keyof typeof REDIS_STREAMS_CONFIG.streams) => {
  return {
    stream: REDIS_STREAMS_CONFIG.streams[streamName],
    consumerGroup: REDIS_STREAMS_CONFIG.consumerGroups[streamName],
    config: REDIS_STREAMS_CONFIG,
  };
};

export const getPubSubConfig = (channel: keyof typeof REDIS_PUBSUB_CONFIG.channels) => {
  return {
    channel: REDIS_PUBSUB_CONFIG.channels[channel],
    config: REDIS_PUBSUB_CONFIG,
  };
};