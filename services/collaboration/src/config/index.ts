/**
 * Configuration Management
 * 
 * Centralized configuration for the collaboration service
 * with environment-based settings and validation.
 */

import { z } from 'zod';

// Environment schema validation
const envSchema = z.object({
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  PORT: z.string().transform(Number).default('8005'),
  LOG_LEVEL: z.enum(['error', 'warn', 'info', 'debug']).default('info'),
  
  // Authentication
  JWT_SECRET: z.string().min(32, 'JWT secret must be at least 32 characters'),
  JWT_EXPIRES_IN: z.string().default('24h'),
  
  // Redis Configuration
  REDIS_HOST: z.string().default('localhost'),
  REDIS_PORT: z.string().transform(Number).default('6379'),
  REDIS_PASSWORD: z.string().optional(),
  REDIS_DB: z.string().transform(Number).default('0'),
  REDIS_CLUSTER_ENABLED: z.string().transform(Boolean).default('false'),
  REDIS_CLUSTER_NODES: z.string().optional(),
  REDIS_TLS_ENABLED: z.string().transform(Boolean).default('false'),
  REDIS_CERT_PATH: z.string().optional(),
  REDIS_KEY_PATH: z.string().optional(),
  REDIS_CA_PATH: z.string().optional(),
  REDIS_ALLOWED_IPS: z.string().optional(),
  
  // Google Cloud Configuration
  GOOGLE_CLOUD_PROJECT_ID: z.string(),
  FIRESTORE_DATABASE_ID: z.string().default('(default)'),
  SPANNER_INSTANCE_ID: z.string(),
  SPANNER_DATABASE_ID: z.string(),
  
  // Service Configuration
  ALLOWED_ORIGINS: z.string().default('http://localhost:3000'),
  MAX_CONNECTIONS_PER_SESSION: z.string().transform(Number).default('50'),
  SESSION_TIMEOUT_MS: z.string().transform(Number).default('3600000'), // 1 hour
  
  // Performance
  ENABLE_CLUSTERING: z.string().transform(Boolean).default('false'),
  ENABLE_METRICS: z.string().transform(Boolean).default('true'),
  METRICS_PORT: z.string().transform(Number).default('9090'),
});

// Parse and validate environment variables
const parseEnv = () => {
  try {
    return envSchema.parse(process.env);
  } catch (error) {
    console.error('❌ Invalid environment configuration:', error);
    process.exit(1);
  }
};

export const config = parseEnv();

// Configuration object
export const appConfig = {
  // Server configuration
  server: {
    port: config.PORT,
    environment: config.NODE_ENV,
    logLevel: config.LOG_LEVEL,
    allowedOrigins: config.ALLOWED_ORIGINS.split(','),
  },

  // Authentication configuration
  auth: {
    jwtSecret: config.JWT_SECRET,
    jwtExpiresIn: config.JWT_EXPIRES_IN,
  },

  // Redis configuration
  redis: {
    host: config.REDIS_HOST,
    port: config.REDIS_PORT,
    password: config.REDIS_PASSWORD,
    database: config.REDIS_DB,
    retryDelayOnFailover: 100,
    enableReadyCheck: true,
    maxRetriesPerRequest: 3,
    cluster: {
      enabled: config.REDIS_CLUSTER_ENABLED,
      nodes: config.REDIS_CLUSTER_NODES ? 
        config.REDIS_CLUSTER_NODES.split(',').map(node => {
          const [host, port] = node.split(':');
          return { host, port: parseInt(port, 10) };
        }) : 
        [{ host: config.REDIS_HOST, port: config.REDIS_PORT }],
    },
    tls: config.REDIS_TLS_ENABLED,
    cert: config.REDIS_CERT_PATH,
    key: config.REDIS_KEY_PATH,
    ca: config.REDIS_CA_PATH,
    allowedIPs: config.REDIS_ALLOWED_IPS ? config.REDIS_ALLOWED_IPS.split(',') : [],
  },

  // Google Cloud configuration
  gcp: {
    projectId: config.GOOGLE_CLOUD_PROJECT_ID,
    firestore: {
      databaseId: config.FIRESTORE_DATABASE_ID,
    },
    spanner: {
      instanceId: config.SPANNER_INSTANCE_ID,
      databaseId: config.SPANNER_DATABASE_ID,
    },
  },

  // Collaboration service configuration
  collaboration: {
    maxConnectionsPerSession: config.MAX_CONNECTIONS_PER_SESSION,
    sessionTimeoutMs: config.SESSION_TIMEOUT_MS,
    enableClustering: config.ENABLE_CLUSTERING,
  },

  // Monitoring configuration
  monitoring: {
    enableMetrics: config.ENABLE_METRICS,
    metricsPort: config.METRICS_PORT,
  },
} as const;

// Type exports
export type AppConfig = typeof appConfig;
export type Environment = typeof config.NODE_ENV;