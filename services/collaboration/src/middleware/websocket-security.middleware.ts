/**
 * WebSocket Security Middleware
 * 
 * Security middleware specifically for WebSocket connections
 * including authentication, rate limiting, and event validation.
 */

import { Socket } from 'socket.io';
import { ExtendedError } from 'socket.io/dist/namespace';
import { jwtManager, JWTPayload, TokenError } from '../utils/jwt';
import { auditService, AuditEventType, AuditSeverity } from '../services/audit.service';
import { redisClient } from '../clients/redis.client';
import { logger } from '../utils/logger';
import { z } from 'zod';

// Rate limiting configuration for WebSocket events
const WS_RATE_LIMITS = {
  message: { windowMs: 60000, max: 60 }, // 60 messages per minute
  cursor: { windowMs: 1000, max: 20 }, // 20 cursor updates per second
  join: { windowMs: 60000, max: 10 }, // 10 joins per minute
  share: { windowMs: 60000, max: 5 }, // 5 shares per minute
};

/**
 * WebSocket Authentication Middleware
 */
export const wsAuthMiddleware = async (
  socket: Socket,
  next: (err?: ExtendedError) => void
): Promise<void> => {
  const token = extractTokenFromSocket(socket);
  const ipAddress = socket.request.socket.remoteAddress;
  const userAgent = socket.request.headers['user-agent'];

  if (!token) {
    await auditService.logSecurityEvent(
      AuditEventType.ACCESS_DENIED,
      AuditSeverity.MEDIUM,
      'WebSocket connection without token',
      { socketId: socket.id },
      { ipAddress, userAgent }
    );

    const error = new Error('Authentication required') as ExtendedError;
    error.data = { code: 'AUTH_REQUIRED' };
    return next(error);
  }

  try {
    const payload = jwtManager.verifyToken(token);
    
    // Attach user data to socket
    socket.data.user = payload;
    socket.data.authenticated = true;
    
    await auditService.logAuthEvent(
      AuditEventType.LOGIN_SUCCESS,
      payload.userId,
      payload.email,
      true,
      { 
        connectionType: 'websocket',
        },
      { ipAddress, userAgent }
    );

    logger.debug('WebSocket authentication successful', {
      userId: payload.userId,
      ipAddress,
    });

    next();
  } catch (error) {
    await auditService.logSecurityEvent(
      AuditEventType.INVALID_TOKEN,
      AuditSeverity.MEDIUM,
      'WebSocket authentication failed',
      { 
          error: error instanceof TokenError ? error.message : 'Unknown error',
      },
      { ipAddress, userAgent }
    );

    const authError = new Error('Authentication failed') as ExtendedError;
    authError.data = { 
      code: error instanceof TokenError ? error.code : 'AUTH_FAILED' 
    };
    next(authError);
  }
};

/**
 * WebSocket Rate Limiting Middleware
 */
export const wsRateLimitMiddleware = (socket: Socket) => {
  const userId = socket.data.user?.userId;
  const ipAddress = socket.request.socket.remoteAddress;

  // Create rate limiting wrapper for events
  const createRateLimitedHandler = (
    eventName: string,
    originalHandler: Function,
    config: { windowMs: number; max: number }
  ) => {
    return async (data: any, callback?: Function) => {
      const key = `ws_rate_limit:${eventName}:${userId}:${ipAddress}`;
      const window = Math.floor(Date.now() / config.windowMs);
      const redisKey = `${key}:${window}`;

      try {
        const current = await redisClient.get(redisKey);
        const count = parseInt(current || '0', 10);

        if (count >= config.max) {
          await auditService.logSecurityEvent(
            AuditEventType.RATE_LIMIT_EXCEEDED,
            AuditSeverity.MEDIUM,
            `WebSocket rate limit exceeded for ${eventName}`,
            {
              eventName,
              count,
              limit: config.max,
              userId,
                    },
            { ipAddress }
          );

          socket.emit('error', {
            code: 'RATE_LIMIT_EXCEEDED',
            message: `Rate limit exceeded for ${eventName}`,
            retryAfter: config.windowMs / 1000,
          });

          return;
        }

        // Increment counter
        await redisClient.set(redisKey, (count + 1).toString(), config.windowMs / 1000);

        // Call original handler
        return originalHandler.call(socket, data, callback);
      } catch (error) {
        logger.error('WebSocket rate limiting error', { error, eventName, userId });
        // Continue without rate limiting on error
        return originalHandler.call(socket, data, callback);
      }
    };
  };

  // Apply rate limiting to specific events
  const originalOn = socket.on.bind(socket);
  socket.on = function(eventName: string, handler: (...args: any[]) => void) {
    const config = WS_RATE_LIMITS[eventName as keyof typeof WS_RATE_LIMITS];
    if (config) {
      const rateLimitedHandler = createRateLimitedHandler(eventName, handler, config);
      return originalOn(eventName, rateLimitedHandler);
    }
    return originalOn(eventName, handler);
  };
};

/**
 * WebSocket Input Validation Middleware
 */
export const wsInputValidationMiddleware = (socket: Socket) => {
  const userId = socket.data.user?.userId;
  const ipAddress = socket.request.socket.remoteAddress;

  // Event validation schemas
  const eventSchemas = {
    join_session: z.object({
      sessionId: z.string().uuid(),
    }),
    leave_session: z.object({
      sessionId: z.string().uuid(),
    }),
    session_message: z.object({
      sessionId: z.string().uuid(),
      message: z.string().min(1).max(1000),
      type: z.enum(['text', 'system', 'analysis_share']).default('text'),
      metadata: z.record(z.any()).optional(),
    }),
    cursor_position: z.object({
      sessionId: z.string().uuid(),
      position: z.object({
        x: z.number().min(0).max(10000),
        y: z.number().min(0).max(10000),
        elementId: z.string().optional(),
      }),
    }),
    share_analysis: z.object({
      sessionId: z.string().uuid(),
      analysis: z.object({
        id: z.string(),
        type: z.enum(['code_analysis', 'pattern_detection', 'security_scan', 'performance_analysis']),
        title: z.string().min(1).max(200),
        results: z.record(z.any()),
        metadata: z.record(z.any()).optional(),
      }),
    }),
  };

  // Wrap event handlers with validation
  const originalOn = socket.on.bind(socket);
  socket.on = function(eventName: string, handler: (...args: any[]) => void) {
    const schema = eventSchemas[eventName as keyof typeof eventSchemas];
    
    if (schema) {
      const validatedHandler = async (data: any, callback?: Function) => {
        try {
          const validatedData = schema.parse(data);
          return handler.call(socket, validatedData, callback);
        } catch (error) {
          await auditService.logSecurityEvent(
            AuditEventType.SUSPICIOUS_ACTIVITY,
            AuditSeverity.MEDIUM,
            `WebSocket input validation failed for ${eventName}`,
            {
              eventName,
              validationError: error instanceof Error ? error.message : 'Unknown error',
              userId,
                      data: JSON.stringify(data).substring(0, 500), // Limit logged data
            },
            { ipAddress }
          );

          socket.emit('error', {
            code: 'VALIDATION_ERROR',
            message: 'Invalid input data',
            event: eventName,
            details: error instanceof Error ? error.message : 'Unknown error',
          });
        }
      };
      
      return originalOn(eventName, validatedHandler);
    }
    
    return originalOn(eventName, handler);
  };
};

/**
 * WebSocket Authorization Middleware
 */
export const wsAuthorizationMiddleware = (socket: Socket) => {
  const user = socket.data.user as JWTPayload;
  const ipAddress = socket.request.socket.remoteAddress;

  // Authorization wrapper for events
  const createAuthorizedHandler = (
    eventName: string,
    originalHandler: Function,
    authCheck: (data: any, user: JWTPayload) => Promise<boolean>
  ) => {
    return async (data: any, callback?: Function) => {
      try {
        const authorized = await authCheck(data, user);
        
        if (!authorized) {
          await auditService.logAuthorizationEvent(
            false,
            user.userId,
            user.email,
            `websocket:${eventName}`,
            eventName,
            { ipAddress }
          );

          socket.emit('error', {
            code: 'AUTHORIZATION_FAILED',
            message: 'Not authorized for this action',
            event: eventName,
          });

          return;
        }

        await auditService.logAuthorizationEvent(
          true,
          user.userId,
          user.email,
          `websocket:${eventName}`,
          eventName,
          { ipAddress }
        );

        return originalHandler.call(socket, data, callback);
      } catch (error) {
        logger.error('WebSocket authorization error', { error, eventName, userId: user.userId });
        socket.emit('error', {
          code: 'AUTHORIZATION_ERROR',
          message: 'Authorization check failed',
          event: eventName,
        });
      }
    };
  };

  // Authorization checks for different events
  const authChecks = {
    join_session: async (data: any, user: JWTPayload) => {
      // Check if user is a member of the session's team
      // This would typically involve a database query
      return true; // Simplified for demo
    },
    join_team: async (data: any, user: JWTPayload) => {
      // Check if user is a member of the team
      return user.teamIds.includes(data.teamId);
    },
    share_analysis: async (data: any, user: JWTPayload) => {
      // Check if user has permission to share analysis
      return user.permissions.includes('analysis:share');
    },
  };

  // Apply authorization to specific events
  const originalOn = socket.on.bind(socket);
  socket.on = function(eventName: string, handler: (...args: any[]) => void) {
    const authCheck = authChecks[eventName as keyof typeof authChecks];
    if (authCheck) {
      const authorizedHandler = createAuthorizedHandler(eventName, handler, authCheck);
      return originalOn(eventName, authorizedHandler);
    }
    return originalOn(eventName, handler);
  };
};

/**
 * WebSocket Connection Monitoring Middleware
 */
export const wsMonitoringMiddleware = (socket: Socket) => {
  const user = socket.data.user as JWTPayload;
  const ipAddress = socket.request.socket.remoteAddress;
  const userAgent = socket.request.headers['user-agent'];
  const connectionTime = Date.now();

  // Track connection metrics
  const updateConnectionMetrics = async (event: string, data?: any) => {
    const key = `ws_metrics:${user.userId}`;
    const metrics = {
      lastActivity: new Date().toISOString(),
      event,
      connectionTime,
      sessionDuration: Date.now() - connectionTime,
      data: data ? JSON.stringify(data).substring(0, 100) : undefined,
    };

    await redisClient.set(key, JSON.stringify(metrics), 3600); // 1 hour TTL
  };

  // Monitor all events
  const originalEmit = socket.emit.bind(socket);
  socket.emit = function(eventName: string, ...args: any[]) {
    updateConnectionMetrics(`emit:${eventName}`, args[0]);
    return originalEmit(eventName, ...args);
  };

  const originalOn = socket.on.bind(socket);
  socket.on = function(eventName: string, handler: (...args: any[]) => void) {
    const monitoredHandler = async (data: any, callback?: Function) => {
      await updateConnectionMetrics(`on:${eventName}`, data);
      return handler.call(socket, data, callback);
    };
    
    return originalOn(eventName, monitoredHandler);
  };

  // Log connection events
  socket.on('connect', () => {
    logger.info('WebSocket connection established', {
      userId: user.userId,
      ipAddress,
      userAgent,
    });
  });

  socket.on('disconnect', async (reason) => {
    const sessionDuration = Date.now() - connectionTime;
    
    await auditService.logSessionEvent(
      AuditEventType.SESSION_LEFT,
      socket.id,
      user.userId,
      user.email,
      'WebSocket disconnection',
      { 
        reason,
        sessionDuration,
        },
      { ipAddress, userAgent }
    );

    logger.info('WebSocket connection closed', {
      userId: user.userId,
      reason,
      sessionDuration,
    });
  });
};

/**
 * Extract token from WebSocket handshake
 */
function extractTokenFromSocket(socket: Socket): string | null {
  // Check authorization header
  const authHeader = socket.handshake.headers.authorization;
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }
  
  // Check query params
  const queryToken = socket.handshake.query.token;
  if (typeof queryToken === 'string') {
    return queryToken;
  }
  
  // Check auth object
  const authToken = socket.handshake.auth?.token;
  if (typeof authToken === 'string') {
    return authToken;
  }
  
  return null;
}

/**
 * Setup all WebSocket security middleware
 */
export const setupWebSocketSecurity = (socket: Socket): void => {
  wsRateLimitMiddleware(socket);
  wsInputValidationMiddleware(socket);
  wsAuthorizationMiddleware(socket);
  wsMonitoringMiddleware(socket);
};

/**
 * WebSocket Connection Security Summary
 */
export const wsSecuritySummary = (socket: Socket): void => {
  const user = socket.data.user as JWTPayload;
  
  logger.info('WebSocket security initialized', {
    userId: user.userId,
    socketId: socket.id,
    permissions: user.permissions,
    teamIds: user.teamIds,
    role: user.role,
    securityFeatures: [
      'authentication',
      'rate_limiting',
      'input_validation',
      'authorization',
      'monitoring',
      'audit_logging',
    ],
  });
};