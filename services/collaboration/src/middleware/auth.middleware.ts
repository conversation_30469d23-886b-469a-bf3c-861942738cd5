/**
 * Authentication Middleware
 * 
 * JWT-based authentication for both HTTP requests and WebSocket connections.
 * Validates tokens and extracts user information.
 */

import { Request, Response, NextFunction } from 'express';
import { Socket } from 'socket.io';
import jwt from 'jsonwebtoken';
import { appConfig } from '../config';
import { logger } from '../utils/logger';
import { JWTPayload as JWTPayloadType } from '../utils/jwt';

// Extend Express Request type
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        email: string;
        name?: string;
        role?: string;
      };
    }
  }
}

// JWT payload interface
interface JWTPayload {
  userId: string;
  email: string;
  name?: string;
  role?: string;
  iat?: number;
  exp?: number;
}

/**
 * HTTP Authentication Middleware
 */
export const authMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const token = extractToken(req);
    
    if (!token) {
      res.status(401).json({
        error: 'Unauthorized',
        message: 'No token provided',
        timestamp: new Date().toISOString(),
      });
      return;
    }

    const decoded = jwt.verify(token, appConfig.auth.jwtSecret) as JWTPayloadType;
    
    req.user = {
      id: decoded.userId,
      email: decoded.email,
      name: decoded.name,
      role: decoded.role,
    };

    logger.debug('User authenticated via HTTP', {
      userId: req.user.id,
      email: req.user.email,
      path: req.path,
      method: req.method,
    });

    next();
  } catch (error) {
    if (error instanceof jwt.TokenExpiredError) {
      res.status(401).json({
        error: 'Token Expired',
        message: 'JWT token has expired',
        timestamp: new Date().toISOString(),
      });
    } else if (error instanceof jwt.JsonWebTokenError) {
      res.status(401).json({
        error: 'Invalid Token',
        message: 'JWT token is invalid',
        timestamp: new Date().toISOString(),
      });
    } else {
      logger.error('Authentication error', { error });
      res.status(500).json({
        error: 'Authentication Error',
        message: 'Failed to authenticate user',
        timestamp: new Date().toISOString(),
      });
    }
  }
};

/**
 * WebSocket Authentication Middleware
 */
export const authenticateSocket = async (
  socket: Socket,
  next: (err?: Error) => void
): Promise<void> => {
  try {
    const token = extractSocketToken(socket);
    
    if (!token) {
      const error = new Error('No token provided');
      (error as any).data = { code: 'NO_TOKEN' };
      next(error);
      return;
    }

    const decoded = jwt.verify(token, appConfig.auth.jwtSecret) as JWTPayloadType;
    
    // Attach user data to socket (compatible with websocket-security middleware)
    socket.data.userId = decoded.userId;
    socket.data.userEmail = decoded.email;
    socket.data.userName = decoded.name;
    socket.data.userRole = decoded.role;
    
    // Also set user object for WebSocket security middleware compatibility
    socket.data.user = {
      userId: decoded.userId,
      email: decoded.email,
      name: decoded.name,
      role: decoded.role || 'member',
      permissions: ['read', 'write'], // Basic permissions - would come from database in production
      teamIds: ['default'], // Would come from database in production
      tokenType: 'access' as const,
    };

    logger.debug('User authenticated via WebSocket', {
      socketId: socket.id,
      userId: decoded.userId,
      email: decoded.email,
    });

    next();
  } catch (error) {
    let errorMessage = 'Authentication failed';
    let errorCode = 'AUTH_ERROR';

    if (error instanceof jwt.TokenExpiredError) {
      errorMessage = 'Token expired';
      errorCode = 'TOKEN_EXPIRED';
    } else if (error instanceof jwt.JsonWebTokenError) {
      errorMessage = 'Invalid token';
      errorCode = 'INVALID_TOKEN';
    }

    logger.error('WebSocket authentication error', { error, socketId: socket.id });
    
    const authError = new Error(errorMessage);
    (authError as any).data = { code: errorCode };
    next(authError);
  }
};

/**
 * Optional Authentication Middleware
 * Allows requests without authentication but extracts user info if present
 */
export const optionalAuthMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const token = extractToken(req);
    
    if (token) {
      try {
        const decoded = jwt.verify(token, appConfig.auth.jwtSecret) as JWTPayloadType;
        req.user = {
          id: decoded.userId,
          email: decoded.email,
          name: decoded.name,
          role: decoded.role,
        };
      } catch (error) {
        // Token is invalid, but we continue without authentication
        logger.debug('Optional auth failed, continuing without user', { error });
      }
    }

    next();
  } catch (error) {
    logger.error('Optional authentication error', { error });
    next();
  }
};

/**
 * Role-based authorization middleware
 */
export const requireRole = (requiredRole: string) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required',
        timestamp: new Date().toISOString(),
      });
      return;
    }

    if (req.user.role !== requiredRole && req.user.role !== 'admin') {
      res.status(403).json({
        error: 'Forbidden',
        message: `Role '${requiredRole}' required`,
        timestamp: new Date().toISOString(),
      });
      return;
    }

    next();
  };
};

/**
 * Extract token from HTTP request
 */
function extractToken(req: Request): string | null {
  const authHeader = req.headers.authorization;
  
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }
  
  // Check for token in query params (for WebSocket upgrades)
  const queryToken = req.query.token;
  if (typeof queryToken === 'string') {
    return queryToken;
  }
  
  return null;
}

/**
 * Extract token from WebSocket handshake
 */
function extractSocketToken(socket: Socket): string | null {
  // Check authorization header
  const authHeader = socket.handshake.headers.authorization;
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }
  
  // Check query params
  const queryToken = socket.handshake.query.token;
  if (typeof queryToken === 'string') {
    return queryToken;
  }
  
  // Check auth object (for Socket.IO client)
  const authToken = socket.handshake.auth?.token;
  if (typeof authToken === 'string') {
    return authToken;
  }
  
  return null;
}

/**
 * Generate JWT token (for testing purposes)
 */
export const generateTestToken = (payload: Omit<JWTPayloadType, 'iat' | 'exp'>): string => {
  return jwt.sign(payload as any, appConfig.auth.jwtSecret, {
    expiresIn: '15m',
  } as jwt.SignOptions);
};