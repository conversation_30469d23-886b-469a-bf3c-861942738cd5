/**
 * Performance Middleware
 * 
 * Performance monitoring, request timing, memory usage tracking,
 * and resource optimization middleware for the collaboration service.
 */

import { Request, Response, NextFunction } from 'express';
import { performance } from 'perf_hooks';
import { logger } from '../utils/logger';
import { redisClient } from '../clients/redis.client';
import promClient from 'prom-client';
import os from 'os';

// Prometheus metrics
const httpRequestDuration = new promClient.Histogram({
  name: 'http_request_duration_seconds',
  help: 'Duration of HTTP requests in seconds',
  labelNames: ['method', 'route', 'status_code', 'user_id'],
  buckets: [0.1, 0.5, 1, 2, 5, 10],
});

const httpRequestsTotal = new promClient.Counter({
  name: 'http_requests_total',
  help: 'Total number of HTTP requests',
  labelNames: ['method', 'route', 'status_code', 'user_id'],
});

const websocketConnectionsTotal = new promClient.Gauge({
  name: 'websocket_connections_total',
  help: 'Total number of active WebSocket connections',
  labelNames: ['room', 'user_id'],
});

const redisOperationsTotal = new promClient.Counter({
  name: 'redis_operations_total',
  help: 'Total number of Redis operations',
  labelNames: ['operation', 'status'],
});

const memoryUsage = new promClient.Gauge({
  name: 'nodejs_memory_usage_bytes',
  help: 'Memory usage in bytes',
  labelNames: ['type'],
});

const databaseQueryDuration = new promClient.Histogram({
  name: 'database_query_duration_seconds',
  help: 'Duration of database queries in seconds',
  labelNames: ['operation', 'collection', 'status'],
  buckets: [0.01, 0.05, 0.1, 0.5, 1, 2, 5],
});

// Custom metrics tracking
interface PerformanceMetrics {
  requestCount: number;
  averageResponseTime: number;
  memoryUsage: NodeJS.MemoryUsage;
  cpuUsage: NodeJS.CpuUsage;
  activeConnections: number;
  errorRate: number;
}

/**
 * Performance monitoring middleware
 */
export const performanceMonitoring = (req: Request, res: Response, next: NextFunction): void => {
  const startTime = performance.now();
  const startUsage = process.cpuUsage();
  
  // Track request start
  const route = req.route?.path || req.path;
  const method = req.method;
  const userId = req.user?.id || 'anonymous';

  // Monitor response
  const originalSend = res.send;
  res.send = function(body: any) {
    const duration = (performance.now() - startTime) / 1000; // Convert to seconds
    const statusCode = res.statusCode.toString();
    
    // Record metrics
    httpRequestDuration.observe(
      { method, route, status_code: statusCode, user_id: userId },
      duration
    );
    
    httpRequestsTotal.inc({ method, route, status_code: statusCode, user_id: userId });
    
    // Log performance data
    logger.info('Request completed', {
      method,
      route,
      statusCode,
      duration: `${duration.toFixed(3)}s`,
      userId,
      contentLength: res.get('Content-Length'),
      timestamp: new Date().toISOString(),
    });
    
    // Track slow requests
    if (duration > 1) {
      logger.warn('Slow request detected', {
        method,
        route,
        duration: `${duration.toFixed(3)}s`,
        userId,
        memoryUsage: process.memoryUsage(),
      });
    }
    
    return originalSend.call(this, body);
  };

  next();
};

/**
 * Memory monitoring middleware
 */
export const memoryMonitoring = (req: Request, res: Response, next: NextFunction): void => {
  const memUsage = process.memoryUsage();
  
  // Update Prometheus metrics
  memoryUsage.set({ type: 'rss' }, memUsage.rss);
  memoryUsage.set({ type: 'heap_total' }, memUsage.heapTotal);
  memoryUsage.set({ type: 'heap_used' }, memUsage.heapUsed);
  memoryUsage.set({ type: 'external' }, memUsage.external);
  
  // Log memory warnings
  const heapUsedMB = memUsage.heapUsed / 1024 / 1024;
  const heapTotalMB = memUsage.heapTotal / 1024 / 1024;
  const heapUsagePercent = (heapUsedMB / heapTotalMB) * 100;
  
  if (heapUsagePercent > 85) {
    logger.warn('High memory usage detected', {
      heapUsed: `${heapUsedMB.toFixed(2)}MB`,
      heapTotal: `${heapTotalMB.toFixed(2)}MB`,
      usage: `${heapUsagePercent.toFixed(2)}%`,
      rss: `${(memUsage.rss / 1024 / 1024).toFixed(2)}MB`,
    });
  }
  
  next();
};

/**
 * Redis operation tracking
 */
export const trackRedisOperation = (operation: string, status: 'success' | 'error'): void => {
  redisOperationsTotal.inc({ operation, status });
};

/**
 * Database query tracking
 */
export const trackDatabaseQuery = (
  operation: string,
  collection: string,
  duration: number,
  status: 'success' | 'error'
): void => {
  databaseQueryDuration.observe(
    { operation, collection, status },
    duration / 1000 // Convert to seconds
  );
};

/**
 * WebSocket connection tracking
 */
export const trackWebSocketConnection = (action: 'connect' | 'disconnect', room?: string, userId?: string): void => {
  const labels = { room: room || 'default', user_id: userId || 'anonymous' };
  
  if (action === 'connect') {
    websocketConnectionsTotal.inc(labels);
  } else {
    websocketConnectionsTotal.dec(labels);
  }
};

/**
 * Performance metrics collector
 */
export class PerformanceCollector {
  private static instance: PerformanceCollector;
  private metrics: PerformanceMetrics;
  private intervalId: NodeJS.Timeout | null = null;

  private constructor() {
    this.metrics = {
      requestCount: 0,
      averageResponseTime: 0,
      memoryUsage: process.memoryUsage(),
      cpuUsage: process.cpuUsage(),
      activeConnections: 0,
      errorRate: 0,
    };
  }

  static getInstance(): PerformanceCollector {
    if (!PerformanceCollector.instance) {
      PerformanceCollector.instance = new PerformanceCollector();
    }
    return PerformanceCollector.instance;
  }

  /**
   * Start collecting performance metrics
   */
  startCollection(): void {
    this.intervalId = setInterval(() => {
      this.collectMetrics();
    }, 30000); // Collect every 30 seconds
  }

  /**
   * Stop collecting performance metrics
   */
  stopCollection(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }

  /**
   * Collect system metrics
   */
  private async collectMetrics(): Promise<void> {
    try {
      const memUsage = process.memoryUsage();
      const cpuUsage = process.cpuUsage();
      
      this.metrics = {
        ...this.metrics,
        memoryUsage: memUsage,
        cpuUsage: cpuUsage,
      };

      // Store metrics in Redis for historical tracking
      const metricsKey = `performance:metrics:${Date.now()}`;
      await redisClient.set(metricsKey, JSON.stringify(this.metrics), 3600); // 1 hour TTL

      // Log system metrics
      logger.debug('System metrics collected', {
        memory: {
          rss: `${(memUsage.rss / 1024 / 1024).toFixed(2)}MB`,
          heapTotal: `${(memUsage.heapTotal / 1024 / 1024).toFixed(2)}MB`,
          heapUsed: `${(memUsage.heapUsed / 1024 / 1024).toFixed(2)}MB`,
          external: `${(memUsage.external / 1024 / 1024).toFixed(2)}MB`,
        },
        cpu: {
          user: cpuUsage.user,
          system: cpuUsage.system,
        },
        loadAverage: os.loadavg(),
        uptime: process.uptime(),
      });
    } catch (error) {
      logger.error('Failed to collect performance metrics', { error });
    }
  }

  /**
   * Get current metrics
   */
  getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  /**
   * Get historical metrics
   */
  async getHistoricalMetrics(minutes: number = 60): Promise<PerformanceMetrics[]> {
    try {
      const now = Date.now();
      const start = now - (minutes * 60 * 1000);
      
      const keys = await redisClient.getRawClient().keys(`performance:metrics:*`);
      const historicalMetrics: PerformanceMetrics[] = [];
      
      for (const key of keys) {
        const timestamp = parseInt(key.split(':').pop() || '0');
        if (timestamp >= start) {
          const data = await redisClient.get(key);
          if (data) {
            historicalMetrics.push(JSON.parse(data));
          }
        }
      }
      
      return historicalMetrics.sort((a, b) => 
        new Date(a.memoryUsage.toString()).getTime() - new Date(b.memoryUsage.toString()).getTime()
      );
    } catch (error) {
      logger.error('Failed to get historical metrics', { error });
      return [];
    }
  }
}

/**
 * Circuit breaker for external services
 */
export class CircuitBreaker {
  private failureCount = 0;
  private lastFailureTime = 0;
  private state: 'closed' | 'open' | 'half-open' = 'closed';
  
  constructor(
    private readonly failureThreshold: number = 5,
    private readonly timeout: number = 60000,
    private readonly retryTimeout: number = 30000
  ) {}

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === 'open') {
      if (Date.now() - this.lastFailureTime > this.retryTimeout) {
        this.state = 'half-open';
      } else {
        throw new Error('Circuit breaker is open');
      }
    }

    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }

  private onSuccess(): void {
    this.failureCount = 0;
    this.state = 'closed';
  }

  private onFailure(): void {
    this.failureCount++;
    this.lastFailureTime = Date.now();

    if (this.failureCount >= this.failureThreshold) {
      this.state = 'open';
      logger.warn('Circuit breaker opened', {
        failureCount: this.failureCount,
        threshold: this.failureThreshold,
      });
    }
  }

  getState(): string {
    return this.state;
  }
}

/**
 * Request caching middleware
 */
export const requestCaching = (ttl: number = 300) => {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    // Only cache GET requests
    if (req.method !== 'GET') {
      return next();
    }

    const cacheKey = `cache:${req.originalUrl}:${req.user?.id || 'anonymous'}`;
    
    try {
      const cachedResponse = await redisClient.get(cacheKey);
      
      if (cachedResponse) {
        const { data, statusCode, headers } = JSON.parse(cachedResponse);
        
        // Set cached headers
        Object.entries(headers).forEach(([key, value]) => {
          res.set(key, value as string);
        });
        
        res.set('X-Cache', 'HIT');
        res.status(statusCode).json(data);
        
        logger.debug('Cache hit', { cacheKey, statusCode });
        return;
      }
    } catch (error) {
      logger.error('Cache read error', { error, cacheKey });
    }

    // Intercept response
    const originalSend = res.send;
    res.send = function(body: any) {
      // Cache successful responses
      if (res.statusCode >= 200 && res.statusCode < 300) {
        const cacheData = {
          data: typeof body === 'string' ? JSON.parse(body) : body,
          statusCode: res.statusCode,
          headers: res.getHeaders(),
        };
        
        redisClient.set(cacheKey, JSON.stringify(cacheData), ttl)
          .catch(error => logger.error('Cache write error', { error, cacheKey }));
      }
      
      res.set('X-Cache', 'MISS');
      return originalSend.call(this, body);
    };

    next();
  };
};

/**
 * Health check endpoint data
 */
export const getHealthMetrics = async (): Promise<{
  status: string;
  timestamp: string;
  uptime: number;
  memory: NodeJS.MemoryUsage;
  cpu: NodeJS.CpuUsage;
  connections: {
    redis: boolean;
    database: boolean;
  };
  performance: {
    avgResponseTime: number;
    requestCount: number;
    errorRate: number;
  };
}> => {
  const collector = PerformanceCollector.getInstance();
  const metrics = collector.getMetrics();
  
  return {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    cpu: process.cpuUsage(),
    connections: {
      redis: await testRedisConnection(),
      database: await testDatabaseConnection(),
    },
    performance: {
      avgResponseTime: metrics.averageResponseTime,
      requestCount: metrics.requestCount,
      errorRate: metrics.errorRate,
    },
  };
};

/**
 * Test Redis connection
 */
async function testRedisConnection(): Promise<boolean> {
  try {
    await redisClient.ping();
    return true;
  } catch (error) {
    return false;
  }
}

/**
 * Test database connection
 */
async function testDatabaseConnection(): Promise<boolean> {
  try {
    // This would test actual database connection
    return true;
  } catch (error) {
    return false;
  }
}

// Export singleton instance
export const performanceCollector = PerformanceCollector.getInstance();

// Export prometheus metrics register
export const metricsRegister = promClient.register;