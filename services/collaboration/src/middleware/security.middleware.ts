/**
 * Security Middleware
 * 
 * Comprehensive security middleware including rate limiting,
 * input validation, CSRF protection, and security headers.
 */

import { Request, Response, NextFunction } from 'express';
import rateLimit from 'express-rate-limit';
import helmet from 'helmet';
import { body, param, query, validationResult } from 'express-validator';
import { redisClient } from '../clients/redis.client';
import { logger } from '../utils/logger';
import { jwtManager, JWTPayload, Permission, hasPermission, isTeamMember } from '../utils/jwt';
import crypto from 'crypto';

// Rate limiting configurations
const RATE_LIMIT_CONFIGS = {
  general: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // limit each IP to 100 requests per windowMs
  },
  auth: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5, // limit each IP to 5 auth requests per windowMs
  },
  websocket: {
    windowMs: 60 * 1000, // 1 minute
    max: 60, // limit each IP to 60 WebSocket events per minute
  },
  api: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 1000, // limit each IP to 1000 API requests per windowMs
  },
};

/**
 * Security headers middleware
 */
export const securityHeaders = helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "wss:", "ws:"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
      childSrc: ["'none'"],
      formAction: ["'self'"],
      upgradeInsecureRequests: [],
    },
  },
  crossOriginEmbedderPolicy: false,
  crossOriginOpenerPolicy: { policy: 'same-origin-allow-popups' },
  crossOriginResourcePolicy: { policy: 'cross-origin' },
  dnsPrefetchControl: true,
  frameguard: { action: 'deny' },
  hidePoweredBy: true,
  hsts: {
    maxAge: 31536000, // 1 year
    includeSubDomains: true,
    preload: true,
  },
  ieNoOpen: true,
  noSniff: true,
  originAgentCluster: true,
  permittedCrossDomainPolicies: false,
  referrerPolicy: { policy: 'no-referrer' },
  xssFilter: true,
});

/**
 * Rate limiting middleware factory
 */
export const createRateLimit = (config: keyof typeof RATE_LIMIT_CONFIGS) => {
  const rateLimitConfig = RATE_LIMIT_CONFIGS[config];
  
  return rateLimit({
    windowMs: rateLimitConfig.windowMs,
    max: rateLimitConfig.max,
    message: {
      error: 'Too Many Requests',
      message: `Rate limit exceeded. Try again in ${rateLimitConfig.windowMs / 1000} seconds.`,
      retryAfter: rateLimitConfig.windowMs / 1000,
    },
    standardHeaders: true,
    legacyHeaders: false,
    skip: (req) => {
      // Skip rate limiting for health checks
      return req.path.startsWith('/health');
    },
    keyGenerator: (req) => {
      // Use IP address and optional user ID for rate limiting
      const userId = req.user?.id || 'anonymous';
      return `${req.ip}-${userId}`;
    },
    handler: (req: Request, res: Response) => {
      logger.warn('Rate limit exceeded', {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        path: req.path,
        method: req.method,
        userId: req.user?.id,
        timestamp: new Date().toISOString(),
      });
    },
  });
};

/**
 * Advanced rate limiting with Redis
 */
export const advancedRateLimit = (options: {
  windowMs: number;
  max: number;
  keyGenerator?: (req: Request) => string;
  skipSuccessfulRequests?: boolean;
}) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const key = options.keyGenerator ? options.keyGenerator(req) : `rate_limit:${req.ip}`;
      const window = Math.floor(Date.now() / options.windowMs);
      const redisKey = `${key}:${window}`;

      // Get current count
      const current = await redisClient.get(redisKey);
      const count = parseInt(current || '0', 10);

      if (count >= options.max) {
        // Rate limit exceeded
        const retryAfter = Math.ceil(options.windowMs / 1000);
        
        res.set({
          'X-RateLimit-Limit': options.max.toString(),
          'X-RateLimit-Remaining': '0',
          'X-RateLimit-Reset': (Date.now() + (options.windowMs - (Date.now() % options.windowMs))).toString(),
          'Retry-After': retryAfter.toString(),
        });

        logger.warn('Advanced rate limit exceeded', {
          key,
          count,
          limit: options.max,
          ip: req.ip,
          userId: req.user?.id,
        });

        return res.status(429).json({
          error: 'Too Many Requests',
          message: 'Rate limit exceeded',
          retryAfter,
        });
      }

      // Increment counter
      await redisClient.set(redisKey, (count + 1).toString(), options.windowMs / 1000);

      // Set headers
      res.set({
        'X-RateLimit-Limit': options.max.toString(),
        'X-RateLimit-Remaining': (options.max - count - 1).toString(),
        'X-RateLimit-Reset': (Date.now() + (options.windowMs - (Date.now() % options.windowMs))).toString(),
      });

      next();
    } catch (error) {
      logger.error('Rate limiting error', { error });
      // Continue without rate limiting on error
      next();
    }
  };
};

/**
 * Permission-based authorization middleware
 */
export const requirePermission = (permission: Permission | string) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required',
      });
    }

    const userPayload = {
      ...req.user!,
      userId: req.user!.id,
      permissions: [],
      teamIds: [],
      tokenType: 'access' as const
    } as JWTPayload;
    
    if (!hasPermission(userPayload, permission)) {
      logger.warn('Permission denied', {
        userId: userPayload.userId,
        requiredPermission: permission,
        userPermissions: userPayload.permissions,
        path: req.path,
        method: req.method,
      });

      return res.status(403).json({
        error: 'Forbidden',
        message: `Permission '${permission}' required`,
      });
    }

    next();
  };
};

/**
 * Team membership authorization middleware
 */
export const requireTeamMembership = (teamIdParam: string = 'teamId') => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required',
      });
    }

    const userPayload = {
      ...req.user!,
      userId: req.user!.id,
      permissions: [],
      teamIds: [],
      tokenType: 'access' as const
    } as JWTPayload;
    const teamId = req.params[teamIdParam];

    if (!teamId) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Team ID required',
      });
    }

    if (!isTeamMember(userPayload, teamId)) {
      logger.warn('Team access denied', {
        userId: userPayload.userId,
        teamId,
        userTeams: userPayload.teamIds,
        path: req.path,
        method: req.method,
      });

      return res.status(403).json({
        error: 'Forbidden',
        message: 'Team membership required',
      });
    }

    next();
  };
};

/**
 * Input validation middleware
 */
export const validateInput = (req: Request, res: Response, next: NextFunction) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    logger.warn('Input validation failed', {
      errors: errors.array(),
      body: req.body,
      params: req.params,
      query: req.query,
      userId: req.user?.id,
      path: req.path,
      method: req.method,
    });

    return res.status(400).json({
      error: 'Validation Error',
      message: 'Invalid input data',
      details: errors.array(),
    });
  }

  next();
};

/**
 * CSRF protection middleware
 */
export const csrfProtection = (req: Request, res: Response, next: NextFunction) => {
  // Skip CSRF protection for GET requests and health checks
  if (req.method === 'GET' || req.path.startsWith('/health')) {
    return next();
  }

  const token = req.get('X-CSRF-Token') || req.body._csrf;

  if (!token) {
    logger.warn('CSRF protection triggered', {
      hasToken: !!token,
      userId: req.user?.id,
      path: req.path,
      method: req.method,
    });

    return res.status(403).json({
      error: 'Forbidden',
      message: 'CSRF token validation failed',
    });
  }

  next();
};

/**
 * Generate CSRF token
 */
export const generateCSRFToken = (req: Request, res: Response, next: NextFunction) => {
  // CSRF token generation would be handled by session middleware
  // For now, we'll just pass through
  next();
};

/**
 * Request size limitation middleware
 */
export const limitRequestSize = (maxSize: string = '10mb') => {
  return (req: Request, res: Response, next: NextFunction) => {
    const contentLength = req.get('content-length');
    
    if (contentLength) {
      const sizeInBytes = parseInt(contentLength, 10);
      const maxSizeInBytes = parseSize(maxSize);
      
      if (sizeInBytes > maxSizeInBytes) {
        logger.warn('Request size limit exceeded', {
          contentLength: sizeInBytes,
          maxSize: maxSizeInBytes,
          userId: req.user?.id,
          path: req.path,
          method: req.method,
        });

        return res.status(413).json({
          error: 'Payload Too Large',
          message: `Request size exceeds limit of ${maxSize}`,
        });
      }
    }

    next();
  };
};

/**
 * IP whitelist middleware
 */
export const ipWhitelist = (allowedIPs: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const clientIP = req.ip || 'unknown';
    
    if (!allowedIPs.includes(clientIP)) {
      logger.warn('IP not whitelisted', {
        clientIP,
        allowedIPs,
        path: req.path,
        method: req.method,
      });

      return res.status(403).json({
        error: 'Forbidden',
        message: 'IP address not allowed',
      });
    }

    next();
  };
};

/**
 * Request timeout middleware
 */
export const requestTimeout = (timeoutMs: number = 30000) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const timeout = setTimeout(() => {
      if (!res.headersSent) {
        logger.warn('Request timeout', {
          timeout: timeoutMs,
          userId: req.user?.id,
          path: req.path,
          method: req.method,
        });

        res.status(408).json({
          error: 'Request Timeout',
          message: 'Request took too long to process',
        });
      }
    }, timeoutMs);

    res.on('finish', () => {
      clearTimeout(timeout);
    });

    next();
  };
};

/**
 * Security event logging middleware
 */
export const securityEventLogger = (req: Request, res: Response, next: NextFunction) => {
  // Log security-relevant events
  const originalSend = res.send;
  
  res.send = function(body: any) {
    if (res.statusCode >= 400) {
      logger.warn('Security event', {
        statusCode: res.statusCode,
        method: req.method,
        path: req.path,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        userId: req.user?.id,
        body: typeof body === 'string' ? body : JSON.stringify(body),
        timestamp: new Date().toISOString(),
      });
    }
    
    return originalSend.call(this, body);
  };

  next();
};

// Validation schemas
export const teamValidation = {
  create: [
    body('name').isLength({ min: 1, max: 100 }).trim().escape(),
    body('description').optional().isLength({ max: 500 }).trim().escape(),
  ],
  update: [
    param('teamId').isUUID(),
    body('name').optional().isLength({ min: 1, max: 100 }).trim().escape(),
    body('description').optional().isLength({ max: 500 }).trim().escape(),
  ],
  addMember: [
    param('teamId').isUUID(),
    body('userId').isUUID(),
    body('role').isIn(['member', 'admin']),
  ],
};

export const sessionValidation = {
  create: [
    body('teamId').isUUID(),
    body('name').isLength({ min: 1, max: 100 }).trim().escape(),
    body('type').isIn(['analysis', 'pattern_development', 'code_review', 'discussion']),
    body('description').optional().isLength({ max: 500 }).trim().escape(),
  ],
  update: [
    param('sessionId').isUUID(),
    body('name').optional().isLength({ min: 1, max: 100 }).trim().escape(),
    body('description').optional().isLength({ max: 500 }).trim().escape(),
  ],
};

// Helper functions
function parseSize(size: string): number {
  const units = { b: 1, kb: 1024, mb: 1024 * 1024, gb: 1024 * 1024 * 1024 };
  const match = size.toLowerCase().match(/^(\d+)(b|kb|mb|gb)$/);
  
  if (!match) {
    throw new Error('Invalid size format');
  }
  
  const [, value, unit] = match;
  return parseInt(value, 10) * units[unit as keyof typeof units];
}