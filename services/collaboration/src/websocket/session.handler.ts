/**
 * Session WebSocket Handler
 * 
 * Handles WebSocket events for collaboration sessions,
 * including joining, leaving, and real-time updates.
 */

import { Server as SocketIOServer, Socket } from 'socket.io';
import { z } from 'zod';
import { redisClient } from '../clients/redis.client';
import { firestoreClient } from '../clients/firestore.client';
import { spannerClient } from '../clients/spanner.client';
import { logger } from '../utils/logger';

// Event payload schemas
const joinSessionSchema = z.object({
  sessionId: z.string().uuid(),
});

const leaveSessionSchema = z.object({
  sessionId: z.string().uuid(),
});

const sessionMessageSchema = z.object({
  sessionId: z.string().uuid(),
  message: z.string().min(1).max(1000),
  type: z.enum(['text', 'system', 'analysis_share']).default('text'),
  metadata: z.record(z.any()).optional(),
});

const cursorPositionSchema = z.object({
  sessionId: z.string().uuid(),
  position: z.object({
    x: z.number(),
    y: z.number(),
    elementId: z.string().optional(),
  }),
});

/**
 * Setup session-related WebSocket handlers
 */
export function setupSessionHandlers(io: SocketIOServer, socket: Socket): void {
  const userId = socket.data.userId;
  const userEmail = socket.data.userEmail;

  /**
   * Join a collaboration session
   */
  socket.on('join_session', async (data: any) => {
    try {
      const { sessionId } = joinSessionSchema.parse(data);
      
      // Verify session exists and user has access
      const session = await spannerClient.sessions.get(sessionId);
      if (!session) {
        socket.emit('error', {
          event: 'join_session',
          error: 'Session not found',
          code: 'SESSION_NOT_FOUND',
        });
        return;
      }

      // Check if user is a team member
      const members = await spannerClient.teams.getMembers(session.team_id);
      const isMember = members.some(member => member.user_id === userId);

      if (!isMember) {
        socket.emit('error', {
          event: 'join_session',
          error: 'Access denied',
          code: 'ACCESS_DENIED',
        });
        return;
      }

      // Join the session room
      await socket.join(`session:${sessionId}`);

      // Update presence in Redis
      await redisClient.sadd(`presence:${sessionId}`, userId);

      // Update participant list in Firestore
      await firestoreClient.sessions.update(sessionId, {
        lastActivity: new Date(),
      });

      // Set user presence in Firestore
      await firestoreClient.presence.set(sessionId, userId, {
        socketId: socket.id,
        joinedAt: new Date(),
        userEmail,
        userName: socket.data.userName,
      });

      // Notify other participants
      socket.to(`session:${sessionId}`).emit('user_joined', {
        userId,
        userEmail,
        userName: socket.data.userName,
        sessionId,
        timestamp: new Date().toISOString(),
      });

      // Send current session state to the joining user
      const currentPresence = await redisClient.smembers(`presence:${sessionId}`);
      
      socket.emit('session_joined', {
        sessionId,
        session,
        participants: currentPresence,
        timestamp: new Date().toISOString(),
      });

      logger.info('User joined session', {
        userId,
        sessionId,
        socketId: socket.id,
      });

    } catch (error) {
      logger.error('Error joining session', { error, userId, data });
      socket.emit('error', {
        event: 'join_session',
        error: 'Failed to join session',
        code: 'JOIN_FAILED',
      });
    }
  });

  /**
   * Leave a collaboration session
   */
  socket.on('leave_session', async (data: any) => {
    try {
      const { sessionId } = leaveSessionSchema.parse(data);

      // Leave the session room
      await socket.leave(`session:${sessionId}`);

      // Remove from presence
      await redisClient.srem(`presence:${sessionId}`, userId);
      await firestoreClient.presence.remove(sessionId, userId);

      // Notify other participants
      socket.to(`session:${sessionId}`).emit('user_left', {
        userId,
        userEmail,
        sessionId,
        timestamp: new Date().toISOString(),
      });

      socket.emit('session_left', {
        sessionId,
        timestamp: new Date().toISOString(),
      });

      logger.info('User left session', {
        userId,
        sessionId,
        socketId: socket.id,
      });

    } catch (error) {
      logger.error('Error leaving session', { error, userId, data });
      socket.emit('error', {
        event: 'leave_session',
        error: 'Failed to leave session',
        code: 'LEAVE_FAILED',
      });
    }
  });

  /**
   * Send a message in a session
   */
  socket.on('session_message', async (data: any) => {
    try {
      const { sessionId, message, type, metadata } = sessionMessageSchema.parse(data);

      // Verify user is in the session
      const isInSession = await redisClient.sismember(`presence:${sessionId}`, userId);
      if (!isInSession) {
        socket.emit('error', {
          event: 'session_message',
          error: 'Not in session',
          code: 'NOT_IN_SESSION',
        });
        return;
      }

      // Save message to Firestore
      const messageId = await firestoreClient.messages.create(sessionId, {
        userId,
        userEmail,
        userName: socket.data.userName,
        message,
        type,
        metadata,
      });

      // Broadcast to all session participants
      io.to(`session:${sessionId}`).emit('message_received', {
        id: messageId,
        userId,
        userEmail,
        userName: socket.data.userName,
        message,
        type,
        metadata,
        sessionId,
        timestamp: new Date().toISOString(),
      });

      logger.debug('Session message sent', {
        messageId,
        userId,
        sessionId,
        type,
      });

    } catch (error) {
      logger.error('Error sending session message', { error, userId, data });
      socket.emit('error', {
        event: 'session_message',
        error: 'Failed to send message',
        code: 'MESSAGE_FAILED',
      });
    }
  });

  /**
   * Update cursor position in session
   */
  socket.on('cursor_position', async (data: any) => {
    try {
      const { sessionId, position } = cursorPositionSchema.parse(data);

      // Verify user is in the session
      const isInSession = await redisClient.sismember(`presence:${sessionId}`, userId);
      if (!isInSession) {
        return; // Silently ignore if not in session
      }

      // Broadcast cursor position to other participants
      socket.to(`session:${sessionId}`).emit('cursor_update', {
        userId,
        userEmail,
        userName: socket.data.userName,
        position,
        sessionId,
        timestamp: new Date().toISOString(),
      });

    } catch (error) {
      logger.error('Error updating cursor position', { error, userId, data });
    }
  });

  /**
   * Request session state synchronization
   */
  socket.on('sync_session', async (data: any) => {
    try {
      const { sessionId } = joinSessionSchema.parse(data);

      // Verify user is in the session
      const isInSession = await redisClient.sismember(`presence:${sessionId}`, userId);
      if (!isInSession) {
        socket.emit('error', {
          event: 'sync_session',
          error: 'Not in session',
          code: 'NOT_IN_SESSION',
        });
        return;
      }

      // Get current session state
      const session = await spannerClient.sessions.get(sessionId);
      const currentPresence = await redisClient.smembers(`presence:${sessionId}`);
      const recentMessages = await firestoreClient.messages.list(sessionId, 20);

      socket.emit('session_synced', {
        sessionId,
        session,
        participants: currentPresence,
        recentMessages,
        timestamp: new Date().toISOString(),
      });

    } catch (error) {
      logger.error('Error syncing session', { error, userId, data });
      socket.emit('error', {
        event: 'sync_session',
        error: 'Failed to sync session',
        code: 'SYNC_FAILED',
      });
    }
  });
}