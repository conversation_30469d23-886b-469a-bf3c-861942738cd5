/**
 * WebSocket Server Initialization
 * 
 * Socket.IO server setup with Redis adapter for scaling,
 * authentication middleware, and event handlers.
 */

import { Server as HTTPServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';
import { createAdapter } from '@socket.io/redis-streams-adapter';
import { redisClient } from '../clients/redis.client';
import { appConfig } from '../config';
import { logger } from '../utils/logger';
import { authenticateSocket } from '../middleware/auth.middleware';
import { setupWebSocketSecurity, wsSecuritySummary } from '../middleware/websocket-security.middleware';

// Event handlers
import { setupSessionHandlers } from './session.handler';
import { setupTeamHandlers } from './team.handler';
import { setupAnalysisHandlers } from './analysis.handler';

/**
 * Initialize Socket.IO server with Redis adapter
 */
export async function initializeSocketIO(server: HTTPServer): Promise<SocketIOServer> {
  try {
    // Create Socket.IO server
    const io = new SocketIOServer(server, {
      cors: {
        origin: appConfig.server.allowedOrigins,
        credentials: true,
        methods: ['GET', 'POST'],
      },
      transports: ['websocket', 'polling'],
      pingTimeout: 60000,
      pingInterval: 25000,
      upgradeTimeout: 10000,
      allowEIO3: true,
    });

    // Setup Redis adapter for scaling
    const pubClient = redisClient.getRawClient();
    const subClient = pubClient.duplicate();
    await subClient.connect();

    io.adapter(createAdapter(pubClient, {
      streamName: 'socket.io-collaboration',
      maxLen: 10000,
      readCount: 100,
      heartbeatInterval: 5000,
      heartbeatTimeout: 10000,
    }));

    logger.info('Socket.IO Redis adapter configured');

    // Authentication middleware
    io.use(authenticateSocket);

    // Connection handling
    io.on('connection', (socket) => {
      const userId = socket.data.userId;
      const userEmail = socket.data.userEmail;

      logger.info('WebSocket connection established', {
        socketId: socket.id,
        userId,
        userEmail,
        connectTime: new Date().toISOString(),
      });

      // Apply comprehensive WebSocket security middleware
      setupWebSocketSecurity(socket);
      wsSecuritySummary(socket);

      // Setup event handlers
      setupSessionHandlers(io, socket);
      setupTeamHandlers(io, socket);
      setupAnalysisHandlers(io, socket);

      // Handle disconnection
      socket.on('disconnect', (reason) => {
        logger.info('WebSocket connection closed', {
          socketId: socket.id,
          userId,
          userEmail,
          reason,
          disconnectTime: new Date().toISOString(),
        });

        // Clean up user presence
        cleanupUserPresence(socket);
      });

      // Handle connection errors
      socket.on('error', (error) => {
        logger.error('WebSocket error', {
          socketId: socket.id,
          userId,
          error,
        });
      });

      // Send connection acknowledgment
      socket.emit('connected', {
        message: 'Connected to collaboration service',
        socketId: socket.id,
        userId,
        timestamp: new Date().toISOString(),
      });
    });

    // Global error handling
    io.on('error', (error) => {
      logger.error('Socket.IO server error', { error });
    });

    logger.info('Socket.IO server initialized successfully');
    return io;

  } catch (error) {
    logger.error('Failed to initialize Socket.IO server', { error });
    throw error;
  }
}

/**
 * Clean up user presence on disconnect
 */
async function cleanupUserPresence(socket: any): Promise<void> {
  try {
    const userId = socket.data.userId;
    const sessionRooms = Array.from(socket.rooms).filter((room): room is string => 
      typeof room === 'string' && room.startsWith('session:') && room !== socket.id
    );

    // Remove user from all session presence
    for (const room of sessionRooms) {
      const sessionId = room.replace('session:', '');
      
      // Remove from Redis presence set
      await redisClient.srem(`presence:${sessionId}`, userId);
      
      // Notify others in the session
      socket.to(room).emit('user_left', {
        userId,
        sessionId,
        timestamp: new Date().toISOString(),
      });
    }

    logger.debug('User presence cleaned up', { userId, sessionRooms });
  } catch (error) {
    logger.error('Error cleaning up user presence', { error });
  }
}

/**
 * Get active connections count
 */
export async function getActiveConnections(io: SocketIOServer): Promise<number> {
  try {
    const sockets = await io.fetchSockets();
    return sockets.length;
  } catch (error) {
    logger.error('Error fetching active connections', { error });
    return 0;
  }
}

/**
 * Get connections by session
 */
export async function getSessionConnections(
  io: SocketIOServer, 
  sessionId: string
): Promise<number> {
  try {
    const sockets = await io.in(`session:${sessionId}`).fetchSockets();
    return sockets.length;
  } catch (error) {
    logger.error('Error fetching session connections', { error, sessionId });
    return 0;
  }
}

/**
 * Broadcast to all connections
 */
export function broadcastToAll(
  io: SocketIOServer, 
  event: string, 
  data: any
): void {
  io.emit(event, data);
  logger.debug('Broadcast to all connections', { event, data });
}

/**
 * Broadcast to session
 */
export function broadcastToSession(
  io: SocketIOServer, 
  sessionId: string, 
  event: string, 
  data: any
): void {
  io.to(`session:${sessionId}`).emit(event, data);
  logger.debug('Broadcast to session', { sessionId, event, data });
}

/**
 * Broadcast to team
 */
export function broadcastToTeam(
  io: SocketIOServer, 
  teamId: string, 
  event: string, 
  data: any
): void {
  io.to(`team:${teamId}`).emit(event, data);
  logger.debug('Broadcast to team', { teamId, event, data });
}