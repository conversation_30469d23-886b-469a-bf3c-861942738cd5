/**
 * Team WebSocket Handler
 * 
 * Handles WebSocket events for team-related operations,
 * including team updates, member changes, and notifications.
 */

import { Server as SocketIOServer, Socket } from 'socket.io';
import { z } from 'zod';
import { redisClient } from '../clients/redis.client';
import { spannerClient } from '../clients/spanner.client';
import { logger } from '../utils/logger';

// Event payload schemas
const joinTeamSchema = z.object({
  teamId: z.string().uuid(),
});

const leaveTeamSchema = z.object({
  teamId: z.string().uuid(),
});

const teamNotificationSchema = z.object({
  teamId: z.string().uuid(),
  type: z.enum(['member_added', 'member_removed', 'session_started', 'session_ended', 'announcement']),
  message: z.string().min(1).max(500),
  metadata: z.record(z.any()).optional(),
});

/**
 * Setup team-related WebSocket handlers
 */
export function setupTeamHandlers(io: SocketIOServer, socket: Socket): void {
  const userId = socket.data.userId;
  const userEmail = socket.data.userEmail;

  /**
   * Join team room for real-time updates
   */
  socket.on('join_team', async (data: any) => {
    try {
      const { teamId } = joinTeamSchema.parse(data);
      
      // Verify team exists and user has access
      const team = await spannerClient.teams.get(teamId);
      if (!team) {
        socket.emit('error', {
          event: 'join_team',
          error: 'Team not found',
          code: 'TEAM_NOT_FOUND',
        });
        return;
      }

      // Check if user is a team member
      const members = await spannerClient.teams.getMembers(teamId);
      const isMember = members.some(member => member.user_id === userId);

      if (!isMember) {
        socket.emit('error', {
          event: 'join_team',
          error: 'Access denied',
          code: 'ACCESS_DENIED',
        });
        return;
      }

      // Join the team room
      await socket.join(`team:${teamId}`);

      // Update online members in Redis
      await redisClient.sadd(`team_online:${teamId}`, userId);

      // Notify other team members
      socket.to(`team:${teamId}`).emit('member_online', {
        userId,
        userEmail,
        userName: socket.data.userName,
        teamId,
        timestamp: new Date().toISOString(),
      });

      // Send team state to the joining user
      const onlineMembers = await redisClient.smembers(`team_online:${teamId}`);
      
      socket.emit('team_joined', {
        teamId,
        team,
        members,
        onlineMembers,
        timestamp: new Date().toISOString(),
      });

      logger.info('User joined team room', {
        userId,
        teamId,
        socketId: socket.id,
      });

    } catch (error) {
      logger.error('Error joining team', { error, userId, data });
      socket.emit('error', {
        event: 'join_team',
        error: 'Failed to join team',
        code: 'JOIN_TEAM_FAILED',
      });
    }
  });

  /**
   * Leave team room
   */
  socket.on('leave_team', async (data: any) => {
    try {
      const { teamId } = leaveTeamSchema.parse(data);

      // Leave the team room
      await socket.leave(`team:${teamId}`);

      // Remove from online members
      await redisClient.srem(`team_online:${teamId}`, userId);

      // Notify other team members
      socket.to(`team:${teamId}`).emit('member_offline', {
        userId,
        userEmail,
        teamId,
        timestamp: new Date().toISOString(),
      });

      socket.emit('team_left', {
        teamId,
        timestamp: new Date().toISOString(),
      });

      logger.info('User left team room', {
        userId,
        teamId,
        socketId: socket.id,
      });

    } catch (error) {
      logger.error('Error leaving team', { error, userId, data });
      socket.emit('error', {
        event: 'leave_team',
        error: 'Failed to leave team',
        code: 'LEAVE_TEAM_FAILED',
      });
    }
  });

  /**
   * Send team notification
   */
  socket.on('team_notification', async (data: any) => {
    try {
      const { teamId, type, message, metadata } = teamNotificationSchema.parse(data);

      // Verify user is a team member
      const members = await spannerClient.teams.getMembers(teamId);
      const isMember = members.some(member => member.user_id === userId);

      if (!isMember) {
        socket.emit('error', {
          event: 'team_notification',
          error: 'Access denied',
          code: 'ACCESS_DENIED',
        });
        return;
      }

      // Broadcast notification to all team members
      io.to(`team:${teamId}`).emit('notification_received', {
        userId,
        userEmail,
        userName: socket.data.userName,
        teamId,
        type,
        message,
        metadata,
        timestamp: new Date().toISOString(),
      });

      logger.info('Team notification sent', {
        userId,
        teamId,
        type,
        message,
      });

    } catch (error) {
      logger.error('Error sending team notification', { error, userId, data });
      socket.emit('error', {
        event: 'team_notification',
        error: 'Failed to send notification',
        code: 'NOTIFICATION_FAILED',
      });
    }
  });

  /**
   * Get team activity status
   */
  socket.on('get_team_activity', async (data: any) => {
    try {
      const { teamId } = joinTeamSchema.parse(data);

      // Verify user is a team member
      const members = await spannerClient.teams.getMembers(teamId);
      const isMember = members.some(member => member.user_id === userId);

      if (!isMember) {
        socket.emit('error', {
          event: 'get_team_activity',
          error: 'Access denied',
          code: 'ACCESS_DENIED',
        });
        return;
      }

      // Get team activity data
      const onlineMembers = await redisClient.smembers(`team_online:${teamId}`);
      const activeSessions = await spannerClient.sessions.listByTeam(teamId);

      socket.emit('team_activity', {
        teamId,
        onlineMembers,
        activeSessions,
        timestamp: new Date().toISOString(),
      });

    } catch (error) {
      logger.error('Error getting team activity', { error, userId, data });
      socket.emit('error', {
        event: 'get_team_activity',
        error: 'Failed to get team activity',
        code: 'ACTIVITY_FAILED',
      });
    }
  });

  /**
   * Handle disconnect - clean up team presence
   */
  socket.on('disconnect', async () => {
    try {
      // Get all team rooms the user was in
      const teamRooms = Array.from(socket.rooms).filter(room => 
        room.startsWith('team:') && room !== socket.id
      );

      // Clean up presence in all teams
      for (const room of teamRooms) {
        const teamId = room.replace('team:', '');
        
        // Remove from online members
        await redisClient.srem(`team_online:${teamId}`, userId);
        
        // Notify other team members
        socket.to(room).emit('member_offline', {
          userId,
          userEmail,
          teamId,
          timestamp: new Date().toISOString(),
        });
      }

      logger.debug('Team presence cleaned up on disconnect', {
        userId,
        teamRooms,
      });

    } catch (error) {
      logger.error('Error cleaning up team presence', { error, userId });
    }
  });
}