/**
 * Analysis WebSocket Handler
 * 
 * Handles WebSocket events for analysis sharing,
 * real-time analysis updates, and collaborative analysis sessions.
 */

import { Server as SocketIOServer, Socket } from 'socket.io';
import { z } from 'zod';
import { redisClient } from '../clients/redis.client';
import { firestoreClient } from '../clients/firestore.client';
import { spannerClient } from '../clients/spanner.client';
import { logger } from '../utils/logger';

// Event payload schemas
const shareAnalysisSchema = z.object({
  sessionId: z.string().uuid(),
  analysis: z.object({
    id: z.string(),
    type: z.enum(['code_analysis', 'pattern_detection', 'security_scan', 'performance_analysis']),
    title: z.string().min(1).max(200),
    results: z.record(z.any()),
    metadata: z.record(z.any()).optional(),
  }),
});

const analysisCommentSchema = z.object({
  sessionId: z.string().uuid(),
  analysisId: z.string(),
  comment: z.string().min(1).max(1000),
  lineNumber: z.number().optional(),
  selection: z.object({
    start: z.number(),
    end: z.number(),
  }).optional(),
});

const analysisReactionSchema = z.object({
  sessionId: z.string().uuid(),
  analysisId: z.string(),
  reaction: z.enum(['like', 'dislike', 'important', 'question']),
});

const liveAnalysisSchema = z.object({
  sessionId: z.string().uuid(),
  analysisId: z.string(),
  progress: z.number().min(0).max(100),
  status: z.enum(['starting', 'running', 'completed', 'failed']),
  currentStep: z.string().optional(),
  results: z.record(z.any()).optional(),
});

/**
 * Setup analysis-related WebSocket handlers
 */
export function setupAnalysisHandlers(io: SocketIOServer, socket: Socket): void {
  const userId = socket.data.userId;
  const userEmail = socket.data.userEmail;

  /**
   * Share analysis results in a session
   */
  socket.on('share_analysis', async (data: any) => {
    try {
      const { sessionId, analysis } = shareAnalysisSchema.parse(data);

      // Verify user is in the session
      const isInSession = await redisClient.sismember(`presence:${sessionId}`, userId);
      if (!isInSession) {
        socket.emit('error', {
          event: 'share_analysis',
          error: 'Not in session',
          code: 'NOT_IN_SESSION',
        });
        return;
      }

      // Store analysis in Firestore
      await firestoreClient.sessions.collection().doc(sessionId).collection('events').add({
        type: 'analysis_share',
        userId,
        userEmail,
        userName: socket.data.userName,
        analysis,
        timestamp: new Date(),
      });

      // Broadcast analysis to all session participants
      io.to(`session:${sessionId}`).emit('analysis_shared', {
        userId,
        userEmail,
        userName: socket.data.userName,
        sessionId,
        analysis,
        timestamp: new Date().toISOString(),
      });

      logger.info('Analysis shared in session', {
        userId,
        sessionId,
        analysisId: analysis.id,
        analysisType: analysis.type,
      });

    } catch (error) {
      logger.error('Error sharing analysis', { error, userId, data });
      socket.emit('error', {
        event: 'share_analysis',
        error: 'Failed to share analysis',
        code: 'SHARE_ANALYSIS_FAILED',
      });
    }
  });

  /**
   * Add comment to shared analysis
   */
  socket.on('analysis_comment', async (data: any) => {
    try {
      const { sessionId, analysisId, comment, lineNumber, selection } = analysisCommentSchema.parse(data);

      // Verify user is in the session
      const isInSession = await redisClient.sismember(`presence:${sessionId}`, userId);
      if (!isInSession) {
        socket.emit('error', {
          event: 'analysis_comment',
          error: 'Not in session',
          code: 'NOT_IN_SESSION',
        });
        return;
      }

      // Store comment in Firestore
      const commentId = await firestoreClient.messages.create(sessionId, {
        type: 'analysis_comment',
        userId,
        userEmail,
        userName: socket.data.userName,
        analysisId,
        comment,
        lineNumber,
        selection,
      });

      // Broadcast comment to all session participants
      io.to(`session:${sessionId}`).emit('analysis_comment_added', {
        id: commentId,
        userId,
        userEmail,
        userName: socket.data.userName,
        sessionId,
        analysisId,
        comment,
        lineNumber,
        selection,
        timestamp: new Date().toISOString(),
      });

      logger.debug('Analysis comment added', {
        commentId,
        userId,
        sessionId,
        analysisId,
      });

    } catch (error) {
      logger.error('Error adding analysis comment', { error, userId, data });
      socket.emit('error', {
        event: 'analysis_comment',
        error: 'Failed to add comment',
        code: 'COMMENT_FAILED',
      });
    }
  });

  /**
   * React to shared analysis
   */
  socket.on('analysis_reaction', async (data: any) => {
    try {
      const { sessionId, analysisId, reaction } = analysisReactionSchema.parse(data);

      // Verify user is in the session
      const isInSession = await redisClient.sismember(`presence:${sessionId}`, userId);
      if (!isInSession) {
        socket.emit('error', {
          event: 'analysis_reaction',
          error: 'Not in session',
          code: 'NOT_IN_SESSION',
        });
        return;
      }

      // Store reaction in Redis (for quick access)
      const reactionKey = `analysis_reactions:${analysisId}:${reaction}`;
      await redisClient.sadd(reactionKey, userId);

      // Also store in Firestore for persistence
      await firestoreClient.messages.create(sessionId, {
        type: 'analysis_reaction',
        userId,
        userEmail,
        userName: socket.data.userName,
        analysisId,
        reaction,
      });

      // Get reaction counts
      const reactionCounts = await getAnalysisReactionCounts(analysisId);

      // Broadcast reaction to all session participants
      io.to(`session:${sessionId}`).emit('analysis_reaction_added', {
        userId,
        userEmail,
        userName: socket.data.userName,
        sessionId,
        analysisId,
        reaction,
        reactionCounts,
        timestamp: new Date().toISOString(),
      });

      logger.debug('Analysis reaction added', {
        userId,
        sessionId,
        analysisId,
        reaction,
      });

    } catch (error) {
      logger.error('Error adding analysis reaction', { error, userId, data });
      socket.emit('error', {
        event: 'analysis_reaction',
        error: 'Failed to add reaction',
        code: 'REACTION_FAILED',
      });
    }
  });

  /**
   * Stream live analysis updates
   */
  socket.on('live_analysis_update', async (data: any) => {
    try {
      const { sessionId, analysisId, progress, status, currentStep, results } = liveAnalysisSchema.parse(data);

      // Verify user is in the session
      const isInSession = await redisClient.sismember(`presence:${sessionId}`, userId);
      if (!isInSession) {
        socket.emit('error', {
          event: 'live_analysis_update',
          error: 'Not in session',
          code: 'NOT_IN_SESSION',
        });
        return;
      }

      // Update analysis progress in Redis
      const progressKey = `analysis_progress:${analysisId}`;
      await redisClient.hset(progressKey, 'progress', progress.toString());
      await redisClient.hset(progressKey, 'status', status);
      await redisClient.hset(progressKey, 'currentStep', currentStep || '');
      await redisClient.hset(progressKey, 'updatedAt', new Date().toISOString());

      // Set expiration for progress data (1 hour)
      await redisClient.getRawClient().expire(progressKey, 3600);

      // Broadcast live update to all session participants
      io.to(`session:${sessionId}`).emit('live_analysis_update', {
        userId,
        sessionId,
        analysisId,
        progress,
        status,
        currentStep,
        results,
        timestamp: new Date().toISOString(),
      });

      logger.debug('Live analysis update sent', {
        userId,
        sessionId,
        analysisId,
        progress,
        status,
      });

    } catch (error) {
      logger.error('Error sending live analysis update', { error, userId, data });
      socket.emit('error', {
        event: 'live_analysis_update',
        error: 'Failed to send update',
        code: 'UPDATE_FAILED',
      });
    }
  });

  /**
   * Request analysis history for a session
   */
  socket.on('get_analysis_history', async (data: any) => {
    try {
      const { sessionId } = z.object({ sessionId: z.string().uuid() }).parse(data);

      // Verify user is in the session
      const isInSession = await redisClient.sismember(`presence:${sessionId}`, userId);
      if (!isInSession) {
        socket.emit('error', {
          event: 'get_analysis_history',
          error: 'Not in session',
          code: 'NOT_IN_SESSION',
        });
        return;
      }

      // Get analysis history from Firestore
      const messages = await firestoreClient.messages.list(sessionId, 100);
      const analysisHistory = messages.filter(msg => 
        msg.type === 'analysis_share' || msg.type === 'analysis_comment'
      );

      socket.emit('analysis_history', {
        sessionId,
        history: analysisHistory,
        timestamp: new Date().toISOString(),
      });

    } catch (error) {
      logger.error('Error getting analysis history', { error, userId, data });
      socket.emit('error', {
        event: 'get_analysis_history',
        error: 'Failed to get history',
        code: 'HISTORY_FAILED',
      });
    }
  });
}

/**
 * Get reaction counts for an analysis
 */
async function getAnalysisReactionCounts(analysisId: string): Promise<Record<string, number>> {
  const reactions = ['like', 'dislike', 'important', 'question'];
  const counts: Record<string, number> = {};

  for (const reaction of reactions) {
    const reactionKey = `analysis_reactions:${analysisId}:${reaction}`;
    const members = await redisClient.smembers(reactionKey);
    counts[reaction] = members.length;
  }

  return counts;
}