/**
 * Firestore Client Configuration
 * 
 * Firestore client for real-time data synchronization,
 * team data storage, and collaborative document management.
 */

import { Firestore, DocumentData, CollectionReference, DocumentReference } from '@google-cloud/firestore';
import { appConfig } from '../config';
import { logger } from '../utils/logger';

/**
 * Firestore client wrapper with typed collections
 */
export class FirestoreClient {
  private db: Firestore;
  private initialized = false;

  constructor() {
    this.db = new Firestore({
      projectId: appConfig.gcp.projectId,
      databaseId: appConfig.gcp.firestore.databaseId,
    });
  }

  /**
   * Initialize Firestore client
   */
  async initialize(): Promise<void> {
    try {
      // Test connection
      await this.db.collection('_healthcheck').limit(1).get();
      this.initialized = true;
      logger.info('Firestore client initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize Firestore client', { error });
      throw error;
    }
  }

  /**
   * Close Firestore client
   */
  async close(): Promise<void> {
    if (this.initialized) {
      await this.db.terminate();
      this.initialized = false;
      logger.info('Firestore client closed');
    }
  }

  /**
   * Get raw Firestore instance
   */
  getRawClient(): Firestore {
    return this.db;
  }

  /**
   * Get collection reference
   */
  collection(collectionPath: string): CollectionReference<DocumentData> {
    return this.db.collection(collectionPath);
  }

  /**
   * Get document reference
   */
  doc(documentPath: string): DocumentReference<DocumentData> {
    return this.db.doc(documentPath);
  }

  /**
   * Teams collection methods
   */
  get teams() {
    const db = this.db;
    return {
      collection: () => db.collection('teams'),
      doc: (teamId: string) => db.doc(`teams/${teamId}`),
      
      async create(teamData: any) {
        const docRef = db.collection('teams').doc();
        await docRef.set({
          ...teamData,
          id: docRef.id,
          createdAt: new Date(),
          updatedAt: new Date(),
        });
        return docRef.id;
      },

      async get(teamId: string) {
        const doc = await db.doc(`teams/${teamId}`).get();
        return doc.exists ? doc.data() : null;
      },

      async update(teamId: string, updates: any) {
        await db.doc(`teams/${teamId}`).update({
          ...updates,
          updatedAt: new Date(),
        });
      },

      async delete(teamId: string) {
        await db.doc(`teams/${teamId}`).delete();
      },

      async listByUser(userId: string) {
        const snapshot = await db.collection('teams')
          .where('members', 'array-contains', userId)
          .get();
        return snapshot.docs.map(doc => doc.data());
      },
    };
  }

  /**
   * Sessions collection methods
   */
  get sessions() {
    const db = this.db;
    return {
      collection: () => db.collection('sessions'),
      doc: (sessionId: string) => db.doc(`sessions/${sessionId}`),
      
      async create(sessionData: any) {
        const docRef = db.collection('sessions').doc();
        await docRef.set({
          ...sessionData,
          id: docRef.id,
          createdAt: new Date(),
          updatedAt: new Date(),
          status: 'active',
        });
        return docRef.id;
      },

      async get(sessionId: string) {
        const doc = await db.doc(`sessions/${sessionId}`).get();
        return doc.exists ? doc.data() : null;
      },

      async update(sessionId: string, updates: any) {
        await db.doc(`sessions/${sessionId}`).update({
          ...updates,
          updatedAt: new Date(),
        });
      },

      async end(sessionId: string) {
        await db.doc(`sessions/${sessionId}`).update({
          status: 'ended',
          endedAt: new Date(),
          updatedAt: new Date(),
        });
      },

      async listByTeam(teamId: string) {
        const snapshot = await db.collection('sessions')
          .where('teamId', '==', teamId)
          .where('status', '==', 'active')
          .get();
        return snapshot.docs.map(doc => doc.data());
      },
    };
  }

  /**
   * Messages collection methods
   */
  get messages() {
    const db = this.db;
    return {
      collection: (sessionId: string) => db.collection(`sessions/${sessionId}/messages`),
      
      async create(sessionId: string, messageData: any) {
        const docRef = db.collection(`sessions/${sessionId}/messages`).doc();
        await docRef.set({
          ...messageData,
          id: docRef.id,
          createdAt: new Date(),
        });
        return docRef.id;
      },

      async list(sessionId: string, limit = 50) {
        const snapshot = await db.collection(`sessions/${sessionId}/messages`)
          .orderBy('createdAt', 'desc')
          .limit(limit)
          .get();
        return snapshot.docs.map(doc => doc.data());
      },

      onSnapshot(sessionId: string, callback: (messages: any[]) => void) {
        return db.collection(`sessions/${sessionId}/messages`)
          .orderBy('createdAt', 'desc')
          .limit(50)
          .onSnapshot(snapshot => {
            const messages = snapshot.docs.map(doc => doc.data());
            callback(messages);
          });
      },
    };
  }

  /**
   * User presence methods
   */
  get presence() {
    const db = this.db;
    return {
      async set(sessionId: string, userId: string, presenceData: any) {
        await db.doc(`sessions/${sessionId}/presence/${userId}`).set({
          ...presenceData,
          userId,
          sessionId,
          lastSeen: new Date(),
        });
      },

      async remove(sessionId: string, userId: string) {
        await db.doc(`sessions/${sessionId}/presence/${userId}`).delete();
      },

      onSnapshot(sessionId: string, callback: (presence: any[]) => void) {
        return db.collection(`sessions/${sessionId}/presence`)
          .onSnapshot(snapshot => {
            const presence = snapshot.docs.map(doc => doc.data());
            callback(presence);
          });
      },
    };
  }

  /**
   * Batch operations
   */
  batch() {
    return this.db.batch();
  }

  /**
   * Transaction operations
   */
  async runTransaction<T>(callback: (transaction: any) => Promise<T>): Promise<T> {
    return this.db.runTransaction(callback);
  }
}

// Export singleton instance
export const firestoreClient = new FirestoreClient();