/**
 * Spanner Client Configuration
 * 
 * Spanner client for operational data storage,
 * team metadata, and transactional operations.
 */

import { Spanner, Database, Transaction } from '@google-cloud/spanner';
import { appConfig } from '../config';
import { logger } from '../utils/logger';

/**
 * Spanner client wrapper with typed operations
 */
export class SpannerClient {
  private spanner: Spanner;
  private database: Database;
  private initialized = false;

  constructor() {
    this.spanner = new Spanner({
      projectId: appConfig.gcp.projectId,
    });
    
    this.database = this.spanner
      .instance(appConfig.gcp.spanner.instanceId)
      .database(appConfig.gcp.spanner.databaseId);
  }

  /**
   * Initialize Spanner client
   */
  async initialize(): Promise<void> {
    try {
      // Test connection
      await this.database.run('SELECT 1');
      this.initialized = true;
      logger.info('Spanner client initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize Spanner client', { error });
      throw error;
    }
  }

  /**
   * Close Spanner client
   */
  async close(): Promise<void> {
    if (this.initialized) {
      await this.spanner.close();
      this.initialized = false;
      logger.info('Spanner client closed');
    }
  }

  /**
   * Get raw Spanner database instance
   */
  getRawClient(): Database {
    return this.database;
  }

  /**
   * Execute a query
   */
  async query(sql: string, params?: any): Promise<any[]> {
    try {
      const [rows] = await this.database.run({
        sql,
        params,
      });
      return rows.map(row => row.toJSON());
    } catch (error) {
      logger.error('Spanner query error', { sql, params, error });
      throw error;
    }
  }

  /**
   * Execute a DML statement
   */
  async executeDml(sql: string, params?: any): Promise<number> {
    try {
      const [rowCount] = await this.database.runUpdate({
        sql,
        params,
      });
      return rowCount;
    } catch (error) {
      logger.error('Spanner DML error', { sql, params, error });
      throw error;
    }
  }

  /**
   * Run a transaction
   */
  async runTransaction<T>(callback: (transaction: Transaction) => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      this.database.runTransaction((err, transaction) => {
        if (err) {
          reject(err);
          return;
        }
        if (!transaction) {
          reject(new Error('Transaction is null'));
          return;
        }
        callback(transaction)
          .then(result => resolve(result))
          .catch(error => reject(error));
      });
    });
  }

  /**
   * Team operations
   */
  get teams() {
    const client = this;
    return {
      async create(teamData: any): Promise<string> {
        const sql = `
          INSERT INTO teams (id, name, description, owner_id, created_at, updated_at)
          VALUES (@id, @name, @description, @ownerId, @createdAt, @updatedAt)
        `;
        
        const params = {
          id: teamData.id,
          name: teamData.name,
          description: teamData.description || null,
          ownerId: teamData.ownerId,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };

        await client.executeDml(sql, params);
        return teamData.id;
      },

      async get(teamId: string): Promise<any> {
        const sql = `
          SELECT * FROM teams WHERE id = @teamId
        `;
        const results = await client.query(sql, { teamId });
        return results[0] || null;
      },

      async update(teamId: string, updates: any): Promise<void> {
        const sql = `
          UPDATE teams 
          SET name = @name, description = @description, updated_at = @updatedAt
          WHERE id = @teamId
        `;
        
        const params = {
          teamId,
          name: updates.name,
          description: updates.description,
          updatedAt: new Date().toISOString(),
        };

        await client.executeDml(sql, params);
      },

      async delete(teamId: string): Promise<void> {
        const sql = `DELETE FROM teams WHERE id = @teamId`;
        await client.executeDml(sql, { teamId });
      },

      async listByUser(userId: string): Promise<any[]> {
        const sql = `
          SELECT t.* FROM teams t
          JOIN team_members tm ON t.id = tm.team_id
          WHERE tm.user_id = @userId
        `;
        return client.query(sql, { userId });
      },

      async addMember(teamId: string, userId: string, role = 'member'): Promise<void> {
        const sql = `
          INSERT INTO team_members (team_id, user_id, role, joined_at)
          VALUES (@teamId, @userId, @role, @joinedAt)
        `;
        
        const params = {
          teamId,
          userId,
          role,
          joinedAt: new Date().toISOString(),
        };

        await client.executeDml(sql, params);
      },

      async removeMember(teamId: string, userId: string): Promise<void> {
        const sql = `
          DELETE FROM team_members 
          WHERE team_id = @teamId AND user_id = @userId
        `;
        await client.executeDml(sql, { teamId, userId });
      },

      async getMembers(teamId: string): Promise<any[]> {
        const sql = `
          SELECT tm.*, u.email, u.name FROM team_members tm
          JOIN users u ON tm.user_id = u.id
          WHERE tm.team_id = @teamId
        `;
        return client.query(sql, { teamId });
      },
    };
  }

  /**
   * Session operations
   */
  get sessions() {
    const client = this;
    return {
      async create(sessionData: any): Promise<string> {
        const sql = `
          INSERT INTO sessions (id, team_id, name, type, status, created_by, created_at, updated_at)
          VALUES (@id, @teamId, @name, @type, @status, @createdBy, @createdAt, @updatedAt)
        `;
        
        const params = {
          id: sessionData.id,
          teamId: sessionData.teamId,
          name: sessionData.name,
          type: sessionData.type || 'analysis',
          status: 'active',
          createdBy: sessionData.createdBy,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };

        await client.executeDml(sql, params);
        return sessionData.id;
      },

      async get(sessionId: string): Promise<any> {
        const sql = `
          SELECT * FROM sessions WHERE id = @sessionId
        `;
        const results = await client.query(sql, { sessionId });
        return results[0] || null;
      },

      async update(sessionId: string, updates: any): Promise<void> {
        const sql = `
          UPDATE sessions 
          SET name = @name, status = @status, updated_at = @updatedAt
          WHERE id = @sessionId
        `;
        
        const params = {
          sessionId,
          name: updates.name,
          status: updates.status,
          updatedAt: new Date().toISOString(),
        };

        await client.executeDml(sql, params);
      },

      async end(sessionId: string): Promise<void> {
        const sql = `
          UPDATE sessions 
          SET status = 'ended', ended_at = @endedAt, updated_at = @updatedAt
          WHERE id = @sessionId
        `;
        
        const params = {
          sessionId,
          endedAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };

        await client.executeDml(sql, params);
      },

      async listByTeam(teamId: string): Promise<any[]> {
        const sql = `
          SELECT * FROM sessions 
          WHERE team_id = @teamId AND status = 'active'
          ORDER BY created_at DESC
        `;
        return client.query(sql, { teamId });
      },
    };
  }

  /**
   * User operations
   */
  get users() {
    const client = this;
    return {
      async create(userData: any): Promise<string> {
        const sql = `
          INSERT INTO users (id, email, name, created_at, updated_at)
          VALUES (@id, @email, @name, @createdAt, @updatedAt)
        `;
        
        const params = {
          id: userData.id,
          email: userData.email,
          name: userData.name,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };

        await client.executeDml(sql, params);
        return userData.id;
      },

      async get(userId: string): Promise<any> {
        const sql = `
          SELECT * FROM users WHERE id = @userId
        `;
        const results = await client.query(sql, { userId });
        return results[0] || null;
      },

      async getByEmail(email: string): Promise<any> {
        const sql = `
          SELECT * FROM users WHERE email = @email
        `;
        const results = await client.query(sql, { email });
        return results[0] || null;
      },

      async update(userId: string, updates: any): Promise<void> {
        const sql = `
          UPDATE users 
          SET name = @name, updated_at = @updatedAt
          WHERE id = @userId
        `;
        
        const params = {
          userId,
          name: updates.name,
          updatedAt: new Date().toISOString(),
        };

        await client.executeDml(sql, params);
      },
    };
  }

  /**
   * Health check
   */
  async healthCheck(): Promise<boolean> {
    try {
      await this.query('SELECT 1 as health');
      return true;
    } catch (error) {
      logger.error('Spanner health check failed', { error });
      return false;
    }
  }
}

// Export singleton instance
export const spannerClient = new SpannerClient();