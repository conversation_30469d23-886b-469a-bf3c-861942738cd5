/**
 * WebSocket Client SDK
 * 
 * TypeScript client for connecting to the collaboration service
 * with automatic reconnection, event handling, and type safety.
 */

import { io, Socket } from 'socket.io-client';

// Event types
export interface SessionEvent {
  sessionId: string;
  userId: string;
  userEmail: string;
  userName?: string;
  timestamp: string;
}

export interface MessageEvent extends SessionEvent {
  id: string;
  message: string;
  type: 'text' | 'system' | 'analysis_share';
  metadata?: Record<string, any>;
}

export interface AnalysisEvent extends SessionEvent {
  analysis: {
    id: string;
    type: 'code_analysis' | 'pattern_detection' | 'security_scan' | 'performance_analysis';
    title: string;
    results: Record<string, any>;
    metadata?: Record<string, any>;
  };
}

export interface CursorEvent extends SessionEvent {
  position: {
    x: number;
    y: number;
    elementId?: string;
  };
}

export interface PresenceEvent extends SessionEvent {
  participants: string[];
}

// Connection states
export enum ConnectionState {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  RECONNECTING = 'reconnecting',
  ERROR = 'error',
}

// Client configuration
export interface ClientConfig {
  url: string;
  token: string;
  autoReconnect?: boolean;
  reconnectDelay?: number;
  maxReconnectAttempts?: number;
  timeout?: number;
}

// Event handlers
export interface EventHandlers {
  onConnect?: () => void;
  onDisconnect?: (reason: string) => void;
  onError?: (error: Error) => void;
  onReconnect?: () => void;
  onConnectionStateChange?: (state: ConnectionState) => void;
  
  // Session events
  onUserJoined?: (event: SessionEvent) => void;
  onUserLeft?: (event: SessionEvent) => void;
  onMessageReceived?: (event: MessageEvent) => void;
  onAnalysisShared?: (event: AnalysisEvent) => void;
  onCursorUpdate?: (event: CursorEvent) => void;
  onPresenceUpdate?: (event: PresenceEvent) => void;
  
  // Team events
  onMemberOnline?: (event: SessionEvent) => void;
  onMemberOffline?: (event: SessionEvent) => void;
  onNotificationReceived?: (event: any) => void;
}

/**
 * WebSocket Client for Collaboration Service
 */
export class CollaborationClient {
  private socket: Socket | null = null;
  private config: ClientConfig;
  private handlers: EventHandlers = {};
  private reconnectAttempts = 0;
  private connectionState = ConnectionState.DISCONNECTED;
  private heartbeatInterval: NodeJS.Timeout | null = null;

  constructor(config: ClientConfig) {
    this.config = {
      autoReconnect: true,
      reconnectDelay: 1000,
      maxReconnectAttempts: 5,
      timeout: 10000,
      ...config,
    };
  }

  /**
   * Connect to the collaboration service
   */
  async connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.updateConnectionState(ConnectionState.CONNECTING);

      this.socket = io(this.config.url, {
        auth: {
          token: this.config.token,
        },
        transports: ['websocket', 'polling'],
        timeout: this.config.timeout,
        autoConnect: false,
      });

      this.setupEventHandlers();

      this.socket.on('connect', () => {
        this.updateConnectionState(ConnectionState.CONNECTED);
        this.reconnectAttempts = 0;
        this.startHeartbeat();
        this.handlers.onConnect?.();
        resolve();
      });

      this.socket.on('connect_error', (error) => {
        this.updateConnectionState(ConnectionState.ERROR);
        this.handlers.onError?.(error);
        reject(error);
      });

      this.socket.connect();
    });
  }

  /**
   * Disconnect from the service
   */
  disconnect(): void {
    this.stopHeartbeat();
    
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    
    this.updateConnectionState(ConnectionState.DISCONNECTED);
  }

  /**
   * Register event handlers
   */
  on(handlers: Partial<EventHandlers>): void {
    this.handlers = { ...this.handlers, ...handlers };
  }

  /**
   * Join a collaboration session
   */
  async joinSession(sessionId: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.socket) {
        reject(new Error('Not connected'));
        return;
      }

      this.socket.emit('join_session', { sessionId });
      
      this.socket.once('session_joined', () => {
        resolve();
      });

      this.socket.once('error', (error) => {
        reject(new Error(error.error || 'Failed to join session'));
      });
    });
  }

  /**
   * Leave a collaboration session
   */
  async leaveSession(sessionId: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.socket) {
        reject(new Error('Not connected'));
        return;
      }

      this.socket.emit('leave_session', { sessionId });
      
      this.socket.once('session_left', () => {
        resolve();
      });

      this.socket.once('error', (error) => {
        reject(new Error(error.error || 'Failed to leave session'));
      });
    });
  }

  /**
   * Send a message in a session
   */
  sendMessage(sessionId: string, message: string, type: 'text' | 'system' | 'analysis_share' = 'text'): void {
    if (!this.socket) {
      throw new Error('Not connected');
    }

    this.socket.emit('session_message', {
      sessionId,
      message,
      type,
    });
  }

  /**
   * Update cursor position
   */
  updateCursor(sessionId: string, position: { x: number; y: number; elementId?: string }): void {
    if (!this.socket) {
      return;
    }

    this.socket.emit('cursor_position', {
      sessionId,
      position,
    });
  }

  /**
   * Share analysis results
   */
  shareAnalysis(sessionId: string, analysis: any): void {
    if (!this.socket) {
      throw new Error('Not connected');
    }

    this.socket.emit('share_analysis', {
      sessionId,
      analysis,
    });
  }

  /**
   * Join team room
   */
  async joinTeam(teamId: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.socket) {
        reject(new Error('Not connected'));
        return;
      }

      this.socket.emit('join_team', { teamId });
      
      this.socket.once('team_joined', () => {
        resolve();
      });

      this.socket.once('error', (error) => {
        reject(new Error(error.error || 'Failed to join team'));
      });
    });
  }

  /**
   * Get connection state
   */
  getConnectionState(): ConnectionState {
    return this.connectionState;
  }

  /**
   * Check if connected
   */
  isConnected(): boolean {
    return this.connectionState === ConnectionState.CONNECTED;
  }

  /**
   * Setup event handlers
   */
  private setupEventHandlers(): void {
    if (!this.socket) return;

    // Connection events
    this.socket.on('disconnect', (reason) => {
      this.updateConnectionState(ConnectionState.DISCONNECTED);
      this.stopHeartbeat();
      this.handlers.onDisconnect?.(reason);
      
      if (this.config.autoReconnect && this.reconnectAttempts < this.config.maxReconnectAttempts!) {
        this.reconnect();
      }
    });

    this.socket.on('reconnect', () => {
      this.updateConnectionState(ConnectionState.CONNECTED);
      this.startHeartbeat();
      this.handlers.onReconnect?.();
    });

    // Session events
    if (this.handlers.onUserJoined) {
      this.socket.on('user_joined', this.handlers.onUserJoined);
    }
    if (this.handlers.onUserLeft) {
      this.socket.on('user_left', this.handlers.onUserLeft);
    }
    if (this.handlers.onMessageReceived) {
      this.socket.on('message_received', this.handlers.onMessageReceived);
    }
    if (this.handlers.onAnalysisShared) {
      this.socket.on('analysis_shared', this.handlers.onAnalysisShared);
    }
    if (this.handlers.onCursorUpdate) {
      this.socket.on('cursor_update', this.handlers.onCursorUpdate);
    }
    if (this.handlers.onPresenceUpdate) {
      this.socket.on('session_synced', this.handlers.onPresenceUpdate);
    }

    // Team events
    if (this.handlers.onMemberOnline) {
      this.socket.on('member_online', this.handlers.onMemberOnline);
    }
    if (this.handlers.onMemberOffline) {
      this.socket.on('member_offline', this.handlers.onMemberOffline);
    }
    if (this.handlers.onNotificationReceived) {
      this.socket.on('notification_received', this.handlers.onNotificationReceived);
    }
  }

  /**
   * Reconnect to the service
   */
  private reconnect(): void {
    this.reconnectAttempts++;
    this.updateConnectionState(ConnectionState.RECONNECTING);
    
    setTimeout(() => {
      this.connect().catch(() => {
        // Reconnection failed, will try again if attempts remaining
      });
    }, this.config.reconnectDelay! * this.reconnectAttempts);
  }

  /**
   * Update connection state
   */
  private updateConnectionState(state: ConnectionState): void {
    this.connectionState = state;
    this.handlers.onConnectionStateChange?.(state);
  }

  /**
   * Start heartbeat to keep connection alive
   */
  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      if (this.socket) {
        this.socket.emit('ping');
      }
    }, 30000); // 30 seconds
  }

  /**
   * Stop heartbeat
   */
  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }
}