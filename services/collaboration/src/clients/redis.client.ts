/**
 * Redis Client
 * 
 * Enhanced Redis client with cluster support, connection pooling,
 * performance monitoring, and advanced caching strategies.
 */

import Redis, { Cluster } from 'ioredis';
import { logger } from '../utils/logger';
import { appConfig } from '../config';
import { redisConfig, getCacheConfig, getStreamConfig, getPubSubConfig } from '../config/redis.config';
import { trackRedisOperation } from '../middleware/performance.middleware';
import { performance } from 'perf_hooks';

// Performance tracking interface
interface OperationMetrics {
  operation: string;
  duration: number;
  success: boolean;
  error?: string;
}

class RedisClient {
  private client: Redis | Cluster;
  private subscriber: Redis | Cluster;
  private publisher: Redis | Cluster;
  private isClusterMode: boolean;
  private connectionPool: Map<string, Redis> = new Map();
  private operationMetrics: OperationMetrics[] = [];
  private connected = false;

  constructor() {
    this.isClusterMode = appConfig.redis.cluster?.enabled || false;
    
    if (this.isClusterMode) {
      this.client = new Cluster(redisConfig.clusterNodes, redisConfig.cluster);
      this.subscriber = new Cluster(redisConfig.clusterNodes, redisConfig.cluster);
      this.publisher = new Cluster(redisConfig.clusterNodes, redisConfig.cluster);
    } else {
      this.client = new Redis(redisConfig.client);
      this.subscriber = new Redis(redisConfig.client);
      this.publisher = new Redis(redisConfig.client);
    }

    this.setupEventHandlers();
    this.setupPerformanceMonitoring();
  }

  private setupEventHandlers(): void {
    // Main client events
    this.client.on('connect', () => {
      logger.info('Redis client connected', { cluster: this.isClusterMode });
      this.connected = true;
    });

    this.client.on('error', (error) => {
      logger.error('Redis client error', { error, cluster: this.isClusterMode });
      this.connected = false;
    });

    this.client.on('close', () => {
      logger.warn('Redis client connection closed', { cluster: this.isClusterMode });
      this.connected = false;
    });

    this.client.on('reconnecting', () => {
      logger.info('Redis client reconnecting', { cluster: this.isClusterMode });
    });

    // Subscriber events
    this.subscriber.on('connect', () => {
      logger.info('Redis subscriber connected', { cluster: this.isClusterMode });
    });

    this.subscriber.on('error', (error) => {
      logger.error('Redis subscriber error', { error, cluster: this.isClusterMode });
    });

    // Publisher events
    this.publisher.on('connect', () => {
      logger.info('Redis publisher connected', { cluster: this.isClusterMode });
    });

    this.publisher.on('error', (error) => {
      logger.error('Redis publisher error', { error, cluster: this.isClusterMode });
    });

    // Cluster-specific events
    if (this.isClusterMode) {
      const clusterClient = this.client as Cluster;
      
      clusterClient.on('ready', () => {
        logger.info('Redis cluster ready');
      });

      clusterClient.on('node error', (error, node) => {
        logger.error('Redis cluster node error', { error, node: node.options });
      });

      clusterClient.on('+node', (node) => {
        logger.info('Redis cluster node added', { node: node.options });
      });

      clusterClient.on('-node', (node) => {
        logger.info('Redis cluster node removed', { node: node.options });
      });
    }
  }

  private setupPerformanceMonitoring(): void {
    // Performance monitoring is now handled per operation
    // instead of monkey-patching the client
  }

  async connect(): Promise<void> {
    try {
      await Promise.all([
        this.client.connect(),
        this.subscriber.connect(),
        this.publisher.connect(),
      ]);
      
      this.connected = true;
      logger.info('All Redis connections established', { cluster: this.isClusterMode });
      
      // Test connection
      await this.ping();
    } catch (error) {
      logger.error('Failed to connect to Redis', { error, cluster: this.isClusterMode });
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    try {
      await Promise.all([
        this.client.disconnect(),
        this.subscriber.disconnect(),
        this.publisher.disconnect(),
      ]);
      
      // Close connection pool
      for (const [key, connection] of Array.from(this.connectionPool.entries())) {
        await connection.disconnect();
        this.connectionPool.delete(key);
      }
      
      this.connected = false;
      logger.info('All Redis connections closed', { cluster: this.isClusterMode });
    } catch (error) {
      logger.error('Error disconnecting from Redis', { error });
    }
  }

  // Enhanced basic operations with caching strategies
  async get(key: string, strategy?: keyof typeof redisConfig.cache): Promise<string | null> {
    try {
      const start = performance.now();
      const result = await this.client.get(key);
      
      this.trackOperation('GET', performance.now() - start, true);
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.trackOperation('GET', 0, false, errorMessage);
      logger.error('Redis GET error', { error: errorMessage, key });
      throw error;
    }
  }

  async set(key: string, value: string, ttl?: number, strategy?: keyof typeof redisConfig.cache): Promise<void> {
    try {
      const start = performance.now();
      
      if (strategy) {
        const cacheConfig = getCacheConfig(strategy);
        const prefixedKey = `${cacheConfig.prefix}${key}`;
        const cacheTtl = ttl || cacheConfig.ttl;
        
        await this.client.setex(prefixedKey, cacheTtl, value);
      } else if (ttl) {
        await this.client.setex(key, ttl, value);
      } else {
        await this.client.set(key, value);
      }
      
      this.trackOperation('SET', performance.now() - start, true);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.trackOperation('SET', 0, false, errorMessage);
      logger.error('Redis SET error', { error: errorMessage, key, strategy });
      throw error;
    }
  }

  async del(key: string): Promise<number> {
    try {
      const start = performance.now();
      const result = await this.client.del(key);
      
      this.trackOperation('DEL', performance.now() - start, true);
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.trackOperation('DEL', 0, false, errorMessage);
      logger.error('Redis DEL error', { error: errorMessage, key });
      throw error;
    }
  }

  async exists(key: string): Promise<number> {
    try {
      const start = performance.now();
      const result = await this.client.exists(key);
      
      this.trackOperation('EXISTS', performance.now() - start, true);
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.trackOperation('EXISTS', 0, false, errorMessage);
      logger.error('Redis EXISTS error', { error: errorMessage, key });
      throw error;
    }
  }

  async expire(key: string, ttl: number): Promise<number> {
    try {
      const start = performance.now();
      const result = await this.client.expire(key, ttl);
      
      this.trackOperation('EXPIRE', performance.now() - start, true);
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.trackOperation('EXPIRE', 0, false, errorMessage);
      logger.error('Redis EXPIRE error', { error: errorMessage, key });
      throw error;
    }
  }

  async ttl(key: string): Promise<number> {
    try {
      const start = performance.now();
      const result = await this.client.ttl(key);
      
      this.trackOperation('TTL', performance.now() - start, true);
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.trackOperation('TTL', 0, false, errorMessage);
      logger.error('Redis TTL error', { error: errorMessage, key });
      throw error;
    }
  }

  // Hash operations
  async hget(key: string, field: string): Promise<string | null> {
    try {
      const start = performance.now();
      const result = await this.client.hget(key, field);
      
      this.trackOperation('HGET', performance.now() - start, true);
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.trackOperation('HGET', 0, false, errorMessage);
      logger.error('Redis HGET error', { error: errorMessage, key, field });
      throw error;
    }
  }

  async hset(key: string, field: string, value: string): Promise<number> {
    try {
      const start = performance.now();
      const result = await this.client.hset(key, field, value);
      
      this.trackOperation('HSET', performance.now() - start, true);
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.trackOperation('HSET', 0, false, errorMessage);
      logger.error('Redis HSET error', { error: errorMessage, key, field });
      throw error;
    }
  }

  async hgetall(key: string): Promise<Record<string, string>> {
    try {
      const start = performance.now();
      const result = await this.client.hgetall(key);
      
      this.trackOperation('HGETALL', performance.now() - start, true);
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.trackOperation('HGETALL', 0, false, errorMessage);
      logger.error('Redis HGETALL error', { error: errorMessage, key });
      throw error;
    }
  }

  async hdel(key: string, field: string): Promise<number> {
    try {
      const start = performance.now();
      const result = await this.client.hdel(key, field);
      
      this.trackOperation('HDEL', performance.now() - start, true);
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.trackOperation('HDEL', 0, false, errorMessage);
      logger.error('Redis HDEL error', { error: errorMessage, key, field });
      throw error;
    }
  }

  // List operations
  async lpush(key: string, value: string): Promise<number> {
    try {
      const start = performance.now();
      const result = await this.client.lpush(key, value);
      
      this.trackOperation('LPUSH', performance.now() - start, true);
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.trackOperation('LPUSH', 0, false, errorMessage);
      logger.error('Redis LPUSH error', { error: errorMessage, key });
      throw error;
    }
  }

  async rpush(key: string, value: string): Promise<number> {
    try {
      const start = performance.now();
      const result = await this.client.rpush(key, value);
      
      this.trackOperation('RPUSH', performance.now() - start, true);
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.trackOperation('RPUSH', 0, false, errorMessage);
      logger.error('Redis RPUSH error', { error: errorMessage, key });
      throw error;
    }
  }

  async lpop(key: string): Promise<string | null> {
    try {
      const start = performance.now();
      const result = await this.client.lpop(key);
      
      this.trackOperation('LPOP', performance.now() - start, true);
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.trackOperation('LPOP', 0, false, errorMessage);
      logger.error('Redis LPOP error', { error: errorMessage, key });
      throw error;
    }
  }

  async rpop(key: string): Promise<string | null> {
    try {
      const start = performance.now();
      const result = await this.client.rpop(key);
      
      this.trackOperation('RPOP', performance.now() - start, true);
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.trackOperation('RPOP', 0, false, errorMessage);
      logger.error('Redis RPOP error', { error: errorMessage, key });
      throw error;
    }
  }

  async lrange(key: string, start: number, stop: number): Promise<string[]> {
    try {
      const opStart = performance.now();
      const result = await this.client.lrange(key, start, stop);
      
      this.trackOperation('LRANGE', performance.now() - opStart, true);
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.trackOperation('LRANGE', 0, false, errorMessage);
      logger.error('Redis LRANGE error', { error: errorMessage, key, start, stop });
      throw error;
    }
  }

  // Set operations
  async sadd(key: string, member: string): Promise<number> {
    try {
      const start = performance.now();
      const result = await this.client.sadd(key, member);
      
      this.trackOperation('SADD', performance.now() - start, true);
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.trackOperation('SADD', 0, false, errorMessage);
      logger.error('Redis SADD error', { error: errorMessage, key, member });
      throw error;
    }
  }

  async srem(key: string, member: string): Promise<number> {
    try {
      const start = performance.now();
      const result = await this.client.srem(key, member);
      
      this.trackOperation('SREM', performance.now() - start, true);
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.trackOperation('SREM', 0, false, errorMessage);
      logger.error('Redis SREM error', { error: errorMessage, key, member });
      throw error;
    }
  }

  async smembers(key: string): Promise<string[]> {
    try {
      const start = performance.now();
      const result = await this.client.smembers(key);
      
      this.trackOperation('SMEMBERS', performance.now() - start, true);
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.trackOperation('SMEMBERS', 0, false, errorMessage);
      logger.error('Redis SMEMBERS error', { error: errorMessage, key });
      throw error;
    }
  }

  async sismember(key: string, member: string): Promise<number> {
    try {
      const start = performance.now();
      const result = await this.client.sismember(key, member);
      
      this.trackOperation('SISMEMBER', performance.now() - start, true);
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.trackOperation('SISMEMBER', 0, false, errorMessage);
      logger.error('Redis SISMEMBER error', { error: errorMessage, key, member });
      throw error;
    }
  }

  // Pub/Sub operations
  async publish(channel: string, message: string): Promise<number> {
    try {
      const start = performance.now();
      const result = await this.publisher.publish(channel, message);
      
      this.trackOperation('PUBLISH', performance.now() - start, true);
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.trackOperation('PUBLISH', 0, false, errorMessage);
      logger.error('Redis PUBLISH error', { error: errorMessage, channel });
      throw error;
    }
  }

  async subscribe(channel: string, callback: (message: string) => void): Promise<void> {
    try {
      await this.subscriber.subscribe(channel);
      this.subscriber.on('message', (receivedChannel, message) => {
        if (receivedChannel === channel) {
          callback(message);
        }
      });
    } catch (error) {
      logger.error('Redis SUBSCRIBE error', { error, channel });
      throw error;
    }
  }

  async unsubscribe(channel: string): Promise<void> {
    try {
      await this.subscriber.unsubscribe(channel);
    } catch (error) {
      logger.error('Redis UNSUBSCRIBE error', { error, channel });
      throw error;
    }
  }

  // Streams operations
  async xadd(stream: string, id: string, data: Record<string, string>): Promise<string> {
    try {
      const start = performance.now();
      const fields = Object.entries(data).flat();
      const result = await this.client.xadd(stream, id, ...fields);
      
      this.trackOperation('XADD', performance.now() - start, true);
      return result || '';
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.trackOperation('XADD', 0, false, errorMessage);
      logger.error('Redis XADD error', { error: errorMessage, stream });
      throw error;
    }
  }

  async xread(streams: string[], ids: string[], count?: number, block?: number): Promise<any[]> {
    try {
      const start = performance.now();
      const args = ['STREAMS', ...streams, ...ids];
      
      if (count) {
        args.unshift('COUNT', count.toString());
      }
      
      if (block !== undefined) {
        args.unshift('BLOCK', block.toString());
      }
      
      const result = await this.client.xread('STREAMS', ...streams, ...ids);
      
      this.trackOperation('XREAD', performance.now() - start, true);
      return result || [];
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.trackOperation('XREAD', 0, false, errorMessage);
      logger.error('Redis XREAD error', { error: errorMessage, streams });
      throw error;
    }
  }

  // Utility methods
  async ping(): Promise<string> {
    try {
      const start = performance.now();
      const result = await this.client.ping();
      
      this.trackOperation('PING', performance.now() - start, true);
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.trackOperation('PING', 0, false, errorMessage);
      logger.error('Redis PING error', { error: errorMessage });
      throw error;
    }
  }

  async info(): Promise<string> {
    try {
      const start = performance.now();
      const result = await this.client.info();
      
      this.trackOperation('INFO', performance.now() - start, true);
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.trackOperation('INFO', 0, false, errorMessage);
      logger.error('Redis INFO error', { error: errorMessage });
      throw error;
    }
  }

  async flushdb(): Promise<string> {
    try {
      const start = performance.now();
      const result = await this.client.flushdb();
      
      this.trackOperation('FLUSHDB', performance.now() - start, true);
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.trackOperation('FLUSHDB', 0, false, errorMessage);
      logger.error('Redis FLUSHDB error', { error: errorMessage });
      throw error;
    }
  }

  // Performance and monitoring methods
  private trackOperation(operation: string, duration: number, success: boolean, error?: string): void {
    const metric: OperationMetrics = {
      operation,
      duration,
      success,
      error,
    };
    
    this.operationMetrics.push(metric);
    
    // Keep only last 1000 operations
    if (this.operationMetrics.length > 1000) {
      this.operationMetrics.shift();
    }
  }

  getOperationMetrics(): OperationMetrics[] {
    return [...this.operationMetrics];
  }

  async getClusterInfo(): Promise<any> {
    if (!this.isClusterMode) {
      return null;
    }
    
    try {
      const cluster = this.client as Cluster;
      return {
        nodes: cluster.nodes(),
        status: cluster.status,
        isCluster: true,
      };
    } catch (error) {
      logger.error('Failed to get cluster info', { error });
      return null;
    }
  }

  async getConnectionInfo(): Promise<any> {
    try {
      const info = await this.info();
      return {
        isConnected: this.connected,
        isCluster: this.isClusterMode,
        info: info,
        metrics: this.getOperationMetrics().slice(-10), // Last 10 operations
      };
    } catch (error) {
      logger.error('Failed to get connection info', { error });
      return {
        isConnected: this.connected,
        isCluster: this.isClusterMode,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  // Get raw clients for advanced operations
  getRawClient(): Redis | Cluster {
    return this.client;
  }
  
  // Check if Redis client is connected
  isConnected(): boolean {
    return this.connected;
  }

  getRawSubscriber(): Redis | Cluster {
    return this.subscriber;
  }

  getRawPublisher(): Redis | Cluster {
    return this.publisher;
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    try {
      await this.ping();
      return true;
    } catch (error) {
      logger.error('Redis health check failed', { error });
      return false;
    }
  }

  // Graceful shutdown
  async shutdown(): Promise<void> {
    logger.info('Shutting down Redis client...');
    
    try {
      // Close all connections
      await this.disconnect();
      
      // Log final metrics
      const totalOps = this.operationMetrics.length;
      const successfulOps = this.operationMetrics.filter(m => m.success).length;
      const avgDuration = this.operationMetrics.reduce((sum, m) => sum + m.duration, 0) / totalOps;
      
      logger.info('Redis client shutdown completed', {
        totalOperations: totalOps,
        successfulOperations: successfulOps,
        successRate: totalOps > 0 ? (successfulOps / totalOps * 100).toFixed(2) + '%' : '0%',
        avgDuration: avgDuration.toFixed(2) + 'ms',
      });
    } catch (error) {
      logger.error('Error during Redis shutdown', { error });
    }
  }
}

export const redisClient = new RedisClient();