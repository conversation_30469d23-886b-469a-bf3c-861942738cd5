/**
 * External Client Initialization
 * 
 * Initializes and manages connections to external services
 * including Redis, Firestore, and Spanner.
 */

import { logger } from '../utils/logger';
import { redisClient } from './redis.client';
import { firestoreClient } from './firestore.client';
import { spannerClient } from './spanner.client';

/**
 * Initialize all external clients
 */
export async function initializeClients(): Promise<void> {
  try {
    // Initialize Redis client
    logger.info('Initializing Redis client');
    await redisClient.connect();
    logger.info('Redis client connected successfully');

    // Initialize Firestore client
    logger.info('Initializing Firestore client');
    await firestoreClient.initialize();
    logger.info('Firestore client initialized successfully');

    // Initialize Spanner client
    logger.info('Initializing Spanner client');
    await spannerClient.initialize();
    logger.info('Spanner client initialized successfully');

    logger.info('All external clients initialized successfully');
  } catch (error) {
    logger.error('Failed to initialize external clients', { error });
    throw error;
  }
}

/**
 * Close all external clients
 */
export async function closeClients(): Promise<void> {
  try {
    logger.info('Closing external clients');

    // Close Redis client
    if (redisClient.isConnected()) {
      await redisClient.disconnect();
      logger.info('Redis client closed');
    }

    // Close Firestore client
    await firestoreClient.close();
    logger.info('Firestore client closed');

    // Close Spanner client
    await spannerClient.close();
    logger.info('Spanner client closed');

    logger.info('All external clients closed successfully');
  } catch (error) {
    logger.error('Error closing external clients', { error });
    throw error;
  }
}

// Export individual clients
export { redisClient } from './redis.client';
export { firestoreClient } from './firestore.client';
export { spannerClient } from './spanner.client';