{"compilerOptions": {"target": "ES2022", "module": "commonjs", "lib": ["ES2022"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "sourceMap": true, "declaration": true, "declarationMap": true, "incremental": true, "tsBuildInfoFile": "./dist/.tsbuildinfo", "types": ["node", "jest"]}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "tests/**/*", "src/components/**/*", "src/hooks/**/*"]}