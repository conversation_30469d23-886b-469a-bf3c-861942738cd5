# Environment Configuration
NODE_ENV=development
PORT=8005
LOG_LEVEL=info

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-at-least-32-characters-long
JWT_EXPIRES_IN=24h

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Google Cloud Configuration
GOOGLE_CLOUD_PROJECT_ID=your-project-id
FIRESTORE_DATABASE_ID=(default)
SPANNER_INSTANCE_ID=your-instance-id
SPANNER_DATABASE_ID=your-database-id

# Service Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001
MAX_CONNECTIONS_PER_SESSION=50
SESSION_TIMEOUT_MS=3600000

# Performance Configuration
ENABLE_CLUSTERING=false
ENABLE_METRICS=true
METRICS_PORT=9090