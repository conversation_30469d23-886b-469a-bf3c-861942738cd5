/**
 * Simple WebSocket test client
 * 
 * Tests the collaboration service WebSocket functionality.
 */

const io = require('socket.io-client');

const COLLABORATION_URL = process.env.COLLABORATION_URL || 'http://localhost:8002';
const TEST_TOKEN = process.env.TEST_TOKEN || 'test-jwt-token';

console.log('Connecting to:', COLLABORATION_URL);

// Create socket connection
const socket = io(COLLABORATION_URL, {
  auth: {
    token: TEST_TOKEN
  },
  transports: ['websocket', 'polling'],
  reconnection: true,
  reconnectionAttempts: 3,
  reconnectionDelay: 1000,
});

// Connection events
socket.on('connect', () => {
  console.log('✅ Connected to collaboration service');
  console.log('Socket ID:', socket.id);
  
  // Join a test session
  socket.emit('session:join', {
    sessionId: 'test-session-123',
    teamId: 'test-team-456'
  });
});

socket.on('connect_error', (error) => {
  console.error('❌ Connection error:', error.message);
  if (error.type === 'TransportError') {
    console.error('Transport error details:', error);
  }
});

socket.on('disconnect', (reason) => {
  console.log('🔌 Disconnected:', reason);
});

// Session events
socket.on('session:joined', (data) => {
  console.log('📥 Joined session:', data);
  
  // Send a test message
  socket.emit('message:send', {
    sessionId: 'test-session-123',
    content: 'Hello from test client!',
    type: 'text'
  });
});

socket.on('message:received', (data) => {
  console.log('💬 Message received:', data);
});

socket.on('user:joined', (data) => {
  console.log('👤 User joined:', data);
});

socket.on('user:left', (data) => {
  console.log('👤 User left:', data);
});

// Error handling
socket.on('error', (error) => {
  console.error('❌ Socket error:', error);
});

// Keep the script running
console.log('Test client is running. Press Ctrl+C to exit.');

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\nShutting down test client...');
  socket.disconnect();
  process.exit(0);
});