# Collaboration Service

Real-time collaboration service for the CCL platform, enabling shared code analysis sessions, team pattern development, and collaborative workspaces.

## Features

- **Real-time WebSocket Communication**: Sub-50ms latency for real-time updates
- **Team Management**: Create teams, manage members, and set permissions
- **Collaborative Sessions**: Shared analysis sessions with live cursor tracking
- **Message System**: Real-time messaging within collaboration sessions
- **Analysis Sharing**: Share and collaborate on code analysis results
- **Presence Tracking**: Real-time user presence and activity status
- **Redis Scaling**: Horizontal scaling with Redis Streams adapter

## Architecture

- **Language**: TypeScript/Node.js 20+
- **Framework**: Express.js + Socket.IO
- **Databases**: Firestore (real-time) + <PERSON>is (cache/pubsub) + Spanner (persistence)
- **Authentication**: JWT-based authentication
- **Scaling**: Redis Streams adapter for multi-instance scaling

## Getting Started

### Prerequisites

- Node.js 20+ 
- Redis server
- Google Cloud credentials with Firestore and Spanner access

### Installation

```bash
# Install dependencies
npm install

# Copy environment file
cp .env.example .env

# Edit environment variables
nano .env

# Build the project
npm run build

# Start development server
npm run dev
```

### Environment Variables

```env
NODE_ENV=development
PORT=8005
JWT_SECRET=your-super-secret-jwt-key-at-least-32-characters-long
REDIS_HOST=localhost
REDIS_PORT=6379
GOOGLE_CLOUD_PROJECT_ID=your-project-id
SPANNER_INSTANCE_ID=your-instance-id
SPANNER_DATABASE_ID=your-database-id
```

## API Endpoints

### Health Check
- `GET /health` - Basic health check
- `GET /health/detailed` - Detailed health check with dependencies
- `GET /health/ready` - Readiness probe
- `GET /health/live` - Liveness probe

### Teams
- `POST /api/teams` - Create a new team
- `GET /api/teams` - Get user's teams
- `GET /api/teams/:teamId` - Get team details
- `PUT /api/teams/:teamId` - Update team
- `POST /api/teams/:teamId/members` - Add team member
- `DELETE /api/teams/:teamId/members/:memberId` - Remove team member

### Sessions
- `POST /api/sessions` - Create a new collaboration session
- `GET /api/sessions/team/:teamId` - Get team sessions
- `GET /api/sessions/:sessionId` - Get session details
- `PUT /api/sessions/:sessionId` - Update session
- `POST /api/sessions/:sessionId/end` - End session
- `GET /api/sessions/:sessionId/messages` - Get session messages

## WebSocket Events

### Connection
- `connected` - Connection acknowledgment
- `error` - Error events with codes

### Sessions
- `join_session` - Join a collaboration session
- `leave_session` - Leave a collaboration session
- `session_message` - Send message in session
- `cursor_position` - Update cursor position
- `sync_session` - Request session synchronization

### Teams
- `join_team` - Join team room for updates
- `leave_team` - Leave team room
- `team_notification` - Send team notification
- `get_team_activity` - Get team activity status

### Analysis
- `share_analysis` - Share analysis results
- `analysis_comment` - Add comment to analysis
- `analysis_reaction` - React to analysis
- `live_analysis_update` - Stream live analysis updates
- `get_analysis_history` - Get analysis history

## Development

### Scripts

```bash
npm run dev         # Start development server
npm run build       # Build for production
npm run start       # Start production server
npm test            # Run tests
npm run lint        # Run linting
npm run format      # Format code
```

### Project Structure

```
src/
├── index.ts              # Application entry point
├── app.ts                # Express application setup
├── config/               # Configuration management
├── controllers/          # HTTP controllers
├── services/             # Business logic
├── websocket/            # WebSocket handlers
├── models/               # Data models
├── clients/              # External service clients
├── middleware/           # Express middleware
├── types/                # TypeScript types
└── utils/                # Utility functions
```

### Testing

```bash
# Run all tests
npm test

# Run specific test file
npm test -- --testPathPattern=session.test.ts

# Run with coverage
npm run test:coverage

# Run WebSocket tests
npm run test:websocket
```

## Deployment

### Docker

```bash
# Build Docker image
docker build -t collaboration-service .

# Run container
docker run -p 8005:8005 collaboration-service
```

### Environment Setup

1. **Redis**: Set up Redis server or use managed service
2. **Google Cloud**: Configure Firestore and Spanner
3. **Authentication**: Set up JWT secret and user management
4. **Monitoring**: Configure health checks and metrics

## Performance

- **Target Latency**: <50ms for real-time operations
- **Concurrent Users**: 50+ per session
- **WebSocket Connections**: Stable for >1 hour
- **Uptime**: 99.95% availability target

## Security

- JWT authentication for all operations
- Input validation with Zod schemas
- Rate limiting on API endpoints
- CORS configuration
- Security headers with Helmet
- Team-based access control

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run linting and tests
6. Create a pull request

## License

This project is part of the CCL platform and follows the platform's licensing terms.