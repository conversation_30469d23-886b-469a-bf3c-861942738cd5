# ============================================
# Multi-stage, Multi-arch Dockerfile for Analysis Engine
# Supports both standard and Cloud Run deployment
# Optimized for size and performance
# ============================================

# Build argument for runtime target (must be before first FROM)
ARG RUNTIME_TARGET=standard

# Use specific Rust version for reproducibility
ARG RUST_VERSION=1.82.0

# Stage 1: Dependencies caching (multi-arch aware)
FROM --platform=$BUILDPLATFORM rust:${RUST_VERSION}-bookworm AS dependencies

WORKDIR /usr/src/app

# Install build dependencies with minimal layers
RUN apt-get update && apt-get install -y \
    pkg-config \
    libssl-dev \
    ca-certificates \
    git \
    --no-install-recommends \
    && rm -rf /var/lib/apt/lists/*

# Copy only Cargo files for dependency caching
COPY Cargo.toml Cargo.lock ./
COPY build.rs ./

# Create dummy sources to build dependencies
RUN mkdir -p src/bin benches && \
    echo "fn main() {}" > src/main.rs && \
    echo "fn main() {}" > src/bin/test_ai_services.rs && \
    echo "fn main() {}" > src/bin/load_test.rs && \
    echo "fn main() {}" > src/bin/performance_validator.rs && \
    echo "fn main() {}" > src/bin/api_validator.rs && \
    echo "fn main() {}" > benches/analysis_bench.rs && \
    echo "fn main() {}" > benches/regex_performance.rs && \
    echo "fn main() {}" > benches/load_test_bench.rs && \
    echo "fn main() {}" > benches/production_scenarios.rs && \
    echo "fn main() {}" > benches/encryption_benchmarks.rs && \
    cargo build --release && \
    rm -rf src benches

# Stage 2: Builder (multi-arch aware)
FROM --platform=$TARGETPLATFORM rust:${RUST_VERSION}-bookworm AS builder

# Import target platform for cross-compilation
ARG TARGETPLATFORM
ARG BUILDPLATFORM

WORKDIR /usr/src/app

# Install build dependencies
RUN apt-get update && apt-get install -y \
    pkg-config \
    libssl-dev \
    ca-certificates \
    git \
    --no-install-recommends \
    && rm -rf /var/lib/apt/lists/*

# Set up cross-compilation if needed
RUN if [ "$BUILDPLATFORM" != "$TARGETPLATFORM" ]; then \
    case "$TARGETPLATFORM" in \
        "linux/arm64") rustup target add aarch64-unknown-linux-gnu ;; \
        "linux/amd64") rustup target add x86_64-unknown-linux-gnu ;; \
    esac; \
    fi

# Copy cached dependencies from previous stage
COPY --from=dependencies /usr/src/app/target target
COPY --from=dependencies /usr/local/cargo /usr/local/cargo

# Copy source code
COPY Cargo.toml Cargo.lock ./
COPY src ./src
COPY benches ./benches
COPY build.rs ./

# Build with aggressive optimizations and proper target
ENV CARGO_BUILD_JOBS=8
ENV CARGO_REGISTRIES_CRATES_IO_PROTOCOL=sparse

RUN --mount=type=cache,target=/usr/local/cargo/registry \
    --mount=type=cache,target=/usr/src/app/target \
    if [ "$BUILDPLATFORM" != "$TARGETPLATFORM" ]; then \
        case "$TARGETPLATFORM" in \
            "linux/arm64") cargo build --release --target aarch64-unknown-linux-gnu && \
                cp target/aarch64-unknown-linux-gnu/release/analysis-engine target/release/analysis-engine ;; \
            "linux/amd64") cargo build --release --target x86_64-unknown-linux-gnu && \
                cp target/x86_64-unknown-linux-gnu/release/analysis-engine target/release/analysis-engine ;; \
        esac; \
    else \
        cargo build --release; \
    fi && \
    strip target/release/analysis-engine && \
    # Create a smaller binary using UPX if available (optional)
    (which upx > /dev/null 2>&1 && upx --best --lzma target/release/analysis-engine || true)

# Stage 3a: Runtime standard - minimal Debian
FROM debian:bookworm-slim AS runtime-standard

RUN apt-get update && apt-get install -y \
    ca-certificates \
    libssl3 \
    curl \
    --no-install-recommends \
    && rm -rf /var/lib/apt/lists/* \
    && useradd -m -u 1001 appuser

# Stage 3b: Distroless runtime for Cloud Run (smallest possible)
FROM gcr.io/distroless/cc-debian12:nonroot AS runtime-distroless

# Copy SSL certificates
COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/

# Final stage: Choose runtime based on build arg
FROM runtime-${RUNTIME_TARGET} AS final

# Copy the binary with proper permissions
COPY --from=builder --chown=1001:1001 /usr/src/app/target/release/analysis-engine /app/analysis-engine

# For standard runtime, ensure executable permissions
RUN if [ "$RUNTIME_TARGET" = "standard" ]; then \
    chmod +x /app/analysis-engine; \
    fi

# Set non-root user
USER ${RUNTIME_TARGET:+appuser}

# Environment variables with production defaults
ENV RUST_LOG=info \
    RUST_BACKTRACE=1 \
    PORT=8001 \
    ANALYSIS_ENGINE_ADDR=0.0.0.0:8001

# Copy startup script for standard runtime only
COPY --chown=1001:1001 scripts/docker-entrypoint.sh /app/docker-entrypoint.sh
RUN if [ "$RUNTIME_TARGET" = "standard" ]; then \
    chmod +x /app/docker-entrypoint.sh; \
    fi

# Health check with optimized intervals
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:${PORT}/health/live || exit 1

# Expose port
EXPOSE ${PORT}

# Optimized entrypoint for each runtime
ENTRYPOINT ["sh", "-c", "if [ -f /app/docker-entrypoint.sh ]; then exec /app/docker-entrypoint.sh; else exec /app/analysis-engine; fi"]

# Labels for container metadata
LABEL org.opencontainers.image.title="Analysis Engine" \
    org.opencontainers.image.description="High-performance code analysis engine with multi-language support" \
    org.opencontainers.image.vendor="Episteme" \
    org.opencontainers.image.version="0.1.0" \
    org.opencontainers.image.source="https://github.com/episteme-org/brasilia"

# ============================================
# Build Instructions
# ============================================
#
# Multi-arch build with Docker Buildx:
# docker buildx create --use --name multiarch
# docker buildx build --platform linux/amd64,linux/arm64 -t analysis-engine:latest --push .
#
# Local single-arch builds:
# AMD64: docker build -t analysis-engine:latest .
# ARM64: docker build --platform linux/arm64 -t analysis-engine:latest .
#
# Cloud Run optimized:
# docker buildx build --platform linux/amd64,linux/arm64 \
#   --build-arg RUNTIME_TARGET=distroless \
#   -t analysis-engine:cloudrun --push .
#
# ============================================
# Size Optimization Results
# ============================================
#
# Optimizations applied:
# 1. Pinned Rust version (1.82.0) for reproducibility
# 2. Multi-stage build with dependency caching
# 3. Minimal runtime images (bookworm-slim / distroless)
# 4. Removed unnecessary packages with --no-install-recommends
# 5. Strip binary for size reduction
# 6. Optional UPX compression for further reduction
# 7. Build cache mounts for faster rebuilds
# 8. Sparse registry protocol for faster downloads
#
# Expected sizes:
# - Standard runtime: ~150-200MB
# - Distroless runtime: ~100-130MB
# - Binary size: ~50-70MB (after stripping)
#
# ============================================