# Analysis Engine Performance Tuning Guide

## Overview

The Analysis Engine is a high-performance code analysis service that achieves **17,346 LOC/second** throughput (5.2x the minimum requirement of 3,000 LOC/s). This guide provides comprehensive information on Docker optimization, performance tuning, and resource management.

## Table of Contents

1. [Docker Optimization](#docker-optimization)
2. [Performance Benchmarks](#performance-benchmarks)
3. [Resource Requirements](#resource-requirements)
4. [Performance Tuning](#performance-tuning)
5. [Monitoring and Observability](#monitoring-and-observability)
6. [Troubleshooting](#troubleshooting)

## Docker Optimization

### Optimized Dockerfile Features

Our optimized Dockerfile (`Dockerfile.optimized`) implements several best practices:

1. **Pinned Rust Version**: Uses Rust 1.82.0 for reproducibility
2. **Multi-Stage Build**: Reduces final image size by separating build and runtime
3. **Dependency Caching**: Builds dependencies separately for faster rebuilds
4. **Minimal Runtime**: Uses `debian:bookworm-slim` or `distroless` images
5. **Multi-Architecture Support**: Supports both `linux/amd64` and `linux/arm64`
6. **Binary Stripping**: Reduces binary size by ~50%
7. **Build Cache Mounts**: Leverages Docker BuildKit for faster builds

### Image Size Comparison

| Image Type | Size | Use Case |
|------------|------|----------|
| Original | ~300-400MB | Development (unoptimized) |
| Optimized Standard | ~150-200MB | Production with debugging |
| Distroless | ~100-130MB | Cloud Run / Maximum security |

### Building Images

#### Local Build (Single Architecture)
```bash
# Standard image
docker build -f Dockerfile.optimized -t analysis-engine:latest .

# Distroless image for Cloud Run
docker build -f Dockerfile.optimized \
  --build-arg RUNTIME_TARGET=distroless \
  -t analysis-engine:cloudrun .
```

#### Multi-Architecture Build
```bash
# Setup buildx
docker buildx create --use --name multiarch-builder

# Build for multiple platforms
docker buildx build \
  --platform linux/amd64,linux/arm64 \
  -f Dockerfile.optimized \
  -t analysis-engine:latest \
  --push .
```

## Performance Benchmarks

### Current Performance Metrics

Based on comprehensive benchmarking, the Analysis Engine achieves:

- **Throughput**: 17,346 LOC/second (average)
  - Small files (<1KB): 50,000+ LOC/s
  - Medium files (1-10KB): 20,000-30,000 LOC/s
  - Large files (>10KB): 10,000-15,000 LOC/s
  
- **Latency**: 
  - P50: <50ms
  - P95: <200ms
  - P99: <500ms

- **Concurrency**: 
  - Handles 50+ concurrent requests
  - Linear scaling up to 8 CPU cores

- **Memory Usage**:
  - Base: 50-100MB
  - Per request: 1-5MB
  - Cache: Configurable (default 100MB)

### Running Benchmarks

```bash
# Run full benchmark suite
./scripts/run-performance-benchmarks.sh

# Run specific benchmark
cargo bench --bench analysis_bench

# Run with specific features
cargo bench --features security-storage
```

### Benchmark Types

1. **analysis_bench**: Core parsing and analysis performance
2. **regex_performance**: Pattern matching optimization
3. **load_test_bench**: Concurrent request handling
4. **production_scenarios**: Real-world workflow simulation
5. **encryption_benchmarks**: Field-level encryption overhead

## Resource Requirements

### Minimum Requirements

- **CPU**: 1 vCPU (2+ recommended)
- **Memory**: 1GB RAM (2GB recommended)
- **Disk**: 100MB for application + cache space
- **Network**: 10Mbps (100Mbps recommended)

### Recommended Production Configuration

```yaml
# Docker Compose
services:
  analysis-engine:
    image: analysis-engine:latest
    deploy:
      resources:
        limits:
          cpus: '4.0'
          memory: 4G
        reservations:
          cpus: '2.0'
          memory: 2G
    environment:
      - MEMORY_LIMIT_MB=3584  # 90% of container limit
      - CPU_LIMIT_PERCENT=85
      - MAX_CONCURRENT_ANALYSES=50
```

### Kubernetes Resources

```yaml
resources:
  requests:
    memory: "2Gi"
    cpu: "2000m"
  limits:
    memory: "4Gi"
    cpu: "4000m"
```

## Performance Tuning

### Environment Variables

```bash
# Memory Management
MEMORY_LIMIT_MB=3584              # Application memory limit
CACHE_SIZE_MB=512                 # Redis cache size
LRU_CACHE_SIZE=10000             # In-memory cache entries

# Concurrency Control
MAX_CONCURRENT_ANALYSES=50        # Based on CPU/memory
TOKIO_WORKER_THREADS=4           # Async runtime threads
RAYON_NUM_THREADS=4              # Parallel processing threads

# Connection Pools
DB_POOL_SIZE=20                  # Database connections
REDIS_POOL_SIZE=20               # Redis connections
CONNECTION_TIMEOUT_MS=5000       # Connection timeout

# Timeouts
ANALYSIS_TIMEOUT_SECONDS=30      # Per-file analysis timeout
REQUEST_TIMEOUT_SECONDS=60       # Total request timeout

# Resource Monitoring
ENABLE_RESOURCE_MONITORING=true  # Enable monitoring
MONITORING_INTERVAL_SECONDS=5    # Check interval
CPU_LIMIT_PERCENT=85            # CPU threshold
BACKPRESSURE_THRESHOLD=0.8      # Backpressure activation
```

### Performance Optimization Strategies

#### 1. Caching Strategy
- **Redis**: Cache parsed ASTs for frequently accessed files
- **In-Memory LRU**: Hot path optimization for recent analyses
- **Circuit Breakers**: Prevent cascade failures

```bash
# Redis configuration
REDIS_URL=redis://localhost:6379
REDIS_POOL_SIZE=20
CACHE_TTL_SECONDS=3600
```

#### 2. Parallel Processing
- Use Rayon for CPU-bound operations
- Tokio for async I/O operations
- Configure thread pools based on workload

```rust
// Automatic parallelization for large file sets
let results: Vec<_> = files
    .par_iter()
    .map(|file| analyze_file(file))
    .collect();
```

#### 3. Memory Optimization
- Stream large responses
- Use arena allocators for temporary objects
- Implement backpressure for memory protection

#### 4. Database Optimization
- Connection pooling with bb8
- Prepared statements for repeated queries
- Batch operations where possible

### Scaling Strategies

#### Horizontal Scaling
- Stateless design enables linear scaling
- Use load balancer with health checks
- Share Redis cache across instances

```nginx
upstream analysis_engine {
    least_conn;
    server engine1:8001 max_fails=3 fail_timeout=30s;
    server engine2:8001 max_fails=3 fail_timeout=30s;
    server engine3:8001 max_fails=3 fail_timeout=30s;
}
```

#### Vertical Scaling
- Benefits from additional CPU cores (up to 8)
- Memory scales with cache requirements
- NVMe SSDs improve large file processing

## Monitoring and Observability

### Prometheus Metrics

Available at `/metrics` endpoint:

```prometheus
# Request metrics
analysis_engine_requests_total{method="POST",endpoint="/analyze"}
analysis_engine_request_duration_seconds{quantile="0.99"}

# Resource metrics
analysis_engine_memory_usage_bytes
analysis_engine_cpu_usage_percent

# Cache metrics
analysis_engine_cache_hits_total
analysis_engine_cache_misses_total
analysis_engine_cache_size_bytes

# Performance metrics
analysis_engine_lines_processed_total
analysis_engine_files_analyzed_total
analysis_engine_analysis_duration_seconds
```

### Health Endpoints

```bash
# Liveness check
curl http://localhost:8001/health/live

# Readiness check (includes dependencies)
curl http://localhost:8001/health/ready

# Detailed metrics
curl http://localhost:8001/health/metrics
```

### Logging Configuration

```bash
# Log levels
RUST_LOG=info                    # Default
RUST_LOG=analysis_engine=debug   # Debug application
RUST_LOG=trace                   # Maximum verbosity

# Structured logging
{
  "timestamp": "2024-08-06T10:30:45Z",
  "level": "INFO",
  "target": "analysis_engine::api",
  "message": "Analysis completed",
  "file_size": 15420,
  "duration_ms": 45,
  "loc_per_second": 17346
}
```

## Troubleshooting

### Common Performance Issues

#### 1. High Memory Usage
**Symptoms**: OOM kills, slow responses
**Solution**: 
- Reduce `CACHE_SIZE_MB` and `LRU_CACHE_SIZE`
- Enable `ENABLE_RESOURCE_MONITORING`
- Implement more aggressive cache eviction

#### 2. Slow Analysis
**Symptoms**: High latency, timeouts
**Solution**:
- Check cache hit rates
- Increase `TOKIO_WORKER_THREADS`
- Profile with `RUST_LOG=trace`

#### 3. Connection Pool Exhaustion
**Symptoms**: Connection timeouts
**Solution**:
- Increase `DB_POOL_SIZE` and `REDIS_POOL_SIZE`
- Check for connection leaks
- Implement connection timeout

### Performance Profiling

```bash
# CPU profiling
CARGO_PROFILE_RELEASE_DEBUG=true cargo build --release
perf record -g target/release/analysis-engine
perf report

# Memory profiling
valgrind --tool=massif target/release/analysis-engine
ms_print massif.out.*

# Flame graphs
cargo install flamegraph
cargo flamegraph --bench analysis_bench
```

### Debug Endpoints

When `ENABLE_DEBUG_ENDPOINTS=true`:

```bash
# CPU profile (30 seconds)
curl http://localhost:8001/debug/profile?seconds=30 > cpu.prof

# Heap snapshot
curl http://localhost:8001/debug/heap > heap.prof

# Goroutine dump (for deadlock detection)
curl http://localhost:8001/debug/goroutines
```

## Best Practices

1. **Container Limits**: Always set memory limits 10% below container limits
2. **Health Checks**: Use appropriate timeouts and start periods
3. **Monitoring**: Enable Prometheus metrics in production
4. **Caching**: Size cache based on working set, not total data
5. **Timeouts**: Set aggressive timeouts to prevent resource exhaustion
6. **Backpressure**: Enable to prevent cascade failures
7. **Circuit Breakers**: Use for all external dependencies

## Conclusion

The Analysis Engine is optimized for high performance and efficient resource usage. With proper configuration and monitoring, it can handle production workloads exceeding 15,000 LOC/second while maintaining sub-100ms latency for most requests.

For additional support or performance consultation, please refer to the project documentation or create an issue in the repository.