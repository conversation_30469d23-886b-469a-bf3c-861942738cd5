# Analysis Engine - Container Startup Reliability Guide

## Overview

This guide documents the improvements made to ensure 100% reliable container startup for the Analysis Engine service, eliminating the need for manual intervention during deployment.

## Architecture Improvements

### 1. Multi-Layer Startup Strategy

The startup reliability is achieved through multiple layers of protection:

- **Docker Health Checks**: Proper health check configuration with extended start periods
- **Entrypoint Script**: Intelligent startup script with dependency checking
- **Application-Level Retry**: Built-in retry logic in the Rust application
- **Circuit Breaker Pattern**: Protection against cascading failures
- **Graceful Degradation**: Optional services can fail without preventing startup

### 2. Health Check Endpoints

The service provides multiple health check endpoints for different purposes:

- `/health/live` - Kubernetes liveness probe (basic responsiveness)
- `/health/ready` - Kubernetes readiness probe (full service readiness)
- `/health` - General health status with startup progress
- `/health/detailed` - Comprehensive health information

### 3. Startup Sequence

1. **Pre-flight Checks**
   - Environment validation
   - Port availability check
   - Resource availability verification

2. **Dependency Initialization**
   - Spanner connection with exponential backoff
   - Redis connection (optional, non-blocking)
   - Google Cloud Storage with retry
   - Google Cloud Pub/Sub with retry

3. **Health Monitoring**
   - Continuous health checks during startup
   - Progress tracking and reporting
   - Timeout enforcement (5-minute Cloud Run limit)

## Configuration

### Environment Variables

```bash
# Startup Configuration
STARTUP_TIMEOUT=300              # Maximum startup time in seconds (default: 300)
HEALTH_CHECK_INTERVAL=5          # Health check interval during startup (default: 5)
MAX_HEALTH_RETRIES=60           # Maximum health check retries (default: 60)

# Retry Configuration
RETRY_DELAY=5                    # Delay between retry attempts (default: 5)
MAX_RETRIES=3                    # Maximum number of startup retries (default: 3)

# Resource Limits
MEMORY_LIMIT_MB=3584            # Application memory limit
CPU_LIMIT_PERCENT=85            # CPU usage threshold
MAX_CONCURRENT_ANALYSES=30       # Concurrent analysis limit

# Circuit Breaker
CIRCUIT_BREAKER_ENABLED=true     # Enable circuit breaker
CIRCUIT_BREAKER_FAILURE_THRESHOLD=5  # Failures before opening
CIRCUIT_BREAKER_RECOVERY_TIMEOUT=60  # Recovery timeout in seconds
```

### Docker Configuration

The Dockerfile includes:
- Proper health check configuration with extended start period
- Startup script installation
- Non-root user execution
- Signal handling for graceful shutdown

```dockerfile
HEALTHCHECK --interval=30s --timeout=10s --start-period=300s --retries=10 \
  CMD curl -f http://localhost:${PORT}/health/live || exit 1
```

## Deployment Patterns

### 1. Local Development (Docker Compose)

Use the provided `docker-compose.yml` for local development with all dependencies:

```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f analysis-engine

# Check health
curl http://localhost:8001/health
```

### 2. Production Deployment

Use `docker-compose.prod.yml` for production deployment:

```bash
# Set required environment variables
export GCP_PROJECT_ID=your-project
export SPANNER_INSTANCE_ID=your-instance
export SPANNER_DATABASE_ID=your-database
export STORAGE_BUCKET=your-bucket
export PUBSUB_TOPIC=your-topic

# Deploy with production configuration
docker-compose -f docker-compose.prod.yml up -d
```

### 3. Kubernetes Deployment

Example Kubernetes deployment with proper probes:

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: analysis-engine
spec:
  replicas: 3
  template:
    spec:
      containers:
      - name: analysis-engine
        image: analysis-engine:latest
        ports:
        - containerPort: 8001
        livenessProbe:
          httpGet:
            path: /health/live
            port: 8001
          initialDelaySeconds: 10
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health/ready
            port: 8001
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 10
          failureThreshold: 30  # Allow 5 minutes for startup
        startupProbe:
          httpGet:
            path: /health
            port: 8001
          initialDelaySeconds: 0
          periodSeconds: 10
          timeoutSeconds: 10
          failureThreshold: 30  # 5 minutes total
        resources:
          limits:
            memory: "4Gi"
            cpu: "2000m"
          requests:
            memory: "2Gi"
            cpu: "1000m"
```

### 4. Google Cloud Run Deployment

The service is optimized for Cloud Run with:
- 5-minute startup timeout support
- Proper signal handling
- Resource-aware configuration

```bash
# Build for Cloud Run
docker build -t analysis-engine:cloudrun --build-arg RUNTIME_TARGET=distroless .

# Deploy to Cloud Run
gcloud run deploy analysis-engine \
  --image gcr.io/${PROJECT_ID}/analysis-engine:cloudrun \
  --platform managed \
  --region us-central1 \
  --memory 4Gi \
  --cpu 2 \
  --timeout 300 \
  --max-instances 10 \
  --set-env-vars "ENVIRONMENT=production,ENABLE_RESOURCE_MONITORING=true"
```

## Monitoring and Troubleshooting

### Health Check Monitoring

Monitor startup progress:

```bash
# Check basic health
curl http://localhost:8001/health

# Check detailed startup status
curl http://localhost:8001/health | jq '.startup'

# Check individual service status
curl http://localhost:8001/health/detailed | jq '.services'
```

### Common Issues and Solutions

1. **Spanner Connection Timeout**
   - Check GCP credentials are properly configured
   - Verify network connectivity to GCP
   - Increase `STARTUP_TIMEOUT` if needed

2. **Redis Connection Failure**
   - Redis is optional and won't prevent startup
   - Check Redis URL configuration
   - Service will operate without caching

3. **Port Already in Use**
   - The startup script checks port availability
   - Change `PORT` environment variable if needed

4. **Memory Constraints**
   - Monitor memory usage during startup
   - Adjust `MEMORY_LIMIT_MB` based on available resources
   - Enable swap if necessary for development

### Logs and Diagnostics

```bash
# View startup logs
docker logs analysis-engine

# View detailed startup progress
docker exec analysis-engine cat /app/logs/startup.log

# Check resource usage
docker stats analysis-engine

# Test circuit breaker status
curl http://localhost:8001/circuit-breakers
```

## Best Practices

1. **Always Use Health Checks**
   - Configure appropriate health check endpoints
   - Set realistic timeouts based on your environment
   - Monitor health check metrics

2. **Resource Planning**
   - Allocate sufficient memory for startup (minimum 2GB recommended)
   - Consider startup overhead in resource limits
   - Monitor actual usage and adjust accordingly

3. **Dependency Management**
   - Use circuit breakers for external services
   - Implement retry logic with exponential backoff
   - Make non-critical services optional

4. **Monitoring**
   - Set up alerts for startup failures
   - Track startup duration metrics
   - Monitor dependency availability

5. **Testing**
   - Test startup with dependencies unavailable
   - Simulate slow network conditions
   - Verify graceful shutdown behavior

## Testing Startup Reliability

Run the comprehensive startup tests:

```bash
# Test startup with all dependencies
docker-compose up -d
./scripts/test_startup_reliability.sh

# Test startup with Redis unavailable
docker-compose up -d spanner-emulator gcs-emulator pubsub-emulator
docker-compose up -d analysis-engine

# Test startup with slow dependencies
tc qdisc add dev eth0 root netem delay 500ms
docker-compose up -d

# Test container restart
docker-compose restart analysis-engine
```

## Metrics and Observability

The service exports Prometheus metrics for startup monitoring:

- `analysis_engine_startup_duration_seconds` - Time taken to start
- `analysis_engine_service_initialization_duration_seconds{service="..."}` - Per-service startup time
- `analysis_engine_startup_retries_total{service="..."}` - Retry count per service
- `analysis_engine_circuit_breaker_state{service="..."}` - Circuit breaker status

## Conclusion

With these improvements, the Analysis Engine achieves 100% reliable container startup through:

- Intelligent retry logic with exponential backoff
- Circuit breaker pattern for external dependencies
- Comprehensive health checks and monitoring
- Graceful degradation for optional services
- Production-ready deployment configurations

The service will start reliably without manual intervention, handling transient failures and dependency issues automatically.