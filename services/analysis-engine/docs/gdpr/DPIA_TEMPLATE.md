# Data Protection Impact Assessment (DPIA) Template
## Analysis Engine - Episteme

### Document Control
- **Version**: 1.0
- **Date**: [Assessment Date]
- **Assessor**: [Name]
- **Reviewer**: [DPO Name]
- **Status**: [Draft/Under Review/Approved]

---

## 1. Processing Overview

### 1.1 Processing Description
**Name of Processing Activity**: [e.g., "New ML Model for Code Pattern Analysis"]

**Brief Description**:
[Describe what personal data will be processed and why]

**Processing Start Date**: [Date]

**Processing Duration**: [Ongoing/Time-limited]

### 1.2 Purpose of Processing
- [ ] Primary Purpose: [Description]
- [ ] Secondary Purposes: [If any]

### 1.3 Legal Basis
Select applicable legal basis under GDPR Article 6:
- [ ] Consent (6.1.a)
- [ ] Contract (6.1.b)
- [ ] Legal Obligation (6.1.c)
- [ ] Vital Interests (6.1.d)
- [ ] Public Task (6.1.e)
- [ ] Legitimate Interests (6.1.f)

**Justification**: [Explain why this legal basis applies]

---

## 2. Data Categories and Sources

### 2.1 Personal Data Categories
| Category | Description | Special Category? | Volume |
|----------|-------------|------------------|---------|
| User Identifiers | Email, User ID | No | [Number] |
| Technical Data | IP addresses, User agents | No | [Number] |
| Analysis Data | Repository URLs, Code metrics | No | [Number] |
| [Add more] | | | |

### 2.2 Data Sources
- [ ] Directly from data subjects
- [ ] From third parties (specify): ___________
- [ ] Public sources (specify): ___________
- [ ] System-generated

### 2.3 Data Subjects
- [ ] Customers/Users
- [ ] Employees
- [ ] Business contacts
- [ ] Other: ___________

**Estimated number of data subjects**: [Number]

---

## 3. Processing Details

### 3.1 Processing Operations
Describe each processing operation:
1. **Collection**: [How data is collected]
2. **Storage**: [Where and how data is stored]
3. **Use**: [How data is used]
4. **Sharing**: [If and how data is shared]
5. **Retention**: [How long data is kept]
6. **Deletion**: [How data is deleted]

### 3.2 Technology and Methods
- **Systems Used**: [List systems/applications]
- **Security Measures**: 
  - [ ] Encryption at rest (AES-256)
  - [ ] Encryption in transit (TLS 1.3)
  - [ ] Access controls (RBAC)
  - [ ] Audit logging
  - [ ] Other: ___________

### 3.3 Data Flows
```
[Create a simple flow diagram]
User → Analysis Engine → Processing → Storage → Output
                ↓                        ↓
            Audit Logs              Encrypted DB
```

---

## 4. Necessity and Proportionality

### 4.1 Necessity Test
**Why is this processing necessary?**
[Explain why the processing cannot be achieved through other means]

### 4.2 Proportionality Test
**Is the processing proportionate to the purpose?**
- Data minimization applied: [Yes/No - Explain]
- Only necessary data collected: [Yes/No - Explain]
- Retention periods justified: [Yes/No - Explain]

### 4.3 Alternatives Considered
| Alternative | Why Rejected |
|-------------|--------------|
| [Alternative 1] | [Reason] |
| [Alternative 2] | [Reason] |

---

## 5. Consultation

### 5.1 Internal Stakeholders
- [ ] Legal Team - Date: _____ Outcome: _____
- [ ] Security Team - Date: _____ Outcome: _____
- [ ] Engineering Team - Date: _____ Outcome: _____
- [ ] DPO - Date: _____ Outcome: _____

### 5.2 External Consultation
- [ ] Data subjects consulted: [Yes/No - Details]
- [ ] Regulatory authority consulted: [Yes/No - Details]

---

## 6. Risk Assessment

### 6.1 Risk Identification
| Risk ID | Risk Description | Likelihood | Impact | Risk Level |
|---------|------------------|------------|---------|------------|
| R001 | Unauthorized access to personal data | Low | High | Medium |
| R002 | Data breach during transmission | Low | High | Medium |
| R003 | Excessive data retention | Medium | Medium | Medium |
| R004 | [Add more] | | | |

**Likelihood Scale**: Low (1), Medium (2), High (3)
**Impact Scale**: Low (1), Medium (2), High (3)
**Risk Level**: Low (1-2), Medium (3-4), High (5-9)

### 6.2 Risk Mitigation
| Risk ID | Mitigation Measure | Residual Risk | Owner |
|---------|-------------------|---------------|--------|
| R001 | Implement RBAC, MFA | Low | Security Team |
| R002 | Use TLS 1.3, VPN | Low | DevOps Team |
| R003 | Automated retention policies | Low | Data Team |
| R004 | [Mitigation] | | |

---

## 7. Data Subject Rights

### 7.1 Rights Implementation
Confirm implementation of data subject rights:
- [ ] Right of Access (Article 15) - API: `/api/gdpr/users/{id}/data`
- [ ] Right to Rectification (Article 16) - API: `/api/users/{id}`
- [ ] Right to Erasure (Article 17) - API: `/api/gdpr/users/{id}`
- [ ] Right to Restrict Processing (Article 18) - Implemented
- [ ] Right to Data Portability (Article 20) - API: `/api/gdpr/users/{id}/export`
- [ ] Right to Object (Article 21) - Via consent management

### 7.2 Communication Plan
How will data subjects be informed?
- [ ] Privacy notice update
- [ ] Email notification
- [ ] In-app notification
- [ ] Website announcement

---

## 8. International Transfers

### 8.1 Transfer Assessment
Will personal data be transferred outside the EEA?
- [ ] No
- [ ] Yes (complete section below)

If Yes:
- **Countries**: [List countries]
- **Transfer Mechanism**: 
  - [ ] Adequacy decision
  - [ ] Standard Contractual Clauses
  - [ ] Binding Corporate Rules
  - [ ] Other: ___________

---

## 9. Data Protection by Design

### 9.1 Privacy Measures
- [ ] Data minimization implemented
- [ ] Purpose limitation enforced
- [ ] Privacy defaults configured
- [ ] Anonymization/Pseudonymization used
- [ ] Regular privacy reviews scheduled

### 9.2 Technical Measures
- [ ] Encryption implemented
- [ ] Access logging enabled
- [ ] Retention automation configured
- [ ] Deletion cascades implemented
- [ ] Consent management integrated

---

## 10. Conclusions and Sign-off

### 10.1 Overall Risk Assessment
**Overall Risk Level**: [Low/Medium/High]

**DPO Recommendation**: [Proceed/Proceed with conditions/Do not proceed]

### 10.2 Conditions and Actions
| Action | Responsible | Deadline | Status |
|--------|-------------|----------|---------|
| [Action 1] | [Name] | [Date] | [Status] |
| [Action 2] | [Name] | [Date] | [Status] |

### 10.3 Approval

**Prepared by**: _________________ Date: _______
**Reviewed by (DPO)**: _________________ Date: _______
**Approved by**: _________________ Date: _______

---

## Appendices

### A. Supporting Documentation
- [ ] Privacy Notice (version: _____)
- [ ] Legal basis assessment
- [ ] Legitimate interests assessment (if applicable)
- [ ] Risk assessment details
- [ ] Technical architecture diagram

### B. Review Schedule
- **Next Review Date**: [Date]
- **Review Trigger Events**:
  - Significant change in processing
  - New technology implementation
  - Regulatory changes
  - Security incident

### C. Version History
| Version | Date | Author | Changes |
|---------|------|--------|---------|
| 1.0 | [Date] | [Name] | Initial assessment |

---

**DPIA Template Version**: 1.0
**Based on**: GDPR Articles 35-36, EDPB Guidelines
**For**: Episteme Analysis Engine