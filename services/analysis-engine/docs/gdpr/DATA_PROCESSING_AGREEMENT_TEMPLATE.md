# Data Processing Agreement (DPA) Template
## In accordance with GDPR Article 28

**Between**:

**Data Controller**: [Customer Name]  
Address: [Customer Address]  
("Controller")

**and**

**Data Processor**: Episteme AI Limited  
Address: [Company Address]  
("Processor")

**Effective Date**: [Date]

---

## 1. Definitions

In this Agreement:
- **"Data Protection Laws"** means GDPR (EU) 2016/679 and any applicable national data protection laws
- **"Personal Data"** means any information relating to an identified or identifiable natural person
- **"Processing"** has the meaning given in Article 4(2) GDPR
- **"Sub-processor"** means any third party engaged by Processor to process Personal Data
- **"Services"** means the Analysis Engine services provided under the Main Agreement

---

## 2. Processing of Personal Data

### 2.1 Relationship
The parties acknowledge that with regard to Personal Data, Customer is the Controller and Epistem<PERSON> is the Processor.

### 2.2 Processor's Obligations
The Processor shall:
- (a) Process Personal Data only on documented instructions from Controller (Annex 1)
- (b) Ensure persons authorized to process Personal Data have committed to confidentiality
- (c) Take all measures required pursuant to Article 32 GDPR
- (d) Respect conditions for engaging Sub-processors (Section 5)
- (e) Assist Controller in responding to data subject requests
- (f) Assist Controller in ensuring compliance with Articles 32-36 GDPR
- (g) Delete or return Personal Data at the end of services
- (h) Make available all information necessary to demonstrate compliance

### 2.3 Controller's Obligations
The Controller shall:
- (a) Ensure lawful basis for Processing
- (b) Provide clear instructions for Processing
- (c) Ensure accuracy of Personal Data
- (d) Comply with all applicable Data Protection Laws

---

## 3. Security of Processing

### 3.1 Technical and Organizational Measures
Processor implements and maintains the security measures detailed in Annex 2, including:
- Encryption of Personal Data at rest and in transit
- Access controls and authentication
- Regular security testing and assessment
- Incident detection and response procedures

### 3.2 Security Updates
Processor may update security measures provided:
- Overall security level is not reduced
- Controller is notified of material changes
- Updates comply with industry standards

---

## 4. Data Subject Rights

### 4.1 Assistance with Requests
Processor shall:
- Promptly notify Controller of any data subject request
- Provide reasonable assistance via technical measures
- Not respond directly unless authorized by Controller

### 4.2 Available Tools
Processor provides the following APIs for data subject rights:
- Access: `/api/gdpr/users/{id}/data`
- Rectification: `/api/users/{id}`
- Erasure: `/api/gdpr/users/{id}`
- Portability: `/api/gdpr/users/{id}/export`
- Consent Management: `/api/gdpr/consent`

---

## 5. Sub-processors

### 5.1 Authorized Sub-processors
Controller consents to the Sub-processors listed in Annex 3.

### 5.2 New Sub-processors
- Processor shall notify Controller 30 days before engaging new Sub-processors
- Controller may object within 14 days on reasonable grounds
- If objection cannot be resolved, Controller may terminate affected Services

### 5.3 Sub-processor Requirements
Processor ensures each Sub-processor:
- Is bound by written agreement with same data protection obligations
- Provides sufficient guarantees of technical and organizational measures
- Remains fully liable for Sub-processor performance

---

## 6. International Transfers

### 6.1 Transfer Restrictions
Personal Data shall not be transferred outside EEA unless:
- Adequate protection exists (adequacy decision)
- Appropriate safeguards are implemented (SCCs)
- Controller provides explicit written authorization

### 6.2 Transfer Mechanisms
For authorized transfers, parties agree to:
- Execute Standard Contractual Clauses (Module 2: Controller to Processor)
- Implement supplementary measures as required
- Conduct transfer impact assessments

---

## 7. Data Breach Notification

### 7.1 Processor Obligations
Upon becoming aware of a Personal Data breach, Processor shall:
- Notify Controller without undue delay (maximum 24 hours)
- Provide information required under Article 33(3) GDPR
- Cooperate in investigation and remediation
- Document all breaches regardless of risk level

### 7.2 Notification Contents
- Nature of breach
- Categories and number of data subjects affected
- Categories and number of records affected
- Likely consequences
- Measures taken or proposed

---

## 8. Audit and Compliance

### 8.1 Audit Rights
Controller has the right to:
- Request compliance information (quarterly maximum)
- Conduct audits with 30 days notice
- Use independent third-party auditor

### 8.2 Audit Process
- Audits conducted during business hours
- Processor provides reasonable cooperation
- Controller bears audit costs unless material non-compliance found
- Confidentiality agreements required

### 8.3 Certifications
Processor maintains:
- ISO 27001 certification
- SOC 2 Type II report
- Annual penetration testing

---

## 9. Data Return and Deletion

### 9.1 Upon Termination
Processor shall, at Controller's option:
- Return all Personal Data via secure transfer
- Delete all Personal Data and provide certification
- Combination of return and deletion

### 9.2 Retention Exceptions
Processor may retain Personal Data only:
- As required by applicable law
- Subject to confidentiality obligations
- With security measures maintained

### 9.3 Timeline
- Export available within 30 days
- Deletion completed within 60 days
- Certification provided within 90 days

---

## 10. Liability and Indemnification

### 10.1 Liability Allocation
- Each party liable for its own GDPR violations
- Processor liable for failure to comply with processor obligations
- Controller liable for unlawful processing instructions

### 10.2 Indemnification
Each party indemnifies the other against:
- Regulatory fines due to own violations
- Third-party claims from own breaches
- Costs from failure to comply with this Agreement

### 10.3 Liability Caps
Subject to Main Agreement except:
- No cap for willful misconduct or gross negligence
- Regulatory fines remain direct liability

---

## 11. Term and Termination

### 11.1 Duration
This Agreement:
- Commences on Effective Date
- Continues for duration of Main Agreement
- Survives for obligations relating to Personal Data

### 11.2 Termination Rights
Either party may terminate if:
- Other party materially breaches and fails to cure within 30 days
- Continued Processing would violate Data Protection Laws
- Regulatory authority requires termination

---

## 12. General Terms

### 12.1 Entire Agreement
This Agreement and Annexes constitute entire agreement regarding Processing.

### 12.2 Modification
Amendments must be in writing and signed by both parties.

### 12.3 Governing Law
This Agreement governed by laws of [Jurisdiction].

### 12.4 Order of Precedence
In case of conflict:
1. Data Protection Laws
2. This Agreement
3. Main Agreement

---

## Signatures

**For Controller**:  
Name: _________________________  
Title: _________________________  
Date: _________________________  
Signature: _____________________

**For Processor**:  
Name: _________________________  
Title: _________________________  
Date: _________________________  
Signature: _____________________

---

## ANNEX 1: Processing Instructions

### Subject Matter and Duration
- **Subject**: Processing Personal Data for code analysis services
- **Duration**: Term of Main Agreement
- **Nature**: Automated analysis of code repositories

### Type of Personal Data
- User account information
- Repository metadata
- Analysis results
- Usage analytics

### Categories of Data Subjects
- Controller's employees
- Controller's customers
- Controller's contractors

### Processing Operations
- Storage and hosting
- Analysis and computation
- Backup and recovery
- Security monitoring

---

## ANNEX 2: Technical and Organizational Measures

### Technical Measures
1. **Encryption**
   - At rest: AES-256
   - In transit: TLS 1.3
   - Key management: Google KMS

2. **Access Control**
   - Multi-factor authentication
   - Role-based access control
   - Principle of least privilege
   - Regular access reviews

3. **System Security**
   - Firewall protection
   - Intrusion detection
   - Regular patching
   - Vulnerability scanning

4. **Data Protection**
   - Data loss prevention
   - Backup encryption
   - Secure deletion
   - Retention controls

### Organizational Measures
1. **Personnel**
   - Background checks
   - Confidentiality agreements
   - Security training
   - Access provisioning/deprovisioning

2. **Physical Security**
   - Data center security (Google Cloud)
   - Access restrictions
   - Environmental controls
   - Asset disposal procedures

3. **Incident Management**
   - 24/7 monitoring
   - Incident response team
   - Escalation procedures
   - Breach notification process

4. **Business Continuity**
   - Disaster recovery plan
   - Regular backups
   - Redundancy measures
   - Recovery testing

---

## ANNEX 3: Authorized Sub-processors

| Sub-processor | Purpose | Location | Safeguards |
|---------------|---------|----------|------------|
| Google Cloud Platform | Infrastructure | EU/Global | SCCs, Adequacy |
| Redis Labs | Caching | EU | DPA, SCCs |
| [Others as applicable] | | | |

**Notification Method**: Email to Controller's designated contact

**Objection Process**:
1. Controller notifies objection within 14 days
2. Parties discuss alternatives in good faith
3. If unresolved, Controller may terminate affected Services

---

**Version**: 1.0  
**Template Updated**: [Date]  
**Based on**: GDPR Article 28, EDPB Guidance