# JWT Authentication Fix Documentation

## Problem Summary

The JWT authentication system in the Analysis Engine was reportedly "disabled" or not working properly. The error message "Authentication required for rate limiting" was appearing even with valid authentication tokens.

## Root Cause Analysis

1. **Incorrect Middleware Configuration**: The protected routes were using `OptionalAuthLayer` instead of the mandatory `AuthLayer`. This made authentication optional rather than required.

2. **Handler Expectations**: The API handlers (e.g., `analyze_repository`, `create_analysis`) were expecting a mandatory `AuthUser` extension, but with optional authentication, this extension wasn't always present when authentication failed.

3. **Missing Auth Requirement**: The `analyze_single_file` handler wasn't requiring authentication at all, even though it was in the protected routes section.

## Changes Made

### 1. Fixed Middleware Configuration (src/main.rs)
```rust
// Before:
.layer(api::auth_extractor::OptionalAuthLayer::new(state.clone()))

// After:
.layer(api::auth_extractor::AuthLayer::new(state.clone()))
```

### 2. Updated Single File Handler (src/api/handlers/single_file.rs)
```rust
// Before:
pub async fn analyze_single_file(
    State(_state): State<Arc<AppState>>,
    Json(request): Json<SingleFileAnalysisRequest>,
) -> Result<impl IntoResponse, ApiError>

// After:
pub async fn analyze_single_file(
    State(_state): State<Arc<AppState>>,
    Extension(_auth_user): Extension<crate::api::auth_extractor::AuthUser>,
    Json(request): Json<SingleFileAnalysisRequest>,
) -> Result<impl IntoResponse, ApiError>
```

## Authentication Methods Supported

The system now properly supports two authentication methods:

### 1. API Key Authentication
- Header: `x-api-key: ak_<key>`
- Test key for development: `ak_test_key_12345678`
- Production keys are validated against the Spanner database

### 2. JWT Token Authentication
- Header: `Authorization: Bearer <token>`
- JWT must be signed with the `JWT_SECRET` environment variable
- Required claims:
  - `sub`: User ID
  - `exp`: Expiration time
  - `iat`: Issued at time
  - `aud`: Must be "ccl-analysis-engine"
  - `iss`: Must match JWT_ISSUER env var (default: "ccl-analysis-engine")

## Testing Authentication

### 1. Generate a JWT Token
```bash
# Set the JWT secret
export JWT_SECRET=your-secret-key

# Use the provided JWT generator
node generate_jwt.js
```

### 2. Test with API Key
```bash
curl -X POST http://localhost:8001/api/v1/analyze \
  -H "Content-Type: application/json" \
  -H "x-api-key: ak_test_key_12345678" \
  -d '{"file_path": "test.js", "language": "javascript", "content": "console.log(\"test\");"}'
```

### 3. Test with JWT Token
```bash
curl -X POST http://localhost:8001/api/v1/analyze \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <your-jwt-token>" \
  -d '{"file_path": "test.js", "language": "javascript", "content": "console.log(\"test\");"}'
```

## Environment Variables

- `JWT_SECRET`: Secret key for JWT signing/verification (required for JWT auth)
- `JWT_ISSUER`: Expected issuer claim (default: "ccl-analysis-engine")
- `JWT_MAX_AGE_SECONDS`: Maximum token age (default: 604800 = 7 days)
- `JWT_REQUIRE_JTI`: Require JWT ID for revocation (default: true)
- `JWT_REQUIRE_DEVICE_BINDING`: Require device fingerprint (default: false)

## Rate Limiting

Once authenticated, users are subject to rate limiting based on:
- API Key: Rate limit stored in database
- JWT: Rate limit fetched from user profile in database
- Default rate limit: 100 requests per hour

## Security Features

1. **JWT Token Validation**:
   - Signature verification
   - Expiration checking
   - Audience validation
   - Issuer validation
   - Optional device binding
   - Token revocation support (via JTI)

2. **API Key Security**:
   - PBKDF2-style hashing with 100k iterations
   - Salt-based storage
   - Prefix-based efficient lookup
   - Expiration support

3. **Audit Logging**:
   - All authentication attempts are logged
   - Success/failure tracking
   - Client IP and user agent recording

## Troubleshooting

1. **"No authentication provided"**: Add either `x-api-key` header or `Authorization: Bearer` header
2. **"JWT_SECRET not configured"**: Set the `JWT_SECRET` environment variable
3. **"Invalid token signature"**: Ensure the JWT is signed with the same secret
4. **"Token has expired"**: Generate a new token with a future expiration
5. **"Invalid API key"**: Use the test key or ensure your key is in the database

## Conclusion

The JWT authentication system is now fully functional and properly integrated with Axum 0.8. All protected endpoints require authentication, and both API key and JWT token methods work correctly. The system includes comprehensive security features, rate limiting, and audit logging.