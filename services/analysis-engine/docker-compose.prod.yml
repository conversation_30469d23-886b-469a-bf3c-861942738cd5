version: '3.8'

# Production-ready Docker Compose configuration
# This demonstrates proper deployment patterns with real GCP services

services:
  # Redis cache service (managed Redis recommended for production)
  redis:
    image: redis:7-alpine
    container_name: analysis-engine-redis-prod
    ports:
      - "127.0.0.1:6379:6379"  # Only bind to localhost
    command: [
      "redis-server",
      "--maxmemory", "512mb",
      "--maxmemory-policy", "allkeys-lru",
      "--save", "",  # Disable persistence for cache
      "--tcp-keepalive", "60",
      "--timeout", "300"
    ]
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    volumes:
      - redis-prod-data:/data
    networks:
      - prod-network
    restart: always
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 768M
        reservations:
          cpus: '0.25'
          memory: 512M

  # Analysis Engine service
  analysis-engine:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        RUNTIME_TARGET: standard
    image: analysis-engine:prod
    container_name: analysis-engine-prod
    ports:
      - "127.0.0.1:8001:8001"  # Only bind to localhost, use reverse proxy
    environment:
      # Service configuration
      - PORT=8001
      - RUST_LOG=info,analysis_engine=warn
      - RUST_BACKTRACE=full
      
      # GCP configuration (use real services)
      - GOOGLE_APPLICATION_CREDENTIALS=/app/credentials/service-account.json
      - GCP_PROJECT_ID=${GCP_PROJECT_ID}
      - SPANNER_INSTANCE_ID=${SPANNER_INSTANCE_ID}
      - SPANNER_DATABASE_ID=${SPANNER_DATABASE_ID}
      - STORAGE_BUCKET=${STORAGE_BUCKET}
      - PUBSUB_TOPIC=${PUBSUB_TOPIC}
      
      # Redis configuration
      - REDIS_URL=redis://redis:6379
      
      # Resource limits
      - MEMORY_LIMIT_MB=3584
      - CPU_LIMIT_PERCENT=85
      - MAX_CONCURRENT_ANALYSES=30
      - ENABLE_RESOURCE_MONITORING=true
      - MONITORING_INTERVAL_SECONDS=5
      
      # Circuit breaker configuration
      - CIRCUIT_BREAKER_ENABLED=true
      - CIRCUIT_BREAKER_FAILURE_THRESHOLD=5
      - CIRCUIT_BREAKER_RECOVERY_TIMEOUT=60
      - CIRCUIT_BREAKER_HALF_OPEN_REQUESTS=3
      
      # Security configuration
      - ENABLE_SECURITY_HEADERS=true
      - ENABLE_RATE_LIMITING=true
      - RATE_LIMIT_PER_MINUTE=60
      - ENABLE_CSRF_PROTECTION=true
      
      # Production flags
      - ENVIRONMENT=production
      - ENABLE_DEBUG_ENDPOINTS=false
      - ENABLE_METRICS_ENDPOINT=true
    depends_on:
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health/ready"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 300s
    volumes:
      - ./credentials:/app/credentials:ro
      - ./logs:/app/logs
      - analysis-cache:/app/cache
    networks:
      - prod-network
    deploy:
      mode: replicated
      replicas: 2
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G
      restart_policy:
        condition: any
        delay: 5s
        max_attempts: 3
        window: 120s
      update_config:
        parallelism: 1
        delay: 10s
        failure_action: rollback
        monitor: 60s
        max_failure_ratio: 0.3
    restart: unless-stopped
    logging:
      driver: json-file
      options:
        max-size: "10m"
        max-file: "3"

  # Nginx reverse proxy with SSL termination
  nginx:
    image: nginx:alpine
    container_name: nginx-prod
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx-cache:/var/cache/nginx
    depends_on:
      - analysis-engine
    networks:
      - prod-network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: always
    logging:
      driver: json-file
      options:
        max-size: "10m"
        max-file: "3"

  # Prometheus for metrics collection
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus-prod
    ports:
      - "127.0.0.1:9090:9090"
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--storage.tsdb.retention.time=7d'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/prometheus-prod.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-prod-data:/prometheus
    networks:
      - prod-network
    restart: always
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 1G
        reservations:
          cpus: '0.25'
          memory: 512M

  # Grafana for visualization
  grafana:
    image: grafana/grafana:latest
    container_name: grafana-prod
    ports:
      - "127.0.0.1:3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=redis-datasource
    volumes:
      - grafana-prod-data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    depends_on:
      - prometheus
    networks:
      - prod-network
    restart: always
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M

volumes:
  redis-prod-data:
  analysis-cache:
  nginx-cache:
  prometheus-prod-data:
  grafana-prod-data:

networks:
  prod-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16