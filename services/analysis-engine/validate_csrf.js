#!/usr/bin/env node

/**
 * CSRF Protection Validation Script
 * Tests EPIS-003 CSRF implementation 
 */

const https = require('https');
const http = require('http');
const querystring = require('querystring');

const BASE_URL = 'http://localhost:8001';

console.log('🔍 CSRF Protection Validation - EPIS-003');
console.log('=========================================\n');

// Test 1: Check CSRF status endpoint
async function testCsrfStatus() {
    console.log('1. Testing CSRF status endpoint...');
    try {
        const response = await makeRequest('GET', '/security/csrf-status');
        const data = JSON.parse(response);
        console.log('   ✅ CSRF Status Response:', JSON.stringify(data, null, 2));
        
        if (data.csrf_protection && data.csrf_protection.enabled) {
            console.log('   ✅ CSRF protection is enabled\n');
            return true;
        } else {
            console.log('   ❌ CSRF protection not enabled\n');
            return false;
        }
    } catch (error) {
        console.log(`   ❌ Failed to get CSRF status: ${error.message}\n`);
        return false;
    }
}

// Test 2: GET request should set CSRF token
async function testGetRequestCsrfToken() {
    console.log('2. Testing GET request CSRF token generation...');
    try {
        const { response, headers } = await makeRequestWithHeaders('GET', '/api/v1/languages');
        
        // Check for CSRF token in cookie
        const setCookie = headers['set-cookie'];
        let csrfCookie = null;
        if (setCookie) {
            csrfCookie = setCookie.find(cookie => cookie.includes('csrf_token='));
        }
        
        // Check for CSRF token in header
        const csrfHeader = headers['x-csrf-token'];
        
        if (csrfCookie || csrfHeader) {
            console.log('   ✅ CSRF token provided in response');
            if (csrfCookie) console.log('   ✅ CSRF cookie set:', csrfCookie.split(';')[0]);
            if (csrfHeader) console.log('   ✅ CSRF header provided:', csrfHeader);
            console.log('');
            return { cookie: csrfCookie, header: csrfHeader };
        } else {
            console.log('   ❌ No CSRF token found in response');
            console.log('');
            return null;
        }
    } catch (error) {
        console.log(`   ❌ Failed to test GET request: ${error.message}\n`);
        return null;
    }
}

// Test 3: POST request without CSRF token should fail
async function testPostWithoutCsrf() {
    console.log('3. Testing POST request without CSRF token (should fail)...');
    try {
        const response = await makeRequest('POST', '/api/v1/analyze', JSON.stringify({
            content: 'test code',
            language: 'javascript'
        }), {
            'Content-Type': 'application/json'
        });
        
        console.log('   ❌ POST request succeeded without CSRF token (security issue!)');
        console.log('   Response:', response);
        console.log('');
        return false;
    } catch (error) {
        if (error.message.includes('403') || error.message.includes('CSRF')) {
            console.log('   ✅ POST request correctly rejected without CSRF token');
            console.log('   Error:', error.message);
            console.log('');
            return true;
        } else {
            console.log(`   ❌ Unexpected error: ${error.message}\n`);
            return false;
        }
    }
}

// Test 4: Health endpoints should not require CSRF
async function testHealthEndpointsSkipCsrf() {
    console.log('4. Testing health endpoints skip CSRF protection...');
    try {
        const healthResponse = await makeRequest('GET', '/health');
        const health = JSON.parse(healthResponse);
        
        if (health.status === 'healthy') {
            console.log('   ✅ Health endpoint accessible without CSRF');
            console.log('');
            return true;
        } else {
            console.log('   ❌ Health endpoint returned unexpected response');
            console.log('');
            return false;
        }
    } catch (error) {
        console.log(`   ❌ Health endpoint failed: ${error.message}\n`);
        return false;
    }
}

// Helper function to make HTTP requests
function makeRequest(method, path, data = null, headers = {}) {
    return new Promise((resolve, reject) => {
        const options = {
            hostname: 'localhost',
            port: 8001,
            path: path,
            method: method,
            headers: {
                ...headers
            }
        };

        if (data && method !== 'GET') {
            options.headers['Content-Length'] = Buffer.byteLength(data);
        }

        const req = http.request(options, (res) => {
            let body = '';
            res.on('data', (chunk) => {
                body += chunk;
            });

            res.on('end', () => {
                if (res.statusCode >= 200 && res.statusCode < 300) {
                    resolve(body);
                } else {
                    reject(new Error(`HTTP ${res.statusCode}: ${body}`));
                }
            });
        });

        req.on('error', (err) => {
            reject(err);
        });

        if (data && method !== 'GET') {
            req.write(data);
        }

        req.end();
    });
}

// Helper function to make HTTP requests with headers
function makeRequestWithHeaders(method, path, data = null, requestHeaders = {}) {
    return new Promise((resolve, reject) => {
        const options = {
            hostname: 'localhost',
            port: 8001,
            path: path,
            method: method,
            headers: {
                ...requestHeaders
            }
        };

        if (data && method !== 'GET') {
            options.headers['Content-Length'] = Buffer.byteLength(data);
        }

        const req = http.request(options, (res) => {
            let body = '';
            res.on('data', (chunk) => {
                body += chunk;
            });

            res.on('end', () => {
                if (res.statusCode >= 200 && res.statusCode < 300) {
                    resolve({ response: body, headers: res.headers });
                } else {
                    reject(new Error(`HTTP ${res.statusCode}: ${body}`));
                }
            });
        });

        req.on('error', (err) => {
            reject(err);
        });

        if (data && method !== 'GET') {
            req.write(data);
        }

        req.end();
    });
}

// Run all tests
async function runTests() {
    console.log('Starting CSRF protection validation tests...\n');
    
    // Test if service is running
    try {
        await makeRequest('GET', '/health');
        console.log('✅ Analysis Engine service is running\n');
    } catch (error) {
        console.log('❌ Analysis Engine service not running on localhost:8001');
        console.log('Please start the service first: cargo run\n');
        process.exit(1);
    }
    
    let passedTests = 0;
    const totalTests = 4;
    
    // Run tests
    if (await testCsrfStatus()) passedTests++;
    if (await testGetRequestCsrfToken()) passedTests++;
    if (await testPostWithoutCsrf()) passedTests++;
    if (await testHealthEndpointsSkipCsrf()) passedTests++;
    
    // Results
    console.log('='.repeat(50));
    console.log(`CSRF Protection Test Results: ${passedTests}/${totalTests} passed`);
    
    if (passedTests === totalTests) {
        console.log('🎉 All CSRF protection tests PASSED!');
        console.log('✅ EPIS-003: CSRF protection successfully implemented');
        console.log('\nCSRF Protection Features Validated:');
        console.log('- ✅ Double Submit Cookie pattern implemented');
        console.log('- ✅ CSRF tokens generated for GET requests');
        console.log('- ✅ CSRF validation enforced for POST/PUT/DELETE');
        console.log('- ✅ Health endpoints exempted from CSRF checks');
        console.log('- ✅ CSRF status monitoring endpoint available');
        process.exit(0);
    } else {
        console.log('❌ Some CSRF protection tests FAILED');
        console.log('🚨 EPIS-003: CSRF implementation needs fixes');
        process.exit(1);
    }
}

// Handle process termination
process.on('SIGINT', () => {
    console.log('\n\n⏹️  Test interrupted by user');
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n\n⏹️  Test terminated');
    process.exit(0);
});

// Run the tests
runTests().catch(error => {
    console.error('💥 Test execution failed:', error);
    process.exit(1);
});