#!/bin/bash
# Load test for Analysis Engine production deployment
set -euo pipefail

echo "🚀 Analysis Engine Production Load Testing"
echo "=========================================="

# Configuration
SERVICE_URL="https://analysis-engine-572735000332.us-central1.run.app"
RESULTS_DIR="load_test_results"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
REPORT_FILE="${RESULTS_DIR}/production_load_test_${TIMESTAMP}.txt"

# Create results directory
mkdir -p "$RESULTS_DIR"

# Test configuration
CONCURRENT_REQUESTS=10
TOTAL_REQUESTS=100
TEST_DURATION=60  # seconds

echo "Configuration:"
echo "- Service URL: $SERVICE_URL"
echo "- Concurrent requests: $CONCURRENT_REQUESTS"
echo "- Total requests: $TOTAL_REQUESTS"
echo "- Test duration: ${TEST_DURATION}s"
echo ""

# Check service health first
echo "🏥 Checking service health..."
if curl -sf "${SERVICE_URL}/health" > /dev/null; then
    echo "✅ Service is healthy"
else
    echo "❌ Service health check failed"
    exit 1
fi

# Simple load test using curl
echo ""
echo "🎯 Running load test..."
echo "Start time: $(date)"

# Function to make concurrent requests
run_concurrent_test() {
    local start_time=$(date +%s)
    success_count=0
    error_count=0
    total_response_time=0
    
    echo "Testing endpoint: /api/v1/languages"
    
    # Make concurrent requests
    for i in $(seq 1 $TOTAL_REQUESTS); do
        {
            local req_start=$(date +%s.%N)
            if response=$(curl -sf -w "\\n%{http_code}\\n%{time_total}" "${SERVICE_URL}/api/v1/languages" 2>/dev/null); then
                local http_code=$(echo "$response" | tail -2 | head -1)
                local response_time=$(echo "$response" | tail -1)
                
                if [ "$http_code" = "200" ]; then
                    echo "Request $i: Success (${response_time}s)"
                    ((success_count++))
                else
                    echo "Request $i: Failed (HTTP $http_code)"
                    ((error_count++))
                fi
            else
                echo "Request $i: Error"
                ((error_count++))
            fi
        } &
        
        # Control concurrency
        if [ $((i % CONCURRENT_REQUESTS)) -eq 0 ]; then
            wait
        fi
    done
    
    # Wait for remaining requests
    wait
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    # Calculate success rate
    local success_rate=$(echo "scale=2; ($success_count * 100) / $TOTAL_REQUESTS" | bc -l)
    
    echo ""
    echo "📊 Results Summary:"
    echo "=================="
    echo "Total Requests: $TOTAL_REQUESTS"
    echo "Successful: $success_count"
    echo "Failed: $error_count"
    echo "Success Rate: ${success_rate}%"
    echo "Test Duration: ${duration}s"
    echo "Requests/sec: $(echo "scale=2; $TOTAL_REQUESTS / $duration" | bc -l)"
}

# Run the test and save results
{
    run_concurrent_test
    
    echo ""
    echo "🧪 Testing Analysis Endpoint (single request)..."
    
    # Test analysis endpoint with a small repository
    analysis_start=$(date +%s.%N)
    analysis_response=$(curl -sf -X POST "${SERVICE_URL}/api/v1/analyze" \
        -H "Content-Type: application/json" \
        -d '{
            "repository_url": "https://github.com/golang/example",
            "branch": "master",
            "enable_patterns": true,
            "enable_embeddings": false
        }' -w "\\n%{http_code}\\n%{time_total}" 2>/dev/null || echo "Failed")
    
    if [ "$analysis_response" != "Failed" ]; then
        analysis_code=$(echo "$analysis_response" | tail -2 | head -1)
        analysis_time=$(echo "$analysis_response" | tail -1)
        echo "Analysis endpoint response: HTTP $analysis_code (${analysis_time}s)"
    else
        echo "Analysis endpoint test failed"
    fi
    
    echo ""
    echo "📈 Performance Targets:"
    echo "====================="
    echo "Target P95 Response Time: <100ms"
    echo "Target Success Rate: >99.5%"
    echo "Target Concurrent Support: 100+"
    
    echo ""
    echo "🎯 Recommendations:"
    if (( $(echo "$success_rate >= 99.5" | bc -l) )); then
        echo "✅ Success rate meets target"
    else
        echo "⚠️  Success rate below target (99.5%)"
    fi
    
    echo ""
    echo "📋 Full test completed at: $(date)"
    
} | tee "$REPORT_FILE"

echo ""
echo "💾 Results saved to: $REPORT_FILE"
echo ""
echo "🚀 Load Testing 1M LOC Repository"
echo "================================="
echo "For a full 1M LOC test, we recommend:"
echo "1. Use a large repository like kubernetes/kubernetes or torvalds/linux"
echo "2. Monitor the analysis progress via the API"
echo "3. Track memory and CPU usage in Cloud Monitoring"
echo "4. Verify completion time <5 minutes"
echo ""
echo "Example command for 1M LOC test:"
echo 'curl -X POST "'${SERVICE_URL}'/api/v1/analyze" \'
echo '  -H "Content-Type: application/json" \'
echo '  -d '"'"'{"repository_url": "https://github.com/kubernetes/kubernetes", "branch": "master"}'"'"