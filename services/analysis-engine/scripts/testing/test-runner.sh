#!/bin/bash
# Unified Testing Framework for Analysis Engine
# Consolidates all testing capabilities into a single, comprehensive tool

# Source shared libraries
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/../lib/logging.sh"
source "$SCRIPT_DIR/../lib/error-handling.sh"
source "$SCRIPT_DIR/../lib/validation.sh"
source "$SCRIPT_DIR/../config/environments.sh"
source "$SCRIPT_DIR/../config/defaults.sh"

# ============================================================================
# TESTING CONFIGURATION
# ============================================================================

# Test modes
TEST_MODE="${TEST_MODE:-comprehensive}"  # quick, comprehensive, performance, integration, all
TEST_LANGUAGES="${TEST_LANGUAGES:-}"     # Comma-separated list, empty for all
PARALLEL_EXECUTION="${PARALLEL_EXECUTION:-false}"
SKIP_BUILD="${SKIP_BUILD:-false}"
GENERATE_REPORTS="${GENERATE_REPORTS:-true}"
TEST_TIMEOUT="${TEST_TIMEOUT:-$DEFAULT_TEST_TIMEOUT}"

# Performance testing
PERFORMANCE_TARGET_LOC="${PERFORMANCE_TARGET_LOC:-1000000}"  # 1M LOC
PERFORMANCE_TARGET_TIME="${PERFORMANCE_TARGET_TIME:-300}"   # 5 minutes
PERFORMANCE_MIN_THROUGHPUT="${PERFORMANCE_MIN_THROUGHPUT:-3333}"  # LOC/second

# Test data configuration
TEST_DATA_DIR="${TEST_DATA_DIR:-$SCRIPT_DIR/../../test-data}"
RESULTS_DIR="${RESULTS_DIR:-$TEST_DATA_DIR/results}"
REPORTS_DIR="${REPORTS_DIR:-$TEST_DATA_DIR/reports}"

# Test counters
TESTS_TOTAL=0
TESTS_PASSED=0
TESTS_FAILED=0
TESTS_SKIPPED=0

# ============================================================================
# HELPER FUNCTIONS
# ============================================================================

# Initialize testing environment
initialize_testing() {
    log_header "ANALYSIS ENGINE UNIFIED TESTING FRAMEWORK"
    log_info "Test mode: $TEST_MODE"
    log_info "Languages: $([ -n "$TEST_LANGUAGES" ] && echo "$TEST_LANGUAGES" || echo "all")"
    log_info "Parallel execution: $PARALLEL_EXECUTION"
    log_info "Test timeout: ${TEST_TIMEOUT}s"
    
    # Create directories
    mkdir -p "$TEST_DATA_DIR" "$RESULTS_DIR" "$REPORTS_DIR"
    
    # Reset test counters
    TESTS_TOTAL=0
    TESTS_PASSED=0
    TESTS_FAILED=0
    TESTS_SKIPPED=0
    
    # Check prerequisites
    check_testing_prerequisites
}

# Check testing prerequisites
check_testing_prerequisites() {
    log_info "Checking testing prerequisites"
    
    # Check if analysis engine is built
    local analysis_engine_path="$SCRIPT_DIR/../../target/release/analysis-engine"
    if [[ ! -f "$analysis_engine_path" ]]; then
        if [[ "$SKIP_BUILD" == "true" ]]; then
            log_error "Analysis engine not found and --skip-build specified"
            exit $ERR_MISSING_DEPENDENCY
        fi
        
        log_info "Building analysis engine..."
        if ! build_analysis_engine; then
            log_error "Failed to build analysis engine"
            exit $ERR_DEPLOYMENT_FAILURE
        fi
    else
        log_success "Analysis engine binary found"
    fi
    
    # Check performance validator
    local validator_path="$SCRIPT_DIR/../../target/release/performance_validator"
    if [[ ! -f "$validator_path" ]]; then
        log_info "Building performance validator..."
        if ! build_performance_validator; then
            log_warn "Performance validator build failed, performance tests will be skipped"
        fi
    else
        log_success "Performance validator found"
    fi
    
    # Check required commands
    require_command "cargo" "rust"
    
    if [[ "$PARALLEL_EXECUTION" == "true" ]]; then
        if command -v parallel &> /dev/null; then
            log_success "GNU parallel found"
        else
            log_warn "GNU parallel not found, disabling parallel execution"
            PARALLEL_EXECUTION=false
        fi
    fi
}

# Build analysis engine
build_analysis_engine() {
    log_info "Building analysis engine (release mode)"
    
    cd "$SCRIPT_DIR/../../" || exit $ERR_RESOURCE_NOT_FOUND
    
    if cargo build --release; then
        log_success "Analysis engine built successfully"
        return 0
    else
        log_error "Analysis engine build failed"
        return 1
    fi
}

# Build performance validator
build_performance_validator() {
    log_info "Building performance validator"
    
    cd "$SCRIPT_DIR/../../" || exit $ERR_RESOURCE_NOT_FOUND
    
    if cargo build --release --bin performance_validator; then
        log_success "Performance validator built successfully"
        return 0
    else
        log_error "Performance validator build failed"
        return 1
    fi
}

# Execute test with tracking
execute_test() {
    local test_name="$1"
    local test_command="$2"
    local test_timeout="${3:-$TEST_TIMEOUT}"
    
    ((TESTS_TOTAL++))
    
    log_info "Running test: $test_name"
    
    local start_time=$(date +%s)
    local result_file="$RESULTS_DIR/${test_name}_$(date +%Y%m%d_%H%M%S).log"
    
    if timeout "$test_timeout" bash -c "$test_command" > "$result_file" 2>&1; then
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        
        log_success "Test passed: $test_name (${duration}s)"
        ((TESTS_PASSED++))
        return 0
    else
        local exit_code=$?
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        
        if [[ $exit_code -eq 124 ]]; then
            log_error "Test timed out: $test_name (${duration}s)"
        else
            log_error "Test failed: $test_name (${duration}s, exit code: $exit_code)"
        fi
        
        # Show last few lines of output for debugging
        if [[ -f "$result_file" ]]; then
            log_debug "Last 5 lines of test output:"
            tail -5 "$result_file" | while read -r line; do
                log_debug "  $line"
            done
        fi
        
        ((TESTS_FAILED++))
        return 1
    fi
}

# ============================================================================
# TEST SUITES
# ============================================================================

# Quick tests - basic functionality
run_quick_tests() {
    log_header "QUICK TESTS"
    
    # Unit tests
    execute_test "unit_tests" "cd '$SCRIPT_DIR/../../' && cargo test --lib"
    
    # Basic compilation check
    execute_test "compilation_check" "cd '$SCRIPT_DIR/../../' && cargo check --all-targets"
    
    # Basic health check (if service is running)
    if command -v curl &> /dev/null && [[ -n "${SERVICE_URL:-}" ]]; then
        execute_test "service_health" "curl -f -s --max-time 10 '$SERVICE_URL/health'"
    else
        log_info "Skipping service health test (curl not available or SERVICE_URL not set)"
        ((TESTS_SKIPPED++))
    fi
}

# Comprehensive tests - full test suite
run_comprehensive_tests() {
    log_header "COMPREHENSIVE TESTS"
    
    # Run quick tests first
    run_quick_tests
    
    # Integration tests
    execute_test "integration_tests" "cd '$SCRIPT_DIR/../../' && cargo test --test '*'"
    
    # Clippy linting
    execute_test "clippy_lint" "cd '$SCRIPT_DIR/../../' && cargo clippy -- -D warnings"
    
    # Format check
    execute_test "format_check" "cd '$SCRIPT_DIR/../../' && cargo fmt --check"
    
    # Security audit
    execute_test "security_audit" "cd '$SCRIPT_DIR/../../' && cargo audit" 300
    
    # Benchmark tests (if available)
    if [[ -f "$SCRIPT_DIR/../../benches" ]]; then
        execute_test "benchmark_tests" "cd '$SCRIPT_DIR/../../' && cargo bench --no-run"
    else
        log_info "No benchmark tests found, skipping"
        ((TESTS_SKIPPED++))
    fi
}

# Performance tests - validate performance claims
run_performance_tests() {
    log_header "PERFORMANCE TESTS"
    
    local validator_path="$SCRIPT_DIR/../../target/release/performance_validator"
    
    if [[ ! -f "$validator_path" ]]; then
        log_error "Performance validator not found, skipping performance tests"
        ((TESTS_SKIPPED++))
        return 1
    fi
    
    # Test with small repository first
    local test_repo="$TEST_DATA_DIR/test-repo"
    if [[ -d "$test_repo" ]]; then
        execute_test "performance_small_repo" "'$validator_path' '$test_repo'" 60
    else
        log_info "Test repository not found, creating minimal test data"
        create_test_repository
        execute_test "performance_minimal_test" "'$validator_path' '$test_repo'" 60
    fi
    
    # Test language support
    execute_test "language_support_test" "cd '$SCRIPT_DIR/../../' && cargo run --release --bin analysis-engine -- --help | grep -q 'languages'"
    
    # Performance validation with existing test data
    if [[ -d "$TEST_DATA_DIR/repositories" ]]; then
        local repo_count=$(find "$TEST_DATA_DIR/repositories" -maxdepth 1 -type d | wc -l)
        if [[ $repo_count -gt 1 ]]; then
            log_info "Found $((repo_count - 1)) test repositories"
            execute_test "performance_multiple_repos" "'$validator_path' '$TEST_DATA_DIR/repositories'" 600
        fi
    fi
}

# Integration tests - service integration
run_integration_tests() {
    log_header "INTEGRATION TESTS"
    
    # Database connectivity (if configured)
    if [[ -n "${SPANNER_INSTANCE_ID:-}" ]]; then
        execute_test "spanner_connectivity" "gcloud spanner databases execute-sql '$SPANNER_DATABASE_ID' --instance='$SPANNER_INSTANCE_ID' --sql='SELECT 1'"
    else
        log_info "Spanner not configured, skipping database tests"
        ((TESTS_SKIPPED++))
    fi
    
    # Redis connectivity (if configured)
    if [[ -n "${REDIS_URL:-}" ]]; then
        local redis_host=$(echo "$REDIS_URL" | sed 's|redis://||' | cut -d: -f1)
        local redis_port=$(echo "$REDIS_URL" | sed 's|redis://||' | cut -d: -f2)
        redis_port="${redis_port:-6379}"
        
        execute_test "redis_connectivity" "timeout 5 bash -c 'exec 3<>/dev/tcp/$redis_host/$redis_port && echo PING >&3 && read response <&3 && exec 3<&-'"
    else
        log_info "Redis not configured, skipping cache tests"
        ((TESTS_SKIPPED++))
    fi
    
    # Storage connectivity (if configured)
    if [[ -n "${STORAGE_BUCKET:-}" ]]; then
        execute_test "storage_connectivity" "gsutil ls 'gs://$STORAGE_BUCKET' > /dev/null"
    else
        log_info "Storage not configured, skipping storage tests"
        ((TESTS_SKIPPED++))
    fi
    
    # Service endpoints (if service is running)
    if [[ -n "${SERVICE_URL:-}" ]]; then
        execute_test "api_endpoints" "curl -f -s --max-time 10 '$SERVICE_URL/api/v1/languages'"
        execute_test "health_endpoint" "curl -f -s --max-time 10 '$SERVICE_URL/health'"
        execute_test "ready_endpoint" "curl -f -s --max-time 10 '$SERVICE_URL/ready'"
    else
        log_info "Service not running, skipping API tests"
        ((TESTS_SKIPPED++))
        ((TESTS_SKIPPED++))
        ((TESTS_SKIPPED++))
    fi
}

# Create minimal test repository
create_test_repository() {
    local test_repo="$TEST_DATA_DIR/test-repo"
    mkdir -p "$test_repo"
    
    # Create sample files
    cat > "$test_repo/main.rs" << 'EOF'
fn main() {
    println!("Hello, world!");
}

fn add(a: i32, b: i32) -> i32 {
    a + b
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_add() {
        assert_eq!(add(2, 3), 5);
    }
}
EOF

    cat > "$test_repo/lib.py" << 'EOF'
def hello_world():
    print("Hello, world!")

def add(a, b):
    return a + b

if __name__ == "__main__":
    hello_world()
EOF

    cat > "$test_repo/script.js" << 'EOF'
function helloWorld() {
    console.log("Hello, world!");
}

function add(a, b) {
    return a + b;
}

helloWorld();
EOF

    log_info "Created minimal test repository: $test_repo"
}

# ============================================================================
# REPORT GENERATION
# ============================================================================

# Generate test report
generate_test_report() {
    if [[ "$GENERATE_REPORTS" != "true" ]]; then
        return 0
    fi
    
    log_header "GENERATING TEST REPORT"
    
    local report_file="$REPORTS_DIR/test_report_$(date +%Y%m%d_%H%M%S).md"
    local timestamp=$(date)
    local total_tests=$TESTS_TOTAL
    local success_rate=0
    
    if [[ $total_tests -gt 0 ]]; then
        success_rate=$((TESTS_PASSED * 100 / total_tests))
    fi
    
    cat > "$report_file" << EOF
# Analysis Engine Test Report

**Generated:** $timestamp  
**Test Mode:** $TEST_MODE  
**Languages:** $([ -n "$TEST_LANGUAGES" ] && echo "$TEST_LANGUAGES" || echo "all")  
**Parallel Execution:** $PARALLEL_EXECUTION  

## Summary

- **Total Tests:** $TESTS_TOTAL
- **Passed:** $TESTS_PASSED
- **Failed:** $TESTS_FAILED
- **Skipped:** $TESTS_SKIPPED
- **Success Rate:** $success_rate%

## Test Results

$(if [[ $TESTS_FAILED -eq 0 ]]; then
    echo "✅ **ALL TESTS PASSED**"
elif [[ $success_rate -ge 80 ]]; then
    echo "⚠️ **TESTS PASSED WITH WARNINGS**"
else
    echo "❌ **TESTS FAILED**"
fi)

## Performance Metrics

$(if [[ -f "$SCRIPT_DIR/../../target/release/performance_validator" ]]; then
    echo "- Performance validator available"
    if [[ -d "$TEST_DATA_DIR/repositories" ]]; then
        echo "- Test repositories available"
    fi
else
    echo "- Performance validator not available"
fi)

## Test Artifacts

- **Results Directory:** \`$RESULTS_DIR\`
- **Test Data Directory:** \`$TEST_DATA_DIR\`
- **Report Location:** \`$report_file\`

## Recommendations

$(if [[ $TESTS_FAILED -gt 0 ]]; then
    echo "- Review failed test logs in \`$RESULTS_DIR\`"
    echo "- Fix failing tests before deployment"
fi

if [[ $TESTS_SKIPPED -gt 0 ]]; then
    echo "- Configure missing services to enable skipped tests"
fi

if [[ $success_rate -ge 95 ]]; then
    echo "- System ready for production deployment"
elif [[ $success_rate -ge 80 ]]; then
    echo "- System ready for staging deployment"
else
    echo "- System needs fixes before deployment"
fi)

---

*Generated by Analysis Engine Unified Testing Framework*
EOF

    log_success "Test report generated: $report_file"
}

# Print test summary
print_test_summary() {
    log_header "TEST EXECUTION SUMMARY"
    
    log_info "Test Mode: $TEST_MODE"
    log_info "Total Tests: $TESTS_TOTAL"
    
    if [[ $TESTS_PASSED -gt 0 ]]; then
        log_success "Passed: $TESTS_PASSED"
    fi
    
    if [[ $TESTS_FAILED -gt 0 ]]; then
        log_error "Failed: $TESTS_FAILED"
    fi
    
    if [[ $TESTS_SKIPPED -gt 0 ]]; then
        log_warn "Skipped: $TESTS_SKIPPED"
    fi
    
    local success_rate=0
    if [[ $TESTS_TOTAL -gt 0 ]]; then
        success_rate=$((TESTS_PASSED * 100 / TESTS_TOTAL))
        log_info "Success Rate: $success_rate%"
    fi
    
    # Determine overall result
    if [[ $TESTS_FAILED -eq 0 ]]; then
        log_success "🎉 All tests completed successfully!"
        return 0
    elif [[ $success_rate -ge 80 ]]; then
        log_warn "⚠️ Tests completed with warnings"
        return 0
    else
        log_error "❌ Tests failed"
        return 1
    fi
}

# ============================================================================
# COMMAND LINE INTERFACE
# ============================================================================

# Show usage information
show_usage() {
    cat << EOF
Usage: $0 [OPTIONS] [TEST_MODE]

Unified testing framework for the Analysis Engine.

TEST MODES:
    quick           Basic functionality tests (default: 5-10 minutes)
    comprehensive   Full test suite including linting and security
    performance     Performance validation and throughput testing
    integration     Service integration and connectivity tests
    all             All test suites combined

OPTIONS:
    --languages LANG    Comma-separated list of languages to test
    --parallel          Enable parallel test execution
    --skip-build        Skip building analysis engine and validator
    --no-reports        Skip test report generation
    --timeout SECONDS   Test timeout in seconds (default: $DEFAULT_TEST_TIMEOUT)
    --test-data DIR     Test data directory (default: $TEST_DATA_DIR)
    -h, --help          Show this help message

EXAMPLES:
    $0                              # Quick tests
    $0 comprehensive                # Full test suite
    $0 performance --parallel       # Performance tests with parallel execution
    $0 integration --timeout 60     # Integration tests with 60s timeout
    $0 all --languages rust,python  # All tests for specific languages

ENVIRONMENT VARIABLES:
    SERVICE_URL         Service URL for integration tests
    TEST_LANGUAGES      Default languages to test
    PARALLEL_EXECUTION  Enable parallel execution by default

EOF
}

# Parse command line arguments
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --languages)
                TEST_LANGUAGES="$2"
                shift 2
                ;;
            --parallel)
                PARALLEL_EXECUTION=true
                shift
                ;;
            --skip-build)
                SKIP_BUILD=true
                shift
                ;;
            --no-reports)
                GENERATE_REPORTS=false
                shift
                ;;
            --timeout)
                TEST_TIMEOUT="$2"
                shift 2
                ;;
            --test-data)
                TEST_DATA_DIR="$2"
                RESULTS_DIR="$TEST_DATA_DIR/results"
                REPORTS_DIR="$TEST_DATA_DIR/reports"
                shift 2
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            quick|comprehensive|performance|integration|all)
                TEST_MODE="$1"
                shift
                ;;
            *)
                log_error "Unknown option: $1"
                show_usage
                exit $ERR_INVALID_CONFIG
                ;;
        esac
    done
}

# ============================================================================
# MAIN EXECUTION
# ============================================================================

main() {
    # Parse arguments
    parse_arguments "$@"
    
    # Initialize testing environment
    initialize_testing
    
    # Execute tests based on mode
    case "$TEST_MODE" in
        "quick")
            run_quick_tests
            ;;
        "comprehensive")
            run_comprehensive_tests
            ;;
        "performance")
            run_performance_tests
            ;;
        "integration")
            run_integration_tests
            ;;
        "all")
            run_quick_tests
            run_comprehensive_tests
            run_performance_tests
            run_integration_tests
            ;;
        *)
            log_error "Invalid test mode: $TEST_MODE"
            show_usage
            exit $ERR_INVALID_CONFIG
            ;;
    esac
    
    # Generate report
    generate_test_report
    
    # Print summary and return appropriate exit code
    print_test_summary
}

# Execute main function
main "$@"