#!/bin/bash
# Production-ready startup wrapper with comprehensive error handling

set -euo pipefail

# Configuration
LOG_DIR="${LOG_DIR:-/app/logs}"
STARTUP_LOG="${LOG_DIR}/startup.log"
RETRY_DELAY="${RETRY_DELAY:-5}"
MAX_RETRIES="${MAX_RETRIES:-3}"
GRACEFUL_SHUTDOWN_TIMEOUT="${GRACEFUL_SHUTDOWN_TIMEOUT:-30}"

# Ensure log directory exists
mkdir -p "$LOG_DIR"

# Logging function
log() {
    local level=$1
    shift
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [$level] $*" | tee -a "$STARTUP_LOG"
}

# Error handler
handle_error() {
    local exit_code=$1
    local line_number=$2
    log "ERROR" "Script failed with exit code $exit_code at line $line_number"
    
    # Send alert if configured
    if [ -n "${ALERT_WEBHOOK_URL:-}" ]; then
        curl -s -X POST "$ALERT_WEBHOOK_URL" \
            -H "Content-Type: application/json" \
            -d "{\"text\":\"Analysis Engine startup failed: exit code $exit_code at line $line_number\"}" \
            >/dev/null 2>&1 || true
    fi
    
    exit "$exit_code"
}

# Set error trap
trap 'handle_error $? $LINENO' ERR

# Validate environment
validate_environment() {
    log "INFO" "Validating environment configuration..."
    
    local errors=0
    
    # Check required environment variables
    local required_vars=(
        "GCP_PROJECT_ID"
        "SPANNER_INSTANCE_ID"
        "SPANNER_DATABASE_ID"
        "STORAGE_BUCKET"
    )
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var:-}" ]; then
            log "ERROR" "Required environment variable $var is not set"
            errors=$((errors + 1))
        fi
    done
    
    # Check GCP credentials
    if [ -z "${GOOGLE_APPLICATION_CREDENTIALS:-}" ] && [ -z "${GOOGLE_CLOUD_PROJECT:-}" ]; then
        log "WARN" "No GCP credentials configured - will attempt default credentials"
    fi
    
    # Check resource limits
    if [ -n "${MEMORY_LIMIT_MB:-}" ]; then
        local available_memory=$(free -m | awk 'NR==2{printf "%d", $7}')
        if [ "$available_memory" -lt "${MEMORY_LIMIT_MB}" ]; then
            log "WARN" "Available memory ($available_memory MB) is less than configured limit (${MEMORY_LIMIT_MB} MB)"
        fi
    fi
    
    if [ $errors -gt 0 ]; then
        log "ERROR" "Environment validation failed with $errors errors"
        return 1
    fi
    
    log "INFO" "Environment validation passed"
    return 0
}

# Pre-startup checks
pre_startup_checks() {
    log "INFO" "Running pre-startup checks..."
    
    # Check disk space
    local available_space=$(df -m /app | awk 'NR==2 {print $4}')
    if [ "$available_space" -lt 1000 ]; then
        log "WARN" "Low disk space: ${available_space}MB available"
    fi
    
    # Test network connectivity
    if ! timeout 5 curl -s -o /dev/null https://www.googleapis.com; then
        log "WARN" "Cannot reach Google APIs - may affect startup"
    fi
    
    # Check if port is already in use
    if nc -z localhost "${PORT:-8001}" 2>/dev/null; then
        log "ERROR" "Port ${PORT:-8001} is already in use"
        return 1
    fi
    
    log "INFO" "Pre-startup checks completed"
    return 0
}

# Start the application with retry logic
start_application() {
    local attempt=1
    local pid=""
    
    while [ $attempt -le "$MAX_RETRIES" ]; do
        log "INFO" "Starting Analysis Engine (attempt $attempt/$MAX_RETRIES)..."
        
        # Start the application
        if [ -f /app/docker-entrypoint.sh ]; then
            /app/docker-entrypoint.sh &
        else
            /app/analysis-engine &
        fi
        pid=$!
        
        # Wait for startup to complete or fail
        local wait_time=0
        local max_wait=300  # 5 minutes
        
        while [ $wait_time -lt $max_wait ]; do
            # Check if process is still running
            if ! kill -0 "$pid" 2>/dev/null; then
                log "ERROR" "Analysis Engine process died (attempt $attempt)"
                wait "$pid" 2>/dev/null || true
                break
            fi
            
            # Check health endpoint
            if curl -s -f "http://localhost:${PORT:-8001}/health/ready" >/dev/null 2>&1; then
                log "INFO" "Analysis Engine started successfully"
                echo "$pid" > /var/run/analysis-engine.pid
                return 0
            fi
            
            sleep 5
            wait_time=$((wait_time + 5))
        done
        
        # Kill the process if it's still running
        if kill -0 "$pid" 2>/dev/null; then
            log "WARN" "Startup timeout reached, killing process"
            kill -TERM "$pid" 2>/dev/null || true
            sleep 5
            kill -KILL "$pid" 2>/dev/null || true
        fi
        
        attempt=$((attempt + 1))
        
        if [ $attempt -le "$MAX_RETRIES" ]; then
            log "INFO" "Waiting ${RETRY_DELAY}s before retry..."
            sleep "$RETRY_DELAY"
        fi
    done
    
    log "ERROR" "Failed to start Analysis Engine after $MAX_RETRIES attempts"
    return 1
}

# Graceful shutdown handler
graceful_shutdown() {
    log "INFO" "Initiating graceful shutdown..."
    
    if [ -f /var/run/analysis-engine.pid ]; then
        local pid=$(cat /var/run/analysis-engine.pid)
        
        if kill -0 "$pid" 2>/dev/null; then
            log "INFO" "Sending SIGTERM to process $pid"
            kill -TERM "$pid"
            
            # Wait for graceful shutdown
            local waited=0
            while [ $waited -lt "$GRACEFUL_SHUTDOWN_TIMEOUT" ] && kill -0 "$pid" 2>/dev/null; do
                sleep 1
                waited=$((waited + 1))
            done
            
            # Force kill if still running
            if kill -0 "$pid" 2>/dev/null; then
                log "WARN" "Process did not shut down gracefully, forcing kill"
                kill -KILL "$pid" 2>/dev/null || true
            fi
        fi
        
        rm -f /var/run/analysis-engine.pid
    fi
    
    log "INFO" "Shutdown complete"
}

# Set up signal handlers
trap graceful_shutdown SIGTERM SIGINT

# Main execution
main() {
    log "INFO" "Starting Analysis Engine startup wrapper"
    log "INFO" "Version: ${VERSION:-unknown}"
    log "INFO" "Environment: ${ENVIRONMENT:-production}"
    
    # Validate environment
    if ! validate_environment; then
        exit 1
    fi
    
    # Run pre-startup checks
    if ! pre_startup_checks; then
        exit 1
    fi
    
    # Start the application
    if ! start_application; then
        exit 1
    fi
    
    log "INFO" "Analysis Engine is running"
    
    # Monitor the application
    while true; do
        if [ -f /var/run/analysis-engine.pid ]; then
            local pid=$(cat /var/run/analysis-engine.pid)
            
            if ! kill -0 "$pid" 2>/dev/null; then
                log "ERROR" "Analysis Engine process died unexpectedly"
                rm -f /var/run/analysis-engine.pid
                
                # Attempt restart
                log "INFO" "Attempting automatic restart..."
                if ! start_application; then
                    log "ERROR" "Automatic restart failed"
                    exit 1
                fi
            fi
        fi
        
        sleep 30
    done
}

# Run main function
main "$@"