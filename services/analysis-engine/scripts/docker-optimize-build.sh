#!/bin/bash
set -euo pipefail

# Docker optimization and multi-arch build script
# This script builds optimized Docker images and measures their sizes

echo "=== Analysis Engine Docker Optimization Script ==="
echo ""

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to format bytes to human readable
format_bytes() {
    local bytes=$1
    if [ $bytes -gt 1073741824 ]; then
        echo "$(echo "scale=2; $bytes/1073741824" | bc) GB"
    elif [ $bytes -gt 1048576 ]; then
        echo "$(echo "scale=2; $bytes/1048576" | bc) MB"
    else
        echo "$(echo "scale=2; $bytes/1024" | bc) KB"
    fi
}

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if buildx is available
if ! docker buildx version &> /dev/null; then
    print_warning "Docker buildx not found. Installing buildx..."
    docker buildx install
fi

# Create results directory
RESULTS_DIR="./docker-optimization-results"
mkdir -p "$RESULTS_DIR"
TIMESTAMP=$(date +'%Y%m%d_%H%M%S')
RESULTS_FILE="$RESULTS_DIR/optimization_results_${TIMESTAMP}.md"

# Start results file
cat > "$RESULTS_FILE" << EOF
# Docker Optimization Results
**Date**: $(date)
**Platform**: $(uname -m)

## Build Information
- **Rust Version**: 1.82.0 (pinned)
- **Base Image**: rust:1.82.0-bookworm
- **Runtime Images**: debian:bookworm-slim, gcr.io/distroless/cc-debian12

EOF

# Build original Dockerfile for comparison
print_status "Building original Dockerfile for comparison..."
if docker build -f Dockerfile -t analysis-engine:original . > "$RESULTS_DIR/build_original.log" 2>&1; then
    ORIGINAL_SIZE=$(docker images --format "{{.Size}}" analysis-engine:original)
    print_status "Original image built successfully: $ORIGINAL_SIZE"
    echo "### Original Image" >> "$RESULTS_FILE"
    echo "- **Size**: $ORIGINAL_SIZE" >> "$RESULTS_FILE"
    echo "" >> "$RESULTS_FILE"
else
    print_warning "Failed to build original image, continuing with optimized build"
fi

# Setup buildx for multi-arch if not exists
print_status "Setting up Docker buildx for multi-arch builds..."
if ! docker buildx ls | grep -q multiarch-builder; then
    docker buildx create --name multiarch-builder --driver docker-container --use
else
    docker buildx use multiarch-builder
fi

# Build optimized standard image (current architecture only for testing)
print_status "Building optimized standard image..."
BUILD_START=$(date +%s)

if docker buildx build \
    -f Dockerfile.optimized \
    --build-arg RUST_VERSION=1.82.0 \
    --build-arg RUNTIME_TARGET=standard \
    --platform linux/$(uname -m | sed 's/x86_64/amd64/') \
    --load \
    -t analysis-engine:optimized-standard \
    . > "$RESULTS_DIR/build_optimized_standard.log" 2>&1; then
    
    BUILD_END=$(date +%s)
    BUILD_TIME=$((BUILD_END - BUILD_START))
    OPTIMIZED_SIZE=$(docker images --format "{{.Size}}" analysis-engine:optimized-standard)
    
    print_status "Optimized standard image built successfully"
    echo "### Optimized Standard Image" >> "$RESULTS_FILE"
    echo "- **Size**: $OPTIMIZED_SIZE" >> "$RESULTS_FILE"
    echo "- **Build Time**: ${BUILD_TIME}s" >> "$RESULTS_FILE"
    echo "" >> "$RESULTS_FILE"
else
    print_error "Failed to build optimized standard image"
    cat "$RESULTS_DIR/build_optimized_standard.log"
    exit 1
fi

# Build distroless image
print_status "Building distroless image..."
BUILD_START=$(date +%s)

if docker buildx build \
    -f Dockerfile.optimized \
    --build-arg RUST_VERSION=1.82.0 \
    --build-arg RUNTIME_TARGET=distroless \
    --platform linux/$(uname -m | sed 's/x86_64/amd64/') \
    --load \
    -t analysis-engine:distroless \
    . > "$RESULTS_DIR/build_distroless.log" 2>&1; then
    
    BUILD_END=$(date +%s)
    BUILD_TIME=$((BUILD_END - BUILD_START))
    DISTROLESS_SIZE=$(docker images --format "{{.Size}}" analysis-engine:distroless)
    
    print_status "Distroless image built successfully"
    echo "### Distroless Image" >> "$RESULTS_FILE"
    echo "- **Size**: $DISTROLESS_SIZE" >> "$RESULTS_FILE"
    echo "- **Build Time**: ${BUILD_TIME}s" >> "$RESULTS_FILE"
    echo "" >> "$RESULTS_FILE"
else
    print_error "Failed to build distroless image"
    cat "$RESULTS_DIR/build_distroless.log"
fi

# Analyze image layers
print_status "Analyzing image layers..."
echo "## Layer Analysis" >> "$RESULTS_FILE"
echo "" >> "$RESULTS_FILE"

if command -v dive &> /dev/null; then
    dive analysis-engine:optimized-standard --ci > "$RESULTS_DIR/dive_analysis.txt" 2>&1 || true
    echo "Layer analysis saved to dive_analysis.txt" >> "$RESULTS_FILE"
else
    docker history analysis-engine:optimized-standard --no-trunc > "$RESULTS_DIR/layer_history.txt"
    echo "### Optimized Standard Image Layers" >> "$RESULTS_FILE"
    echo '```' >> "$RESULTS_FILE"
    docker history analysis-engine:optimized-standard | head -20 >> "$RESULTS_FILE"
    echo '```' >> "$RESULTS_FILE"
fi

# Test multi-arch build capability (without pushing)
print_status "Testing multi-arch build capability..."
echo "" >> "$RESULTS_FILE"
echo "## Multi-Architecture Support" >> "$RESULTS_FILE"
echo "" >> "$RESULTS_FILE"

if docker buildx build \
    -f Dockerfile.optimized \
    --build-arg RUST_VERSION=1.82.0 \
    --build-arg RUNTIME_TARGET=standard \
    --platform linux/amd64,linux/arm64 \
    -t analysis-engine:multiarch \
    . --dry-run > "$RESULTS_DIR/multiarch_test.log" 2>&1; then
    
    print_status "Multi-arch build capability confirmed"
    echo "✅ Multi-arch build (amd64, arm64) supported" >> "$RESULTS_FILE"
else
    print_warning "Multi-arch build test failed"
    echo "❌ Multi-arch build test failed (see multiarch_test.log)" >> "$RESULTS_FILE"
fi

# Size comparison summary
echo "" >> "$RESULTS_FILE"
echo "## Size Comparison Summary" >> "$RESULTS_FILE"
echo "" >> "$RESULTS_FILE"
echo "| Image | Size | Reduction |" >> "$RESULTS_FILE"
echo "|-------|------|-----------|" >> "$RESULTS_FILE"

if [ ! -z "${ORIGINAL_SIZE:-}" ]; then
    echo "| Original | $ORIGINAL_SIZE | - |" >> "$RESULTS_FILE"
fi
echo "| Optimized Standard | $OPTIMIZED_SIZE | - |" >> "$RESULTS_FILE"
if [ ! -z "${DISTROLESS_SIZE:-}" ]; then
    echo "| Distroless | $DISTROLESS_SIZE | - |" >> "$RESULTS_FILE"
fi

# Binary size analysis
print_status "Analyzing binary size..."
docker run --rm analysis-engine:optimized-standard ls -lh /app/analysis-engine > "$RESULTS_DIR/binary_size.txt" 2>&1 || true

echo "" >> "$RESULTS_FILE"
echo "## Optimization Techniques Applied" >> "$RESULTS_FILE"
echo "" >> "$RESULTS_FILE"
echo "1. ✅ Pinned Rust version (1.82.0) for reproducibility" >> "$RESULTS_FILE"
echo "2. ✅ Multi-stage build with dependency caching" >> "$RESULTS_FILE"
echo "3. ✅ Minimal runtime images (bookworm-slim / distroless)" >> "$RESULTS_FILE"
echo "4. ✅ Removed unnecessary packages with --no-install-recommends" >> "$RESULTS_FILE"
echo "5. ✅ Strip binary for size reduction" >> "$RESULTS_FILE"
echo "6. ✅ Build cache mounts for faster rebuilds" >> "$RESULTS_FILE"
echo "7. ✅ Sparse registry protocol for faster downloads" >> "$RESULTS_FILE"
echo "8. ✅ Multi-architecture support (amd64, arm64)" >> "$RESULTS_FILE"

# Print results location
echo ""
print_status "Optimization complete! Results saved to: $RESULTS_FILE"
echo ""
echo "To view the results:"
echo "  cat $RESULTS_FILE"
echo ""
echo "To build for production with multi-arch:"
echo "  docker buildx build --platform linux/amd64,linux/arm64 -f Dockerfile.optimized -t analysis-engine:latest ."