#!/bin/bash
# Setup production alerting for Analysis Engine
set -euo pipefail

echo "🚨 Setting up Analysis Engine Production Alerting..."

# Configuration
PROJECT_ID="vibe-match-463114"
SERVICE_NAME="analysis-engine"
NOTIFICATION_EMAIL="${ALERT_EMAIL:-<EMAIL>}"

# Create notification channel (email)
echo "📧 Creating notification channel..."
CHANNEL_NAME="analysis-engine-alerts"

# Check if channel exists
EXISTING_CHANNEL=$(gcloud alpha monitoring channels list \
    --filter="displayName='${CHANNEL_NAME}'" \
    --format="value(name)" \
    --project=${PROJECT_ID} 2>/dev/null || echo "")

if [ -z "$EXISTING_CHANNEL" ]; then
    # Create email notification channel
    cat > /tmp/notification-channel.json << EOF
{
  "type": "email",
  "displayName": "${CHANNEL_NAME}",
  "labels": {
    "email_address": "${NOTIFICATION_EMAIL}"
  },
  "enabled": true
}
EOF

    CHANNEL_ID=$(gcloud alpha monitoring channels create \
        --channel-content-from-file=/tmp/notification-channel.json \
        --project=${PROJECT_ID} \
        --format="value(name)")
    echo "✅ Created notification channel: ${CHANNEL_ID}"
else
    CHANNEL_ID="$EXISTING_CHANNEL"
    echo "✅ Using existing notification channel: ${CHANNEL_ID}"
fi

# Create alert policies
echo "🚨 Creating alert policies..."

# 1. Service Down Alert
cat > /tmp/service-down-alert.yaml << EOF
displayName: "Analysis Engine - Service Down"
conditions:
  - displayName: "No successful requests for 5 minutes"
    conditionThreshold:
      filter: |
        resource.type="cloud_run_revision"
        AND resource.labels.service_name="${SERVICE_NAME}"
        AND metric.type="run.googleapis.com/request_count"
        AND metric.labels.response_code_class="2xx"
      aggregations:
        - alignmentPeriod: "300s"
          perSeriesAligner: "ALIGN_SUM"
      comparison: "COMPARISON_LT"
      thresholdValue: 1
      duration: "300s"
alertStrategy:
  autoClose: "1800s"
notificationChannels:
  - "${CHANNEL_ID}"
documentation:
  content: |
    The Analysis Engine service has not received any successful requests in the last 5 minutes.
    
    **Troubleshooting Steps:**
    1. Check Cloud Run logs: https://console.cloud.google.com/run/detail/us-central1/${SERVICE_NAME}/logs
    2. Verify service health: curl https://analysis-engine-572735000332.us-central1.run.app/health
    3. Check for deployment issues or quota limits
EOF

# 2. High Error Rate Alert
cat > /tmp/high-error-rate-alert.yaml << EOF
displayName: "Analysis Engine - High Error Rate"
conditions:
  - displayName: "Error rate > 5% for 5 minutes"
    conditionThreshold:
      filter: |
        resource.type="cloud_run_revision"
        AND resource.labels.service_name="${SERVICE_NAME}"
        AND metric.type="run.googleapis.com/request_count"
        AND metric.labels.response_code_class!="2xx"
      aggregations:
        - alignmentPeriod: "60s"
          perSeriesAligner: "ALIGN_RATE"
          crossSeriesReducer: "REDUCE_SUM"
      comparison: "COMPARISON_GT"
      thresholdValue: 0.05
      duration: "300s"
alertStrategy:
  autoClose: "1800s"
notificationChannels:
  - "${CHANNEL_ID}"
documentation:
  content: |
    The Analysis Engine error rate has exceeded 5% for the last 5 minutes.
    
    **Common Causes:**
    - Database connection issues
    - Redis cache problems
    - Invalid requests
    - Resource limits exceeded
EOF

# 3. High Memory Usage Alert
cat > /tmp/high-memory-alert.yaml << EOF
displayName: "Analysis Engine - High Memory Usage"
conditions:
  - displayName: "Memory utilization > 80% for 10 minutes"
    conditionThreshold:
      filter: |
        resource.type="cloud_run_revision"
        AND resource.labels.service_name="${SERVICE_NAME}"
        AND metric.type="run.googleapis.com/container/memory/utilizations"
      aggregations:
        - alignmentPeriod: "60s"
          perSeriesAligner: "ALIGN_MEAN"
          crossSeriesReducer: "REDUCE_MEAN"
      comparison: "COMPARISON_GT"
      thresholdValue: 0.8
      duration: "600s"
alertStrategy:
  autoClose: "1800s"
notificationChannels:
  - "${CHANNEL_ID}"
documentation:
  content: |
    The Analysis Engine memory usage has exceeded 80% for 10 minutes.
    
    **Actions:**
    - Check for memory leaks in recent deployments
    - Verify large repository analyses
    - Consider scaling up memory allocation
EOF

# 4. High Latency Alert
cat > /tmp/high-latency-alert.yaml << EOF
displayName: "Analysis Engine - High Latency"
conditions:
  - displayName: "P95 latency > 1000ms for 5 minutes"
    conditionThreshold:
      filter: |
        resource.type="cloud_run_revision"
        AND resource.labels.service_name="${SERVICE_NAME}"
        AND metric.type="run.googleapis.com/request_latencies"
      aggregations:
        - alignmentPeriod: "60s"
          perSeriesAligner: "ALIGN_PERCENTILE_95"
          crossSeriesReducer: "REDUCE_MEAN"
      comparison: "COMPARISON_GT"
      thresholdValue: 1000
      duration: "300s"
alertStrategy:
  autoClose: "1800s"
notificationChannels:
  - "${CHANNEL_ID}"
documentation:
  content: |
    The Analysis Engine P95 latency has exceeded 1 second for 5 minutes.
    
    **Check:**
    - Database query performance
    - Redis cache hit rate
    - Concurrent request load
    - External API response times
EOF

# Create the alert policies
for alert_file in service-down high-error-rate high-memory high-latency; do
    echo "Creating ${alert_file} alert..."
    if gcloud alpha monitoring policies create \
        --policy-from-file="/tmp/${alert_file}-alert.yaml" \
        --project=${PROJECT_ID} 2>&1 | grep -q "already exists"; then
        echo "⚠️  Alert ${alert_file} already exists"
    else
        echo "✅ Created ${alert_file} alert"
    fi
done

# Create uptime check
echo "🏥 Creating uptime check..."
cat > /tmp/uptime-check.yaml << EOF
displayName: "Analysis Engine Health Check"
monitoredResource:
  type: "uptime_url"
  labels:
    host: "analysis-engine-572735000332.us-central1.run.app"
    project_id: "${PROJECT_ID}"
httpCheck:
  path: "/health"
  port: 443
  requestMethod: "GET"
  useSsl: true
  validateSsl: true
period: "60s"
timeout: "10s"
contentMatchers:
  - content: "healthy"
    matcher: "CONTAINS_STRING"
selectedRegions:
  - "USA"
  - "EUROPE"
EOF

if gcloud alpha monitoring uptime create \
    --config-from-file=/tmp/uptime-check.yaml \
    --project=${PROJECT_ID} 2>&1 | grep -q "already exists"; then
    echo "⚠️  Uptime check already exists"
else
    echo "✅ Created uptime check"
fi

# Clean up temp files
rm -f /tmp/*-alert.yaml
rm -f /tmp/notification-channel.json
rm -f /tmp/uptime-check.yaml

echo ""
echo "✅ Alerting setup complete!"
echo ""
echo "📊 Alert Policies Created:"
echo "  1. Service Down - No requests for 5 minutes"
echo "  2. High Error Rate - >5% errors for 5 minutes"
echo "  3. High Memory Usage - >80% for 10 minutes"
echo "  4. High Latency - P95 >1s for 5 minutes"
echo ""
echo "📧 Notification Channel: ${NOTIFICATION_EMAIL}"
echo ""
echo "🏥 Uptime Check: Every 60 seconds from USA and Europe"
echo ""
echo "📱 Next Steps:"
echo "  1. Update notification email: export ALERT_EMAIL=<EMAIL>"
echo "  2. Add SMS/Slack notifications if needed"
echo "  3. Test alerts with: gcloud alpha monitoring policies list --project=${PROJECT_ID}"
echo "  4. View in console: https://console.cloud.google.com/monitoring/alerting"