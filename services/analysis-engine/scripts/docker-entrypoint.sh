#!/bin/sh
# Docker entrypoint script with startup reliability improvements

set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
STARTUP_TIMEOUT=${STARTUP_TIMEOUT:-300}  # 5 minutes default (Cloud Run limit)
HEALTH_CHECK_INTERVAL=${HEALTH_CHECK_INTERVAL:-5}
MAX_HEALTH_RETRIES=${MAX_HEALTH_RETRIES:-60}  # 5 minutes worth of checks
PORT=${PORT:-8001}

# Log with timestamp
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $*"
}

# Check if a TCP port is open
check_port() {
    local host=$1
    local port=$2
    timeout 2 sh -c "echo > /dev/tcp/$host/$port" 2>/dev/null
}

# Wait for optional dependencies with timeout
wait_for_optional_service() {
    local service_name=$1
    local host=$2
    local port=$3
    local max_wait=$4
    
    log "${YELLOW}Checking for optional service: $service_name${NC}"
    
    local waited=0
    while [ $waited -lt $max_wait ]; do
        if check_port "$host" "$port"; then
            log "${GREEN}✓ $service_name is available${NC}"
            return 0
        fi
        sleep 1
        waited=$((waited + 1))
    done
    
    log "${YELLOW}⚠ $service_name not available after ${max_wait}s, continuing without it${NC}"
    return 1
}

# Signal handlers for graceful shutdown
handle_sigterm() {
    log "${YELLOW}Received SIGTERM, initiating graceful shutdown...${NC}"
    if [ -n "$APP_PID" ]; then
        kill -TERM "$APP_PID" 2>/dev/null || true
        wait "$APP_PID" 2>/dev/null || true
    fi
    exit 0
}

handle_sigint() {
    log "${YELLOW}Received SIGINT, initiating graceful shutdown...${NC}"
    if [ -n "$APP_PID" ]; then
        kill -INT "$APP_PID" 2>/dev/null || true
        wait "$APP_PID" 2>/dev/null || true
    fi
    exit 0
}

# Set up signal handlers
trap handle_sigterm TERM
trap handle_sigint INT

# Pre-flight checks
log "${GREEN}Starting Analysis Engine container...${NC}"
log "Configuration:"
log "  - PORT: $PORT"
log "  - RUST_LOG: ${RUST_LOG:-info}"
log "  - STARTUP_TIMEOUT: ${STARTUP_TIMEOUT}s"
log "  - Environment: ${ENVIRONMENT:-production}"

# Check for optional Redis service (non-blocking)
if [ -n "$REDIS_URL" ]; then
    # Extract host and port from Redis URL
    REDIS_HOST=$(echo "$REDIS_URL" | sed -e 's|redis://||' -e 's|:.*||')
    REDIS_PORT=$(echo "$REDIS_URL" | sed -e 's|.*:||' -e 's|/.*||')
    wait_for_optional_service "Redis" "$REDIS_HOST" "$REDIS_PORT" 10 || true
fi

# Environment validation
if [ -z "$GCP_PROJECT_ID" ]; then
    log "${YELLOW}⚠ Warning: GCP_PROJECT_ID not set${NC}"
fi

if [ -z "$GOOGLE_APPLICATION_CREDENTIALS" ] && [ -z "$GOOGLE_CLOUD_PROJECT" ]; then
    log "${YELLOW}⚠ Warning: No GCP credentials configured${NC}"
fi

# Create a health check script for internal use
cat > /tmp/health_check.sh << 'EOF'
#!/bin/sh
response=$(curl -s -w "\n%{http_code}" http://localhost:${PORT:-8001}/health/live 2>/dev/null)
http_code=$(echo "$response" | tail -n1)
if [ "$http_code" = "200" ]; then
    exit 0
else
    exit 1
fi
EOF
chmod +x /tmp/health_check.sh

# Start the application in the background
log "${GREEN}Starting Analysis Engine service...${NC}"
/app/analysis-engine &
APP_PID=$!

# Monitor startup progress
log "${YELLOW}Monitoring startup progress...${NC}"
start_time=$(date +%s)
health_check_count=0

while true; do
    current_time=$(date +%s)
    elapsed=$((current_time - start_time))
    
    # Check if process is still running
    if ! kill -0 "$APP_PID" 2>/dev/null; then
        log "${RED}✗ Analysis Engine process died unexpectedly${NC}"
        exit 1
    fi
    
    # Check if startup timeout exceeded
    if [ $elapsed -gt $STARTUP_TIMEOUT ]; then
        log "${RED}✗ Startup timeout exceeded (${STARTUP_TIMEOUT}s)${NC}"
        kill -TERM "$APP_PID" 2>/dev/null || true
        exit 1
    fi
    
    # Try health check
    if /tmp/health_check.sh; then
        # Get detailed startup status
        startup_info=$(curl -s http://localhost:${PORT}/health 2>/dev/null | grep -o '"is_ready":[^,}]*' | cut -d: -f2)
        
        if [ "$startup_info" = "true" ]; then
            log "${GREEN}✓ Analysis Engine is fully ready after ${elapsed}s${NC}"
            break
        else
            # Service is responding but still initializing
            services_ready=$(curl -s http://localhost:${PORT}/health 2>/dev/null | grep -o '"services_ready":"[^"]*"' | cut -d'"' -f4)
            log "Startup in progress: $services_ready services ready..."
        fi
    else
        log "Waiting for service to start (${elapsed}s elapsed)..."
    fi
    
    health_check_count=$((health_check_count + 1))
    if [ $health_check_count -gt $MAX_HEALTH_RETRIES ]; then
        log "${RED}✗ Health check failed after $MAX_HEALTH_RETRIES attempts${NC}"
        kill -TERM "$APP_PID" 2>/dev/null || true
        exit 1
    fi
    
    sleep $HEALTH_CHECK_INTERVAL
done

# Display final startup information
log "${GREEN}=== Startup Complete ===${NC}"
curl -s http://localhost:${PORT}/health 2>/dev/null | jq '.' 2>/dev/null || curl -s http://localhost:${PORT}/health

# Wait for the application process
log "${GREEN}Analysis Engine is running (PID: $APP_PID)${NC}"
wait "$APP_PID"
exit_code=$?

log "Analysis Engine exited with code: $exit_code"
exit $exit_code