#!/bin/bash
set -euo pipefail

# Security Storage Validation Script
# Validates all security enhancements for the analysis engine storage layer

echo "🔒 Analysis Engine Security Storage Validation"
echo "=============================================="

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Validation results
declare -i TOTAL_TESTS=0
declare -i PASSED_TESTS=0
declare -i FAILED_TESTS=0

# Log test result
log_test() {
    local test_name="$1"
    local result="$2"
    local details="${3:-}"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    if [[ "$result" == "PASS" ]]; then
        echo -e "${GREEN}✓${NC} $test_name"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        if [[ -n "$details" ]]; then
            echo "   $details"
        fi
    else
        echo -e "${RED}✗${NC} $test_name"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        if [[ -n "$details" ]]; then
            echo -e "   ${RED}Error:${NC} $details"
        fi
    fi
}

# Check if required environment variables are set
check_environment() {
    echo -e "\n${BLUE}Checking Environment Configuration${NC}"
    
    local required_vars=(
        "GOOGLE_CLOUD_PROJECT"
        "GOOGLE_CLOUD_KMS_KEY_RING"
        "GOOGLE_CLOUD_KMS_CRYPTO_KEY"
        "GOOGLE_CLOUD_KMS_LOCATION"
    )
    
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var:-}" ]]; then
            log_test "Environment variable $var" "FAIL" "Variable not set"
        else
            log_test "Environment variable $var" "PASS" "Set to: ${!var}"
        fi
    done
}

# Test compilation with security features
test_compilation() {
    echo -e "\n${BLUE}Testing Compilation${NC}"
    
    if cargo check --features security-storage; then
        log_test "Compilation with security features" "PASS"
    else
        log_test "Compilation with security features" "FAIL" "Build failed"
        return 1
    fi
    
    if cargo clippy --features security-storage -- -D warnings; then
        log_test "Clippy linting" "PASS"
    else
        log_test "Clippy linting" "FAIL" "Linting errors found"
    fi
    
    if cargo fmt --check; then
        log_test "Code formatting" "PASS"
    else
        log_test "Code formatting" "FAIL" "Code not properly formatted"
    fi
}

# Test security modules
test_security_modules() {
    echo -e "\n${BLUE}Testing Security Modules${NC}"
    
    # Test encryption module
    if cargo test --features security-storage encryption -- --nocapture; then
        log_test "Encryption module tests" "PASS"
    else
        log_test "Encryption module tests" "FAIL" "Encryption tests failed"
    fi
    
    # Test audit logging module
    if cargo test --features security-storage audit -- --nocapture; then
        log_test "Audit logging tests" "PASS"
    else
        log_test "Audit logging tests" "FAIL" "Audit logging tests failed"
    fi
    
    # Test access control module
    if cargo test --features security-storage access_control -- --nocapture; then
        log_test "Access control tests" "PASS"
    else
        log_test "Access control tests" "FAIL" "Access control tests failed"
    fi
}

# Test KMS integration
test_kms_integration() {
    echo -e "\n${BLUE}Testing KMS Integration${NC}"
    
    # Test KMS client initialization
    if cargo run --bin test_kms_client --features security-storage; then
        log_test "KMS client initialization" "PASS"
    else
        log_test "KMS client initialization" "FAIL" "KMS client failed to initialize"
    fi
    
    # Test key creation and rotation
    if cargo run --bin test_key_rotation --features security-storage; then
        log_test "Key rotation functionality" "PASS"
    else
        log_test "Key rotation functionality" "FAIL" "Key rotation failed"
    fi
}

# Test database encryption
test_database_encryption() {
    echo -e "\n${BLUE}Testing Database Encryption${NC}"
    
    # Test field-level encryption
    if cargo test --features security-storage test_field_encryption -- --nocapture; then
        log_test "Field-level encryption" "PASS"
    else
        log_test "Field-level encryption" "FAIL" "Field encryption tests failed"
    fi
    
    # Test encrypted storage operations
    if cargo test --features security-storage test_encrypted_storage -- --nocapture; then
        log_test "Encrypted storage operations" "PASS"
    else
        log_test "Encrypted storage operations" "FAIL" "Encrypted storage tests failed"
    fi
}

# Test audit logging
test_audit_logging() {
    echo -e "\n${BLUE}Testing Audit Logging${NC}"
    
    # Test audit record creation
    if cargo test --features security-storage test_audit_creation -- --nocapture; then
        log_test "Audit record creation" "PASS"
    else
        log_test "Audit record creation" "FAIL" "Audit record creation failed"
    fi
    
    # Test audit immutability
    if cargo test --features security-storage test_audit_immutability -- --nocapture; then
        log_test "Audit log immutability" "PASS"
    else
        log_test "Audit log immutability" "FAIL" "Audit immutability tests failed"
    fi
    
    # Test threat detection
    if cargo test --features security-storage test_threat_detection -- --nocapture; then
        log_test "Threat detection" "PASS"
    else
        log_test "Threat detection" "FAIL" "Threat detection tests failed"
    fi
}

# Test access control
test_access_control() {
    echo -e "\n${BLUE}Testing Access Control${NC}"
    
    # Test RBAC enforcement
    if cargo test --features security-storage test_rbac_enforcement -- --nocapture; then
        log_test "RBAC enforcement" "PASS"
    else
        log_test "RBAC enforcement" "FAIL" "RBAC enforcement tests failed"
    fi
    
    # Test permission checks
    if cargo test --features security-storage test_permission_checks -- --nocapture; then
        log_test "Permission checking" "PASS"
    else
        log_test "Permission checking" "FAIL" "Permission checking tests failed"
    fi
}

# Test compliance features
test_compliance_features() {
    echo -e "\n${BLUE}Testing Compliance Features${NC}"
    
    # Test GDPR compliance
    if cargo test --features security-storage test_gdpr_compliance -- --nocapture; then
        log_test "GDPR compliance features" "PASS"
    else
        log_test "GDPR compliance features" "FAIL" "GDPR compliance tests failed"
    fi
    
    # Test data deletion
    if cargo test --features security-storage test_data_deletion -- --nocapture; then
        log_test "Data deletion (right to be forgotten)" "PASS"
    else
        log_test "Data deletion (right to be forgotten)" "FAIL" "Data deletion tests failed"
    fi
    
    # Test compliance reporting
    if cargo test --features security-storage test_compliance_reporting -- --nocapture; then
        log_test "Compliance reporting" "PASS"
    else
        log_test "Compliance reporting" "FAIL" "Compliance reporting tests failed"
    fi
}

# Test performance impact
test_performance_impact() {
    echo -e "\n${BLUE}Testing Performance Impact${NC}"
    
    # Build release version with security features
    if cargo build --release --features security-storage; then
        log_test "Release build with security features" "PASS"
    else
        log_test "Release build with security features" "FAIL" "Release build failed"
        return 1
    fi
    
    # Run performance benchmarks
    if cargo bench --features security-storage; then
        log_test "Performance benchmarks" "PASS"
    else
        log_test "Performance benchmarks" "FAIL" "Benchmarks failed to run"
    fi
    
    # Validate performance requirements (<5% degradation)
    if ./scripts/security/validate_performance_requirements.sh; then
        log_test "Performance requirements (<5% degradation)" "PASS"
    else
        log_test "Performance requirements (<5% degradation)" "FAIL" "Performance degradation exceeds 5%"
    fi
}

# Test security vulnerabilities
test_security_vulnerabilities() {
    echo -e "\n${BLUE}Testing Security Vulnerabilities${NC}"
    
    # Run cargo audit
    if cargo audit; then
        log_test "Cargo audit (dependency vulnerabilities)" "PASS"
    else
        log_test "Cargo audit (dependency vulnerabilities)" "FAIL" "Vulnerable dependencies found"
    fi
    
    # Run security scanner if available
    if command -v semgrep >/dev/null 2>&1; then
        if semgrep --config=auto --error --quiet services/analysis-engine/src/; then
            log_test "Static security analysis (semgrep)" "PASS"
        else
            log_test "Static security analysis (semgrep)" "FAIL" "Security issues found"
        fi
    else
        log_test "Static security analysis (semgrep)" "SKIP" "Semgrep not installed"
    fi
}

# Integration test with live service
test_integration() {
    echo -e "\n${BLUE}Testing Integration${NC}"
    
    # Start service in background for testing
    echo "Starting analysis engine with security features..."
    cargo run --release --features security-storage --bin analysis-engine &
    local service_pid=$!
    
    # Wait for service to start
    sleep 10
    
    # Test service health with security features
    if curl -f http://localhost:8001/health/security; then
        log_test "Security health endpoint" "PASS"
    else
        log_test "Security health endpoint" "FAIL" "Security health check failed"
    fi
    
    # Test encrypted API operations
    if ./scripts/security/test_encrypted_api.sh; then
        log_test "Encrypted API operations" "PASS"
    else
        log_test "Encrypted API operations" "FAIL" "Encrypted API tests failed"
    fi
    
    # Test audit logging in live service
    if ./scripts/security/test_audit_logging_live.sh; then
        log_test "Live audit logging" "PASS"
    else
        log_test "Live audit logging" "FAIL" "Live audit logging failed"
    fi
    
    # Clean up
    kill $service_pid 2>/dev/null || true
    wait $service_pid 2>/dev/null || true
}

# Main execution
main() {
    echo "Starting security storage validation..."
    echo "Timestamp: $(date)"
    echo ""
    
    # Check if we're in the right directory
    if [[ ! -f "Cargo.toml" ]] || [[ ! -d "src" ]]; then
        echo -e "${RED}Error:${NC} Please run this script from the analysis-engine directory"
        exit 1
    fi
    
    # Run all tests
    check_environment
    test_compilation
    test_security_modules
    test_kms_integration
    test_database_encryption
    test_audit_logging
    test_access_control
    test_compliance_features
    test_performance_impact
    test_security_vulnerabilities
    test_integration
    
    # Summary
    echo ""
    echo "=========================================="
    echo "Security Validation Summary"
    echo "=========================================="
    echo -e "Total Tests: ${BLUE}$TOTAL_TESTS${NC}"
    echo -e "Passed: ${GREEN}$PASSED_TESTS${NC}"
    echo -e "Failed: ${RED}$FAILED_TESTS${NC}"
    
    if [[ $FAILED_TESTS -eq 0 ]]; then
        echo -e "\n${GREEN}🎉 All security validation tests passed!${NC}"
        echo "The security storage enhancement is ready for production deployment."
        exit 0
    else
        echo -e "\n${RED}❌ Security validation failed!${NC}"
        echo "Please fix the failing tests before proceeding with deployment."
        exit 1
    fi
}

# Allow running specific test functions
if [[ $# -gt 0 ]]; then
    case "$1" in
        "environment") check_environment ;;
        "compilation") test_compilation ;;
        "security") test_security_modules ;;
        "kms") test_kms_integration ;;
        "encryption") test_database_encryption ;;
        "audit") test_audit_logging ;;
        "access") test_access_control ;;
        "compliance") test_compliance_features ;;
        "performance") test_performance_impact ;;
        "vulnerabilities") test_security_vulnerabilities ;;
        "integration") test_integration ;;
        *)
            echo "Usage: $0 [environment|compilation|security|kms|encryption|audit|access|compliance|performance|vulnerabilities|integration]"
            exit 1
            ;;
    esac
else
    main
fi