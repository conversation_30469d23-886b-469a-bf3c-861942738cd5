#!/bin/bash
# Error Handling Library for Analysis Engine Scripts
# Provides robust error handling and recovery mechanisms

# Guard against multiple sourcing
if [[ -n "${ANALYSIS_ENGINE_ERROR_HANDLING_LOADED:-}" ]]; then
    return 0
fi
ANALYSIS_ENGINE_ERROR_HANDLING_LOADED=1

# Source logging library
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/logging.sh"

# Error codes
readonly ERR_GENERAL=1
readonly ERR_MISSING_DEPENDENCY=2
readonly ERR_INVALID_CONFIG=3
readonly ERR_NETWORK_FAILURE=4
readonly ERR_AUTH_FAILURE=5
readonly ERR_DEPLOYMENT_FAILURE=6
readonly ERR_VALIDATION_FAILURE=7
readonly ERR_TIMEOUT=8
readonly ERR_PERMISSION_DENIED=9
readonly ERR_RESOURCE_NOT_FOUND=10

# Error tracking
LAST_ERROR_CODE=0
LAST_ERROR_MESSAGE=""
ERROR_CONTEXT=""

# Enable strict error handling
set_strict_mode() {
    set -euo pipefail
    log_debug "Strict mode enabled: -euo pipefail"
}

# Disable strict mode (for specific cases)
unset_strict_mode() {
    set +euo pipefail
    log_debug "Strict mode disabled"
}

# Error handler function
handle_error() {
    local exit_code=$?
    local line_number=$1
    local bash_lineno=$2
    local last_command="${3:-unknown}"
    local funcstack=("${FUNCNAME[@]:1}")
    
    LAST_ERROR_CODE=$exit_code
    LAST_ERROR_MESSAGE="Command failed with exit code $exit_code"
    
    # Log error details
    log_error "Script failed at line $line_number"
    log_error "Command: $last_command"
    log_error "Exit code: $exit_code"
    
    if [[ -n "$ERROR_CONTEXT" ]]; then
        log_error "Context: $ERROR_CONTEXT"
    fi
    
    # Print call stack
    if [[ ${#funcstack[@]} -gt 1 ]]; then
        log_error "Call stack:"
        local i
        for i in "${!funcstack[@]}"; do
            if [[ $i -gt 0 ]]; then
                log_error "  ${i}: ${funcstack[$i]}"
            fi
        done
    fi
    
    # Call cleanup if function exists
    if declare -f cleanup_on_error > /dev/null; then
        log_info "Running cleanup procedures..."
        cleanup_on_error || log_warn "Cleanup procedures failed"
    fi
    
    exit $exit_code
}

# Set up error trap
setup_error_handling() {
    set_strict_mode
    trap 'handle_error $LINENO $BASH_LINENO "$BASH_COMMAND"' ERR
    log_debug "Error handling initialized"
}

# Set error context for better debugging
set_error_context() {
    ERROR_CONTEXT="$1"
    log_debug "Error context set: $ERROR_CONTEXT"
}

# Clear error context
clear_error_context() {
    ERROR_CONTEXT=""
    log_debug "Error context cleared"
}

# Execute command with error handling
safe_execute() {
    local description="$1"
    shift
    local command=("$@")
    
    log_info "Executing: $description"
    log_debug "Command: ${command[*]}"
    
    set_error_context "$description"
    
    if "${command[@]}"; then
        log_success "$description completed successfully"
        clear_error_context
        return 0
    else
        local exit_code=$?
        log_error "$description failed with exit code $exit_code"
        clear_error_context
        return $exit_code
    fi
}

# Execute command with retry logic
retry_execute() {
    local max_attempts="$1"
    local delay="$2"
    local description="$3"
    shift 3
    local command=("$@")
    
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        log_info "Attempt $attempt/$max_attempts: $description"
        
        if safe_execute "$description" "${command[@]}"; then
            return 0
        fi
        
        if [[ $attempt -lt $max_attempts ]]; then
            log_warn "Attempt $attempt failed, retrying in ${delay}s..."
            sleep "$delay"
        fi
        
        ((attempt++))
    done
    
    log_error "All $max_attempts attempts failed for: $description"
    return $ERR_GENERAL
}

# Execute command with timeout
timeout_execute() {
    local timeout_duration="$1"
    local description="$2"
    shift 2
    local command=("$@")
    
    log_info "Executing with ${timeout_duration}s timeout: $description"
    
    if timeout "$timeout_duration" bash -c "$(printf '%q ' "${command[@]}")"; then
        log_success "$description completed within timeout"
        return 0
    else
        local exit_code=$?
        if [[ $exit_code -eq 124 ]]; then
            log_error "$description timed out after ${timeout_duration}s"
            return $ERR_TIMEOUT
        else
            log_error "$description failed with exit code $exit_code"
            return $exit_code
        fi
    fi
}

# Check if command exists
require_command() {
    local cmd="$1"
    local package="${2:-$cmd}"
    
    if ! command -v "$cmd" &> /dev/null; then
        log_error "Required command '$cmd' not found"
        log_error "Please install '$package' package"
        exit $ERR_MISSING_DEPENDENCY
    fi
    
    log_debug "Required command '$cmd' found"
}

# Check if file exists and is readable
require_file() {
    local file="$1"
    
    if [[ ! -f "$file" ]]; then
        log_error "Required file not found: $file"
        exit $ERR_RESOURCE_NOT_FOUND
    fi
    
    if [[ ! -r "$file" ]]; then
        log_error "Required file not readable: $file"
        exit $ERR_PERMISSION_DENIED
    fi
    
    log_debug "Required file accessible: $file"
}

# Check if directory exists and is accessible
require_directory() {
    local dir="$1"
    
    if [[ ! -d "$dir" ]]; then
        log_error "Required directory not found: $dir"
        exit $ERR_RESOURCE_NOT_FOUND
    fi
    
    if [[ ! -r "$dir" ]]; then
        log_error "Required directory not readable: $dir"
        exit $ERR_PERMISSION_DENIED
    fi
    
    log_debug "Required directory accessible: $dir"
}

# Validate environment variable
require_env_var() {
    local var_name="$1"
    local var_value="${!var_name:-}"
    
    if [[ -z "$var_value" ]]; then
        log_error "Required environment variable not set: $var_name"
        exit $ERR_INVALID_CONFIG
    fi
    
    log_debug "Required environment variable set: $var_name"
}

# Network connectivity check
check_network_connectivity() {
    local host="${1:-*******}"
    local timeout="${2:-5}"
    
    if ping -c 1 -W "$timeout" "$host" &> /dev/null; then
        log_debug "Network connectivity check passed (host: $host)"
        return 0
    else
        log_error "Network connectivity check failed (host: $host)"
        return $ERR_NETWORK_FAILURE
    fi
}

# URL accessibility check
check_url_accessible() {
    local url="$1"
    local timeout="${2:-10}"
    
    if curl -f -s --max-time "$timeout" "$url" &> /dev/null; then
        log_debug "URL accessibility check passed: $url"
        return 0
    else
        log_error "URL accessibility check failed: $url"
        return $ERR_NETWORK_FAILURE
    fi
}

# Service health check
check_service_health() {
    local service_url="$1"
    local expected_status="${2:-200}"
    local timeout="${3:-10}"
    
    local actual_status
    actual_status=$(curl -s -w "%{http_code}" -o /dev/null --max-time "$timeout" "$service_url" 2>/dev/null || echo "000")
    
    if [[ "$actual_status" == "$expected_status" ]]; then
        log_debug "Service health check passed: $service_url (status: $actual_status)"
        return 0
    else
        log_error "Service health check failed: $service_url (expected: $expected_status, actual: $actual_status)"
        return $ERR_NETWORK_FAILURE
    fi
}

# Disk space check
check_disk_space() {
    local path="${1:-.}"
    local min_space_mb="${2:-1024}"  # Default 1GB
    
    local available_space_kb
    available_space_kb=$(df "$path" | awk 'NR==2 {print $4}')
    local available_space_mb=$((available_space_kb / 1024))
    
    if [[ $available_space_mb -ge $min_space_mb ]]; then
        log_debug "Disk space check passed: ${available_space_mb}MB available (minimum: ${min_space_mb}MB)"
        return 0
    else
        log_error "Insufficient disk space: ${available_space_mb}MB available (minimum: ${min_space_mb}MB)"
        exit $ERR_RESOURCE_NOT_FOUND
    fi
}

# Memory check
check_available_memory() {
    local min_memory_mb="${1:-512}"  # Default 512MB
    
    local available_memory_kb
    available_memory_kb=$(awk '/MemAvailable/ {print $2}' /proc/meminfo 2>/dev/null || echo "0")
    local available_memory_mb=$((available_memory_kb / 1024))
    
    if [[ $available_memory_mb -ge $min_memory_mb ]]; then
        log_debug "Memory check passed: ${available_memory_mb}MB available (minimum: ${min_memory_mb}MB)"
        return 0
    else
        log_warn "Low memory warning: ${available_memory_mb}MB available (minimum: ${min_memory_mb}MB)"
        return 1
    fi
}

# Exit with proper cleanup
exit_with_error() {
    local exit_code="$1"
    local message="$2"
    
    log_error "$message"
    
    # Call cleanup if function exists
    if declare -f cleanup_on_error > /dev/null; then
        log_info "Running cleanup procedures..."
        cleanup_on_error || log_warn "Cleanup procedures failed"
    fi
    
    exit "$exit_code"
}

# Graceful shutdown handler
graceful_shutdown() {
    log_info "Received shutdown signal, cleaning up..."
    
    # Call cleanup if function exists
    if declare -f cleanup_on_shutdown > /dev/null; then
        cleanup_on_shutdown || log_warn "Shutdown cleanup failed"
    fi
    
    log_info "Graceful shutdown completed"
    exit 0
}

# Set up signal handlers
setup_signal_handlers() {
    trap graceful_shutdown SIGTERM SIGINT
    log_debug "Signal handlers initialized"
}

# Initialize error handling when sourced
if [[ "${BASH_SOURCE[0]}" != "${0}" ]]; then
    setup_error_handling
    setup_signal_handlers
fi