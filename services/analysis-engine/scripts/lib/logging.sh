#!/bin/bash
# Centralized Logging Library for Analysis Engine Scripts
# Provides consistent logging functionality across all scripts

# Guard against multiple sourcing
if [[ -n "${ANALYSIS_ENGINE_LOGGING_LOADED:-}" ]]; then
    return 0
fi
ANALYSIS_ENGINE_LOGGING_LOADED=1

# Color codes for terminal output
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly CYAN='\033[0;36m'
readonly MAGENTA='\033[0;35m'
readonly NC='\033[0m' # No Color

# Log level configuration
readonly LOG_LEVEL_ERROR=1
readonly LOG_LEVEL_WARN=2
readonly LOG_LEVEL_INFO=3
readonly LOG_LEVEL_DEBUG=4

# Default log level (can be overridden by ANALYSIS_ENGINE_LOG_LEVEL)
DEFAULT_LOG_LEVEL=${ANALYSIS_ENGINE_LOG_LEVEL:-$LOG_LEVEL_INFO}

# Log file configuration
LOG_FILE="${ANALYSIS_ENGINE_LOG_FILE:-}"
LOG_TO_FILE=${ANALYSIS_ENGINE_LOG_TO_FILE:-false}

# Internal logging function
_log() {
    local level=$1
    local level_name=$2
    local color=$3
    local message="$4"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    local script_name=$(basename "$0")
    
    # Check log level
    if [[ $level -gt ${DEFAULT_LOG_LEVEL:-$LOG_LEVEL_INFO} ]]; then
        return 0
    fi
    
    # Format message
    local formatted_message="${timestamp} [${level_name}] ${script_name}: ${message}"
    
    # Output to console with color
    if [[ -t 1 ]]; then  # Check if stdout is a terminal
        echo -e "${color}${formatted_message}${NC}"
    else
        echo "${formatted_message}"
    fi
    
    # Output to log file if configured
    if [[ "$LOG_TO_FILE" == "true" && -n "$LOG_FILE" ]]; then
        echo "${formatted_message}" >> "$LOG_FILE"
    fi
}

# Public logging functions
log_error() {
    _log $LOG_LEVEL_ERROR "ERROR" "$RED" "$*" >&2
}

log_warn() {
    _log $LOG_LEVEL_WARN "WARN" "$YELLOW" "$*"
}

log_info() {
    _log $LOG_LEVEL_INFO "INFO" "$BLUE" "$*"
}

log_debug() {
    _log $LOG_LEVEL_DEBUG "DEBUG" "$CYAN" "$*"
}

log_success() {
    _log $LOG_LEVEL_INFO "SUCCESS" "$GREEN" "$*"
}

# Special logging functions
log_header() {
    local title="$1"
    local border=$(printf '=%.0s' $(seq 1 ${#title}))
    echo -e "\n${CYAN}${border}${NC}"
    echo -e "${CYAN}${title}${NC}"
    echo -e "${CYAN}${border}${NC}"
}

log_step() {
    local step_num="$1"
    local description="$2"
    echo -e "\n${MAGENTA}▶ Step ${step_num}: ${description}${NC}"
}

log_separator() {
    echo -e "${CYAN}────────────────────────────────────────────────────────────────${NC}"
}

# Progress indicators
log_progress() {
    local current=$1
    local total=$2
    local description="$3"
    local percentage=$((current * 100 / total))
    local progress_bar=$(printf "█%.0s" $(seq 1 $((percentage / 5))))
    local spaces=$(printf " %.0s" $(seq 1 $((20 - percentage / 5))))
    
    echo -ne "\r${BLUE}Progress: [${progress_bar}${spaces}] ${percentage}% ${description}${NC}"
    
    if [[ $current -eq $total ]]; then
        echo -e "\n${GREEN}✓ Completed: ${description}${NC}"
    fi
}

# Counters for tracking operations
SUCCESS_COUNT=0
WARNING_COUNT=0
ERROR_COUNT=0

# Function to increment counters and log
log_and_count() {
    local type="$1"
    shift
    local message="$*"
    
    case "$type" in
        "success")
            ((SUCCESS_COUNT++))
            log_success "$message"
            ;;
        "warning")
            ((WARNING_COUNT++))
            log_warn "$message"
            ;;
        "error")
            ((ERROR_COUNT++))
            log_error "$message"
            ;;
        *)
            log_error "Invalid log type: $type"
            ;;
    esac
}

# Summary reporting
log_summary() {
    local total=$((SUCCESS_COUNT + WARNING_COUNT + ERROR_COUNT))
    
    if [[ $total -eq 0 ]]; then
        log_info "No operations tracked"
        return 0
    fi
    
    log_header "OPERATION SUMMARY"
    log_info "Total operations: $total"
    
    if [[ $SUCCESS_COUNT -gt 0 ]]; then
        log_success "Successful: $SUCCESS_COUNT"
    fi
    
    if [[ $WARNING_COUNT -gt 0 ]]; then
        log_warn "Warnings: $WARNING_COUNT"
    fi
    
    if [[ $ERROR_COUNT -gt 0 ]]; then
        log_error "Errors: $ERROR_COUNT"
    fi
    
    local success_rate=$((SUCCESS_COUNT * 100 / total))
    if [[ $ERROR_COUNT -eq 0 ]]; then
        log_success "Operation completed successfully (${success_rate}% success rate)"
        return 0
    else
        log_error "Operation completed with errors (${success_rate}% success rate)"
        return 1
    fi
}

# Initialize log file if configured
init_logging() {
    if [[ "$LOG_TO_FILE" == "true" && -n "$LOG_FILE" ]]; then
        # Create log directory if it doesn't exist
        local log_dir=$(dirname "$LOG_FILE")
        mkdir -p "$log_dir"
        
        # Initialize log file
        echo "=== Analysis Engine Script Log - $(date) ===" > "$LOG_FILE"
        log_info "Logging initialized to: $LOG_FILE"
    fi
}

# Cleanup logging
cleanup_logging() {
    if [[ "$LOG_TO_FILE" == "true" && -n "$LOG_FILE" ]]; then
        echo "=== Log session ended - $(date) ===" >> "$LOG_FILE"
    fi
}

# Set up trap for cleanup
trap cleanup_logging EXIT

# Initialize logging when sourced
if [[ "${BASH_SOURCE[0]}" != "${0}" ]]; then
    init_logging
fi