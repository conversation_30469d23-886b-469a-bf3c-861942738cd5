#!/bin/bash
# Deployment Utilities Library for Analysis Engine Scripts
# Provides deployment-specific helper functions

# Guard against multiple sourcing
if [[ -n "${ANALYSIS_ENGINE_DEPLOYMENT_UTILS_LOADED:-}" ]]; then
    return 0
fi
ANALYSIS_ENGINE_DEPLOYMENT_UTILS_LOADED=1

# Source dependencies
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/logging.sh"
source "$SCRIPT_DIR/error-handling.sh"
source "$SCRIPT_DIR/validation.sh"

# Deployment configuration defaults
readonly DEFAULT_TIMEOUT=600
readonly DEFAULT_MAX_INSTANCES=100
readonly DEFAULT_MIN_INSTANCES=1
readonly DEFAULT_CONCURRENCY=50
readonly DEFAULT_MEMORY="4Gi"
readonly DEFAULT_CPU="4"

# Build Docker image with optimizations
build_docker_image() {
    local image_name="$1"
    local tag="$2"
    local dockerfile="${3:-Dockerfile.simple}"
    local context_dir="${4:-.}"
    local platform="${5:-linux/amd64}"
    
    log_info "Building Docker image: ${image_name}:${tag}"
    
    local build_args=(
        "--platform" "$platform"
        "--tag" "${image_name}:${tag}"
        "--tag" "${image_name}:latest"
        "--file" "$dockerfile"
    )
    
    # Add build context
    build_args+=("$context_dir")
    
    set_error_context "Docker image build"
    
    if docker build "${build_args[@]}"; then
        log_success "Docker image built successfully: ${image_name}:${tag}"
        clear_error_context
        return 0
    else
        log_error "Docker image build failed: ${image_name}:${tag}"
        clear_error_context
        return $ERR_DEPLOYMENT_FAILURE
    fi
}

# Push Docker image to registry
push_docker_image() {
    local image_name="$1"
    local tag="$2"
    local push_latest="${3:-true}"
    
    log_info "Pushing Docker image: ${image_name}:${tag}"
    
    set_error_context "Docker image push"
    
    if docker push "${image_name}:${tag}"; then
        log_success "Docker image pushed: ${image_name}:${tag}"
        
        if [[ "$push_latest" == "true" ]]; then
            if docker push "${image_name}:latest"; then
                log_success "Docker image pushed: ${image_name}:latest"
            else
                log_warn "Failed to push latest tag"
            fi
        fi
        
        clear_error_context
        return 0
    else
        log_error "Docker image push failed: ${image_name}:${tag}"
        clear_error_context
        return $ERR_DEPLOYMENT_FAILURE
    fi
}

# Deploy to Cloud Run
deploy_to_cloud_run() {
    local service_name="$1"
    local image_url="$2"
    local project_id="$3"
    local region="${4:-us-central1}"
    local service_account="$5"
    local vpc_connector="$6"
    shift 6
    local env_vars=("$@")
    
    log_info "Deploying to Cloud Run: $service_name"
    
    local deploy_cmd=(
        "gcloud" "run" "deploy" "$service_name"
        "--image" "$image_url"
        "--platform" "managed"
        "--region" "$region"
        "--project" "$project_id"
        "--memory" "$DEFAULT_MEMORY"
        "--cpu" "$DEFAULT_CPU"
        "--timeout" "$DEFAULT_TIMEOUT"
        "--max-instances" "$DEFAULT_MAX_INSTANCES"
        "--min-instances" "$DEFAULT_MIN_INSTANCES"
        "--concurrency" "$DEFAULT_CONCURRENCY"
        "--allow-unauthenticated"
    )
    
    # Add service account if provided
    if [[ -n "$service_account" ]]; then
        deploy_cmd+=("--service-account" "$service_account")
    fi
    
    # Add VPC connector if provided
    if [[ -n "$vpc_connector" ]]; then
        deploy_cmd+=("--vpc-connector" "$vpc_connector")
    fi
    
    # Add environment variables
    for env_var in "${env_vars[@]}"; do
        deploy_cmd+=("--set-env-vars" "$env_var")
    done
    
    set_error_context "Cloud Run deployment"
    
    if "${deploy_cmd[@]}"; then
        log_success "Cloud Run deployment completed: $service_name"
        clear_error_context
        return 0
    else
        log_error "Cloud Run deployment failed: $service_name"
        clear_error_context
        return $ERR_DEPLOYMENT_FAILURE
    fi
}

# Get Cloud Run service URL
get_service_url() {
    local service_name="$1"
    local project_id="$2"
    local region="${3:-us-central1}"
    
    local service_url
    service_url=$(gcloud run services describe "$service_name" \
        --platform managed \
        --region "$region" \
        --project "$project_id" \
        --format 'value(status.url)' 2>/dev/null)
    
    if [[ -n "$service_url" ]]; then
        echo "$service_url"
        return 0
    else
        log_error "Failed to get service URL for: $service_name"
        return $ERR_RESOURCE_NOT_FOUND
    fi
}

# Wait for deployment to be ready
wait_for_deployment() {
    local service_name="$1"
    local project_id="$2"
    local region="${3:-us-central1}"
    local timeout="${4:-300}"
    local interval="${5:-10}"
    
    log_info "Waiting for deployment to be ready: $service_name"
    
    local start_time=$(date +%s)
    local end_time=$((start_time + timeout))
    
    while [[ $(date +%s) -lt $end_time ]]; do
        local status
        status=$(gcloud run services describe "$service_name" \
            --platform managed \
            --region "$region" \
            --project "$project_id" \
            --format 'value(status.conditions[0].status)' 2>/dev/null)
        
        if [[ "$status" == "True" ]]; then
            log_success "Deployment is ready: $service_name"
            return 0
        fi
        
        log_info "Deployment not ready yet, waiting ${interval}s..."
        sleep "$interval"
    done
    
    log_error "Deployment readiness timeout: $service_name"
    return $ERR_TIMEOUT
}

# Rollback deployment
rollback_deployment() {
    local service_name="$1"
    local project_id="$2"
    local region="${3:-us-central1}"
    local revision_to_rollback_to="$4"
    
    log_info "Rolling back deployment: $service_name"
    
    if [[ -n "$revision_to_rollback_to" ]]; then
        log_info "Rolling back to specific revision: $revision_to_rollback_to"
        local rollback_cmd=(
            "gcloud" "run" "services" "update-traffic" "$service_name"
            "--to-revisions" "$revision_to_rollback_to=100"
            "--region" "$region"
            "--project" "$project_id"
        )
    else
        log_info "Rolling back to previous revision"
        local rollback_cmd=(
            "gcloud" "run" "services" "update-traffic" "$service_name"
            "--to-latest"
            "--region" "$region"
            "--project" "$project_id"
        )
    fi
    
    set_error_context "Deployment rollback"
    
    if "${rollback_cmd[@]}"; then
        log_success "Rollback completed: $service_name"
        clear_error_context
        return 0
    else
        log_error "Rollback failed: $service_name"
        clear_error_context
        return $ERR_DEPLOYMENT_FAILURE
    fi
}

# Blue-green deployment
blue_green_deploy() {
    local service_name="$1"
    local new_image_url="$2"
    local project_id="$3"
    local region="${4:-us-central1}"
    local service_account="$5"
    local vpc_connector="$6"
    shift 6
    local env_vars=("$@")
    
    log_info "Starting blue-green deployment: $service_name"
    
    # Create new revision
    local temp_service="${service_name}-staging"
    
    # Deploy to staging
    if deploy_to_cloud_run "$temp_service" "$new_image_url" "$project_id" "$region" "$service_account" "$vpc_connector" "${env_vars[@]}"; then
        log_success "Staging deployment completed: $temp_service"
    else
        log_error "Staging deployment failed"
        return $ERR_DEPLOYMENT_FAILURE
    fi
    
    # Wait for staging to be ready
    if wait_for_deployment "$temp_service" "$project_id" "$region"; then
        log_success "Staging service is ready"
    else
        log_error "Staging service failed to become ready"
        return $ERR_DEPLOYMENT_FAILURE
    fi
    
    # Get staging URL for validation
    local staging_url
    staging_url=$(get_service_url "$temp_service" "$project_id" "$region")
    
    # Validate staging deployment
    if validate_service_health "$staging_url"; then
        log_success "Staging validation passed"
    else
        log_error "Staging validation failed"
        log_info "Cleaning up staging service..."
        gcloud run services delete "$temp_service" --region="$region" --project="$project_id" --quiet || true
        return $ERR_VALIDATION_FAILURE
    fi
    
    # Promote staging to production
    log_info "Promoting staging to production..."
    if deploy_to_cloud_run "$service_name" "$new_image_url" "$project_id" "$region" "$service_account" "$vpc_connector" "${env_vars[@]}"; then
        log_success "Production deployment completed"
        
        # Clean up staging
        log_info "Cleaning up staging service..."
        gcloud run services delete "$temp_service" --region="$region" --project="$project_id" --quiet || true
        
        return 0
    else
        log_error "Production deployment failed"
        return $ERR_DEPLOYMENT_FAILURE
    fi
}

# Check deployment prerequisites
check_deployment_prerequisites() {
    local project_id="$1"
    local service_account="$2"
    local image_name="$3"
    local tag="$4"
    
    log_info "Checking deployment prerequisites"
    
    # Check required commands
    require_command "gcloud" "google-cloud-sdk"
    require_command "docker" "docker"
    
    # Check authentication
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q "@"; then
        log_error "No active gcloud authentication found"
        exit $ERR_AUTH_FAILURE
    fi
    
    # Check project access
    if ! gcloud projects describe "$project_id" &>/dev/null; then
        log_error "Cannot access project: $project_id"
        exit $ERR_AUTH_FAILURE
    fi
    
    # Check service account
    if [[ -n "$service_account" ]] && ! validate_service_account "$service_account" "$project_id"; then
        log_error "Service account validation failed: $service_account"
        exit $ERR_INVALID_CONFIG
    fi
    
    # Check image exists
    if ! validate_registry_image "$image_name" "$tag"; then
        log_warn "Container Registry image not found, will be built: ${image_name}:${tag}"
    fi
    
    log_success "Deployment prerequisites check completed"
}

# Generate deployment configuration
generate_env_vars() {
    local project_id="$1"
    local instance_id="$2"
    local database_id="$3"
    local redis_url="$4"
    local storage_bucket="$5"
    local jwt_secret="$6"
    
    local env_vars=(
        "GCP_PROJECT_ID=$project_id"
        "GOOGLE_CLOUD_PROJECT=$project_id"
        "SPANNER_PROJECT_ID=$project_id"
        "SPANNER_INSTANCE_ID=$instance_id"
        "SPANNER_DATABASE_ID=$database_id"
        "REDIS_URL=$redis_url"
        "STORAGE_BUCKET=$storage_bucket"
        "STORAGE_BUCKET_NAME=$storage_bucket"
        "PUBSUB_TOPIC=analysis-events"
        "PUBSUB_TOPIC_EVENTS=analysis-events"
        "PUBSUB_TOPIC_PROGRESS=analysis-progress"
        "PUBSUB_TOPIC_PATTERNS=pattern-detected"
        "ENVIRONMENT=production"
        "RUST_LOG=info"
        "RUST_BACKTRACE=1"
        "MAX_CONCURRENT_ANALYSES=50"
        "MAX_FILE_SIZE_BYTES=10485760"
        "PARSE_TIMEOUT_SECONDS=30"
        "MAX_ANALYSIS_MEMORY_MB=2048"
        "MAX_DEPENDENCY_COUNT=10000"
    )
    
    # Add JWT configuration if secret provided
    if [[ -n "$jwt_secret" ]]; then
        env_vars+=(
            "JWT_SECRET=$jwt_secret"
            "ENABLE_AUTH=true"
            "CORS_ORIGINS=*"
            "API_KEY_HEADER=x-api-key"
            "RATE_LIMIT_PER_HOUR=1000"
            "JWT_ROTATION_DAYS=7"
            "ENABLE_AUDIT_LOGGING=true"
        )
    fi
    
    printf '%s\n' "${env_vars[@]}"
}

# Post-deployment validation
post_deployment_validation() {
    local service_name="$1"
    local project_id="$2"
    local region="${3:-us-central1}"
    local expected_tables=("${@:4}")
    
    log_info "Running post-deployment validation"
    
    # Get service URL
    local service_url
    service_url=$(get_service_url "$service_name" "$project_id" "$region")
    
    if [[ -z "$service_url" ]]; then
        log_error "Cannot get service URL"
        return $ERR_DEPLOYMENT_FAILURE
    fi
    
    log_info "Service URL: $service_url"
    
    # Validate service health
    if ! validate_service_health "$service_url"; then
        log_error "Service health validation failed"
        return $ERR_VALIDATION_FAILURE
    fi
    
    # Validate API endpoints
    if ! validate_api_endpoint "$service_url" "/ready"; then
        log_warn "Ready endpoint validation failed (non-critical)"
    fi
    
    # Validate response time
    if ! validate_response_time "$service_url/health" 200; then
        log_warn "Response time validation failed (>200ms)"
    fi
    
    log_success "Post-deployment validation completed"
    return 0
}

# Cleanup failed deployment
cleanup_failed_deployment() {
    local service_name="$1"
    local project_id="$2"
    local region="${3:-us-central1}"
    local image_name="$4"
    local tag="$5"
    
    log_info "Cleaning up failed deployment"
    
    # Delete Cloud Run service if it exists
    if gcloud run services describe "$service_name" --region="$region" --project="$project_id" &>/dev/null; then
        log_info "Deleting Cloud Run service: $service_name"
        gcloud run services delete "$service_name" --region="$region" --project="$project_id" --quiet || true
    fi
    
    # Clean up staging service if it exists
    local staging_service="${service_name}-staging"
    if gcloud run services describe "$staging_service" --region="$region" --project="$project_id" &>/dev/null; then
        log_info "Deleting staging service: $staging_service"
        gcloud run services delete "$staging_service" --region="$region" --project="$project_id" --quiet || true
    fi
    
    # Remove local Docker images
    if [[ -n "$image_name" && -n "$tag" ]]; then
        log_info "Removing local Docker images"
        docker rmi "${image_name}:${tag}" 2>/dev/null || true
        docker rmi "${image_name}:latest" 2>/dev/null || true
    fi
    
    log_info "Cleanup completed"
}

# Define cleanup function for error handling
cleanup_on_error() {
    if [[ -n "${DEPLOYMENT_SERVICE_NAME:-}" && -n "${DEPLOYMENT_PROJECT_ID:-}" ]]; then
        cleanup_failed_deployment "$DEPLOYMENT_SERVICE_NAME" "$DEPLOYMENT_PROJECT_ID" "${DEPLOYMENT_REGION:-us-central1}" "${DEPLOYMENT_IMAGE_NAME:-}" "${DEPLOYMENT_TAG:-}"
    fi
}