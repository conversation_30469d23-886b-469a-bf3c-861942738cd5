#!/bin/bash
# Validation Library for Analysis Engine Scripts
# Provides comprehensive validation functions for various components

# Guard against multiple sourcing
if [[ -n "${ANALYSIS_ENGINE_VALIDATION_LOADED:-}" ]]; then
    return 0
fi
ANALYSIS_ENGINE_VALIDATION_LOADED=1

# Source dependencies
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/logging.sh"
source "$SCRIPT_DIR/error-handling.sh"

# Validation results tracking
VALIDATION_PASSED=0
VALIDATION_FAILED=0
VALIDATION_WARNINGS=0

# Reset validation counters
reset_validation_counters() {
    VALIDATION_PASSED=0
    VALIDATION_FAILED=0
    VALIDATION_WARNINGS=0
}

# Validation wrapper function
validate() {
    local description="$1"
    shift
    local validation_function="$@"
    
    log_info "Validating: $description"
    
    if eval "$validation_function"; then
        log_and_count "success" "$description"
        ((VALIDATION_PASSED++))
        return 0
    else
        log_and_count "error" "$description"
        ((VALIDATION_FAILED++))
        return 1
    fi
}

# Validation wrapper for warnings
validate_with_warning() {
    local description="$1"
    shift
    local validation_function="$@"
    
    log_info "Validating: $description"
    
    if eval "$validation_function"; then
        log_and_count "success" "$description"
        ((VALIDATION_PASSED++))
        return 0
    else
        log_and_count "warning" "$description (non-critical)"
        ((VALIDATION_WARNINGS++))
        return 1
    fi
}

# Cloud Run service validation
validate_cloud_run_service() {
    local service_name="$1"
    local project_id="$2"
    local region="${3:-us-central1}"
    
    if gcloud run services describe "$service_name" \
        --project="$project_id" \
        --region="$region" \
        --format="value(status.url)" &>/dev/null; then
        log_debug "Cloud Run service '$service_name' exists and is accessible"
        return 0
    else
        log_debug "Cloud Run service '$service_name' not found or not accessible"
        return 1
    fi
}

# Spanner database validation
validate_spanner_database() {
    local instance_id="$1"
    local database_id="$2"
    local project_id="$3"
    
    if gcloud spanner databases describe "$database_id" \
        --instance="$instance_id" \
        --project="$project_id" &>/dev/null; then
        log_debug "Spanner database '$database_id' in instance '$instance_id' is accessible"
        return 0
    else
        log_debug "Spanner database '$database_id' in instance '$instance_id' not accessible"
        return 1
    fi
}

# Spanner table validation
validate_spanner_tables() {
    local instance_id="$1"
    local database_id="$2"
    local project_id="$3"
    shift 3
    local expected_tables=("$@")
    
    log_debug "Checking for ${#expected_tables[@]} required tables"
    
    local existing_tables
    existing_tables=$(gcloud spanner databases execute-sql "$database_id" \
        --instance="$instance_id" \
        --project="$project_id" \
        --sql="SELECT table_name FROM information_schema.tables WHERE table_schema = ''" \
        --format="value(table_name)" 2>/dev/null)
    
    local missing_tables=()
    for table in "${expected_tables[@]}"; do
        if ! echo "$existing_tables" | grep -q "^$table$"; then
            missing_tables+=("$table")
        fi
    done
    
    if [[ ${#missing_tables[@]} -eq 0 ]]; then
        log_debug "All ${#expected_tables[@]} required tables found"
        return 0
    else
        log_debug "Missing tables: ${missing_tables[*]}"
        return 1
    fi
}

# Service health endpoint validation
validate_service_health() {
    local service_url="$1"
    local endpoint="${2:-/health}"
    local expected_status="${3:-200}"
    local timeout="${4:-10}"
    
    local full_url="${service_url}${endpoint}"
    local actual_status
    actual_status=$(curl -s -w "%{http_code}" -o /dev/null --max-time "$timeout" "$full_url" 2>/dev/null || echo "000")
    
    if [[ "$actual_status" == "$expected_status" ]]; then
        log_debug "Health endpoint responding correctly: $full_url (status: $actual_status)"
        return 0
    else
        log_debug "Health endpoint check failed: $full_url (expected: $expected_status, actual: $actual_status)"
        return 1
    fi
}

# API endpoint validation
validate_api_endpoint() {
    local service_url="$1"
    local endpoint="$2"
    local method="${3:-GET}"
    local expected_status="${4:-200}"
    local auth_header="${5:-}"
    local timeout="${6:-10}"
    
    local full_url="${service_url}${endpoint}"
    local curl_args=(-s -w "%{http_code}" -o /dev/null --max-time "$timeout" -X "$method")
    
    if [[ -n "$auth_header" ]]; then
        curl_args+=(-H "$auth_header")
    fi
    
    local actual_status
    actual_status=$(curl "${curl_args[@]}" "$full_url" 2>/dev/null || echo "000")
    
    if [[ "$actual_status" == "$expected_status" ]]; then
        log_debug "API endpoint responding correctly: $method $full_url (status: $actual_status)"
        return 0
    else
        log_debug "API endpoint check failed: $method $full_url (expected: $expected_status, actual: $actual_status)"
        return 1
    fi
}

# Docker image validation
validate_docker_image() {
    local image_name="$1"
    local tag="${2:-latest}"
    
    local full_image="${image_name}:${tag}"
    
    if docker image inspect "$full_image" &>/dev/null; then
        log_debug "Docker image exists locally: $full_image"
        return 0
    else
        log_debug "Docker image not found locally: $full_image"
        return 1
    fi
}

# Container Registry image validation
validate_registry_image() {
    local image_name="$1"
    local tag="${2:-latest}"
    
    local full_image="${image_name}:${tag}"
    
    if gcloud container images describe "$full_image" &>/dev/null; then
        log_debug "Container Registry image exists: $full_image"
        return 0
    else
        log_debug "Container Registry image not found: $full_image"
        return 1
    fi
}

# IAM service account validation
validate_service_account() {
    local service_account="$1"
    local project_id="$2"
    
    if gcloud iam service-accounts describe "$service_account" --project="$project_id" &>/dev/null; then
        log_debug "Service account exists: $service_account"
        return 0
    else
        log_debug "Service account not found: $service_account"
        return 1
    fi
}

# IAM roles validation
validate_service_account_roles() {
    local service_account="$1"
    local project_id="$2"
    shift 2
    local required_roles=("$@")
    
    log_debug "Checking ${#required_roles[@]} required roles for service account"
    
    local existing_roles
    existing_roles=$(gcloud projects get-iam-policy "$project_id" \
        --flatten="bindings[].members" \
        --filter="bindings.members:serviceAccount:$service_account" \
        --format="value(bindings.role)" 2>/dev/null | sort)
    
    local missing_roles=()
    for role in "${required_roles[@]}"; do
        if ! echo "$existing_roles" | grep -q "^$role$"; then
            missing_roles+=("$role")
        fi
    done
    
    if [[ ${#missing_roles[@]} -eq 0 ]]; then
        log_debug "All ${#required_roles[@]} required roles found"
        return 0
    else
        log_debug "Missing roles: ${missing_roles[*]}"
        return 1
    fi
}

# Secret Manager secret validation
validate_secret() {
    local secret_name="$1"
    local project_id="$2"
    
    if gcloud secrets describe "$secret_name" --project="$project_id" &>/dev/null; then
        log_debug "Secret exists: $secret_name"
        return 0
    else
        log_debug "Secret not found: $secret_name"
        return 1
    fi
}

# Secret Manager secret access validation
validate_secret_access() {
    local secret_name="$1"
    local project_id="$2"
    
    if gcloud secrets versions access latest --secret="$secret_name" --project="$project_id" &>/dev/null; then
        log_debug "Secret is accessible: $secret_name"
        return 0
    else
        log_debug "Secret not accessible: $secret_name"
        return 1
    fi
}

# VPC connector validation
validate_vpc_connector() {
    local connector_name="$1"
    local region="$2"
    local project_id="$3"
    
    if gcloud compute networks vpc-access connectors describe "$connector_name" \
        --region="$region" \
        --project="$project_id" &>/dev/null; then
        log_debug "VPC connector exists: $connector_name"
        return 0
    else
        log_debug "VPC connector not found: $connector_name"
        return 1
    fi
}

# Storage bucket validation
validate_storage_bucket() {
    local bucket_name="$1"
    
    if gsutil ls "gs://$bucket_name" &>/dev/null; then
        log_debug "Storage bucket exists: $bucket_name"
        return 0
    else
        log_debug "Storage bucket not found: $bucket_name"
        return 1
    fi
}

# Pub/Sub topic validation
validate_pubsub_topic() {
    local topic_name="$1"
    local project_id="$2"
    
    if gcloud pubsub topics describe "$topic_name" --project="$project_id" &>/dev/null; then
        log_debug "Pub/Sub topic exists: $topic_name"
        return 0
    else
        log_debug "Pub/Sub topic not found: $topic_name"
        return 1
    fi
}

# Redis instance validation
validate_redis_connection() {
    local redis_url="$1"
    local timeout="${2:-5}"
    
    # Extract host and port from redis URL
    local redis_host=$(echo "$redis_url" | sed 's|redis://||' | cut -d: -f1)
    local redis_port=$(echo "$redis_url" | sed 's|redis://||' | cut -d: -f2)
    redis_port="${redis_port:-6379}"
    
    if timeout "$timeout" bash -c "exec 3<>/dev/tcp/$redis_host/$redis_port && echo -e 'PING\r\n' >&3 && read -r response <&3 && exec 3<&-" &>/dev/null; then
        log_debug "Redis connection successful: $redis_url"
        return 0
    else
        log_debug "Redis connection failed: $redis_url"
        return 1
    fi
}

# Environment variable validation
validate_environment_variables() {
    local required_vars=("$@")
    
    log_debug "Checking ${#required_vars[@]} required environment variables"
    
    local missing_vars=()
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var:-}" ]]; then
            missing_vars+=("$var")
        fi
    done
    
    if [[ ${#missing_vars[@]} -eq 0 ]]; then
        log_debug "All ${#required_vars[@]} required environment variables set"
        return 0
    else
        log_debug "Missing environment variables: ${missing_vars[*]}"
        return 1
    fi
}

# File permissions validation
validate_file_permissions() {
    local file_path="$1"
    local expected_permissions="$2"
    
    if [[ ! -f "$file_path" ]]; then
        log_debug "File not found: $file_path"
        return 1
    fi
    
    local actual_permissions
    actual_permissions=$(stat -c "%a" "$file_path" 2>/dev/null)
    
    if [[ "$actual_permissions" == "$expected_permissions" ]]; then
        log_debug "File permissions correct: $file_path ($actual_permissions)"
        return 0
    else
        log_debug "File permissions incorrect: $file_path (expected: $expected_permissions, actual: $actual_permissions)"
        return 1
    fi
}

# Performance validation
validate_response_time() {
    local url="$1"
    local max_response_time_ms="$2"
    local attempts="${3:-3}"
    
    log_debug "Testing response time for $url (max: ${max_response_time_ms}ms, attempts: $attempts)"
    
    local total_time=0
    local successful_requests=0
    
    for ((i=1; i<=attempts; i++)); do
        local start_time=$(date +%s%N)
        if curl -s -o /dev/null -w "%{http_code}" "$url" | grep -q "200"; then
            local end_time=$(date +%s%N)
            local response_time_ms=$(( (end_time - start_time) / 1000000 ))
            total_time=$((total_time + response_time_ms))
            ((successful_requests++))
            log_debug "Attempt $i: ${response_time_ms}ms"
        else
            log_debug "Attempt $i: failed"
        fi
    done
    
    if [[ $successful_requests -eq 0 ]]; then
        log_debug "All requests failed"
        return 1
    fi
    
    local average_time=$((total_time / successful_requests))
    
    if [[ $average_time -le $max_response_time_ms ]]; then
        log_debug "Average response time acceptable: ${average_time}ms (max: ${max_response_time_ms}ms)"
        return 0
    else
        log_debug "Average response time too high: ${average_time}ms (max: ${max_response_time_ms}ms)"
        return 1
    fi
}

# Validation summary
print_validation_summary() {
    local total=$((VALIDATION_PASSED + VALIDATION_FAILED + VALIDATION_WARNINGS))
    
    log_header "VALIDATION SUMMARY"
    
    if [[ $total -eq 0 ]]; then
        log_info "No validations performed"
        return 0
    fi
    
    log_info "Total validations: $total"
    
    if [[ $VALIDATION_PASSED -gt 0 ]]; then
        log_success "Passed: $VALIDATION_PASSED"
    fi
    
    if [[ $VALIDATION_WARNINGS -gt 0 ]]; then
        log_warn "Warnings: $VALIDATION_WARNINGS"
    fi
    
    if [[ $VALIDATION_FAILED -gt 0 ]]; then
        log_error "Failed: $VALIDATION_FAILED"
    fi
    
    local success_rate=$((VALIDATION_PASSED * 100 / total))
    
    if [[ $VALIDATION_FAILED -eq 0 ]]; then
        log_success "All validations passed (${success_rate}% success rate)"
        return 0
    elif [[ $success_rate -ge 80 ]]; then
        log_warn "Validation completed with warnings (${success_rate}% success rate)"
        return 0
    else
        log_error "Validation failed (${success_rate}% success rate)"
        return 1
    fi
}