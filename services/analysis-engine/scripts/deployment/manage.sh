#!/bin/bash
# Quick access to Analysis Engine management scripts
set -euo pipefail

SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

case "${1:-help}" in
    deploy)
        shift
        exec "${SCRIPT_DIR}/scripts/standard/deploy.sh" "$@"
        ;;
    health|check)
        shift
        exec "${SCRIPT_DIR}/scripts/standard/health-check.sh" "$@"
        ;;
    status)
        exec "${SCRIPT_DIR}/scripts/standard/maintenance.sh" status
        ;;
    logs)
        exec "${SCRIPT_DIR}/scripts/standard/maintenance.sh" logs
        ;;
    restart)
        exec "${SCRIPT_DIR}/scripts/standard/maintenance.sh" restart
        ;;
    scale)
        exec "${SCRIPT_DIR}/scripts/standard/maintenance.sh" scale
        ;;
    verify)
        exec "${SCRIPT_DIR}/scripts/standard/verify.sh"
        ;;
    help|--help|-h)
        echo "Analysis Engine Management"
        echo "========================"
        echo ""
        echo "Usage: ./manage.sh [command] [options]"
        echo ""
        echo "Commands:"
        echo "  deploy [options]  - Deploy the service"
        echo "  health [--detailed] - Check service health"
        echo "  status           - Show service status"
        echo "  logs             - View service logs"
        echo "  restart          - Restart the service"
        echo "  scale            - Scale service instances"
        echo "  verify           - Verify infrastructure"
        echo "  help             - Show this help"
        echo ""
        echo "Examples:"
        echo "  ./manage.sh deploy"
        echo "  ./manage.sh health --detailed"
        echo "  ./manage.sh logs"
        ;;
    *)
        echo "Unknown command: $1"
        echo "Run './manage.sh help' for usage"
        exit 1
        ;;
esac