#!/bin/bash
# Enhanced Deployment Script for Analysis Engine
# Provides robust deployment with error handling, validation, and rollback capabilities

# Source shared libraries
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/../lib/logging.sh"
source "$SCRIPT_DIR/../lib/error-handling.sh"
source "$SCRIPT_DIR/../lib/validation.sh"
source "$SCRIPT_DIR/../lib/deployment-utils.sh"
source "$SCRIPT_DIR/../config/environments.sh"
source "$SCRIPT_DIR/../config/defaults.sh"
source "$SCRIPT_DIR/../config/validation-rules.sh"

# ============================================================================
# DEPLOYMENT CONFIGURATION
# ============================================================================

# Default deployment parameters
DEPLOYMENT_MODE="${DEPLOYMENT_MODE:-standard}"  # standard, blue-green, rolling
IMAGE_TAG="${IMAGE_TAG:-latest}"
SKIP_BUILD="${SKIP_BUILD:-false}"
DRY_RUN="${DRY_RUN:-false}"
FORCE_DEPLOY="${FORCE_DEPLOY:-false}"
SKIP_VALIDATION="${SKIP_VALIDATION:-false}"
ENABLE_ROLLBACK="${ENABLE_ROLLBACK:-true}"

# Set global variables for cleanup
export DEPLOYMENT_SERVICE_NAME="$SERVICE_NAME"
export DEPLOYMENT_PROJECT_ID="$PROJECT_ID"
export DEPLOYMENT_REGION="$REGION"
export DEPLOYMENT_IMAGE_NAME="$IMAGE_NAME"
export DEPLOYMENT_TAG="$IMAGE_TAG"

# ============================================================================
# DEPLOYMENT FUNCTIONS
# ============================================================================

# Parse command line arguments
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --tag)
                IMAGE_TAG="$2"
                export DEPLOYMENT_TAG="$IMAGE_TAG"
                shift 2
                ;;
            --mode)
                DEPLOYMENT_MODE="$2"
                shift 2
                ;;
            --no-build|--skip-build)
                SKIP_BUILD=true
                shift
                ;;
            --dry-run)
                DRY_RUN=true
                shift
                ;;
            --force)
                FORCE_DEPLOY=true
                shift
                ;;
            --skip-validation)
                SKIP_VALIDATION=true
                shift
                ;;
            --no-rollback)
                ENABLE_ROLLBACK=false
                shift
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                show_usage
                exit $ERR_INVALID_CONFIG
                ;;
        esac
    done
}

# Show usage information
show_usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Deploy the Analysis Engine to Google Cloud Run with enhanced error handling.

OPTIONS:
    --tag TAG           Docker image tag (default: latest)
    --mode MODE         Deployment mode: standard|blue-green|rolling (default: standard)
    --skip-build        Skip Docker build, use existing image
    --dry-run           Show what would be deployed without executing
    --force             Force deployment even if validation fails
    --skip-validation   Skip pre-deployment validation
    --no-rollback       Disable automatic rollback on failure
    -h, --help          Show this help message

DEPLOYMENT MODES:
    standard            Direct deployment (fastest)
    blue-green          Zero-downtime deployment with staging validation
    rolling             Gradual deployment with traffic shifting

EXAMPLES:
    $0                                    # Standard deployment with latest tag
    $0 --tag v1.2.3                     # Deploy specific version
    $0 --mode blue-green --tag v1.2.3   # Zero-downtime deployment
    $0 --dry-run                         # Preview deployment without executing
    $0 --skip-build --tag existing      # Deploy existing image without building

ENVIRONMENT VARIABLES:
    All configuration is loaded from config/environments.sh
    Override specific values by setting environment variables before running.

EOF
}

# Pre-deployment validation
run_pre_deployment_validation() {
    if [[ "$SKIP_VALIDATION" == "true" ]]; then
        log_warn "Skipping pre-deployment validation"
        return 0
    fi
    
    log_header "PRE-DEPLOYMENT VALIDATION"
    
    # Reset validation counters
    reset_validation_counters
    
    # Check deployment prerequisites
    validate "Deployment prerequisites" "check_deployment_prerequisites '$PROJECT_ID' '$SERVICE_ACCOUNT' '$IMAGE_NAME' '$IMAGE_TAG'"
    
    # Validate configuration parameters
    validate "Deployment configuration" "validate_deployment_config '$PROJECT_ID' '$REGION' '$SERVICE_NAME' '$IMAGE_TAG' '$CLOUD_RUN_MEMORY' '$CLOUD_RUN_CPU' '$CLOUD_RUN_TIMEOUT' '$CLOUD_RUN_MIN_INSTANCES' '$CLOUD_RUN_MAX_INSTANCES' '$CLOUD_RUN_CONCURRENCY'"
    
    # Validate application configuration
    validate "Application configuration" "validate_application_config '$MAX_FILE_SIZE_BYTES' '$MAX_CONCURRENT_ANALYSES' '$PARSE_TIMEOUT_SECONDS' '$MAX_ANALYSIS_MEMORY_MB' '$MAX_DEPENDENCY_COUNT' '$RATE_LIMIT_PER_HOUR'"
    
    # Check infrastructure components
    validate "Spanner database" "validate_spanner_database '$SPANNER_INSTANCE_ID' '$SPANNER_DATABASE_ID' '$PROJECT_ID'"
    validate "Storage bucket" "validate_storage_bucket '$STORAGE_BUCKET'"
    validate "Service account" "validate_service_account '$SERVICE_ACCOUNT' '$PROJECT_ID'"
    
    # Check required IAM roles
    local required_roles=(
        "roles/spanner.databaseUser"
        "roles/storage.objectAdmin"
        "roles/pubsub.publisher"
        "roles/pubsub.viewer"
        "roles/aiplatform.user"
        "roles/monitoring.metricWriter"
        "roles/cloudtrace.agent"
    )
    validate_with_warning "Service account roles" "validate_service_account_roles '$SERVICE_ACCOUNT' '$PROJECT_ID' '${required_roles[@]}'"
    
    # Validate secrets
    validate_with_warning "JWT secret" "validate_secret '$JWT_SECRET_NAME' '$PROJECT_ID'"
    
    # Check VPC connector
    validate_with_warning "VPC connector" "validate_vpc_connector '$VPC_CONNECTOR' '$REGION' '$PROJECT_ID'"
    
    # Print validation summary
    print_validation_summary
    local validation_result=$?
    
    if [[ $validation_result -ne 0 && "$FORCE_DEPLOY" != "true" ]]; then
        log_error "Pre-deployment validation failed. Use --force to deploy anyway."
        exit $ERR_VALIDATION_FAILURE
    fi
    
    return 0
}

# Load JWT secret from Secret Manager
load_jwt_secret() {
    log_info "Loading JWT secret from Secret Manager"
    
    local jwt_secret
    jwt_secret=$(gcloud secrets versions access latest --secret="$JWT_SECRET_NAME" --project="$PROJECT_ID" 2>/dev/null)
    
    if [[ -n "$jwt_secret" ]]; then
        export JWT_SECRET="$jwt_secret"
        log_success "JWT secret loaded successfully"
        
        # Validate secret strength
        if validate_jwt_secret "$jwt_secret"; then
            log_success "JWT secret validation passed"
        else
            log_warn "JWT secret validation failed (weak secret)"
        fi
    else
        log_warn "JWT secret not found in Secret Manager"
        log_warn "Authentication will be disabled"
        export JWT_SECRET=""
    fi
}

# Build and push Docker image
build_and_push_image() {
    if [[ "$SKIP_BUILD" == "true" ]]; then
        log_info "Skipping Docker build (--skip-build specified)"
        
        # Validate that image exists
        if ! validate_registry_image "$IMAGE_NAME" "$IMAGE_TAG"; then
            log_error "Image not found in registry: ${IMAGE_NAME}:${IMAGE_TAG}"
            exit $ERR_RESOURCE_NOT_FOUND
        fi
        
        return 0
    fi
    
    log_header "DOCKER BUILD AND PUSH"
    
    # Build image
    if ! build_docker_image "$IMAGE_NAME" "$IMAGE_TAG"; then
        log_error "Docker image build failed"
        exit $ERR_DEPLOYMENT_FAILURE
    fi
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "Dry run mode - skipping image push"
        return 0
    fi
    
    # Push image
    if ! push_docker_image "$IMAGE_NAME" "$IMAGE_TAG"; then
        log_error "Docker image push failed"
        exit $ERR_DEPLOYMENT_FAILURE
    fi
    
    return 0
}

# Generate environment variables for deployment
generate_deployment_env_vars() {
    log_info "Generating deployment environment variables"
    
    # Load JWT secret
    load_jwt_secret
    
    # Generate environment variables
    local env_vars
    env_vars=$(generate_env_vars "$PROJECT_ID" "$SPANNER_INSTANCE_ID" "$SPANNER_DATABASE_ID" "$REDIS_URL" "$STORAGE_BUCKET" "$JWT_SECRET")
    
    # Convert to array
    mapfile -t ENV_VARS_ARRAY <<< "$env_vars"
    
    log_success "Generated ${#ENV_VARS_ARRAY[@]} environment variables"
}

# Standard deployment
deploy_standard() {
    log_header "STANDARD DEPLOYMENT"
    
    local image_url="${IMAGE_NAME}:${IMAGE_TAG}"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "Dry run mode - would deploy:"
        log_info "  Service: $SERVICE_NAME"
        log_info "  Image: $image_url"
        log_info "  Project: $PROJECT_ID"
        log_info "  Region: $REGION"
        return 0
    fi
    
    # Deploy to Cloud Run
    if deploy_to_cloud_run "$SERVICE_NAME" "$image_url" "$PROJECT_ID" "$REGION" "$SERVICE_ACCOUNT" "$VPC_CONNECTOR" "${ENV_VARS_ARRAY[@]}"; then
        log_success "Deployment completed successfully"
    else
        log_error "Deployment failed"
        exit $ERR_DEPLOYMENT_FAILURE
    fi
    
    # Wait for deployment to be ready
    if wait_for_deployment "$SERVICE_NAME" "$PROJECT_ID" "$REGION"; then
        log_success "Service is ready and healthy"
    else
        log_error "Service failed to become ready"
        exit $ERR_DEPLOYMENT_FAILURE
    fi
}

# Blue-green deployment
deploy_blue_green() {
    log_header "BLUE-GREEN DEPLOYMENT"
    
    local image_url="${IMAGE_NAME}:${IMAGE_TAG}"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "Dry run mode - would perform blue-green deployment"
        return 0
    fi
    
    if blue_green_deploy "$SERVICE_NAME" "$image_url" "$PROJECT_ID" "$REGION" "$SERVICE_ACCOUNT" "$VPC_CONNECTOR" "${ENV_VARS_ARRAY[@]}"; then
        log_success "Blue-green deployment completed successfully"
    else
        log_error "Blue-green deployment failed"
        exit $ERR_DEPLOYMENT_FAILURE
    fi
}

# Post-deployment validation
run_post_deployment_validation() {
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "Dry run mode - skipping post-deployment validation"
        return 0
    fi
    
    log_header "POST-DEPLOYMENT VALIDATION"
    
    # Reset validation counters
    reset_validation_counters
    
    # Run comprehensive validation
    if post_deployment_validation "$SERVICE_NAME" "$PROJECT_ID" "$REGION" "${REQUIRED_TABLES[@]}"; then
        log_success "Post-deployment validation passed"
    else
        log_error "Post-deployment validation failed"
        
        if [[ "$ENABLE_ROLLBACK" == "true" ]]; then
            log_warn "Initiating automatic rollback"
            rollback_deployment "$SERVICE_NAME" "$PROJECT_ID" "$REGION"
        fi
        
        exit $ERR_VALIDATION_FAILURE
    fi
}

# Print deployment summary
print_deployment_summary() {
    local service_url
    service_url=$(get_service_url "$SERVICE_NAME" "$PROJECT_ID" "$REGION" 2>/dev/null)
    
    log_header "DEPLOYMENT SUMMARY"
    
    log_info "Service: $SERVICE_NAME"
    log_info "Project: $PROJECT_ID"
    log_info "Region: $REGION"
    log_info "Image: ${IMAGE_NAME}:${IMAGE_TAG}"
    log_info "Mode: $DEPLOYMENT_MODE"
    
    if [[ -n "$service_url" ]]; then
        log_info "Service URL: $service_url"
        log_info "Health Check: $service_url/health"
        log_info "API Documentation: $service_url/docs"
    fi
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "Status: DRY RUN COMPLETED"
    else
        log_success "Status: DEPLOYMENT SUCCESSFUL"
    fi
}

# ============================================================================
# MAIN EXECUTION
# ============================================================================

main() {
    # Initialize logging and error handling
    log_header "ANALYSIS ENGINE DEPLOYMENT"
    log_info "Starting deployment at $(date)"
    
    # Parse command line arguments
    parse_arguments "$@"
    
    # Apply defaults
    apply_defaults
    
    # Log deployment configuration
    log_info "Deployment configuration:"
    log_info "  Service: $SERVICE_NAME"
    log_info "  Image Tag: $IMAGE_TAG"
    log_info "  Mode: $DEPLOYMENT_MODE"
    log_info "  Skip Build: $SKIP_BUILD"
    log_info "  Dry Run: $DRY_RUN"
    log_info "  Force: $FORCE_DEPLOY"
    
    # Execute deployment pipeline
    run_pre_deployment_validation
    build_and_push_image
    generate_deployment_env_vars
    
    # Execute deployment based on mode
    case "$DEPLOYMENT_MODE" in
        "standard")
            deploy_standard
            ;;
        "blue-green")
            deploy_blue_green
            ;;
        "rolling")
            log_warn "Rolling deployment not yet implemented, using standard deployment"
            deploy_standard
            ;;
        *)
            log_error "Invalid deployment mode: $DEPLOYMENT_MODE"
            exit $ERR_INVALID_CONFIG
            ;;
    esac
    
    # Post-deployment validation
    run_post_deployment_validation
    
    # Print summary
    print_deployment_summary
    
    log_success "Deployment completed successfully at $(date)"
}

# Execute main function
main "$@"