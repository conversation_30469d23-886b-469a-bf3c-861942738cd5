#!/bin/bash
# Standard health check script for Analysis Engine
# Usage: ./health-check.sh [--detailed]
set -euo pipefail

# Configuration
PROJECT_ID="vibe-match-463114"
REGION="us-central1"
SERVICE_NAME="analysis-engine"
DETAILED=false

# Parse arguments
if [[ "${1:-}" == "--detailed" ]]; then
    DETAILED=true
fi

# Get service URL
SERVICE_URL=$(gcloud run services describe ${SERVICE_NAME} \
    --platform managed \
    --region ${REGION} \
    --project ${PROJECT_ID} \
    --format 'value(status.url)' 2>/dev/null) || {
    echo "❌ Failed to get service URL. Is the service deployed?"
    exit 1
}

echo "🏥 Health Check for Analysis Engine"
echo "==================================="
echo "Service URL: ${SERVICE_URL}"
echo ""

# Function to check endpoint
check_endpoint() {
    local endpoint=$1
    local name=$2
    local response
    local status
    
    response=$(curl -s -w "\n%{http_code}" "${SERVICE_URL}${endpoint}" 2>/dev/null || echo "000")
    status=$(echo "$response" | tail -n1)
    body=$(echo "$response" | head -n-1)
    
    if [[ "$status" == "200" ]]; then
        echo "✅ ${name}: OK"
        if [[ "$DETAILED" == true ]] && [[ -n "$body" ]]; then
            echo "   Response: $(echo "$body" | jq -c '.' 2>/dev/null || echo "$body")"
        fi
    else
        echo "❌ ${name}: Failed (HTTP ${status})"
        if [[ "$DETAILED" == true ]] && [[ -n "$body" ]]; then
            echo "   Response: $body"
        fi
    fi
    
    return 0
}

# Check endpoints
echo "📋 Checking Endpoints:"
echo "---------------------"
check_endpoint "/health" "Basic Health"
check_endpoint "/health/ready" "Readiness"
check_endpoint "/health/live" "Liveness"
check_endpoint "/api/v1/version" "Version"
check_endpoint "/api/v1/languages" "Languages"

# Check readiness details
echo ""
echo "🔍 Service Readiness Details:"
echo "----------------------------"
ready_response=$(curl -s "${SERVICE_URL}/health/ready" | jq '.' 2>/dev/null || echo "{}")
if [[ -n "$ready_response" ]]; then
    echo "$ready_response" | jq -r '
        if .ready then
            "Overall Status: ✅ READY"
        else
            "Overall Status: ⚠️  NOT READY"
        end,
        "",
        "Components:",
        "- Spanner: " + (if .checks.spanner then "✅" else "❌" end),
        "- Storage: " + (if .checks.storage then "✅" else "❌" end),
        "- Pub/Sub: " + (if .checks.pubsub then "✅" else "❌" end)
    '
fi

# Check recent logs if detailed
if [[ "$DETAILED" == true ]]; then
    echo ""
    echo "📜 Recent Logs (Errors/Warnings):"
    echo "--------------------------------"
    gcloud logging read "resource.type=cloud_run_revision AND resource.labels.service_name=${SERVICE_NAME} AND (severity>=WARNING)" \
        --limit=5 \
        --project=${PROJECT_ID} \
        --format="table(timestamp,severity,textPayload)" \
        --freshness=10m 2>/dev/null || echo "No recent warnings/errors"
fi

# Check service configuration
echo ""
echo "⚙️  Service Configuration:"
echo "------------------------"
gcloud run services describe ${SERVICE_NAME} \
    --platform managed \
    --region ${REGION} \
    --project ${PROJECT_ID} \
    --format="table(
        spec.template.spec.containers[0].resources.limits.memory:label=MEMORY,
        spec.template.spec.containers[0].resources.limits.cpu:label=CPU,
        spec.template.spec.template.scaling.minInstances:label=MIN_INSTANCES,
        spec.template.spec.template.scaling.maxInstances:label=MAX_INSTANCES
    )" 2>/dev/null || echo "Failed to get configuration"

echo ""
echo "🎯 Health check complete!"