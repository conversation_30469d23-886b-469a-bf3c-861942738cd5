#!/bin/bash

# SOC 2 Compliance Validation Script
# This script validates security controls for SOC 2 compliance

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Counters
PASSED=0
FAILED=0
WARNINGS=0

echo -e "${BLUE}═══════════════════════════════════════════════════════════════${NC}"
echo -e "${BLUE}        SOC 2 Compliance Validation - Analysis Engine          ${NC}"
echo -e "${BLUE}═══════════════════════════════════════════════════════════════${NC}"
echo ""

# Function to check a control
check_control() {
    local control_name="$1"
    local check_command="$2"
    local expected_result="${3:-}"
    
    echo -n "Checking $control_name... "
    
    if eval "$check_command"; then
        echo -e "${GREEN}✓ PASSED${NC}"
        ((PASSED++))
        return 0
    else
        echo -e "${RED}✗ FAILED${NC}"
        ((FAILED++))
        return 1
    fi
}

# Function to check with warning
check_with_warning() {
    local control_name="$1"
    local check_command="$2"
    
    echo -n "Checking $control_name... "
    
    if eval "$check_command"; then
        echo -e "${GREEN}✓ PASSED${NC}"
        ((PASSED++))
        return 0
    else
        echo -e "${YELLOW}⚠ WARNING${NC}"
        ((WARNINGS++))
        return 1
    fi
}

echo -e "${BLUE}1. Security Controls${NC}"
echo "─────────────────────────────────────────"

# Check JWT authentication implementation
check_control "JWT Authentication" \
    "grep -q 'jsonwebtoken' Cargo.toml && grep -q 'validate_jwt_token' src/api/auth_extractor.rs"

# Check API key authentication
check_control "API Key Authentication" \
    "grep -q 'validate_api_key' src/api/auth_extractor.rs"

# Check rate limiting
check_control "Rate Limiting Implementation" \
    "test -f src/api/rate_limit_extractor.rs && grep -q 'rate_limit_middleware' src/api/rate_limit_extractor.rs"

# Check CSRF protection
check_control "CSRF Protection" \
    "test -f src/api/middleware/csrf.rs && grep -q 'csrf_middleware' src/api/middleware/csrf.rs"

# Check audit logging
check_control "Audit Logging" \
    "test -d src/storage/audit && grep -q 'AuditLogger' src/api/auth_extractor.rs"

# Check encryption
check_control "Field Encryption" \
    "test -f src/storage/encryption/field_encryption.rs"

# Check KMS integration
check_control "KMS Integration" \
    "test -f src/storage/encryption/kms_client.rs"

echo ""
echo -e "${BLUE}2. Availability Controls${NC}"
echo "─────────────────────────────────────────"

# Check health endpoint
check_control "Health Monitoring Endpoint" \
    "grep -q '/health' src/api/handlers/health.rs"

# Check Prometheus metrics
check_control "Prometheus Metrics" \
    "grep -q 'prometheus' Cargo.toml && test -f src/services/security/compliance/soc2/metrics.rs"

# Check resource limits
check_control "Resource Limits Configuration" \
    "grep -q 'resource_limits' src/config.rs"

# Check circuit breaker
check_control "Circuit Breaker Implementation" \
    "grep -q 'circuit_breaker' src/config.rs"

echo ""
echo -e "${BLUE}3. Processing Integrity Controls${NC}"
echo "─────────────────────────────────────────"

# Check input validation
check_control "Input Validation" \
    "grep -q 'validate' src/api/handlers/analysis.rs"

# Check error handling
check_control "Structured Error Handling" \
    "test -f src/api/errors.rs && grep -q 'ErrorResponse' src/api/errors.rs"

# Check transaction monitoring
check_control "Transaction Success Metrics" \
    "grep -q 'INTEGRITY_TRANSACTION_SUCCESS' src/services/security/compliance/soc2/metrics.rs"

echo ""
echo -e "${BLUE}4. Confidentiality Controls${NC}"
echo "─────────────────────────────────────────"

# Check data classification
check_control "Sensitive Data Marking" \
    "grep -q 'sensitive' src/models/security.rs"

# Check encryption metrics
check_control "Encryption Metrics" \
    "grep -q 'CONFIDENTIALITY_ENCRYPTED_FIELDS' src/services/security/compliance/soc2/metrics.rs"

# Check access controls
check_control "Role-Based Access Control" \
    "grep -q 'AuthMethod' src/api/auth_extractor.rs"

echo ""
echo -e "${BLUE}5. Privacy Controls${NC}"
echo "─────────────────────────────────────────"

# Check consent management
check_control "Consent Tracking" \
    "grep -q 'PRIVACY_CONSENT_CHANGES' src/services/security/compliance/soc2/metrics.rs"

# Check deletion support
check_control "Deletion Request Support" \
    "grep -q 'PRIVACY_DELETION_REQUESTS' src/services/security/compliance/soc2/metrics.rs"

# Check export support
check_control "Data Export Support" \
    "grep -q 'PRIVACY_EXPORT_REQUESTS' src/services/security/compliance/soc2/metrics.rs"

echo ""
echo -e "${BLUE}6. Dependency Security${NC}"
echo "─────────────────────────────────────────"

# Run cargo audit
echo -n "Running cargo audit... "
if cargo audit --quiet 2>/dev/null; then
    echo -e "${GREEN}✓ No vulnerabilities${NC}"
    ((PASSED++))
else
    # Check if it's just warnings
    if cargo audit 2>&1 | grep -q "warning:"; then
        echo -e "${YELLOW}⚠ Warnings found (non-critical)${NC}"
        ((WARNINGS++))
    else
        echo -e "${RED}✗ Vulnerabilities found${NC}"
        ((FAILED++))
    fi
fi

echo ""
echo -e "${BLUE}7. Configuration Security${NC}"
echo "─────────────────────────────────────────"

# Check for hardcoded secrets
check_control "No Hardcoded Secrets" \
    "! grep -r 'JWT_SECRET.*=.*\"' src/ --include='*.rs' 2>/dev/null"

# Check environment variable usage
check_control "Environment Variable Usage" \
    "grep -q 'env::var' src/config.rs"

# Check TLS configuration
check_control "TLS/HTTPS Configuration" \
    "grep -q 'cors_origins' src/config.rs"

echo ""
echo -e "${BLUE}8. SOC 2 Automation${NC}"
echo "─────────────────────────────────────────"

# Check SOC 2 service implementation
check_control "SOC 2 Service Module" \
    "test -d src/services/security/compliance/soc2"

# Check evidence collection
check_control "Evidence Collection" \
    "test -f src/services/security/compliance/soc2/evidence.rs"

# Check compliance monitoring (dashboard)
check_control "Compliance Monitoring" \
    "test -f src/services/security/compliance/soc2/dashboard.rs"

# Check report generation
check_control "Report Generation" \
    "test -f src/services/security/compliance/soc2/reports.rs"

echo ""
echo -e "${BLUE}9. Testing Coverage${NC}"
echo "─────────────────────────────────────────"

# Check for security tests
check_with_warning "Security Test Suite" \
    "test -f tests/security/auth_tests.rs"

# Check for integration tests
check_with_warning "Integration Tests" \
    "test -f tests/security_integration_test.rs"

# Check for SOC 2 example
check_control "SOC 2 Integration Example" \
    "test -f examples/soc2_compliance_integration.rs"

echo ""
echo -e "${BLUE}═══════════════════════════════════════════════════════════════${NC}"
echo -e "${BLUE}                     Validation Summary                        ${NC}"
echo -e "${BLUE}═══════════════════════════════════════════════════════════════${NC}"
echo ""
echo -e "Controls Passed:  ${GREEN}$PASSED${NC}"
echo -e "Controls Failed:  ${RED}$FAILED${NC}"
echo -e "Warnings:         ${YELLOW}$WARNINGS${NC}"
echo ""

# Calculate compliance percentage
TOTAL=$((PASSED + FAILED))
if [ $TOTAL -gt 0 ]; then
    PERCENTAGE=$(( (PASSED * 100) / TOTAL ))
    echo -e "Compliance Score: ${GREEN}${PERCENTAGE}%${NC}"
    
    if [ $PERCENTAGE -ge 95 ]; then
        echo -e "Status:           ${GREEN}✓ SOC 2 COMPLIANT${NC}"
    elif [ $PERCENTAGE -ge 80 ]; then
        echo -e "Status:           ${YELLOW}⚠ MOSTLY COMPLIANT (Address failures)${NC}"
    else
        echo -e "Status:           ${RED}✗ NOT COMPLIANT${NC}"
    fi
else
    echo -e "Status:           ${RED}✗ NO CONTROLS CHECKED${NC}"
fi

echo ""
echo -e "${BLUE}═══════════════════════════════════════════════════════════════${NC}"

# Exit with appropriate code
if [ $FAILED -gt 0 ]; then
    exit 1
else
    exit 0
fi