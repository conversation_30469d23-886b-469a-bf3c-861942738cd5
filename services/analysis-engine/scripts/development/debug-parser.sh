#!/bin/bash
# Enhanced Parser Debugging Tool for Analysis Engine
# Provides comprehensive debugging capabilities for parser issues with enterprise-grade error handling

# Source shared libraries
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/../lib/logging.sh"
source "$SCRIPT_DIR/../lib/error-handling.sh"
source "$SCRIPT_DIR/../lib/validation.sh"
source "$SCRIPT_DIR/../config/environments.sh"
source "$SCRIPT_DIR/../config/defaults.sh"

# ============================================================================
# DEBUGGING CONFIGURATION
# ============================================================================

# Debug options
DEBUG_MODE="${DEBUG_MODE:-comprehensive}"  # quick, comprehensive, detailed
DEBUG_TARGET="${DEBUG_TARGET:-}"            # specific file or directory
DEBUG_LANGUAGES="${DEBUG_LANGUAGES:-}"      # comma-separated list
SAVE_RESULTS="${SAVE_RESULTS:-true}"
GENERATE_REPORT="${GENERATE_REPORT:-true}"

# Output configuration
DEBUG_DATA_DIR="${DEBUG_DATA_DIR:-debug-data}"
RESULTS_DIR="${RESULTS_DIR:-$DEBUG_DATA_DIR/results}"
REPORTS_DIR="${REPORTS_DIR:-$DEBUG_DATA_DIR/reports}"

# Performance validator settings
VALIDATOR_TIMEOUT="${VALIDATOR_TIMEOUT:-60}"
TEST_FILE_COUNT="${TEST_FILE_COUNT:-10}"

# ============================================================================
# HELPER FUNCTIONS
# ============================================================================

# Initialize debugging environment
initialize_debugging() {
    log_header "ANALYSIS ENGINE PARSER DEBUG TOOL"
    log_info "Debug mode: $DEBUG_MODE"
    log_info "Target: $([ -n "$DEBUG_TARGET" ] && echo "$DEBUG_TARGET" || echo "current directory")"
    log_info "Languages: $([ -n "$DEBUG_LANGUAGES" ] && echo "$DEBUG_LANGUAGES" || echo "all")"
    
    # Create directories
    mkdir -p "$DEBUG_DATA_DIR" "$RESULTS_DIR" "$REPORTS_DIR"
    
    # Check prerequisites
    check_debugging_prerequisites
}

# Check debugging prerequisites
check_debugging_prerequisites() {
    log_info "Checking debugging prerequisites"
    
    # Check if performance validator exists
    local validator_path="$SCRIPT_DIR/../../target/release/performance_validator"
    if [[ ! -f "$validator_path" ]]; then
        log_warn "Performance validator not found, building..."
        if ! build_performance_validator; then
            log_error "Failed to build performance validator"
            exit $ERR_MISSING_DEPENDENCY
        fi
    else
        log_success "Performance validator found"
    fi
    
    # Check if analysis engine binary exists
    local engine_path="$SCRIPT_DIR/../../target/release/analysis-engine"
    if [[ ! -f "$engine_path" ]]; then
        log_warn "Analysis engine binary not found, building..."
        if ! build_analysis_engine; then
            log_error "Failed to build analysis engine"
            exit $ERR_MISSING_DEPENDENCY
        fi
    else
        log_success "Analysis engine binary found"
    fi
    
    # Check required commands
    require_command "curl" "http client"
    
    # Validate target if specified
    if [[ -n "$DEBUG_TARGET" && ! -e "$DEBUG_TARGET" ]]; then
        log_error "Debug target does not exist: $DEBUG_TARGET"
        exit $ERR_RESOURCE_NOT_FOUND
    fi
}

# Build performance validator
build_performance_validator() {
    log_info "Building performance validator"
    
    cd "$SCRIPT_DIR/../../" || exit $ERR_RESOURCE_NOT_FOUND
    
    if cargo build --release --bin performance_validator; then
        log_success "Performance validator built successfully"
        return 0
    else
        log_error "Performance validator build failed"
        return 1
    fi
}

# Build analysis engine
build_analysis_engine() {
    log_info "Building analysis engine"
    
    cd "$SCRIPT_DIR/../../" || exit $ERR_RESOURCE_NOT_FOUND
    
    if cargo build --release; then
        log_success "Analysis engine built successfully"
        return 0
    else
        log_error "Analysis engine build failed"
        return 1
    fi
}

# ============================================================================
# DEBUGGING FUNCTIONS
# ============================================================================

# Quick debug - basic functionality check
run_quick_debug() {
    log_header "QUICK PARSER DEBUG"
    
    local target="${DEBUG_TARGET:-$SCRIPT_DIR/../../}"
    local result_file="$RESULTS_DIR/quick_debug_$(date +%Y%m%d_%H%M%S).log"
    
    log_info "Running quick parser test on: $target"
    
    # Run performance validator with timeout
    if timeout "$VALIDATOR_TIMEOUT" "$SCRIPT_DIR/../../target/release/performance_validator" "$target" > "$result_file" 2>&1; then
        log_success "Quick debug completed successfully"
        
        # Extract key metrics
        extract_debug_metrics "$result_file"
    else
        local exit_code=$?
        if [[ $exit_code -eq 124 ]]; then
            log_error "Quick debug timed out after ${VALIDATOR_TIMEOUT}s"
        else
            log_error "Quick debug failed with exit code: $exit_code"
        fi
        
        # Show error details
        show_debug_errors "$result_file"
    fi
}

# Comprehensive debug - detailed analysis
run_comprehensive_debug() {
    log_header "COMPREHENSIVE PARSER DEBUG"
    
    # Test different scenarios
    test_parser_initialization
    test_language_loading
    test_specific_files
    test_performance_characteristics
    test_service_integration
}

# Test parser initialization
test_parser_initialization() {
    log_step "Testing parser initialization"
    
    local result_file="$RESULTS_DIR/parser_init_$(date +%Y%m%d_%H%M%S).log"
    
    # Test language registry loading
    log_info "Testing language registry initialization..."
    if timeout 30 cargo test test_language_registry -- --nocapture > "$result_file" 2>&1; then
        log_success "Language registry initialization test passed"
    else
        log_error "Language registry initialization failed"
        show_debug_errors "$result_file"
    fi
}

# Test language loading
test_language_loading() {
    log_step "Testing language loading"
    
    local result_file="$RESULTS_DIR/language_loading_$(date +%Y%m%d_%H%M%S).log"
    
    # Check service endpoints if running
    if is_service_running; then
        log_info "Testing language loading via API..."
        
        local languages_response
        if languages_response=$(curl -s --max-time 10 "$SERVICE_URL/api/v1/languages" 2>/dev/null); then
            local language_count
            language_count=$(echo "$languages_response" | jq '.languages | length' 2>/dev/null || echo "0")
            
            if [[ $language_count -gt 0 ]]; then
                log_success "Language loading test passed ($language_count languages loaded)"
                echo "$languages_response" > "$result_file"
                
                # Extract and display language info
                extract_language_info "$languages_response"
            else
                log_error "No languages loaded"
                echo "$languages_response" > "$result_file"
            fi
        else
            log_error "Failed to query languages endpoint"
        fi
    else
        log_info "Service not running, testing language loading directly..."
        
        # Test language loading through performance validator
        echo 'fn main() { println!("test"); }' > /tmp/test_debug.rs
        if timeout 30 "$SCRIPT_DIR/../../target/release/performance_validator" /tmp > "$result_file" 2>&1; then
            log_success "Direct language loading test passed"
        else
            log_error "Direct language loading test failed"
            show_debug_errors "$result_file"
        fi
        rm -f /tmp/test_debug.rs
    fi
}

# Test specific files
test_specific_files() {
    log_step "Testing specific file types"
    
    # Create test files for different languages
    create_test_files
    
    # Test each language
    local test_languages=("rust" "python" "javascript" "typescript" "go")
    
    for lang in "${test_languages[@]}"; do
        test_language_parsing "$lang"
    done
    
    # Cleanup test files
    cleanup_test_files
}

# Create test files for different languages
create_test_files() {
    log_info "Creating test files for different languages"
    
    mkdir -p /tmp/parser_debug_test
    
    # Rust test file
    cat > /tmp/parser_debug_test/test.rs << 'EOF'
fn fibonacci(n: u64) -> u64 {
    match n {
        0 => 0,
        1 => 1,
        _ => fibonacci(n - 1) + fibonacci(n - 2),
    }
}

fn main() {
    println!("Fibonacci(10) = {}", fibonacci(10));
}
EOF

    # Python test file
    cat > /tmp/parser_debug_test/test.py << 'EOF'
def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n - 1) + fibonacci(n - 2)

if __name__ == "__main__":
    print(f"Fibonacci(10) = {fibonacci(10)}")
EOF

    # JavaScript test file
    cat > /tmp/parser_debug_test/test.js << 'EOF'
function fibonacci(n) {
    if (n <= 1) {
        return n;
    }
    return fibonacci(n - 1) + fibonacci(n - 2);
}

console.log(`Fibonacci(10) = ${fibonacci(10)}`);
EOF

    # TypeScript test file
    cat > /tmp/parser_debug_test/test.ts << 'EOF'
function fibonacci(n: number): number {
    if (n <= 1) {
        return n;
    }
    return fibonacci(n - 1) + fibonacci(n - 2);
}

console.log(`Fibonacci(10) = ${fibonacci(10)}`);
EOF

    # Go test file
    cat > /tmp/parser_debug_test/test.go << 'EOF'
package main

import "fmt"

func fibonacci(n int) int {
    if n <= 1 {
        return n
    }
    return fibonacci(n-1) + fibonacci(n-2)
}

func main() {
    fmt.Printf("Fibonacci(10) = %d\n", fibonacci(10))
}
EOF

    log_success "Test files created"
}

# Test parsing for specific language
test_language_parsing() {
    local language="$1"
    local result_file="$RESULTS_DIR/lang_${language}_$(date +%Y%m%d_%H%M%S).log"
    
    log_info "Testing $language parsing..."
    
    if timeout 30 "$SCRIPT_DIR/../../target/release/performance_validator" /tmp/parser_debug_test > "$result_file" 2>&1; then
        # Check if the specific language was processed
        if grep -q "$language" "$result_file" 2>/dev/null; then
            log_success "$language parsing test passed"
        else
            log_warn "$language parsing test completed but no $language files found in output"
        fi
    else
        log_error "$language parsing test failed"
        show_debug_errors "$result_file"
    fi
}

# Test performance characteristics
test_performance_characteristics() {
    log_step "Testing performance characteristics"
    
    local target="${DEBUG_TARGET:-$SCRIPT_DIR/../../src}"
    local result_file="$RESULTS_DIR/performance_debug_$(date +%Y%m%d_%H%M%S).log"
    
    log_info "Running performance analysis on: $target"
    
    if timeout "$VALIDATOR_TIMEOUT" "$SCRIPT_DIR/../../target/release/performance_validator" "$target" > "$result_file" 2>&1; then
        log_success "Performance test completed"
        
        # Extract performance metrics
        extract_performance_metrics "$result_file"
    else
        log_error "Performance test failed"
        show_debug_errors "$result_file"
    fi
}

# Test service integration
test_service_integration() {
    log_step "Testing service integration"
    
    if is_service_running; then
        log_info "Service is running, testing API endpoints..."
        
        # Test health endpoint
        test_health_endpoint
        
        # Test analysis endpoint
        test_analysis_endpoint
        
        # Test metrics endpoint
        test_metrics_endpoint
    else
        log_info "Service not running, skipping integration tests"
        log_info "Start service with: cargo run --release"
    fi
}

# ============================================================================
# ANALYSIS FUNCTIONS
# ============================================================================

# Extract debug metrics from result file
extract_debug_metrics() {
    local result_file="$1"
    
    if [[ ! -f "$result_file" ]]; then
        return 1
    fi
    
    log_info "Extracting debug metrics:"
    
    # Extract lines per second
    local lines_per_sec
    lines_per_sec=$(grep -o "Lines/Second: [0-9,]*" "$result_file" | head -1 | sed 's/Lines\/Second: //' | tr -d ',')
    
    if [[ -n "$lines_per_sec" ]]; then
        log_info "  Performance: $lines_per_sec LOC/second"
    fi
    
    # Extract success rate
    local success_rate
    success_rate=$(grep -o "Success Rate: [0-9.]*%" "$result_file" | head -1 | sed 's/Success Rate: //')
    
    if [[ -n "$success_rate" ]]; then
        log_info "  Success Rate: $success_rate"
    fi
    
    # Extract total files
    local total_files
    total_files=$(grep -o "Total files: [0-9,]*" "$result_file" | head -1 | sed 's/Total files: //' | tr -d ',')
    
    if [[ -n "$total_files" ]]; then
        log_info "  Total Files: $total_files"
    fi
}

# Extract language information
extract_language_info() {
    local languages_response="$1"
    
    log_info "Language loading analysis:"
    
    # Extract parser info
    local tree_sitter_count
    tree_sitter_count=$(echo "$languages_response" | jq '.parser_info.tree_sitter.count' 2>/dev/null || echo "0")
    
    local adapter_count
    adapter_count=$(echo "$languages_response" | jq '.parser_info.adapters.count' 2>/dev/null || echo "0")
    
    log_info "  Tree-sitter languages: $tree_sitter_count"
    log_info "  Adapter languages: $adapter_count"
    
    # Show top languages
    log_info "  Active languages:"
    echo "$languages_response" | jq -r '.languages[0:5][].name' 2>/dev/null | while read -r lang; do
        log_info "    - $lang"
    done
}

# Extract performance metrics
extract_performance_metrics() {
    local result_file="$1"
    
    log_info "Performance analysis:"
    
    # Extract timing information
    local parse_time
    parse_time=$(grep -o "Total parsing time: [0-9.]*s" "$result_file" | sed 's/Total parsing time: //' | sed 's/s//')
    
    if [[ -n "$parse_time" ]]; then
        log_info "  Parse time: ${parse_time}s"
    fi
    
    # Extract memory usage if available
    local memory_usage
    memory_usage=$(grep -o "Memory usage: [0-9.]* MB" "$result_file" | sed 's/Memory usage: //')
    
    if [[ -n "$memory_usage" ]]; then
        log_info "  Memory usage: $memory_usage"
    fi
}

# Show debug errors from result file
show_debug_errors() {
    local result_file="$1"
    
    if [[ ! -f "$result_file" ]]; then
        return 1
    fi
    
    log_error "Debug errors found:"
    
    # Show last 10 lines of errors
    local error_lines
    error_lines=$(grep -E "(Failed to analyze|Error:|error:|panic)" "$result_file" | tail -10)
    
    if [[ -n "$error_lines" ]]; then
        echo "$error_lines" | while read -r line; do
            log_error "  $line"
        done
    else
        log_info "  No specific error messages found"
        log_info "  Last 5 lines of output:"
        tail -5 "$result_file" | while read -r line; do
            log_debug "    $line"
        done
    fi
}

# ============================================================================
# SERVICE TESTING FUNCTIONS
# ============================================================================

# Check if service is running
is_service_running() {
    curl -s --max-time 5 "$SERVICE_URL/health" > /dev/null 2>&1
}

# Test health endpoint
test_health_endpoint() {
    log_info "Testing health endpoint..."
    
    local health_response
    if health_response=$(curl -s --max-time 10 "$SERVICE_URL/health" 2>/dev/null); then
        if echo "$health_response" | grep -q "healthy" 2>/dev/null; then
            log_success "Health endpoint working"
        else
            log_warn "Health endpoint responding but not healthy"
            log_debug "Response: $health_response"
        fi
    else
        log_error "Health endpoint not responding"
    fi
}

# Test analysis endpoint
test_analysis_endpoint() {
    log_info "Testing analysis endpoint..."
    
    local test_payload='{"content": "fn main() { println!(\"test\"); }", "language": "rust", "file_path": "test.rs"}'
    local analysis_response
    
    if analysis_response=$(curl -s -X POST "$SERVICE_URL/api/v1/analyze" \
        -H "Content-Type: application/json" \
        -d "$test_payload" \
        --max-time 10 2>/dev/null); then
        
        if echo "$analysis_response" | grep -q "ast\|metrics" 2>/dev/null; then
            log_success "Analysis endpoint working"
        else
            log_warn "Analysis endpoint responding but unexpected format"
            log_debug "Response: $analysis_response"
        fi
    else
        log_error "Analysis endpoint not responding"
    fi
}

# Test metrics endpoint
test_metrics_endpoint() {
    log_info "Testing metrics endpoint..."
    
    local metrics_response
    if metrics_response=$(curl -s --max-time 10 "$SERVICE_URL/metrics" 2>/dev/null); then
        if [[ -n "$metrics_response" ]]; then
            log_success "Metrics endpoint working"
            
            # Count metrics
            local metric_count
            metric_count=$(echo "$metrics_response" | grep -c "^[a-zA-Z]" 2>/dev/null || echo "0")
            log_info "  Metrics available: $metric_count"
        else
            log_warn "Metrics endpoint responding but empty"
        fi
    else
        log_error "Metrics endpoint not responding"
    fi
}

# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================

# Cleanup test files
cleanup_test_files() {
    log_info "Cleaning up test files"
    rm -rf /tmp/parser_debug_test
}

# Generate debug report
generate_debug_report() {
    if [[ "$GENERATE_REPORT" != "true" ]]; then
        return 0
    fi
    
    log_header "GENERATING DEBUG REPORT"
    
    local report_file="$REPORTS_DIR/debug_report_$(date +%Y%m%d_%H%M%S).md"
    local timestamp=$(date)
    
    cat > "$report_file" << EOF
# Analysis Engine Parser Debug Report

**Generated:** $timestamp  
**Debug Mode:** $DEBUG_MODE  
**Target:** $([ -n "$DEBUG_TARGET" ] && echo "$DEBUG_TARGET" || echo "current directory")  
**Languages:** $([ -n "$DEBUG_LANGUAGES" ] && echo "$DEBUG_LANGUAGES" || echo "all")  

## Debug Results

### Prerequisites Check
- Performance validator: $([ -f "$SCRIPT_DIR/../../target/release/performance_validator" ] && echo "✅ Available" || echo "❌ Missing")
- Analysis engine: $([ -f "$SCRIPT_DIR/../../target/release/analysis-engine" ] && echo "✅ Available" || echo "❌ Missing")
- Service status: $(is_service_running && echo "✅ Running" || echo "❌ Not running")

### Test Results

$(if [[ "$DEBUG_MODE" == "quick" ]]; then
    echo "Quick debug test completed. Check result files in \`$RESULTS_DIR\`."
elif [[ "$DEBUG_MODE" == "comprehensive" ]]; then
    echo "Comprehensive debug completed with the following tests:"
    echo "- Parser initialization"
    echo "- Language loading"
    echo "- Specific file types"
    echo "- Performance characteristics"
    echo "- Service integration"
fi)

### Debug Artifacts

- **Results Directory:** \`$RESULTS_DIR\`
- **Reports Directory:** \`$REPORTS_DIR\`
- **Debug Data:** \`$DEBUG_DATA_DIR\`

### Recommendations

$(if is_service_running; then
    echo "- Service is running and responsive"
else
    echo "- Start the service to enable integration testing"
fi)

$(if [[ -f "$SCRIPT_DIR/../../target/release/performance_validator" ]]; then
    echo "- Performance validator is available for testing"
else
    echo "- Build performance validator for comprehensive testing"
fi)

---

*Generated by Analysis Engine Parser Debug Tool*
EOF

    log_success "Debug report generated: $report_file"
}

# ============================================================================
# COMMAND LINE INTERFACE
# ============================================================================

# Show usage information
show_usage() {
    cat << EOF
Usage: $0 [OPTIONS] [TARGET]

Enhanced parser debugging tool for the Analysis Engine.

OPTIONS:
    --mode MODE         Debug mode: quick|comprehensive|detailed (default: comprehensive)
    --languages LANG    Comma-separated list of languages to test
    --timeout SECONDS   Timeout for operations (default: $VALIDATOR_TIMEOUT)
    --no-report        Skip debug report generation
    --no-save          Skip saving debug results
    -h, --help         Show this help message

TARGET:
    File or directory to debug (default: current directory)

EXAMPLES:
    $0                              # Comprehensive debug of current directory
    $0 --mode quick                 # Quick debug test
    $0 --mode comprehensive src/    # Debug specific directory
    $0 --languages rust,python      # Test specific languages only
    $0 --timeout 120 large_repo/   # Extended timeout for large repositories

ENVIRONMENT VARIABLES:
    DEBUG_MODE          Default debug mode
    DEBUG_LANGUAGES     Default languages to test
    SERVICE_URL         Service URL for integration tests

EOF
}

# Parse command line arguments
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --mode)
                DEBUG_MODE="$2"
                shift 2
                ;;
            --languages)
                DEBUG_LANGUAGES="$2"
                shift 2
                ;;
            --timeout)
                VALIDATOR_TIMEOUT="$2"
                shift 2
                ;;
            --no-report)
                GENERATE_REPORT=false
                shift
                ;;
            --no-save)
                SAVE_RESULTS=false
                shift
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            -*)
                log_error "Unknown option: $1"
                show_usage
                exit $ERR_INVALID_CONFIG
                ;;
            *)
                DEBUG_TARGET="$1"
                shift
                ;;
        esac
    done
}

# ============================================================================
# MAIN EXECUTION
# ============================================================================

main() {
    # Initialize error handling
    setup_error_handling
    
    # Parse arguments
    parse_arguments "$@"
    
    # Apply defaults
    apply_defaults
    
    # Initialize debugging environment
    initialize_debugging
    
    # Execute debug based on mode
    case "$DEBUG_MODE" in
        "quick")
            run_quick_debug
            ;;
        "comprehensive")
            run_comprehensive_debug
            ;;
        "detailed")
            run_comprehensive_debug
            # Additional detailed analysis could be added here
            ;;
        *)
            log_error "Invalid debug mode: $DEBUG_MODE"
            show_usage
            exit $ERR_INVALID_CONFIG
            ;;
    esac
    
    # Generate report
    generate_debug_report
    
    log_success "Parser debugging completed successfully"
}

# Execute main function
main "$@"