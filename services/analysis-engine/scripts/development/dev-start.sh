#!/bin/bash
# Enhanced Development Startup Script for Analysis Engine
# Provides comprehensive environment setup and validation for local development

# Source shared libraries
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/../lib/logging.sh"
source "$SCRIPT_DIR/../lib/error-handling.sh"
source "$SCRIPT_DIR/../lib/validation.sh"
source "$SCRIPT_DIR/../config/environments.sh"
source "$SCRIPT_DIR/../config/defaults.sh"

# ============================================================================
# DEVELOPMENT CONFIGURATION
# ============================================================================

# Development mode settings
DEV_MODE="${DEV_MODE:-development}"
DEV_PORT="${DEV_PORT:-8001}"
DEV_LOG_LEVEL="${DEV_LOG_LEVEL:-debug}"
ENABLE_HOT_RELOAD="${ENABLE_HOT_RELOAD:-false}"
SKIP_AUTH="${SKIP_AUTH:-true}"
USE_MOCK_SERVICES="${USE_MOCK_SERVICES:-true}"

# Build configuration
BUILD_MODE="${BUILD_MODE:-debug}"        # debug or release
REBUILD_ON_START="${REBUILD_ON_START:-false}"
CHECK_DEPENDENCIES="${CHECK_DEPENDENCIES:-true}"

# Service configuration
START_TIMEOUT="${START_TIMEOUT:-30}"
HEALTH_CHECK_RETRIES="${HEALTH_CHECK_RETRIES:-5}"
HEALTH_CHECK_INTERVAL="${HEALTH_CHECK_INTERVAL:-3}"

# Environment file settings
ENV_FILE="${ENV_FILE:-$SCRIPT_DIR/../../.env}"
ENV_EXAMPLE_FILE="${ENV_EXAMPLE_FILE:-$SCRIPT_DIR/../../.env.example}"

# ============================================================================
# HELPER FUNCTIONS
# ============================================================================

# Initialize development environment
initialize_development() {
    log_header "ANALYSIS ENGINE DEVELOPMENT STARTUP"
    log_info "Development mode: $DEV_MODE"
    log_info "Port: $DEV_PORT"
    log_info "Log level: $DEV_LOG_LEVEL"
    log_info "Build mode: $BUILD_MODE"
    
    # Check prerequisites
    check_development_prerequisites
    
    # Setup environment
    setup_development_environment
    
    # Validate configuration
    validate_development_config
}

# Check development prerequisites
check_development_prerequisites() {
    log_info "Checking development prerequisites"
    
    # Check required commands
    require_command "cargo" "Rust toolchain"
    require_command "git" "version control"
    
    # Check if we're in the right directory
    if [[ ! -f "$SCRIPT_DIR/../../Cargo.toml" ]]; then
        log_error "Not in analysis engine root directory"
        exit $ERR_RESOURCE_NOT_FOUND
    fi
    
    # Check if project compiles
    if [[ "$CHECK_DEPENDENCIES" == "true" ]]; then
        check_project_compilation
    fi
    
    log_success "Prerequisites check completed"
}

# Check project compilation
check_project_compilation() {
    log_info "Checking project compilation..."
    
    cd "$SCRIPT_DIR/../../" || exit $ERR_RESOURCE_NOT_FOUND
    
    if cargo check --quiet; then
        log_success "Project compiles successfully"
    else
        log_error "Project has compilation errors"
        log_error "Fix compilation errors before starting development"
        exit $ERR_DEPLOYMENT_FAILURE
    fi
}

# Setup development environment
setup_development_environment() {
    log_info "Setting up development environment"
    
    # Create .env file if it doesn't exist
    setup_env_file
    
    # Set development environment variables
    setup_development_env_vars
    
    # Setup development directories
    setup_development_directories
    
    log_success "Development environment setup completed"
}

# Setup .env file
setup_env_file() {
    if [[ ! -f "$ENV_FILE" ]]; then
        if [[ -f "$ENV_EXAMPLE_FILE" ]]; then
            log_info "Creating .env from .env.example"
            cp "$ENV_EXAMPLE_FILE" "$ENV_FILE"
        else
            log_info "Creating default .env file"
            create_default_env_file
        fi
        log_success ".env file created"
    else
        log_info ".env file already exists"
    fi
    
    # Validate .env file
    validate_env_file
}

# Create default .env file
create_default_env_file() {
    cat > "$ENV_FILE" << EOF
# Analysis Engine Development Environment Configuration
# Generated by dev-start.sh on $(date)

# Environment
ENVIRONMENT=development
RUST_LOG=analysis_engine=debug,tower_http=debug
RUST_BACKTRACE=1

# Service Configuration
PORT=$DEV_PORT
ENABLE_AUTH=$SKIP_AUTH
ENABLE_METRICS=true
ENABLE_TRACING=true

# Development Settings
USE_MOCK_SERVICES=$USE_MOCK_SERVICES
ENABLE_HOT_RELOAD=$ENABLE_HOT_RELOAD

# Database (Mock for development)
GCP_PROJECT_ID=development-project
SPANNER_INSTANCE_ID=development-instance
SPANNER_DATABASE_ID=development-database

# Cache (Optional for development)
REDIS_URL=redis://localhost:6379

# Storage (Mock for development)
STORAGE_BUCKET=development-storage

# Security (Development only - weak settings)
JWT_SECRET=development-secret-key-not-for-production

# Rate Limiting (Relaxed for development)
RATE_LIMIT_PER_HOUR=10000

# Performance Settings
MAX_CONCURRENT_ANALYSES=10
MAX_FILE_SIZE_BYTES=10485760
PARSE_TIMEOUT_SECONDS=30

# Logging
LOG_LEVEL=debug
LOG_FORMAT=pretty
EOF

    log_success "Default .env file created"
}

# Validate .env file
validate_env_file() {
    log_info "Validating .env file"
    
    # Source the .env file
    if source "$ENV_FILE"; then
        log_success ".env file is valid"
    else
        log_error ".env file has syntax errors"
        exit $ERR_INVALID_CONFIG
    fi
    
    # Check required variables
    local required_vars=("ENVIRONMENT" "PORT" "RUST_LOG")
    local missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var:-}" ]]; then
            missing_vars+=("$var")
        fi
    done
    
    if [[ ${#missing_vars[@]} -gt 0 ]]; then
        log_error "Missing required environment variables: ${missing_vars[*]}"
        exit $ERR_INVALID_CONFIG
    fi
}

# Setup development environment variables
setup_development_env_vars() {
    log_info "Setting development environment variables"
    
    # Load .env file
    if [[ -f "$ENV_FILE" ]]; then
        source "$ENV_FILE"
        log_success "Environment variables loaded from .env"
    fi
    
    # Override with development-specific settings
    export ENVIRONMENT="$DEV_MODE"
    export PORT="$DEV_PORT"
    export RUST_LOG="analysis_engine=$DEV_LOG_LEVEL,tower_http=debug"
    export RUST_BACKTRACE=1
    
    # Development overrides
    export ENABLE_AUTH="$SKIP_AUTH"
    export USE_MOCK_SERVICES="$USE_MOCK_SERVICES"
    
    # Mock GCP settings for development
    export GCP_PROJECT_ID="${GCP_PROJECT_ID:-development-project}"
    export SPANNER_INSTANCE_ID="${SPANNER_INSTANCE_ID:-development-instance}"
    export SPANNER_DATABASE_ID="${SPANNER_DATABASE_ID:-development-database}"
    
    log_info "Development environment configured:"
    log_info "  Environment: $ENVIRONMENT"
    log_info "  Port: $PORT"
    log_info "  Log Level: $DEV_LOG_LEVEL"
    log_info "  Authentication: $([ "$ENABLE_AUTH" = "true" ] && echo "enabled" || echo "disabled")"
    log_info "  Mock Services: $([ "$USE_MOCK_SERVICES" = "true" ] && echo "enabled" || echo "disabled")"
}

# Setup development directories
setup_development_directories() {
    local dev_dirs=(
        "$SCRIPT_DIR/../../tmp"
        "$SCRIPT_DIR/../../logs"
        "$SCRIPT_DIR/../../dev-data"
        "$SCRIPT_DIR/../../test-results"
    )
    
    for dir in "${dev_dirs[@]}"; do
        if [[ ! -d "$dir" ]]; then
            mkdir -p "$dir"
            log_info "Created directory: $(basename "$dir")"
        fi
    done
}

# Validate development configuration
validate_development_config() {
    log_info "Validating development configuration"
    
    # Reset validation counters
    reset_validation_counters
    
    # Validate port availability
    validate "Port availability" "check_port_available '$DEV_PORT'"
    
    # Validate Rust environment
    validate "Rust toolchain" "check_rust_environment"
    
    # Validate project structure
    validate "Project structure" "check_project_structure"
    
    # Print validation summary
    print_validation_summary
    local validation_result=$?
    
    if [[ $validation_result -ne 0 ]]; then
        log_error "Development configuration validation failed"
        log_error "Please fix the issues above before starting development"
        exit $ERR_VALIDATION_FAILURE
    fi
}

# Check if port is available
check_port_available() {
    local port="$1"
    
    if command -v lsof &> /dev/null; then
        if lsof -i ":$port" &> /dev/null; then
            log_debug "Port $port is already in use"
            return 1
        fi
    elif command -v netstat &> /dev/null; then
        if netstat -an | grep ":$port " &> /dev/null; then
            log_debug "Port $port is already in use"
            return 1
        fi
    else
        log_debug "Cannot check port availability (lsof/netstat not found)"
        return 0
    fi
    
    log_debug "Port $port is available"
    return 0
}

# Check Rust environment
check_rust_environment() {
    # Check Rust version
    if ! cargo --version &> /dev/null; then
        log_debug "Cargo not available"
        return 1
    fi
    
    # Check if project has dependencies resolved
    cd "$SCRIPT_DIR/../../" || return 1
    
    if [[ ! -f "Cargo.lock" ]]; then
        log_debug "Cargo.lock not found, dependencies may not be resolved"
        return 1
    fi
    
    log_debug "Rust environment is ready"
    return 0
}

# Check project structure
check_project_structure() {
    local required_files=(
        "$SCRIPT_DIR/../../Cargo.toml"
        "$SCRIPT_DIR/../../src/main.rs"
        "$SCRIPT_DIR/../../src/lib.rs"
    )
    
    for file in "${required_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            log_debug "Required file missing: $(basename "$file")"
            return 1
        fi
    done
    
    log_debug "Project structure is valid"
    return 0
}

# ============================================================================
# BUILD AND START FUNCTIONS
# ============================================================================

# Build the project
build_project() {
    log_header "BUILDING PROJECT"
    
    cd "$SCRIPT_DIR/../../" || exit $ERR_RESOURCE_NOT_FOUND
    
    local build_cmd
    if [[ "$BUILD_MODE" == "release" ]]; then
        build_cmd="cargo build --release"
        log_info "Building in release mode..."
    else
        build_cmd="cargo build"
        log_info "Building in debug mode..."
    fi
    
    if $build_cmd; then
        log_success "Build completed successfully"
        return 0
    else
        log_error "Build failed"
        exit $ERR_DEPLOYMENT_FAILURE
    fi
}

# Start the development server
start_development_server() {
    log_header "STARTING DEVELOPMENT SERVER"
    
    cd "$SCRIPT_DIR/../../" || exit $ERR_RESOURCE_NOT_FOUND
    
    # Determine binary path
    local binary_path
    if [[ "$BUILD_MODE" == "release" ]]; then
        binary_path="./target/release/analysis-engine"
    else
        binary_path="./target/debug/analysis-engine"
    fi
    
    # Check if binary exists
    if [[ ! -f "$binary_path" ]]; then
        log_warn "Binary not found, building project first..."
        build_project
    fi
    
    log_info "Starting Analysis Engine on port $DEV_PORT..."
    log_info "Press Ctrl+C to stop the server"
    log_info ""
    log_info "Service will be available at:"
    log_info "  Health: http://localhost:$DEV_PORT/health"
    log_info "  API: http://localhost:$DEV_PORT/api/v1/"
    log_info "  Metrics: http://localhost:$DEV_PORT/metrics"
    log_info ""
    
    # Setup cleanup on exit
    trap cleanup_development EXIT
    
    # Start the server
    if [[ "$BUILD_MODE" == "release" ]]; then
        cargo run --release
    else
        cargo run
    fi
}

# Cleanup development environment
cleanup_development() {
    log_info "Cleaning up development environment..."
    
    # Kill any remaining processes
    pkill -f "analysis-engine" 2>/dev/null || true
    
    # Clean up temporary files
    rm -f /tmp/analysis-engine-* 2>/dev/null || true
    
    log_success "Development cleanup completed"
}

# Wait for service to be ready
wait_for_service() {
    log_info "Waiting for service to be ready..."
    
    local retries=0
    local service_url="http://localhost:$DEV_PORT"
    
    while [[ $retries -lt $HEALTH_CHECK_RETRIES ]]; do
        if curl -s --max-time 5 "$service_url/health" > /dev/null 2>&1; then
            log_success "Service is ready and responding"
            log_info "Health check: $service_url/health"
            return 0
        fi
        
        ((retries++))
        log_info "Waiting for service... (attempt $retries/$HEALTH_CHECK_RETRIES)"
        sleep $HEALTH_CHECK_INTERVAL
    done
    
    log_error "Service failed to start within $START_TIMEOUT seconds"
    return 1
}

# ============================================================================
# COMMAND LINE INTERFACE
# ============================================================================

# Show usage information
show_usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Enhanced development startup script for the Analysis Engine.

OPTIONS:
    --mode MODE         Development mode: development|debug (default: development)
    --port PORT         Port to run on (default: $DEV_PORT)
    --log-level LEVEL   Log level: trace|debug|info|warn|error (default: $DEV_LOG_LEVEL)
    --build-mode MODE   Build mode: debug|release (default: debug)
    --rebuild          Force rebuild before starting
    --no-auth          Disable authentication (default)
    --auth             Enable authentication
    --mock-services    Use mock services (default)
    --real-services    Use real GCP services
    --hot-reload       Enable hot reloading (experimental)
    --env-file FILE    Environment file to use (default: .env)
    -h, --help         Show this help message

EXAMPLES:
    $0                              # Start with default settings
    $0 --port 8002                  # Start on different port
    $0 --build-mode release         # Start with release build
    $0 --auth --real-services       # Start with authentication and real services
    $0 --rebuild --log-level debug  # Force rebuild with debug logging

ENVIRONMENT VARIABLES:
    DEV_MODE            Development mode
    DEV_PORT            Development port
    DEV_LOG_LEVEL       Development log level
    BUILD_MODE          Build mode (debug/release)

EOF
}

# Parse command line arguments
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --mode)
                DEV_MODE="$2"
                shift 2
                ;;
            --port)
                DEV_PORT="$2"
                shift 2
                ;;
            --log-level)
                DEV_LOG_LEVEL="$2"
                shift 2
                ;;
            --build-mode)
                BUILD_MODE="$2"
                shift 2
                ;;
            --rebuild)
                REBUILD_ON_START=true
                shift
                ;;
            --no-auth)
                SKIP_AUTH=true
                shift
                ;;
            --auth)
                SKIP_AUTH=false
                shift
                ;;
            --mock-services)
                USE_MOCK_SERVICES=true
                shift
                ;;
            --real-services)
                USE_MOCK_SERVICES=false
                shift
                ;;
            --hot-reload)
                ENABLE_HOT_RELOAD=true
                shift
                ;;
            --env-file)
                ENV_FILE="$2"
                shift 2
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                show_usage
                exit $ERR_INVALID_CONFIG
                ;;
        esac
    done
}

# ============================================================================
# MAIN EXECUTION
# ============================================================================

main() {
    # Initialize error handling
    setup_error_handling
    
    # Parse arguments
    parse_arguments "$@"
    
    # Apply defaults
    apply_defaults
    
    # Initialize development environment
    initialize_development
    
    # Build project if needed
    if [[ "$REBUILD_ON_START" == "true" ]]; then
        build_project
    fi
    
    # Start development server
    start_development_server
}

# Execute main function
main "$@"