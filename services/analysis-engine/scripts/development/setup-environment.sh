#!/bin/bash
# Enhanced Environment Setup Script for Analysis Engine
# Provides comprehensive environment preparation for development and testing

# Source shared libraries
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/../lib/logging.sh"
source "$SCRIPT_DIR/../lib/error-handling.sh"
source "$SCRIPT_DIR/../lib/validation.sh"
source "$SCRIPT_DIR/../config/environments.sh"
source "$SCRIPT_DIR/../config/defaults.sh"
source "$SCRIPT_DIR/../config/validation-rules.sh"

# ============================================================================
# SETUP CONFIGURATION
# ============================================================================

# Setup modes
SETUP_MODE="${SETUP_MODE:-development}"  # development, testing, ci, production
SETUP_TYPE="${SETUP_TYPE:-comprehensive}"  # minimal, standard, comprehensive
FORCE_SETUP="${FORCE_SETUP:-false}"
SKIP_VALIDATION="${SKIP_VALIDATION:-false}"

# Environment configuration
TARGET_ENVIRONMENT="${TARGET_ENVIRONMENT:-development}"
CREATE_ENV_FILE="${CREATE_ENV_FILE:-true}"
BACKUP_EXISTING="${BACKUP_EXISTING:-true}"

# Directory setup
SETUP_DIRECTORIES="${SETUP_DIRECTORIES:-true}"
SETUP_TEST_DATA="${SETUP_TEST_DATA:-true}"
SETUP_LOGS="${SETUP_LOGS:-true}"

# Dependency management
CHECK_DEPENDENCIES="${CHECK_DEPENDENCIES:-true}"
INSTALL_DEPENDENCIES="${INSTALL_DEPENDENCIES:-false}"
UPDATE_DEPENDENCIES="${UPDATE_DEPENDENCIES:-false}"

# ============================================================================
# DIRECTORY STRUCTURE CONFIGURATION
# ============================================================================

# Define directory structure
declare -A REQUIRED_DIRECTORIES=(
    ["test-data"]="Test data and repositories"
    ["test-data/repositories"]="Test repositories for validation"
    ["test-data/results"]="Test execution results"
    ["test-data/combined"]="Combined test data"
    ["debug-data"]="Debug output and analysis"
    ["debug-data/results"]="Debug execution results"
    ["debug-data/reports"]="Debug analysis reports"
    ["logs"]="Service and script logs"
    ["tmp"]="Temporary files and cache"
    ["dev-data"]="Development-specific data"
    ["coverage-results"]="Code coverage reports"
    ["performance-results"]="Performance benchmarking data"
)

# Define optional directories
declare -A OPTIONAL_DIRECTORIES=(
    ["benchmarks"]="Performance benchmark data"
    ["docs/generated"]="Generated documentation"
    ["scripts/archive"]="Archived scripts and configs"
    ["validation-results"]="Validation evidence and reports"
)

# ============================================================================
# ENVIRONMENT FILE TEMPLATES
# ============================================================================

# Development environment template
create_development_env() {
    local env_file="$1"
    
    cat > "$env_file" << EOF
# Analysis Engine Development Environment
# Generated by setup-environment.sh on $(date)

# ===========================================
# ENVIRONMENT CONFIGURATION
# ===========================================

# Environment type
ENVIRONMENT=development
TARGET_ENVIRONMENT=development

# Logging configuration
RUST_LOG=analysis_engine=debug,tower_http=debug,tracing=debug
RUST_BACKTRACE=1
LOG_LEVEL=debug
LOG_FORMAT=pretty
ENABLE_STRUCTURED_LOGGING=false

# ===========================================
# SERVICE CONFIGURATION
# ===========================================

# Server settings
PORT=8001
ENABLE_METRICS=true
ENABLE_TRACING=true
HEALTH_CHECK_INTERVAL=30

# Authentication (disabled for development)
ENABLE_AUTH=false
JWT_SECRET=development-secret-key-not-for-production

# ===========================================
# DEVELOPMENT SETTINGS
# ===========================================

# Development features
USE_MOCK_SERVICES=true
ENABLE_HOT_RELOAD=false
SKIP_DEPENDENCY_CHECKS=false

# Performance settings (relaxed for development)
MAX_CONCURRENT_ANALYSES=10
MAX_FILE_SIZE_BYTES=10485760
PARSE_TIMEOUT_SECONDS=30
MAX_ANALYSIS_MEMORY_MB=2048
MAX_DEPENDENCY_COUNT=10000

# Rate limiting (relaxed for development)
RATE_LIMIT_PER_HOUR=10000

# ===========================================
# MOCK SERVICE CONFIGURATION
# ===========================================

# Mock GCP settings
GCP_PROJECT_ID=development-project
SPANNER_INSTANCE_ID=development-instance
SPANNER_DATABASE_ID=development-database

# Mock Redis settings (optional)
REDIS_URL=redis://localhost:6379
REDIS_TIMEOUT=5
REDIS_MAX_CONNECTIONS=10

# Mock storage settings
STORAGE_BUCKET=development-storage

# Mock Pub/Sub settings
PUBSUB_TOPIC_EVENTS=development-events
PUBSUB_TOPIC_PROGRESS=development-progress
PUBSUB_TOPIC_PATTERNS=development-patterns

# ===========================================
# TESTING CONFIGURATION
# ===========================================

# Test execution settings
TEST_TIMEOUT=300
TEST_PARALLELISM=4
TEST_DATA_DIR=test-data

# Performance testing
PERFORMANCE_TARGET_LOC=100000
PERFORMANCE_TARGET_TIME=30
PERFORMANCE_MIN_THROUGHPUT=3333

# Validation settings
VALIDATION_RETRY_ATTEMPTS=3
VALIDATION_RETRY_DELAY=5

# ===========================================
# DEVELOPMENT TOOLS
# ===========================================

# Debug settings
DEBUG_MODE=comprehensive
SAVE_DEBUG_RESULTS=true
GENERATE_DEBUG_REPORTS=true

# Build settings
BUILD_MODE=debug
REBUILD_ON_CHANGE=false
CHECK_DEPENDENCIES=true

# ===========================================
# PATHS AND DIRECTORIES
# ===========================================

# Data directories
DEBUG_DATA_DIR=debug-data
RESULTS_DIR=test-data/results
REPORTS_DIR=debug-data/reports

# Cache settings
ENABLE_CACHING=true
CACHE_TTL=3600

EOF
}

# Testing environment template
create_testing_env() {
    local env_file="$1"
    
    cat > "$env_file" << EOF
# Analysis Engine Testing Environment
# Generated by setup-environment.sh on $(date)

# ===========================================
# ENVIRONMENT CONFIGURATION
# ===========================================

# Environment type
ENVIRONMENT=testing
TARGET_ENVIRONMENT=testing

# Logging configuration
RUST_LOG=analysis_engine=info,tower_http=info
RUST_BACKTRACE=1
LOG_LEVEL=info
LOG_FORMAT=json
ENABLE_STRUCTURED_LOGGING=true

# ===========================================
# SERVICE CONFIGURATION
# ===========================================

# Server settings
PORT=8001
ENABLE_METRICS=true
ENABLE_TRACING=true
HEALTH_CHECK_INTERVAL=10

# Authentication (optional for testing)
ENABLE_AUTH=false
JWT_SECRET=testing-secret-key

# ===========================================
# TESTING CONFIGURATION
# ===========================================

# Test execution settings
TEST_TIMEOUT=600
TEST_PARALLELISM=8
TEST_DATA_DIR=test-data
GENERATE_REPORTS=true

# Performance testing
PERFORMANCE_TARGET_LOC=1000000
PERFORMANCE_TARGET_TIME=300
PERFORMANCE_MIN_THROUGHPUT=3333

# Validation settings
VALIDATION_RETRY_ATTEMPTS=5
VALIDATION_RETRY_DELAY=3

# ===========================================
# MOCK SERVICE CONFIGURATION
# ===========================================

# Mock GCP settings
GCP_PROJECT_ID=testing-project
SPANNER_INSTANCE_ID=testing-instance
SPANNER_DATABASE_ID=testing-database

# Mock Redis settings
REDIS_URL=redis://localhost:6379
REDIS_TIMEOUT=10
REDIS_MAX_CONNECTIONS=50

# Mock storage settings
STORAGE_BUCKET=testing-storage

# ===========================================
# PERFORMANCE CONFIGURATION
# ===========================================

# Performance settings
MAX_CONCURRENT_ANALYSES=50
MAX_FILE_SIZE_BYTES=10485760
PARSE_TIMEOUT_SECONDS=30
MAX_ANALYSIS_MEMORY_MB=4096
MAX_DEPENDENCY_COUNT=50000

# Rate limiting
RATE_LIMIT_PER_HOUR=5000

EOF
}

# CI environment template
create_ci_env() {
    local env_file="$1"
    
    cat > "$env_file" << EOF
# Analysis Engine CI Environment
# Generated by setup-environment.sh on $(date)

# ===========================================
# ENVIRONMENT CONFIGURATION
# ===========================================

# Environment type
ENVIRONMENT=ci
TARGET_ENVIRONMENT=ci

# Logging configuration
RUST_LOG=analysis_engine=info
RUST_BACKTRACE=1
LOG_LEVEL=info
LOG_FORMAT=json
ENABLE_STRUCTURED_LOGGING=true

# ===========================================
# CI CONFIGURATION
# ===========================================

# Build settings
BUILD_MODE=release
CHECK_DEPENDENCIES=true
SKIP_OPTIONAL_TESTS=false

# Test execution
TEST_TIMEOUT=1200
TEST_PARALLELISM=4
GENERATE_REPORTS=true
SAVE_RESULTS=true

# ===========================================
# MOCK SERVICE CONFIGURATION
# ===========================================

# CI GCP settings
GCP_PROJECT_ID=ci-project
SPANNER_INSTANCE_ID=ci-instance
SPANNER_DATABASE_ID=ci-database

# Minimal service configuration
ENABLE_AUTH=false
USE_MOCK_SERVICES=true
ENABLE_METRICS=false
ENABLE_TRACING=false

# ===========================================
# PERFORMANCE CONFIGURATION
# ===========================================

# CI performance limits
MAX_CONCURRENT_ANALYSES=20
MAX_FILE_SIZE_BYTES=10485760
PARSE_TIMEOUT_SECONDS=60

EOF
}

# ============================================================================
# SETUP FUNCTIONS
# ============================================================================

# Initialize environment setup
initialize_setup() {
    log_header "ANALYSIS ENGINE ENVIRONMENT SETUP"
    log_info "Setup mode: $SETUP_MODE"
    log_info "Setup type: $SETUP_TYPE"
    log_info "Target environment: $TARGET_ENVIRONMENT"
    log_info "Force setup: $FORCE_SETUP"
    
    # Check prerequisites
    check_setup_prerequisites
}

# Check setup prerequisites
check_setup_prerequisites() {
    log_info "Checking setup prerequisites"
    
    # Check if we're in the right directory
    if [[ ! -f "$SCRIPT_DIR/../../Cargo.toml" ]]; then
        log_error "Not in analysis engine root directory"
        exit $ERR_RESOURCE_NOT_FOUND
    fi
    
    # Check required commands
    require_command "mkdir" "directory creation"
    require_command "chmod" "permission management"
    
    # Check if git is available for backup
    if command -v git &> /dev/null; then
        log_success "Git available for backup operations"
    else
        log_warn "Git not available, backups will be manual"
    fi
    
    log_success "Prerequisites check completed"
}

# Setup directory structure
setup_directory_structure() {
    log_header "SETTING UP DIRECTORY STRUCTURE"
    
    local base_dir="$SCRIPT_DIR/../../"
    
    # Create required directories
    log_info "Creating required directories..."
    for dir in "${!REQUIRED_DIRECTORIES[@]}"; do
        local full_path="$base_dir$dir"
        local description="${REQUIRED_DIRECTORIES[$dir]}"
        
        if [[ ! -d "$full_path" ]] || [[ "$FORCE_SETUP" == "true" ]]; then
            mkdir -p "$full_path"
            log_success "Created: $dir ($description)"
        else
            log_info "Exists: $dir"
        fi
    done
    
    # Create optional directories if comprehensive setup
    if [[ "$SETUP_TYPE" == "comprehensive" ]]; then
        log_info "Creating optional directories..."
        for dir in "${!OPTIONAL_DIRECTORIES[@]}"; do
            local full_path="$base_dir$dir"
            local description="${OPTIONAL_DIRECTORIES[$dir]}"
            
            if [[ ! -d "$full_path" ]]; then
                mkdir -p "$full_path"
                log_success "Created: $dir ($description)"
            else
                log_info "Exists: $dir"
            fi
        done
    fi
    
    # Set appropriate permissions
    setup_directory_permissions "$base_dir"
    
    log_success "Directory structure setup completed"
}

# Setup directory permissions
setup_directory_permissions() {
    local base_dir="$1"
    
    log_info "Setting directory permissions..."
    
    # Set standard permissions for data directories
    local data_dirs=("test-data" "debug-data" "logs" "tmp" "dev-data")
    for dir in "${data_dirs[@]}"; do
        if [[ -d "$base_dir$dir" ]]; then
            chmod 755 "$base_dir$dir"
            log_debug "Set permissions for $dir"
        fi
    done
    
    # Set write permissions for temporary directories
    local temp_dirs=("tmp" "logs")
    for dir in "${temp_dirs[@]}"; do
        if [[ -d "$base_dir$dir" ]]; then
            chmod 777 "$base_dir$dir"
            log_debug "Set write permissions for $dir"
        fi
    done
    
    log_success "Directory permissions configured"
}

# Setup environment file
setup_environment_file() {
    log_header "SETTING UP ENVIRONMENT FILE"
    
    local env_file="$SCRIPT_DIR/../../.env"
    local env_example_file="$SCRIPT_DIR/../../.env.example"
    
    # Backup existing .env if it exists
    if [[ -f "$env_file" && "$BACKUP_EXISTING" == "true" ]]; then
        backup_existing_env_file "$env_file"
    fi
    
    # Create environment file based on target environment
    if [[ "$CREATE_ENV_FILE" == "true" ]] || [[ "$FORCE_SETUP" == "true" ]] || [[ ! -f "$env_file" ]]; then
        log_info "Creating environment file for: $TARGET_ENVIRONMENT"
        
        case "$TARGET_ENVIRONMENT" in
            "development")
                create_development_env "$env_file"
                ;;
            "testing")
                create_testing_env "$env_file"
                ;;
            "ci")
                create_ci_env "$env_file"
                ;;
            *)
                log_warn "Unknown target environment: $TARGET_ENVIRONMENT, using development"
                create_development_env "$env_file"
                ;;
        esac
        
        log_success "Environment file created: .env"
    else
        log_info "Environment file already exists, skipping creation"
    fi
    
    # Create .env.example if it doesn't exist
    if [[ ! -f "$env_example_file" ]]; then
        log_info "Creating .env.example template"
        create_development_env "$env_example_file"
        
        # Add warning to example file
        sed -i '1i# Example environment file - DO NOT use in production' "$env_example_file"
        sed -i '2i# Copy to .env and modify for your environment\n' "$env_example_file"
        
        log_success "Created .env.example template"
    fi
    
    # Validate environment file
    validate_environment_file "$env_file"
}

# Backup existing environment file
backup_existing_env_file() {
    local env_file="$1"
    local backup_file="${env_file}.backup.$(date +%Y%m%d_%H%M%S)"
    
    log_info "Backing up existing environment file..."
    
    if cp "$env_file" "$backup_file"; then
        log_success "Backup created: $(basename "$backup_file")"
    else
        log_error "Failed to create backup"
        exit $ERR_DEPLOYMENT_FAILURE
    fi
}

# Validate environment file
validate_environment_file() {
    local env_file="$1"
    
    log_info "Validating environment file..."
    
    if [[ ! -f "$env_file" ]]; then
        log_error "Environment file not found: $env_file"
        return 1
    fi
    
    # Check if file can be sourced
    if bash -n "$env_file"; then
        log_success "Environment file syntax is valid"
    else
        log_error "Environment file has syntax errors"
        return 1
    fi
    
    # Check for required variables
    local required_vars=("ENVIRONMENT" "RUST_LOG")
    local missing_vars=()
    
    # Source the file in a subshell to check variables
    local env_content
    env_content=$(set -a && source "$env_file" && env)
    
    for var in "${required_vars[@]}"; do
        if ! echo "$env_content" | grep -q "^$var="; then
            missing_vars+=("$var")
        fi
    done
    
    if [[ ${#missing_vars[@]} -gt 0 ]]; then
        log_warn "Missing recommended variables: ${missing_vars[*]}"
    else
        log_success "All required variables are present"
    fi
    
    return 0
}

# Setup dependencies
setup_dependencies() {
    if [[ "$CHECK_DEPENDENCIES" != "true" ]]; then
        return 0
    fi
    
    log_header "CHECKING DEPENDENCIES"
    
    # Check Rust toolchain
    check_rust_dependencies
    
    # Check system dependencies
    check_system_dependencies
    
    # Check optional dependencies
    check_optional_dependencies
    
    log_success "Dependency check completed"
}

# Check Rust dependencies
check_rust_dependencies() {
    log_info "Checking Rust dependencies..."
    
    # Check Rust installation
    if ! command -v rustc &> /dev/null; then
        log_error "Rust is not installed"
        log_error "Install Rust from: https://rustup.rs/"
        exit $ERR_MISSING_DEPENDENCY
    fi
    
    # Check Cargo
    if ! command -v cargo &> /dev/null; then
        log_error "Cargo is not available"
        exit $ERR_MISSING_DEPENDENCY
    fi
    
    # Check if dependencies are resolved
    cd "$SCRIPT_DIR/../../" || exit $ERR_RESOURCE_NOT_FOUND
    
    if [[ ! -f "Cargo.lock" ]] || [[ "$UPDATE_DEPENDENCIES" == "true" ]]; then
        log_info "Resolving Cargo dependencies..."
        
        if cargo fetch; then
            log_success "Cargo dependencies resolved"
        else
            log_error "Failed to resolve Cargo dependencies"
            exit $ERR_MISSING_DEPENDENCY
        fi
    else
        log_success "Cargo dependencies already resolved"
    fi
    
    # Check compilation
    log_info "Checking project compilation..."
    if cargo check --quiet; then
        log_success "Project compiles successfully"
    else
        log_error "Project has compilation errors"
        exit $ERR_DEPLOYMENT_FAILURE
    fi
}

# Check system dependencies
check_system_dependencies() {
    log_info "Checking system dependencies..."
    
    # Required system commands
    local required_commands=("git" "curl" "grep" "awk" "sed")
    local missing_commands=()
    
    for cmd in "${required_commands[@]}"; do
        if ! command -v "$cmd" &> /dev/null; then
            missing_commands+=("$cmd")
        fi
    done
    
    if [[ ${#missing_commands[@]} -gt 0 ]]; then
        log_error "Missing required commands: ${missing_commands[*]}"
        exit $ERR_MISSING_DEPENDENCY
    else
        log_success "All required system commands are available"
    fi
}

# Check optional dependencies
check_optional_dependencies() {
    log_info "Checking optional dependencies..."
    
    # Optional but recommended commands
    local optional_commands=("jq" "tokei" "parallel" "lsof" "netstat")
    local missing_optional=()
    
    for cmd in "${optional_commands[@]}"; do
        if ! command -v "$cmd" &> /dev/null; then
            missing_optional+=("$cmd")
        else
            log_success "$cmd is available"
        fi
    done
    
    if [[ ${#missing_optional[@]} -gt 0 ]]; then
        log_warn "Missing optional commands: ${missing_optional[*]}"
        log_info "These commands provide enhanced functionality but are not required"
    fi
}

# ============================================================================
# VALIDATION FUNCTIONS
# ============================================================================

# Run comprehensive validation
run_comprehensive_validation() {
    if [[ "$SKIP_VALIDATION" == "true" ]]; then
        log_info "Skipping validation (--skip-validation specified)"
        return 0
    fi
    
    log_header "RUNNING COMPREHENSIVE VALIDATION"
    
    # Reset validation counters
    reset_validation_counters
    
    # Validate directory structure
    validate "Directory structure" "validate_directory_structure"
    
    # Validate environment file
    validate "Environment file" "validate_environment_file '$SCRIPT_DIR/../../.env'"
    
    # Validate permissions
    validate "Directory permissions" "validate_directory_permissions"
    
    # Validate Rust environment
    validate "Rust environment" "validate_rust_environment"
    
    # Print validation summary
    print_validation_summary
    local validation_result=$?
    
    if [[ $validation_result -ne 0 ]]; then
        log_error "Environment validation failed"
        log_error "Please fix the issues above before proceeding"
        exit $ERR_VALIDATION_FAILURE
    fi
    
    log_success "Environment validation completed successfully"
}

# Validate directory structure
validate_directory_structure() {
    local base_dir="$SCRIPT_DIR/../../"
    
    for dir in "${!REQUIRED_DIRECTORIES[@]}"; do
        local full_path="$base_dir$dir"
        if [[ ! -d "$full_path" ]]; then
            log_debug "Required directory missing: $dir"
            return 1
        fi
    done
    
    log_debug "Directory structure is valid"
    return 0
}

# Validate directory permissions
validate_directory_permissions() {
    local base_dir="$SCRIPT_DIR/../../"
    
    # Check if directories are writable
    local writable_dirs=("tmp" "logs" "test-data" "debug-data")
    
    for dir in "${writable_dirs[@]}"; do
        local full_path="$base_dir$dir"
        if [[ -d "$full_path" && ! -w "$full_path" ]]; then
            log_debug "Directory not writable: $dir"
            return 1
        fi
    done
    
    log_debug "Directory permissions are correct"
    return 0
}

# Validate Rust environment
validate_rust_environment() {
    cd "$SCRIPT_DIR/../../" || return 1
    
    # Check if Cargo.toml exists
    if [[ ! -f "Cargo.toml" ]]; then
        log_debug "Cargo.toml not found"
        return 1
    fi
    
    # Check if dependencies are resolved
    if [[ ! -f "Cargo.lock" ]]; then
        log_debug "Cargo.lock not found - dependencies not resolved"
        return 1
    fi
    
    # Check basic compilation
    if ! cargo check --quiet &> /dev/null; then
        log_debug "Project does not compile"
        return 1
    fi
    
    log_debug "Rust environment is valid"
    return 0
}

# ============================================================================
# COMMAND LINE INTERFACE
# ============================================================================

# Show usage information
show_usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Enhanced environment setup script for the Analysis Engine.

OPTIONS:
    --mode MODE         Setup mode: development|testing|ci (default: development)
    --type TYPE         Setup type: minimal|standard|comprehensive (default: comprehensive)
    --env ENV           Target environment: development|testing|ci|production (default: development)
    --force             Force setup even if files exist
    --skip-validation   Skip validation steps
    --no-env            Skip environment file creation
    --no-backup         Skip backing up existing files
    --no-directories    Skip directory creation
    --install-deps      Install missing dependencies (if possible)
    --update-deps       Update existing dependencies
    -h, --help          Show this help message

EXAMPLES:
    $0                              # Comprehensive development setup
    $0 --mode testing               # Setup for testing environment
    $0 --type minimal               # Minimal setup (directories only)
    $0 --env ci --no-backup         # CI setup without backups
    $0 --force --update-deps        # Force setup with dependency updates

ENVIRONMENT VARIABLES:
    SETUP_MODE          Default setup mode
    SETUP_TYPE          Default setup type
    TARGET_ENVIRONMENT  Default target environment
    FORCE_SETUP         Force setup flag

EOF
}

# Parse command line arguments
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --mode)
                SETUP_MODE="$2"
                shift 2
                ;;
            --type)
                SETUP_TYPE="$2"
                shift 2
                ;;
            --env)
                TARGET_ENVIRONMENT="$2"
                shift 2
                ;;
            --force)
                FORCE_SETUP=true
                shift
                ;;
            --skip-validation)
                SKIP_VALIDATION=true
                shift
                ;;
            --no-env)
                CREATE_ENV_FILE=false
                shift
                ;;
            --no-backup)
                BACKUP_EXISTING=false
                shift
                ;;
            --no-directories)
                SETUP_DIRECTORIES=false
                shift
                ;;
            --install-deps)
                INSTALL_DEPENDENCIES=true
                shift
                ;;
            --update-deps)
                UPDATE_DEPENDENCIES=true
                shift
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                show_usage
                exit $ERR_INVALID_CONFIG
                ;;
        esac
    done
}

# ============================================================================
# MAIN EXECUTION
# ============================================================================

main() {
    # Initialize error handling
    setup_error_handling
    
    # Parse arguments
    parse_arguments "$@"
    
    # Apply defaults
    apply_defaults
    
    # Initialize setup
    initialize_setup
    
    # Run setup steps based on configuration
    if [[ "$SETUP_DIRECTORIES" == "true" ]]; then
        setup_directory_structure
    fi
    
    if [[ "$CREATE_ENV_FILE" == "true" ]]; then
        setup_environment_file
    fi
    
    if [[ "$CHECK_DEPENDENCIES" == "true" ]]; then
        setup_dependencies
    fi
    
    # Run validation
    run_comprehensive_validation
    
    # Final summary
    log_header "ENVIRONMENT SETUP SUMMARY"
    log_success "Environment setup completed successfully"
    log_info "Setup mode: $SETUP_MODE"
    log_info "Setup type: $SETUP_TYPE"
    log_info "Target environment: $TARGET_ENVIRONMENT"
    log_info ""
    log_info "Next steps:"
    log_info "1. Review the .env file and customize as needed"
    log_info "2. Start development: ./scripts/development/dev-start.sh"
    log_info "3. Run tests: ./scripts/testing/test-runner.sh"
    log_info "4. Debug issues: ./scripts/development/debug-parser.sh"
}

# Execute main function
main "$@"