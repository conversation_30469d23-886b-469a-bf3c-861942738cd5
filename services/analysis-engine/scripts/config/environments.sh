#!/bin/bash
# Environment Configuration for Analysis Engine Scripts
# Centralizes all environment variables and configuration

# Guard against multiple sourcing
if [[ -n "${ANALYSIS_ENGINE_ENVIRONMENTS_LOADED:-}" ]]; then
    return 0
fi
ANALYSIS_ENGINE_ENVIRONMENTS_LOADED=1

# ============================================================================
# GOOGLE CLOUD PLATFORM CONFIGURATION
# ============================================================================

# Core project settings
export PROJECT_ID="${PROJECT_ID:-vibe-match-463114}"
export REGION="${REGION:-us-central1}"
export ZONE="${ZONE:-us-central1-a}"

# Service configuration
export SERVICE_NAME="${SERVICE_NAME:-analysis-engine}"
export SERVICE_ACCOUNT="${SERVICE_ACCOUNT:-analysis-engine@${PROJECT_ID}.iam.gserviceaccount.com}"

# Container Registry settings
export REGISTRY="${REGISTRY:-gcr.io}"
export IMAGE_NAME="${IMAGE_NAME:-${REGISTRY}/${PROJECT_ID}/${SERVICE_NAME}}"

# ============================================================================
# SPANNER DATABASE CONFIGURATION
# ============================================================================

export SPANNER_PROJECT_ID="${SPANNER_PROJECT_ID:-$PROJECT_ID}"
export SPANNER_INSTANCE_ID="${SPANNER_INSTANCE_ID:-ccl-instance}"
export SPANNER_DATABASE_ID="${SPANNER_DATABASE_ID:-ccl_main}"

# Database connection settings
export SPANNER_TIMEOUT="${SPANNER_TIMEOUT:-30}"
export SPANNER_MAX_SESSIONS="${SPANNER_MAX_SESSIONS:-400}"

# ============================================================================
# REDIS CACHE CONFIGURATION
# ============================================================================

export REDIS_HOST="${REDIS_HOST:-***********}"
export REDIS_PORT="${REDIS_PORT:-6379}"
export REDIS_URL="${REDIS_URL:-redis://${REDIS_HOST}:${REDIS_PORT}}"

# Redis connection settings
export REDIS_TIMEOUT="${REDIS_TIMEOUT:-5}"
export REDIS_MAX_CONNECTIONS="${REDIS_MAX_CONNECTIONS:-100}"

# ============================================================================
# STORAGE CONFIGURATION
# ============================================================================

export STORAGE_BUCKET="${STORAGE_BUCKET:-ccl-analysis-artifacts}"
export STORAGE_BUCKET_NAME="${STORAGE_BUCKET_NAME:-$STORAGE_BUCKET}"

# ============================================================================
# PUB/SUB CONFIGURATION
# ============================================================================

export PUBSUB_TOPIC_EVENTS="${PUBSUB_TOPIC_EVENTS:-analysis-events}"
export PUBSUB_TOPIC_PROGRESS="${PUBSUB_TOPIC_PROGRESS:-analysis-progress}"
export PUBSUB_TOPIC_PATTERNS="${PUBSUB_TOPIC_PATTERNS:-pattern-detected}"

# Legacy compatibility
export PUBSUB_TOPIC="${PUBSUB_TOPIC:-$PUBSUB_TOPIC_EVENTS}"

# ============================================================================
# VPC AND NETWORKING CONFIGURATION
# ============================================================================

export VPC_CONNECTOR="${VPC_CONNECTOR:-analysis-engine-connector}"
export VPC_NETWORK="${VPC_NETWORK:-default}"

# ============================================================================
# CLOUD RUN CONFIGURATION
# ============================================================================

# Resource limits
export CLOUD_RUN_MEMORY="${CLOUD_RUN_MEMORY:-4Gi}"
export CLOUD_RUN_CPU="${CLOUD_RUN_CPU:-4}"
export CLOUD_RUN_TIMEOUT="${CLOUD_RUN_TIMEOUT:-600}"

# Scaling configuration
export CLOUD_RUN_MAX_INSTANCES="${CLOUD_RUN_MAX_INSTANCES:-100}"
export CLOUD_RUN_MIN_INSTANCES="${CLOUD_RUN_MIN_INSTANCES:-1}"
export CLOUD_RUN_CONCURRENCY="${CLOUD_RUN_CONCURRENCY:-50}"

# ============================================================================
# APPLICATION CONFIGURATION
# ============================================================================

# Environment settings
export ENVIRONMENT="${ENVIRONMENT:-production}"
export RUST_LOG="${RUST_LOG:-info}"
export RUST_BACKTRACE="${RUST_BACKTRACE:-1}"

# Performance settings
export MAX_CONCURRENT_ANALYSES="${MAX_CONCURRENT_ANALYSES:-50}"
export MAX_FILE_SIZE_BYTES="${MAX_FILE_SIZE_BYTES:-10485760}"
export PARSE_TIMEOUT_SECONDS="${PARSE_TIMEOUT_SECONDS:-30}"
export MAX_ANALYSIS_MEMORY_MB="${MAX_ANALYSIS_MEMORY_MB:-2048}"
export MAX_DEPENDENCY_COUNT="${MAX_DEPENDENCY_COUNT:-10000}"

# ============================================================================
# AUTHENTICATION AND SECURITY
# ============================================================================

# JWT configuration (loaded from Secret Manager)
export JWT_SECRET_NAME="${JWT_SECRET_NAME:-analysis-engine-jwt-secret}"
export ENABLE_AUTH="${ENABLE_AUTH:-true}"
export CORS_ORIGINS="${CORS_ORIGINS:-*}"
export API_KEY_HEADER="${API_KEY_HEADER:-x-api-key}"

# Rate limiting
export RATE_LIMIT_PER_HOUR="${RATE_LIMIT_PER_HOUR:-1000}"
export JWT_ROTATION_DAYS="${JWT_ROTATION_DAYS:-7}"

# Security settings
export ENABLE_AUDIT_LOGGING="${ENABLE_AUDIT_LOGGING:-true}"
export SECURITY_HEADERS_ENABLED="${SECURITY_HEADERS_ENABLED:-true}"

# ============================================================================
# MONITORING AND OBSERVABILITY
# ============================================================================

# Logging configuration
export LOG_LEVEL="${LOG_LEVEL:-info}"
export ENABLE_STRUCTURED_LOGGING="${ENABLE_STRUCTURED_LOGGING:-true}"
export LOG_FORMAT="${LOG_FORMAT:-json}"

# Metrics and monitoring
export ENABLE_METRICS="${ENABLE_METRICS:-true}"
export METRICS_PORT="${METRICS_PORT:-9090}"
export HEALTH_CHECK_INTERVAL="${HEALTH_CHECK_INTERVAL:-30}"

# Tracing
export ENABLE_TRACING="${ENABLE_TRACING:-true}"
export TRACE_SAMPLING_RATE="${TRACE_SAMPLING_RATE:-0.1}"

# ============================================================================
# DEVELOPMENT AND TESTING CONFIGURATION
# ============================================================================

# Development settings
export DEV_MODE="${DEV_MODE:-false}"
export DEBUG_ENABLED="${DEBUG_ENABLED:-false}"
export MOCK_EXTERNAL_SERVICES="${MOCK_EXTERNAL_SERVICES:-false}"

# Testing configuration
export TEST_TIMEOUT="${TEST_TIMEOUT:-300}"
export TEST_PARALLELISM="${TEST_PARALLELISM:-4}"
export TEST_DATA_DIR="${TEST_DATA_DIR:-test-data}"

# Performance testing
export LOAD_TEST_DURATION="${LOAD_TEST_DURATION:-60}"
export LOAD_TEST_CONCURRENT_USERS="${LOAD_TEST_CONCURRENT_USERS:-10}"
export LOAD_TEST_REQUEST_RATE="${LOAD_TEST_REQUEST_RATE:-100}"

# ============================================================================
# SCRIPT CONFIGURATION
# ============================================================================

# Script behavior
export SCRIPT_VERBOSE="${SCRIPT_VERBOSE:-false}"
export SCRIPT_DRY_RUN="${SCRIPT_DRY_RUN:-false}"
export SCRIPT_FORCE="${SCRIPT_FORCE:-false}"

# Logging for scripts
export ANALYSIS_ENGINE_LOG_LEVEL="${ANALYSIS_ENGINE_LOG_LEVEL:-3}"  # INFO level
export ANALYSIS_ENGINE_LOG_TO_FILE="${ANALYSIS_ENGINE_LOG_TO_FILE:-false}"
export ANALYSIS_ENGINE_LOG_FILE="${ANALYSIS_ENGINE_LOG_FILE:-}"

# Script timeouts
export DEPLOYMENT_TIMEOUT="${DEPLOYMENT_TIMEOUT:-1800}"  # 30 minutes
export VALIDATION_TIMEOUT="${VALIDATION_TIMEOUT:-600}"   # 10 minutes
export HEALTH_CHECK_TIMEOUT="${HEALTH_CHECK_TIMEOUT:-60}" # 1 minute

# ============================================================================
# DERIVED CONFIGURATIONS
# ============================================================================

# Construct derived values
export SPANNER_DATABASE_PATH="projects/${SPANNER_PROJECT_ID}/instances/${SPANNER_INSTANCE_ID}/databases/${SPANNER_DATABASE_ID}"
export STORAGE_BUCKET_URL="gs://${STORAGE_BUCKET}"

# Service URLs (to be set after deployment)
export SERVICE_URL="${SERVICE_URL:-}"
export INTERNAL_SERVICE_URL="${INTERNAL_SERVICE_URL:-}"

# ============================================================================
# ENVIRONMENT VALIDATION
# ============================================================================

# Required environment variables
REQUIRED_ENV_VARS=(
    "PROJECT_ID"
    "REGION"
    "SERVICE_NAME"
    "SPANNER_INSTANCE_ID"
    "SPANNER_DATABASE_ID"
    "STORAGE_BUCKET"
)

# Validate required environment variables
validate_environment() {
    local missing_vars=()
    
    for var in "${REQUIRED_ENV_VARS[@]}"; do
        if [[ -z "${!var:-}" ]]; then
            missing_vars+=("$var")
        fi
    done
    
    if [[ ${#missing_vars[@]} -gt 0 ]]; then
        echo "Error: Missing required environment variables:" >&2
        printf '  - %s\n' "${missing_vars[@]}" >&2
        return 1
    fi
    
    return 0
}

# Load environment-specific overrides
load_environment_overrides() {
    local env_file="${1:-}"
    
    if [[ -n "$env_file" && -f "$env_file" ]]; then
        echo "Loading environment overrides from: $env_file"
        # shellcheck source=/dev/null
        source "$env_file"
    fi
}

# Export all configuration for child processes
export_all_config() {
    # This function ensures all variables are properly exported
    # This is useful when calling scripts that need environment variables
    
    local config_vars=(
        # Core configuration
        "PROJECT_ID" "REGION" "ZONE" "SERVICE_NAME" "SERVICE_ACCOUNT"
        "REGISTRY" "IMAGE_NAME"
        
        # Database
        "SPANNER_PROJECT_ID" "SPANNER_INSTANCE_ID" "SPANNER_DATABASE_ID"
        "SPANNER_TIMEOUT" "SPANNER_MAX_SESSIONS" "SPANNER_DATABASE_PATH"
        
        # Redis
        "REDIS_HOST" "REDIS_PORT" "REDIS_URL" "REDIS_TIMEOUT" "REDIS_MAX_CONNECTIONS"
        
        # Storage
        "STORAGE_BUCKET" "STORAGE_BUCKET_NAME" "STORAGE_BUCKET_URL"
        
        # Pub/Sub
        "PUBSUB_TOPIC_EVENTS" "PUBSUB_TOPIC_PROGRESS" "PUBSUB_TOPIC_PATTERNS" "PUBSUB_TOPIC"
        
        # Cloud Run
        "CLOUD_RUN_MEMORY" "CLOUD_RUN_CPU" "CLOUD_RUN_TIMEOUT"
        "CLOUD_RUN_MAX_INSTANCES" "CLOUD_RUN_MIN_INSTANCES" "CLOUD_RUN_CONCURRENCY"
        
        # Application
        "ENVIRONMENT" "RUST_LOG" "RUST_BACKTRACE"
        "MAX_CONCURRENT_ANALYSES" "MAX_FILE_SIZE_BYTES" "PARSE_TIMEOUT_SECONDS"
        "MAX_ANALYSIS_MEMORY_MB" "MAX_DEPENDENCY_COUNT"
        
        # Security
        "JWT_SECRET_NAME" "ENABLE_AUTH" "CORS_ORIGINS" "API_KEY_HEADER"
        "RATE_LIMIT_PER_HOUR" "JWT_ROTATION_DAYS" "ENABLE_AUDIT_LOGGING"
        
        # Monitoring
        "LOG_LEVEL" "ENABLE_STRUCTURED_LOGGING" "LOG_FORMAT"
        "ENABLE_METRICS" "METRICS_PORT" "HEALTH_CHECK_INTERVAL"
        "ENABLE_TRACING" "TRACE_SAMPLING_RATE"
        
        # Development
        "DEV_MODE" "DEBUG_ENABLED" "MOCK_EXTERNAL_SERVICES"
        
        # Testing
        "TEST_TIMEOUT" "TEST_PARALLELISM" "TEST_DATA_DIR"
        "LOAD_TEST_DURATION" "LOAD_TEST_CONCURRENT_USERS" "LOAD_TEST_REQUEST_RATE"
        
        # Scripts
        "SCRIPT_VERBOSE" "SCRIPT_DRY_RUN" "SCRIPT_FORCE"
        "ANALYSIS_ENGINE_LOG_LEVEL" "ANALYSIS_ENGINE_LOG_TO_FILE" "ANALYSIS_ENGINE_LOG_FILE"
        "DEPLOYMENT_TIMEOUT" "VALIDATION_TIMEOUT" "HEALTH_CHECK_TIMEOUT"
    )
    
    for var in "${config_vars[@]}"; do
        if [[ -n "${!var:-}" ]]; then
            export "$var"
        fi
    done
}

# ============================================================================
# INITIALIZATION
# ============================================================================

# Auto-validate and export when sourced
if [[ "${BASH_SOURCE[0]}" != "${0}" ]]; then
    # Only validate if not in testing mode
    if [[ "${SKIP_ENV_VALIDATION:-false}" != "true" ]]; then
        validate_environment
    fi
    
    # Export all configuration
    export_all_config
    
    # Load any environment-specific overrides
    if [[ -n "${ENV_OVERRIDE_FILE:-}" ]]; then
        load_environment_overrides "$ENV_OVERRIDE_FILE"
    fi
fi