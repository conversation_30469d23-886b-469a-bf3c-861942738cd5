#!/bin/bash
# Default Values Configuration for Analysis Engine Scripts
# Provides sensible defaults for all configurable parameters

# Guard against multiple sourcing
if [[ -n "${ANALYSIS_ENGINE_DEFAULTS_LOADED:-}" ]]; then
    return 0
fi
ANALYSIS_ENGINE_DEFAULTS_LOADED=1

# ============================================================================
# DEFAULT VALUES REGISTRY
# ============================================================================

# This file contains all default values used across the Analysis Engine scripts.
# These values are used as fallbacks when environment variables are not set.

# ============================================================================
# INFRASTRUCTURE DEFAULTS
# ============================================================================

# Google Cloud Platform defaults
readonly DEFAULT_PROJECT_ID="vibe-match-463114"
readonly DEFAULT_REGION="us-central1"
readonly DEFAULT_ZONE="us-central1-a"

# Service defaults
readonly DEFAULT_SERVICE_NAME="analysis-engine"
readonly DEFAULT_REGISTRY="gcr.io"

# ============================================================================
# DATABASE DEFAULTS
# ============================================================================

# Spanner defaults
readonly DEFAULT_SPANNER_INSTANCE="ccl-instance"
readonly DEFAULT_SPANNER_DATABASE="ccl_main"
readonly DEFAULT_SPANNER_TIMEOUT=30
readonly DEFAULT_SPANNER_MAX_SESSIONS=400

# Database schema defaults
readonly DEFAULT_REQUIRED_TABLES=(
    "analyses"
    "file_analyses"
    "patterns"
    "repositories"
    "security_findings"
)

readonly DEFAULT_MINIMUM_INDEXES=5

# ============================================================================
# CACHE AND STORAGE DEFAULTS
# ============================================================================

# Redis defaults
readonly DEFAULT_REDIS_HOST="***********"
readonly DEFAULT_REDIS_PORT=6379
readonly DEFAULT_REDIS_TIMEOUT=5
readonly DEFAULT_REDIS_MAX_CONNECTIONS=100

# Storage defaults
readonly DEFAULT_STORAGE_BUCKET="ccl-analysis-artifacts"

# ============================================================================
# MESSAGING DEFAULTS
# ============================================================================

# Pub/Sub topic defaults
readonly DEFAULT_PUBSUB_TOPIC_EVENTS="analysis-events"
readonly DEFAULT_PUBSUB_TOPIC_PROGRESS="analysis-progress"
readonly DEFAULT_PUBSUB_TOPIC_PATTERNS="pattern-detected"

# ============================================================================
# CLOUD RUN DEPLOYMENT DEFAULTS
# ============================================================================

# Resource allocation defaults
readonly DEFAULT_CLOUD_RUN_MEMORY="4Gi"
readonly DEFAULT_CLOUD_RUN_CPU=4
readonly DEFAULT_CLOUD_RUN_TIMEOUT=600

# Scaling defaults
readonly DEFAULT_CLOUD_RUN_MAX_INSTANCES=100
readonly DEFAULT_CLOUD_RUN_MIN_INSTANCES=1
readonly DEFAULT_CLOUD_RUN_CONCURRENCY=50

# Networking defaults
readonly DEFAULT_VPC_CONNECTOR="analysis-engine-connector"
readonly DEFAULT_VPC_NETWORK="default"

# ============================================================================
# APPLICATION PERFORMANCE DEFAULTS
# ============================================================================

# Analysis engine performance defaults
readonly DEFAULT_MAX_CONCURRENT_ANALYSES=50
readonly DEFAULT_MAX_FILE_SIZE_BYTES=10485760  # 10MB
readonly DEFAULT_PARSE_TIMEOUT_SECONDS=30
readonly DEFAULT_MAX_ANALYSIS_MEMORY_MB=2048   # 2GB
readonly DEFAULT_MAX_DEPENDENCY_COUNT=10000

# Performance thresholds
readonly DEFAULT_MAX_RESPONSE_TIME_MS=200
readonly DEFAULT_HEALTH_CHECK_TIMEOUT=10
readonly DEFAULT_API_TIMEOUT=30

# ============================================================================
# SECURITY DEFAULTS
# ============================================================================

# Authentication defaults
readonly DEFAULT_JWT_SECRET_NAME="analysis-engine-jwt-secret"
readonly DEFAULT_ENABLE_AUTH=true
readonly DEFAULT_API_KEY_HEADER="x-api-key"

# Rate limiting defaults
readonly DEFAULT_RATE_LIMIT_PER_HOUR=1000
readonly DEFAULT_JWT_ROTATION_DAYS=7

# CORS defaults
readonly DEFAULT_CORS_ORIGINS="*"

# Security headers defaults
readonly DEFAULT_SECURITY_HEADERS_ENABLED=true

# ============================================================================
# MONITORING AND LOGGING DEFAULTS
# ============================================================================

# Logging defaults
readonly DEFAULT_LOG_LEVEL=3  # Using numeric value for LOG_LEVEL_INFO
readonly DEFAULT_RUST_LOG="info"
readonly DEFAULT_RUST_BACKTRACE=1
readonly DEFAULT_LOG_FORMAT="json"
readonly DEFAULT_ENABLE_STRUCTURED_LOGGING=true

# Metrics defaults
readonly DEFAULT_ENABLE_METRICS=true
readonly DEFAULT_METRICS_PORT=9090
readonly DEFAULT_HEALTH_CHECK_INTERVAL=30

# Tracing defaults
readonly DEFAULT_ENABLE_TRACING=true
readonly DEFAULT_TRACE_SAMPLING_RATE=0.1

# ============================================================================
# TESTING DEFAULTS
# ============================================================================

# Test execution defaults
readonly DEFAULT_TEST_TIMEOUT=300
readonly DEFAULT_TEST_PARALLELISM=4
readonly DEFAULT_TEST_DATA_DIR="test-data"

# Load testing defaults
readonly DEFAULT_LOAD_TEST_DURATION=60
readonly DEFAULT_LOAD_TEST_CONCURRENT_USERS=10
readonly DEFAULT_LOAD_TEST_REQUEST_RATE=100

# Performance testing defaults
readonly DEFAULT_PERFORMANCE_TEST_ITERATIONS=3
readonly DEFAULT_PERFORMANCE_TEST_WARMUP=10

# Validation defaults
readonly DEFAULT_VALIDATION_RETRY_ATTEMPTS=3
readonly DEFAULT_VALIDATION_RETRY_DELAY=5

# ============================================================================
# SCRIPT EXECUTION DEFAULTS
# ============================================================================

# Script behavior defaults
readonly DEFAULT_SCRIPT_VERBOSE=false
readonly DEFAULT_SCRIPT_DRY_RUN=false
readonly DEFAULT_SCRIPT_FORCE=false

# Script timeout defaults
readonly DEFAULT_DEPLOYMENT_TIMEOUT=1800  # 30 minutes
readonly DEFAULT_VALIDATION_TIMEOUT=600   # 10 minutes
readonly DEFAULT_MIGRATION_TIMEOUT=300    # 5 minutes

# Retry defaults
readonly DEFAULT_RETRY_ATTEMPTS=3
readonly DEFAULT_RETRY_DELAY=5
readonly DEFAULT_RETRY_BACKOFF=2

# ============================================================================
# DOCKER DEFAULTS
# ============================================================================

# Docker build defaults
readonly DEFAULT_DOCKERFILE="Dockerfile.simple"
readonly DEFAULT_DOCKER_PLATFORM="linux/amd64"
readonly DEFAULT_DOCKER_CONTEXT="."

# Container defaults
readonly DEFAULT_CONTAINER_PORT=8001
readonly DEFAULT_HEALTH_CHECK_PATH="/health"
readonly DEFAULT_READY_CHECK_PATH="/ready"

# ============================================================================
# ENVIRONMENT-SPECIFIC DEFAULTS
# ============================================================================

# Development environment defaults
readonly DEFAULT_DEV_LOG_LEVEL="debug"
readonly DEFAULT_DEV_ENABLE_MOCK_SERVICES=true
readonly DEFAULT_DEV_SKIP_AUTH=true

# Staging environment defaults
readonly DEFAULT_STAGING_MIN_INSTANCES=1
readonly DEFAULT_STAGING_MAX_INSTANCES=10

# Production environment defaults
readonly DEFAULT_PROD_MIN_INSTANCES=2
readonly DEFAULT_PROD_MAX_INSTANCES=100
readonly DEFAULT_PROD_ENABLE_AUDIT_LOGGING=true

# ============================================================================
# VALIDATION CRITERIA DEFAULTS
# ============================================================================

# Service health validation defaults
readonly DEFAULT_HEALTH_CHECK_RETRIES=5
# DEFAULT_HEALTH_CHECK_INTERVAL is already defined above as 30

# Performance validation defaults
readonly DEFAULT_MIN_SUCCESS_RATE=95  # 95%
readonly DEFAULT_MAX_ERROR_RATE=5     # 5%

# Resource validation defaults
readonly DEFAULT_MIN_DISK_SPACE_MB=1024  # 1GB
readonly DEFAULT_MIN_MEMORY_MB=512       # 512MB

# ============================================================================
# HELPER FUNCTIONS
# ============================================================================

# Get default value for a configuration parameter
get_default_value() {
    local param_name="$1"
    local default_var="DEFAULT_${param_name}"
    
    if [[ -n "${!default_var:-}" ]]; then
        echo "${!default_var}"
    else
        echo ""
    fi
}

# Get effective value (environment variable or default)
get_effective_value() {
    local param_name="$1"
    local env_value="${!param_name:-}"
    
    if [[ -n "$env_value" ]]; then
        echo "$env_value"
    else
        get_default_value "$param_name"
    fi
}

# List all available defaults
list_defaults() {
    echo "Available default values:"
    echo "========================"
    
    # Get all DEFAULT_ variables
    for var in $(compgen -v | grep "^DEFAULT_" | sort); do
        local param_name="${var#DEFAULT_}"
        local default_value="${!var}"
        local current_value="${!param_name:-}"
        
        printf "%-30s: %s" "$param_name" "$default_value"
        
        if [[ -n "$current_value" && "$current_value" != "$default_value" ]]; then
            printf " (current: %s)" "$current_value"
        fi
        
        echo ""
    done
}

# Validate that defaults are sensible
validate_defaults() {
    local errors=()
    
    # Validate numeric defaults
    if [[ ! "$DEFAULT_CLOUD_RUN_CPU" =~ ^[0-9]+$ ]]; then
        errors+=("DEFAULT_CLOUD_RUN_CPU must be numeric")
    fi
    
    if [[ ! "$DEFAULT_CLOUD_RUN_TIMEOUT" =~ ^[0-9]+$ ]]; then
        errors+=("DEFAULT_CLOUD_RUN_TIMEOUT must be numeric")
    fi
    
    if [[ ! "$DEFAULT_REDIS_PORT" =~ ^[0-9]+$ ]]; then
        errors+=("DEFAULT_REDIS_PORT must be numeric")
    fi
    
    # Validate boolean defaults
    if [[ "$DEFAULT_ENABLE_AUTH" != "true" && "$DEFAULT_ENABLE_AUTH" != "false" ]]; then
        errors+=("DEFAULT_ENABLE_AUTH must be true or false")
    fi
    
    # Validate ranges
    if [[ "$DEFAULT_TRACE_SAMPLING_RATE" != "0.1" ]]; then
        # Should be between 0.0 and 1.0
        if ! awk "BEGIN {exit !($DEFAULT_TRACE_SAMPLING_RATE >= 0.0 && $DEFAULT_TRACE_SAMPLING_RATE <= 1.0)}"; then
            errors+=("DEFAULT_TRACE_SAMPLING_RATE must be between 0.0 and 1.0")
        fi
    fi
    
    # Report validation results
    if [[ ${#errors[@]} -gt 0 ]]; then
        echo "Default values validation failed:" >&2
        printf '  - %s\n' "${errors[@]}" >&2
        return 1
    fi
    
    echo "Default values validation passed"
    return 0
}

# Apply defaults to environment if not already set
apply_defaults() {
    local force_override="${1:-false}"
    
    # Infrastructure
    [[ -z "${PROJECT_ID:-}" || "$force_override" == "true" ]] && export PROJECT_ID="$DEFAULT_PROJECT_ID"
    [[ -z "${REGION:-}" || "$force_override" == "true" ]] && export REGION="$DEFAULT_REGION"
    [[ -z "${ZONE:-}" || "$force_override" == "true" ]] && export ZONE="$DEFAULT_ZONE"
    [[ -z "${SERVICE_NAME:-}" || "$force_override" == "true" ]] && export SERVICE_NAME="$DEFAULT_SERVICE_NAME"
    [[ -z "${REGISTRY:-}" || "$force_override" == "true" ]] && export REGISTRY="$DEFAULT_REGISTRY"
    
    # Database
    [[ -z "${SPANNER_INSTANCE_ID:-}" || "$force_override" == "true" ]] && export SPANNER_INSTANCE_ID="$DEFAULT_SPANNER_INSTANCE"
    [[ -z "${SPANNER_DATABASE_ID:-}" || "$force_override" == "true" ]] && export SPANNER_DATABASE_ID="$DEFAULT_SPANNER_DATABASE"
    [[ -z "${SPANNER_TIMEOUT:-}" || "$force_override" == "true" ]] && export SPANNER_TIMEOUT="$DEFAULT_SPANNER_TIMEOUT"
    [[ -z "${SPANNER_MAX_SESSIONS:-}" || "$force_override" == "true" ]] && export SPANNER_MAX_SESSIONS="$DEFAULT_SPANNER_MAX_SESSIONS"
    
    # Redis
    [[ -z "${REDIS_HOST:-}" || "$force_override" == "true" ]] && export REDIS_HOST="$DEFAULT_REDIS_HOST"
    [[ -z "${REDIS_PORT:-}" || "$force_override" == "true" ]] && export REDIS_PORT="$DEFAULT_REDIS_PORT"
    [[ -z "${REDIS_TIMEOUT:-}" || "$force_override" == "true" ]] && export REDIS_TIMEOUT="$DEFAULT_REDIS_TIMEOUT"
    [[ -z "${REDIS_MAX_CONNECTIONS:-}" || "$force_override" == "true" ]] && export REDIS_MAX_CONNECTIONS="$DEFAULT_REDIS_MAX_CONNECTIONS"
    
    # Storage
    [[ -z "${STORAGE_BUCKET:-}" || "$force_override" == "true" ]] && export STORAGE_BUCKET="$DEFAULT_STORAGE_BUCKET"
    
    # Cloud Run
    [[ -z "${CLOUD_RUN_MEMORY:-}" || "$force_override" == "true" ]] && export CLOUD_RUN_MEMORY="$DEFAULT_CLOUD_RUN_MEMORY"
    [[ -z "${CLOUD_RUN_CPU:-}" || "$force_override" == "true" ]] && export CLOUD_RUN_CPU="$DEFAULT_CLOUD_RUN_CPU"
    [[ -z "${CLOUD_RUN_TIMEOUT:-}" || "$force_override" == "true" ]] && export CLOUD_RUN_TIMEOUT="$DEFAULT_CLOUD_RUN_TIMEOUT"
    [[ -z "${CLOUD_RUN_MAX_INSTANCES:-}" || "$force_override" == "true" ]] && export CLOUD_RUN_MAX_INSTANCES="$DEFAULT_CLOUD_RUN_MAX_INSTANCES"
    [[ -z "${CLOUD_RUN_MIN_INSTANCES:-}" || "$force_override" == "true" ]] && export CLOUD_RUN_MIN_INSTANCES="$DEFAULT_CLOUD_RUN_MIN_INSTANCES"
    [[ -z "${CLOUD_RUN_CONCURRENCY:-}" || "$force_override" == "true" ]] && export CLOUD_RUN_CONCURRENCY="$DEFAULT_CLOUD_RUN_CONCURRENCY"
    
    # Application
    [[ -z "${MAX_CONCURRENT_ANALYSES:-}" || "$force_override" == "true" ]] && export MAX_CONCURRENT_ANALYSES="$DEFAULT_MAX_CONCURRENT_ANALYSES"
    [[ -z "${MAX_FILE_SIZE_BYTES:-}" || "$force_override" == "true" ]] && export MAX_FILE_SIZE_BYTES="$DEFAULT_MAX_FILE_SIZE_BYTES"
    [[ -z "${PARSE_TIMEOUT_SECONDS:-}" || "$force_override" == "true" ]] && export PARSE_TIMEOUT_SECONDS="$DEFAULT_PARSE_TIMEOUT_SECONDS"
    [[ -z "${MAX_ANALYSIS_MEMORY_MB:-}" || "$force_override" == "true" ]] && export MAX_ANALYSIS_MEMORY_MB="$DEFAULT_MAX_ANALYSIS_MEMORY_MB"
    [[ -z "${MAX_DEPENDENCY_COUNT:-}" || "$force_override" == "true" ]] && export MAX_DEPENDENCY_COUNT="$DEFAULT_MAX_DEPENDENCY_COUNT"
    
    # Logging
    [[ -z "${LOG_LEVEL:-}" || "$force_override" == "true" ]] && export LOG_LEVEL="$DEFAULT_LOG_LEVEL"
    [[ -z "${RUST_LOG:-}" || "$force_override" == "true" ]] && export RUST_LOG="$DEFAULT_RUST_LOG"
    [[ -z "${RUST_BACKTRACE:-}" || "$force_override" == "true" ]] && export RUST_BACKTRACE="$DEFAULT_RUST_BACKTRACE"
    
    # Security
    [[ -z "${JWT_SECRET_NAME:-}" || "$force_override" == "true" ]] && export JWT_SECRET_NAME="$DEFAULT_JWT_SECRET_NAME"
    [[ -z "${ENABLE_AUTH:-}" || "$force_override" == "true" ]] && export ENABLE_AUTH="$DEFAULT_ENABLE_AUTH"
    [[ -z "${RATE_LIMIT_PER_HOUR:-}" || "$force_override" == "true" ]] && export RATE_LIMIT_PER_HOUR="$DEFAULT_RATE_LIMIT_PER_HOUR"
    
    echo "Defaults applied to environment variables"
}

# ============================================================================
# INITIALIZATION
# ============================================================================

# Validate defaults when sourced
if [[ "${BASH_SOURCE[0]}" != "${0}" ]]; then
    if [[ "${SKIP_DEFAULTS_VALIDATION:-false}" != "true" ]]; then
        validate_defaults
    fi
fi