#!/bin/bash
# Validation Rules Configuration for Analysis Engine Scripts
# Defines validation criteria and business rules

# Source dependencies
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/../lib/logging.sh"

# ============================================================================
# VALIDATION RULES REGISTRY
# ============================================================================

# This file contains all validation rules used across the Analysis Engine scripts.
# Rules are organized by category and include both functional and non-functional requirements.

# ============================================================================
# INFRASTRUCTURE VALIDATION RULES
# ============================================================================

# Google Cloud Project validation rules
readonly VALID_PROJECT_ID_PATTERN="^[a-z][a-z0-9-]{4,28}[a-z0-9]$"
readonly VALID_REGION_PATTERN="^[a-z]+-[a-z0-9]+$"

# Service naming rules
readonly VALID_SERVICE_NAME_PATTERN="^[a-z][a-z0-9-]{1,48}[a-z0-9]$"
readonly MAX_SERVICE_NAME_LENGTH=50

# Container image rules
readonly VALID_IMAGE_TAG_PATTERN="^[a-zA-Z0-9][a-zA-Z0-9._-]{0,127}$"
readonly MAX_IMAGE_TAG_LENGTH=128

# ============================================================================
# DATABASE VALIDATION RULES
# ============================================================================

# Spanner instance and database rules
readonly VALID_SPANNER_INSTANCE_PATTERN="^[a-z][a-z0-9-]{0,29}[a-z0-9]$"
readonly VALID_SPANNER_DATABASE_PATTERN="^[a-z][a-z0-9_-]{0,29}[a-z0-9]$"
readonly MIN_SPANNER_TIMEOUT=5
readonly MAX_SPANNER_TIMEOUT=300
readonly MIN_SPANNER_SESSIONS=1
readonly MAX_SPANNER_SESSIONS=10000

# Required database tables
readonly REQUIRED_TABLES=(
    "analyses"
    "file_analyses"
    "patterns"
    "repositories"
    "security_findings"
)

# Minimum required indexes
readonly MIN_REQUIRED_INDEXES=5

# ============================================================================
# PERFORMANCE VALIDATION RULES
# ============================================================================

# Response time requirements
readonly MAX_HEALTH_CHECK_RESPONSE_TIME_MS=200
readonly MAX_API_RESPONSE_TIME_MS=500
readonly MAX_ANALYSIS_RESPONSE_TIME_MS=30000  # 30 seconds

# Throughput requirements
readonly MIN_REQUESTS_PER_SECOND=100
readonly MIN_CONCURRENT_CONNECTIONS=50

# Resource utilization limits
readonly MAX_CPU_UTILIZATION_PERCENT=80
readonly MAX_MEMORY_UTILIZATION_PERCENT=85
readonly MIN_DISK_SPACE_MB=1024
readonly MIN_AVAILABLE_MEMORY_MB=512

# ============================================================================
# SECURITY VALIDATION RULES
# ============================================================================

# JWT token validation rules
readonly MIN_JWT_SECRET_LENGTH=32
readonly MAX_JWT_TOKEN_AGE_HOURS=24
readonly MIN_JWT_ROTATION_DAYS=1
readonly MAX_JWT_ROTATION_DAYS=30

# Rate limiting rules
readonly MIN_RATE_LIMIT_PER_HOUR=100
readonly MAX_RATE_LIMIT_PER_HOUR=10000
# DEFAULT_RATE_LIMIT_PER_HOUR is defined in defaults.sh

# Required security headers
readonly REQUIRED_SECURITY_HEADERS=(
    "X-Content-Type-Options"
    "X-Frame-Options"
    "X-XSS-Protection"
    "Strict-Transport-Security"
)

# ============================================================================
# APPLICATION VALIDATION RULES
# ============================================================================

# File size and processing limits
readonly MIN_MAX_FILE_SIZE_BYTES=1048576      # 1MB
readonly MAX_MAX_FILE_SIZE_BYTES=104857600    # 100MB
# DEFAULT_MAX_FILE_SIZE_BYTES is defined in defaults.sh

# Concurrency limits
readonly MIN_CONCURRENT_ANALYSES=1
readonly MAX_CONCURRENT_ANALYSES=1000
readonly MIN_PARSE_TIMEOUT_SECONDS=5
readonly MAX_PARSE_TIMEOUT_SECONDS=300

# Memory limits
readonly MIN_ANALYSIS_MEMORY_MB=256
readonly MAX_ANALYSIS_MEMORY_MB=8192
readonly MIN_DEPENDENCY_COUNT=100
readonly MAX_DEPENDENCY_COUNT=100000

# ============================================================================
# CLOUD RUN VALIDATION RULES
# ============================================================================

# Resource allocation rules
readonly VALID_MEMORY_VALUES=("256Mi" "512Mi" "1Gi" "2Gi" "4Gi" "8Gi")
readonly MIN_CPU_COUNT=1
readonly MAX_CPU_COUNT=8
readonly MIN_TIMEOUT_SECONDS=60
readonly MAX_TIMEOUT_SECONDS=3600

# Scaling rules
readonly MIN_MIN_INSTANCES=0
readonly MAX_MIN_INSTANCES=1000
readonly MIN_MAX_INSTANCES=1
readonly MAX_MAX_INSTANCES=1000
readonly MIN_CONCURRENCY=1
readonly MAX_CONCURRENCY=1000

# ============================================================================
# VALIDATION FUNCTIONS
# ============================================================================

# Validate Google Cloud project ID
validate_project_id() {
    local project_id="$1"
    
    if [[ ! "$project_id" =~ $VALID_PROJECT_ID_PATTERN ]]; then
        log_debug "Invalid project ID format: $project_id"
        return 1
    fi
    
    log_debug "Project ID format valid: $project_id"
    return 0
}

# Validate region
validate_region() {
    local region="$1"
    
    if [[ ! "$region" =~ $VALID_REGION_PATTERN ]]; then
        log_debug "Invalid region format: $region"
        return 1
    fi
    
    log_debug "Region format valid: $region"
    return 0
}

# Validate service name
validate_service_name() {
    local service_name="$1"
    
    if [[ ! "$service_name" =~ $VALID_SERVICE_NAME_PATTERN ]]; then
        log_debug "Invalid service name format: $service_name"
        return 1
    fi
    
    if [[ ${#service_name} -gt $MAX_SERVICE_NAME_LENGTH ]]; then
        log_debug "Service name too long: $service_name (max: $MAX_SERVICE_NAME_LENGTH)"
        return 1
    fi
    
    log_debug "Service name valid: $service_name"
    return 0
}

# Validate image tag
validate_image_tag() {
    local tag="$1"
    
    if [[ ! "$tag" =~ $VALID_IMAGE_TAG_PATTERN ]]; then
        log_debug "Invalid image tag format: $tag"
        return 1
    fi
    
    if [[ ${#tag} -gt $MAX_IMAGE_TAG_LENGTH ]]; then
        log_debug "Image tag too long: $tag (max: $MAX_IMAGE_TAG_LENGTH)"
        return 1
    fi
    
    log_debug "Image tag valid: $tag"
    return 0
}

# Validate Spanner instance ID
validate_spanner_instance() {
    local instance_id="$1"
    
    if [[ ! "$instance_id" =~ $VALID_SPANNER_INSTANCE_PATTERN ]]; then
        log_debug "Invalid Spanner instance ID format: $instance_id"
        return 1
    fi
    
    log_debug "Spanner instance ID valid: $instance_id"
    return 0
}

# Validate Spanner database ID
validate_spanner_database() {
    local database_id="$1"
    
    if [[ ! "$database_id" =~ $VALID_SPANNER_DATABASE_PATTERN ]]; then
        log_debug "Invalid Spanner database ID format: $database_id"
        return 1
    fi
    
    log_debug "Spanner database ID valid: $database_id"
    return 0
}

# Validate numeric range
validate_numeric_range() {
    local value="$1"
    local min_value="$2"
    local max_value="$3"
    local param_name="${4:-value}"
    
    if [[ ! "$value" =~ ^[0-9]+$ ]]; then
        log_debug "Invalid numeric value for $param_name: $value"
        return 1
    fi
    
    if [[ $value -lt $min_value || $value -gt $max_value ]]; then
        log_debug "$param_name out of range: $value (min: $min_value, max: $max_value)"
        return 1
    fi
    
    log_debug "$param_name in valid range: $value"
    return 0
}

# Validate Cloud Run memory value
validate_memory_value() {
    local memory="$1"
    
    for valid_memory in "${VALID_MEMORY_VALUES[@]}"; do
        if [[ "$memory" == "$valid_memory" ]]; then
            log_debug "Memory value valid: $memory"
            return 0
        fi
    done
    
    log_debug "Invalid memory value: $memory (valid: ${VALID_MEMORY_VALUES[*]})"
    return 1
}

# Validate CPU count
validate_cpu_count() {
    local cpu="$1"
    
    if ! validate_numeric_range "$cpu" "$MIN_CPU_COUNT" "$MAX_CPU_COUNT" "CPU count"; then
        return 1
    fi
    
    return 0
}

# Validate timeout value
validate_timeout() {
    local timeout="$1"
    local param_name="${2:-timeout}"
    
    if ! validate_numeric_range "$timeout" "$MIN_TIMEOUT_SECONDS" "$MAX_TIMEOUT_SECONDS" "$param_name"; then
        return 1
    fi
    
    return 0
}

# Validate scaling configuration
validate_scaling_config() {
    local min_instances="$1"
    local max_instances="$2"
    local concurrency="$3"
    
    if ! validate_numeric_range "$min_instances" "$MIN_MIN_INSTANCES" "$MAX_MIN_INSTANCES" "min instances"; then
        return 1
    fi
    
    if ! validate_numeric_range "$max_instances" "$MIN_MAX_INSTANCES" "$MAX_MAX_INSTANCES" "max instances"; then
        return 1
    fi
    
    if ! validate_numeric_range "$concurrency" "$MIN_CONCURRENCY" "$MAX_CONCURRENCY" "concurrency"; then
        return 1
    fi
    
    if [[ $min_instances -gt $max_instances ]]; then
        log_debug "Min instances ($min_instances) cannot be greater than max instances ($max_instances)"
        return 1
    fi
    
    log_debug "Scaling configuration valid"
    return 0
}

# Validate file size limit
validate_file_size_limit() {
    local max_file_size="$1"
    
    if ! validate_numeric_range "$max_file_size" "$MIN_MAX_FILE_SIZE_BYTES" "$MAX_MAX_FILE_SIZE_BYTES" "max file size"; then
        return 1
    fi
    
    return 0
}

# Validate concurrent analyses limit
validate_concurrent_analyses() {
    local max_concurrent="$1"
    
    if ! validate_numeric_range "$max_concurrent" "$MIN_CONCURRENT_ANALYSES" "$MAX_CONCURRENT_ANALYSES" "concurrent analyses"; then
        return 1
    fi
    
    return 0
}

# Validate parse timeout
validate_parse_timeout() {
    local timeout="$1"
    
    if ! validate_numeric_range "$timeout" "$MIN_PARSE_TIMEOUT_SECONDS" "$MAX_PARSE_TIMEOUT_SECONDS" "parse timeout"; then
        return 1
    fi
    
    return 0
}

# Validate analysis memory limit
validate_analysis_memory() {
    local memory_mb="$1"
    
    if ! validate_numeric_range "$memory_mb" "$MIN_ANALYSIS_MEMORY_MB" "$MAX_ANALYSIS_MEMORY_MB" "analysis memory"; then
        return 1
    fi
    
    return 0
}

# Validate dependency count limit
validate_dependency_count() {
    local max_dependencies="$1"
    
    if ! validate_numeric_range "$max_dependencies" "$MIN_DEPENDENCY_COUNT" "$MAX_DEPENDENCY_COUNT" "dependency count"; then
        return 1
    fi
    
    return 0
}

# Validate JWT secret strength
validate_jwt_secret() {
    local jwt_secret="$1"
    
    if [[ ${#jwt_secret} -lt $MIN_JWT_SECRET_LENGTH ]]; then
        log_debug "JWT secret too short: ${#jwt_secret} chars (min: $MIN_JWT_SECRET_LENGTH)"
        return 1
    fi
    
    log_debug "JWT secret length valid: ${#jwt_secret} chars"
    return 0
}

# Validate rate limit configuration
validate_rate_limit() {
    local rate_limit="$1"
    
    if ! validate_numeric_range "$rate_limit" "$MIN_RATE_LIMIT_PER_HOUR" "$MAX_RATE_LIMIT_PER_HOUR" "rate limit"; then
        return 1
    fi
    
    return 0
}

# Validate response time performance
validate_response_time_performance() {
    local response_time_ms="$1"
    local endpoint_type="${2:-api}"
    
    local max_time
    case "$endpoint_type" in
        "health")
            max_time=$MAX_HEALTH_CHECK_RESPONSE_TIME_MS
            ;;
        "api")
            max_time=$MAX_API_RESPONSE_TIME_MS
            ;;
        "analysis")
            max_time=$MAX_ANALYSIS_RESPONSE_TIME_MS
            ;;
        *)
            max_time=$MAX_API_RESPONSE_TIME_MS
            ;;
    esac
    
    if [[ $response_time_ms -gt $max_time ]]; then
        log_debug "Response time too high for $endpoint_type: ${response_time_ms}ms (max: ${max_time}ms)"
        return 1
    fi
    
    log_debug "Response time acceptable for $endpoint_type: ${response_time_ms}ms"
    return 0
}

# ============================================================================
# COMPREHENSIVE VALIDATION FUNCTIONS
# ============================================================================

# Validate complete deployment configuration
validate_deployment_config() {
    local project_id="$1"
    local region="$2"
    local service_name="$3"
    local image_tag="$4"
    local memory="$5"
    local cpu="$6"
    local timeout="$7"
    local min_instances="$8"
    local max_instances="$9"
    local concurrency="${10}"
    
    log_info "Validating deployment configuration"
    
    local validation_errors=()
    
    # Validate basic parameters
    validate_project_id "$project_id" || validation_errors+=("project_id")
    validate_region "$region" || validation_errors+=("region")
    validate_service_name "$service_name" || validation_errors+=("service_name")
    validate_image_tag "$image_tag" || validation_errors+=("image_tag")
    
    # Validate resource configuration
    validate_memory_value "$memory" || validation_errors+=("memory")
    validate_cpu_count "$cpu" || validation_errors+=("cpu")
    validate_timeout "$timeout" "deployment timeout" || validation_errors+=("timeout")
    
    # Validate scaling configuration
    validate_scaling_config "$min_instances" "$max_instances" "$concurrency" || validation_errors+=("scaling")
    
    if [[ ${#validation_errors[@]} -gt 0 ]]; then
        log_error "Deployment configuration validation failed: ${validation_errors[*]}"
        return 1
    fi
    
    log_success "Deployment configuration validation passed"
    return 0
}

# Validate complete application configuration
validate_application_config() {
    local max_file_size="$1"
    local max_concurrent="$2"
    local parse_timeout="$3"
    local analysis_memory="$4"
    local max_dependencies="$5"
    local rate_limit="$6"
    
    log_info "Validating application configuration"
    
    local validation_errors=()
    
    validate_file_size_limit "$max_file_size" || validation_errors+=("max_file_size")
    validate_concurrent_analyses "$max_concurrent" || validation_errors+=("max_concurrent")
    validate_parse_timeout "$parse_timeout" || validation_errors+=("parse_timeout")
    validate_analysis_memory "$analysis_memory" || validation_errors+=("analysis_memory")
    validate_dependency_count "$max_dependencies" || validation_errors+=("max_dependencies")
    validate_rate_limit "$rate_limit" || validation_errors+=("rate_limit")
    
    if [[ ${#validation_errors[@]} -gt 0 ]]; then
        log_error "Application configuration validation failed: ${validation_errors[*]}"
        return 1
    fi
    
    log_success "Application configuration validation passed"
    return 0
}

# Get validation summary
get_validation_rules_summary() {
    cat << EOF
Analysis Engine Validation Rules Summary
========================================

Infrastructure:
- Project ID: Must match pattern $VALID_PROJECT_ID_PATTERN
- Region: Must match pattern $VALID_REGION_PATTERN
- Service Name: Must match pattern $VALID_SERVICE_NAME_PATTERN

Performance:
- Health Check Response: < ${MAX_HEALTH_CHECK_RESPONSE_TIME_MS}ms
- API Response: < ${MAX_API_RESPONSE_TIME_MS}ms
- Analysis Response: < ${MAX_ANALYSIS_RESPONSE_TIME_MS}ms
- Min Requests/Second: $MIN_REQUESTS_PER_SECOND

Security:
- JWT Secret: Min $MIN_JWT_SECRET_LENGTH characters
- Rate Limit: $MIN_RATE_LIMIT_PER_HOUR-$MAX_RATE_LIMIT_PER_HOUR per hour
- Required Headers: ${REQUIRED_SECURITY_HEADERS[*]}

Resources:
- Memory: ${VALID_MEMORY_VALUES[*]}
- CPU: $MIN_CPU_COUNT-$MAX_CPU_COUNT cores
- Timeout: $MIN_TIMEOUT_SECONDS-$MAX_TIMEOUT_SECONDS seconds

Application:
- File Size: $MIN_MAX_FILE_SIZE_BYTES-$MAX_MAX_FILE_SIZE_BYTES bytes
- Concurrent Analyses: $MIN_CONCURRENT_ANALYSES-$MAX_CONCURRENT_ANALYSES
- Parse Timeout: $MIN_PARSE_TIMEOUT_SECONDS-$MAX_PARSE_TIMEOUT_SECONDS seconds
EOF
}