version: '3.8'

services:
  analysis-engine:
    build:
      context: .
      dockerfile: Dockerfile.optimized
      args:
        RUST_VERSION: "1.82.0"
        RUNTIME_TARGET: standard
      platforms:
        - linux/amd64
        - linux/arm64
      cache_from:
        - type=registry,ref=analysis-engine:buildcache
      cache_to:
        - type=registry,ref=analysis-engine:buildcache,mode=max
    image: analysis-engine:latest
    ports:
      - "8001:8001"
    environment:
      - RUST_LOG=info
      - RUST_BACKTRACE=1
      - MEMORY_LIMIT_MB=1800
      - CPU_LIMIT_PERCENT=90
      - MAX_CONCURRENT_ANALYSES=30
      - ENABLE_RESOURCE_MONITORING=true
      - MONITORING_INTERVAL_SECONDS=5
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '1.0'
          memory: 1G
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    restart: unless-stopped
    networks:
      - analysis-network

  # Distroless variant for Cloud Run
  analysis-engine-distroless:
    build:
      context: .
      dockerfile: Dockerfile.optimized
      args:
        RUST_VERSION: "1.82.0"
        RUNTIME_TARGET: distroless
      platforms:
        - linux/amd64
        - linux/arm64
    image: analysis-engine:cloudrun
    profiles:
      - cloudrun
    networks:
      - analysis-network

networks:
  analysis-network:
    driver: bridge

# Build commands:
# 
# Setup buildx for multi-arch:
# docker buildx create --use --name multiarch-builder
# 
# Build multi-arch images:
# docker compose -f docker-compose.multiarch.yml build
# 
# Build and push to registry:
# docker compose -f docker-compose.multiarch.yml build --push
# 
# Build Cloud Run variant:
# docker compose -f docker-compose.multiarch.yml --profile cloudrun build