version: '3.8'

services:
  # Redis cache service (optional dependency)
  redis:
    image: redis:7-alpine
    container_name: analysis-engine-redis
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 3s
      retries: 10
      start_period: 10s
    volumes:
      - redis-data:/data
    networks:
      - analysis-engine-network

  # Google Cloud Spanner emulator for local development
  spanner-emulator:
    image: gcr.io/cloud-spanner-emulator/emulator:latest
    container_name: analysis-engine-spanner
    ports:
      - "9010:9010"  # gRPC port
      - "9020:9020"  # REST port
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9020/"]
      interval: 5s
      timeout: 3s
      retries: 10
      start_period: 10s
    networks:
      - analysis-engine-network

  # Google Cloud Storage emulator (using fake-gcs-server)
  gcs-emulator:
    image: fsouza/fake-gcs-server:latest
    container_name: analysis-engine-gcs
    ports:
      - "4443:4443"
    command: ["-scheme", "http", "-port", "4443", "-external-url", "http://localhost:4443"]
    volumes:
      - gcs-data:/data
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4443/storage/v1/b"]
      interval: 5s
      timeout: 3s
      retries: 10
      start_period: 10s
    networks:
      - analysis-engine-network

  # Google Cloud Pub/Sub emulator
  pubsub-emulator:
    image: gcr.io/google.com/cloudsdktool/cloud-sdk:latest
    container_name: analysis-engine-pubsub
    ports:
      - "8085:8085"
    command: ["gcloud", "beta", "emulators", "pubsub", "start", "--host-port=0.0.0.0:8085"]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8085/"]
      interval: 5s
      timeout: 3s
      retries: 10
      start_period: 20s
    networks:
      - analysis-engine-network

  # Analysis Engine service
  analysis-engine:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        RUNTIME_TARGET: standard
    image: analysis-engine:local
    container_name: analysis-engine
    ports:
      - "8001:8001"
    environment:
      # Service configuration
      - PORT=8001
      - RUST_LOG=info,analysis_engine=debug
      - RUST_BACKTRACE=1
      
      # GCP emulator configuration
      - SPANNER_EMULATOR_HOST=spanner-emulator:9010
      - STORAGE_EMULATOR_HOST=http://gcs-emulator:4443
      - PUBSUB_EMULATOR_HOST=pubsub-emulator:8085
      
      # Service URLs
      - REDIS_URL=redis://redis:6379
      - GCP_PROJECT_ID=test-project
      - SPANNER_INSTANCE_ID=test-instance
      - SPANNER_DATABASE_ID=test-db
      - STORAGE_BUCKET=test-bucket
      - PUBSUB_TOPIC=analysis-updates
      
      # Resource limits
      - MEMORY_LIMIT_MB=1800
      - CPU_LIMIT_PERCENT=90
      - MAX_CONCURRENT_ANALYSES=10
      
      # Startup configuration
      - STARTUP_TIMEOUT=300
      - HEALTH_CHECK_INTERVAL=5
      
      # Development mode flags
      - ENVIRONMENT=development
      - ENABLE_DEBUG_ENDPOINTS=true
    depends_on:
      redis:
        condition: service_healthy
      spanner-emulator:
        condition: service_healthy
      gcs-emulator:
        condition: service_healthy
      pubsub-emulator:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health/ready"]
      interval: 30s
      timeout: 10s
      retries: 10
      start_period: 300s
    volumes:
      - ./logs:/app/logs
      - ./test-data:/app/test-data
    networks:
      - analysis-engine-network
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '1.0'
          memory: 1G
    restart: unless-stopped

  # Monitoring with Prometheus (optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: analysis-engine-prometheus
    ports:
      - "9090:9090"
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    networks:
      - analysis-engine-network
    profiles:
      - monitoring

volumes:
  redis-data:
  gcs-data:
  prometheus-data:

networks:
  analysis-engine-network:
    driver: bridge