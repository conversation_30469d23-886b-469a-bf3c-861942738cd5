📊 Running basic performance validation...
🚀 Starting performance validation for: test-data/repositories/jekyll
📁 Found 436 files to analyze
📊 Processing batch 1/9 (50 files)
⏱️  Progress: 50/436 files (11.5%), 322558 LOC/s
📊 Processing batch 2/9 (50 files)
⏱️  Progress: 100/436 files (22.9%), 189991 LOC/s
📊 Processing batch 3/9 (50 files)
❌ Failed to analyze test-data/repositories/jekyll/test/source/dynamic_file.php: Could not detect language for file: dynamic_file.php
❌ Failed to analyze test-data/repositories/jekyll/test/source/_encodings/Unicode16LECRLFandBOM.md: Failed to read file: stream did not contain valid UTF-8
⏱️  Progress: 150/436 files (34.4%), 106731 LOC/s
📊 Processing batch 4/9 (50 files)
⏱️  Progress: 200/436 files (45.9%), 142315 LOC/s
📊 Processing batch 5/9 (50 files)
⏱️  Progress: 250/436 files (57.3%), 209447 LOC/s
📊 Processing batch 6/9 (50 files)
⏱️  Progress: 300/436 files (68.8%), 218373 LOC/s
📊 Processing batch 7/9 (50 files)
⏱️  Progress: 350/436 files (80.3%), 236507 LOC/s
📊 Processing batch 8/9 (50 files)
⏱️  Progress: 400/436 files (91.7%), 242362 LOC/s
📊 Processing batch 9/9 (36 files)
⏱️  Progress: 436/436 files (100.0%), 240384 LOC/s

🎯 PERFORMANCE VALIDATION RESULTS
=====================================
📁 Total Files: 436
📝 Total Lines: 33252
⏱️  Duration: 0.14 seconds
🚀 Lines/Second: 238163
📊 Files/Second: 3122.8
✅ Success Rate: 99.5%
💾 Memory Usage: 0.0 MB

📈 LANGUAGE-SPECIFIC PERFORMANCE
====================================
📦 RUBY (160 files, 16365 LOC):
  ✅ Success Rate: 100.0%
  🚀 Throughput: 319403 LOC/s
  📊 Avg File Size: 102 LOC
📦 MARKDOWN (163 files, 12270 LOC):
  ✅ Success Rate: 99.4%
  🚀 Throughput: 235072 LOC/s
  📊 Avg File Size: 75 LOC
📦 CSS (19 files, 2304 LOC):
  ✅ Success Rate: 100.0%
  🚀 Throughput: 378680 LOC/s
  📊 Avg File Size: 121 LOC
📦 JAVASCRIPT (5 files, 1079 LOC):
  ✅ Success Rate: 100.0%
  🚀 Throughput: 673899 LOC/s
  📊 Avg File Size: 216 LOC
📦 HTML (82 files, 1013 LOC):
  ✅ Success Rate: 100.0%
  🚀 Throughput: 38578 LOC/s
  📊 Avg File Size: 12 LOC
📦 JSON (4 files, 190 LOC):
  ✅ Success Rate: 100.0%
  🚀 Throughput: 148333 LOC/s
  📊 Avg File Size: 48 LOC
📦 XML (1 files, 29 LOC):
  ✅ Success Rate: 100.0%
  🚀 Throughput: 90561 LOC/s
  📊 Avg File Size: 29 LOC
📦 TOML (1 files, 2 LOC):
  ✅ Success Rate: 100.0%
  🚀 Throughput: 6246 LOC/s
  📊 Avg File Size: 2 LOC
📦 PHP (1 files, 0 LOC):
  ✅ Success Rate: 0.0%
  🚀 Throughput: 0 LOC/s
  📊 Avg File Size: 0 LOC
  ⚠️  Warning: Low success rate for php

🎯 1M LOC PERFORMANCE PROJECTION:
Based on current throughput: 4.20 seconds for 1M LOC
✅ PROJECTION: Can likely achieve 1M LOC in under 5 minutes

📊 Running comprehensive analysis...
🚀 Starting comprehensive repository analysis for: test-data/repositories/jekyll
📁 Found 436 files for comprehensive analysis
📊 Processing comprehensive batch 1/18 (25 files)
⏱️  Comprehensive Progress: 25/436 files (5.7%), 145692 LOC/s, 37457 AST nodes, 33 symbols
📊 Processing comprehensive batch 2/18 (25 files)
⏱️  Comprehensive Progress: 50/436 files (11.5%), 152090 LOC/s, 47917 AST nodes, 46 symbols
📊 Processing comprehensive batch 3/18 (25 files)
⏱️  Comprehensive Progress: 75/436 files (17.2%), 153412 LOC/s, 48156 AST nodes, 57 symbols
📊 Processing comprehensive batch 4/18 (25 files)
⏱️  Comprehensive Progress: 100/436 files (22.9%), 154660 LOC/s, 48284 AST nodes, 82 symbols
📊 Processing comprehensive batch 5/18 (25 files)
❌ Failed comprehensive analysis for test-data/repositories/jekyll/test/source/dynamic_file.php: test-data/repositories/jekyll/test/source/dynamic_file.php: Could not detect language for file: dynamic_file.php
❌ Failed comprehensive analysis for test-data/repositories/jekyll/test/source/_encodings/Unicode16LECRLFandBOM.md: stream did not contain valid UTF-8
⏱️  Comprehensive Progress: 125/436 files (28.7%), 157527 LOC/s, 56427 AST nodes, 97 symbols
📊 Processing comprehensive batch 6/18 (25 files)
⏱️  Comprehensive Progress: 150/436 files (34.4%), 157815 LOC/s, 69154 AST nodes, 110 symbols
📊 Processing comprehensive batch 7/18 (25 files)
⏱️  Comprehensive Progress: 175/436 files (40.1%), 158817 LOC/s, 87648 AST nodes, 121 symbols
📊 Processing comprehensive batch 8/18 (25 files)
⏱️  Comprehensive Progress: 200/436 files (45.9%), 183270 LOC/s, 88940 AST nodes, 818 symbols
📊 Processing comprehensive batch 9/18 (25 files)
⏱️  Comprehensive Progress: 225/436 files (51.6%), 202804 LOC/s, 93018 AST nodes, 1404 symbols
📊 Processing comprehensive batch 10/18 (25 files)
⏱️  Comprehensive Progress: 250/436 files (57.3%), 259730 LOC/s, 93043 AST nodes, 9560 symbols
📊 Processing comprehensive batch 11/18 (25 files)
⏱️  Comprehensive Progress: 275/436 files (63.1%), 274453 LOC/s, 93068 AST nodes, 10451 symbols
📊 Processing comprehensive batch 12/18 (25 files)
⏱️  Comprehensive Progress: 300/436 files (68.8%), 290130 LOC/s, 93951 AST nodes, 10922 symbols
📊 Processing comprehensive batch 13/18 (25 files)
⏱️  Comprehensive Progress: 325/436 files (74.5%), 290853 LOC/s, 113064 AST nodes, 11094 symbols
📊 Processing comprehensive batch 14/18 (25 files)
⏱️  Comprehensive Progress: 350/436 files (80.3%), 281160 LOC/s, 123370 AST nodes, 11171 symbols
📊 Processing comprehensive batch 15/18 (25 files)
⏱️  Comprehensive Progress: 375/436 files (86.0%), 272357 LOC/s, 136862 AST nodes, 11196 symbols
📊 Processing comprehensive batch 16/18 (25 files)
⏱️  Comprehensive Progress: 400/436 files (91.7%), 264031 LOC/s, 153232 AST nodes, 11221 symbols
📊 Processing comprehensive batch 17/18 (25 files)
⏱️  Comprehensive Progress: 425/436 files (97.5%), 249329 LOC/s, 181069 AST nodes, 11366 symbols
📊 Processing comprehensive batch 18/18 (11 files)
⏱️  Comprehensive Progress: 436/436 files (100.0%), 248837 LOC/s, 182396 AST nodes, 11367 symbols

🎯 COMPREHENSIVE ANALYSIS RESULTS
==================================
📁 Files analyzed: 436
📝 Lines processed: 44848
🌳 AST nodes generated: 182396
🔍 Symbols extracted: 11367
🎯 Patterns detected: 4047
⏱️  Duration: 0.18 seconds
🚀 Lines/Second: 248814
✅ Success Rate: 99.5%

🎯 PERFORMANCE VALIDATION RESULTS
=====================================
📁 Total Files: 436
📝 Total Lines: 44848
⏱️  Duration: 0.18 seconds
🚀 Lines/Second: 248814
📊 Files/Second: 2418.9
✅ Success Rate: 99.5%
💾 Memory Usage: 0.0 MB

📈 LANGUAGE-SPECIFIC PERFORMANCE
====================================

🎯 1M LOC PERFORMANCE PROJECTION:
Based on current throughput: 4.02 seconds for 1M LOC
✅ PROJECTION: Can likely achieve 1M LOC in under 5 minutes

📊 Results saved to: performance_results.json
