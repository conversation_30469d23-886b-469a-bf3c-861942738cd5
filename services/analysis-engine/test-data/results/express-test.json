📊 Running basic performance validation...
🚀 Starting performance validation for: test-data/repositories/express
📁 Found 161 files to analyze
📊 Processing batch 1/4 (50 files)
⏱️  Progress: 50/161 files (31.1%), 131230 LOC/s
📊 Processing batch 2/4 (50 files)
⏱️  Progress: 100/161 files (62.1%), 225912 LOC/s
📊 Processing batch 3/4 (50 files)
⏱️  Progress: 150/161 files (93.2%), 229252 LOC/s
📊 Processing batch 4/4 (11 files)
⏱️  Progress: 161/161 files (100.0%), 243076 LOC/s

🎯 PERFORMANCE VALIDATION RESULTS
=====================================
📁 Total Files: 161
📝 Total Lines: 20548
⏱️  Duration: 0.08 seconds
🚀 Lines/Second: 241797
📊 Files/Second: 1894.6
✅ Success Rate: 100.0%
💾 Memory Usage: 0.0 MB

📈 LANGUAGE-SPECIFIC PERFORMANCE
====================================
📦 JAVASCRIPT (142 files, 16851 LOC):
  ✅ Success Rate: 100.0%
  🚀 Throughput: 224825 LOC/s
  📊 Avg File Size: 119 LOC
📦 MARKDOWN (6 files, 3510 LOC):
  ✅ Success Rate: 100.0%
  🚀 Throughput: 1108314 LOC/s
  📊 Avg File Size: 585 LOC
📦 JSON (1 files, 99 LOC):
  ✅ Success Rate: 100.0%
  🚀 Throughput: 187561 LOC/s
  📊 Avg File Size: 99 LOC
📦 HTML (8 files, 44 LOC):
  ✅ Success Rate: 100.0%
  🚀 Throughput: 10420 LOC/s
  📊 Avg File Size: 6 LOC
📦 CSS (4 files, 44 LOC):
  ✅ Success Rate: 100.0%
  🚀 Throughput: 20840 LOC/s
  📊 Avg File Size: 11 LOC

🎯 1M LOC PERFORMANCE PROJECTION:
Based on current throughput: 4.14 seconds for 1M LOC
✅ PROJECTION: Can likely achieve 1M LOC in under 5 minutes

📊 Running comprehensive analysis...
🚀 Starting comprehensive repository analysis for: test-data/repositories/express
📁 Found 161 files for comprehensive analysis
📊 Processing comprehensive batch 1/7 (25 files)
⏱️  Comprehensive Progress: 25/161 files (15.5%), 163344 LOC/s, 66631 AST nodes, 636 symbols
📊 Processing comprehensive batch 2/7 (25 files)
⏱️  Comprehensive Progress: 50/161 files (31.1%), 166694 LOC/s, 95277 AST nodes, 876 symbols
📊 Processing comprehensive batch 3/7 (25 files)
⏱️  Comprehensive Progress: 75/161 files (46.6%), 169698 LOC/s, 120813 AST nodes, 1082 symbols
📊 Processing comprehensive batch 4/7 (25 files)
⏱️  Comprehensive Progress: 100/161 files (62.1%), 209190 LOC/s, 161526 AST nodes, 5420 symbols
📊 Processing comprehensive batch 5/7 (25 files)
⏱️  Comprehensive Progress: 125/161 files (77.6%), 208769 LOC/s, 169530 AST nodes, 5521 symbols
📊 Processing comprehensive batch 6/7 (25 files)
⏱️  Comprehensive Progress: 150/161 files (93.2%), 209124 LOC/s, 177317 AST nodes, 5670 symbols
📊 Processing comprehensive batch 7/7 (11 files)
⏱️  Comprehensive Progress: 161/161 files (100.0%), 217193 LOC/s, 191047 AST nodes, 5956 symbols

🎯 COMPREHENSIVE ANALYSIS RESULTS
==================================
📁 Files analyzed: 161
📝 Lines processed: 25674
🌳 AST nodes generated: 191047
🔍 Symbols extracted: 5956
🎯 Patterns detected: 3868
⏱️  Duration: 0.12 seconds
🚀 Lines/Second: 217184
✅ Success Rate: 100.0%

🎯 PERFORMANCE VALIDATION RESULTS
=====================================
📁 Total Files: 161
📝 Total Lines: 25674
⏱️  Duration: 0.12 seconds
🚀 Lines/Second: 217184
📊 Files/Second: 1361.9
✅ Success Rate: 100.0%
💾 Memory Usage: 0.0 MB

📈 LANGUAGE-SPECIFIC PERFORMANCE
====================================

🎯 1M LOC PERFORMANCE PROJECTION:
Based on current throughput: 4.60 seconds for 1M LOC
✅ PROJECTION: Can likely achieve 1M LOC in under 5 minutes

📊 Results saved to: performance_results.json
