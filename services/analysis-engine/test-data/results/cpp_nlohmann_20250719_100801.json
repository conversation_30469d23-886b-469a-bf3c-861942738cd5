📊 Running basic performance validation...
🚀 Starting performance validation for: test-data/repositories/nlohmann-json
📁 Found 750 files to analyze
📊 Processing batch 1/15 (50 files)
⏱️  Progress: 50/750 files (6.7%), 207574 LOC/s
📊 Processing batch 2/15 (50 files)
⏱️  Progress: 100/750 files (13.3%), 218855 LOC/s
📊 Processing batch 3/15 (50 files)
⏱️  Progress: 150/750 files (20.0%), 206543 LOC/s
📊 Processing batch 4/15 (50 files)
⏱️  Progress: 200/750 files (26.7%), 201072 LOC/s
📊 Processing batch 5/15 (50 files)
⏱️  Progress: 250/750 files (33.3%), 209719 LOC/s
📊 Processing batch 6/15 (50 files)
⏱️  Progress: 300/750 files (40.0%), 210075 LOC/s
📊 Processing batch 7/15 (50 files)
⏱️  Progress: 350/750 files (46.7%), 212981 LOC/s
📊 Processing batch 8/15 (50 files)
⏱️  Progress: 400/750 files (53.3%), 214402 LOC/s
📊 Processing batch 9/15 (50 files)
⏱️  Progress: 450/750 files (60.0%), 215481 LOC/s
📊 Processing batch 10/15 (50 files)
⏱️  Progress: 500/750 files (66.7%), 216618 LOC/s
📊 Processing batch 11/15 (50 files)
⏱️  Progress: 550/750 files (73.3%), 218001 LOC/s
📊 Processing batch 12/15 (50 files)
⏱️  Progress: 600/750 files (80.0%), 221076 LOC/s
📊 Processing batch 13/15 (50 files)
⏱️  Progress: 650/750 files (86.7%), 225834 LOC/s
📊 Processing batch 14/15 (50 files)
⏱️  Progress: 700/750 files (93.3%), 229450 LOC/s
📊 Processing batch 15/15 (50 files)
⏱️  Progress: 750/750 files (100.0%), 216009 LOC/s

🎯 PERFORMANCE VALIDATION RESULTS
=====================================
📁 Total Files: 750
📝 Total Lines: 129160
⏱️  Duration: 0.60 seconds
🚀 Lines/Second: 215748
📊 Files/Second: 1252.8
✅ Success Rate: 100.0%
💾 Memory Usage: 0.0 MB

📈 LANGUAGE-SPECIFIC PERFORMANCE
====================================
📦 CPP (463 files, 104830 LOC):
  ✅ Success Rate: 100.0%
  🚀 Throughput: 283652 LOC/s
  📊 Avg File Size: 226 LOC
📦 MARKDOWN (255 files, 18074 LOC):
  ✅ Success Rate: 100.0%
  🚀 Throughput: 88796 LOC/s
  📊 Avg File Size: 71 LOC
📦 C (18 files, 5150 LOC):
  ✅ Success Rate: 100.0%
  🚀 Throughput: 358439 LOC/s
  📊 Avg File Size: 286 LOC
📦 PYTHON (6 files, 810 LOC):
  ✅ Success Rate: 100.0%
  🚀 Throughput: 169128 LOC/s
  📊 Avg File Size: 135 LOC
📦 SQL (1 files, 241 LOC):
  ✅ Success Rate: 100.0%
  🚀 Throughput: 301924 LOC/s
  📊 Avg File Size: 241 LOC
📦 JSON (3 files, 26 LOC):
  ✅ Success Rate: 100.0%
  🚀 Throughput: 10858 LOC/s
  📊 Avg File Size: 9 LOC
📦 HTML (2 files, 18 LOC):
  ✅ Success Rate: 100.0%
  🚀 Throughput: 11275 LOC/s
  📊 Avg File Size: 9 LOC
📦 BASH (1 files, 8 LOC):
  ✅ Success Rate: 100.0%
  🚀 Throughput: 10022 LOC/s
  📊 Avg File Size: 8 LOC
📦 CSS (1 files, 3 LOC):
  ✅ Success Rate: 100.0%
  🚀 Throughput: 3758 LOC/s
  📊 Avg File Size: 3 LOC

🎯 1M LOC PERFORMANCE PROJECTION:
Based on current throughput: 4.64 seconds for 1M LOC
✅ PROJECTION: Can likely achieve 1M LOC in under 5 minutes

📊 Running comprehensive analysis...
🚀 Starting comprehensive repository analysis for: test-data/repositories/nlohmann-json
📁 Found 750 files for comprehensive analysis
📊 Processing comprehensive batch 1/30 (25 files)
⏱️  Comprehensive Progress: 25/750 files (3.3%), 158562 LOC/s, 70454 AST nodes, 7413 symbols
📊 Processing comprehensive batch 2/30 (25 files)
⏱️  Comprehensive Progress: 50/750 files (6.7%), 147823 LOC/s, 167358 AST nodes, 7855 symbols
📊 Processing comprehensive batch 3/30 (25 files)
⏱️  Comprehensive Progress: 75/750 files (10.0%), 179583 LOC/s, 198919 AST nodes, 8845 symbols
📊 Processing comprehensive batch 4/30 (25 files)
⏱️  Comprehensive Progress: 100/750 files (13.3%), 171814 LOC/s, 234199 AST nodes, 9110 symbols
📊 Processing comprehensive batch 5/30 (25 files)
⏱️  Comprehensive Progress: 125/750 files (16.7%), 171570 LOC/s, 238407 AST nodes, 9142 symbols
📊 Processing comprehensive batch 6/30 (25 files)
⏱️  Comprehensive Progress: 150/750 files (20.0%), 170517 LOC/s, 245050 AST nodes, 9182 symbols
📊 Processing comprehensive batch 7/30 (25 files)
⏱️  Comprehensive Progress: 175/750 files (23.3%), 165855 LOC/s, 268595 AST nodes, 9393 symbols
📊 Processing comprehensive batch 8/30 (25 files)
⏱️  Comprehensive Progress: 200/750 files (26.7%), 149591 LOC/s, 332483 AST nodes, 9797 symbols
📊 Processing comprehensive batch 9/30 (25 files)
⏱️  Comprehensive Progress: 225/750 files (30.0%), 132982 LOC/s, 476027 AST nodes, 9949 symbols
📊 Processing comprehensive batch 10/30 (25 files)
⏱️  Comprehensive Progress: 250/750 files (33.3%), 119863 LOC/s, 668934 AST nodes, 10135 symbols
📊 Processing comprehensive batch 11/30 (25 files)
⏱️  Comprehensive Progress: 275/750 files (36.7%), 116523 LOC/s, 792140 AST nodes, 11885 symbols
📊 Processing comprehensive batch 12/30 (25 files)
⏱️  Comprehensive Progress: 300/750 files (40.0%), 118230 LOC/s, 792993 AST nodes, 12896 symbols
📊 Processing comprehensive batch 13/30 (25 files)
⏱️  Comprehensive Progress: 325/750 files (43.3%), 120890 LOC/s, 793018 AST nodes, 14503 symbols
📊 Processing comprehensive batch 14/30 (25 files)
⏱️  Comprehensive Progress: 350/750 files (46.7%), 121276 LOC/s, 797298 AST nodes, 14645 symbols
📊 Processing comprehensive batch 15/30 (25 files)
⏱️  Comprehensive Progress: 375/750 files (50.0%), 121394 LOC/s, 802795 AST nodes, 14689 symbols
📊 Processing comprehensive batch 16/30 (25 files)
⏱️  Comprehensive Progress: 400/750 files (53.3%), 121526 LOC/s, 809285 AST nodes, 14737 symbols
📊 Processing comprehensive batch 17/30 (25 files)
⏱️  Comprehensive Progress: 425/750 files (56.7%), 121509 LOC/s, 814855 AST nodes, 14773 symbols
📊 Processing comprehensive batch 18/30 (25 files)
⏱️  Comprehensive Progress: 450/750 files (60.0%), 121660 LOC/s, 821139 AST nodes, 14805 symbols
📊 Processing comprehensive batch 19/30 (25 files)
⏱️  Comprehensive Progress: 475/750 files (63.3%), 121770 LOC/s, 825687 AST nodes, 14835 symbols
📊 Processing comprehensive batch 20/30 (25 files)
⏱️  Comprehensive Progress: 500/750 files (66.7%), 121864 LOC/s, 832631 AST nodes, 14869 symbols
📊 Processing comprehensive batch 21/30 (25 files)
⏱️  Comprehensive Progress: 525/750 files (70.0%), 121933 LOC/s, 839084 AST nodes, 14918 symbols
📊 Processing comprehensive batch 22/30 (25 files)
⏱️  Comprehensive Progress: 550/750 files (73.3%), 122081 LOC/s, 845294 AST nodes, 14999 symbols
📊 Processing comprehensive batch 23/30 (25 files)
⏱️  Comprehensive Progress: 575/750 files (76.7%), 123194 LOC/s, 845319 AST nodes, 15546 symbols
📊 Processing comprehensive batch 24/30 (25 files)
⏱️  Comprehensive Progress: 600/750 files (80.0%), 124740 LOC/s, 845344 AST nodes, 16419 symbols
📊 Processing comprehensive batch 25/30 (25 files)
⏱️  Comprehensive Progress: 625/750 files (83.3%), 126462 LOC/s, 845369 AST nodes, 17313 symbols
📊 Processing comprehensive batch 26/30 (25 files)
⏱️  Comprehensive Progress: 650/750 files (86.7%), 128340 LOC/s, 845394 AST nodes, 18556 symbols
📊 Processing comprehensive batch 27/30 (25 files)
⏱️  Comprehensive Progress: 675/750 files (90.0%), 129939 LOC/s, 845419 AST nodes, 19437 symbols
📊 Processing comprehensive batch 28/30 (25 files)
⏱️  Comprehensive Progress: 700/750 files (93.3%), 131128 LOC/s, 845444 AST nodes, 20015 symbols
📊 Processing comprehensive batch 29/30 (25 files)
⏱️  Comprehensive Progress: 725/750 files (96.7%), 132534 LOC/s, 845469 AST nodes, 20737 symbols
📊 Processing comprehensive batch 30/30 (25 files)
⏱️  Comprehensive Progress: 750/750 files (100.0%), 143746 LOC/s, 857530 AST nodes, 23594 symbols

🎯 COMPREHENSIVE ANALYSIS RESULTS
==================================
📁 Files analyzed: 750
📝 Lines processed: 174377
🌳 AST nodes generated: 857530
🔍 Symbols extracted: 23594
🎯 Patterns detected: 21839
⏱️  Duration: 1.21 seconds
🚀 Lines/Second: 143743
✅ Success Rate: 100.0%

🎯 PERFORMANCE VALIDATION RESULTS
=====================================
📁 Total Files: 750
📝 Total Lines: 174377
⏱️  Duration: 1.21 seconds
🚀 Lines/Second: 143743
📊 Files/Second: 618.2
✅ Success Rate: 100.0%
💾 Memory Usage: 0.0 MB

📈 LANGUAGE-SPECIFIC PERFORMANCE
====================================

🎯 1M LOC PERFORMANCE PROJECTION:
Based on current throughput: 6.96 seconds for 1M LOC
✅ PROJECTION: Can likely achieve 1M LOC in under 5 minutes

📊 Results saved to: performance_results.json
