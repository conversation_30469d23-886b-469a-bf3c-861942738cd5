📊 Running basic performance validation...
🚀 Starting performance validation for: test-data/repositories/actix-web
📁 Found 356 files to analyze
📊 Processing batch 1/8 (50 files)
⏱️  Progress: 50/356 files (14.0%), 158487 LOC/s
📊 Processing batch 2/8 (50 files)
⏱️  Progress: 100/356 files (28.1%), 251200 LOC/s
📊 Processing batch 3/8 (50 files)
⏱️  Progress: 150/356 files (42.1%), 289626 LOC/s
📊 Processing batch 4/8 (50 files)
⏱️  Progress: 200/356 files (56.2%), 300787 LOC/s
📊 Processing batch 5/8 (50 files)
⏱️  Progress: 250/356 files (70.2%), 332394 LOC/s
📊 Processing batch 6/8 (50 files)
⏱️  Progress: 300/356 files (84.3%), 361697 LOC/s
📊 Processing batch 7/8 (50 files)
⏱️  Progress: 350/356 files (98.3%), 360185 LOC/s
📊 Processing batch 8/8 (6 files)
⏱️  Progress: 356/356 files (100.0%), 355536 LOC/s

🎯 PERFORMANCE VALIDATION RESULTS
=====================================
📁 Total Files: 356
📝 Total Lines: 59303
⏱️  Duration: 0.17 seconds
🚀 Lines/Second: 354697
📊 Files/Second: 2129.3
✅ Success Rate: 100.0%
💾 Memory Usage: 0.0 MB

📈 LANGUAGE-SPECIFIC PERFORMANCE
====================================
📦 RUST (305 files, 55007 LOC):
  ✅ Success Rate: 100.0%
  🚀 Throughput: 384016 LOC/s
  📊 Avg File Size: 180 LOC
📦 MARKDOWN (34 files, 3383 LOC):
  ✅ Success Rate: 100.0%
  🚀 Throughput: 211862 LOC/s
  📊 Avg File Size: 100 LOC
📦 TOML (15 files, 889 LOC):
  ✅ Success Rate: 100.0%
  🚀 Throughput: 126195 LOC/s
  📊 Avg File Size: 59 LOC
📦 BASH (1 files, 24 LOC):
  ✅ Success Rate: 100.0%
  🚀 Throughput: 51102 LOC/s
  📊 Avg File Size: 24 LOC
📦 JAVASCRIPT (1 files, 0 LOC):
  ✅ Success Rate: 100.0%
  🚀 Throughput: 0 LOC/s
  📊 Avg File Size: 0 LOC

🎯 1M LOC PERFORMANCE PROJECTION:
Based on current throughput: 2.82 seconds for 1M LOC
✅ PROJECTION: Can likely achieve 1M LOC in under 5 minutes

📊 Running comprehensive analysis...
🚀 Starting comprehensive repository analysis for: test-data/repositories/actix-web
📁 Found 356 files for comprehensive analysis
📊 Processing comprehensive batch 1/15 (25 files)
⏱️  Comprehensive Progress: 25/356 files (7.0%), 216075 LOC/s, 36198 AST nodes, 2560 symbols
📊 Processing comprehensive batch 2/15 (25 files)
⏱️  Comprehensive Progress: 50/356 files (14.0%), 198432 LOC/s, 94335 AST nodes, 3383 symbols
📊 Processing comprehensive batch 3/15 (25 files)
⏱️  Comprehensive Progress: 75/356 files (21.1%), 195185 LOC/s, 163011 AST nodes, 4377 symbols
📊 Processing comprehensive batch 4/15 (25 files)
⏱️  Comprehensive Progress: 100/356 files (28.1%), 197243 LOC/s, 213280 AST nodes, 5253 symbols
📊 Processing comprehensive batch 5/15 (25 files)
⏱️  Comprehensive Progress: 125/356 files (35.1%), 197506 LOC/s, 257537 AST nodes, 6667 symbols
📊 Processing comprehensive batch 6/15 (25 files)
⏱️  Comprehensive Progress: 150/356 files (42.1%), 198598 LOC/s, 303215 AST nodes, 7849 symbols
📊 Processing comprehensive batch 7/15 (25 files)
⏱️  Comprehensive Progress: 175/356 files (49.2%), 195374 LOC/s, 367729 AST nodes, 9265 symbols
📊 Processing comprehensive batch 8/15 (25 files)
⏱️  Comprehensive Progress: 200/356 files (56.2%), 197578 LOC/s, 374813 AST nodes, 9938 symbols
📊 Processing comprehensive batch 9/15 (25 files)
⏱️  Comprehensive Progress: 225/356 files (63.2%), 197770 LOC/s, 423485 AST nodes, 11137 symbols
📊 Processing comprehensive batch 10/15 (25 files)
⏱️  Comprehensive Progress: 250/356 files (70.2%), 203207 LOC/s, 469780 AST nodes, 14172 symbols
📊 Processing comprehensive batch 11/15 (25 files)
⏱️  Comprehensive Progress: 275/356 files (77.2%), 201939 LOC/s, 539986 AST nodes, 15289 symbols
📊 Processing comprehensive batch 12/15 (25 files)
⏱️  Comprehensive Progress: 300/356 files (84.3%), 200816 LOC/s, 616587 AST nodes, 16528 symbols
📊 Processing comprehensive batch 13/15 (25 files)
⏱️  Comprehensive Progress: 325/356 files (91.3%), 200644 LOC/s, 656419 AST nodes, 17084 symbols
📊 Processing comprehensive batch 14/15 (25 files)
⏱️  Comprehensive Progress: 350/356 files (98.3%), 200309 LOC/s, 668824 AST nodes, 17407 symbols
📊 Processing comprehensive batch 15/15 (6 files)
⏱️  Comprehensive Progress: 356/356 files (100.0%), 200343 LOC/s, 680461 AST nodes, 17731 symbols

🎯 COMPREHENSIVE ANALYSIS RESULTS
==================================
📁 Files analyzed: 356
📝 Lines processed: 84893
🌳 AST nodes generated: 680461
🔍 Symbols extracted: 17731
🎯 Patterns detected: 35459
⏱️  Duration: 0.42 seconds
🚀 Lines/Second: 200341
✅ Success Rate: 100.0%

🎯 PERFORMANCE VALIDATION RESULTS
=====================================
📁 Total Files: 356
📝 Total Lines: 84893
⏱️  Duration: 0.42 seconds
🚀 Lines/Second: 200341
📊 Files/Second: 840.1
✅ Success Rate: 100.0%
💾 Memory Usage: 0.0 MB

📈 LANGUAGE-SPECIFIC PERFORMANCE
====================================

🎯 1M LOC PERFORMANCE PROJECTION:
Based on current throughput: 4.99 seconds for 1M LOC
✅ PROJECTION: Can likely achieve 1M LOC in under 5 minutes

📊 Results saved to: performance_results.json
