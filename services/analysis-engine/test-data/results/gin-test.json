📊 Running basic performance validation...
🚀 Starting performance validation for: test-data/repositories/gin
📁 Found 105 files to analyze
📊 Processing batch 1/3 (50 files)
⏱️  Progress: 50/105 files (47.6%), 162990 LOC/s
📊 Processing batch 2/3 (50 files)
⏱️  Progress: 100/105 files (95.2%), 207424 LOC/s
📊 Processing batch 3/3 (5 files)
⏱️  Progress: 105/105 files (100.0%), 201431 LOC/s

🎯 PERFORMANCE VALIDATION RESULTS
=====================================
📁 Total Files: 105
📝 Total Lines: 19483
⏱️  Duration: 0.10 seconds
🚀 Lines/Second: 200298
📊 Files/Second: 1079.5
✅ Success Rate: 100.0%
💾 Memory Usage: 0.0 MB

📈 LANGUAGE-SPECIFIC PERFORMANCE
====================================
📦 GO (94 files, 16057 LOC):
  ✅ Success Rate: 100.0%
  🚀 Throughput: 184394 LOC/s
  📊 Avg File Size: 171 LOC
📦 MARKDOWN (11 files, 3426 LOC):
  ✅ Success Rate: 100.0%
  🚀 Throughput: 336206 LOC/s
  📊 Avg File Size: 311 LOC

🎯 1M LOC PERFORMANCE PROJECTION:
Based on current throughput: 4.99 seconds for 1M LOC
✅ PROJECTION: Can likely achieve 1M LOC in under 5 minutes

📊 Running comprehensive analysis...
🚀 Starting comprehensive repository analysis for: test-data/repositories/gin
📁 Found 105 files for comprehensive analysis
📊 Processing comprehensive batch 1/5 (25 files)
⏱️  Comprehensive Progress: 25/105 files (23.8%), 228083 LOC/s, 41949 AST nodes, 1567 symbols
📊 Processing comprehensive batch 2/5 (25 files)
⏱️  Comprehensive Progress: 50/105 files (47.6%), 259126 LOC/s, 68788 AST nodes, 2224 symbols
📊 Processing comprehensive batch 3/5 (25 files)
⏱️  Comprehensive Progress: 75/105 files (71.4%), 232884 LOC/s, 100727 AST nodes, 2579 symbols
📊 Processing comprehensive batch 4/5 (25 files)
⏱️  Comprehensive Progress: 100/105 files (95.2%), 200981 LOC/s, 190502 AST nodes, 3501 symbols
📊 Processing comprehensive batch 5/5 (5 files)
⏱️  Comprehensive Progress: 105/105 files (100.0%), 199117 LOC/s, 208644 AST nodes, 3628 symbols

🎯 COMPREHENSIVE ANALYSIS RESULTS
==================================
📁 Files analyzed: 105
📝 Lines processed: 25700
🌳 AST nodes generated: 208644
🔍 Symbols extracted: 3628
🎯 Patterns detected: 5582
⏱️  Duration: 0.13 seconds
🚀 Lines/Second: 199102
✅ Success Rate: 100.0%

🎯 PERFORMANCE VALIDATION RESULTS
=====================================
📁 Total Files: 105
📝 Total Lines: 25700
⏱️  Duration: 0.13 seconds
🚀 Lines/Second: 199102
📊 Files/Second: 813.5
✅ Success Rate: 100.0%
💾 Memory Usage: 0.0 MB

📈 LANGUAGE-SPECIFIC PERFORMANCE
====================================

🎯 1M LOC PERFORMANCE PROJECTION:
Based on current throughput: 5.02 seconds for 1M LOC
✅ PROJECTION: Can likely achieve 1M LOC in under 5 minutes

📊 Results saved to: performance_results.json
