📊 Running basic performance validation...
🚀 Starting performance validation for: test-data/repositories/cpython
📁 Found 3489 files to analyze
📊 Processing batch 1/70 (50 files)
⏱️  Progress: 50/3489 files (1.4%), 269850 LOC/s
📊 Processing batch 2/70 (50 files)
⏱️  Progress: 100/3489 files (2.9%), 340876 LOC/s
📊 Processing batch 3/70 (50 files)
⏱️  Progress: 150/3489 files (4.3%), 572469 LOC/s
📊 Processing batch 4/70 (50 files)
⏱️  Progress: 200/3489 files (5.7%), 549427 LOC/s
📊 Processing batch 5/70 (50 files)
⏱️  Progress: 250/3489 files (7.2%), 537782 LOC/s
📊 Processing batch 6/70 (50 files)
⏱️  Progress: 300/3489 files (8.6%), 530759 LOC/s
📊 Processing batch 7/70 (50 files)
⏱️  Progress: 350/3489 files (10.0%), 548278 LOC/s
📊 Processing batch 8/70 (50 files)
⏱️  Progress: 400/3489 files (11.5%), 526545 LOC/s
📊 Processing batch 9/70 (50 files)
⏱️  Progress: 450/3489 files (12.9%), 513287 LOC/s
📊 Processing batch 10/70 (50 files)
⏱️  Progress: 500/3489 files (14.3%), 508352 LOC/s
📊 Processing batch 11/70 (50 files)
⏱️  Progress: 550/3489 files (15.8%), 506250 LOC/s
📊 Processing batch 12/70 (50 files)
⏱️  Progress: 600/3489 files (17.2%), 501057 LOC/s
📊 Processing batch 13/70 (50 files)
⏱️  Progress: 650/3489 files (18.6%), 500665 LOC/s
📊 Processing batch 14/70 (50 files)
⏱️  Progress: 700/3489 files (20.1%), 508304 LOC/s
📊 Processing batch 15/70 (50 files)
⏱️  Progress: 750/3489 files (21.5%), 525159 LOC/s
📊 Processing batch 16/70 (50 files)
⏱️  Progress: 800/3489 files (22.9%), 523436 LOC/s
📊 Processing batch 17/70 (50 files)
⏱️  Progress: 850/3489 files (24.4%), 489680 LOC/s
📊 Processing batch 18/70 (50 files)
⏱️  Progress: 900/3489 files (25.8%), 488870 LOC/s
📊 Processing batch 19/70 (50 files)
⏱️  Progress: 950/3489 files (27.2%), 495674 LOC/s
📊 Processing batch 20/70 (50 files)
⏱️  Progress: 1000/3489 files (28.7%), 502036 LOC/s
📊 Processing batch 21/70 (50 files)
⏱️  Progress: 1050/3489 files (30.1%), 502716 LOC/s
📊 Processing batch 22/70 (50 files)
⏱️  Progress: 1100/3489 files (31.5%), 500160 LOC/s
📊 Processing batch 23/70 (50 files)
⏱️  Progress: 1150/3489 files (33.0%), 489474 LOC/s
📊 Processing batch 24/70 (50 files)
❌ Failed to analyze test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/non-scalar-escaped.toml: TOML parse error: TOML parse error at line 1, column 6
  |
1 | a="\ud800"
  |      ^
invalid unicode 4-digit hex code
value is out of range

❌ Failed to analyze test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/invalid-escaped-unicode.toml: TOML parse error: TOML parse error at line 1, column 22
  |
1 | escaped-unicode = "\uabag"
  |                      ^
invalid unicode 4-digit hex code

❌ Failed to analyze test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/unclosed-multiline-string.toml: TOML parse error: TOML parse error at line 4, column 7
  |
4 | eteta
  |       ^
invalid multiline basic string

❌ Failed to analyze test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/array/file-end-after-val.toml: TOML parse error: TOML parse error at line 1, column 5
  |
1 | a=[1
  |     ^
invalid array
expected `]`

❌ Failed to analyze test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/array/unclosed-empty.toml: TOML parse error: TOML parse error at line 1, column 4
  |
1 | v=[
  |    ^
invalid array
expected `]`

❌ Failed to analyze test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/array/unclosed-after-item.toml: TOML parse error: TOML parse error at line 1, column 6
  |
1 | v=[1,
  |      ^
invalid array
expected `]`

❌ Failed to analyze test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/unclosed-string.toml: TOML parse error: TOML parse error at line 1, column 29
  |
1 | "a-string".must-be = "closed
  |                             ^
invalid basic string

❌ Failed to analyze test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/keys-and-vals/no-value.toml: TOML parse error: TOML parse error at line 1, column 14
  |
1 | why-no-value=
  |              ^


❌ Failed to analyze test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/keys-and-vals/only-ws-after-dot.toml: TOML parse error: TOML parse error at line 1, column 3
  |
1 | fs.
  |   ^
expected `.`, `=`

❌ Failed to analyze test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/keys-and-vals/overwrite-with-deep-table.toml: TOML parse error: TOML parse error at line 2, column 1
  |
2 | [a.b.c.d]
  | ^
invalid table header
dotted key `a` attempted to extend non-table type (integer)

❌ Failed to analyze test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/keys-and-vals/ends-early.toml: TOML parse error: TOML parse error at line 1, column 6
  |
1 | fs.fw
  |      ^
expected `.`, `=`

❌ Failed to analyze test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/keys-and-vals/ends-early-table-def.toml: TOML parse error: TOML parse error at line 1, column 11
  |
1 | [fwfw.wafw
  |           ^
invalid table header
expected `.`, `]`

❌ Failed to analyze test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/inline-table/define-twice.toml: TOML parse error: TOML parse error at line 1, column 10
  |
1 | table = { dupe = 1, dupe = 2 }
  |          ^
duplicate key `dupe`

❌ Failed to analyze test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/inline-table/define-twice-in-subtable.toml: TOML parse error: TOML parse error at line 1, column 11
  |
1 | table1 = { table2.dupe = 1, table2.dupe = 2 }
  |           ^
duplicate key `dupe`

❌ Failed to analyze test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/inline-table/overwrite-implicitly.toml: TOML parse error: TOML parse error at line 1, column 6
  |
1 | a = { b = 1, b.c = 2 }
  |      ^
dotted key `b` attempted to extend non-table type (integer)

❌ Failed to analyze test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/inline-table/overwrite-value-in-inner-array.toml: TOML parse error: TOML parse error at line 1, column 8
  |
1 | tab = { inner.table = [{}], inner.table.val = "bad" }
  |        ^
dotted key `inner.table` attempted to extend non-table type (array)

❌ Failed to analyze test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/inline-table/override-val-with-array.toml: TOML parse error: TOML parse error at line 3, column 1
  |
3 | [[inline-t.nest]]
  | ^
invalid table header
dotted key `inline-t` attempted to extend non-table type (inline table)

❌ Failed to analyze test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/inline-table/mutate.toml: TOML parse error: TOML parse error at line 2, column 1
  |
2 | a.b = 2
  | ^
dotted key `a` attempted to extend non-table type (inline table)

❌ Failed to analyze test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/inline-table/unclosed-empty.toml: TOML parse error: TOML parse error at line 1, column 4
  |
1 | a={
  |    ^
invalid inline table
expected `}`

❌ Failed to analyze test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/inline-table/override-val-with-table.toml: TOML parse error: TOML parse error at line 3, column 1
  |
3 | [inline-t.nest]
  | ^
invalid table header
dotted key `inline-t` attempted to extend non-table type (inline table)

❌ Failed to analyze test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/inline-table/overwrite-value-in-inner-table.toml: TOML parse error: TOML parse error at line 1, column 8
  |
1 | tab = { inner = { dog = "best" }, inner.cat = "worst" }
  |        ^
duplicate key `inner`

❌ Failed to analyze test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/inline-table/file-end-after-key-val.toml: TOML parse error: TOML parse error at line 1, column 7
  |
1 | a={b=1
  |       ^
invalid inline table
expected `}`

⏱️  Progress: 1200/3489 files (34.4%), 483647 LOC/s
📊 Processing batch 25/70 (50 files)
❌ Failed to analyze test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/inline-table/override-val-in-table.toml: TOML parse error: TOML parse error at line 5, column 1
  |
5 | nested.inline-t.nest = 2
  | ^
duplicate key `nested`

❌ Failed to analyze test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/multiline-basic-str/escape-only.toml: TOML parse error: TOML parse error at line 1, column 15
  |
1 | bee = """\"""
  |               ^
invalid multiline basic string

❌ Failed to analyze test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/multiline-basic-str/carriage-return.toml: TOML parse error: TOML parse error at line 1, column 38
  |
1 | s="""cr is not an allowed line ending
but we just tried to use it
  |                                      ^
invalid multiline basic string

❌ Failed to analyze test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/multiline-basic-str/unclosed-ends-in-whitespace-escape.toml: TOML parse error: TOML parse error at line 3, column 6
  |
3 | gee\	 
  |      ^
invalid escape sequence
expected `b`, `f`, `n`, `r`, `t`, `u`, `U`, `\`, `"`

❌ Failed to analyze test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/multiline-basic-str/file-ends-after-opening.toml: TOML parse error: TOML parse error at line 1, column 6
  |
1 | a="""
  |      ^
invalid multiline basic string

❌ Failed to analyze test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/multiline-basic-str/last-line-escape.toml: TOML parse error: TOML parse error at line 4, column 7
  |
4 | gee \   """
  |       ^
invalid escape sequence
expected `b`, `f`, `n`, `r`, `t`, `u`, `U`, `\`, `"`

❌ Failed to analyze test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/literal-str/unclosed.toml: TOML parse error: TOML parse error at line 1, column 15
  |
1 | unclosed='dwdd
  |               ^
invalid literal string

❌ Failed to analyze test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/invalid-hex.toml: TOML parse error: TOML parse error at line 1, column 9
  |
1 | hex = 0xgabba00f1
  |         ^
invalid hexadecimal integer

❌ Failed to analyze test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/invalid-comment-char.toml: TOML parse error: TOML parse error at line 1, column 14
  |
1 | # form feed () not allowed in comments
  |              ^


❌ Failed to analyze test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/dotted-keys/extend-defined-table.toml: TOML parse error: TOML parse error at line 4, column 3
  |
4 |   b.c.t = 9
  |   ^
duplicate key `c`

❌ Failed to analyze test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/dotted-keys/extend-defined-table-with-subtable.toml: TOML parse error: TOML parse error at line 4, column 3
  |
4 |   b.c.d.k.t = 8
  |   ^
duplicate key `d`

❌ Failed to analyze test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/dotted-keys/extend-defined-aot.toml: TOML parse error: TOML parse error at line 3, column 1
  |
3 | arr.val1=1
  | ^
duplicate key `val1`

❌ Failed to analyze test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/dotted-keys/access-non-table.toml: TOML parse error: TOML parse error at line 2, column 1
  |
2 | a.b = true
  | ^
dotted key `a` attempted to extend non-table type (boolean)

❌ Failed to analyze test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/array-missing-comma.toml: TOML parse error: TOML parse error at line 1, column 14
  |
1 | arrr = [true false]
  |              ^
invalid array
expected `]`

❌ Failed to analyze test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/table/redefine-2.toml: TOML parse error: TOML parse error at line 3, column 1
  |
3 | [t1.t2.t3]
  | ^
invalid table header
duplicate key `t3` in table `t1.t2`

❌ Failed to analyze test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/table/eof-after-opening.toml: TOML parse error: TOML parse error at line 1, column 1
  |
1 | [
  | ^
invalid table header

❌ Failed to analyze test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/table/redefine-1.toml: TOML parse error: TOML parse error at line 3, column 1
  |
3 | [t1.t2]
  | ^
invalid table header
duplicate key `t2` in table `t1`

❌ Failed to analyze test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/boolean/invalid-false-casing.toml: TOML parse error: TOML parse error at line 1, column 5
  |
1 | val=falsE
  |     ^
invalid string
expected `"`, `'`

❌ Failed to analyze test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/boolean/invalid-true-casing.toml: TOML parse error: TOML parse error at line 1, column 5
  |
1 | val=trUe
  |     ^
invalid string
expected `"`, `'`

❌ Failed to analyze test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/dates-and-times/invalid-day.toml: TOML parse error: TOML parse error at line 1, column 44
  |
1 | "only 28 or 29 days in february" = 1988-02-30
  |                                            ^
invalid date-time
value is out of range

❌ Failed to analyze test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/inline-table-missing-comma.toml: TOML parse error: TOML parse error at line 1, column 31
  |
1 | arrr = { comma-missing = true valid-toml = false }
  |                               ^
invalid inline table
expected `}`

❌ Failed to analyze test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/multiline-literal-str/unclosed.toml: TOML parse error: TOML parse error at line 3, column 5
  |
3 | gee ''
  |     ^
invalid multiline literal string

❌ Failed to analyze test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/multiline-literal-str/file-ends-after-opening.toml: TOML parse error: TOML parse error at line 1, column 6
  |
1 | a='''
  |      ^
invalid multiline literal string

❌ Failed to analyze test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/array-of-tables/overwrite-array-in-parent.toml: TOML parse error: TOML parse error at line 4, column 1
  |
4 | arr = 2
  | ^
duplicate key `arr` in table `parent-table`

❌ Failed to analyze test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/array-of-tables/overwrite-bool-with-aot.toml: TOML parse error: TOML parse error at line 2, column 1
  |
2 | [[a]]
  | ^
invalid table header
duplicate key `a` in document root

❌ Failed to analyze test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/missing-closing-square-bracket.toml: TOML parse error: TOML parse error at line 1, column 25
  |
1 | [closing-bracket.missingö
  |                         ^^
invalid table header
expected `.`, `]`

❌ Failed to analyze test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/basic-str-ends-in-escape.toml: TOML parse error: TOML parse error at line 1, column 28
  |
1 | "backslash is the last char\
  |                            ^
invalid basic string

❌ Failed to analyze test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/missing-closing-double-square-bracket.toml: TOML parse error: TOML parse error at line 1, column 26
  |
1 | [[closing-bracket.missing]
  |                          ^
invalid table header
expected `.`, `]]`

⏱️  Progress: 1250/3489 files (35.8%), 481555 LOC/s
📊 Processing batch 26/70 (50 files)
⏱️  Progress: 1300/3489 files (37.3%), 476465 LOC/s
📊 Processing batch 27/70 (50 files)
⏱️  Progress: 1350/3489 files (38.7%), 472749 LOC/s
📊 Processing batch 28/70 (50 files)
⏱️  Progress: 1400/3489 files (40.1%), 467451 LOC/s
📊 Processing batch 29/70 (50 files)
⏱️  Progress: 1450/3489 files (41.6%), 467075 LOC/s
📊 Processing batch 30/70 (50 files)
⏱️  Progress: 1500/3489 files (43.0%), 444478 LOC/s
📊 Processing batch 31/70 (50 files)
⏱️  Progress: 1550/3489 files (44.4%), 443462 LOC/s
📊 Processing batch 32/70 (50 files)
⏱️  Progress: 1600/3489 files (45.9%), 443617 LOC/s
📊 Processing batch 33/70 (50 files)
⏱️  Progress: 1650/3489 files (47.3%), 442898 LOC/s
📊 Processing batch 34/70 (50 files)
❌ Failed to analyze test-data/repositories/cpython/Lib/test/tokenizedata/badsyntax_pep3120.py: Failed to read file: stream did not contain valid UTF-8
⏱️  Progress: 1700/3489 files (48.7%), 442804 LOC/s
📊 Processing batch 35/70 (50 files)
❌ Failed to analyze test-data/repositories/cpython/Lib/test/xmltestdata/test.xml: Failed to read file: stream did not contain valid UTF-8
⏱️  Progress: 1750/3489 files (50.2%), 434345 LOC/s
📊 Processing batch 36/70 (50 files)
⏱️  Progress: 1800/3489 files (51.6%), 431428 LOC/s
📊 Processing batch 37/70 (50 files)
❌ Failed to analyze test-data/repositories/cpython/Lib/test/encoded_modules/module_koi8_r.py: Failed to read file: stream did not contain valid UTF-8
❌ Failed to analyze test-data/repositories/cpython/Lib/test/encoded_modules/module_iso_8859_1.py: Failed to read file: stream did not contain valid UTF-8
⏱️  Progress: 1850/3489 files (53.0%), 432183 LOC/s
📊 Processing batch 38/70 (50 files)
⏱️  Progress: 1900/3489 files (54.5%), 432998 LOC/s
📊 Processing batch 39/70 (50 files)
⏱️  Progress: 1950/3489 files (55.9%), 431391 LOC/s
📊 Processing batch 40/70 (50 files)
⏱️  Progress: 2000/3489 files (57.3%), 427458 LOC/s
📊 Processing batch 41/70 (50 files)
⏱️  Progress: 2050/3489 files (58.8%), 430845 LOC/s
📊 Processing batch 42/70 (50 files)
⏱️  Progress: 2100/3489 files (60.2%), 427892 LOC/s
📊 Processing batch 43/70 (50 files)
⏱️  Progress: 2150/3489 files (61.6%), 425619 LOC/s
📊 Processing batch 44/70 (50 files)
⏱️  Progress: 2200/3489 files (63.1%), 448404 LOC/s
📊 Processing batch 45/70 (50 files)
⏱️  Progress: 2250/3489 files (64.5%), 447948 LOC/s
📊 Processing batch 46/70 (50 files)
⏱️  Progress: 2300/3489 files (65.9%), 449477 LOC/s
📊 Processing batch 47/70 (50 files)
⏱️  Progress: 2350/3489 files (67.4%), 448812 LOC/s
📊 Processing batch 48/70 (50 files)
⏱️  Progress: 2400/3489 files (68.8%), 446616 LOC/s
📊 Processing batch 49/70 (50 files)
⏱️  Progress: 2450/3489 files (70.2%), 449619 LOC/s
📊 Processing batch 50/70 (50 files)
⏱️  Progress: 2500/3489 files (71.7%), 452744 LOC/s
📊 Processing batch 51/70 (50 files)
⏱️  Progress: 2550/3489 files (73.1%), 453031 LOC/s
📊 Processing batch 52/70 (50 files)
⏱️  Progress: 2600/3489 files (74.5%), 454100 LOC/s
📊 Processing batch 53/70 (50 files)
⏱️  Progress: 2650/3489 files (76.0%), 454943 LOC/s
📊 Processing batch 54/70 (50 files)
⏱️  Progress: 2700/3489 files (77.4%), 455332 LOC/s
📊 Processing batch 55/70 (50 files)
⏱️  Progress: 2750/3489 files (78.8%), 454815 LOC/s
📊 Processing batch 56/70 (50 files)
⏱️  Progress: 2800/3489 files (80.3%), 460736 LOC/s
📊 Processing batch 57/70 (50 files)
⏱️  Progress: 2850/3489 files (81.7%), 460979 LOC/s
📊 Processing batch 58/70 (50 files)
⏱️  Progress: 2900/3489 files (83.1%), 460422 LOC/s
📊 Processing batch 59/70 (50 files)
⏱️  Progress: 2950/3489 files (84.6%), 464318 LOC/s
📊 Processing batch 60/70 (50 files)
⏱️  Progress: 3000/3489 files (86.0%), 461924 LOC/s
📊 Processing batch 61/70 (50 files)
⏱️  Progress: 3050/3489 files (87.4%), 468025 LOC/s
📊 Processing batch 62/70 (50 files)
⏱️  Progress: 3100/3489 files (88.9%), 454363 LOC/s
📊 Processing batch 63/70 (50 files)
⏱️  Progress: 3150/3489 files (90.3%), 455146 LOC/s
📊 Processing batch 64/70 (50 files)
⏱️  Progress: 3200/3489 files (91.7%), 434804 LOC/s
📊 Processing batch 65/70 (50 files)
❌ Failed to analyze test-data/repositories/cpython/Modules/_xxtestfuzz/fuzz_elementtree_parsewhole_corpus/test.xml: Failed to read file: stream did not contain valid UTF-8
⏱️  Progress: 3250/3489 files (93.1%), 428724 LOC/s
📊 Processing batch 66/70 (50 files)
⏱️  Progress: 3300/3489 files (94.6%), 429585 LOC/s
📊 Processing batch 67/70 (50 files)
⏱️  Progress: 3350/3489 files (96.0%), 429287 LOC/s
📊 Processing batch 68/70 (50 files)
⏱️  Progress: 3400/3489 files (97.4%), 431199 LOC/s
📊 Processing batch 69/70 (50 files)
⏱️  Progress: 3450/3489 files (98.9%), 431061 LOC/s
📊 Processing batch 70/70 (39 files)
⏱️  Progress: 3489/3489 files (100.0%), 425901 LOC/s

🎯 PERFORMANCE VALIDATION RESULTS
=====================================
📁 Total Files: 3489
📝 Total Lines: 1626797
⏱️  Duration: 3.82 seconds
🚀 Lines/Second: 425632
📊 Files/Second: 912.9
✅ Success Rate: 98.4%
💾 Memory Usage: 0.0 MB

📈 LANGUAGE-SPECIFIC PERFORMANCE
====================================
📦 PYTHON (2119 files, 804133 LOC):
  ✅ Success Rate: 99.9%
  🚀 Throughput: 346416 LOC/s
  📊 Avg File Size: 379 LOC
📦 C (1085 files, 724421 LOC):
  ✅ Success Rate: 100.0%
  🚀 Throughput: 609485 LOC/s
  📊 Avg File Size: 668 LOC
📦 JSON (30 files, 53156 LOC):
  ✅ Success Rate: 100.0%
  🚀 Throughput: 1617456 LOC/s
  📊 Avg File Size: 1772 LOC
📦 XML (119 files, 31524 LOC):
  ✅ Success Rate: 98.3%
  🚀 Throughput: 241822 LOC/s
  📊 Avg File Size: 265 LOC
📦 MARKDOWN (31 files, 4405 LOC):
  ✅ Success Rate: 100.0%
  🚀 Throughput: 129714 LOC/s
  📊 Avg File Size: 142 LOC
📦 CPP (7 files, 3789 LOC):
  ✅ Success Rate: 100.0%
  🚀 Throughput: 494115 LOC/s
  📊 Avg File Size: 541 LOC
📦 TOML (71 files, 2580 LOC):
  ✅ Success Rate: 29.6%
  🚀 Throughput: 33171 LOC/s
  📊 Avg File Size: 36 LOC
  ⚠️  Warning: Low success rate for toml
📦 HTML (12 files, 2110 LOC):
  ✅ Success Rate: 100.0%
  🚀 Throughput: 160510 LOC/s
  📊 Avg File Size: 176 LOC
📦 BASH (10 files, 446 LOC):
  ✅ Success Rate: 100.0%
  🚀 Throughput: 40713 LOC/s
  📊 Avg File Size: 45 LOC
📦 JAVASCRIPT (3 files, 136 LOC):
  ✅ Success Rate: 100.0%
  🚀 Throughput: 41383 LOC/s
  📊 Avg File Size: 45 LOC
📦 CSS (2 files, 97 LOC):
  ✅ Success Rate: 100.0%
  🚀 Throughput: 44273 LOC/s
  📊 Avg File Size: 48 LOC

🎯 1M LOC PERFORMANCE CLAIM VALIDATION:
Target: 1000000 LOC in 300 seconds
Actual: 1626797 LOC in 3.82 seconds
Projected 1M LOC time: 2.35 seconds
✅ CLAIM VALIDATED: Can process 1M LOC in under 5 minutes!
🚀 Performance margin: 127.7x faster than required

📊 Running comprehensive analysis...
🚀 Starting comprehensive repository analysis for: test-data/repositories/cpython
📁 Found 3489 files for comprehensive analysis
📊 Processing comprehensive batch 1/140 (25 files)
⏱️  Comprehensive Progress: 25/3489 files (0.7%), 189321 LOC/s, 65581 AST nodes, 262 symbols
📊 Processing comprehensive batch 2/140 (25 files)
⏱️  Comprehensive Progress: 50/3489 files (1.4%), 220206 LOC/s, 132820 AST nodes, 541 symbols
📊 Processing comprehensive batch 3/140 (25 files)
⏱️  Comprehensive Progress: 75/3489 files (2.1%), 229848 LOC/s, 174537 AST nodes, 3225 symbols
📊 Processing comprehensive batch 4/140 (25 files)
⏱️  Comprehensive Progress: 100/3489 files (2.9%), 225284 LOC/s, 210662 AST nodes, 3400 symbols
📊 Processing comprehensive batch 5/140 (25 files)
⏱️  Comprehensive Progress: 125/3489 files (3.6%), 218691 LOC/s, 255510 AST nodes, 3660 symbols
📊 Processing comprehensive batch 6/140 (25 files)
⏱️  Comprehensive Progress: 150/3489 files (4.3%), 324755 LOC/s, 318629 AST nodes, 3904 symbols
📊 Processing comprehensive batch 7/140 (25 files)
⏱️  Comprehensive Progress: 175/3489 files (5.0%), 303342 LOC/s, 377142 AST nodes, 4525 symbols
📊 Processing comprehensive batch 8/140 (25 files)
⏱️  Comprehensive Progress: 200/3489 files (5.7%), 287519 LOC/s, 431150 AST nodes, 4910 symbols
📊 Processing comprehensive batch 9/140 (25 files)
⏱️  Comprehensive Progress: 225/3489 files (6.4%), 275614 LOC/s, 484233 AST nodes, 5204 symbols
📊 Processing comprehensive batch 10/140 (25 files)
⏱️  Comprehensive Progress: 250/3489 files (7.2%), 264399 LOC/s, 551128 AST nodes, 5610 symbols
📊 Processing comprehensive batch 11/140 (25 files)
⏱️  Comprehensive Progress: 275/3489 files (7.9%), 255604 LOC/s, 636315 AST nodes, 6123 symbols
📊 Processing comprehensive batch 12/140 (25 files)
⏱️  Comprehensive Progress: 300/3489 files (8.6%), 228363 LOC/s, 916373 AST nodes, 7077 symbols
📊 Processing comprehensive batch 13/140 (25 files)
⏱️  Comprehensive Progress: 325/3489 files (9.3%), 215776 LOC/s, 1354937 AST nodes, 8159 symbols
📊 Processing comprehensive batch 14/140 (25 files)
⏱️  Comprehensive Progress: 350/3489 files (10.0%), 213127 LOC/s, 1626929 AST nodes, 8994 symbols
📊 Processing comprehensive batch 15/140 (25 files)
⏱️  Comprehensive Progress: 375/3489 files (10.7%), 213625 LOC/s, 1700382 AST nodes, 9363 symbols
📊 Processing comprehensive batch 16/140 (25 files)
⏱️  Comprehensive Progress: 400/3489 files (11.5%), 213968 LOC/s, 1797252 AST nodes, 9776 symbols
📊 Processing comprehensive batch 17/140 (25 files)
⏱️  Comprehensive Progress: 425/3489 files (12.2%), 214345 LOC/s, 1806395 AST nodes, 9779 symbols
📊 Processing comprehensive batch 18/140 (25 files)
⏱️  Comprehensive Progress: 450/3489 files (12.9%), 214186 LOC/s, 1840273 AST nodes, 9917 symbols
📊 Processing comprehensive batch 19/140 (25 files)
⏱️  Comprehensive Progress: 475/3489 files (13.6%), 214821 LOC/s, 1851997 AST nodes, 10117 symbols
📊 Processing comprehensive batch 20/140 (25 files)
⏱️  Comprehensive Progress: 500/3489 files (14.3%), 214499 LOC/s, 1890743 AST nodes, 10784 symbols
📊 Processing comprehensive batch 21/140 (25 files)
⏱️  Comprehensive Progress: 525/3489 files (15.0%), 214987 LOC/s, 1908418 AST nodes, 11118 symbols
📊 Processing comprehensive batch 22/140 (25 files)
⏱️  Comprehensive Progress: 550/3489 files (15.8%), 215685 LOC/s, 1928228 AST nodes, 11457 symbols
📊 Processing comprehensive batch 23/140 (25 files)
⏱️  Comprehensive Progress: 575/3489 files (16.5%), 216360 LOC/s, 1970074 AST nodes, 12068 symbols
📊 Processing comprehensive batch 24/140 (25 files)
⏱️  Comprehensive Progress: 600/3489 files (17.2%), 216723 LOC/s, 1977894 AST nodes, 12252 symbols
📊 Processing comprehensive batch 25/140 (25 files)
⏱️  Comprehensive Progress: 625/3489 files (17.9%), 217003 LOC/s, 1993802 AST nodes, 12423 symbols
📊 Processing comprehensive batch 26/140 (25 files)
⏱️  Comprehensive Progress: 650/3489 files (18.6%), 217226 LOC/s, 2025410 AST nodes, 12780 symbols
📊 Processing comprehensive batch 27/140 (25 files)
⏱️  Comprehensive Progress: 675/3489 files (19.3%), 218232 LOC/s, 2045147 AST nodes, 12877 symbols
📊 Processing comprehensive batch 28/140 (25 files)
⏱️  Comprehensive Progress: 700/3489 files (20.1%), 221102 LOC/s, 2091420 AST nodes, 14716 symbols
📊 Processing comprehensive batch 29/140 (25 files)
⏱️  Comprehensive Progress: 725/3489 files (20.8%), 219287 LOC/s, 2158521 AST nodes, 15011 symbols
📊 Processing comprehensive batch 30/140 (25 files)
⏱️  Comprehensive Progress: 750/3489 files (21.5%), 217002 LOC/s, 2592408 AST nodes, 17113 symbols
📊 Processing comprehensive batch 31/140 (25 files)
⏱️  Comprehensive Progress: 775/3489 files (22.2%), 215462 LOC/s, 2737906 AST nodes, 17813 symbols
📊 Processing comprehensive batch 32/140 (25 files)
⏱️  Comprehensive Progress: 800/3489 files (22.9%), 215539 LOC/s, 2933413 AST nodes, 18872 symbols
📊 Processing comprehensive batch 33/140 (25 files)
⏱️  Comprehensive Progress: 825/3489 files (23.6%), 215285 LOC/s, 3010355 AST nodes, 19341 symbols
📊 Processing comprehensive batch 34/140 (25 files)
⏱️  Comprehensive Progress: 850/3489 files (24.4%), 219291 LOC/s, 3088239 AST nodes, 20056 symbols
📊 Processing comprehensive batch 35/140 (25 files)
⏱️  Comprehensive Progress: 875/3489 files (25.1%), 219027 LOC/s, 3130655 AST nodes, 20262 symbols
📊 Processing comprehensive batch 36/140 (25 files)
⏱️  Comprehensive Progress: 900/3489 files (25.8%), 219012 LOC/s, 3176558 AST nodes, 20613 symbols
📊 Processing comprehensive batch 37/140 (25 files)
⏱️  Comprehensive Progress: 925/3489 files (26.5%), 219553 LOC/s, 3215549 AST nodes, 20839 symbols
📊 Processing comprehensive batch 38/140 (25 files)
⏱️  Comprehensive Progress: 950/3489 files (27.2%), 220034 LOC/s, 3258781 AST nodes, 21057 symbols
📊 Processing comprehensive batch 39/140 (25 files)
⏱️  Comprehensive Progress: 975/3489 files (27.9%), 220513 LOC/s, 3300220 AST nodes, 21270 symbols
📊 Processing comprehensive batch 40/140 (25 files)
⏱️  Comprehensive Progress: 1000/3489 files (28.7%), 220887 LOC/s, 3334639 AST nodes, 21519 symbols
📊 Processing comprehensive batch 41/140 (25 files)
⏱️  Comprehensive Progress: 1025/3489 files (29.4%), 220882 LOC/s, 3391046 AST nodes, 21922 symbols
📊 Processing comprehensive batch 42/140 (25 files)
⏱️  Comprehensive Progress: 1050/3489 files (30.1%), 220158 LOC/s, 3482486 AST nodes, 22377 symbols
📊 Processing comprehensive batch 43/140 (25 files)
⏱️  Comprehensive Progress: 1075/3489 files (30.8%), 219549 LOC/s, 3543123 AST nodes, 22701 symbols
📊 Processing comprehensive batch 44/140 (25 files)
⏱️  Comprehensive Progress: 1100/3489 files (31.5%), 216744 LOC/s, 3709502 AST nodes, 23759 symbols
📊 Processing comprehensive batch 45/140 (25 files)
⏱️  Comprehensive Progress: 1125/3489 files (32.2%), 212811 LOC/s, 3897876 AST nodes, 25397 symbols
📊 Processing comprehensive batch 46/140 (25 files)
⏱️  Comprehensive Progress: 1150/3489 files (33.0%), 210891 LOC/s, 4008770 AST nodes, 26696 symbols
📊 Processing comprehensive batch 47/140 (25 files)
⏱️  Comprehensive Progress: 1175/3489 files (33.7%), 210827 LOC/s, 4012156 AST nodes, 26731 symbols
📊 Processing comprehensive batch 48/140 (25 files)
❌ Failed comprehensive analysis for test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/non-scalar-escaped.toml: : TOML parse error: TOML parse error at line 1, column 6
  |
1 | a="\ud800"
  |      ^
invalid unicode 4-digit hex code
value is out of range

❌ Failed comprehensive analysis for test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/invalid-escaped-unicode.toml: : TOML parse error: TOML parse error at line 1, column 22
  |
1 | escaped-unicode = "\uabag"
  |                      ^
invalid unicode 4-digit hex code

❌ Failed comprehensive analysis for test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/unclosed-multiline-string.toml: : TOML parse error: TOML parse error at line 4, column 7
  |
4 | eteta
  |       ^
invalid multiline basic string

❌ Failed comprehensive analysis for test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/array/file-end-after-val.toml: : TOML parse error: TOML parse error at line 1, column 5
  |
1 | a=[1
  |     ^
invalid array
expected `]`

❌ Failed comprehensive analysis for test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/array/unclosed-empty.toml: : TOML parse error: TOML parse error at line 1, column 4
  |
1 | v=[
  |    ^
invalid array
expected `]`

❌ Failed comprehensive analysis for test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/array/unclosed-after-item.toml: : TOML parse error: TOML parse error at line 1, column 6
  |
1 | v=[1,
  |      ^
invalid array
expected `]`

❌ Failed comprehensive analysis for test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/unclosed-string.toml: : TOML parse error: TOML parse error at line 1, column 29
  |
1 | "a-string".must-be = "closed
  |                             ^
invalid basic string

❌ Failed comprehensive analysis for test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/keys-and-vals/no-value.toml: : TOML parse error: TOML parse error at line 1, column 14
  |
1 | why-no-value=
  |              ^


❌ Failed comprehensive analysis for test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/keys-and-vals/only-ws-after-dot.toml: : TOML parse error: TOML parse error at line 1, column 3
  |
1 | fs.
  |   ^
expected `.`, `=`

❌ Failed comprehensive analysis for test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/keys-and-vals/overwrite-with-deep-table.toml: : TOML parse error: TOML parse error at line 2, column 1
  |
2 | [a.b.c.d]
  | ^
invalid table header
dotted key `a` attempted to extend non-table type (integer)

❌ Failed comprehensive analysis for test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/keys-and-vals/ends-early.toml: : TOML parse error: TOML parse error at line 1, column 6
  |
1 | fs.fw
  |      ^
expected `.`, `=`

❌ Failed comprehensive analysis for test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/keys-and-vals/ends-early-table-def.toml: : TOML parse error: TOML parse error at line 1, column 11
  |
1 | [fwfw.wafw
  |           ^
invalid table header
expected `.`, `]`

❌ Failed comprehensive analysis for test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/inline-table/define-twice.toml: : TOML parse error: TOML parse error at line 1, column 10
  |
1 | table = { dupe = 1, dupe = 2 }
  |          ^
duplicate key `dupe`

❌ Failed comprehensive analysis for test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/inline-table/define-twice-in-subtable.toml: : TOML parse error: TOML parse error at line 1, column 11
  |
1 | table1 = { table2.dupe = 1, table2.dupe = 2 }
  |           ^
duplicate key `dupe`

❌ Failed comprehensive analysis for test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/inline-table/overwrite-implicitly.toml: : TOML parse error: TOML parse error at line 1, column 6
  |
1 | a = { b = 1, b.c = 2 }
  |      ^
dotted key `b` attempted to extend non-table type (integer)

❌ Failed comprehensive analysis for test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/inline-table/overwrite-value-in-inner-array.toml: : TOML parse error: TOML parse error at line 1, column 8
  |
1 | tab = { inner.table = [{}], inner.table.val = "bad" }
  |        ^
dotted key `inner.table` attempted to extend non-table type (array)

❌ Failed comprehensive analysis for test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/inline-table/override-val-with-array.toml: : TOML parse error: TOML parse error at line 3, column 1
  |
3 | [[inline-t.nest]]
  | ^
invalid table header
dotted key `inline-t` attempted to extend non-table type (inline table)

❌ Failed comprehensive analysis for test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/inline-table/mutate.toml: : TOML parse error: TOML parse error at line 2, column 1
  |
2 | a.b = 2
  | ^
dotted key `a` attempted to extend non-table type (inline table)

❌ Failed comprehensive analysis for test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/inline-table/unclosed-empty.toml: : TOML parse error: TOML parse error at line 1, column 4
  |
1 | a={
  |    ^
invalid inline table
expected `}`

❌ Failed comprehensive analysis for test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/inline-table/override-val-with-table.toml: : TOML parse error: TOML parse error at line 3, column 1
  |
3 | [inline-t.nest]
  | ^
invalid table header
dotted key `inline-t` attempted to extend non-table type (inline table)

❌ Failed comprehensive analysis for test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/inline-table/overwrite-value-in-inner-table.toml: : TOML parse error: TOML parse error at line 1, column 8
  |
1 | tab = { inner = { dog = "best" }, inner.cat = "worst" }
  |        ^
duplicate key `inner`

❌ Failed comprehensive analysis for test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/inline-table/file-end-after-key-val.toml: : TOML parse error: TOML parse error at line 1, column 7
  |
1 | a={b=1
  |       ^
invalid inline table
expected `}`

⏱️  Comprehensive Progress: 1200/3489 files (34.4%), 210780 LOC/s, 4012194 AST nodes, 26732 symbols
📊 Processing comprehensive batch 49/140 (25 files)
❌ Failed comprehensive analysis for test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/inline-table/override-val-in-table.toml: : TOML parse error: TOML parse error at line 5, column 1
  |
5 | nested.inline-t.nest = 2
  | ^
duplicate key `nested`

❌ Failed comprehensive analysis for test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/multiline-basic-str/escape-only.toml: : TOML parse error: TOML parse error at line 1, column 15
  |
1 | bee = """\"""
  |               ^
invalid multiline basic string

❌ Failed comprehensive analysis for test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/multiline-basic-str/carriage-return.toml: : TOML parse error: TOML parse error at line 1, column 38
  |
1 | s="""cr is not an allowed line ending
but we just tried to use it
  |                                      ^
invalid multiline basic string

❌ Failed comprehensive analysis for test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/multiline-basic-str/unclosed-ends-in-whitespace-escape.toml: : TOML parse error: TOML parse error at line 3, column 6
  |
3 | gee\	 
  |      ^
invalid escape sequence
expected `b`, `f`, `n`, `r`, `t`, `u`, `U`, `\`, `"`

❌ Failed comprehensive analysis for test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/multiline-basic-str/file-ends-after-opening.toml: : TOML parse error: TOML parse error at line 1, column 6
  |
1 | a="""
  |      ^
invalid multiline basic string

❌ Failed comprehensive analysis for test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/multiline-basic-str/last-line-escape.toml: : TOML parse error: TOML parse error at line 4, column 7
  |
4 | gee \   """
  |       ^
invalid escape sequence
expected `b`, `f`, `n`, `r`, `t`, `u`, `U`, `\`, `"`

❌ Failed comprehensive analysis for test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/literal-str/unclosed.toml: : TOML parse error: TOML parse error at line 1, column 15
  |
1 | unclosed='dwdd
  |               ^
invalid literal string

❌ Failed comprehensive analysis for test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/invalid-hex.toml: : TOML parse error: TOML parse error at line 1, column 9
  |
1 | hex = 0xgabba00f1
  |         ^
invalid hexadecimal integer

❌ Failed comprehensive analysis for test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/invalid-comment-char.toml: : TOML parse error: TOML parse error at line 1, column 14
  |
1 | # form feed () not allowed in comments
  |              ^


❌ Failed comprehensive analysis for test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/dotted-keys/extend-defined-table.toml: : TOML parse error: TOML parse error at line 4, column 3
  |
4 |   b.c.t = 9
  |   ^
duplicate key `c`

❌ Failed comprehensive analysis for test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/dotted-keys/extend-defined-table-with-subtable.toml: : TOML parse error: TOML parse error at line 4, column 3
  |
4 |   b.c.d.k.t = 8
  |   ^
duplicate key `d`

❌ Failed comprehensive analysis for test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/dotted-keys/extend-defined-aot.toml: : TOML parse error: TOML parse error at line 3, column 1
  |
3 | arr.val1=1
  | ^
duplicate key `val1`

❌ Failed comprehensive analysis for test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/dotted-keys/access-non-table.toml: : TOML parse error: TOML parse error at line 2, column 1
  |
2 | a.b = true
  | ^
dotted key `a` attempted to extend non-table type (boolean)

❌ Failed comprehensive analysis for test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/array-missing-comma.toml: : TOML parse error: TOML parse error at line 1, column 14
  |
1 | arrr = [true false]
  |              ^
invalid array
expected `]`

❌ Failed comprehensive analysis for test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/table/redefine-2.toml: : TOML parse error: TOML parse error at line 3, column 1
  |
3 | [t1.t2.t3]
  | ^
invalid table header
duplicate key `t3` in table `t1.t2`

❌ Failed comprehensive analysis for test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/table/eof-after-opening.toml: : TOML parse error: TOML parse error at line 1, column 1
  |
1 | [
  | ^
invalid table header

❌ Failed comprehensive analysis for test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/table/redefine-1.toml: : TOML parse error: TOML parse error at line 3, column 1
  |
3 | [t1.t2]
  | ^
invalid table header
duplicate key `t2` in table `t1`

❌ Failed comprehensive analysis for test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/boolean/invalid-false-casing.toml: : TOML parse error: TOML parse error at line 1, column 5
  |
1 | val=falsE
  |     ^
invalid string
expected `"`, `'`

❌ Failed comprehensive analysis for test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/boolean/invalid-true-casing.toml: : TOML parse error: TOML parse error at line 1, column 5
  |
1 | val=trUe
  |     ^
invalid string
expected `"`, `'`

❌ Failed comprehensive analysis for test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/dates-and-times/invalid-day.toml: : TOML parse error: TOML parse error at line 1, column 44
  |
1 | "only 28 or 29 days in february" = 1988-02-30
  |                                            ^
invalid date-time
value is out of range

❌ Failed comprehensive analysis for test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/inline-table-missing-comma.toml: : TOML parse error: TOML parse error at line 1, column 31
  |
1 | arrr = { comma-missing = true valid-toml = false }
  |                               ^
invalid inline table
expected `}`

❌ Failed comprehensive analysis for test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/multiline-literal-str/unclosed.toml: : TOML parse error: TOML parse error at line 3, column 5
  |
3 | gee ''
  |     ^
invalid multiline literal string

❌ Failed comprehensive analysis for test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/multiline-literal-str/file-ends-after-opening.toml: : TOML parse error: TOML parse error at line 1, column 6
  |
1 | a='''
  |      ^
invalid multiline literal string

❌ Failed comprehensive analysis for test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/array-of-tables/overwrite-array-in-parent.toml: : TOML parse error: TOML parse error at line 4, column 1
  |
4 | arr = 2
  | ^
duplicate key `arr` in table `parent-table`

❌ Failed comprehensive analysis for test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/array-of-tables/overwrite-bool-with-aot.toml: : TOML parse error: TOML parse error at line 2, column 1
  |
2 | [[a]]
  | ^
invalid table header
duplicate key `a` in document root

⏱️  Comprehensive Progress: 1225/3489 files (35.1%), 210660 LOC/s, 4012194 AST nodes, 26732 symbols
📊 Processing comprehensive batch 50/140 (25 files)
❌ Failed comprehensive analysis for test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/missing-closing-square-bracket.toml: : TOML parse error: TOML parse error at line 1, column 25
  |
1 | [closing-bracket.missingö
  |                         ^^
invalid table header
expected `.`, `]`

❌ Failed comprehensive analysis for test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/basic-str-ends-in-escape.toml: : TOML parse error: TOML parse error at line 1, column 28
  |
1 | "backslash is the last char\
  |                            ^
invalid basic string

❌ Failed comprehensive analysis for test-data/repositories/cpython/Lib/test/test_tomllib/data/invalid/missing-closing-double-square-bracket.toml: : TOML parse error: TOML parse error at line 1, column 26
  |
1 | [[closing-bracket.missing]
  |                          ^
invalid table header
expected `.`, `]]`

⏱️  Comprehensive Progress: 1250/3489 files (35.8%), 208553 LOC/s, 4147558 AST nodes, 27893 symbols
📊 Processing comprehensive batch 51/140 (25 files)
⏱️  Comprehensive Progress: 1275/3489 files (36.5%), 207890 LOC/s, 4202476 AST nodes, 28565 symbols
📊 Processing comprehensive batch 52/140 (25 files)
⏱️  Comprehensive Progress: 1300/3489 files (37.3%), 206069 LOC/s, 4347978 AST nodes, 29743 symbols
📊 Processing comprehensive batch 53/140 (25 files)
⏱️  Comprehensive Progress: 1325/3489 files (38.0%), 202784 LOC/s, 4605561 AST nodes, 31823 symbols
📊 Processing comprehensive batch 54/140 (25 files)
⏱️  Comprehensive Progress: 1350/3489 files (38.7%), 200943 LOC/s, 4757811 AST nodes, 32948 symbols
📊 Processing comprehensive batch 55/140 (25 files)
⏱️  Comprehensive Progress: 1375/3489 files (39.4%), 200741 LOC/s, 4814281 AST nodes, 33553 symbols
📊 Processing comprehensive batch 56/140 (25 files)
⏱️  Comprehensive Progress: 1400/3489 files (40.1%), 200901 LOC/s, 4934070 AST nodes, 34621 symbols
📊 Processing comprehensive batch 57/140 (25 files)
⏱️  Comprehensive Progress: 1425/3489 files (40.8%), 199417 LOC/s, 5047259 AST nodes, 35400 symbols
📊 Processing comprehensive batch 58/140 (25 files)
⏱️  Comprehensive Progress: 1450/3489 files (41.6%), 195426 LOC/s, 5375001 AST nodes, 37489 symbols
📊 Processing comprehensive batch 59/140 (25 files)
⏱️  Comprehensive Progress: 1475/3489 files (42.3%), 194840 LOC/s, 5421551 AST nodes, 38008 symbols
📊 Processing comprehensive batch 60/140 (25 files)
⏱️  Comprehensive Progress: 1500/3489 files (43.0%), 193254 LOC/s, 5559678 AST nodes, 40031 symbols
📊 Processing comprehensive batch 61/140 (25 files)
⏱️  Comprehensive Progress: 1525/3489 files (43.7%), 192914 LOC/s, 5601549 AST nodes, 40519 symbols
📊 Processing comprehensive batch 62/140 (25 files)
⏱️  Comprehensive Progress: 1550/3489 files (44.4%), 192655 LOC/s, 5632855 AST nodes, 40900 symbols
📊 Processing comprehensive batch 63/140 (25 files)
⏱️  Comprehensive Progress: 1575/3489 files (45.1%), 192579 LOC/s, 5651559 AST nodes, 41173 symbols
📊 Processing comprehensive batch 64/140 (25 files)
⏱️  Comprehensive Progress: 1600/3489 files (45.9%), 192073 LOC/s, 5708760 AST nodes, 41772 symbols
📊 Processing comprehensive batch 65/140 (25 files)
⏱️  Comprehensive Progress: 1625/3489 files (46.6%), 191693 LOC/s, 5751764 AST nodes, 42253 symbols
📊 Processing comprehensive batch 66/140 (25 files)
⏱️  Comprehensive Progress: 1650/3489 files (47.3%), 191450 LOC/s, 5817389 AST nodes, 42935 symbols
📊 Processing comprehensive batch 67/140 (25 files)
⏱️  Comprehensive Progress: 1675/3489 files (48.0%), 189158 LOC/s, 6135871 AST nodes, 45419 symbols
📊 Processing comprehensive batch 68/140 (25 files)
❌ Failed comprehensive analysis for test-data/repositories/cpython/Lib/test/tokenizedata/badsyntax_pep3120.py: stream did not contain valid UTF-8
⏱️  Comprehensive Progress: 1700/3489 files (48.7%), 189505 LOC/s, 6199603 AST nodes, 46112 symbols
📊 Processing comprehensive batch 69/140 (25 files)
⏱️  Comprehensive Progress: 1725/3489 files (49.4%), 188878 LOC/s, 6294809 AST nodes, 46890 symbols
📊 Processing comprehensive batch 70/140 (25 files)
❌ Failed comprehensive analysis for test-data/repositories/cpython/Lib/test/xmltestdata/test.xml: stream did not contain valid UTF-8
⏱️  Comprehensive Progress: 1750/3489 files (50.2%), 188792 LOC/s, 6308574 AST nodes, 47125 symbols
📊 Processing comprehensive batch 71/140 (25 files)
⏱️  Comprehensive Progress: 1775/3489 files (50.9%), 188810 LOC/s, 6308599 AST nodes, 47309 symbols
📊 Processing comprehensive batch 72/140 (25 files)
⏱️  Comprehensive Progress: 1800/3489 files (51.6%), 188515 LOC/s, 6461675 AST nodes, 48665 symbols
📊 Processing comprehensive batch 73/140 (25 files)
❌ Failed comprehensive analysis for test-data/repositories/cpython/Lib/test/encoded_modules/module_koi8_r.py: stream did not contain valid UTF-8
❌ Failed comprehensive analysis for test-data/repositories/cpython/Lib/test/encoded_modules/module_iso_8859_1.py: stream did not contain valid UTF-8
⏱️  Comprehensive Progress: 1825/3489 files (52.3%), 187444 LOC/s, 6626022 AST nodes, 50086 symbols
📊 Processing comprehensive batch 74/140 (25 files)
⏱️  Comprehensive Progress: 1850/3489 files (53.0%), 186648 LOC/s, 6730487 AST nodes, 50976 symbols
📊 Processing comprehensive batch 75/140 (25 files)
⏱️  Comprehensive Progress: 1875/3489 files (53.7%), 185982 LOC/s, 6854006 AST nodes, 52469 symbols
📊 Processing comprehensive batch 76/140 (25 files)
⏱️  Comprehensive Progress: 1900/3489 files (54.5%), 184769 LOC/s, 7051742 AST nodes, 54787 symbols
📊 Processing comprehensive batch 77/140 (25 files)
⏱️  Comprehensive Progress: 1925/3489 files (55.2%), 183761 LOC/s, 7197908 AST nodes, 56210 symbols
📊 Processing comprehensive batch 78/140 (25 files)
⏱️  Comprehensive Progress: 1950/3489 files (55.9%), 183653 LOC/s, 7225168 AST nodes, 56425 symbols
📊 Processing comprehensive batch 79/140 (25 files)
⏱️  Comprehensive Progress: 1975/3489 files (56.6%), 183000 LOC/s, 7349751 AST nodes, 57280 symbols
📊 Processing comprehensive batch 80/140 (25 files)
⏱️  Comprehensive Progress: 2000/3489 files (57.3%), 182945 LOC/s, 7408066 AST nodes, 57759 symbols
📊 Processing comprehensive batch 81/140 (25 files)
⏱️  Comprehensive Progress: 2025/3489 files (58.0%), 182091 LOC/s, 7568012 AST nodes, 59092 symbols
📊 Processing comprehensive batch 82/140 (25 files)
⏱️  Comprehensive Progress: 2050/3489 files (58.8%), 181613 LOC/s, 7744153 AST nodes, 60726 symbols
📊 Processing comprehensive batch 83/140 (25 files)
⏱️  Comprehensive Progress: 2075/3489 files (59.5%), 180097 LOC/s, 7955533 AST nodes, 62678 symbols
📊 Processing comprehensive batch 84/140 (25 files)
⏱️  Comprehensive Progress: 2100/3489 files (60.2%), 179558 LOC/s, 8043423 AST nodes, 63529 symbols
📊 Processing comprehensive batch 85/140 (25 files)
⏱️  Comprehensive Progress: 2125/3489 files (60.9%), 178595 LOC/s, 8181443 AST nodes, 64602 symbols
📊 Processing comprehensive batch 86/140 (25 files)
⏱️  Comprehensive Progress: 2150/3489 files (61.6%), 177492 LOC/s, 8356392 AST nodes, 65881 symbols
📊 Processing comprehensive batch 87/140 (25 files)
⏱️  Comprehensive Progress: 2175/3489 files (62.3%), 183403 LOC/s, 8541915 AST nodes, 66966 symbols
📊 Processing comprehensive batch 88/140 (25 files)
⏱️  Comprehensive Progress: 2200/3489 files (63.1%), 182778 LOC/s, 8738467 AST nodes, 68786 symbols
📊 Processing comprehensive batch 89/140 (25 files)
⏱️  Comprehensive Progress: 2225/3489 files (63.8%), 182194 LOC/s, 8880275 AST nodes, 70140 symbols
📊 Processing comprehensive batch 90/140 (25 files)
⏱️  Comprehensive Progress: 2250/3489 files (64.5%), 181632 LOC/s, 9070044 AST nodes, 72152 symbols
📊 Processing comprehensive batch 91/140 (25 files)
⏱️  Comprehensive Progress: 2275/3489 files (65.2%), 181269 LOC/s, 9170851 AST nodes, 72771 symbols
📊 Processing comprehensive batch 92/140 (25 files)
⏱️  Comprehensive Progress: 2300/3489 files (65.9%), 180177 LOC/s, 9447278 AST nodes, 75099 symbols
📊 Processing comprehensive batch 93/140 (25 files)
⏱️  Comprehensive Progress: 2325/3489 files (66.6%), 178620 LOC/s, 9841580 AST nodes, 78433 symbols
📊 Processing comprehensive batch 94/140 (25 files)
⏱️  Comprehensive Progress: 2350/3489 files (67.4%), 178411 LOC/s, 9931649 AST nodes, 79105 symbols
📊 Processing comprehensive batch 95/140 (25 files)
⏱️  Comprehensive Progress: 2375/3489 files (68.1%), 178351 LOC/s, 9959523 AST nodes, 79299 symbols
📊 Processing comprehensive batch 96/140 (25 files)
⏱️  Comprehensive Progress: 2400/3489 files (68.8%), 178291 LOC/s, 10039935 AST nodes, 79775 symbols
📊 Processing comprehensive batch 97/140 (25 files)
⏱️  Comprehensive Progress: 2425/3489 files (69.5%), 178384 LOC/s, 10171313 AST nodes, 80551 symbols
📊 Processing comprehensive batch 98/140 (25 files)
⏱️  Comprehensive Progress: 2450/3489 files (70.2%), 178624 LOC/s, 10258216 AST nodes, 81413 symbols
📊 Processing comprehensive batch 99/140 (25 files)
⏱️  Comprehensive Progress: 2475/3489 files (70.9%), 178577 LOC/s, 10334614 AST nodes, 82091 symbols
📊 Processing comprehensive batch 100/140 (25 files)
⏱️  Comprehensive Progress: 2500/3489 files (71.7%), 178932 LOC/s, 10512262 AST nodes, 83088 symbols
📊 Processing comprehensive batch 101/140 (25 files)
⏱️  Comprehensive Progress: 2525/3489 files (72.4%), 178895 LOC/s, 10582347 AST nodes, 83398 symbols
📊 Processing comprehensive batch 102/140 (25 files)
⏱️  Comprehensive Progress: 2550/3489 files (73.1%), 178927 LOC/s, 10638482 AST nodes, 83710 symbols
📊 Processing comprehensive batch 103/140 (25 files)
⏱️  Comprehensive Progress: 2575/3489 files (73.8%), 178870 LOC/s, 10686767 AST nodes, 84075 symbols
📊 Processing comprehensive batch 104/140 (25 files)
⏱️  Comprehensive Progress: 2600/3489 files (74.5%), 178985 LOC/s, 10844440 AST nodes, 84861 symbols
📊 Processing comprehensive batch 105/140 (25 files)
⏱️  Comprehensive Progress: 2625/3489 files (75.2%), 179057 LOC/s, 10945420 AST nodes, 85295 symbols
📊 Processing comprehensive batch 106/140 (25 files)
⏱️  Comprehensive Progress: 2650/3489 files (76.0%), 179218 LOC/s, 10998074 AST nodes, 85740 symbols
📊 Processing comprehensive batch 107/140 (25 files)
⏱️  Comprehensive Progress: 2675/3489 files (76.7%), 179142 LOC/s, 11112785 AST nodes, 86385 symbols
📊 Processing comprehensive batch 108/140 (25 files)
⏱️  Comprehensive Progress: 2700/3489 files (77.4%), 179069 LOC/s, 11159141 AST nodes, 86756 symbols
📊 Processing comprehensive batch 109/140 (25 files)
⏱️  Comprehensive Progress: 2725/3489 files (78.1%), 178945 LOC/s, 11200930 AST nodes, 87132 symbols
📊 Processing comprehensive batch 110/140 (25 files)
⏱️  Comprehensive Progress: 2750/3489 files (78.8%), 178766 LOC/s, 11250384 AST nodes, 87596 symbols
📊 Processing comprehensive batch 111/140 (25 files)
⏱️  Comprehensive Progress: 2775/3489 files (79.5%), 178553 LOC/s, 11339792 AST nodes, 88266 symbols
📊 Processing comprehensive batch 112/140 (25 files)
⏱️  Comprehensive Progress: 2800/3489 files (80.3%), 180110 LOC/s, 11455877 AST nodes, 88963 symbols
📊 Processing comprehensive batch 113/140 (25 files)
⏱️  Comprehensive Progress: 2825/3489 files (81.0%), 180118 LOC/s, 11517651 AST nodes, 89303 symbols
📊 Processing comprehensive batch 114/140 (25 files)
⏱️  Comprehensive Progress: 2850/3489 files (81.7%), 180216 LOC/s, 11613400 AST nodes, 89934 symbols
📊 Processing comprehensive batch 115/140 (25 files)
⏱️  Comprehensive Progress: 2875/3489 files (82.4%), 180160 LOC/s, 11659918 AST nodes, 90161 symbols
📊 Processing comprehensive batch 116/140 (25 files)
⏱️  Comprehensive Progress: 2900/3489 files (83.1%), 180140 LOC/s, 11679479 AST nodes, 90250 symbols
📊 Processing comprehensive batch 117/140 (25 files)
⏱️  Comprehensive Progress: 2925/3489 files (83.8%), 180140 LOC/s, 11693066 AST nodes, 90420 symbols
📊 Processing comprehensive batch 118/140 (25 files)
⏱️  Comprehensive Progress: 2950/3489 files (84.6%), 180768 LOC/s, 11873220 AST nodes, 91539 symbols
📊 Processing comprehensive batch 119/140 (25 files)
⏱️  Comprehensive Progress: 2975/3489 files (85.3%), 180908 LOC/s, 11914018 AST nodes, 91830 symbols
📊 Processing comprehensive batch 120/140 (25 files)
⏱️  Comprehensive Progress: 3000/3489 files (86.0%), 181131 LOC/s, 12058704 AST nodes, 92555 symbols
📊 Processing comprehensive batch 121/140 (25 files)
⏱️  Comprehensive Progress: 3025/3489 files (86.7%), 181631 LOC/s, 12260624 AST nodes, 93615 symbols
📊 Processing comprehensive batch 122/140 (25 files)
⏱️  Comprehensive Progress: 3050/3489 files (87.4%), 182076 LOC/s, 12458287 AST nodes, 94410 symbols
📊 Processing comprehensive batch 123/140 (25 files)
⏱️  Comprehensive Progress: 3075/3489 files (88.1%), 182207 LOC/s, 12495555 AST nodes, 94601 symbols
📊 Processing comprehensive batch 124/140 (25 files)
⏱️  Comprehensive Progress: 3100/3489 files (88.9%), 180927 LOC/s, 12680913 AST nodes, 95269 symbols
📊 Processing comprehensive batch 125/140 (25 files)
⏱️  Comprehensive Progress: 3125/3489 files (89.6%), 181583 LOC/s, 12858814 AST nodes, 96313 symbols
📊 Processing comprehensive batch 126/140 (25 files)
⏱️  Comprehensive Progress: 3150/3489 files (90.3%), 181548 LOC/s, 12962938 AST nodes, 96902 symbols
📊 Processing comprehensive batch 127/140 (25 files)
⏱️  Comprehensive Progress: 3175/3489 files (91.0%), 179134 LOC/s, 13042910 AST nodes, 97435 symbols
📊 Processing comprehensive batch 128/140 (25 files)
⏱️  Comprehensive Progress: 3200/3489 files (91.7%), 177756 LOC/s, 13195904 AST nodes, 98074 symbols
📊 Processing comprehensive batch 129/140 (25 files)
⏱️  Comprehensive Progress: 3225/3489 files (92.4%), 176700 LOC/s, 13368374 AST nodes, 98839 symbols
📊 Processing comprehensive batch 130/140 (25 files)
❌ Failed comprehensive analysis for test-data/repositories/cpython/Modules/_xxtestfuzz/fuzz_elementtree_parsewhole_corpus/test.xml: stream did not contain valid UTF-8
⏱️  Comprehensive Progress: 3250/3489 files (93.1%), 176699 LOC/s, 13368398 AST nodes, 98977 symbols
📊 Processing comprehensive batch 131/140 (25 files)
⏱️  Comprehensive Progress: 3275/3489 files (93.9%), 176699 LOC/s, 13369293 AST nodes, 99138 symbols
📊 Processing comprehensive batch 132/140 (25 files)
⏱️  Comprehensive Progress: 3300/3489 files (94.6%), 177149 LOC/s, 13475691 AST nodes, 99744 symbols
📊 Processing comprehensive batch 133/140 (25 files)
⏱️  Comprehensive Progress: 3325/3489 files (95.3%), 177402 LOC/s, 13689207 AST nodes, 100807 symbols
📊 Processing comprehensive batch 134/140 (25 files)
⏱️  Comprehensive Progress: 3350/3489 files (96.0%), 177622 LOC/s, 13807492 AST nodes, 101349 symbols
📊 Processing comprehensive batch 135/140 (25 files)
⏱️  Comprehensive Progress: 3375/3489 files (96.7%), 177809 LOC/s, 13956229 AST nodes, 102467 symbols
📊 Processing comprehensive batch 136/140 (25 files)
⏱️  Comprehensive Progress: 3400/3489 files (97.4%), 178207 LOC/s, 14161769 AST nodes, 103565 symbols
📊 Processing comprehensive batch 137/140 (25 files)
⏱️  Comprehensive Progress: 3425/3489 files (98.2%), 178283 LOC/s, 14201371 AST nodes, 103674 symbols
📊 Processing comprehensive batch 138/140 (25 files)
⏱️  Comprehensive Progress: 3450/3489 files (98.9%), 178411 LOC/s, 14249934 AST nodes, 103985 symbols
📊 Processing comprehensive batch 139/140 (25 files)
⏱️  Comprehensive Progress: 3475/3489 files (99.6%), 178458 LOC/s, 14286028 AST nodes, 104218 symbols
📊 Processing comprehensive batch 140/140 (14 files)
⏱️  Comprehensive Progress: 3489/3489 files (100.0%), 178293 LOC/s, 14319663 AST nodes, 104455 symbols

🎯 COMPREHENSIVE ANALYSIS RESULTS
==================================
📁 Files analyzed: 3489
📝 Lines processed: 2066559
🌳 AST nodes generated: 14319663
🔍 Symbols extracted: 104455
🎯 Patterns detected: 524809
⏱️  Duration: 11.59 seconds
🚀 Lines/Second: 178293
✅ Success Rate: 98.4%

🎯 PERFORMANCE VALIDATION RESULTS
=====================================
📁 Total Files: 3489
📝 Total Lines: 2066559
⏱️  Duration: 11.59 seconds
🚀 Lines/Second: 178293
📊 Files/Second: 301.0
✅ Success Rate: 98.4%
💾 Memory Usage: 0.0 MB

📈 LANGUAGE-SPECIFIC PERFORMANCE
====================================

🎯 1M LOC PERFORMANCE CLAIM VALIDATION:
Target: 1000000 LOC in 300 seconds
Actual: 2066559 LOC in 11.59 seconds
Projected 1M LOC time: 5.61 seconds
✅ CLAIM VALIDATED: Can process 1M LOC in under 5 minutes!
🚀 Performance margin: 53.5x faster than required

📊 Results saved to: performance_results.json
