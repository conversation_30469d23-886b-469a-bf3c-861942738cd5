📊 Running basic performance validation...
🚀 Starting performance validation for: test-data/repositories/rust-compiler
📁 Found 36650 files to analyze
📊 Processing batch 1/733 (50 files)
⏱️  Progress: 50/36650 files (0.1%), 91331 LOC/s
📊 Processing batch 2/733 (50 files)
⏱️  Progress: 100/36650 files (0.3%), 92299 LOC/s
📊 Processing batch 3/733 (50 files)
⏱️  Progress: 150/36650 files (0.4%), 94699 LOC/s
📊 Processing batch 4/733 (50 files)
⏱️  Progress: 200/36650 files (0.5%), 96432 LOC/s
📊 Processing batch 5/733 (50 files)
⏱️  Progress: 250/36650 files (0.7%), 99219 LOC/s
📊 Processing batch 6/733 (50 files)
⏱️  Progress: 300/36650 files (0.8%), 102427 LOC/s
📊 Processing batch 7/733 (50 files)
⏱️  Progress: 350/36650 files (1.0%), 100013 LOC/s
📊 Processing batch 8/733 (50 files)
⏱️  Progress: 400/36650 files (1.1%), 101653 LOC/s
📊 Processing batch 9/733 (50 files)
⏱️  Progress: 450/36650 files (1.2%), 103460 LOC/s
📊 Processing batch 10/733 (50 files)
⏱️  Progress: 500/36650 files (1.4%), 105564 LOC/s
📊 Processing batch 11/733 (50 files)
⏱️  Progress: 550/36650 files (1.5%), 105941 LOC/s
📊 Processing batch 12/733 (50 files)
⏱️  Progress: 600/36650 files (1.6%), 103843 LOC/s
📊 Processing batch 13/733 (50 files)
⏱️  Progress: 650/36650 files (1.8%), 103239 LOC/s
📊 Processing batch 14/733 (50 files)
⏱️  Progress: 700/36650 files (1.9%), 104328 LOC/s
📊 Processing batch 15/733 (50 files)
⏱️  Progress: 750/36650 files (2.0%), 105066 LOC/s
📊 Processing batch 16/733 (50 files)
⏱️  Progress: 800/36650 files (2.2%), 105913 LOC/s
📊 Processing batch 17/733 (50 files)
⏱️  Progress: 850/36650 files (2.3%), 107055 LOC/s
📊 Processing batch 18/733 (50 files)
⏱️  Progress: 900/36650 files (2.5%), 107168 LOC/s
📊 Processing batch 19/733 (50 files)
⏱️  Progress: 950/36650 files (2.6%), 108515 LOC/s
📊 Processing batch 20/733 (50 files)
⏱️  Progress: 1000/36650 files (2.7%), 109829 LOC/s
📊 Processing batch 21/733 (50 files)
⏱️  Progress: 1050/36650 files (2.9%), 110675 LOC/s
📊 Processing batch 22/733 (50 files)
⏱️  Progress: 1100/36650 files (3.0%), 111665 LOC/s
📊 Processing batch 23/733 (50 files)
⏱️  Progress: 1150/36650 files (3.1%), 114649 LOC/s
📊 Processing batch 24/733 (50 files)
⏱️  Progress: 1200/36650 files (3.3%), 116179 LOC/s
📊 Processing batch 25/733 (50 files)
⏱️  Progress: 1250/36650 files (3.4%), 118539 LOC/s
📊 Processing batch 26/733 (50 files)
⏱️  Progress: 1300/36650 files (3.5%), 121262 LOC/s
📊 Processing batch 27/733 (50 files)
⏱️  Progress: 1350/36650 files (3.7%), 122809 LOC/s
📊 Processing batch 28/733 (50 files)
⏱️  Progress: 1400/36650 files (3.8%), 125095 LOC/s
📊 Processing batch 29/733 (50 files)
⏱️  Progress: 1450/36650 files (4.0%), 126048 LOC/s
📊 Processing batch 30/733 (50 files)
⏱️  Progress: 1500/36650 files (4.1%), 128171 LOC/s
📊 Processing batch 31/733 (50 files)
⏱️  Progress: 1550/36650 files (4.2%), 129235 LOC/s
📊 Processing batch 32/733 (50 files)
⏱️  Progress: 1600/36650 files (4.4%), 130782 LOC/s
📊 Processing batch 33/733 (50 files)
⏱️  Progress: 1650/36650 files (4.5%), 133130 LOC/s
📊 Processing batch 34/733 (50 files)
⏱️  Progress: 1700/36650 files (4.6%), 133487 LOC/s
📊 Processing batch 35/733 (50 files)
⏱️  Progress: 1750/36650 files (4.8%), 134913 LOC/s
📊 Processing batch 36/733 (50 files)
⏱️  Progress: 1800/36650 files (4.9%), 136148 LOC/s
📊 Processing batch 37/733 (50 files)
⏱️  Progress: 1850/36650 files (5.0%), 137199 LOC/s
📊 Processing batch 38/733 (50 files)
⏱️  Progress: 1900/36650 files (5.2%), 138384 LOC/s
📊 Processing batch 39/733 (50 files)
⏱️  Progress: 1950/36650 files (5.3%), 138621 LOC/s
📊 Processing batch 40/733 (50 files)
⏱️  Progress: 2000/36650 files (5.5%), 139988 LOC/s
📊 Processing batch 41/733 (50 files)
⏱️  Progress: 2050/36650 files (5.6%), 140040 LOC/s
📊 Processing batch 42/733 (50 files)
⏱️  Progress: 2100/36650 files (5.7%), 140186 LOC/s
📊 Processing batch 43/733 (50 files)
⏱️  Progress: 2150/36650 files (5.9%), 139739 LOC/s
📊 Processing batch 44/733 (50 files)
⏱️  Progress: 2200/36650 files (6.0%), 141538 LOC/s
📊 Processing batch 45/733 (50 files)
⏱️  Progress: 2250/36650 files (6.1%), 142709 LOC/s
📊 Processing batch 46/733 (50 files)
⏱️  Progress: 2300/36650 files (6.3%), 143702 LOC/s
📊 Processing batch 47/733 (50 files)
⏱️  Progress: 2350/36650 files (6.4%), 144319 LOC/s
📊 Processing batch 48/733 (50 files)
⏱️  Progress: 2400/36650 files (6.5%), 144343 LOC/s
📊 Processing batch 49/733 (50 files)
⏱️  Progress: 2450/36650 files (6.7%), 145298 LOC/s
📊 Processing batch 50/733 (50 files)
⏱️  Progress: 2500/36650 files (6.8%), 146112 LOC/s
📊 Processing batch 51/733 (50 files)
⏱️  Progress: 2550/36650 files (7.0%), 146812 LOC/s
📊 Processing batch 52/733 (50 files)
⏱️  Progress: 2600/36650 files (7.1%), 146869 LOC/s
📊 Processing batch 53/733 (50 files)
⏱️  Progress: 2650/36650 files (7.2%), 144630 LOC/s
📊 Processing batch 54/733 (50 files)
⏱️  Progress: 2700/36650 files (7.4%), 144965 LOC/s
📊 Processing batch 55/733 (50 files)
⏱️  Progress: 2750/36650 files (7.5%), 143666 LOC/s
📊 Processing batch 56/733 (50 files)
⏱️  Progress: 2800/36650 files (7.6%), 143218 LOC/s
📊 Processing batch 57/733 (50 files)
⏱️  Progress: 2850/36650 files (7.8%), 141768 LOC/s
📊 Processing batch 58/733 (50 files)
⏱️  Progress: 2900/36650 files (7.9%), 141153 LOC/s
📊 Processing batch 59/733 (50 files)
⏱️  Progress: 2950/36650 files (8.0%), 141557 LOC/s
📊 Processing batch 60/733 (50 files)
⏱️  Progress: 3000/36650 files (8.2%), 141556 LOC/s
📊 Processing batch 61/733 (50 files)
⏱️  Progress: 3050/36650 files (8.3%), 141925 LOC/s
📊 Processing batch 62/733 (50 files)
⏱️  Progress: 3100/36650 files (8.5%), 142042 LOC/s
📊 Processing batch 63/733 (50 files)
⏱️  Progress: 3150/36650 files (8.6%), 143402 LOC/s
📊 Processing batch 64/733 (50 files)
⏱️  Progress: 3200/36650 files (8.7%), 143898 LOC/s
📊 Processing batch 65/733 (50 files)
⏱️  Progress: 3250/36650 files (8.9%), 144516 LOC/s
📊 Processing batch 66/733 (50 files)
⏱️  Progress: 3300/36650 files (9.0%), 144903 LOC/s
📊 Processing batch 67/733 (50 files)
⏱️  Progress: 3350/36650 files (9.1%), 145111 LOC/s
📊 Processing batch 68/733 (50 files)
⏱️  Progress: 3400/36650 files (9.3%), 145162 LOC/s
📊 Processing batch 69/733 (50 files)
⏱️  Progress: 3450/36650 files (9.4%), 144977 LOC/s
📊 Processing batch 70/733 (50 files)
⏱️  Progress: 3500/36650 files (9.5%), 145577 LOC/s
📊 Processing batch 71/733 (50 files)
⏱️  Progress: 3550/36650 files (9.7%), 145742 LOC/s
📊 Processing batch 72/733 (50 files)
⏱️  Progress: 3600/36650 files (9.8%), 146300 LOC/s
📊 Processing batch 73/733 (50 files)
⏱️  Progress: 3650/36650 files (10.0%), 146288 LOC/s
📊 Processing batch 74/733 (50 files)
⏱️  Progress: 3700/36650 files (10.1%), 146653 LOC/s
📊 Processing batch 75/733 (50 files)
⏱️  Progress: 3750/36650 files (10.2%), 147175 LOC/s
📊 Processing batch 76/733 (50 files)
⏱️  Progress: 3800/36650 files (10.4%), 147347 LOC/s
📊 Processing batch 77/733 (50 files)
⏱️  Progress: 3850/36650 files (10.5%), 147491 LOC/s
📊 Processing batch 78/733 (50 files)
⏱️  Progress: 3900/36650 files (10.6%), 147912 LOC/s
📊 Processing batch 79/733 (50 files)
⏱️  Progress: 3950/36650 files (10.8%), 148409 LOC/s
📊 Processing batch 80/733 (50 files)
⏱️  Progress: 4000/36650 files (10.9%), 148310 LOC/s
📊 Processing batch 81/733 (50 files)
⏱️  Progress: 4050/36650 files (11.1%), 148687 LOC/s
📊 Processing batch 82/733 (50 files)
⏱️  Progress: 4100/36650 files (11.2%), 148936 LOC/s
📊 Processing batch 83/733 (50 files)
⏱️  Progress: 4150/36650 files (11.3%), 148948 LOC/s
📊 Processing batch 84/733 (50 files)
⏱️  Progress: 4200/36650 files (11.5%), 148721 LOC/s
📊 Processing batch 85/733 (50 files)
⏱️  Progress: 4250/36650 files (11.6%), 149302 LOC/s
📊 Processing batch 86/733 (50 files)
⏱️  Progress: 4300/36650 files (11.7%), 149372 LOC/s
📊 Processing batch 87/733 (50 files)
⏱️  Progress: 4350/36650 files (11.9%), 149783 LOC/s
📊 Processing batch 88/733 (50 files)
⏱️  Progress: 4400/36650 files (12.0%), 150060 LOC/s
📊 Processing batch 89/733 (50 files)
⏱️  Progress: 4450/36650 files (12.1%), 149955 LOC/s
📊 Processing batch 90/733 (50 files)
⏱️  Progress: 4500/36650 files (12.3%), 152657 LOC/s
📊 Processing batch 91/733 (50 files)
⏱️  Progress: 4550/36650 files (12.4%), 152978 LOC/s
📊 Processing batch 92/733 (50 files)
⏱️  Progress: 4600/36650 files (12.6%), 152781 LOC/s
📊 Processing batch 93/733 (50 files)
⏱️  Progress: 4650/36650 files (12.7%), 152859 LOC/s
📊 Processing batch 94/733 (50 files)
⏱️  Progress: 4700/36650 files (12.8%), 152855 LOC/s
📊 Processing batch 95/733 (50 files)
⏱️  Progress: 4750/36650 files (13.0%), 152812 LOC/s
📊 Processing batch 96/733 (50 files)
⏱️  Progress: 4800/36650 files (13.1%), 152749 LOC/s
📊 Processing batch 97/733 (50 files)
⏱️  Progress: 4850/36650 files (13.2%), 152762 LOC/s
📊 Processing batch 98/733 (50 files)
⏱️  Progress: 4900/36650 files (13.4%), 152975 LOC/s
📊 Processing batch 99/733 (50 files)
⏱️  Progress: 4950/36650 files (13.5%), 153065 LOC/s
📊 Processing batch 100/733 (50 files)
⏱️  Progress: 5000/36650 files (13.6%), 153787 LOC/s
📊 Processing batch 101/733 (50 files)
⏱️  Progress: 5050/36650 files (13.8%), 154357 LOC/s
📊 Processing batch 102/733 (50 files)
⏱️  Progress: 5100/36650 files (13.9%), 154224 LOC/s
📊 Processing batch 103/733 (50 files)
⏱️  Progress: 5150/36650 files (14.1%), 154638 LOC/s
📊 Processing batch 104/733 (50 files)
⏱️  Progress: 5200/36650 files (14.2%), 154863 LOC/s
📊 Processing batch 105/733 (50 files)
⏱️  Progress: 5250/36650 files (14.3%), 155226 LOC/s
📊 Processing batch 106/733 (50 files)
⏱️  Progress: 5300/36650 files (14.5%), 155277 LOC/s
📊 Processing batch 107/733 (50 files)
⏱️  Progress: 5350/36650 files (14.6%), 155451 LOC/s
📊 Processing batch 108/733 (50 files)
⏱️  Progress: 5400/36650 files (14.7%), 155372 LOC/s
📊 Processing batch 109/733 (50 files)
⏱️  Progress: 5450/36650 files (14.9%), 154923 LOC/s
📊 Processing batch 110/733 (50 files)
⏱️  Progress: 5500/36650 files (15.0%), 154730 LOC/s
📊 Processing batch 111/733 (50 files)
⏱️  Progress: 5550/36650 files (15.1%), 154926 LOC/s
📊 Processing batch 112/733 (50 files)
⏱️  Progress: 5600/36650 files (15.3%), 155282 LOC/s
📊 Processing batch 113/733 (50 files)
⏱️  Progress: 5650/36650 files (15.4%), 154881 LOC/s
📊 Processing batch 114/733 (50 files)
⏱️  Progress: 5700/36650 files (15.6%), 155069 LOC/s
📊 Processing batch 115/733 (50 files)
⏱️  Progress: 5750/36650 files (15.7%), 155324 LOC/s
📊 Processing batch 116/733 (50 files)
⏱️  Progress: 5800/36650 files (15.8%), 155606 LOC/s
📊 Processing batch 117/733 (50 files)
⏱️  Progress: 5850/36650 files (16.0%), 155090 LOC/s
📊 Processing batch 118/733 (50 files)
⏱️  Progress: 5900/36650 files (16.1%), 154325 LOC/s
📊 Processing batch 119/733 (50 files)
⏱️  Progress: 5950/36650 files (16.2%), 150827 LOC/s
📊 Processing batch 120/733 (50 files)
⏱️  Progress: 6000/36650 files (16.4%), 151416 LOC/s
📊 Processing batch 121/733 (50 files)
⏱️  Progress: 6050/36650 files (16.5%), 152049 LOC/s
📊 Processing batch 122/733 (50 files)
⏱️  Progress: 6100/36650 files (16.6%), 152413 LOC/s
📊 Processing batch 123/733 (50 files)
⏱️  Progress: 6150/36650 files (16.8%), 152769 LOC/s
📊 Processing batch 124/733 (50 files)
⏱️  Progress: 6200/36650 files (16.9%), 152989 LOC/s
📊 Processing batch 125/733 (50 files)
⏱️  Progress: 6250/36650 files (17.1%), 153329 LOC/s
📊 Processing batch 126/733 (50 files)
⏱️  Progress: 6300/36650 files (17.2%), 154094 LOC/s
📊 Processing batch 127/733 (50 files)
⏱️  Progress: 6350/36650 files (17.3%), 153855 LOC/s
📊 Processing batch 128/733 (50 files)
⏱️  Progress: 6400/36650 files (17.5%), 154235 LOC/s
📊 Processing batch 129/733 (50 files)
⏱️  Progress: 6450/36650 files (17.6%), 154351 LOC/s
📊 Processing batch 130/733 (50 files)
⏱️  Progress: 6500/36650 files (17.7%), 154489 LOC/s
📊 Processing batch 131/733 (50 files)
⏱️  Progress: 6550/36650 files (17.9%), 155013 LOC/s
📊 Processing batch 132/733 (50 files)
⏱️  Progress: 6600/36650 files (18.0%), 155954 LOC/s
📊 Processing batch 133/733 (50 files)
⏱️  Progress: 6650/36650 files (18.1%), 155871 LOC/s
📊 Processing batch 134/733 (50 files)
⏱️  Progress: 6700/36650 files (18.3%), 156932 LOC/s
📊 Processing batch 135/733 (50 files)
⏱️  Progress: 6750/36650 files (18.4%), 157275 LOC/s
📊 Processing batch 136/733 (50 files)
⏱️  Progress: 6800/36650 files (18.6%), 157199 LOC/s
📊 Processing batch 137/733 (50 files)
⏱️  Progress: 6850/36650 files (18.7%), 157417 LOC/s
📊 Processing batch 138/733 (50 files)
⏱️  Progress: 6900/36650 files (18.8%), 157538 LOC/s
📊 Processing batch 139/733 (50 files)
⏱️  Progress: 6950/36650 files (19.0%), 157704 LOC/s
📊 Processing batch 140/733 (50 files)
⏱️  Progress: 7000/36650 files (19.1%), 157591 LOC/s
📊 Processing batch 141/733 (50 files)
⏱️  Progress: 7050/36650 files (19.2%), 157919 LOC/s
📊 Processing batch 142/733 (50 files)
⏱️  Progress: 7100/36650 files (19.4%), 158176 LOC/s
📊 Processing batch 143/733 (50 files)
⏱️  Progress: 7150/36650 files (19.5%), 158148 LOC/s
📊 Processing batch 144/733 (50 files)
⏱️  Progress: 7200/36650 files (19.6%), 156490 LOC/s
📊 Processing batch 145/733 (50 files)
⏱️  Progress: 7250/36650 files (19.8%), 156297 LOC/s
📊 Processing batch 146/733 (50 files)
⏱️  Progress: 7300/36650 files (19.9%), 156411 LOC/s
📊 Processing batch 147/733 (50 files)
⏱️  Progress: 7350/36650 files (20.1%), 156014 LOC/s
📊 Processing batch 148/733 (50 files)
⏱️  Progress: 7400/36650 files (20.2%), 154877 LOC/s
📊 Processing batch 149/733 (50 files)
⏱️  Progress: 7450/36650 files (20.3%), 154904 LOC/s
📊 Processing batch 150/733 (50 files)
⏱️  Progress: 7500/36650 files (20.5%), 155107 LOC/s
📊 Processing batch 151/733 (50 files)
⏱️  Progress: 7550/36650 files (20.6%), 155326 LOC/s
📊 Processing batch 152/733 (50 files)
⏱️  Progress: 7600/36650 files (20.7%), 155681 LOC/s
📊 Processing batch 153/733 (50 files)
⏱️  Progress: 7650/36650 files (20.9%), 155804 LOC/s
📊 Processing batch 154/733 (50 files)
⏱️  Progress: 7700/36650 files (21.0%), 156242 LOC/s
📊 Processing batch 155/733 (50 files)
⏱️  Progress: 7750/36650 files (21.1%), 156240 LOC/s
📊 Processing batch 156/733 (50 files)
⏱️  Progress: 7800/36650 files (21.3%), 156176 LOC/s
📊 Processing batch 157/733 (50 files)
⏱️  Progress: 7850/36650 files (21.4%), 155973 LOC/s
📊 Processing batch 158/733 (50 files)
⏱️  Progress: 7900/36650 files (21.6%), 155744 LOC/s
📊 Processing batch 159/733 (50 files)
⏱️  Progress: 7950/36650 files (21.7%), 155587 LOC/s
📊 Processing batch 160/733 (50 files)
⏱️  Progress: 8000/36650 files (21.8%), 133923 LOC/s
📊 Processing batch 161/733 (50 files)
⏱️  Progress: 8050/36650 files (22.0%), 133562 LOC/s
📊 Processing batch 162/733 (50 files)
⏱️  Progress: 8100/36650 files (22.1%), 133415 LOC/s
📊 Processing batch 163/733 (50 files)
⏱️  Progress: 8150/36650 files (22.2%), 132257 LOC/s
📊 Processing batch 164/733 (50 files)
⏱️  Progress: 8200/36650 files (22.4%), 132188 LOC/s
📊 Processing batch 165/733 (50 files)
⏱️  Progress: 8250/36650 files (22.5%), 131951 LOC/s
📊 Processing batch 166/733 (50 files)
⏱️  Progress: 8300/36650 files (22.6%), 131912 LOC/s
📊 Processing batch 167/733 (50 files)
⏱️  Progress: 8350/36650 files (22.8%), 131817 LOC/s
📊 Processing batch 168/733 (50 files)
⏱️  Progress: 8400/36650 files (22.9%), 130619 LOC/s
📊 Processing batch 169/733 (50 files)
⏱️  Progress: 8450/36650 files (23.1%), 130505 LOC/s
📊 Processing batch 170/733 (50 files)
⏱️  Progress: 8500/36650 files (23.2%), 130558 LOC/s
📊 Processing batch 171/733 (50 files)
⏱️  Progress: 8550/36650 files (23.3%), 130311 LOC/s
📊 Processing batch 172/733 (50 files)
⏱️  Progress: 8600/36650 files (23.5%), 130342 LOC/s
📊 Processing batch 173/733 (50 files)
⏱️  Progress: 8650/36650 files (23.6%), 130738 LOC/s
📊 Processing batch 174/733 (50 files)
⏱️  Progress: 8700/36650 files (23.7%), 131010 LOC/s
📊 Processing batch 175/733 (50 files)
⏱️  Progress: 8750/36650 files (23.9%), 131126 LOC/s
📊 Processing batch 176/733 (50 files)
⏱️  Progress: 8800/36650 files (24.0%), 131282 LOC/s
📊 Processing batch 177/733 (50 files)
⏱️  Progress: 8850/36650 files (24.1%), 131476 LOC/s
📊 Processing batch 178/733 (50 files)
⏱️  Progress: 8900/36650 files (24.3%), 131661 LOC/s
📊 Processing batch 179/733 (50 files)
⏱️  Progress: 8950/36650 files (24.4%), 131812 LOC/s
📊 Processing batch 180/733 (50 files)
⏱️  Progress: 9000/36650 files (24.6%), 131826 LOC/s
📊 Processing batch 181/733 (50 files)
⏱️  Progress: 9050/36650 files (24.7%), 132192 LOC/s
📊 Processing batch 182/733 (50 files)
⏱️  Progress: 9100/36650 files (24.8%), 132347 LOC/s
📊 Processing batch 183/733 (50 files)
⏱️  Progress: 9150/36650 files (25.0%), 132298 LOC/s
📊 Processing batch 184/733 (50 files)
⏱️  Progress: 9200/36650 files (25.1%), 132274 LOC/s
📊 Processing batch 185/733 (50 files)
⏱️  Progress: 9250/36650 files (25.2%), 131908 LOC/s
📊 Processing batch 186/733 (50 files)
⏱️  Progress: 9300/36650 files (25.4%), 131895 LOC/s
📊 Processing batch 187/733 (50 files)
⏱️  Progress: 9350/36650 files (25.5%), 131615 LOC/s
📊 Processing batch 188/733 (50 files)
⏱️  Progress: 9400/36650 files (25.6%), 131645 LOC/s
📊 Processing batch 189/733 (50 files)
⏱️  Progress: 9450/36650 files (25.8%), 131448 LOC/s
📊 Processing batch 190/733 (50 files)
⏱️  Progress: 9500/36650 files (25.9%), 131149 LOC/s
📊 Processing batch 191/733 (50 files)
⏱️  Progress: 9550/36650 files (26.1%), 131095 LOC/s
📊 Processing batch 192/733 (50 files)
⏱️  Progress: 9600/36650 files (26.2%), 131231 LOC/s
📊 Processing batch 193/733 (50 files)
⏱️  Progress: 9650/36650 files (26.3%), 131024 LOC/s
📊 Processing batch 194/733 (50 files)
⏱️  Progress: 9700/36650 files (26.5%), 130886 LOC/s
📊 Processing batch 195/733 (50 files)
⏱️  Progress: 9750/36650 files (26.6%), 130791 LOC/s
📊 Processing batch 196/733 (50 files)
⏱️  Progress: 9800/36650 files (26.7%), 131068 LOC/s
📊 Processing batch 197/733 (50 files)
⏱️  Progress: 9850/36650 files (26.9%), 130949 LOC/s
📊 Processing batch 198/733 (50 files)
⏱️  Progress: 9900/36650 files (27.0%), 128766 LOC/s
📊 Processing batch 199/733 (50 files)
⏱️  Progress: 9950/36650 files (27.1%), 129243 LOC/s
📊 Processing batch 200/733 (50 files)
⏱️  Progress: 10000/36650 files (27.3%), 129149 LOC/s
📊 Processing batch 201/733 (50 files)
⏱️  Progress: 10050/36650 files (27.4%), 129175 LOC/s
📊 Processing batch 202/733 (50 files)
⏱️  Progress: 10100/36650 files (27.6%), 129196 LOC/s
📊 Processing batch 203/733 (50 files)
⏱️  Progress: 10150/36650 files (27.7%), 129100 LOC/s
📊 Processing batch 204/733 (50 files)
⏱️  Progress: 10200/36650 files (27.8%), 128996 LOC/s
📊 Processing batch 205/733 (50 files)
⏱️  Progress: 10250/36650 files (28.0%), 129005 LOC/s
📊 Processing batch 206/733 (50 files)
⏱️  Progress: 10300/36650 files (28.1%), 128792 LOC/s
📊 Processing batch 207/733 (50 files)
⏱️  Progress: 10350/36650 files (28.2%), 128699 LOC/s
📊 Processing batch 208/733 (50 files)
⏱️  Progress: 10400/36650 files (28.4%), 128795 LOC/s
📊 Processing batch 209/733 (50 files)
⏱️  Progress: 10450/36650 files (28.5%), 129049 LOC/s
📊 Processing batch 210/733 (50 files)
⏱️  Progress: 10500/36650 files (28.6%), 129154 LOC/s
📊 Processing batch 211/733 (50 files)
⏱️  Progress: 10550/36650 files (28.8%), 129567 LOC/s
📊 Processing batch 212/733 (50 files)
⏱️  Progress: 10600/36650 files (28.9%), 129653 LOC/s
📊 Processing batch 213/733 (50 files)
⏱️  Progress: 10650/36650 files (29.1%), 129575 LOC/s
📊 Processing batch 214/733 (50 files)
⏱️  Progress: 10700/36650 files (29.2%), 129613 LOC/s
📊 Processing batch 215/733 (50 files)
⏱️  Progress: 10750/36650 files (29.3%), 129843 LOC/s
📊 Processing batch 216/733 (50 files)
⏱️  Progress: 10800/36650 files (29.5%), 129945 LOC/s
📊 Processing batch 217/733 (50 files)
⏱️  Progress: 10850/36650 files (29.6%), 130114 LOC/s
📊 Processing batch 218/733 (50 files)
⏱️  Progress: 10900/36650 files (29.7%), 130187 LOC/s
📊 Processing batch 219/733 (50 files)
⏱️  Progress: 10950/36650 files (29.9%), 130379 LOC/s
📊 Processing batch 220/733 (50 files)
⏱️  Progress: 11000/36650 files (30.0%), 130604 LOC/s
📊 Processing batch 221/733 (50 files)
⏱️  Progress: 11050/36650 files (30.2%), 130632 LOC/s
📊 Processing batch 222/733 (50 files)
⏱️  Progress: 11100/36650 files (30.3%), 130687 LOC/s
📊 Processing batch 223/733 (50 files)
⏱️  Progress: 11150/36650 files (30.4%), 130921 LOC/s
📊 Processing batch 224/733 (50 files)
⏱️  Progress: 11200/36650 files (30.6%), 131096 LOC/s
📊 Processing batch 225/733 (50 files)
⏱️  Progress: 11250/36650 files (30.7%), 131197 LOC/s
📊 Processing batch 226/733 (50 files)
⏱️  Progress: 11300/36650 files (30.8%), 131366 LOC/s
📊 Processing batch 227/733 (50 files)
⏱️  Progress: 11350/36650 files (31.0%), 130735 LOC/s
📊 Processing batch 228/733 (50 files)
⏱️  Progress: 11400/36650 files (31.1%), 130453 LOC/s
📊 Processing batch 229/733 (50 files)
⏱️  Progress: 11450/36650 files (31.2%), 130744 LOC/s
📊 Processing batch 230/733 (50 files)
⏱️  Progress: 11500/36650 files (31.4%), 131341 LOC/s
📊 Processing batch 231/733 (50 files)
⏱️  Progress: 11550/36650 files (31.5%), 131532 LOC/s
📊 Processing batch 232/733 (50 files)
⏱️  Progress: 11600/36650 files (31.7%), 131500 LOC/s
📊 Processing batch 233/733 (50 files)
⏱️  Progress: 11650/36650 files (31.8%), 131641 LOC/s
📊 Processing batch 234/733 (50 files)
⏱️  Progress: 11700/36650 files (31.9%), 131822 LOC/s
📊 Processing batch 235/733 (50 files)
⏱️  Progress: 11750/36650 files (32.1%), 132087 LOC/s
📊 Processing batch 236/733 (50 files)
⏱️  Progress: 11800/36650 files (32.2%), 132246 LOC/s
📊 Processing batch 237/733 (50 files)
⏱️  Progress: 11850/36650 files (32.3%), 132286 LOC/s
📊 Processing batch 238/733 (50 files)
⏱️  Progress: 11900/36650 files (32.5%), 132651 LOC/s
📊 Processing batch 239/733 (50 files)
⏱️  Progress: 11950/36650 files (32.6%), 132597 LOC/s
📊 Processing batch 240/733 (50 files)
⏱️  Progress: 12000/36650 files (32.7%), 132647 LOC/s
📊 Processing batch 241/733 (50 files)
⏱️  Progress: 12050/36650 files (32.9%), 132923 LOC/s
📊 Processing batch 242/733 (50 files)
⏱️  Progress: 12100/36650 files (33.0%), 133253 LOC/s
📊 Processing batch 243/733 (50 files)
⏱️  Progress: 12150/36650 files (33.2%), 133593 LOC/s
📊 Processing batch 244/733 (50 files)
⏱️  Progress: 12200/36650 files (33.3%), 133637 LOC/s
📊 Processing batch 245/733 (50 files)
⏱️  Progress: 12250/36650 files (33.4%), 133619 LOC/s
📊 Processing batch 246/733 (50 files)
⏱️  Progress: 12300/36650 files (33.6%), 133432 LOC/s
📊 Processing batch 247/733 (50 files)
⏱️  Progress: 12350/36650 files (33.7%), 133578 LOC/s
📊 Processing batch 248/733 (50 files)
⏱️  Progress: 12400/36650 files (33.8%), 133307 LOC/s
📊 Processing batch 249/733 (50 files)
⏱️  Progress: 12450/36650 files (34.0%), 133408 LOC/s
📊 Processing batch 250/733 (50 files)
⏱️  Progress: 12500/36650 files (34.1%), 136803 LOC/s
📊 Processing batch 251/733 (50 files)
⏱️  Progress: 12550/36650 files (34.2%), 136722 LOC/s
📊 Processing batch 252/733 (50 files)
⏱️  Progress: 12600/36650 files (34.4%), 136752 LOC/s
📊 Processing batch 253/733 (50 files)
⏱️  Progress: 12650/36650 files (34.5%), 136790 LOC/s
📊 Processing batch 254/733 (50 files)
⏱️  Progress: 12700/36650 files (34.7%), 136862 LOC/s
📊 Processing batch 255/733 (50 files)
⏱️  Progress: 12750/36650 files (34.8%), 136886 LOC/s
📊 Processing batch 256/733 (50 files)
⏱️  Progress: 12800/36650 files (34.9%), 136979 LOC/s
📊 Processing batch 257/733 (50 files)
⏱️  Progress: 12850/36650 files (35.1%), 137162 LOC/s
📊 Processing batch 258/733 (50 files)
⏱️  Progress: 12900/36650 files (35.2%), 137265 LOC/s
📊 Processing batch 259/733 (50 files)
⏱️  Progress: 12950/36650 files (35.3%), 137289 LOC/s
📊 Processing batch 260/733 (50 files)
⏱️  Progress: 13000/36650 files (35.5%), 137479 LOC/s
📊 Processing batch 261/733 (50 files)
⏱️  Progress: 13050/36650 files (35.6%), 137624 LOC/s
📊 Processing batch 262/733 (50 files)
⏱️  Progress: 13100/36650 files (35.7%), 137851 LOC/s
📊 Processing batch 263/733 (50 files)
⏱️  Progress: 13150/36650 files (35.9%), 137951 LOC/s
📊 Processing batch 264/733 (50 files)
⏱️  Progress: 13200/36650 files (36.0%), 138028 LOC/s
📊 Processing batch 265/733 (50 files)
⏱️  Progress: 13250/36650 files (36.2%), 138243 LOC/s
📊 Processing batch 266/733 (50 files)
⏱️  Progress: 13300/36650 files (36.3%), 138657 LOC/s
📊 Processing batch 267/733 (50 files)
⏱️  Progress: 13350/36650 files (36.4%), 138888 LOC/s
📊 Processing batch 268/733 (50 files)
⏱️  Progress: 13400/36650 files (36.6%), 139022 LOC/s
📊 Processing batch 269/733 (50 files)
⏱️  Progress: 13450/36650 files (36.7%), 139191 LOC/s
📊 Processing batch 270/733 (50 files)
⏱️  Progress: 13500/36650 files (36.8%), 139325 LOC/s
📊 Processing batch 271/733 (50 files)
⏱️  Progress: 13550/36650 files (37.0%), 139574 LOC/s
📊 Processing batch 272/733 (50 files)
⏱️  Progress: 13600/36650 files (37.1%), 139826 LOC/s
📊 Processing batch 273/733 (50 files)
⏱️  Progress: 13650/36650 files (37.2%), 139900 LOC/s
📊 Processing batch 274/733 (50 files)
⏱️  Progress: 13700/36650 files (37.4%), 139962 LOC/s
📊 Processing batch 275/733 (50 files)
⏱️  Progress: 13750/36650 files (37.5%), 140419 LOC/s
📊 Processing batch 276/733 (50 files)
⏱️  Progress: 13800/36650 files (37.7%), 140672 LOC/s
📊 Processing batch 277/733 (50 files)
⏱️  Progress: 13850/36650 files (37.8%), 140623 LOC/s
📊 Processing batch 278/733 (50 files)
⏱️  Progress: 13900/36650 files (37.9%), 140748 LOC/s
📊 Processing batch 279/733 (50 files)
⏱️  Progress: 13950/36650 files (38.1%), 140926 LOC/s
📊 Processing batch 280/733 (50 files)
⏱️  Progress: 14000/36650 files (38.2%), 141019 LOC/s
📊 Processing batch 281/733 (50 files)
⏱️  Progress: 14050/36650 files (38.3%), 141038 LOC/s
📊 Processing batch 282/733 (50 files)
⏱️  Progress: 14100/36650 files (38.5%), 141232 LOC/s
📊 Processing batch 283/733 (50 files)
⏱️  Progress: 14150/36650 files (38.6%), 141354 LOC/s
📊 Processing batch 284/733 (50 files)
⏱️  Progress: 14200/36650 files (38.7%), 141439 LOC/s
📊 Processing batch 285/733 (50 files)
⏱️  Progress: 14250/36650 files (38.9%), 141470 LOC/s
📊 Processing batch 286/733 (50 files)
⏱️  Progress: 14300/36650 files (39.0%), 141672 LOC/s
📊 Processing batch 287/733 (50 files)
⏱️  Progress: 14350/36650 files (39.2%), 141639 LOC/s
📊 Processing batch 288/733 (50 files)
⏱️  Progress: 14400/36650 files (39.3%), 141755 LOC/s
📊 Processing batch 289/733 (50 files)
⏱️  Progress: 14450/36650 files (39.4%), 141862 LOC/s
📊 Processing batch 290/733 (50 files)
⏱️  Progress: 14500/36650 files (39.6%), 141980 LOC/s
📊 Processing batch 291/733 (50 files)
⏱️  Progress: 14550/36650 files (39.7%), 142016 LOC/s
📊 Processing batch 292/733 (50 files)
⏱️  Progress: 14600/36650 files (39.8%), 142122 LOC/s
📊 Processing batch 293/733 (50 files)
⏱️  Progress: 14650/36650 files (40.0%), 142171 LOC/s
📊 Processing batch 294/733 (50 files)
⏱️  Progress: 14700/36650 files (40.1%), 142239 LOC/s
📊 Processing batch 295/733 (50 files)
⏱️  Progress: 14750/36650 files (40.2%), 142211 LOC/s
📊 Processing batch 296/733 (50 files)
⏱️  Progress: 14800/36650 files (40.4%), 142218 LOC/s
📊 Processing batch 297/733 (50 files)
⏱️  Progress: 14850/36650 files (40.5%), 142679 LOC/s
📊 Processing batch 298/733 (50 files)
⏱️  Progress: 14900/36650 files (40.7%), 142621 LOC/s
📊 Processing batch 299/733 (50 files)
⏱️  Progress: 14950/36650 files (40.8%), 142772 LOC/s
📊 Processing batch 300/733 (50 files)
⏱️  Progress: 15000/36650 files (40.9%), 142789 LOC/s
📊 Processing batch 301/733 (50 files)
⏱️  Progress: 15050/36650 files (41.1%), 142884 LOC/s
📊 Processing batch 302/733 (50 files)
⏱️  Progress: 15100/36650 files (41.2%), 142714 LOC/s
📊 Processing batch 303/733 (50 files)
⏱️  Progress: 15150/36650 files (41.3%), 142875 LOC/s
📊 Processing batch 304/733 (50 files)
⏱️  Progress: 15200/36650 files (41.5%), 143095 LOC/s
📊 Processing batch 305/733 (50 files)
⏱️  Progress: 15250/36650 files (41.6%), 143250 LOC/s
📊 Processing batch 306/733 (50 files)
⏱️  Progress: 15300/36650 files (41.7%), 143515 LOC/s
📊 Processing batch 307/733 (50 files)
⏱️  Progress: 15350/36650 files (41.9%), 143923 LOC/s
📊 Processing batch 308/733 (50 files)
⏱️  Progress: 15400/36650 files (42.0%), 143787 LOC/s
📊 Processing batch 309/733 (50 files)
⏱️  Progress: 15450/36650 files (42.2%), 144022 LOC/s
📊 Processing batch 310/733 (50 files)
⏱️  Progress: 15500/36650 files (42.3%), 144162 LOC/s
📊 Processing batch 311/733 (50 files)
⏱️  Progress: 15550/36650 files (42.4%), 144223 LOC/s
📊 Processing batch 312/733 (50 files)
⏱️  Progress: 15600/36650 files (42.6%), 144291 LOC/s
📊 Processing batch 313/733 (50 files)
⏱️  Progress: 15650/36650 files (42.7%), 144402 LOC/s
📊 Processing batch 314/733 (50 files)
⏱️  Progress: 15700/36650 files (42.8%), 144544 LOC/s
📊 Processing batch 315/733 (50 files)
⏱️  Progress: 15750/36650 files (43.0%), 144486 LOC/s
📊 Processing batch 316/733 (50 files)
⏱️  Progress: 15800/36650 files (43.1%), 144564 LOC/s
📊 Processing batch 317/733 (50 files)
⏱️  Progress: 15850/36650 files (43.2%), 144614 LOC/s
📊 Processing batch 318/733 (50 files)
⏱️  Progress: 15900/36650 files (43.4%), 144667 LOC/s
📊 Processing batch 319/733 (50 files)
⏱️  Progress: 15950/36650 files (43.5%), 144746 LOC/s
📊 Processing batch 320/733 (50 files)

thread '<unknown>' has overflowed its stack
fatal runtime error: stack overflow, aborting
