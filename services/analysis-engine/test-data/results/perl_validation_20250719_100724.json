📊 Running basic performance validation...
🚀 Starting performance validation for: test-data/repositories/perl
📁 Found 401 files to analyze
📊 Processing batch 1/9 (50 files)
⏱️  Progress: 50/401 files (12.5%), 146012 LOC/s
📊 Processing batch 2/9 (50 files)
⏱️  Progress: 100/401 files (24.9%), 263202 LOC/s
📊 Processing batch 3/9 (50 files)
⏱️  Progress: 150/401 files (37.4%), 268542 LOC/s
📊 Processing batch 4/9 (50 files)
⏱️  Progress: 200/401 files (49.9%), 290729 LOC/s
📊 Processing batch 5/9 (50 files)
⏱️  Progress: 250/401 files (62.3%), 362544 LOC/s
📊 Processing batch 6/9 (50 files)
⏱️  Progress: 300/401 files (74.8%), 384111 LOC/s
📊 Processing batch 7/9 (50 files)
⏱️  Progress: 350/401 files (87.3%), 359927 LOC/s
📊 Processing batch 8/9 (50 files)
⏱️  Progress: 400/401 files (99.8%), 355906 LOC/s
📊 Processing batch 9/9 (1 files)
⏱️  Progress: 401/401 files (100.0%), 354118 LOC/s

🎯 PERFORMANCE VALIDATION RESULTS
=====================================
📁 Total Files: 401
📝 Total Lines: 274920
⏱️  Duration: 0.78 seconds
🚀 Lines/Second: 354009
📊 Files/Second: 516.4
✅ Success Rate: 100.0%
💾 Memory Usage: 0.0 MB

📈 LANGUAGE-SPECIFIC PERFORMANCE
====================================
📦 C (248 files, 262613 LOC):
  ✅ Success Rate: 100.0%
  🚀 Throughput: 546786 LOC/s
  📊 Avg File Size: 1059 LOC
📦 BASH (90 files, 8237 LOC):
  ✅ Success Rate: 100.0%
  🚀 Throughput: 47258 LOC/s
  📊 Avg File Size: 92 LOC
📦 XML (34 files, 2429 LOC):
  ✅ Success Rate: 100.0%
  🚀 Throughput: 36889 LOC/s
  📊 Avg File Size: 71 LOC
📦 JSON (22 files, 1403 LOC):
  ✅ Success Rate: 100.0%
  🚀 Throughput: 32930 LOC/s
  📊 Avg File Size: 64 LOC
📦 MARKDOWN (7 files, 238 LOC):
  ✅ Success Rate: 100.0%
  🚀 Throughput: 17556 LOC/s
  📊 Avg File Size: 34 LOC

🎯 1M LOC PERFORMANCE PROJECTION:
Based on current throughput: 2.82 seconds for 1M LOC
✅ PROJECTION: Can likely achieve 1M LOC in under 5 minutes

📊 Running comprehensive analysis...
🚀 Starting comprehensive repository analysis for: test-data/repositories/perl
📁 Found 401 files for comprehensive analysis
📊 Processing comprehensive batch 1/17 (25 files)
⏱️  Comprehensive Progress: 25/401 files (6.2%), 135278 LOC/s, 7613 AST nodes, 384 symbols
📊 Processing comprehensive batch 2/17 (25 files)
⏱️  Comprehensive Progress: 50/401 files (12.5%), 222233 LOC/s, 8991 AST nodes, 1104 symbols
📊 Processing comprehensive batch 3/17 (25 files)
⏱️  Comprehensive Progress: 75/401 files (18.7%), 176129 LOC/s, 164178 AST nodes, 1419 symbols
📊 Processing comprehensive batch 4/17 (25 files)
⏱️  Comprehensive Progress: 100/401 files (24.9%), 179710 LOC/s, 214277 AST nodes, 1693 symbols
📊 Processing comprehensive batch 5/17 (25 files)
⏱️  Comprehensive Progress: 125/401 files (31.2%), 186838 LOC/s, 452108 AST nodes, 2206 symbols
📊 Processing comprehensive batch 6/17 (25 files)
⏱️  Comprehensive Progress: 150/401 files (37.4%), 153795 LOC/s, 596056 AST nodes, 2998 symbols
📊 Processing comprehensive batch 7/17 (25 files)
⏱️  Comprehensive Progress: 175/401 files (43.6%), 157158 LOC/s, 742563 AST nodes, 3416 symbols
📊 Processing comprehensive batch 8/17 (25 files)
⏱️  Comprehensive Progress: 200/401 files (49.9%), 156323 LOC/s, 868174 AST nodes, 3819 symbols
📊 Processing comprehensive batch 9/17 (25 files)
⏱️  Comprehensive Progress: 225/401 files (56.1%), 163342 LOC/s, 1209687 AST nodes, 4594 symbols
📊 Processing comprehensive batch 10/17 (25 files)
⏱️  Comprehensive Progress: 250/401 files (62.3%), 167090 LOC/s, 1455098 AST nodes, 5465 symbols
📊 Processing comprehensive batch 11/17 (25 files)
⏱️  Comprehensive Progress: 275/401 files (68.6%), 173086 LOC/s, 1577528 AST nodes, 5990 symbols
📊 Processing comprehensive batch 12/17 (25 files)
⏱️  Comprehensive Progress: 300/401 files (74.8%), 181045 LOC/s, 1724972 AST nodes, 7103 symbols
📊 Processing comprehensive batch 13/17 (25 files)
⏱️  Comprehensive Progress: 325/401 files (81.0%), 182767 LOC/s, 1786745 AST nodes, 7521 symbols
📊 Processing comprehensive batch 14/17 (25 files)
⏱️  Comprehensive Progress: 350/401 files (87.3%), 183249 LOC/s, 1799720 AST nodes, 7522 symbols
📊 Processing comprehensive batch 15/17 (25 files)
⏱️  Comprehensive Progress: 375/401 files (93.5%), 184033 LOC/s, 1807869 AST nodes, 7522 symbols
📊 Processing comprehensive batch 16/17 (25 files)
⏱️  Comprehensive Progress: 400/401 files (99.8%), 184873 LOC/s, 1877929 AST nodes, 7669 symbols
📊 Processing comprehensive batch 17/17 (1 files)
⏱️  Comprehensive Progress: 401/401 files (100.0%), 184869 LOC/s, 1878632 AST nodes, 7673 symbols

🎯 COMPREHENSIVE ANALYSIS RESULTS
==================================
📁 Files analyzed: 401
📝 Lines processed: 378172
🌳 AST nodes generated: 1878632
🔍 Symbols extracted: 7673
🎯 Patterns detected: 55003
⏱️  Duration: 2.05 seconds
🚀 Lines/Second: 184869
✅ Success Rate: 100.0%

🎯 PERFORMANCE VALIDATION RESULTS
=====================================
📁 Total Files: 401
📝 Total Lines: 378172
⏱️  Duration: 2.05 seconds
🚀 Lines/Second: 184869
📊 Files/Second: 196.0
✅ Success Rate: 100.0%
💾 Memory Usage: 0.0 MB

📈 LANGUAGE-SPECIFIC PERFORMANCE
====================================

🎯 1M LOC PERFORMANCE PROJECTION:
Based on current throughput: 5.41 seconds for 1M LOC
✅ PROJECTION: Can likely achieve 1M LOC in under 5 minutes

📊 Results saved to: performance_results.json
