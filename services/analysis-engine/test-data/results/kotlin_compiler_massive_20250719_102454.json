📊 Running basic performance validation...
🚀 Starting performance validation for: test-data/repositories/kotlin-compiler
📁 Found 6905 files to analyze
📊 Processing batch 1/139 (50 files)
⏱️  Progress: 50/6905 files (0.7%), 41871 LOC/s
📊 Processing batch 2/139 (50 files)
⏱️  Progress: 100/6905 files (1.4%), 53495 LOC/s
📊 Processing batch 3/139 (50 files)
⏱️  Progress: 150/6905 files (2.2%), 116121 LOC/s
📊 Processing batch 4/139 (50 files)
⏱️  Progress: 200/6905 files (2.9%), 135539 LOC/s
📊 Processing batch 5/139 (50 files)
⏱️  Progress: 250/6905 files (3.6%), 141889 LOC/s
📊 Processing batch 6/139 (50 files)
⏱️  Progress: 300/6905 files (4.3%), 152660 LOC/s
📊 Processing batch 7/139 (50 files)
⏱️  Progress: 350/6905 files (5.1%), 157427 LOC/s
📊 Processing batch 8/139 (50 files)
⏱️  Progress: 400/6905 files (5.8%), 165591 LOC/s
📊 Processing batch 9/139 (50 files)
⏱️  Progress: 450/6905 files (6.5%), 169337 LOC/s
📊 Processing batch 10/139 (50 files)
⏱️  Progress: 500/6905 files (7.2%), 195429 LOC/s
📊 Processing batch 11/139 (50 files)
⏱️  Progress: 550/6905 files (8.0%), 207578 LOC/s
📊 Processing batch 12/139 (50 files)
⏱️  Progress: 600/6905 files (8.7%), 345294 LOC/s
📊 Processing batch 13/139 (50 files)
⏱️  Progress: 650/6905 files (9.4%), 310084 LOC/s
📊 Processing batch 14/139 (50 files)
⏱️  Progress: 700/6905 files (10.1%), 304506 LOC/s
📊 Processing batch 15/139 (50 files)
⏱️  Progress: 750/6905 files (10.9%), 305849 LOC/s
📊 Processing batch 16/139 (50 files)
⏱️  Progress: 800/6905 files (11.6%), 305611 LOC/s
📊 Processing batch 17/139 (50 files)
⏱️  Progress: 850/6905 files (12.3%), 307017 LOC/s
📊 Processing batch 18/139 (50 files)
⏱️  Progress: 900/6905 files (13.0%), 310849 LOC/s
📊 Processing batch 19/139 (50 files)
⏱️  Progress: 950/6905 files (13.8%), 300820 LOC/s
📊 Processing batch 20/139 (50 files)
⏱️  Progress: 1000/6905 files (14.5%), 298586 LOC/s
📊 Processing batch 21/139 (50 files)
⏱️  Progress: 1050/6905 files (15.2%), 297936 LOC/s
📊 Processing batch 22/139 (50 files)
⏱️  Progress: 1100/6905 files (15.9%), 297723 LOC/s
📊 Processing batch 23/139 (50 files)
⏱️  Progress: 1150/6905 files (16.7%), 292910 LOC/s
📊 Processing batch 24/139 (50 files)
⏱️  Progress: 1200/6905 files (17.4%), 290855 LOC/s
📊 Processing batch 25/139 (50 files)
⏱️  Progress: 1250/6905 files (18.1%), 289297 LOC/s
📊 Processing batch 26/139 (50 files)
⏱️  Progress: 1300/6905 files (18.8%), 291911 LOC/s
📊 Processing batch 27/139 (50 files)
⏱️  Progress: 1350/6905 files (19.6%), 381384 LOC/s
📊 Processing batch 28/139 (50 files)
⏱️  Progress: 1400/6905 files (20.3%), 363075 LOC/s
📊 Processing batch 29/139 (50 files)
⏱️  Progress: 1450/6905 files (21.0%), 363735 LOC/s
📊 Processing batch 30/139 (50 files)
⏱️  Progress: 1500/6905 files (21.7%), 345372 LOC/s
📊 Processing batch 31/139 (50 files)
⏱️  Progress: 1550/6905 files (22.4%), 342762 LOC/s
📊 Processing batch 32/139 (50 files)
⏱️  Progress: 1600/6905 files (23.2%), 342324 LOC/s
📊 Processing batch 33/139 (50 files)
⏱️  Progress: 1650/6905 files (23.9%), 368731 LOC/s
📊 Processing batch 34/139 (50 files)
⏱️  Progress: 1700/6905 files (24.6%), 383332 LOC/s
📊 Processing batch 35/139 (50 files)
⏱️  Progress: 1750/6905 files (25.3%), 375695 LOC/s
📊 Processing batch 36/139 (50 files)
⏱️  Progress: 1800/6905 files (26.1%), 375391 LOC/s
📊 Processing batch 37/139 (50 files)
⏱️  Progress: 1850/6905 files (26.8%), 375247 LOC/s
📊 Processing batch 38/139 (50 files)
⏱️  Progress: 1900/6905 files (27.5%), 374149 LOC/s
📊 Processing batch 39/139 (50 files)
⏱️  Progress: 1950/6905 files (28.2%), 374049 LOC/s
📊 Processing batch 40/139 (50 files)
⏱️  Progress: 2000/6905 files (29.0%), 373764 LOC/s
📊 Processing batch 41/139 (50 files)
⏱️  Progress: 2050/6905 files (29.7%), 373828 LOC/s
📊 Processing batch 42/139 (50 files)
⏱️  Progress: 2100/6905 files (30.4%), 373814 LOC/s
📊 Processing batch 43/139 (50 files)
⏱️  Progress: 2150/6905 files (31.1%), 385714 LOC/s
📊 Processing batch 44/139 (50 files)
⏱️  Progress: 2200/6905 files (31.9%), 380948 LOC/s
📊 Processing batch 45/139 (50 files)
⏱️  Progress: 2250/6905 files (32.6%), 381526 LOC/s
📊 Processing batch 46/139 (50 files)
⏱️  Progress: 2300/6905 files (33.3%), 381186 LOC/s
📊 Processing batch 47/139 (50 files)
⏱️  Progress: 2350/6905 files (34.0%), 381200 LOC/s
📊 Processing batch 48/139 (50 files)
⏱️  Progress: 2400/6905 files (34.8%), 380711 LOC/s
📊 Processing batch 49/139 (50 files)
⏱️  Progress: 2450/6905 files (35.5%), 378886 LOC/s
📊 Processing batch 50/139 (50 files)
⏱️  Progress: 2500/6905 files (36.2%), 377378 LOC/s
📊 Processing batch 51/139 (50 files)
⏱️  Progress: 2550/6905 files (36.9%), 377015 LOC/s
📊 Processing batch 52/139 (50 files)
⏱️  Progress: 2600/6905 files (37.7%), 375131 LOC/s
📊 Processing batch 53/139 (50 files)
⏱️  Progress: 2650/6905 files (38.4%), 374478 LOC/s
📊 Processing batch 54/139 (50 files)
⏱️  Progress: 2700/6905 files (39.1%), 374371 LOC/s
📊 Processing batch 55/139 (50 files)
⏱️  Progress: 2750/6905 files (39.8%), 374250 LOC/s
📊 Processing batch 56/139 (50 files)
⏱️  Progress: 2800/6905 files (40.6%), 374997 LOC/s
📊 Processing batch 57/139 (50 files)
⏱️  Progress: 2850/6905 files (41.3%), 375710 LOC/s
📊 Processing batch 58/139 (50 files)
⏱️  Progress: 2900/6905 files (42.0%), 374704 LOC/s
📊 Processing batch 59/139 (50 files)
⏱️  Progress: 2950/6905 files (42.7%), 374787 LOC/s
📊 Processing batch 60/139 (50 files)
⏱️  Progress: 3000/6905 files (43.4%), 374759 LOC/s
📊 Processing batch 61/139 (50 files)
⏱️  Progress: 3050/6905 files (44.2%), 374694 LOC/s
📊 Processing batch 62/139 (50 files)
⏱️  Progress: 3100/6905 files (44.9%), 374728 LOC/s
📊 Processing batch 63/139 (50 files)
⏱️  Progress: 3150/6905 files (45.6%), 374667 LOC/s
📊 Processing batch 64/139 (50 files)
⏱️  Progress: 3200/6905 files (46.3%), 374295 LOC/s
📊 Processing batch 65/139 (50 files)
⏱️  Progress: 3250/6905 files (47.1%), 374995 LOC/s
📊 Processing batch 66/139 (50 files)
⏱️  Progress: 3300/6905 files (47.8%), 374793 LOC/s
📊 Processing batch 67/139 (50 files)
⏱️  Progress: 3350/6905 files (48.5%), 376488 LOC/s
📊 Processing batch 68/139 (50 files)
⏱️  Progress: 3400/6905 files (49.2%), 375641 LOC/s
📊 Processing batch 69/139 (50 files)
⏱️  Progress: 3450/6905 files (50.0%), 375950 LOC/s
📊 Processing batch 70/139 (50 files)
⏱️  Progress: 3500/6905 files (50.7%), 376211 LOC/s
📊 Processing batch 71/139 (50 files)
⏱️  Progress: 3550/6905 files (51.4%), 375377 LOC/s
📊 Processing batch 72/139 (50 files)
⏱️  Progress: 3600/6905 files (52.1%), 380467 LOC/s
📊 Processing batch 73/139 (50 files)
⏱️  Progress: 3650/6905 files (52.9%), 380024 LOC/s
📊 Processing batch 74/139 (50 files)
⏱️  Progress: 3700/6905 files (53.6%), 380141 LOC/s
📊 Processing batch 75/139 (50 files)
⏱️  Progress: 3750/6905 files (54.3%), 380055 LOC/s
📊 Processing batch 76/139 (50 files)
⏱️  Progress: 3800/6905 files (55.0%), 380130 LOC/s
📊 Processing batch 77/139 (50 files)
⏱️  Progress: 3850/6905 files (55.8%), 379890 LOC/s
📊 Processing batch 78/139 (50 files)
⏱️  Progress: 3900/6905 files (56.5%), 379527 LOC/s
📊 Processing batch 79/139 (50 files)
⏱️  Progress: 3950/6905 files (57.2%), 379449 LOC/s
📊 Processing batch 80/139 (50 files)
⏱️  Progress: 4000/6905 files (57.9%), 379375 LOC/s
📊 Processing batch 81/139 (50 files)
⏱️  Progress: 4050/6905 files (58.7%), 379354 LOC/s
📊 Processing batch 82/139 (50 files)
⏱️  Progress: 4100/6905 files (59.4%), 379276 LOC/s
📊 Processing batch 83/139 (50 files)
⏱️  Progress: 4150/6905 files (60.1%), 379186 LOC/s
📊 Processing batch 84/139 (50 files)
⏱️  Progress: 4200/6905 files (60.8%), 378131 LOC/s
📊 Processing batch 85/139 (50 files)
⏱️  Progress: 4250/6905 files (61.5%), 377811 LOC/s
📊 Processing batch 86/139 (50 files)
⏱️  Progress: 4300/6905 files (62.3%), 377580 LOC/s
📊 Processing batch 87/139 (50 files)
⏱️  Progress: 4350/6905 files (63.0%), 377402 LOC/s
📊 Processing batch 88/139 (50 files)
⏱️  Progress: 4400/6905 files (63.7%), 377253 LOC/s
📊 Processing batch 89/139 (50 files)
⏱️  Progress: 4450/6905 files (64.4%), 377006 LOC/s
📊 Processing batch 90/139 (50 files)
⏱️  Progress: 4500/6905 files (65.2%), 376849 LOC/s
📊 Processing batch 91/139 (50 files)
⏱️  Progress: 4550/6905 files (65.9%), 376657 LOC/s
📊 Processing batch 92/139 (50 files)
⏱️  Progress: 4600/6905 files (66.6%), 376505 LOC/s
📊 Processing batch 93/139 (50 files)
⏱️  Progress: 4650/6905 files (67.3%), 376368 LOC/s
📊 Processing batch 94/139 (50 files)
⏱️  Progress: 4700/6905 files (68.1%), 375992 LOC/s
📊 Processing batch 95/139 (50 files)
⏱️  Progress: 4750/6905 files (68.8%), 375547 LOC/s
📊 Processing batch 96/139 (50 files)
⏱️  Progress: 4800/6905 files (69.5%), 374854 LOC/s
📊 Processing batch 97/139 (50 files)
⏱️  Progress: 4850/6905 files (70.2%), 374408 LOC/s
📊 Processing batch 98/139 (50 files)
⏱️  Progress: 4900/6905 files (71.0%), 374064 LOC/s
📊 Processing batch 99/139 (50 files)
⏱️  Progress: 4950/6905 files (71.7%), 373710 LOC/s
📊 Processing batch 100/139 (50 files)
⏱️  Progress: 5000/6905 files (72.4%), 373452 LOC/s
📊 Processing batch 101/139 (50 files)
⏱️  Progress: 5050/6905 files (73.1%), 372414 LOC/s
📊 Processing batch 102/139 (50 files)
⏱️  Progress: 5100/6905 files (73.9%), 372199 LOC/s
📊 Processing batch 103/139 (50 files)
⏱️  Progress: 5150/6905 files (74.6%), 371948 LOC/s
📊 Processing batch 104/139 (50 files)
⏱️  Progress: 5200/6905 files (75.3%), 371742 LOC/s
📊 Processing batch 105/139 (50 files)
⏱️  Progress: 5250/6905 files (76.0%), 371524 LOC/s
📊 Processing batch 106/139 (50 files)
⏱️  Progress: 5300/6905 files (76.8%), 371236 LOC/s
📊 Processing batch 107/139 (50 files)
⏱️  Progress: 5350/6905 files (77.5%), 371125 LOC/s
📊 Processing batch 108/139 (50 files)
⏱️  Progress: 5400/6905 files (78.2%), 371031 LOC/s
📊 Processing batch 109/139 (50 files)
⏱️  Progress: 5450/6905 files (78.9%), 371082 LOC/s
📊 Processing batch 110/139 (50 files)
⏱️  Progress: 5500/6905 files (79.7%), 371000 LOC/s
📊 Processing batch 111/139 (50 files)
⏱️  Progress: 5550/6905 files (80.4%), 370942 LOC/s
📊 Processing batch 112/139 (50 files)
⏱️  Progress: 5600/6905 files (81.1%), 370863 LOC/s
📊 Processing batch 113/139 (50 files)
⏱️  Progress: 5650/6905 files (81.8%), 370963 LOC/s
📊 Processing batch 114/139 (50 files)
⏱️  Progress: 5700/6905 files (82.5%), 408993 LOC/s
📊 Processing batch 115/139 (50 files)
⏱️  Progress: 5750/6905 files (83.3%), 398979 LOC/s
📊 Processing batch 116/139 (50 files)
⏱️  Progress: 5800/6905 files (84.0%), 398602 LOC/s
📊 Processing batch 117/139 (50 files)
⏱️  Progress: 5850/6905 files (84.7%), 398146 LOC/s
📊 Processing batch 118/139 (50 files)
⏱️  Progress: 5900/6905 files (85.4%), 398388 LOC/s
📊 Processing batch 119/139 (50 files)
⏱️  Progress: 5950/6905 files (86.2%), 398561 LOC/s
📊 Processing batch 120/139 (50 files)
⏱️  Progress: 6000/6905 files (86.9%), 398275 LOC/s
📊 Processing batch 121/139 (50 files)
⏱️  Progress: 6050/6905 files (87.6%), 398325 LOC/s
📊 Processing batch 122/139 (50 files)
⏱️  Progress: 6100/6905 files (88.3%), 421420 LOC/s
📊 Processing batch 123/139 (50 files)
⏱️  Progress: 6150/6905 files (89.1%), 407431 LOC/s
📊 Processing batch 124/139 (50 files)
⏱️  Progress: 6200/6905 files (89.8%), 405461 LOC/s
📊 Processing batch 125/139 (50 files)
⏱️  Progress: 6250/6905 files (90.5%), 407411 LOC/s
📊 Processing batch 126/139 (50 files)
⏱️  Progress: 6300/6905 files (91.2%), 406709 LOC/s
📊 Processing batch 127/139 (50 files)
⏱️  Progress: 6350/6905 files (92.0%), 407004 LOC/s
📊 Processing batch 128/139 (50 files)
⏱️  Progress: 6400/6905 files (92.7%), 407506 LOC/s
📊 Processing batch 129/139 (50 files)
⏱️  Progress: 6450/6905 files (93.4%), 408072 LOC/s
📊 Processing batch 130/139 (50 files)
⏱️  Progress: 6500/6905 files (94.1%), 407343 LOC/s
📊 Processing batch 131/139 (50 files)
⏱️  Progress: 6550/6905 files (94.9%), 406628 LOC/s
📊 Processing batch 132/139 (50 files)
⏱️  Progress: 6600/6905 files (95.6%), 406572 LOC/s
📊 Processing batch 133/139 (50 files)
⏱️  Progress: 6650/6905 files (96.3%), 406575 LOC/s
📊 Processing batch 134/139 (50 files)
⏱️  Progress: 6700/6905 files (97.0%), 405877 LOC/s
📊 Processing batch 135/139 (50 files)
⏱️  Progress: 6750/6905 files (97.8%), 406172 LOC/s
📊 Processing batch 136/139 (50 files)
⏱️  Progress: 6800/6905 files (98.5%), 402284 LOC/s
📊 Processing batch 137/139 (50 files)
⏱️  Progress: 6850/6905 files (99.2%), 400550 LOC/s
📊 Processing batch 138/139 (50 files)
⏱️  Progress: 6900/6905 files (99.9%), 400757 LOC/s
📊 Processing batch 139/139 (5 files)
⏱️  Progress: 6905/6905 files (100.0%), 400726 LOC/s

🎯 PERFORMANCE VALIDATION RESULTS
=====================================
📁 Total Files: 6905
📝 Total Lines: 2757319
⏱️  Duration: 6.88 seconds
🚀 Lines/Second: 400725
📊 Files/Second: 1003.5
✅ Success Rate: 100.0%
💾 Memory Usage: 0.0 MB

📈 LANGUAGE-SPECIFIC PERFORMANCE
====================================
📦 JAVA (4523 files, 2514962 LOC):
  ✅ Success Rate: 100.0%
  🚀 Throughput: 557993 LOC/s
  📊 Avg File Size: 556 LOC
📦 C (530 files, 68224 LOC):
  ✅ Success Rate: 100.0%
  🚀 Throughput: 129177 LOC/s
  📊 Avg File Size: 129 LOC
📦 JSON (281 files, 60092 LOC):
  ✅ Success Rate: 100.0%
  🚀 Throughput: 214602 LOC/s
  📊 Avg File Size: 214 LOC
📦 CPP (341 files, 40979 LOC):
  ✅ Success Rate: 100.0%
  🚀 Throughput: 120595 LOC/s
  📊 Avg File Size: 120 LOC
📦 MARKDOWN (254 files, 33612 LOC):
  ✅ Success Rate: 100.0%
  🚀 Throughput: 132796 LOC/s
  📊 Avg File Size: 132 LOC
📦 XML (529 files, 26227 LOC):
  ✅ Success Rate: 100.0%
  🚀 Throughput: 49753 LOC/s
  📊 Avg File Size: 50 LOC
📦 TYPESCRIPT (135 files, 5721 LOC):
  ✅ Success Rate: 100.0%
  🚀 Throughput: 42527 LOC/s
  📊 Avg File Size: 42 LOC
📦 JAVASCRIPT (268 files, 5106 LOC):
  ✅ Success Rate: 100.0%
  🚀 Throughput: 19119 LOC/s
  📊 Avg File Size: 19 LOC
📦 PYTHON (3 files, 1248 LOC):
  ✅ Success Rate: 100.0%
  🚀 Throughput: 417462 LOC/s
  📊 Avg File Size: 416 LOC
📦 BASH (24 files, 651 LOC):
  ✅ Success Rate: 100.0%
  🚀 Throughput: 27220 LOC/s
  📊 Avg File Size: 27 LOC
📦 CSS (5 files, 240 LOC):
  ✅ Success Rate: 100.0%
  🚀 Throughput: 48169 LOC/s
  📊 Avg File Size: 48 LOC
📦 TOML (3 files, 158 LOC):
  ✅ Success Rate: 100.0%
  🚀 Throughput: 52852 LOC/s
  📊 Avg File Size: 53 LOC
📦 HTML (8 files, 94 LOC):
  ✅ Success Rate: 100.0%
  🚀 Throughput: 11791 LOC/s
  📊 Avg File Size: 12 LOC
📦 SCALA (1 files, 5 LOC):
  ✅ Success Rate: 100.0%
  🚀 Throughput: 5018 LOC/s
  📊 Avg File Size: 5 LOC

🎯 1M LOC PERFORMANCE CLAIM VALIDATION:
Target: 1000000 LOC in 300 seconds
Actual: 2757319 LOC in 6.88 seconds
Projected 1M LOC time: 2.50 seconds
✅ CLAIM VALIDATED: Can process 1M LOC in under 5 minutes!
🚀 Performance margin: 120.2x faster than required

📊 Running comprehensive analysis...
🚀 Starting comprehensive repository analysis for: test-data/repositories/kotlin-compiler
📁 Found 6905 files for comprehensive analysis
📊 Processing comprehensive batch 1/277 (25 files)
⏱️  Comprehensive Progress: 25/6905 files (0.4%), 32859 LOC/s, 99815 AST nodes, 2484 symbols
📊 Processing comprehensive batch 2/277 (25 files)
⏱️  Comprehensive Progress: 50/6905 files (0.7%), 41562 LOC/s, 141442 AST nodes, 3432 symbols
📊 Processing comprehensive batch 3/277 (25 files)
⏱️  Comprehensive Progress: 75/6905 files (1.1%), 49070 LOC/s, 173170 AST nodes, 4360 symbols
📊 Processing comprehensive batch 4/277 (25 files)
⏱️  Comprehensive Progress: 100/6905 files (1.4%), 51730 LOC/s, 181822 AST nodes, 4670 symbols
📊 Processing comprehensive batch 5/277 (25 files)
⏱️  Comprehensive Progress: 125/6905 files (1.8%), 54871 LOC/s, 199490 AST nodes, 5162 symbols
📊 Processing comprehensive batch 6/277 (25 files)
⏱️  Comprehensive Progress: 150/6905 files (2.2%), 100484 LOC/s, 551472 AST nodes, 13861 symbols
📊 Processing comprehensive batch 7/277 (25 files)
⏱️  Comprehensive Progress: 175/6905 files (2.5%), 107221 LOC/s, 614922 AST nodes, 15573 symbols
📊 Processing comprehensive batch 8/277 (25 files)
⏱️  Comprehensive Progress: 200/6905 files (2.9%), 112395 LOC/s, 679129 AST nodes, 17212 symbols
📊 Processing comprehensive batch 9/277 (25 files)
⏱️  Comprehensive Progress: 225/6905 files (3.3%), 113874 LOC/s, 705830 AST nodes, 17665 symbols
📊 Processing comprehensive batch 10/277 (25 files)
⏱️  Comprehensive Progress: 250/6905 files (3.6%), 115603 LOC/s, 736081 AST nodes, 18217 symbols
📊 Processing comprehensive batch 11/277 (25 files)
⏱️  Comprehensive Progress: 275/6905 files (4.0%), 118497 LOC/s, 779195 AST nodes, 19138 symbols
📊 Processing comprehensive batch 12/277 (25 files)
⏱️  Comprehensive Progress: 300/6905 files (4.3%), 120469 LOC/s, 813152 AST nodes, 19804 symbols
📊 Processing comprehensive batch 13/277 (25 files)
⏱️  Comprehensive Progress: 325/6905 files (4.7%), 121874 LOC/s, 842423 AST nodes, 20330 symbols
📊 Processing comprehensive batch 14/277 (25 files)
⏱️  Comprehensive Progress: 350/6905 files (5.1%), 122556 LOC/s, 862623 AST nodes, 20605 symbols
📊 Processing comprehensive batch 15/277 (25 files)
⏱️  Comprehensive Progress: 375/6905 files (5.4%), 123587 LOC/s, 889501 AST nodes, 21054 symbols
📊 Processing comprehensive batch 16/277 (25 files)
⏱️  Comprehensive Progress: 400/6905 files (5.8%), 125753 LOC/s, 931859 AST nodes, 21951 symbols
📊 Processing comprehensive batch 17/277 (25 files)
⏱️  Comprehensive Progress: 425/6905 files (6.2%), 126224 LOC/s, 951362 AST nodes, 22199 symbols
📊 Processing comprehensive batch 18/277 (25 files)
⏱️  Comprehensive Progress: 450/6905 files (6.5%), 127058 LOC/s, 975801 AST nodes, 22591 symbols
📊 Processing comprehensive batch 19/277 (25 files)
⏱️  Comprehensive Progress: 475/6905 files (6.9%), 127737 LOC/s, 998991 AST nodes, 22944 symbols
📊 Processing comprehensive batch 20/277 (25 files)
⏱️  Comprehensive Progress: 500/6905 files (7.2%), 136332 LOC/s, 1195796 AST nodes, 27940 symbols
📊 Processing comprehensive batch 21/277 (25 files)
⏱️  Comprehensive Progress: 525/6905 files (7.6%), 139848 LOC/s, 1291156 AST nodes, 30231 symbols
📊 Processing comprehensive batch 22/277 (25 files)
⏱️  Comprehensive Progress: 550/6905 files (8.0%), 142544 LOC/s, 1380300 AST nodes, 32372 symbols
📊 Processing comprehensive batch 23/277 (25 files)
⏱️  Comprehensive Progress: 575/6905 files (8.3%), 145935 LOC/s, 1472495 AST nodes, 34805 symbols
📊 Processing comprehensive batch 24/277 (25 files)
⏱️  Comprehensive Progress: 600/6905 files (8.7%), 177263 LOC/s, 3145821 AST nodes, 80064 symbols
📊 Processing comprehensive batch 25/277 (25 files)
⏱️  Comprehensive Progress: 625/6905 files (9.1%), 181675 LOC/s, 3712985 AST nodes, 95071 symbols
📊 Processing comprehensive batch 26/277 (25 files)
⏱️  Comprehensive Progress: 650/6905 files (9.4%), 181929 LOC/s, 3755484 AST nodes, 96117 symbols
📊 Processing comprehensive batch 27/277 (25 files)
⏱️  Comprehensive Progress: 675/6905 files (9.8%), 181932 LOC/s, 3756547 AST nodes, 96117 symbols
📊 Processing comprehensive batch 28/277 (25 files)
⏱️  Comprehensive Progress: 700/6905 files (10.1%), 181960 LOC/s, 3757532 AST nodes, 96124 symbols
📊 Processing comprehensive batch 29/277 (25 files)
⏱️  Comprehensive Progress: 725/6905 files (10.5%), 182080 LOC/s, 3792726 AST nodes, 96883 symbols
📊 Processing comprehensive batch 30/277 (25 files)
⏱️  Comprehensive Progress: 750/6905 files (10.9%), 182412 LOC/s, 3838010 AST nodes, 97864 symbols
📊 Processing comprehensive batch 31/277 (25 files)
⏱️  Comprehensive Progress: 775/6905 files (11.2%), 182603 LOC/s, 3878252 AST nodes, 98699 symbols
📊 Processing comprehensive batch 32/277 (25 files)
⏱️  Comprehensive Progress: 800/6905 files (11.6%), 182986 LOC/s, 3947877 AST nodes, 100323 symbols
📊 Processing comprehensive batch 33/277 (25 files)
⏱️  Comprehensive Progress: 825/6905 files (11.9%), 183682 LOC/s, 4060491 AST nodes, 103149 symbols
📊 Processing comprehensive batch 34/277 (25 files)
⏱️  Comprehensive Progress: 850/6905 files (12.3%), 184045 LOC/s, 4109964 AST nodes, 104255 symbols
📊 Processing comprehensive batch 35/277 (25 files)
⏱️  Comprehensive Progress: 875/6905 files (12.7%), 185014 LOC/s, 4273185 AST nodes, 108454 symbols
📊 Processing comprehensive batch 36/277 (25 files)
⏱️  Comprehensive Progress: 900/6905 files (13.0%), 181853 LOC/s, 4325316 AST nodes, 108928 symbols
📊 Processing comprehensive batch 37/277 (25 files)
⏱️  Comprehensive Progress: 925/6905 files (13.4%), 177568 LOC/s, 4345601 AST nodes, 108928 symbols
📊 Processing comprehensive batch 38/277 (25 files)
⏱️  Comprehensive Progress: 950/6905 files (13.8%), 177114 LOC/s, 4355639 AST nodes, 108928 symbols
📊 Processing comprehensive batch 39/277 (25 files)
⏱️  Comprehensive Progress: 975/6905 files (14.1%), 176762 LOC/s, 4363007 AST nodes, 108928 symbols
📊 Processing comprehensive batch 40/277 (25 files)
⏱️  Comprehensive Progress: 1000/6905 files (14.5%), 176432 LOC/s, 4370761 AST nodes, 108928 symbols
📊 Processing comprehensive batch 41/277 (25 files)
⏱️  Comprehensive Progress: 1025/6905 files (14.8%), 175940 LOC/s, 4378744 AST nodes, 108928 symbols
📊 Processing comprehensive batch 42/277 (25 files)
⏱️  Comprehensive Progress: 1050/6905 files (15.2%), 175582 LOC/s, 4385821 AST nodes, 108984 symbols
📊 Processing comprehensive batch 43/277 (25 files)
⏱️  Comprehensive Progress: 1075/6905 files (15.6%), 175413 LOC/s, 4394036 AST nodes, 109009 symbols
📊 Processing comprehensive batch 44/277 (25 files)
⏱️  Comprehensive Progress: 1100/6905 files (15.9%), 175185 LOC/s, 4401247 AST nodes, 109009 symbols
📊 Processing comprehensive batch 45/277 (25 files)
⏱️  Comprehensive Progress: 1125/6905 files (16.3%), 174781 LOC/s, 4421054 AST nodes, 109111 symbols
📊 Processing comprehensive batch 46/277 (25 files)
⏱️  Comprehensive Progress: 1150/6905 files (16.7%), 169287 LOC/s, 4423638 AST nodes, 109165 symbols
📊 Processing comprehensive batch 47/277 (25 files)
⏱️  Comprehensive Progress: 1175/6905 files (17.0%), 169132 LOC/s, 4425691 AST nodes, 109180 symbols
📊 Processing comprehensive batch 48/277 (25 files)
⏱️  Comprehensive Progress: 1200/6905 files (17.4%), 168870 LOC/s, 4435449 AST nodes, 109478 symbols
📊 Processing comprehensive batch 49/277 (25 files)
⏱️  Comprehensive Progress: 1225/6905 files (17.7%), 168770 LOC/s, 4438725 AST nodes, 109494 symbols
📊 Processing comprehensive batch 50/277 (25 files)
⏱️  Comprehensive Progress: 1250/6905 files (18.1%), 168740 LOC/s, 4438997 AST nodes, 109496 symbols
📊 Processing comprehensive batch 51/277 (25 files)
⏱️  Comprehensive Progress: 1275/6905 files (18.5%), 168713 LOC/s, 4442966 AST nodes, 109561 symbols
📊 Processing comprehensive batch 52/277 (25 files)
⏱️  Comprehensive Progress: 1300/6905 files (18.8%), 169097 LOC/s, 4513947 AST nodes, 111261 symbols
📊 Processing comprehensive batch 53/277 (25 files)
⏱️  Comprehensive Progress: 1325/6905 files (19.2%), 172692 LOC/s, 5604995 AST nodes, 139601 symbols
📊 Processing comprehensive batch 54/277 (25 files)
⏱️  Comprehensive Progress: 1350/6905 files (19.6%), 176210 LOC/s, 7070604 AST nodes, 178049 symbols
📊 Processing comprehensive batch 55/277 (25 files)
⏱️  Comprehensive Progress: 1375/6905 files (19.9%), 176259 LOC/s, 7095939 AST nodes, 178681 symbols
📊 Processing comprehensive batch 56/277 (25 files)
⏱️  Comprehensive Progress: 1400/6905 files (20.3%), 176361 LOC/s, 7126614 AST nodes, 179510 symbols
📊 Processing comprehensive batch 57/277 (25 files)
⏱️  Comprehensive Progress: 1425/6905 files (20.6%), 176472 LOC/s, 7154331 AST nodes, 180185 symbols
📊 Processing comprehensive batch 58/277 (25 files)
⏱️  Comprehensive Progress: 1450/6905 files (21.0%), 176569 LOC/s, 7162338 AST nodes, 180497 symbols
📊 Processing comprehensive batch 59/277 (25 files)
⏱️  Comprehensive Progress: 1475/6905 files (21.4%), 176608 LOC/s, 7172334 AST nodes, 180795 symbols
📊 Processing comprehensive batch 60/277 (25 files)
⏱️  Comprehensive Progress: 1500/6905 files (21.7%), 175248 LOC/s, 7628893 AST nodes, 190836 symbols
📊 Processing comprehensive batch 61/277 (25 files)
⏱️  Comprehensive Progress: 1525/6905 files (22.1%), 175236 LOC/s, 7655574 AST nodes, 191631 symbols
📊 Processing comprehensive batch 62/277 (25 files)
⏱️  Comprehensive Progress: 1550/6905 files (22.4%), 175244 LOC/s, 7666044 AST nodes, 191924 symbols
📊 Processing comprehensive batch 63/277 (25 files)
⏱️  Comprehensive Progress: 1575/6905 files (22.8%), 175271 LOC/s, 7676716 AST nodes, 192297 symbols
📊 Processing comprehensive batch 64/277 (25 files)
⏱️  Comprehensive Progress: 1600/6905 files (23.2%), 175226 LOC/s, 7707337 AST nodes, 193151 symbols
📊 Processing comprehensive batch 65/277 (25 files)
⏱️  Comprehensive Progress: 1625/6905 files (23.5%), 176279 LOC/s, 8167516 AST nodes, 205392 symbols
📊 Processing comprehensive batch 66/277 (25 files)
⏱️  Comprehensive Progress: 1650/6905 files (23.9%), 179177 LOC/s, 9187828 AST nodes, 232235 symbols
📊 Processing comprehensive batch 67/277 (25 files)
⏱️  Comprehensive Progress: 1675/6905 files (24.3%), 182195 LOC/s, 10694373 AST nodes, 271502 symbols
📊 Processing comprehensive batch 68/277 (25 files)
⏱️  Comprehensive Progress: 1700/6905 files (24.6%), 182173 LOC/s, 10698847 AST nodes, 271584 symbols
📊 Processing comprehensive batch 69/277 (25 files)
⏱️  Comprehensive Progress: 1725/6905 files (25.0%), 182161 LOC/s, 10699994 AST nodes, 271621 symbols
📊 Processing comprehensive batch 70/277 (25 files)
⏱️  Comprehensive Progress: 1750/6905 files (25.3%), 182155 LOC/s, 10702363 AST nodes, 271653 symbols
📊 Processing comprehensive batch 71/277 (25 files)
⏱️  Comprehensive Progress: 1775/6905 files (25.7%), 182139 LOC/s, 10705362 AST nodes, 271717 symbols
📊 Processing comprehensive batch 72/277 (25 files)
⏱️  Comprehensive Progress: 1800/6905 files (26.1%), 182111 LOC/s, 10709759 AST nodes, 271785 symbols
📊 Processing comprehensive batch 73/277 (25 files)
⏱️  Comprehensive Progress: 1825/6905 files (26.4%), 182016 LOC/s, 10724578 AST nodes, 271915 symbols
📊 Processing comprehensive batch 74/277 (25 files)
⏱️  Comprehensive Progress: 1850/6905 files (26.8%), 181963 LOC/s, 10733062 AST nodes, 271997 symbols
📊 Processing comprehensive batch 75/277 (25 files)
⏱️  Comprehensive Progress: 1875/6905 files (27.2%), 181919 LOC/s, 10744496 AST nodes, 272145 symbols
📊 Processing comprehensive batch 76/277 (25 files)
⏱️  Comprehensive Progress: 1900/6905 files (27.5%), 181883 LOC/s, 10752041 AST nodes, 272243 symbols
📊 Processing comprehensive batch 77/277 (25 files)
⏱️  Comprehensive Progress: 1925/6905 files (27.9%), 181835 LOC/s, 10762260 AST nodes, 272361 symbols
📊 Processing comprehensive batch 78/277 (25 files)
⏱️  Comprehensive Progress: 1950/6905 files (28.2%), 181764 LOC/s, 10776864 AST nodes, 272502 symbols
📊 Processing comprehensive batch 79/277 (25 files)
⏱️  Comprehensive Progress: 1975/6905 files (28.6%), 181729 LOC/s, 10782073 AST nodes, 272529 symbols
📊 Processing comprehensive batch 80/277 (25 files)
⏱️  Comprehensive Progress: 2000/6905 files (29.0%), 181775 LOC/s, 10791707 AST nodes, 272632 symbols
📊 Processing comprehensive batch 81/277 (25 files)
⏱️  Comprehensive Progress: 2025/6905 files (29.3%), 181772 LOC/s, 10799970 AST nodes, 272922 symbols
📊 Processing comprehensive batch 82/277 (25 files)
⏱️  Comprehensive Progress: 2050/6905 files (29.7%), 181765 LOC/s, 10803189 AST nodes, 273021 symbols
📊 Processing comprehensive batch 83/277 (25 files)
⏱️  Comprehensive Progress: 2075/6905 files (30.1%), 181757 LOC/s, 10806927 AST nodes, 273138 symbols
📊 Processing comprehensive batch 84/277 (25 files)
⏱️  Comprehensive Progress: 2100/6905 files (30.4%), 181750 LOC/s, 10810473 AST nodes, 273291 symbols
📊 Processing comprehensive batch 85/277 (25 files)
⏱️  Comprehensive Progress: 2125/6905 files (30.8%), 181785 LOC/s, 10823920 AST nodes, 273634 symbols
📊 Processing comprehensive batch 86/277 (25 files)
⏱️  Comprehensive Progress: 2150/6905 files (31.1%), 183008 LOC/s, 11803090 AST nodes, 299592 symbols
📊 Processing comprehensive batch 87/277 (25 files)
⏱️  Comprehensive Progress: 2175/6905 files (31.5%), 182899 LOC/s, 11825531 AST nodes, 299744 symbols
📊 Processing comprehensive batch 88/277 (25 files)
⏱️  Comprehensive Progress: 2200/6905 files (31.9%), 182958 LOC/s, 11838517 AST nodes, 299943 symbols
📊 Processing comprehensive batch 89/277 (25 files)
⏱️  Comprehensive Progress: 2225/6905 files (32.2%), 182910 LOC/s, 11933365 AST nodes, 300663 symbols
📊 Processing comprehensive batch 90/277 (25 files)
⏱️  Comprehensive Progress: 2250/6905 files (32.6%), 182829 LOC/s, 11950275 AST nodes, 300999 symbols
📊 Processing comprehensive batch 91/277 (25 files)
⏱️  Comprehensive Progress: 2275/6905 files (32.9%), 182716 LOC/s, 11969653 AST nodes, 301246 symbols
📊 Processing comprehensive batch 92/277 (25 files)
⏱️  Comprehensive Progress: 2300/6905 files (33.3%), 182544 LOC/s, 11987296 AST nodes, 301426 symbols
📊 Processing comprehensive batch 93/277 (25 files)
⏱️  Comprehensive Progress: 2325/6905 files (33.7%), 182317 LOC/s, 12021445 AST nodes, 301836 symbols
📊 Processing comprehensive batch 94/277 (25 files)
⏱️  Comprehensive Progress: 2350/6905 files (34.0%), 182172 LOC/s, 12043750 AST nodes, 302130 symbols
📊 Processing comprehensive batch 95/277 (25 files)
⏱️  Comprehensive Progress: 2375/6905 files (34.4%), 182056 LOC/s, 12059859 AST nodes, 302439 symbols
📊 Processing comprehensive batch 96/277 (25 files)
⏱️  Comprehensive Progress: 2400/6905 files (34.8%), 181705 LOC/s, 12105758 AST nodes, 303035 symbols
📊 Processing comprehensive batch 97/277 (25 files)
⏱️  Comprehensive Progress: 2425/6905 files (35.1%), 181439 LOC/s, 12144303 AST nodes, 303395 symbols
📊 Processing comprehensive batch 98/277 (25 files)
⏱️  Comprehensive Progress: 2450/6905 files (35.5%), 180657 LOC/s, 12212067 AST nodes, 303902 symbols
📊 Processing comprehensive batch 99/277 (25 files)
⏱️  Comprehensive Progress: 2475/6905 files (35.8%), 180315 LOC/s, 12252853 AST nodes, 304460 symbols
📊 Processing comprehensive batch 100/277 (25 files)
⏱️  Comprehensive Progress: 2500/6905 files (36.2%), 180128 LOC/s, 12302313 AST nodes, 304875 symbols
📊 Processing comprehensive batch 101/277 (25 files)
⏱️  Comprehensive Progress: 2525/6905 files (36.6%), 179940 LOC/s, 12332648 AST nodes, 305237 symbols
📊 Processing comprehensive batch 102/277 (25 files)
⏱️  Comprehensive Progress: 2550/6905 files (36.9%), 179764 LOC/s, 12360368 AST nodes, 305523 symbols
📊 Processing comprehensive batch 103/277 (25 files)
⏱️  Comprehensive Progress: 2575/6905 files (37.3%), 179541 LOC/s, 12387846 AST nodes, 305858 symbols
📊 Processing comprehensive batch 104/277 (25 files)
⏱️  Comprehensive Progress: 2600/6905 files (37.7%), 178483 LOC/s, 12496091 AST nodes, 307798 symbols
📊 Processing comprehensive batch 105/277 (25 files)
⏱️  Comprehensive Progress: 2625/6905 files (38.0%), 178465 LOC/s, 12500705 AST nodes, 307851 symbols
📊 Processing comprehensive batch 106/277 (25 files)
⏱️  Comprehensive Progress: 2650/6905 files (38.4%), 178457 LOC/s, 12501153 AST nodes, 307904 symbols
📊 Processing comprehensive batch 107/277 (25 files)
⏱️  Comprehensive Progress: 2675/6905 files (38.7%), 178452 LOC/s, 12501834 AST nodes, 307970 symbols
📊 Processing comprehensive batch 108/277 (25 files)
⏱️  Comprehensive Progress: 2700/6905 files (39.1%), 178445 LOC/s, 12502856 AST nodes, 308049 symbols
📊 Processing comprehensive batch 109/277 (25 files)
⏱️  Comprehensive Progress: 2725/6905 files (39.5%), 178438 LOC/s, 12504098 AST nodes, 308116 symbols
📊 Processing comprehensive batch 110/277 (25 files)
⏱️  Comprehensive Progress: 2750/6905 files (39.8%), 178433 LOC/s, 12504771 AST nodes, 308173 symbols
📊 Processing comprehensive batch 111/277 (25 files)
⏱️  Comprehensive Progress: 2775/6905 files (40.2%), 178149 LOC/s, 12633757 AST nodes, 311440 symbols
📊 Processing comprehensive batch 112/277 (25 files)
⏱️  Comprehensive Progress: 2800/6905 files (40.6%), 177949 LOC/s, 12706483 AST nodes, 312705 symbols
📊 Processing comprehensive batch 113/277 (25 files)
⏱️  Comprehensive Progress: 2825/6905 files (40.9%), 178151 LOC/s, 12790972 AST nodes, 313435 symbols
📊 Processing comprehensive batch 114/277 (25 files)
⏱️  Comprehensive Progress: 2850/6905 files (41.3%), 178291 LOC/s, 12822029 AST nodes, 315118 symbols
📊 Processing comprehensive batch 115/277 (25 files)
⏱️  Comprehensive Progress: 2875/6905 files (41.6%), 178388 LOC/s, 12822622 AST nodes, 315907 symbols
📊 Processing comprehensive batch 116/277 (25 files)
⏱️  Comprehensive Progress: 2900/6905 files (42.0%), 178478 LOC/s, 12823137 AST nodes, 316628 symbols
📊 Processing comprehensive batch 117/277 (25 files)
⏱️  Comprehensive Progress: 2925/6905 files (42.4%), 178560 LOC/s, 12824399 AST nodes, 317390 symbols
📊 Processing comprehensive batch 118/277 (25 files)
⏱️  Comprehensive Progress: 2950/6905 files (42.7%), 178633 LOC/s, 12827516 AST nodes, 318000 symbols
📊 Processing comprehensive batch 119/277 (25 files)
⏱️  Comprehensive Progress: 2975/6905 files (43.1%), 178637 LOC/s, 12829811 AST nodes, 318222 symbols
📊 Processing comprehensive batch 120/277 (25 files)
⏱️  Comprehensive Progress: 3000/6905 files (43.4%), 178653 LOC/s, 12832604 AST nodes, 318364 symbols
📊 Processing comprehensive batch 121/277 (25 files)
⏱️  Comprehensive Progress: 3025/6905 files (43.8%), 178657 LOC/s, 12834193 AST nodes, 318466 symbols
📊 Processing comprehensive batch 122/277 (25 files)
⏱️  Comprehensive Progress: 3050/6905 files (44.2%), 178670 LOC/s, 12834739 AST nodes, 318601 symbols
📊 Processing comprehensive batch 123/277 (25 files)
⏱️  Comprehensive Progress: 3075/6905 files (44.5%), 178686 LOC/s, 12835167 AST nodes, 318774 symbols
📊 Processing comprehensive batch 124/277 (25 files)
⏱️  Comprehensive Progress: 3100/6905 files (44.9%), 178717 LOC/s, 12835192 AST nodes, 318997 symbols
📊 Processing comprehensive batch 125/277 (25 files)
⏱️  Comprehensive Progress: 3125/6905 files (45.3%), 178724 LOC/s, 12835398 AST nodes, 319119 symbols
📊 Processing comprehensive batch 126/277 (25 files)
⏱️  Comprehensive Progress: 3150/6905 files (45.6%), 178725 LOC/s, 12836373 AST nodes, 319202 symbols
📊 Processing comprehensive batch 127/277 (25 files)
⏱️  Comprehensive Progress: 3175/6905 files (46.0%), 178900 LOC/s, 12849861 AST nodes, 319316 symbols
📊 Processing comprehensive batch 128/277 (25 files)
⏱️  Comprehensive Progress: 3200/6905 files (46.3%), 178895 LOC/s, 12850498 AST nodes, 319367 symbols
📊 Processing comprehensive batch 129/277 (25 files)
⏱️  Comprehensive Progress: 3225/6905 files (46.7%), 179729 LOC/s, 12860289 AST nodes, 319911 symbols
📊 Processing comprehensive batch 130/277 (25 files)
⏱️  Comprehensive Progress: 3250/6905 files (47.1%), 180009 LOC/s, 12905054 AST nodes, 320111 symbols
📊 Processing comprehensive batch 131/277 (25 files)
⏱️  Comprehensive Progress: 3275/6905 files (47.4%), 179999 LOC/s, 12908180 AST nodes, 320232 symbols
📊 Processing comprehensive batch 132/277 (25 files)
⏱️  Comprehensive Progress: 3300/6905 files (47.8%), 180008 LOC/s, 12909482 AST nodes, 320325 symbols
📊 Processing comprehensive batch 133/277 (25 files)
⏱️  Comprehensive Progress: 3325/6905 files (48.2%), 180003 LOC/s, 12910204 AST nodes, 320389 symbols
📊 Processing comprehensive batch 134/277 (25 files)
⏱️  Comprehensive Progress: 3350/6905 files (48.5%), 181009 LOC/s, 12933466 AST nodes, 320953 symbols
📊 Processing comprehensive batch 135/277 (25 files)
⏱️  Comprehensive Progress: 3375/6905 files (48.9%), 181029 LOC/s, 12936887 AST nodes, 321072 symbols
📊 Processing comprehensive batch 136/277 (25 files)
⏱️  Comprehensive Progress: 3400/6905 files (49.2%), 181060 LOC/s, 12938493 AST nodes, 321347 symbols
📊 Processing comprehensive batch 137/277 (25 files)
⏱️  Comprehensive Progress: 3425/6905 files (49.6%), 181197 LOC/s, 12939361 AST nodes, 322373 symbols
📊 Processing comprehensive batch 138/277 (25 files)
⏱️  Comprehensive Progress: 3450/6905 files (50.0%), 181176 LOC/s, 12962555 AST nodes, 323190 symbols
📊 Processing comprehensive batch 139/277 (25 files)
⏱️  Comprehensive Progress: 3475/6905 files (50.3%), 181449 LOC/s, 13013487 AST nodes, 323604 symbols
📊 Processing comprehensive batch 140/277 (25 files)
⏱️  Comprehensive Progress: 3500/6905 files (50.7%), 181497 LOC/s, 13020303 AST nodes, 324331 symbols
📊 Processing comprehensive batch 141/277 (25 files)
⏱️  Comprehensive Progress: 3525/6905 files (51.0%), 181471 LOC/s, 13035900 AST nodes, 324671 symbols
📊 Processing comprehensive batch 142/277 (25 files)
⏱️  Comprehensive Progress: 3550/6905 files (51.4%), 181785 LOC/s, 13036756 AST nodes, 326339 symbols
📊 Processing comprehensive batch 143/277 (25 files)
⏱️  Comprehensive Progress: 3575/6905 files (51.8%), 183583 LOC/s, 13036781 AST nodes, 387450 symbols
📊 Processing comprehensive batch 144/277 (25 files)
⏱️  Comprehensive Progress: 3600/6905 files (52.1%), 184513 LOC/s, 13036806 AST nodes, 396708 symbols
📊 Processing comprehensive batch 145/277 (25 files)
⏱️  Comprehensive Progress: 3625/6905 files (52.5%), 184545 LOC/s, 13036831 AST nodes, 397005 symbols
📊 Processing comprehensive batch 146/277 (25 files)
⏱️  Comprehensive Progress: 3650/6905 files (52.9%), 184567 LOC/s, 13036856 AST nodes, 397206 symbols
📊 Processing comprehensive batch 147/277 (25 files)
⏱️  Comprehensive Progress: 3675/6905 files (53.2%), 184634 LOC/s, 13036881 AST nodes, 397737 symbols
📊 Processing comprehensive batch 148/277 (25 files)
⏱️  Comprehensive Progress: 3700/6905 files (53.6%), 184669 LOC/s, 13037303 AST nodes, 398058 symbols
📊 Processing comprehensive batch 149/277 (25 files)
⏱️  Comprehensive Progress: 3725/6905 files (53.9%), 184679 LOC/s, 13042544 AST nodes, 398138 symbols
📊 Processing comprehensive batch 150/277 (25 files)
⏱️  Comprehensive Progress: 3750/6905 files (54.3%), 184728 LOC/s, 13046057 AST nodes, 398139 symbols
📊 Processing comprehensive batch 151/277 (25 files)
⏱️  Comprehensive Progress: 3775/6905 files (54.7%), 184795 LOC/s, 13049240 AST nodes, 398440 symbols
📊 Processing comprehensive batch 152/277 (25 files)
⏱️  Comprehensive Progress: 3800/6905 files (55.0%), 184763 LOC/s, 13064815 AST nodes, 398925 symbols
📊 Processing comprehensive batch 153/277 (25 files)
⏱️  Comprehensive Progress: 3825/6905 files (55.4%), 184747 LOC/s, 13073104 AST nodes, 399252 symbols
📊 Processing comprehensive batch 154/277 (25 files)
⏱️  Comprehensive Progress: 3850/6905 files (55.8%), 184743 LOC/s, 13074187 AST nodes, 399353 symbols
📊 Processing comprehensive batch 155/277 (25 files)
⏱️  Comprehensive Progress: 3875/6905 files (56.1%), 184739 LOC/s, 13075150 AST nodes, 399435 symbols
📊 Processing comprehensive batch 156/277 (25 files)
⏱️  Comprehensive Progress: 3900/6905 files (56.5%), 184733 LOC/s, 13076296 AST nodes, 399533 symbols
📊 Processing comprehensive batch 157/277 (25 files)
⏱️  Comprehensive Progress: 3925/6905 files (56.8%), 184728 LOC/s, 13077842 AST nodes, 399642 symbols
📊 Processing comprehensive batch 158/277 (25 files)
⏱️  Comprehensive Progress: 3950/6905 files (57.2%), 184723 LOC/s, 13079289 AST nodes, 399772 symbols
📊 Processing comprehensive batch 159/277 (25 files)
⏱️  Comprehensive Progress: 3975/6905 files (57.6%), 184717 LOC/s, 13080562 AST nodes, 399887 symbols
📊 Processing comprehensive batch 160/277 (25 files)
⏱️  Comprehensive Progress: 4000/6905 files (57.9%), 184712 LOC/s, 13082538 AST nodes, 400041 symbols
📊 Processing comprehensive batch 161/277 (25 files)
⏱️  Comprehensive Progress: 4025/6905 files (58.3%), 184717 LOC/s, 13084594 AST nodes, 400227 symbols
📊 Processing comprehensive batch 162/277 (25 files)
⏱️  Comprehensive Progress: 4050/6905 files (58.7%), 184716 LOC/s, 13086701 AST nodes, 400425 symbols
📊 Processing comprehensive batch 163/277 (25 files)
⏱️  Comprehensive Progress: 4075/6905 files (59.0%), 184715 LOC/s, 13088723 AST nodes, 400516 symbols
📊 Processing comprehensive batch 164/277 (25 files)
⏱️  Comprehensive Progress: 4100/6905 files (59.4%), 184716 LOC/s, 13090812 AST nodes, 400606 symbols
📊 Processing comprehensive batch 165/277 (25 files)
⏱️  Comprehensive Progress: 4125/6905 files (59.7%), 184711 LOC/s, 13093758 AST nodes, 400892 symbols
📊 Processing comprehensive batch 166/277 (25 files)
⏱️  Comprehensive Progress: 4150/6905 files (60.1%), 184699 LOC/s, 13095725 AST nodes, 401038 symbols
📊 Processing comprehensive batch 167/277 (25 files)
⏱️  Comprehensive Progress: 4175/6905 files (60.5%), 184648 LOC/s, 13102136 AST nodes, 401308 symbols
📊 Processing comprehensive batch 168/277 (25 files)
⏱️  Comprehensive Progress: 4200/6905 files (60.8%), 184611 LOC/s, 13105541 AST nodes, 401465 symbols
📊 Processing comprehensive batch 169/277 (25 files)
⏱️  Comprehensive Progress: 4225/6905 files (61.2%), 184560 LOC/s, 13111207 AST nodes, 401614 symbols
📊 Processing comprehensive batch 170/277 (25 files)
⏱️  Comprehensive Progress: 4250/6905 files (61.5%), 184521 LOC/s, 13115474 AST nodes, 401733 symbols
📊 Processing comprehensive batch 171/277 (25 files)
⏱️  Comprehensive Progress: 4275/6905 files (61.9%), 184462 LOC/s, 13122471 AST nodes, 401945 symbols
📊 Processing comprehensive batch 172/277 (25 files)
⏱️  Comprehensive Progress: 4300/6905 files (62.3%), 184428 LOC/s, 13127219 AST nodes, 402109 symbols
📊 Processing comprehensive batch 173/277 (25 files)
⏱️  Comprehensive Progress: 4325/6905 files (62.6%), 184408 LOC/s, 13130041 AST nodes, 402243 symbols
📊 Processing comprehensive batch 174/277 (25 files)
⏱️  Comprehensive Progress: 4350/6905 files (63.0%), 184362 LOC/s, 13136154 AST nodes, 402442 symbols
📊 Processing comprehensive batch 175/277 (25 files)
⏱️  Comprehensive Progress: 4375/6905 files (63.4%), 184317 LOC/s, 13141768 AST nodes, 402603 symbols
📊 Processing comprehensive batch 176/277 (25 files)
⏱️  Comprehensive Progress: 4400/6905 files (63.7%), 184291 LOC/s, 13144663 AST nodes, 402690 symbols
📊 Processing comprehensive batch 177/277 (25 files)
⏱️  Comprehensive Progress: 4425/6905 files (64.1%), 184238 LOC/s, 13151070 AST nodes, 402846 symbols
📊 Processing comprehensive batch 178/277 (25 files)
⏱️  Comprehensive Progress: 4450/6905 files (64.4%), 184202 LOC/s, 13156277 AST nodes, 402994 symbols
📊 Processing comprehensive batch 179/277 (25 files)
⏱️  Comprehensive Progress: 4475/6905 files (64.8%), 184161 LOC/s, 13163229 AST nodes, 403200 symbols
📊 Processing comprehensive batch 180/277 (25 files)
⏱️  Comprehensive Progress: 4500/6905 files (65.2%), 184114 LOC/s, 13170322 AST nodes, 403439 symbols
📊 Processing comprehensive batch 181/277 (25 files)
⏱️  Comprehensive Progress: 4525/6905 files (65.5%), 184085 LOC/s, 13174142 AST nodes, 403608 symbols
📊 Processing comprehensive batch 182/277 (25 files)
⏱️  Comprehensive Progress: 4550/6905 files (65.9%), 184053 LOC/s, 13177500 AST nodes, 403727 symbols
📊 Processing comprehensive batch 183/277 (25 files)
⏱️  Comprehensive Progress: 4575/6905 files (66.3%), 184016 LOC/s, 13181950 AST nodes, 403919 symbols
📊 Processing comprehensive batch 184/277 (25 files)
⏱️  Comprehensive Progress: 4600/6905 files (66.6%), 183984 LOC/s, 13186594 AST nodes, 404049 symbols
📊 Processing comprehensive batch 185/277 (25 files)
⏱️  Comprehensive Progress: 4625/6905 files (67.0%), 183952 LOC/s, 13191231 AST nodes, 404196 symbols
📊 Processing comprehensive batch 186/277 (25 files)
⏱️  Comprehensive Progress: 4650/6905 files (67.3%), 183914 LOC/s, 13197836 AST nodes, 404466 symbols
📊 Processing comprehensive batch 187/277 (25 files)
⏱️  Comprehensive Progress: 4675/6905 files (67.7%), 183762 LOC/s, 13222168 AST nodes, 405083 symbols
📊 Processing comprehensive batch 188/277 (25 files)
⏱️  Comprehensive Progress: 4700/6905 files (68.1%), 183638 LOC/s, 13240407 AST nodes, 405517 symbols
📊 Processing comprehensive batch 189/277 (25 files)
⏱️  Comprehensive Progress: 4725/6905 files (68.4%), 183549 LOC/s, 13254190 AST nodes, 405855 symbols
📊 Processing comprehensive batch 190/277 (25 files)
⏱️  Comprehensive Progress: 4750/6905 files (68.8%), 183423 LOC/s, 13271793 AST nodes, 406193 symbols
📊 Processing comprehensive batch 191/277 (25 files)
⏱️  Comprehensive Progress: 4775/6905 files (69.2%), 183306 LOC/s, 13291535 AST nodes, 406809 symbols
📊 Processing comprehensive batch 192/277 (25 files)
⏱️  Comprehensive Progress: 4800/6905 files (69.5%), 183228 LOC/s, 13305629 AST nodes, 407305 symbols
📊 Processing comprehensive batch 193/277 (25 files)
⏱️  Comprehensive Progress: 4825/6905 files (69.9%), 183111 LOC/s, 13321852 AST nodes, 407821 symbols
📊 Processing comprehensive batch 194/277 (25 files)
⏱️  Comprehensive Progress: 4850/6905 files (70.2%), 183032 LOC/s, 13331497 AST nodes, 408040 symbols
📊 Processing comprehensive batch 195/277 (25 files)
⏱️  Comprehensive Progress: 4875/6905 files (70.6%), 182917 LOC/s, 13350398 AST nodes, 408542 symbols
📊 Processing comprehensive batch 196/277 (25 files)
⏱️  Comprehensive Progress: 4900/6905 files (71.0%), 182822 LOC/s, 13363793 AST nodes, 408833 symbols
📊 Processing comprehensive batch 197/277 (25 files)
⏱️  Comprehensive Progress: 4925/6905 files (71.3%), 182708 LOC/s, 13381561 AST nodes, 409342 symbols
📊 Processing comprehensive batch 198/277 (25 files)
⏱️  Comprehensive Progress: 4950/6905 files (71.7%), 182612 LOC/s, 13394644 AST nodes, 409724 symbols
📊 Processing comprehensive batch 199/277 (25 files)
⏱️  Comprehensive Progress: 4975/6905 files (72.0%), 182531 LOC/s, 13406356 AST nodes, 410037 symbols
📊 Processing comprehensive batch 200/277 (25 files)
⏱️  Comprehensive Progress: 5000/6905 files (72.4%), 182461 LOC/s, 13416710 AST nodes, 410300 symbols
📊 Processing comprehensive batch 201/277 (25 files)
⏱️  Comprehensive Progress: 5025/6905 files (72.8%), 182376 LOC/s, 13427308 AST nodes, 410633 symbols
📊 Processing comprehensive batch 202/277 (25 files)
⏱️  Comprehensive Progress: 5050/6905 files (73.1%), 182307 LOC/s, 13436197 AST nodes, 410911 symbols
📊 Processing comprehensive batch 203/277 (25 files)
⏱️  Comprehensive Progress: 5075/6905 files (73.5%), 182241 LOC/s, 13445296 AST nodes, 411143 symbols
📊 Processing comprehensive batch 204/277 (25 files)
⏱️  Comprehensive Progress: 5100/6905 files (73.9%), 182195 LOC/s, 13451555 AST nodes, 411358 symbols
📊 Processing comprehensive batch 205/277 (25 files)
⏱️  Comprehensive Progress: 5125/6905 files (74.2%), 182111 LOC/s, 13462722 AST nodes, 411582 symbols
📊 Processing comprehensive batch 206/277 (25 files)
⏱️  Comprehensive Progress: 5150/6905 files (74.6%), 182025 LOC/s, 13473120 AST nodes, 411883 symbols
📊 Processing comprehensive batch 207/277 (25 files)
⏱️  Comprehensive Progress: 5175/6905 files (74.9%), 181946 LOC/s, 13483017 AST nodes, 412113 symbols
📊 Processing comprehensive batch 208/277 (25 files)
⏱️  Comprehensive Progress: 5200/6905 files (75.3%), 181852 LOC/s, 13493933 AST nodes, 412336 symbols
📊 Processing comprehensive batch 209/277 (25 files)
⏱️  Comprehensive Progress: 5225/6905 files (75.7%), 181764 LOC/s, 13504281 AST nodes, 412605 symbols
📊 Processing comprehensive batch 210/277 (25 files)
⏱️  Comprehensive Progress: 5250/6905 files (76.0%), 181680 LOC/s, 13513355 AST nodes, 412897 symbols
📊 Processing comprehensive batch 211/277 (25 files)
⏱️  Comprehensive Progress: 5275/6905 files (76.4%), 181590 LOC/s, 13524166 AST nodes, 413251 symbols
📊 Processing comprehensive batch 212/277 (25 files)
⏱️  Comprehensive Progress: 5300/6905 files (76.8%), 181471 LOC/s, 13540562 AST nodes, 413779 symbols
📊 Processing comprehensive batch 213/277 (25 files)
⏱️  Comprehensive Progress: 5325/6905 files (77.1%), 181475 LOC/s, 13540998 AST nodes, 414027 symbols
📊 Processing comprehensive batch 214/277 (25 files)
⏱️  Comprehensive Progress: 5350/6905 files (77.5%), 181475 LOC/s, 13541670 AST nodes, 414205 symbols
📊 Processing comprehensive batch 215/277 (25 files)
⏱️  Comprehensive Progress: 5375/6905 files (77.8%), 181463 LOC/s, 13542707 AST nodes, 414279 symbols
📊 Processing comprehensive batch 216/277 (25 files)
⏱️  Comprehensive Progress: 5400/6905 files (78.2%), 181454 LOC/s, 13543343 AST nodes, 414334 symbols
📊 Processing comprehensive batch 217/277 (25 files)
⏱️  Comprehensive Progress: 5425/6905 files (78.6%), 181453 LOC/s, 13544696 AST nodes, 414397 symbols
📊 Processing comprehensive batch 218/277 (25 files)
⏱️  Comprehensive Progress: 5450/6905 files (78.9%), 181503 LOC/s, 13547726 AST nodes, 415062 symbols
📊 Processing comprehensive batch 219/277 (25 files)
⏱️  Comprehensive Progress: 5475/6905 files (79.3%), 181497 LOC/s, 13549578 AST nodes, 415125 symbols
📊 Processing comprehensive batch 220/277 (25 files)
⏱️  Comprehensive Progress: 5500/6905 files (79.7%), 181488 LOC/s, 13550751 AST nodes, 415207 symbols
📊 Processing comprehensive batch 221/277 (25 files)
⏱️  Comprehensive Progress: 5525/6905 files (80.0%), 181469 LOC/s, 13552903 AST nodes, 415368 symbols
📊 Processing comprehensive batch 222/277 (25 files)
⏱️  Comprehensive Progress: 5550/6905 files (80.4%), 181460 LOC/s, 13554329 AST nodes, 415453 symbols
📊 Processing comprehensive batch 223/277 (25 files)
⏱️  Comprehensive Progress: 5575/6905 files (80.7%), 181447 LOC/s, 13554995 AST nodes, 415499 symbols
📊 Processing comprehensive batch 224/277 (25 files)
⏱️  Comprehensive Progress: 5600/6905 files (81.1%), 181435 LOC/s, 13555454 AST nodes, 415540 symbols
📊 Processing comprehensive batch 225/277 (25 files)
⏱️  Comprehensive Progress: 5625/6905 files (81.5%), 181429 LOC/s, 13557175 AST nodes, 415632 symbols
📊 Processing comprehensive batch 226/277 (25 files)
⏱️  Comprehensive Progress: 5650/6905 files (81.8%), 181491 LOC/s, 13566510 AST nodes, 416374 symbols
📊 Processing comprehensive batch 227/277 (25 files)
⏱️  Comprehensive Progress: 5675/6905 files (82.2%), 183912 LOC/s, 14975218 AST nodes, 455227 symbols
📊 Processing comprehensive batch 228/277 (25 files)
⏱️  Comprehensive Progress: 5700/6905 files (82.5%), 185762 LOC/s, 16144918 AST nodes, 486725 symbols
📊 Processing comprehensive batch 229/277 (25 files)
⏱️  Comprehensive Progress: 5725/6905 files (82.9%), 185788 LOC/s, 16295279 AST nodes, 490630 symbols
📊 Processing comprehensive batch 230/277 (25 files)
⏱️  Comprehensive Progress: 5750/6905 files (83.3%), 185650 LOC/s, 16341432 AST nodes, 491581 symbols
📊 Processing comprehensive batch 231/277 (25 files)
⏱️  Comprehensive Progress: 5775/6905 files (83.6%), 185601 LOC/s, 16370862 AST nodes, 492301 symbols
📊 Processing comprehensive batch 232/277 (25 files)
⏱️  Comprehensive Progress: 5800/6905 files (84.0%), 185529 LOC/s, 16409144 AST nodes, 493218 symbols
📊 Processing comprehensive batch 233/277 (25 files)
⏱️  Comprehensive Progress: 5825/6905 files (84.4%), 185432 LOC/s, 16445728 AST nodes, 494054 symbols
📊 Processing comprehensive batch 234/277 (25 files)
⏱️  Comprehensive Progress: 5850/6905 files (84.7%), 185429 LOC/s, 16455139 AST nodes, 494281 symbols
📊 Processing comprehensive batch 235/277 (25 files)
⏱️  Comprehensive Progress: 5875/6905 files (85.1%), 185217 LOC/s, 16528642 AST nodes, 496025 symbols
📊 Processing comprehensive batch 236/277 (25 files)
⏱️  Comprehensive Progress: 5900/6905 files (85.4%), 185098 LOC/s, 16600699 AST nodes, 497659 symbols
📊 Processing comprehensive batch 237/277 (25 files)
⏱️  Comprehensive Progress: 5925/6905 files (85.8%), 185024 LOC/s, 16651461 AST nodes, 498873 symbols
📊 Processing comprehensive batch 238/277 (25 files)
⏱️  Comprehensive Progress: 5950/6905 files (86.2%), 184925 LOC/s, 16721769 AST nodes, 500546 symbols
📊 Processing comprehensive batch 239/277 (25 files)
⏱️  Comprehensive Progress: 5975/6905 files (86.5%), 184723 LOC/s, 16836231 AST nodes, 502662 symbols
📊 Processing comprehensive batch 240/277 (25 files)
⏱️  Comprehensive Progress: 6000/6905 files (86.9%), 184585 LOC/s, 16921422 AST nodes, 504431 symbols
📊 Processing comprehensive batch 241/277 (25 files)
⏱️  Comprehensive Progress: 6025/6905 files (87.3%), 184545 LOC/s, 16963070 AST nodes, 505493 symbols
📊 Processing comprehensive batch 242/277 (25 files)
⏱️  Comprehensive Progress: 6050/6905 files (87.6%), 184397 LOC/s, 17042276 AST nodes, 507099 symbols
📊 Processing comprehensive batch 243/277 (25 files)
⏱️  Comprehensive Progress: 6075/6905 files (88.0%), 184489 LOC/s, 17129800 AST nodes, 509473 symbols
📊 Processing comprehensive batch 244/277 (25 files)
⏱️  Comprehensive Progress: 6100/6905 files (88.3%), 177003 LOC/s, 19088924 AST nodes, 562387 symbols
📊 Processing comprehensive batch 245/277 (25 files)
⏱️  Comprehensive Progress: 6125/6905 files (88.7%), 176401 LOC/s, 19634636 AST nodes, 576802 symbols
📊 Processing comprehensive batch 246/277 (25 files)
⏱️  Comprehensive Progress: 6150/6905 files (89.1%), 176238 LOC/s, 19675408 AST nodes, 577720 symbols
📊 Processing comprehensive batch 247/277 (25 files)
⏱️  Comprehensive Progress: 6175/6905 files (89.4%), 176288 LOC/s, 19698560 AST nodes, 579160 symbols
📊 Processing comprehensive batch 248/277 (25 files)
⏱️  Comprehensive Progress: 6200/6905 files (89.8%), 176236 LOC/s, 19715355 AST nodes, 579699 symbols
📊 Processing comprehensive batch 249/277 (25 files)
⏱️  Comprehensive Progress: 6225/6905 files (90.2%), 175908 LOC/s, 19917514 AST nodes, 585185 symbols
📊 Processing comprehensive batch 250/277 (25 files)
⏱️  Comprehensive Progress: 6250/6905 files (90.5%), 174980 LOC/s, 20037828 AST nodes, 588418 symbols
📊 Processing comprehensive batch 251/277 (25 files)
⏱️  Comprehensive Progress: 6275/6905 files (90.9%), 174652 LOC/s, 20077840 AST nodes, 589331 symbols
📊 Processing comprehensive batch 252/277 (25 files)
⏱️  Comprehensive Progress: 6300/6905 files (91.2%), 174350 LOC/s, 20123504 AST nodes, 590309 symbols
📊 Processing comprehensive batch 253/277 (25 files)
⏱️  Comprehensive Progress: 6325/6905 files (91.6%), 174295 LOC/s, 20152987 AST nodes, 590656 symbols
📊 Processing comprehensive batch 254/277 (25 files)
⏱️  Comprehensive Progress: 6350/6905 files (92.0%), 174333 LOC/s, 20170661 AST nodes, 590656 symbols
📊 Processing comprehensive batch 255/277 (25 files)
⏱️  Comprehensive Progress: 6375/6905 files (92.3%), 174366 LOC/s, 20186324 AST nodes, 590656 symbols
📊 Processing comprehensive batch 256/277 (25 files)
⏱️  Comprehensive Progress: 6400/6905 files (92.7%), 174452 LOC/s, 20220699 AST nodes, 590656 symbols
📊 Processing comprehensive batch 257/277 (25 files)
⏱️  Comprehensive Progress: 6425/6905 files (93.0%), 174447 LOC/s, 20230325 AST nodes, 590656 symbols
📊 Processing comprehensive batch 258/277 (25 files)
⏱️  Comprehensive Progress: 6450/6905 files (93.4%), 174526 LOC/s, 20269306 AST nodes, 590656 symbols
📊 Processing comprehensive batch 259/277 (25 files)
⏱️  Comprehensive Progress: 6475/6905 files (93.8%), 174082 LOC/s, 20443612 AST nodes, 594604 symbols
📊 Processing comprehensive batch 260/277 (25 files)
⏱️  Comprehensive Progress: 6500/6905 files (94.1%), 174010 LOC/s, 20487713 AST nodes, 595459 symbols
📊 Processing comprehensive batch 261/277 (25 files)
⏱️  Comprehensive Progress: 6525/6905 files (94.5%), 173802 LOC/s, 20525612 AST nodes, 596614 symbols
📊 Processing comprehensive batch 262/277 (25 files)
⏱️  Comprehensive Progress: 6550/6905 files (94.9%), 173522 LOC/s, 20544939 AST nodes, 597067 symbols
📊 Processing comprehensive batch 263/277 (25 files)
⏱️  Comprehensive Progress: 6575/6905 files (95.2%), 173458 LOC/s, 20554134 AST nodes, 597305 symbols
📊 Processing comprehensive batch 264/277 (25 files)
⏱️  Comprehensive Progress: 6600/6905 files (95.6%), 173342 LOC/s, 20563502 AST nodes, 597539 symbols
📊 Processing comprehensive batch 265/277 (25 files)
⏱️  Comprehensive Progress: 6625/6905 files (95.9%), 173307 LOC/s, 20581094 AST nodes, 598015 symbols
📊 Processing comprehensive batch 266/277 (25 files)
⏱️  Comprehensive Progress: 6650/6905 files (96.3%), 173228 LOC/s, 20592151 AST nodes, 598282 symbols
📊 Processing comprehensive batch 267/277 (25 files)
⏱️  Comprehensive Progress: 6675/6905 files (96.7%), 173159 LOC/s, 20611888 AST nodes, 598826 symbols
📊 Processing comprehensive batch 268/277 (25 files)
⏱️  Comprehensive Progress: 6700/6905 files (97.0%), 173124 LOC/s, 20617531 AST nodes, 598990 symbols
📊 Processing comprehensive batch 269/277 (25 files)
⏱️  Comprehensive Progress: 6725/6905 files (97.4%), 173106 LOC/s, 20620335 AST nodes, 599108 symbols
📊 Processing comprehensive batch 270/277 (25 files)
⏱️  Comprehensive Progress: 6750/6905 files (97.8%), 172591 LOC/s, 20757572 AST nodes, 602618 symbols
📊 Processing comprehensive batch 271/277 (25 files)
⏱️  Comprehensive Progress: 6775/6905 files (98.1%), 172779 LOC/s, 20946620 AST nodes, 603742 symbols
📊 Processing comprehensive batch 272/277 (25 files)
⏱️  Comprehensive Progress: 6800/6905 files (98.5%), 171834 LOC/s, 21457524 AST nodes, 613597 symbols
📊 Processing comprehensive batch 273/277 (25 files)
⏱️  Comprehensive Progress: 6825/6905 files (98.8%), 171901 LOC/s, 21457549 AST nodes, 615059 symbols
📊 Processing comprehensive batch 274/277 (25 files)
⏱️  Comprehensive Progress: 6850/6905 files (99.2%), 171888 LOC/s, 21457574 AST nodes, 615490 symbols
📊 Processing comprehensive batch 275/277 (25 files)
⏱️  Comprehensive Progress: 6875/6905 files (99.6%), 171926 LOC/s, 21457599 AST nodes, 616478 symbols
📊 Processing comprehensive batch 276/277 (25 files)
⏱️  Comprehensive Progress: 6900/6905 files (99.9%), 171929 LOC/s, 21457624 AST nodes, 616806 symbols
📊 Processing comprehensive batch 277/277 (5 files)
⏱️  Comprehensive Progress: 6905/6905 files (100.0%), 171927 LOC/s, 21457629 AST nodes, 616822 symbols

🎯 COMPREHENSIVE ANALYSIS RESULTS
==================================
📁 Files analyzed: 6905
📝 Lines processed: 3271931
🌳 AST nodes generated: 21457629
🔍 Symbols extracted: 616822
🎯 Patterns detected: 1010104
⏱️  Duration: 19.03 seconds
🚀 Lines/Second: 171927
✅ Success Rate: 100.0%

🎯 PERFORMANCE VALIDATION RESULTS
=====================================
📁 Total Files: 6905
📝 Total Lines: 3271931
⏱️  Duration: 19.03 seconds
🚀 Lines/Second: 171927
📊 Files/Second: 362.8
✅ Success Rate: 100.0%
💾 Memory Usage: 0.0 MB

📈 LANGUAGE-SPECIFIC PERFORMANCE
====================================

🎯 1M LOC PERFORMANCE CLAIM VALIDATION:
Target: 1000000 LOC in 300 seconds
Actual: 3271931 LOC in 19.03 seconds
Projected 1M LOC time: 5.82 seconds
✅ CLAIM VALIDATED: Can process 1M LOC in under 5 minutes!
🚀 Performance margin: 51.6x faster than required

📊 Results saved to: performance_results.json
