// episteme/services/analysis-engine/tests/e2e/real_repository_test.rs

//! # End-to-End (E2E) Test: Analysis on a Medium-Sized Real Repository
//!
//! ## Purpose
//! This test module validates the analysis engine's ability to process a complete,
//! non-trivial, real-world repository. Unlike the basic workflow test which uses a small
//! repo for speed, this test uses a medium-sized project to uncover issues related to
//! scale, file diversity, and more complex code structures.
//!
//! ## Methodology
//! - It follows the same E2E pattern as `analysis_workflow.rs`: start a test server,
//!   connect to a test database, and initiate analysis via an API call.
//! - It specifically selects a repository from the `tests/real-data/medium/` directory.
//! - The primary goal is to ensure the analysis completes successfully without errors
//!   and that the stored results are reasonable for the repository's size and composition.
//!
//! ## Execution
//! This is a slow test and is intended to be run as part of a nightly or pre-release
//! validation suite, not during regular development cycles.
//!
//! ```bash
//! cargo test --test real_repository_test -- --ignored --nocapture
//! ```

use crate::fixtures::real_repos;
use crate::fixtures::test_helpers::TestServer;

/// # Test: `test_analysis_on_medium_real_repository`
///
/// ## Description
/// This E2E test orchestrates the analysis of a medium-sized repository (~10k-100k LOC).
/// It verifies that the system can handle a larger workload without crashing and that the
/// persisted results are plausible.
///
/// ## Success Criteria
/// - The system must successfully complete the analysis of a medium-sized repository.
/// - The final status in the database must be "completed".
/// - The key metrics stored in the database (e.g., LOC count, file count, language breakdown)
///   should be within an expected, plausible range for the chosen repository.
#[tokio::test]
#[ignore] // Ignored by default due to long run time and dependency on large data fixtures.
async fn test_analysis_on_medium_real_repository() {
    // --- Arrange ---
    // 1. Start the test server.
    let test_server = TestServer::start().await;
    let server_base_url = test_server.base_url();

    // 2. Select a medium-sized repository for the test.
    let repo_path = real_repos::get_repo_path("medium/standard-benchmark-repo")
        .await
        .expect("Failed to get repository for testing.");
    // The service might not handle git clones, so we can point it to the local file path.
    let repo_url = format!("file://{}", repo_path.display());
    let expected_min_loc = 10_000; // Plausibility check for ripgrep.
    println!("Target repository for medium analysis: {}", repo_url);

    // --- Act ---
    // 3. Initiate the analysis via an API call.
    let client = reqwest::Client::new();
    let analysis_endpoint = format!("{}/api/v1/analysis", server_base_url);
    let response = client
        .post(&analysis_endpoint)
        .json(&serde_json::json!({ "repository_url": repo_url }))
        .send()
        .await
        .expect("Failed to send analysis request.");

    // --- Assert (Initial) ---

    // 4. Check for the initial "Created" response.
    assert_eq!(response.status(), reqwest::StatusCode::CREATED);
    let response_body: serde_json::Value = response.json().await.unwrap();
    let analysis_id = response_body["analysis_id"].as_str().unwrap();
    println!(
        "Analysis of medium repository started with ID: {}",
        analysis_id
    );

    // --- Assert (Final) ---

    // 5. Poll for completion. This could take a minute or more.
    println!("Polling for analysis completion (this may take a while)...");
    let status_endpoint = format!("{}/api/v1/analysis/{}/status", server_base_url, analysis_id);
    let mut final_status = serde_json::Value::Null;

    for _ in 0..60 {
        // Poll for up to 5 minutes
        tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;
        let status_response = client.get(&status_endpoint).send().await.unwrap();
        if status_response.status().is_success() {
            let status_body: serde_json::Value = status_response.json().await.unwrap();
            println!(
                "  - Current status: {}",
                status_body["status"].as_str().unwrap_or("unknown")
            );
            if status_body["status"] == "Completed" || status_body["status"] == "Failed" {
                final_status = status_body;
                break;
            }
        }
    }

    // 6. Verify the final result from the database by fetching the full result.
    assert_ne!(final_status, serde_json::Value::Null, "Analysis timed out.");
    assert_eq!(
        final_status["status"], "Completed",
        "Analysis did not complete successfully."
    );

    let results_endpoint = format!("{}/api/v1/analysis/{}", server_base_url, analysis_id);
    let results_response = client.get(&results_endpoint).send().await.unwrap();
    let result: serde_json::Value = results_response.json().await.unwrap();
    println!("Analysis completed. Final result:\n{:#?}", result);

    // 7. Plausibility check: Ensure the LOC count is within a reasonable range.
    let actual_loc = result["metrics"]["lines_of_code"].as_u64().unwrap();
    assert!(
        actual_loc > expected_min_loc,
        "The final LOC count ({}) is unexpectedly low for this repository. Expected at least {}.",
        actual_loc,
        expected_min_loc
    );

    println!("E2E test on medium repository completed and results are plausible.");
}
