//! Resource optimization integration tests

use analysis_engine::{
    backpressure::{BackpressureConfig, BackpressureManager, BackpressureDecision, BackpressureMetrics},
    config::{ResourceOptimizationConfig, ServiceConfig},
    monitoring::{ResourceMonitor, SystemMonitor, SystemMonitorConfig},
};
use std::sync::Arc;
use std::time::Duration;
use tokio::time::sleep;

#[tokio::test]
async fn test_system_monitor_initialization() {
    let config = SystemMonitorConfig {
        cpu_limit_percent: 90.0,
        memory_limit_mb: 2048,
        cpu_warning_threshold: 80.0,
        memory_warning_threshold_percent: 85.0,
        monitoring_interval: Duration::from_secs(1),
        enable_process_monitoring: true,
        enable_disk_monitoring: false,
        enable_network_monitoring: false,
    };
    
    let monitor = SystemMonitor::new(config);
    
    // Start monitoring
    monitor.start().await.unwrap();
    
    // Give it time to collect initial metrics
    sleep(Duration::from_secs(2)).await;
    
    // Get metrics
    let metrics = monitor.get_metrics().await;
    
    // Basic validation
    assert!(metrics.total_memory > 0);
    assert!(metrics.cpu_count > 0);
    assert!(metrics.cpu_usage_percent >= 0.0 && metrics.cpu_usage_percent <= 100.0);
    assert!(metrics.memory_usage_percent >= 0.0 && metrics.memory_usage_percent <= 100.0);
    
    // Stop monitoring
    monitor.stop().await.unwrap();
}

#[tokio::test]
async fn test_resource_monitor_with_cpu_limits() {
    let monitor = ResourceMonitor::with_cpu_limit(
        1024,  // 1GB memory limit
        60,    // 60 second timeout
        75.0,  // 75% CPU limit
    );
    
    monitor.start_monitoring().await;
    
    // Check limits (should pass under normal conditions)
    let result = monitor.check_limits().await;
    
    // In a real test environment, this should pass
    if let Err(e) = result {
        eprintln!("Resource limit check failed (this is expected in constrained environments): {}", e);
    }
    
    // Get system resource info
    let info = monitor.get_system_resource_info().await;
    
    assert!(info.total_memory > 0);
    assert!(info.cpu_count > 0);
    assert!(info.cpu_usage >= 0.0 && info.cpu_usage <= 100.0);
}

#[tokio::test]
async fn test_backpressure_with_system_monitor() {
    // Create system monitor
    let system_config = SystemMonitorConfig {
        cpu_limit_percent: 90.0,
        memory_limit_mb: 2048,
        ..Default::default()
    };
    let system_monitor = Arc::new(SystemMonitor::new(system_config));
    
    // Create backpressure manager with system monitor
    let bp_config = BackpressureConfig {
        max_concurrent_analyses: 10,
        max_analysis_memory_mb: 2048,
        cpu_threshold_percent: 90.0,
        ..Default::default()
    };
    let backpressure = BackpressureManager::with_system_monitor(bp_config, system_monitor.clone());
    
    // Start system monitor
    system_monitor.start().await.unwrap();
    sleep(Duration::from_secs(1)).await;
    
    // Check analysis request (should be allowed under normal conditions)
    let decision = backpressure.check_analysis_request().await;
    
    match decision {
        BackpressureDecision::Allow => {
            println!("Request allowed - system resources are within limits");
        }
        BackpressureDecision::Reject(reason) => {
            println!("Request rejected: {:?}", reason);
            // This might happen in CI/CD environments with limited resources
        }
        BackpressureDecision::Throttle(duration) => {
            println!("Request throttled for {:?}", duration);
        }
    }
    
    // Stop system monitor
    system_monitor.stop().await.unwrap();
}

#[tokio::test]
async fn test_resource_optimization_config() {
    let config = ResourceOptimizationConfig::default();
    
    assert_eq!(config.cpu_limit_percent, 90.0);
    assert_eq!(config.memory_limit_mb, 4096);
    assert_eq!(config.cpu_warning_threshold, 80.0);
    assert_eq!(config.memory_warning_threshold_percent, 85.0);
    assert!(config.enable_resource_monitoring);
    assert_eq!(config.monitoring_interval_seconds, 5);
}

#[tokio::test]
async fn test_memory_limit_enforcement() {
    let monitor = ResourceMonitor::new(100, 60); // 100MB limit, 60s timeout
    
    monitor.start_monitoring().await;
    
    // Simulate memory usage
    monitor.add_memory_usage(50 * 1024 * 1024); // 50MB
    
    // Should pass
    assert!(monitor.check_limits().await.is_ok());
    
    // Add more memory to exceed limit
    monitor.add_memory_usage(60 * 1024 * 1024); // +60MB = 110MB total
    
    // Should fail
    match monitor.check_limits().await {
        Err(e) => println!("Expected memory limit error: {}", e),
        Ok(_) => panic!("Expected memory limit to be exceeded"),
    }
}

#[tokio::test]
async fn test_system_resource_summary() {
    let config = SystemMonitorConfig {
        cpu_limit_percent: 50.0,  // Low limit for testing
        memory_limit_mb: 1024,
        memory_warning_threshold_percent: 80.0,
        ..Default::default()
    };
    
    let monitor = SystemMonitor::new(config);
    let summary = monitor.get_resource_summary().await;
    
    assert!(summary.memory_usage_mb > 0);
    assert!(summary.cpu_usage >= 0.0);
    assert!(summary.process_memory_mb >= 0);
    
    // Check limit flags (may or may not be set depending on system state)
    println!("CPU limited: {}, Memory limited: {}", 
             summary.is_cpu_limited, 
             summary.is_memory_limited);
}

#[tokio::test]
async fn test_backpressure_metrics_update() {
    let config = BackpressureConfig::default();
    let manager = BackpressureManager::new(config);
    
    // Create test metrics
    let metrics = BackpressureMetrics {
        memory_usage_mb: 1024,
        cpu_usage_percent: 45.0,
        active_analyses: 5,
        active_requests: 5,
        queued_requests: 10,
        rejected_requests: 2,
        avg_response_time_ms: 150,
        last_updated: 0,
    };
    
    // Update metrics
    manager.update_metrics(metrics).await.unwrap();
    
    // Retrieve and verify
    let retrieved = manager.get_metrics().await;
    assert_eq!(retrieved.active_analyses, 5);
    assert_eq!(retrieved.queued_requests, 10);
    assert!(retrieved.last_updated > 0);
}

#[cfg(all(test, not(target_env = "msvc")))] // Skip on Windows CI
#[tokio::test]
async fn test_resource_pressure_detection() {
    let config = SystemMonitorConfig {
        cpu_limit_percent: 10.0,  // Very low limit to trigger
        memory_limit_mb: 100,     // Very low limit to trigger
        cpu_warning_threshold: 5.0,
        memory_warning_threshold_percent: 50.0,
        ..Default::default()
    };
    
    let monitor = SystemMonitor::new(config);
    
    // Start monitoring
    monitor.start().await.unwrap();
    sleep(Duration::from_secs(2)).await;
    
    // Check if under pressure (likely true with such low limits)
    let under_pressure = monitor.is_under_pressure().await;
    println!("System under pressure: {}", under_pressure);
    
    // Stop monitoring
    monitor.stop().await.unwrap();
}