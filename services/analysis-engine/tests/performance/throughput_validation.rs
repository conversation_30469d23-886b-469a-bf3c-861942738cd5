//! # Performance Throughput Validation
//!
//! ## Purpose
//! This module contains the critical performance test designed to validate the headline
//! throughput claim of the `analysis-engine`: **analyzing 1 million Lines of Code (LOC)
//! in under 5 minutes**. This test serves as a primary benchmark for overall system
//! performance and is a key guard against regressions.
//!
//! ## Methodology
//! The test methodology is designed for honesty and reproducibility:
//! 1.  **Real Data**: It uses a very large, real-world open-source repository (or a collection
//!     of them) from the `tests/real-data/large/` directory.
//! 2.  **Validated LOC Count**: Before the test runs, the exact LOC count of the target
//!     repository is verified using `tokei` to prevent testing against an inaccurate baseline.
//! 3.  **End-to-End Timing**: The test times the *entire* analysis process to capture the full,
//!     user-experienced latency.
//! 4.  **Evidence Generation**: The test outputs a detailed report with the verified LOC count,
//!     total time taken, and calculated LOC/second. This report is saved as a timestamped
//!     evidence file.
//!
//! ## Execution
//! This is the most resource-intensive test in the suite and must be run explicitly.
//!
//! ```bash
//! cargo test --test throughput_validation -- --ignored --nocapture
//! ```

use serde::Deserialize;
use std::collections::HashMap;
use std::path::Path;
use std::process::Command;
use std::time::{Duration, Instant};

// Local crate helpers for test setup and evidence collection.
use analysis_engine::models::AnalysisRequest;
use analysis_engine::services::analyzer::AnalysisService;
use std::sync::Arc;

use crate::fixtures::real_repos;
use crate::validation::evidence_collector;

/// Represents the stats object in `tokei`'s JSON output for a language.
#[derive(Deserialize, Debug)]
struct TokeiStats {
    code: u64,
}

/// Represents the overall JSON report from `tokei`.
type TokeiReport = HashMap<String, TokeiStats>;

/// # Test: `validate_throughput_claim_on_large_repo`
///
/// ## Description
/// This test directly validates the "1M LOC in < 5 minutes" claim. It selects a
/// massive repository, verifies its size, and times a full analysis run.
///
/// ## Success Criteria
/// - The total time taken for the analysis must be less than 300 seconds.
/// - The test must produce a verifiable evidence file in `validation-evidence/performance/`.
#[tokio::test]
#[ignore] // This test is very long-running and is ignored by default.
async fn test_validate_throughput_claim_on_large_repo() {
    // --- Arrange ---
    const TARGET_DURATION: Duration = Duration::from_secs(300); // 5 minutes

    println!("========================================================================");
    println!("  PERFORMANCE THROUGHPUT VALIDATION");
    println!(
        "  Target: Analyze a large repository in under {} seconds",
        TARGET_DURATION.as_secs()
    );
    println!("========================================================================");

    // 1. Select the large repository for the test.
    let repo_path = real_repos::get_repo_path("large/throughput-repo")
        .await
        .expect("Failed to get repository. Is git installed?");
    println!("  Using repository: {}", repo_path.display());

    // 2. Get the *validated* LOC count. This is a critical step for honesty.
    let verified_loc_count = get_validated_loc(&repo_path)
        .await
        .expect("Failed to get validated LOC count.");
    println!("  Verified LOC count: {}", verified_loc_count);

    // 3. Create an instance of the analysis service for testing.
    let analysis_service = Arc::new(
        AnalysisService::new_for_testing()
            .await
            .expect("Failed to create analysis service for testing."),
    );

    // --- Act ---
    // 4. Run the analysis and time it.
    println!("\n  Starting analysis...");
    let start_time = Instant::now();

    // Use the direct testing method that takes a repository path
    let analysis_result = analysis_service
        .analyze_repository_for_testing(&repo_path.to_string_lossy())
        .await
        .expect("Analysis failed");

    let elapsed_duration = start_time.elapsed();
    println!(
        "  Analysis finished in: {:.2} seconds",
        elapsed_duration.as_secs_f64()
    );

    // --- Assert ---
    // 5. Calculate the final metrics and generate the report.
    let loc_per_second = verified_loc_count as f64 / elapsed_duration.as_secs_f64();
    let did_pass = elapsed_duration < TARGET_DURATION;

    let report = format!(
        "\n--- Throughput Validation Report ---\n\
         Repository Path:     {}\n\
         Verified LOC:        {}\n\
         ----------------------------------\n\
         Target Time:         < {:.2} s\n\
         Actual Time:         {:.2} s\n\
         ----------------------------------\n\
         Throughput:          {:.0} LOC/sec\n\
         \n\
         Result:              {}",
        repo_path.display(),
        verified_loc_count,
        TARGET_DURATION.as_secs_f64(),
        elapsed_duration.as_secs_f64(),
        loc_per_second,
        if did_pass { "✅ PASS" } else { "❌ FAIL" }
    );

    println!("{}", report);

    // 6. Save the report as evidence.
    evidence_collector::save_raw_evidence("performance", "throughput-validation", "txt", &report)
        .expect("Failed to save evidence file.");

    // 7. The final assertion. Fail the test if the time target was missed.
    assert!(
        did_pass,
        "Throughput validation failed. Actual time ({:.2}s) exceeded target time ({}s). See report above.",
        elapsed_duration.as_secs_f64(),
        TARGET_DURATION.as_secs()
    );
}

/// Helper to get a validated LOC count from `tokei`.
async fn get_validated_loc(repo_path: &Path) -> Result<u64, String> {
    let mut cmd = Command::new("tokei");
    cmd.arg("--output=json").arg(repo_path);

    let output = evidence_collector::run_command_and_save_evidence(
        &mut cmd,
        "verification",
        "tokei-throughput-loc",
    )
    .map_err(|e| format!("Evidence collector failed for tokei: {:?}", e))?;

    let stdout = String::from_utf8(output.stdout)
        .map_err(|e| format!("Failed to read stdout from tokei: {}", e))?;

    let report: TokeiReport = serde_json::from_str(&stdout)
        .map_err(|e| format!("Failed to parse tokei JSON: {}. Body: {}", e, stdout))?;

    // `tokei`'s top-level JSON object is a map of languages. The "Total" key contains the sum.
    report
        .get("Total")
        .map(|stats| stats.code)
        .ok_or_else(|| "Tokei JSON output missing 'Total' key".to_string())
}
