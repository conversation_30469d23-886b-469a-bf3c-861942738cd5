//! # Memory Profiling and Validation
//!
//! ## Purpose
//! This module is dedicated to monitoring and validating the memory consumption of the
//! `analysis-engine`. The primary objective is to ensure that the engine operates
//! within acceptable memory limits, especially when processing large codebases or
//! large individual files. This prevents memory leaks and ensures stable, predictable
//! performance in production environments.
//!
//! ## Methodology
//! We use a combination of Rust's global allocator statistics and potentially more
//! advanced profiling crates (like `dhat` or `jemalloc-pprof`) to measure memory usage.
//! The tests run the analysis pipeline on large, real-world repositories and measure
//! peak memory allocation during the process. The core metric we track is **memory usage
//! per Line of Code (LOC)**.
//!
//! ## Success Criteria
//! - The engine's memory usage must stay below a defined threshold, currently targeted at
//!   **< 1 KB per LOC** on average for a large repository.
//! - The test must produce evidence, such as memory usage graphs or raw statistics, saved
//!   to the `validation-evidence/performance/` directory.
//!
//! ## Execution
//! Memory profiling is resource-intensive and can significantly slow down execution. These
//! tests are marked `#[ignore]` and must be run explicitly.
//!
//! ```bash
//! cargo test --test memory_profiling -- --ignored --nocapture
//! ```

use crate::fixtures::real_repos;
use crate::validation::evidence_collector;
use analysis_engine::{
    config::ServiceConfig,
    models::{AnalysisRequest, TokeiReport},
    services::{analyzer::performance::PerformanceManager, analyzer::AnalysisService},
};
use std::{path::Path, process::Command, sync::Arc};

/// # Test: `test_memory_usage_is_within_target_per_loc`
///
/// ## Description
/// This test measures the peak memory usage while analyzing a large, real-world code
/// repository. It then calculates the memory-per-LOC ratio and asserts that it is
/// below the defined target.
///
/// ## Success Criteria
/// - Peak memory usage / Total LOC must be less than `TARGET_KB_PER_LOC`.
/// - Raw memory statistics and the calculated ratio must be saved as evidence.
#[tokio::test]
#[ignore] // Ignored by default due to high resource usage and long run time.
async fn test_memory_usage_is_within_target_per_loc() {
    // --- Arrange ---
    const TARGET_KB_PER_LOC: f64 = 1.5; // Set a realistic target, e.g., 1.5 KB per LOC

    println!(
        "Starting memory profiling test. Target: < {} KB/LOC.",
        TARGET_KB_PER_LOC
    );

    // 1. Get a large repository from our test data fixtures.
    let repo_path = real_repos::get_repo_path("large/throughput-repo")
        .await
        .expect("Failed to get repository for testing.");

    // 2. Get the accurate LOC count for the repository to use as a denominator.
    let loc_count = get_validated_loc(&repo_path)
        .await
        .expect("Failed to get validated LOC count.");

    // 3. Set up the analysis service and performance manager.
    let config = Arc::new(ServiceConfig::default());
    let analysis_service = Arc::new(
        AnalysisService::new_for_testing()
            .await
            .expect("Failed to create analysis service."),
    );
    let perf_manager = PerformanceManager::new(config, None);

    // --- Act ---
    // 4. Run the analysis.
    let _analysis_result = analysis_service
        .analyze_repository_for_testing(&repo_path.to_string_lossy())
        .await
        .expect("Analysis failed");

    // 5. Get peak memory usage *after* the analysis is complete.
    let peak_memory_mb = perf_manager.get_peak_memory_usage();
    let peak_memory_kb = peak_memory_mb * 1024;

    // 6. Calculate the key metric: Kilobytes used per Line of Code.
    let actual_kb_per_loc = peak_memory_kb as f64 / loc_count as f64;

    // --- Assert ---

    // 5. Create a report of the findings.
    let report = format!(
        "Memory Profiling Report\n\
         -------------------------\n\
         Repository: {}\n\
         Total LOC: {}\n\
         Peak Memory Allocated: {:.2} KB\n\
         \n\
         Memory per LOC: {:.4} KB/LOC\n\
         Target per LOC: < {:.4} KB/LOC\n\
         Result: {}",
        repo_path,
        loc_count,
        peak_kb,
        actual_kb_per_loc,
        TARGET_KB_PER_LOC,
        if actual_kb_per_loc < TARGET_KB_PER_LOC {
            "PASS"
        } else {
            "FAIL"
        }
    );

    println!("{}", report);

    evidence_collector::save_raw_evidence("performance", "memory-profiling", "txt", &report)
        .expect("Failed to save evidence report.");

    // 7. Assert that the measured usage is below the target.
    assert!(
        actual_kb_per_loc < TARGET_KB_PER_LOC,
        "Memory usage ({:.4} KB/LOC) exceeded the target of (< {:.4} KB/LOC).",
        actual_kb_per_loc,
        TARGET_KB_PER_LOC
    );
}

/// Helper to get a validated LOC count from `tokei`.
async fn get_validated_loc(repo_path: &Path) -> Result<u64, String> {
    let mut cmd = Command::new("tokei");
    cmd.arg("--output=json").arg(repo_path);

    let output = evidence_collector::run_command_and_save_evidence(
        &mut cmd,
        "verification",
        "tokei-memory-loc",
    )
    .map_err(|e| format!("Evidence collector failed for tokei: {:?}", e))?;

    let stdout = String::from_utf8(output.stdout)
        .map_err(|e| format!("Failed to read stdout from tokei: {}", e))?;

    let report: TokeiReport = serde_json::from_str(&stdout)
        .map_err(|e| format!("Failed to parse tokei JSON: {}. Body: {}", e, stdout))?;

    // `tokei`'s top-level JSON object is a map of languages. The "Total" key contains the sum.
    report
        .get("Total")
        .map(|stats| stats.code)
        .ok_or_else(|| "Tokei JSON output missing 'Total' key".to_string())
}
