// episteme/services/analysis-engine/tests/fixtures/real_repos.rs

//! # Real Repository Fixtures
//!
//! ## Purpose
//! This module provides a safe and standardized interface for accessing the real-world
//! code repositories used in our E2E, performance, and validation tests. Its primary
//! job is to abstract away the details of fetching and locating these test data assets.
//!
//! ## Methodology
//! - **On-Demand Fetching**: When a test requests a repository for the first time, this
//!   module checks if it exists locally. If not, it clones it from its source URL into
//!   the appropriate subdirectory within `tests/real-data/`.
//! - **Caching**: Once a repository is cloned, it is available locally for all subsequent
//!   test runs, making them much faster.
//! - **Centralized Management**: The list of available repositories and their source URLs
//!   are managed here, providing a single source of truth for our test data.
//!
//! ## Usage
//! Tests should *never* hardcode paths to repositories. Instead, they should always call
//! `real_repos::get_repo_path(...)` to get a validated path to the required data.
//!
//! ```rust,ignore
//! use crate::fixtures::real_repos;
//!
//! #[tokio::test]
//! async fn my_test() {
//!     let repo_path = real_repos::get_repo_path("small/example-repo").await.unwrap();
//!     // ... use repo_path in your test ...
//! }
//! ```

use std::path::{Path, PathBuf};
use std::process::Command;

const REAL_DATA_ROOT: &str = "tests/real-data";

/// Represents an error that can occur while fetching a repository.
#[derive(Debug)]
pub enum Error {
    Io(std::io::Error),
    Command(String),
    NotFound(String),
}

/// Fetches a repository if it doesn't exist locally and returns its path.
///
/// This is the main entry point for tests needing access to a real repository.
///
/// # Arguments
/// * `repo_identifier` - A string identifying the repo, in the format `"<size>/<name>"`,
///   e.g., `"small/my-cool-repo"`.
///
/// # Returns
/// A `Result` containing the `PathBuf` to the local copy of the repository, or an `Error`.
pub async fn get_repo_path(repo_identifier: &str) -> Result<PathBuf, Error> {
    // 1. Get the URL for the given identifier.
    let repo_url = get_repo_url(repo_identifier).ok_or_else(|| {
        Error::NotFound(format!(
            "No repository defined for identifier '{}'",
            repo_identifier
        ))
    })?;

    // 2. Determine the expected local path.
    let local_path = Path::new(REAL_DATA_ROOT).join(repo_identifier);

    // 3. If the path already exists, assume it's the correct repo and return it.
    if local_path.exists() {
        println!(
            "[Real Repos] Found existing repository at: {}",
            local_path.display()
        );
        return Ok(local_path);
    }

    // 4. If it doesn't exist, clone it.
    println!(
        "[Real Repos] Repository not found locally. Cloning from {}...",
        repo_url
    );

    // Create the parent directory (e.g., `tests/real-data/small`) if it doesn't exist.
    if let Some(parent_dir) = local_path.parent() {
        std::fs::create_dir_all(parent_dir).map_err(Error::Io)?;
    }

    // Execute the `git clone` command.
    let mut cmd = Command::new("git");
    cmd.arg("clone")
        .arg("--depth=1")
        .arg(repo_url)
        .arg(&local_path);

    let output = cmd.output().map_err(Error::Io)?;

    if !output.status.success() {
        let stderr = String::from_utf8_lossy(&output.stderr);
        return Err(Error::Command(format!(
            "Failed to clone repository '{}'. Git stderr: {}",
            repo_url, stderr
        )));
    }

    println!(
        "[Real Repos] Successfully cloned to: {}",
        local_path.display()
    );

    Ok(local_path)
}

/// A centralized map of repository identifiers to their source URLs.
///
/// In a more advanced system, this could be loaded from a JSON or TOML file.
fn get_repo_url(repo_identifier: &str) -> Option<&'static str> {
    match repo_identifier {
        // --- Small Repositories (<10k LOC) ---
        "small/example-repo-1" => Some("https://github.com/stedolan/jq.git"), // Example: jq is small and popular
        "small/example-repo-2" => Some("https://github.com/benbernard/RecordFlux.git"),

        // --- Medium Repositories (10k-100k LOC) ---
        "medium/standard-benchmark-repo" => Some("https://github.com/BurntSushi/ripgrep.git"), // ripgrep is a great real-world Rust project
        "medium/another-repo" => Some("https://github.com/tokio-rs/tokio.git"),

        // --- Large Repositories (100k+ LOC) ---
        "large/throughput-repo" => Some("https://github.com/rust-lang/rust.git"), // The Rust compiler itself is a massive test case
        "large/scale-test-repo" => Some("https://github.com/torvalds/linux.git"), // The ultimate scale test

        _ => None,
    }
}

// Example of how this might be used in another test (for documentation purposes).
#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    #[ignore] // This test performs real I/O and network operations.
    async fn test_get_repo_path_works() {
        // This test will attempt to clone a small repository.
        // It requires `git` to be installed and network access.
        let identifier = "small/example-repo-1";
        let result = get_repo_path(identifier).await;

        assert!(result.is_ok(), "get_repo_path failed: {:?}", result.err());

        let path = result.unwrap();
        // Check that the path looks correct and that a `.git` directory exists,
        // which confirms it's likely a valid clone.
        assert!(path.ends_with(identifier));
        assert!(path.join(".git").exists());

        // Clean up after the test by removing the cloned directory.
        // In a real test suite, you might leave it cached.
        let _ = std::fs::remove_dir_all(path);
    }
}
