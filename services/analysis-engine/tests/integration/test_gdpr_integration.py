#!/usr/bin/env python3
"""
GDPR Integration Tests for Analysis Engine

Tests the complete GDPR compliance workflow including:
- Data deletion requests and cascades
- Data export in multiple formats
- Consent management and withdrawal
- Audit trail generation
"""

import asyncio
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional

import pytest
import httpx


class GDPRTestClient:
    """Test client for GDPR API endpoints"""
    
    def __init__(self, base_url: str = "http://localhost:8001"):
        self.base_url = base_url
        self.client = httpx.AsyncClient(timeout=30.0)
    
    async def close(self):
        await self.client.aclose()
    
    # Deletion endpoints
    async def request_deletion(self, user_id: str, reason: str, scope: Optional[Dict] = None) -> Dict:
        """Request user data deletion"""
        response = await self.client.delete(
            f"{self.base_url}/api/gdpr/users/{user_id}",
            json={"reason": reason, "scope": scope}
        )
        response.raise_for_status()
        return response.json()
    
    async def get_deletion_status(self, user_id: str) -> List[Dict]:
        """Get deletion request status"""
        response = await self.client.get(
            f"{self.base_url}/api/gdpr/users/{user_id}/deletion-status"
        )
        response.raise_for_status()
        return response.json()["data"]
    
    # Export endpoints
    async def request_export(self, user_id: str, format: str = "Json", include_encrypted: bool = True) -> Dict:
        """Request data export"""
        response = await self.client.post(
            f"{self.base_url}/api/gdpr/users/{user_id}/export",
            json={"format": format, "include_encrypted": include_encrypted}
        )
        response.raise_for_status()
        return response.json()
    
    async def get_export_status(self, request_id: str) -> Dict:
        """Get export request status"""
        response = await self.client.get(
            f"{self.base_url}/api/gdpr/export/{request_id}"
        )
        response.raise_for_status()
        return response.json()["data"]
    
    async def download_export(self, request_id: str) -> bytes:
        """Download exported data"""
        response = await self.client.get(
            f"{self.base_url}/api/gdpr/export/download/{request_id}"
        )
        response.raise_for_status()
        return response.content
    
    # Consent endpoints
    async def update_consent(self, user_id: str, consent_type: str, granted: bool) -> Dict:
        """Update user consent"""
        response = await self.client.post(
            f"{self.base_url}/api/gdpr/consent",
            json={
                "user_id": user_id,
                "consent_type": consent_type,
                "granted": granted,
                "ip_address": "127.0.0.1",
                "user_agent": "GDPR Test Client"
            }
        )
        response.raise_for_status()
        return response.json()
    
    async def get_consent_status(self, user_id: str) -> Dict:
        """Get current consent status"""
        response = await self.client.get(
            f"{self.base_url}/api/gdpr/consent/{user_id}"
        )
        response.raise_for_status()
        return response.json()["data"]
    
    async def get_consent_history(self, user_id: str, consent_type: Optional[str] = None, limit: int = 100) -> List[Dict]:
        """Get consent history"""
        params = {"limit": limit}
        if consent_type:
            params["consent_type"] = consent_type
        
        response = await self.client.get(
            f"{self.base_url}/api/gdpr/consent/{user_id}/history",
            params=params
        )
        response.raise_for_status()
        return response.json()["data"]
    
    async def withdraw_all_consents(self, user_id: str) -> List[Dict]:
        """Withdraw all consents"""
        response = await self.client.post(
            f"{self.base_url}/api/gdpr/consent/{user_id}/withdraw-all"
        )
        response.raise_for_status()
        return response.json()["data"]
    
    async def get_consent_receipt(self, user_id: str) -> Dict:
        """Get consent receipt"""
        response = await self.client.get(
            f"{self.base_url}/api/gdpr/consent/{user_id}/receipt"
        )
        response.raise_for_status()
        return response.json()["data"]
    
    # Admin endpoints
    async def health_check(self) -> Dict:
        """Check GDPR service health"""
        response = await self.client.get(
            f"{self.base_url}/api/gdpr/health"
        )
        response.raise_for_status()
        return response.json()["data"]
    
    async def process_pending_requests(self) -> Dict:
        """Process pending GDPR requests"""
        response = await self.client.post(
            f"{self.base_url}/api/gdpr/process-pending"
        )
        response.raise_for_status()
        return response.json()["data"]


@pytest.fixture
async def gdpr_client():
    """Create GDPR test client"""
    client = GDPRTestClient()
    yield client
    await client.close()


@pytest.fixture
def test_user_id():
    """Generate test user ID"""
    return f"test-user-{int(time.time())}"


class TestGDPRCompliance:
    """Test GDPR compliance features"""
    
    async def test_consent_lifecycle(self, gdpr_client: GDPRTestClient, test_user_id: str):
        """Test complete consent management lifecycle"""
        
        # 1. Grant initial consents
        analytics_consent = await gdpr_client.update_consent(
            test_user_id, "Analytics", True
        )
        assert analytics_consent["success"]
        assert analytics_consent["data"]["granted"] == True
        
        marketing_consent = await gdpr_client.update_consent(
            test_user_id, "Marketing", True
        )
        assert marketing_consent["success"]
        
        # 2. Check consent status
        status = await gdpr_client.get_consent_status(test_user_id)
        assert "Analytics" in status["consents"]
        assert status["consents"]["Analytics"]["granted"] == True
        assert "Marketing" in status["consents"]
        assert status["consents"]["Marketing"]["granted"] == True
        
        # 3. Get consent history
        history = await gdpr_client.get_consent_history(test_user_id)
        assert len(history) >= 2
        assert any(h["consent_type"] == "Analytics" for h in history)
        assert any(h["consent_type"] == "Marketing" for h in history)
        
        # 4. Withdraw specific consent
        withdrawn = await gdpr_client.update_consent(
            test_user_id, "Marketing", False
        )
        assert withdrawn["success"]
        assert withdrawn["data"]["granted"] == False
        
        # 5. Verify updated status
        updated_status = await gdpr_client.get_consent_status(test_user_id)
        assert updated_status["consents"]["Analytics"]["granted"] == True
        assert updated_status["consents"]["Marketing"]["granted"] == False
        
        # 6. Get consent receipt
        receipt = await gdpr_client.get_consent_receipt(test_user_id)
        assert receipt["version"] == "1.0"
        assert receipt["jurisdiction"] == "EU"
        assert receipt["subject"]["userID"] == test_user_id
        
        # 7. Withdraw all consents
        all_withdrawn = await gdpr_client.withdraw_all_consents(test_user_id)
        assert len(all_withdrawn) > 0
        
        # 8. Verify all consents withdrawn
        final_status = await gdpr_client.get_consent_status(test_user_id)
        for consent_type, state in final_status["consents"].items():
            assert state["granted"] == False
    
    async def test_data_export_workflow(self, gdpr_client: GDPRTestClient, test_user_id: str):
        """Test data export in multiple formats"""
        
        # 1. Request JSON export
        json_export = await gdpr_client.request_export(
            test_user_id, "Json", include_encrypted=True
        )
        assert json_export["success"]
        json_request_id = json_export["data"]["request_id"]
        
        # 2. Request CSV export
        csv_export = await gdpr_client.request_export(
            test_user_id, "Csv", include_encrypted=False
        )
        assert csv_export["success"]
        csv_request_id = csv_export["data"]["request_id"]
        
        # 3. Request combined export
        combined_export = await gdpr_client.request_export(
            test_user_id, "Combined", include_encrypted=True
        )
        assert combined_export["success"]
        combined_request_id = combined_export["data"]["request_id"]
        
        # 4. Process pending exports
        await gdpr_client.process_pending_requests()
        
        # 5. Check export status
        await asyncio.sleep(2)  # Allow processing time
        
        json_status = await gdpr_client.get_export_status(json_request_id)
        assert json_status["status"] in ["Processing", "Ready"]
        
        csv_status = await gdpr_client.get_export_status(csv_request_id)
        assert csv_status["status"] in ["Processing", "Ready"]
        
        # 6. Simulate download (in real test, would verify actual data)
        if json_status["status"] == "Ready":
            download_result = await gdpr_client.download_export(json_request_id)
            assert download_result  # Would verify actual content
    
    async def test_deletion_cascade(self, gdpr_client: GDPRTestClient, test_user_id: str):
        """Test data deletion with cascading"""
        
        # 1. Request full deletion
        deletion_request = await gdpr_client.request_deletion(
            test_user_id,
            "User requested account deletion",
            scope=None  # Full deletion
        )
        assert deletion_request["success"]
        request_id = deletion_request["data"]["request_id"]
        
        # 2. Check deletion status
        status_list = await gdpr_client.get_deletion_status(test_user_id)
        assert len(status_list) > 0
        assert status_list[0]["request_id"] == request_id
        assert status_list[0]["status"] == "Pending"
        
        # 3. Process pending deletions
        await gdpr_client.process_pending_requests()
        
        # 4. Verify deletion progress
        await asyncio.sleep(2)
        updated_status = await gdpr_client.get_deletion_status(test_user_id)
        assert updated_status[0]["status"] in ["Processing", "Completed"]
        
        # 5. If completed, verify deletion certificate
        if updated_status[0]["status"] == "Completed":
            assert updated_status[0]["completed_at"] is not None
            # In real implementation, would verify certificate
    
    async def test_partial_deletion(self, gdpr_client: GDPRTestClient, test_user_id: str):
        """Test partial data deletion with specific scope"""
        
        # Request deletion of specific analyses only
        scope = {
            "analysis_requests": False,
            "analysis_results": True,
            "user_data": False,
            "specific_analyses": ["analysis-123", "analysis-456"]
        }
        
        partial_deletion = await gdpr_client.request_deletion(
            test_user_id,
            "Delete specific analyses only",
            scope=scope
        )
        
        assert partial_deletion["success"]
        assert partial_deletion["data"]["scope"] is not None
        assert partial_deletion["data"]["scope"]["analysis_results"] == True
        assert partial_deletion["data"]["scope"]["user_data"] == False
    
    async def test_gdpr_health_check(self, gdpr_client: GDPRTestClient):
        """Test GDPR service health check"""
        
        health = await gdpr_client.health_check()
        assert health["status"] == "healthy"
        assert health["services"]["deletion"] == "operational"
        assert health["services"]["export"] == "operational"
        assert health["services"]["consent"] == "operational"
    
    async def test_consent_version_tracking(self, gdpr_client: GDPRTestClient, test_user_id: str):
        """Test consent version tracking"""
        
        # Grant consent
        consent1 = await gdpr_client.update_consent(
            test_user_id, "DataProcessing", True
        )
        version1 = consent1["data"]["consent_version"]
        
        # Get history and verify version
        history = await gdpr_client.get_consent_history(
            test_user_id, "DataProcessing", limit=1
        )
        assert len(history) > 0
        assert history[0]["consent_version"] == version1
    
    async def test_compliance_demonstration(self, gdpr_client: GDPRTestClient, test_user_id: str):
        """Test compliance demonstration features"""
        
        # Create audit trail through various operations
        await gdpr_client.update_consent(test_user_id, "Analytics", True)
        await gdpr_client.request_export(test_user_id, "Json", False)
        await gdpr_client.update_consent(test_user_id, "Analytics", False)
        
        # Get comprehensive consent history
        history = await gdpr_client.get_consent_history(test_user_id)
        assert len(history) >= 2
        
        # Verify audit trail includes all operations
        granted_events = [h for h in history if h["granted"]]
        withdrawn_events = [h for h in history if not h["granted"]]
        assert len(granted_events) > 0
        assert len(withdrawn_events) > 0


if __name__ == "__main__":
    # Run integration tests
    import sys
    sys.exit(pytest.main([__file__, "-v"]))