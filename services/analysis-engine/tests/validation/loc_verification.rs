//! # LOC Verification Tests
//!
//! ## Purpose
//! This module is dedicated to ensuring the honesty and accuracy of our internal
//! Lines of Code (LOC) counting logic. The primary goal is to prevent the kind
//! of metric inflation that undermined previous performance claims.
//!
//! ## Methodology
//! We achieve this by cross-validating our internal LOC counter against well-established,
//! industry-standard external tools: `tokei` and `cloc`. These tests operate on
//! real-world code repositories stored in `tests/real-data/`.
//!
//! ## Execution
//! These tests are resource-intensive and have external dependencies (`tokei`, `cloc`,
//! and git). Therefore, they are marked with `#[ignore]` and must be run explicitly
//! as part of the validation suite.
//!
//! ```bash
//! cargo test --test loc_verification -- --ignored --nocapture
//! ```

use serde::Deserialize;
use std::collections::HashMap;
use std::path::Path;
use std::process::Command;

use analysis_engine::services::language_detector::LanguageDetector;

use crate::fixtures::real_repos;
use crate::validation::evidence_collector;

// --- Data Structures for Deserializing JSON Output ---

/// Represents the stats object in `tokei`'s JSON output.
#[derive(Deserialize, Debug)]
struct TokeiStats {
    code: u64,
}

/// A simplified representation of `tokei`'s JSON output, focusing on the total.
type TokeiReport = HashMap<String, TokeiStats>;

/// Represents the stats object for the SUM in `cloc`'s JSON output.
#[derive(Deserialize, Debug)]
struct ClocStats {
    code: u64,
}

/// A simplified representation of `cloc`'s JSON output.
#[derive(Deserialize, Debug)]
struct ClocReport {
    #[serde(rename = "SUM")]
    sum: ClocStats,
}

/// # Test: `cross_validate_loc_counts_with_tokei_and_cloc`
///
/// This test has been updated from a placeholder to a full implementation. It now:
/// - Fetches a real repository using the `real_repos` fixture.
/// - Runs `tokei` and `cloc` using the `evidence_collector` to persist raw output.
/// - Parses the JSON output from both tools.
/// - Compares a placeholder for our internal LOC count against both external tools.
#[tokio::test]
#[ignore] // Ignored by default due to external dependencies and long run time.
async fn test_loc_count_matches_external_tools() {
    // --- Arrange ---
    const TOLERANCE_PERCENTAGE: f64 = 2.0; // Allow 2% discrepancy.

    // 1. Get the path to a medium-sized, real-world repository.
    // This ensures the test is running against a realistic and consistent dataset.
    let repo_path = real_repos::get_repo_path("medium/standard-benchmark-repo")
        .await
        .expect("Failed to get repository for testing. Is git installed?");

    // --- Act ---

    // 2. Get the LOC count from our internal analysis engine.
    let detector = LanguageDetector::new();
    let lang_stats = detector
        .detect_languages_with_stats(&repo_path)
        .expect("Internal language detection failed.");
    let internal_loc_count: u64 = lang_stats.values().map(|stats| stats.lines as u64).sum();

    // 3. Get the LOC count from `tokei`.
    let tokei_loc_count = run_and_parse_loc_tool(&repo_path, "tokei")
        .await
        .expect("Failed to run or parse tokei");

    // 4. Get the LOC count from `cloc`.
    let cloc_loc_count = run_and_parse_loc_tool(&repo_path, "cloc")
        .await
        .expect("Failed to run or parse cloc");

    // --- Assert ---
    println!("\n--- LOC Verification Results ---");
    println!("Internal Engine: {}", internal_loc_count);
    println!("Tokei:           {}", tokei_loc_count);
    println!("Cloc:            {}", cloc_loc_count);
    println!("---------------------------------");

    // 5. Compare the internal count against `tokei`.
    validate_discrepancy(
        "tokei",
        internal_loc_count,
        tokei_loc_count,
        TOLERANCE_PERCENTAGE,
    );

    // 6. Compare the internal count against `cloc`.
    validate_discrepancy(
        "cloc",
        internal_loc_count,
        cloc_loc_count,
        TOLERANCE_PERCENTAGE,
    );

    println!(
        "\n✅ LOC counts successfully validated against tokei and cloc within {}% tolerance.",
        TOLERANCE_PERCENTAGE
    );
}

/// Helper to run an external LOC tool, save the evidence, and parse the result.
async fn run_and_parse_loc_tool(repo_path: &Path, tool: &str) -> Result<u64, String> {
    // Both `tokei` and `cloc` support JSON output, which is much more reliable for parsing.
    let mut cmd = Command::new(tool);
    cmd.arg("--json").arg("--by-file-sum=code").arg(repo_path);

    // Use the evidence collector to run the command and save its output.
    let output = evidence_collector::run_command_and_save_evidence(
        &mut cmd,
        "verification",
        &format!("{}-loc", tool),
    )
    .map_err(|e| format!("Evidence collector failed for {}: {:?}", tool, e))?;

    let stdout = String::from_utf8(output.stdout)
        .map_err(|e| format!("Failed to read stdout from {}: {}", tool, e))?;

    // Parse the JSON output to extract the total code line count.
    match tool {
        "tokei" => {
            let report: TokeiReport = serde_json::from_str(&stdout)
                .map_err(|e| format!("Failed to parse tokei JSON: {}. Body: {}", e, stdout))?;
            // `tokei` includes a "Total" key with the sum of all languages.
            report
                .get("Total")
                .map(|stats| stats.code)
                .ok_or_else(|| "Tokei JSON output missing 'Total' key".to_string())
        }
        "cloc" => {
            let report: ClocReport = serde_json::from_str(&stdout)
                .map_err(|e| format!("Failed to parse cloc JSON: {}. Body: {}", e, stdout))?;
            Ok(report.sum.code)
        }
        _ => panic!("Unsupported tool specified: {}", tool),
    }
}

/// Helper function to assert that the discrepancy between two counts is within a tolerance.
fn validate_discrepancy(tool_name: &str, internal_count: u64, external_count: u64, tolerance: f64) {
    let diff = (internal_count as i64 - external_count as i64).abs() as f64;
    let percentage_diff = (diff / external_count as f64) * 100.0;
    assert!(
        percentage_diff <= tolerance,
        "LOC count discrepancy with {} is {:.2}%, which is over the {}% tolerance.\n  Internal: {}\n  {}:    {}",
        tool_name,
        percentage_diff,
        tolerance,
        internal_count,
        tool_name,
        external_count
    );
}
