// episteme/services/analysis-engine/tests/validation/truth_validator.rs

//! # Truth Validator
//!
//! ## Purpose
//! This module is the ultimate programmatic enforcer of our system's documented promises
//! and invariants. It is designed to be a "meta-test" that validates high-level claims
//! made in project documentation (like a `TRUTH-MANIFEST.md` or `PRODUCTION-STATUS.md`).
//!
//! The goal is to prevent a disconnect between our documentation and the actual behavior
//! of the software. If we claim the system can do X, this validator provides an automated
//! test that proves it.
//!
//! ## Methodology
//! - The validator defines a series of "claims," each corresponding to a specific, measurable
//!   statement about the system.
//! - Each claim has a corresponding validation function that executes a test to verify it.
//!   This might involve making API calls, running analysis, or checking database state.
//! - The test iterates through all defined claims and reports on which ones pass and which
//!   ones fail, providing a clear and comprehensive audit of our system's capabilities
//!   against its promises.
//!
//! ## Execution
//! This is a high-level, slow, and potentially network-dependent test. It is marked `#[ignore]`.
//! ```bash
//! cargo test --test truth_validator -- --ignored --nocapture
//! ```

use std::future::Future;
use std::pin::Pin;
use tokio::time::Instant;

// A simple alias for a validation function, which is an async function returning a Result.
type ValidationFn = Pin<Box<dyn Future<Output = Result<(), String>>>>;

/// Represents a single, verifiable claim about the system.
struct Claim {
    /// A human-readable description of the claim, ideally matching documentation.
    description: &'static str,
    /// The async function that executes the logic to validate the claim.
    validation_fn: Box<dyn Fn() -> ValidationFn>,
}

impl Claim {
    fn new(description: &'static str, validation_fn: impl Fn() -> ValidationFn + 'static) -> Self {
        Self {
            description,
            validation_fn: Box::new(validation_fn),
        }
    }
}

/// # Test: `validate_all_system_truths`
///
/// ## Description
/// This master test iterates through a predefined list of system claims and executes
/// their validation logic. It aggregates the results to provide a single, comprehensive
/// status report on the system's adherence to its documented promises.
///
/// ## Success Criteria
/// - All defined claims must pass their validation checks.
/// - If any claim fails, the test fails, and a detailed report is printed.
#[tokio::test]
#[ignore] // Ignored by default due to its comprehensive and slow nature.
async fn validate_all_system_truths() {
    println!("========================================================================");
    println!("  TRUTH VALIDATOR: Verifying all documented system claims...");
    println!("========================================================================");

    // Claims are defined here to link directly to the validation logic.
    // These should be updated to match actual system capabilities.
    let claims = get_system_claims();

    let mut passed_count = 0;
    let mut failed_claims = Vec::new();

    for claim in claims {
        println!("\n▶️  VALIDATING CLAIM: {}", claim.description);
        let start = Instant::now();
        let result = (claim.validation_fn)().await;
        let duration = start.elapsed();

        match result {
            Ok(_) => {
                println!("  ✅ PASS ({:.2?}s)", duration);
                passed_count += 1;
            }
            Err(e) => {
                println!("  ❌ FAIL ({:.2?}s)", duration);
                println!("     Reason: {}", e);
                failed_claims.push((claim.description.to_string(), e));
            }
        }
    }

    println!("\n--------------------- TRUTH VALIDATION SUMMARY ---------------------");
    println!("  Claims Validated: {}", passed_count + failed_claims.len());
    println!("  Claims Passed:    {}", passed_count);
    println!("  Claims Failed:    {}", failed_claims.len());
    println!("--------------------------------------------------------------------");

    if !failed_claims.is_empty() {
        eprintln!("\nThe following system truths were found to be FALSE:");
        for (description, error) in &failed_claims {
            eprintln!("  - {}: {}", description, error);
        }
        panic!("One or more system truths failed validation.");
    }

    println!("\n🎉 All system truths have been successfully validated!");
}

/// Defines the list of all claims to be validated.
/// This acts as the central manifest of testable promises.
fn get_system_claims() -> Vec<Claim> {
    vec![
        Claim::new(
            "The system API supports exactly 31 programming languages.",
            || Box::pin(validate_language_count()),
        ),
        Claim::new(
            "The internal LOC counter is within 2% of `tokei`'s count for a standard repo.",
            || Box::pin(validate_loc_accuracy()),
        ),
        Claim::new(
            "The system rejects inputs with path traversal patterns.",
            || Box::pin(validate_path_traversal_rejection()),
        ),
    ]
}

// --- Individual Claim Validation Functions ---
// These are placeholders that would call the actual test logic from other modules.

async fn validate_language_count() -> Result<(), String> {
    use analysis_engine::create_app;
    use tokio::net::TcpListener;
    
    // Start actual test server
    let app = create_app().await.map_err(|e| format!("Failed to create app: {}", e))?;
    let listener = TcpListener::bind("127.0.0.1:0")
        .await
        .map_err(|e| format!("Failed to bind listener: {}", e))?;
    let addr = listener.local_addr().map_err(|e| format!("Failed to get address: {}", e))?;
    
    // Spawn server in background
    tokio::spawn(async move {
        axum::serve(listener, app)
            .await
            .expect("Test server failed to run");
    });
    
    // Wait for server to start
    tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
    
    // Make actual API call
    let endpoint = format!("http://{}/api/v1/languages", addr);
    let response = reqwest::get(&endpoint).await.map_err(|e| e.to_string())?;
    
    if !response.status().is_success() {
        return Err(format!("API request failed with status: {}", response.status()));
    }
    
    let body: serde_json::Value = response.json().await.map_err(|e| e.to_string())?;
    let languages = body["languages"].as_array().ok_or("Invalid response format")?;
    let count = languages.len();
    
    // This will fail until actual language count matches the claim
    if count == 31 {
        Ok(())
    } else {
        Err(format!("Expected 31 languages, but API returned {}", count))
    }
}

async fn validate_loc_accuracy() -> Result<(), String> {
    use analysis_engine::services::analyzer::AnalysisService;
    use analysis_engine::storage::{CacheManager, StorageOperations, PubSubOperations};
    use analysis_engine::config::ServiceConfig;
    use std::sync::Arc;
    use std::fs;
    use std::process::Command;
    
    // Create a test repository for LOC validation
    let temp_dir = tempfile::tempdir().map_err(|e| format!("Failed to create temp dir: {}", e))?;
    let repo_path = temp_dir.path().join("loc-test-repo");
    fs::create_dir_all(&repo_path).map_err(|e| format!("Failed to create repo: {}", e))?;
    
    // Create test files with known LOC
    let test_files = vec![
        ("main.rs", "fn main() {\n    println!(\"Hello, world!\");\n}"),
        ("lib.rs", "pub fn hello() -> &'static str {\n    \"Hello\"\n}"),
        ("test.py", "def hello():\n    return \"Hello\""),
    ];
    
    for (filename, content) in test_files {
        fs::write(repo_path.join(filename), content)
            .map_err(|e| format!("Failed to create test file: {}", e))?;
    }
    
    // Get LOC count from tokei (external tool)
    let tokei_output = Command::new("tokei")
        .arg("--output=json")
        .arg(&repo_path)
        .output()
        .map_err(|e| format!("Failed to run tokei: {}", e))?;
    
    let tokei_stdout = String::from_utf8(tokei_output.stdout)
        .map_err(|e| format!("Failed to read tokei output: {}", e))?;
    
    let tokei_json: serde_json::Value = serde_json::from_str(&tokei_stdout)
        .map_err(|e| format!("Failed to parse tokei JSON: {}", e))?;
    
    let tokei_loc = tokei_json["Total"]["code"].as_u64()
        .ok_or("Failed to get LOC count from tokei")?;
    
    // Get LOC count from our internal analyzer
    let config = Arc::new(ServiceConfig::default());
    let cache_manager = Arc::new(CacheManager::new_for_testing().await
        .map_err(|e| format!("Failed to create cache manager: {}", e))?);
    let storage_client = Arc::new(StorageOperations::new_for_testing().await
        .map_err(|e| format!("Failed to create storage client: {}", e))?);
    let pubsub_client = Arc::new(PubSubOperations::new_for_testing().await
        .map_err(|e| format!("Failed to create pubsub client: {}", e))?);
    
    let analysis_service = AnalysisService::new(
        None,
        storage_client,
        pubsub_client,
        cache_manager,
        config,
    ).await.map_err(|e| format!("Failed to create analysis service: {}", e))?;
    
    // This will fail until actual LOC counting is implemented
    let analysis_result = analysis_service.analyze_repository_for_testing(
        &repo_path.to_string_lossy()
    ).await.map_err(|e| format!("Analysis failed: {}", e))?;
    
    // Extract LOC from analysis result
    let internal_loc = analysis_result.metrics
        .and_then(|m| m.lines_of_code)
        .ok_or("Internal LOC count not available")?;
    
    let diff = (internal_loc as i64 - tokei_loc as i64).abs() as f64;
    let percentage_diff = (diff / tokei_loc as f64) * 100.0;
    
    if percentage_diff <= 2.0 {
        Ok(())
    } else {
        Err(format!(
            "LOC count discrepancy is {:.2}%, which is over the 2% tolerance. Internal: {}, Tokei: {}",
            percentage_diff, internal_loc, tokei_loc
        ))
    }
}

async fn validate_path_traversal_rejection() -> Result<(), String> {
    use analysis_engine::create_app;
    use tokio::net::TcpListener;
    
    // Start actual test server
    let app = create_app().await.map_err(|e| format!("Failed to create app: {}", e))?;
    let listener = TcpListener::bind("127.0.0.1:0")
        .await
        .map_err(|e| format!("Failed to bind listener: {}", e))?;
    let addr = listener.local_addr().map_err(|e| format!("Failed to get address: {}", e))?;
    
    // Spawn server in background
    tokio::spawn(async move {
        axum::serve(listener, app)
            .await
            .expect("Test server failed to run");
    });
    
    // Wait for server to start
    tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
    
    // Test path traversal payload
    let payload = "../../../../etc/passwd";
    let endpoint = format!("http://{}/api/v1/analysis", addr);
    
    let response = reqwest::Client::new()
        .post(&endpoint)
        .json(&serde_json::json!({
            "repository_url": payload,
            "branch": "main"
        }))
        .send()
        .await
        .map_err(|e| format!("Failed to send request: {}", e))?;
    
    let status_code = response.status().as_u16();
    
    // This will fail until actual path traversal protection is implemented
    if status_code >= 400 && status_code < 500 {
        Ok(())
    } else {
        Err(format!(
            "Path traversal payload was not rejected correctly. Got status: {}",
            status_code
        ))
    }
}
