// episteme/services/analysis-engine/tests/validation/evidence_collector.rs

//! # Evidence Collector
//!
//! ## Purpose
//! This module is the cornerstone of the "Evidence Over Claims" principle in our testing
//! framework. Its sole responsibility is to provide a standardized and reliable way to
//! capture and persist the raw output of validation and performance tests.
//!
//! By centralizing evidence collection here, we ensure that all performance metrics,
//! external tool outputs, and validation reports are stored in a consistent, timestamped
//! format in the `tests/validation-evidence/` directory. This directory is git-ignored
//! and serves as the undeniable source of truth for our performance claims.
//!
//! ## Usage
//! Other test modules (like `loc_verification` or `throughput_validation`) should use
//! the functions in this module to save their results.
//!
//! ## Key Functions
//! - `save_raw_evidence`: Saves any string content to a timestamped file.
//! - `run_command_and_save_evidence`: Executes an external command (like `cloc` or `tokei`),
//!   captures its output, saves it as evidence, and returns the output for parsing.

use std::fs;
use std::path::{Path, PathBuf};
use std::process::{Command, Output};
use std::time::{SystemTime, UNIX_EPOCH};

// A simple error type for this module for now.
#[derive(Debug)]
pub enum Error {
    Io(std::io::Error),
    CommandFailed(String),
}

impl From<std::io::Error> for Error {
    fn from(err: std::io::Error) -> Self {
        Error::Io(err)
    }
}

const EVIDENCE_ROOT_DIR: &str = "tests/validation-evidence";

/// Saves a string of content as a raw evidence file in a specified subdirectory.
///
/// This function handles directory creation and generates a unique, timestamped filename.
///
/// # Arguments
/// * `subdirectory` - The sub-folder within `tests/validation-evidence/` (e.g., "performance", "verification").
/// * `prefix` - A descriptive prefix for the filename (e.g., "throughput-report", "cloc-output").
/// * `extension` - The file extension (e.g., "txt", "json").
/// * `content` - The string content to write to the file.
///
/// # Returns
/// A `Result` containing the `PathBuf` of the newly created evidence file, or an `Error`.
pub fn save_raw_evidence(
    subdirectory: &str,
    prefix: &str,
    extension: &str,
    content: &str,
) -> Result<PathBuf, Error> {
    // 1. Create the target directory if it doesn't exist.
    let target_dir = Path::new(EVIDENCE_ROOT_DIR).join(subdirectory);
    fs::create_dir_all(&target_dir)?;

    // 2. Generate a unique, timestamped filename.
    let timestamp = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .expect("Time went backwards")
        .as_secs();
    let filename = format!("{}-{}.{}", prefix, timestamp, extension);
    let file_path = target_dir.join(filename);

    // 3. Write the content to the file.
    fs::write(&file_path, content)?;

    println!(
        "[Evidence Collector] Saved evidence to: {}",
        file_path.display()
    );

    Ok(file_path)
}

/// Executes a command, captures its output, saves it as evidence, and returns the output.
///
/// This is a high-level helper that bundles the common pattern of running an external tool
/// for validation and needing to both save its raw output and parse it.
///
/// # Arguments
/// * `command` - A mutable `std::process::Command` ready to be executed.
/// * `evidence_subdirectory` - The subdirectory to save the evidence in.
/// * `evidence_prefix` - A descriptive prefix for the evidence file.
///
/// # Returns
/// A `Result` containing the `std::process::Output` if the command succeeds, or an `Error`.
pub fn run_command_and_save_evidence(
    command: &mut Command,
    evidence_subdirectory: &str,
    evidence_prefix: &str,
) -> Result<Output, Error> {
    println!("[Evidence Collector] Running command: {:?}", command);

    // Execute the command
    let output = command.output()?;

    // Create a detailed report from the command execution.
    let report = format!(
        "--- Command Execution Report ---\n\
         Command: {:?}\n\
         Exit Status: {}\n\
         \n\
         --- STDOUT ---\n\
         {}\n\
         \n\
         --- STDERR ---\n\
         {}",
        command,
        output.status,
        String::from_utf8_lossy(&output.stdout),
        String::from_utf8_lossy(&output.stderr)
    );

    // Save the full report as evidence.
    save_raw_evidence(evidence_subdirectory, evidence_prefix, "txt", &report)?;

    // Check if the command itself was successful. If not, return an error.
    if !output.status.success() {
        let err_msg = format!(
            "Command {:?} failed with status {}. See evidence file for details.",
            command, output.status
        );
        return Err(Error::CommandFailed(err_msg));
    }

    Ok(output)
}

// Example of how this might be used in another test (for documentation purposes).
#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    #[ignore] // This is an example, not a real test to be run. It writes to the filesystem.
    fn test_save_evidence_works() {
        let content = "This is a test evidence file.";
        let result = save_raw_evidence("test-output", "example", "txt", content);
        assert!(result.is_ok());
        let path = result.unwrap();
        assert!(path.exists());
        let read_content = fs::read_to_string(path).unwrap();
        assert_eq!(read_content, content);
    }
}
