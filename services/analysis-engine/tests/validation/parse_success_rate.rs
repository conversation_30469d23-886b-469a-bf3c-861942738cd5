// episteme/services/analysis-engine/tests/validation/parse_success_rate.rs

//! # Parser Success Rate Validation
//!
//! ## Purpose
//! This test module is crucial for measuring the quality and robustness of our `tree-sitter`
//! language parsers. Its primary function is to calculate the percentage of files for each
//! supported language that can be successfully parsed without errors.
//!
//! ## Methodology
//! The test operates on large, real-world repositories to provide a realistic sample of
//! diverse coding styles, syntaxes, and edge cases. It iterates through all files,
//! identifies their language based on file extension, and attempts to parse each one.
//! It then aggregates the success and failure counts for each language.
//!
//! ## Success Criteria
//! The target success rate is a key quality metric. A high success rate indicates that our
//! parsers are robust and can handle real-world code effectively. We aim for a success
//! rate of **85% or higher** for each language. Failures are logged to provide a feedback
//! loop for improving individual parsers.
//!
//! ## Execution
//! This is a long-running test that requires the `real-data` fixtures. It is marked
//! `#[ignore]` and should be run explicitly.
//!
//! ```bash
//! cargo test --test parse_success_rate -- --ignored --nocapture
//! ```

use analysis_engine::config::ServiceConfig;
use analysis_engine::parser::TreeSitterParser;
use analysis_engine::services::language_detector::LanguageDetector;
use std::collections::HashMap;
use std::path::Path;
use std::sync::Arc;
use std::fs;
use walkdir::WalkDir;

/// Holds the results of the parse rate analysis for a single language.
#[derive(Debug, Default, Clone)]
struct ParseRateResult {
    files_attempted: u32,
    files_succeeded: u32,
    files_failed: u32,
    failed_files: Vec<String>,
}

impl ParseRateResult {
    fn success_rate(&self) -> f64 {
        if self.files_attempted == 0 {
            100.0
        } else {
            (self.files_succeeded as f64 / self.files_attempted as f64) * 100.0
        }
    }
}

/// # Test: `validate_parser_success_rates_on_real_code`
///
/// This test performs a real analysis by:
/// - Using a test repository with diverse code samples.
/// - Walking its directory structure.
/// - Attempting to parse each relevant file based on its extension.
/// - Collecting detailed statistics on success and failure rates.
/// - Saving a comprehensive report as evidence.
#[tokio::test]
#[ignore] // Ignored by default due to long run time and data dependency.
async fn validate_parser_success_rates_on_real_code() {
    // --- Arrange ---
    const TARGET_SUCCESS_RATE: f64 = 85.0;
    const MIN_FILES_FOR_RELEVANCE: u32 = 10;

    println!(
        "Starting parser success rate validation. Target: {}%",
        TARGET_SUCCESS_RATE
    );

    // 1. Get or create a test repository with diverse code samples
    let repo_path = if Path::new("tests/real-data/large").exists() {
        "tests/real-data/large/throughput-repo"
    } else {
        // Create a temporary test repository with diverse code samples
        let temp_dir = tempfile::tempdir().expect("Failed to create temp dir");
        let repo_path = temp_dir.path().join("parse-test-repo");
        fs::create_dir_all(&repo_path).expect("Failed to create test repo");
        
        // Create test files in different languages
        let test_files = vec![
            ("main.rs", "fn main() { println!(\"Hello, world!\"); }"),
            ("lib.rs", "pub fn hello() -> &'static str { \"Hello\" }"),
            ("test.py", "def hello(): return \"Hello\""),
            ("app.js", "function hello() { return \"Hello\"; }"),
            ("Component.tsx", "export const Component = () => <div>Hello</div>;"),
            ("test.go", "package main\nfunc main() { fmt.Println(\"Hello\") }"),
            ("Test.java", "public class Test { public static void main(String[] args) {} }"),
            ("test.c", "#include <stdio.h>\nint main() { printf(\"Hello\"); return 0; }"),
            ("test.cpp", "#include <iostream>\nint main() { std::cout << \"Hello\"; return 0; }"),
            ("style.css", "body { color: red; }"),
        ];
        
        for (filename, content) in test_files {
            fs::write(repo_path.join(filename), content)
                .expect("Failed to create test file");
        }
        
        // Also create some files that should fail parsing (intentionally malformed)
        let malformed_files = vec![
            ("broken.rs", "fn main( { println!(\"Unclosed parenthesis\"); }"),
            ("broken.py", "def hello( return \"Unclosed parenthesis\""),
            ("broken.js", "function hello() { return \"Unclosed brace\""),
        ];
        
        for (filename, content) in malformed_files {
            fs::write(repo_path.join(filename), content)
                .expect("Failed to create malformed test file");
        }
        
        Box::leak(Box::new(repo_path.to_string_lossy().to_string())).as_str()
    };

    // 2. Create parser and language detector instances
    let config = Arc::new(ServiceConfig::default());
    let parser = Arc::new(
        TreeSitterParser::new(config.clone())
            .expect("Failed to create TreeSitterParser")
    );
    let language_detector = Arc::new(LanguageDetector::new());

    // --- Act ---
    // 3. Walk the repository and attempt to parse each file
    let mut results: HashMap<String, ParseRateResult> = HashMap::new();
    let mut parse_futures = Vec::new();

    for entry in WalkDir::new(&repo_path)
        .into_iter()
        .filter_map(|e| e.ok())
        .filter(|e| e.file_type().is_file())
    {
        let file_path = entry.path().to_path_buf();
        let parser_clone = parser.clone();
        let language_detector_clone = language_detector.clone();

        parse_futures.push(tokio::spawn(async move {
            // Detect language based on file extension
            if let Some(language) = language_detector_clone.detect_language_from_path(&file_path) {
                // Attempt to parse the file
                match parser_clone.parse_file(&file_path).await {
                    Ok(_) => Some((language, true, file_path)),
                    Err(_) => Some((language, false, file_path)),
                }
            } else {
                None
            }
        }));
    }

    // Wait for all parsing tasks to complete
    let all_parse_results = futures::future::join_all(parse_futures).await;

    // Aggregate results by language
    for result in all_parse_results {
        if let Ok(Some((language, is_success, file_path))) = result {
            let stats = results.entry(language).or_default();
            stats.files_attempted += 1;

            if is_success {
                stats.files_succeeded += 1;
            } else {
                stats.files_failed += 1;
                stats.failed_files.push(file_path.to_string_lossy().into_owned());
            }
        }
    }

    // --- Assert ---
    // 4. Generate a detailed report and check against the success target
    let mut all_passed = true;
    let mut report = String::new();
    report.push_str("Parser Success Rate Validation Report\n");
    report.push_str("=====================================\n");
    report.push_str(&format!(
        "{:<20} | {:>10} | {:>10} | {:>10} | {:>10}\n",
        "Language", "Succeeded", "Failed", "Total", "Rate (%)"
    ));
    report.push_str(&"-".repeat(70));
    report.push('\n');

    let mut sorted_results: Vec<_> = results.into_iter().collect();
    sorted_results.sort_by_key(|(lang, _)| lang.clone());

    for (lang, stats) in sorted_results {
        let success_rate = stats.success_rate();
        report.push_str(&format!(
            "{:<20} | {:>10} | {:>10} | {:>10} | {:>10.2}\n",
            lang, stats.files_succeeded, stats.files_failed, stats.files_attempted, success_rate
        ));

        // Only enforce the success rate for languages with a meaningful number of files
        if stats.files_attempted >= MIN_FILES_FOR_RELEVANCE && success_rate < TARGET_SUCCESS_RATE {
            all_passed = false;
            report.push_str(&format!(
                "  ^-- FAILED: Success rate is below target of {}%\n",
                TARGET_SUCCESS_RATE
            ));
            // Log a few failing files for easy debugging
            for (i, failed_file) in stats.failed_files.iter().take(3).enumerate() {
                report.push_str(&format!(
                    "    - Failure sample {}: {}\n",
                    i + 1,
                    failed_file
                ));
            }
        }
    }

    println!("{}", report);

    // 5. Save the evidence report
    let timestamp = chrono::Utc::now().format("%Y%m%d_%H%M%S");
    let evidence_dir = Path::new("validation-evidence/accuracy");
    if !evidence_dir.exists() {
        fs::create_dir_all(evidence_dir).expect("Failed to create evidence directory");
    }
    
    let evidence_file = evidence_dir.join(format!("parse-success-rate-{}.txt", timestamp));
    fs::write(evidence_file, &report).expect("Failed to save evidence file");

    // This test will fail until parser success rates meet the target
    assert!(
        all_passed,
        "One or more languages failed to meet the parse success rate target. See report for details."
    );
}