//! # Language Detection Unit Tests
//!
//! These tests focus on the behavior of language detection logic without coupling
//! to specific implementation details. They test the public contract and expected
//! behaviors using standard unit testing practices.

use std::collections::HashMap;
use std::path::Path;

// Mock trait for language detection to avoid coupling to specific implementation
pub trait LanguageDetector {
    fn detect_from_extension(&self, file_path: &Path) -> Option<String>;
    fn detect_from_content(&self, content: &str) -> Option<String>;
    fn detect_from_shebang(&self, content: &str) -> Option<String>;
    fn get_supported_languages(&self) -> Vec<String>;
}

// Simple test implementation
struct TestLanguageDetector {
    extension_map: HashMap<String, String>,
    supported_languages: Vec<String>,
}

impl TestLanguageDetector {
    fn new() -> Self {
        let mut extension_map = HashMap::new();
        extension_map.insert("rs".to_string(), "rust".to_string());
        extension_map.insert("py".to_string(), "python".to_string());
        extension_map.insert("js".to_string(), "javascript".to_string());
        extension_map.insert("ts".to_string(), "typescript".to_string());
        extension_map.insert("go".to_string(), "go".to_string());
        extension_map.insert("java".to_string(), "java".to_string());
        extension_map.insert("cpp".to_string(), "cpp".to_string());
        extension_map.insert("c".to_string(), "c".to_string());

        let supported_languages = vec![
            "rust".to_string(),
            "python".to_string(),
            "javascript".to_string(),
            "typescript".to_string(),
            "go".to_string(),
            "java".to_string(),
            "cpp".to_string(),
            "c".to_string(),
        ];

        Self {
            extension_map,
            supported_languages,
        }
    }
}

impl LanguageDetector for TestLanguageDetector {
    fn detect_from_extension(&self, file_path: &Path) -> Option<String> {
        file_path
            .extension()
            .and_then(|ext| ext.to_str())
            .and_then(|ext| self.extension_map.get(ext))
            .cloned()
    }

    fn detect_from_content(&self, content: &str) -> Option<String> {
        if content.contains("fn main()") && content.contains("println!") {
            Some("rust".to_string())
        } else if content.contains("def ") && content.contains("import ") {
            Some("python".to_string())
        } else if content.contains("function ") && content.contains("console.log") {
            Some("javascript".to_string())
        } else {
            None
        }
    }

    fn detect_from_shebang(&self, content: &str) -> Option<String> {
        if content.starts_with("#!/usr/bin/env python") {
            Some("python".to_string())
        } else if content.starts_with("#!/bin/bash") {
            Some("bash".to_string())
        } else if content.starts_with("#!/usr/bin/node") {
            Some("javascript".to_string())
        } else {
            None
        }
    }

    fn get_supported_languages(&self) -> Vec<String> {
        self.supported_languages.clone()
    }
}

// Test cases following unit test best practices

#[cfg(test)]
mod tests {
    use super::*;

    fn create_detector() -> TestLanguageDetector {
        TestLanguageDetector::new()
    }

    // Extension-based detection tests

    #[test]
    fn detect_rust_from_rs_extension() {
        // Arrange
        let detector = create_detector();
        let file_path = Path::new("main.rs");

        // Act
        let result = detector.detect_from_extension(file_path);

        // Assert
        assert_eq!(result, Some("rust".to_string()));
    }

    #[test]
    fn detect_python_from_py_extension() {
        // Arrange
        let detector = create_detector();
        let file_path = Path::new("script.py");

        // Act
        let result = detector.detect_from_extension(file_path);

        // Assert
        assert_eq!(result, Some("python".to_string()));
    }

    #[test]
    fn detect_javascript_from_js_extension() {
        // Arrange
        let detector = create_detector();
        let file_path = Path::new("app.js");

        // Act
        let result = detector.detect_from_extension(file_path);

        // Assert
        assert_eq!(result, Some("javascript".to_string()));
    }

    #[test]
    fn returns_none_for_unknown_extension() {
        // Arrange
        let detector = create_detector();
        let file_path = Path::new("document.txt");

        // Act
        let result = detector.detect_from_extension(file_path);

        // Assert
        assert_eq!(result, None);
    }

    #[test]
    fn returns_none_for_file_without_extension() {
        // Arrange
        let detector = create_detector();
        let file_path = Path::new("README");

        // Act
        let result = detector.detect_from_extension(file_path);

        // Assert
        assert_eq!(result, None);
    }

    #[test]
    fn handles_complex_file_path() {
        // Arrange
        let detector = create_detector();
        let file_path = Path::new("/path/to/some/deep/directory/file.rs");

        // Act
        let result = detector.detect_from_extension(file_path);

        // Assert
        assert_eq!(result, Some("rust".to_string()));
    }

    // Content-based detection tests

    #[test]
    fn detect_rust_from_content_with_main_function() {
        // Arrange
        let detector = create_detector();
        let content = "fn main() {\n    println!(\"Hello, world!\");\n}";

        // Act
        let result = detector.detect_from_content(content);

        // Assert
        assert_eq!(result, Some("rust".to_string()));
    }

    #[test]
    fn detect_python_from_content_with_def_and_import() {
        // Arrange
        let detector = create_detector();
        let content = "import os\n\ndef hello():\n    print('Hello')";

        // Act
        let result = detector.detect_from_content(content);

        // Assert
        assert_eq!(result, Some("python".to_string()));
    }

    #[test]
    fn detect_javascript_from_content_with_function_and_console() {
        // Arrange
        let detector = create_detector();
        let content = "function hello() {\n    console.log('Hello');\n}";

        // Act
        let result = detector.detect_from_content(content);

        // Assert
        assert_eq!(result, Some("javascript".to_string()));
    }

    #[test]
    fn returns_none_for_unrecognizable_content() {
        // Arrange
        let detector = create_detector();
        let content = "This is just plain text content";

        // Act
        let result = detector.detect_from_content(content);

        // Assert
        assert_eq!(result, None);
    }

    #[test]
    fn returns_none_for_empty_content() {
        // Arrange
        let detector = create_detector();
        let content = "";

        // Act
        let result = detector.detect_from_content(content);

        // Assert
        assert_eq!(result, None);
    }

    // Shebang-based detection tests

    #[test]
    fn detect_python_from_python_shebang() {
        // Arrange
        let detector = create_detector();
        let content = "#!/usr/bin/env python\nprint('Hello')";

        // Act
        let result = detector.detect_from_shebang(content);

        // Assert
        assert_eq!(result, Some("python".to_string()));
    }

    #[test]
    fn detect_bash_from_bash_shebang() {
        // Arrange
        let detector = create_detector();
        let content = "#!/bin/bash\necho 'Hello'";

        // Act
        let result = detector.detect_from_shebang(content);

        // Assert
        assert_eq!(result, Some("bash".to_string()));
    }

    #[test]
    fn detect_javascript_from_node_shebang() {
        // Arrange
        let detector = create_detector();
        let content = "#!/usr/bin/node\nconsole.log('Hello');";

        // Act
        let result = detector.detect_from_shebang(content);

        // Assert
        assert_eq!(result, Some("javascript".to_string()));
    }

    #[test]
    fn returns_none_for_unknown_shebang() {
        // Arrange
        let detector = create_detector();
        let content = "#!/usr/bin/unknown\nsome content";

        // Act
        let result = detector.detect_from_shebang(content);

        // Assert
        assert_eq!(result, None);
    }

    #[test]
    fn returns_none_for_content_without_shebang() {
        // Arrange
        let detector = create_detector();
        let content = "print('Hello')\n";

        // Act
        let result = detector.detect_from_shebang(content);

        // Assert
        assert_eq!(result, None);
    }

    #[test]
    fn returns_none_for_shebang_not_at_start() {
        // Arrange
        let detector = create_detector();
        let content = "# Comment\n#!/usr/bin/env python\nprint('Hello')";

        // Act
        let result = detector.detect_from_shebang(content);

        // Assert
        assert_eq!(result, None);
    }

    // Supported languages tests

    #[test]
    fn returns_non_empty_supported_languages_list() {
        // Arrange
        let detector = create_detector();

        // Act
        let result = detector.get_supported_languages();

        // Assert
        assert!(!result.is_empty());
    }

    #[test]
    fn supported_languages_contains_rust() {
        // Arrange
        let detector = create_detector();

        // Act
        let result = detector.get_supported_languages();

        // Assert
        assert!(result.contains(&"rust".to_string()));
    }

    #[test]
    fn supported_languages_contains_python() {
        // Arrange
        let detector = create_detector();

        // Act
        let result = detector.get_supported_languages();

        // Assert
        assert!(result.contains(&"python".to_string()));
    }

    #[test]
    fn supported_languages_contains_javascript() {
        // Arrange
        let detector = create_detector();

        // Act
        let result = detector.get_supported_languages();

        // Assert
        assert!(result.contains(&"javascript".to_string()));
    }

    #[test]
    fn supported_languages_has_expected_minimum_count() {
        // Arrange
        let detector = create_detector();

        // Act
        let result = detector.get_supported_languages();

        // Assert
        assert!(
            result.len() >= 5,
            "Expected at least 5 supported languages, got {}",
            result.len()
        );
    }

    // Edge case tests

    #[test]
    fn handles_uppercase_extensions() {
        // Arrange
        let detector = create_detector();
        let file_path = Path::new("Main.RS");

        // Act
        let result = detector.detect_from_extension(file_path);

        // Assert
        // Note: This test shows expected behavior - case sensitivity
        assert_eq!(result, None);
    }

    #[test]
    fn handles_multiple_dots_in_filename() {
        // Arrange
        let detector = create_detector();
        let file_path = Path::new("test.spec.js");

        // Act
        let result = detector.detect_from_extension(file_path);

        // Assert
        assert_eq!(result, Some("javascript".to_string()));
    }

    #[test]
    fn handles_very_long_content() {
        // Arrange
        let detector = create_detector();
        let mut content = "fn main() {\n    println!(\"Hello, world!\");\n".to_string();
        // Create a very long string
        for _ in 0..1000 {
            content.push_str("    let x = 42;\n");
        }
        content.push_str("}");

        // Act
        let result = detector.detect_from_content(&content);

        // Assert
        assert_eq!(result, Some("rust".to_string()));
    }

    #[test]
    fn handles_content_with_special_characters() {
        // Arrange
        let detector = create_detector();
        let content = "fn main() {\n    println!(\"Hello, 世界! 🦀\");\n}";

        // Act
        let result = detector.detect_from_content(content);

        // Assert
        assert_eq!(result, Some("rust".to_string()));
    }
}
