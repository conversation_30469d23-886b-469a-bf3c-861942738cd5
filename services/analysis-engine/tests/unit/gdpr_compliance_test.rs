//! Comprehensive unit tests for GDPR compliance module

#[cfg(test)]
#[cfg(feature = "security-storage")]
mod gdpr_tests {
    use analysis_engine::services::security::gdpr::*;
    use analysis_engine::storage::access_control::rbac::RbacManager;
    use analysis_engine::storage::audit::{AuditConfig, AuditService};
    use analysis_engine::storage::encryption::{EncryptionConfig, FieldEncryptionService};
    use analysis_engine::storage::spanner::SpannerOperations;
    use chrono::{Duration, Utc};
    use std::collections::HashMap;
    use std::sync::Arc;

    // Mock implementations for testing
    struct MockSpannerOperations;
    struct MockEncryptionService;
    struct MockAuditStorage;

    #[tokio::test]
    async fn test_deletion_request_creation() {
        let config = GdprConfig::default();
        assert_eq!(config.deletion_deadline_days, 30);

        let request = DeletionRequest {
            request_id: "test-123".to_string(),
            user_id: "user-456".to_string(),
            reason: "User requested deletion".to_string(),
            requested_at: Utc::now(),
            deadline: Utc::now() + Duration::days(30),
            status: DeletionStatus::Pending,
            scope: None,
            completed_at: None,
            errors: Vec::new(),
        };

        assert_eq!(request.status, DeletionStatus::Pending);
        assert!(request.errors.is_empty());
    }

    #[test]
    fn test_deletion_scope() {
        let full_scope = DeletionScope {
            analysis_requests: true,
            analysis_results: true,
            user_data: true,
            specific_analyses: None,
        };

        assert!(full_scope.analysis_requests);
        assert!(full_scope.analysis_results);
        assert!(full_scope.user_data);

        let partial_scope = DeletionScope {
            analysis_requests: false,
            analysis_results: true,
            user_data: false,
            specific_analyses: Some(vec!["analysis-1".to_string(), "analysis-2".to_string()]),
        };

        assert!(!partial_scope.analysis_requests);
        assert!(partial_scope.analysis_results);
        assert_eq!(partial_scope.specific_analyses.unwrap().len(), 2);
    }

    #[test]
    fn test_deletion_certificate() {
        use sha2::{Digest, Sha256};

        let summary = DeletionSummary {
            analysis_requests_count: 10,
            analysis_results_count: 25,
            user_data_count: 1,
            cascaded_deletions: 5,
            total_size_bytes: 1_024_000,
        };

        // Test certificate generation logic
        let content = serde_json::json!({
            "user_id": "test-user",
            "deleted_items": summary,
            "timestamp": Utc::now(),
        });

        let mut hasher = Sha256::new();
        hasher.update(serde_json::to_string(&content).unwrap());
        let hash = format!("{:x}", hasher.finalize());

        assert_eq!(hash.len(), 64); // SHA-256 produces 64 hex characters
    }

    #[test]
    fn test_export_formats() {
        assert_eq!(ExportFormat::Json.to_string(), "Json");
        assert_eq!(ExportFormat::Csv.to_string(), "Csv");
        assert_eq!(ExportFormat::Combined.to_string(), "Combined");
    }

    #[test]
    fn test_export_request_creation() {
        let request = ExportRequest {
            request_id: "export-123".to_string(),
            user_id: "user-456".to_string(),
            format: ExportFormat::Json,
            include_encrypted: true,
            requested_at: Utc::now(),
            status: ExportStatus::Pending,
            download_url: None,
            expires_at: None,
        };

        assert_eq!(request.status, ExportStatus::Pending);
        assert!(request.download_url.is_none());
        assert!(request.include_encrypted);
    }

    #[test]
    fn test_data_export_structure() {
        let export = DataExport {
            metadata: ExportMetadata {
                request_id: "export-123".to_string(),
                user_id: "user-456".to_string(),
                generated_at: Utc::now(),
                data_from: Utc::now() - Duration::days(365),
                data_to: Utc::now(),
                format_version: "1.0".to_string(),
                total_records: 42,
            },
            user_data: Some(serde_json::json!({
                "name": "Test User",
                "email": "<EMAIL>",
            })),
            analysis_requests: vec![
                serde_json::json!({
                    "id": "req-1",
                    "repository_url": "https://github.com/test/repo",
                }),
            ],
            analysis_results: vec![],
            consent_history: vec![],
        };

        assert_eq!(export.metadata.total_records, 42);
        assert!(export.user_data.is_some());
        assert_eq!(export.analysis_requests.len(), 1);
    }

    #[test]
    fn test_consent_types() {
        let consent_types = vec![
            ConsentType::DataProcessing,
            ConsentType::Analytics,
            ConsentType::Marketing,
            ConsentType::DataSharing,
            ConsentType::AutomatedDecisionMaking,
            ConsentType::Custom("newsletter".to_string()),
        ];

        // Test equality
        assert_eq!(ConsentType::DataProcessing, ConsentType::DataProcessing);
        assert_ne!(ConsentType::DataProcessing, ConsentType::Analytics);

        // Test custom type
        if let ConsentType::Custom(name) = &consent_types[5] {
            assert_eq!(name, "newsletter");
        }
    }

    #[test]
    fn test_consent_record() {
        let record = ConsentRecord {
            consent_id: "consent-123".to_string(),
            user_id: "user-456".to_string(),
            consent_type: ConsentType::Analytics,
            granted: true,
            timestamp: Utc::now(),
            ip_address: Some("***********".to_string()),
            user_agent: Some("Mozilla/5.0".to_string()),
            consent_version: "1.0".to_string(),
            metadata: None,
        };

        assert!(record.granted);
        assert_eq!(record.consent_version, "1.0");
        assert!(record.ip_address.is_some());
    }

    #[test]
    fn test_consent_status() {
        let mut consents = HashMap::new();
        consents.insert(
            ConsentType::DataProcessing,
            ConsentState {
                granted: true,
                updated_at: Utc::now(),
                version: "1.0".to_string(),
                expires_at: None,
            },
        );
        consents.insert(
            ConsentType::Analytics,
            ConsentState {
                granted: false,
                updated_at: Utc::now(),
                version: "1.0".to_string(),
                expires_at: None,
            },
        );

        let status = ConsentStatus {
            user_id: "user-456".to_string(),
            consents,
            last_updated: Utc::now(),
        };

        assert_eq!(status.consents.len(), 2);
        assert!(status.consents.get(&ConsentType::DataProcessing).unwrap().granted);
        assert!(!status.consents.get(&ConsentType::Analytics).unwrap().granted);
    }

    #[test]
    fn test_privacy_settings_defaults() {
        let settings = PrivacySettings::default();

        // All consents should default to false (privacy by design)
        for (_, granted) in &settings.default_consents {
            assert!(!granted, "All default consents should be false");
        }

        assert_eq!(settings.retention_period_days, 365 * 2); // 2 years
        assert!(settings.anonymize_after_retention);
        assert!(settings.require_explicit_consent);
        assert!(settings.privacy_preserving_analytics);
    }

    #[test]
    fn test_gdpr_audit_event_types() {
        let event_types = vec![
            GdprEventType::DeletionRequested,
            GdprEventType::DeletionCompleted,
            GdprEventType::ExportRequested,
            GdprEventType::ExportCompleted,
            GdprEventType::ExportDownloaded,
            GdprEventType::ConsentUpdated,
            GdprEventType::ConsentWithdrawn,
            GdprEventType::DataAccessRequest,
            GdprEventType::DataRectificationRequest,
        ];

        assert_eq!(event_types.len(), 9);
        assert_eq!(event_types[0], GdprEventType::DeletionRequested);
    }

    #[test]
    fn test_compliance_status() {
        let status = ComplianceStatus {
            user_id: "user-123".to_string(),
            is_compliant: true,
            has_pending_deletion: false,
            consent_status: None,
            last_privacy_review: Utc::now(),
        };

        assert!(status.is_compliant);
        assert!(!status.has_pending_deletion);
    }

    #[tokio::test]
    async fn test_consent_receipt_generation() {
        let receipt = serde_json::json!({
            "version": "1.0",
            "jurisdiction": "EU",
            "consentTimestamp": Utc::now().timestamp(),
            "collectionMethod": "WEB_FORM",
            "consentReceiptID": "receipt-123",
            "subject": {
                "userID": "user-456",
            },
            "dataController": {
                "organization": "Episteme AI",
                "contact": {
                    "email": "<EMAIL>",
                },
            },
            "services": [
                {
                    "service": "Code Analysis",
                    "purposes": [{
                        "purpose": "Analyze code repositories",
                        "consentType": "EXPLICIT",
                        "termination": "USER_REQUEST",
                    }],
                },
            ],
        });

        assert_eq!(receipt["version"], "1.0");
        assert_eq!(receipt["jurisdiction"], "EU");
        assert_eq!(receipt["collectionMethod"], "WEB_FORM");
    }

    #[test]
    fn test_deletion_status_transitions() {
        let statuses = vec![
            DeletionStatus::Pending,
            DeletionStatus::Processing,
            DeletionStatus::Completed,
            DeletionStatus::Failed,
            DeletionStatus::Cancelled,
        ];

        // Test string conversion
        assert_eq!(statuses[0].to_string(), "Pending");
        assert_eq!(statuses[1].to_string(), "Processing");
        assert_eq!(statuses[2].to_string(), "Completed");
    }

    #[test]
    fn test_export_status_transitions() {
        let statuses = vec![
            ExportStatus::Pending,
            ExportStatus::Processing,
            ExportStatus::Ready,
            ExportStatus::Failed,
            ExportStatus::Expired,
        ];

        // Test string conversion
        assert_eq!(statuses[0].to_string(), "Pending");
        assert_eq!(statuses[2].to_string(), "Ready");
        assert_eq!(statuses[4].to_string(), "Expired");
    }

    #[test]
    fn test_gdpr_config() {
        let config = GdprConfig::default();

        assert_eq!(config.deletion_deadline_days, 30);
        assert_eq!(config.export_expiration_hours, 48);
        assert!(config.enable_auto_anonymization);
        assert_eq!(config.batch_size, 100);

        // Test privacy settings
        assert_eq!(config.privacy_settings.retention_period_days, 365 * 2);
    }

    #[test]
    fn test_consent_update_request() {
        let update = ConsentUpdate {
            user_id: "user-123".to_string(),
            consent_type: ConsentType::Marketing,
            granted: false,
            ip_address: Some("********".to_string()),
            user_agent: Some("Chrome/91.0".to_string()),
        };

        assert_eq!(update.user_id, "user-123");
        assert!(!update.granted);
        assert!(matches!(update.consent_type, ConsentType::Marketing));
    }

    #[test]
    fn test_export_compression() {
        use flate2::write::GzEncoder;
        use flate2::Compression;
        use std::io::Write;

        let data = b"This is test data for GDPR export compression";
        let mut encoder = GzEncoder::new(Vec::new(), Compression::default());
        encoder.write_all(data).unwrap();
        let compressed = encoder.finish().unwrap();

        assert!(compressed.len() < data.len());
    }

    #[test]
    fn test_csv_export_format() {
        use csv::Writer;

        let mut buffer = Vec::new();
        {
            let mut wtr = Writer::from_writer(&mut buffer);
            wtr.write_record(&["consent_id", "user_id", "type", "granted"]).unwrap();
            wtr.write_record(&["c1", "u1", "Analytics", "true"]).unwrap();
            wtr.write_record(&["c2", "u1", "Marketing", "false"]).unwrap();
            wtr.flush().unwrap();
        }

        let csv_content = String::from_utf8(buffer).unwrap();
        assert!(csv_content.contains("consent_id,user_id,type,granted"));
        assert!(csv_content.contains("Analytics"));
        assert!(csv_content.contains("Marketing"));
    }
}