//! Integration tests for contract validation
use analysis_engine::{
    contracts::{AnalysisOutput, Repository, Analysis, Metadata, FileAnalysis, ASTNode, Range, Position, FileMetrics, RepositoryMetrics, LanguageBreakdown, LanguageStats, PerformanceMetrics},
    validation::{ContractValidator, ValidationConfig, validate_ast_output},
};
use chrono::Utc;
use std::collections::HashMap;

fn create_valid_analysis_output() -> AnalysisOutput {
    AnalysisOutput {
        repository: Repository {
            id: "repo_a1b2c3d4e5f6g7h8".to_string(),
            url: "https://github.com/example/repo".to_string(),
            commit: "a1b2c3d4e5f67890abcdef1234567890abcdef12".to_string(), // 40 char hex
            branch: "main".to_string(),
            size_bytes: 1024,
            clone_time_ms: 500,
        },
        analysis: Analysis {
            files: vec![
                FileAnalysis {
                    path: "src/main.rs".to_string(),
                    language: "rust".to_string(),
                    content_hash: "a1b2c3d4e5f67890abcdef1234567890abcdef1234567890abcdef1234567890".to_string(), // 64 char hex
                    size_bytes: 1024,
                    ast: ASTNode {
                        node_type: "module".to_string(),
                        name: Some("main".to_string()),
                        range: Range {
                            start: Position { line: 1, column: 0, byte: 0 },
                            end: Position { line: 100, column: 0, byte: 1024 },
                        },
                        children: vec![],
                        properties: Some(HashMap::new()), // Empty map instead of None
                        text: Some("".to_string()), // Empty string instead of None
                    },
                    metrics: FileMetrics {
                        lines_of_code: 100,
                        total_lines: 120,
                        complexity: 10,
                        maintainability_index: 75.5,
                        function_count: 5,
                        class_count: 2,
                        comment_ratio: 0.15,
                    },
                    chunks: vec![],
                    symbols: vec![],
                }
            ],
            metrics: RepositoryMetrics {
                total_files: 1,
                total_lines: 120,
                total_complexity: 10,
                average_complexity: 10.0,
                maintainability_score: 75.5,
                technical_debt_minutes: 30,
                test_coverage_estimate: Some(0.8), // Value between 0 and 1
            },
            languages: LanguageBreakdown {
                primary_language: "rust".to_string(),
                languages: {
                    let mut map = HashMap::new();
                    map.insert("rust".to_string(), LanguageStats {
                        lines: 120,
                        files: 1,
                        percentage: 100.0,
                        bytes: 1024,
                    });
                    map
                },
            },
            embeddings: vec![],
            patterns: vec![],
        },
        metadata: Metadata {
            analysis_id: "analysis_a1b2c3d4e5f6g7h8".to_string(),
            version: "0.1.0".to_string(),
            timestamp: Utc::now(),
            duration_ms: 1000,
            performance: PerformanceMetrics {
                parsing_time_ms: 500,
                embedding_time_ms: 100,
                total_memory_mb: 100.0,
                files_per_second: Some(10.0),
                cache_hit_ratio: Some(0.8),
            },
            warnings: vec![],
        },
    }
}

#[test]
fn test_valid_output_passes_validation() {
    let output = create_valid_analysis_output();
    let result = validate_ast_output(&output);
    
    if !result.is_valid() {
        if let Some(errors) = result.errors() {
            for error in errors {
                eprintln!("Validation error: {:?}", error);
            }
        }
    }
    
    assert!(result.is_valid(), "Valid output should pass validation");
}

#[test]
fn test_invalid_repository_id_fails() {
    let mut output = create_valid_analysis_output();
    output.repository.id = "invalid_repo_id".to_string();
    
    let result = validate_ast_output(&output);
    
    assert!(!result.is_valid(), "Invalid repository ID should fail validation");
    if let Some(errors) = result.errors() {
        // Print errors for debugging
        for error in errors {
            eprintln!("Error: {:?}", error);
        }
        // Schema validation will catch this before our custom validation
        assert!(errors.iter().any(|e| e.code == "SCHEMA_VALIDATION_ERROR" && e.path == "/repository/id"));
    }
}

#[test]
fn test_invalid_analysis_id_fails() {
    let mut output = create_valid_analysis_output();
    output.metadata.analysis_id = "invalid_id".to_string();
    
    let result = validate_ast_output(&output);
    
    assert!(!result.is_valid(), "Invalid analysis ID should fail validation");
    if let Some(errors) = result.errors() {
        // Schema validation will catch this before our custom validation
        assert!(errors.iter().any(|e| e.code == "SCHEMA_VALIDATION_ERROR" && e.path == "/metadata/analysis_id"));
    }
}

#[test]
fn test_contract_validator_with_config() {
    let config = ValidationConfig {
        enabled: true,
        validate_responses: true,
        max_validation_time_ms: 100,
        schema_cache_size: 10,
    };
    
    let validator = ContractValidator::new(config);
    let output = create_valid_analysis_output();
    
    let result = validator.validate(&output);
    assert!(result.is_valid());
    
    // Check metrics
    let metrics = validator.metrics();
    assert_eq!(metrics.total_validations, 1);
    assert_eq!(metrics.successful_validations, 1);
    assert_eq!(metrics.failed_validations, 0);
}

#[test]
fn test_disabled_validation_always_passes() {
    let config = ValidationConfig {
        enabled: false,
        ..Default::default()
    };
    
    let validator = ContractValidator::new(config);
    let mut output = create_valid_analysis_output();
    output.repository.id = "invalid".to_string(); // This would normally fail
    
    let result = validator.validate(&output);
    assert!(result.is_valid(), "Validation should pass when disabled");
}

#[test]
fn test_multiple_validation_errors() {
    let mut output = create_valid_analysis_output();
    output.repository.id = "bad_id".to_string();
    output.metadata.analysis_id = "wrong_format".to_string();
    
    let result = validate_ast_output(&output);
    
    assert!(!result.is_valid());
    if let Some(errors) = result.errors() {
        assert!(errors.len() >= 2, "Should have at least 2 validation errors");
        // Schema validation will catch this before our custom validation
        assert!(errors.iter().any(|e| e.code == "SCHEMA_VALIDATION_ERROR" && e.path == "/repository/id"));
        // Schema validation will catch this before our custom validation
        assert!(errors.iter().any(|e| e.code == "SCHEMA_VALIDATION_ERROR" && e.path == "/metadata/analysis_id"));
    }
}