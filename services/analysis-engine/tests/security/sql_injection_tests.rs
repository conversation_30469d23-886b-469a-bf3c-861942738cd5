//! SQL Injection Prevention Tests
//! 
//! Tests to ensure all database queries use parameterized statements
//! and are protected against SQL injection attacks.

use anyhow::Result;
use google_cloud_spanner::client::Client;
use google_cloud_spanner::statement::Statement;
use std::collections::HashMap;

/// Test helper to validate that queries use only parameterized statements
pub struct SqlInjectionValidator;

impl SqlInjectionValidator {
    /// Check if a query string contains potential SQL injection vulnerabilities
    pub fn validate_query_safety(query: &str) -> Result<()> {
        // Check for common SQL injection patterns
        let dangerous_patterns = [
            // String concatenation patterns that could be vulnerable
            "format!(",
            "&format!(",
            "push_str(&format!",
            
            // Direct string interpolation in SQL
            "${",
            "#{",
            
            // Unparameterized values in common SQL clauses
            "WHERE.*=.*[^@]", // WHERE clause without parameter
            "LIMIT [0-9]+",   // Direct LIMIT value
            "OFFSET [0-9]+",  // Direct OFFSET value
            
            // Comment injection attempts
            "--",
            "/*",
            "*/",
            
            // Union injection patterns
            "UNION",
            "union",
        ];
        
        for pattern in &dangerous_patterns {
            if query.contains(pattern) {
                // Special handling for LIMIT/OFFSET - allow if properly parameterized
                if pattern.starts_with("LIMIT") || pattern.starts_with("OFFSET") {
                    if query.contains("@limit") || query.contains("@offset") {
                        continue; // Properly parameterized
                    }
                }
                
                return Err(anyhow::anyhow!(
                    "Potentially unsafe SQL pattern detected: {} in query: {}",
                    pattern,
                    query
                ));
            }
        }
        
        Ok(())
    }
    
    /// Validate that all parameters in a query are properly bound
    pub fn validate_parameter_binding(query: &str, params: &HashMap<String, String>) -> Result<()> {
        // Find all parameter placeholders in the query
        let param_regex = regex::Regex::new(r"@(\w+)").unwrap();
        let query_params: std::collections::HashSet<String> = param_regex
            .captures_iter(query)
            .map(|cap| cap[1].to_string())
            .collect();
        
        // Check that all parameters in the query are bound
        for param in &query_params {
            if !params.contains_key(param) {
                return Err(anyhow::anyhow!(
                    "Unbound parameter @{} found in query: {}",
                    param,
                    query
                ));
            }
        }
        
        // Check for unused parameters (potential typos)
        for (param_name, _) in params {
            if !query_params.contains(param_name) {
                tracing::warn!("Unused parameter {} provided for query", param_name);
            }
        }
        
        Ok(())
    }
    
    /// Test SQL injection resistance with malicious inputs
    pub fn test_injection_resistance(base_query: &str, test_values: Vec<&str>) -> Result<()> {
        for malicious_value in test_values {
            // Create a statement and attempt to bind the malicious value
            let mut statement = Statement::new(base_query);
            
            // These should be safely parameterized and not cause injection
            statement.add_param("test_param", &malicious_value);
            
            // If we get here without panicking, the parameterization worked
            tracing::info!("Successfully parameterized malicious input: {}", malicious_value);
        }
        
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_detect_unsafe_query_patterns() {
        // Test cases that should be flagged as unsafe
        let unsafe_queries = vec![
            "SELECT * FROM users WHERE id = " + "user_input",
            "SELECT * FROM users LIMIT 100",  // Unparameterized LIMIT
            "SELECT * FROM users OFFSET 50",  // Unparameterized OFFSET
            "SELECT * FROM users -- comment",
            "SELECT * FROM users /* comment */",
        ];
        
        for query in unsafe_queries {
            assert!(SqlInjectionValidator::validate_query_safety(query).is_err(),
                    "Query should be flagged as unsafe: {}", query);
        }
    }
    
    #[test]
    fn test_allow_safe_query_patterns() {
        // Test cases that should be considered safe
        let safe_queries = vec![
            "SELECT * FROM users WHERE id = @user_id",
            "SELECT * FROM users LIMIT @limit OFFSET @offset",
            "SELECT * FROM users WHERE status = @status AND created_at > @date",
            "INSERT INTO users (name, email) VALUES (@name, @email)",
            "UPDATE users SET status = @status WHERE id = @id",
        ];
        
        for query in safe_queries {
            assert!(SqlInjectionValidator::validate_query_safety(query).is_ok(),
                    "Query should be considered safe: {}", query);
        }
    }
    
    #[test]
    fn test_parameter_binding_validation() {
        let query = "SELECT * FROM users WHERE id = @user_id AND status = @status";
        
        // Test with all parameters bound
        let mut params = HashMap::new();
        params.insert("user_id".to_string(), "123".to_string());
        params.insert("status".to_string(), "active".to_string());
        
        assert!(SqlInjectionValidator::validate_parameter_binding(query, &params).is_ok());
        
        // Test with missing parameter
        params.remove("status");
        assert!(SqlInjectionValidator::validate_parameter_binding(query, &params).is_err());
    }
    
    #[test]
    fn test_sql_injection_resistance() {
        let query = "SELECT * FROM users WHERE email = @test_param";
        
        let malicious_inputs = vec![
            "'; DROP TABLE users; --",
            "' OR '1'='1",
            "'; INSERT INTO users (email) VALUES ('<EMAIL>'); --",
            "admin'/*",
            "' UNION SELECT password FROM admin_users --",
            "'; EXEC xp_cmdshell('del c:\\*.*'); --",
        ];
        
        // This should not panic or cause issues because inputs are parameterized
        assert!(SqlInjectionValidator::test_injection_resistance(query, malicious_inputs).is_ok());
    }
    
    #[test]
    fn test_numeric_injection_patterns() {
        let malicious_numeric_inputs = vec![
            "1; DROP TABLE users; --",
            "1 OR 1=1",
            "1 UNION SELECT * FROM passwords",
        ];
        
        let query = "SELECT * FROM users WHERE id = @test_param";
        assert!(SqlInjectionValidator::test_injection_resistance(query, malicious_numeric_inputs).is_ok());
    }
}

/// Integration tests for Spanner operations
#[cfg(test)]
mod integration_tests {
    use super::*;
    use crate::storage::spanner::SpannerOperations;
    use crate::models::{ListAnalysesParams, AnalysisStatus};
    use chrono::Utc;
    
    // Note: These tests require a test Spanner instance
    // In real implementation, you'd use a test database or mock
    
    #[tokio::test]
    #[ignore = "Requires Spanner test instance"]
    async fn test_list_analyses_injection_resistance() -> Result<()> {
        // This test would require setting up a test Spanner instance
        // For now, it's marked as ignored but shows the pattern
        
        let spanner_ops = create_test_spanner_ops().await?;
        
        // Test with potentially malicious inputs in parameters
        let malicious_params = ListAnalysesParams {
            status: Some(AnalysisStatus::Completed),
            repository_url: Some("'; DROP TABLE analyses; --".to_string()),
            created_after: Some(Utc::now()),
            created_before: None,
            page: Some(1),
            per_page: Some(20),
        };
        
        // This should safely handle the malicious input due to parameterization
        let result = spanner_ops.list_analyses(&malicious_params).await;
        
        // The query should execute safely (though likely return no results)
        // and not cause any SQL injection
        match result {
            Ok(_) => {
                // Success - malicious input was safely parameterized
                tracing::info!("SQL injection test passed - malicious input safely handled");
            }
            Err(e) => {
                // Check that it's not a SQL injection error but a normal query error
                let error_msg = e.to_string().to_lowercase();
                assert!(!error_msg.contains("syntax error"), 
                        "SQL injection may have occurred: {}", e);
                assert!(!error_msg.contains("unexpected token"), 
                        "SQL injection may have occurred: {}", e);
            }
        }
        
        Ok(())
    }
    
    async fn create_test_spanner_ops() -> Result<SpannerOperations> {
        // In a real test, you'd create a test client
        // This is just a placeholder showing the pattern
        todo!("Implement test Spanner client creation")
    }
}