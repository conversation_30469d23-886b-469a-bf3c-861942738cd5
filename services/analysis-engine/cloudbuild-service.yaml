# Cloud Build configuration for Analysis Engine (Rust service)
steps:
  # 1. Code quality checks
  - name: 'rust:1.70'
    id: 'quality-checks'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        set -e
        cd /workspace
        
        echo "Installing Rust toolchain..."
        rustup component add rustfmt clippy
        
        echo "Checking code formatting..."
        cargo fmt -- --check
        
        echo "Running Clippy (linting)..."
        cargo clippy --all-targets --all-features -- -D warnings
        
        echo "Running unit tests..."
        cargo test --all-features --verbose
        
        echo "Building release binary..."
        cargo build --release

  # 2. Security scanning
  - name: 'rust:1.70'
    id: 'security-scan'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        echo "Installing cargo-audit..."
        cargo install cargo-audit
        
        echo "Running security audit..."
        cargo audit
        
        echo "Checking for unsafe code..."
        grep -r "unsafe " src/ && echo "WARNING: Found unsafe code blocks" || echo "No unsafe code found"
    waitFor: ['quality-checks']

  # 3. Build Docker image
  - name: 'gcr.io/cloud-builders/docker'
    id: 'build-image'
    args:
      - 'build'
      - '-t'
      - '${_IMAGE_REGISTRY}/analysis-engine:$BUILD_ID'
      - '-t'
      - '${_IMAGE_REGISTRY}/analysis-engine:latest'
      - '.'
    waitFor: ['security-scan']

  # 4. Test Docker image
  - name: 'gcr.io/cloud-builders/docker'
    id: 'test-image'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        echo "Testing Docker image..."
        
        # Run container and test endpoints
        docker run -d --name test-analysis-engine -p 8001:8001 \
          ${_IMAGE_REGISTRY}/analysis-engine:$BUILD_ID
        
        sleep 10
        
        # Health check
        curl -f http://localhost:8001/health || exit 1
        
        # Ready check  
        curl -f http://localhost:8001/ready || exit 1
        
        echo "Docker image tests passed"
        docker stop test-analysis-engine
    waitFor: ['build-image']

  # 5. Push images
  - name: 'gcr.io/cloud-builders/docker'
    id: 'push-images'
    args:
      - 'push'
      - '--all-tags'
      - '${_IMAGE_REGISTRY}/analysis-engine'
    waitFor: ['test-image']

  # 6. Deploy to development
  - name: 'gcr.io/cloud-builders/gcloud'
    id: 'deploy-dev'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        if [ "$BRANCH_NAME" = "develop" ] || [ "$BRANCH_NAME" = "main" ]; then
          echo "Deploying Analysis Engine to development environment..."
          
          gcloud run deploy analysis-engine-dev \
            --image ${_IMAGE_REGISTRY}/analysis-engine:$BUILD_ID \
            --platform managed \
            --region us-central1 \
            --memory 2Gi \
            --cpu 2 \
            --max-instances 10 \
            --min-instances 0 \
            --timeout 300s \
            --concurrency 1000 \
            --port 8001 \
            --set-env-vars "ENVIRONMENT=development,VERSION=$BUILD_ID,RUST_LOG=info" \
            --allow-unauthenticated
          
          echo "Development deployment complete"
        else
          echo "Skipping development deployment - not develop or main branch"
        fi
    waitFor: ['push-images']

# Substitutions
substitutions:
  _IMAGE_REGISTRY: 'us-central1-docker.pkg.dev/vibe-match-463114/ccl-services'

# Options
options:
  machineType: 'E2_HIGHCPU_32'
  diskSizeGb: 200
  logging: CLOUD_LOGGING_ONLY

# Timeout
timeout: '2400s'

# Artifacts
artifacts:
  images:
    - '${_IMAGE_REGISTRY}/analysis-engine:$BUILD_ID'
    - '${_IMAGE_REGISTRY}/analysis-engine:latest'