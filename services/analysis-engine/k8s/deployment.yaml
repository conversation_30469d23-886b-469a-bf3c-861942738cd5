apiVersion: apps/v1
kind: Deployment
metadata:
  name: analysis-engine
  namespace: episteme
  labels:
    app: analysis-engine
    tier: backend
    version: v1
spec:
  replicas: 3
  selector:
    matchLabels:
      app: analysis-engine
  template:
    metadata:
      labels:
        app: analysis-engine
        tier: backend
        version: v1
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8001"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: analysis-engine
      containers:
      - name: analysis-engine
        image: gcr.io/ccl-platform/analysis-engine:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8001
          name: http
          protocol: TCP
        env:
        - name: PORT
          value: "8001"
        - name: ENVIRONMENT
          value: "production"
        - name: GCP_PROJECT_ID
          value: "ccl-platform"
        - name: ENABLE_RESOURCE_MONITORING
          value: "true"
        - name: CPU_LIMIT_PERCENT
          value: "85"  # Lower than container limit to prevent throttling
        - name: MEMORY_LIMIT_MB
          value: "3584"  # 3.5GB, leaving 512MB buffer
        - name: MAX_CONCURRENT_ANALYSES
          value: "30"
        - name: RUST_LOG
          value: "info,analysis_engine=debug"
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"  # 1 CPU core
            ephemeral-storage: "5Gi"
          limits:
            memory: "4Gi"
            cpu: "2000m"  # 2 CPU cores
            ephemeral-storage: "10Gi"
        livenessProbe:
          httpGet:
            path: /health/live
            port: 8001
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health/ready
            port: 8001
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /health
            port: 8001
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 30
        volumeMounts:
        - name: temp
          mountPath: /tmp
        securityContext:
          runAsNonRoot: true
          runAsUser: 1001
          readOnlyRootFilesystem: true
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
      volumes:
      - name: temp
        emptyDir: {}
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - analysis-engine
              topologyKey: kubernetes.io/hostname
      topologySpreadConstraints:
      - maxSkew: 1
        topologyKey: topology.kubernetes.io/zone
        whenUnsatisfiable: DoNotSchedule
        labelSelector:
          matchLabels:
            app: analysis-engine
---
apiVersion: v1
kind: Service
metadata:
  name: analysis-engine
  namespace: episteme
  labels:
    app: analysis-engine
spec:
  type: ClusterIP
  ports:
  - port: 8001
    targetPort: 8001
    protocol: TCP
    name: http
  selector:
    app: analysis-engine
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: analysis-engine
  namespace: episteme
  annotations:
    iam.gke.io/gcp-service-account: <EMAIL>
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: analysis-engine
  namespace: episteme
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: analysis-engine
  minReplicas: 3
  maxReplicas: 20
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  - type: Pods
    pods:
      metric:
        name: http_requests_per_second
      target:
        type: AverageValue
        averageValue: "100"
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
      - type: Pods
        value: 2
        periodSeconds: 60
      selectPolicy: Min
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 30
      - type: Pods
        value: 4
        periodSeconds: 30
      selectPolicy: Max
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: analysis-engine
  namespace: episteme
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: analysis-engine
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: analysis-engine-config
  namespace: episteme
data:
  # Resource optimization settings
  resource-limits.yaml: |
    cpu:
      limit_percent: 85
      warning_threshold: 75
      monitoring_interval_ms: 5000
    memory:
      limit_mb: 3584
      warning_threshold_percent: 85
      monitoring_interval_seconds: 5
    container:
      cpu_cores: 2.0
      memory_mb: 4096
  # Application configuration
  app-config.yaml: |
    analysis:
      max_concurrent_analyses: 30
      max_repository_size_gb: 5
      analysis_timeout_seconds: 300
      max_file_size_mb: 50
    backpressure:
      max_concurrent_parsing: 100
      max_concurrent_database: 50
      max_concurrent_storage: 75
      queue_size_threshold: 500