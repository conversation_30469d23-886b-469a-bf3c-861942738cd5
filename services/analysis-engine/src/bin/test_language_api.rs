use analysis_engine::parser::language_registry::get_language;
use tree_sitter::Language;

fn main() {
    println!("Testing tree-sitter language API patterns\n");

    // Test 1: Try calling LANGUAGE as a function (newer pattern)
    println!("Test 1: Newer crates with LANGUAGE constant as function pointer");
    test_language("rust");

    // Test 2: Try using language() function (older pattern)
    println!("\nTest 2: Older crates with language() function");
    test_language("yaml");
}

fn test_language(name: &str) {
    match get_language(name) {
        Some(language) => {
            println!("✓ {}: get_language(\"{}\") works", name, name);
            println!("  Language node count: {}", language.node_kind_count());
        }
        None => {
            println!("✗ {}: get_language(\"{}\") failed", name, name);
        }
    }
}