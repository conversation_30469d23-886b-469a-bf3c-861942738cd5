/// Test binary to verify unsafe_bindings module works correctly
/// This tests our centralized unsafe operations in isolation

use analysis_engine::parser::unsafe_bindings::{load_language_unsafe, is_language_supported, supported_languages, LanguageLoadError};

fn main() {
    println!("Testing unsafe_bindings module...\n");

    // Test 1: Check supported languages
    println!("1. Testing supported languages:");
    let languages = supported_languages();
    println!("   Found {} supported languages", languages.len());
    println!("   Sample languages: {:?}", &languages[..5.min(languages.len())]);
    
    // Test 2: Test language support check
    println!("\n2. Testing language support check:");
    assert!(is_language_supported("rust"), "Rust should be supported");
    assert!(is_language_supported("python"), "Python should be supported");
    assert!(is_language_supported("javascript"), "JavaScript should be supported");
    assert!(!is_language_supported("invalid_language"), "Invalid language should not be supported");
    println!("   ✓ Language support checks passed");

    // Test 3: Test loading supported languages
    println!("\n3. Testing language loading:");
    
    // Test Rust
    match load_language_unsafe("rust") {
        Ok(lang) => {
            println!("   ✓ Successfully loaded Rust language");
            println!("     Language version: {}", lang.version());
        }
        Err(e) => {
            eprintln!("   ✗ Failed to load Rust: {}", e);
            std::process::exit(1);
        }
    }
    
    // Test Python
    match load_language_unsafe("python") {
        Ok(lang) => {
            println!("   ✓ Successfully loaded Python language");
            println!("     Language version: {}", lang.version());
        }
        Err(e) => {
            eprintln!("   ✗ Failed to load Python: {}", e);
            std::process::exit(1);
        }
    }
    
    // Test JavaScript
    match load_language_unsafe("javascript") {
        Ok(lang) => {
            println!("   ✓ Successfully loaded JavaScript language");
            println!("     Language version: {}", lang.version());
        }
        Err(e) => {
            eprintln!("   ✗ Failed to load JavaScript: {}", e);
            std::process::exit(1);
        }
    }

    // Test 4: Test error handling for unsupported language
    println!("\n4. Testing error handling:");
    match load_language_unsafe("invalid_language") {
        Ok(_) => {
            eprintln!("   ✗ Should have failed for invalid language");
            std::process::exit(1);
        }
        Err(LanguageLoadError::Unsupported(lang)) => {
            println!("   ✓ Correctly rejected unsupported language: {}", lang);
        }
        Err(e) => {
            eprintln!("   ✗ Unexpected error type: {}", e);
            std::process::exit(1);
        }
    }

    // Test 5: Test all supported languages can be loaded
    println!("\n5. Testing all supported languages:");
    let mut success_count = 0;
    let mut failure_count = 0;
    
    for &lang_name in &languages {
        match load_language_unsafe(lang_name) {
            Ok(_) => {
                success_count += 1;
                print!("✓");
            }
            Err(e) => {
                failure_count += 1;
                print!("✗");
                eprintln!("\n   Failed to load {}: {}", lang_name, e);
            }
        }
    }
    
    println!("\n   Results: {} successful, {} failed out of {} total", 
             success_count, failure_count, languages.len());
    
    if failure_count == 0 {
        println!("\n🎉 All tests passed! Unsafe bindings module is working correctly.");
        println!("✅ All {} unsafe calls have been successfully centralized.", languages.len());
    } else {
        eprintln!("\n❌ Some tests failed. Check the errors above.");
        std::process::exit(1);
    }
}
