// Test program to verify all languages in the registry are working
use analysis_engine::parser::language_registry::{supported_languages, get_language};

fn main() {
    println!("Testing Language Registry...\n");
    
    let languages = supported_languages();
    println!("Total languages supported: {}", languages.len());
    println!("Languages: {:?}\n", languages);
    
    // Test each language
    let mut success_count = 0;
    let mut failed_languages: Vec<&'static str> = Vec::new();
    
    for lang_name in languages {
        match get_language(lang_name) {
            Some(_lang) => {
                println!("✓ {} - loaded successfully", lang_name);
                success_count += 1;
            }
            None => {
                println!("✗ {} - FAILED to load", lang_name);
                failed_languages.push(lang_name);
            }
        }
    }
    
    println!("\n=== Summary ===");
    println!("Successful: {}/{}", success_count, languages.len());
    if !failed_languages.is_empty() {
        println!("Failed: {:?}", failed_languages);
    } else {
        println!("All languages loaded successfully!");
    }
    
    // Test some specific languages
    println!("\n=== Language Details ===");
    for lang in &["rust", "python", "javascript", "go", "java"] {
        if let Some(_) = get_language(lang) {
            println!("{}: Available ✓", lang);
        } else {
            println!("{}: Not available ✗", lang);
        }
    }
}