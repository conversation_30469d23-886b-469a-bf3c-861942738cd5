use anyhow::Result;
use analysis_engine::services::ai_test::AIServicesTest;
use tracing::info;
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

#[tokio::main]
async fn main() -> Result<()> {
    // Load environment variables
    dotenvy::dotenv().ok();

    // Initialize tracing
    tracing_subscriber::registry()
        .with(
            tracing_subscriber::EnvFilter::try_from_default_env()
                .unwrap_or_else(|_| "analysis_engine=debug".into()),
        )
        .with(tracing_subscriber::fmt::layer())
        .init();

    info!("Starting AI Services Integration Test");
    info!("Project ID: {}", std::env::var("GCP_PROJECT_ID").unwrap_or_else(|_| "not set".to_string()));
    info!("Region: {}", std::env::var("GCP_REGION").unwrap_or_else(|_| "not set".to_string()));
    info!("Gemini Model: {}", std::env::var("GEMINI_MODEL_NAME").unwrap_or_else(|_| "not set".to_string()));
    info!("Gemini Pro Model: {}", std::env::var("GEMINI_PRO_MODEL_NAME").unwrap_or_else(|_| "not set".to_string()));

    // Check for credentials
    if std::env::var("GOOGLE_APPLICATION_CREDENTIALS").is_err() {
        eprintln!("ERROR: GOOGLE_APPLICATION_CREDENTIALS not set");
        eprintln!("Please set: export GOOGLE_APPLICATION_CREDENTIALS=/path/to/service-account-key.json");
        std::process::exit(1);
    }

    // Initialize AI services
    let ai_services = match AIServicesTest::new().await {
        Ok(services) => {
            info!("✓ AI services initialized successfully");
            services
        }
        Err(e) => {
            eprintln!("✗ Failed to initialize AI services: {}", e);
            eprintln!("\nThis usually indicates missing IAM permissions or configuration issues.");
            std::process::exit(1);
        }
    };

    // Test AI Pattern Detection
    println!("\n=== Testing AI Pattern Detection ===");
    match ai_services.test_ai_pattern_detection().await {
        Ok(_) => println!("✓ AI Pattern Detection test passed"),
        Err(e) => {
            println!("✗ AI Pattern Detection test failed: {}", e);
            if e.to_string().contains("403") || e.to_string().contains("permission") {
                println!("  → This appears to be an IAM permission issue");
                println!("  → The service account needs 'roles/aiplatform.user' role");
            }
        }
    }

    // Test Code Quality Assessment
    println!("\n=== Testing Code Quality Assessment ===");
    match ai_services.test_code_quality_assessment().await {
        Ok(_) => println!("✓ Code Quality Assessment test passed"),
        Err(e) => {
            println!("✗ Code Quality Assessment test failed: {}", e);
            if e.to_string().contains("403") || e.to_string().contains("permission") {
                println!("  → This appears to be an IAM permission issue");
            }
        }
    }

    // Test Semantic Search
    println!("\n=== Testing Semantic Search ===");
    match ai_services.test_semantic_search().await {
        Ok(_) => println!("✓ Semantic Search test passed"),
        Err(e) => {
            println!("✗ Semantic Search test failed: {}", e);
            if e.to_string().contains("403") || e.to_string().contains("permission") {
                println!("  → This appears to be an IAM permission issue for embeddings API");
            }
        }
    }

    // Test Repository Insights
    println!("\n=== Testing Repository Insights ===");
    match ai_services.test_repository_insights().await {
        Ok(_) => println!("✓ Repository Insights test passed"),
        Err(e) => {
            println!("✗ Repository Insights test failed: {}", e);
            if e.to_string().contains("403") || e.to_string().contains("permission") {
                println!("  → This appears to be an IAM permission issue");
            }
        }
    }

    // Print feature toggles status
    println!("\n=== Feature Toggles Status ===");
    let toggles = ai_services.embeddings_service.get_feature_toggles();
    println!("AI Pattern Detection: {}", if toggles.enable_ai_pattern_detection { "✓ Enabled" } else { "✗ Disabled" });
    println!("Code Quality Assessment: {}", if toggles.enable_code_quality_assessment { "✓ Enabled" } else { "✗ Disabled" });
    println!("Semantic Search: {}", if toggles.enable_semantic_search { "✓ Enabled" } else { "✗ Disabled" });
    println!("Repository Insights: {}", if toggles.enable_repository_insights { "✓ Enabled" } else { "✗ Disabled" });
    println!("Embeddings: {}", if toggles.enable_embeddings { "✓ Enabled" } else { "✗ Disabled" });
    println!("Fallback Embeddings: {}", if toggles.use_fallback_embeddings { "✓ Enabled" } else { "✗ Disabled" });

    println!("\n=== Test Summary ===");
    println!("If tests failed with 403 errors, you need to grant IAM permissions:");
    println!("1. Run: ./test_ai_integration.sh");
    println!("2. Follow the IAM permission fix commands shown");
    println!("3. Wait 1-2 minutes for permissions to propagate");
    println!("4. Run this test again");

    Ok(())
}