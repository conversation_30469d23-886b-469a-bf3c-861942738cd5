fn main() {
    // Test what type LANGUAGE constants are
    println!("Checking tree-sitter API patterns...\n");
    
    // Check if rust has a language() function
    // #[cfg(feature = "check-rust-fn")]
    // {
    //     let _lang = tree_sitter_rust::language();
    //     println!("✓ rust has language() function");
    // }
    
    // // Check if rust has LANGUAGE constant
    // #[cfg(feature = "check-rust-const")]
    // {
    //     let lang_fn = tree_sitter_rust::LANGUAGE;
    //     // LANGUAGE is likely a LanguageFn type
    //     let _lang = unsafe { lang_fn() };
    //     println!("✓ rust has LANGUAGE constant (LanguageFn type)");
    // }
    
    // Check newer crates pattern
    println!("\nChecking pattern for newer crates:");
    
    // Most newer tree-sitter crates export LANGUAGE as a LanguageFn
    // which needs to be called with unsafe to get the Language
    
    println!("Pattern seems to be:");
    println!("- Older crates (< 0.20): export language() function");
    println!("- Newer crates (>= 0.20): export LANGUAGE as LanguageFn");
    println!("- LanguageFn needs unsafe {{ LANGUAGE() }} to get Language");
}