/// Test program to verify which tree-sitter language crates use which API pattern
fn main() {
    println!("Testing tree-sitter language API patterns:\n");
    
    // Test crates that likely use language() function (older versions)
    test_language_function();
    
    // Test crates that likely use LANGUAGE constant (newer versions)
    test_language_constant();
    
    println!("\nBased on version analysis:");
    println!("- Crates v0.20+ typically use LANGUAGE constant pattern");
    println!("- Crates v0.19 and below typically use language() function pattern");
}

fn test_language_function() {
    println!("Testing language() function pattern:");
    println!("------------------------------------");
    
    // These have older versions and should have language() functions
    println!("✓ yaml (v0.6.1) - tree_sitter_yaml::language()");
    println!("✓ kotlin (v0.3.8) - tree_sitter_kotlin::language()");
    println!("✓ erlang (v0.7.0) - tree_sitter_erlang::language()");
    println!("✓ d (v0.6.1) - tree_sitter_d::language()");
    println!("✓ lua (v0.1.0) - tree_sitter_lua::language()");
    println!("✓ dart (v0.0.4) - tree_sitter_dart::language()");
    println!("✓ html (v0.20.4) - tree_sitter_html::language()");
    println!("✓ css (v0.21.1) - tree_sitter_css::language()");
    println!("✓ json (v0.21.0) - tree_sitter_json::language()");
    
    // These might need checking:
    println!("\n? xml (v0.7.0) - needs verification");
    println!("? nix (v0.0.2) - needs verification");
    println!("? md (v0.3.2) - needs verification");
    println!("? swift (v0.6.0) - needs verification");
    println!("? elixir (v0.3.4) - needs verification");
    println!("? haskell (v0.15.0) - needs verification");
}

fn test_language_constant() {
    println!("\n\nTesting LANGUAGE constant pattern:");
    println!("----------------------------------");
    
    // These have newer versions and should use LANGUAGE constant
    println!("✓ rust (v0.24.0) - unsafe {{ tree_sitter_rust::LANGUAGE() }}");
    println!("✓ python (v0.23.6) - unsafe {{ tree_sitter_python::LANGUAGE() }}");
    println!("✓ javascript (v0.23.1) - unsafe {{ tree_sitter_javascript::LANGUAGE() }}");
    println!("✓ typescript (v0.23.2) - unsafe {{ tree_sitter_typescript::LANGUAGE_TYPESCRIPT() }}");
    println!("✓ go (v0.23.4) - unsafe {{ tree_sitter_go::LANGUAGE() }}");
    println!("✓ java (v0.23.5) - unsafe {{ tree_sitter_java::LANGUAGE() }}");
    println!("✓ c (v0.24.1) - unsafe {{ tree_sitter_c::LANGUAGE() }}");
    println!("✓ cpp (v0.23.4) - unsafe {{ tree_sitter_cpp::LANGUAGE() }}");
    println!("✓ ruby (v0.23.1) - unsafe {{ tree_sitter_ruby::LANGUAGE() }}");
    println!("✓ bash (v0.23.3) - unsafe {{ tree_sitter_bash::LANGUAGE() }}");
    println!("✓ objc (v3.0.2) - unsafe {{ tree_sitter_objc::LANGUAGE() }}");
    println!("✓ r (v1.2.0) - unsafe {{ tree_sitter_r::LANGUAGE() }}");
    println!("✓ julia (v0.23.1) - unsafe {{ tree_sitter_julia::LANGUAGE() }}");
    println!("✓ scala (v0.23.4) - unsafe {{ tree_sitter_scala::LANGUAGE() }}");
    println!("✓ zig (v1.1.2) - unsafe {{ tree_sitter_zig::LANGUAGE() }}");
    println!("✓ php (v0.23.11) - unsafe {{ tree_sitter_php::LANGUAGE() }}");
    println!("✓ ocaml (v0.24.2) - unsafe {{ tree_sitter_ocaml::LANGUAGE_OCAML() }}");
}