use analysis_engine::parser::language_registry::get_language;

fn main() {
    println!("Testing tree-sitter language APIs...\n");

    let languages = vec![
        "rust",
        "python",
        "javascript",
        "go",
        "java",
        "yaml",
        "kotlin",
    ];

    for lang_name in languages {
        test_language(lang_name);
    }

    println!("\nAll tests completed!");
}

fn test_language(name: &str) {
    println!("Testing tree-sitter-{}:", name);
    match get_language(name) {
        Some(language) => {
            println!("  - Has language() function: ✓");
            println!("  - Language name: {:?}", language);
        }
        None => {
            println!("  - Failed to get language");
        }
    }
}