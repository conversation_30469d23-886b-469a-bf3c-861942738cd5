use analysis_engine::parser::language_registry::get_language;
use tree_sitter::Parser;

fn main() {
    // Test markdown parsing
    let language = get_language("md").expect("Failed to get md language");
    let mut parser = Parser::new();
    parser.set_language(&language).expect("Failed to set language");
    
    let markdown_content = r#"# Hello World

This is a **markdown** document with:
- A list item
- Another item

```rust
fn main() {
    println!("Code block!");
}
```
"#;
    
    let tree = parser.parse(markdown_content, None).expect("Failed to parse");
    let root = tree.root_node();
    
    println!("✅ Markdown parsed successfully!");
    println!("Root node: {:?}", root.kind());
    println!("Child count: {}", root.child_count());
    
    // Print first few children
    for i in 0..std::cmp::min(5, root.child_count()) {
        if let Some(child) = root.child(i) {
            println!("  Child {}: {:?}", i, child.kind());
        }
    }
}