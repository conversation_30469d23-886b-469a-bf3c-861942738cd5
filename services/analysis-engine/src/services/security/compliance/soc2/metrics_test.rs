//! Comprehensive tests for SOC 2 metrics module

#[cfg(test)]
mod tests {
    use super::super::*;
    use prometheus::Registry;
    use std::sync::Arc;
    use anyhow::Result;

    #[tokio::test]
    async fn test_create_soc2_metrics() -> Result<()> {
        // Create a new registry
        let registry = Arc::new(Registry::new());
        
        // Create SOC 2 metrics - this should not panic
        let metrics = Soc2Metrics::new(registry.clone())?;
        
        // Verify registry is stored
        assert!(Arc::ptr_eq(&metrics.registry, &registry));
        
        Ok(())
    }

    #[tokio::test]
    async fn test_security_metrics() -> Result<()> {
        let registry = Arc::new(Registry::new());
        let metrics = Soc2Metrics::new(registry)?;
        
        // Test access attempts tracking
        metrics.record_access_attempt(true, "jwt");
        metrics.record_access_attempt(false, "api_key");
        metrics.record_access_attempt(false, "jwt");
        
        // Verify counter values
        let success_jwt = metrics.access_attempts
            .with_label_values(&["success", "jwt"])
            .get();
        assert_eq!(success_jwt, 1);
        
        let failure_api_key = metrics.access_attempts
            .with_label_values(&["failure", "api_key"])
            .get();
        assert_eq!(failure_api_key, 1);
        
        let failure_jwt = metrics.access_attempts
            .with_label_values(&["failure", "jwt"])
            .get();
        assert_eq!(failure_jwt, 1);
        
        // Verify unauthorized blocked counter
        assert_eq!(metrics.unauthorized_blocked.get(), 2);
        
        Ok(())
    }

    #[tokio::test]
    async fn test_encryption_operations() -> Result<()> {
        let registry = Arc::new(Registry::new());
        let metrics = Soc2Metrics::new(registry)?;
        
        // Record different encryption operations
        metrics.record_encryption_operation("encrypt", "AES-256-GCM");
        metrics.record_encryption_operation("decrypt", "AES-256-GCM");
        metrics.record_encryption_operation("encrypt", "RSA-4096");
        metrics.record_encryption_operation("encrypt", "AES-256-GCM");
        
        // Verify counters
        let encrypt_aes = metrics.encryption_ops
            .with_label_values(&["encrypt", "AES-256-GCM"])
            .get();
        assert_eq!(encrypt_aes, 2);
        
        let decrypt_aes = metrics.encryption_ops
            .with_label_values(&["decrypt", "AES-256-GCM"])
            .get();
        assert_eq!(decrypt_aes, 1);
        
        let encrypt_rsa = metrics.encryption_ops
            .with_label_values(&["encrypt", "RSA-4096"])
            .get();
        assert_eq!(encrypt_rsa, 1);
        
        Ok(())
    }

    #[tokio::test]
    async fn test_availability_metrics() -> Result<()> {
        let registry = Arc::new(Registry::new());
        let metrics = Soc2Metrics::new(registry)?;
        
        // Test uptime gauge
        metrics.update_uptime(99.95);
        assert_eq!(metrics.uptime_gauge.get(), 99.95);
        
        metrics.update_uptime(99.99);
        assert_eq!(metrics.uptime_gauge.get(), 99.99);
        
        // Test response time histogram
        metrics.record_response_time("/api/v1/analyze", "POST", 45.5);
        metrics.record_response_time("/api/v1/analyze", "POST", 120.0);
        metrics.record_response_time("/api/v1/health", "GET", 5.0);
        
        // Test error rate gauge
        metrics.error_rate
            .with_label_values(&["analysis-service", "timeout"])
            .set(0.02);
        
        let error_rate = metrics.error_rate
            .with_label_values(&["analysis-service", "timeout"])
            .get();
        assert_eq!(error_rate, 0.02);
        
        Ok(())
    }

    #[tokio::test]
    async fn test_integrity_metrics() -> Result<()> {
        let registry = Arc::new(Registry::new());
        let metrics = Soc2Metrics::new(registry)?;
        
        // Test validation failures
        metrics.record_validation_failure("input", "high");
        metrics.record_validation_failure("checksum", "critical");
        metrics.record_validation_failure("input", "low");
        
        let input_high = metrics.validation_failures
            .with_label_values(&["input", "high"])
            .get();
        assert_eq!(input_high, 1);
        
        let checksum_critical = metrics.validation_failures
            .with_label_values(&["checksum", "critical"])
            .get();
        assert_eq!(checksum_critical, 1);
        
        // Test corruption detection
        metrics.corruption_detected.inc();
        assert_eq!(metrics.corruption_detected.get(), 1);
        
        // Test transaction success rate
        metrics.transaction_success.set(98.5);
        assert_eq!(metrics.transaction_success.get(), 98.5);
        
        Ok(())
    }

    #[tokio::test]
    async fn test_confidentiality_metrics() -> Result<()> {
        let registry = Arc::new(Registry::new());
        let metrics = Soc2Metrics::new(registry)?;
        
        // Test encrypted fields count
        metrics.encrypted_fields.set(150.0);
        assert_eq!(metrics.encrypted_fields.get(), 150.0);
        
        // Test decryption requests
        metrics.decryption_requests
            .with_label_values(&["user_request", "true"])
            .inc();
        metrics.decryption_requests
            .with_label_values(&["audit", "true"])
            .inc();
        metrics.decryption_requests
            .with_label_values(&["user_request", "false"])
            .inc();
        
        let user_authorized = metrics.decryption_requests
            .with_label_values(&["user_request", "true"])
            .get();
        assert_eq!(user_authorized, 1);
        
        let user_unauthorized = metrics.decryption_requests
            .with_label_values(&["user_request", "false"])
            .get();
        assert_eq!(user_unauthorized, 1);
        
        // Test access violations
        metrics.access_violations.inc();
        metrics.access_violations.inc();
        assert_eq!(metrics.access_violations.get(), 2);
        
        Ok(())
    }

    #[tokio::test]
    async fn test_privacy_metrics() -> Result<()> {
        let registry = Arc::new(Registry::new());
        let metrics = Soc2Metrics::new(registry)?;
        
        // Test consent changes
        metrics.consent_changes
            .with_label_values(&["marketing", "grant"])
            .inc();
        metrics.consent_changes
            .with_label_values(&["analytics", "revoke"])
            .inc();
        metrics.consent_changes
            .with_label_values(&["marketing", "grant"])
            .inc();
        
        let marketing_grant = metrics.consent_changes
            .with_label_values(&["marketing", "grant"])
            .get();
        assert_eq!(marketing_grant, 2);
        
        let analytics_revoke = metrics.consent_changes
            .with_label_values(&["analytics", "revoke"])
            .get();
        assert_eq!(analytics_revoke, 1);
        
        // Test deletion requests
        metrics.deletion_requests.inc();
        metrics.deletion_requests.inc();
        metrics.deletion_requests.inc();
        assert_eq!(metrics.deletion_requests.get(), 3);
        
        // Test export requests
        metrics.export_requests.inc();
        assert_eq!(metrics.export_requests.get(), 1);
        
        Ok(())
    }

    #[tokio::test]
    async fn test_compliance_scores() -> Result<()> {
        let registry = Arc::new(Registry::new());
        let metrics = Soc2Metrics::new(registry)?;
        
        // Initial scores should be 100.0
        let security_score = metrics.compliance_score
            .with_label_values(&["security"])
            .get();
        assert_eq!(security_score, 100.0);
        
        let availability_score = metrics.compliance_score
            .with_label_values(&["availability"])
            .get();
        assert_eq!(availability_score, 100.0);
        
        // Update scores
        metrics.update_compliance_score("security", 95.5);
        metrics.update_compliance_score("privacy", 88.0);
        
        let updated_security = metrics.compliance_score
            .with_label_values(&["security"])
            .get();
        assert_eq!(updated_security, 95.5);
        
        let updated_privacy = metrics.compliance_score
            .with_label_values(&["privacy"])
            .get();
        assert_eq!(updated_privacy, 88.0);
        
        Ok(())
    }

    #[tokio::test]
    async fn test_control_effectiveness() -> Result<()> {
        let registry = Arc::new(Registry::new());
        let metrics = Soc2Metrics::new(registry)?;
        
        // Update control effectiveness
        metrics.update_control_effectiveness("AC-1", "security", 98.0);
        metrics.update_control_effectiveness("AU-2", "security", 95.5);
        metrics.update_control_effectiveness("CM-1", "availability", 99.0);
        
        let ac1_effectiveness = metrics.control_effectiveness
            .with_label_values(&["AC-1", "security"])
            .get();
        assert_eq!(ac1_effectiveness, 98.0);
        
        let au2_effectiveness = metrics.control_effectiveness
            .with_label_values(&["AU-2", "security"])
            .get();
        assert_eq!(au2_effectiveness, 95.5);
        
        let cm1_effectiveness = metrics.control_effectiveness
            .with_label_values(&["CM-1", "availability"])
            .get();
        assert_eq!(cm1_effectiveness, 99.0);
        
        Ok(())
    }

    #[tokio::test]
    async fn test_key_rotation() -> Result<()> {
        let registry = Arc::new(Registry::new());
        let metrics = Soc2Metrics::new(registry)?;
        
        // Test key rotation counter
        metrics.key_rotation.inc();
        metrics.key_rotation.inc();
        assert_eq!(metrics.key_rotation.get(), 2);
        
        Ok(())
    }

    #[tokio::test]
    async fn test_concurrent_metric_updates() -> Result<()> {
        let registry = Arc::new(Registry::new());
        let metrics = Arc::new(Soc2Metrics::new(registry)?);
        
        // Spawn multiple tasks to update metrics concurrently
        let mut handles = vec![];
        
        for i in 0..10 {
            let metrics_clone = metrics.clone();
            let handle = tokio::spawn(async move {
                for _ in 0..100 {
                    metrics_clone.record_access_attempt(i % 2 == 0, "concurrent");
                    metrics_clone.record_encryption_operation("encrypt", "AES");
                    metrics_clone.key_rotation.inc();
                }
            });
            handles.push(handle);
        }
        
        // Wait for all tasks to complete
        for handle in handles {
            handle.await?;
        }
        
        // Verify totals
        let success_count = metrics.access_attempts
            .with_label_values(&["success", "concurrent"])
            .get();
        let failure_count = metrics.access_attempts
            .with_label_values(&["failure", "concurrent"])
            .get();
        
        assert_eq!(success_count + failure_count, 1000);
        assert_eq!(metrics.key_rotation.get(), 1000);
        
        let encrypt_count = metrics.encryption_ops
            .with_label_values(&["encrypt", "AES"])
            .get();
        assert_eq!(encrypt_count, 1000);
        
        Ok(())
    }

    #[tokio::test]
    async fn test_metric_registry_gathering() -> Result<()> {
        let registry = Arc::new(Registry::new());
        let metrics = Soc2Metrics::new(registry.clone())?;
        
        // Update some metrics
        metrics.record_access_attempt(true, "test");
        metrics.update_uptime(99.9);
        metrics.update_compliance_score("overall", 95.0);
        
        // Gather all metrics from registry
        let metric_families = registry.gather();
        
        // Verify we have metrics registered
        assert!(!metric_families.is_empty());
        
        // Check for specific metric families
        let metric_names: Vec<String> = metric_families
            .iter()
            .map(|mf| mf.get_name().to_string())
            .collect();
        
        assert!(metric_names.contains(&"soc2_security_access_attempts_total".to_string()));
        assert!(metric_names.contains(&"soc2_availability_uptime_percentage".to_string()));
        assert!(metric_names.contains(&"soc2_compliance_score".to_string()));
        
        Ok(())
    }
}