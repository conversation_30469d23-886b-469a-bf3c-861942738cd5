//! SOC 2 compliance metrics and Prometheus integration

use anyhow::{Context, Result};
use lazy_static::lazy_static;
use prometheus::{
    register_counter_vec_with_registry, register_gauge_vec_with_registry,
    register_histogram_vec_with_registry, Counter, CounterVec, Gauge, GaugeVec, Histogram,
    HistogramVec, Registry,
};
use std::sync::Arc;
use tracing::{debug, info};

lazy_static! {
    // Security Metrics
    static ref SECURITY_ACCESS_ATTEMPTS: CounterVec = CounterVec::new(
        prometheus::Opts::new(
            "soc2_security_access_attempts_total",
            "Total number of access attempts"
        ),
        &["result", "auth_method"]
    ).unwrap();

    static ref SECURITY_UNAUTHORIZED_BLOCKED: Counter = Counter::new(
        "soc2_security_unauthorized_access_blocked",
        "Total unauthorized access attempts blocked"
    ).unwrap();

    static ref SECURITY_ENCRYPTION_OPS: CounterVec = CounterVec::new(
        prometheus::Opts::new(
            "soc2_security_encryption_operations_total",
            "Total encryption operations"
        ),
        &["operation", "algorithm"]
    ).unwrap();

    static ref SECURITY_KEY_ROTATION: Counter = Counter::new(
        "soc2_security_key_rotation_events",
        "Total key rotation events"
    ).unwrap();

    // Availability Metrics
    static ref AVAILABILITY_UPTIME: Gauge = Gauge::new(
        "soc2_availability_uptime_percentage",
        "System uptime percentage"
    ).unwrap();

    static ref AVAILABILITY_RESPONSE_TIME: HistogramVec = HistogramVec::new(
        prometheus::HistogramOpts::new(
            "soc2_availability_response_time_milliseconds",
            "Response time in milliseconds"
        ).buckets(vec![10.0, 25.0, 50.0, 100.0, 250.0, 500.0, 1000.0, 2500.0, 5000.0]),
        &["endpoint", "method"]
    ).unwrap();

    static ref AVAILABILITY_ERROR_RATE: GaugeVec = GaugeVec::new(
        prometheus::Opts::new(
            "soc2_availability_error_rate",
            "Error rate per service"
        ),
        &["service", "error_type"]
    ).unwrap();

    // Processing Integrity Metrics
    static ref INTEGRITY_VALIDATION_FAILURES: CounterVec = CounterVec::new(
        prometheus::Opts::new(
            "soc2_integrity_validation_failures",
            "Data validation failures"
        ),
        &["validation_type", "severity"]
    ).unwrap();

    static ref INTEGRITY_CORRUPTION_DETECTED: Counter = Counter::new(
        "soc2_integrity_data_corruption_detected",
        "Data corruption incidents detected"
    ).unwrap();

    static ref INTEGRITY_TRANSACTION_SUCCESS: Gauge = Gauge::new(
        "soc2_integrity_transaction_success_rate",
        "Transaction success rate percentage"
    ).unwrap();

    // Confidentiality Metrics
    static ref CONFIDENTIALITY_ENCRYPTED_FIELDS: Gauge = Gauge::new(
        "soc2_confidentiality_encrypted_fields_total",
        "Total number of encrypted fields"
    ).unwrap();

    static ref CONFIDENTIALITY_DECRYPTION_REQUESTS: CounterVec = CounterVec::new(
        prometheus::Opts::new(
            "soc2_confidentiality_decryption_requests",
            "Decryption requests"
        ),
        &["purpose", "authorized"]
    ).unwrap();

    static ref CONFIDENTIALITY_ACCESS_VIOLATIONS: Counter = Counter::new(
        "soc2_confidentiality_access_violations",
        "Confidentiality access violations detected"
    ).unwrap();

    // Privacy Metrics
    static ref PRIVACY_CONSENT_CHANGES: CounterVec = CounterVec::new(
        prometheus::Opts::new(
            "soc2_privacy_consent_changes",
            "Privacy consent changes"
        ),
        &["consent_type", "action"]
    ).unwrap();

    static ref PRIVACY_DELETION_REQUESTS: Counter = Counter::new(
        "soc2_privacy_deletion_requests",
        "Privacy data deletion requests"
    ).unwrap();

    static ref PRIVACY_EXPORT_REQUESTS: Counter = Counter::new(
        "soc2_privacy_export_requests",
        "Privacy data export requests"
    ).unwrap();

    // Compliance Score Metrics
    static ref COMPLIANCE_SCORE: GaugeVec = GaugeVec::new(
        prometheus::Opts::new(
            "soc2_compliance_score",
            "Overall compliance score by principle"
        ),
        &["principle"]
    ).unwrap();

    static ref CONTROL_EFFECTIVENESS: GaugeVec = GaugeVec::new(
        prometheus::Opts::new(
            "soc2_control_effectiveness",
            "Control effectiveness percentage"
        ),
        &["control_id", "principle"]
    ).unwrap();
}

/// SOC 2 metrics collection
pub struct Soc2Metrics {
    registry: Arc<Registry>,
    
    // Security
    pub access_attempts: CounterVec,
    pub unauthorized_blocked: Counter,
    pub encryption_ops: CounterVec,
    pub key_rotation: Counter,
    
    // Availability
    pub uptime_gauge: Gauge,
    pub response_time: HistogramVec,
    pub error_rate: GaugeVec,
    
    // Processing Integrity
    pub validation_failures: CounterVec,
    pub corruption_detected: Counter,
    pub transaction_success: Gauge,
    
    // Confidentiality
    pub encrypted_fields: Gauge,
    pub decryption_requests: CounterVec,
    pub access_violations: Counter,
    
    // Privacy
    pub consent_changes: CounterVec,
    pub deletion_requests: Counter,
    pub export_requests: Counter,
    
    // Compliance
    pub compliance_score: GaugeVec,
    pub control_effectiveness: GaugeVec,
}

impl Soc2Metrics {
    /// Create new SOC 2 metrics instance
    pub fn new(registry: Arc<Registry>) -> Result<Self> {
        info!("Initializing SOC 2 metrics");

        // Create metrics first, then register them
        
        // Security metrics
        let unauthorized_blocked = Counter::new(
            "soc2_security_unauthorized_access_blocked",
            "Total unauthorized access attempts blocked"
        ).context("Failed to create unauthorized_blocked metric")?;
        registry.register(Box::new(unauthorized_blocked.clone()))
            .context("Failed to register unauthorized_blocked metric")?;
            
        let key_rotation = Counter::new(
            "soc2_security_key_rotation_events",
            "Total key rotation events"
        ).context("Failed to create key_rotation metric")?;
        registry.register(Box::new(key_rotation.clone()))
            .context("Failed to register key_rotation metric")?;
            
        // Availability metrics
        let uptime_gauge = Gauge::new(
            "soc2_availability_uptime_percentage",
            "System uptime percentage"
        ).context("Failed to create uptime_gauge metric")?;
        registry.register(Box::new(uptime_gauge.clone()))
            .context("Failed to register uptime_gauge metric")?;
            
        // Processing Integrity metrics
        let corruption_detected = Counter::new(
            "soc2_integrity_data_corruption_detected",
            "Data corruption incidents detected"
        ).context("Failed to create corruption_detected metric")?;
        registry.register(Box::new(corruption_detected.clone()))
            .context("Failed to register corruption_detected metric")?;
            
        let transaction_success = Gauge::new(
            "soc2_integrity_transaction_success_rate",
            "Transaction success rate percentage"
        ).context("Failed to create transaction_success metric")?;
        registry.register(Box::new(transaction_success.clone()))
            .context("Failed to register transaction_success metric")?;
            
        // Confidentiality metrics
        let encrypted_fields = Gauge::new(
            "soc2_confidentiality_encrypted_fields_total",
            "Total number of encrypted fields"
        ).context("Failed to create encrypted_fields metric")?;
        registry.register(Box::new(encrypted_fields.clone()))
            .context("Failed to register encrypted_fields metric")?;
            
        let access_violations = Counter::new(
            "soc2_confidentiality_access_violations",
            "Confidentiality access violations detected"
        ).context("Failed to create access_violations metric")?;
        registry.register(Box::new(access_violations.clone()))
            .context("Failed to register access_violations metric")?;
            
        // Privacy metrics
        let deletion_requests = Counter::new(
            "soc2_privacy_deletion_requests",
            "Privacy data deletion requests"
        ).context("Failed to create deletion_requests metric")?;
        registry.register(Box::new(deletion_requests.clone()))
            .context("Failed to register deletion_requests metric")?;
            
        let export_requests = Counter::new(
            "soc2_privacy_export_requests",
            "Privacy data export requests"
        ).context("Failed to create export_requests metric")?;
        registry.register(Box::new(export_requests.clone()))
            .context("Failed to register export_requests metric")?;

        // Register all metrics with the provided registry
        let metrics = Self {
            registry: registry.clone(),
            
            // Security
            access_attempts: register_counter_vec_with_registry!(
                prometheus::Opts::new(
                    "soc2_security_access_attempts_total",
                    "Total number of access attempts"
                ),
                &["result", "auth_method"],
                registry
            ).context("Failed to register access_attempts metric")?,
            
            unauthorized_blocked,
            
            encryption_ops: register_counter_vec_with_registry!(
                prometheus::Opts::new(
                    "soc2_security_encryption_operations_total",
                    "Total encryption operations"
                ),
                &["operation", "algorithm"],
                registry
            ).context("Failed to register encryption_ops metric")?,
            
            key_rotation,
            
            // Availability
            uptime_gauge,
            
            response_time: register_histogram_vec_with_registry!(
                prometheus::HistogramOpts::new(
                    "soc2_availability_response_time_milliseconds",
                    "Response time in milliseconds"
                ).buckets(vec![10.0, 25.0, 50.0, 100.0, 250.0, 500.0, 1000.0, 2500.0, 5000.0]),
                &["endpoint", "method"],
                registry
            ).context("Failed to register response_time metric")?,
            
            error_rate: register_gauge_vec_with_registry!(
                prometheus::Opts::new(
                    "soc2_availability_error_rate",
                    "Error rate per service"
                ),
                &["service", "error_type"],
                registry
            ).context("Failed to register error_rate metric")?,
            
            // Processing Integrity
            validation_failures: register_counter_vec_with_registry!(
                prometheus::Opts::new(
                    "soc2_integrity_validation_failures",
                    "Data validation failures"
                ),
                &["validation_type", "severity"],
                registry
            ).context("Failed to register validation_failures metric")?,
            
            corruption_detected,
            
            transaction_success,
            
            // Confidentiality
            encrypted_fields,
            
            decryption_requests: register_counter_vec_with_registry!(
                prometheus::Opts::new(
                    "soc2_confidentiality_decryption_requests",
                    "Decryption requests"
                ),
                &["purpose", "authorized"],
                registry
            ).context("Failed to register decryption_requests metric")?,
            
            access_violations,
            
            // Privacy
            consent_changes: register_counter_vec_with_registry!(
                prometheus::Opts::new(
                    "soc2_privacy_consent_changes",
                    "Privacy consent changes"
                ),
                &["consent_type", "action"],
                registry
            ).context("Failed to register consent_changes metric")?,
            
            deletion_requests,
            
            export_requests,
            
            // Compliance
            compliance_score: register_gauge_vec_with_registry!(
                prometheus::Opts::new(
                    "soc2_compliance_score",
                    "Overall compliance score by principle"
                ),
                &["principle"],
                registry
            ).context("Failed to register compliance_score metric")?,
            
            control_effectiveness: register_gauge_vec_with_registry!(
                prometheus::Opts::new(
                    "soc2_control_effectiveness",
                    "Control effectiveness percentage"
                ),
                &["control_id", "principle"],
                registry
            ).context("Failed to register control_effectiveness metric")?,
        };

        // Initialize compliance scores
        metrics.compliance_score.with_label_values(&["security"]).set(100.0);
        metrics.compliance_score.with_label_values(&["availability"]).set(100.0);
        metrics.compliance_score.with_label_values(&["processing_integrity"]).set(100.0);
        metrics.compliance_score.with_label_values(&["confidentiality"]).set(100.0);
        metrics.compliance_score.with_label_values(&["privacy"]).set(100.0);
        metrics.compliance_score.with_label_values(&["overall"]).set(100.0);

        info!("SOC 2 metrics initialized successfully");
        Ok(metrics)
    }

    /// Record an access attempt
    pub fn record_access_attempt(&self, successful: bool, auth_method: &str) {
        let result = if successful { "success" } else { "failure" };
        self.access_attempts
            .with_label_values(&[result, auth_method])
            .inc();
        
        if !successful {
            self.unauthorized_blocked.inc();
        }
    }

    /// Record an encryption operation
    pub fn record_encryption_operation(&self, operation: &str, algorithm: &str) {
        self.encryption_ops
            .with_label_values(&[operation, algorithm])
            .inc();
    }

    /// Record a response time
    pub fn record_response_time(&self, endpoint: &str, method: &str, duration_ms: f64) {
        self.response_time
            .with_label_values(&[endpoint, method])
            .observe(duration_ms);
    }

    /// Update uptime percentage
    pub fn update_uptime(&self, percentage: f64) {
        self.uptime_gauge.set(percentage);
    }

    /// Record a validation failure
    pub fn record_validation_failure(&self, validation_type: &str, severity: &str) {
        self.validation_failures
            .with_label_values(&[validation_type, severity])
            .inc();
    }

    /// Update compliance score for a principle
    pub fn update_compliance_score(&self, principle: &str, score: f64) {
        self.compliance_score
            .with_label_values(&[principle])
            .set(score);
    }

    /// Update control effectiveness
    pub fn update_control_effectiveness(&self, control_id: &str, principle: &str, effectiveness: f64) {
        self.control_effectiveness
            .with_label_values(&[control_id, principle])
            .set(effectiveness);
    }
}

/// Compliance monitor that calculates and tracks compliance scores
pub struct ComplianceMonitor {
    metrics: Arc<Soc2Metrics>,
    audit_logger: Arc<crate::audit::AuditLogger>,
    registry: Arc<Registry>,
}

impl ComplianceMonitor {
    /// Create new compliance monitor
    pub fn new(
        metrics: Arc<Soc2Metrics>,
        audit_logger: Arc<crate::audit::AuditLogger>,
        registry: Arc<Registry>,
    ) -> Self {
        Self {
            metrics,
            audit_logger,
            registry,
        }
    }

    /// Calculate overall compliance score
    pub async fn calculate_compliance_score(&self) -> Result<ComplianceScore> {
        debug!("Calculating SOC 2 compliance score");

        // Calculate individual principle scores
        let security_score = self.calculate_security_score().await?;
        let availability_score = self.calculate_availability_score().await?;
        let integrity_score = self.calculate_integrity_score().await?;
        let confidentiality_score = self.calculate_confidentiality_score().await?;
        let privacy_score = self.calculate_privacy_score().await?;

        // Calculate weighted overall score
        let overall_score = (
            security_score * 0.25 +
            availability_score * 0.20 +
            integrity_score * 0.20 +
            confidentiality_score * 0.20 +
            privacy_score * 0.15
        );

        // Update metrics
        self.metrics.update_compliance_score("security", security_score);
        self.metrics.update_compliance_score("availability", availability_score);
        self.metrics.update_compliance_score("processing_integrity", integrity_score);
        self.metrics.update_compliance_score("confidentiality", confidentiality_score);
        self.metrics.update_compliance_score("privacy", privacy_score);
        self.metrics.update_compliance_score("overall", overall_score);

        let score = ComplianceScore {
            overall: overall_score,
            security: security_score,
            availability: availability_score,
            processing_integrity: integrity_score,
            confidentiality: confidentiality_score,
            privacy: privacy_score,
            calculated_at: chrono::Utc::now(),
        };

        // Log compliance calculation
        self.audit_compliance_calculation(&score).await?;

        Ok(score)
    }

    /// Calculate security principle score
    async fn calculate_security_score(&self) -> Result<f64> {
        // Base score starts at 100
        let mut score: f64 = 100.0;

        // Deduct points for security violations
        // This would integrate with actual security metrics
        
        // Example: Check unauthorized access attempts
        // In production, this would query actual metrics
        
        Ok(score.max(0.0))
    }

    /// Calculate availability principle score
    async fn calculate_availability_score(&self) -> Result<f64> {
        // Get current uptime percentage
        // In production, this would calculate from actual uptime metrics
        let uptime = 99.95; // Example value
        
        Ok(uptime)
    }

    /// Calculate processing integrity score
    async fn calculate_integrity_score(&self) -> Result<f64> {
        // Calculate based on validation failures and data corruption
        let mut score: f64 = 100.0;
        
        // In production, calculate from actual validation metrics
        
        Ok(score.max(0.0))
    }

    /// Calculate confidentiality score
    async fn calculate_confidentiality_score(&self) -> Result<f64> {
        // Calculate based on access violations and encryption compliance
        let mut score: f64 = 100.0;
        
        // In production, check encryption compliance and access violations
        
        Ok(score.max(0.0))
    }

    /// Calculate privacy score
    async fn calculate_privacy_score(&self) -> Result<f64> {
        // Calculate based on consent compliance and data handling
        let mut score: f64 = 100.0;
        
        // In production, check GDPR compliance metrics
        
        Ok(score.max(0.0))
    }

    /// Log compliance calculation to audit trail
    async fn audit_compliance_calculation(&self, score: &ComplianceScore) -> Result<()> {
        use crate::audit::{AuditAction, AuditEventBuilder, AuditOutcome, AuditSeverity};
        
        let metadata = serde_json::json!({
            "compliance_scores": {
                "overall": score.overall,
                "security": score.security,
                "availability": score.availability,
                "processing_integrity": score.processing_integrity,
                "confidentiality": score.confidentiality,
                "privacy": score.privacy
            },
            "calculated_at": score.calculated_at.to_rfc3339()
        });

        let event = AuditEventBuilder::new(AuditAction::SecurityAssessmentCompleted)
            .resource("soc2_compliance", "compliance_score")
            .outcome(AuditOutcome::Success)
            .severity(AuditSeverity::Info)
            .metadata(metadata)
            .build();

        self.audit_logger.log_event(event).await?;
        Ok(())
    }

    /// Perform health check
    pub async fn health_check(&self) -> Result<()> {
        // Verify metrics are being collected
        debug!("SOC 2 compliance monitor health check passed");
        Ok(())
    }
}

/// Compliance score structure
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct ComplianceScore {
    pub overall: f64,
    pub security: f64,
    pub availability: f64,
    pub processing_integrity: f64,
    pub confidentiality: f64,
    pub privacy: f64,
    pub calculated_at: chrono::DateTime<chrono::Utc>,
}


#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_soc2_metrics_creation() {
        let registry = Arc::new(Registry::new());
        let metrics = Soc2Metrics::new(registry).unwrap();
        
        // Test recording metrics
        metrics.record_access_attempt(true, "jwt");
        metrics.record_encryption_operation("encrypt", "AES-256-GCM");
        metrics.update_uptime(99.95);
    }

    #[tokio::test]
    async fn test_compliance_score_calculation() {
        let score = ComplianceScore {
            overall: 98.5,
            security: 97.0,
            availability: 99.9,
            processing_integrity: 99.0,
            confidentiality: 100.0,
            privacy: 96.0,
            calculated_at: chrono::Utc::now(),
        };

        assert!(score.overall > 95.0);
        assert_eq!(score.confidentiality, 100.0);
    }
}