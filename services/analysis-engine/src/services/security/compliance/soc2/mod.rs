//! SOC 2 compliance automation module
//! 
//! This module implements comprehensive SOC 2 compliance monitoring and reporting
//! across all five trust service principles: Security, Availability, Processing Integrity,
//! Confidentiality, and Privacy.

pub mod api;
pub mod collectors;
pub mod dashboard;
pub mod evidence;
pub mod metrics;
pub mod reports;
pub mod trust_principles;

// Re-export main types
pub use api::Soc2Api;
pub use collectors::ComplianceCollector;
pub use dashboard::ComplianceDashboard;
pub use evidence::EvidenceCollector;
pub use metrics::{Soc2Metrics, ComplianceMonitor, ComplianceScore};
pub use reports::ReportGenerator;
pub use trust_principles::TrustPrincipleMonitor;

use anyhow::Result;
use prometheus::Registry;
use std::sync::Arc;
use tracing::info;

/// SOC 2 compliance configuration
#[derive(Debug, Clone)]
pub struct Soc2Config {
    /// Enable automatic evidence collection
    pub enable_auto_collection: bool,
    
    /// Evidence retention period in days
    pub evidence_retention_days: u32,
    
    /// Compliance score calculation interval in seconds
    pub score_calculation_interval: u64,
    
    /// Alert thresholds for each trust principle
    pub alert_thresholds: TrustPrincipleThresholds,
    
    /// Report generation schedule
    pub report_schedule: ReportSchedule,
}

impl Default for Soc2Config {
    fn default() -> Self {
        Self {
            enable_auto_collection: true,
            evidence_retention_days: 365,
            score_calculation_interval: 300, // 5 minutes
            alert_thresholds: TrustPrincipleThresholds::default(),
            report_schedule: ReportSchedule::default(),
        }
    }
}

/// Alert thresholds for trust principles
#[derive(Debug, Clone)]
pub struct TrustPrincipleThresholds {
    pub security_threshold: f64,
    pub availability_threshold: f64,
    pub processing_integrity_threshold: f64,
    pub confidentiality_threshold: f64,
    pub privacy_threshold: f64,
}

impl Default for TrustPrincipleThresholds {
    fn default() -> Self {
        Self {
            security_threshold: 95.0,
            availability_threshold: 99.9,
            processing_integrity_threshold: 99.0,
            confidentiality_threshold: 100.0,
            privacy_threshold: 98.0,
        }
    }
}

/// Report generation schedule
#[derive(Debug, Clone)]
pub struct ReportSchedule {
    pub daily_enabled: bool,
    pub monthly_enabled: bool,
    pub quarterly_enabled: bool,
    pub annual_enabled: bool,
}

impl Default for ReportSchedule {
    fn default() -> Self {
        Self {
            daily_enabled: true,
            monthly_enabled: true,
            quarterly_enabled: true,
            annual_enabled: true,
        }
    }
}

/// Main SOC 2 compliance service
pub struct Soc2Service {
    config: Soc2Config,
    metrics: Arc<Soc2Metrics>,
    monitor: Arc<ComplianceMonitor>,
    collector: Arc<ComplianceCollector>,
    trust_monitor: Arc<TrustPrincipleMonitor>,
    evidence_collector: Arc<EvidenceCollector>,
    dashboard: Arc<ComplianceDashboard>,
    report_generator: Arc<ReportGenerator>,
}

impl Soc2Service {
    /// Create a new SOC 2 compliance service
    pub async fn new(
        config: Soc2Config,
        registry: Arc<Registry>,
        audit_logger: Arc<crate::audit::AuditLogger>,
        encryption_service: Option<Arc<dyn crate::storage::encryption::EncryptionService + Send + Sync>>,
    ) -> Result<Self> {
        info!("Initializing SOC 2 compliance service");

        // Initialize metrics
        let metrics = Arc::new(Soc2Metrics::new(registry.clone())?);
        
        // Create compliance monitor
        let monitor = Arc::new(ComplianceMonitor::new(
            metrics.clone(),
            audit_logger.clone(),
            registry.clone(),
        ));

        // Create collectors
        let collector = Arc::new(ComplianceCollector::new(
            metrics.clone(),
            audit_logger.clone(),
        ));

        // Create trust principle monitor
        let trust_monitor = Arc::new(TrustPrincipleMonitor::new(
            metrics.clone(),
            config.alert_thresholds.clone(),
        ));

        // Create evidence collector
        let evidence_collector = Arc::new(EvidenceCollector::new(
            audit_logger.clone(),
            encryption_service,
            config.evidence_retention_days,
        ));

        // Create dashboard
        let dashboard = Arc::new(ComplianceDashboard::new(
            monitor.clone(),
            trust_monitor.clone(),
            evidence_collector.clone(),
        ));

        // Create report generator
        let report_generator = Arc::new(ReportGenerator::new(
            dashboard.clone(),
            evidence_collector.clone(),
            config.report_schedule.clone(),
        ));

        let service = Self {
            config,
            metrics,
            monitor,
            collector,
            trust_monitor,
            evidence_collector,
            dashboard,
            report_generator,
        };

        info!("SOC 2 compliance service initialized successfully");
        Ok(service)
    }

    /// Start background monitoring tasks
    pub async fn start_monitoring(&self) -> Result<()> {
        info!("Starting SOC 2 compliance monitoring");

        // Start metric collection
        if self.config.enable_auto_collection {
            self.collector.start_collection(self.config.score_calculation_interval).await?;
        }

        // Start trust principle monitoring
        self.trust_monitor.start_monitoring().await?;

        // Start evidence collection
        self.evidence_collector.start_collection().await?;

        // Start report generation
        self.report_generator.start_scheduled_generation().await?;

        info!("SOC 2 compliance monitoring started");
        Ok(())
    }

    /// Stop monitoring tasks
    pub async fn stop_monitoring(&self) -> Result<()> {
        info!("Stopping SOC 2 compliance monitoring");
        
        self.collector.stop_collection().await?;
        self.trust_monitor.stop_monitoring().await?;
        self.evidence_collector.stop_collection().await?;
        self.report_generator.stop_generation().await?;

        info!("SOC 2 compliance monitoring stopped");
        Ok(())
    }

    /// Get the compliance dashboard
    pub fn dashboard(&self) -> &Arc<ComplianceDashboard> {
        &self.dashboard
    }

    /// Get the compliance monitor
    pub fn monitor(&self) -> &Arc<ComplianceMonitor> {
        &self.monitor
    }

    /// Get the evidence collector
    pub fn evidence_collector(&self) -> &Arc<EvidenceCollector> {
        &self.evidence_collector
    }

    /// Get the report generator
    pub fn report_generator(&self) -> &Arc<ReportGenerator> {
        &self.report_generator
    }

    /// Create API routes for SOC 2 compliance
    pub fn create_api(&self) -> Soc2Api {
        Soc2Api::new(
            self.dashboard.clone(),
            self.monitor.clone(),
            self.evidence_collector.clone(),
            self.report_generator.clone(),
        )
    }

    /// Perform health check
    pub async fn health_check(&self) -> Result<()> {
        self.monitor.health_check().await?;
        self.evidence_collector.health_check().await?;
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_soc2_config_defaults() {
        let config = Soc2Config::default();
        assert!(config.enable_auto_collection);
        assert_eq!(config.evidence_retention_days, 365);
        assert_eq!(config.score_calculation_interval, 300);
    }

    #[test]
    fn test_trust_principle_thresholds() {
        let thresholds = TrustPrincipleThresholds::default();
        assert_eq!(thresholds.security_threshold, 95.0);
        assert_eq!(thresholds.availability_threshold, 99.9);
        assert_eq!(thresholds.processing_integrity_threshold, 99.0);
        assert_eq!(thresholds.confidentiality_threshold, 100.0);
        assert_eq!(thresholds.privacy_threshold, 98.0);
    }
}

#[cfg(test)]
#[path = "tests.rs"]
mod soc2_tests;