//! SOC 2 trust principle monitoring

use anyhow::Result;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use tokio::time::{interval, Duration};
use tracing::{debug, error, info, warn};

use super::metrics::{ComplianceScore, Soc2Metrics};
use super::TrustPrincipleThresholds;

/// Trust principle types
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum TrustPrinciple {
    Security,
    Availability,
    ProcessingIntegrity,
    Confidentiality,
    Privacy,
}

impl TrustPrinciple {
    pub fn as_str(&self) -> &'static str {
        match self {
            Self::Security => "security",
            Self::Availability => "availability",
            Self::ProcessingIntegrity => "processing_integrity",
            Self::Confidentiality => "confidentiality",
            Self::Privacy => "privacy",
        }
    }

    pub fn all() -> Vec<Self> {
        vec![
            Self::Security,
            Self::Availability,
            Self::ProcessingIntegrity,
            Self::Confidentiality,
            Self::Privacy,
        ]
    }
}

/// Trust principle status
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TrustPrincipleStatus {
    pub principle: TrustPrinciple,
    pub score: f64,
    pub threshold: f64,
    pub is_compliant: bool,
    pub controls: Vec<ControlStatus>,
    pub last_evaluated: DateTime<Utc>,
    pub trend: ComplianceTrend,
}

/// Control status
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ControlStatus {
    pub control_id: String,
    pub description: String,
    pub effectiveness: f64,
    pub is_operating: bool,
    pub last_tested: DateTime<Utc>,
    pub evidence_count: usize,
}

/// Compliance trend
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComplianceTrend {
    pub direction: TrendDirection,
    pub change_percentage: f64,
    pub period_days: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TrendDirection {
    Improving,
    Stable,
    Declining,
}

/// Trust principle monitor
pub struct TrustPrincipleMonitor {
    metrics: Arc<Soc2Metrics>,
    thresholds: TrustPrincipleThresholds,
    status_cache: RwLock<HashMap<TrustPrinciple, TrustPrincipleStatus>>,
    monitoring_task: RwLock<Option<tokio::task::JoinHandle<()>>>,
}

impl TrustPrincipleMonitor {
    /// Create new trust principle monitor
    pub fn new(metrics: Arc<Soc2Metrics>, thresholds: TrustPrincipleThresholds) -> Self {
        Self {
            metrics,
            thresholds,
            status_cache: RwLock::new(HashMap::new()),
            monitoring_task: RwLock::new(None),
        }
    }

    /// Start monitoring trust principles
    pub async fn start_monitoring(&self) -> Result<()> {
        info!("Starting trust principle monitoring");

        let metrics = self.metrics.clone();
        let thresholds = self.thresholds.clone();

        let task = tokio::spawn(async move {
            let mut ticker = interval(Duration::from_secs(60)); // Check every minute
            
            loop {
                ticker.tick().await;
                
                if let Err(e) = Self::check_trust_principles(&metrics, &thresholds).await {
                    error!("Failed to check trust principles: {}", e);
                }
            }
        });

        *self.monitoring_task.write().await = Some(task);
        Ok(())
    }

    /// Stop monitoring
    pub async fn stop_monitoring(&self) -> Result<()> {
        if let Some(task) = self.monitoring_task.write().await.take() {
            task.abort();
            info!("Stopped trust principle monitoring");
        }
        Ok(())
    }

    /// Check all trust principles
    async fn check_trust_principles(
        metrics: &Arc<Soc2Metrics>,
        thresholds: &TrustPrincipleThresholds,
    ) -> Result<()> {
        debug!("Checking trust principle compliance");

        // Check each principle
        for principle in TrustPrinciple::all() {
            let threshold = match principle {
                TrustPrinciple::Security => thresholds.security_threshold,
                TrustPrinciple::Availability => thresholds.availability_threshold,
                TrustPrinciple::ProcessingIntegrity => thresholds.processing_integrity_threshold,
                TrustPrinciple::Confidentiality => thresholds.confidentiality_threshold,
                TrustPrinciple::Privacy => thresholds.privacy_threshold,
            };

            // Get current score from metrics
            let score = Self::get_principle_score(metrics, principle).await?;

            if score < threshold {
                warn!(
                    "Trust principle {} below threshold: {:.2}% < {:.2}%",
                    principle.as_str(),
                    score,
                    threshold
                );
                
                // Trigger alerts
                Self::trigger_compliance_alert(principle, score, threshold).await?;
            }
        }

        Ok(())
    }

    /// Get score for a specific principle
    async fn get_principle_score(
        metrics: &Arc<Soc2Metrics>,
        principle: TrustPrinciple,
    ) -> Result<f64> {
        // In production, this would calculate from actual metrics
        // For now, return example values
        let score = match principle {
            TrustPrinciple::Security => 97.5,
            TrustPrinciple::Availability => 99.95,
            TrustPrinciple::ProcessingIntegrity => 99.8,
            TrustPrinciple::Confidentiality => 100.0,
            TrustPrinciple::Privacy => 98.0,
        };

        Ok(score)
    }

    /// Trigger compliance alert
    async fn trigger_compliance_alert(
        principle: TrustPrinciple,
        score: f64,
        threshold: f64,
    ) -> Result<()> {
        error!(
            "COMPLIANCE ALERT: {} principle score {:.2}% is below threshold {:.2}%",
            principle.as_str(),
            score,
            threshold
        );

        // In production, this would:
        // - Send notifications to compliance team
        // - Create incident tickets
        // - Trigger automated remediation
        // - Update dashboards

        Ok(())
    }

    /// Get status for all trust principles
    pub async fn get_all_status(&self) -> Result<Vec<TrustPrincipleStatus>> {
        let mut statuses = Vec::new();

        for principle in TrustPrinciple::all() {
            let status = self.get_principle_status(principle).await?;
            statuses.push(status);
        }

        Ok(statuses)
    }

    /// Get status for a specific trust principle
    pub async fn get_principle_status(&self, principle: TrustPrinciple) -> Result<TrustPrincipleStatus> {
        // Check cache first
        if let Some(cached) = self.status_cache.read().await.get(&principle) {
            if cached.last_evaluated > Utc::now() - chrono::Duration::minutes(5) {
                return Ok(cached.clone());
            }
        }

        // Calculate fresh status
        let status = self.calculate_principle_status(principle).await?;

        // Update cache
        self.status_cache.write().await.insert(principle, status.clone());

        Ok(status)
    }

    /// Calculate status for a trust principle
    async fn calculate_principle_status(&self, principle: TrustPrinciple) -> Result<TrustPrincipleStatus> {
        let score = Self::get_principle_score(&self.metrics, principle).await?;
        let threshold = self.get_threshold(principle);
        let is_compliant = score >= threshold;

        let controls = self.get_principle_controls(principle).await?;
        let trend = self.calculate_trend(principle).await?;

        Ok(TrustPrincipleStatus {
            principle,
            score,
            threshold,
            is_compliant,
            controls,
            last_evaluated: Utc::now(),
            trend,
        })
    }

    /// Get threshold for a principle
    fn get_threshold(&self, principle: TrustPrinciple) -> f64 {
        match principle {
            TrustPrinciple::Security => self.thresholds.security_threshold,
            TrustPrinciple::Availability => self.thresholds.availability_threshold,
            TrustPrinciple::ProcessingIntegrity => self.thresholds.processing_integrity_threshold,
            TrustPrinciple::Confidentiality => self.thresholds.confidentiality_threshold,
            TrustPrinciple::Privacy => self.thresholds.privacy_threshold,
        }
    }

    /// Get controls for a trust principle
    async fn get_principle_controls(&self, principle: TrustPrinciple) -> Result<Vec<ControlStatus>> {
        // In production, this would query actual control status
        // For now, return example controls

        let controls = match principle {
            TrustPrinciple::Security => vec![
                ControlStatus {
                    control_id: "CC6.1".to_string(),
                    description: "Logical and physical access controls".to_string(),
                    effectiveness: 98.5,
                    is_operating: true,
                    last_tested: Utc::now() - chrono::Duration::days(7),
                    evidence_count: 150,
                },
                ControlStatus {
                    control_id: "CC6.6".to_string(),
                    description: "Encryption of data in transit and at rest".to_string(),
                    effectiveness: 100.0,
                    is_operating: true,
                    last_tested: Utc::now() - chrono::Duration::days(3),
                    evidence_count: 500,
                },
            ],
            TrustPrinciple::Availability => vec![
                ControlStatus {
                    control_id: "A1.1".to_string(),
                    description: "Capacity planning and monitoring".to_string(),
                    effectiveness: 99.5,
                    is_operating: true,
                    last_tested: Utc::now() - chrono::Duration::days(1),
                    evidence_count: 1000,
                },
            ],
            TrustPrinciple::ProcessingIntegrity => vec![
                ControlStatus {
                    control_id: "PI1.1".to_string(),
                    description: "Processing inputs are validated".to_string(),
                    effectiveness: 99.8,
                    is_operating: true,
                    last_tested: Utc::now() - chrono::Duration::hours(12),
                    evidence_count: 2500,
                },
            ],
            TrustPrinciple::Confidentiality => vec![
                ControlStatus {
                    control_id: "C1.1".to_string(),
                    description: "Confidential information protection".to_string(),
                    effectiveness: 100.0,
                    is_operating: true,
                    last_tested: Utc::now() - chrono::Duration::days(2),
                    evidence_count: 750,
                },
            ],
            TrustPrinciple::Privacy => vec![
                ControlStatus {
                    control_id: "P1.1".to_string(),
                    description: "Privacy notice and consent management".to_string(),
                    effectiveness: 98.0,
                    is_operating: true,
                    last_tested: Utc::now() - chrono::Duration::days(5),
                    evidence_count: 300,
                },
            ],
        };

        Ok(controls)
    }

    /// Calculate compliance trend
    async fn calculate_trend(&self, principle: TrustPrinciple) -> Result<ComplianceTrend> {
        // In production, this would analyze historical data
        // For now, return stable trend

        Ok(ComplianceTrend {
            direction: TrendDirection::Stable,
            change_percentage: 0.5,
            period_days: 30,
        })
    }

    /// Get critical controls that need attention
    pub async fn get_critical_controls(&self) -> Result<Vec<(TrustPrinciple, ControlStatus)>> {
        let mut critical = Vec::new();

        for principle in TrustPrinciple::all() {
            let status = self.get_principle_status(principle).await?;
            
            for control in status.controls {
                if control.effectiveness < 95.0 || !control.is_operating {
                    critical.push((principle, control));
                }
            }
        }

        Ok(critical)
    }

    /// Generate compliance summary
    pub async fn generate_summary(&self) -> Result<ComplianceSummary> {
        let statuses = self.get_all_status().await?;
        
        let overall_compliant = statuses.iter().all(|s| s.is_compliant);
        let average_score = statuses.iter().map(|s| s.score).sum::<f64>() / statuses.len() as f64;
        
        let principles_at_risk = statuses
            .iter()
            .filter(|s| !s.is_compliant)
            .map(|s| s.principle)
            .collect();

        let critical_controls = self.get_critical_controls().await?;

        Ok(ComplianceSummary {
            overall_compliant,
            average_score,
            principles_at_risk,
            critical_control_count: critical_controls.len(),
            last_updated: Utc::now(),
        })
    }
}

/// Compliance summary
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComplianceSummary {
    pub overall_compliant: bool,
    pub average_score: f64,
    pub principles_at_risk: Vec<TrustPrinciple>,
    pub critical_control_count: usize,
    pub last_updated: DateTime<Utc>,
}

#[cfg(test)]
mod tests {
    use super::*;
    use prometheus::Registry;

    #[tokio::test]
    async fn test_trust_principle_monitor() {
        let registry = Arc::new(Registry::new());
        let metrics = Arc::new(Soc2Metrics::new(registry).unwrap());
        let thresholds = TrustPrincipleThresholds::default();
        
        let monitor = TrustPrincipleMonitor::new(metrics, thresholds);
        
        let status = monitor.get_principle_status(TrustPrinciple::Security).await.unwrap();
        assert!(status.score > 0.0);
        assert!(!status.controls.is_empty());
    }

    #[test]
    fn test_trust_principle_all() {
        let principles = TrustPrinciple::all();
        assert_eq!(principles.len(), 5);
        assert!(principles.contains(&TrustPrinciple::Security));
        assert!(principles.contains(&TrustPrinciple::Privacy));
    }
}