//! SOC 2 compliance REST API endpoints

use anyhow::Result;
use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::{IntoResponse, Response},
    routing::{get, post},
    Json, Router,
};
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tracing::{error, info};

use super::dashboard::{ComplianceDashboard, TimeRange};
use super::evidence::{EvidenceCollector, EvidenceItem, EvidenceRequest};
use super::metrics::ComplianceMonitor;
use super::reports::{ExportFormat, ReportGenerator, ReportType};
use super::trust_principles::TrustPrinciple;

/// API state
#[derive(Clone)]
pub struct ApiState {
    dashboard: Arc<ComplianceDashboard>,
    monitor: Arc<ComplianceMonitor>,
    evidence_collector: Arc<EvidenceCollector>,
    report_generator: Arc<ReportGenerator>,
}

/// SOC 2 compliance API
pub struct Soc2Api {
    state: ApiState,
}

impl Soc2Api {
    /// Create new SOC 2 API
    pub fn new(
        dashboard: Arc<ComplianceDashboard>,
        monitor: Arc<ComplianceMonitor>,
        evidence_collector: Arc<EvidenceCollector>,
        report_generator: Arc<ReportGenerator>,
    ) -> Self {
        Self {
            state: ApiState {
                dashboard,
                monitor,
                evidence_collector,
                report_generator,
            },
        }
    }

    /// Create API routes
    pub fn routes(&self) -> Router {
        Router::new()
            // Dashboard endpoints
            .route("/api/compliance/soc2/dashboard", get(get_dashboard))
            .route("/api/compliance/soc2/dashboard/executive", get(get_executive_summary))
            .route("/api/compliance/soc2/dashboard/trends", get(get_compliance_trends))
            
            // Metrics endpoint
            .route("/api/compliance/soc2/metrics", get(get_metrics))
            
            // Trust principles endpoints
            .route("/api/compliance/soc2/principles", get(get_all_principles))
            .route("/api/compliance/soc2/principles/:principle", get(get_principle_details))
            
            // Reports endpoints
            .route("/api/compliance/soc2/reports", get(list_reports))
            .route("/api/compliance/soc2/reports/:type", get(generate_report))
            .route("/api/compliance/soc2/reports/:id/export", get(export_report))
            .route("/api/compliance/soc2/reports/:id/attest", post(add_attestation))
            
            // Evidence endpoints
            .route("/api/compliance/soc2/evidence", get(list_evidence))
            .route("/api/compliance/soc2/evidence", post(add_evidence))
            .route("/api/compliance/soc2/evidence/:control", get(get_control_evidence))
            .route("/api/compliance/soc2/evidence/package", post(generate_evidence_package))
            
            // Health check
            .route("/api/compliance/soc2/health", get(health_check))
            
            .with_state(self.state.clone())
    }
}

/// Query parameters for dashboard
#[derive(Debug, Deserialize)]
struct DashboardQuery {
    #[serde(default = "default_time_range")]
    time_range: String,
    #[serde(default)]
    start_date: Option<DateTime<Utc>>,
    #[serde(default)]
    end_date: Option<DateTime<Utc>>,
}

fn default_time_range() -> String {
    "last_30_days".to_string()
}

/// Get main compliance dashboard
async fn get_dashboard(
    State(state): State<ApiState>,
    Query(params): Query<DashboardQuery>,
) -> Result<Json<serde_json::Value>, AppError> {
    info!("Getting SOC 2 compliance dashboard");

    let time_range = parse_time_range(&params)?;
    let dashboard_data = state.dashboard.get_dashboard_data(time_range).await?;

    Ok(Json(serde_json::to_value(dashboard_data)?))
}

/// Get executive summary
async fn get_executive_summary(
    State(state): State<ApiState>,
) -> Result<Json<serde_json::Value>, AppError> {
    info!("Getting SOC 2 executive summary");

    let summary = state.dashboard.get_executive_summary().await?;
    Ok(Json(serde_json::to_value(summary)?))
}

/// Query parameters for trends
#[derive(Debug, Deserialize)]
struct TrendsQuery {
    #[serde(default = "default_trend_days")]
    days: u32,
}

fn default_trend_days() -> u32 {
    30
}

/// Get compliance trends
async fn get_compliance_trends(
    State(state): State<ApiState>,
    Query(params): Query<TrendsQuery>,
) -> Result<Json<serde_json::Value>, AppError> {
    info!("Getting compliance trends for {} days", params.days);

    let trends = state.dashboard.get_compliance_trends(params.days).await?;
    Ok(Json(serde_json::to_value(trends)?))
}

/// Get Prometheus metrics
async fn get_metrics(
    State(_state): State<ApiState>,
) -> Result<Response, AppError> {
    use prometheus::Encoder;
    
    let encoder = prometheus::TextEncoder::new();
    let metric_families = prometheus::gather();
    let mut buffer = Vec::new();
    
    encoder.encode(&metric_families, &mut buffer)
        .map_err(|e| AppError::Internal(format!("Failed to encode metrics: {}", e)))?;
    
    Ok(Response::builder()
        .status(StatusCode::OK)
        .header("Content-Type", encoder.format_type())
        .body(buffer.into())
        .unwrap())
}

/// Get all trust principles status
async fn get_all_principles(
    State(state): State<ApiState>,
) -> Result<Json<Vec<serde_json::Value>>, AppError> {
    info!("Getting all trust principle statuses");

    // Get dashboard data which includes trust principle statuses
    let dashboard_data = state.dashboard.get_dashboard_data(TimeRange::Last24Hours).await?;
    let json_values: Vec<_> = dashboard_data.trust_principles.into_iter()
        .map(|s| serde_json::to_value(s).unwrap())
        .collect();

    Ok(Json(json_values))
}

/// Get specific trust principle details
async fn get_principle_details(
    State(state): State<ApiState>,
    Path(principle): Path<String>,
) -> Result<Json<serde_json::Value>, AppError> {
    info!("Getting details for principle: {}", principle);

    let trust_principle = match principle.as_str() {
        "security" => TrustPrinciple::Security,
        "availability" => TrustPrinciple::Availability,
        "processing_integrity" => TrustPrinciple::ProcessingIntegrity,
        "confidentiality" => TrustPrinciple::Confidentiality,
        "privacy" => TrustPrinciple::Privacy,
        _ => return Err(AppError::BadRequest("Invalid trust principle".to_string())),
    };

    // Get dashboard data and find the specific principle
    let dashboard_data = state.dashboard.get_dashboard_data(TimeRange::Last24Hours).await?;
    let status = dashboard_data.trust_principles
        .into_iter()
        .find(|s| s.principle == trust_principle)
        .ok_or_else(|| AppError::BadRequest(format!("Trust principle {} not found", principle)))?;
    
    Ok(Json(serde_json::to_value(status)?))
}

/// List available reports
async fn list_reports(
    State(_state): State<ApiState>,
) -> Result<Json<Vec<ReportInfo>>, AppError> {
    // In production, this would query stored reports
    let reports = vec![
        ReportInfo {
            id: "daily-2024-01-15".to_string(),
            report_type: "Daily".to_string(),
            generated_at: Utc::now() - chrono::Duration::hours(2),
            period_start: Utc::now() - chrono::Duration::days(1),
            period_end: Utc::now(),
        },
        ReportInfo {
            id: "monthly-2024-01".to_string(),
            report_type: "Monthly".to_string(),
            generated_at: Utc::now() - chrono::Duration::days(15),
            period_start: Utc::now() - chrono::Duration::days(45),
            period_end: Utc::now() - chrono::Duration::days(15),
        },
    ];

    Ok(Json(reports))
}

#[derive(Debug, Serialize)]
struct ReportInfo {
    id: String,
    report_type: String,
    generated_at: DateTime<Utc>,
    period_start: DateTime<Utc>,
    period_end: DateTime<Utc>,
}

/// Generate a report
async fn generate_report(
    State(state): State<ApiState>,
    Path(report_type): Path<String>,
) -> Result<Json<serde_json::Value>, AppError> {
    info!("Generating {} report", report_type);

    let report_type = match report_type.as_str() {
        "daily" => ReportType::Daily,
        "monthly" => ReportType::Monthly,
        "quarterly" => ReportType::Quarterly,
        "annual" => ReportType::Annual,
        _ => return Err(AppError::BadRequest("Invalid report type".to_string())),
    };

    let report = state.report_generator.generate_report(report_type).await?;
    Ok(Json(serde_json::to_value(report)?))
}

/// Query parameters for export
#[derive(Debug, Deserialize)]
struct ExportQuery {
    #[serde(default = "default_export_format")]
    format: String,
}

fn default_export_format() -> String {
    "json".to_string()
}

/// Export a report
async fn export_report(
    State(state): State<ApiState>,
    Path(report_id): Path<String>,
    Query(params): Query<ExportQuery>,
) -> Result<Response, AppError> {
    info!("Exporting report {} as {}", report_id, params.format);

    let format = match params.format.as_str() {
        "json" => ExportFormat::Json,
        "pdf" => ExportFormat::Pdf,
        "html" => ExportFormat::Html,
        _ => return Err(AppError::BadRequest("Invalid export format".to_string())),
    };

    // In production, fetch the report by ID
    // For now, generate a sample report
    let report = state.report_generator.generate_report(ReportType::Daily).await?;
    let exported = state.report_generator.export_report(&report, format).await?;

    let content_type = match params.format.as_str() {
        "json" => "application/json",
        "pdf" => "application/pdf",
        "html" => "text/html",
        _ => "application/octet-stream",
    };

    Ok(Response::builder()
        .status(StatusCode::OK)
        .header("Content-Type", content_type)
        .header("Content-Disposition", format!("attachment; filename=\"soc2-report-{}.{}\"", report_id, params.format))
        .body(exported.into())
        .unwrap())
}

/// Attestation request
#[derive(Debug, Deserialize)]
struct AttestationRequest {
    attester_name: String,
    attester_role: String,
    statement: String,
}

/// Add attestation to report
async fn add_attestation(
    State(state): State<ApiState>,
    Path(report_id): Path<String>,
    Json(request): Json<AttestationRequest>,
) -> Result<StatusCode, AppError> {
    info!("Adding attestation to report {}", report_id);

    state.report_generator.add_attestation(
        &report_id,
        request.attester_name,
        request.attester_role,
        request.statement,
    ).await?;

    Ok(StatusCode::NO_CONTENT)
}

/// Query parameters for evidence
#[derive(Debug, Deserialize)]
struct EvidenceQuery {
    #[serde(default)]
    start_date: Option<DateTime<Utc>>,
    #[serde(default)]
    end_date: Option<DateTime<Utc>>,
}

/// List evidence
async fn list_evidence(
    State(state): State<ApiState>,
    Query(params): Query<EvidenceQuery>,
) -> Result<Json<Vec<EvidenceItem>>, AppError> {
    info!("Listing evidence items");

    let evidence = if let (Some(start), Some(end)) = (params.start_date, params.end_date) {
        state.evidence_collector.get_evidence_by_time_range(start, end).await?
    } else {
        state.evidence_collector.get_all_evidence().await?
    };

    Ok(Json(evidence))
}

/// Add evidence
async fn add_evidence(
    State(state): State<ApiState>,
    Json(evidence): Json<EvidenceItem>,
) -> Result<StatusCode, AppError> {
    info!("Adding evidence item for control {}", evidence.control_id);

    state.evidence_collector.add_evidence(evidence).await?;
    Ok(StatusCode::CREATED)
}

/// Get evidence for specific control
async fn get_control_evidence(
    State(state): State<ApiState>,
    Path(control_id): Path<String>,
) -> Result<Json<Vec<EvidenceItem>>, AppError> {
    info!("Getting evidence for control {}", control_id);

    let evidence = state.evidence_collector.get_evidence_for_control(&control_id).await?;
    Ok(Json(evidence))
}

/// Evidence package request
#[derive(Debug, Deserialize)]
struct EvidencePackageRequest {
    control_ids: Vec<String>,
    start_date: DateTime<Utc>,
    end_date: DateTime<Utc>,
}

/// Generate evidence package
async fn generate_evidence_package(
    State(state): State<ApiState>,
    Json(request): Json<EvidencePackageRequest>,
) -> Result<Json<serde_json::Value>, AppError> {
    info!("Generating evidence package for {} controls", request.control_ids.len());

    let package = state.evidence_collector.generate_evidence_package(
        request.control_ids,
        request.start_date,
        request.end_date,
    ).await?;

    Ok(Json(serde_json::to_value(package)?))
}

/// Health check
async fn health_check(
    State(state): State<ApiState>,
) -> Result<Json<HealthStatus>, AppError> {
    // Check all components
    let monitor_healthy = state.monitor.health_check().await.is_ok();
    let evidence_healthy = state.evidence_collector.health_check().await.is_ok();

    let status = HealthStatus {
        healthy: monitor_healthy && evidence_healthy,
        components: vec![
            ComponentHealth {
                name: "compliance_monitor".to_string(),
                healthy: monitor_healthy,
            },
            ComponentHealth {
                name: "evidence_collector".to_string(),
                healthy: evidence_healthy,
            },
        ],
        timestamp: Utc::now(),
    };

    Ok(Json(status))
}

#[derive(Debug, Serialize)]
struct HealthStatus {
    healthy: bool,
    components: Vec<ComponentHealth>,
    timestamp: DateTime<Utc>,
}

#[derive(Debug, Serialize)]
struct ComponentHealth {
    name: String,
    healthy: bool,
}

/// Parse time range from query parameters
fn parse_time_range(params: &DashboardQuery) -> Result<TimeRange, AppError> {
    if let (Some(start), Some(end)) = (params.start_date, params.end_date) {
        return Ok(TimeRange::Custom { start, end });
    }

    match params.time_range.as_str() {
        "last_24_hours" => Ok(TimeRange::Last24Hours),
        "last_7_days" => Ok(TimeRange::Last7Days),
        "last_30_days" => Ok(TimeRange::Last30Days),
        "last_90_days" => Ok(TimeRange::Last90Days),
        _ => Err(AppError::BadRequest("Invalid time range".to_string())),
    }
}

/// Application error type
#[derive(Debug)]
enum AppError {
    Internal(String),
    BadRequest(String),
}

impl From<anyhow::Error> for AppError {
    fn from(err: anyhow::Error) -> Self {
        error!("Internal error: {}", err);
        AppError::Internal(err.to_string())
    }
}

impl From<serde_json::Error> for AppError {
    fn from(err: serde_json::Error) -> Self {
        error!("JSON error: {}", err);
        AppError::Internal(err.to_string())
    }
}

impl IntoResponse for AppError {
    fn into_response(self) -> Response {
        let (status, message) = match self {
            AppError::Internal(msg) => (StatusCode::INTERNAL_SERVER_ERROR, msg),
            AppError::BadRequest(msg) => (StatusCode::BAD_REQUEST, msg),
        };

        let body = Json(serde_json::json!({
            "error": message,
        }));

        (status, body).into_response()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_parse_time_range() {
        let params = DashboardQuery {
            time_range: "last_7_days".to_string(),
            start_date: None,
            end_date: None,
        };

        let result = parse_time_range(&params).unwrap();
        match result {
            TimeRange::Last7Days => (),
            _ => panic!("Wrong time range"),
        }
    }
}