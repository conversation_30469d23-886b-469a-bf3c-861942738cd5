//! SOC 2 compliance dashboard data aggregation

use anyhow::Result;
use chrono::{DateTime, Duration, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use tracing::{debug, info};

use super::evidence::EvidenceCollector;
use super::metrics::{ComplianceMonitor, ComplianceScore};
use super::trust_principles::{TrustPrinciple, TrustPrincipleMonitor, TrustPrincipleStatus};

/// Dashboard time range
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TimeRange {
    Last24Hours,
    Last7Days,
    Last30Days,
    Last90Days,
    Custom { start: DateTime<Utc>, end: DateTime<Utc> },
}

impl TimeRange {
    pub fn to_duration(&self) -> Duration {
        match self {
            Self::Last24Hours => Duration::hours(24),
            Self::Last7Days => Duration::days(7),
            Self::Last30Days => Duration::days(30),
            Self::Last90Days => Duration::days(90),
            Self::Custom { start, end } => *end - *start,
        }
    }

    pub fn start_time(&self) -> DateTime<Utc> {
        match self {
            Self::Last24Hours => Utc::now() - Duration::hours(24),
            Self::Last7Days => Utc::now() - Duration::days(7),
            Self::Last30Days => Utc::now() - Duration::days(30),
            Self::Last90Days => Utc::now() - Duration::days(90),
            Self::Custom { start, .. } => *start,
        }
    }
}

/// Dashboard data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DashboardData {
    pub compliance_score: ComplianceScore,
    pub trust_principles: Vec<TrustPrincipleStatus>,
    pub alerts: Vec<ComplianceAlert>,
    pub evidence_summary: EvidenceSummary,
    pub historical_scores: Vec<HistoricalScore>,
    pub control_effectiveness: HashMap<String, f64>,
    pub time_range: TimeRange,
    pub generated_at: DateTime<Utc>,
}

/// Compliance alert
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComplianceAlert {
    pub id: String,
    pub severity: AlertSeverity,
    pub principle: TrustPrinciple,
    pub title: String,
    pub description: String,
    pub created_at: DateTime<Utc>,
    pub acknowledged: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AlertSeverity {
    Critical,
    High,
    Medium,
    Low,
}

/// Evidence summary
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EvidenceSummary {
    pub total_evidence_items: usize,
    pub evidence_by_principle: HashMap<TrustPrinciple, usize>,
    pub recent_collections: Vec<RecentCollection>,
    pub collection_coverage: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RecentCollection {
    pub control_id: String,
    pub evidence_type: String,
    pub collected_at: DateTime<Utc>,
    pub item_count: usize,
}

/// Historical score
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HistoricalScore {
    pub timestamp: DateTime<Utc>,
    pub overall: f64,
    pub by_principle: HashMap<TrustPrinciple, f64>,
}

/// Compliance dashboard
pub struct ComplianceDashboard {
    monitor: Arc<ComplianceMonitor>,
    trust_monitor: Arc<TrustPrincipleMonitor>,
    evidence_collector: Arc<EvidenceCollector>,
}

impl ComplianceDashboard {
    /// Create new compliance dashboard
    pub fn new(
        monitor: Arc<ComplianceMonitor>,
        trust_monitor: Arc<TrustPrincipleMonitor>,
        evidence_collector: Arc<EvidenceCollector>,
    ) -> Self {
        Self {
            monitor,
            trust_monitor,
            evidence_collector,
        }
    }

    /// Get dashboard data
    pub async fn get_dashboard_data(&self, time_range: TimeRange) -> Result<DashboardData> {
        info!("Generating SOC 2 compliance dashboard data");

        // Get current compliance score
        let compliance_score = self.monitor.calculate_compliance_score().await?;

        // Get trust principle statuses
        let trust_principles = self.trust_monitor.get_all_status().await?;

        // Get alerts
        let alerts = self.get_active_alerts(&trust_principles).await?;

        // Get evidence summary
        let evidence_summary = self.get_evidence_summary(&time_range).await?;

        // Get historical scores
        let historical_scores = self.get_historical_scores(&time_range).await?;

        // Get control effectiveness
        let control_effectiveness = self.get_control_effectiveness(&trust_principles).await?;

        Ok(DashboardData {
            compliance_score,
            trust_principles,
            alerts,
            evidence_summary,
            historical_scores,
            control_effectiveness,
            time_range,
            generated_at: Utc::now(),
        })
    }

    /// Get active compliance alerts
    async fn get_active_alerts(&self, principles: &[TrustPrincipleStatus]) -> Result<Vec<ComplianceAlert>> {
        let mut alerts = Vec::new();

        for status in principles {
            if !status.is_compliant {
                alerts.push(ComplianceAlert {
                    id: uuid::Uuid::new_v4().to_string(),
                    severity: AlertSeverity::High,
                    principle: status.principle,
                    title: format!("{} below threshold", status.principle.as_str()),
                    description: format!(
                        "{} compliance score {:.1}% is below threshold {:.1}%",
                        status.principle.as_str(),
                        status.score,
                        status.threshold
                    ),
                    created_at: Utc::now(),
                    acknowledged: false,
                });
            }

            // Check for critical controls
            for control in &status.controls {
                if control.effectiveness < 90.0 {
                    alerts.push(ComplianceAlert {
                        id: uuid::Uuid::new_v4().to_string(),
                        severity: AlertSeverity::Medium,
                        principle: status.principle,
                        title: format!("Control {} effectiveness low", control.control_id),
                        description: format!(
                            "Control {} effectiveness is {:.1}%",
                            control.control_id,
                            control.effectiveness
                        ),
                        created_at: Utc::now(),
                        acknowledged: false,
                    });
                }
            }
        }

        Ok(alerts)
    }

    /// Get evidence summary
    async fn get_evidence_summary(&self, time_range: &TimeRange) -> Result<EvidenceSummary> {
        // In production, this would query the evidence store
        let mut evidence_by_principle = HashMap::new();
        evidence_by_principle.insert(TrustPrinciple::Security, 1250);
        evidence_by_principle.insert(TrustPrinciple::Availability, 2100);
        evidence_by_principle.insert(TrustPrinciple::ProcessingIntegrity, 1800);
        evidence_by_principle.insert(TrustPrinciple::Confidentiality, 950);
        evidence_by_principle.insert(TrustPrinciple::Privacy, 650);

        let total_evidence_items: usize = evidence_by_principle.values().sum();

        let recent_collections = vec![
            RecentCollection {
                control_id: "CC6.1".to_string(),
                evidence_type: "Access Logs".to_string(),
                collected_at: Utc::now() - Duration::hours(2),
                item_count: 150,
            },
            RecentCollection {
                control_id: "A1.1".to_string(),
                evidence_type: "Uptime Reports".to_string(),
                collected_at: Utc::now() - Duration::hours(4),
                item_count: 24,
            },
        ];

        Ok(EvidenceSummary {
            total_evidence_items,
            evidence_by_principle,
            recent_collections,
            collection_coverage: 98.5,
        })
    }

    /// Get historical compliance scores
    async fn get_historical_scores(&self, time_range: &TimeRange) -> Result<Vec<HistoricalScore>> {
        // In production, this would query historical data
        let mut scores = Vec::new();
        let duration = time_range.to_duration();
        let points = 30; // Number of data points

        for i in 0..points {
            let offset = duration / points as i32 * i as i32;
            let timestamp = time_range.start_time() + offset;

            let mut by_principle = HashMap::new();
            by_principle.insert(TrustPrinciple::Security, 97.0 + (i as f64 * 0.1));
            by_principle.insert(TrustPrinciple::Availability, 99.9);
            by_principle.insert(TrustPrinciple::ProcessingIntegrity, 99.5);
            by_principle.insert(TrustPrinciple::Confidentiality, 100.0);
            by_principle.insert(TrustPrinciple::Privacy, 97.5 + (i as f64 * 0.05));

            let overall = by_principle.values().sum::<f64>() / by_principle.len() as f64;

            scores.push(HistoricalScore {
                timestamp,
                overall,
                by_principle,
            });
        }

        Ok(scores)
    }

    /// Get control effectiveness mapping
    async fn get_control_effectiveness(
        &self,
        principles: &[TrustPrincipleStatus],
    ) -> Result<HashMap<String, f64>> {
        let mut effectiveness = HashMap::new();

        for status in principles {
            for control in &status.controls {
                effectiveness.insert(control.control_id.clone(), control.effectiveness);
            }
        }

        Ok(effectiveness)
    }

    /// Get executive summary
    pub async fn get_executive_summary(&self) -> Result<ExecutiveSummary> {
        debug!("Generating executive summary");

        let compliance_score = self.monitor.calculate_compliance_score().await?;
        let trust_summary = self.trust_monitor.generate_summary().await?;
        let dashboard_data = self.get_dashboard_data(TimeRange::Last30Days).await?;

        Ok(ExecutiveSummary {
            overall_compliance: compliance_score.overall,
            is_compliant: trust_summary.overall_compliant,
            principles_at_risk: trust_summary.principles_at_risk,
            critical_alerts: dashboard_data.alerts.iter()
                .filter(|a| matches!(a.severity, AlertSeverity::Critical | AlertSeverity::High))
                .count(),
            evidence_items: dashboard_data.evidence_summary.total_evidence_items,
            last_assessment: compliance_score.calculated_at,
        })
    }

    /// Get compliance trends
    pub async fn get_compliance_trends(&self, days: u32) -> Result<ComplianceTrends> {
        debug!("Calculating compliance trends for {} days", days);

        let time_range = TimeRange::Custom {
            start: Utc::now() - Duration::days(days as i64),
            end: Utc::now(),
        };

        let historical = self.get_historical_scores(&time_range).await?;

        // Calculate trends
        let mut principle_trends = HashMap::new();
        
        for principle in TrustPrinciple::all() {
            if let (Some(first), Some(last)) = (historical.first(), historical.last()) {
                let first_score = first.by_principle.get(&principle).unwrap_or(&0.0);
                let last_score = last.by_principle.get(&principle).unwrap_or(&0.0);
                let change = last_score - first_score;
                
                principle_trends.insert(principle, PrincipleTrend {
                    current_score: *last_score,
                    change_percentage: (change / first_score) * 100.0,
                    is_improving: change > 0.0,
                });
            }
        }

        Ok(ComplianceTrends {
            period_days: days,
            principle_trends,
            overall_trend: self.calculate_overall_trend(&historical)?,
        })
    }

    /// Calculate overall trend
    fn calculate_overall_trend(&self, historical: &[HistoricalScore]) -> Result<f64> {
        if historical.len() < 2 {
            return Ok(0.0);
        }

        let first = historical.first().unwrap().overall;
        let last = historical.last().unwrap().overall;
        
        Ok(((last - first) / first) * 100.0)
    }
}

/// Executive summary
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExecutiveSummary {
    pub overall_compliance: f64,
    pub is_compliant: bool,
    pub principles_at_risk: Vec<TrustPrinciple>,
    pub critical_alerts: usize,
    pub evidence_items: usize,
    pub last_assessment: DateTime<Utc>,
}

/// Compliance trends
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComplianceTrends {
    pub period_days: u32,
    pub principle_trends: HashMap<TrustPrinciple, PrincipleTrend>,
    pub overall_trend: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PrincipleTrend {
    pub current_score: f64,
    pub change_percentage: f64,
    pub is_improving: bool,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_time_range_duration() {
        let range = TimeRange::Last7Days;
        let duration = range.to_duration();
        assert_eq!(duration, Duration::days(7));
    }

    #[test]
    fn test_alert_severity() {
        let alert = ComplianceAlert {
            id: "test".to_string(),
            severity: AlertSeverity::Critical,
            principle: TrustPrinciple::Security,
            title: "Test Alert".to_string(),
            description: "Test description".to_string(),
            created_at: Utc::now(),
            acknowledged: false,
        };

        assert!(matches!(alert.severity, AlertSeverity::Critical));
    }
}