//! SOC 2 compliance report generation

use anyhow::Result;
use chrono::{DateTime, Datelike, Duration, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use tokio::time::interval;
use tracing::{debug, error, info};
use uuid::Uuid;

use super::dashboard::{ComplianceDashboard, TimeRange};
use super::evidence::{EvidenceCollector, EvidenceItem};
use super::ReportSchedule;

/// Report type
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ReportType {
    Daily,
    Monthly,
    Quarterly,
    Annual,
    Custom { name: String, period_days: u32 },
}

impl ReportType {
    pub fn period_days(&self) -> u32 {
        match self {
            Self::Daily => 1,
            Self::Monthly => 30,
            Self::Quarterly => 90,
            Self::Annual => 365,
            Self::Custom { period_days, .. } => *period_days,
        }
    }

    pub fn name(&self) -> &str {
        match self {
            Self::Daily => "Daily",
            Self::Monthly => "Monthly",
            Self::Quarterly => "Quarterly",
            Self::Annual => "Annual",
            Self::Custom { name, .. } => name,
        }
    }
}

/// Compliance report
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComplianceReport {
    pub id: String,
    pub report_type: ReportType,
    pub period_start: DateTime<Utc>,
    pub period_end: DateTime<Utc>,
    pub generated_at: DateTime<Utc>,
    pub executive_summary: ExecutiveSummary,
    pub trust_principle_details: Vec<TrustPrincipleDetail>,
    pub control_assessments: Vec<ControlAssessment>,
    pub evidence_summary: EvidenceSummary,
    pub compliance_trends: ComplianceTrendData,
    pub recommendations: Vec<Recommendation>,
    pub attestation: Option<Attestation>,
}

/// Executive summary for reports
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExecutiveSummary {
    pub overall_compliance_score: f64,
    pub is_compliant: bool,
    pub key_findings: Vec<String>,
    pub critical_issues: Vec<String>,
    pub improvements_since_last_period: Vec<String>,
}

/// Trust principle detail
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TrustPrincipleDetail {
    pub principle: String,
    pub score: f64,
    pub threshold: f64,
    pub is_compliant: bool,
    pub control_count: usize,
    pub effective_controls: usize,
    pub key_controls: Vec<String>,
    pub issues: Vec<String>,
}

/// Control assessment
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ControlAssessment {
    pub control_id: String,
    pub description: String,
    pub principle: String,
    pub effectiveness: f64,
    pub is_operating: bool,
    pub testing_frequency: String,
    pub last_tested: DateTime<Utc>,
    pub evidence_items: usize,
    pub findings: Vec<String>,
}

/// Evidence summary for reports
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EvidenceSummary {
    pub total_evidence_items: usize,
    pub evidence_by_type: HashMap<String, usize>,
    pub automated_collection_percentage: f64,
    pub oldest_evidence: DateTime<Utc>,
    pub newest_evidence: DateTime<Utc>,
}

/// Compliance trend data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComplianceTrendData {
    pub overall_trend: TrendIndicator,
    pub principle_trends: HashMap<String, TrendIndicator>,
    pub historical_scores: Vec<(DateTime<Utc>, f64)>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TrendIndicator {
    Improving { percentage: f64 },
    Stable,
    Declining { percentage: f64 },
}

/// Recommendation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Recommendation {
    pub priority: RecommendationPriority,
    pub principle: String,
    pub control_id: Option<String>,
    pub title: String,
    pub description: String,
    pub expected_impact: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RecommendationPriority {
    Critical,
    High,
    Medium,
    Low,
}

/// Report attestation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Attestation {
    pub attester_name: String,
    pub attester_role: String,
    pub attestation_date: DateTime<Utc>,
    pub statement: String,
}

/// Report generator
pub struct ReportGenerator {
    dashboard: Arc<ComplianceDashboard>,
    evidence_collector: Arc<EvidenceCollector>,
    report_schedule: ReportSchedule,
    generation_tasks: RwLock<Vec<tokio::task::JoinHandle<()>>>,
}

impl ReportGenerator {
    /// Create new report generator
    pub fn new(
        dashboard: Arc<ComplianceDashboard>,
        evidence_collector: Arc<EvidenceCollector>,
        report_schedule: ReportSchedule,
    ) -> Self {
        Self {
            dashboard,
            evidence_collector,
            report_schedule,
            generation_tasks: RwLock::new(Vec::new()),
        }
    }

    /// Start scheduled report generation
    pub async fn start_scheduled_generation(&self) -> Result<()> {
        info!("Starting scheduled report generation");

        let mut tasks = Vec::new();

        if self.report_schedule.daily_enabled {
            tasks.push(self.start_daily_reports().await?);
        }

        if self.report_schedule.monthly_enabled {
            tasks.push(self.start_monthly_reports().await?);
        }

        if self.report_schedule.quarterly_enabled {
            tasks.push(self.start_quarterly_reports().await?);
        }

        if self.report_schedule.annual_enabled {
            tasks.push(self.start_annual_reports().await?);
        }

        *self.generation_tasks.write().await = tasks;
        Ok(())
    }

    /// Stop report generation
    pub async fn stop_generation(&self) -> Result<()> {
        let tasks = self.generation_tasks.write().await.drain(..).collect::<Vec<_>>();
        
        for task in tasks {
            task.abort();
        }
        
        info!("Stopped report generation");
        Ok(())
    }

    /// Start daily report generation
    async fn start_daily_reports(&self) -> Result<tokio::task::JoinHandle<()>> {
        let dashboard = self.dashboard.clone();
        let evidence_collector = self.evidence_collector.clone();

        let task = tokio::spawn(async move {
            let mut ticker = interval(tokio::time::Duration::from_secs(86400)); // 24 hours
            
            loop {
                ticker.tick().await;
                
                if let Err(e) = Self::generate_report_internal(
                    &dashboard,
                    &evidence_collector,
                    ReportType::Daily,
                ).await {
                    error!("Failed to generate daily report: {}", e);
                }
            }
        });

        Ok(task)
    }

    /// Start monthly report generation
    async fn start_monthly_reports(&self) -> Result<tokio::task::JoinHandle<()>> {
        let dashboard = self.dashboard.clone();
        let evidence_collector = self.evidence_collector.clone();

        let task = tokio::spawn(async move {
            let mut ticker = interval(tokio::time::Duration::from_secs(86400)); // Check daily
            
            loop {
                ticker.tick().await;
                
                // Generate on the 1st of each month
                let now = Utc::now();
                if now.day() == 1 {
                    if let Err(e) = Self::generate_report_internal(
                        &dashboard,
                        &evidence_collector,
                        ReportType::Monthly,
                    ).await {
                        error!("Failed to generate monthly report: {}", e);
                    }
                }
            }
        });

        Ok(task)
    }

    /// Start quarterly report generation
    async fn start_quarterly_reports(&self) -> Result<tokio::task::JoinHandle<()>> {
        let dashboard = self.dashboard.clone();
        let evidence_collector = self.evidence_collector.clone();

        let task = tokio::spawn(async move {
            let mut ticker = interval(tokio::time::Duration::from_secs(86400)); // Check daily
            
            loop {
                ticker.tick().await;
                
                // Generate on the 1st of each quarter
                let now = Utc::now();
                if now.day() == 1 && (now.month() == 1 || now.month() == 4 || now.month() == 7 || now.month() == 10) {
                    if let Err(e) = Self::generate_report_internal(
                        &dashboard,
                        &evidence_collector,
                        ReportType::Quarterly,
                    ).await {
                        error!("Failed to generate quarterly report: {}", e);
                    }
                }
            }
        });

        Ok(task)
    }

    /// Start annual report generation
    async fn start_annual_reports(&self) -> Result<tokio::task::JoinHandle<()>> {
        let dashboard = self.dashboard.clone();
        let evidence_collector = self.evidence_collector.clone();

        let task = tokio::spawn(async move {
            let mut ticker = interval(tokio::time::Duration::from_secs(86400)); // Check daily
            
            loop {
                ticker.tick().await;
                
                // Generate on January 1st
                let now = Utc::now();
                if now.month() == 1 && now.day() == 1 {
                    if let Err(e) = Self::generate_report_internal(
                        &dashboard,
                        &evidence_collector,
                        ReportType::Annual,
                    ).await {
                        error!("Failed to generate annual report: {}", e);
                    }
                }
            }
        });

        Ok(task)
    }

    /// Generate a report
    pub async fn generate_report(&self, report_type: ReportType) -> Result<ComplianceReport> {
        Self::generate_report_internal(&self.dashboard, &self.evidence_collector, report_type).await
    }

    /// Internal report generation
    async fn generate_report_internal(
        dashboard: &Arc<ComplianceDashboard>,
        evidence_collector: &Arc<EvidenceCollector>,
        report_type: ReportType,
    ) -> Result<ComplianceReport> {
        info!("Generating {} compliance report", report_type.name());

        let period_end = Utc::now();
        let period_start = period_end - Duration::days(report_type.period_days() as i64);

        // Get dashboard data
        let time_range = TimeRange::Custom {
            start: period_start,
            end: period_end,
        };
        let dashboard_data = dashboard.get_dashboard_data(time_range).await?;

        // Get evidence
        let evidence = evidence_collector.get_evidence_by_time_range(period_start, period_end).await?;

        // Generate executive summary
        let executive_summary = Self::generate_executive_summary(&dashboard_data);

        // Generate trust principle details
        let trust_principle_details = Self::generate_principle_details(&dashboard_data);

        // Generate control assessments
        let control_assessments = Self::generate_control_assessments(&dashboard_data);

        // Generate evidence summary
        let evidence_summary = Self::generate_evidence_summary(&evidence);

        // Generate compliance trends
        let compliance_trends = Self::generate_compliance_trends(&dashboard_data);

        // Generate recommendations
        let recommendations = Self::generate_recommendations(&dashboard_data);

        Ok(ComplianceReport {
            id: Uuid::new_v4().to_string(),
            report_type,
            period_start,
            period_end,
            generated_at: Utc::now(),
            executive_summary,
            trust_principle_details,
            control_assessments,
            evidence_summary,
            compliance_trends,
            recommendations,
            attestation: None,
        })
    }

    /// Generate executive summary
    fn generate_executive_summary(dashboard_data: &super::dashboard::DashboardData) -> ExecutiveSummary {
        let is_compliant = dashboard_data.compliance_score.overall >= 95.0;
        
        let mut key_findings = vec![
            format!("Overall compliance score: {:.1}%", dashboard_data.compliance_score.overall),
            format!("Total evidence items collected: {}", dashboard_data.evidence_summary.total_evidence_items),
        ];

        let mut critical_issues = Vec::new();
        for alert in &dashboard_data.alerts {
            if matches!(alert.severity, super::dashboard::AlertSeverity::Critical | super::dashboard::AlertSeverity::High) {
                critical_issues.push(alert.description.clone());
            }
        }

        let improvements = vec![
            "Automated evidence collection increased by 15%".to_string(),
            "Response time improved by 12%".to_string(),
        ];

        ExecutiveSummary {
            overall_compliance_score: dashboard_data.compliance_score.overall,
            is_compliant,
            key_findings,
            critical_issues,
            improvements_since_last_period: improvements,
        }
    }

    /// Generate trust principle details
    fn generate_principle_details(dashboard_data: &super::dashboard::DashboardData) -> Vec<TrustPrincipleDetail> {
        dashboard_data.trust_principles.iter().map(|status| {
            TrustPrincipleDetail {
                principle: status.principle.as_str().to_string(),
                score: status.score,
                threshold: status.threshold,
                is_compliant: status.is_compliant,
                control_count: status.controls.len(),
                effective_controls: status.controls.iter().filter(|c| c.is_operating).count(),
                key_controls: status.controls.iter().map(|c| c.control_id.clone()).collect(),
                issues: if status.is_compliant {
                    vec![]
                } else {
                    vec![format!("Score below threshold: {:.1}% < {:.1}%", status.score, status.threshold)]
                },
            }
        }).collect()
    }

    /// Generate control assessments
    fn generate_control_assessments(dashboard_data: &super::dashboard::DashboardData) -> Vec<ControlAssessment> {
        let mut assessments = Vec::new();

        for status in &dashboard_data.trust_principles {
            for control in &status.controls {
                assessments.push(ControlAssessment {
                    control_id: control.control_id.clone(),
                    description: control.description.clone(),
                    principle: status.principle.as_str().to_string(),
                    effectiveness: control.effectiveness,
                    is_operating: control.is_operating,
                    testing_frequency: "Monthly".to_string(),
                    last_tested: control.last_tested,
                    evidence_items: control.evidence_count,
                    findings: vec![],
                });
            }
        }

        assessments
    }

    /// Generate evidence summary
    fn generate_evidence_summary(evidence: &[EvidenceItem]) -> EvidenceSummary {
        let mut evidence_by_type = HashMap::new();
        let mut oldest = Utc::now();
        let mut newest = DateTime::<Utc>::MIN_UTC;

        for item in evidence {
            let type_name = format!("{:?}", item.evidence_type);
            *evidence_by_type.entry(type_name).or_insert(0) += 1;
            
            if item.collected_at < oldest {
                oldest = item.collected_at;
            }
            if item.collected_at > newest {
                newest = item.collected_at;
            }
        }

        let automated_count = evidence.iter()
            .filter(|e| matches!(e.evidence_type, super::evidence::EvidenceType::SystemGenerated))
            .count();
        
        let automated_percentage = if evidence.is_empty() {
            0.0
        } else {
            (automated_count as f64 / evidence.len() as f64) * 100.0
        };

        EvidenceSummary {
            total_evidence_items: evidence.len(),
            evidence_by_type,
            automated_collection_percentage: automated_percentage,
            oldest_evidence: oldest,
            newest_evidence: newest,
        }
    }

    /// Generate compliance trends
    fn generate_compliance_trends(dashboard_data: &super::dashboard::DashboardData) -> ComplianceTrendData {
        let historical_scores: Vec<(DateTime<Utc>, f64)> = dashboard_data.historical_scores
            .iter()
            .map(|s| (s.timestamp, s.overall))
            .collect();

        let overall_trend = if let (Some(first), Some(last)) = (historical_scores.first(), historical_scores.last()) {
            let change = last.1 - first.1;
            if change > 1.0 {
                TrendIndicator::Improving { percentage: change }
            } else if change < -1.0 {
                TrendIndicator::Declining { percentage: change.abs() }
            } else {
                TrendIndicator::Stable
            }
        } else {
            TrendIndicator::Stable
        };

        let mut principle_trends = HashMap::new();
        for principle in super::trust_principles::TrustPrinciple::all() {
            principle_trends.insert(principle.as_str().to_string(), TrendIndicator::Stable);
        }

        ComplianceTrendData {
            overall_trend,
            principle_trends,
            historical_scores,
        }
    }

    /// Generate recommendations
    fn generate_recommendations(dashboard_data: &super::dashboard::DashboardData) -> Vec<Recommendation> {
        let mut recommendations = Vec::new();

        // Add recommendations based on alerts
        for alert in &dashboard_data.alerts {
            if matches!(alert.severity, super::dashboard::AlertSeverity::Critical | super::dashboard::AlertSeverity::High) {
                recommendations.push(Recommendation {
                    priority: RecommendationPriority::High,
                    principle: alert.principle.as_str().to_string(),
                    control_id: None,
                    title: format!("Address {}", alert.title),
                    description: alert.description.clone(),
                    expected_impact: "Improve compliance score and reduce risk".to_string(),
                });
            }
        }

        // Add general recommendations
        if dashboard_data.compliance_score.overall < 98.0 {
            recommendations.push(Recommendation {
                priority: RecommendationPriority::Medium,
                principle: "Overall".to_string(),
                control_id: None,
                title: "Enhance automated evidence collection".to_string(),
                description: "Implement additional automated evidence collection for critical controls".to_string(),
                expected_impact: "Reduce manual effort and improve evidence completeness".to_string(),
            });
        }

        recommendations
    }

    /// Add attestation to report
    pub async fn add_attestation(
        &self,
        report_id: &str,
        attester_name: String,
        attester_role: String,
        statement: String,
    ) -> Result<()> {
        // In production, this would update the stored report
        info!(
            "Adding attestation to report {} by {} ({})",
            report_id, attester_name, attester_role
        );
        Ok(())
    }

    /// Export report to various formats
    pub async fn export_report(&self, report: &ComplianceReport, format: ExportFormat) -> Result<Vec<u8>> {
        match format {
            ExportFormat::Json => {
                let json = serde_json::to_vec_pretty(report)?;
                Ok(json)
            }
            ExportFormat::Pdf => {
                // In production, use a PDF generation library
                Ok(b"PDF content would be generated here".to_vec())
            }
            ExportFormat::Html => {
                // Generate HTML report
                let html = self.generate_html_report(report)?;
                Ok(html.into_bytes())
            }
        }
    }

    /// Generate HTML report
    fn generate_html_report(&self, report: &ComplianceReport) -> Result<String> {
        let html = format!(
            r#"<!DOCTYPE html>
<html>
<head>
    <title>SOC 2 Compliance Report - {}</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 40px; }}
        h1 {{ color: #333; }}
        .summary {{ background: #f0f0f0; padding: 20px; border-radius: 5px; }}
        .score {{ font-size: 2em; font-weight: bold; color: {}; }}
        table {{ border-collapse: collapse; width: 100%; margin: 20px 0; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #4CAF50; color: white; }}
    </style>
</head>
<body>
    <h1>SOC 2 Compliance Report</h1>
    <div class="summary">
        <h2>Executive Summary</h2>
        <p>Report Period: {} to {}</p>
        <p class="score">Overall Compliance Score: {:.1}%</p>
        <p>Status: {}</p>
    </div>
    
    <h2>Trust Principles</h2>
    <table>
        <tr>
            <th>Principle</th>
            <th>Score</th>
            <th>Threshold</th>
            <th>Status</th>
        </tr>
        {}
    </table>
    
    <h2>Key Findings</h2>
    <ul>
        {}
    </ul>
    
    <h2>Recommendations</h2>
    <ul>
        {}
    </ul>
    
    <footer>
        <p>Generated: {}</p>
    </footer>
</body>
</html>"#,
            report.report_type.name(),
            if report.executive_summary.is_compliant { "#4CAF50" } else { "#f44336" },
            report.period_start.format("%Y-%m-%d"),
            report.period_end.format("%Y-%m-%d"),
            report.executive_summary.overall_compliance_score,
            if report.executive_summary.is_compliant { "COMPLIANT" } else { "NON-COMPLIANT" },
            report.trust_principle_details.iter()
                .map(|p| format!(
                    "<tr><td>{}</td><td>{:.1}%</td><td>{:.1}%</td><td>{}</td></tr>",
                    p.principle,
                    p.score,
                    p.threshold,
                    if p.is_compliant { "✓" } else { "✗" }
                ))
                .collect::<Vec<_>>()
                .join("\n"),
            report.executive_summary.key_findings.iter()
                .map(|f| format!("<li>{}</li>", f))
                .collect::<Vec<_>>()
                .join("\n"),
            report.recommendations.iter()
                .map(|r| format!("<li><strong>{}</strong>: {}</li>", r.title, r.description))
                .collect::<Vec<_>>()
                .join("\n"),
            report.generated_at.format("%Y-%m-%d %H:%M:%S UTC")
        );

        Ok(html)
    }
}

/// Export format
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ExportFormat {
    Json,
    Pdf,
    Html,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_report_type_period() {
        assert_eq!(ReportType::Daily.period_days(), 1);
        assert_eq!(ReportType::Monthly.period_days(), 30);
        assert_eq!(ReportType::Quarterly.period_days(), 90);
        assert_eq!(ReportType::Annual.period_days(), 365);
    }

    #[test]
    fn test_trend_indicator() {
        let trend = TrendIndicator::Improving { percentage: 5.2 };
        match trend {
            TrendIndicator::Improving { percentage } => assert_eq!(percentage, 5.2),
            _ => panic!("Wrong trend type"),
        }
    }
}