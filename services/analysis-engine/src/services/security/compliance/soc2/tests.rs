//! Unit tests for SOC 2 compliance automation

#[cfg(test)]
mod tests {
    use super::super::*;
    use crate::audit::AuditLogger;
    use prometheus::Registry;
    use std::sync::Arc;

    async fn create_test_service() -> Soc2Service {
        let config = Soc2Config::default();
        let registry = Arc::new(Registry::new());
        let audit_logger = Arc::new(AuditLogger::new(None));
        
        Soc2Service::new(config, registry, audit_logger, None)
            .await
            .expect("Failed to create SOC 2 service")
    }

    #[tokio::test]
    async fn test_soc2_service_creation() {
        let service = create_test_service().await;
        
        // Verify service is created
        assert!(service.health_check().await.is_ok());
    }

    #[tokio::test]
    async fn test_compliance_score_calculation() {
        let service = create_test_service().await;
        
        // Calculate compliance score
        let score = service.monitor().calculate_compliance_score().await.unwrap();
        
        // Verify score structure
        assert!(score.overall >= 0.0 && score.overall <= 100.0);
        assert!(score.security >= 0.0 && score.security <= 100.0);
        assert!(score.availability >= 0.0 && score.availability <= 100.0);
        assert!(score.processing_integrity >= 0.0 && score.processing_integrity <= 100.0);
        assert!(score.confidentiality >= 0.0 && score.confidentiality <= 100.0);
        assert!(score.privacy >= 0.0 && score.privacy <= 100.0);
    }

    #[tokio::test]
    async fn test_trust_principle_monitoring() {
        let service = create_test_service().await;
        
        // Get all trust principle statuses
        let statuses = service.dashboard().trust_monitor.get_all_status().await.unwrap();
        
        // Should have 5 principles
        assert_eq!(statuses.len(), 5);
        
        // Check each principle
        for status in statuses {
            assert!(status.score >= 0.0 && status.score <= 100.0);
            assert!(status.threshold > 0.0);
            assert!(!status.controls.is_empty());
        }
    }

    #[tokio::test]
    async fn test_evidence_collection() {
        use evidence::{EvidenceItem, EvidenceType, EvidenceData};
        use chrono::{Utc, Duration};
        use std::collections::HashMap;
        use uuid::Uuid;

        let service = create_test_service().await;
        
        // Create test evidence
        let evidence = EvidenceItem {
            id: Uuid::new_v4().to_string(),
            control_id: "TEST-001".to_string(),
            evidence_type: EvidenceType::SystemGenerated,
            title: "Test Evidence".to_string(),
            description: "Unit test evidence".to_string(),
            collected_at: Utc::now(),
            collector: "unit-test".to_string(),
            data: EvidenceData::Text("Test data".to_string()),
            metadata: HashMap::new(),
            retention_until: Utc::now() + Duration::days(365),
        };

        // Add evidence
        service.evidence_collector().add_evidence(evidence.clone()).await.unwrap();

        // Retrieve evidence
        let retrieved = service.evidence_collector()
            .get_evidence_for_control("TEST-001")
            .await
            .unwrap();

        assert_eq!(retrieved.len(), 1);
        assert_eq!(retrieved[0].id, evidence.id);
    }

    #[tokio::test]
    async fn test_report_generation() {
        use reports::ReportType;

        let service = create_test_service().await;
        
        // Generate daily report
        let report = service.report_generator()
            .generate_report(ReportType::Daily)
            .await
            .unwrap();

        // Verify report structure
        assert!(!report.id.is_empty());
        assert_eq!(report.report_type.name(), "Daily");
        assert!(report.executive_summary.overall_compliance_score >= 0.0);
        assert!(report.executive_summary.overall_compliance_score <= 100.0);
        assert!(!report.trust_principle_details.is_empty());
        assert!(!report.control_assessments.is_empty());
    }

    #[tokio::test]
    async fn test_metric_recording() {
        let service = create_test_service().await;
        let metrics = service.monitor().metrics.clone();

        // Record various metrics
        metrics.record_access_attempt(true, "jwt");
        metrics.record_access_attempt(false, "invalid");
        metrics.record_encryption_operation("encrypt", "AES-256-GCM");
        metrics.record_response_time("/api/test", "GET", 50.0);
        metrics.update_uptime(99.95);
        metrics.record_validation_failure("input", "error");

        // Verify metrics are recorded (would need to query Prometheus in real test)
        assert!(true); // Placeholder - in production, query metrics
    }

    #[tokio::test]
    async fn test_dashboard_data() {
        use dashboard::TimeRange;

        let service = create_test_service().await;
        
        // Get dashboard data
        let data = service.dashboard()
            .get_dashboard_data(TimeRange::Last30Days)
            .await
            .unwrap();

        // Verify dashboard data structure
        assert!(data.compliance_score.overall >= 0.0);
        assert_eq!(data.trust_principles.len(), 5);
        assert!(data.evidence_summary.total_evidence_items >= 0);
        assert!(!data.historical_scores.is_empty());
    }

    #[tokio::test]
    async fn test_compliance_alerts() {
        let service = create_test_service().await;
        
        // Get trust principle statuses
        let statuses = service.dashboard().trust_monitor.get_all_status().await.unwrap();
        
        // Get alerts based on statuses
        let dashboard_data = service.dashboard()
            .get_dashboard_data(dashboard::TimeRange::Last24Hours)
            .await
            .unwrap();

        // Alerts should be generated for non-compliant principles
        for status in statuses {
            if !status.is_compliant {
                let has_alert = dashboard_data.alerts.iter()
                    .any(|a| a.principle == status.principle);
                assert!(has_alert);
            }
        }
    }

    #[tokio::test]
    async fn test_evidence_package_generation() {
        use chrono::{Utc, Duration};

        let service = create_test_service().await;
        
        // Generate evidence package
        let package = service.evidence_collector()
            .generate_evidence_package(
                vec!["CC6.1".to_string(), "A1.1".to_string()],
                Utc::now() - Duration::days(30),
                Utc::now(),
            )
            .await
            .unwrap();

        // Verify package structure
        assert!(!package.id.is_empty());
        assert_eq!(package.control_ids.len(), 2);
    }

    #[tokio::test]
    async fn test_control_effectiveness() {
        let service = create_test_service().await;
        let metrics = service.monitor().metrics.clone();

        // Update control effectiveness
        metrics.update_control_effectiveness("CC6.1", "security", 98.5);
        metrics.update_control_effectiveness("A1.1", "availability", 99.9);
        
        // Get trust principle status
        let security_status = service.dashboard().trust_monitor
            .get_principle_status(trust_principles::TrustPrinciple::Security)
            .await
            .unwrap();

        // Verify control is included
        let control = security_status.controls.iter()
            .find(|c| c.control_id == "CC6.1");
        assert!(control.is_some());
    }

    #[tokio::test]
    async fn test_privacy_metrics() {
        let service = create_test_service().await;
        let metrics = service.monitor().metrics.clone();

        // Record privacy events
        metrics.consent_changes.with_label_values(&["analytics", "granted"]).inc();
        metrics.deletion_requests.inc();
        metrics.export_requests.inc();

        // Calculate privacy score
        let score = service.monitor().calculate_compliance_score().await.unwrap();
        assert!(score.privacy >= 0.0 && score.privacy <= 100.0);
    }

    #[tokio::test]
    async fn test_report_export() {
        use reports::{ReportType, ExportFormat};

        let service = create_test_service().await;
        
        // Generate report
        let report = service.report_generator()
            .generate_report(ReportType::Daily)
            .await
            .unwrap();

        // Export to different formats
        let json_export = service.report_generator()
            .export_report(&report, ExportFormat::Json)
            .await
            .unwrap();

        let html_export = service.report_generator()
            .export_report(&report, ExportFormat::Html)
            .await
            .unwrap();

        // Verify exports
        assert!(!json_export.is_empty());
        assert!(!html_export.is_empty());
        
        // Verify JSON is valid
        let parsed: serde_json::Value = serde_json::from_slice(&json_export).unwrap();
        assert!(parsed.is_object());
        
        // Verify HTML contains expected content
        let html_str = String::from_utf8(html_export).unwrap();
        assert!(html_str.contains("SOC 2 Compliance Report"));
    }

    #[tokio::test]
    async fn test_monitoring_lifecycle() {
        let service = create_test_service().await;
        
        // Start monitoring
        assert!(service.start_monitoring().await.is_ok());
        
        // Stop monitoring
        assert!(service.stop_monitoring().await.is_ok());
    }

    #[tokio::test]
    async fn test_api_creation() {
        let service = create_test_service().await;
        
        // Create API
        let api = service.create_api();
        let routes = api.routes();
        
        // Verify routes are created (would need to test with actual HTTP client)
        assert!(true); // Placeholder
    }
}