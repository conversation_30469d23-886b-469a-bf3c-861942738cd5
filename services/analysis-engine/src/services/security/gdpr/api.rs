//! REST API endpoints for GDPR compliance operations
//! 
//! This module provides HTTP endpoints for data deletion, export,
//! consent management, and compliance operations.

use crate::services::security::gdpr::{
    ConsentService, ConsentUpdate, DeletionScope, DeletionService, ExportFormat, ExportService,
    GdprService,
};
use crate::services::security::gdpr::models::{ConsentType, DeletionRequest, ExportRequest};
use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::{IntoResponse, Response},
    Json,
};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tracing::{error, info};

/// Request to delete user data
#[derive(Debug, Deserialize)]
pub struct DeleteUserDataRequest {
    /// Reason for deletion
    pub reason: String,
    /// Optional scope to limit deletion
    pub scope: Option<DeletionScope>,
}

/// Request to export user data
#[derive(Debug, Deserialize)]
pub struct ExportUserDataRequest {
    /// Export format
    pub format: ExportFormat,
    /// Whether to include encrypted fields
    pub include_encrypted: bool,
}

/// Query parameters for consent history
#[derive(Debug, Deserialize)]
pub struct ConsentHistoryQuery {
    /// Filter by consent type
    pub consent_type: Option<String>,
    /// Maximum number of records
    pub limit: Option<usize>,
}

/// Response for API endpoints
#[derive(Debug, Serialize)]
pub struct ApiResponse<T> {
    pub success: bool,
    pub data: Option<T>,
    pub error: Option<String>,
}

impl<T: Serialize> ApiResponse<T> {
    pub fn success(data: T) -> Self {
        Self {
            success: true,
            data: Some(data),
            error: None,
        }
    }

    pub fn error(message: String) -> Self {
        Self {
            success: false,
            data: None,
            error: Some(message),
        }
    }
}

/// Delete user data (GDPR Article 17 - Right to Erasure)
/// 
/// DELETE /api/gdpr/users/{user_id}
pub async fn delete_user_data(
    State(gdpr): State<Arc<GdprService>>,
    Path(user_id): Path<String>,
    Json(request): Json<DeleteUserDataRequest>,
) -> Response {
    info!("Received deletion request for user: {}", user_id);

    match gdpr
        .deletion_service()
        .initiate_deletion(&user_id, &request.reason, request.scope)
        .await
    {
        Ok(deletion_request) => {
            info!("Deletion request created: {}", deletion_request.request_id);
            (
                StatusCode::ACCEPTED,
                Json(ApiResponse::success(deletion_request)),
            )
                .into_response()
        }
        Err(e) => {
            error!("Failed to initiate deletion: {}", e);
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ApiResponse::<DeletionRequest>::error(e.to_string())),
            )
                .into_response()
        }
    }
}

/// Get deletion status for a user
/// 
/// GET /api/gdpr/users/{user_id}/deletion-status
pub async fn get_deletion_status(
    State(gdpr): State<Arc<GdprService>>,
    Path(user_id): Path<String>,
) -> Response {
    match gdpr.deletion_service().get_deletion_status(&user_id).await {
        Ok(requests) => (StatusCode::OK, Json(ApiResponse::success(requests))).into_response(),
        Err(e) => {
            error!("Failed to get deletion status: {}", e);
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ApiResponse::<Vec<DeletionRequest>>::error(e.to_string())),
            )
                .into_response()
        }
    }
}

/// Export user data (GDPR Article 20 - Data Portability)
/// 
/// POST /api/gdpr/users/{user_id}/export
pub async fn export_user_data(
    State(gdpr): State<Arc<GdprService>>,
    Path(user_id): Path<String>,
    Json(request): Json<ExportUserDataRequest>,
) -> Response {
    info!("Received export request for user: {}", user_id);

    match gdpr
        .export_service()
        .initiate_export(&user_id, request.format, request.include_encrypted)
        .await
    {
        Ok(export_request) => {
            info!("Export request created: {}", export_request.request_id);
            (
                StatusCode::ACCEPTED,
                Json(ApiResponse::success(export_request)),
            )
                .into_response()
        }
        Err(e) => {
            error!("Failed to initiate export: {}", e);
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ApiResponse::<ExportRequest>::error(e.to_string())),
            )
                .into_response()
        }
    }
}

/// Get export status
/// 
/// GET /api/gdpr/export/{request_id}
pub async fn get_export_status(
    State(gdpr): State<Arc<GdprService>>,
    Path(request_id): Path<String>,
) -> Response {
    match gdpr.export_service().get_export_request(&request_id).await {
        Ok(request) => (StatusCode::OK, Json(ApiResponse::success(request))).into_response(),
        Err(e) => {
            error!("Failed to get export status: {}", e);
            (
                StatusCode::NOT_FOUND,
                Json(ApiResponse::<ExportRequest>::error(e.to_string())),
            )
                .into_response()
        }
    }
}

/// Download exported data
/// 
/// GET /api/gdpr/export/download/{request_id}
pub async fn download_export(
    State(gdpr): State<Arc<GdprService>>,
    Path(request_id): Path<String>,
) -> Response {
    // In production, this would:
    // 1. Verify the export is ready
    // 2. Check authorization
    // 3. Return a redirect to cloud storage signed URL
    // 4. Log the download

    match gdpr.export_service().mark_as_downloaded(&request_id, None).await {
        Ok(_) => {
            // For now, return a placeholder response
            (
                StatusCode::OK,
                Json(ApiResponse::success("Download URL would be provided here")),
            )
                .into_response()
        }
        Err(e) => {
            error!("Failed to process download: {}", e);
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ApiResponse::<String>::error(e.to_string())),
            )
                .into_response()
        }
    }
}

/// Update user consent
/// 
/// POST /api/gdpr/consent
pub async fn update_consent(
    State(gdpr): State<Arc<GdprService>>,
    Json(update): Json<ConsentUpdate>,
) -> Response {
    info!(
        "Updating consent for user {} - type: {:?}",
        update.user_id, update.consent_type
    );

    match gdpr.consent_service().update_consent(update).await {
        Ok(record) => (StatusCode::OK, Json(ApiResponse::success(record))).into_response(),
        Err(e) => {
            error!("Failed to update consent: {}", e);
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ApiResponse::<serde_json::Value>::error(e.to_string())),
            )
                .into_response()
        }
    }
}

/// Get consent status for a user
/// 
/// GET /api/gdpr/consent/{user_id}
pub async fn get_consent_status(
    State(gdpr): State<Arc<GdprService>>,
    Path(user_id): Path<String>,
) -> Response {
    match gdpr.consent_service().get_consent_status(&user_id).await {
        Ok(status) => (StatusCode::OK, Json(ApiResponse::success(status))).into_response(),
        Err(e) => {
            error!("Failed to get consent status: {}", e);
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ApiResponse::<serde_json::Value>::error(e.to_string())),
            )
                .into_response()
        }
    }
}

/// Get consent history for a user
/// 
/// GET /api/gdpr/consent/{user_id}/history
pub async fn get_consent_history(
    State(gdpr): State<Arc<GdprService>>,
    Path(user_id): Path<String>,
    Query(params): Query<ConsentHistoryQuery>,
) -> Response {
    let consent_type = params.consent_type.map(|ct| match ct.as_str() {
        "DataProcessing" => ConsentType::DataProcessing,
        "Analytics" => ConsentType::Analytics,
        "Marketing" => ConsentType::Marketing,
        "DataSharing" => ConsentType::DataSharing,
        "AutomatedDecisionMaking" => ConsentType::AutomatedDecisionMaking,
        custom => ConsentType::Custom(custom.to_string()),
    });

    match gdpr
        .consent_service()
        .get_consent_history(&user_id, consent_type, params.limit)
        .await
    {
        Ok(history) => (StatusCode::OK, Json(ApiResponse::success(history))).into_response(),
        Err(e) => {
            error!("Failed to get consent history: {}", e);
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ApiResponse::<Vec<serde_json::Value>>::error(e.to_string())),
            )
                .into_response()
        }
    }
}

/// Generate consent receipt
/// 
/// GET /api/gdpr/consent/{user_id}/receipt
pub async fn get_consent_receipt(
    State(gdpr): State<Arc<GdprService>>,
    Path(user_id): Path<String>,
) -> Response {
    match gdpr
        .consent_service()
        .generate_consent_receipt(&user_id)
        .await
    {
        Ok(receipt) => (StatusCode::OK, Json(ApiResponse::success(receipt))).into_response(),
        Err(e) => {
            error!("Failed to generate consent receipt: {}", e);
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ApiResponse::<serde_json::Value>::error(e.to_string())),
            )
                .into_response()
        }
    }
}

/// Withdraw all consents for a user
/// 
/// POST /api/gdpr/consent/{user_id}/withdraw-all
pub async fn withdraw_all_consents(
    State(gdpr): State<Arc<GdprService>>,
    Path(user_id): Path<String>,
) -> Response {
    info!("Withdrawing all consents for user: {}", user_id);

    match gdpr
        .consent_service()
        .withdraw_all_consents(&user_id, None, None)
        .await
    {
        Ok(records) => (StatusCode::OK, Json(ApiResponse::success(records))).into_response(),
        Err(e) => {
            error!("Failed to withdraw consents: {}", e);
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ApiResponse::<Vec<serde_json::Value>>::error(e.to_string())),
            )
                .into_response()
        }
    }
}

/// Health check for GDPR services
/// 
/// GET /api/gdpr/health
pub async fn health_check(State(gdpr): State<Arc<GdprService>>) -> Response {
    match gdpr.health_check().await {
        Ok(_) => (
            StatusCode::OK,
            Json(ApiResponse::success(serde_json::json!({
                "status": "healthy",
                "services": {
                    "deletion": "operational",
                    "export": "operational",
                    "consent": "operational"
                }
            }))),
        )
            .into_response(),
        Err(e) => {
            error!("GDPR health check failed: {}", e);
            (
                StatusCode::SERVICE_UNAVAILABLE,
                Json(ApiResponse::<serde_json::Value>::error(format!(
                    "Health check failed: {}",
                    e
                ))),
            )
                .into_response()
        }
    }
}

/// Process pending GDPR requests (for scheduled jobs)
/// 
/// POST /api/gdpr/process-pending
pub async fn process_pending_requests(State(gdpr): State<Arc<GdprService>>) -> Response {
    info!("Processing pending GDPR requests");

    let mut results = serde_json::json!({
        "deletions": 0,
        "exports": 0,
        "errors": Vec::<String>::new()
    });

    // Process pending deletions
    match gdpr.deletion_service().process_pending_deletions().await {
        Ok(reports) => {
            results["deletions"] = serde_json::json!(reports.len());
        }
        Err(e) => {
            error!("Failed to process pending deletions: {}", e);
            if let Some(errors) = results["errors"].as_array_mut() {
                errors.push(serde_json::json!(format!("Deletion processing: {}", e)));
            }
        }
    }

    // Process pending exports
    match gdpr.export_service().process_pending_exports().await {
        Ok(exports) => {
            results["exports"] = serde_json::json!(exports.len());
        }
        Err(e) => {
            error!("Failed to process pending exports: {}", e);
            if let Some(errors) = results["errors"].as_array_mut() {
                errors.push(serde_json::json!(format!("Export processing: {}", e)));
            }
        }
    }

    (StatusCode::OK, Json(ApiResponse::success(results))).into_response()
}

/// Configure GDPR API routes
pub fn configure_routes(gdpr_service: Arc<GdprService>) -> axum::Router {
    use axum::routing::{delete, get, post};

    axum::Router::new()
        // Deletion endpoints
        .route("/api/gdpr/users/:user_id", delete(delete_user_data))
        .route(
            "/api/gdpr/users/:user_id/deletion-status",
            get(get_deletion_status),
        )
        // Export endpoints
        .route("/api/gdpr/users/:user_id/export", post(export_user_data))
        .route("/api/gdpr/export/:request_id", get(get_export_status))
        .route(
            "/api/gdpr/export/download/:request_id",
            get(download_export),
        )
        // Consent endpoints
        .route("/api/gdpr/consent", post(update_consent))
        .route("/api/gdpr/consent/:user_id", get(get_consent_status))
        .route(
            "/api/gdpr/consent/:user_id/history",
            get(get_consent_history),
        )
        .route(
            "/api/gdpr/consent/:user_id/receipt",
            get(get_consent_receipt),
        )
        .route(
            "/api/gdpr/consent/:user_id/withdraw-all",
            post(withdraw_all_consents),
        )
        // Admin endpoints
        .route("/api/gdpr/health", get(health_check))
        .route("/api/gdpr/process-pending", post(process_pending_requests))
        .with_state(gdpr_service)
}