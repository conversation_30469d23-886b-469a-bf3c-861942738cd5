//! Consent management implementation for GDPR Article 7
//! 
//! This module handles granular consent tracking, version history,
//! withdrawal handling, and integration with access control.

use crate::models::security::SecurityError;
use crate::services::security::gdpr::models::*;
use crate::storage::access_control::rbac::RbacManager;
use crate::storage::audit::{AuditContext, AuditService};
use crate::storage::spanner::SpannerOperations;
use anyhow::{Context, Result};
use chrono::{Duration, Utc};
use google_cloud_spanner::statement::Statement;
use google_cloud_spanner::client::Error as SpannerError;
use serde_json::json;
use std::collections::HashMap;
use std::sync::Arc;
use tracing::{debug, error, info, warn};
use uuid::Uuid;

/// Service for managing user consent under GDPR
pub struct ConsentService {
    spanner: Arc<SpannerOperations>,
    audit: Arc<AuditService>,
    rbac: Arc<RbacManager>,
    config: GdprConfig,
}

impl ConsentService {
    /// Create a new consent service
    pub fn new(
        spanner: Arc<SpannerOperations>,
        audit: Arc<AuditService>,
        rbac: Arc<RbacManager>,
        config: GdprConfig,
    ) -> Self {
        Self {
            spanner,
            audit,
            rbac,
            config,
        }
    }

    /// Update consent for a user
    /// 
    /// # Arguments
    /// * `update` - Consent update request
    /// 
    /// # Returns
    /// * Updated consent record
    pub async fn update_consent(&self, update: ConsentUpdate) -> Result<ConsentRecord> {
        info!(
            "Updating consent for user {} - type: {:?}, granted: {}",
            update.user_id, update.consent_type, update.granted
        );

        // Create consent record
        let record = ConsentRecord {
            consent_id: Uuid::new_v4().to_string(),
            user_id: update.user_id.clone(),
            consent_type: update.consent_type.clone(),
            granted: update.granted,
            timestamp: Utc::now(),
            ip_address: update.ip_address.clone(),
            user_agent: update.user_agent.clone(),
            consent_version: self.get_current_consent_version().await?,
            metadata: None,
        };

        // Store consent record
        self.store_consent_record(&record).await?;

        // Update current consent state
        self.update_consent_state(&record).await?;

        // Handle consent-based access control
        self.update_access_control(&record).await?;

        // Log audit event
        let event_type = if update.granted {
            "gdpr_consent_granted"
        } else {
            "gdpr_consent_withdrawn"
        };

        let context = AuditContext {
            user_id: Some(update.user_id.clone()),
            ip_address: update.ip_address.clone(),
            user_agent: update.user_agent.clone(),
            session_id: None,
            request_id: Some(record.consent_id.clone()),
        };

        self.audit
            .log_operation_with_metadata(
                event_type,
                "consent",
                &record.consent_id,
                crate::models::security::AuditResult::Success,
                context,
                Some(HashMap::from([
                    ("consent_type".to_string(), json!(update.consent_type)),
                    ("granted".to_string(), json!(update.granted)),
                    ("version".to_string(), json!(record.consent_version)),
                ])),
            )
            .await?;

        info!("Consent updated successfully: {}", record.consent_id);
        Ok(record)
    }

    /// Get current consent status for a user
    /// 
    /// # Arguments
    /// * `user_id` - User ID to get consent status for
    /// 
    /// # Returns
    /// * Current consent status
    pub async fn get_consent_status(&self, user_id: &str) -> Result<ConsentStatus> {
        debug!("Getting consent status for user: {}", user_id);

        // Get all consent types and their current states
        let stmt = Statement::new(
            "SELECT consent_type, granted, updated_at, version, expires_at
             FROM gdpr_consent_states
             WHERE user_id = @user_id"
        );
        let mut stmt = stmt;
        stmt.add_param("user_id", &user_id.to_string());

        let mut transaction = self.spanner.client.read_only_transaction()
            .await?;
        let mut result_set = transaction.query(stmt).await?;

        let mut consents = HashMap::new();
        let mut last_updated = Utc::now() - Duration::days(365 * 10); // Far past

        while let Some(row) = result_set.next().await? {
            let consent_type_str: String = row.column_by_name("consent_type")?;
            let updated_at_str: String = row.column_by_name("updated_at")?;
            let updated_at = chrono::DateTime::parse_from_rfc3339(&updated_at_str)
                .context("Failed to parse updated_at timestamp")?
                .with_timezone(&Utc);

            if updated_at > last_updated {
                last_updated = updated_at;
            }

            let consent_type = match consent_type_str.as_str() {
                "DataProcessing" => ConsentType::DataProcessing,
                "Analytics" => ConsentType::Analytics,
                "Marketing" => ConsentType::Marketing,
                "DataSharing" => ConsentType::DataSharing,
                "AutomatedDecisionMaking" => ConsentType::AutomatedDecisionMaking,
                custom => ConsentType::Custom(custom.to_string()),
            };

            let state = ConsentState {
                granted: row.column_by_name("granted")?,
                updated_at,
                version: row.column_by_name("version")?,
                expires_at: {
                    let expires_at_str: Option<String> = row.column_by_name("expires_at")?;
                    expires_at_str.map(|ts| chrono::DateTime::parse_from_rfc3339(&ts)
                        .context("Failed to parse expires_at timestamp")
                        .map(|dt| dt.with_timezone(&Utc)))
                        .transpose()?
                },
            };

            consents.insert(consent_type, state);
        }

        // Apply default consents for any missing types
        for (consent_type, default_granted) in &self.config.privacy_settings.default_consents {
            if !consents.contains_key(consent_type) {
                consents.insert(
                    consent_type.clone(),
                    ConsentState {
                        granted: *default_granted,
                        updated_at: Utc::now(),
                        version: self.get_current_consent_version().await?,
                        expires_at: None,
                    },
                );
            }
        }

        Ok(ConsentStatus {
            user_id: user_id.to_string(),
            consents,
            last_updated,
        })
    }

    /// Get consent history for a user
    /// 
    /// # Arguments
    /// * `user_id` - User ID
    /// * `consent_type` - Optional filter by consent type
    /// * `limit` - Maximum number of records to return
    /// 
    /// # Returns
    /// * List of consent records
    pub async fn get_consent_history(
        &self,
        user_id: &str,
        consent_type: Option<ConsentType>,
        limit: Option<usize>,
    ) -> Result<Vec<ConsentRecord>> {
        debug!("Getting consent history for user: {}", user_id);

        let mut stmt = if let Some(ref ct) = consent_type {
            let s = Statement::new(
                "SELECT * FROM gdpr_consent_records
                 WHERE user_id = @user_id
                 AND consent_type = @consent_type
                 ORDER BY timestamp DESC
                 LIMIT @limit"
            );
            let mut s = s;
            s.add_param("user_id", &user_id.to_string());
            s.add_param("consent_type", &format!("{:?}", ct));
            s.add_param("limit", &(limit.unwrap_or(100) as i64));
            s
        } else {
            let s = Statement::new(
                "SELECT * FROM gdpr_consent_records
                 WHERE user_id = @user_id
                 ORDER BY timestamp DESC
                 LIMIT @limit"
            );
            let mut s = s;
            s.add_param("user_id", &user_id.to_string());
            s.add_param("limit", &(limit.unwrap_or(100) as i64));
            s
        };

        let mut transaction = self.spanner.client.read_only_transaction()
            .await?;
        let mut result_set = transaction.query(stmt).await?;

        let mut records = Vec::new();
        while let Some(row) = result_set.next().await? {
            let consent_type_str: String = row.column_by_name("consent_type")?;
            let metadata_json: Option<String> = row.column_by_name("metadata")?;

            records.push(ConsentRecord {
                consent_id: row.column_by_name("consent_id")?,
                user_id: row.column_by_name("user_id")?,
                consent_type: match consent_type_str.as_str() {
                    "DataProcessing" => ConsentType::DataProcessing,
                    "Analytics" => ConsentType::Analytics,
                    "Marketing" => ConsentType::Marketing,
                    "DataSharing" => ConsentType::DataSharing,
                    "AutomatedDecisionMaking" => ConsentType::AutomatedDecisionMaking,
                    custom => ConsentType::Custom(custom.to_string()),
                },
                granted: row.column_by_name("granted")?,
                timestamp: {
                    let timestamp_str: String = row.column_by_name("timestamp")?;
                    chrono::DateTime::parse_from_rfc3339(&timestamp_str)
                        .context("Failed to parse timestamp")?
                        .with_timezone(&Utc)
                },
                ip_address: row.column_by_name("ip_address")?,
                user_agent: row.column_by_name("user_agent")?,
                consent_version: row.column_by_name("consent_version")?,
                metadata: metadata_json.and_then(|s| serde_json::from_str(&s).ok()),
            });
        }

        Ok(records)
    }

    /// Withdraw all consents for a user
    /// 
    /// # Arguments
    /// * `user_id` - User ID
    /// * `ip_address` - IP address of the request
    /// * `user_agent` - User agent string
    /// 
    /// # Returns
    /// * List of withdrawn consent records
    pub async fn withdraw_all_consents(
        &self,
        user_id: &str,
        ip_address: Option<String>,
        user_agent: Option<String>,
    ) -> Result<Vec<ConsentRecord>> {
        info!("Withdrawing all consents for user: {}", user_id);

        let current_status = self.get_consent_status(user_id).await?;
        let mut withdrawn_records = Vec::new();

        for (consent_type, state) in current_status.consents {
            if state.granted {
                let update = ConsentUpdate {
                    user_id: user_id.to_string(),
                    consent_type: consent_type.clone(),
                    granted: false,
                    ip_address: ip_address.clone(),
                    user_agent: user_agent.clone(),
                };

                let record = self.update_consent(update).await?;
                withdrawn_records.push(record);
            }
        }

        info!(
            "Withdrew {} consents for user: {}",
            withdrawn_records.len(),
            user_id
        );
        Ok(withdrawn_records)
    }

    /// Check if user has given consent for a specific type
    /// 
    /// # Arguments
    /// * `user_id` - User ID
    /// * `consent_type` - Type of consent to check
    /// 
    /// # Returns
    /// * Whether consent is granted
    pub async fn has_consent(&self, user_id: &str, consent_type: &ConsentType) -> Result<bool> {
        let status = self.get_consent_status(user_id).await?;
        
        Ok(status
            .consents
            .get(consent_type)
            .map(|state| state.granted)
            .unwrap_or_else(|| {
                // Use default if not found
                self.config
                    .privacy_settings
                    .default_consents
                    .get(consent_type)
                    .copied()
                    .unwrap_or(false)
            }))
    }

    /// Store consent record in database
    async fn store_consent_record(&self, record: &ConsentRecord) -> Result<()> {
        let record = record.clone();
        // Serialize metadata outside the transaction to handle errors properly
        let metadata_json = serde_json::to_string(&record.metadata)?;
        
        let (_, _) = self.spanner.client
            .read_write_transaction(|tx| {
                let record = record.clone();
                let metadata_json = metadata_json.clone();
                Box::pin(async move {
                    let stmt = Statement::new(
                        "INSERT INTO gdpr_consent_records 
                         (consent_id, user_id, consent_type, granted, timestamp, ip_address, user_agent, consent_version, metadata)
                         VALUES (@consent_id, @user_id, @consent_type, @granted, @timestamp, @ip_address, @user_agent, @consent_version, @metadata)"
                    );
                    let mut stmt = stmt;
                    stmt.add_param("consent_id", &record.consent_id);
                    stmt.add_param("user_id", &record.user_id);
                    stmt.add_param("consent_type", &format!("{:?}", record.consent_type));
                    stmt.add_param("granted", &record.granted);
                    stmt.add_param("timestamp", &record.timestamp.to_rfc3339());
                    stmt.add_param("ip_address", &record.ip_address);
                    stmt.add_param("user_agent", &record.user_agent);
                    stmt.add_param("consent_version", &record.consent_version);
                    stmt.add_param("metadata", &metadata_json);

                    tx.update(stmt).await?;
                    Ok::<(), SpannerError>(())
                })
            })
            .await?;

        Ok(())
    }

    /// Update current consent state
    async fn update_consent_state(&self, record: &ConsentRecord) -> Result<()> {
        let record = record.clone();
        let (_, _) = self.spanner.client
            .read_write_transaction(|tx| {
                let record = record.clone();
                Box::pin(async move {
                    // Check if state exists
                    let check_stmt = Statement::new(
                        "SELECT 1 FROM gdpr_consent_states 
                         WHERE user_id = @user_id AND consent_type = @consent_type"
                    );
                    let mut check_stmt = check_stmt;
                    check_stmt.add_param("user_id", &record.user_id);
                    check_stmt.add_param("consent_type", &format!("{:?}", record.consent_type));

                    let mut result_set = tx.query(check_stmt).await?;
                    let exists = result_set.next().await?.is_some();

                    let stmt = if exists {
                        // Update existing state
                        let s = Statement::new(
                            "UPDATE gdpr_consent_states 
                             SET granted = @granted,
                                 updated_at = @updated_at,
                                 version = @version
                             WHERE user_id = @user_id AND consent_type = @consent_type"
                        );
                        let mut s = s;
                        s.add_param("granted", &record.granted);
                        s.add_param("updated_at", &record.timestamp.to_rfc3339());
                        s.add_param("version", &record.consent_version);
                        s.add_param("user_id", &record.user_id);
                        s.add_param("consent_type", &format!("{:?}", record.consent_type));
                        s
                    } else {
                        // Insert new state
                        let s = Statement::new(
                            "INSERT INTO gdpr_consent_states 
                             (user_id, consent_type, granted, updated_at, version)
                             VALUES (@user_id, @consent_type, @granted, @updated_at, @version)"
                        );
                        let mut s = s;
                        s.add_param("user_id", &record.user_id);
                        s.add_param("consent_type", &format!("{:?}", record.consent_type));
                        s.add_param("granted", &record.granted);
                        s.add_param("updated_at", &record.timestamp.to_rfc3339());
                        s.add_param("version", &record.consent_version);
                        s
                    };

                    tx.update(stmt).await?;
                    Ok::<(), SpannerError>(())
                })
            })
            .await?;

        Ok(())
    }

    /// Update access control based on consent
    async fn update_access_control(&self, record: &ConsentRecord) -> Result<()> {
        // Map consent types to roles
        let role_mapping = match &record.consent_type {
            ConsentType::DataProcessing => Some("data_processor"),
            ConsentType::Analytics => Some("analytics_viewer"),
            ConsentType::Marketing => Some("marketing_recipient"),
            _ => None,
        };

        if let Some(role) = role_mapping {
            if record.granted {
                // Grant role
                if let Err(e) = self.rbac.assign_role(&record.user_id, role).await {
                    warn!(
                        "Failed to assign role {} to user {}: {}",
                        role, record.user_id, e
                    );
                }
            } else {
                // Revoke role
                if let Err(e) = self.rbac.remove_role(&record.user_id, role).await {
                    // It's okay if the role wasn't assigned
                    debug!(
                        "Failed to remove role {} from user {}: {}",
                        role, record.user_id, e
                    );
                }
            }
        }

        Ok(())
    }

    /// Get current consent version
    async fn get_current_consent_version(&self) -> Result<String> {
        // In production, this would retrieve the current version from configuration
        Ok("1.0".to_string())
    }

    /// Generate consent receipt for a user
    /// 
    /// # Arguments
    /// * `user_id` - User ID
    /// 
    /// # Returns
    /// * Consent receipt in ISO/IEC 29184 format
    pub async fn generate_consent_receipt(&self, user_id: &str) -> Result<serde_json::Value> {
        let status = self.get_consent_status(user_id).await?;
        let history = self.get_consent_history(user_id, None, Some(50)).await?;

        let receipt = json!({
            "version": "1.0",
            "jurisdiction": "EU",
            "consentTimestamp": Utc::now().timestamp(),
            "collectionMethod": "WEB_FORM",
            "consentReceiptID": Uuid::new_v4().to_string(),
            "publicKey": null, // Would include public key in production
            "subject": {
                "userID": user_id,
            },
            "dataController": {
                "onBehalf": false,
                "contact": {
                    "email": "<EMAIL>",
                    "phone": null,
                    "address": null,
                },
                "organization": "Episteme AI",
                "controller": {
                    "onBehalf": false,
                    "contact": {
                        "email": "<EMAIL>",
                    },
                    "organization": "Episteme AI",
                },
            },
            "services": self.generate_service_descriptions(&status),
            "sensitive": false,
            "spiCat": [], // Sensitive personal information categories
            "consentHistory": self.format_consent_history(&history),
        });

        Ok(receipt)
    }

    /// Generate service descriptions for consent receipt
    fn generate_service_descriptions(&self, status: &ConsentStatus) -> Vec<serde_json::Value> {
        let mut services = Vec::new();

        for (consent_type, state) in &status.consents {
            let service = match consent_type {
                ConsentType::DataProcessing => json!({
                    "service": "Code Analysis",
                    "purposes": [{
                        "purpose": "Analyze code repositories",
                        "purposeCategory": ["1", "2"], // Service provision, performance
                        "consentType": "EXPLICIT",
                        "termination": "USER_REQUEST",
                        "thirdPartyDisclosure": false,
                    }],
                }),
                ConsentType::Analytics => json!({
                    "service": "Usage Analytics",
                    "purposes": [{
                        "purpose": "Improve service quality",
                        "purposeCategory": ["4"], // Analytics
                        "consentType": "EXPLICIT",
                        "termination": "USER_REQUEST",
                        "thirdPartyDisclosure": false,
                    }],
                }),
                ConsentType::Marketing => json!({
                    "service": "Marketing Communications",
                    "purposes": [{
                        "purpose": "Send product updates and offers",
                        "purposeCategory": ["5"], // Marketing
                        "consentType": "EXPLICIT",
                        "termination": "USER_REQUEST",
                        "thirdPartyDisclosure": false,
                    }],
                }),
                _ => continue,
            };

            services.push(service);
        }

        services
    }

    /// Format consent history for receipt
    fn format_consent_history(&self, history: &[ConsentRecord]) -> Vec<serde_json::Value> {
        history
            .iter()
            .take(10) // Last 10 changes
            .map(|record| {
                json!({
                    "timestamp": record.timestamp.timestamp(),
                    "consentType": format!("{:?}", record.consent_type),
                    "granted": record.granted,
                    "version": record.consent_version,
                })
            })
            .collect()
    }

    /// Verify consent requirements for an operation
    /// 
    /// # Arguments
    /// * `user_id` - User ID
    /// * `required_consents` - List of required consent types
    /// 
    /// # Returns
    /// * Whether all required consents are granted
    pub async fn verify_consents(
        &self,
        user_id: &str,
        required_consents: &[ConsentType],
    ) -> Result<bool> {
        for consent_type in required_consents {
            if !self.has_consent(user_id, consent_type).await? {
                return Ok(false);
            }
        }
        Ok(true)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_consent_type_formatting() {
        let ct = ConsentType::DataProcessing;
        assert_eq!(format!("{:?}", ct), "DataProcessing");

        let ct = ConsentType::Custom("TestConsent".to_string());
        assert_eq!(format!("{:?}", ct), "Custom(\"TestConsent\")");
    }

    #[tokio::test]
    async fn test_consent_receipt_generation() {
        // Test receipt JSON structure
        let receipt = json!({
            "version": "1.0",
            "jurisdiction": "EU",
            "consentTimestamp": 1234567890,
            "services": [],
        });

        assert_eq!(receipt["version"], "1.0");
        assert_eq!(receipt["jurisdiction"], "EU");
    }
}