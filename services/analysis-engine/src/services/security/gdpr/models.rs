//! GDPR-specific models and data structures
//! 
//! This module defines the data structures used for GDPR compliance including
//! deletion requests, export formats, consent management, and audit records.

use anyhow::Result;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// Request to delete user data under GDPR Article 17 (Right to Erasure)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeletionRequest {
    /// Unique identifier for the deletion request
    pub request_id: String,
    /// User ID whose data should be deleted
    pub user_id: String,
    /// Reason for deletion (e.g., "user request", "data retention policy")
    pub reason: String,
    /// Timestamp when the request was created
    pub requested_at: DateTime<Utc>,
    /// Deadline for completion (typically 30 days from request)
    pub deadline: DateTime<Utc>,
    /// Current status of the deletion request
    pub status: DeletionStatus,
    /// Specific data categories to delete (None means all data)
    pub scope: Option<DeletionScope>,
    /// Timestamp when deletion was completed
    pub completed_at: Option<DateTime<Utc>>,
    /// Any errors encountered during deletion
    pub errors: Vec<String>,
}

/// Status of a deletion request
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum DeletionStatus {
    /// Request received and queued
    Pending,
    /// Deletion in progress
    Processing,
    /// Deletion completed successfully
    Completed,
    /// Deletion failed with errors
    Failed,
    /// Request cancelled by user or admin
    Cancelled,
}

/// Scope of data deletion
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeletionScope {
    /// Delete analysis requests
    pub analysis_requests: bool,
    /// Delete analysis results
    pub analysis_results: bool,
    /// Delete user profile data
    pub user_data: bool,
    /// Specific analysis IDs to delete (None means all)
    pub specific_analyses: Option<Vec<String>>,
}

/// Report of completed deletion
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeletionReport {
    /// Request ID this report belongs to
    pub request_id: String,
    /// User ID whose data was deleted
    pub user_id: String,
    /// Timestamp of completion
    pub completed_at: DateTime<Utc>,
    /// Summary of deleted data
    pub deleted_items: DeletionSummary,
    /// Cryptographic proof of deletion
    pub deletion_certificate: DeletionCertificate,
}

/// Summary of deleted items
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeletionSummary {
    /// Number of analysis requests deleted
    pub analysis_requests_count: u64,
    /// Number of analysis results deleted
    pub analysis_results_count: u64,
    /// Number of user data records deleted
    pub user_data_count: u64,
    /// Number of related records deleted (cascaded)
    pub cascaded_deletions: u64,
    /// Total size of data deleted (in bytes)
    pub total_size_bytes: u64,
}

/// Cryptographic certificate proving deletion
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeletionCertificate {
    /// SHA-256 hash of deletion details
    pub content_hash: String,
    /// Digital signature of the hash
    pub signature: String,
    /// Timestamp of certificate generation
    pub issued_at: DateTime<Utc>,
    /// Certificate validity period
    pub valid_until: DateTime<Utc>,
}

/// Request for data export under GDPR Article 20 (Data Portability)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExportRequest {
    /// Unique identifier for the export request
    pub request_id: String,
    /// User ID whose data should be exported
    pub user_id: String,
    /// Requested export format
    pub format: ExportFormat,
    /// Whether to include encrypted fields (requires additional auth)
    pub include_encrypted: bool,
    /// Timestamp when the request was created
    pub requested_at: DateTime<Utc>,
    /// Current status of the export
    pub status: ExportStatus,
    /// URL where the export can be downloaded (when ready)
    pub download_url: Option<String>,
    /// Expiration time for the download URL
    pub expires_at: Option<DateTime<Utc>>,
}

/// Supported export formats
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ExportFormat {
    /// JSON format (default)
    Json,
    /// CSV format (tabular data only)
    Csv,
    /// Both JSON and CSV in a ZIP archive
    Combined,
}

/// Status of an export request
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ExportStatus {
    /// Request received and queued
    Pending,
    /// Export generation in progress
    Processing,
    /// Export ready for download
    Ready,
    /// Export failed
    Failed,
    /// Export downloaded and expired
    Expired,
}

/// Data export package
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DataExport {
    /// Export metadata
    pub metadata: ExportMetadata,
    /// User profile data
    pub user_data: Option<serde_json::Value>,
    /// Analysis requests
    pub analysis_requests: Vec<serde_json::Value>,
    /// Analysis results
    pub analysis_results: Vec<serde_json::Value>,
    /// Consent history
    pub consent_history: Vec<ConsentRecord>,
}

/// Export metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExportMetadata {
    /// Export request ID
    pub request_id: String,
    /// User ID
    pub user_id: String,
    /// Export generation timestamp
    pub generated_at: DateTime<Utc>,
    /// Data coverage period
    pub data_from: DateTime<Utc>,
    pub data_to: DateTime<Utc>,
    /// Export format version
    pub format_version: String,
    /// Total records exported
    pub total_records: u64,
}

/// User consent record for GDPR Article 7
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConsentRecord {
    /// Unique consent record ID
    pub consent_id: String,
    /// User ID
    pub user_id: String,
    /// Type of consent
    pub consent_type: ConsentType,
    /// Whether consent is granted
    pub granted: bool,
    /// Timestamp of consent action
    pub timestamp: DateTime<Utc>,
    /// IP address where consent was given
    pub ip_address: Option<String>,
    /// User agent string
    pub user_agent: Option<String>,
    /// Version of consent text shown to user
    pub consent_version: String,
    /// Additional metadata
    pub metadata: Option<HashMap<String, serde_json::Value>>,
}

/// Types of consent that can be managed
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub enum ConsentType {
    /// Consent for data processing
    DataProcessing,
    /// Consent for analytics
    Analytics,
    /// Consent for marketing communications
    Marketing,
    /// Consent for data sharing with third parties
    DataSharing,
    /// Consent for automated decision making
    AutomatedDecisionMaking,
    /// Custom consent type
    Custom(String),
}

/// Current consent status for a user
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConsentStatus {
    /// User ID
    pub user_id: String,
    /// Map of consent types to current status
    pub consents: HashMap<ConsentType, ConsentState>,
    /// Last update timestamp
    pub last_updated: DateTime<Utc>,
}

/// State of a specific consent
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConsentState {
    /// Whether consent is currently granted
    pub granted: bool,
    /// When this consent was last updated
    pub updated_at: DateTime<Utc>,
    /// Version of consent text
    pub version: String,
    /// When consent expires (if applicable)
    pub expires_at: Option<DateTime<Utc>>,
}

/// Request to update consent
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConsentUpdate {
    /// User ID
    pub user_id: String,
    /// Type of consent to update
    pub consent_type: ConsentType,
    /// New consent state (true = granted, false = withdrawn)
    pub granted: bool,
    /// IP address of the request
    pub ip_address: Option<String>,
    /// User agent string
    pub user_agent: Option<String>,
}

/// GDPR audit event
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GdprAuditEvent {
    /// Unique event ID
    pub event_id: String,
    /// Type of GDPR operation
    pub event_type: GdprEventType,
    /// User ID involved
    pub user_id: String,
    /// Operator who initiated the action
    pub operator_id: Option<String>,
    /// Timestamp of the event
    pub timestamp: DateTime<Utc>,
    /// IP address
    pub ip_address: Option<String>,
    /// Success or failure
    pub success: bool,
    /// Additional event details
    pub details: HashMap<String, serde_json::Value>,
}

/// Types of GDPR events for audit logging
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum GdprEventType {
    /// Data deletion requested
    DeletionRequested,
    /// Data deletion completed
    DeletionCompleted,
    /// Data export requested
    ExportRequested,
    /// Data export completed
    ExportCompleted,
    /// Data export downloaded
    ExportDownloaded,
    /// Consent updated
    ConsentUpdated,
    /// Consent withdrawn
    ConsentWithdrawn,
    /// Data access request
    DataAccessRequest,
    /// Data rectification request
    DataRectificationRequest,
}

/// Privacy settings with sensible defaults
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PrivacySettings {
    /// Default consent states for new users
    pub default_consents: HashMap<ConsentType, bool>,
    /// Data retention period in days
    pub retention_period_days: u32,
    /// Whether to anonymize data after retention period
    pub anonymize_after_retention: bool,
    /// Require explicit consent for processing
    pub require_explicit_consent: bool,
    /// Enable privacy-preserving analytics
    pub privacy_preserving_analytics: bool,
}

impl Default for PrivacySettings {
    fn default() -> Self {
        let mut default_consents = HashMap::new();
        // Privacy by default - all consents start as false
        default_consents.insert(ConsentType::DataProcessing, false);
        default_consents.insert(ConsentType::Analytics, false);
        default_consents.insert(ConsentType::Marketing, false);
        default_consents.insert(ConsentType::DataSharing, false);
        default_consents.insert(ConsentType::AutomatedDecisionMaking, false);

        Self {
            default_consents,
            retention_period_days: 365 * 2, // 2 years default
            anonymize_after_retention: true,
            require_explicit_consent: true,
            privacy_preserving_analytics: true,
        }
    }
}

/// Configuration for GDPR service
#[derive(Debug, Clone)]
pub struct GdprConfig {
    /// Privacy settings
    pub privacy_settings: PrivacySettings,
    /// Deletion deadline in days (default: 30)
    pub deletion_deadline_days: u32,
    /// Export expiration in hours (default: 48)
    pub export_expiration_hours: u32,
    /// Enable automatic anonymization
    pub enable_auto_anonymization: bool,
    /// Batch size for bulk operations
    pub batch_size: usize,
}

impl Default for GdprConfig {
    fn default() -> Self {
        Self {
            privacy_settings: PrivacySettings::default(),
            deletion_deadline_days: 30,
            export_expiration_hours: 48,
            enable_auto_anonymization: true,
            batch_size: 100,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_privacy_settings_default() {
        let settings = PrivacySettings::default();
        
        // All consents should be false by default (privacy by design)
        assert_eq!(settings.default_consents.get(&ConsentType::DataProcessing), Some(&false));
        assert_eq!(settings.default_consents.get(&ConsentType::Analytics), Some(&false));
        assert_eq!(settings.default_consents.get(&ConsentType::Marketing), Some(&false));
        
        // Other defaults
        assert_eq!(settings.retention_period_days, 365 * 2);
        assert!(settings.anonymize_after_retention);
        assert!(settings.require_explicit_consent);
        assert!(settings.privacy_preserving_analytics);
    }

    #[test]
    fn test_gdpr_config_default() {
        let config = GdprConfig::default();
        
        assert_eq!(config.deletion_deadline_days, 30);
        assert_eq!(config.export_expiration_hours, 48);
        assert!(config.enable_auto_anonymization);
        assert_eq!(config.batch_size, 100);
    }

    #[test]
    fn test_consent_type_equality() {
        assert_eq!(ConsentType::DataProcessing, ConsentType::DataProcessing);
        assert_ne!(ConsentType::DataProcessing, ConsentType::Analytics);
        assert_eq!(
            ConsentType::Custom("test".to_string()),
            ConsentType::Custom("test".to_string())
        );
    }
}