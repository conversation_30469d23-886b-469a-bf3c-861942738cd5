//! GDPR compliance module for data protection and privacy
//! 
//! This module implements comprehensive GDPR compliance features including:
//! - Right to Erasure (Article 17) - Complete data deletion with cascades
//! - Data Portability (Article 20) - Export user data in machine-readable formats
//! - Consent Management (Article 7) - Granular consent tracking and withdrawal
//! - Privacy by Design (Article 25) - Default privacy settings and encryption
//! 
//! # Architecture
//! 
//! The module is organized into several components:
//! - `deletion` - Handles data deletion requests with full cascade support
//! - `export` - Manages data export in JSON/CSV formats with compression
//! - `consent` - Tracks and manages user consent with version history
//! - `models` - GDPR-specific data structures and types
//! - `api` - REST API endpoints for all GDPR operations
//! 
//! # Usage
//! 
//! ```rust
//! use episteme::services::security::gdpr::{GdprService, GdprConfig};
//! 
//! // Create GDPR service
//! let config = GdprConfig::default();
//! let gdpr_service = GdprService::new(spanner, encryption, audit, rbac, config).await?;
//! 
//! // Handle deletion request
//! let deletion_request = gdpr_service
//!     .deletion_service()
//!     .initiate_deletion(user_id, "User requested deletion", None)
//!     .await?;
//! 
//! // Export user data
//! let export_request = gdpr_service
//!     .export_service()
//!     .initiate_export(user_id, ExportFormat::Json, true)
//!     .await?;
//! 
//! // Update consent
//! let consent_update = ConsentUpdate {
//!     user_id: user_id.to_string(),
//!     consent_type: ConsentType::Analytics,
//!     granted: false,
//!     ip_address: Some(ip),
//!     user_agent: Some(agent),
//! };
//! gdpr_service.consent_service().update_consent(consent_update).await?;
//! ```

pub mod api;
pub mod consent;
pub mod deletion;
pub mod export;
pub mod models;

// Re-export main types
pub use consent::ConsentService;
pub use deletion::DeletionService;
pub use export::ExportService;
pub use models::*;  // This already includes ConsentUpdate

use crate::models::security::SecurityError;
use crate::storage::access_control::rbac::RbacManager;
use crate::storage::audit::AuditService;
use crate::storage::encryption::EncryptionService;
use crate::storage::spanner::SpannerOperations;
use anyhow::Result;
use std::sync::Arc;
use tracing::{debug, info};

/// Main GDPR service coordinator
pub struct GdprService {
    deletion_service: Arc<DeletionService>,
    export_service: Arc<ExportService>,
    consent_service: Arc<ConsentService>,
}

impl GdprService {
    /// Create a new GDPR service
    /// 
    /// # Arguments
    /// * `spanner` - Spanner database operations
    /// * `encryption` - Encryption service for handling encrypted fields
    /// * `audit` - Audit logging service
    /// * `rbac` - Role-based access control manager
    /// * `config` - GDPR configuration
    /// 
    /// # Returns
    /// * New GDPR service instance
    pub async fn new(
        spanner: Arc<SpannerOperations>,
        encryption: Arc<dyn EncryptionService + Send + Sync>,
        audit: Arc<AuditService>,
        rbac: Arc<RbacManager>,
        config: GdprConfig,
    ) -> Result<Self, SecurityError> {
        info!("Initializing GDPR compliance service");

        // Create sub-services
        let deletion_service = Arc::new(DeletionService::new(
            spanner.clone(),
            encryption.clone(),
            audit.clone(),
            config.clone(),
        ));

        let export_service = Arc::new(ExportService::new(
            spanner.clone(),
            encryption.clone(),
            audit.clone(),
            config.clone(),
        ));

        let consent_service = Arc::new(ConsentService::new(
            spanner.clone(),
            audit.clone(),
            rbac.clone(),
            config.clone(),
        ));

        let service = Self {
            deletion_service,
            export_service,
            consent_service,
        };

        info!("GDPR compliance service initialized successfully");
        Ok(service)
    }

    /// Get deletion service
    pub fn deletion_service(&self) -> &Arc<DeletionService> {
        &self.deletion_service
    }

    /// Get export service
    pub fn export_service(&self) -> &Arc<ExportService> {
        &self.export_service
    }

    /// Get consent service
    pub fn consent_service(&self) -> &Arc<ConsentService> {
        &self.consent_service
    }

    /// Check if a user has the right to access their data
    /// 
    /// # Arguments
    /// * `user_id` - User requesting access
    /// * `target_user_id` - User whose data is being accessed
    /// 
    /// # Returns
    /// * Whether access is allowed
    pub async fn verify_data_access(
        &self,
        user_id: &str,
        target_user_id: &str,
    ) -> Result<bool> {
        // Users can always access their own data
        if user_id == target_user_id {
            return Ok(true);
        }

        // Check if user has admin privileges or specific data access role
        // This would integrate with RBAC to check permissions
        
        Ok(false)
    }

    /// Verify GDPR compliance status for a user
    /// 
    /// # Arguments
    /// * `user_id` - User ID to check
    /// 
    /// # Returns
    /// * Compliance status information
    pub async fn get_compliance_status(&self, user_id: &str) -> Result<ComplianceStatus> {
        debug!("Checking GDPR compliance status for user: {}", user_id);

        // Check consent status
        let consent_status = self.consent_service.get_consent_status(user_id).await?;
        
        // Check for pending deletion requests
        let deletion_requests = self.deletion_service.get_deletion_status(user_id).await?;
        let has_pending_deletion = deletion_requests
            .iter()
            .any(|req| matches!(req.status, DeletionStatus::Pending | DeletionStatus::Processing));

        // Check data retention compliance
        let is_compliant = !has_pending_deletion && 
            consent_status.consents.get(&ConsentType::DataProcessing)
                .map(|state| state.granted)
                .unwrap_or(false);

        Ok(ComplianceStatus {
            user_id: user_id.to_string(),
            is_compliant,
            has_pending_deletion,
            consent_status: Some(consent_status),
            last_privacy_review: chrono::Utc::now(),
        })
    }

    /// Perform health check on all GDPR services
    pub async fn health_check(&self) -> Result<()> {
        debug!("Performing GDPR service health check");
        
        // For now, just verify services are accessible
        // In production, would check database connectivity, etc.
        
        info!("GDPR service health check passed");
        Ok(())
    }

    /// Create database tables for GDPR compliance
    /// 
    /// This should be run during initial setup to create necessary tables
    pub async fn create_tables(spanner: &SpannerOperations) -> Result<()> {
        info!("Creating GDPR compliance tables");

        // Create deletion requests table
        let deletion_table = r#"
            CREATE TABLE IF NOT EXISTS gdpr_deletion_requests (
                request_id STRING(64) NOT NULL,
                user_id STRING(64) NOT NULL,
                reason STRING(1024) NOT NULL,
                requested_at TIMESTAMP NOT NULL,
                deadline TIMESTAMP NOT NULL,
                status STRING(32) NOT NULL,
                scope JSON,
                completed_at TIMESTAMP,
                errors JSON,
                PRIMARY KEY (request_id),
                INDEX idx_user_deletion (user_id, requested_at DESC),
                INDEX idx_deletion_status (status, requested_at)
            )
        "#;

        // Create export requests table
        let export_table = r#"
            CREATE TABLE IF NOT EXISTS gdpr_export_requests (
                request_id STRING(64) NOT NULL,
                user_id STRING(64) NOT NULL,
                format STRING(32) NOT NULL,
                include_encrypted BOOL NOT NULL,
                requested_at TIMESTAMP NOT NULL,
                status STRING(32) NOT NULL,
                download_url STRING(1024),
                expires_at TIMESTAMP,
                PRIMARY KEY (request_id),
                INDEX idx_user_export (user_id, requested_at DESC),
                INDEX idx_export_status (status, requested_at)
            )
        "#;

        // Create consent records table
        let consent_records_table = r#"
            CREATE TABLE IF NOT EXISTS gdpr_consent_records (
                consent_id STRING(64) NOT NULL,
                user_id STRING(64) NOT NULL,
                consent_type STRING(64) NOT NULL,
                granted BOOL NOT NULL,
                timestamp TIMESTAMP NOT NULL,
                ip_address STRING(64),
                user_agent STRING(1024),
                consent_version STRING(32) NOT NULL,
                metadata JSON,
                PRIMARY KEY (consent_id),
                INDEX idx_user_consent (user_id, timestamp DESC),
                INDEX idx_consent_type (consent_type, timestamp DESC)
            )
        "#;

        // Create consent states table (current state per user/type)
        let consent_states_table = r#"
            CREATE TABLE IF NOT EXISTS gdpr_consent_states (
                user_id STRING(64) NOT NULL,
                consent_type STRING(64) NOT NULL,
                granted BOOL NOT NULL,
                updated_at TIMESTAMP NOT NULL,
                version STRING(32) NOT NULL,
                expires_at TIMESTAMP,
                PRIMARY KEY (user_id, consent_type),
                INDEX idx_consent_granted (granted, updated_at)
            )
        "#;

        // Execute table creation
        // Note: In production, these would be executed as DDL statements
        // For now, we'll log the SQL that should be run

        info!("GDPR tables SQL generated - execute these in Spanner:");
        println!("{}", deletion_table);
        println!("{}", export_table);
        println!("{}", consent_records_table);
        println!("{}", consent_states_table);

        Ok(())
    }
}

/// GDPR compliance status for a user
#[derive(Debug, Clone, serde::Serialize)]
pub struct ComplianceStatus {
    pub user_id: String,
    pub is_compliant: bool,
    pub has_pending_deletion: bool,
    pub consent_status: Option<ConsentStatus>,
    pub last_privacy_review: chrono::DateTime<chrono::Utc>,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_gdpr_config_defaults() {
        let config = GdprConfig::default();
        assert_eq!(config.deletion_deadline_days, 30);
        assert_eq!(config.export_expiration_hours, 48);
        assert!(config.enable_auto_anonymization);
    }

    #[test]
    fn test_privacy_settings_defaults() {
        let settings = PrivacySettings::default();
        
        // All consents should default to false (privacy by design)
        for (_, granted) in settings.default_consents {
            assert!(!granted);
        }
        
        assert!(settings.require_explicit_consent);
        assert!(settings.privacy_preserving_analytics);
    }
}