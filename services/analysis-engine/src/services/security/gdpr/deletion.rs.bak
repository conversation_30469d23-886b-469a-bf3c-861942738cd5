//! Data deletion cascade implementation for GDPR Article 17 (Right to Erasure)
//! 
//! This module handles complete data deletion with cascading through related tables,
//! encryption handling, audit trail generation, and deletion certificates.

use crate::models::security::SecurityError;
use crate::services::security::gdpr::models::*;
use crate::storage::audit::{AuditContext, AuditService};
use crate::storage::encryption::EncryptionService;
use crate::storage::spanner::SpannerOperations;
use anyhow::{Context, Result};
use chrono::{Duration, Utc};
use google_cloud_spanner::client::Error as SpannerError;
use google_cloud_spanner::statement::Statement;
use google_cloud_spanner::transaction::Transaction;
use serde_json::json;
use sha2::{Digest, Sha256};
use std::collections::HashMap;
use std::sync::Arc;
use tracing::{debug, error, info, warn};
use uuid::Uuid;

/// Service for handling GDPR data deletion requests
pub struct DeletionService {
    spanner: Arc<SpannerOperations>,
    encryption: Arc<dyn EncryptionService + Send + Sync>,
    audit: Arc<AuditService>,
    config: GdprConfig,
}

impl DeletionService {
    /// Create a new deletion service
    pub fn new(
        spanner: Arc<SpannerOperations>,
        encryption: Arc<dyn EncryptionService + Send + Sync>,
        audit: Arc<AuditService>,
        config: GdprConfig,
    ) -> Self {
        Self {
            spanner,
            encryption,
            audit,
            config,
        }
    }

    /// Initiate a deletion request for a user
    /// 
    /// # Arguments
    /// * `user_id` - ID of the user requesting deletion
    /// * `reason` - Reason for deletion
    /// * `scope` - Optional scope to limit deletion
    /// 
    /// # Returns
    /// * Deletion request with tracking ID
    pub async fn initiate_deletion(
        &self,
        user_id: &str,
        reason: &str,
        scope: Option<DeletionScope>,
    ) -> Result<DeletionRequest> {
        info!("Initiating GDPR deletion request for user: {}", user_id);

        let request = DeletionRequest {
            request_id: Uuid::new_v4().to_string(),
            user_id: user_id.to_string(),
            reason: reason.to_string(),
            requested_at: Utc::now(),
            deadline: Utc::now() + Duration::days(self.config.deletion_deadline_days as i64),
            status: DeletionStatus::Pending,
            scope: scope.clone(),
            completed_at: None,
            errors: Vec::new(),
        };

        // Store deletion request in database
        self.store_deletion_request(&request).await?;

        // Log audit event
        let context = AuditContext {
            user_id: Some(user_id.to_string()),
            ip_address: None,
            user_agent: None,
            session_id: None,
            request_id: Some(request.request_id.clone()),
        };

        self.audit
            .log_operation_with_metadata(
                "gdpr_deletion_requested",
                "deletion_request",
                &request.request_id,
                crate::models::security::AuditResult::Success,
                context,
                Some(HashMap::from([
                    ("reason".to_string(), json!(reason)),
                    ("has_scope".to_string(), json!(scope.is_some())),
                ])),
            )
            .await?;

        info!("Deletion request created: {}", request.request_id);
        Ok(request)
    }

    /// Process a pending deletion request
    /// 
    /// # Arguments
    /// * `request_id` - ID of the deletion request to process
    /// 
    /// # Returns
    /// * Deletion report with certificate
    pub async fn process_deletion(&self, request_id: &str) -> Result<DeletionReport> {
        info!("Processing deletion request: {}", request_id);

        // Retrieve deletion request
        let mut request = self.get_deletion_request(request_id).await?;

        if request.status != DeletionStatus::Pending {
            return Err(anyhow::anyhow!(
                "Deletion request {} is not in pending status",
                request_id
            ));
        }

        // Update status to processing
        request.status = DeletionStatus::Processing;
        self.update_deletion_request(&request).await?;

        // Perform deletion with transaction
        let deletion_summary = match self.execute_deletion(&request).await {
            Ok(summary) => {
                request.status = DeletionStatus::Completed;
                request.completed_at = Some(Utc::now());
                summary
            }
            Err(e) => {
                error!("Deletion failed for request {}: {}", request_id, e);
                request.status = DeletionStatus::Failed;
                request.errors.push(e.to_string());
                self.update_deletion_request(&request).await?;
                return Err(e);
            }
        };

        // Update request as completed
        self.update_deletion_request(&request).await?;

        // Generate deletion certificate
        let certificate = self.generate_deletion_certificate(&request, &deletion_summary)?;

        let report = DeletionReport {
            request_id: request.request_id.clone(),
            user_id: request.user_id.clone(),
            completed_at: Utc::now(),
            deleted_items: deletion_summary,
            deletion_certificate: certificate,
        };

        // Log successful deletion
        let context = AuditContext {
            user_id: Some(request.user_id.clone()),
            ip_address: None,
            user_agent: None,
            session_id: None,
            request_id: Some(request.request_id.clone()),
        };

        self.audit
            .log_operation_with_metadata(
                "gdpr_deletion_completed",
                "deletion_request",
                &request.request_id,
                crate::models::security::AuditResult::Success,
                context,
                Some(HashMap::from([
                    ("deleted_items".to_string(), json!(report.deleted_items)),
                ])),
            )
            .await?;

        info!("Deletion completed for request: {}", request_id);
        Ok(report)
    }

    /// Execute the actual deletion within a transaction
    async fn execute_deletion(&self, request: &DeletionRequest) -> Result<DeletionSummary> {
        let request = request.clone();
        let encryption = self.encryption.clone();
        
        let (_, summary) = self.spanner.client
            .read_write_transaction(|tx| {
                let request = request.clone();
                let encryption = encryption.clone();
                Box::pin(async move {
                    let mut summary = DeletionSummary {
                        analysis_requests_count: 0,
                        analysis_results_count: 0,
                        user_data_count: 0,
                        cascaded_deletions: 0,
                        total_size_bytes: 0,
                    };

                    // Determine what to delete based on scope
                    let scope = request.scope.as_ref().map_or(
                        DeletionScope {
                            analysis_requests: true,
                            analysis_results: true,
                            user_data: true,
                            specific_analyses: None,
                        },
                        |s| s.clone(),
                    );

                    // Delete analysis results first (child records)
                    if scope.analysis_results {
                        debug!("Deleting analysis results for user: {}", request.user_id);
                        let mut stmt = Statement::new(
                            "DELETE FROM analysis_results 
                             WHERE user_id = @user_id"
                        );
                        stmt.add_param("user_id", &request.user_id);
                        let count = tx.update(stmt).await?;
                        summary.analysis_results_count = count as u64;
                    }

                    // Delete analysis requests
                    if scope.analysis_requests {
                        debug!("Deleting analysis requests for user: {}", request.user_id);
                        let mut stmt = Statement::new(
                            "DELETE FROM analysis_requests 
                             WHERE user_id = @user_id"
                        );
                        stmt.add_param("user_id", &request.user_id);
                        let count = tx.update(stmt).await?;
                        summary.analysis_requests_count = count as u64;
                    }

                    // Delete user data
                    if scope.user_data {
                        debug!("Deleting user data for user: {}", request.user_id);
                        let mut stmt = Statement::new(
                            "DELETE FROM user_data 
                             WHERE user_id = @user_id"
                        );
                        stmt.add_param("user_id", &request.user_id);
                        let count = tx.update(stmt).await?;
                        summary.user_data_count = count as u64;
                    }

                    // Delete consent records (always delete these)
                    debug!("Deleting consent records for user: {}", request.user_id);
                    let mut stmt = Statement::new(
                        "DELETE FROM gdpr_consent_records 
                         WHERE user_id = @user_id"
                    );
                    stmt.add_param("user_id", &request.user_id);
                    let consent_count = tx.update(stmt).await?;
                    summary.cascaded_deletions += consent_count as u64;

                    Ok::<DeletionSummary, SpannerError>(summary)
                })
            })
            .await?;

        Ok(summary)
    }

    // These helper methods are no longer used after refactoring to inline transaction logic
    /*
    /// Delete analysis results for a user
    async fn delete_analysis_results(
        &self,
        transaction: &mut Transaction,
        user_id: &str,
        scope: &DeletionScope,
    ) -> Result<u64> {
        debug!("Deleting analysis results for user: {}", user_id);

        let mut stmt = Statement::new(
            "DELETE FROM analysis_results 
             WHERE user_id = @user_id"
        );
        stmt.add_param("user_id", user_id);

        // Add specific analysis filter if provided
        if let Some(ref analysis_ids) = scope.specific_analyses {
            if !analysis_ids.is_empty() {
                stmt = Statement::new(
                    "DELETE FROM analysis_results 
                     WHERE user_id = @user_id 
                     AND analysis_id IN UNNEST(@analysis_ids)"
                );
                stmt.add_param("user_id", user_id);
                stmt.add_param("analysis_ids", analysis_ids.as_slice());
            }
        }

        let count = transaction.update(&stmt).await?;
        debug!("Deleted {} analysis results", count);
        Ok(count as u64)
    }

    /// Delete analysis requests for a user*/
    async fn delete_analysis_requests(
        &self,
        transaction: &mut Transaction,
        user_id: &str,
        scope: &DeletionScope,
    ) -> Result<u64> {
        debug!("Deleting analysis requests for user: {}", user_id);

        let mut stmt = Statement::new(
            "DELETE FROM analysis_requests 
             WHERE user_id = @user_id"
        );
        stmt.add_param("user_id", user_id);

        // Add specific analysis filter if provided
        if let Some(ref analysis_ids) = scope.specific_analyses {
            if !analysis_ids.is_empty() {
                stmt = Statement::new(
                    "DELETE FROM analysis_requests 
                     WHERE user_id = @user_id 
                     AND id IN UNNEST(@analysis_ids)"
                );
                stmt.add_param("user_id", user_id);
                stmt.add_param("analysis_ids", analysis_ids.as_slice());
            }
        }

        let count = transaction.update(&stmt).await?;
        debug!("Deleted {} analysis requests", count);
        Ok(count as u64)
    }

    /// Delete user profile data
    async fn delete_user_data(
        &self,
        transaction: &mut Transaction,
        user_id: &str,
    ) -> Result<u64> {
        debug!("Deleting user data for user: {}", user_id);

        let stmt = Statement::new(
            "DELETE FROM user_data 
             WHERE user_id = @user_id"
        );
        let mut stmt = stmt;
        stmt.add_param("user_id", user_id);

        let count = transaction.update(&stmt).await?;
        debug!("Deleted {} user data records", count);
        Ok(count as u64)
    }

    /// Delete consent records for a user
    async fn delete_consent_records(
        &self,
        transaction: &mut Transaction,
        user_id: &str,
    ) -> Result<u64> {
        debug!("Deleting consent records for user: {}", user_id);

        let stmt = Statement::new(
            "DELETE FROM gdpr_consent_records 
             WHERE user_id = @user_id"
        );
        let mut stmt = stmt;
        stmt.add_param("user_id", user_id);

        let count = transaction.update(&stmt).await?;
        debug!("Deleted {} consent records", count);
        Ok(count as u64)
    }
    */

    /// Store deletion request in database
    async fn store_deletion_request(&self, request: &DeletionRequest) -> Result<()> {
        let request = request.clone();
        // Serialize JSON fields outside the transaction
        let scope_json = serde_json::to_string(&request.scope)?;
        let errors_json = serde_json::to_string(&request.errors)?;
        
        let (_, _) = self.spanner.client
            .read_write_transaction(|tx| {
                let request = request.clone();
                let scope_json = scope_json.clone();
                let errors_json = errors_json.clone();
                Box::pin(async move {
                    let stmt = Statement::new(
                        "INSERT INTO gdpr_deletion_requests 
                         (request_id, user_id, reason, requested_at, deadline, status, scope, errors)
                         VALUES (@request_id, @user_id, @reason, @requested_at, @deadline, @status, @scope, @errors)"
                    );
                    let mut stmt = stmt;
                    stmt.add_param("request_id", &request.request_id);
                    stmt.add_param("user_id", &request.user_id);
                    stmt.add_param("reason", &request.reason);
                    stmt.add_param("requested_at", &request.requested_at.to_rfc3339());
                    stmt.add_param("deadline", &request.deadline.to_rfc3339());
                    stmt.add_param("status", &request.status.to_string());
                    stmt.add_param("scope", &scope_json);
                    stmt.add_param("errors", &errors_json);

                    tx.update(stmt).await?;
                    Ok::<(), SpannerError>(())
                })
            })
            .await?;

        Ok(())
    }

    /// Update deletion request in database
    async fn update_deletion_request(&self, request: &DeletionRequest) -> Result<()> {
        let request = request.clone();
        // Serialize JSON fields outside the transaction
        let errors_json = serde_json::to_string(&request.errors)?;
        
        let (_, _) = self.spanner.client
            .read_write_transaction(|tx| {
                let request = request.clone();
                let errors_json = errors_json.clone();
                Box::pin(async move {
                    let stmt = Statement::new(
                        "UPDATE gdpr_deletion_requests 
                         SET status = @status, 
                             completed_at = @completed_at,
                             errors = @errors
                         WHERE request_id = @request_id"
                    );
                    let mut stmt = stmt;
                    stmt.add_param("status", &request.status.to_string());
                    stmt.add_param("completed_at", &request.completed_at.map(|dt| dt.to_rfc3339()));
                    stmt.add_param("errors", &errors_json);
                    stmt.add_param("request_id", &request.request_id);

                    tx.update(stmt).await?;
                    Ok::<(), SpannerError>(())
                })
            })
            .await?;

        Ok(())
    }

    /// Get deletion request from database
    async fn get_deletion_request(&self, request_id: &str) -> Result<DeletionRequest> {
        let stmt = Statement::new(
            "SELECT request_id, user_id, reason, requested_at, deadline, status, scope, completed_at, errors
             FROM gdpr_deletion_requests
             WHERE request_id = @request_id"
        );
        let mut stmt = stmt;
        stmt.add_param("request_id", request_id);

        let mut result_set = self.spanner.client.read_only_transaction()
            .await?
            .query(&stmt)
            .await?;

        if let Some(row) = result_set.next().await? {
            let status_str: String = row.column_by_name("status")?;
            let scope_json: String = row.column_by_name("scope")?;
            let errors_json: String = row.column_by_name("errors")?;

            Ok(DeletionRequest {
                request_id: row.column_by_name("request_id")?,
                user_id: row.column_by_name("user_id")?,
                reason: row.column_by_name("reason")?,
                requested_at: {
                    let timestamp_str: String = row.column_by_name("requested_at")?;
                    chrono::DateTime::parse_from_rfc3339(&timestamp_str)
                        .context("Failed to parse requested_at timestamp")?
                        .with_timezone(&Utc)
                },
                deadline: {
                    let timestamp_str: String = row.column_by_name("deadline")?;
                    chrono::DateTime::parse_from_rfc3339(&timestamp_str)
                        .context("Failed to parse deadline timestamp")?
                        .with_timezone(&Utc)
                },
                status: match status_str.as_str() {
                    "Pending" => DeletionStatus::Pending,
                    "Processing" => DeletionStatus::Processing,
                    "Completed" => DeletionStatus::Completed,
                    "Failed" => DeletionStatus::Failed,
                    "Cancelled" => DeletionStatus::Cancelled,
                    _ => DeletionStatus::Pending,
                },
                scope: serde_json::from_str(&scope_json)?,
                completed_at: {
                    let timestamp_str: Option<String> = row.column_by_name("completed_at")?;
                    timestamp_str.map(|ts| chrono::DateTime::parse_from_rfc3339(&ts)
                        .context("Failed to parse completed_at timestamp")
                        .map(|dt| dt.with_timezone(&Utc)))
                        .transpose()?
                },
                errors: serde_json::from_str(&errors_json)?,
            })
        } else {
            Err(anyhow::anyhow!("Deletion request not found: {}", request_id))
        }
    }

    /// Generate cryptographic certificate of deletion
    fn generate_deletion_certificate(
        &self,
        request: &DeletionRequest,
        summary: &DeletionSummary,
    ) -> Result<DeletionCertificate> {
        // Create content to be hashed
        let content = json!({
            "request_id": request.request_id,
            "user_id": request.user_id,
            "completed_at": request.completed_at,
            "deleted_items": summary,
            "reason": request.reason,
        });

        // Calculate SHA-256 hash
        let mut hasher = Sha256::new();
        hasher.update(serde_json::to_string(&content)?);
        let hash = format!("{:x}", hasher.finalize());

        // In production, this would use a proper digital signature
        // For now, we'll use a simple HMAC-like signature
        let signature = format!("GDPR-CERT-{}-{}", &hash[..16], Utc::now().timestamp());

        Ok(DeletionCertificate {
            content_hash: hash,
            signature,
            issued_at: Utc::now(),
            valid_until: Utc::now() + Duration::days(365 * 7), // 7 years
        })
    }

    /// Get deletion status for a user
    pub async fn get_deletion_status(&self, user_id: &str) -> Result<Vec<DeletionRequest>> {
        let stmt = Statement::new(
            "SELECT request_id, user_id, reason, requested_at, deadline, status, scope, completed_at, errors
             FROM gdpr_deletion_requests
             WHERE user_id = @user_id
             ORDER BY requested_at DESC"
        );
        let mut stmt = stmt;
        stmt.add_param("user_id", &user_id.to_string());

        let mut result_set = self.spanner.client.read_only_transaction()
            .await?
            .query(stmt)
            .await?;

        let mut requests = Vec::new();
        while let Some(row) = result_set.next().await? {
            let status_str: String = row.column_by_name("status")?;
            let scope_json: String = row.column_by_name("scope")?;
            let errors_json: String = row.column_by_name("errors")?;

            requests.push(DeletionRequest {
                request_id: row.column_by_name("request_id")?,
                user_id: row.column_by_name("user_id")?,
                reason: row.column_by_name("reason")?,
                requested_at: row.column_by_name("requested_at")?,
                deadline: row.column_by_name("deadline")?,
                status: match status_str.as_str() {
                    "Pending" => DeletionStatus::Pending,
                    "Processing" => DeletionStatus::Processing,
                    "Completed" => DeletionStatus::Completed,
                    "Failed" => DeletionStatus::Failed,
                    "Cancelled" => DeletionStatus::Cancelled,
                    _ => DeletionStatus::Pending,
                },
                scope: serde_json::from_str(&scope_json)?,
                completed_at: row.column_by_name("completed_at")?,
                errors: serde_json::from_str(&errors_json)?,
            });
        }

        Ok(requests)
    }

    /// Process all pending deletion requests (for scheduled job)
    pub async fn process_pending_deletions(&self) -> Result<Vec<DeletionReport>> {
        info!("Processing pending GDPR deletion requests");

        // Get all pending requests
        let stmt = Statement::new(
            "SELECT request_id
             FROM gdpr_deletion_requests
             WHERE status = 'Pending'
             AND requested_at <= @cutoff_time
             ORDER BY requested_at
             LIMIT @batch_size"
        );
        let mut stmt = stmt;
        stmt.add_param("cutoff_time", Utc::now() - Duration::minutes(5)); // 5 minute delay
        stmt.add_param("batch_size", self.config.batch_size as i64);

        let mut result_set = self.spanner.client.read_only_transaction()
            .await?
            .query(&stmt)
            .await?;

        let mut request_ids = Vec::new();
        while let Some(row) = result_set.next().await? {
            request_ids.push(row.column_by_name::<String>("request_id")?);
        }

        let mut reports = Vec::new();
        for request_id in request_ids {
            match self.process_deletion(&request_id).await {
                Ok(report) => reports.push(report),
                Err(e) => {
                    error!("Failed to process deletion request {}: {}", request_id, e);
                    // Continue processing other requests
                }
            }
        }

        info!("Processed {} deletion requests", reports.len());
        Ok(reports)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_deletion_certificate_generation() {
        // Test certificate generation logic
        let content = json!({
            "test": "data",
            "user_id": "test-user",
        });

        let mut hasher = Sha256::new();
        hasher.update(serde_json::to_string(&content).unwrap());
        let hash = format!("{:x}", hasher.finalize());

        assert_eq!(hash.len(), 64); // SHA-256 produces 64 hex characters
    }
}