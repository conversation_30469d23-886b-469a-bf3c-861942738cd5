//! Advanced input validation and sanitization module
//! 
//! This module provides comprehensive input validation, sanitization, and security
//! checking for all API endpoints with production-grade security measures.

use crate::errors::AnalysisError;
use regex::Regex;
use reqwest::Url;
use serde::{Deserialize, Serialize};
use std::collections::HashSet;
use validator::{Validate, ValidationError};

lazy_static::lazy_static! {
    /// Precompiled regex patterns for efficient validation
    static ref URL_REGEX: Regex = Regex::new(r"^https?://[^\s/$.?#].[^\s]*$").unwrap();
    static ref GIT_URL_REGEX: Regex = Regex::new(r"^https://github\.com/[a-zA-Z0-9_.-]+/[a-zA-Z0-9_.-]+(?:\.git)?$").unwrap();
    static ref BRANCH_NAME_REGEX: Regex = Regex::new(r"^[a-zA-Z0-9/_.-]+$").unwrap();
    static ref COMMIT_HASH_REGEX: Regex = Regex::new(r"^[a-f0-9]{40}$").unwrap();
    static ref ANALYSIS_ID_REGEX: Regex = Regex::new(r"^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$").unwrap();
    static ref USER_ID_REGEX: Regex = Regex::new(r"^[a-zA-Z0-9_.-]+$").unwrap();
    static ref FILE_PATH_REGEX: Regex = Regex::new(r"^[a-zA-Z0-9/_.-]+$").unwrap();
    static ref LANGUAGE_NAME_REGEX: Regex = Regex::new(r"^[a-zA-Z0-9_+#-]+$").unwrap();
    
    /// Blacklisted URL patterns that should never be allowed
    static ref BLACKLISTED_URL_PATTERNS: Vec<Regex> = vec![
        Regex::new(r"localhost|127\.0\.0\.1|0\.0\.0\.0").unwrap(),
        Regex::new(r"10\.\d+\.\d+\.\d+").unwrap(),
        Regex::new(r"192\.168\.\d+\.\d+").unwrap(),
        Regex::new(r"172\.(1[6-9]|2\d|3[01])\.\d+\.\d+").unwrap(),
        Regex::new(r"file://|ftp://|sftp://").unwrap(),
    ];
    
    /// Allowed GitHub domains for repository analysis
    static ref ALLOWED_DOMAINS: HashSet<&'static str> = {
        let mut set = HashSet::new();
        set.insert("github.com");
        set.insert("gitlab.com");
        set.insert("bitbucket.org");
        set
    };
}

/// Validation result with detailed error information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationResult {
    pub is_valid: bool,
    pub errors: Vec<ValidationError>,
    pub warnings: Vec<ValidationWarning>,
    pub sanitized_input: Option<String>,
}

/// Validation warning for potentially suspicious input
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationWarning {
    pub field: String,
    pub message: String,
    pub severity: WarningSeverity,
    pub recommendation: String,
}

/// Warning severity levels
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum WarningSeverity {
    Low,
    Medium,
    High,
    Critical,
}

/// Comprehensive input validator with security-focused validation
pub struct InputValidator {
    max_url_length: usize,
    max_branch_length: usize,
    max_file_path_length: usize,
    max_user_id_length: usize,
    enable_advanced_scanning: bool,
}

impl Default for InputValidator {
    fn default() -> Self {
        Self {
            max_url_length: 2048,
            max_branch_length: 255,
            max_file_path_length: 1024,
            max_user_id_length: 255,
            enable_advanced_scanning: true,
        }
    }
}

impl InputValidator {
    /// Create a new validator with custom configuration
    pub fn new() -> Self {
        Self::default()
    }

    /// Configure validator for production use
    pub fn production_config() -> Self {
        Self {
            max_url_length: 1024,
            max_branch_length: 128,
            max_file_path_length: 512,
            max_user_id_length: 64,
            enable_advanced_scanning: true,
        }
    }

    /// Validate repository URL with comprehensive security checks
    pub fn validate_repository_url(&self, url: &str) -> Result<ValidationResult, AnalysisError> {
        let mut errors = Vec::new();
        let mut warnings = Vec::new();
        let mut is_valid = true;

        // Length validation
        if url.len() > self.max_url_length {
            errors.push(ValidationError::new("url_too_long"));
            is_valid = false;
        }

        // Format validation
        if !URL_REGEX.is_match(url) {
            errors.push(ValidationError::new("invalid_url_format"));
            is_valid = false;
        }

        // Security checks - blacklisted patterns
        for pattern in BLACKLISTED_URL_PATTERNS.iter() {
            if pattern.is_match(url) {
                errors.push(ValidationError::new("blacklisted_url"));
                is_valid = false;
                break;
            }
        }

        // Domain validation
        if let Ok(parsed_url) = Url::parse(url) {
            if let Some(domain) = parsed_url.domain() {
                if !ALLOWED_DOMAINS.contains(domain) {
                    warnings.push(ValidationWarning {
                        field: "repository_url".to_string(),
                        message: format!("Domain '{domain}' is not in the allowed list"),
                        severity: WarningSeverity::Medium,
                        recommendation: "Consider using GitHub, GitLab, or Bitbucket".to_string(),
                    });
                }
            }
        }

        // GitHub-specific validation
        if url.contains("github.com") && !GIT_URL_REGEX.is_match(url) {
            warnings.push(ValidationWarning {
                field: "repository_url".to_string(),
                message: "GitHub URL format may be incorrect".to_string(),
                severity: WarningSeverity::Low,
                recommendation: "Use format: https://github.com/owner/repo".to_string(),
            });
        }

        // Advanced security scanning
        if self.enable_advanced_scanning {
            if url.contains("..") || url.contains("//") {
                warnings.push(ValidationWarning {
                    field: "repository_url".to_string(),
                    message: "URL contains potentially suspicious patterns".to_string(),
                    severity: WarningSeverity::High,
                    recommendation: "Review URL for path traversal attempts".to_string(),
                });
            }

            if !url.is_ascii() {
                warnings.push(ValidationWarning {
                    field: "repository_url".to_string(),
                    message: "URL contains non-ASCII characters".to_string(),
                    severity: WarningSeverity::Medium,
                    recommendation: "Use ASCII-only URLs for better compatibility".to_string(),
                });
            }
        }

        Ok(ValidationResult {
            is_valid,
            errors,
            warnings,
            sanitized_input: Some(self.sanitize_url(url)),
        })
    }

    /// Validate branch name with security considerations
    pub fn validate_branch_name(&self, branch: &str) -> Result<ValidationResult, AnalysisError> {
        let mut errors = Vec::new();
        let mut warnings = Vec::new();
        let mut is_valid = true;

        // Length validation
        if branch.len() > self.max_branch_length {
            errors.push(ValidationError::new("branch_name_too_long"));
            is_valid = false;
        }

        // Format validation
        if !BRANCH_NAME_REGEX.is_match(branch) {
            errors.push(ValidationError::new("invalid_branch_format"));
            is_valid = false;
        }

        // Security checks
        if branch.starts_with('-') || branch.ends_with('-') {
            warnings.push(ValidationWarning {
                field: "branch".to_string(),
                message: "Branch name starts or ends with dash".to_string(),
                severity: WarningSeverity::Low,
                recommendation: "Use standard branch naming conventions".to_string(),
            });
        }

        if branch.contains("..") {
            errors.push(ValidationError::new("suspicious_branch_pattern"));
            is_valid = false;
        }

        Ok(ValidationResult {
            is_valid,
            errors,
            warnings,
            sanitized_input: Some(self.sanitize_branch_name(branch)),
        })
    }

    /// Validate commit hash format
    pub fn validate_commit_hash(&self, hash: &str) -> Result<ValidationResult, AnalysisError> {
        let mut errors = Vec::new();
        let is_valid = COMMIT_HASH_REGEX.is_match(hash);

        if !is_valid {
            errors.push(ValidationError::new("invalid_commit_hash"));
        }

        Ok(ValidationResult {
            is_valid,
            errors,
            warnings: Vec::new(),
            sanitized_input: Some(hash.to_lowercase()),
        })
    }

    /// Validate analysis ID format
    pub fn validate_analysis_id(&self, id: &str) -> Result<ValidationResult, AnalysisError> {
        let mut errors = Vec::new();
        let is_valid = ANALYSIS_ID_REGEX.is_match(id);

        if !is_valid {
            errors.push(ValidationError::new("invalid_analysis_id"));
        }

        Ok(ValidationResult {
            is_valid,
            errors,
            warnings: Vec::new(),
            sanitized_input: Some(id.to_lowercase()),
        })
    }

    /// Validate user ID with security checks
    pub fn validate_user_id(&self, user_id: &str) -> Result<ValidationResult, AnalysisError> {
        let mut errors = Vec::new();
        let mut warnings = Vec::new();
        let mut is_valid = true;

        // Length validation
        if user_id.len() > self.max_user_id_length {
            errors.push(ValidationError::new("user_id_too_long"));
            is_valid = false;
        }

        // Format validation
        if !USER_ID_REGEX.is_match(user_id) {
            errors.push(ValidationError::new("invalid_user_id_format"));
            is_valid = false;
        }

        // Security checks
        if user_id.starts_with('.') || user_id.ends_with('.') {
            warnings.push(ValidationWarning {
                field: "user_id".to_string(),
                message: "User ID starts or ends with dot".to_string(),
                severity: WarningSeverity::Low,
                recommendation: "Use standard user ID format".to_string(),
            });
        }

        if user_id.contains("..") {
            errors.push(ValidationError::new("suspicious_user_id_pattern"));
            is_valid = false;
        }

        Ok(ValidationResult {
            is_valid,
            errors,
            warnings,
            sanitized_input: Some(self.sanitize_user_id(user_id)),
        })
    }

    /// Validate file path with security considerations
    pub fn validate_file_path(&self, path: &str) -> Result<ValidationResult, AnalysisError> {
        let mut errors = Vec::new();
        let mut warnings = Vec::new();
        let mut is_valid = true;

        // Length validation
        if path.len() > self.max_file_path_length {
            errors.push(ValidationError::new("file_path_too_long"));
            is_valid = false;
        }

        // Format validation
        if !FILE_PATH_REGEX.is_match(path) {
            errors.push(ValidationError::new("invalid_file_path_format"));
            is_valid = false;
        }

        // Security checks - path traversal prevention
        if path.contains("..") || path.contains("//") {
            errors.push(ValidationError::new("path_traversal_attempt"));
            is_valid = false;
        }

        if path.starts_with('/') {
            warnings.push(ValidationWarning {
                field: "file_path".to_string(),
                message: "Absolute file path detected".to_string(),
                severity: WarningSeverity::Medium,
                recommendation: "Use relative paths when possible".to_string(),
            });
        }

        Ok(ValidationResult {
            is_valid,
            errors,
            warnings,
            sanitized_input: Some(self.sanitize_file_path(path)),
        })
    }

    /// Validate language name
    pub fn validate_language_name(&self, language: &str) -> Result<ValidationResult, AnalysisError> {
        let mut errors = Vec::new();
        let is_valid = LANGUAGE_NAME_REGEX.is_match(language) && language.len() <= 50;

        if !is_valid {
            errors.push(ValidationError::new("invalid_language_name"));
        }

        Ok(ValidationResult {
            is_valid,
            errors,
            warnings: Vec::new(),
            sanitized_input: Some(language.to_lowercase()),
        })
    }

    /// Sanitize URL by removing potentially dangerous characters
    fn sanitize_url(&self, url: &str) -> String {
        url.chars()
            .filter(|c| c.is_ascii() && !c.is_control())
            .collect::<String>()
            .trim()
            .to_string()
    }

    /// Sanitize branch name
    fn sanitize_branch_name(&self, branch: &str) -> String {
        branch.chars()
            .filter(|c| c.is_ascii_alphanumeric() || matches!(c, '/' | '_' | '.' | '-'))
            .collect::<String>()
            .trim_matches('-')
            .to_string()
    }

    /// Sanitize user ID
    fn sanitize_user_id(&self, user_id: &str) -> String {
        user_id.chars()
            .filter(|c| c.is_ascii_alphanumeric() || matches!(c, '_' | '.' | '-'))
            .collect::<String>()
            .trim_matches('.')
            .to_string()
    }

    /// Sanitize file path
    fn sanitize_file_path(&self, path: &str) -> String {
        path.replace("..", ".")
            .replace("//", "/")
            .chars()
            .filter(|c| c.is_ascii_alphanumeric() || matches!(c, '/' | '_' | '.' | '-'))
            .collect::<String>()
            .trim_matches('/')
            .to_string()
    }

    /// Batch validation for multiple inputs
    pub fn validate_batch(&self, inputs: Vec<(&str, &str)>) -> Result<Vec<ValidationResult>, AnalysisError> {
        let mut results = Vec::new();

        for (input_type, input_value) in inputs {
            let result = match input_type {
                "url" => self.validate_repository_url(input_value)?,
                "branch" => self.validate_branch_name(input_value)?,
                "commit" => self.validate_commit_hash(input_value)?,
                "analysis_id" => self.validate_analysis_id(input_value)?,
                "user_id" => self.validate_user_id(input_value)?,
                "file_path" => self.validate_file_path(input_value)?,
                "language" => self.validate_language_name(input_value)?,
                _ => {
                    return Err(AnalysisError::invalid_input(
                        format!("Unknown input type: {input_type}")
                    ));
                }
            };
            results.push(result);
        }

        Ok(results)
    }

    /// Get validation summary statistics
    pub fn get_validation_stats(&self, results: &[ValidationResult]) -> ValidationStats {
        let total = results.len();
        let valid = results.iter().filter(|r| r.is_valid).count();
        let errors = results.iter().map(|r| r.errors.len()).sum();
        let warnings = results.iter().map(|r| r.warnings.len()).sum();

        ValidationStats {
            total_validations: total,
            valid_inputs: valid,
            invalid_inputs: total - valid,
            total_errors: errors,
            total_warnings: warnings,
            success_rate: if total > 0 { valid as f64 / total as f64 } else { 0.0 },
        }
    }
}

/// Validation statistics for monitoring and reporting
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationStats {
    pub total_validations: usize,
    pub valid_inputs: usize,
    pub invalid_inputs: usize,
    pub total_errors: usize,
    pub total_warnings: usize,
    pub success_rate: f64,
}

/// Enhanced validator trait for custom validation logic
pub trait ValidatorExt: Validate {
    /// Validate with detailed error reporting
    fn validate_detailed(&self) -> Result<ValidationResult, AnalysisError>;
    
    /// Validate with custom security rules
    fn validate_secure(&self) -> Result<ValidationResult, AnalysisError>;
}

/// Rate limiting configuration for validation endpoints
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationRateLimit {
    pub max_validations_per_minute: u32,
    pub max_batch_size: usize,
    pub enable_throttling: bool,
}

impl Default for ValidationRateLimit {
    fn default() -> Self {
        Self {
            max_validations_per_minute: 1000,
            max_batch_size: 100,
            enable_throttling: true,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_url_validation() {
        let validator = InputValidator::new();
        
        // Valid URLs
        assert!(validator.validate_repository_url("https://github.com/owner/repo").unwrap().is_valid);
        assert!(validator.validate_repository_url("https://gitlab.com/owner/repo").unwrap().is_valid);
        
        // Invalid URLs
        assert!(!validator.validate_repository_url("localhost:8080").unwrap().is_valid);
        assert!(!validator.validate_repository_url("file:///etc/passwd").unwrap().is_valid);
        assert!(!validator.validate_repository_url("http://***********/repo").unwrap().is_valid);
    }

    #[test]
    fn test_branch_validation() {
        let validator = InputValidator::new();
        
        // Valid branches
        assert!(validator.validate_branch_name("main").unwrap().is_valid);
        assert!(validator.validate_branch_name("feature/new-feature").unwrap().is_valid);
        assert!(validator.validate_branch_name("release/v1.0.0").unwrap().is_valid);
        
        // Invalid branches
        assert!(!validator.validate_branch_name("../../../etc/passwd").unwrap().is_valid);
        assert!(!validator.validate_branch_name("branch with spaces").unwrap().is_valid);
    }

    #[test]
    fn test_commit_hash_validation() {
        let validator = InputValidator::new();
        
        // Valid commit hashes
        assert!(validator.validate_commit_hash("a1b2c3d4e5f6789012345678901234567890abcd").unwrap().is_valid);
        
        // Invalid commit hashes
        assert!(!validator.validate_commit_hash("short").unwrap().is_valid);
        assert!(!validator.validate_commit_hash("not-a-hash").unwrap().is_valid);
    }

    #[test]
    fn test_batch_validation() {
        let validator = InputValidator::new();
        
        let inputs = vec![
            ("url", "https://github.com/owner/repo"),
            ("branch", "main"),
            ("commit", "a1b2c3d4e5f6789012345678901234567890abcd"),
        ];
        
        let results = validator.validate_batch(inputs).unwrap();
        assert_eq!(results.len(), 3);
        assert!(results.iter().all(|r| r.is_valid));
    }

    #[test]
    fn test_validation_stats() {
        let validator = InputValidator::new();
        
        let results = vec![
            ValidationResult {
                is_valid: true,
                errors: vec![],
                warnings: vec![],
                sanitized_input: None,
            },
            ValidationResult {
                is_valid: false,
                errors: vec![ValidationError::new("test")],
                warnings: vec![],
                sanitized_input: None,
            },
        ];
        
        let stats = validator.get_validation_stats(&results);
        assert_eq!(stats.total_validations, 2);
        assert_eq!(stats.valid_inputs, 1);
        assert_eq!(stats.invalid_inputs, 1);
        assert_eq!(stats.success_rate, 0.5);
    }
}