//! CSRF Protection Middleware
//!
//! This module provides Cross-Site Request Forgery (CSRF) protection using the Double Submit Cookie pattern.
//! It generates and validates CSRF tokens to protect against CSRF attacks on state-changing operations.

use axum::{
    extract::{Request, State},
    http::{header, HeaderMap, Method, StatusCode},
    middleware::Next,
    response::{IntoResponse, Response},
    Json,
};
use base64::{engine::general_purpose::URL_SAFE_NO_PAD, Engine as _};
use rand::{thread_rng, Rng};
use serde_json::json;
use sha2::{Digest, Sha256};
use std::sync::Arc;
use std::time::{SystemTime, UNIX_EPOCH};

use crate::api::{errors::ErrorResponse, AppState};

/// CSRF token configuration
const CSRF_TOKEN_LENGTH: usize = 32;
const CSRF_COOKIE_NAME: &str = "csrf_token";
const CSRF_HEADER_NAME: &str = "X-CSRF-Token";
const CSRF_TOKEN_LIFETIME: u64 = 3600; // 1 hour

/// CSRF protection middleware that implements the Double Submit Cookie pattern
///
/// This middleware:
/// 1. Generates CSRF tokens for GET requests and sets them as secure cookies
/// 2. Validates CSRF tokens for state-changing operations (POST, PUT, DELETE, PATCH)
/// 3. Uses time-based token validation to prevent replay attacks
/// 4. Implements SameSite=Strict and Secure cookies for enhanced security
pub async fn csrf_middleware(
    State(state): State<Arc<AppState>>,
    request: Request,
    next: Next,
) -> Response {
    let method = request.method().clone();
    let headers = request.headers().clone();
    
    // Skip CSRF protection for health/monitoring endpoints
    let path = request.uri().path();
    if path.starts_with("/health") || path.starts_with("/metrics") {
        return next.run(request).await;
    }

    match method {
        // For GET requests, generate and set CSRF token
        Method::GET => handle_get_request(request, next, state).await,
        // For state-changing operations, validate CSRF token
        Method::POST | Method::PUT | Method::DELETE | Method::PATCH => {
            handle_state_changing_request(request, next, headers).await
        }
        // For other methods (HEAD, OPTIONS), pass through
        _ => next.run(request).await,
    }
}

/// Handle GET requests by generating and setting CSRF tokens
async fn handle_get_request(
    request: Request,
    next: Next,
    _state: Arc<AppState>,
) -> Response {
    let mut response = next.run(request).await;
    
    // Generate a new CSRF token
    let csrf_token = generate_csrf_token();
    
    // Set the CSRF token as a secure cookie
    let cookie_value = format!(
        "{}={}; Path=/; HttpOnly; Secure; SameSite=Strict; Max-Age={}",
        CSRF_COOKIE_NAME,
        csrf_token,
        CSRF_TOKEN_LIFETIME
    );
    
    if let Ok(cookie_header) = cookie_value.parse() {
        response.headers_mut().insert(header::SET_COOKIE, cookie_header);
    }
    
    // Also provide the token in a header for JavaScript access (if needed)
    if let Ok(token_header) = csrf_token.parse() {
        response.headers_mut().insert(
            axum::http::HeaderName::from_static("x-csrf-token"),
            token_header,
        );
    }
    
    response
}

/// Handle state-changing requests by validating CSRF tokens
async fn handle_state_changing_request(
    request: Request,
    next: Next,
    headers: HeaderMap,
) -> Response {
    // Extract CSRF token from header
    let header_token = headers
        .get(CSRF_HEADER_NAME)
        .and_then(|v| v.to_str().ok())
        .map(|s| s.to_string());
    
    // Extract CSRF token from cookie
    let cookie_token = extract_csrf_token_from_cookies(&headers);
    
    match (header_token, cookie_token) {
        (Some(h_token), Some(c_token)) => {
            // Validate that both tokens match and are valid
            if validate_csrf_tokens(&h_token, &c_token) {
                // Tokens are valid, proceed with request
                next.run(request).await
            } else {
                // Invalid tokens, return CSRF error
                csrf_validation_error("CSRF token validation failed")
            }
        }
        (None, _) => csrf_validation_error("Missing CSRF token in header"),
        (_, None) => csrf_validation_error("Missing CSRF token in cookie"),
    }
}

/// Generate a cryptographically secure CSRF token with timestamp
fn generate_csrf_token() -> String {
    let mut token_bytes = vec![0u8; CSRF_TOKEN_LENGTH];
    thread_rng().fill(&mut token_bytes[..]);
    
    // Add timestamp to the token for expiration validation
    let timestamp = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_secs();
    
    let timestamp_bytes = timestamp.to_be_bytes();
    token_bytes.extend_from_slice(&timestamp_bytes);
    
    // Create HMAC-like signature using SHA256
    let mut hasher = Sha256::new();
    hasher.update(&token_bytes);
    let signature = hasher.finalize();
    
    // Combine token, timestamp, and signature
    let mut final_token = token_bytes;
    final_token.extend_from_slice(&signature[..8]); // Use first 8 bytes of signature
    
    URL_SAFE_NO_PAD.encode(final_token)
}

/// Extract CSRF token from request cookies
fn extract_csrf_token_from_cookies(headers: &HeaderMap) -> Option<String> {
    headers
        .get(header::COOKIE)
        .and_then(|cookie_header| cookie_header.to_str().ok())
        .and_then(|cookie_str| {
            cookie_str
                .split(';')
                .find_map(|cookie| {
                    let cookie = cookie.trim();
                    if cookie.starts_with(CSRF_COOKIE_NAME) {
                        cookie.split('=').nth(1).map(|s| s.to_string())
                    } else {
                        None
                    }
                })
        })
}

/// Validate CSRF tokens using Double Submit Cookie pattern
fn validate_csrf_tokens(header_token: &str, cookie_token: &str) -> bool {
    // Tokens must match (Double Submit Cookie pattern)
    if header_token != cookie_token {
        return false;
    }
    
    // Decode and validate token structure
    if let Ok(token_bytes) = URL_SAFE_NO_PAD.decode(header_token) {
        if token_bytes.len() < CSRF_TOKEN_LENGTH + 8 + 8 {
            return false; // Invalid token length
        }
        
        // Extract timestamp from token
        let timestamp_start = CSRF_TOKEN_LENGTH;
        let timestamp_bytes = &token_bytes[timestamp_start..timestamp_start + 8];
        let timestamp = u64::from_be_bytes([
            timestamp_bytes[0],
            timestamp_bytes[1],
            timestamp_bytes[2],
            timestamp_bytes[3],
            timestamp_bytes[4],
            timestamp_bytes[5],
            timestamp_bytes[6],
            timestamp_bytes[7],
        ]);
        
        // Check if token is expired
        let current_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();
        
        if current_time > timestamp + CSRF_TOKEN_LIFETIME {
            return false; // Token expired
        }
        
        // Validate signature
        let token_data = &token_bytes[..timestamp_start + 8];
        let mut hasher = Sha256::new();
        hasher.update(token_data);
        let expected_signature = hasher.finalize();
        let provided_signature = &token_bytes[timestamp_start + 8..];
        
        // Compare first 8 bytes of signature
        if provided_signature.len() >= 8 && expected_signature[..8] == provided_signature[..8] {
            return true;
        }
    }
    
    false
}

/// Return CSRF validation error response
fn csrf_validation_error(message: &str) -> Response {
    let error = ErrorResponse::new(
        crate::api::errors::ErrorType::Authorization,
        format!("CSRF protection: {}", message),
    );
    
    let json_response = Json(json!({
        "error": error,
        "csrf_protection": {
            "required": true,
            "instructions": {
                "step_1": "Make a GET request to obtain CSRF token",
                "step_2": "Include X-CSRF-Token header in state-changing requests",
                "step_3": "Ensure CSRF cookie is sent with request"
            }
        }
    }));
    
    (StatusCode::FORBIDDEN, json_response).into_response()
}

/// Get current CSRF protection status for monitoring
pub fn get_csrf_protection_status() -> serde_json::Value {
    json!({
        "csrf_protection": {
            "enabled": true,
            "method": "Double Submit Cookie",
            "token_lifetime_seconds": CSRF_TOKEN_LIFETIME,
            "protected_methods": ["POST", "PUT", "DELETE", "PATCH"],
            "cookie_security": {
                "http_only": true,
                "secure": true,
                "same_site": "Strict"
            }
        }
    })
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_csrf_token_generation() {
        let token1 = generate_csrf_token();
        let token2 = generate_csrf_token();
        
        // Tokens should be different
        assert_ne!(token1, token2);
        
        // Tokens should be valid base64
        assert!(URL_SAFE_NO_PAD.decode(&token1).is_ok());
        assert!(URL_SAFE_NO_PAD.decode(&token2).is_ok());
    }
    
    #[test]
    fn test_csrf_token_validation() {
        let token = generate_csrf_token();
        
        // Valid: same token in header and cookie
        assert!(validate_csrf_tokens(&token, &token));
        
        // Invalid: different tokens
        let other_token = generate_csrf_token();
        assert!(!validate_csrf_tokens(&token, &other_token));
        
        // Invalid: malformed token
        assert!(!validate_csrf_tokens("invalid", &token));
    }
    
    #[test]
    fn test_cookie_extraction() {
        let mut headers = HeaderMap::new();
        let cookie_value = format!("{}=test_token_123; other=value", CSRF_COOKIE_NAME);
        headers.insert(header::COOKIE, cookie_value.parse().unwrap());
        
        let extracted = extract_csrf_token_from_cookies(&headers);
        assert_eq!(extracted, Some("test_token_123".to_string()));
    }
}