//! Tower Service-based security middleware for Axum 0.8 compatibility
//!
//! This module provides a proper Tower Layer/Service implementation of the security middleware
//! to resolve trait bound issues with Axum 0.8's from_fn_with_state approach.

use crate::api::{
    errors::{ErrorResponse, ErrorType},
    security_middleware::{
        check_ip_rate_limit, check_request_fingerprint_with_headers, check_threat_patterns_with_uri,
        add_security_headers, add_response_security_headers, extract_client_ip,
        SecurityMiddleware,
    },
    AppState,
};
use axum::{
    extract::Request,
    http::StatusCode,
    response::{IntoResponse, Response},
    Json,
};
use futures::future::BoxFuture;
use std::sync::Arc;
use std::task::{Context, Poll};
use tower::{Layer, Service};
use uuid::Uuid;

/// Security Layer that implements Tower's Layer trait
/// This layer creates SecurityService instances that handle the actual middleware logic
#[derive(Clone)]
pub struct SecurityLayer {
    state: Arc<AppState>,
}

impl SecurityLayer {
    /// Create a new SecurityLayer with the given application state
    pub fn new(state: Arc<AppState>) -> Self {
        Self { state }
    }
}

impl<S> Layer<S> for SecurityLayer {
    type Service = SecurityService<S>;

    fn layer(&self, inner: S) -> Self::Service {
        SecurityService {
            inner,
            state: self.state.clone(),
        }
    }
}

/// Security Service that implements Tower's Service trait
/// This service wraps the inner service and adds security middleware functionality
#[derive(Clone)]
pub struct SecurityService<S> {
    inner: S,
    state: Arc<AppState>,
}

impl<S> Service<Request> for SecurityService<S>
where
    S: Service<Request, Response = Response> + Clone + Send + 'static,
    S::Future: Send + 'static,
    S::Error: std::error::Error + Send + Sync + 'static,
{
    type Response = Response;
    type Error = S::Error;
    type Future = BoxFuture<'static, Result<Self::Response, Self::Error>>;

    fn poll_ready(&mut self, cx: &mut Context<'_>) -> Poll<Result<(), Self::Error>> {
        self.inner.poll_ready(cx)
    }

    fn call(&mut self, mut request: Request) -> Self::Future {
        let state = self.state.clone();
        let request_id = Uuid::new_v4().to_string();
        
        // Extract information needed for security checks before moving the request
        let client_ip = extract_client_ip(&request);
        
        // Clone the inner service to avoid borrowing issues
        let mut inner = self.inner.clone();
        
        Box::pin(async move {
            
            // Initialize threat patterns if needed
            if crate::api::security_middleware::THREAT_PATTERNS.is_empty() {
                SecurityMiddleware::init_threat_patterns();
            }

            // Check if IP is blocked
            if let Some(blocked_entry) = crate::api::security_middleware::BLOCKED_IPS.get(&client_ip) {
                if std::time::Instant::now() < blocked_entry.expires_at {
                    let error = ErrorResponse::new(
                        ErrorType::Authorization,
                        "IP address is temporarily blocked due to security violations".to_string(),
                    );
                    
                    // Log security event
                    crate::api::security_middleware::log_security_event(
                        &state,
                        crate::api::security_middleware::SecurityEventType::BlockedIpRequest,
                        &client_ip,
                        &request_id,
                        format!("Blocked IP attempted request: {}", blocked_entry.reason),
                    ).await;

                    return Ok((StatusCode::FORBIDDEN, Json(error)).into_response());
                } else {
                    // Remove expired block
                    crate::api::security_middleware::BLOCKED_IPS.remove(&client_ip);
                }
            }

            // IP-based rate limiting
            if let Err(response) = check_ip_rate_limit(&client_ip, &state, &request_id).await {
                return Ok(response);
            }

            // Request fingerprinting and anomaly detection
            // Note: We need to extract necessary information before moving the request
            let headers = request.headers().clone();
            let user_agent = headers
                .get("user-agent")
                .and_then(|v| v.to_str().ok())
                .unwrap_or("unknown")
                .to_string();
            
            if let Err(response) = check_request_fingerprint_with_headers(&headers, &user_agent, &client_ip, &state, &request_id).await {
                return Ok(response);
            }

            // Threat detection
            // Extract URI and query for threat pattern checking
            let uri = request.uri().to_string();
            let query = request.uri().query().unwrap_or("");
            
            if let Err(response) = check_threat_patterns_with_uri(&uri, query, &client_ip, &state, &request_id).await {
                return Ok(response);
            }

            // Add security headers to request
            add_security_headers(&mut request);

            // Process the request with the inner service
            let mut response = inner.call(request).await?;

            // Add security headers to response
            add_response_security_headers(&mut response);

            // Log successful request processing
            tracing::debug!(
                "Security middleware processed request {} from {}",
                request_id,
                client_ip
            );

            Ok(response)
        })
    }
}

/// Re-export functions that were public in the original security_middleware module
pub use crate::api::security_middleware::{
    get_security_stats as get_stats,
    block_ip_manually as block_ip,
    unblock_ip_manually as unblock_ip,
};

#[cfg(test)]
mod tests {
    use super::*;
    use axum::{routing::get, Router};
    use tower::ServiceBuilder;

    #[tokio::test]
    async fn test_security_layer_creation() {
        // Create a mock AppState (would need actual implementation in real test)
        let state = Arc::new(AppState::new().await.unwrap());
        
        // Create the security layer
        let security_layer = SecurityLayer::new(state.clone());
        
        // Create a simple router with the security layer
        let _app: Router<()> = Router::new()
            .route("/test", get(|| async { "OK" }))
            .layer(security_layer)
            .with_state(state);
        
        // The test passes if the router compiles successfully
        // In a real test, you would make requests to verify functionality
    }
    
    #[tokio::test]
    async fn test_security_service_with_service_builder() {
        let state = Arc::new(AppState::new().await.unwrap());
        
        // Test that SecurityLayer works with ServiceBuilder
        let _app: Router<()> = Router::new()
            .route("/test", get(|| async { "OK" }))
            .layer(
                ServiceBuilder::new()
                    .layer(SecurityLayer::new(state.clone()))
                    .into_inner()
            )
            .with_state(state);
        
        // The test passes if the router compiles successfully
    }
}