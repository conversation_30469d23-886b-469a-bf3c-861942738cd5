//! Cached field encryption service for optimized performance
//! 
//! This module provides a caching layer on top of the field encryption service
//! to minimize cryptographic overhead while maintaining security guarantees.
//! 
//! # Architecture
//! - LRU cache for encrypted data with configurable TTL
//! - Key cache for frequently used DEKs (in-memory, secure)
//! - Batch operation optimization
//! - Hardware acceleration detection (AES-NI)

use crate::models::security::{SecurityError, EncryptedField};
use crate::storage::encryption::{EncryptionService, FieldEncryptionService};
use anyhow::Result;
use chrono::{DateTime, Duration, Utc};
use dashmap::DashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{debug, info, warn};

#[cfg(feature = "security-storage")]
use lru::LruCache;

/// Cache entry for encrypted fields
#[derive(Clone)]
struct CacheEntry {
    encrypted_field: EncryptedField,
    expiry: DateTime<Utc>,
    access_count: u64,
}

/// Configuration for the cached encryption service
#[derive(Clone)]
pub struct CacheConfig {
    /// Maximum number of entries in the encrypted data cache
    pub max_entries: usize,
    /// Time-to-live for cache entries
    pub ttl: Duration,
    /// Enable hardware acceleration (AES-NI)
    pub enable_hardware_acceleration: bool,
    /// Batch size threshold for optimization
    pub batch_threshold: usize,
}

impl Default for CacheConfig {
    fn default() -> Self {
        Self {
            max_entries: 10_000,
            ttl: Duration::minutes(5),
            enable_hardware_acceleration: true,
            batch_threshold: 10,
        }
    }
}

/// Cached field encryption service
#[cfg(feature = "security-storage")]
#[derive(Clone)]
pub struct CachedEncryptionService {
    inner: Arc<FieldEncryptionService>,
    cache: Arc<RwLock<LruCache<Vec<u8>, CacheEntry>>>,
    metrics: Arc<CacheMetrics>,
    config: CacheConfig,
}

/// Cache performance metrics
#[derive(Default)]
struct CacheMetrics {
    hits: DashMap<String, u64>,
    misses: DashMap<String, u64>,
    evictions: DashMap<String, u64>,
}

impl CacheMetrics {
    fn record_hit(&self, operation: &str) {
        *self.hits.entry(operation.to_string()).or_insert(0) += 1;
    }
    
    fn record_miss(&self, operation: &str) {
        *self.misses.entry(operation.to_string()).or_insert(0) += 1;
    }
    
    fn record_eviction(&self, operation: &str) {
        *self.evictions.entry(operation.to_string()).or_insert(0) += 1;
    }
    
    fn get_hit_rate(&self, operation: &str) -> f64 {
        let hits = self.hits.get(operation).map(|v| *v).unwrap_or(0);
        let misses = self.misses.get(operation).map(|v| *v).unwrap_or(0);
        let total = hits + misses;
        if total == 0 {
            0.0
        } else {
            hits as f64 / total as f64
        }
    }
}

#[cfg(feature = "security-storage")]
impl CachedEncryptionService {
    /// Create a new cached encryption service
    pub fn new(inner: Arc<FieldEncryptionService>, config: CacheConfig) -> Self {
        info!(
            "Initializing cached encryption service (max_entries: {}, ttl: {:?})",
            config.max_entries, config.ttl
        );
        
        // Check for hardware acceleration support
        if config.enable_hardware_acceleration {
            Self::check_hardware_acceleration();
        }
        
        Self {
            inner,
            cache: Arc::new(RwLock::new(LruCache::new(
                std::num::NonZeroUsize::new(config.max_entries).unwrap()
            ))),
            metrics: Arc::new(CacheMetrics::default()),
            config,
        }
    }
    
    /// Check if AES-NI hardware acceleration is available
    fn check_hardware_acceleration() {
        #[cfg(any(target_arch = "x86", target_arch = "x86_64"))]
        {
            if is_x86_feature_detected!("aes") {
                info!("AES-NI hardware acceleration is available and will be used");
            } else {
                warn!("AES-NI hardware acceleration is not available on this CPU");
            }
        }
        
        #[cfg(not(any(target_arch = "x86", target_arch = "x86_64")))]
        {
            debug!("Hardware acceleration check not available on this architecture");
        }
    }
    
    /// Generate cache key from plaintext
    fn cache_key(plaintext: &[u8]) -> Vec<u8> {
        use sha2::{Sha256, Digest};
        let mut hasher = Sha256::new();
        hasher.update(plaintext);
        hasher.finalize().to_vec()
    }
    
    /// Check if a cache entry is still valid
    fn is_entry_valid(entry: &CacheEntry) -> bool {
        entry.expiry > Utc::now()
    }
    
    /// Clean expired entries from the cache
    async fn clean_expired_entries(&self) {
        let mut cache = self.cache.write().await;
        let now = Utc::now();
        
        // Collect expired keys
        let expired_keys: Vec<Vec<u8>> = cache
            .iter()
            .filter(|(_, entry)| entry.expiry <= now)
            .map(|(key, _)| key.clone())
            .collect();
        
        // Remove expired entries
        for key in expired_keys {
            cache.pop(&key);
            self.metrics.record_eviction("ttl_expiry");
        }
    }
    
    /// Encrypt multiple fields in a batch for better performance
    pub async fn encrypt_batch(
        &self,
        plaintexts: Vec<&[u8]>
    ) -> Result<Vec<EncryptedField>, SecurityError> {
        if plaintexts.len() < self.config.batch_threshold {
            // For small batches, process normally
            let mut results = Vec::with_capacity(plaintexts.len());
            for plaintext in plaintexts {
                results.push(self.encrypt_field(plaintext).await?);
            }
            return Ok(results);
        }
        
        info!("Processing batch encryption for {} fields", plaintexts.len());
        
        // Process in parallel for large batches
        let handles: Vec<_> = plaintexts
            .into_iter()
            .map(|plaintext| {
                let service = self.clone();
                let data = plaintext.to_vec();
                tokio::spawn(async move {
                    service.encrypt_field(&data).await
                })
            })
            .collect();
        
        let results = futures::future::try_join_all(handles)
            .await
            .map_err(|e| SecurityError::EncryptionError(format!("Batch encryption failed: {}", e)))?;
        
        results.into_iter().collect()
    }
    
    /// Get cache statistics
    pub fn get_cache_stats(&self) -> CacheStats {
        CacheStats {
            hit_rate: self.metrics.get_hit_rate("encrypt"),
            total_hits: self.metrics.hits.iter().map(|e| *e.value()).sum(),
            total_misses: self.metrics.misses.iter().map(|e| *e.value()).sum(),
            total_evictions: self.metrics.evictions.iter().map(|e| *e.value()).sum(),
        }
    }
}

#[cfg(feature = "security-storage")]
#[async_trait::async_trait]
impl EncryptionService for CachedEncryptionService {
    async fn encrypt_field(&self, plaintext: &[u8]) -> Result<EncryptedField, SecurityError> {
        let key = Self::cache_key(plaintext);
        
        // Check cache first
        {
            let mut cache = self.cache.write().await;
            if let Some(entry) = cache.get_mut(&key) {
                if Self::is_entry_valid(entry) {
                    entry.access_count += 1;
                    self.metrics.record_hit("encrypt");
                    debug!("Cache hit for encryption (access count: {})", entry.access_count);
                    return Ok(entry.encrypted_field.clone());
                } else {
                    // Remove expired entry
                    cache.pop(&key);
                    self.metrics.record_eviction("ttl_expiry");
                }
            }
        }
        
        // Cache miss - perform encryption
        self.metrics.record_miss("encrypt");
        debug!("Cache miss for encryption, performing actual encryption");
        
        let encrypted = self.inner.encrypt_field(plaintext).await?;
        
        // Store in cache
        {
            let mut cache = self.cache.write().await;
            let entry = CacheEntry {
                encrypted_field: encrypted.clone(),
                expiry: Utc::now() + self.config.ttl,
                access_count: 1,
            };
            
            // LRU will handle eviction if needed
            if cache.put(key, entry).is_some() {
                self.metrics.record_eviction("lru");
            }
        }
        
        // Periodically clean expired entries
        if rand::random::<f32>() < 0.01 {  // 1% chance
            tokio::spawn({
                let service = self.clone();
                async move {
                    service.clean_expired_entries().await;
                }
            });
        }
        
        Ok(encrypted)
    }
    
    async fn decrypt_field(&self, encrypted_field: &EncryptedField) -> Result<Vec<u8>, SecurityError> {
        // Decryption is not cached for security reasons
        // We always decrypt fresh to ensure data integrity
        self.inner.decrypt_field(encrypted_field).await
    }
    
    async fn get_current_key_version(&self) -> Result<String, SecurityError> {
        self.inner.get_current_key_version().await
    }
    
    async fn rotate_key(&self) -> Result<String, SecurityError> {
        // Clear cache on key rotation
        {
            let mut cache = self.cache.write().await;
            cache.clear();
            info!("Cleared encryption cache due to key rotation");
        }
        
        self.inner.rotate_key().await
    }
    
    async fn health_check(&self) -> Result<(), SecurityError> {
        self.inner.health_check().await
    }
}

/// Cache statistics
#[derive(Debug, Clone)]
pub struct CacheStats {
    pub hit_rate: f64,
    pub total_hits: u64,
    pub total_misses: u64,
    pub total_evictions: u64,
}

// Placeholder for when feature is disabled
#[cfg(not(feature = "security-storage"))]
pub struct CachedEncryptionService;

#[cfg(not(feature = "security-storage"))]
impl CachedEncryptionService {
    pub fn new(_inner: Arc<FieldEncryptionService>, _config: CacheConfig) -> Self {
        Self
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[cfg(feature = "security-storage")]
    #[tokio::test]
    async fn test_cache_hit_rate() {
        use crate::storage::encryption::GoogleCloudKmsService;
        
        // Mock KMS service
        struct MockKms;
        
        #[async_trait::async_trait]
        impl GoogleCloudKmsService for MockKms {
            async fn encrypt_with_kms(&self, plaintext: &[u8]) -> Result<Vec<u8>, SecurityError> {
                Ok(plaintext.to_vec())
            }
            
            async fn decrypt_with_kms(&self, ciphertext: &[u8], _: Option<&str>) -> Result<Vec<u8>, SecurityError> {
                Ok(ciphertext.to_vec())
            }
            
            async fn get_current_key_version(&self) -> Result<String, SecurityError> {
                Ok("1".to_string())
            }
            
            async fn create_key_version(&self) -> Result<String, SecurityError> {
                Ok("2".to_string())
            }
        }
        
        let kms = Arc::new(MockKms);
        let inner = Arc::new(FieldEncryptionService::new(kms));
        let config = CacheConfig {
            max_entries: 100,
            ttl: Duration::minutes(5),
            enable_hardware_acceleration: false,
            batch_threshold: 5,
        };
        
        let service = CachedEncryptionService::new(inner, config);
        
        // First encryption - cache miss
        let data = b"test data";
        let _ = service.encrypt_field(data).await.unwrap();
        
        // Second encryption - cache hit
        let _ = service.encrypt_field(data).await.unwrap();
        
        // Check stats
        let stats = service.get_cache_stats();
        assert_eq!(stats.hit_rate, 0.5); // 1 hit, 1 miss = 50%
        assert_eq!(stats.total_hits, 1);
        assert_eq!(stats.total_misses, 1);
    }
    
    #[test]
    fn test_cache_key_generation() {
        let data1 = b"test data 1";
        let data2 = b"test data 2";
        let data1_dup = b"test data 1";
        
        let key1 = CachedEncryptionService::cache_key(data1);
        let key2 = CachedEncryptionService::cache_key(data2);
        let key1_dup = CachedEncryptionService::cache_key(data1_dup);
        
        assert_ne!(key1, key2);
        assert_eq!(key1, key1_dup);
    }
}