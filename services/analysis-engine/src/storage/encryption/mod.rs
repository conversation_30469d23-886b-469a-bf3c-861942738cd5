//! Encryption module for secure storage
//! 
//! This module provides comprehensive encryption capabilities for the analysis engine,
//! including field-level encryption, key management via Google Cloud KMS, and automated
//! key rotation. All encryption follows industry best practices with AES-256-GCM and
//! envelope encryption patterns.

#[cfg(feature = "security-storage")]
pub mod kms_client;

#[cfg(feature = "security-storage")]
pub mod field_encryption;

#[cfg(feature = "security-storage")]
pub mod key_rotation;

#[cfg(feature = "security-storage")]
pub mod cached_encryption;

#[cfg(feature = "security-storage")]
pub mod batch_encryption;

#[cfg(feature = "security-storage")]
pub use kms_client::*;

#[cfg(feature = "security-storage")]
pub use field_encryption::*;

#[cfg(feature = "security-storage")]
pub use key_rotation::*;

// Re-export commonly used types for convenience
#[cfg(feature = "security-storage")]
pub use crate::models::security::{EncryptedField, SecurityError};

/// Encryption service configuration
#[cfg(feature = "security-storage")]
#[derive(Debug, Clone)]
pub struct EncryptionConfig {
    pub project_id: String,
    pub location: String,
    pub key_ring: String,
    pub crypto_key: String,
    pub key_rotation_period_days: u32,
}

#[cfg(feature = "security-storage")]
impl EncryptionConfig {
    /// Create encryption configuration from environment variables
    /// 
    /// # Environment Variables
    /// - `GOOGLE_CLOUD_PROJECT`: Google Cloud project ID
    /// - `GOOGLE_CLOUD_KMS_LOCATION`: KMS location (e.g., "global")
    /// - `GOOGLE_CLOUD_KMS_KEY_RING`: KMS key ring name
    /// - `GOOGLE_CLOUD_KMS_CRYPTO_KEY`: Crypto key name
    /// - `KMS_KEY_ROTATION_PERIOD_DAYS`: Key rotation period (default: 90)
    pub fn from_env() -> Result<Self, SecurityError> {
        use std::env;
        
        let project_id = env::var("GOOGLE_CLOUD_PROJECT")
            .map_err(|_| SecurityError::ConfigurationError("GOOGLE_CLOUD_PROJECT not set".to_string()))?;
        
        let location = env::var("GOOGLE_CLOUD_KMS_LOCATION")
            .map_err(|_| SecurityError::ConfigurationError("GOOGLE_CLOUD_KMS_LOCATION not set".to_string()))?;
        
        let key_ring = env::var("GOOGLE_CLOUD_KMS_KEY_RING")
            .map_err(|_| SecurityError::ConfigurationError("GOOGLE_CLOUD_KMS_KEY_RING not set".to_string()))?;
        
        let crypto_key = env::var("GOOGLE_CLOUD_KMS_CRYPTO_KEY")
            .map_err(|_| SecurityError::ConfigurationError("GOOGLE_CLOUD_KMS_CRYPTO_KEY not set".to_string()))?;
        
        let key_rotation_period_days = env::var("KMS_KEY_ROTATION_PERIOD_DAYS")
            .unwrap_or_else(|_| "90".to_string())
            .parse()
            .map_err(|_| SecurityError::ConfigurationError("Invalid KMS_KEY_ROTATION_PERIOD_DAYS".to_string()))?;
        
        Ok(Self {
            project_id,
            location,
            key_ring,
            crypto_key,
            key_rotation_period_days,
        })
    }
    
    /// Get the full KMS key name for this configuration
    pub fn key_name(&self) -> String {
        format!(
            "projects/{}/locations/{}/keyRings/{}/cryptoKeys/{}",
            self.project_id, self.location, self.key_ring, self.crypto_key
        )
    }
}

/// Main encryption service interface
#[cfg(feature = "security-storage")]
#[async_trait::async_trait]
pub trait EncryptionService: Send + Sync {
    /// Encrypt a field using envelope encryption
    async fn encrypt_field(&self, plaintext: &[u8]) -> Result<EncryptedField, SecurityError>;
    
    /// Decrypt a field using the appropriate key version
    async fn decrypt_field(&self, encrypted_field: &EncryptedField) -> Result<Vec<u8>, SecurityError>;
    
    /// Get the current key version
    async fn get_current_key_version(&self) -> Result<String, SecurityError>;
    
    /// Rotate the encryption key
    async fn rotate_key(&self) -> Result<String, SecurityError>;
    
    /// Verify the encryption service is healthy
    async fn health_check(&self) -> Result<(), SecurityError>;
}

// Placeholder implementations when security-storage feature is disabled
#[cfg(not(feature = "security-storage"))]
pub struct EncryptionConfig;

#[cfg(not(feature = "security-storage"))]
impl EncryptionConfig {
    pub fn from_env() -> Result<Self, String> {
        Err("Security storage feature not enabled".to_string())
    }
}

#[cfg(not(feature = "security-storage"))]
pub trait EncryptionService {}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[cfg(feature = "security-storage")]
    #[test]
    fn test_encryption_config_key_name() {
        let config = EncryptionConfig {
            project_id: "test-project".to_string(),
            location: "global".to_string(),
            key_ring: "test-ring".to_string(),
            crypto_key: "test-key".to_string(),
            key_rotation_period_days: 90,
        };
        
        let expected = "projects/test-project/locations/global/keyRings/test-ring/cryptoKeys/test-key";
        assert_eq!(config.key_name(), expected);
    }
    
    #[cfg(feature = "security-storage")]
    #[test]
    fn test_encryption_config_from_env_missing_vars() {
        // Clear environment variables
        std::env::remove_var("GOOGLE_CLOUD_PROJECT");
        
        let result = EncryptionConfig::from_env();
        assert!(result.is_err());
        
        if let Err(SecurityError::ConfigurationError(msg)) = result {
            assert!(msg.contains("GOOGLE_CLOUD_PROJECT"));
        } else {
            panic!("Expected ConfigurationError");
        }
    }
}