//! Batch encryption optimization for high-throughput scenarios
//! 
//! This module provides optimized batch encryption operations that leverage
//! parallelization and hardware acceleration to maintain performance targets.

use crate::models::security::{SecurityError, EncryptedField};
use crate::storage::encryption::{EncryptionService, FieldEncryptionService};
use anyhow::Result;
use rayon::prelude::*;
use std::sync::Arc;
use tokio::task;
use tracing::{debug, info, warn};

#[cfg(feature = "security-storage")]
use futures::future::join_all;

/// Batch encryption configuration
#[derive(Clone)]
pub struct BatchConfig {
    /// Number of parallel workers for encryption
    pub parallel_workers: usize,
    /// Chunk size for batch processing
    pub chunk_size: usize,
    /// Enable SIMD optimizations
    pub enable_simd: bool,
}

impl Default for BatchConfig {
    fn default() -> Self {
        Self {
            parallel_workers: num_cpus::get(),
            chunk_size: 100,
            enable_simd: true,
        }
    }
}

/// Batch encryption service for optimized throughput
#[cfg(feature = "security-storage")]
pub struct BatchEncryptionService {
    inner: Arc<FieldEncryptionService>,
    config: BatchConfig,
}

#[cfg(feature = "security-storage")]
impl BatchEncryptionService {
    /// Create a new batch encryption service
    pub fn new(inner: Arc<FieldEncryptionService>, config: BatchConfig) -> Self {
        info!(
            "Initializing batch encryption service (workers: {}, chunk_size: {})",
            config.parallel_workers, config.chunk_size
        );
        
        Self { inner, config }
    }
    
    /// Encrypt multiple fields in parallel batches
    /// 
    /// This function optimizes encryption for large numbers of fields by:
    /// 1. Chunking the input into optimal batch sizes
    /// 2. Processing chunks in parallel using thread pool
    /// 3. Leveraging CPU cache locality within chunks
    pub async fn encrypt_fields_batch(
        &self,
        plaintexts: Vec<Vec<u8>>
    ) -> Result<Vec<EncryptedField>, SecurityError> {
        let total_count = plaintexts.len();
        
        if total_count == 0 {
            return Ok(vec![]);
        }
        
        info!("Starting batch encryption of {} fields", total_count);
        let start_time = std::time::Instant::now();
        
        // For small batches, use simple parallel processing
        if total_count <= self.config.chunk_size {
            return self.encrypt_parallel(plaintexts).await;
        }
        
        // For large batches, use chunked parallel processing
        let chunks: Vec<Vec<Vec<u8>>> = plaintexts
            .chunks(self.config.chunk_size)
            .map(|chunk| chunk.to_vec())
            .collect();
        
        debug!("Processing {} chunks of size {}", chunks.len(), self.config.chunk_size);
        
        // Process chunks in parallel
        let handles: Vec<_> = chunks
            .into_iter()
            .map(|chunk| {
                let service = self.inner.clone();
                task::spawn(async move {
                    let mut results = Vec::with_capacity(chunk.len());
                    for plaintext in chunk {
                        match service.encrypt_field(&plaintext).await {
                            Ok(encrypted) => results.push(encrypted),
                            Err(e) => return Err(e),
                        }
                    }
                    Ok(results)
                })
            })
            .collect();
        
        // Collect results
        let chunk_results = join_all(handles).await;
        let mut all_results = Vec::with_capacity(total_count);
        
        for result in chunk_results {
            match result {
                Ok(Ok(encrypted_fields)) => all_results.extend(encrypted_fields),
                Ok(Err(e)) => return Err(e),
                Err(e) => return Err(SecurityError::EncryptionError(
                    format!("Task join error: {}", e)
                )),
            }
        }
        
        let elapsed = start_time.elapsed();
        let throughput_mb = (plaintexts.iter().map(|p| p.len()).sum::<usize>() as f64) 
            / 1_048_576.0 / elapsed.as_secs_f64();
        
        info!(
            "Batch encryption completed: {} fields in {:?} ({:.2} MB/s)",
            total_count, elapsed, throughput_mb
        );
        
        Ok(all_results)
    }
    
    /// Decrypt multiple fields in parallel batches
    pub async fn decrypt_fields_batch(
        &self,
        encrypted_fields: Vec<EncryptedField>
    ) -> Result<Vec<Vec<u8>>, SecurityError> {
        let total_count = encrypted_fields.len();
        
        if total_count == 0 {
            return Ok(vec![]);
        }
        
        info!("Starting batch decryption of {} fields", total_count);
        let start_time = std::time::Instant::now();
        
        // Process in parallel using tokio tasks
        let handles: Vec<_> = encrypted_fields
            .into_iter()
            .map(|field| {
                let service = self.inner.clone();
                task::spawn(async move {
                    service.decrypt_field(&field).await
                })
            })
            .collect();
        
        // Collect results
        let results = join_all(handles).await;
        let mut decrypted = Vec::with_capacity(total_count);
        
        for result in results {
            match result {
                Ok(Ok(plaintext)) => decrypted.push(plaintext),
                Ok(Err(e)) => return Err(e),
                Err(e) => return Err(SecurityError::DecryptionError(
                    format!("Task join error: {}", e)
                )),
            }
        }
        
        let elapsed = start_time.elapsed();
        info!(
            "Batch decryption completed: {} fields in {:?}",
            total_count, elapsed
        );
        
        Ok(decrypted)
    }
    
    /// Encrypt fields in parallel without chunking (for smaller batches)
    async fn encrypt_parallel(
        &self,
        plaintexts: Vec<Vec<u8>>
    ) -> Result<Vec<EncryptedField>, SecurityError> {
        let handles: Vec<_> = plaintexts
            .into_iter()
            .map(|plaintext| {
                let service = self.inner.clone();
                task::spawn(async move {
                    service.encrypt_field(&plaintext).await
                })
            })
            .collect();
        
        let results = join_all(handles).await;
        let mut encrypted = Vec::with_capacity(results.len());
        
        for result in results {
            match result {
                Ok(Ok(field)) => encrypted.push(field),
                Ok(Err(e)) => return Err(e),
                Err(e) => return Err(SecurityError::EncryptionError(
                    format!("Task join error: {}", e)
                )),
            }
        }
        
        Ok(encrypted)
    }
    
    /// Optimized encryption for homogeneous data (same size fields)
    /// 
    /// This function provides additional optimizations when all fields
    /// are the same size, allowing for better memory allocation and
    /// cache utilization.
    pub async fn encrypt_uniform_batch(
        &self,
        plaintexts: Vec<Vec<u8>>,
        field_size: usize
    ) -> Result<Vec<EncryptedField>, SecurityError> {
        if plaintexts.is_empty() {
            return Ok(vec![]);
        }
        
        // Verify all fields are the expected size
        if !plaintexts.iter().all(|p| p.len() == field_size) {
            return Err(SecurityError::InvalidInput(
                "Not all fields match the specified size".to_string()
            ));
        }
        
        debug!(
            "Optimized batch encryption for {} uniform fields of size {}",
            plaintexts.len(), field_size
        );
        
        // Use Rayon for CPU-bound parallel processing
        let service = self.inner.clone();
        let runtime = tokio::runtime::Handle::current();
        
        task::spawn_blocking(move || {
            plaintexts
                .par_iter()
                .map(|plaintext| {
                    runtime.block_on(async {
                        service.encrypt_field(plaintext).await
                    })
                })
                .collect::<Result<Vec<_>, _>>()
        })
        .await
        .map_err(|e| SecurityError::EncryptionError(format!("Blocking task error: {}", e)))?
    }
}

/// Streaming encryption for very large datasets
#[cfg(feature = "security-storage")]
pub struct StreamingEncryption {
    service: Arc<FieldEncryptionService>,
    buffer_size: usize,
}

#[cfg(feature = "security-storage")]
impl StreamingEncryption {
    pub fn new(service: Arc<FieldEncryptionService>, buffer_size: usize) -> Self {
        Self {
            service,
            buffer_size,
        }
    }
    
    /// Process a stream of fields with buffering for optimal performance
    pub async fn process_stream<I>(
        &self,
        stream: I
    ) -> Result<Vec<EncryptedField>, SecurityError>
    where
        I: Iterator<Item = Vec<u8>>,
    {
        let mut results = Vec::new();
        let mut buffer = Vec::with_capacity(self.buffer_size);
        
        for item in stream {
            buffer.push(item);
            
            if buffer.len() >= self.buffer_size {
                let batch = std::mem::take(&mut buffer);
                let encrypted = self.encrypt_buffer(batch).await?;
                results.extend(encrypted);
            }
        }
        
        // Process remaining items
        if !buffer.is_empty() {
            let encrypted = self.encrypt_buffer(buffer).await?;
            results.extend(encrypted);
        }
        
        Ok(results)
    }
    
    async fn encrypt_buffer(
        &self,
        buffer: Vec<Vec<u8>>
    ) -> Result<Vec<EncryptedField>, SecurityError> {
        let batch_service = BatchEncryptionService::new(
            self.service.clone(),
            BatchConfig::default()
        );
        
        batch_service.encrypt_fields_batch(buffer).await
    }
}

// Placeholder for when feature is disabled
#[cfg(not(feature = "security-storage"))]
pub struct BatchEncryptionService;

#[cfg(not(feature = "security-storage"))]
impl BatchEncryptionService {
    pub fn new(_inner: Arc<FieldEncryptionService>, _config: BatchConfig) -> Self {
        Self
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[cfg(feature = "security-storage")]
    #[tokio::test]
    async fn test_batch_encryption_empty() {
        use crate::storage::encryption::GoogleCloudKmsService;
        
        // Mock KMS
        struct MockKms;
        
        #[async_trait::async_trait]
        impl GoogleCloudKmsService for MockKms {
            async fn encrypt_with_kms(&self, plaintext: &[u8]) -> Result<Vec<u8>, SecurityError> {
                Ok(plaintext.to_vec())
            }
            
            async fn decrypt_with_kms(&self, ciphertext: &[u8], _: Option<&str>) -> Result<Vec<u8>, SecurityError> {
                Ok(ciphertext.to_vec())
            }
            
            async fn get_current_key_version(&self) -> Result<String, SecurityError> {
                Ok("1".to_string())
            }
            
            async fn create_key_version(&self) -> Result<String, SecurityError> {
                Ok("2".to_string())
            }
        }
        
        let kms = Arc::new(MockKms);
        let field_service = Arc::new(FieldEncryptionService::new(kms));
        let batch_service = BatchEncryptionService::new(field_service, BatchConfig::default());
        
        let result = batch_service.encrypt_fields_batch(vec![]).await;
        assert!(result.is_ok());
        assert_eq!(result.unwrap().len(), 0);
    }
    
    #[test]
    fn test_batch_config_defaults() {
        let config = BatchConfig::default();
        assert!(config.parallel_workers > 0);
        assert_eq!(config.chunk_size, 100);
        assert!(config.enable_simd);
    }
}