//! Field-level encryption implementation
//! 
//! This module implements envelope encryption for sensitive data fields using
//! AES-256-GCM for data encryption and Google Cloud KMS for key encryption.
//! It provides secure, performant field-level encryption suitable for database storage.

use crate::models::security::{SecurityError, EncryptedField};
use crate::storage::encryption::{EncryptionService, GoogleCloudKmsService};
use anyhow::{Context, Result};
use chrono::Utc;
use std::sync::Arc;
use tracing::{debug, error, info, warn};

#[cfg(feature = "security-storage")]
use aes_gcm::{
    aead::{Aead, AeadCore, KeyInit, OsRng},
    Aes256Gcm, Key, Nonce,
};

#[cfg(feature = "security-storage")]
use ring::rand::{SecureRandom, SystemRandom};

#[cfg(feature = "security-storage")]
use zeroize::{Zeroize, ZeroizeOnDrop};

/// Data Encryption Key (DEK) for envelope encryption
#[cfg(feature = "security-storage")]
#[derive(ZeroizeOnDrop)]
struct DataEncryptionKey {
    key: [u8; 32], // 256-bit key for AES-256-GCM
}

#[cfg(feature = "security-storage")]
impl DataEncryptionKey {
    /// Generate a new random DEK
    fn generate() -> Result<Self, SecurityError> {
        let mut key = [0u8; 32];
        let rng = SystemRandom::new();
        
        rng.fill(&mut key)
            .map_err(|e| SecurityError::KeyGenerationError(format!("Failed to generate DEK: {:?}", e)))?;
        
        Ok(Self { key })
    }
    
    /// Get the key bytes (be careful with this!)
    fn as_bytes(&self) -> &[u8] {
        &self.key
    }
}

/// Field encryption service implementation
#[cfg(feature = "security-storage")]
pub struct FieldEncryptionService {
    kms_service: Arc<GoogleCloudKmsService>,
}

#[cfg(feature = "security-storage")]
impl FieldEncryptionService {
    /// Create a new field encryption service
    /// 
    /// # Arguments
    /// * `kms_service` - KMS service for key encryption/decryption
    /// 
    /// # Returns
    /// * New field encryption service instance
    pub fn new(kms_service: Arc<GoogleCloudKmsService>) -> Self {
        info!("Initializing field encryption service");
        Self { kms_service }
    }
    
    /// Generate a new data encryption key (DEK)
    /// 
    /// # Returns
    /// * `Result<DataEncryptionKey, SecurityError>` - New DEK or error
    async fn generate_dek(&self) -> Result<DataEncryptionKey, SecurityError> {
        debug!("Generating new data encryption key");
        DataEncryptionKey::generate()
    }
    
    /// Encrypt data with a DEK using AES-256-GCM
    /// 
    /// # Arguments
    /// * `plaintext` - Data to encrypt
    /// * `dek` - Data encryption key
    /// 
    /// # Returns
    /// * `Result<Vec<u8>, SecurityError>` - Encrypted data (nonce + ciphertext) or error
    /// 
    /// # Security Notes
    /// * Uses AES-256-GCM for authenticated encryption
    /// * Generates random nonce for each encryption
    /// * Includes authentication tag for integrity verification
    fn encrypt_with_dek(&self, plaintext: &[u8], dek: &DataEncryptionKey) -> Result<Vec<u8>, SecurityError> {
        if plaintext.is_empty() {
            return Err(SecurityError::InvalidInput("Cannot encrypt empty data".to_string()));
        }
        
        debug!("Encrypting {} bytes with DEK", plaintext.len());
        
        let key = Key::<Aes256Gcm>::from_slice(dek.as_bytes());
        let cipher = Aes256Gcm::new(key);
        
        // Generate random nonce
        let nonce = Aes256Gcm::generate_nonce(&mut OsRng);
        
        // Encrypt the data
        let ciphertext = cipher
            .encrypt(&nonce, plaintext)
            .map_err(|e| {
                error!("AES-256-GCM encryption failed: {:?}", e);
                SecurityError::EncryptionError(format!("AES encryption failed: {}", e))
            })?;
        
        // Prepend nonce to ciphertext for storage
        let mut result = Vec::with_capacity(12 + ciphertext.len());
        result.extend_from_slice(&nonce);
        result.extend_from_slice(&ciphertext);
        
        debug!("Successfully encrypted data with DEK (result size: {} bytes)", result.len());
        Ok(result)
    }
    
    /// Decrypt data with a DEK using AES-256-GCM
    /// 
    /// # Arguments
    /// * `encrypted_data` - Encrypted data (nonce + ciphertext)
    /// * `dek` - Data encryption key
    /// 
    /// # Returns
    /// * `Result<Vec<u8>, SecurityError>` - Decrypted data or error
    /// 
    /// # Security Notes
    /// * Extracts nonce from the beginning of encrypted data
    /// * Verifies authentication tag during decryption
    /// * Fails securely if authentication fails
    fn decrypt_with_dek(&self, encrypted_data: &[u8], dek: &DataEncryptionKey) -> Result<Vec<u8>, SecurityError> {
        if encrypted_data.len() < 12 {
            return Err(SecurityError::InvalidInput("Encrypted data too short (missing nonce)".to_string()));
        }
        
        debug!("Decrypting {} bytes with DEK", encrypted_data.len());
        
        let key = Key::<Aes256Gcm>::from_slice(dek.as_bytes());
        let cipher = Aes256Gcm::new(key);
        
        // Extract nonce and ciphertext
        let (nonce_bytes, ciphertext) = encrypted_data.split_at(12);
        let nonce = Nonce::from_slice(nonce_bytes);
        
        // Decrypt the data
        let plaintext = cipher
            .decrypt(nonce, ciphertext)
            .map_err(|e| {
                error!("AES-256-GCM decryption failed: {:?}", e);
                SecurityError::DecryptionError(format!("AES decryption failed: {}", e))
            })?;
        
        debug!("Successfully decrypted data with DEK (result size: {} bytes)", plaintext.len());
        Ok(plaintext)
    }
    
    /// Encrypt a DEK using KMS
    /// 
    /// # Arguments
    /// * `dek` - Data encryption key to encrypt
    /// 
    /// # Returns
    /// * `Result<Vec<u8>, SecurityError>` - Encrypted DEK or error
    async fn encrypt_dek_with_kms(&self, dek: &DataEncryptionKey) -> Result<Vec<u8>, SecurityError> {
        debug!("Encrypting DEK with KMS");
        self.kms_service.encrypt_with_kms(dek.as_bytes()).await
    }
    
    /// Decrypt a DEK using KMS
    /// 
    /// # Arguments
    /// * `encrypted_dek` - Encrypted DEK bytes
    /// * `key_version` - Optional specific key version to use
    /// 
    /// # Returns
    /// * `Result<DataEncryptionKey, SecurityError>` - Decrypted DEK or error
    async fn decrypt_dek_with_kms(&self, encrypted_dek: &[u8], key_version: Option<&str>) -> Result<DataEncryptionKey, SecurityError> {
        debug!("Decrypting DEK with KMS");
        
        let dek_bytes = self.kms_service.decrypt_with_kms(encrypted_dek, key_version).await?;
        
        if dek_bytes.len() != 32 {
            return Err(SecurityError::InvalidInput(format!("Invalid DEK length: expected 32, got {}", dek_bytes.len())));
        }
        
        let mut key = [0u8; 32];
        key.copy_from_slice(&dek_bytes);
        
        Ok(DataEncryptionKey { key })
    }
}

#[cfg(feature = "security-storage")]
#[async_trait::async_trait]
impl EncryptionService for FieldEncryptionService {
    /// Encrypt a field using envelope encryption
    /// 
    /// # Arguments
    /// * `plaintext` - Data to encrypt
    /// 
    /// # Returns
    /// * `Result<EncryptedField, SecurityError>` - Encrypted field with metadata or error
    /// 
    /// # Implementation
    /// 1. Generate a random Data Encryption Key (DEK)
    /// 2. Encrypt the plaintext with the DEK using AES-256-GCM
    /// 3. Encrypt the DEK with Google Cloud KMS
    /// 4. Store encrypted DEK + encrypted data together
    async fn encrypt_field(&self, plaintext: &[u8]) -> Result<EncryptedField, SecurityError> {
        if plaintext.is_empty() {
            return Err(SecurityError::InvalidInput("Cannot encrypt empty field".to_string()));
        }
        
        info!("Encrypting field with envelope encryption ({} bytes)", plaintext.len());
        
        // Step 1: Generate DEK
        let dek = self.generate_dek().await?;
        
        // Step 2: Encrypt data with DEK
        let encrypted_data = self.encrypt_with_dek(plaintext, &dek)?;
        
        // Step 3: Encrypt DEK with KMS
        let encrypted_dek = self.encrypt_dek_with_kms(&dek).await?;
        
        // Step 4: Get current key version for metadata
        let key_version = self.kms_service.get_current_key_version().await?;
        
        // Step 5: Combine encrypted DEK and encrypted data
        let mut combined_data = Vec::with_capacity(4 + encrypted_dek.len() + encrypted_data.len());
        combined_data.extend_from_slice(&(encrypted_dek.len() as u32).to_be_bytes());
        combined_data.extend_from_slice(&encrypted_dek);
        combined_data.extend_from_slice(&encrypted_data);
        
        let encrypted_field = EncryptedField {
            encrypted_data: combined_data,
            key_version,
            encryption_algorithm: "envelope:aes256gcm+kms".to_string(),
            created_at: Utc::now(),
        };
        
        info!("Successfully encrypted field (total size: {} bytes)", encrypted_field.encrypted_data.len());
        Ok(encrypted_field)
    }
    
    /// Decrypt a field using envelope encryption
    /// 
    /// # Arguments
    /// * `encrypted_field` - Encrypted field with metadata
    /// 
    /// # Returns
    /// * `Result<Vec<u8>, SecurityError>` - Decrypted data or error
    /// 
    /// # Implementation
    /// 1. Extract encrypted DEK and encrypted data
    /// 2. Decrypt the DEK with Google Cloud KMS using the stored key version
    /// 3. Decrypt the data with the DEK using AES-256-GCM
    async fn decrypt_field(&self, encrypted_field: &EncryptedField) -> Result<Vec<u8>, SecurityError> {
        if encrypted_field.encrypted_data.len() < 4 {
            return Err(SecurityError::InvalidInput("Encrypted field data too short".to_string()));
        }
        
        info!("Decrypting field with envelope encryption (key version: {})", encrypted_field.key_version);
        
        // Step 1: Extract encrypted DEK and encrypted data
        let data = &encrypted_field.encrypted_data;
        let dek_length = u32::from_be_bytes([data[0], data[1], data[2], data[3]]) as usize;
        
        if data.len() < 4 + dek_length {
            return Err(SecurityError::InvalidInput("Invalid encrypted field format".to_string()));
        }
        
        let encrypted_dek = &data[4..4 + dek_length];
        let encrypted_data = &data[4 + dek_length..];
        
        // Step 2: Decrypt DEK with KMS
        let dek = self.decrypt_dek_with_kms(encrypted_dek, Some(&encrypted_field.key_version)).await?;
        
        // Step 3: Decrypt data with DEK
        let plaintext = self.decrypt_with_dek(encrypted_data, &dek)?;
        
        info!("Successfully decrypted field ({} bytes)", plaintext.len());
        Ok(plaintext)
    }
    
    /// Get the current key version
    async fn get_current_key_version(&self) -> Result<String, SecurityError> {
        self.kms_service.get_current_key_version().await
    }
    
    /// Rotate the encryption key
    async fn rotate_key(&self) -> Result<String, SecurityError> {
        info!("Initiating key rotation");
        self.kms_service.create_key_version().await
    }
    
    /// Verify the encryption service is healthy
    async fn health_check(&self) -> Result<(), SecurityError> {
        debug!("Performing field encryption health check");
        
        // Test full envelope encryption/decryption cycle
        let test_data = b"field_encryption_health_check";
        let encrypted_field = self.encrypt_field(test_data).await?;
        let decrypted_data = self.decrypt_field(&encrypted_field).await?;
        
        if decrypted_data != test_data {
            error!("Field encryption health check failed: decrypted data doesn't match original");
            return Err(SecurityError::HealthCheckError("Field encrypt/decrypt test failed".to_string()));
        }
        
        info!("Field encryption health check passed");
        Ok(())
    }
}

// Placeholder implementation when security-storage feature is disabled
#[cfg(not(feature = "security-storage"))]
pub struct FieldEncryptionService;

#[cfg(not(feature = "security-storage"))]
impl FieldEncryptionService {
    pub fn new(_kms_service: Arc<GoogleCloudKmsService>) -> Self {
        Self
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[cfg(feature = "security-storage")]
    #[test]
    fn test_dek_generation() {
        let dek = DataEncryptionKey::generate().unwrap();
        assert_eq!(dek.as_bytes().len(), 32);
        
        // Generate another DEK and ensure they're different
        let dek2 = DataEncryptionKey::generate().unwrap();
        assert_ne!(dek.as_bytes(), dek2.as_bytes());
    }
    
    #[cfg(feature = "security-storage")]
    #[test]
    fn test_aes_encryption_roundtrip() {
        // This is a unit test for the AES encryption logic without KMS
        let dek = DataEncryptionKey::generate().unwrap();
        let plaintext = b"test data for encryption";
        
        let key = aes_gcm::Key::<aes_gcm::Aes256Gcm>::from_slice(dek.as_bytes());
        let cipher = aes_gcm::Aes256Gcm::new(key);
        let nonce = aes_gcm::Aes256Gcm::generate_nonce(&mut aes_gcm::aead::OsRng);
        
        let ciphertext = cipher.encrypt(&nonce, plaintext.as_ref()).unwrap();
        let decrypted = cipher.decrypt(&nonce, ciphertext.as_ref()).unwrap();
        
        assert_eq!(decrypted, plaintext);
    }
    
    #[test]
    fn test_empty_data_handling() {
        let empty_data: &[u8] = &[];
        assert!(empty_data.is_empty());
        
        // Test that our validation logic would catch this
        let result = "Cannot encrypt empty field";
        assert!(result.contains("empty"));
    }
    
    #[test]
    fn test_encrypted_field_format() {
        // Test the format we use for storing encrypted fields
        let dek_len = 256u32;
        let dek_len_bytes = dek_len.to_be_bytes();
        assert_eq!(dek_len_bytes.len(), 4);
        assert_eq!(u32::from_be_bytes(dek_len_bytes), 256);
    }
}