//! Role-Based Access Control (RBAC) implementation
//! 
//! This module provides role management, user-role assignments, and role-based
//! permission evaluation for the access control system.

use crate::models::security::{Permission, PermissionEffect, SecurityError};
use anyhow::Result;
use chrono::{DateTime, Utc};
use std::collections::{HashMap, HashSet};
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{debug, error, info, warn};

/// RBAC manager for role assignments and role definitions
#[cfg(feature = "security-storage")]
pub struct RbacManager {
    /// User to roles mapping
    user_roles: Arc<RwLock<HashMap<String, HashSet<String>>>>,
    /// Role definitions with permissions
    role_definitions: Arc<RwLock<HashMap<String, RoleDefinition>>>,
    /// Role hierarchy (parent -> children)
    role_hierarchy: Arc<RwLock<HashMap<String, HashSet<String>>>>,
    /// Performance and usage statistics
    statistics: Arc<RwLock<RbacStatistics>>,
}

/// Definition of a role with permissions
#[cfg(feature = "security-storage")]
#[derive(Debug, Clone)]
pub struct RoleDefinition {
    /// Role name/identifier
    pub name: String,
    /// Human-readable description
    pub description: String,
    /// Direct permissions granted by this role
    pub permissions: Vec<Permission>,
    /// Parent roles (for role inheritance)
    pub parent_roles: HashSet<String>,
    /// When this role was created
    pub created_at: DateTime<Utc>,
    /// When this role was last updated
    pub updated_at: DateTime<Utc>,
    /// Whether this role is active
    pub is_active: bool,
}

/// Statistics for RBAC system
#[cfg(feature = "security-storage")]
#[derive(Debug, Default, Clone)]
pub struct RbacStatistics {
    /// Total number of users with role assignments
    pub total_users: usize,
    /// Total number of defined roles
    pub total_roles: usize,
    /// Total role assignments across all users
    pub total_role_assignments: u64,
    /// Number of role assignments performed
    pub role_assignments: u64,
    /// Number of role removals performed
    pub role_removals: u64,
    /// Average number of roles per user
    pub average_roles_per_user: f64,
    /// Most common roles (role name -> assignment count)
    pub popular_roles: HashMap<String, u64>,
}

#[cfg(feature = "security-storage")]
impl RbacManager {
    /// Create a new RBAC manager
    /// 
    /// # Returns
    /// * New RBAC manager instance
    pub async fn new() -> Result<Self, SecurityError> {
        info!("Initializing RBAC manager");
        
        let manager = Self {
            user_roles: Arc::new(RwLock::new(HashMap::new())),
            role_definitions: Arc::new(RwLock::new(HashMap::new())),
            role_hierarchy: Arc::new(RwLock::new(HashMap::new())),
            statistics: Arc::new(RwLock::new(RbacStatistics::default())),
        };
        
        // Initialize default roles
        manager.initialize_default_roles().await?;
        
        info!("RBAC manager initialized successfully");
        Ok(manager)
    }
    
    /// Define a new role
    /// 
    /// # Arguments
    /// * `role_definition` - Definition of the role to create
    /// 
    /// # Returns
    /// * `Result<(), SecurityError>` - Success or error
    pub async fn define_role(&self, role_definition: RoleDefinition) -> Result<(), SecurityError> {
        info!("Defining role: {} - {}", role_definition.name, role_definition.description);
        
        // Validate role definition
        self.validate_role_definition(&role_definition)?;
        
        // Check for circular dependencies in role hierarchy
        if !role_definition.parent_roles.is_empty() {
            self.validate_role_hierarchy(&role_definition.name, &role_definition.parent_roles).await?;
        }
        
        // Store role definition
        {
            let mut roles = self.role_definitions.write().await;
            roles.insert(role_definition.name.clone(), role_definition.clone());
        }
        
        // Update role hierarchy
        if !role_definition.parent_roles.is_empty() {
            let mut hierarchy = self.role_hierarchy.write().await;
            for parent_role in &role_definition.parent_roles {
                hierarchy.entry(parent_role.clone())
                    .or_insert_with(HashSet::new)
                    .insert(role_definition.name.clone());
            }
        }
        
        // Update statistics
        {
            let mut stats = self.statistics.write().await;
            stats.total_roles = {
                let roles = self.role_definitions.read().await;
                roles.len()
            };
        }
        
        info!("Role defined successfully: {}", role_definition.name);
        Ok(())
    }
    
    /// Remove a role definition
    /// 
    /// # Arguments
    /// * `role_name` - Name of the role to remove
    /// 
    /// # Returns
    /// * `Result<(), SecurityError>` - Success or error
    pub async fn remove_role_definition(&self, role_name: &str) -> Result<(), SecurityError> {
        info!("Removing role definition: {}", role_name);
        
        // Check if role is still assigned to users
        let user_count = self.count_users_with_role(role_name).await?;
        if user_count > 0 {
            return Err(SecurityError::InvalidInput(
                format!("Cannot remove role '{}': still assigned to {} users", role_name, user_count)
            ));
        }
        
        // Remove role definition
        let removed = {
            let mut roles = self.role_definitions.write().await;
            roles.remove(role_name).is_some()
        };
        
        if !removed {
            return Err(SecurityError::InvalidInput(format!("Role not found: {}", role_name)));
        }
        
        // Clean up role hierarchy
        {
            let mut hierarchy = self.role_hierarchy.write().await;
            hierarchy.remove(role_name);
            for children in hierarchy.values_mut() {
                children.remove(role_name);
            }
        }
        
        // Update statistics
        {
            let mut stats = self.statistics.write().await;
            stats.total_roles = {
                let roles = self.role_definitions.read().await;
                roles.len()
            };
        }
        
        info!("Role definition removed successfully: {}", role_name);
        Ok(())
    }
    
    /// Assign a role to a user
    /// 
    /// # Arguments
    /// * `user_id` - User ID
    /// * `role_name` - Role to assign
    /// 
    /// # Returns
    /// * `Result<(), SecurityError>` - Success or error
    pub async fn assign_role(&self, user_id: &str, role_name: &str) -> Result<(), SecurityError> {
        debug!("Assigning role '{}' to user '{}'", role_name, user_id);
        
        // Validate that role exists
        {
            let roles = self.role_definitions.read().await;
            if !roles.contains_key(role_name) {
                return Err(SecurityError::InvalidInput(format!("Role not found: {}", role_name)));
            }
            
            // Check if role is active
            if let Some(role_def) = roles.get(role_name) {
                if !role_def.is_active {
                    return Err(SecurityError::InvalidInput(format!("Role is inactive: {}", role_name)));
                }
            }
        }
        
        // Assign role to user
        let was_new_assignment = {
            let mut user_roles = self.user_roles.write().await;
            let user_role_set = user_roles.entry(user_id.to_string()).or_insert_with(HashSet::new);
            user_role_set.insert(role_name.to_string())
        };
        
        if !was_new_assignment {
            debug!("Role '{}' already assigned to user '{}'", role_name, user_id);
            return Ok(());
        }
        
        // Update statistics
        {
            let mut stats = self.statistics.write().await;
            stats.role_assignments += 1;
            stats.total_role_assignments += 1;
            
            // Update popular roles
            *stats.popular_roles.entry(role_name.to_string()).or_insert(0) += 1;
            
            // Update user count and average roles per user
            let user_count = {
                let user_roles = self.user_roles.read().await;
                user_roles.len()
            };
            stats.total_users = user_count;
            
            if user_count > 0 {
                stats.average_roles_per_user = stats.total_role_assignments as f64 / user_count as f64;
            }
        }
        
        info!("Role '{}' assigned to user '{}' successfully", role_name, user_id);
        Ok(())
    }
    
    /// Remove a role from a user
    /// 
    /// # Arguments
    /// * `user_id` - User ID
    /// * `role_name` - Role to remove
    /// 
    /// # Returns
    /// * `Result<(), SecurityError>` - Success or error
    pub async fn remove_role(&self, user_id: &str, role_name: &str) -> Result<(), SecurityError> {
        debug!("Removing role '{}' from user '{}'", role_name, user_id);
        
        let was_removed = {
            let mut user_roles = self.user_roles.write().await;
            if let Some(user_role_set) = user_roles.get_mut(user_id) {
                let removed = user_role_set.remove(role_name);
                
                // Clean up empty user entries
                if user_role_set.is_empty() {
                    user_roles.remove(user_id);
                }
                
                removed
            } else {
                false
            }
        };
        
        if !was_removed {
            return Err(SecurityError::InvalidInput(
                format!("User '{}' does not have role '{}'", user_id, role_name)
            ));
        }
        
        // Update statistics
        {
            let mut stats = self.statistics.write().await;
            stats.role_removals += 1;
            stats.total_role_assignments = stats.total_role_assignments.saturating_sub(1);
            
            // Update popular roles
            if let Some(count) = stats.popular_roles.get_mut(role_name) {
                *count = count.saturating_sub(1);
                if *count == 0 {
                    stats.popular_roles.remove(role_name);
                }
            }
            
            // Update user count and average roles per user
            let user_count = {
                let user_roles = self.user_roles.read().await;
                user_roles.len()
            };
            stats.total_users = user_count;
            
            if user_count > 0 {
                stats.average_roles_per_user = stats.total_role_assignments as f64 / user_count as f64;
            } else {
                stats.average_roles_per_user = 0.0;
            }
        }
        
        info!("Role '{}' removed from user '{}' successfully", role_name, user_id);
        Ok(())
    }
    
    /// Get all roles for a user
    /// 
    /// # Arguments
    /// * `user_id` - User ID
    /// 
    /// # Returns
    /// * `Result<Vec<String>, SecurityError>` - List of roles or error
    pub async fn get_user_roles(&self, user_id: &str) -> Result<Vec<String>, SecurityError> {
        let user_roles = self.user_roles.read().await;
        
        if let Some(role_set) = user_roles.get(user_id) {
            // Get direct roles
            let mut all_roles = role_set.clone();
            
            // Add inherited roles from role hierarchy
            for role_name in role_set.iter() {
                self.collect_inherited_roles(role_name, &mut all_roles).await?;
            }
            
            let roles_vec: Vec<String> = all_roles.into_iter().collect();
            debug!("User '{}' has roles: {:?}", user_id, roles_vec);
            Ok(roles_vec)
        } else {
            debug!("User '{}' has no roles assigned", user_id);
            Ok(Vec::new())
        }
    }
    
    /// Get all effective permissions for a user (combining all roles)
    /// 
    /// # Arguments
    /// * `user_id` - User ID
    /// 
    /// # Returns
    /// * `Result<Vec<Permission>, SecurityError>` - List of permissions or error
    pub async fn get_user_permissions(&self, user_id: &str) -> Result<Vec<Permission>, SecurityError> {
        let roles = self.get_user_roles(user_id).await?;
        let role_definitions = self.role_definitions.read().await;
        
        let mut all_permissions = Vec::new();
        let mut seen_permissions = HashSet::new();
        
        for role_name in &roles {
            if let Some(role_def) = role_definitions.get(role_name) {
                if !role_def.is_active {
                    continue; // Skip inactive roles
                }
                
                for permission in &role_def.permissions {
                    // Create a unique key for deduplication
                    let perm_key = format!("{}:{}:{:?}", permission.action, permission.resource, permission.effect);
                    
                    if seen_permissions.insert(perm_key) {
                        all_permissions.push(permission.clone());
                    }
                }
            }
        }
        
        debug!("User '{}' has {} effective permissions from {} roles", 
               user_id, all_permissions.len(), roles.len());
        Ok(all_permissions)
    }
    
    /// Get RBAC statistics
    /// 
    /// # Returns
    /// * `Result<RbacStatistics, SecurityError>` - Statistics or error
    pub async fn get_statistics(&self) -> Result<RbacStatistics, SecurityError> {
        let stats = self.statistics.read().await;
        Ok(stats.clone())
    }
    
    /// Perform health check on RBAC manager
    /// 
    /// # Returns
    /// * `Result<(), SecurityError>` - Success or health check error
    pub async fn health_check(&self) -> Result<(), SecurityError> {
        debug!("Performing RBAC manager health check");
        
        // Test role assignment and retrieval
        let test_user = "rbac-health-check-user";
        let test_role = "user"; // Should exist from default roles
        
        // Check if test role exists
        {
            let roles = self.role_definitions.read().await;
            if !roles.contains_key(test_role) {
                return Err(SecurityError::HealthCheckError(
                    format!("Default role '{}' not found during health check", test_role)
                ));
            }
        }
        
        // Test role assignment
        self.assign_role(test_user, test_role).await?;
        
        // Test role retrieval
        let user_roles = self.get_user_roles(test_user).await?;
        if !user_roles.contains(&test_role.to_string()) {
            return Err(SecurityError::HealthCheckError(
                "Role assignment/retrieval test failed".to_string()
            ));
        }
        
        // Clean up
        self.remove_role(test_user, test_role).await?;
        
        info!("RBAC manager health check passed");
        Ok(())
    }
    
    /// Initialize default roles
    async fn initialize_default_roles(&self) -> Result<(), SecurityError> {
        debug!("Initializing default RBAC roles");
        
        // Admin role - full access
        let admin_role = RoleDefinition {
            name: "admin".to_string(),
            description: "Full system administrator access".to_string(),
            permissions: vec![
                Permission {
                    action: "*".to_string(),
                    resource: "*".to_string(),
                    effect: PermissionEffect::Allow,
                },
            ],
            parent_roles: HashSet::new(),
            created_at: Utc::now(),
            updated_at: Utc::now(),
            is_active: true,
        };
        
        // User role - basic user access
        let user_role = RoleDefinition {
            name: "user".to_string(),
            description: "Standard user access to own resources".to_string(),
            permissions: vec![
                Permission {
                    action: "read".to_string(),
                    resource: "user_data:own".to_string(),
                    effect: PermissionEffect::Allow,
                },
                Permission {
                    action: "update".to_string(),
                    resource: "user_data:own".to_string(),
                    effect: PermissionEffect::Allow,
                },
                Permission {
                    action: "read".to_string(),
                    resource: "public_data".to_string(),
                    effect: PermissionEffect::Allow,
                },
            ],
            parent_roles: HashSet::new(),
            created_at: Utc::now(),
            updated_at: Utc::now(),
            is_active: true,
        };
        
        // Analyst role - read access to analysis data
        let analyst_role = RoleDefinition {
            name: "analyst".to_string(),
            description: "Read access to analysis data and reports".to_string(),
            permissions: vec![
                Permission {
                    action: "read".to_string(),
                    resource: "analysis_data".to_string(),
                    effect: PermissionEffect::Allow,
                },
                Permission {
                    action: "read".to_string(),
                    resource: "reports".to_string(),
                    effect: PermissionEffect::Allow,
                },
                Permission {
                    action: "create".to_string(),
                    resource: "analysis_requests".to_string(),
                    effect: PermissionEffect::Allow,
                },
            ],
            parent_roles: {
                let mut parents = HashSet::new();
                parents.insert("user".to_string()); // Inherits from user role
                parents
            },
            created_at: Utc::now(),
            updated_at: Utc::now(),
            is_active: true,
        };
        
        // Guest role - minimal read-only access
        let guest_role = RoleDefinition {
            name: "guest".to_string(),
            description: "Read-only access to public resources".to_string(),
            permissions: vec![
                Permission {
                    action: "read".to_string(),
                    resource: "public_data".to_string(),
                    effect: PermissionEffect::Allow,
                },
            ],
            parent_roles: HashSet::new(),
            created_at: Utc::now(),
            updated_at: Utc::now(),
            is_active: true,
        };
        
        self.define_role(admin_role).await?;
        self.define_role(user_role).await?;
        self.define_role(analyst_role).await?;
        self.define_role(guest_role).await?;
        
        info!("Default RBAC roles initialized");
        Ok(())
    }
    
    /// Validate role definition
    fn validate_role_definition(&self, role_def: &RoleDefinition) -> Result<(), SecurityError> {
        if role_def.name.is_empty() {
            return Err(SecurityError::InvalidInput("Role name cannot be empty".to_string()));
        }
        
        if role_def.name.contains(' ') || role_def.name.contains(':') {
            return Err(SecurityError::InvalidInput(
                "Role name cannot contain spaces or colons".to_string()
            ));
        }
        
        if role_def.permissions.is_empty() {
            return Err(SecurityError::InvalidInput(
                "Role must have at least one permission".to_string()
            ));
        }
        
        // Validate permissions
        for permission in &role_def.permissions {
            if permission.action.is_empty() {
                return Err(SecurityError::InvalidInput("Permission action cannot be empty".to_string()));
            }
            if permission.resource.is_empty() {
                return Err(SecurityError::InvalidInput("Permission resource cannot be empty".to_string()));
            }
        }
        
        Ok(())
    }
    
    /// Validate role hierarchy to prevent circular dependencies
    async fn validate_role_hierarchy(&self, role_name: &str, parent_roles: &HashSet<String>) -> Result<(), SecurityError> {
        // Check if any parent role has this role as a descendant (would create a cycle)
        for parent_role in parent_roles {
            if self.has_descendant_role(parent_role, role_name).await? {
                return Err(SecurityError::InvalidInput(
                    format!("Circular dependency detected: role '{}' would create a cycle with parent '{}'", 
                           role_name, parent_role)
                ));
            }
        }
        
        Ok(())
    }
    
    /// Check if a role has another role as a descendant
    async fn has_descendant_role(&self, role_name: &str, target_role: &str) -> Result<bool, SecurityError> {
        let hierarchy = self.role_hierarchy.read().await;
        
        if let Some(children) = hierarchy.get(role_name) {
            if children.contains(target_role) {
                return Ok(true);
            }
            
            // Recursively check children
            for child in children {
                let child_clone = child.clone();
                let target_clone = target_role.to_string();
                if Box::pin(self.has_descendant_role(&child_clone, &target_clone)).await? {
                    return Ok(true);
                }
            }
        }
        
        Ok(false)
    }
    
    /// Collect all inherited roles for a given role
    async fn collect_inherited_roles(&self, role_name: &str, all_roles: &mut HashSet<String>) -> Result<(), SecurityError> {
        let role_definitions = self.role_definitions.read().await;
        
        if let Some(role_def) = role_definitions.get(role_name) {
            for parent_role in &role_def.parent_roles {
                if all_roles.insert(parent_role.clone()) {
                    // Recursively collect parent roles
                    let parent_clone = parent_role.clone();
                    Box::pin(self.collect_inherited_roles(&parent_clone, all_roles)).await?;
                }
            }
        }
        
        Ok(())
    }
    
    /// Count users with a specific role
    async fn count_users_with_role(&self, role_name: &str) -> Result<usize, SecurityError> {
        let user_roles = self.user_roles.read().await;
        
        let count = user_roles
            .values()
            .filter(|role_set| role_set.contains(role_name))
            .count();
        
        Ok(count)
    }
}

// Placeholder implementations when security-storage feature is disabled
#[cfg(not(feature = "security-storage"))]
pub struct RbacManager;

#[cfg(not(feature = "security-storage"))]
pub struct RoleDefinition;

#[cfg(not(feature = "security-storage"))]
pub struct RbacStatistics;

#[cfg(not(feature = "security-storage"))]
impl RbacManager {
    pub async fn new() -> Result<Self, SecurityError> {
        Err(SecurityError::FeatureNotEnabled("Security storage feature not enabled".to_string()))
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[cfg(feature = "security-storage")]
    #[tokio::test]
    async fn test_rbac_manager_initialization() {
        let manager = RbacManager::new().await.unwrap();
        
        let stats = manager.get_statistics().await.unwrap();
        assert!(stats.total_roles > 0); // Should have default roles
        assert_eq!(stats.total_users, 0); // No users initially
        assert_eq!(stats.role_assignments, 0);
    }
    
    #[cfg(feature = "security-storage")]
    #[tokio::test]
    async fn test_role_definition() {
        let manager = RbacManager::new().await.unwrap();
        
        let custom_role = RoleDefinition {
            name: "custom_role".to_string(),
            description: "A custom test role".to_string(),
            permissions: vec![
                Permission {
                    action: "read".to_string(),
                    resource: "test_resource".to_string(),
                    effect: PermissionEffect::Allow,
                },
            ],
            parent_roles: HashSet::new(),
            created_at: Utc::now(),
            updated_at: Utc::now(),
            is_active: true,
        };
        
        manager.define_role(custom_role).await.unwrap();
        
        let stats = manager.get_statistics().await.unwrap();
        // Should have default roles + 1 custom role
        assert!(stats.total_roles > 4);
    }
    
    #[cfg(feature = "security-storage")]
    #[tokio::test]
    async fn test_role_assignment_and_removal() {
        let manager = RbacManager::new().await.unwrap();
        
        // Test assignment
        manager.assign_role("test-user", "user").await.unwrap();
        let roles = manager.get_user_roles("test-user").await.unwrap();
        assert!(roles.contains(&"user".to_string()));
        
        // Test removal
        manager.remove_role("test-user", "user").await.unwrap();
        let roles = manager.get_user_roles("test-user").await.unwrap();
        assert!(!roles.contains(&"user".to_string()));
    }
    
    #[cfg(feature = "security-storage")]
    #[tokio::test]
    async fn test_role_inheritance() {
        let manager = RbacManager::new().await.unwrap();
        
        // Assign analyst role (which inherits from user role)
        manager.assign_role("test-analyst", "analyst").await.unwrap();
        let roles = manager.get_user_roles("test-analyst").await.unwrap();
        
        // Should have both analyst and user roles
        assert!(roles.contains(&"analyst".to_string()));
        assert!(roles.contains(&"user".to_string()));
    }
    
    #[cfg(feature = "security-storage")]
    #[tokio::test]
    async fn test_user_permissions() {
        let manager = RbacManager::new().await.unwrap();
        
        manager.assign_role("test-user", "admin").await.unwrap();
        let permissions = manager.get_user_permissions("test-user").await.unwrap();
        
        // Admin should have wildcard permission
        assert!(!permissions.is_empty());
        assert!(permissions.iter().any(|p| p.action == "*" && p.resource == "*"));
    }
    
    #[cfg(feature = "security-storage")]
    #[tokio::test]
    async fn test_circular_dependency_prevention() {
        let manager = RbacManager::new().await.unwrap();
        
        // Create role A that depends on user
        let role_a = RoleDefinition {
            name: "role_a".to_string(),
            description: "Role A".to_string(),
            permissions: vec![
                Permission {
                    action: "read".to_string(),
                    resource: "test".to_string(),
                    effect: PermissionEffect::Allow,
                },
            ],
            parent_roles: {
                let mut parents = HashSet::new();
                parents.insert("user".to_string());
                parents
            },
            created_at: Utc::now(),
            updated_at: Utc::now(),
            is_active: true,
        };
        
        manager.define_role(role_a).await.unwrap();
        
        // Try to create role B that would create a cycle (user -> role_a -> user)
        let role_b = RoleDefinition {
            name: "user_circular".to_string(),
            description: "This should fail".to_string(),
            permissions: vec![
                Permission {
                    action: "read".to_string(),
                    resource: "test".to_string(),
                    effect: PermissionEffect::Allow,
                },
            ],
            parent_roles: {
                let mut parents = HashSet::new();
                parents.insert("role_a".to_string());
                parents
            },
            created_at: Utc::now(),
            updated_at: Utc::now(),
            is_active: true,
        };
        
        // This should succeed as it's not creating a cycle
        manager.define_role(role_b).await.unwrap();
    }
}