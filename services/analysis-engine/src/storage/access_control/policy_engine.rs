//! Policy engine implementation for access control
//! 
//! This module provides the core policy evaluation engine that determines
//! whether a user has permission to perform a specific action on a resource.

use crate::models::security::{SecurityPolicy, Permission, PermissionEffect, PolicyCondition, ConditionOperator, SecurityError};
use crate::storage::access_control::{AccessContext, AccessDecision, AccessControlConfig};
use anyhow::{Context, Result};
use chrono::{DateTime, Utc};
use serde_json::Value;
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{debug, error, info, warn};

/// Policy engine for evaluating access control policies
#[cfg(feature = "security-storage")]
pub struct PolicyEngine {
    /// Storage for all security policies
    policies: Arc<RwLock<HashMap<String, SecurityPolicy>>>,
    /// Policy evaluation cache for performance
    policy_cache: Arc<RwLock<HashMap<String, CachedPolicyResult>>>,
    /// Engine configuration
    config: AccessControlConfig,
    /// Performance and usage statistics
    statistics: Arc<RwLock<PolicyEngineStatistics>>,
}

/// Cached policy evaluation result
#[cfg(feature = "security-storage")]
#[derive(Debug, Clone)]
struct CachedPolicyResult {
    /// The access decision
    decision: AccessDecision,
    /// When this result was cached
    cached_at: DateTime<Utc>,
    /// TTL for this cache entry
    expires_at: DateTime<Utc>,
}

/// Statistics for policy engine performance
#[cfg(feature = "security-storage")]
#[derive(Debug, Default, Clone)]
pub struct PolicyEngineStatistics {
    /// Total number of policies loaded
    pub total_policies: usize,
    /// Number of active (non-expired) policies
    pub active_policies: usize,
    /// Total policy evaluations performed
    pub policy_evaluations: u64,
    /// Cache hits
    pub cache_hits: u64,
    /// Cache misses
    pub cache_misses: u64,
    /// Total evaluation time in milliseconds
    pub total_evaluation_time_ms: u64,
    /// Cache hit rate (0.0 to 1.0)
    pub cache_hit_rate: f64,
    /// Average decision time in milliseconds
    pub average_decision_time_ms: f64,
}

#[cfg(feature = "security-storage")]
impl PolicyEngine {
    /// Create a new policy engine
    /// 
    /// # Arguments
    /// * `config` - Access control configuration
    /// 
    /// # Returns
    /// * New policy engine instance
    pub async fn new(config: AccessControlConfig) -> Result<Self, SecurityError> {
        info!("Initializing policy engine");
        debug!("Policy engine config: caching={}, ttl={}s", 
               config.enable_policy_caching, config.cache_ttl_seconds);
        
        let engine = Self {
            policies: Arc::new(RwLock::new(HashMap::new())),
            policy_cache: Arc::new(RwLock::new(HashMap::new())),
            config,
            statistics: Arc::new(RwLock::new(PolicyEngineStatistics::default())),
        };
        
        info!("Policy engine initialized successfully");
        Ok(engine)
    }
    
    /// Add a security policy
    /// 
    /// # Arguments
    /// * `policy` - Security policy to add
    /// 
    /// # Returns
    /// * `Result<(), SecurityError>` - Success or error
    pub async fn add_policy(&self, policy: SecurityPolicy) -> Result<(), SecurityError> {
        debug!("Adding policy: {} for resource type: {}", 
               policy.policy_id, policy.resource_type);
        
        // Validate policy
        self.validate_policy(&policy)?;
        
        // Store policy
        let policy_id = policy.policy_id.clone();
        {
            let mut policies = self.policies.write().await;
            policies.insert(policy_id.clone(), policy);
        }
        
        // Clear cache since policies have changed
        if self.config.enable_policy_caching {
            let mut cache = self.policy_cache.write().await;
            cache.clear();
            debug!("Policy cache cleared due to policy addition");
        }
        
        // Update statistics
        {
            let mut stats = self.statistics.write().await;
            stats.total_policies = {
                let policies = self.policies.read().await;
                policies.len()
            };
            stats.active_policies = stats.total_policies; // All policies are active by default
        }
        
        info!("Policy added successfully: {}", policy_id);
        Ok(())
    }
    
    /// Remove a security policy
    /// 
    /// # Arguments
    /// * `policy_id` - ID of the policy to remove
    /// 
    /// # Returns
    /// * `Result<(), SecurityError>` - Success or error
    pub async fn remove_policy(&self, policy_id: &str) -> Result<(), SecurityError> {
        debug!("Removing policy: {}", policy_id);
        
        let removed = {
            let mut policies = self.policies.write().await;
            policies.remove(policy_id).is_some()
        };
        
        if !removed {
            return Err(SecurityError::InvalidInput(format!("Policy not found: {}", policy_id)));
        }
        
        // Clear cache since policies have changed
        if self.config.enable_policy_caching {
            let mut cache = self.policy_cache.write().await;
            cache.clear();
            debug!("Policy cache cleared due to policy removal");
        }
        
        // Update statistics
        {
            let mut stats = self.statistics.write().await;
            stats.total_policies = {
                let policies = self.policies.read().await;
                policies.len()
            };
            stats.active_policies = stats.total_policies;
        }
        
        info!("Policy removed successfully: {}", policy_id);
        Ok(())
    }
    
    /// Evaluate policies for an access context
    /// 
    /// # Arguments
    /// * `context` - Access context to evaluate
    /// 
    /// # Returns
    /// * `Result<Option<AccessDecision>, SecurityError>` - Access decision or None if no policy applies
    pub async fn evaluate_policies(&self, context: &AccessContext) -> Result<Option<AccessDecision>, SecurityError> {
        let start_time = std::time::Instant::now();
        
        debug!("Evaluating policies for: user={}, action={}, resource={}", 
               context.user_id, context.action, context.resource);
        
        // Check cache first if enabled
        if self.config.enable_policy_caching {
            let cache_key = self.generate_cache_key(context);
            if let Some(cached_result) = self.get_from_cache(&cache_key).await? {
                let mut stats = self.statistics.write().await;
                stats.cache_hits += 1;
                stats.policy_evaluations += 1;
                self.update_cache_hit_rate(&mut stats);
                debug!("Cache hit for access decision: {}", cache_key);
                return Ok(Some(cached_result.decision));
            } else {
                let mut stats = self.statistics.write().await;
                stats.cache_misses += 1;
                debug!("Cache miss for access decision: {}", cache_key);
            }
        }
        
        // Evaluate policies
        let decision = self.evaluate_policies_internal(context).await?;
        
        // Cache result if enabled
        if let Some(ref decision) = decision {
            if self.config.enable_policy_caching {
                let cache_key = self.generate_cache_key(context);
                self.store_in_cache(cache_key, decision.clone()).await?;
            }
        }
        
        // Update statistics
        let duration = start_time.elapsed();
        {
            let mut stats = self.statistics.write().await;
            stats.policy_evaluations += 1;
            stats.total_evaluation_time_ms += duration.as_millis() as u64;
            stats.average_decision_time_ms = stats.total_evaluation_time_ms as f64 / stats.policy_evaluations as f64;
            if self.config.enable_policy_caching {
                self.update_cache_hit_rate(&mut stats);
            }
        }
        
        debug!("Policy evaluation completed in {:?}: allowed={:?}", 
               duration, decision.as_ref().map(|d| d.allowed));
        
        Ok(decision)
    }
    
    /// Get policy engine statistics
    /// 
    /// # Returns
    /// * `Result<PolicyEngineStatistics, SecurityError>` - Statistics or error
    pub async fn get_statistics(&self) -> Result<PolicyEngineStatistics, SecurityError> {
        let stats = self.statistics.read().await;
        Ok(stats.clone())
    }
    
    /// Perform health check on policy engine
    /// 
    /// # Returns
    /// * `Result<(), SecurityError>` - Success or health check error
    pub async fn health_check(&self) -> Result<(), SecurityError> {
        debug!("Performing policy engine health check");
        
        // Test policy evaluation with dummy context
        let test_context = AccessContext {
            user_id: "health-check-user".to_string(),
            action: "read".to_string(),
            resource: "health-check-resource".to_string(),
            attributes: HashMap::new(),
            timestamp: Utc::now(),
            ip_address: Some("127.0.0.1".to_string()),
            user_agent: Some("health-check".to_string()),
        };
        
        let _decision = self.evaluate_policies(&test_context).await?;
        
        // Check policy storage
        let policies = self.policies.read().await;
        debug!("Policy engine health check: {} policies loaded", policies.len());
        
        info!("Policy engine health check passed");
        Ok(())
    }
    
    /// Internal policy evaluation logic
    async fn evaluate_policies_internal(&self, context: &AccessContext) -> Result<Option<AccessDecision>, SecurityError> {
        let policies = self.policies.read().await;
        
        let best_decision: Option<AccessDecision> = None;
        let mut deny_decision: Option<AccessDecision> = None;
        
        for (policy_id, policy) in policies.iter() {
            // Check if policy applies to this resource type
            if !self.resource_matches(&policy.resource_type, &context.resource) {
                continue;
            }
            
            // Evaluate policy conditions
            if !self.evaluate_conditions(&policy.conditions, context).await? {
                continue;
            }
            
            // Check permissions within this policy
            for permission in &policy.permissions {
                if self.permission_matches(permission, &context.action, &context.resource) {
                    let decision = AccessDecision {
                        allowed: matches!(permission.effect, PermissionEffect::Allow),
                        reason: format!("Policy '{}' permission: {} {} on {}", 
                                       policy_id, permission.effect.as_str(), permission.action, permission.resource),
                        applied_policy: Some(policy_id.clone()),
                        matched_permission: Some(permission.clone()),
                        timestamp: Utc::now(),
                    };
                    
                    match permission.effect {
                        PermissionEffect::Allow => {
                            // Allow takes precedence, return immediately
                            debug!("Allow decision from policy '{}': {} on {}", 
                                   policy_id, context.action, context.resource);
                            return Ok(Some(decision));
                        }
                        PermissionEffect::Deny => {
                            // Store deny decision but continue checking for allows
                            debug!("Deny decision from policy '{}': {} on {}", 
                                   policy_id, context.action, context.resource);
                            deny_decision = Some(decision);
                        }
                    }
                }
            }
        }
        
        // Return deny if we found one, otherwise no decision
        Ok(deny_decision.or(best_decision))
    }
    
    /// Validate a security policy
    fn validate_policy(&self, policy: &SecurityPolicy) -> Result<(), SecurityError> {
        if policy.policy_id.is_empty() {
            return Err(SecurityError::InvalidInput("Policy ID cannot be empty".to_string()));
        }
        
        if policy.resource_type.is_empty() {
            return Err(SecurityError::InvalidInput("Resource type cannot be empty".to_string()));
        }
        
        if policy.permissions.is_empty() {
            return Err(SecurityError::InvalidInput("Policy must have at least one permission".to_string()));
        }
        
        for permission in &policy.permissions {
            if permission.action.is_empty() {
                return Err(SecurityError::InvalidInput("Permission action cannot be empty".to_string()));
            }
            if permission.resource.is_empty() {
                return Err(SecurityError::InvalidInput("Permission resource cannot be empty".to_string()));
            }
        }
        
        Ok(())
    }
    
    /// Check if a resource pattern matches a specific resource
    fn resource_matches(&self, pattern: &str, resource: &str) -> bool {
        if pattern == "*" {
            return true;
        }
        
        // Extract resource type from resource (e.g., "user_data:123" -> "user_data")
        let resource_type = resource.split(':').next().unwrap_or(resource);
        
        pattern == resource_type || pattern == resource
    }
    
    /// Check if a permission matches an action and resource
    fn permission_matches(&self, permission: &Permission, action: &str, resource: &str) -> bool {
        let action_matches = permission.action == "*" || permission.action == action;
        
        let resource_matches = if permission.resource == "*" {
            true
        } else if permission.resource.contains(":") {
            // Exact resource match (e.g., "user_data:123")
            permission.resource == resource
        } else if permission.resource.ends_with(":own") {
            // Owner-based resource match (e.g., "user_data:own")
            let resource_type = permission.resource.strip_suffix(":own").unwrap_or(&permission.resource);
            resource.starts_with(&format!("{}:", resource_type))
        } else {
            // Resource type match (e.g., "user_data")
            resource.starts_with(&format!("{}:", permission.resource)) || permission.resource == resource
        };
        
        action_matches && resource_matches
    }
    
    /// Evaluate policy conditions
    async fn evaluate_conditions(&self, conditions: &[PolicyCondition], context: &AccessContext) -> Result<bool, SecurityError> {
        for condition in conditions {
            if !self.evaluate_condition(condition, context).await? {
                return Ok(false);
            }
        }
        Ok(true)
    }
    
    /// Evaluate a single policy condition
    async fn evaluate_condition(&self, condition: &PolicyCondition, context: &AccessContext) -> Result<bool, SecurityError> {
        // Get the field value from context
        let field_value = self.get_field_value(&condition.field, context)?;
        
        // Apply operator
        let result = match condition.operator {
            ConditionOperator::Equals => field_value == condition.value,
            ConditionOperator::NotEquals => field_value != condition.value,
            ConditionOperator::Contains => {
                match (&field_value, &condition.value) {
                    (Value::String(field_str), Value::String(condition_str)) => {
                        field_str.contains(condition_str)
                    }
                    (Value::Array(field_array), _) => {
                        field_array.contains(&condition.value)
                    }
                    _ => false,
                }
            }
            ConditionOperator::GreaterThan => {
                match (&field_value, &condition.value) {
                    (Value::Number(field_num), Value::Number(condition_num)) => {
                        field_num.as_f64().unwrap_or(0.0) > condition_num.as_f64().unwrap_or(0.0)
                    }
                    _ => false,
                }
            }
            ConditionOperator::LessThan => {
                match (&field_value, &condition.value) {
                    (Value::Number(field_num), Value::Number(condition_num)) => {
                        field_num.as_f64().unwrap_or(0.0) < condition_num.as_f64().unwrap_or(0.0)
                    }
                    _ => false,
                }
            }
            ConditionOperator::In => {
                match &condition.value {
                    Value::Array(array) => array.contains(&field_value),
                    _ => false,
                }
            }
            ConditionOperator::NotIn => {
                match &condition.value {
                    Value::Array(array) => !array.contains(&field_value),
                    _ => true,
                }
            }
        };
        
        debug!("Condition evaluation: {} {} {} = {}", 
               condition.field, condition.operator.as_str(), condition.value, result);
        
        Ok(result)
    }
    
    /// Get field value from context
    fn get_field_value(&self, field: &str, context: &AccessContext) -> Result<Value, SecurityError> {
        match field {
            "user_id" => Ok(Value::String(context.user_id.clone())),
            "action" => Ok(Value::String(context.action.clone())),
            "resource" => Ok(Value::String(context.resource.clone())),
            "timestamp" => Ok(Value::String(context.timestamp.to_rfc3339())),
            "ip_address" => Ok(context.ip_address.as_ref()
                .map(|ip| Value::String(ip.clone()))
                .unwrap_or(Value::Null)),
            "user_agent" => Ok(context.user_agent.as_ref()
                .map(|ua| Value::String(ua.clone()))
                .unwrap_or(Value::Null)),
            _ => {
                // Check custom attributes
                Ok(context.attributes.get(field).cloned().unwrap_or(Value::Null))
            }
        }
    }
    
    /// Generate cache key for a context
    fn generate_cache_key(&self, context: &AccessContext) -> String {
        // Include relevant context fields in cache key
        let mut key_parts = vec![
            context.user_id.clone(),
            context.action.clone(),
            context.resource.clone(),
        ];
        
        // Add sorted attributes to ensure consistent key
        let mut attr_keys: Vec<&String> = context.attributes.keys().collect();
        attr_keys.sort();
        for key in attr_keys {
            if let Some(value) = context.attributes.get(key) {
                key_parts.push(format!("{}={}", key, value));
            }
        }
        
        // Use SHA-256 hash for consistent key length
        use sha2::{Sha256, Digest};
        let mut hasher = Sha256::new();
        hasher.update(key_parts.join("|"));
        format!("{:x}", hasher.finalize())
    }
    
    /// Get result from cache
    async fn get_from_cache(&self, cache_key: &str) -> Result<Option<CachedPolicyResult>, SecurityError> {
        if !self.config.enable_policy_caching {
            return Ok(None);
        }
        
        let cache = self.policy_cache.read().await;
        if let Some(cached_result) = cache.get(cache_key) {
            // Check if cache entry is still valid
            if Utc::now() < cached_result.expires_at {
                return Ok(Some(cached_result.clone()));
            }
        }
        
        Ok(None)
    }
    
    /// Store result in cache
    async fn store_in_cache(&self, cache_key: String, decision: AccessDecision) -> Result<(), SecurityError> {
        if !self.config.enable_policy_caching {
            return Ok(());
        }
        
        let now = Utc::now();
        let expires_at = now + chrono::Duration::seconds(self.config.cache_ttl_seconds as i64);
        
        let cached_result = CachedPolicyResult {
            decision,
            cached_at: now,
            expires_at,
        };
        
        let mut cache = self.policy_cache.write().await;
        cache.insert(cache_key, cached_result);
        
        Ok(())
    }
    
    /// Update cache hit rate statistics
    fn update_cache_hit_rate(&self, stats: &mut PolicyEngineStatistics) {
        let total_requests = stats.cache_hits + stats.cache_misses;
        if total_requests > 0 {
            stats.cache_hit_rate = stats.cache_hits as f64 / total_requests as f64;
        }
    }
}

impl ConditionOperator {
    fn as_str(&self) -> &str {
        match self {
            ConditionOperator::Equals => "==",
            ConditionOperator::NotEquals => "!=",
            ConditionOperator::Contains => "contains",
            ConditionOperator::GreaterThan => ">",
            ConditionOperator::LessThan => "<",
            ConditionOperator::In => "in",
            ConditionOperator::NotIn => "not_in",
        }
    }
}

impl PermissionEffect {
    fn as_str(&self) -> &str {
        match self {
            PermissionEffect::Allow => "allow",
            PermissionEffect::Deny => "deny",
        }
    }
}

// Placeholder implementations when security-storage feature is disabled
#[cfg(not(feature = "security-storage"))]
pub struct PolicyEngine;

#[cfg(not(feature = "security-storage"))]
pub struct PolicyEngineStatistics;

#[cfg(not(feature = "security-storage"))]
impl PolicyEngine {
    pub async fn new(_config: AccessControlConfig) -> Result<Self, SecurityError> {
        Err(SecurityError::FeatureNotEnabled("Security storage feature not enabled".to_string()))
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::models::security::{SecurityPolicy, Permission, PermissionEffect, PolicyCondition, ConditionOperator};
    
    #[cfg(feature = "security-storage")]
    #[tokio::test]
    async fn test_policy_engine_initialization() {
        let config = AccessControlConfig::default();
        let engine = PolicyEngine::new(config).await.unwrap();
        
        let stats = engine.get_statistics().await.unwrap();
        assert_eq!(stats.total_policies, 0);
        assert_eq!(stats.policy_evaluations, 0);
    }
    
    #[cfg(feature = "security-storage")]
    #[tokio::test]
    async fn test_policy_addition_and_removal() {
        let config = AccessControlConfig::default();
        let engine = PolicyEngine::new(config).await.unwrap();
        
        let policy = SecurityPolicy {
            policy_id: "test_policy".to_string(),
            resource_type: "test_resource".to_string(),
            permissions: vec![
                Permission {
                    action: "read".to_string(),
                    resource: "test_resource".to_string(),
                    effect: PermissionEffect::Allow,
                },
            ],
            conditions: vec![],
            created_at: Utc::now(),
            updated_at: Utc::now(),
        };
        
        // Add policy
        engine.add_policy(policy).await.unwrap();
        let stats = engine.get_statistics().await.unwrap();
        assert_eq!(stats.total_policies, 1);
        
        // Remove policy
        engine.remove_policy("test_policy").await.unwrap();
        let stats = engine.get_statistics().await.unwrap();
        assert_eq!(stats.total_policies, 0);
    }
    
    #[cfg(feature = "security-storage")]
    #[tokio::test]
    async fn test_policy_evaluation() {
        let config = AccessControlConfig::default();
        let engine = PolicyEngine::new(config).await.unwrap();
        
        // Add an allow policy
        let policy = SecurityPolicy {
            policy_id: "allow_read_policy".to_string(),
            resource_type: "user_data".to_string(),
            permissions: vec![
                Permission {
                    action: "read".to_string(),
                    resource: "user_data".to_string(),
                    effect: PermissionEffect::Allow,
                },
            ],
            conditions: vec![
                PolicyCondition {
                    field: "user_id".to_string(),
                    operator: ConditionOperator::Equals,
                    value: Value::String("test-user".to_string()),
                },
            ],
            created_at: Utc::now(),
            updated_at: Utc::now(),
        };
        
        engine.add_policy(policy).await.unwrap();
        
        // Test matching context
        let context = AccessContext {
            user_id: "test-user".to_string(),
            action: "read".to_string(),
            resource: "user_data:123".to_string(),
            attributes: HashMap::new(),
            timestamp: Utc::now(),
            ip_address: Some("***********".to_string()),
            user_agent: Some("test-client".to_string()),
        };
        
        let decision = engine.evaluate_policies(&context).await.unwrap();
        assert!(decision.is_some());
        assert!(decision.unwrap().allowed);
        
        // Test non-matching context
        let context = AccessContext {
            user_id: "different-user".to_string(),
            action: "read".to_string(),
            resource: "user_data:123".to_string(),
            attributes: HashMap::new(),
            timestamp: Utc::now(),
            ip_address: Some("***********".to_string()),
            user_agent: Some("test-client".to_string()),
        };
        
        let decision = engine.evaluate_policies(&context).await.unwrap();
        assert!(decision.is_none()); // No policy matches, so no decision
    }
    
    #[cfg(feature = "security-storage")]
    #[tokio::test]
    async fn test_resource_matching() {
        let config = AccessControlConfig::default();
        let engine = PolicyEngine::new(config).await.unwrap();
        
        // Test wildcard matching
        assert!(engine.resource_matches("*", "any_resource"));
        
        // Test exact type matching
        assert!(engine.resource_matches("user_data", "user_data:123"));
        assert!(engine.resource_matches("user_data", "user_data"));
        
        // Test non-matching
        assert!(!engine.resource_matches("user_data", "admin_data:123"));
    }
    
    #[cfg(feature = "security-storage")]
    #[tokio::test]
    async fn test_condition_evaluation() {
        let config = AccessControlConfig::default();
        let engine = PolicyEngine::new(config).await.unwrap();
        
        let context = AccessContext {
            user_id: "test-user".to_string(),
            action: "read".to_string(),
            resource: "user_data:123".to_string(),
            attributes: {
                let mut attrs = HashMap::new();
                attrs.insert("role".to_string(), Value::String("admin".to_string()));
                attrs
            },
            timestamp: Utc::now(),
            ip_address: Some("***********".to_string()),
            user_agent: Some("test-client".to_string()),
        };
        
        // Test equals condition
        let condition = PolicyCondition {
            field: "user_id".to_string(),
            operator: ConditionOperator::Equals,
            value: Value::String("test-user".to_string()),
        };
        assert!(engine.evaluate_condition(&condition, &context).await.unwrap());
        
        // Test contains condition
        let condition = PolicyCondition {
            field: "role".to_string(),
            operator: ConditionOperator::Contains,
            value: Value::String("admin".to_string()),
        };
        assert!(engine.evaluate_condition(&condition, &context).await.unwrap());
        
        // Test not equals condition
        let condition = PolicyCondition {
            field: "user_id".to_string(),
            operator: ConditionOperator::NotEquals,
            value: Value::String("other-user".to_string()),
        };
        assert!(engine.evaluate_condition(&condition, &context).await.unwrap());
    }
}