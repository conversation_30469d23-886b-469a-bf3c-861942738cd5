//! Storage implementations for audit records
//! 
//! This module provides different storage backends for audit records including
//! Spanner-based storage and in-memory storage for testing.

use crate::models::security::{AuditRecord, AuditResult, SecurityError};
use crate::storage::audit::{AuditStorage, AuditQuery, TimeRange, AuditStatistics};
use anyhow::Result;
use chrono::{DateTime, Utc};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{debug, error, info, warn};

/// Spanner-based audit storage implementation
#[cfg(feature = "security-storage")]
pub struct SpannerAuditStorage {
    // In production, this would contain actual Spanner client
    // For now, we implement a secure local storage with proper interfaces
    records: Arc<RwLock<Vec<AuditRecord>>>,
    database_name: String,
}

#[cfg(feature = "security-storage")]
impl SpannerAuditStorage {
    /// Create a new Spanner audit storage instance
    /// 
    /// # Arguments
    /// * `database_name` - Name of the Spanner database
    /// 
    /// # Returns
    /// * New Spanner audit storage instance
    pub async fn new(database_name: String) -> Result<Self, SecurityError> {
        info!("Initializing Spanner audit storage: {}", database_name);
        
        // In production, this would initialize actual Spanner client
        // For now, we use secure local storage with proper interfaces
        let storage = Self {
            records: Arc::new(RwLock::new(Vec::new())),
            database_name,
        };
        
        // Perform connection health check
        storage.health_check().await?;
        
        info!("Spanner audit storage initialized successfully");
        Ok(storage)
    }
    
    /// Perform health check on Spanner connection
    async fn health_check(&self) -> Result<(), SecurityError> {
        debug!("Performing Spanner audit storage health check");
        
        // In production, this would test actual Spanner connectivity
        // For now, we test the storage interface
        let test_record = AuditRecord {
            audit_id: "health-check".to_string(),
            user_id: Some("system".to_string()),
            operation: "health_check".to_string(),
            resource_type: "audit_storage".to_string(),
            resource_id: "health_check".to_string(),
            timestamp: Utc::now(),
            ip_address: Some("127.0.0.1".to_string()),
            user_agent: Some("audit-storage/health-check".to_string()),
            result: AuditResult::Success,
            risk_score: Some(0.1),
            metadata: None,
        };
        
        // Test store and retrieve
        self.store_record(&test_record).await?;
        
        let query = AuditQuery {
            user_id: Some("system".to_string()),
            operation: Some("health_check".to_string()),
            resource_type: None,
            resource_id: None,
            result: None,
            time_range: None,
            risk_score_min: None,
            limit: Some(1),
            offset: None,
        };
        
        let results = self.query_records(&query).await?;
        if results.is_empty() {
            return Err(SecurityError::HealthCheckError("Failed to retrieve health check record".to_string()));
        }
        
        info!("Spanner audit storage health check passed");
        Ok(())
    }
}

#[cfg(feature = "security-storage")]
#[async_trait::async_trait]
impl AuditStorage for SpannerAuditStorage {
    /// Store a single audit record
    async fn store_record(&self, record: &AuditRecord) -> Result<(), SecurityError> {
        debug!("Storing audit record: {} - {} on {}", 
               record.audit_id, record.operation, record.resource_type);
        
        // In production, this would execute Spanner INSERT
        // For now, we store securely in memory with proper validation
        
        // Validate required fields
        if record.audit_id.is_empty() {
            return Err(SecurityError::InvalidInput("Audit ID cannot be empty".to_string()));
        }
        
        if record.operation.is_empty() {
            return Err(SecurityError::InvalidInput("Operation cannot be empty".to_string()));
        }
        
        if record.resource_type.is_empty() {
            return Err(SecurityError::InvalidInput("Resource type cannot be empty".to_string()));
        }
        
        // Store the record
        let mut records = self.records.write().await;
        records.push(record.clone());
        
        debug!("Audit record stored successfully: {}", record.audit_id);
        Ok(())
    }
    
    /// Store multiple audit records in batch
    async fn store_batch(&self, records: &[AuditRecord]) -> Result<(), SecurityError> {
        info!("Storing batch of {} audit records", records.len());
        
        if records.is_empty() {
            return Ok(());
        }
        
        // In production, this would use Spanner batch operations
        // For now, we validate and store each record
        for record in records {
            self.store_record(record).await?;
        }
        
        info!("Batch of {} audit records stored successfully", records.len());
        Ok(())
    }
    
    /// Query audit records with filters
    async fn query_records(&self, query: &AuditQuery) -> Result<Vec<AuditRecord>, SecurityError> {
        debug!("Querying audit records with filters");
        
        let records = self.records.read().await;
        let mut results = Vec::new();
        
        for record in records.iter() {
            let mut matches = true;
            
            // Apply filters
            if let Some(ref user_id) = query.user_id {
                if record.user_id.as_ref() != Some(user_id) {
                    matches = false;
                }
            }
            
            if let Some(ref operation) = query.operation {
                if !record.operation.contains(operation) {
                    matches = false;
                }
            }
            
            if let Some(ref resource_type) = query.resource_type {
                if !record.resource_type.contains(resource_type) {
                    matches = false;
                }
            }
            
            if let Some(ref resource_id) = query.resource_id {
                if record.resource_id != *resource_id {
                    matches = false;
                }
            }
            
            if let Some(ref result) = query.result {
                if !matches_audit_result(&record.result, result) {
                    matches = false;
                }
            }
            
            if let Some(ref time_range) = query.time_range {
                if record.timestamp < time_range.start || record.timestamp > time_range.end {
                    matches = false;
                }
            }
            
            if let Some(risk_min) = query.risk_score_min {
                if record.risk_score.unwrap_or(0.0) < risk_min {
                    matches = false;
                }
            }
            
            if matches {
                results.push(record.clone());
            }
        }
        
        // Apply limit and offset
        if let Some(offset) = query.offset {
            if offset < results.len() {
                results = results[offset..].to_vec();
            } else {
                results.clear();
            }
        }
        
        if let Some(limit) = query.limit {
            results.truncate(limit);
        }
        
        debug!("Query returned {} audit records", results.len());
        Ok(results)
    }
    
    /// Get audit statistics
    async fn get_statistics(&self, time_range: TimeRange) -> Result<AuditStatistics, SecurityError> {
        debug!("Calculating audit statistics for range: {} to {}", 
               time_range.start, time_range.end);
        
        let records = self.records.read().await;
        let mut stats = AuditStatistics {
            total_operations: 0,
            successful_operations: 0,
            failed_operations: 0,
            unauthorized_attempts: 0,
            forbidden_attempts: 0,
            high_risk_operations: 0,
            unique_users: std::collections::HashSet::<String>::new().len() as u64,
            unique_resources: std::collections::HashSet::<String>::new().len() as u64,
        };
        
        let mut unique_users = std::collections::HashSet::new();
        let mut unique_resources = std::collections::HashSet::new();
        
        for record in records.iter() {
            // Filter by time range
            if record.timestamp < time_range.start || record.timestamp > time_range.end {
                continue;
            }
            
            stats.total_operations += 1;
            
            // Count by result type
            match record.result {
                AuditResult::Success => stats.successful_operations += 1,
                AuditResult::Failure(_) => stats.failed_operations += 1,
                AuditResult::Unauthorized => stats.unauthorized_attempts += 1,
                AuditResult::Forbidden => stats.forbidden_attempts += 1,
            }
            
            // Count high-risk operations
            if let Some(risk_score) = record.risk_score {
                if risk_score >= 0.7 {
                    stats.high_risk_operations += 1;
                }
            }
            
            // Track unique users and resources
            if let Some(ref user_id) = record.user_id {
                unique_users.insert(user_id.clone());
            }
            unique_resources.insert(format!("{}:{}", record.resource_type, record.resource_id));
        }
        
        stats.unique_users = unique_users.len() as u64;
        stats.unique_resources = unique_resources.len() as u64;
        
        debug!("Audit statistics calculated: {} total operations, {} successful", 
               stats.total_operations, stats.successful_operations);
        
        Ok(stats)
    }
}

/// In-memory audit storage for testing
#[cfg(feature = "security-storage")]
pub struct InMemoryAuditStorage {
    records: Arc<RwLock<Vec<AuditRecord>>>,
}

#[cfg(feature = "security-storage")]
impl InMemoryAuditStorage {
    /// Create a new in-memory audit storage instance
    pub fn new() -> Self {
        Self {
            records: Arc::new(RwLock::new(Vec::new())),
        }
    }
    
    /// Clear all stored records (for testing)
    pub async fn clear(&self) {
        let mut records = self.records.write().await;
        records.clear();
    }
    
    /// Get the number of stored records (for testing)
    pub async fn count(&self) -> usize {
        let records = self.records.read().await;
        records.len()
    }
}

#[cfg(feature = "security-storage")]
#[async_trait::async_trait]
impl AuditStorage for InMemoryAuditStorage {
    async fn store_record(&self, record: &AuditRecord) -> Result<(), SecurityError> {
        let mut records = self.records.write().await;
        records.push(record.clone());
        Ok(())
    }
    
    async fn store_batch(&self, batch_records: &[AuditRecord]) -> Result<(), SecurityError> {
        let mut records = self.records.write().await;
        records.extend_from_slice(batch_records);
        Ok(())
    }
    
    async fn query_records(&self, query: &AuditQuery) -> Result<Vec<AuditRecord>, SecurityError> {
        let records = self.records.read().await;
        let mut results: Vec<AuditRecord> = records
            .iter()
            .filter(|record| {
                // Apply all filters
                if let Some(ref user_id) = query.user_id {
                    if record.user_id.as_ref() != Some(user_id) {
                        return false;
                    }
                }
                
                if let Some(ref operation) = query.operation {
                    if !record.operation.contains(operation) {
                        return false;
                    }
                }
                
                if let Some(ref time_range) = query.time_range {
                    if record.timestamp < time_range.start || record.timestamp > time_range.end {
                        return false;
                    }
                }
                
                true
            })
            .cloned()
            .collect();
        
        // Apply limit and offset
        if let Some(offset) = query.offset {
            if offset < results.len() {
                results = results[offset..].to_vec();
            } else {
                results.clear();
            }
        }
        
        if let Some(limit) = query.limit {
            results.truncate(limit);
        }
        
        Ok(results)
    }
    
    async fn get_statistics(&self, time_range: TimeRange) -> Result<AuditStatistics, SecurityError> {
        let records = self.records.read().await;
        let filtered_records: Vec<&AuditRecord> = records
            .iter()
            .filter(|record| record.timestamp >= time_range.start && record.timestamp <= time_range.end)
            .collect();
        
        let total_operations = filtered_records.len() as u64;
        let successful_operations = filtered_records
            .iter()
            .filter(|record| matches!(record.result, AuditResult::Success))
            .count() as u64;
        
        let failed_operations = filtered_records
            .iter()
            .filter(|record| matches!(record.result, AuditResult::Failure(_)))
            .count() as u64;
        
        let unauthorized_attempts = filtered_records
            .iter()
            .filter(|record| matches!(record.result, AuditResult::Unauthorized))
            .count() as u64;
        
        let forbidden_attempts = filtered_records
            .iter()
            .filter(|record| matches!(record.result, AuditResult::Forbidden))
            .count() as u64;
        
        let high_risk_operations = filtered_records
            .iter()
            .filter(|record| record.risk_score.unwrap_or(0.0) >= 0.7)
            .count() as u64;
        
        let unique_users = filtered_records
            .iter()
            .filter_map(|record| record.user_id.as_ref())
            .collect::<std::collections::HashSet<_>>()
            .len() as u64;
        
        let unique_resources = filtered_records
            .iter()
            .map(|record| format!("{}:{}", record.resource_type, record.resource_id))
            .collect::<std::collections::HashSet<_>>()
            .len() as u64;
        
        Ok(AuditStatistics {
            total_operations,
            successful_operations,
            failed_operations,
            unauthorized_attempts,
            forbidden_attempts,
            high_risk_operations,
            unique_users,
            unique_resources,
        })
    }
}

/// Helper function to match audit results
fn matches_audit_result(actual: &AuditResult, expected: &AuditResult) -> bool {
    match (actual, expected) {
        (AuditResult::Success, AuditResult::Success) => true,
        (AuditResult::Failure(_), AuditResult::Failure(_)) => true,
        (AuditResult::Unauthorized, AuditResult::Unauthorized) => true,
        (AuditResult::Forbidden, AuditResult::Forbidden) => true,
        _ => false,
    }
}

// Placeholder implementations when security-storage feature is disabled
#[cfg(not(feature = "security-storage"))]
pub struct SpannerAuditStorage;

#[cfg(not(feature = "security-storage"))]
pub struct InMemoryAuditStorage;

#[cfg(not(feature = "security-storage"))]
impl SpannerAuditStorage {
    pub async fn new(_database_name: String) -> Result<Self, SecurityError> {
        Err(SecurityError::FeatureNotEnabled("Security storage feature not enabled".to_string()))
    }
}

#[cfg(not(feature = "security-storage"))]
impl InMemoryAuditStorage {
    pub fn new() -> Self {
        Self
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use chrono::Duration;
    
    #[cfg(feature = "security-storage")]
    fn create_test_record(audit_id: &str, operation: &str, user_id: Option<&str>) -> AuditRecord {
        AuditRecord {
            audit_id: audit_id.to_string(),
            user_id: user_id.map(String::from),
            operation: operation.to_string(),
            resource_type: "test_resource".to_string(),
            resource_id: "test_id".to_string(),
            timestamp: Utc::now(),
            ip_address: Some("***********".to_string()),
            user_agent: Some("test-agent".to_string()),
            result: AuditResult::Success,
            risk_score: Some(0.5),
            metadata: None,
        }
    }
    
    #[cfg(feature = "security-storage")]
    #[tokio::test]
    async fn test_in_memory_storage_basic_operations() {
        let storage = InMemoryAuditStorage::new();
        
        // Test storing a record
        let record = create_test_record("test-1", "read", Some("user-1"));
        storage.store_record(&record).await.unwrap();
        
        assert_eq!(storage.count().await, 1);
        
        // Test querying records
        let query = AuditQuery {
            user_id: Some("user-1".to_string()),
            operation: None,
            resource_type: None,
            resource_id: None,
            result: None,
            time_range: None,
            risk_score_min: None,
            limit: None,
            offset: None,
        };
        
        let results = storage.query_records(&query).await.unwrap();
        assert_eq!(results.len(), 1);
        assert_eq!(results[0].audit_id, "test-1");
    }
    
    #[cfg(feature = "security-storage")]
    #[tokio::test]
    async fn test_batch_operations() {
        let storage = InMemoryAuditStorage::new();
        
        let records = vec![
            create_test_record("test-1", "read", Some("user-1")),
            create_test_record("test-2", "write", Some("user-2")),
            create_test_record("test-3", "delete", Some("user-1")),
        ];
        
        storage.store_batch(&records).await.unwrap();
        assert_eq!(storage.count().await, 3);
    }
    
    #[cfg(feature = "security-storage")]
    #[tokio::test]
    async fn test_statistics_calculation() {
        let storage = InMemoryAuditStorage::new();
        
        // Create test records with different results
        let mut record1 = create_test_record("test-1", "read", Some("user-1"));
        record1.result = AuditResult::Success;
        
        let mut record2 = create_test_record("test-2", "write", Some("user-2"));
        record2.result = AuditResult::Failure("test error".to_string());
        
        let mut record3 = create_test_record("test-3", "admin", Some("user-1"));
        record3.result = AuditResult::Unauthorized;
        record3.risk_score = Some(0.8); // High risk
        
        storage.store_batch(&[record1, record2, record3]).await.unwrap();
        
        let time_range = TimeRange {
            start: Utc::now() - Duration::hours(1),
            end: Utc::now() + Duration::hours(1),
        };
        
        let stats = storage.get_statistics(time_range).await.unwrap();
        
        assert_eq!(stats.total_operations, 3);
        assert_eq!(stats.successful_operations, 1);
        assert_eq!(stats.failed_operations, 1);
        assert_eq!(stats.unauthorized_attempts, 1);
        assert_eq!(stats.high_risk_operations, 1);
        assert_eq!(stats.unique_users, 2);
    }
    
    #[cfg(feature = "security-storage")]
    #[tokio::test]
    async fn test_query_filters() {
        let storage = InMemoryAuditStorage::new();
        
        let records = vec![
            create_test_record("test-1", "read", Some("user-1")),
            create_test_record("test-2", "write", Some("user-2")),
            create_test_record("test-3", "read", Some("user-1")),
        ];
        
        storage.store_batch(&records).await.unwrap();
        
        // Test user filter
        let query = AuditQuery {
            user_id: Some("user-1".to_string()),
            operation: None,
            resource_type: None,
            resource_id: None,
            result: None,
            time_range: None,
            risk_score_min: None,
            limit: None,
            offset: None,
        };
        
        let results = storage.query_records(&query).await.unwrap();
        assert_eq!(results.len(), 2);
        
        // Test operation filter
        let query = AuditQuery {
            user_id: None,
            operation: Some("write".to_string()),
            resource_type: None,
            resource_id: None,
            result: None,
            time_range: None,
            risk_score_min: None,
            limit: None,
            offset: None,
        };
        
        let results = storage.query_records(&query).await.unwrap();
        assert_eq!(results.len(), 1);
        assert_eq!(results[0].audit_id, "test-2");
    }
}