//! Audit logging module for security operations
//! 
//! This module provides comprehensive audit logging for all security-related operations
//! including data access, encryption/decryption, key rotation, and policy changes.
//! It implements immutable audit trails with structured logging and threat detection.

use crate::models::security::{<PERSON>t<PERSON><PERSON>ord, AuditResult, SecurityError};
use anyhow::{Context, Result};
use chrono::{DateTime, Utc};
use serde_json::Value;
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{debug, error, info, warn};
use uuid::Uuid;

pub mod storage;
pub mod threat_detection;

// Re-export threat detection types
#[cfg(feature = "security-storage")]
pub use threat_detection::{ThreatDetector, AdvancedThreatDetector, ThreatDetectionConfig};

/// Audit service for logging security operations
#[cfg(feature = "security-storage")]
pub struct AuditService {
    storage: Arc<dyn AuditStorage + Send + Sync>,
    threat_detector: Arc<ThreatDetector>,
    config: AuditConfig,
}

/// Configuration for audit logging
#[cfg(feature = "security-storage")]
#[derive(Debug, Clone)]
pub struct AuditConfig {
    /// Whether to enable async audit logging (default: true)
    pub async_logging: bool,
    /// Buffer size for batch operations (default: 100)
    pub batch_size: usize,
    /// Flush interval in seconds (default: 30)
    pub flush_interval_secs: u64,
    /// Enable threat detection (default: true)
    pub enable_threat_detection: bool,
    /// Risk score threshold for alerts (default: 0.7)
    pub risk_threshold: f64,
}

impl Default for AuditConfig {
    fn default() -> Self {
        Self {
            async_logging: true,
            batch_size: 100,
            flush_interval_secs: 30,
            enable_threat_detection: true,
            risk_threshold: 0.7,
        }
    }
}

/// Trait for audit storage backends
#[cfg(feature = "security-storage")]
#[async_trait::async_trait]
pub trait AuditStorage {
    /// Store a single audit record
    async fn store_record(&self, record: &AuditRecord) -> Result<(), SecurityError>;
    
    /// Store multiple audit records in batch
    async fn store_batch(&self, records: &[AuditRecord]) -> Result<(), SecurityError>;
    
    /// Query audit records with filters
    async fn query_records(
        &self,
        filters: &AuditQuery,
    ) -> Result<Vec<AuditRecord>, SecurityError>;
    
    /// Get audit statistics
    async fn get_statistics(&self, time_range: TimeRange) -> Result<AuditStatistics, SecurityError>;
}

/// Query parameters for audit records
#[cfg(feature = "security-storage")]
#[derive(Debug, Clone)]
pub struct AuditQuery {
    pub user_id: Option<String>,
    pub operation: Option<String>,
    pub resource_type: Option<String>,
    pub resource_id: Option<String>,
    pub result: Option<AuditResult>,
    pub time_range: Option<TimeRange>,
    pub risk_score_min: Option<f64>,
    pub limit: Option<usize>,
    pub offset: Option<usize>,
}

/// Time range for queries
#[cfg(feature = "security-storage")]
#[derive(Debug, Clone)]
pub struct TimeRange {
    pub start: DateTime<Utc>,
    pub end: DateTime<Utc>,
}

/// Audit statistics
#[cfg(feature = "security-storage")]
#[derive(Debug, Clone)]
pub struct AuditStatistics {
    pub total_operations: u64,
    pub successful_operations: u64,
    pub failed_operations: u64,
    pub unauthorized_attempts: u64,
    pub forbidden_attempts: u64,
    pub high_risk_operations: u64,
    pub unique_users: u64,
    pub unique_resources: u64,
}

/// Context for audit operations
#[cfg(feature = "security-storage")]
#[derive(Debug, Clone)]
pub struct AuditContext {
    pub user_id: Option<String>,
    pub ip_address: Option<String>,
    pub user_agent: Option<String>,
    pub session_id: Option<String>,
    pub request_id: Option<String>,
}

#[cfg(feature = "security-storage")]
impl AuditService {
    /// Create a new audit service
    /// 
    /// # Arguments
    /// * `storage` - Storage backend for audit records
    /// * `config` - Audit configuration
    /// 
    /// # Returns
    /// * New audit service instance
    pub async fn new(
        storage: Arc<dyn AuditStorage + Send + Sync>,
        config: AuditConfig,
    ) -> Result<Self, SecurityError> {
        info!("Initializing audit service");
        debug!("Audit config: async={}, batch_size={}, flush_interval={}s", 
               config.async_logging, config.batch_size, config.flush_interval_secs);
        
        let threat_config = ThreatDetectionConfig {
            risk_threshold: config.risk_threshold,
            ..ThreatDetectionConfig::default()
        };
        let threat_detector = Arc::new(ThreatDetector::new(threat_config).await?);
        
        let service = Self {
            storage,
            threat_detector,
            config,
        };
        
        info!("Audit service initialized successfully");
        Ok(service)
    }
    
    /// Log a security operation
    /// 
    /// # Arguments
    /// * `operation` - Operation being performed
    /// * `resource_type` - Type of resource being accessed
    /// * `resource_id` - ID of the resource
    /// * `result` - Result of the operation
    /// * `context` - Additional context for the operation
    /// 
    /// # Returns
    /// * `Result<(), SecurityError>` - Success or audit error
    pub async fn log_operation(
        &self,
        operation: &str,
        resource_type: &str,
        resource_id: &str,
        result: AuditResult,
        context: AuditContext,
    ) -> Result<(), SecurityError> {
        self.log_operation_with_metadata(
            operation,
            resource_type,
            resource_id,
            result,
            context,
            None,
        ).await
    }
    
    /// Log a security operation with additional metadata
    /// 
    /// # Arguments
    /// * `operation` - Operation being performed
    /// * `resource_type` - Type of resource being accessed
    /// * `resource_id` - ID of the resource
    /// * `result` - Result of the operation
    /// * `context` - Additional context for the operation
    /// * `metadata` - Optional additional metadata
    /// 
    /// # Returns
    /// * `Result<(), SecurityError>` - Success or audit error
    pub async fn log_operation_with_metadata(
        &self,
        operation: &str,
        resource_type: &str,
        resource_id: &str,
        result: AuditResult,
        context: AuditContext,
        metadata: Option<HashMap<String, Value>>,
    ) -> Result<(), SecurityError> {
        let audit_id = Uuid::new_v4().to_string();
        let timestamp = Utc::now();
        
        // Calculate risk score if threat detection is enabled
        let risk_score = if self.config.enable_threat_detection {
            Some(self.threat_detector.calculate_comprehensive_risk_score(
                operation,
                resource_type,
                &result,
                &context,
            ).await?)
        } else {
            None
        };
        
        let record = AuditRecord {
            audit_id,
            user_id: context.user_id,
            operation: operation.to_string(),
            resource_type: resource_type.to_string(),
            resource_id: resource_id.to_string(),
            timestamp,
            ip_address: context.ip_address,
            user_agent: context.user_agent,
            result,
            risk_score,
            metadata,
        };
        
        // Log high-risk operations immediately
        if let Some(score) = risk_score {
            if score >= self.config.risk_threshold {
                warn!("High-risk operation detected: {} on {} (risk: {:.3})", 
                      operation, resource_type, score);
            }
        }
        
        // Store the audit record
        if self.config.async_logging {
            // TODO: Implement async batching for better performance
            self.storage.store_record(&record).await?;
        } else {
            self.storage.store_record(&record).await?;
        }
        
        debug!("Audit record stored: {} - {} on {}", 
               record.audit_id, operation, resource_type);
        
        Ok(())
    }
    
    /// Query audit records
    /// 
    /// # Arguments
    /// * `query` - Query parameters
    /// 
    /// # Returns
    /// * `Result<Vec<AuditRecord>, SecurityError>` - Matching audit records
    pub async fn query_records(&self, query: &AuditQuery) -> Result<Vec<AuditRecord>, SecurityError> {
        debug!("Querying audit records with filters");
        self.storage.query_records(query).await
    }
    
    /// Get audit statistics for a time range
    /// 
    /// # Arguments
    /// * `time_range` - Time range to analyze
    /// 
    /// # Returns
    /// * `Result<AuditStatistics, SecurityError>` - Audit statistics
    pub async fn get_statistics(&self, time_range: TimeRange) -> Result<AuditStatistics, SecurityError> {
        debug!("Getting audit statistics for range: {} to {}", 
               time_range.start, time_range.end);
        self.storage.get_statistics(time_range).await
    }
    
    /// Perform health check on audit service
    /// 
    /// # Returns
    /// * `Result<(), SecurityError>` - Success or health check error
    pub async fn health_check(&self) -> Result<(), SecurityError> {
        debug!("Performing audit service health check");
        
        // Test audit logging with a health check record
        let context = AuditContext {
            user_id: Some("system".to_string()),
            ip_address: Some("127.0.0.1".to_string()),
            user_agent: Some("audit-service/health-check".to_string()),
            session_id: None,
            request_id: None,
        };
        
        self.log_operation(
            "health_check",
            "audit_service",
            "health_check",
            AuditResult::Success,
            context,
        ).await?;
        
        info!("Audit service health check passed");
        Ok(())
    }
}


// Placeholder implementations when security-storage feature is disabled
#[cfg(not(feature = "security-storage"))]
pub struct AuditService;

#[cfg(not(feature = "security-storage"))]
pub struct AuditConfig;

#[cfg(not(feature = "security-storage"))]
pub struct AuditContext;

#[cfg(not(feature = "security-storage"))]
impl AuditService {
    pub async fn new(
        _storage: Arc<dyn AuditStorage + Send + Sync>,
        _config: AuditConfig,
    ) -> Result<Self, SecurityError> {
        Err(SecurityError::FeatureNotEnabled("Security storage feature not enabled".to_string()))
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[cfg(feature = "security-storage")]
    #[test]
    fn test_audit_config_defaults() {
        let config = AuditConfig::default();
        assert!(config.async_logging);
        assert_eq!(config.batch_size, 100);
        assert_eq!(config.flush_interval_secs, 30);
        assert!(config.enable_threat_detection);
        assert_eq!(config.risk_threshold, 0.7);
    }
    
    #[cfg(feature = "security-storage")]
    #[test]
    fn test_time_range_creation() {
        let start = Utc::now();
        let end = start + chrono::Duration::hours(1);
        
        let range = TimeRange { start, end };
        assert!(range.end > range.start);
    }
    
    #[cfg(feature = "security-storage")]
    #[tokio::test]
    async fn test_threat_detector_initialization() {
        let config = ThreatDetectionConfig {
            risk_threshold: 0.8,
            ..ThreatDetectionConfig::default()
        };
        let detector = ThreatDetector::new(config).await.unwrap();
        
        let stats = detector.get_statistics().await.unwrap();
        assert!(stats.active_threat_patterns > 0);
    }
    
    #[cfg(feature = "security-storage")]
    #[tokio::test]
    async fn test_risk_score_calculation() {
        let config = ThreatDetectionConfig {
            risk_threshold: 0.7,
            ..ThreatDetectionConfig::default()
        };
        let detector = ThreatDetector::new(config).await.unwrap();
        let context = AuditContext {
            user_id: Some("test-user".to_string()),
            ip_address: Some("***********".to_string()),
            user_agent: Some("test-agent".to_string()),
            session_id: None,
            request_id: None,
        };
        
        let score = detector.calculate_comprehensive_risk_score(
            "authenticate",
            "user_account",
            &AuditResult::Unauthorized,
            &context,
        ).await.unwrap();
        
        assert!(score >= 0.0 && score <= 1.0); // Should be valid risk score
    }
}