//! Threat detection and risk scoring for audit operations
//! 
//! This module provides real-time threat detection capabilities for audit events
//! including anomaly detection, risk scoring, and suspicious pattern identification.

use crate::models::security::{AuditResult, SecurityError};
use crate::storage::audit::AuditContext;
use anyhow::{Context, Result};
use chrono::{DateTime, Duration, Utc, Timelike};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{debug, info, warn};

/// Advanced threat detection service
#[cfg(feature = "security-storage")]
pub struct AdvancedThreatDetector {
    /// Risk scoring configuration
    config: ThreatDetectionConfig,
    /// Pattern database for threat detection
    patterns: Arc<RwLock<ThreatPatternDatabase>>,
    /// Recent activity tracking for anomaly detection
    activity_tracker: Arc<RwLock<ActivityTracker>>,
}

/// Configuration for threat detection
#[cfg(feature = "security-storage")]
#[derive(Debug, Clone)]
pub struct ThreatDetectionConfig {
    /// Base risk threshold for alerts (0.0 to 1.0, default: 0.7)
    pub risk_threshold: f64,
    /// Enable machine learning enhanced detection (default: true)
    pub enable_ml_detection: bool,
    /// Time window for anomaly detection in minutes (default: 60)
    pub anomaly_window_minutes: i64,
    /// Minimum events for statistical analysis (default: 10)
    pub min_events_for_analysis: usize,
    /// Enable IP-based anomaly detection (default: true)
    pub enable_ip_analysis: bool,
    /// Enable user behavior analysis (default: true)
    pub enable_user_behavior_analysis: bool,
}

impl Default for ThreatDetectionConfig {
    fn default() -> Self {
        Self {
            risk_threshold: 0.7,
            enable_ml_detection: true,
            anomaly_window_minutes: 60,
            min_events_for_analysis: 10,
            enable_ip_analysis: true,
            enable_user_behavior_analysis: true,
        }
    }
}

/// Database of threat patterns and indicators
#[cfg(feature = "security-storage")]
#[derive(Debug)]
struct ThreatPatternDatabase {
    /// Known malicious patterns
    malicious_patterns: Vec<ThreatPattern>,
    /// Suspicious operation patterns
    suspicious_operations: HashMap<String, f64>,
    /// IP-based threat intelligence
    ip_intelligence: HashMap<String, IpRiskProfile>,
    /// User behavior baselines
    user_baselines: HashMap<String, UserBehaviorBaseline>,
}

/// Individual threat pattern
#[cfg(feature = "security-storage")]
#[derive(Debug, Clone)]
struct ThreatPattern {
    /// Pattern name/ID
    name: String,
    /// Operation pattern (regex or exact match)
    operation_pattern: String,
    /// Resource type pattern
    resource_pattern: Option<String>,
    /// Risk score (0.0 to 1.0)
    risk_score: f64,
    /// Pattern description
    description: String,
    /// Severity level
    severity: ThreatSeverity,
    /// MITRE ATT&CK technique if applicable
    mitre_technique: Option<String>,
}

/// IP risk profile
#[cfg(feature = "security-storage")]
#[derive(Debug, Clone)]
struct IpRiskProfile {
    /// Base risk score for this IP
    base_risk: f64,
    /// Number of failed attempts
    failed_attempts: u32,
    /// Last seen timestamp
    last_seen: DateTime<Utc>,
    /// Geographic location info
    location: Option<String>,
    /// Known threat intelligence sources
    threat_sources: Vec<String>,
}

/// User behavior baseline
#[cfg(feature = "security-storage")]
#[derive(Debug, Clone)]
struct UserBehaviorBaseline {
    /// User ID
    user_id: String,
    /// Typical operation patterns
    typical_operations: HashMap<String, u32>,
    /// Typical access times (hours of day)
    typical_hours: Vec<u8>,
    /// Average risk score for this user
    average_risk: f64,
    /// Last activity timestamp
    last_activity: DateTime<Utc>,
    /// Number of sessions analyzed
    sessions_analyzed: u32,
}

/// Threat severity levels
#[cfg(feature = "security-storage")]
#[derive(Debug, Clone, PartialEq)]
enum ThreatSeverity {
    Info,
    Low,
    Medium,
    High,
    Critical,
}

/// Activity tracker for anomaly detection
#[cfg(feature = "security-storage")]
#[derive(Debug)]
struct ActivityTracker {
    /// Recent events by user
    user_events: HashMap<String, Vec<ActivityEvent>>,
    /// Recent events by IP
    ip_events: HashMap<String, Vec<ActivityEvent>>,
    /// Global activity statistics
    global_stats: GlobalActivityStats,
}

/// Individual activity event
#[cfg(feature = "security-storage")]
#[derive(Debug, Clone)]
struct ActivityEvent {
    /// Event timestamp
    timestamp: DateTime<Utc>,
    /// Operation performed
    operation: String,
    /// Resource accessed
    resource_type: String,
    /// Result of operation
    result: AuditResult,
    /// Calculated risk score
    risk_score: f64,
}

/// Global activity statistics
#[cfg(feature = "security-storage")]
#[derive(Debug, Default)]
struct GlobalActivityStats {
    /// Total events processed
    total_events: u64,
    /// Events by operation type
    operation_counts: HashMap<String, u64>,
    /// Average risk score
    average_risk: f64,
    /// Peak activity hours
    peak_hours: HashMap<u8, u64>,
}

#[cfg(feature = "security-storage")]
impl AdvancedThreatDetector {
    /// Create a new advanced threat detector
    /// 
    /// # Arguments
    /// * `config` - Threat detection configuration
    /// 
    /// # Returns
    /// * New threat detector instance
    pub async fn new(config: ThreatDetectionConfig) -> Result<Self, SecurityError> {
        info!("Initializing advanced threat detector");
        debug!("Threat detection config: threshold={:.3}, ml_enabled={}, window={}min", 
               config.risk_threshold, config.enable_ml_detection, config.anomaly_window_minutes);
        
        let patterns = Self::initialize_threat_patterns().await?;
        let activity_tracker = ActivityTracker {
            user_events: HashMap::new(),
            ip_events: HashMap::new(),
            global_stats: GlobalActivityStats::default(),
        };
        
        let detector = Self {
            config,
            patterns: Arc::new(RwLock::new(patterns)),
            activity_tracker: Arc::new(RwLock::new(activity_tracker)),
        };
        
        info!("Advanced threat detector initialized successfully");
        Ok(detector)
    }
    
    /// Calculate comprehensive risk score for an operation
    /// 
    /// # Arguments
    /// * `operation` - Operation being performed
    /// * `resource_type` - Type of resource being accessed
    /// * `result` - Result of the operation
    /// * `context` - Additional context for the operation
    /// 
    /// # Returns
    /// * `Result<f64, SecurityError>` - Risk score (0.0 to 1.0) or error
    pub async fn calculate_comprehensive_risk_score(
        &self,
        operation: &str,
        resource_type: &str,
        result: &AuditResult,
        context: &AuditContext,
    ) -> Result<f64, SecurityError> {
        debug!("Calculating comprehensive risk score for: {} on {}", operation, resource_type);
        
        let mut total_risk = 0.0f64;
        let mut risk_factors = Vec::new();
        
        // 1. Base risk from operation result
        let result_risk = Self::calculate_result_risk(result);
        total_risk += result_risk;
        risk_factors.push(format!("result_risk: {:.3}", result_risk));
        
        // 2. Pattern-based risk assessment
        let pattern_risk = self.assess_pattern_risk(operation, resource_type).await?;
        total_risk += pattern_risk;
        risk_factors.push(format!("pattern_risk: {:.3}", pattern_risk));
        
        // 3. IP-based risk assessment
        if self.config.enable_ip_analysis {
            if let Some(ref ip) = context.ip_address {
                let ip_risk = self.assess_ip_risk(ip).await?;
                total_risk += ip_risk;
                risk_factors.push(format!("ip_risk: {:.3}", ip_risk));
            }
        }
        
        // 4. User behavior analysis
        if self.config.enable_user_behavior_analysis {
            if let Some(ref user_id) = context.user_id {
                let behavior_risk = self.assess_user_behavior_risk(
                    user_id, operation, resource_type, result
                ).await?;
                total_risk += behavior_risk;
                risk_factors.push(format!("behavior_risk: {:.3}", behavior_risk));
            }
        }
        
        // 5. Temporal anomaly detection
        let temporal_risk = self.assess_temporal_anomalies(operation, &context).await?;
        total_risk += temporal_risk;
        risk_factors.push(format!("temporal_risk: {:.3}", temporal_risk));
        
        // 6. Context-based risk factors
        let context_risk = Self::assess_context_risk(context);
        total_risk += context_risk;
        risk_factors.push(format!("context_risk: {:.3}", context_risk));
        
        // Normalize risk score to 0.0-1.0 range
        let final_risk = (total_risk / 6.0).min(1.0).max(0.0);
        
        // Log high-risk operations with detailed breakdown
        if final_risk >= self.config.risk_threshold {
            warn!("High-risk operation detected: {} on {} (final_risk: {:.3}) - Factors: [{}]", 
                  operation, resource_type, final_risk, risk_factors.join(", "));
        } else {
            debug!("Risk assessment completed: {} on {} (risk: {:.3})", 
                   operation, resource_type, final_risk);
        }
        
        // Update activity tracking
        self.update_activity_tracking(operation, resource_type, result, context, final_risk).await?;
        
        Ok(final_risk)
    }
    
    /// Initialize threat pattern database
    async fn initialize_threat_patterns() -> Result<ThreatPatternDatabase, SecurityError> {
        debug!("Initializing threat pattern database");
        
        let malicious_patterns = vec![
            ThreatPattern {
                name: "multiple_auth_failures".to_string(),
                operation_pattern: "auth|login|authenticate".to_string(),
                resource_pattern: Some("user|account".to_string()),
                risk_score: 0.8,
                description: "Multiple authentication failures indicating brute force attack".to_string(),
                severity: ThreatSeverity::High,
                mitre_technique: Some("T1110".to_string()), // Brute Force
            },
            ThreatPattern {
                name: "privilege_escalation".to_string(),
                operation_pattern: "admin|sudo|elevate|promote".to_string(),
                resource_pattern: Some("user|role|permission".to_string()),
                risk_score: 0.9,
                description: "Potential privilege escalation attempt".to_string(),
                severity: ThreatSeverity::Critical,
                mitre_technique: Some("T1068".to_string()), // Exploitation for Privilege Escalation
            },
            ThreatPattern {
                name: "sensitive_data_access".to_string(),
                operation_pattern: "decrypt|read|download|export".to_string(),
                resource_pattern: Some("secret|key|credential|pii|financial".to_string()),
                risk_score: 0.7,
                description: "Access to sensitive data resources".to_string(),
                severity: ThreatSeverity::High,
                mitre_technique: Some("T1005".to_string()), // Data from Local System
            },
            ThreatPattern {
                name: "bulk_data_operations".to_string(),
                operation_pattern: "batch|bulk|mass|export_all".to_string(),
                resource_pattern: None,
                risk_score: 0.6,
                description: "Bulk data operations potentially indicating data exfiltration".to_string(),
                severity: ThreatSeverity::Medium,
                mitre_technique: Some("T1041".to_string()), // Exfiltration Over C2 Channel
            },
            ThreatPattern {
                name: "unusual_admin_operations".to_string(),
                operation_pattern: "delete|destroy|purge|wipe".to_string(),
                resource_pattern: Some("database|backup|log|audit".to_string()),
                risk_score: 0.8,
                description: "Unusual administrative operations on critical resources".to_string(),
                severity: ThreatSeverity::High,
                mitre_technique: Some("T1070".to_string()), // Indicator Removal on Host
            },
        ];
        
        let suspicious_operations = [
            ("decrypt", 0.6),
            ("admin", 0.7),
            ("export", 0.5),
            ("delete", 0.6),
            ("modify_permissions", 0.7),
            ("access_logs", 0.5),
        ].iter().map(|(k, v)| (k.to_string(), *v)).collect();
        
        let database = ThreatPatternDatabase {
            malicious_patterns,
            suspicious_operations,
            ip_intelligence: HashMap::new(),
            user_baselines: HashMap::new(),
        };
        
        info!("Threat pattern database initialized with {} patterns", database.malicious_patterns.len());
        Ok(database)
    }
    
    /// Calculate base risk from operation result
    fn calculate_result_risk(result: &AuditResult) -> f64 {
        match result {
            AuditResult::Success => 0.1,
            AuditResult::Failure(_) => 0.4,
            AuditResult::Unauthorized => 0.8,
            AuditResult::Forbidden => 0.7,
        }
    }
    
    /// Assess risk based on threat patterns
    async fn assess_pattern_risk(&self, operation: &str, resource_type: &str) -> Result<f64, SecurityError> {
        let patterns = self.patterns.read().await;
        let mut max_risk: f64 = 0.0;
        
        // Check against malicious patterns
        for pattern in &patterns.malicious_patterns {
            let operation_matches = operation.to_lowercase().contains(&pattern.operation_pattern.to_lowercase()) ||
                regex::Regex::new(&pattern.operation_pattern)
                    .map(|re| re.is_match(&operation.to_lowercase()))
                    .unwrap_or(false);
            
            let resource_matches = pattern.resource_pattern.as_ref()
                .map(|pattern_str| resource_type.to_lowercase().contains(&pattern_str.to_lowercase()))
                .unwrap_or(true);
            
            if operation_matches && resource_matches {
                max_risk = max_risk.max(pattern.risk_score);
                debug!("Threat pattern matched: {} (risk: {:.3})", pattern.name, pattern.risk_score);
            }
        }
        
        // Check suspicious operations
        for (op_pattern, risk) in &patterns.suspicious_operations {
            if operation.to_lowercase().contains(&op_pattern.to_lowercase()) {
                max_risk = max_risk.max(*risk);
                debug!("Suspicious operation detected: {} (risk: {:.3})", op_pattern, risk);
            }
        }
        
        Ok(max_risk)
    }
    
    /// Assess IP-based risk
    async fn assess_ip_risk(&self, ip: &str) -> Result<f64, SecurityError> {
        let patterns = self.patterns.read().await;
        
        if let Some(ip_profile) = patterns.ip_intelligence.get(ip) {
            let mut risk = ip_profile.base_risk;
            
            // Increase risk based on failed attempts
            if ip_profile.failed_attempts > 5 {
                risk += 0.3;
            } else if ip_profile.failed_attempts > 2 {
                risk += 0.1;
            }
            
            // Check if IP has been seen recently
            let time_since_last_seen = Utc::now() - ip_profile.last_seen;
            if time_since_last_seen < Duration::minutes(10) && ip_profile.failed_attempts > 0 {
                risk += 0.2; // Recent activity from problematic IP
            }
            
            Ok(risk.min(1.0))
        } else {
            // Unknown IP - moderate risk
            Ok(0.2)
        }
    }
    
    /// Assess user behavior-based risk
    async fn assess_user_behavior_risk(
        &self,
        user_id: &str,
        operation: &str,
        resource_type: &str,
        result: &AuditResult,
    ) -> Result<f64, SecurityError> {
        let patterns = self.patterns.read().await;
        
        if let Some(baseline) = patterns.user_baselines.get(user_id) {
            let mut risk: f64 = 0.0;
            
            // Check if operation is unusual for this user
            let operation_frequency = baseline.typical_operations.get(operation).unwrap_or(&0);
            if *operation_frequency == 0 {
                risk += 0.3; // User never performed this operation before
                debug!("Unusual operation for user {}: {}", user_id, operation);
            } else if *operation_frequency < 3 {
                risk += 0.1; // Rarely performed operation
            }
            
            // Check if operation is happening at unusual time
            let current_hour = Utc::now().hour() as u8;
            if !baseline.typical_hours.contains(&current_hour) {
                risk += 0.2; // Unusual time for this user
                debug!("Unusual time for user {}: {}:00", user_id, current_hour);
            }
            
            // Check if result pattern is unusual
            if matches!(result, AuditResult::Failure(_) | AuditResult::Unauthorized | AuditResult::Forbidden) {
                if baseline.average_risk < 0.3 {
                    risk += 0.3; // Usually successful user now failing
                }
            }
            
            Ok(risk.min(1.0))
        } else {
            // New user - moderate risk until baseline is established
            Ok(0.3)
        }
    }
    
    /// Assess temporal anomalies
    async fn assess_temporal_anomalies(
        &self,
        operation: &str,
        context: &AuditContext,
    ) -> Result<f64, SecurityError> {
        let activity = self.activity_tracker.read().await;
        let mut risk: f64 = 0.0;
        
        // Check for rapid successive operations
        if let Some(ref user_id) = context.user_id {
            if let Some(user_events) = activity.user_events.get(user_id) {
                let recent_events: Vec<&ActivityEvent> = user_events
                    .iter()
                    .filter(|event| Utc::now() - event.timestamp < Duration::minutes(5))
                    .collect();
                
                if recent_events.len() > 10 {
                    risk += 0.4; // Unusually high activity
                    debug!("High activity detected for user {}: {} events in 5 minutes", 
                           user_id, recent_events.len());
                }
                
                // Check for repeated failed operations
                let recent_failures = recent_events
                    .iter()
                    .filter(|event| !matches!(event.result, AuditResult::Success))
                    .count();
                
                if recent_failures > 3 {
                    risk += 0.3; // Multiple failures in short time
                    debug!("Multiple failures detected for user {}: {} failures in 5 minutes", 
                           user_id, recent_failures);
                }
            }
        }
        
        // Check global activity patterns
        let operation_count = activity.global_stats.operation_counts.get(operation).unwrap_or(&0);
        if *operation_count == 0 {
            risk += 0.1; // Very rare operation globally
        }
        
        Ok(risk.min(1.0))
    }
    
    /// Assess context-based risk factors
    fn assess_context_risk(context: &AuditContext) -> f64 {
        let mut risk: f64 = 0.0;
        
        // Missing context increases risk
        if context.user_id.is_none() {
            risk += 0.2; // Anonymous operation
        }
        
        if context.ip_address.is_none() {
            risk += 0.1; // Missing IP
        }
        
        if context.user_agent.is_none() {
            risk += 0.1; // Missing user agent
        }
        
        // Suspicious user agents
        if let Some(ref user_agent) = context.user_agent {
            let suspicious_agents = ["curl", "wget", "python", "bot", "scanner", "crawler"];
            if suspicious_agents.iter().any(|&agent| user_agent.to_lowercase().contains(agent)) {
                risk += 0.3;
            }
        }
        
        risk.min(1.0)
    }
    
    /// Update activity tracking for future anomaly detection
    async fn update_activity_tracking(
        &self,
        operation: &str,
        resource_type: &str,
        result: &AuditResult,
        context: &AuditContext,
        risk_score: f64,
    ) -> Result<(), SecurityError> {
        let mut activity = self.activity_tracker.write().await;
        let now = Utc::now();
        
        let event = ActivityEvent {
            timestamp: now,
            operation: operation.to_string(),
            resource_type: resource_type.to_string(),
            result: result.clone(),
            risk_score,
        };
        
        // Update user events
        if let Some(ref user_id) = context.user_id {
            let user_events = activity.user_events.entry(user_id.clone()).or_insert_with(Vec::new);
            user_events.push(event.clone());
            
            // Keep only recent events (within anomaly window)
            let cutoff = now - Duration::minutes(self.config.anomaly_window_minutes);
            user_events.retain(|e| e.timestamp > cutoff);
        }
        
        // Update IP events
        if let Some(ref ip) = context.ip_address {
            let ip_events = activity.ip_events.entry(ip.clone()).or_insert_with(Vec::new);
            ip_events.push(event);
            
            // Keep only recent events
            let cutoff = now - Duration::minutes(self.config.anomaly_window_minutes);
            ip_events.retain(|e| e.timestamp > cutoff);
        }
        
        // Update global statistics
        activity.global_stats.total_events += 1;
        *activity.global_stats.operation_counts.entry(operation.to_string()).or_insert(0) += 1;
        
        // Update average risk (running average)
        let total_events = activity.global_stats.total_events as f64;
        activity.global_stats.average_risk = 
            (activity.global_stats.average_risk * (total_events - 1.0) + risk_score) / total_events;
        
        // Update peak hours
        let current_hour = now.hour() as u8;
        *activity.global_stats.peak_hours.entry(current_hour).or_insert(0) += 1;
        
        Ok(())
    }
    
    /// Get threat detection statistics
    pub async fn get_statistics(&self) -> Result<ThreatDetectionStatistics, SecurityError> {
        let activity = self.activity_tracker.read().await;
        let patterns = self.patterns.read().await;
        
        Ok(ThreatDetectionStatistics {
            total_events_analyzed: activity.global_stats.total_events,
            average_risk_score: activity.global_stats.average_risk,
            active_threat_patterns: patterns.malicious_patterns.len(),
            monitored_users: activity.user_events.len(),
            monitored_ips: activity.ip_events.len(),
            high_risk_operations: activity.user_events
                .values()
                .flatten()
                .filter(|event| event.risk_score >= 0.7)
                .count(),
        })
    }
}

/// Statistics for threat detection system
#[cfg(feature = "security-storage")]
#[derive(Debug, Clone)]
pub struct ThreatDetectionStatistics {
    pub total_events_analyzed: u64,
    pub average_risk_score: f64,
    pub active_threat_patterns: usize,
    pub monitored_users: usize,
    pub monitored_ips: usize,
    pub high_risk_operations: usize,
}

// Type alias for convenience
#[cfg(feature = "security-storage")]
pub type ThreatDetector = AdvancedThreatDetector;

// Placeholder implementations when security-storage feature is disabled
#[cfg(not(feature = "security-storage"))]
pub struct AdvancedThreatDetector;

#[cfg(not(feature = "security-storage"))]
pub struct ThreatDetectionConfig;

#[cfg(not(feature = "security-storage"))]
pub struct ThreatDetectionStatistics;

#[cfg(not(feature = "security-storage"))]
impl AdvancedThreatDetector {
    pub async fn new(_config: ThreatDetectionConfig) -> Result<Self, SecurityError> {
        Err(SecurityError::FeatureNotEnabled("Security storage feature not enabled".to_string()))
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[cfg(feature = "security-storage")]
    #[tokio::test]
    async fn test_threat_detector_initialization() {
        let config = ThreatDetectionConfig::default();
        let detector = AdvancedThreatDetector::new(config).await.unwrap();
        
        let stats = detector.get_statistics().await.unwrap();
        assert_eq!(stats.total_events_analyzed, 0);
        assert!(stats.active_threat_patterns > 0);
    }
    
    #[cfg(feature = "security-storage")]
    #[tokio::test]
    async fn test_risk_score_calculation() {
        let config = ThreatDetectionConfig::default();
        let detector = AdvancedThreatDetector::new(config).await.unwrap();
        
        let context = AuditContext {
            user_id: Some("test-user".to_string()),
            ip_address: Some("***********".to_string()),
            user_agent: Some("legitimate-client".to_string()),
            session_id: None,
            request_id: None,
        };
        
        // Test high-risk operation
        let risk = detector.calculate_comprehensive_risk_score(
            "admin_delete",
            "user_account",
            &AuditResult::Unauthorized,
            &context,
        ).await.unwrap();
        
        assert!(risk > 0.5, "Should detect high risk for admin operation with unauthorized result");
        
        // Test low-risk operation
        let risk = detector.calculate_comprehensive_risk_score(
            "read",
            "public_data",
            &AuditResult::Success,
            &context,
        ).await.unwrap();
        
        assert!(risk < 0.5, "Should detect low risk for normal read operation");
    }
    
    #[cfg(feature = "security-storage")]
    #[tokio::test]
    async fn test_pattern_matching() {
        let config = ThreatDetectionConfig::default();
        let detector = AdvancedThreatDetector::new(config).await.unwrap();
        
        // Test privilege escalation pattern
        let risk = detector.assess_pattern_risk("admin_promote_user", "user_account").await.unwrap();
        assert!(risk > 0.8, "Should detect high risk for privilege escalation pattern");
        
        // Test normal operation
        let risk = detector.assess_pattern_risk("read_profile", "user_data").await.unwrap();
        assert!(risk < 0.3, "Should detect low risk for normal operation");
    }
    
    #[cfg(feature = "security-storage")]
    #[test]
    fn test_context_risk_assessment() {
        // Test missing context
        let context = AuditContext {
            user_id: None,
            ip_address: None,
            user_agent: None,
            session_id: None,
            request_id: None,
        };
        let risk = AdvancedThreatDetector::assess_context_risk(&context);
        assert!(risk > 0.3, "Should detect high risk for missing context");
        
        // Test suspicious user agent
        let context = AuditContext {
            user_id: Some("user".to_string()),
            ip_address: Some("*******".to_string()),
            user_agent: Some("curl/7.68.0".to_string()),
            session_id: None,
            request_id: None,
        };
        let risk = AdvancedThreatDetector::assess_context_risk(&context);
        assert!(risk > 0.2, "Should detect risk for suspicious user agent");
    }
}