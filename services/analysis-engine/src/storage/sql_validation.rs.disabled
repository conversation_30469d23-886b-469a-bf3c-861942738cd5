//! SQL Query Validation Module
//! 
//! Provides validation and security checks for SQL queries to prevent
//! SQL injection attacks and ensure proper parameterization.

use anyhow::{Context, Result};
use regex::Regex;
use std::collections::HashSet;
use tracing::{warn, info, error};

/// SQL Query validator for preventing injection attacks
pub struct SqlQueryValidator {
    /// Regex patterns for detecting unsafe SQL constructions
    unsafe_patterns: Vec<Regex>,
    /// SQL keywords that should not appear in user input
    sql_keywords: HashSet<String>,
}

impl Default for SqlQueryValidator {
    fn default() -> Self {
        Self::new()
    }
}

impl SqlQueryValidator {
    /// Create a new SQL query validator with default security rules
    pub fn new() -> Self {
        let unsafe_patterns = vec![
            // String concatenation in SQL contexts
            Regex::new(r"format!\s*\(\s*['\"].*?(SELECT|INSERT|UPDATE|DELETE|WHERE|LIMIT|OFFSET)").unwrap(),
            // Direct string interpolation patterns  
            Regex::new(r"\$\{.*\}").unwrap(),
            // Unparameterized LIMIT/OFFSET (numbers directly in SQL)
            Regex::new(r"(?i)\b(LIMIT|OFFSET)\s+\d+").unwrap(),
            // SQL comments that could indicate injection
            Regex::new(r"--|\*/|/\*").unwrap(),
            // UNION attacks
            Regex::new(r"(?i)\bUNION\s+(ALL\s+)?SELECT\b").unwrap(),
            // Stacked queries (semicolon followed by SQL keywords)
            Regex::new(r";.*?\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER)\b").unwrap(),
        ];
        
        let sql_keywords = [
            "SELECT", "INSERT", "UPDATE", "DELETE", "DROP", "CREATE", "ALTER",
            "UNION", "WHERE", "HAVING", "ORDER", "GROUP", "LIMIT", "OFFSET",
            "EXEC", "EXECUTE", "DECLARE", "TRUNCATE", "GRANT", "REVOKE",
            "INFORMATION_SCHEMA", "SYSOBJECTS", "SYSCOLUMNS", "XP_CMDSHELL"
        ].iter().map(|&s| s.to_uppercase()).collect();
        
        Self {
            unsafe_patterns,
            sql_keywords,
        }
    }
    
    /// Validate that a SQL query uses only safe, parameterized constructions
    pub fn validate_query_safety(&self, query: &str) -> Result<()> {
        info!("Validating SQL query safety: {}", self.sanitize_query_for_log(query));
        
        // Check for unsafe patterns
        for (i, pattern) in self.unsafe_patterns.iter().enumerate() {
            if pattern.is_match(query) {
                error!("Unsafe SQL pattern {} detected in query: {}", i, self.sanitize_query_for_log(query));
                return Err(anyhow::anyhow!(
                    "Unsafe SQL pattern detected (pattern {}): query may be vulnerable to injection",
                    i
                ));
            }
        }
        
        // Special check for LIMIT/OFFSET - they should use parameters
        if query.to_uppercase().contains("LIMIT") && !query.contains("@limit") {
            // Allow if it's a properly parameterized query being rebuilt
            if !query.contains("@") {
                warn!("LIMIT clause detected without parameter in query: {}", self.sanitize_query_for_log(query));
                return Err(anyhow::anyhow!("LIMIT clause must use @limit parameter"));
            }
        }
        
        if query.to_uppercase().contains("OFFSET") && !query.contains("@offset") {
            if !query.contains("@") {
                warn!("OFFSET clause detected without parameter in query: {}", self.sanitize_query_for_log(query));
                return Err(anyhow::anyhow!("OFFSET clause must use @offset parameter"));
            }
        }
        
        info!("SQL query validation passed");
        Ok(())
    }
    
    /// Validate that user input doesn't contain SQL injection attempts
    pub fn validate_user_input(&self, input: &str, field_name: &str) -> Result<()> {
        // Check for SQL keywords in user input (case insensitive)
        let upper_input = input.to_uppercase();
        for keyword in &self.sql_keywords {
            if upper_input.contains(keyword) {
                warn!("SQL keyword '{}' detected in user input for field '{}': {}", 
                      keyword, field_name, self.sanitize_input_for_log(input));
                // Don't fail here, just log - parameterized queries will handle this safely
                // But it's good to monitor for potential injection attempts
            }
        }
        
        // Check for obvious injection patterns
        if input.contains("';") || input.contains("'--") || input.contains("'/*") {
            warn!("Potential SQL injection pattern detected in field '{}': {}", 
                  field_name, self.sanitize_input_for_log(input));
        }
        
        // Check for extremely long inputs that might be injection attempts
        if input.len() > 1000 {
            warn!("Unusually long input ({} chars) in field '{}' - potential injection attempt", 
                  input.len(), field_name);
        }
        
        Ok(())
    }
    
    /// Extract and validate all SQL parameters from a query
    pub fn extract_parameters(&self, query: &str) -> Result<HashSet<String>> {
        let param_regex = Regex::new(r"@(\w+)").unwrap();
        let parameters: HashSet<String> = param_regex
            .captures_iter(query)
            .map(|cap| cap[1].to_string())
            .collect();
        
        info!("Extracted {} parameters from query: {:?}", parameters.len(), parameters);
        Ok(parameters)
    }
    
    /// Validate that all parameters in a query are bound
    pub fn validate_parameter_binding(&self, query: &str, bound_params: &HashSet<String>) -> Result<()> {
        let query_params = self.extract_parameters(query)?;
        
        // Check that all parameters in the query are bound
        for param in &query_params {
            if !bound_params.contains(param) {
                error!("Unbound parameter '{}' in query: {}", param, self.sanitize_query_for_log(query));
                return Err(anyhow::anyhow!("Parameter '{}' is used in query but not bound", param));
            }
        }
        
        // Warn about unused parameters (might indicate typos)
        for param in bound_params {
            if !query_params.contains(param) {
                warn!("Parameter '{}' is bound but not used in query", param);
            }
        }
        
        info!("Parameter binding validation passed: {} parameters validated", query_params.len());
        Ok(())
    }
    
    /// Sanitize a SQL query for safe logging (remove potential sensitive data)
    fn sanitize_query_for_log(&self, query: &str) -> String {
        // Replace potential parameter values with placeholders for logging
        let mut sanitized = query.to_string();
        
        // Remove any literal string values that might contain sensitive data
        let string_literal_regex = Regex::new(r"'[^']*'").unwrap();
        sanitized = string_literal_regex.replace_all(&sanitized, "'***'").to_string();
        
        // Truncate very long queries for logs
        if sanitized.len() > 200 {
            sanitized = format!("{}...", &sanitized[..200]);
        }
        
        sanitized
    }
    
    /// Sanitize user input for safe logging
    fn sanitize_input_for_log(&self, input: &str) -> String {
        // Truncate and mask potentially sensitive input
        if input.len() > 50 {
            format!("{}***", &input[..20])
        } else {
            input.replace("'", "***").replace("\"", "***")
        }
    }
}

/// Trait for validating SQL operations
pub trait SqlSecurityValidation {
    /// Validate a SQL query before execution
    fn validate_sql_security(&self, query: &str, params: &HashSet<String>) -> Result<()>;
}

impl SqlSecurityValidation for SqlQueryValidator {
    fn validate_sql_security(&self, query: &str, params: &HashSet<String>) -> Result<()> {
        self.validate_query_safety(query)
            .context("Query safety validation failed")?;
            
        self.validate_parameter_binding(query, params)
            .context("Parameter binding validation failed")?;
            
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_query_safety_validation() {
        let validator = SqlQueryValidator::new();
        
        // Safe queries should pass
        let safe_queries = vec![
            "SELECT * FROM users WHERE id = @user_id",
            "INSERT INTO users (name, email) VALUES (@name, @email)",
            "UPDATE users SET status = @status WHERE id = @id",
            "SELECT * FROM users LIMIT @limit OFFSET @offset",
        ];
        
        for query in safe_queries {
            assert!(validator.validate_query_safety(query).is_ok(), 
                    "Safe query should pass validation: {}", query);
        }
        
        // Unsafe queries should fail
        let unsafe_queries = vec![
            "SELECT * FROM users WHERE id = " + "user_input", // This would be caught by compiler anyway
            "SELECT * FROM users LIMIT 100", // Unparameterized LIMIT
            "SELECT * FROM users -- comment",
            "SELECT * FROM users /* comment */",
        ];
        
        // Note: The first test case wouldn't actually compile in Rust, but represents the pattern
        for query in &unsafe_queries[1..] { // Skip the first one
            assert!(validator.validate_query_safety(query).is_err(), 
                    "Unsafe query should fail validation: {}", query);
        }
    }
    
    #[test]
    fn test_user_input_validation() {
        let validator = SqlQueryValidator::new();
        
        // Normal inputs should pass (with warnings for SQL keywords)
        assert!(validator.validate_user_input("<EMAIL>", "email").is_ok());
        assert!(validator.validate_user_input("John Doe", "name").is_ok());
        
        // Injection attempts should be logged but not fail (parameterized queries handle them)
        assert!(validator.validate_user_input("'; DROP TABLE users; --", "email").is_ok());
        assert!(validator.validate_user_input("' OR '1'='1", "email").is_ok());
    }
    
    #[test]
    fn test_parameter_extraction() {
        let validator = SqlQueryValidator::new();
        
        let query = "SELECT * FROM users WHERE id = @user_id AND status = @status";
        let params = validator.extract_parameters(query).unwrap();
        
        assert_eq!(params.len(), 2);
        assert!(params.contains("user_id"));
        assert!(params.contains("status"));
    }
    
    #[test]
    fn test_parameter_binding_validation() {
        let validator = SqlQueryValidator::new();
        
        let query = "SELECT * FROM users WHERE id = @user_id AND status = @status";
        
        // All parameters bound - should pass
        let mut bound_params = HashSet::new();
        bound_params.insert("user_id".to_string());
        bound_params.insert("status".to_string());
        
        assert!(validator.validate_parameter_binding(query, &bound_params).is_ok());
        
        // Missing parameter - should fail
        bound_params.remove("status");
        assert!(validator.validate_parameter_binding(query, &bound_params).is_err());
    }
}