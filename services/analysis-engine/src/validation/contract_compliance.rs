//! Contract compliance validation implementation
//!
//! This module provides JSON schema-based validation for ensuring
//! all outputs comply with the AST output v1 contract.

use crate::contracts::AnalysisOutput;
use jsonschema::JSONSchema;
use serde::{Deserialize, Serialize};
use serde_json::Value;
use std::sync::RwLock;
use std::time::Instant;

/// AST output v1 schema loaded from contract definition
static AST_OUTPUT_SCHEMA: &str = include_str!("../../../../contracts/schemas/ast-output-v1.json");


/// Validation error with detailed information
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ValidationError {
    /// Error code for categorization
    pub code: String,
    /// Human-readable error message
    pub message: String,
    /// JSON path to the error location
    pub path: String,
    /// Error severity
    pub severity: ValidationSeverity,
}

/// Validation error severity levels
#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Deserialize, PartialEq, Eq)]
pub enum ValidationSeverity {
    Error,
    Warning,
    Info,
}

/// Contract validator for runtime validation
pub struct ContractValidator {
    config: crate::validation::ValidationConfig,
    metrics: RwLock<crate::validation::ValidationMetrics>,
}

impl ContractValidator {
    /// Create a new contract validator
    pub fn new(config: crate::validation::ValidationConfig) -> Self {
        Self {
            config,
            metrics: RwLock::new(crate::validation::ValidationMetrics {
                schema_version: "v1.0.0".to_string(),
                ..Default::default()
            }),
        }
    }
    
    /// Get current validation metrics
    pub fn metrics(&self) -> crate::validation::ValidationMetrics {
        self.metrics.read().unwrap().clone()
    }
    
    /// Validate an AnalysisOutput against the schema
    pub fn validate(&self, output: &AnalysisOutput) -> crate::validation::ValidationResult {
        if !self.config.enabled {
            return crate::validation::ValidationResult::Valid;
        }
        
        let start = Instant::now();
        let result = validate_ast_output(output);
        let duration_ms = start.elapsed().as_secs_f64() * 1000.0;
        
        // Record metrics
        if let Ok(mut metrics) = self.metrics.write() {
            metrics.record_validation(&result, duration_ms);
        }
        
        // Record prometheus metrics
        let is_valid = matches!(result, crate::validation::ValidationResult::Valid);
        let error_codes: Vec<String> = match &result {
            crate::validation::ValidationResult::Invalid(errors) => {
                errors.iter().map(|e| e.code.clone()).collect()
            }
            _ => vec![],
        };
        
        crate::metrics::prometheus::record_contract_validation(
            "ast-output-v1",
            is_valid,
            duration_ms / 1000.0, // Convert to seconds
            &error_codes,
        );
        
        result
    }
}

/// Validate an AnalysisOutput against the AST output v1 schema
pub fn validate_ast_output(output: &AnalysisOutput) -> crate::validation::ValidationResult {
    // Convert to JSON value
    let json_value = match serde_json::to_value(output) {
        Ok(value) => value,
        Err(e) => {
            return crate::validation::ValidationResult::Invalid(vec![
                ValidationError {
                    code: "SERIALIZATION_ERROR".to_string(),
                    message: format!("Failed to serialize output: {e}"),
                    path: "/".to_string(),
                    severity: ValidationSeverity::Error,
                }
            ]);
        }
    };
    
    // Perform JSON schema validation
    validate_against_schema(&json_value, "ast-output-v1")
}

/// Validate a JSON value against a named schema
pub fn validate_against_schema(value: &Value, schema_name: &str) -> crate::validation::ValidationResult {
    // Get or compile the schema
    let validator = match get_or_compile_schema(schema_name) {
        Ok(v) => v,
        Err(e) => {
            return crate::validation::ValidationResult::Invalid(vec![
                ValidationError {
                    code: "SCHEMA_LOAD_ERROR".to_string(),
                    message: format!("Failed to load schema: {e}"),
                    path: "/".to_string(),
                    severity: ValidationSeverity::Error,
                }
            ]);
        }
    };
    
    // Validate the value
    let validation_result = validator.validate(value);
    
    match validation_result {
        Ok(_) => {
            // Perform additional custom validations
            let mut errors = Vec::new();
            
            // Validate ID patterns
            if let Some(repo_id) = value.pointer("/repository/id").and_then(|v| v.as_str()) {
                if !is_valid_repository_id(repo_id) {
                    errors.push(ValidationError {
                        code: "INVALID_REPOSITORY_ID".to_string(),
                        message: format!("Repository ID '{repo_id}' does not match pattern 'repo_[a-zA-Z0-9]{{16}}'"),
                        path: "/repository/id".to_string(),
                        severity: ValidationSeverity::Error,
                    });
                }
            }
            
            if let Some(analysis_id) = value.pointer("/metadata/analysis_id").and_then(|v| v.as_str()) {
                if !is_valid_analysis_id(analysis_id) {
                    errors.push(ValidationError {
                        code: "INVALID_ANALYSIS_ID".to_string(),
                        message: format!("Analysis ID '{analysis_id}' does not match pattern 'analysis_[a-zA-Z0-9]{{16}}'"),
                        path: "/metadata/analysis_id".to_string(),
                        severity: ValidationSeverity::Error,
                    });
                }
            }
            
            // Validate chunk IDs
            if let Some(files) = value.pointer("/analysis/files").and_then(|v| v.as_array()) {
                for (file_idx, file) in files.iter().enumerate() {
                    if let Some(chunks) = file.pointer("/chunks").and_then(|v| v.as_array()) {
                        for (chunk_idx, chunk) in chunks.iter().enumerate() {
                            if let Some(chunk_id) = chunk.pointer("/chunk_id").and_then(|v| v.as_str()) {
                                if !is_valid_chunk_id(chunk_id) {
                                    errors.push(ValidationError {
                                        code: "INVALID_CHUNK_ID".to_string(),
                                        message: format!("Chunk ID '{chunk_id}' does not match pattern 'chunk_[a-zA-Z0-9]{{16}}'"),
                                        path: format!("/analysis/files/{file_idx}/chunks/{chunk_idx}/chunk_id"),
                                        severity: ValidationSeverity::Error,
                                    });
                                }
                            }
                        }
                    }
                }
            }
            
            // Validate enum values are lowercase
            validate_enum_values(&mut errors, value);
            
            if errors.is_empty() {
                crate::validation::ValidationResult::Valid
            } else {
                crate::validation::ValidationResult::Invalid(errors)
            }
        }
        Err(e) => {
            // Convert JSON schema errors to our error format
            let errors: Vec<ValidationError> = e
                .map(|err| ValidationError {
                    code: "SCHEMA_VALIDATION_ERROR".to_string(),
                    message: err.to_string(),
                    path: err.instance_path.to_string(),
                    severity: ValidationSeverity::Error,
                })
                .collect();
            
            crate::validation::ValidationResult::Invalid(errors)
        }
    }
}

/// Get or compile a schema by name
fn get_or_compile_schema(schema_name: &str) -> Result<&'static JSONSchema, String> {
    // Use a static validator for the AST output schema
    use std::sync::OnceLock;
    static VALIDATOR: OnceLock<JSONSchema> = OnceLock::new();
    
    let schema = VALIDATOR.get_or_init(|| {
        let schema_value: Value = serde_json::from_str(AST_OUTPUT_SCHEMA)
            .expect("Failed to parse schema JSON");
        
        JSONSchema::compile(&schema_value)
            .expect("Failed to compile schema")
    });
    
    match schema_name {
        "ast-output-v1" => Ok(schema),
        _ => Err(format!("Unknown schema: {schema_name}")),
    }
}

/// Validate repository ID pattern
fn is_valid_repository_id(id: &str) -> bool {
    id.len() == 21 && id.starts_with("repo_") && id[5..].chars().all(|c| c.is_alphanumeric())
}

/// Validate analysis ID pattern
fn is_valid_analysis_id(id: &str) -> bool {
    id.len() == 25 && id.starts_with("analysis_") && id[9..].chars().all(|c| c.is_alphanumeric())
}

/// Validate chunk ID pattern
fn is_valid_chunk_id(id: &str) -> bool {
    id.len() == 22 && id.starts_with("chunk_") && id[6..].chars().all(|c| c.is_alphanumeric())
}

/// Validate that enum values are lowercase strings
fn validate_enum_values(errors: &mut Vec<ValidationError>, value: &Value) {
    // Check pattern types
    if let Some(patterns) = value.pointer("/analysis/patterns").and_then(|v| v.as_array()) {
        for (idx, pattern) in patterns.iter().enumerate() {
            if let Some(pattern_type) = pattern.pointer("/pattern_type").and_then(|v| v.as_str()) {
                let valid_types = ["design_pattern", "anti_pattern", "security_issue", "performance_issue", "code_smell"];
                if !valid_types.contains(&pattern_type) {
                    errors.push(ValidationError {
                        code: "INVALID_ENUM_VALUE".to_string(),
                        message: format!("Pattern type '{pattern_type}' is not a valid enum value. Expected one of: {valid_types:?}"),
                        path: format!("/analysis/patterns/{idx}/pattern_type"),
                        severity: ValidationSeverity::Error,
                    });
                }
            }
        }
    }
    
    // Check symbol types and visibility
    if let Some(files) = value.pointer("/analysis/files").and_then(|v| v.as_array()) {
        for (file_idx, file) in files.iter().enumerate() {
            if let Some(symbols) = file.pointer("/symbols").and_then(|v| v.as_array()) {
                for (sym_idx, symbol) in symbols.iter().enumerate() {
                    // Check symbol type
                    if let Some(sym_type) = symbol.pointer("/type").and_then(|v| v.as_str()) {
                        let valid_types = ["function", "method", "class", "interface", "variable", "constant", "type", "namespace"];
                        if !valid_types.contains(&sym_type) {
                            errors.push(ValidationError {
                                code: "INVALID_ENUM_VALUE".to_string(),
                                message: format!("Symbol type '{sym_type}' is not a valid enum value. Expected one of: {valid_types:?}"),
                                path: format!("/analysis/files/{file_idx}/symbols/{sym_idx}/type"),
                                severity: ValidationSeverity::Error,
                            });
                        }
                    }
                    
                    // Check visibility
                    if let Some(visibility) = symbol.pointer("/visibility").and_then(|v| v.as_str()) {
                        let valid_visibility = ["public", "private", "protected", "internal"];
                        if !valid_visibility.contains(&visibility) {
                            errors.push(ValidationError {
                                code: "INVALID_ENUM_VALUE".to_string(),
                                message: format!("Symbol visibility '{visibility}' is not a valid enum value. Expected one of: {valid_visibility:?}"),
                                path: format!("/analysis/files/{file_idx}/symbols/{sym_idx}/visibility"),
                                severity: ValidationSeverity::Error,
                            });
                        }
                    }
                }
            }
            
            // Check chunk types
            if let Some(chunks) = file.pointer("/chunks").and_then(|v| v.as_array()) {
                for (chunk_idx, chunk) in chunks.iter().enumerate() {
                    if let Some(chunk_type) = chunk.pointer("/type").and_then(|v| v.as_str()) {
                        let valid_types = ["function", "class", "method", "block", "comment", "import"];
                        if !valid_types.contains(&chunk_type) {
                            errors.push(ValidationError {
                                code: "INVALID_ENUM_VALUE".to_string(),
                                message: format!("Chunk type '{chunk_type}' is not a valid enum value. Expected one of: {valid_types:?}"),
                                path: format!("/analysis/files/{file_idx}/chunks/{chunk_idx}/type"),
                                severity: ValidationSeverity::Error,
                            });
                        }
                    }
                }
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_valid_id_patterns() {
        assert!(is_valid_repository_id("repo_a1b2c3d4e5f6g7h8"));
        assert!(!is_valid_repository_id("repo_a1b2c3d4e5f6g7")); // Too short
        assert!(!is_valid_repository_id("repository_a1b2c3d4e5f6g7h8")); // Wrong prefix
        assert!(!is_valid_repository_id("repo_a1b2c3d4e5f6g7h!")); // Invalid char
        
        assert!(is_valid_analysis_id("analysis_a1b2c3d4e5f6g7h8"));
        assert!(!is_valid_analysis_id("analysis_a1b2c3d4e5f6g7")); // Too short
        
        assert!(is_valid_chunk_id("chunk_a1b2c3d4e5f6g7h8"));
        assert!(!is_valid_chunk_id("chunk_a1b2c3d4e5f6g7")); // Too short
    }
    
    #[test]
    fn test_schema_loading() {
        let result = get_or_compile_schema("ast-output-v1");
        assert!(result.is_ok());
        
        let result = get_or_compile_schema("unknown-schema");
        assert!(result.is_err());
    }
    
    #[test]
    fn test_enum_validation() {
        let valid_json = serde_json::json!({
            "analysis": {
                "patterns": [{
                    "pattern_type": "design_pattern"
                }],
                "files": [{
                    "symbols": [{
                        "type": "function",
                        "visibility": "public"
                    }],
                    "chunks": [{
                        "type": "method"
                    }]
                }]
            }
        });
        
        let mut errors = Vec::new();
        validate_enum_values(&mut errors, &valid_json);
        assert!(errors.is_empty());
        
        let invalid_json = serde_json::json!({
            "analysis": {
                "patterns": [{
                    "pattern_type": "DesignPattern" // Should be lowercase
                }],
                "files": [{
                    "symbols": [{
                        "type": "Function", // Should be lowercase
                        "visibility": "Public" // Should be lowercase
                    }]
                }]
            }
        });
        
        let mut errors = Vec::new();
        validate_enum_values(&mut errors, &invalid_json);
        assert_eq!(errors.len(), 3);
    }
}