//! Streaming-specific data models for enhanced analysis engine performance
//! 
//! This module provides production-ready streaming models that build on the existing
//! analysis-engine architecture while adding enhanced capabilities for:
//! - Memory-bounded streaming with backpressure management
//! - Real-time progress tracking via WebSocket
//! - Cache-first parsing with Redis integration
//! - Error recovery maintaining 99.9% completion rate
//! 
//! Performance targets:
//! - First chunk: <100ms response time
//! - Throughput: >67,900 LOC/s (Evidence Gate 2 validated)
//! - Memory: <4GB for 10M LOC analysis (Cloud Run compatible)
//! - Concurrent: 50+ simultaneous streaming analyses

use crate::models::{AstNode, PerformanceMetrics};
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

/// Enhanced streaming chunk with metadata and error handling
/// Builds on existing ContentChunk pattern from file_processor.rs
#[derive(Debug, Clone)]
pub struct StreamingChunk {
    /// Unique identifier for this chunk
    pub id: ChunkId,
    /// Source file path
    pub file_path: String,
    /// Content hash for cache lookups (SHA-256)
    pub content_hash: String,
    /// Parsed AST nodes for this chunk
    pub ast_nodes: Vec<AstNode>,
    /// Performance metrics for this chunk
    pub metrics: ChunkMetrics,
    /// Progress information for real-time updates
    pub progress: ProgressInfo,
    /// Error information if parsing failed (maintains stream continuity)
    pub error: Option<ParseError>,
}

/// Chunk identifier for tracking and caching
/// Used for Redis cache keys and progress tracking
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub struct ChunkId {
    /// Source file path
    pub file_path: String,
    /// Byte offset in the file where this chunk starts
    pub offset: u64,
    /// Size of this chunk in bytes
    pub size: usize,
}

/// Performance metrics per chunk for detailed monitoring
#[derive(Debug, Clone, Default)]
pub struct ChunkMetrics {
    /// Time spent parsing this chunk (ms)
    pub parse_duration_ms: u64,
    /// Number of AST nodes generated
    pub ast_node_count: usize,
    /// Memory usage during parsing (MB)
    pub memory_usage_mb: usize,
    /// Whether this was a cache hit (Redis optimization)
    pub cache_hit: bool,
    /// Throughput for this chunk (LOC/s)
    pub throughput_loc_per_sec: f64,
}

/// Progress information for real-time WebSocket updates
#[derive(Debug, Clone)]
pub struct ProgressInfo {
    /// Bytes processed so far
    pub bytes_processed: u64,
    /// Total bytes to process
    pub total_bytes: u64,
    /// Number of chunks completed
    pub chunks_completed: usize,
    /// Estimated completion time (ms from now)
    pub estimated_completion_ms: u64,
    /// Current throughput (LOC/s)
    pub current_throughput_loc_per_sec: f64,
    /// Memory usage percentage (0.0-1.0)
    pub memory_usage_percent: f32,
}

/// Streaming analysis request configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StreamingAnalysisRequest {
    /// Unique analysis identifier
    pub analysis_id: String,
    /// Repository path or URL to analyze
    pub repository_path: String,
    /// Languages to analyze (empty = all supported)
    pub languages: Vec<String>,
    /// Streaming configuration parameters
    pub streaming_config: StreamingConfig,
    /// Optional WebSocket endpoint for progress updates
    pub progress_callback: Option<String>,
    /// Include file patterns (glob)
    pub include_patterns: Vec<String>,
    /// Exclude file patterns (glob)
    pub exclude_patterns: Vec<String>,
}

/// Production streaming configuration with validated defaults
/// All defaults based on existing analysis-engine patterns and Cloud Run limits
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StreamingConfig {
    /// Chunk size in bytes (default: 64KB - matches existing file_processor.rs)
    pub chunk_size_bytes: usize,
    /// Maximum memory usage in MB (default: 3072MB - Cloud Run limit - 1GB buffer)
    pub max_memory_mb: usize,
    /// Backpressure threshold (0.0-1.0, default: 0.8 - from memory_monitor.rs patterns)
    pub backpressure_threshold: f32,
    /// Progress update interval in ms (default: 1000ms - matches progress_reporter.rs)
    pub progress_interval_ms: u64,
    /// Enable error recovery (default: true - Backend persona requirement)
    pub error_recovery_enabled: bool,
    /// Enable Redis caching (default: true - Redis integration)
    pub cache_enabled: bool,
    /// Maximum concurrent chunks (default: 10 - prevents memory spike)
    pub concurrent_chunk_limit: usize,
    /// File timeout in seconds (default: 30s - existing StreamingAnalysisConfig)
    pub file_timeout_seconds: u64,
}

impl Default for StreamingConfig {
    fn default() -> Self {
        Self {
            chunk_size_bytes: 65536,    // 64KB - matches existing file_processor.rs
            max_memory_mb: 3072,        // 3GB - safe buffer under 4GB Cloud Run limit  
            backpressure_threshold: 0.8, // 80% - from memory_monitor.rs patterns
            progress_interval_ms: 1000,  // 1s - matches progress_reporter.rs
            error_recovery_enabled: true, // Backend persona requirement
            cache_enabled: true,         // Leverage existing Redis integration
            concurrent_chunk_limit: 10,  // Prevent unbounded concurrency
            file_timeout_seconds: 30,    // Matches existing StreamingAnalysisConfig
        }
    }
}

/// Enhanced progress update for WebSocket streaming
/// Extends the existing ProgressUpdate with streaming-specific information
#[derive(Debug, Clone, Serialize)]
#[serde(tag = "type")]
pub enum StreamingProgressUpdate {
    /// General progress update
    Progress {
        analysis_id: String,
        bytes_processed: u64,
        total_bytes: u64,
        throughput_loc_per_sec: f64,
        estimated_completion_ms: u64,
        memory_usage_percent: f32,
    },
    /// Individual chunk completed
    ChunkCompleted {
        analysis_id: String,
        chunk_id: String,
        file_path: String,
        parse_duration_ms: u64,
        ast_node_count: usize,
        cache_hit: bool,
        throughput_loc_per_sec: f64,
    },
    /// Parsing error occurred (stream continues)
    Error {
        analysis_id: String,
        error_message: String,
        file_path: Option<String>,
        recoverable: bool,
        error_count: usize,
    },
    /// Analysis completed
    Completed {
        analysis_id: String,
        total_duration_ms: u64,
        total_files: usize,
        success_rate: f64,
        cache_hit_rate: f64,
        average_throughput_loc_per_sec: f64,
    },
    /// Analysis failed (unrecoverable)
    Failed {
        analysis_id: String,
        error_message: String,
        files_processed: usize,
        duration_ms: u64,
    },
}

/// Parse error information for streaming context
/// Allows stream to continue processing other files
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ParseError {
    /// Type of error that occurred
    pub error_type: ParseErrorType,
    /// Human-readable error message
    pub message: String,
    /// Whether this error is recoverable (stream can continue)
    pub recoverable: bool,
    /// Retry count for this file
    pub retry_count: usize,
    /// Timestamp when error occurred
    pub timestamp: DateTime<Utc>,
}

/// Types of parse errors in streaming context
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub enum ParseErrorType {
    /// Parser failed to parse syntax
    SyntaxError,
    /// Language not supported
    UnsupportedLanguage,
    /// File too large for processing
    FileTooLarge,
    /// Processing timeout exceeded
    Timeout,
    /// Memory limit exceeded
    MemoryLimit,
    /// I/O error reading file
    IoError,
    /// Cache lookup failed
    CacheError,
    /// Unknown error
    Other,
}

/// Processing error for streaming operations
/// Used in Result types throughout streaming components
#[derive(Debug, thiserror::Error)]
pub enum ProcessingError {
    #[error("Backpressure limit reached - system under pressure")]
    BackpressureLimit,
    
    #[error("Memory limit exceeded: {current_mb}MB > {limit_mb}MB")]
    MemoryLimitExceeded { current_mb: usize, limit_mb: usize },
    
    #[error("File timeout: {file_path} exceeded {timeout_seconds}s")]
    FileTimeout { file_path: String, timeout_seconds: u64 },
    
    #[error("Parse error in {file_path}: {message}")]
    ParseError { file_path: String, message: String },
    
    #[error("Cache error: {message}")]
    CacheError { message: String },
    
    #[error("I/O error: {message}")]
    IoError { message: String },
    
    #[error("Configuration error: {message}")]
    ConfigError { message: String },
}

/// Streaming analysis result
/// Response for streaming analysis requests
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StreamingAnalysisResult {
    /// Analysis identifier
    pub analysis_id: String,
    /// Current status of the analysis
    pub status: StreamingAnalysisStatus,
    /// Started timestamp
    pub started_at: DateTime<Utc>,
    /// Optional completion timestamp
    pub completed_at: Option<DateTime<Utc>>,
    /// Total duration in milliseconds
    pub duration_ms: Option<u64>,
    /// Current progress (0.0-1.0)
    pub progress: f64,
    /// Current processing stage
    pub stage: String,
    /// Overall performance metrics
    pub performance_metrics: Option<PerformanceMetrics>,
    /// Number of chunks processed
    pub chunks_processed: usize,
    /// Total chunks to process
    pub total_chunks: Option<usize>,
    /// Success rate (0.0-1.0)
    pub success_rate: f64,
    /// Cache hit rate (0.0-1.0)
    pub cache_hit_rate: f64,
    /// Current memory usage in MB
    pub memory_usage_mb: usize,
    /// Whether backpressure is active
    pub backpressure_active: bool,
    /// Error message if failed
    pub error_message: Option<String>,
    /// WebSocket URL for progress updates
    pub progress_url: Option<String>,
}

/// Status of streaming analysis
#[derive(Debug, Clone, Copy, PartialEq, Serialize, Deserialize)]
#[serde(rename_all = "snake_case")]
pub enum StreamingAnalysisStatus {
    /// Analysis is pending
    Pending,
    /// Analysis is actively streaming
    Streaming,
    /// Analysis completed successfully
    Completed,
    /// Analysis failed
    Failed,
    /// Analysis was cancelled
    Cancelled,
    /// Analysis paused due to backpressure
    Paused,
}

impl Default for StreamingAnalysisStatus {
    fn default() -> Self {
        StreamingAnalysisStatus::Pending
    }
}

/// Backpressure state information
#[derive(Debug, Clone)]
pub struct BackpressureState {
    /// Current system load (0.0-1.0)
    pub current_load: f32,
    /// Memory usage percentage (0.0-1.0)
    pub memory_usage_percent: f32,
    /// Buffer utilization percentage (0.0-1.0)
    pub buffer_usage_percent: f32,
    /// Whether backpressure is currently active
    pub active: bool,
    /// Current throttling delay in milliseconds
    pub throttle_delay_ms: u64,
    /// Last adjustment timestamp
    pub last_adjustment: DateTime<Utc>,
}

impl Default for BackpressureState {
    fn default() -> Self {
        Self {
            current_load: 0.0,
            memory_usage_percent: 0.0,
            buffer_usage_percent: 0.0,
            active: false,
            throttle_delay_ms: 0,
            last_adjustment: Utc::now(),
        }
    }
}

/// Cache statistics for monitoring Redis integration
#[derive(Debug, Clone, Default, Serialize)]
pub struct CacheStats {
    /// Total cache lookups attempted
    pub lookups: usize,
    /// Cache hits
    pub hits: usize,
    /// Cache misses
    pub misses: usize,
    /// Cache hit rate (0.0-1.0)
    pub hit_rate: f64,
    /// Average lookup time in ms
    pub average_lookup_ms: f64,
    /// Total time saved by cache hits in ms
    pub time_saved_ms: u64,
}

impl CacheStats {
    /// Update cache statistics with a new lookup
    pub fn record_lookup(&mut self, hit: bool, lookup_time_ms: u64) {
        self.lookups += 1;
        if hit {
            self.hits += 1;
        } else {
            self.misses += 1;
        }
        self.hit_rate = self.hits as f64 / self.lookups as f64;
        
        // Update average lookup time (simple moving average)
        let current_avg = self.average_lookup_ms;
        self.average_lookup_ms = (current_avg * (self.lookups - 1) as f64 + lookup_time_ms as f64) / self.lookups as f64;
        
        if hit {
            // Estimate time saved (assume cache hit is 10x faster than parsing)
            self.time_saved_ms += lookup_time_ms * 10;
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_streaming_config_defaults() {
        let config = StreamingConfig::default();
        assert_eq!(config.chunk_size_bytes, 65536); // 64KB
        assert_eq!(config.max_memory_mb, 3072); // 3GB
        assert_eq!(config.backpressure_threshold, 0.8); // 80%
        assert_eq!(config.progress_interval_ms, 1000); // 1s
        assert!(config.error_recovery_enabled);
        assert!(config.cache_enabled);
        assert_eq!(config.concurrent_chunk_limit, 10);
        assert_eq!(config.file_timeout_seconds, 30);
    }

    #[test]
    fn test_chunk_id_creation() {
        let chunk_id = ChunkId {
            file_path: "src/main.rs".to_string(),
            offset: 1024,
            size: 512,
        };
        
        assert_eq!(chunk_id.file_path, "src/main.rs");
        assert_eq!(chunk_id.offset, 1024);
        assert_eq!(chunk_id.size, 512);
    }

    #[test]
    fn test_cache_stats_update() {
        let mut stats = CacheStats::default();
        
        // Record a cache hit
        stats.record_lookup(true, 5);
        assert_eq!(stats.hits, 1);
        assert_eq!(stats.misses, 0);
        assert_eq!(stats.hit_rate, 1.0);
        
        // Record a cache miss
        stats.record_lookup(false, 100);
        assert_eq!(stats.hits, 1);
        assert_eq!(stats.misses, 1);
        assert_eq!(stats.hit_rate, 0.5);
        
        // Check time saved calculation
        assert_eq!(stats.time_saved_ms, 50); // 5ms * 10x speedup
    }

    #[test]
    fn test_backpressure_state_default() {
        let state = BackpressureState::default();
        assert_eq!(state.current_load, 0.0);
        assert_eq!(state.memory_usage_percent, 0.0);
        assert_eq!(state.buffer_usage_percent, 0.0);
        assert!(!state.active);
        assert_eq!(state.throttle_delay_ms, 0);
    }

    #[test]
    fn test_processing_error_types() {
        let error = ProcessingError::BackpressureLimit;
        assert!(error.to_string().contains("Backpressure limit reached"));
        
        let error = ProcessingError::MemoryLimitExceeded { 
            current_mb: 4096, 
            limit_mb: 3072 
        };
        assert!(error.to_string().contains("4096MB > 3072MB"));
    }

    #[test]
    fn test_streaming_analysis_status_serialization() {
        let status = StreamingAnalysisStatus::Streaming;
        let json = serde_json::to_string(&status).unwrap();
        assert_eq!(json, "\"streaming\"");
        
        let status = StreamingAnalysisStatus::Completed;
        let json = serde_json::to_string(&status).unwrap();
        assert_eq!(json, "\"completed\"");
    }
}