/// Startup management module for handling service initialization with retry logic
use anyhow::{anyhow, Result};
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use tokio::time::{sleep, timeout};
use tracing::{error, info, warn};

/// Maximum total startup time allowed (Cloud Run limit)
const MAX_STARTUP_DURATION: Duration = Duration::from_secs(300); // 5 minutes

/// Initial retry delay
const INITIAL_RETRY_DELAY: Duration = Duration::from_secs(1);

/// Maximum retry delay (exponential backoff cap)
const MAX_RETRY_DELAY: Duration = Duration::from_secs(30);

/// Backoff multiplier for exponential backoff
const BACKOFF_MULTIPLIER: f64 = 2.0;

/// Service initialization status
#[derive(Debug, Clone, PartialEq, serde::Serialize, serde::Deserialize)]
pub enum ServiceStatus {
    NotStarted,
    Initializing { attempt: u32, last_error: Option<String> },
    Ready,
    Failed { error: String },
}

/// Individual service health information
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct ServiceHealth {
    pub name: String,
    pub status: ServiceStatus,
    pub startup_time: Option<Duration>,
}

/// Startup manager for tracking initialization state
#[derive(Clone)]
pub struct StartupManager {
    pub services: Arc<RwLock<Vec<ServiceHealth>>>,
    pub overall_status: Arc<RwLock<StartupStatus>>,
    pub start_time: Instant,
}

/// Overall startup status
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct StartupStatus {
    pub is_ready: bool,
    pub total_services: usize,
    pub ready_services: usize,
    pub failed_services: usize,
    pub startup_duration: Duration,
}

impl StartupManager {
    /// Create a new startup manager
    pub fn new() -> Self {
        let services = vec![
            ServiceHealth {
                name: "Spanner".to_string(),
                status: ServiceStatus::NotStarted,
                startup_time: None,
            },
            ServiceHealth {
                name: "Redis".to_string(),
                status: ServiceStatus::NotStarted,
                startup_time: None,
            },
            ServiceHealth {
                name: "Google Cloud Storage".to_string(),
                status: ServiceStatus::NotStarted,
                startup_time: None,
            },
            ServiceHealth {
                name: "Google Cloud Pub/Sub".to_string(),
                status: ServiceStatus::NotStarted,
                startup_time: None,
            },
        ];

        let overall_status = StartupStatus {
            is_ready: false,
            total_services: services.len(),
            ready_services: 0,
            failed_services: 0,
            startup_duration: Duration::from_secs(0),
        };

        Self {
            services: Arc::new(RwLock::new(services)),
            overall_status: Arc::new(RwLock::new(overall_status)),
            start_time: Instant::now(),
        }
    }

    /// Update service status
    pub async fn update_service_status(&self, service_name: &str, status: ServiceStatus) {
        let mut services = self.services.write().await;
        let service_start_time = self.start_time;
        
        if let Some(service) = services.iter_mut().find(|s| s.name == service_name) {
            service.status = status.clone();
            
            // Update startup time if service is ready
            if matches!(status, ServiceStatus::Ready) {
                service.startup_time = Some(service_start_time.elapsed());
            }
        }

        // Update overall status
        drop(services);
        self.update_overall_status().await;
    }

    /// Update overall startup status
    async fn update_overall_status(&self) {
        let services = self.services.read().await;
        let ready_count = services.iter().filter(|s| matches!(s.status, ServiceStatus::Ready)).count();
        let failed_count = services.iter().filter(|s| matches!(s.status, ServiceStatus::Failed { .. })).count();
        
        let mut overall = self.overall_status.write().await;
        overall.ready_services = ready_count;
        overall.failed_services = failed_count;
        overall.startup_duration = self.start_time.elapsed();
        overall.is_ready = ready_count == overall.total_services;
    }

    /// Get current startup status
    pub async fn get_status(&self) -> StartupStatus {
        self.overall_status.read().await.clone()
    }

    /// Get detailed service health information
    pub async fn get_service_health(&self) -> Vec<ServiceHealth> {
        self.services.read().await.clone()
    }

    /// Check if startup timeout has been exceeded
    pub fn is_timeout(&self) -> bool {
        self.start_time.elapsed() > MAX_STARTUP_DURATION
    }
}

/// Retry configuration for service initialization
pub struct RetryConfig {
    pub max_attempts: u32,
    pub initial_delay: Duration,
    pub max_delay: Duration,
    pub timeout_per_attempt: Duration,
}

impl Default for RetryConfig {
    fn default() -> Self {
        Self {
            max_attempts: 10,
            initial_delay: INITIAL_RETRY_DELAY,
            max_delay: MAX_RETRY_DELAY,
            timeout_per_attempt: Duration::from_secs(30),
        }
    }
}

/// Execute a function with exponential backoff retry logic
pub async fn retry_with_backoff<F, Fut, T>(
    service_name: &str,
    startup_manager: &StartupManager,
    config: RetryConfig,
    mut f: F,
) -> Result<T>
where
    F: FnMut() -> Fut,
    Fut: std::future::Future<Output = Result<T>>,
{
    let start_time = Instant::now();
    let mut attempt = 0;
    let mut delay = config.initial_delay;

    loop {
        attempt += 1;
        
        // Check for overall timeout
        if startup_manager.is_timeout() {
            let error_msg = format!(
                "Service {} initialization timed out after {} seconds (Cloud Run startup limit)",
                service_name,
                MAX_STARTUP_DURATION.as_secs()
            );
            error!("{}", error_msg);
            startup_manager.update_service_status(
                service_name,
                ServiceStatus::Failed { error: error_msg.clone() }
            ).await;
            return Err(anyhow!(error_msg));
        }

        // Update status to initializing
        startup_manager.update_service_status(
            service_name,
            ServiceStatus::Initializing {
                attempt,
                last_error: None,
            }
        ).await;

        info!(
            "Attempting to initialize {} (attempt {}/{})",
            service_name, attempt, config.max_attempts
        );

        // Try to execute the function with timeout
        match timeout(config.timeout_per_attempt, f()).await {
            Ok(Ok(result)) => {
                let elapsed = start_time.elapsed();
                info!(
                    "{} initialized successfully after {} attempts ({:.2}s)",
                    service_name,
                    attempt,
                    elapsed.as_secs_f64()
                );
                startup_manager.update_service_status(service_name, ServiceStatus::Ready).await;
                return Ok(result);
            }
            Ok(Err(e)) => {
                let error_msg = format!("{} initialization failed: {}", service_name, e);
                warn!("{}", error_msg);
                
                if attempt >= config.max_attempts {
                    error!(
                        "{} failed to initialize after {} attempts",
                        service_name, config.max_attempts
                    );
                    startup_manager.update_service_status(
                        service_name,
                        ServiceStatus::Failed { error: error_msg }
                    ).await;
                    return Err(e);
                }

                // Update status with error
                startup_manager.update_service_status(
                    service_name,
                    ServiceStatus::Initializing {
                        attempt,
                        last_error: Some(error_msg),
                    }
                ).await;
            }
            Err(_) => {
                let error_msg = format!(
                    "{} initialization timed out after {}s",
                    service_name,
                    config.timeout_per_attempt.as_secs()
                );
                warn!("{}", error_msg);
                
                if attempt >= config.max_attempts {
                    error!(
                        "{} failed to initialize after {} attempts (timeout)",
                        service_name, config.max_attempts
                    );
                    startup_manager.update_service_status(
                        service_name,
                        ServiceStatus::Failed { error: error_msg.clone() }
                    ).await;
                    return Err(anyhow!(error_msg));
                }

                // Update status with timeout error
                startup_manager.update_service_status(
                    service_name,
                    ServiceStatus::Initializing {
                        attempt,
                        last_error: Some(error_msg),
                    }
                ).await;
            }
        }

        // Calculate next delay with exponential backoff
        info!(
            "Retrying {} initialization in {:.1}s...",
            service_name,
            delay.as_secs_f64()
        );
        sleep(delay).await;
        
        // Exponential backoff with cap
        delay = Duration::from_secs_f64(
            (delay.as_secs_f64() * BACKOFF_MULTIPLIER).min(config.max_delay.as_secs_f64())
        );
    }
}

/// Helper function to retry optional services (like Redis)
pub async fn retry_optional_service<F, Fut, T>(
    service_name: &str,
    startup_manager: &StartupManager,
    config: RetryConfig,
    f: F,
) -> Option<T>
where
    F: FnMut() -> Fut,
    Fut: std::future::Future<Output = Result<T>>,
{
    match retry_with_backoff(service_name, startup_manager, config, f).await {
        Ok(result) => Some(result),
        Err(e) => {
            warn!(
                "{} is not available, continuing without it: {}",
                service_name, e
            );
            None
        }
    }
}