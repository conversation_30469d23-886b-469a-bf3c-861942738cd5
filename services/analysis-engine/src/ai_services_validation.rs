use crate::services::embeddings_enhancement::EnhancedEmbeddingsService;
use crate::services::ai_pattern_detector::AIPatternDetector;
use crate::services::code_quality_assessor::CodeQualityAssessor;
use crate::services::semantic_search::SemanticSearchService;
use crate::services::repository_insights::RepositoryInsightsService;
use std::sync::Arc;
use anyhow::Result;

pub struct AIServicesValidation;

impl AIServicesValidation {
    pub async fn validate_ai_services() -> Result<()> {
        println!("Validating AI Services Implementation...");
        
        // Test 1: Can we create the embeddings service?
        println!("1. Testing EnhancedEmbeddingsService creation...");
        let embeddings_service = Arc::new(EnhancedEmbeddingsService::new().await?);
        println!("   ✓ EnhancedEmbeddingsService created successfully");
        
        // Test 2: Can we create the AI pattern detector?
        println!("2. Testing AIPatternDetector creation...");
        let _ai_pattern_detector = AIPatternDetector::new(embeddings_service.clone()).await?;
        println!("   ✓ AIPatternDetector created successfully");
        
        // Test 3: Can we create the code quality assessor?
        println!("3. Testing CodeQualityAssessor creation...");
        let _code_quality_assessor = CodeQualityAssessor::new(embeddings_service.clone()).await?;
        println!("   ✓ CodeQualityAssessor created successfully");
        
        // Test 4: Can we create the semantic search service?
        println!("4. Testing SemanticSearchService creation...");
        let _semantic_search_service = SemanticSearchService::new(embeddings_service.clone()).await?;
        println!("   ✓ SemanticSearchService created successfully");
        
        // Test 5: Can we create the repository insights service?
        println!("5. Testing RepositoryInsightsService creation...");
        let _repository_insights_service = RepositoryInsightsService::new(embeddings_service.clone()).await?;
        println!("   ✓ RepositoryInsightsService created successfully");
        
        // Test 6: Check feature toggles
        println!("6. Testing feature toggles...");
        let feature_toggles = embeddings_service.get_feature_toggles();
        println!("   ✓ AI Pattern Detection: {}", feature_toggles.enable_ai_pattern_detection);
        println!("   ✓ Code Quality Assessment: {}", feature_toggles.enable_code_quality_assessment);
        println!("   ✓ Semantic Search: {}", feature_toggles.enable_semantic_search);
        println!("   ✓ Repository Insights: {}", feature_toggles.enable_repository_insights);
        
        println!("\n🎉 All AI services validation tests passed!");
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_ai_services_validation() {
        AIServicesValidation::validate_ai_services().await.unwrap();
    }
}