//! Production scenario benchmarks for realistic workload testing
//! Simulates real-world production conditions including multi-tenancy,
//! resource constraints, and various load patterns

use analysis_engine::{
    backpressure::{BackpressureConfig, BackpressureManager, BackpressureDecision},
    cache::RedisCache,
    config::{ResourceOptimizationConfig, ServiceConfig, SystemMonitorConfig},
    monitoring::{ResourceMonitor, SystemMonitor},
    parser::{parallel::ParallelProcessor, TreeSitterParser},
};
use criterion::{black_box, criterion_group, criterion_main, BenchmarkId, Criterion};
use std::collections::HashMap;
use std::fs;
use std::path::PathBuf;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tempfile::TempDir;
use tokio::runtime::Runtime;

/// Simulated tenant with repository characteristics
struct Tenant {
    id: String,
    repository_size: usize, // LOC
    file_count: usize,
    language: &'static str,
}

/// Generate repository for a tenant
fn generate_tenant_repository(tenant: &Tenant, base_dir: &TempDir) -> Vec<PathBuf> {
    let tenant_dir = base_dir.path().join(&tenant.id);
    fs::create_dir_all(&tenant_dir).unwrap();
    
    let lines_per_file = tenant.repository_size / tenant.file_count;
    let mut files = Vec::new();
    
    for i in 0..tenant.file_count {
        let content = generate_code_for_language(tenant.language, lines_per_file);
        let extension = get_language_extension(tenant.language);
        let file_path = tenant_dir.join(format!("file_{}.{}", i, extension));
        fs::write(&file_path, content).unwrap();
        files.push(file_path);
    }
    
    files
}

/// Generate code based on language
fn generate_code_for_language(language: &str, lines: usize) -> String {
    match language {
        "rust" => generate_rust_code(lines),
        "python" => generate_python_code(lines),
        "javascript" => generate_javascript_code(lines),
        "go" => generate_go_code(lines),
        _ => generate_generic_code(lines),
    }
}

fn get_language_extension(language: &str) -> &'static str {
    match language {
        "rust" => "rs",
        "python" => "py",
        "javascript" => "js",
        "go" => "go",
        "java" => "java",
        _ => "txt",
    }
}

fn generate_rust_code(lines: usize) -> String {
    let mut code = String::from("use std::collections::HashMap;\n\n");
    for i in 0..lines / 10 {
        code.push_str(&format!(
            "fn function_{}(x: i32) -> i32 {{ x * {} }}\n",
            i, i
        ));
    }
    code
}

fn generate_python_code(lines: usize) -> String {
    let mut code = String::from("import os\nimport sys\n\n");
    for i in 0..lines / 5 {
        code.push_str(&format!(
            "def function_{}(x):\n    return x * {}\n\n",
            i, i
        ));
    }
    code
}

fn generate_javascript_code(lines: usize) -> String {
    let mut code = String::from("const utils = require('./utils');\n\n");
    for i in 0..lines / 6 {
        code.push_str(&format!(
            "function function{}(x) {{\n  return x * {};\n}}\n\n",
            i, i
        ));
    }
    code
}

fn generate_go_code(lines: usize) -> String {
    let mut code = String::from("package main\n\nimport \"fmt\"\n\n");
    for i in 0..lines / 8 {
        code.push_str(&format!(
            "func function{}(x int) int {{\n\treturn x * {}\n}}\n\n",
            i, i
        ));
    }
    code
}

fn generate_generic_code(lines: usize) -> String {
    (0..lines)
        .map(|i| format!("Line {}: Some generic code content here\n", i))
        .collect()
}

/// Benchmark multi-tenant processing scenario
fn bench_multi_tenant_scenario(c: &mut Criterion) {
    let runtime = Runtime::new().unwrap();
    
    let mut group = c.benchmark_group("multi_tenant_production");
    group.sample_size(10);
    group.measurement_time(Duration::from_secs(60));
    
    // Define tenant scenarios
    let tenant_scenarios = vec![
        ("small_tenants", vec![
            Tenant { id: "tenant1".to_string(), repository_size: 50000, file_count: 100, language: "rust" },
            Tenant { id: "tenant2".to_string(), repository_size: 75000, file_count: 150, language: "python" },
            Tenant { id: "tenant3".to_string(), repository_size: 100000, file_count: 200, language: "javascript" },
        ]),
        ("mixed_tenants", vec![
            Tenant { id: "enterprise1".to_string(), repository_size: 500000, file_count: 1000, language: "java" },
            Tenant { id: "startup1".to_string(), repository_size: 25000, file_count: 50, language: "go" },
            Tenant { id: "startup2".to_string(), repository_size: 30000, file_count: 60, language: "rust" },
            Tenant { id: "medium1".to_string(), repository_size: 150000, file_count: 300, language: "python" },
        ]),
        ("enterprise_tenants", vec![
            Tenant { id: "corp1".to_string(), repository_size: 1000000, file_count: 2000, language: "java" },
            Tenant { id: "corp2".to_string(), repository_size: 800000, file_count: 1600, language: "javascript" },
        ]),
    ];
    
    for (scenario_name, tenants) in tenant_scenarios {
        let temp_dir = TempDir::new().unwrap();
        let mut tenant_files = HashMap::new();
        
        // Generate repositories for all tenants
        for tenant in &tenants {
            let files = generate_tenant_repository(tenant, &temp_dir);
            tenant_files.insert(tenant.id.clone(), files);
        }
        
        group.bench_function(scenario_name, |b| {
            b.iter(|| {
                runtime.block_on(async {
                    let config = Arc::new(ServiceConfig::from_env().unwrap());
                    let parser = Arc::new(TreeSitterParser::new(config.clone()).unwrap());
                    
                    // Production constraints - Cloud Run typical configuration
                    let system_config = SystemMonitorConfig {
                        cpu_limit_percent: 200.0, // 2 CPUs
                        memory_limit_mb: 4096,    // 4GB
                        cpu_warning_threshold: 180.0,
                        memory_warning_threshold_percent: 85.0,
                        monitoring_interval: Duration::from_secs(1),
                        enable_process_monitoring: true,
                        enable_disk_monitoring: false,
                        enable_network_monitoring: false,
                    };
                    let system_monitor = Arc::new(SystemMonitor::new(system_config));
                    
                    // Backpressure configuration for multi-tenancy
                    let bp_config = BackpressureConfig {
                        max_concurrent_analyses: 10, // Lower for multi-tenant fairness
                        max_analysis_memory_mb: 3072, // Leave headroom
                        cpu_threshold_percent: 160.0,
                        memory_threshold_percent: 80.0,
                        ..Default::default()
                    };
                    let backpressure = BackpressureManager::with_system_monitor(
                        bp_config,
                        system_monitor.clone(),
                    );
                    
                    system_monitor.start().await.unwrap();
                    
                    // Process each tenant's repository concurrently
                    let mut handles = vec![];
                    
                    for (tenant_id, files) in &tenant_files {
                        let parser_clone = parser.clone();
                        let backpressure_clone = Arc::new(backpressure.clone());
                        let files_clone = files.clone();
                        let tenant_id_clone = tenant_id.clone();
                        
                        let handle = tokio::spawn(async move {
                            let processor = ParallelProcessor::new(parser_clone);
                            let start = Instant::now();
                            let mut processed = 0;
                            
                            // Process tenant files with backpressure checks
                            for chunk in files_clone.chunks(50) {
                                match backpressure_clone.check_analysis_request().await {
                                    BackpressureDecision::Allow => {
                                        let results = processor.process_files_parallel(
                                            &chunk.to_vec(),
                                            2, // Limited threads per tenant
                                        );
                                        processed += results.len();
                                    }
                                    BackpressureDecision::Throttle(delay) => {
                                        tokio::time::sleep(delay).await;
                                        // Retry after throttle
                                        let results = processor.process_files_parallel(
                                            &chunk.to_vec(),
                                            2,
                                        );
                                        processed += results.len();
                                    }
                                    BackpressureDecision::Reject(reason) => {
                                        eprintln!("Tenant {} rejected: {:?}", tenant_id_clone, reason);
                                        break;
                                    }
                                }
                            }
                            
                            let duration = start.elapsed();
                            (tenant_id_clone, processed, duration)
                        });
                        
                        handles.push(handle);
                    }
                    
                    let results = futures::future::join_all(handles).await;
                    
                    system_monitor.stop().await.unwrap();
                    
                    black_box(results)
                })
            });
        });
    }
    
    group.finish();
}

/// Benchmark gradual load increase (ramp-up testing)
fn bench_gradual_load_increase(c: &mut Criterion) {
    let runtime = Runtime::new().unwrap();
    
    let mut group = c.benchmark_group("gradual_load_increase");
    group.sample_size(5);
    group.measurement_time(Duration::from_secs(120));
    
    let temp_dir = TempDir::new().unwrap();
    
    // Generate files for maximum load
    let all_files: Vec<PathBuf> = (0..500)
        .map(|i| {
            let content = generate_rust_code(1000);
            let file_path = temp_dir.path().join(format!("file_{}.rs", i));
            fs::write(&file_path, content).unwrap();
            file_path
        })
        .collect();
    
    group.bench_function("ramp_up_pattern", |b| {
        b.iter(|| {
            runtime.block_on(async {
                let config = Arc::new(ServiceConfig::from_env().unwrap());
                let parser = Arc::new(TreeSitterParser::new(config).unwrap());
                
                let system_monitor = Arc::new(SystemMonitor::new(SystemMonitorConfig {
                    cpu_limit_percent: 200.0,
                    memory_limit_mb: 4096,
                    ..Default::default()
                }));
                
                system_monitor.start().await.unwrap();
                
                let processor = ParallelProcessor::new(parser);
                let mut total_processed = 0;
                let mut phase_results = vec![];
                
                // Gradually increase load
                let load_phases = vec![
                    (10, 2),   // 10 files, 2 threads
                    (50, 4),   // 50 files, 4 threads
                    (100, 4),  // 100 files, 4 threads
                    (200, 6),  // 200 files, 6 threads
                    (500, 8),  // All files, 8 threads
                ];
                
                for (file_count, thread_count) in load_phases {
                    let phase_files = all_files.iter().take(file_count).cloned().collect();
                    let start = Instant::now();
                    
                    let results = processor.process_files_parallel(&phase_files, thread_count);
                    let duration = start.elapsed();
                    
                    total_processed += results.len();
                    phase_results.push((file_count, duration, results.len()));
                    
                    // Brief pause between phases
                    tokio::time::sleep(Duration::from_secs(1)).await;
                }
                
                system_monitor.stop().await.unwrap();
                
                black_box((total_processed, phase_results))
            })
        });
    });
    
    group.finish();
}

/// Benchmark spike testing (sudden load increase)
fn bench_spike_load(c: &mut Criterion) {
    let runtime = Runtime::new().unwrap();
    
    let mut group = c.benchmark_group("spike_load_testing");
    group.sample_size(10);
    
    let temp_dir = TempDir::new().unwrap();
    let baseline_files = generate_tenant_repository(
        &Tenant { id: "baseline".to_string(), repository_size: 50000, file_count: 100, language: "rust" },
        &temp_dir,
    );
    let spike_files = generate_tenant_repository(
        &Tenant { id: "spike".to_string(), repository_size: 200000, file_count: 400, language: "python" },
        &temp_dir,
    );
    
    group.bench_function("spike_scenario", |b| {
        b.iter(|| {
            runtime.block_on(async {
                let config = Arc::new(ServiceConfig::from_env().unwrap());
                let parser = Arc::new(TreeSitterParser::new(config).unwrap());
                
                let bp_config = BackpressureConfig {
                    max_concurrent_analyses: 20,
                    spike_detection_window: Duration::from_secs(5),
                    spike_threshold_multiplier: 2.0,
                    ..Default::default()
                };
                let backpressure = BackpressureManager::new(bp_config);
                
                let processor = ParallelProcessor::new(parser);
                
                // Normal load
                let normal_start = Instant::now();
                let normal_results = processor.process_files_parallel(&baseline_files, 4);
                let normal_duration = normal_start.elapsed();
                
                // Sudden spike
                let spike_start = Instant::now();
                let mut spike_processed = 0;
                let mut throttled_count = 0;
                
                // Process spike with backpressure
                for chunk in spike_files.chunks(50) {
                    match backpressure.check_analysis_request().await {
                        BackpressureDecision::Allow => {
                            let results = processor.process_files_parallel(&chunk.to_vec(), 8);
                            spike_processed += results.len();
                        }
                        BackpressureDecision::Throttle(delay) => {
                            throttled_count += 1;
                            tokio::time::sleep(delay).await;
                        }
                        BackpressureDecision::Reject(_) => {
                            break;
                        }
                    }
                }
                
                let spike_duration = spike_start.elapsed();
                
                black_box((
                    normal_results.len(),
                    normal_duration,
                    spike_processed,
                    spike_duration,
                    throttled_count,
                ))
            })
        });
    });
    
    group.finish();
}

/// Benchmark cache effectiveness in production scenarios
fn bench_cache_scenarios(c: &mut Criterion) {
    let runtime = Runtime::new().unwrap();
    
    let mut group = c.benchmark_group("cache_effectiveness");
    group.sample_size(10);
    
    // Cache hit rate scenarios
    let cache_scenarios = vec![
        ("cold_cache", 0.0),
        ("warm_cache", 0.5),
        ("hot_cache", 0.9),
    ];
    
    let temp_dir = TempDir::new().unwrap();
    let files = generate_tenant_repository(
        &Tenant { id: "cache_test".to_string(), repository_size: 100000, file_count: 200, language: "javascript" },
        &temp_dir,
    );
    
    for (scenario_name, _cache_hit_rate) in cache_scenarios {
        group.bench_function(scenario_name, |b| {
            b.iter(|| {
                runtime.block_on(async {
                    let config = Arc::new(ServiceConfig::from_env().unwrap());
                    let parser = Arc::new(TreeSitterParser::new(config.clone()).unwrap());
                    
                    // Create Redis cache (simulated for benchmark)
                    let redis_url = config.redis.url.clone();
                    let cache = match RedisCache::new(&redis_url).await {
                        Ok(cache) => Some(Arc::new(cache)),
                        Err(_) => None, // Graceful fallback
                    };
                    
                    let processor = ParallelProcessor::new(parser);
                    
                    // First pass - populate cache
                    let first_pass = processor.process_files_parallel(&files, 4);
                    
                    // Second pass - benefit from cache (if available)
                    let start = Instant::now();
                    let cached_results = processor.process_files_parallel(&files, 4);
                    let duration = start.elapsed();
                    
                    // Compare performance
                    let cache_benefit = if let Some(_cache) = cache {
                        // In real scenario, would check cache hits
                        Some(first_pass.len() == cached_results.len())
                    } else {
                        None
                    };
                    
                    black_box((cached_results.len(), duration, cache_benefit))
                })
            });
        });
    }
    
    group.finish();
}

/// Benchmark network latency simulation (Cloud Storage)
fn bench_network_latency(c: &mut Criterion) {
    let runtime = Runtime::new().unwrap();
    
    let mut group = c.benchmark_group("network_latency_simulation");
    group.sample_size(10);
    
    // Simulate different network conditions
    let network_conditions = vec![
        ("low_latency", Duration::from_millis(10)),
        ("medium_latency", Duration::from_millis(50)),
        ("high_latency", Duration::from_millis(150)),
        ("variable_latency", Duration::from_millis(0)), // Will vary
    ];
    
    let temp_dir = TempDir::new().unwrap();
    let files = generate_tenant_repository(
        &Tenant { id: "network_test".to_string(), repository_size: 50000, file_count: 100, language: "go" },
        &temp_dir,
    );
    
    for (condition_name, base_latency) in network_conditions {
        group.bench_function(condition_name, |b| {
            b.iter(|| {
                runtime.block_on(async {
                    let config = Arc::new(ServiceConfig::from_env().unwrap());
                    let parser = Arc::new(TreeSitterParser::new(config).unwrap());
                    let processor = ParallelProcessor::new(parser);
                    
                    let start = Instant::now();
                    let mut processed = 0;
                    
                    for chunk in files.chunks(10) {
                        // Simulate network latency for file retrieval
                        if base_latency.as_millis() > 0 {
                            tokio::time::sleep(base_latency).await;
                        } else {
                            // Variable latency
                            let variable = Duration::from_millis(rand::random::<u64>() % 100);
                            tokio::time::sleep(variable).await;
                        }
                        
                        let results = processor.process_files_parallel(&chunk.to_vec(), 2);
                        processed += results.len();
                    }
                    
                    let duration = start.elapsed();
                    let effective_throughput = processed as f64 / duration.as_secs_f64();
                    
                    black_box((processed, duration, effective_throughput))
                })
            });
        });
    }
    
    group.finish();
}

criterion_group!(
    benches,
    bench_multi_tenant_scenario,
    bench_gradual_load_increase,
    bench_spike_load,
    bench_cache_scenarios,
    bench_network_latency
);
criterion_main!(benches);