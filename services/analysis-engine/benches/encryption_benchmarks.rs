//! Comprehensive encryption benchmarks for field-level encryption
//! 
//! This benchmark suite measures the performance impact of field-level encryption
//! on the analysis engine's critical path. The goal is to ensure encryption adds
//! no more than 5% overhead to maintain our 67,900 LOC/second baseline.

use analysis_engine::models::security::EncryptedField;
use analysis_engine::storage::encryption::{EncryptionService, FieldEncryptionService, GoogleCloudKmsService};
use criterion::{black_box, criterion_group, criterion_main, BenchmarkId, Criterion, Throughput};
use std::sync::Arc;
use std::time::Duration;
use tokio::runtime::Runtime;

// Ensure we're testing with the security-storage feature
#[cfg(not(feature = "security-storage"))]
compile_error!("Run benchmarks with: cargo bench --features security-storage");

// Test data sizes matching production workloads
const SMALL_FIELD_SIZE: usize = 1024;        // 1KB - User IDs, metadata
const MEDIUM_FIELD_SIZE: usize = 102_400;    // 100KB - Code snippets, comments
const LARGE_FIELD_SIZE: usize = 1_048_576;   // 1MB - Full documents
const XLARGE_FIELD_SIZE: usize = 10_485_760; // 10MB - Large analysis results

// Batch sizes for testing throughput
const BATCH_SIZES: &[usize] = &[1, 10, 100, 1000];

// Helper to generate test data of specific size
fn generate_test_data(size: usize) -> Vec<u8> {
    use rand::{Rng, SeedableRng};
    use rand::rngs::StdRng;
    
    let mut rng = StdRng::seed_from_u64(42); // Deterministic for benchmarks
    let mut data = vec![0u8; size];
    rng.fill(&mut data[..]);
    data
}

// Create a real KMS service for benchmarking with test configuration
async fn create_test_kms_service() -> Arc<GoogleCloudKmsService> {
    use analysis_engine::storage::encryption::EncryptionConfig;
    
    let config = EncryptionConfig {
        project_id: "test-project".to_string(),
        location: "global".to_string(),
        key_ring: "test-ring".to_string(),
        crypto_key: "test-key".to_string(),
        key_rotation_period_days: 90,
    };
    
    Arc::new(GoogleCloudKmsService::new(config).await.expect("Failed to create KMS service"))
}

pub fn field_encryption_benchmarks(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();
    
    // Create encryption service
    let kms_service = rt.block_on(create_test_kms_service());
    let encryption_service = FieldEncryptionService::new(kms_service);
    
    // Benchmark single field encryption
    let mut group = c.benchmark_group("field_encryption/single");
    group.measurement_time(Duration::from_secs(10));
    group.sample_size(100);
    
    for &size in &[SMALL_FIELD_SIZE, MEDIUM_FIELD_SIZE, LARGE_FIELD_SIZE, XLARGE_FIELD_SIZE] {
        let data = generate_test_data(size);
        let size_label = match size {
            SMALL_FIELD_SIZE => "1KB",
            MEDIUM_FIELD_SIZE => "100KB",
            LARGE_FIELD_SIZE => "1MB",
            XLARGE_FIELD_SIZE => "10MB",
            _ => "unknown",
        };
        
        group.throughput(Throughput::Bytes(size as u64));
        group.bench_with_input(
            BenchmarkId::new("encrypt", size_label),
            &data,
            |b, data| {
                b.to_async(&rt).iter(|| async {
                    let result = encryption_service.encrypt_field(black_box(data)).await.unwrap();
                    black_box(result);
                });
            },
        );
    }
    group.finish();
    
    // Benchmark single field decryption
    let mut group = c.benchmark_group("field_encryption/single_decrypt");
    group.measurement_time(Duration::from_secs(10));
    group.sample_size(100);
    
    for &size in &[SMALL_FIELD_SIZE, MEDIUM_FIELD_SIZE, LARGE_FIELD_SIZE, XLARGE_FIELD_SIZE] {
        let data = generate_test_data(size);
        let encrypted = rt.block_on(async {
            encryption_service.encrypt_field(&data).await.unwrap()
        });
        
        let size_label = match size {
            SMALL_FIELD_SIZE => "1KB",
            MEDIUM_FIELD_SIZE => "100KB",
            LARGE_FIELD_SIZE => "1MB",
            XLARGE_FIELD_SIZE => "10MB",
            _ => "unknown",
        };
        
        group.throughput(Throughput::Bytes(size as u64));
        group.bench_with_input(
            BenchmarkId::new("decrypt", size_label),
            &encrypted,
            |b, encrypted| {
                b.to_async(&rt).iter(|| async {
                    let result = encryption_service.decrypt_field(black_box(encrypted)).await.unwrap();
                    black_box(result);
                });
            },
        );
    }
    group.finish();
    
    // Benchmark batch encryption
    let mut group = c.benchmark_group("field_encryption/batch");
    group.measurement_time(Duration::from_secs(15));
    group.sample_size(50);
    
    for &batch_size in BATCH_SIZES {
        for &field_size in &[SMALL_FIELD_SIZE, MEDIUM_FIELD_SIZE] {
            let fields: Vec<Vec<u8>> = (0..batch_size)
                .map(|_| generate_test_data(field_size))
                .collect();
            
            let size_label = match field_size {
                SMALL_FIELD_SIZE => "1KB",
                MEDIUM_FIELD_SIZE => "100KB",
                _ => "unknown",
            };
            
            group.throughput(Throughput::Bytes((field_size * batch_size) as u64));
            group.bench_with_input(
                BenchmarkId::new(format!("encrypt_{}x{}", batch_size, size_label), batch_size),
                &fields,
                |b, fields| {
                    b.to_async(&rt).iter(|| async {
                        let mut results = Vec::with_capacity(fields.len());
                        for field in fields {
                            let encrypted = encryption_service.encrypt_field(black_box(field)).await.unwrap();
                            results.push(encrypted);
                        }
                        black_box(results);
                    });
                },
            );
        }
    }
    group.finish();
    
    // Benchmark concurrent encryption
    let mut group = c.benchmark_group("field_encryption/concurrent");
    group.measurement_time(Duration::from_secs(15));
    group.sample_size(50);
    
    for &concurrency in &[2, 4, 8, 16] {
        let fields: Vec<Vec<u8>> = (0..concurrency)
            .map(|_| generate_test_data(MEDIUM_FIELD_SIZE))
            .collect();
        
        group.throughput(Throughput::Bytes((MEDIUM_FIELD_SIZE * concurrency) as u64));
        group.bench_with_input(
            BenchmarkId::new("encrypt_100KB", concurrency),
            &fields,
            |b, fields| {
                b.to_async(&rt).iter(|| async {
                    let handles: Vec<_> = fields.iter().map(|field| {
                        let service = encryption_service.clone();
                        let field = field.clone();
                        tokio::spawn(async move {
                            service.encrypt_field(black_box(&field)).await.unwrap()
                        })
                    }).collect();
                    
                    let results = futures::future::join_all(handles).await;
                    black_box(results);
                });
            },
        );
    }
    group.finish();
}

// Benchmark AES operations separately to isolate crypto overhead
pub fn aes_benchmarks(c: &mut Criterion) {
    use aes_gcm::{
        aead::{Aead, AeadCore, KeyInit, OsRng},
        Aes256Gcm, Key, Nonce,
    };
    
    let mut group = c.benchmark_group("aes_gcm");
    group.measurement_time(Duration::from_secs(10));
    
    // Generate key once
    let key = Aes256Gcm::generate_key(&mut OsRng);
    let cipher = Aes256Gcm::new(&key);
    
    for &size in &[SMALL_FIELD_SIZE, MEDIUM_FIELD_SIZE, LARGE_FIELD_SIZE] {
        let data = generate_test_data(size);
        let nonce = Aes256Gcm::generate_nonce(&mut OsRng);
        
        let size_label = match size {
            SMALL_FIELD_SIZE => "1KB",
            MEDIUM_FIELD_SIZE => "100KB",
            LARGE_FIELD_SIZE => "1MB",
            _ => "unknown",
        };
        
        group.throughput(Throughput::Bytes(size as u64));
        
        // Benchmark encryption
        group.bench_with_input(
            BenchmarkId::new("encrypt", size_label),
            &data,
            |b, data| {
                b.iter(|| {
                    let encrypted = cipher.encrypt(&nonce, black_box(data.as_ref())).unwrap();
                    black_box(encrypted);
                });
            },
        );
        
        // Benchmark decryption
        let encrypted = cipher.encrypt(&nonce, data.as_ref()).unwrap();
        group.bench_with_input(
            BenchmarkId::new("decrypt", size_label),
            &encrypted,
            |b, encrypted| {
                b.iter(|| {
                    let decrypted = cipher.decrypt(&nonce, black_box(encrypted.as_ref())).unwrap();
                    black_box(decrypted);
                });
            },
        );
    }
    group.finish();
}

// Benchmark key generation
pub fn key_generation_benchmarks(c: &mut Criterion) {
    use ring::rand::{SecureRandom, SystemRandom};
    
    let mut group = c.benchmark_group("key_generation");
    
    group.bench_function("generate_dek_ring", |b| {
        let rng = SystemRandom::new();
        b.iter(|| {
            let mut key = [0u8; 32];
            rng.fill(&mut key).unwrap();
            black_box(key);
        });
    });
    
    group.bench_function("generate_nonce_aes_gcm", |b| {
        use aes_gcm::{Aes256Gcm, aead::{AeadCore, OsRng}};
        b.iter(|| {
            let nonce = Aes256Gcm::generate_nonce(&mut OsRng);
            black_box(nonce);
        });
    });
    
    group.finish();
}

// Memory usage benchmarks
pub fn memory_benchmarks(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();
    let kms_service = rt.block_on(create_test_kms_service());
    let encryption_service = FieldEncryptionService::new(kms_service);
    
    let mut group = c.benchmark_group("field_encryption/memory");
    
    // Measure memory overhead of encrypted fields
    group.bench_function("encrypted_field_overhead", |b| {
        let data = generate_test_data(MEDIUM_FIELD_SIZE);
        let encrypted = rt.block_on(async {
            encryption_service.encrypt_field(&data).await.unwrap()
        });
        
        b.iter(|| {
            let field = EncryptedField {
                encrypted_data: black_box(encrypted.encrypted_data.clone()),
                key_version: black_box(encrypted.key_version.clone()),
                encryption_algorithm: black_box(encrypted.encryption_algorithm.clone()),
                created_at: black_box(encrypted.created_at),
            };
            black_box(field);
        });
    });
    
    group.finish();
}

criterion_group!(
    benches,
    field_encryption_benchmarks,
    aes_benchmarks,
    key_generation_benchmarks,
    memory_benchmarks
);

criterion_main!(benches);