//! Example integration of SOC 2 compliance automation
//! 
//! This example demonstrates how to integrate the SOC 2 compliance monitoring
//! system into the Episteme analysis engine.

use anyhow::Result;
use axum::Router;
use analysis_engine::{
    audit::AuditLogger,
    services::security::compliance::soc2::{
        Soc2Config, Soc2Service, TrustPrincipleThresholds, ReportSchedule,
        collectors::{
            SecurityMetricsCollector, AvailabilityMetricsCollector,
            IntegrityMetricsCollector, ConfidentialityMetricsCollector,
            PrivacyMetricsCollector,
        },
    },
};
#[cfg(feature = "security-storage")]
use analysis_engine::storage::encryption::EncryptionService;
use prometheus::Registry;
use std::sync::Arc;
use tracing::info;

/// Example: Initialize SOC 2 compliance service
pub async fn initialize_soc2_compliance(
    audit_logger: Arc<AuditLogger>,
    #[cfg(feature = "security-storage")]
    encryption_service: Option<Arc<dyn EncryptionService + Send + Sync>>,
    prometheus_registry: Arc<Registry>,
) -> Result<Soc2Service> {
    info!("Initializing SOC 2 compliance automation");

    // Configure SOC 2 compliance
    let config = Soc2Config {
        enable_auto_collection: true,
        evidence_retention_days: 365,
        score_calculation_interval: 300, // 5 minutes
        alert_thresholds: TrustPrincipleThresholds {
            security_threshold: 95.0,
            availability_threshold: 99.9,
            processing_integrity_threshold: 99.0,
            confidentiality_threshold: 100.0,
            privacy_threshold: 98.0,
        },
        report_schedule: ReportSchedule {
            daily_enabled: true,
            monthly_enabled: true,
            quarterly_enabled: true,
            annual_enabled: true,
        },
    };

    // Create SOC 2 service
    let soc2_service = Soc2Service::new(
        config,
        prometheus_registry,
        audit_logger,
        #[cfg(feature = "security-storage")]
        encryption_service,
        #[cfg(not(feature = "security-storage"))]
        None,
    ).await?;

    // Start monitoring
    soc2_service.start_monitoring().await?;

    info!("SOC 2 compliance automation initialized successfully");
    Ok(soc2_service)
}

/// Example: Integrate with API routes
pub fn integrate_soc2_routes(app: Router, soc2_service: &Soc2Service) -> Router {
    // Create SOC 2 API
    let soc2_api = soc2_service.create_api();
    
    // Add routes to application
    app.merge(soc2_api.routes())
}

/// Example: Collect security metrics
pub async fn collect_security_metrics(soc2_service: &Soc2Service) -> Result<()> {
    // Get metrics collectors
    let metrics = soc2_service.monitor().metrics.clone();
    let security_collector = SecurityMetricsCollector::new(metrics);

    // Record authentication attempts
    security_collector.record_auth_attempt(true, "jwt");
    security_collector.record_auth_attempt(false, "api_key");

    // Record encryption operations
    security_collector.record_encryption("encrypt");
    security_collector.record_encryption("decrypt");

    // Record key rotation
    security_collector.record_key_rotation();

    Ok(())
}

/// Example: Collect availability metrics
pub async fn collect_availability_metrics(soc2_service: &Soc2Service) -> Result<()> {
    let metrics = soc2_service.monitor().metrics.clone();
    let availability_collector = AvailabilityMetricsCollector::new(metrics);

    // Record response times
    availability_collector.record_response_time("/api/analysis", "POST", 125.5);
    availability_collector.record_response_time("/api/health", "GET", 5.2);

    // Update error rates
    availability_collector.update_error_rate("analysis-engine", "timeout", 0.1);
    availability_collector.update_error_rate("analysis-engine", "internal", 0.05);

    Ok(())
}

/// Example: Collect processing integrity metrics
pub async fn collect_integrity_metrics(soc2_service: &Soc2Service) -> Result<()> {
    let metrics = soc2_service.monitor().metrics.clone();
    let integrity_collector = IntegrityMetricsCollector::new(metrics);

    // Record validation failures
    integrity_collector.record_validation_failure("input_validation", "warning");
    integrity_collector.record_validation_failure("schema_validation", "error");

    // Record data corruption (hopefully never!)
    // integrity_collector.record_corruption();

    Ok(())
}

/// Example: Collect confidentiality metrics
pub async fn collect_confidentiality_metrics(soc2_service: &Soc2Service) -> Result<()> {
    let metrics = soc2_service.monitor().metrics.clone();
    let confidentiality_collector = ConfidentialityMetricsCollector::new(metrics);

    // Record decryption requests
    confidentiality_collector.record_decryption("user_data_access", true);
    confidentiality_collector.record_decryption("admin_override", true);
    confidentiality_collector.record_decryption("unauthorized_attempt", false);

    Ok(())
}

/// Example: Collect privacy metrics
pub async fn collect_privacy_metrics(soc2_service: &Soc2Service) -> Result<()> {
    let metrics = soc2_service.monitor().metrics.clone();
    let privacy_collector = PrivacyMetricsCollector::new(metrics);

    // Record consent changes
    privacy_collector.record_consent_change("analytics", "granted");
    privacy_collector.record_consent_change("marketing", "withdrawn");

    // Record deletion request
    privacy_collector.record_deletion_request();

    // Record export request
    privacy_collector.record_export_request();

    Ok(())
}

/// Example: Add custom evidence
pub async fn add_custom_evidence(soc2_service: &Soc2Service) -> Result<()> {
    use analysis_engine::services::security::compliance::soc2::evidence::{
        EvidenceItem, EvidenceType, EvidenceData,
    };
    use chrono::{Utc, Duration};
    use std::collections::HashMap;
    use uuid::Uuid;

    let evidence = EvidenceItem {
        id: Uuid::new_v4().to_string(),
        control_id: "CC7.2".to_string(),
        evidence_type: EvidenceType::ManuallyCollected,
        title: "Penetration Test Report".to_string(),
        description: "Annual third-party penetration test results".to_string(),
        collected_at: Utc::now(),
        collector: "security-team".to_string(),
        data: EvidenceData::Json(serde_json::json!({
            "test_date": "2024-01-10",
            "vendor": "SecureTest Inc",
            "vulnerabilities_found": 3,
            "critical": 0,
            "high": 1,
            "medium": 2,
            "low": 0,
            "remediation_status": "in_progress"
        })),
        metadata: HashMap::new(),
        retention_until: Utc::now() + Duration::days(730), // 2 years
    };

    soc2_service.evidence_collector().add_evidence(evidence).await?;

    Ok(())
}

/// Example: Generate on-demand report
pub async fn generate_compliance_report(soc2_service: &Soc2Service) -> Result<()> {
    use analysis_engine::services::security::compliance::soc2::reports::{
        ReportType, ExportFormat,
    };

    // Generate monthly report
    let report = soc2_service
        .report_generator()
        .generate_report(ReportType::Monthly)
        .await?;

    info!("Generated monthly report: {}", report.id);
    info!("Overall compliance score: {:.1}%", report.executive_summary.overall_compliance_score);
    info!("Is compliant: {}", report.executive_summary.is_compliant);

    // Export to different formats
    let json_export = soc2_service
        .report_generator()
        .export_report(&report, ExportFormat::Json)
        .await?;

    let html_export = soc2_service
        .report_generator()
        .export_report(&report, ExportFormat::Html)
        .await?;

    // Save exports
    std::fs::write("soc2-report.json", json_export)?;
    std::fs::write("soc2-report.html", html_export)?;

    Ok(())
}

/// Example: Monitor compliance in real-time
pub async fn monitor_compliance_realtime(soc2_service: &Soc2Service) -> Result<()> {
    use tokio::time::{interval, Duration};

    let mut ticker = interval(Duration::from_secs(60));

    loop {
        ticker.tick().await;

        // Get current compliance score
        let score = soc2_service.monitor().calculate_compliance_score().await?;
        
        info!("Current SOC 2 compliance scores:");
        info!("  Overall: {:.1}%", score.overall);
        info!("  Security: {:.1}%", score.security);
        info!("  Availability: {:.1}%", score.availability);
        info!("  Processing Integrity: {:.1}%", score.processing_integrity);
        info!("  Confidentiality: {:.1}%", score.confidentiality);
        info!("  Privacy: {:.1}%", score.privacy);

        // Check for critical controls
        let critical = soc2_service
            .dashboard()
            .trust_monitor
            .get_critical_controls()
            .await?;

        if !critical.is_empty() {
            info!("⚠️  Critical controls needing attention: {}", critical.len());
            for (principle, control) in critical {
                info!("  - {} / {}: {:.1}% effectiveness",
                    principle.as_str(),
                    control.control_id,
                    control.effectiveness
                );
            }
        }
    }
}

/// Example: Integration with existing monitoring
pub async fn integrate_with_existing_metrics() -> Result<()> {
    use analysis_engine::metrics::prometheus as existing_metrics;

    // SOC 2 metrics automatically integrate with existing Prometheus metrics
    // You can query them together:
    
    // Example: Calculate availability from existing metrics
    let http_success = 15350.0;
    let http_total = 15420.0;
    let availability = (http_success / http_total) * 100.0;
    
    info!("Calculated availability from HTTP metrics: {:.2}%", availability);

    // The SOC 2 system will automatically collect these metrics
    // through the ComplianceCollector

    Ok(())
}

#[tokio::main]
async fn main() -> Result<()> {
    // Initialize tracing
    tracing_subscriber::fmt::init();

    info!("SOC 2 Compliance Integration Example");

    // This is just an example - in production, you would get these from your app
    let audit_logger = Arc::new(AuditLogger::new(None));
    let prometheus_registry = Arc::new(Registry::new());

    // Initialize SOC 2 compliance
    let soc2_service = initialize_soc2_compliance(
        audit_logger,
        #[cfg(feature = "security-storage")]
        None, // No encryption service in this example
        prometheus_registry,
    ).await?;

    // Collect some example metrics
    collect_security_metrics(&soc2_service).await?;
    collect_availability_metrics(&soc2_service).await?;
    collect_integrity_metrics(&soc2_service).await?;
    collect_confidentiality_metrics(&soc2_service).await?;
    collect_privacy_metrics(&soc2_service).await?;

    // Add custom evidence
    add_custom_evidence(&soc2_service).await?;

    // Generate a report
    generate_compliance_report(&soc2_service).await?;

    info!("SOC 2 compliance integration example completed successfully");

    Ok(())
}