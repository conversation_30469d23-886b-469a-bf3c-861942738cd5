# Cloud Build configuration for Query Intelligence (Python service)
steps:
  # 1. Python setup and dependencies
  - name: 'python:3.11'
    id: 'setup-python'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        cd /workspace
        
        echo "Setting up Python environment..."
        python -m pip install --upgrade pip
        
        # Install dependencies
        if [ -f "requirements.txt" ]; then
          pip install -r requirements.txt
        fi
        
        if [ -f "requirements-dev.txt" ]; then
          pip install -r requirements-dev.txt
        fi

  # 2. Code quality checks
  - name: 'python:3.11'
    id: 'quality-checks'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        set -e
        cd /workspace
        
        echo "Running code formatting check..."
        black --check .
        
        echo "Running import sorting check..."
        isort --check-only .
        
        echo "Running linting..."
        flake8 .
        
        echo "Running type checking..."
        mypy .
        
        echo "Running unit tests..."
        python -m pytest tests/ -v --cov=. --cov-report=xml
        
        echo "Code quality checks passed"
    waitFor: ['setup-python']

  # 3. Security scanning
  - name: 'python:3.11'
    id: 'security-scan'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        echo "Installing security tools..."
        pip install bandit safety
        
        echo "Running security scan with bandit..."
        bandit -r . -f json -o bandit-report.json || echo "Bandit scan completed with issues"
        
        echo "Checking for known vulnerabilities..."
        safety check --json > safety-report.json || echo "Safety check completed"
        
        echo "Security scans completed"
    waitFor: ['quality-checks']

  # 4. Build Docker image
  - name: 'gcr.io/cloud-builders/docker'
    id: 'build-image'
    args:
      - 'build'
      - '-t'
      - '${_IMAGE_REGISTRY}/query-intelligence:$BUILD_ID'
      - '-t'
      - '${_IMAGE_REGISTRY}/query-intelligence:latest'
      - '.'
    waitFor: ['security-scan']

  # 5. Test Docker image
  - name: 'gcr.io/cloud-builders/docker'
    id: 'test-image'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        echo "Testing Docker image..."
        
        # Run container and test endpoints
        docker run -d --name test-query-intelligence -p 8002:8000 \
          ${_IMAGE_REGISTRY}/query-intelligence:$BUILD_ID
        
        sleep 15
        
        # Health check
        curl -f http://localhost:8002/health || exit 1
        
        # API endpoint test
        curl -f http://localhost:8002/docs || exit 1
        
        echo "Docker image tests passed"
        docker stop test-query-intelligence
    waitFor: ['build-image']

  # 6. Push images
  - name: 'gcr.io/cloud-builders/docker'
    id: 'push-images'
    args:
      - 'push'
      - '--all-tags'
      - '${_IMAGE_REGISTRY}/query-intelligence'
    waitFor: ['test-image']

  # 7. Deploy to development
  - name: 'gcr.io/cloud-builders/gcloud'
    id: 'deploy-dev'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        if [ "$BRANCH_NAME" = "develop" ] || [ "$BRANCH_NAME" = "main" ]; then
          echo "Deploying Query Intelligence to development environment..."
          
          gcloud run deploy query-intelligence-dev \
            --image ${_IMAGE_REGISTRY}/query-intelligence:$BUILD_ID \
            --platform managed \
            --region us-central1 \
            --memory 2Gi \
            --cpu 2 \
            --max-instances 10 \
            --min-instances 0 \
            --timeout 300s \
            --concurrency 1000 \
            --port 8000 \
            --set-env-vars "ENVIRONMENT=development,VERSION=$BUILD_ID" \
            --allow-unauthenticated
          
          echo "Development deployment complete"
        else
          echo "Skipping development deployment - not develop or main branch"
        fi
    waitFor: ['push-images']

# Performance testing step
  - name: 'python:3.11'
    id: 'performance-test'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        echo "Running performance tests..."
        cd /workspace
        
        # Install performance testing tools
        pip install locust pytest-benchmark
        
        # Run benchmarks if available
        if [ -d "benchmarks" ]; then
          python -m pytest benchmarks/ --benchmark-json=benchmark-results.json
          echo "Performance tests completed"
        else
          echo "No benchmarks found, skipping performance tests"
        fi
    waitFor: ['quality-checks']

# Substitutions
substitutions:
  _IMAGE_REGISTRY: 'us-central1-docker.pkg.dev/vibe-match-463114/ccl-services'

# Options
options:
  machineType: 'E2_HIGHCPU_8'
  diskSizeGb: 100
  logging: CLOUD_LOGGING_ONLY

# Timeout
timeout: '1800s'  # 30 minutes

# Artifacts
artifacts:
  images:
    - '${_IMAGE_REGISTRY}/query-intelligence:$BUILD_ID'
    - '${_IMAGE_REGISTRY}/query-intelligence:latest'
  objects:
    location: 'gs://${PROJECT_ID}-build-artifacts/query-intelligence'
    paths:
      - 'coverage.xml'
      - 'bandit-report.json'
      - 'safety-report.json'
      - 'benchmark-results.json'