"""
Retry Handler Implementation

Provides configurable retry mechanisms with various backoff strategies
for handling transient failures gracefully.
"""

import asyncio
import time
import random
from typing import Optional, Callable, Any, Type, Union, Set, TypeVar
from dataclasses import dataclass, field
from functools import wraps
from abc import ABC, abstractmethod
import structlog

from ..monitoring import MetricsCollector

logger = structlog.get_logger()

T = TypeVar('T')


class RetryExhausted(Exception):
    """Raised when all retry attempts are exhausted"""
    def __init__(self, attempts: int, last_error: Exception):
        self.attempts = attempts
        self.last_error = last_error
        super().__init__(f"Retry exhausted after {attempts} attempts: {last_error}")


class BackoffStrategy(ABC):
    """Base class for backoff strategies"""
    
    @abstractmethod
    def get_delay(self, attempt: int) -> float:
        """Get delay for the given attempt number (1-based)"""
        pass


class ConstantBackoff(BackoffStrategy):
    """Constant delay between retries"""
    
    def __init__(self, delay: float):
        self.delay = delay
        
    def get_delay(self, attempt: int) -> float:
        return self.delay


class LinearBackoff(BackoffStrategy):
    """Linear increase in delay"""
    
    def __init__(self, initial_delay: float, increment: float):
        self.initial_delay = initial_delay
        self.increment = increment
        
    def get_delay(self, attempt: int) -> float:
        return self.initial_delay + (attempt - 1) * self.increment


class ExponentialBackoff(BackoffStrategy):
    """Exponential backoff with jitter"""
    
    def __init__(self, 
                 initial_delay: float = 1.0,
                 multiplier: float = 2.0,
                 max_delay: float = 60.0,
                 jitter: bool = True):
        self.initial_delay = initial_delay
        self.multiplier = multiplier
        self.max_delay = max_delay
        self.jitter = jitter
        
    def get_delay(self, attempt: int) -> float:
        # Calculate exponential delay
        delay = self.initial_delay * (self.multiplier ** (attempt - 1))
        delay = min(delay, self.max_delay)
        
        # Add jitter to prevent thundering herd
        if self.jitter:
            delay = delay * (0.5 + random.random() * 0.5)
            
        return delay


class FibonacciBackoff(BackoffStrategy):
    """Fibonacci sequence backoff"""
    
    def __init__(self, initial_delay: float = 1.0, max_delay: float = 60.0):
        self.initial_delay = initial_delay
        self.max_delay = max_delay
        self._fib_cache = {1: 1, 2: 1}
        
    def _fibonacci(self, n: int) -> int:
        """Calculate fibonacci number with memoization"""
        if n in self._fib_cache:
            return self._fib_cache[n]
            
        self._fib_cache[n] = self._fibonacci(n - 1) + self._fibonacci(n - 2)
        return self._fib_cache[n]
        
    def get_delay(self, attempt: int) -> float:
        fib_value = self._fibonacci(attempt)
        delay = self.initial_delay * fib_value
        return min(delay, self.max_delay)


@dataclass
class RetryPolicy:
    """Retry policy configuration"""
    max_attempts: int = 3
    backoff_strategy: BackoffStrategy = field(default_factory=lambda: ExponentialBackoff())
    retry_on: Set[Type[Exception]] = field(default_factory=lambda: {Exception})
    retry_on_result: Optional[Callable[[Any], bool]] = None
    exclude_exceptions: Set[Type[Exception]] = field(default_factory=set)
    timeout: Optional[float] = None  # Total timeout for all retries
    on_retry: Optional[Callable[[Exception, int], None]] = None  # Callback on retry


class RetryHandler:
    """Handles retry logic with configurable policies"""
    
    def __init__(self, 
                 policy: RetryPolicy,
                 metrics_collector: Optional[MetricsCollector] = None,
                 name: Optional[str] = None):
        self.policy = policy
        self.metrics = metrics_collector
        self.name = name or "retry_handler"
        
        logger.info(
            "retry_handler_initialized",
            name=self.name,
            max_attempts=policy.max_attempts,
            backoff=type(policy.backoff_strategy).__name__
        )
        
    async def execute(self, 
                     func: Callable[..., T],
                     *args,
                     **kwargs) -> T:
        """Execute function with retry logic"""
        start_time = time.time()
        last_error = None
        
        for attempt in range(1, self.policy.max_attempts + 1):
            try:
                # Check total timeout
                if self.policy.timeout:
                    elapsed = time.time() - start_time
                    if elapsed >= self.policy.timeout:
                        raise RetryExhausted(attempt - 1, last_error or TimeoutError("Total timeout exceeded"))
                        
                # Execute function
                if asyncio.iscoroutinefunction(func):
                    result = await func(*args, **kwargs)
                else:
                    result = func(*args, **kwargs)
                    
                # Check if we should retry based on result
                if self.policy.retry_on_result and self.policy.retry_on_result(result):
                    if attempt < self.policy.max_attempts:
                        await self._handle_retry(None, attempt, "result_check_failed")
                        continue
                    else:
                        raise RetryExhausted(attempt, Exception("Result check failed"))
                        
                # Success - record metrics and return
                if self.metrics and attempt > 1:
                    self.metrics.retries_total.labels(
                        operation=self.name,
                        reason="success"
                    ).inc(attempt - 1)
                    
                return result
                
            except Exception as e:
                last_error = e
                
                # Check if we should retry this exception
                if not self._should_retry(e):
                    raise
                    
                # Check if we have more attempts
                if attempt >= self.policy.max_attempts:
                    raise RetryExhausted(attempt, e)
                    
                # Handle retry
                await self._handle_retry(e, attempt, type(e).__name__)
                
        # Should not reach here
        raise RetryExhausted(self.policy.max_attempts, last_error)
        
    def _should_retry(self, exception: Exception) -> bool:
        """Check if exception should trigger retry"""
        # Check exclusions first
        if isinstance(exception, tuple(self.policy.exclude_exceptions)):
            return False
            
        # Check if exception type is in retry list
        for exc_type in self.policy.retry_on:
            if isinstance(exception, exc_type):
                return True
                
        return False
        
    async def _handle_retry(self, 
                           error: Optional[Exception],
                           attempt: int,
                           reason: str):
        """Handle retry with delay and callbacks"""
        # Get delay
        delay = self.policy.backoff_strategy.get_delay(attempt)
        
        # Log retry
        logger.warning(
            "retry_attempt",
            name=self.name,
            attempt=attempt,
            max_attempts=self.policy.max_attempts,
            delay=delay,
            reason=reason,
            error=str(error) if error else None
        )
        
        # Record metrics
        if self.metrics:
            self.metrics.record_retry(self.name, reason)
            
        # Callback
        if self.policy.on_retry and error:
            try:
                if asyncio.iscoroutinefunction(self.policy.on_retry):
                    await self.policy.on_retry(error, attempt)
                else:
                    self.policy.on_retry(error, attempt)
            except Exception as e:
                logger.error("retry_callback_error", error=str(e))
                
        # Wait before retry
        await asyncio.sleep(delay)


def retry(max_attempts: int = 3,
          backoff: Optional[BackoffStrategy] = None,
          retry_on: Optional[Set[Type[Exception]]] = None,
          exclude: Optional[Set[Type[Exception]]] = None,
          timeout: Optional[float] = None,
          name: Optional[str] = None,
          metrics: Optional[MetricsCollector] = None):
    """Decorator for adding retry logic to functions"""
    def decorator(func):
        handler_name = name or f"{func.__module__}.{func.__name__}"
        
        policy = RetryPolicy(
            max_attempts=max_attempts,
            backoff_strategy=backoff or ExponentialBackoff(),
            retry_on=retry_on or {Exception},
            exclude_exceptions=exclude or set(),
            timeout=timeout
        )
        
        handler = RetryHandler(policy, metrics, handler_name)
        
        if asyncio.iscoroutinefunction(func):
            @wraps(func)
            async def async_wrapper(*args, **kwargs):
                return await handler.execute(func, *args, **kwargs)
            return async_wrapper
        else:
            @wraps(func)
            def sync_wrapper(*args, **kwargs):
                loop = asyncio.get_event_loop()
                return loop.run_until_complete(handler.execute(func, *args, **kwargs))
            return sync_wrapper
            
    return decorator


# Convenience retry decorators for common patterns
def retry_on_network_error(max_attempts: int = 3):
    """Retry on network-related errors"""
    return retry(
        max_attempts=max_attempts,
        backoff=ExponentialBackoff(initial_delay=0.5),
        retry_on={
            ConnectionError,
            TimeoutError,
            asyncio.TimeoutError,
            OSError
        }
    )


def retry_on_database_error(max_attempts: int = 3):
    """Retry on database errors"""
    return retry(
        max_attempts=max_attempts,
        backoff=ExponentialBackoff(initial_delay=1.0),
        retry_on={
            ConnectionError,
            TimeoutError,
            # Add specific database exceptions here
        }
    )


def retry_with_fibonacci_backoff(max_attempts: int = 5):
    """Retry with fibonacci backoff"""
    return retry(
        max_attempts=max_attempts,
        backoff=FibonacciBackoff()
    )


# Retry handler factory functions
def create_api_retry_handler(name: str = "api_retry",
                           metrics: Optional[MetricsCollector] = None) -> RetryHandler:
    """Create retry handler for API calls"""
    policy = RetryPolicy(
        max_attempts=3,
        backoff_strategy=ExponentialBackoff(
            initial_delay=1.0,
            max_delay=10.0,
            jitter=True
        ),
        retry_on={
            ConnectionError,
            TimeoutError,
            asyncio.TimeoutError
        },
        timeout=30.0
    )
    return RetryHandler(policy, metrics, name)


def create_database_retry_handler(name: str = "database_retry",
                                metrics: Optional[MetricsCollector] = None) -> RetryHandler:
    """Create retry handler for database operations"""
    policy = RetryPolicy(
        max_attempts=5,
        backoff_strategy=ExponentialBackoff(
            initial_delay=0.1,
            max_delay=5.0,
            jitter=True
        ),
        retry_on={
            ConnectionError,
            TimeoutError,
            # Add database-specific exceptions
        },
        timeout=15.0
    )
    return RetryHandler(policy, metrics, name)