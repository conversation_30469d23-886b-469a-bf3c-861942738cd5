"""
Timeout Manager Implementation

Provides timeout management for operations with proper cleanup
and cancellation handling.
"""

import asyncio
import time
from typing import Optional, Callable, Any, TypeVar, Union
from functools import wraps
from contextlib import asynccontextmanager
from dataclasses import dataclass
import structlog

from ..monitoring import MetricsCollector

logger = structlog.get_logger()

T = TypeVar('T')


class TimeoutError(Exception):
    """Custom timeout error with context"""
    def __init__(self, 
                 operation: str,
                 timeout: float,
                 elapsed: Optional[float] = None):
        self.operation = operation
        self.timeout = timeout
        self.elapsed = elapsed
        
        message = f"Operation '{operation}' timed out after {timeout}s"
        if elapsed:
            message += f" (elapsed: {elapsed:.2f}s)"
            
        super().__init__(message)


@dataclass
class TimeoutConfig:
    """Timeout configuration"""
    default_timeout: float = 30.0           # Default timeout in seconds
    connect_timeout: float = 10.0           # Connection timeout
    read_timeout: float = 30.0              # Read timeout
    write_timeout: float = 30.0             # Write timeout
    total_timeout: Optional[float] = None   # Total operation timeout
    cleanup_timeout: float = 5.0            # Cleanup operation timeout


class TimeoutManager:
    """Manages timeouts for various operations"""
    
    def __init__(self, 
                 config: Optional[TimeoutConfig] = None,
                 metrics_collector: Optional[MetricsCollector] = None):
        self.config = config or TimeoutConfig()
        self.metrics = metrics_collector
        self._active_timeouts = set()
        
        logger.info(
            "timeout_manager_initialized",
            default_timeout=self.config.default_timeout
        )
        
    @asynccontextmanager
    async def timeout(self, 
                     seconds: Optional[float] = None,
                     operation: str = "operation"):
        """Context manager for timeouts with proper cleanup"""
        timeout_value = seconds or self.config.default_timeout
        start_time = time.time()
        task = asyncio.current_task()
        
        if task:
            self._active_timeouts.add(task)
            
        try:
            async with asyncio.timeout(timeout_value):
                yield
                
        except asyncio.TimeoutError:
            elapsed = time.time() - start_time
            
            # Record metrics
            if self.metrics:
                self.metrics.record_timeout(operation)
                
            logger.warning(
                "operation_timeout",
                operation=operation,
                timeout=timeout_value,
                elapsed=elapsed
            )
            
            raise TimeoutError(operation, timeout_value, elapsed)
            
        finally:
            if task and task in self._active_timeouts:
                self._active_timeouts.remove(task)
                
    async def execute_with_timeout(self,
                                  func: Callable[..., T],
                                  timeout: Optional[float] = None,
                                  operation: Optional[str] = None,
                                  *args,
                                  **kwargs) -> T:
        """Execute function with timeout"""
        op_name = operation or func.__name__
        
        async with self.timeout(timeout, op_name):
            if asyncio.iscoroutinefunction(func):
                return await func(*args, **kwargs)
            else:
                # Run sync function in executor
                loop = asyncio.get_event_loop()
                return await loop.run_in_executor(None, func, *args, **kwargs)
                
    async def execute_with_cascading_timeout(self,
                                           operations: list[tuple[Callable, float, str]],
                                           total_timeout: Optional[float] = None) -> list[Any]:
        """Execute multiple operations with individual and total timeouts"""
        results = []
        start_time = time.time()
        total_timeout = total_timeout or self.config.total_timeout
        
        for func, op_timeout, op_name in operations:
            # Check if total timeout exceeded
            if total_timeout:
                elapsed = time.time() - start_time
                remaining = total_timeout - elapsed
                
                if remaining <= 0:
                    raise TimeoutError("cascading_operations", total_timeout, elapsed)
                    
                # Use minimum of operation timeout and remaining time
                effective_timeout = min(op_timeout, remaining)
            else:
                effective_timeout = op_timeout
                
            # Execute operation
            result = await self.execute_with_timeout(
                func,
                effective_timeout,
                op_name
            )
            results.append(result)
            
        return results
        
    def timeout_decorator(self, 
                         seconds: Optional[float] = None,
                         operation: Optional[str] = None):
        """Decorator for adding timeout to functions"""
        def decorator(func):
            op_name = operation or func.__name__
            timeout_value = seconds or self.config.default_timeout
            
            if asyncio.iscoroutinefunction(func):
                @wraps(func)
                async def async_wrapper(*args, **kwargs):
                    return await self.execute_with_timeout(
                        func,
                        timeout_value,
                        op_name,
                        *args,
                        **kwargs
                    )
                return async_wrapper
            else:
                @wraps(func)
                def sync_wrapper(*args, **kwargs):
                    loop = asyncio.get_event_loop()
                    return loop.run_until_complete(
                        self.execute_with_timeout(
                            func,
                            timeout_value,
                            op_name,
                            *args,
                            **kwargs
                        )
                    )
                return sync_wrapper
                
        return decorator
        
    async def shutdown(self, timeout: Optional[float] = None):
        """Shutdown with timeout for cleanup operations"""
        cleanup_timeout = timeout or self.config.cleanup_timeout
        
        if self._active_timeouts:
            logger.info(
                "cancelling_active_timeouts",
                count=len(self._active_timeouts)
            )
            
            # Cancel all active timeout tasks
            for task in self._active_timeouts:
                if not task.done():
                    task.cancel()
                    
            # Wait for cancellations with timeout
            try:
                async with asyncio.timeout(cleanup_timeout):
                    await asyncio.gather(
                        *self._active_timeouts,
                        return_exceptions=True
                    )
            except asyncio.TimeoutError:
                logger.warning(
                    "cleanup_timeout",
                    remaining_tasks=sum(1 for t in self._active_timeouts if not t.done())
                )


# Global timeout manager instance
_timeout_manager: Optional[TimeoutManager] = None


def get_timeout_manager(config: Optional[TimeoutConfig] = None,
                       metrics: Optional[MetricsCollector] = None) -> TimeoutManager:
    """Get or create global timeout manager"""
    global _timeout_manager
    if _timeout_manager is None:
        _timeout_manager = TimeoutManager(config, metrics)
    return _timeout_manager


# Convenience decorators
def timeout(seconds: float, operation: Optional[str] = None):
    """Decorator for adding timeout to functions"""
    manager = get_timeout_manager()
    return manager.timeout_decorator(seconds, operation)


def api_timeout(seconds: float = 30.0):
    """Timeout decorator for API operations"""
    return timeout(seconds, "api_call")


def database_timeout(seconds: float = 10.0):
    """Timeout decorator for database operations"""
    return timeout(seconds, "database_query")


def cache_timeout(seconds: float = 5.0):
    """Timeout decorator for cache operations"""
    return timeout(seconds, "cache_operation")


# Timeout context managers for specific operations
class HTTPTimeout:
    """HTTP request timeout configuration"""
    
    def __init__(self,
                 connect: float = 10.0,
                 read: float = 30.0,
                 write: float = 30.0,
                 pool: float = 10.0):
        self.connect = connect
        self.read = read
        self.write = write
        self.pool = pool
        
    def to_httpx_timeout(self):
        """Convert to httpx timeout object"""
        import httpx
        return httpx.Timeout(
            connect=self.connect,
            read=self.read,
            write=self.write,
            pool=self.pool
        )
        
    def to_aiohttp_timeout(self):
        """Convert to aiohttp timeout object"""
        import aiohttp
        return aiohttp.ClientTimeout(
            total=self.read + self.write,
            connect=self.connect,
            sock_connect=self.connect,
            sock_read=self.read
        )


class DatabaseTimeout:
    """Database operation timeout configuration"""
    
    def __init__(self,
                 connect: float = 10.0,
                 command: float = 30.0,
                 checkout: float = 5.0):
        self.connect = connect
        self.command = command
        self.checkout = checkout
        
    def to_sqlalchemy_args(self) -> dict:
        """Convert to SQLAlchemy connection args"""
        return {
            "connect_timeout": self.connect,
            "command_timeout": self.command,
            "pool_timeout": self.checkout
        }


# Utility functions
async def race_with_timeout(tasks: list[asyncio.Task],
                           timeout: float,
                           return_when: str = asyncio.FIRST_COMPLETED) -> tuple[set, set]:
    """Race multiple tasks with timeout"""
    try:
        async with asyncio.timeout(timeout):
            done, pending = await asyncio.wait(tasks, return_when=return_when)
            return done, pending
    except asyncio.TimeoutError:
        # Cancel pending tasks
        for task in tasks:
            if not task.done():
                task.cancel()
        raise TimeoutError("race_tasks", timeout)


async def staggered_timeout(funcs: list[Callable],
                           base_timeout: float,
                           stagger_delay: float = 1.0) -> Any:
    """Execute functions with staggered start times and timeout"""
    tasks = []
    
    for i, func in enumerate(funcs):
        # Stagger start times
        if i > 0:
            await asyncio.sleep(stagger_delay)
            
        if asyncio.iscoroutinefunction(func):
            task = asyncio.create_task(func())
        else:
            loop = asyncio.get_event_loop()
            task = loop.create_task(loop.run_in_executor(None, func))
            
        tasks.append(task)
        
    # Wait for first to complete or timeout
    done, pending = await race_with_timeout(
        tasks,
        base_timeout,
        asyncio.FIRST_COMPLETED
    )
    
    # Cancel pending tasks
    for task in pending:
        task.cancel()
        
    # Return first completed result
    if done:
        return done.pop().result()
    else:
        raise TimeoutError("staggered_execution", base_timeout)