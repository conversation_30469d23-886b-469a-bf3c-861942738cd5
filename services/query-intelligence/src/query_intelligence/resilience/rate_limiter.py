"""
Rate Limiting Implementation

Provides various rate limiting algorithms including token bucket,
sliding window, and fixed window for request rate control.
"""

import asyncio
import time
from typing import Optional, Dict, Any, Callable, Union
from dataclasses import dataclass, field
from collections import deque
from datetime import datetime, timedelta
from functools import wraps
from abc import ABC, abstractmethod
import structlog
import redis.asyncio as redis

from ..monitoring import MetricsCollector

logger = structlog.get_logger()


class RateLimitExceeded(Exception):
    """Raised when rate limit is exceeded"""
    def __init__(self, 
                 limit: int, 
                 window: float, 
                 retry_after: Optional[float] = None):
        self.limit = limit
        self.window = window
        self.retry_after = retry_after
        
        message = f"Rate limit exceeded: {limit} requests per {window}s"
        if retry_after:
            message += f" (retry after {retry_after:.1f}s)"
            
        super().__init__(message)


@dataclass
class RateLimitConfig:
    """Rate limiter configuration"""
    name: str
    limit: int                      # Maximum requests
    window: float                   # Time window in seconds
    burst_size: Optional[int] = None  # Allow burst (token bucket)
    key_func: Optional[Callable] = None  # Function to extract rate limit key
    exclude_keys: set = field(default_factory=set)  # Keys to exclude from limiting


class RateLimitAlgorithm(ABC):
    """Base class for rate limiting algorithms"""
    
    @abstractmethod
    async def is_allowed(self, key: str) -> tuple[bool, Optional[float]]:
        """Check if request is allowed. Returns (allowed, retry_after)"""
        pass
        
    @abstractmethod
    async def reset(self, key: str):
        """Reset rate limit for a key"""
        pass
        
    @abstractmethod
    def get_stats(self, key: str) -> Dict[str, Any]:
        """Get statistics for a key"""
        pass


class TokenBucket(RateLimitAlgorithm):
    """Token bucket rate limiting algorithm"""
    
    def __init__(self, 
                 rate: float,           # Tokens per second
                 capacity: int,         # Bucket capacity
                 initial_tokens: Optional[int] = None):
        self.rate = rate
        self.capacity = capacity
        self.buckets: Dict[str, Dict[str, Any]] = {}
        self.initial_tokens = initial_tokens or capacity
        self._lock = asyncio.Lock()
        
    async def is_allowed(self, key: str) -> tuple[bool, Optional[float]]:
        """Check if request is allowed"""
        async with self._lock:
            now = time.time()
            
            # Initialize bucket if needed
            if key not in self.buckets:
                self.buckets[key] = {
                    "tokens": self.initial_tokens,
                    "last_update": now
                }
                
            bucket = self.buckets[key]
            
            # Refill tokens based on time passed
            time_passed = now - bucket["last_update"]
            new_tokens = time_passed * self.rate
            bucket["tokens"] = min(self.capacity, bucket["tokens"] + new_tokens)
            bucket["last_update"] = now
            
            # Check if we have tokens
            if bucket["tokens"] >= 1:
                bucket["tokens"] -= 1
                return True, None
            else:
                # Calculate retry time
                retry_after = (1 - bucket["tokens"]) / self.rate
                return False, retry_after
                
    async def reset(self, key: str):
        """Reset bucket for a key"""
        async with self._lock:
            if key in self.buckets:
                del self.buckets[key]
                
    def get_stats(self, key: str) -> Dict[str, Any]:
        """Get bucket statistics"""
        if key not in self.buckets:
            return {
                "tokens": self.initial_tokens,
                "capacity": self.capacity,
                "rate": self.rate
            }
            
        bucket = self.buckets[key]
        now = time.time()
        time_passed = now - bucket["last_update"]
        current_tokens = min(self.capacity, bucket["tokens"] + time_passed * self.rate)
        
        return {
            "tokens": current_tokens,
            "capacity": self.capacity,
            "rate": self.rate,
            "full_in": (self.capacity - current_tokens) / self.rate if current_tokens < self.capacity else 0
        }


class SlidingWindowLog(RateLimitAlgorithm):
    """Sliding window log rate limiting algorithm"""
    
    def __init__(self, limit: int, window: float):
        self.limit = limit
        self.window = window
        self.requests: Dict[str, deque] = {}
        self._lock = asyncio.Lock()
        
    async def is_allowed(self, key: str) -> tuple[bool, Optional[float]]:
        """Check if request is allowed"""
        async with self._lock:
            now = time.time()
            cutoff = now - self.window
            
            # Initialize log if needed
            if key not in self.requests:
                self.requests[key] = deque()
                
            # Remove old entries
            request_log = self.requests[key]
            while request_log and request_log[0] < cutoff:
                request_log.popleft()
                
            # Check limit
            if len(request_log) < self.limit:
                request_log.append(now)
                return True, None
            else:
                # Calculate retry time (when oldest request expires)
                oldest = request_log[0]
                retry_after = oldest + self.window - now
                return False, retry_after
                
    async def reset(self, key: str):
        """Reset log for a key"""
        async with self._lock:
            if key in self.requests:
                del self.requests[key]
                
    def get_stats(self, key: str) -> Dict[str, Any]:
        """Get statistics"""
        if key not in self.requests:
            return {
                "requests": 0,
                "limit": self.limit,
                "window": self.window
            }
            
        now = time.time()
        cutoff = now - self.window
        active_requests = sum(1 for req in self.requests[key] if req >= cutoff)
        
        return {
            "requests": active_requests,
            "limit": self.limit,
            "window": self.window,
            "remaining": max(0, self.limit - active_requests)
        }


class FixedWindow(RateLimitAlgorithm):
    """Fixed window rate limiting algorithm"""
    
    def __init__(self, limit: int, window: float):
        self.limit = limit
        self.window = window
        self.counters: Dict[str, Dict[str, Any]] = {}
        self._lock = asyncio.Lock()
        
    async def is_allowed(self, key: str) -> tuple[bool, Optional[float]]:
        """Check if request is allowed"""
        async with self._lock:
            now = time.time()
            window_start = int(now / self.window) * self.window
            
            # Initialize counter if needed
            if key not in self.counters:
                self.counters[key] = {
                    "count": 0,
                    "window_start": window_start
                }
                
            counter = self.counters[key]
            
            # Reset if new window
            if counter["window_start"] < window_start:
                counter["count"] = 0
                counter["window_start"] = window_start
                
            # Check limit
            if counter["count"] < self.limit:
                counter["count"] += 1
                return True, None
            else:
                # Calculate retry time (next window)
                retry_after = window_start + self.window - now
                return False, retry_after
                
    async def reset(self, key: str):
        """Reset counter for a key"""
        async with self._lock:
            if key in self.counters:
                del self.counters[key]
                
    def get_stats(self, key: str) -> Dict[str, Any]:
        """Get statistics"""
        now = time.time()
        window_start = int(now / self.window) * self.window
        
        if key not in self.counters:
            return {
                "count": 0,
                "limit": self.limit,
                "window": self.window,
                "reset_in": window_start + self.window - now
            }
            
        counter = self.counters[key]
        
        # Check if current window
        if counter["window_start"] < window_start:
            count = 0
        else:
            count = counter["count"]
            
        return {
            "count": count,
            "limit": self.limit,
            "window": self.window,
            "remaining": max(0, self.limit - count),
            "reset_in": window_start + self.window - now
        }


class RedisRateLimiter(RateLimitAlgorithm):
    """Redis-backed distributed rate limiter"""
    
    def __init__(self, 
                 redis_client: redis.Redis,
                 limit: int,
                 window: float,
                 key_prefix: str = "ratelimit"):
        self.redis = redis_client
        self.limit = limit
        self.window = window
        self.key_prefix = key_prefix
        
    async def is_allowed(self, key: str) -> tuple[bool, Optional[float]]:
        """Check if request is allowed using Redis"""
        full_key = f"{self.key_prefix}:{key}"
        now = time.time()
        window_start = now - self.window
        
        # Use Redis sorted set for sliding window
        pipe = self.redis.pipeline()
        
        # Remove old entries
        pipe.zremrangebyscore(full_key, 0, window_start)
        
        # Count current entries
        pipe.zcard(full_key)
        
        # Add current request
        pipe.zadd(full_key, {str(now): now})
        
        # Set expiry
        pipe.expire(full_key, int(self.window) + 1)
        
        results = await pipe.execute()
        count = results[1]
        
        if count < self.limit:
            return True, None
        else:
            # Get oldest entry to calculate retry time
            oldest = await self.redis.zrange(full_key, 0, 0, withscores=True)
            if oldest:
                retry_after = oldest[0][1] + self.window - now
                return False, retry_after
            else:
                return False, self.window
                
    async def reset(self, key: str):
        """Reset rate limit for a key"""
        full_key = f"{self.key_prefix}:{key}"
        await self.redis.delete(full_key)
        
    def get_stats(self, key: str) -> Dict[str, Any]:
        """Get statistics (not implemented for Redis)"""
        return {
            "limit": self.limit,
            "window": self.window,
            "backend": "redis"
        }


class RateLimiter:
    """Main rate limiter class"""
    
    def __init__(self, 
                 config: RateLimitConfig,
                 algorithm: Optional[RateLimitAlgorithm] = None,
                 metrics_collector: Optional[MetricsCollector] = None):
        self.config = config
        self.metrics = metrics_collector
        
        # Use provided algorithm or create default
        if algorithm:
            self.algorithm = algorithm
        elif config.burst_size:
            # Use token bucket for burst support
            self.algorithm = TokenBucket(
                rate=config.limit / config.window,
                capacity=config.burst_size
            )
        else:
            # Use sliding window log by default
            self.algorithm = SlidingWindowLog(
                limit=config.limit,
                window=config.window
            )
            
        logger.info(
            "rate_limiter_initialized",
            name=config.name,
            limit=config.limit,
            window=config.window
        )
        
    async def check_limit(self, 
                         key: Optional[str] = None,
                         *args, 
                         **kwargs) -> None:
        """Check rate limit, raise exception if exceeded"""
        # Extract key
        if key is None and self.config.key_func:
            key = self.config.key_func(*args, **kwargs)
        if key is None:
            key = "default"
            
        # Check if excluded
        if key in self.config.exclude_keys:
            return
            
        # Check limit
        allowed, retry_after = await self.algorithm.is_allowed(key)
        
        if not allowed:
            # Update metrics
            if self.metrics:
                self.metrics.record_error("rate_limit_exceeded", self.config.name)
                
            logger.warning(
                "rate_limit_exceeded",
                name=self.config.name,
                key=key,
                retry_after=retry_after
            )
            
            raise RateLimitExceeded(
                limit=self.config.limit,
                window=self.config.window,
                retry_after=retry_after
            )
            
    async def reset(self, key: Optional[str] = None):
        """Reset rate limit for a key"""
        if key is None:
            key = "default"
        await self.algorithm.reset(key)
        
    def get_stats(self, key: Optional[str] = None) -> Dict[str, Any]:
        """Get rate limit statistics"""
        if key is None:
            key = "default"
        return self.algorithm.get_stats(key)
        
    def __call__(self, func):
        """Use as decorator"""
        if asyncio.iscoroutinefunction(func):
            @wraps(func)
            async def async_wrapper(*args, **kwargs):
                await self.check_limit(None, *args, **kwargs)
                return await func(*args, **kwargs)
            return async_wrapper
        else:
            @wraps(func)
            def sync_wrapper(*args, **kwargs):
                loop = asyncio.get_event_loop()
                loop.run_until_complete(self.check_limit(None, *args, **kwargs))
                return func(*args, **kwargs)
            return sync_wrapper


# Convenience functions for creating rate limiters
def rate_limit(limit: int, 
               window: float,
               burst: Optional[int] = None,
               key_func: Optional[Callable] = None,
               name: Optional[str] = None):
    """Decorator for rate limiting"""
    def decorator(func):
        limiter_name = name or f"{func.__module__}.{func.__name__}"
        config = RateLimitConfig(
            name=limiter_name,
            limit=limit,
            window=window,
            burst_size=burst,
            key_func=key_func
        )
        limiter = RateLimiter(config)
        return limiter(func)
    return decorator


def create_api_rate_limiter(requests_per_minute: int = 60,
                           burst_size: Optional[int] = None,
                           key_func: Optional[Callable] = None) -> RateLimiter:
    """Create rate limiter for API endpoints"""
    config = RateLimitConfig(
        name="api_rate_limiter",
        limit=requests_per_minute,
        window=60.0,
        burst_size=burst_size or int(requests_per_minute * 1.5),
        key_func=key_func or (lambda request: request.client.host if hasattr(request, 'client') else "default")
    )
    return RateLimiter(config)


def create_user_rate_limiter(requests_per_hour: int = 1000,
                            key_func: Optional[Callable] = None) -> RateLimiter:
    """Create rate limiter for user requests"""
    config = RateLimitConfig(
        name="user_rate_limiter",
        limit=requests_per_hour,
        window=3600.0,
        key_func=key_func or (lambda user: user.id if hasattr(user, 'id') else "anonymous")
    )
    return RateLimiter(config)