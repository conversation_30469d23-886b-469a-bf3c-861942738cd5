"""
Graceful Shutdown Implementation

Provides graceful shutdown handling with proper cleanup of resources,
in-flight requests, and dependent services.
"""

import asyncio
import signal
import time
from typing import Optional, Callable, List, Dict, Any, Set
from dataclasses import dataclass, field
from enum import Enum
from contextlib import asynccontextmanager
import structlog

logger = structlog.get_logger()


class ShutdownPhase(Enum):
    """Phases of graceful shutdown"""
    NOT_STARTED = "not_started"
    DRAINING = "draining"          # Stop accepting new requests
    FINISHING = "finishing"        # Finish in-flight requests
    CLEANING = "cleaning"          # Clean up resources
    COMPLETE = "complete"          # Shutdown complete


@dataclass
class ShutdownConfig:
    """Graceful shutdown configuration"""
    drain_timeout: float = 10.0        # Time to stop accepting new requests
    request_timeout: float = 30.0      # Time to finish in-flight requests
    cleanup_timeout: float = 10.0      # Time for resource cleanup
    force_timeout: float = 60.0        # Total time before force shutdown
    health_check_path: str = "/health" # Health check endpoint to mark unhealthy


class ShutdownHandler:
    """Handles graceful shutdown process"""
    
    def __init__(self, config: Optional[ShutdownConfig] = None):
        self.config = config or ShutdownConfig()
        self.phase = ShutdownPhase.NOT_STARTED
        self.shutdown_event = asyncio.Event()
        self.is_healthy = True
        
        # Tracking
        self.active_requests: Set[asyncio.Task] = set()
        self.cleanup_handlers: List[Callable] = []
        self.shutdown_callbacks: Dict[ShutdownPhase, List[Callable]] = {
            phase: [] for phase in ShutdownPhase
        }
        
        # Shutdown task
        self._shutdown_task: Optional[asyncio.Task] = None
        self._start_time: Optional[float] = None
        
        logger.info("shutdown_handler_initialized")
        
    def register_cleanup_handler(self, handler: Callable):
        """Register a cleanup handler to be called during shutdown"""
        self.cleanup_handlers.append(handler)
        
    def register_callback(self, phase: ShutdownPhase, callback: Callable):
        """Register callback for specific shutdown phase"""
        self.shutdown_callbacks[phase].append(callback)
        
    def track_request(self, task: asyncio.Task):
        """Track an active request"""
        self.active_requests.add(task)
        task.add_done_callback(lambda t: self.active_requests.discard(t))
        
    @asynccontextmanager
    async def track_operation(self, name: str = "operation"):
        """Context manager to track operations during shutdown"""
        if self.phase != ShutdownPhase.NOT_STARTED:
            # Don't start new operations during shutdown
            logger.warning(
                "operation_rejected_during_shutdown",
                operation=name,
                phase=self.phase.value
            )
            raise RuntimeError(f"Service is shutting down (phase: {self.phase.value})")
            
        task = asyncio.current_task()
        if task:
            self.track_request(task)
            
        try:
            yield
        finally:
            if task and task in self.active_requests:
                self.active_requests.discard(task)
                
    def is_shutting_down(self) -> bool:
        """Check if shutdown is in progress"""
        return self.phase != ShutdownPhase.NOT_STARTED
        
    def is_accepting_requests(self) -> bool:
        """Check if service is accepting new requests"""
        return self.phase == ShutdownPhase.NOT_STARTED and self.is_healthy
        
    async def initiate_shutdown(self, reason: str = "manual"):
        """Initiate graceful shutdown process"""
        if self._shutdown_task and not self._shutdown_task.done():
            logger.info("shutdown_already_in_progress")
            return
            
        logger.info(
            "initiating_graceful_shutdown",
            reason=reason,
            active_requests=len(self.active_requests)
        )
        
        self._start_time = time.time()
        self._shutdown_task = asyncio.create_task(self._shutdown_process())
        self.shutdown_event.set()
        
    async def _shutdown_process(self):
        """Main shutdown process"""
        try:
            # Phase 1: Draining
            await self._enter_phase(ShutdownPhase.DRAINING)
            await self._drain_new_requests()
            
            # Phase 2: Finishing in-flight requests
            await self._enter_phase(ShutdownPhase.FINISHING)
            await self._finish_active_requests()
            
            # Phase 3: Cleaning up resources
            await self._enter_phase(ShutdownPhase.CLEANING)
            await self._cleanup_resources()
            
            # Phase 4: Complete
            await self._enter_phase(ShutdownPhase.COMPLETE)
            
            elapsed = time.time() - self._start_time
            logger.info(
                "graceful_shutdown_complete",
                elapsed_seconds=elapsed
            )
            
        except asyncio.CancelledError:
            logger.warning("shutdown_process_cancelled")
            raise
        except Exception as e:
            logger.error(
                "shutdown_process_error",
                error=str(e),
                phase=self.phase.value
            )
            raise
            
    async def _enter_phase(self, phase: ShutdownPhase):
        """Enter a new shutdown phase"""
        old_phase = self.phase
        self.phase = phase
        
        logger.info(
            "shutdown_phase_transition",
            from_phase=old_phase.value,
            to_phase=phase.value
        )
        
        # Execute phase callbacks
        for callback in self.shutdown_callbacks[phase]:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback()
                else:
                    callback()
            except Exception as e:
                logger.error(
                    "shutdown_callback_error",
                    phase=phase.value,
                    error=str(e)
                )
                
    async def _drain_new_requests(self):
        """Stop accepting new requests"""
        # Mark service as unhealthy
        self.is_healthy = False
        
        # Wait for drain timeout
        try:
            await asyncio.wait_for(
                asyncio.sleep(self.config.drain_timeout),
                timeout=self.config.drain_timeout
            )
        except asyncio.TimeoutError:
            pass
            
        logger.info(
            "drain_phase_complete",
            duration=self.config.drain_timeout
        )
        
    async def _finish_active_requests(self):
        """Wait for active requests to complete"""
        if not self.active_requests:
            logger.info("no_active_requests")
            return
            
        logger.info(
            "waiting_for_active_requests",
            count=len(self.active_requests)
        )
        
        # Wait for requests with timeout
        try:
            await asyncio.wait_for(
                self._wait_for_requests(),
                timeout=self.config.request_timeout
            )
            logger.info("all_requests_completed")
            
        except asyncio.TimeoutError:
            remaining = len(self.active_requests)
            logger.warning(
                "request_timeout_exceeded",
                remaining_requests=remaining
            )
            
            # Cancel remaining requests
            for task in self.active_requests:
                if not task.done():
                    task.cancel()
                    
    async def _wait_for_requests(self):
        """Wait for all active requests to complete"""
        while self.active_requests:
            # Wait for any request to complete
            done, pending = await asyncio.wait(
                self.active_requests,
                return_when=asyncio.FIRST_COMPLETED
            )
            
            # Remove completed requests
            for task in done:
                self.active_requests.discard(task)
                
            if pending:
                logger.debug(
                    "requests_remaining",
                    count=len(pending)
                )
                
    async def _cleanup_resources(self):
        """Clean up resources"""
        logger.info(
            "cleaning_up_resources",
            handlers=len(self.cleanup_handlers)
        )
        
        cleanup_tasks = []
        
        for handler in self.cleanup_handlers:
            try:
                if asyncio.iscoroutinefunction(handler):
                    task = asyncio.create_task(handler())
                    cleanup_tasks.append(task)
                else:
                    handler()
            except Exception as e:
                logger.error(
                    "cleanup_handler_error",
                    error=str(e)
                )
                
        # Wait for async cleanup tasks
        if cleanup_tasks:
            try:
                await asyncio.wait_for(
                    asyncio.gather(*cleanup_tasks, return_exceptions=True),
                    timeout=self.config.cleanup_timeout
                )
            except asyncio.TimeoutError:
                logger.warning(
                    "cleanup_timeout_exceeded",
                    remaining_tasks=sum(1 for t in cleanup_tasks if not t.done())
                )
                
    async def wait_for_shutdown(self):
        """Wait for shutdown to complete"""
        await self.shutdown_event.wait()
        
        if self._shutdown_task:
            await self._shutdown_task
            
    def install_signal_handlers(self):
        """Install signal handlers for graceful shutdown"""
        loop = asyncio.get_event_loop()
        
        for sig in (signal.SIGTERM, signal.SIGINT):
            loop.add_signal_handler(
                sig,
                lambda s=sig: asyncio.create_task(
                    self._signal_handler(s)
                )
            )
            
        logger.info("signal_handlers_installed")
        
    async def _signal_handler(self, sig: signal.Signals):
        """Handle shutdown signals"""
        logger.info(
            "shutdown_signal_received",
            signal=sig.name
        )
        
        await self.initiate_shutdown(f"signal_{sig.name}")
        
    def get_status(self) -> Dict[str, Any]:
        """Get shutdown status"""
        status = {
            "phase": self.phase.value,
            "is_healthy": self.is_healthy,
            "accepting_requests": self.is_accepting_requests(),
            "active_requests": len(self.active_requests)
        }
        
        if self._start_time:
            status["elapsed_seconds"] = time.time() - self._start_time
            
        return status


class GracefulShutdown:
    """Context manager for graceful shutdown"""
    
    def __init__(self, handler: Optional[ShutdownHandler] = None):
        self.handler = handler or ShutdownHandler()
        
    async def __aenter__(self):
        self.handler.install_signal_handlers()
        return self.handler
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.handler.is_shutting_down():
            await self.handler.wait_for_shutdown()
        else:
            # Initiate shutdown if not already started
            await self.handler.initiate_shutdown("context_exit")


# Global shutdown handler
_shutdown_handler: Optional[ShutdownHandler] = None


def get_shutdown_handler(config: Optional[ShutdownConfig] = None) -> ShutdownHandler:
    """Get or create global shutdown handler"""
    global _shutdown_handler
    if _shutdown_handler is None:
        _shutdown_handler = ShutdownHandler(config)
    return _shutdown_handler


# Convenience decorators
def track_request(func):
    """Decorator to track requests during shutdown"""
    handler = get_shutdown_handler()
    
    if asyncio.iscoroutinefunction(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            async with handler.track_operation(func.__name__):
                return await func(*args, **kwargs)
        return async_wrapper
    else:
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            task = asyncio.current_task()
            if task:
                handler.track_request(task)
            try:
                return func(*args, **kwargs)
            finally:
                if task:
                    handler.active_requests.discard(task)
        return sync_wrapper


# Middleware for web frameworks
async def shutdown_middleware(request, call_next):
    """Middleware to handle shutdown for web requests"""
    handler = get_shutdown_handler()
    
    # Check if accepting requests
    if not handler.is_accepting_requests():
        # Return 503 Service Unavailable
        return JSONResponse(
            status_code=503,
            content={
                "error": "Service Unavailable",
                "message": "Service is shutting down",
                "phase": handler.phase.value
            },
            headers={
                "Retry-After": "60"  # Suggest retry after 60 seconds
            }
        )
        
    # Track request
    task = asyncio.current_task()
    if task:
        handler.track_request(task)
        
    try:
        response = await call_next(request)
        return response
    finally:
        if task:
            handler.active_requests.discard(task)