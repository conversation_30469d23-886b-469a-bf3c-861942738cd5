"""
Bulkhead Pattern Implementation

Provides resource isolation to prevent cascading failures by limiting
concurrent operations in different pools.
"""

import asyncio
from typing import Optional, Dict, Any, Callable, TypeVar, Set
from dataclasses import dataclass
from functools import wraps
from contextlib import asynccontextmanager
import time
import structlog

from ..monitoring import MetricsCollector

logger = structlog.get_logger()

T = TypeVar('T')


class BulkheadRejectedError(Exception):
    """Raised when bulkhead rejects an operation due to capacity"""
    def __init__(self, 
                 bulkhead_name: str,
                 max_concurrent: int,
                 current: int,
                 queue_size: Optional[int] = None):
        self.bulkhead_name = bulkhead_name
        self.max_concurrent = max_concurrent
        self.current = current
        self.queue_size = queue_size
        
        message = (f"Bulkhead '{bulkhead_name}' rejected operation: "
                  f"{current}/{max_concurrent} concurrent operations")
        if queue_size is not None:
            message += f", queue full ({queue_size})"
            
        super().__init__(message)


@dataclass
class BulkheadConfig:
    """Bulkhead configuration"""
    name: str
    max_concurrent: int                    # Maximum concurrent operations
    max_queue_size: Optional[int] = None  # Maximum queue size (None = no queue)
    timeout: Optional[float] = None        # Timeout for queued operations
    track_metrics: bool = True             # Track performance metrics


class BulkheadStats:
    """Bulkhead statistics tracking"""
    
    def __init__(self, name: str):
        self.name = name
        self.active_count = 0
        self.queued_count = 0
        self.total_accepted = 0
        self.total_rejected = 0
        self.total_completed = 0
        self.total_errors = 0
        self.total_timeouts = 0
        self._start_time = time.time()
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert stats to dictionary"""
        runtime = time.time() - self._start_time
        
        return {
            "name": self.name,
            "active": self.active_count,
            "queued": self.queued_count,
            "total_accepted": self.total_accepted,
            "total_rejected": self.total_rejected,
            "total_completed": self.total_completed,
            "total_errors": self.total_errors,
            "total_timeouts": self.total_timeouts,
            "runtime_seconds": runtime,
            "throughput": self.total_completed / runtime if runtime > 0 else 0
        }


class Bulkhead:
    """Bulkhead implementation for resource isolation"""
    
    def __init__(self, 
                 config: BulkheadConfig,
                 metrics_collector: Optional[MetricsCollector] = None):
        self.config = config
        self.metrics = metrics_collector
        
        # Semaphore for limiting concurrent operations
        self._semaphore = asyncio.Semaphore(config.max_concurrent)
        
        # Queue for pending operations
        self._queue: Optional[asyncio.Queue] = None
        if config.max_queue_size is not None:
            self._queue = asyncio.Queue(maxsize=config.max_queue_size)
            
        # Statistics
        self.stats = BulkheadStats(config.name)
        
        # Active operations tracking
        self._active_operations: Set[asyncio.Task] = set()
        
        logger.info(
            "bulkhead_initialized",
            name=config.name,
            max_concurrent=config.max_concurrent,
            max_queue_size=config.max_queue_size
        )
        
    @property
    def available_permits(self) -> int:
        """Get number of available permits"""
        return self._semaphore._value
        
    @property
    def is_full(self) -> bool:
        """Check if bulkhead is at capacity"""
        return self.available_permits == 0
        
    async def acquire(self, timeout: Optional[float] = None):
        """Acquire a permit from the bulkhead"""
        timeout = timeout or self.config.timeout
        
        # Check if we can acquire immediately
        if self._semaphore._value > 0:
            await self._semaphore.acquire()
            self.stats.active_count += 1
            self.stats.total_accepted += 1
            
            if self.metrics and self.config.track_metrics:
                self.metrics.update_active_queries(self.stats.active_count)
                
            return
            
        # If no queue, reject immediately
        if self._queue is None:
            self.stats.total_rejected += 1
            
            if self.metrics:
                self.metrics.record_error("bulkhead_rejected", self.config.name)
                
            raise BulkheadRejectedError(
                self.config.name,
                self.config.max_concurrent,
                self.stats.active_count
            )
            
        # Try to queue the request
        try:
            # Create a future for this request
            future = asyncio.Future()
            
            # Try to add to queue
            try:
                self._queue.put_nowait(future)
                self.stats.queued_count += 1
                
                if self.metrics and self.config.track_metrics:
                    self.metrics.update_queue_size(self.stats.queued_count)
                    
            except asyncio.QueueFull:
                self.stats.total_rejected += 1
                
                if self.metrics:
                    self.metrics.record_error("bulkhead_queue_full", self.config.name)
                    
                raise BulkheadRejectedError(
                    self.config.name,
                    self.config.max_concurrent,
                    self.stats.active_count,
                    self._queue.maxsize
                )
                
            # Wait for permit with timeout
            if timeout:
                await asyncio.wait_for(future, timeout=timeout)
            else:
                await future
                
            self.stats.queued_count -= 1
            self.stats.active_count += 1
            self.stats.total_accepted += 1
            
            if self.metrics and self.config.track_metrics:
                self.metrics.update_queue_size(self.stats.queued_count)
                self.metrics.update_active_queries(self.stats.active_count)
                
        except asyncio.TimeoutError:
            self.stats.total_timeouts += 1
            self.stats.queued_count -= 1
            
            if self.metrics:
                self.metrics.record_timeout(f"bulkhead_{self.config.name}")
                
            raise
            
    def release(self):
        """Release a permit back to the bulkhead"""
        self._semaphore.release()
        self.stats.active_count -= 1
        
        if self.metrics and self.config.track_metrics:
            self.metrics.update_active_queries(self.stats.active_count)
            
        # Process queued requests
        if self._queue and not self._queue.empty():
            try:
                future = self._queue.get_nowait()
                if not future.done():
                    future.set_result(None)
            except asyncio.QueueEmpty:
                pass
                
    @asynccontextmanager
    async def execute(self):
        """Context manager for bulkhead execution"""
        await self.acquire()
        
        task = asyncio.current_task()
        if task:
            self._active_operations.add(task)
            
        try:
            yield
            self.stats.total_completed += 1
        except Exception:
            self.stats.total_errors += 1
            raise
        finally:
            self.release()
            if task:
                self._active_operations.discard(task)
                
    async def call(self, func: Callable[..., T], *args, **kwargs) -> T:
        """Execute function within bulkhead"""
        async with self.execute():
            if asyncio.iscoroutinefunction(func):
                return await func(*args, **kwargs)
            else:
                loop = asyncio.get_event_loop()
                return await loop.run_in_executor(None, func, *args, **kwargs)
                
    def get_stats(self) -> Dict[str, Any]:
        """Get bulkhead statistics"""
        stats = self.stats.to_dict()
        stats["available_permits"] = self.available_permits
        stats["utilization"] = (
            self.stats.active_count / self.config.max_concurrent 
            if self.config.max_concurrent > 0 else 0
        )
        return stats
        
    async def shutdown(self, timeout: float = 30.0):
        """Shutdown bulkhead gracefully"""
        logger.info(
            "bulkhead_shutdown_initiated",
            name=self.config.name,
            active_operations=len(self._active_operations)
        )
        
        # Reject new operations
        if self._queue:
            # Clear queue
            while not self._queue.empty():
                try:
                    future = self._queue.get_nowait()
                    if not future.done():
                        future.set_exception(
                            RuntimeError(f"Bulkhead {self.config.name} shutting down")
                        )
                except asyncio.QueueEmpty:
                    break
                    
        # Wait for active operations
        if self._active_operations:
            try:
                await asyncio.wait_for(
                    asyncio.gather(
                        *self._active_operations,
                        return_exceptions=True
                    ),
                    timeout=timeout
                )
            except asyncio.TimeoutError:
                logger.warning(
                    "bulkhead_shutdown_timeout",
                    name=self.config.name,
                    remaining_operations=len(self._active_operations)
                )


# Global bulkhead registry
_bulkheads: Dict[str, Bulkhead] = {}


def get_bulkhead(name: str,
                config: Optional[BulkheadConfig] = None,
                metrics_collector: Optional[MetricsCollector] = None) -> Bulkhead:
    """Get or create a bulkhead"""
    if name not in _bulkheads:
        if config is None:
            config = BulkheadConfig(name=name, max_concurrent=10)
        _bulkheads[name] = Bulkhead(config, metrics_collector)
        
    return _bulkheads[name]


def bulkhead(name: Optional[str] = None,
            max_concurrent: int = 10,
            max_queue_size: Optional[int] = None,
            timeout: Optional[float] = None):
    """Decorator for bulkhead pattern"""
    def decorator(func):
        bulkhead_name = name or f"{func.__module__}.{func.__name__}"
        
        config = BulkheadConfig(
            name=bulkhead_name,
            max_concurrent=max_concurrent,
            max_queue_size=max_queue_size,
            timeout=timeout
        )
        
        bh = get_bulkhead(bulkhead_name, config)
        
        if asyncio.iscoroutinefunction(func):
            @wraps(func)
            async def async_wrapper(*args, **kwargs):
                return await bh.call(func, *args, **kwargs)
            return async_wrapper
        else:
            @wraps(func)
            def sync_wrapper(*args, **kwargs):
                loop = asyncio.get_event_loop()
                return loop.run_until_complete(bh.call(func, *args, **kwargs))
            return sync_wrapper
            
    return decorator


# Predefined bulkheads for common resources
def database_bulkhead(max_concurrent: int = 20):
    """Bulkhead for database operations"""
    return bulkhead(
        name="database",
        max_concurrent=max_concurrent,
        max_queue_size=50,
        timeout=10.0
    )


def api_bulkhead(max_concurrent: int = 50):
    """Bulkhead for API calls"""
    return bulkhead(
        name="api_calls",
        max_concurrent=max_concurrent,
        max_queue_size=100,
        timeout=30.0
    )


def cpu_intensive_bulkhead(max_concurrent: int = 4):
    """Bulkhead for CPU-intensive operations"""
    return bulkhead(
        name="cpu_intensive",
        max_concurrent=max_concurrent,
        max_queue_size=10,
        timeout=60.0
    )


# Bulkhead manager for coordinating multiple bulkheads
class BulkheadManager:
    """Manages multiple bulkheads"""
    
    def __init__(self):
        self.bulkheads: Dict[str, Bulkhead] = {}
        
    def register(self, bulkhead: Bulkhead):
        """Register a bulkhead"""
        self.bulkheads[bulkhead.config.name] = bulkhead
        
    def get(self, name: str) -> Optional[Bulkhead]:
        """Get a bulkhead by name"""
        return self.bulkheads.get(name)
        
    def get_all_stats(self) -> Dict[str, Dict[str, Any]]:
        """Get stats for all bulkheads"""
        return {
            name: bulkhead.get_stats()
            for name, bulkhead in self.bulkheads.items()
        }
        
    async def shutdown_all(self, timeout: float = 30.0):
        """Shutdown all bulkheads"""
        tasks = [
            bulkhead.shutdown(timeout)
            for bulkhead in self.bulkheads.values()
        ]
        
        await asyncio.gather(*tasks, return_exceptions=True)