"""
Circuit Breaker Pattern Implementation

Provides circuit breaker functionality to prevent cascading failures
and allow systems to recover gracefully.
"""

import asyncio
import time
from typing import Optional, Callable, Any, Dict, TypeVar, Union
from enum import Enum
from dataclasses import dataclass, field
from datetime import datetime, timed<PERSON>ta
from functools import wraps
import structlog
from contextvars import ContextV<PERSON>

from ..monitoring import MetricsCollector

logger = structlog.get_logger()

T = TypeVar('T')


class CircuitState(Enum):
    """Circuit breaker states"""
    CLOSED = "closed"      # Normal operation
    OPEN = "open"         # Failing, rejecting requests
    HALF_OPEN = "half_open"  # Testing if service recovered


@dataclass
class CircuitBreakerConfig:
    """Circuit breaker configuration"""
    name: str
    failure_threshold: int = 5          # Failures before opening
    success_threshold: int = 2          # Successes in half-open before closing
    timeout: float = 60.0              # Seconds before trying half-open
    failure_rate_threshold: float = 0.5  # Failure rate to open circuit
    min_calls: int = 10                # Minimum calls before calculating rate
    exclude_exceptions: tuple = field(default_factory=tuple)  # Exceptions to ignore
    on_open: Optional[Callable] = None  # Callback when circuit opens
    on_close: Optional[Callable] = None  # Callback when circuit closes


class CircuitBreakerError(Exception):
    """Raised when circuit breaker is open"""
    def __init__(self, circuit_name: str, message: str = "Circuit breaker is OPEN"):
        self.circuit_name = circuit_name
        super().__init__(f"{circuit_name}: {message}")


class CircuitBreaker:
    """Circuit breaker implementation"""
    
    def __init__(self, 
                 config: CircuitBreakerConfig,
                 metrics_collector: Optional[MetricsCollector] = None):
        self.config = config
        self.metrics = metrics_collector
        
        # State management
        self._state = CircuitState.CLOSED
        self._failure_count = 0
        self._success_count = 0
        self._last_failure_time: Optional[float] = None
        self._opened_at: Optional[float] = None
        
        # Call tracking for rate calculation
        self._call_stats: Dict[float, bool] = {}  # timestamp -> success
        
        # Lock for thread safety
        self._lock = asyncio.Lock()
        
        logger.info(
            "circuit_breaker_initialized",
            name=config.name,
            failure_threshold=config.failure_threshold,
            timeout=config.timeout
        )
        
    @property
    def state(self) -> CircuitState:
        """Get current circuit state"""
        return self._state
        
    @property
    def is_open(self) -> bool:
        """Check if circuit is open"""
        return self._state == CircuitState.OPEN
        
    @property
    def is_closed(self) -> bool:
        """Check if circuit is closed"""
        return self._state == CircuitState.CLOSED
        
    async def call(self, func: Callable[..., T], *args, **kwargs) -> T:
        """Execute function through circuit breaker"""
        async with self._lock:
            # Check if we should transition to half-open
            if self._state == CircuitState.OPEN:
                if self._should_attempt_reset():
                    await self._transition_to_half_open()
                else:
                    self._record_rejection()
                    raise CircuitBreakerError(
                        self.config.name,
                        f"Circuit breaker is OPEN (opened {time.time() - self._opened_at:.1f}s ago)"
                    )
                    
        # Try to execute the function
        try:
            # For async functions
            if asyncio.iscoroutinefunction(func):
                result = await func(*args, **kwargs)
            else:
                result = func(*args, **kwargs)
                
            await self._on_success()
            return result
            
        except Exception as e:
            # Check if we should count this exception
            if not self._should_count_exception(e):
                raise
                
            await self._on_failure(e)
            raise
            
    async def _on_success(self):
        """Handle successful call"""
        async with self._lock:
            # Record call
            self._record_call(True)
            
            if self._state == CircuitState.HALF_OPEN:
                self._success_count += 1
                
                if self._success_count >= self.config.success_threshold:
                    await self._close()
                    
            elif self._state == CircuitState.CLOSED:
                # Reset failure count on success
                self._failure_count = 0
                
    async def _on_failure(self, error: Exception):
        """Handle failed call"""
        async with self._lock:
            # Record call
            self._record_call(False)
            
            self._last_failure_time = time.time()
            
            if self._state == CircuitState.HALF_OPEN:
                # Single failure in half-open reopens circuit
                await self._open()
                
            elif self._state == CircuitState.CLOSED:
                self._failure_count += 1
                
                # Check if we should open based on count or rate
                if self._should_open():
                    await self._open()
                    
            # Log failure
            logger.warning(
                "circuit_breaker_failure",
                name=self.config.name,
                state=self._state.value,
                failure_count=self._failure_count,
                error=str(error)
            )
            
    def _should_count_exception(self, exception: Exception) -> bool:
        """Check if exception should be counted as failure"""
        return not isinstance(exception, self.config.exclude_exceptions)
        
    def _should_open(self) -> bool:
        """Check if circuit should open"""
        # Check failure count threshold
        if self._failure_count >= self.config.failure_threshold:
            return True
            
        # Check failure rate if we have enough calls
        total_calls = len(self._call_stats)
        if total_calls >= self.config.min_calls:
            failures = sum(1 for success in self._call_stats.values() if not success)
            failure_rate = failures / total_calls
            
            if failure_rate >= self.config.failure_rate_threshold:
                return True
                
        return False
        
    def _should_attempt_reset(self) -> bool:
        """Check if we should try to reset circuit"""
        return (
            self._opened_at is not None and
            time.time() - self._opened_at >= self.config.timeout
        )
        
    async def _open(self):
        """Open the circuit"""
        self._state = CircuitState.OPEN
        self._opened_at = time.time()
        self._failure_count = 0
        self._success_count = 0
        
        # Update metrics
        if self.metrics:
            self.metrics.update_circuit_breaker(self.config.name, 1)
            
        # Callback
        if self.config.on_open:
            try:
                if asyncio.iscoroutinefunction(self.config.on_open):
                    await self.config.on_open()
                else:
                    self.config.on_open()
            except Exception as e:
                logger.error("circuit_breaker_callback_error", error=str(e))
                
        logger.error(
            "circuit_breaker_opened",
            name=self.config.name,
            failure_count=self._failure_count
        )
        
    async def _close(self):
        """Close the circuit"""
        self._state = CircuitState.CLOSED
        self._failure_count = 0
        self._success_count = 0
        self._opened_at = None
        
        # Update metrics
        if self.metrics:
            self.metrics.update_circuit_breaker(self.config.name, 0)
            
        # Callback
        if self.config.on_close:
            try:
                if asyncio.iscoroutinefunction(self.config.on_close):
                    await self.config.on_close()
                else:
                    self.config.on_close()
            except Exception as e:
                logger.error("circuit_breaker_callback_error", error=str(e))
                
        logger.info(
            "circuit_breaker_closed",
            name=self.config.name
        )
        
    async def _transition_to_half_open(self):
        """Transition to half-open state"""
        self._state = CircuitState.HALF_OPEN
        self._success_count = 0
        self._failure_count = 0
        
        # Update metrics
        if self.metrics:
            self.metrics.update_circuit_breaker(self.config.name, 2)
            
        logger.info(
            "circuit_breaker_half_open",
            name=self.config.name
        )
        
    def _record_call(self, success: bool):
        """Record call for rate calculation"""
        now = time.time()
        
        # Clean old entries (keep last minute)
        cutoff = now - 60
        self._call_stats = {
            ts: result 
            for ts, result in self._call_stats.items() 
            if ts > cutoff
        }
        
        # Record new call
        self._call_stats[now] = success
        
    def _record_rejection(self):
        """Record rejected call"""
        if self.metrics:
            self.metrics.record_error("circuit_breaker_open", self.config.name)
            
    async def reset(self):
        """Manually reset the circuit breaker"""
        async with self._lock:
            await self._close()
            self._call_stats.clear()
            
    def get_stats(self) -> Dict[str, Any]:
        """Get circuit breaker statistics"""
        total_calls = len(self._call_stats)
        successful_calls = sum(1 for success in self._call_stats.values() if success)
        
        return {
            "name": self.config.name,
            "state": self._state.value,
            "failure_count": self._failure_count,
            "success_count": self._success_count,
            "total_calls": total_calls,
            "successful_calls": successful_calls,
            "failure_rate": (total_calls - successful_calls) / total_calls if total_calls > 0 else 0,
            "opened_at": datetime.fromtimestamp(self._opened_at).isoformat() if self._opened_at else None,
            "last_failure": datetime.fromtimestamp(self._last_failure_time).isoformat() if self._last_failure_time else None
        }


# Global circuit breaker registry
_circuit_breakers: Dict[str, CircuitBreaker] = {}
_circuit_breaker_context: ContextVar[Optional[str]] = ContextVar('circuit_breaker', default=None)


def get_circuit_breaker(name: str, 
                       config: Optional[CircuitBreakerConfig] = None,
                       metrics_collector: Optional[MetricsCollector] = None) -> CircuitBreaker:
    """Get or create a circuit breaker"""
    if name not in _circuit_breakers:
        if config is None:
            config = CircuitBreakerConfig(name=name)
        _circuit_breakers[name] = CircuitBreaker(config, metrics_collector)
        
    return _circuit_breakers[name]


def circuit_breaker(name: Optional[str] = None,
                   failure_threshold: int = 5,
                   timeout: float = 60.0,
                   exclude_exceptions: tuple = (),
                   metrics_collector: Optional[MetricsCollector] = None):
    """Decorator to apply circuit breaker to a function"""
    def decorator(func):
        # Use function name if no name provided
        cb_name = name or f"{func.__module__}.{func.__name__}"
        
        # Create config
        config = CircuitBreakerConfig(
            name=cb_name,
            failure_threshold=failure_threshold,
            timeout=timeout,
            exclude_exceptions=exclude_exceptions
        )
        
        # Get circuit breaker
        cb = get_circuit_breaker(cb_name, config, metrics_collector)
        
        if asyncio.iscoroutinefunction(func):
            @wraps(func)
            async def async_wrapper(*args, **kwargs):
                return await cb.call(func, *args, **kwargs)
            return async_wrapper
        else:
            @wraps(func)
            def sync_wrapper(*args, **kwargs):
                # Run async call in sync context
                loop = asyncio.get_event_loop()
                return loop.run_until_complete(cb.call(func, *args, **kwargs))
            return sync_wrapper
            
    return decorator


# Specific circuit breakers for common services
def analysis_engine_circuit_breaker():
    """Circuit breaker for Analysis Engine calls"""
    return circuit_breaker(
        name="analysis_engine",
        failure_threshold=3,
        timeout=30.0,
        exclude_exceptions=(asyncio.TimeoutError,)
    )


def pattern_mining_circuit_breaker():
    """Circuit breaker for Pattern Mining calls"""
    return circuit_breaker(
        name="pattern_mining",
        failure_threshold=5,
        timeout=60.0,
        exclude_exceptions=(asyncio.TimeoutError,)
    )


def database_circuit_breaker():
    """Circuit breaker for database operations"""
    return circuit_breaker(
        name="database",
        failure_threshold=10,
        timeout=120.0
    )


def cache_circuit_breaker():
    """Circuit breaker for cache operations"""
    return circuit_breaker(
        name="cache",
        failure_threshold=20,
        timeout=30.0
    )