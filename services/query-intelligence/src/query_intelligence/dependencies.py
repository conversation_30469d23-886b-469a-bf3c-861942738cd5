"""
FastAPI Dependencies

Provides dependency injection for various service components including
database sessions, monitoring, caching, and service clients.
"""

from typing import AsyncGenerator, Optional
from contextlib import asynccontextmanager
import httpx
from sqlalchemy.ext.asyncio import AsyncSession
import redis.asyncio as redis
import structlog

from .database import get_session as get_db_session
from .config import get_settings
from .monitoring import (
    MetricsCollector,
    HealthMonitor,
    AlertManager,
    TracingManager,
    get_tracing_manager
)
from .services.cache_manager import CacheManager
from .services.semantic_search import SemanticSearchService
from .services.query_processor import QueryProcessor
from .services.llm_service import LLMService
from .services.context_builder import ContextBuilder
from .middleware.auth import get_current_user, User

logger = structlog.get_logger()

# Global instances
_metrics_collector: Optional[MetricsCollector] = None
_health_monitor: Optional[HealthMonitor] = None
_alert_manager: Optional[AlertManager] = None
_cache_manager: Optional[CacheManager] = None
_semantic_search: Optional[SemanticSearchService] = None
_query_processor: Optional[QueryProcessor] = None
_llm_service: Optional[LLMService] = None
_context_builder: Optional[ContextBuilder] = None

# HTTP clients
_analysis_engine_client: Optional[httpx.AsyncClient] = None
_pattern_mining_client: Optional[httpx.AsyncClient] = None


async def get_session() -> AsyncGenerator[AsyncSession, None]:
    """Get database session"""
    async for session in get_db_session():
        yield session


def get_settings_dependency():
    """Get application settings"""
    return get_settings()


def get_metrics_collector() -> MetricsCollector:
    """Get or create metrics collector instance"""
    global _metrics_collector
    if _metrics_collector is None:
        _metrics_collector = MetricsCollector()
        logger.info("metrics_collector_created")
    return _metrics_collector


def get_health_monitor() -> HealthMonitor:
    """Get or create health monitor instance"""
    global _health_monitor
    if _health_monitor is None:
        _health_monitor = HealthMonitor(
            metrics_collector=get_metrics_collector()
        )
        logger.info("health_monitor_created")
    return _health_monitor


def get_alert_manager() -> AlertManager:
    """Get or create alert manager instance"""
    global _alert_manager
    if _alert_manager is None:
        _alert_manager = AlertManager(
            metrics_collector=get_metrics_collector()
        )
        logger.info("alert_manager_created")
    return _alert_manager


def get_tracing_manager_dependency() -> TracingManager:
    """Get tracing manager instance"""
    return get_tracing_manager()


async def get_redis_client() -> AsyncGenerator[redis.Redis, None]:
    """Get Redis client"""
    settings = get_settings()
    if not settings.REDIS_URL:
        raise ValueError("Redis URL not configured")
        
    client = redis.from_url(
        settings.REDIS_URL,
        decode_responses=True,
        max_connections=settings.REDIS_MAX_CONNECTIONS
    )
    
    try:
        yield client
    finally:
        await client.close()


async def get_cache_manager() -> CacheManager:
    """Get or create cache manager instance"""
    global _cache_manager
    if _cache_manager is None:
        settings = get_settings()
        
        # Get Redis client if configured
        redis_client = None
        if settings.REDIS_URL:
            redis_client = redis.from_url(
                settings.REDIS_URL,
                decode_responses=True,
                max_connections=settings.REDIS_MAX_CONNECTIONS
            )
            
        _cache_manager = CacheManager(
            redis_client=redis_client,
            default_ttl=settings.CACHE_TTL,
            max_memory_mb=settings.CACHE_MAX_MEMORY_MB
        )
        
        await _cache_manager.initialize()
        logger.info("cache_manager_created")
        
    return _cache_manager


async def get_semantic_search() -> SemanticSearchService:
    """Get or create semantic search service"""
    global _semantic_search
    if _semantic_search is None:
        settings = get_settings()
        cache_manager = await get_cache_manager()
        
        _semantic_search = SemanticSearchService(
            analysis_engine_url=settings.ANALYSIS_ENGINE_URL,
            cache_manager=cache_manager
        )
        
        logger.info("semantic_search_service_created")
        
    return _semantic_search


async def get_llm_service() -> LLMService:
    """Get or create LLM service"""
    global _llm_service
    if _llm_service is None:
        settings = get_settings()
        
        _llm_service = LLMService(
            api_key=settings.GOOGLE_API_KEY,
            model_name=settings.MODEL_NAME,
            temperature=settings.MODEL_TEMPERATURE,
            max_output_tokens=settings.MAX_OUTPUT_TOKENS
        )
        
        logger.info("llm_service_created")
        
    return _llm_service


async def get_context_builder() -> ContextBuilder:
    """Get or create context builder"""
    global _context_builder
    if _context_builder is None:
        settings = get_settings()
        semantic_search = await get_semantic_search()
        
        _context_builder = ContextBuilder(
            semantic_search=semantic_search,
            pattern_mining_url=settings.PATTERN_MINING_URL,
            max_context_length=settings.MAX_CONTEXT_LENGTH
        )
        
        logger.info("context_builder_created")
        
    return _context_builder


async def get_query_processor() -> QueryProcessor:
    """Get or create query processor"""
    global _query_processor
    if _query_processor is None:
        llm_service = await get_llm_service()
        context_builder = await get_context_builder()
        cache_manager = await get_cache_manager()
        metrics = get_metrics_collector()
        
        _query_processor = QueryProcessor(
            llm_service=llm_service,
            context_builder=context_builder,
            cache_manager=cache_manager,
            metrics_collector=metrics
        )
        
        logger.info("query_processor_created")
        
    return _query_processor


async def get_analysis_engine_client() -> httpx.AsyncClient:
    """Get Analysis Engine HTTP client"""
    global _analysis_engine_client
    if _analysis_engine_client is None:
        settings = get_settings()
        
        _analysis_engine_client = httpx.AsyncClient(
            base_url=settings.ANALYSIS_ENGINE_URL,
            timeout=httpx.Timeout(30.0),
            limits=httpx.Limits(
                max_keepalive_connections=20,
                max_connections=50
            ),
            headers={
                "User-Agent": f"QueryIntelligence/{settings.VERSION}"
            }
        )
        
        logger.info("analysis_engine_client_created")
        
    return _analysis_engine_client


async def get_pattern_mining_client() -> httpx.AsyncClient:
    """Get Pattern Mining HTTP client"""
    global _pattern_mining_client
    if _pattern_mining_client is None:
        settings = get_settings()
        
        _pattern_mining_client = httpx.AsyncClient(
            base_url=settings.PATTERN_MINING_URL,
            timeout=httpx.Timeout(30.0),
            limits=httpx.Limits(
                max_keepalive_connections=10,
                max_connections=25
            ),
            headers={
                "User-Agent": f"QueryIntelligence/{settings.VERSION}"
            }
        )
        
        logger.info("pattern_mining_client_created")
        
    return _pattern_mining_client


# Cleanup function for graceful shutdown
async def cleanup_dependencies():
    """Cleanup all dependencies on shutdown"""
    global _cache_manager, _analysis_engine_client, _pattern_mining_client
    global _health_monitor, _alert_manager
    
    # Stop monitoring tasks
    if _health_monitor:
        await _health_monitor.stop()
        
    if _alert_manager:
        await _alert_manager.stop()
        
    # Close cache connections
    if _cache_manager:
        await _cache_manager.close()
        _cache_manager = None
        
    # Close HTTP clients
    if _analysis_engine_client:
        await _analysis_engine_client.aclose()
        _analysis_engine_client = None
        
    if _pattern_mining_client:
        await _pattern_mining_client.aclose()
        _pattern_mining_client = None
        
    # Shutdown tracing
    tracing = get_tracing_manager()
    tracing.shutdown()
    
    logger.info("dependencies_cleanup_complete")


# Lifespan context manager for FastAPI
@asynccontextmanager
async def app_lifespan(app):
    """Application lifespan manager"""
    # Startup
    logger.info("starting_application")
    
    # Initialize monitoring
    health_monitor = get_health_monitor()
    alert_manager = get_alert_manager()
    
    await health_monitor.start()
    await alert_manager.start()
    
    # Initialize cache manager
    await get_cache_manager()
    
    yield
    
    # Shutdown
    logger.info("shutting_down_application")
    await cleanup_dependencies()