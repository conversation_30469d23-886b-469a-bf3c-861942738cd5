"""
Security Hardening Module

Implements comprehensive security hardening patterns for production deployment
including input validation, output sanitization, and security headers.
"""

import re
import html
import json
import secrets
import hashlib
from typing import Optional, Dict, Any, List, Set, Callable, Union
from datetime import datetime, timedelta
from functools import wraps
import structlog
from fastapi import Request, Response, HTTPException
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from jose import JWTError, jwt
from cryptography.fernet import Fernet
import bleach
from urllib.parse import urlparse

logger = structlog.get_logger()


class SecurityConfig:
    """Security configuration"""
    
    # JWT settings
    JWT_ALGORITHM = "HS256"
    JWT_EXPIRATION_HOURS = 24
    JWT_REFRESH_EXPIRATION_DAYS = 30
    
    # Password policy
    MIN_PASSWORD_LENGTH = 12
    REQUIRE_UPPERCASE = True
    REQUIRE_LOWERCASE = True
    REQUIRE_NUMBERS = True
    REQUIRE_SPECIAL = True
    
    # Rate limiting
    MAX_LOGIN_ATTEMPTS = 5
    LOCKOUT_DURATION_MINUTES = 30
    
    # Input validation
    MAX_INPUT_LENGTH = 10000
    MAX_QUERY_LENGTH = 1000
    MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
    
    # Allowed file types
    ALLOWED_FILE_EXTENSIONS = {'.txt', '.json', '.yaml', '.yml', '.csv'}
    ALLOWED_MIME_TYPES = {
        'text/plain', 'application/json', 'application/yaml',
        'application/x-yaml', 'text/csv'
    }
    
    # Security headers
    SECURITY_HEADERS = {
        "X-Content-Type-Options": "nosniff",
        "X-Frame-Options": "DENY",
        "X-XSS-Protection": "1; mode=block",
        "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
        "Content-Security-Policy": (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline' 'unsafe-eval'; "
            "style-src 'self' 'unsafe-inline'; "
            "img-src 'self' data: https:; "
            "font-src 'self'; "
            "connect-src 'self'; "
            "frame-ancestors 'none';"
        ),
        "Referrer-Policy": "strict-origin-when-cross-origin",
        "Permissions-Policy": "geolocation=(), microphone=(), camera=()"
    }


class InputValidator:
    """Validates and sanitizes input data"""
    
    # Patterns for common injection attacks
    SQL_INJECTION_PATTERN = re.compile(
        r"(\b(SELECT|INSERT|UPDATE|DELETE|DROP|UNION|CREATE|ALTER|EXEC|EXECUTE)\b)",
        re.IGNORECASE
    )
    
    XSS_PATTERN = re.compile(
        r"(<script|<iframe|javascript:|onerror=|onload=|onclick=|<img\s+src)",
        re.IGNORECASE
    )
    
    PATH_TRAVERSAL_PATTERN = re.compile(r"\.\./|\.\.\\|%2e%2e%2f|%2e%2e\\")
    
    COMMAND_INJECTION_PATTERN = re.compile(r"[;&|`$]|\$\(|\)|\||&&")
    
    @staticmethod
    def validate_query(query: str) -> str:
        """Validate and sanitize query string"""
        if not query:
            raise ValueError("Query cannot be empty")
            
        if len(query) > SecurityConfig.MAX_QUERY_LENGTH:
            raise ValueError(f"Query exceeds maximum length of {SecurityConfig.MAX_QUERY_LENGTH}")
            
        # Check for injection patterns
        if InputValidator.SQL_INJECTION_PATTERN.search(query):
            logger.warning("potential_sql_injection", query=query[:100])
            raise ValueError("Invalid query: potential SQL injection detected")
            
        if InputValidator.XSS_PATTERN.search(query):
            logger.warning("potential_xss", query=query[:100])
            raise ValueError("Invalid query: potential XSS detected")
            
        # Sanitize HTML entities
        query = html.escape(query)
        
        return query
        
    @staticmethod
    def validate_json_input(data: Dict[str, Any], 
                          max_depth: int = 10,
                          max_keys: int = 100) -> Dict[str, Any]:
        """Validate JSON input for structure and content"""
        def check_depth(obj: Any, current_depth: int = 0):
            if current_depth > max_depth:
                raise ValueError(f"JSON exceeds maximum depth of {max_depth}")
                
            if isinstance(obj, dict):
                if len(obj) > max_keys:
                    raise ValueError(f"JSON object exceeds maximum keys of {max_keys}")
                    
                for key, value in obj.items():
                    if not isinstance(key, str):
                        raise ValueError("JSON keys must be strings")
                    check_depth(value, current_depth + 1)
                    
            elif isinstance(obj, list):
                for item in obj:
                    check_depth(item, current_depth + 1)
                    
        check_depth(data)
        return data
        
    @staticmethod
    def validate_file_path(file_path: str) -> str:
        """Validate file path for security issues"""
        if InputValidator.PATH_TRAVERSAL_PATTERN.search(file_path):
            logger.warning("path_traversal_attempt", path=file_path)
            raise ValueError("Invalid file path: path traversal detected")
            
        # Normalize and validate path
        from pathlib import Path
        path = Path(file_path).resolve()
        
        # Ensure path doesn't escape allowed directories
        # This should be configured based on your allowed directories
        return str(path)
        
    @staticmethod
    def sanitize_output(data: Any) -> Any:
        """Sanitize output data to prevent information leakage"""
        if isinstance(data, dict):
            # Remove sensitive fields
            sensitive_fields = {
                'password', 'secret', 'token', 'api_key', 
                'private_key', 'jwt', 'session_id'
            }
            
            sanitized = {}
            for key, value in data.items():
                if any(sensitive in key.lower() for sensitive in sensitive_fields):
                    sanitized[key] = "***REDACTED***"
                else:
                    sanitized[key] = InputValidator.sanitize_output(value)
                    
            return sanitized
            
        elif isinstance(data, list):
            return [InputValidator.sanitize_output(item) for item in data]
            
        elif isinstance(data, str):
            # Ensure no HTML/script injection in output
            return html.escape(data)
            
        return data


class PasswordValidator:
    """Validates password strength and policy compliance"""
    
    @staticmethod
    def validate_password(password: str) -> List[str]:
        """Validate password against security policy"""
        errors = []
        
        if len(password) < SecurityConfig.MIN_PASSWORD_LENGTH:
            errors.append(f"Password must be at least {SecurityConfig.MIN_PASSWORD_LENGTH} characters")
            
        if SecurityConfig.REQUIRE_UPPERCASE and not re.search(r'[A-Z]', password):
            errors.append("Password must contain at least one uppercase letter")
            
        if SecurityConfig.REQUIRE_LOWERCASE and not re.search(r'[a-z]', password):
            errors.append("Password must contain at least one lowercase letter")
            
        if SecurityConfig.REQUIRE_NUMBERS and not re.search(r'\d', password):
            errors.append("Password must contain at least one number")
            
        if SecurityConfig.REQUIRE_SPECIAL and not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            errors.append("Password must contain at least one special character")
            
        # Check for common passwords
        common_passwords = {
            'password', '12345678', 'qwerty', 'abc123', 'password123',
            'admin', 'letmein', 'welcome', 'monkey', '1234567890'
        }
        
        if password.lower() in common_passwords:
            errors.append("Password is too common")
            
        return errors
        
    @staticmethod
    def hash_password(password: str) -> str:
        """Hash password using secure algorithm"""
        # Use bcrypt or argon2 in production
        import bcrypt
        salt = bcrypt.gensalt()
        return bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')
        
    @staticmethod
    def verify_password(password: str, hashed: str) -> bool:
        """Verify password against hash"""
        import bcrypt
        return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))


class JWTManager:
    """Manages JWT token creation and validation"""
    
    def __init__(self, secret_key: str):
        self.secret_key = secret_key
        self.bearer = HTTPBearer()
        
    def create_access_token(self, 
                          user_id: str,
                          additional_claims: Optional[Dict[str, Any]] = None) -> str:
        """Create JWT access token"""
        expire = datetime.utcnow() + timedelta(hours=SecurityConfig.JWT_EXPIRATION_HOURS)
        
        claims = {
            "sub": user_id,
            "exp": expire,
            "iat": datetime.utcnow(),
            "type": "access"
        }
        
        if additional_claims:
            claims.update(additional_claims)
            
        return jwt.encode(claims, self.secret_key, algorithm=SecurityConfig.JWT_ALGORITHM)
        
    def create_refresh_token(self, user_id: str) -> str:
        """Create JWT refresh token"""
        expire = datetime.utcnow() + timedelta(days=SecurityConfig.JWT_REFRESH_EXPIRATION_DAYS)
        
        claims = {
            "sub": user_id,
            "exp": expire,
            "iat": datetime.utcnow(),
            "type": "refresh"
        }
        
        return jwt.encode(claims, self.secret_key, algorithm=SecurityConfig.JWT_ALGORITHM)
        
    def verify_token(self, token: str, token_type: str = "access") -> Dict[str, Any]:
        """Verify and decode JWT token"""
        try:
            payload = jwt.decode(
                token,
                self.secret_key,
                algorithms=[SecurityConfig.JWT_ALGORITHM]
            )
            
            if payload.get("type") != token_type:
                raise ValueError(f"Invalid token type: expected {token_type}")
                
            return payload
            
        except JWTError as e:
            logger.warning("jwt_verification_failed", error=str(e))
            raise ValueError("Invalid token")
            
    async def get_current_user(self, credentials: HTTPAuthorizationCredentials) -> str:
        """Extract current user from JWT token"""
        payload = self.verify_token(credentials.credentials)
        return payload["sub"]


class EncryptionManager:
    """Manages data encryption and decryption"""
    
    def __init__(self, encryption_key: Optional[str] = None):
        if encryption_key:
            self.fernet = Fernet(encryption_key.encode())
        else:
            # Generate a new key if not provided
            self.fernet = Fernet(Fernet.generate_key())
            
    def encrypt(self, data: Union[str, bytes]) -> str:
        """Encrypt data"""
        if isinstance(data, str):
            data = data.encode()
            
        encrypted = self.fernet.encrypt(data)
        return encrypted.decode()
        
    def decrypt(self, encrypted_data: str) -> str:
        """Decrypt data"""
        decrypted = self.fernet.decrypt(encrypted_data.encode())
        return decrypted.decode()
        
    def encrypt_dict(self, data: Dict[str, Any]) -> str:
        """Encrypt dictionary as JSON"""
        json_data = json.dumps(data)
        return self.encrypt(json_data)
        
    def decrypt_dict(self, encrypted_data: str) -> Dict[str, Any]:
        """Decrypt JSON dictionary"""
        json_data = self.decrypt(encrypted_data)
        return json.loads(json_data)


class SecurityMiddleware:
    """Security middleware for FastAPI"""
    
    def __init__(self, 
                 jwt_manager: Optional[JWTManager] = None,
                 allowed_origins: Optional[List[str]] = None):
        self.jwt_manager = jwt_manager
        self.allowed_origins = allowed_origins or ["*"]
        
    async def __call__(self, request: Request, call_next):
        """Process request with security checks"""
        # Add security headers
        response = await call_next(request)
        
        for header, value in SecurityConfig.SECURITY_HEADERS.items():
            response.headers[header] = value
            
        # Add CORS headers if needed
        origin = request.headers.get("origin")
        if origin and (origin in self.allowed_origins or "*" in self.allowed_origins):
            response.headers["Access-Control-Allow-Origin"] = origin
            response.headers["Access-Control-Allow-Credentials"] = "true"
            response.headers["Access-Control-Allow-Methods"] = "GET, POST, PUT, DELETE, OPTIONS"
            response.headers["Access-Control-Allow-Headers"] = "Content-Type, Authorization"
            
        return response


class APIKeyManager:
    """Manages API key generation and validation"""
    
    def __init__(self):
        self.api_keys: Dict[str, Dict[str, Any]] = {}
        
    def generate_api_key(self, 
                        user_id: str,
                        name: str,
                        permissions: List[str]) -> str:
        """Generate new API key"""
        # Generate secure random key
        api_key = secrets.token_urlsafe(32)
        
        # Hash the key for storage
        key_hash = hashlib.sha256(api_key.encode()).hexdigest()
        
        # Store key metadata
        self.api_keys[key_hash] = {
            "user_id": user_id,
            "name": name,
            "permissions": permissions,
            "created_at": datetime.utcnow(),
            "last_used": None,
            "usage_count": 0
        }
        
        return api_key
        
    def validate_api_key(self, api_key: str) -> Optional[Dict[str, Any]]:
        """Validate API key and return metadata"""
        key_hash = hashlib.sha256(api_key.encode()).hexdigest()
        
        if key_hash in self.api_keys:
            # Update usage statistics
            self.api_keys[key_hash]["last_used"] = datetime.utcnow()
            self.api_keys[key_hash]["usage_count"] += 1
            
            return self.api_keys[key_hash]
            
        return None
        
    def revoke_api_key(self, api_key: str) -> bool:
        """Revoke API key"""
        key_hash = hashlib.sha256(api_key.encode()).hexdigest()
        
        if key_hash in self.api_keys:
            del self.api_keys[key_hash]
            return True
            
        return False


def require_auth(permissions: Optional[List[str]] = None):
    """Decorator to require authentication for endpoints"""
    def decorator(func):
        @wraps(func)
        async def wrapper(request: Request, *args, **kwargs):
            # Check for JWT token
            auth_header = request.headers.get("Authorization")
            if auth_header and auth_header.startswith("Bearer "):
                token = auth_header[7:]
                jwt_manager = request.app.state.jwt_manager
                
                try:
                    payload = jwt_manager.verify_token(token)
                    request.state.user_id = payload["sub"]
                    
                    # Check permissions if required
                    if permissions:
                        user_permissions = payload.get("permissions", [])
                        if not any(p in user_permissions for p in permissions):
                            raise HTTPException(status_code=403, detail="Insufficient permissions")
                            
                except ValueError:
                    raise HTTPException(status_code=401, detail="Invalid token")
                    
            # Check for API key
            elif api_key := request.headers.get("X-API-Key"):
                api_key_manager = request.app.state.api_key_manager
                key_info = api_key_manager.validate_api_key(api_key)
                
                if not key_info:
                    raise HTTPException(status_code=401, detail="Invalid API key")
                    
                request.state.user_id = key_info["user_id"]
                
                # Check permissions if required
                if permissions:
                    if not any(p in key_info["permissions"] for p in permissions):
                        raise HTTPException(status_code=403, detail="Insufficient permissions")
                        
            else:
                raise HTTPException(status_code=401, detail="Authentication required")
                
            return await func(request, *args, **kwargs)
            
        return wrapper
    return decorator


def validate_input(validator: Callable):
    """Decorator to validate input data"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Find request object in args
            request = None
            for arg in args:
                if isinstance(arg, Request):
                    request = arg
                    break
                    
            if request:
                # Get request data
                if request.method in ["POST", "PUT", "PATCH"]:
                    try:
                        data = await request.json()
                        validated_data = validator(data)
                        # Replace request with validated data
                        request._body = json.dumps(validated_data).encode()
                    except Exception as e:
                        logger.warning("input_validation_failed", error=str(e))
                        raise HTTPException(status_code=400, detail=f"Invalid input: {str(e)}")
                        
            return await func(*args, **kwargs)
            
        return wrapper
    return decorator


class SecurityAuditor:
    """Audits security events and generates reports"""
    
    def __init__(self):
        self.events: List[Dict[str, Any]] = []
        
    def log_event(self, 
                 event_type: str,
                 user_id: Optional[str] = None,
                 ip_address: Optional[str] = None,
                 details: Optional[Dict[str, Any]] = None):
        """Log security event"""
        event = {
            "timestamp": datetime.utcnow(),
            "event_type": event_type,
            "user_id": user_id,
            "ip_address": ip_address,
            "details": details or {}
        }
        
        self.events.append(event)
        
        # Log to structured logger
        logger.info(
            "security_event",
            **event
        )
        
    def get_failed_login_attempts(self, 
                                user_id: str,
                                since: datetime) -> int:
        """Get number of failed login attempts for user"""
        count = 0
        for event in self.events:
            if (event["event_type"] == "failed_login" and
                event["user_id"] == user_id and
                event["timestamp"] > since):
                count += 1
                
        return count
        
    def is_user_locked_out(self, user_id: str) -> bool:
        """Check if user is locked out due to failed attempts"""
        lockout_time = datetime.utcnow() - timedelta(
            minutes=SecurityConfig.LOCKOUT_DURATION_MINUTES
        )
        
        failed_attempts = self.get_failed_login_attempts(user_id, lockout_time)
        return failed_attempts >= SecurityConfig.MAX_LOGIN_ATTEMPTS
        
    def generate_security_report(self, 
                               start_date: datetime,
                               end_date: datetime) -> Dict[str, Any]:
        """Generate security report for time period"""
        report_events = [
            e for e in self.events
            if start_date <= e["timestamp"] <= end_date
        ]
        
        # Aggregate by event type
        event_counts = {}
        for event in report_events:
            event_type = event["event_type"]
            event_counts[event_type] = event_counts.get(event_type, 0) + 1
            
        # Find suspicious patterns
        suspicious_ips = {}
        for event in report_events:
            if event["event_type"] in ["failed_login", "invalid_token", "unauthorized_access"]:
                ip = event.get("ip_address")
                if ip:
                    suspicious_ips[ip] = suspicious_ips.get(ip, 0) + 1
                    
        return {
            "period": {
                "start": start_date,
                "end": end_date
            },
            "total_events": len(report_events),
            "event_counts": event_counts,
            "suspicious_ips": {
                ip: count for ip, count in suspicious_ips.items()
                if count > 5
            }
        }