"""
Configuration Management System

Provides centralized configuration management with validation,
hot-reloading, secret management, and environment-specific overrides.
"""

import os
import json
import yaml
from typing import Optional, Dict, Any, List, Set, Callable, Union
from pathlib import Path
from dataclasses import dataclass, field
from enum import Enum
import hashlib
import structlog
from pydantic import BaseModel, validator, ValidationError
import asyncio
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler, FileModifiedEvent

logger = structlog.get_logger()


class ConfigEnvironment(Enum):
    """Configuration environments"""
    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"
    TEST = "test"


class ConfigSource(Enum):
    """Configuration sources in priority order"""
    DEFAULT = 0
    FILE = 1
    ENVIRONMENT = 2
    RUNTIME = 3
    SECRET = 4


@dataclass
class ConfigValidationError:
    """Configuration validation error details"""
    field: str
    message: str
    value: Any
    constraint: Optional[str] = None


class ConfigSchema(BaseModel):
    """Base configuration schema with validation"""
    
    # Service configuration
    service_name: str = "query-intelligence"
    service_version: str = "1.0.0"
    environment: ConfigEnvironment = ConfigEnvironment.DEVELOPMENT
    
    # API configuration
    api_host: str = "0.0.0.0"
    api_port: int = 8001
    api_workers: int = 4
    api_reload: bool = False
    
    # Database configuration
    database_url: Optional[str] = None
    database_pool_size: int = 20
    database_max_overflow: int = 10
    database_pool_timeout: float = 30.0
    database_echo: bool = False
    
    # Redis configuration
    redis_url: Optional[str] = None
    redis_pool_size: int = 10
    redis_decode_responses: bool = True
    redis_socket_timeout: float = 5.0
    
    # External services
    analysis_engine_url: Optional[str] = None
    pattern_mining_url: Optional[str] = None
    
    # Performance configuration
    max_concurrent_queries: int = 100
    max_query_queue_size: int = 200
    query_timeout: float = 30.0
    cache_ttl: int = 3600
    
    # Rate limiting
    rate_limit_enabled: bool = True
    rate_limit_requests_per_minute: int = 60
    rate_limit_burst_size: int = 10
    
    # Monitoring
    metrics_enabled: bool = True
    metrics_port: int = 9090
    tracing_enabled: bool = True
    tracing_sample_rate: float = 0.1
    
    # Security
    jwt_secret_key: Optional[str] = None
    cors_allowed_origins: List[str] = ["*"]
    api_key_header: str = "X-API-Key"
    encryption_key: Optional[str] = None
    
    # Logging
    log_level: str = "INFO"
    log_format: str = "json"
    log_file: Optional[str] = None
    
    @validator('api_port')
    def validate_port(cls, v):
        if not 1 <= v <= 65535:
            raise ValueError('Port must be between 1 and 65535')
        return v
        
    @validator('api_workers')
    def validate_workers(cls, v):
        if v < 1:
            raise ValueError('Workers must be at least 1')
        return v
        
    @validator('database_pool_size')
    def validate_pool_size(cls, v):
        if v < 1:
            raise ValueError('Pool size must be at least 1')
        return v
        
    @validator('rate_limit_requests_per_minute')
    def validate_rate_limit(cls, v):
        if v < 1:
            raise ValueError('Rate limit must be at least 1')
        return v
        
    @validator('tracing_sample_rate')
    def validate_sample_rate(cls, v):
        if not 0 <= v <= 1:
            raise ValueError('Sample rate must be between 0 and 1')
        return v


class ConfigManager:
    """Manages application configuration with validation and hot-reloading"""
    
    def __init__(self, 
                 config_dir: Optional[Path] = None,
                 environment: Optional[ConfigEnvironment] = None):
        self.config_dir = config_dir or Path("config")
        self.environment = environment or self._detect_environment()
        
        # Configuration storage
        self._config: Dict[str, Any] = {}
        self._config_sources: Dict[str, ConfigSource] = {}
        self._config_schema = ConfigSchema
        self._validated_config: Optional[ConfigSchema] = None
        
        # Callbacks for configuration changes
        self._change_callbacks: List[Callable[[str, Any, Any], None]] = []
        
        # File watching
        self._observer: Optional[Observer] = None
        self._watched_files: Set[Path] = set()
        
        # Secret management
        self._secret_providers: Dict[str, Callable[[], Optional[str]]] = {}
        
        # Configuration hash for change detection
        self._config_hash: Optional[str] = None
        
        logger.info(
            "config_manager_initialized",
            environment=self.environment.value,
            config_dir=str(self.config_dir)
        )
        
    def _detect_environment(self) -> ConfigEnvironment:
        """Detect environment from environment variables"""
        env = os.getenv("ENVIRONMENT", "development").lower()
        
        try:
            return ConfigEnvironment(env)
        except ValueError:
            logger.warning(
                "unknown_environment",
                environment=env,
                defaulting_to="development"
            )
            return ConfigEnvironment.DEVELOPMENT
            
    async def load_config(self) -> ConfigSchema:
        """Load configuration from all sources"""
        # 1. Load defaults
        self._load_defaults()
        
        # 2. Load from files
        await self._load_config_files()
        
        # 3. Load from environment variables
        self._load_environment_variables()
        
        # 4. Load secrets
        await self._load_secrets()
        
        # 5. Validate configuration
        self._validated_config = self._validate_config()
        
        # 6. Calculate configuration hash
        self._config_hash = self._calculate_config_hash()
        
        # 7. Start file watching if in development
        if self.environment == ConfigEnvironment.DEVELOPMENT:
            self._start_file_watching()
            
        logger.info(
            "configuration_loaded",
            environment=self.environment.value,
            config_hash=self._config_hash[:8]
        )
        
        return self._validated_config
        
    def _load_defaults(self):
        """Load default configuration values"""
        schema = self._config_schema()
        defaults = schema.dict()
        
        for key, value in defaults.items():
            self._config[key] = value
            self._config_sources[key] = ConfigSource.DEFAULT
            
    async def _load_config_files(self):
        """Load configuration from files"""
        # Base configuration file
        base_file = self.config_dir / "config.yaml"
        if base_file.exists():
            await self._load_file(base_file)
            
        # Environment-specific configuration
        env_file = self.config_dir / f"config.{self.environment.value}.yaml"
        if env_file.exists():
            await self._load_file(env_file)
            
        # Local overrides (not committed to git)
        local_file = self.config_dir / "config.local.yaml"
        if local_file.exists():
            await self._load_file(local_file)
            
    async def _load_file(self, file_path: Path):
        """Load configuration from a single file"""
        try:
            with open(file_path, 'r') as f:
                if file_path.suffix == '.yaml':
                    data = yaml.safe_load(f)
                elif file_path.suffix == '.json':
                    data = json.load(f)
                else:
                    logger.warning(
                        "unsupported_config_format",
                        file=str(file_path)
                    )
                    return
                    
            if data:
                for key, value in data.items():
                    self._config[key] = value
                    self._config_sources[key] = ConfigSource.FILE
                    
            self._watched_files.add(file_path)
            
            logger.debug(
                "config_file_loaded",
                file=str(file_path),
                keys=list(data.keys()) if data else []
            )
            
        except Exception as e:
            logger.error(
                "config_file_load_error",
                file=str(file_path),
                error=str(e)
            )
            
    def _load_environment_variables(self):
        """Load configuration from environment variables"""
        # Map environment variables to config keys
        env_mapping = {
            "SERVICE_NAME": "service_name",
            "SERVICE_VERSION": "service_version",
            "ENVIRONMENT": "environment",
            "API_HOST": "api_host",
            "API_PORT": "api_port",
            "API_WORKERS": "api_workers",
            "DATABASE_URL": "database_url",
            "REDIS_URL": "redis_url",
            "ANALYSIS_ENGINE_URL": "analysis_engine_url",
            "PATTERN_MINING_URL": "pattern_mining_url",
            "JWT_SECRET_KEY": "jwt_secret_key",
            "LOG_LEVEL": "log_level",
            "RATE_LIMIT_ENABLED": "rate_limit_enabled",
            "RATE_LIMIT_RPM": "rate_limit_requests_per_minute",
            "METRICS_ENABLED": "metrics_enabled",
            "TRACING_ENABLED": "tracing_enabled",
            "TRACING_SAMPLE_RATE": "tracing_sample_rate"
        }
        
        for env_var, config_key in env_mapping.items():
            value = os.getenv(env_var)
            if value is not None:
                # Type conversion
                if config_key in ["api_port", "api_workers", "rate_limit_requests_per_minute"]:
                    value = int(value)
                elif config_key in ["rate_limit_enabled", "metrics_enabled", "tracing_enabled"]:
                    value = value.lower() in ["true", "1", "yes", "on"]
                elif config_key == "tracing_sample_rate":
                    value = float(value)
                elif config_key == "environment":
                    value = ConfigEnvironment(value.lower())
                    
                self._config[config_key] = value
                self._config_sources[config_key] = ConfigSource.ENVIRONMENT
                
    async def _load_secrets(self):
        """Load secrets from secret providers"""
        for secret_key, provider in self._secret_providers.items():
            try:
                if asyncio.iscoroutinefunction(provider):
                    value = await provider()
                else:
                    value = provider()
                    
                if value is not None:
                    self._config[secret_key] = value
                    self._config_sources[secret_key] = ConfigSource.SECRET
                    
            except Exception as e:
                logger.error(
                    "secret_load_error",
                    secret=secret_key,
                    error=str(e)
                )
                
    def _validate_config(self) -> ConfigSchema:
        """Validate configuration against schema"""
        try:
            # Filter config to only include schema fields
            schema_fields = set(self._config_schema.__fields__.keys())
            filtered_config = {
                k: v for k, v in self._config.items()
                if k in schema_fields
            }
            
            # Validate
            validated = self._config_schema(**filtered_config)
            
            return validated
            
        except ValidationError as e:
            errors = []
            for error in e.errors():
                errors.append(ConfigValidationError(
                    field=".".join(str(x) for x in error["loc"]),
                    message=error["msg"],
                    value=error.get("input"),
                    constraint=error.get("type")
                ))
                
            logger.error(
                "config_validation_failed",
                errors=[{
                    "field": err.field,
                    "message": err.message
                } for err in errors]
            )
            
            raise
            
    def _calculate_config_hash(self) -> str:
        """Calculate hash of current configuration"""
        # Sort config for consistent hashing
        sorted_config = json.dumps(self._config, sort_keys=True, default=str)
        return hashlib.sha256(sorted_config.encode()).hexdigest()
        
    def _start_file_watching(self):
        """Start watching configuration files for changes"""
        if not self._watched_files:
            return
            
        class ConfigFileHandler(FileSystemEventHandler):
            def __init__(self, manager: ConfigManager):
                self.manager = manager
                
            def on_modified(self, event: FileModifiedEvent):
                if not event.is_directory:
                    file_path = Path(event.src_path)
                    if file_path in self.manager._watched_files:
                        logger.info(
                            "config_file_changed",
                            file=str(file_path)
                        )
                        # Schedule reload
                        asyncio.create_task(self.manager.reload_config())
                        
        self._observer = Observer()
        handler = ConfigFileHandler(self)
        
        # Watch directories containing config files
        watched_dirs = set(f.parent for f in self._watched_files)
        for directory in watched_dirs:
            self._observer.schedule(handler, str(directory), recursive=False)
            
        self._observer.start()
        logger.info("config_file_watching_started")
        
    async def reload_config(self):
        """Reload configuration and notify callbacks if changed"""
        old_config = self._config.copy()
        old_hash = self._config_hash
        
        # Reload configuration
        await self.load_config()
        
        # Check if configuration changed
        if self._config_hash != old_hash:
            # Find changed keys
            changed_keys = set()
            for key in set(old_config.keys()) | set(self._config.keys()):
                if old_config.get(key) != self._config.get(key):
                    changed_keys.add(key)
                    
            logger.info(
                "configuration_reloaded",
                changed_keys=list(changed_keys),
                old_hash=old_hash[:8],
                new_hash=self._config_hash[:8]
            )
            
            # Notify callbacks
            for key in changed_keys:
                old_value = old_config.get(key)
                new_value = self._config.get(key)
                
                for callback in self._change_callbacks:
                    try:
                        if asyncio.iscoroutinefunction(callback):
                            await callback(key, old_value, new_value)
                        else:
                            callback(key, old_value, new_value)
                    except Exception as e:
                        logger.error(
                            "config_change_callback_error",
                            key=key,
                            error=str(e)
                        )
                        
    def register_change_callback(self, callback: Callable[[str, Any, Any], None]):
        """Register callback for configuration changes"""
        self._change_callbacks.append(callback)
        
    def register_secret_provider(self, key: str, provider: Callable[[], Optional[str]]):
        """Register a secret provider for a configuration key"""
        self._secret_providers[key] = provider
        
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value"""
        return self._config.get(key, default)
        
    def get_validated(self) -> ConfigSchema:
        """Get validated configuration"""
        if self._validated_config is None:
            raise RuntimeError("Configuration not loaded")
        return self._validated_config
        
    def set_runtime(self, key: str, value: Any):
        """Set configuration value at runtime"""
        self._config[key] = value
        self._config_sources[key] = ConfigSource.RUNTIME
        
        # Re-validate
        self._validated_config = self._validate_config()
        
        # Notify callbacks
        for callback in self._change_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    asyncio.create_task(callback(key, None, value))
                else:
                    callback(key, None, value)
            except Exception as e:
                logger.error(
                    "config_change_callback_error",
                    key=key,
                    error=str(e)
                )
                
    def get_source(self, key: str) -> Optional[ConfigSource]:
        """Get the source of a configuration value"""
        return self._config_sources.get(key)
        
    def export_config(self, 
                     include_secrets: bool = False,
                     format: str = "yaml") -> str:
        """Export current configuration"""
        export_config = {}
        
        for key, value in self._config.items():
            # Skip secrets unless explicitly included
            if not include_secrets and self._config_sources.get(key) == ConfigSource.SECRET:
                export_config[key] = "***REDACTED***"
            else:
                export_config[key] = value
                
        if format == "yaml":
            return yaml.dump(export_config, default_flow_style=False)
        elif format == "json":
            return json.dumps(export_config, indent=2, default=str)
        else:
            raise ValueError(f"Unsupported export format: {format}")
            
    def validate_production_config(self) -> List[str]:
        """Validate configuration for production readiness"""
        issues = []
        
        if self.environment == ConfigEnvironment.PRODUCTION:
            # Check required production settings
            if not self._config.get("database_url"):
                issues.append("database_url is required for production")
                
            if not self._config.get("redis_url"):
                issues.append("redis_url is required for production")
                
            if not self._config.get("jwt_secret_key"):
                issues.append("jwt_secret_key is required for production")
                
            if self._config.get("api_reload", False):
                issues.append("api_reload should be disabled in production")
                
            if self._config.get("database_echo", False):
                issues.append("database_echo should be disabled in production")
                
            if self._config.get("cors_allowed_origins") == ["*"]:
                issues.append("cors_allowed_origins should be restricted in production")
                
            if self._config.get("log_level", "INFO").upper() == "DEBUG":
                issues.append("log_level should not be DEBUG in production")
                
            if self._config.get("tracing_sample_rate", 0) > 0.1:
                issues.append("tracing_sample_rate should be <= 0.1 in production")
                
        return issues
        
    async def shutdown(self):
        """Shutdown configuration manager"""
        if self._observer and self._observer.is_alive():
            self._observer.stop()
            self._observer.join()
            
        logger.info("config_manager_shutdown")


# Global configuration manager instance
_config_manager: Optional[ConfigManager] = None


def get_config_manager() -> ConfigManager:
    """Get global configuration manager instance"""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager


async def load_config() -> ConfigSchema:
    """Load configuration using global manager"""
    manager = get_config_manager()
    return await manager.load_config()