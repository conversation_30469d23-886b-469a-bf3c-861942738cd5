"""
Resilience Middleware

Integrates all resilience patterns (circuit breaker, rate limiting, retry,
timeout, bulkhead) into FastAPI middleware for comprehensive protection.
"""

import time
from typing import Optional, Callable, Dict, Any
from datetime import datetime
import structlog

from fastapi import Request, Response, HTTPException
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

from ..resilience import (
    CircuitBreaker, CircuitBreakerError, CircuitBreakerConfig,
    RateLimiter, RateLimitExceeded, RateLimitConfig,
    RetryHandler, RetryPolicy, ExponentialBackoff,
    TimeoutManager, TimeoutError as ResilienceTimeoutError,
    get_shutdown_handler,
    Bulkhead, BulkheadRejectedError, BulkheadConfig
)
from ..monitoring import MetricsCollector, get_tracing_manager
from ..config import get_settings

logger = structlog.get_logger()


class ResilienceMiddleware(BaseHTTPMiddleware):
    """Comprehensive resilience middleware"""
    
    def __init__(self, 
                 app: ASGIApp,
                 metrics_collector: Optional[MetricsCollector] = None,
                 enable_circuit_breaker: bool = True,
                 enable_rate_limiting: bool = True,
                 enable_bulkhead: bool = True,
                 enable_timeout: bool = True,
                 enable_shutdown_handling: bool = True):
        super().__init__(app)
        
        self.settings = get_settings()
        self.metrics = metrics_collector
        self.tracing = get_tracing_manager()
        
        # Feature flags
        self.enable_circuit_breaker = enable_circuit_breaker
        self.enable_rate_limiting = enable_rate_limiting
        self.enable_bulkhead = enable_bulkhead
        self.enable_timeout = enable_timeout
        self.enable_shutdown_handling = enable_shutdown_handling
        
        # Initialize components
        self._init_circuit_breakers()
        self._init_rate_limiters()
        self._init_bulkheads()
        self._init_timeout_manager()
        
        # Shutdown handler
        if enable_shutdown_handling:
            self.shutdown_handler = get_shutdown_handler()
            
        logger.info("resilience_middleware_initialized")
        
    def _init_circuit_breakers(self):
        """Initialize circuit breakers for different endpoints"""
        if not self.enable_circuit_breaker:
            return
            
        # Query endpoint circuit breaker
        self.query_circuit_breaker = CircuitBreaker(
            CircuitBreakerConfig(
                name="query_endpoint",
                failure_threshold=5,
                success_threshold=2,
                timeout=60.0,
                failure_rate_threshold=0.5,
                min_calls=10
            ),
            self.metrics
        )
        
        # Health check circuit breaker (more lenient)
        self.health_circuit_breaker = CircuitBreaker(
            CircuitBreakerConfig(
                name="health_endpoint",
                failure_threshold=10,
                timeout=30.0
            ),
            self.metrics
        )
        
    def _init_rate_limiters(self):
        """Initialize rate limiters"""
        if not self.enable_rate_limiting:
            return
            
        # Global rate limiter
        self.global_rate_limiter = RateLimiter(
            RateLimitConfig(
                name="global",
                limit=self.settings.RATE_LIMIT_REQUESTS_PER_MINUTE,
                window=60.0,
                burst_size=int(self.settings.RATE_LIMIT_REQUESTS_PER_MINUTE * 1.5),
                key_func=lambda request: self._get_client_key(request)
            ),
            metrics_collector=self.metrics
        )
        
        # Per-user rate limiter
        self.user_rate_limiter = RateLimiter(
            RateLimitConfig(
                name="per_user",
                limit=1000,
                window=3600.0,  # Per hour
                key_func=lambda request: self._get_user_key(request)
            ),
            metrics_collector=self.metrics
        )
        
    def _init_bulkheads(self):
        """Initialize bulkheads for resource isolation"""
        if not self.enable_bulkhead:
            return
            
        # Query processing bulkhead
        self.query_bulkhead = Bulkhead(
            BulkheadConfig(
                name="query_processing",
                max_concurrent=self.settings.MAX_CONCURRENT_QUERIES,
                max_queue_size=self.settings.MAX_QUERY_QUEUE_SIZE,
                timeout=30.0
            ),
            self.metrics
        )
        
        # API calls bulkhead
        self.api_bulkhead = Bulkhead(
            BulkheadConfig(
                name="api_calls",
                max_concurrent=50,
                max_queue_size=100,
                timeout=10.0
            ),
            self.metrics
        )
        
    def _init_timeout_manager(self):
        """Initialize timeout manager"""
        if not self.enable_timeout:
            return
            
        from ..resilience.timeout_manager import TimeoutConfig
        self.timeout_manager = TimeoutManager(
            TimeoutConfig(
                default_timeout=30.0,
                connect_timeout=10.0,
                read_timeout=30.0,
                write_timeout=30.0
            ),
            self.metrics
        )
        
    def _get_client_key(self, request: Request) -> str:
        """Extract client key for rate limiting"""
        # Try to get from headers first (for proxied requests)
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
            
        # Fall back to client host
        if request.client:
            return request.client.host
            
        return "unknown"
        
    def _get_user_key(self, request: Request) -> str:
        """Extract user key for rate limiting"""
        # Try to get from auth header or session
        auth_header = request.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            # In production, decode JWT to get user ID
            return auth_header[7:][:20]  # Use first 20 chars of token
            
        return self._get_client_key(request)
        
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request through resilience layers"""
        start_time = time.time()
        
        # Create tracing span
        async with self.tracing.async_span(
            "resilience_middleware",
            attributes={
                "http.method": request.method,
                "http.url": str(request.url),
                "http.path": request.url.path
            }
        ) as span:
            try:
                # 1. Shutdown handling
                if self.enable_shutdown_handling:
                    if not self.shutdown_handler.is_accepting_requests():
                        return self._service_unavailable_response(
                            "Service is shutting down",
                            {"phase": self.shutdown_handler.phase.value}
                        )
                        
                # 2. Rate limiting
                if self.enable_rate_limiting and request.url.path != "/health":
                    try:
                        await self.global_rate_limiter.check_limit(
                            self._get_client_key(request),
                            request
                        )
                        
                        # Additional per-user rate limiting for authenticated requests
                        if request.headers.get("Authorization"):
                            await self.user_rate_limiter.check_limit(
                                self._get_user_key(request),
                                request
                            )
                            
                    except RateLimitExceeded as e:
                        span.set_attribute("rate_limit.exceeded", True)
                        return self._rate_limit_exceeded_response(e)
                        
                # 3. Circuit breaker (route-specific)
                circuit_breaker = self._get_circuit_breaker_for_route(request.url.path)
                
                # 4. Bulkhead (for query endpoints)
                bulkhead = self._get_bulkhead_for_route(request.url.path)
                
                # Process request through circuit breaker and bulkhead
                if circuit_breaker and self.enable_circuit_breaker:
                    try:
                        if bulkhead and self.enable_bulkhead:
                            # Execute within both circuit breaker and bulkhead
                            response = await circuit_breaker.call(
                                self._execute_with_bulkhead,
                                bulkhead,
                                request,
                                call_next
                            )
                        else:
                            # Just circuit breaker
                            response = await circuit_breaker.call(
                                call_next,
                                request
                            )
                            
                    except CircuitBreakerError as e:
                        span.set_attribute("circuit_breaker.open", True)
                        return self._circuit_breaker_open_response(e)
                        
                elif bulkhead and self.enable_bulkhead:
                    # Just bulkhead
                    try:
                        async with bulkhead.execute():
                            response = await call_next(request)
                    except BulkheadRejectedError as e:
                        span.set_attribute("bulkhead.rejected", True)
                        return self._bulkhead_rejected_response(e)
                else:
                    # No circuit breaker or bulkhead
                    response = await call_next(request)
                    
                # Record success metrics
                duration = (time.time() - start_time) * 1000
                
                span.set_attributes({
                    "http.status_code": response.status_code,
                    "http.response_time_ms": duration
                })
                
                if self.metrics:
                    self.metrics.record_api_call(
                        request.url.path,
                        request.method,
                        response.status_code
                    )
                    
                # Add resilience headers
                response.headers["X-Response-Time-Ms"] = str(int(duration))
                
                if circuit_breaker:
                    response.headers["X-Circuit-Breaker-State"] = circuit_breaker.state.value
                    
                if bulkhead:
                    stats = bulkhead.get_stats()
                    response.headers["X-Bulkhead-Active"] = str(stats["active"])
                    response.headers["X-Bulkhead-Available"] = str(stats["available_permits"])
                    
                return response
                
            except Exception as e:
                # Record error
                span.record_exception(e)
                
                if self.metrics:
                    self.metrics.record_error(
                        type(e).__name__,
                        f"middleware_{request.url.path}"
                    )
                    
                # Re-raise to let FastAPI handle it
                raise
                
    async def _execute_with_bulkhead(self, 
                                   bulkhead: Bulkhead,
                                   request: Request,
                                   call_next: Callable) -> Response:
        """Execute request within bulkhead"""
        async with bulkhead.execute():
            return await call_next(request)
            
    def _get_circuit_breaker_for_route(self, path: str) -> Optional[CircuitBreaker]:
        """Get appropriate circuit breaker for route"""
        if path.startswith("/api/v1/query"):
            return self.query_circuit_breaker
        elif path.startswith("/health"):
            return self.health_circuit_breaker
        return None
        
    def _get_bulkhead_for_route(self, path: str) -> Optional[Bulkhead]:
        """Get appropriate bulkhead for route"""
        if path.startswith("/api/v1/query"):
            return self.query_bulkhead
        elif path.startswith("/api/v1/") and "query" not in path:
            return self.api_bulkhead
        return None
        
    def _rate_limit_exceeded_response(self, error: RateLimitExceeded) -> JSONResponse:
        """Create rate limit exceeded response"""
        retry_after = int(error.retry_after) if error.retry_after else 60
        
        return JSONResponse(
            status_code=429,
            content={
                "error": "Rate Limit Exceeded",
                "message": str(error),
                "retry_after_seconds": retry_after
            },
            headers={
                "Retry-After": str(retry_after),
                "X-RateLimit-Limit": str(error.limit),
                "X-RateLimit-Window": str(error.window)
            }
        )
        
    def _circuit_breaker_open_response(self, error: CircuitBreakerError) -> JSONResponse:
        """Create circuit breaker open response"""
        return JSONResponse(
            status_code=503,
            content={
                "error": "Service Unavailable",
                "message": "Service temporarily unavailable due to high error rate",
                "circuit_breaker": error.circuit_name,
                "retry_after_seconds": 60
            },
            headers={
                "Retry-After": "60"
            }
        )
        
    def _bulkhead_rejected_response(self, error: BulkheadRejectedError) -> JSONResponse:
        """Create bulkhead rejected response"""
        return JSONResponse(
            status_code=503,
            content={
                "error": "Service Overloaded",
                "message": "Service temporarily overloaded, please retry later",
                "bulkhead": error.bulkhead_name,
                "current_load": error.current,
                "max_capacity": error.max_concurrent
            },
            headers={
                "Retry-After": "10"
            }
        )
        
    def _service_unavailable_response(self, message: str, details: Dict[str, Any]) -> JSONResponse:
        """Create service unavailable response"""
        return JSONResponse(
            status_code=503,
            content={
                "error": "Service Unavailable",
                "message": message,
                "details": details
            },
            headers={
                "Retry-After": "60"
            }
        )
        
    async def shutdown(self):
        """Shutdown resilience components"""
        logger.info("shutting_down_resilience_middleware")
        
        # Shutdown bulkheads
        if self.enable_bulkhead:
            tasks = []
            if hasattr(self, 'query_bulkhead'):
                tasks.append(self.query_bulkhead.shutdown())
            if hasattr(self, 'api_bulkhead'):
                tasks.append(self.api_bulkhead.shutdown())
                
            if tasks:
                await asyncio.gather(*tasks, return_exceptions=True)
                
        # Shutdown timeout manager
        if self.enable_timeout and hasattr(self, 'timeout_manager'):
            await self.timeout_manager.shutdown()