"""
Alerting System for Monitoring

Implements alert rules, thresholds, and notification mechanisms
for proactive monitoring and incident response.
"""

import asyncio
import time
from typing import Dict, Any, Optional, List, Callable, Set
from datetime import datetime, timedelta
from enum import Enum
from dataclasses import dataclass, field
import json
import httpx
from collections import deque
import structlog

from ..config import get_settings
from .metrics import MetricsCollector
from .health import HealthStatus, ServiceHealth

logger = structlog.get_logger()


class AlertSeverity(Enum):
    """Alert severity levels"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"
    
    def __lt__(self, other):
        """Enable comparison for severity levels"""
        order = {
            AlertSeverity.INFO: 0,
            AlertSeverity.WARNING: 1,
            AlertSeverity.ERROR: 2,
            AlertSeverity.CRITICAL: 3
        }
        return order[self] < order[other]


@dataclass
class AlertCondition:
    """Defines conditions for triggering alerts"""
    metric: str
    operator: str  # gt, lt, eq, ne, gte, lte
    threshold: float
    duration_seconds: int = 0  # How long condition must be true
    
    def evaluate(self, value: float) -> bool:
        """Evaluate if condition is met"""
        operators = {
            "gt": lambda x, y: x > y,
            "lt": lambda x, y: x < y,
            "eq": lambda x, y: x == y,
            "ne": lambda x, y: x != y,
            "gte": lambda x, y: x >= y,
            "lte": lambda x, y: x <= y
        }
        
        if self.operator not in operators:
            raise ValueError(f"Unknown operator: {self.operator}")
            
        return operators[self.operator](value, self.threshold)


@dataclass
class AlertRule:
    """Alert rule definition"""
    name: str
    description: str
    severity: AlertSeverity
    conditions: List[AlertCondition]
    cooldown_seconds: int = 300  # Prevent alert spam
    enabled: bool = True
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    # Runtime state
    last_triggered: Optional[datetime] = None
    condition_start_time: Optional[datetime] = None
    
    def can_trigger(self) -> bool:
        """Check if alert can be triggered based on cooldown"""
        if not self.enabled:
            return False
            
        if self.last_triggered is None:
            return True
            
        elapsed = (datetime.now() - self.last_triggered).total_seconds()
        return elapsed >= self.cooldown_seconds


@dataclass
class Alert:
    """Active alert instance"""
    rule_name: str
    severity: AlertSeverity
    message: str
    timestamp: datetime = field(default_factory=datetime.now)
    details: Dict[str, Any] = field(default_factory=dict)
    resolved: bool = False
    resolved_at: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            "rule_name": self.rule_name,
            "severity": self.severity.value,
            "message": self.message,
            "timestamp": self.timestamp.isoformat(),
            "details": self.details,
            "resolved": self.resolved,
            "resolved_at": self.resolved_at.isoformat() if self.resolved_at else None
        }


class AlertManager:
    """Manages alert rules and notifications"""
    
    def __init__(self,
                 metrics_collector: Optional[MetricsCollector] = None,
                 check_interval: int = 10):
        self.settings = get_settings()
        self.metrics = metrics_collector
        self.check_interval = check_interval
        
        # Alert configuration
        self.rules: Dict[str, AlertRule] = {}
        self.active_alerts: Dict[str, Alert] = {}
        self.alert_history: deque = deque(maxlen=1000)
        
        # Notification handlers
        self.notification_handlers: List[Callable] = []
        
        # Background monitoring task
        self.monitor_task: Optional[asyncio.Task] = None
        
        # Initialize default rules
        self._init_default_rules()
        
        logger.info("alert_manager_initialized")
        
    def _init_default_rules(self):
        """Initialize default alert rules"""
        # High error rate alert
        self.add_rule(AlertRule(
            name="high_error_rate",
            description="Error rate exceeds 5%",
            severity=AlertSeverity.ERROR,
            conditions=[
                AlertCondition(
                    metric="error_rate",
                    operator="gt",
                    threshold=5.0,
                    duration_seconds=60
                )
            ],
            cooldown_seconds=300
        ))
        
        # High response time alert
        self.add_rule(AlertRule(
            name="high_response_time",
            description="P95 response time exceeds 500ms",
            severity=AlertSeverity.WARNING,
            conditions=[
                AlertCondition(
                    metric="p95_response_time_ms",
                    operator="gt",
                    threshold=500,
                    duration_seconds=120
                )
            ],
            cooldown_seconds=600
        ))
        
        # Low cache hit rate
        self.add_rule(AlertRule(
            name="low_cache_hit_rate",
            description="Cache hit rate below 50%",
            severity=AlertSeverity.WARNING,
            conditions=[
                AlertCondition(
                    metric="cache_hit_rate",
                    operator="lt",
                    threshold=50.0,
                    duration_seconds=300
                )
            ],
            cooldown_seconds=1800
        ))
        
        # High memory usage
        self.add_rule(AlertRule(
            name="high_memory_usage",
            description="Memory usage exceeds 90%",
            severity=AlertSeverity.CRITICAL,
            conditions=[
                AlertCondition(
                    metric="memory_percent",
                    operator="gt",
                    threshold=90.0,
                    duration_seconds=60
                )
            ],
            cooldown_seconds=300
        ))
        
        # Service unavailable
        self.add_rule(AlertRule(
            name="service_unavailable",
            description="Dependent service is unavailable",
            severity=AlertSeverity.CRITICAL,
            conditions=[
                AlertCondition(
                    metric="service_health",
                    operator="eq",
                    threshold=0,  # 0 = unhealthy
                    duration_seconds=30
                )
            ],
            cooldown_seconds=120
        ))
        
        # SLA violation
        self.add_rule(AlertRule(
            name="sla_violation",
            description="SLA target violated",
            severity=AlertSeverity.ERROR,
            conditions=[
                AlertCondition(
                    metric="sla_compliance",
                    operator="lt",
                    threshold=99.0,
                    duration_seconds=300
                )
            ],
            cooldown_seconds=3600
        ))
        
    async def start(self):
        """Start alert monitoring"""
        if not self.monitor_task:
            self.monitor_task = asyncio.create_task(self._monitor_loop())
            logger.info("alert_monitoring_started")
            
    async def stop(self):
        """Stop alert monitoring"""
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
            logger.info("alert_monitoring_stopped")
            
    async def _monitor_loop(self):
        """Background monitoring loop"""
        while True:
            try:
                await self.check_alerts()
                await asyncio.sleep(self.check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error("alert_monitor_error", error=str(e))
                await asyncio.sleep(self.check_interval)
                
    def add_rule(self, rule: AlertRule):
        """Add an alert rule"""
        self.rules[rule.name] = rule
        logger.info("alert_rule_added", rule_name=rule.name)
        
    def remove_rule(self, rule_name: str):
        """Remove an alert rule"""
        if rule_name in self.rules:
            del self.rules[rule_name]
            logger.info("alert_rule_removed", rule_name=rule_name)
            
    def enable_rule(self, rule_name: str):
        """Enable an alert rule"""
        if rule_name in self.rules:
            self.rules[rule_name].enabled = True
            
    def disable_rule(self, rule_name: str):
        """Disable an alert rule"""
        if rule_name in self.rules:
            self.rules[rule_name].enabled = False
            
    async def check_alerts(self):
        """Check all alert rules"""
        current_metrics = await self._get_current_metrics()
        
        for rule_name, rule in self.rules.items():
            if not rule.enabled:
                continue
                
            try:
                await self._check_rule(rule, current_metrics)
            except Exception as e:
                logger.error(
                    "alert_rule_check_failed",
                    rule_name=rule_name,
                    error=str(e)
                )
                
    async def _check_rule(self, rule: AlertRule, metrics: Dict[str, float]):
        """Check a single alert rule"""
        all_conditions_met = True
        condition_details = {}
        
        # Check all conditions
        for condition in rule.conditions:
            metric_value = metrics.get(condition.metric)
            if metric_value is None:
                all_conditions_met = False
                break
                
            condition_met = condition.evaluate(metric_value)
            condition_details[condition.metric] = {
                "value": metric_value,
                "threshold": condition.threshold,
                "operator": condition.operator,
                "met": condition_met
            }
            
            if not condition_met:
                all_conditions_met = False
                
        # Handle condition state changes
        if all_conditions_met:
            if rule.condition_start_time is None:
                # Conditions just became true
                rule.condition_start_time = datetime.now()
            else:
                # Check if duration requirement is met
                elapsed = (datetime.now() - rule.condition_start_time).total_seconds()
                max_duration = max(c.duration_seconds for c in rule.conditions)
                
                if elapsed >= max_duration and rule.can_trigger():
                    # Trigger alert
                    await self._trigger_alert(rule, condition_details)
        else:
            # Conditions no longer met
            rule.condition_start_time = None
            
            # Check if we should resolve the alert
            if rule.name in self.active_alerts:
                await self._resolve_alert(rule.name)
                
    async def _trigger_alert(self, 
                           rule: AlertRule, 
                           condition_details: Dict[str, Any]):
        """Trigger an alert"""
        alert = Alert(
            rule_name=rule.name,
            severity=rule.severity,
            message=rule.description,
            details={
                "conditions": condition_details,
                "metadata": rule.metadata
            }
        )
        
        # Add to active alerts
        self.active_alerts[rule.name] = alert
        self.alert_history.append(alert)
        
        # Update rule state
        rule.last_triggered = datetime.now()
        
        # Send notifications
        await self._send_notifications(alert)
        
        # Update metrics if available
        if self.metrics:
            self.metrics.record_sla_violation(
                sla_type="alert",
                severity=rule.severity.value
            )
            
        logger.warning(
            "alert_triggered",
            rule_name=rule.name,
            severity=rule.severity.value,
            message=rule.description
        )
        
    async def _resolve_alert(self, rule_name: str):
        """Resolve an active alert"""
        if rule_name in self.active_alerts:
            alert = self.active_alerts[rule_name]
            alert.resolved = True
            alert.resolved_at = datetime.now()
            
            # Remove from active alerts
            del self.active_alerts[rule_name]
            
            # Send resolution notification
            await self._send_resolution_notification(alert)
            
            logger.info(
                "alert_resolved",
                rule_name=rule_name,
                duration_seconds=(alert.resolved_at - alert.timestamp).total_seconds()
            )
            
    async def _send_notifications(self, alert: Alert):
        """Send alert notifications"""
        for handler in self.notification_handlers:
            try:
                await handler(alert)
            except Exception as e:
                logger.error(
                    "notification_handler_failed",
                    handler=handler.__name__,
                    error=str(e)
                )
                
    async def _send_resolution_notification(self, alert: Alert):
        """Send alert resolution notification"""
        # Create resolution notification
        resolution_alert = Alert(
            rule_name=f"{alert.rule_name}_resolved",
            severity=AlertSeverity.INFO,
            message=f"Alert resolved: {alert.message}",
            details={
                "original_alert": alert.to_dict(),
                "duration_seconds": (alert.resolved_at - alert.timestamp).total_seconds()
            }
        )
        
        for handler in self.notification_handlers:
            try:
                await handler(resolution_alert)
            except Exception as e:
                logger.error(
                    "resolution_notification_failed",
                    handler=handler.__name__,
                    error=str(e)
                )
                
    async def _get_current_metrics(self) -> Dict[str, float]:
        """Get current metric values"""
        metrics = {}
        
        if self.metrics:
            # Get metrics from collector
            summary = self.metrics.get_metrics_summary()
            
            # Map to alert metric names
            metrics.update({
                "error_rate": summary.get("error_rate", 0),
                "p95_response_time_ms": summary.get("avg_response_time_ms", 0),
                "cache_hit_rate": summary.get("cache_hit_rate", 0),
                "memory_percent": summary.get("memory_usage_mb", 0) / 4096 * 100,  # Assume 4GB limit
                "cpu_percent": summary.get("cpu_usage_percent", 0),
                "active_queries": summary.get("active_queries", 0)
            })
            
        # Add synthetic metrics
        metrics["sla_compliance"] = 99.5  # Would be calculated from actual SLA metrics
        
        return metrics
        
    def add_notification_handler(self, handler: Callable):
        """Add a notification handler"""
        self.notification_handlers.append(handler)
        logger.info(
            "notification_handler_added",
            handler=handler.__name__
        )
        
    def get_active_alerts(self) -> List[Alert]:
        """Get list of active alerts"""
        return list(self.active_alerts.values())
        
    def get_alert_history(self, 
                         hours: int = 24,
                         severity: Optional[AlertSeverity] = None) -> List[Alert]:
        """Get alert history"""
        cutoff = datetime.now() - timedelta(hours=hours)
        
        filtered_alerts = []
        for alert in self.alert_history:
            if alert.timestamp < cutoff:
                continue
                
            if severity and alert.severity != severity:
                continue
                
            filtered_alerts.append(alert)
            
        return filtered_alerts
        
    def get_alert_statistics(self) -> Dict[str, Any]:
        """Get alert statistics"""
        active_by_severity = {}
        for severity in AlertSeverity:
            active_by_severity[severity.value] = sum(
                1 for a in self.active_alerts.values()
                if a.severity == severity
            )
            
        history_24h = self.get_alert_history(hours=24)
        history_by_severity = {}
        for severity in AlertSeverity:
            history_by_severity[severity.value] = sum(
                1 for a in history_24h
                if a.severity == severity
            )
            
        return {
            "active_alerts": len(self.active_alerts),
            "active_by_severity": active_by_severity,
            "alerts_24h": len(history_24h),
            "alerts_24h_by_severity": history_by_severity,
            "rules_total": len(self.rules),
            "rules_enabled": sum(1 for r in self.rules.values() if r.enabled)
        }


# Built-in notification handlers

async def log_notification_handler(alert: Alert):
    """Log alerts to the logging system"""
    log_method = {
        AlertSeverity.INFO: logger.info,
        AlertSeverity.WARNING: logger.warning,
        AlertSeverity.ERROR: logger.error,
        AlertSeverity.CRITICAL: logger.critical
    }.get(alert.severity, logger.warning)
    
    log_method(
        "alert_notification",
        rule_name=alert.rule_name,
        severity=alert.severity.value,
        message=alert.message,
        details=alert.details
    )


async def webhook_notification_handler(alert: Alert, webhook_url: str):
    """Send alerts to a webhook"""
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.post(
                webhook_url,
                json=alert.to_dict()
            )
            
            if response.status_code != 200:
                logger.error(
                    "webhook_notification_failed",
                    status_code=response.status_code,
                    response=response.text
                )
                
    except Exception as e:
        logger.error(
            "webhook_notification_error",
            error=str(e),
            webhook_url=webhook_url
        )