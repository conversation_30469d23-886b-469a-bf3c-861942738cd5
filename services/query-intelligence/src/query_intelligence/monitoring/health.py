"""
Health Monitoring and Service Health Checks

Implements comprehensive health monitoring for the Query Intelligence service
including dependency checks, resource monitoring, and health status reporting.
"""

import asyncio
import time
import psutil
import os
from typing import Dict, Any, Optional, List, Callable, Tuple
from datetime import datetime, timedelta
from enum import Enum
from dataclasses import dataclass, field
import json
import httpx
import redis.asyncio as redis
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession
import structlog

from ..config import get_settings
from ..database import get_session
from .metrics import MetricsCollector

logger = structlog.get_logger()


class HealthStatus(Enum):
    """Health status levels"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    
    def __lt__(self, other):
        """Enable comparison for determining worst status"""
        order = {
            HealthStatus.HEALTHY: 0,
            HealthStatus.DEGRADED: 1,
            HealthStatus.UNHEALTHY: 2
        }
        return order[self] < order[other]


@dataclass
class ComponentHealth:
    """Health status of a single component"""
    name: str
    status: HealthStatus
    message: str = ""
    details: Dict[str, Any] = field(default_factory=dict)
    last_check: datetime = field(default_factory=datetime.now)
    response_time_ms: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            "name": self.name,
            "status": self.status.value,
            "message": self.message,
            "details": self.details,
            "last_check": self.last_check.isoformat(),
            "response_time_ms": self.response_time_ms
        }


@dataclass
class ServiceHealth:
    """Overall service health status"""
    status: HealthStatus
    version: str
    uptime_seconds: float
    components: Dict[str, ComponentHealth]
    metrics: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            "status": self.status.value,
            "version": self.version,
            "uptime_seconds": self.uptime_seconds,
            "components": {
                name: comp.to_dict() 
                for name, comp in self.components.items()
            },
            "metrics": self.metrics,
            "timestamp": datetime.now().isoformat()
        }


class HealthMonitor:
    """Monitors service health and dependencies"""
    
    def __init__(self, 
                 metrics_collector: Optional[MetricsCollector] = None,
                 check_interval: int = 30):
        self.settings = get_settings()
        self.metrics = metrics_collector
        self.check_interval = check_interval
        self.start_time = time.time()
        
        # Component health checks
        self.health_checks: Dict[str, Callable] = {
            "database": self._check_database,
            "redis": self._check_redis,
            "analysis_engine": self._check_analysis_engine,
            "pattern_mining": self._check_pattern_mining,
            "memory": self._check_memory,
            "disk": self._check_disk,
            "cpu": self._check_cpu
        }
        
        # Health check results cache
        self.health_cache: Dict[str, ComponentHealth] = {}
        self.last_full_check = datetime.now()
        
        # Background health check task
        self.monitor_task: Optional[asyncio.Task] = None
        
        # Thresholds
        self.memory_threshold_percent = 85
        self.disk_threshold_percent = 90
        self.cpu_threshold_percent = 80
        
        logger.info("health_monitor_initialized")
        
    async def start(self):
        """Start background health monitoring"""
        if not self.monitor_task:
            self.monitor_task = asyncio.create_task(self._monitor_loop())
            logger.info("health_monitoring_started")
            
    async def stop(self):
        """Stop health monitoring"""
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
            logger.info("health_monitoring_stopped")
            
    async def _monitor_loop(self):
        """Background monitoring loop"""
        while True:
            try:
                await self.check_all_components()
                await asyncio.sleep(self.check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error("health_monitor_error", error=str(e))
                await asyncio.sleep(self.check_interval)
                
    async def check_health(self, 
                          detailed: bool = False,
                          force_check: bool = False) -> ServiceHealth:
        """Check overall service health"""
        # Check if we need to refresh the cache
        cache_age = (datetime.now() - self.last_full_check).total_seconds()
        
        if force_check or cache_age > self.check_interval:
            await self.check_all_components()
            
        # Determine overall status
        overall_status = HealthStatus.HEALTHY
        for component in self.health_cache.values():
            if component.status > overall_status:
                overall_status = component.status
                
        # Gather metrics if requested
        metrics = {}
        if detailed and self.metrics:
            metrics = self.metrics.get_metrics_summary()
            
        # Calculate uptime
        uptime = time.time() - self.start_time
        
        return ServiceHealth(
            status=overall_status,
            version=self.settings.VERSION,
            uptime_seconds=uptime,
            components=self.health_cache.copy(),
            metrics=metrics
        )
        
    async def check_all_components(self):
        """Check all components health"""
        tasks = []
        
        for name, check_func in self.health_checks.items():
            tasks.append(self._check_component(name, check_func))
            
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Update cache
        for result in results:
            if isinstance(result, ComponentHealth):
                self.health_cache[result.name] = result
            else:
                logger.error("component_check_failed", error=str(result))
                
        self.last_full_check = datetime.now()
        
        # Update metrics if available
        if self.metrics:
            healthy_count = sum(1 for c in self.health_cache.values() 
                              if c.status == HealthStatus.HEALTHY)
            total_count = len(self.health_cache)
            
            self.metrics.availability.set(
                (healthy_count / total_count * 100) if total_count > 0 else 0
            )
            self.metrics.uptime_seconds.set(time.time() - self.start_time)
            
    async def _check_component(self, 
                              name: str, 
                              check_func: Callable) -> ComponentHealth:
        """Check a single component"""
        start_time = time.time()
        
        try:
            health = await check_func()
            health.response_time_ms = (time.time() - start_time) * 1000
            return health
        except Exception as e:
            logger.error(f"health_check_failed_{name}", error=str(e))
            return ComponentHealth(
                name=name,
                status=HealthStatus.UNHEALTHY,
                message=f"Health check failed: {str(e)}",
                response_time_ms=(time.time() - start_time) * 1000
            )
            
    async def _check_database(self) -> ComponentHealth:
        """Check database connectivity and performance"""
        try:
            async with get_session() as session:
                # Execute simple query
                result = await session.execute(text("SELECT 1"))
                result.scalar()
                
                # Check connection pool stats if available
                pool_stats = {}
                if hasattr(session.bind, 'pool'):
                    pool = session.bind.pool
                    pool_stats = {
                        "size": pool.size(),
                        "checked_in": pool.checkedin(),
                        "overflow": pool.overflow(),
                        "total": pool.total()
                    }
                    
            return ComponentHealth(
                name="database",
                status=HealthStatus.HEALTHY,
                message="Database connection successful",
                details={"pool_stats": pool_stats}
            )
            
        except Exception as e:
            return ComponentHealth(
                name="database",
                status=HealthStatus.UNHEALTHY,
                message=f"Database connection failed: {str(e)}"
            )
            
    async def _check_redis(self) -> ComponentHealth:
        """Check Redis connectivity"""
        if not self.settings.REDIS_URL:
            return ComponentHealth(
                name="redis",
                status=HealthStatus.HEALTHY,
                message="Redis not configured"
            )
            
        try:
            client = redis.from_url(self.settings.REDIS_URL)
            
            # Ping Redis
            await client.ping()
            
            # Get memory info
            info = await client.info("memory")
            memory_used_mb = info.get("used_memory", 0) / 1024 / 1024
            
            await client.close()
            
            return ComponentHealth(
                name="redis",
                status=HealthStatus.HEALTHY,
                message="Redis connection successful",
                details={
                    "memory_used_mb": round(memory_used_mb, 2)
                }
            )
            
        except Exception as e:
            return ComponentHealth(
                name="redis",
                status=HealthStatus.DEGRADED,
                message=f"Redis connection failed: {str(e)}"
            )
            
    async def _check_analysis_engine(self) -> ComponentHealth:
        """Check Analysis Engine service health"""
        try:
            async with httpx.AsyncClient(timeout=5.0) as client:
                response = await client.get(
                    f"{self.settings.ANALYSIS_ENGINE_URL}/health"
                )
                
                if response.status_code == 200:
                    data = response.json()
                    status = HealthStatus.HEALTHY if data.get("status") == "healthy" else HealthStatus.DEGRADED
                    
                    return ComponentHealth(
                        name="analysis_engine",
                        status=status,
                        message="Analysis Engine responsive",
                        details=data
                    )
                else:
                    return ComponentHealth(
                        name="analysis_engine",
                        status=HealthStatus.DEGRADED,
                        message=f"Analysis Engine returned {response.status_code}"
                    )
                    
        except Exception as e:
            return ComponentHealth(
                name="analysis_engine",
                status=HealthStatus.UNHEALTHY,
                message=f"Analysis Engine unreachable: {str(e)}"
            )
            
    async def _check_pattern_mining(self) -> ComponentHealth:
        """Check Pattern Mining service health"""
        try:
            async with httpx.AsyncClient(timeout=5.0) as client:
                response = await client.get(
                    f"{self.settings.PATTERN_MINING_URL}/health"
                )
                
                if response.status_code == 200:
                    data = response.json()
                    status = HealthStatus.HEALTHY if data.get("status") == "healthy" else HealthStatus.DEGRADED
                    
                    return ComponentHealth(
                        name="pattern_mining",
                        status=status,
                        message="Pattern Mining responsive",
                        details=data
                    )
                else:
                    return ComponentHealth(
                        name="pattern_mining",
                        status=HealthStatus.DEGRADED,
                        message=f"Pattern Mining returned {response.status_code}"
                    )
                    
        except Exception as e:
            return ComponentHealth(
                name="pattern_mining",
                status=HealthStatus.DEGRADED,  # Degraded not unhealthy as it's optional
                message=f"Pattern Mining unreachable: {str(e)}"
            )
            
    async def _check_memory(self) -> ComponentHealth:
        """Check memory usage"""
        try:
            memory = psutil.virtual_memory()
            process = psutil.Process(os.getpid())
            process_memory = process.memory_info()
            
            # Check if memory usage is high
            if memory.percent > self.memory_threshold_percent:
                status = HealthStatus.UNHEALTHY
                message = f"High memory usage: {memory.percent}%"
            elif memory.percent > self.memory_threshold_percent - 10:
                status = HealthStatus.DEGRADED
                message = f"Elevated memory usage: {memory.percent}%"
            else:
                status = HealthStatus.HEALTHY
                message = "Memory usage normal"
                
            return ComponentHealth(
                name="memory",
                status=status,
                message=message,
                details={
                    "system_percent": memory.percent,
                    "system_available_mb": memory.available / 1024 / 1024,
                    "process_rss_mb": process_memory.rss / 1024 / 1024,
                    "process_vms_mb": process_memory.vms / 1024 / 1024
                }
            )
            
        except Exception as e:
            return ComponentHealth(
                name="memory",
                status=HealthStatus.UNHEALTHY,
                message=f"Memory check failed: {str(e)}"
            )
            
    async def _check_disk(self) -> ComponentHealth:
        """Check disk usage"""
        try:
            disk = psutil.disk_usage('/')
            
            # Check if disk usage is high
            if disk.percent > self.disk_threshold_percent:
                status = HealthStatus.UNHEALTHY
                message = f"High disk usage: {disk.percent}%"
            elif disk.percent > self.disk_threshold_percent - 10:
                status = HealthStatus.DEGRADED
                message = f"Elevated disk usage: {disk.percent}%"
            else:
                status = HealthStatus.HEALTHY
                message = "Disk usage normal"
                
            return ComponentHealth(
                name="disk",
                status=status,
                message=message,
                details={
                    "percent_used": disk.percent,
                    "free_gb": disk.free / 1024 / 1024 / 1024,
                    "total_gb": disk.total / 1024 / 1024 / 1024
                }
            )
            
        except Exception as e:
            return ComponentHealth(
                name="disk",
                status=HealthStatus.UNHEALTHY,
                message=f"Disk check failed: {str(e)}"
            )
            
    async def _check_cpu(self) -> ComponentHealth:
        """Check CPU usage"""
        try:
            # Get CPU usage over 1 second interval
            cpu_percent = psutil.cpu_percent(interval=1)
            process = psutil.Process(os.getpid())
            process_cpu = process.cpu_percent(interval=0.1)
            
            # Check if CPU usage is high
            if cpu_percent > self.cpu_threshold_percent:
                status = HealthStatus.DEGRADED
                message = f"High CPU usage: {cpu_percent}%"
            else:
                status = HealthStatus.HEALTHY
                message = "CPU usage normal"
                
            return ComponentHealth(
                name="cpu",
                status=status,
                message=message,
                details={
                    "system_percent": cpu_percent,
                    "process_percent": process_cpu,
                    "core_count": psutil.cpu_count()
                }
            )
            
        except Exception as e:
            return ComponentHealth(
                name="cpu",
                status=HealthStatus.UNHEALTHY,
                message=f"CPU check failed: {str(e)}"
            )
            
    def add_custom_check(self, 
                        name: str, 
                        check_func: Callable[[], ComponentHealth]):
        """Add a custom health check"""
        self.health_checks[name] = check_func
        logger.info("custom_health_check_added", name=name)
        
    def set_threshold(self, 
                     threshold_type: str, 
                     value: float):
        """Update health check thresholds"""
        if threshold_type == "memory":
            self.memory_threshold_percent = value
        elif threshold_type == "disk":
            self.disk_threshold_percent = value
        elif threshold_type == "cpu":
            self.cpu_threshold_percent = value
        else:
            raise ValueError(f"Unknown threshold type: {threshold_type}")
            
        logger.info("health_threshold_updated", 
                   threshold_type=threshold_type, 
                   value=value)