"""
Metrics Collection and Management

Implements comprehensive metrics collection using Prometheus client library
for enterprise-grade monitoring and observability.
"""

import time
from typing import Dict, Any, Optional, List, Callable
from datetime import datetime, timedelta
from functools import wraps
import asyncio
from contextlib import contextmanager
from dataclasses import dataclass, field
from enum import Enum

from prometheus_client import (
    Counter, Histogram, Gauge, Summary, Info,
    CollectorRegistry, generate_latest,
    CONTENT_TYPE_LATEST, REGISTRY
)
import structlog

from ..config import get_settings

logger = structlog.get_logger()


class MetricType(Enum):
    """Types of metrics collected"""
    COUNTER = "counter"
    HISTOGRAM = "histogram"
    GAUGE = "gauge"
    SUMMARY = "summary"
    INFO = "info"


@dataclass
class QueryMetrics:
    """Query-specific metrics"""
    query_id: str
    start_time: float
    end_time: Optional[float] = None
    repository_id: Optional[str] = None
    query_length: int = 0
    response_length: int = 0
    confidence_score: float = 0.0
    cache_hit: bool = False
    reference_count: int = 0
    error: Optional[str] = None
    status_code: int = 200
    
    @property
    def duration_ms(self) -> float:
        """Calculate duration in milliseconds"""
        if self.end_time:
            return (self.end_time - self.start_time) * 1000
        return 0.0
        
    @property
    def success(self) -> bool:
        """Check if query was successful"""
        return self.error is None and 200 <= self.status_code < 300


@dataclass
class PerformanceMetrics:
    """Performance tracking metrics"""
    cpu_percent: float = 0.0
    memory_mb: float = 0.0
    active_connections: int = 0
    queue_size: int = 0
    cache_size_mb: float = 0.0
    goroutines: int = 0
    
    
class MetricsCollector:
    """Central metrics collection and management"""
    
    def __init__(self, 
                 registry: Optional[CollectorRegistry] = None,
                 prefix: str = "query_intelligence"):
        self.registry = registry or REGISTRY
        self.prefix = prefix
        self.settings = get_settings()
        
        # Initialize metrics
        self._init_query_metrics()
        self._init_performance_metrics()
        self._init_cache_metrics()
        self._init_error_metrics()
        self._init_business_metrics()
        self._init_sla_metrics()
        
        # Custom metrics storage
        self.custom_metrics: Dict[str, Any] = {}
        
        # Metric aggregators
        self.aggregators: Dict[str, Callable] = {}
        
        logger.info("metrics_collector_initialized", prefix=prefix)
        
    def _init_query_metrics(self):
        """Initialize query-related metrics"""
        # Request metrics
        self.query_total = Counter(
            f"{self.prefix}_queries_total",
            "Total number of queries processed",
            ["repository", "status", "cache_hit"],
            registry=self.registry
        )
        
        self.query_duration = Histogram(
            f"{self.prefix}_query_duration_seconds",
            "Query processing duration in seconds",
            ["repository", "cache_hit"],
            buckets=(0.005, 0.01, 0.025, 0.05, 0.075, 0.1, 0.25, 0.5, 0.75, 1.0, 2.5, 5.0),
            registry=self.registry
        )
        
        self.query_response_size = Histogram(
            f"{self.prefix}_response_size_bytes",
            "Size of query responses in bytes",
            ["repository"],
            buckets=(100, 500, 1000, 5000, 10000, 50000, 100000, 500000),
            registry=self.registry
        )
        
        self.query_confidence = Histogram(
            f"{self.prefix}_confidence_score",
            "Confidence scores of query responses",
            ["repository"],
            buckets=(0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 0.95, 0.99),
            registry=self.registry
        )
        
        self.active_queries = Gauge(
            f"{self.prefix}_active_queries",
            "Number of queries currently being processed",
            registry=self.registry
        )
        
        self.query_queue_size = Gauge(
            f"{self.prefix}_query_queue_size",
            "Number of queries waiting in queue",
            registry=self.registry
        )
        
    def _init_performance_metrics(self):
        """Initialize performance metrics"""
        self.cpu_usage = Gauge(
            f"{self.prefix}_cpu_usage_percent",
            "CPU usage percentage",
            registry=self.registry
        )
        
        self.memory_usage = Gauge(
            f"{self.prefix}_memory_usage_mb",
            "Memory usage in megabytes",
            registry=self.registry
        )
        
        self.goroutines = Gauge(
            f"{self.prefix}_goroutines_count",
            "Number of active goroutines/tasks",
            registry=self.registry
        )
        
        self.response_time_summary = Summary(
            f"{self.prefix}_response_time_ms",
            "Response time summary in milliseconds",
            ["endpoint"],
            registry=self.registry
        )
        
        self.throughput = Gauge(
            f"{self.prefix}_throughput_qps",
            "Current throughput in queries per second",
            registry=self.registry
        )
        
    def _init_cache_metrics(self):
        """Initialize cache-related metrics"""
        self.cache_hits = Counter(
            f"{self.prefix}_cache_hits_total",
            "Total number of cache hits",
            ["cache_type"],
            registry=self.registry
        )
        
        self.cache_misses = Counter(
            f"{self.prefix}_cache_misses_total",
            "Total number of cache misses",
            ["cache_type"],
            registry=self.registry
        )
        
        self.cache_evictions = Counter(
            f"{self.prefix}_cache_evictions_total",
            "Total number of cache evictions",
            ["cache_type", "reason"],
            registry=self.registry
        )
        
        self.cache_size = Gauge(
            f"{self.prefix}_cache_size_entries",
            "Number of entries in cache",
            ["cache_type"],
            registry=self.registry
        )
        
        self.cache_memory = Gauge(
            f"{self.prefix}_cache_memory_mb",
            "Memory used by cache in megabytes",
            ["cache_type"],
            registry=self.registry
        )
        
    def _init_error_metrics(self):
        """Initialize error and failure metrics"""
        self.errors_total = Counter(
            f"{self.prefix}_errors_total",
            "Total number of errors",
            ["error_type", "component"],
            registry=self.registry
        )
        
        self.timeouts_total = Counter(
            f"{self.prefix}_timeouts_total",
            "Total number of timeouts",
            ["operation"],
            registry=self.registry
        )
        
        self.circuit_breaker_state = Gauge(
            f"{self.prefix}_circuit_breaker_state",
            "Circuit breaker state (0=closed, 1=open, 2=half-open)",
            ["service"],
            registry=self.registry
        )
        
        self.retries_total = Counter(
            f"{self.prefix}_retries_total",
            "Total number of retries",
            ["operation", "reason"],
            registry=self.registry
        )
        
    def _init_business_metrics(self):
        """Initialize business-level metrics"""
        self.repositories_queried = Gauge(
            f"{self.prefix}_repositories_queried_total",
            "Total unique repositories queried",
            registry=self.registry
        )
        
        self.users_active = Gauge(
            f"{self.prefix}_users_active",
            "Number of active users in the last hour",
            registry=self.registry
        )
        
        self.query_patterns = Counter(
            f"{self.prefix}_query_patterns_total",
            "Query patterns detected",
            ["pattern_type"],
            registry=self.registry
        )
        
        self.api_usage = Counter(
            f"{self.prefix}_api_usage_total",
            "API usage by endpoint and method",
            ["endpoint", "method", "status"],
            registry=self.registry
        )
        
    def _init_sla_metrics(self):
        """Initialize SLA tracking metrics"""
        self.sla_violations = Counter(
            f"{self.prefix}_sla_violations_total",
            "Total SLA violations",
            ["sla_type", "severity"],
            registry=self.registry
        )
        
        self.availability = Gauge(
            f"{self.prefix}_availability_percent",
            "Service availability percentage",
            registry=self.registry
        )
        
        self.uptime_seconds = Gauge(
            f"{self.prefix}_uptime_seconds",
            "Service uptime in seconds",
            registry=self.registry
        )
        
    def record_query(self, metrics: QueryMetrics):
        """Record query metrics"""
        try:
            # Update counters
            self.query_total.labels(
                repository=metrics.repository_id or "unknown",
                status="success" if metrics.success else "error",
                cache_hit=str(metrics.cache_hit)
            ).inc()
            
            # Record duration
            if metrics.duration_ms > 0:
                self.query_duration.labels(
                    repository=metrics.repository_id or "unknown",
                    cache_hit=str(metrics.cache_hit)
                ).observe(metrics.duration_ms / 1000)
                
            # Record response size
            if metrics.response_length > 0:
                self.query_response_size.labels(
                    repository=metrics.repository_id or "unknown"
                ).observe(metrics.response_length)
                
            # Record confidence
            if metrics.confidence_score > 0:
                self.query_confidence.labels(
                    repository=metrics.repository_id or "unknown"
                ).observe(metrics.confidence_score)
                
            # Update cache metrics
            if metrics.cache_hit:
                self.cache_hits.labels(cache_type="query").inc()
            else:
                self.cache_misses.labels(cache_type="query").inc()
                
            # Log for debugging
            logger.debug(
                "query_metrics_recorded",
                query_id=metrics.query_id,
                duration_ms=metrics.duration_ms,
                cache_hit=metrics.cache_hit
            )
            
        except Exception as e:
            logger.error("failed_to_record_query_metrics", error=str(e))
            
    def record_performance(self, metrics: PerformanceMetrics):
        """Record performance metrics"""
        try:
            self.cpu_usage.set(metrics.cpu_percent)
            self.memory_usage.set(metrics.memory_mb)
            self.goroutines.set(metrics.goroutines)
            
            if metrics.cache_size_mb > 0:
                self.cache_memory.labels(cache_type="total").set(metrics.cache_size_mb)
                
            logger.debug(
                "performance_metrics_recorded",
                cpu_percent=metrics.cpu_percent,
                memory_mb=metrics.memory_mb
            )
            
        except Exception as e:
            logger.error("failed_to_record_performance_metrics", error=str(e))
            
    def record_error(self, error_type: str, component: str = "unknown"):
        """Record error occurrence"""
        self.errors_total.labels(error_type=error_type, component=component).inc()
        
    def record_timeout(self, operation: str):
        """Record timeout occurrence"""
        self.timeouts_total.labels(operation=operation).inc()
        
    def record_retry(self, operation: str, reason: str):
        """Record retry attempt"""
        self.retries_total.labels(operation=operation, reason=reason).inc()
        
    def record_api_call(self, endpoint: str, method: str, status: int):
        """Record API call"""
        self.api_usage.labels(
            endpoint=endpoint,
            method=method,
            status=str(status)
        ).inc()
        
    def record_sla_violation(self, sla_type: str, severity: str):
        """Record SLA violation"""
        self.sla_violations.labels(sla_type=sla_type, severity=severity).inc()
        
    def update_cache_size(self, cache_type: str, size: int):
        """Update cache size metric"""
        self.cache_size.labels(cache_type=cache_type).set(size)
        
    def update_circuit_breaker(self, service: str, state: int):
        """Update circuit breaker state"""
        self.circuit_breaker_state.labels(service=service).set(state)
        
    def update_active_queries(self, count: int):
        """Update active queries count"""
        self.active_queries.set(count)
        
    def update_queue_size(self, size: int):
        """Update query queue size"""
        self.query_queue_size.set(size)
        
    def update_throughput(self, qps: float):
        """Update current throughput"""
        self.throughput.set(qps)
        
    @contextmanager
    def track_query(self, repository_id: Optional[str] = None) -> QueryMetrics:
        """Context manager to track query metrics"""
        metrics = QueryMetrics(
            query_id=f"q_{int(time.time() * 1000000)}",
            start_time=time.time(),
            repository_id=repository_id
        )
        
        self.active_queries.inc()
        
        try:
            yield metrics
        finally:
            metrics.end_time = time.time()
            self.active_queries.dec()
            self.record_query(metrics)
            
    def time_operation(self, operation: str):
        """Decorator to time operations"""
        def decorator(func):
            if asyncio.iscoroutinefunction(func):
                @wraps(func)
                async def async_wrapper(*args, **kwargs):
                    start_time = time.time()
                    try:
                        result = await func(*args, **kwargs)
                        duration = (time.time() - start_time) * 1000
                        self.response_time_summary.labels(endpoint=operation).observe(duration)
                        return result
                    except Exception as e:
                        self.record_error(type(e).__name__, operation)
                        raise
                return async_wrapper
            else:
                @wraps(func)
                def sync_wrapper(*args, **kwargs):
                    start_time = time.time()
                    try:
                        result = func(*args, **kwargs)
                        duration = (time.time() - start_time) * 1000
                        self.response_time_summary.labels(endpoint=operation).observe(duration)
                        return result
                    except Exception as e:
                        self.record_error(type(e).__name__, operation)
                        raise
                return sync_wrapper
        return decorator
        
    def get_metrics(self) -> bytes:
        """Get current metrics in Prometheus format"""
        return generate_latest(self.registry)
        
    def get_metrics_summary(self) -> Dict[str, Any]:
        """Get a summary of current metrics"""
        # This would collect and return a summary of key metrics
        # For now, returning a placeholder
        return {
            "queries_total": 0,  # Would fetch from actual metrics
            "error_rate": 0.0,
            "avg_response_time_ms": 0.0,
            "cache_hit_rate": 0.0,
            "active_queries": self.active_queries._value.get(),
            "cpu_usage_percent": self.cpu_usage._value.get(),
            "memory_usage_mb": self.memory_usage._value.get()
        }
        
    def register_custom_metric(self, 
                             name: str, 
                             metric_type: MetricType,
                             description: str,
                             labels: Optional[List[str]] = None):
        """Register a custom metric"""
        full_name = f"{self.prefix}_{name}"
        
        if metric_type == MetricType.COUNTER:
            metric = Counter(full_name, description, labels or [], registry=self.registry)
        elif metric_type == MetricType.HISTOGRAM:
            metric = Histogram(full_name, description, labels or [], registry=self.registry)
        elif metric_type == MetricType.GAUGE:
            metric = Gauge(full_name, description, labels or [], registry=self.registry)
        elif metric_type == MetricType.SUMMARY:
            metric = Summary(full_name, description, labels or [], registry=self.registry)
        elif metric_type == MetricType.INFO:
            metric = Info(full_name, description, labels or [], registry=self.registry)
        else:
            raise ValueError(f"Unknown metric type: {metric_type}")
            
        self.custom_metrics[name] = metric
        return metric
        
    def get_custom_metric(self, name: str):
        """Get a custom metric by name"""
        return self.custom_metrics.get(name)
        
    def reset_metrics(self):
        """Reset all metrics (useful for testing)"""
        # Note: This is primarily for testing and should not be used in production
        logger.warning("resetting_all_metrics")
        # In practice, you would iterate through metrics and reset them
        # For now, this is a placeholder
        pass