"""
Distributed Tracing Implementation

Implements OpenTelemetry-based distributed tracing for comprehensive
request tracking across service boundaries.
"""

import time
import asyncio
from typing import Dict, Any, Optional, Callable, TypeVar, Union
from functools import wraps
from contextlib import contextmanager, asynccontextmanager
from contextvars import ContextVar
import json
import traceback

from opentelemetry import trace
from opentelemetry.trace import (
    Tracer, Span, Status, StatusCode,
    get_tracer, set_span_in_context
)
from opentelemetry.trace.propagation.tracecontext import TraceContextTextMapPropagator
from opentelemetry.exporter.otlp.proto.grpc.trace_exporter import OTLPSpanExporter
from opentelemetry.sdk.trace import TracerProvider, SpanProcessor
from opentelemetry.sdk.trace.export import BatchSpanProcessor, ConsoleSpanExporter
from opentelemetry.sdk.resources import Resource
from opentelemetry.instrumentation.fastapi import FastAPIInstrumentor
from opentelemetry.instrumentation.httpx import HTTPXClientInstrumentor
from opentelemetry.instrumentation.redis import RedisInstrumentor
from opentelemetry.instrumentation.sqlalchemy import SQLAlchemyInstrumentor
from opentelemetry.baggage import get_baggage, set_baggage
import structlog

from ..config import get_settings

logger = structlog.get_logger()

# Type variables for decorators
F = TypeVar('F', bound=Callable[..., Any])

# Context variable for current span
current_span_context: ContextVar[Optional[Span]] = ContextVar('current_span', default=None)


class TracingManager:
    """Manages distributed tracing configuration and operations"""
    
    def __init__(self, service_name: str = "query-intelligence"):
        self.service_name = service_name
        self.settings = get_settings()
        self.tracer_provider: Optional[TracerProvider] = None
        self.tracer: Optional[Tracer] = None
        self.propagator = TraceContextTextMapPropagator()
        
        # Span processors
        self.span_processors: list[SpanProcessor] = []
        
        # Initialize if enabled
        if self.settings.ENABLE_TRACING:
            self._initialize_tracing()
            
    def _initialize_tracing(self):
        """Initialize OpenTelemetry tracing"""
        try:
            # Create resource with service information
            resource = Resource.create({
                "service.name": self.service_name,
                "service.version": self.settings.VERSION,
                "deployment.environment": self.settings.ENVIRONMENT,
                "service.namespace": "ccl-platform",
                "service.instance.id": self.settings.INSTANCE_ID,
            })
            
            # Create tracer provider
            self.tracer_provider = TracerProvider(resource=resource)
            
            # Add OTLP exporter if configured
            if self.settings.OTLP_ENDPOINT:
                otlp_exporter = OTLPSpanExporter(
                    endpoint=self.settings.OTLP_ENDPOINT,
                    insecure=self.settings.OTLP_INSECURE
                )
                self.span_processors.append(
                    BatchSpanProcessor(otlp_exporter)
                )
                
            # Add console exporter in development
            if self.settings.ENVIRONMENT == "development":
                self.span_processors.append(
                    BatchSpanProcessor(ConsoleSpanExporter())
                )
                
            # Add processors to provider
            for processor in self.span_processors:
                self.tracer_provider.add_span_processor(processor)
                
            # Set as global tracer provider
            trace.set_tracer_provider(self.tracer_provider)
            
            # Get tracer
            self.tracer = get_tracer(
                self.service_name,
                self.settings.VERSION
            )
            
            # Auto-instrument libraries
            self._setup_auto_instrumentation()
            
            logger.info(
                "tracing_initialized",
                service_name=self.service_name,
                otlp_endpoint=self.settings.OTLP_ENDPOINT
            )
            
        except Exception as e:
            logger.error("failed_to_initialize_tracing", error=str(e))
            
    def _setup_auto_instrumentation(self):
        """Setup automatic instrumentation for common libraries"""
        try:
            # Instrument FastAPI
            FastAPIInstrumentor.instrument(
                tracer_provider=self.tracer_provider,
                excluded_urls="/health,/metrics"
            )
            
            # Instrument HTTP client
            HTTPXClientInstrumentor.instrument(
                tracer_provider=self.tracer_provider
            )
            
            # Instrument Redis if used
            RedisInstrumentor.instrument(
                tracer_provider=self.tracer_provider
            )
            
            logger.debug("auto_instrumentation_setup_complete")
            
        except Exception as e:
            logger.warning("auto_instrumentation_setup_failed", error=str(e))
            
    def create_span(self, 
                    name: str, 
                    kind: trace.SpanKind = trace.SpanKind.INTERNAL,
                    attributes: Optional[Dict[str, Any]] = None) -> Span:
        """Create a new span"""
        if not self.tracer:
            # Return a no-op span if tracing is disabled
            return trace.get_tracer(__name__).start_span(name)
            
        span = self.tracer.start_span(
            name,
            kind=kind,
            attributes=attributes or {}
        )
        
        # Set common attributes
        span.set_attribute("service.name", self.service_name)
        span.set_attribute("span.type", kind.name)
        
        return span
        
    @contextmanager
    def span(self, 
             name: str,
             kind: trace.SpanKind = trace.SpanKind.INTERNAL,
             attributes: Optional[Dict[str, Any]] = None,
             record_exception: bool = True):
        """Context manager for creating spans"""
        span = self.create_span(name, kind, attributes)
        
        # Store in context var
        token = current_span_context.set(span)
        
        try:
            with trace.use_span(span, end_on_exit=True):
                yield span
        except Exception as e:
            if record_exception:
                span.record_exception(e)
                span.set_status(Status(StatusCode.ERROR, str(e)))
            raise
        finally:
            current_span_context.reset(token)
            
    @asynccontextmanager
    async def async_span(self,
                        name: str,
                        kind: trace.SpanKind = trace.SpanKind.INTERNAL,
                        attributes: Optional[Dict[str, Any]] = None,
                        record_exception: bool = True):
        """Async context manager for creating spans"""
        span = self.create_span(name, kind, attributes)
        
        # Store in context var
        token = current_span_context.set(span)
        
        try:
            with trace.use_span(span, end_on_exit=True):
                yield span
        except Exception as e:
            if record_exception:
                span.record_exception(e)
                span.set_status(Status(StatusCode.ERROR, str(e)))
            raise
        finally:
            current_span_context.reset(token)
            
    def get_current_span(self) -> Optional[Span]:
        """Get the current active span"""
        return current_span_context.get() or trace.get_current_span()
        
    def add_event(self, name: str, attributes: Optional[Dict[str, Any]] = None):
        """Add an event to the current span"""
        span = self.get_current_span()
        if span and span.is_recording():
            span.add_event(name, attributes=attributes or {})
            
    def set_attribute(self, key: str, value: Any):
        """Set an attribute on the current span"""
        span = self.get_current_span()
        if span and span.is_recording():
            span.set_attribute(key, value)
            
    def set_attributes(self, attributes: Dict[str, Any]):
        """Set multiple attributes on the current span"""
        span = self.get_current_span()
        if span and span.is_recording():
            for key, value in attributes.items():
                span.set_attribute(key, value)
                
    def record_exception(self, exception: Exception, escaped: bool = True):
        """Record an exception in the current span"""
        span = self.get_current_span()
        if span and span.is_recording():
            span.record_exception(exception, escaped=escaped)
            span.set_status(Status(StatusCode.ERROR, str(exception)))
            
    def inject_context(self, carrier: Dict[str, Any]):
        """Inject trace context into carrier for propagation"""
        if self.propagator:
            self.propagator.inject(carrier)
            
    def extract_context(self, carrier: Dict[str, Any]):
        """Extract trace context from carrier"""
        if self.propagator:
            return self.propagator.extract(carrier)
        return None
        
    def set_baggage(self, key: str, value: str):
        """Set baggage item for cross-service context propagation"""
        set_baggage(key, value)
        
    def get_baggage(self, key: str) -> Optional[str]:
        """Get baggage item"""
        baggage = get_baggage()
        return baggage.get(key) if baggage else None
        
    def shutdown(self):
        """Shutdown tracing and flush remaining spans"""
        if self.tracer_provider:
            self.tracer_provider.shutdown()
            logger.info("tracing_shutdown_complete")


# Global tracing manager instance
_tracing_manager: Optional[TracingManager] = None


def get_tracing_manager() -> TracingManager:
    """Get or create the global tracing manager"""
    global _tracing_manager
    if _tracing_manager is None:
        _tracing_manager = TracingManager()
    return _tracing_manager


def create_span(name: str, 
                kind: trace.SpanKind = trace.SpanKind.INTERNAL,
                attributes: Optional[Dict[str, Any]] = None) -> Span:
    """Create a new span using the global tracing manager"""
    return get_tracing_manager().create_span(name, kind, attributes)


def trace_async(name: Optional[str] = None,
                kind: trace.SpanKind = trace.SpanKind.INTERNAL,
                attributes: Optional[Dict[str, Any]] = None,
                record_exception: bool = True):
    """Decorator for tracing async functions"""
    def decorator(func: F) -> F:
        span_name = name or f"{func.__module__}.{func.__name__}"
        
        @wraps(func)
        async def wrapper(*args, **kwargs):
            manager = get_tracing_manager()
            
            # Extract attributes from function arguments if needed
            span_attributes = attributes or {}
            
            async with manager.async_span(
                span_name, 
                kind, 
                span_attributes, 
                record_exception
            ) as span:
                # Add function-specific attributes
                span.set_attribute("function.name", func.__name__)
                span.set_attribute("function.module", func.__module__)
                
                # Execute function
                start_time = time.time()
                try:
                    result = await func(*args, **kwargs)
                    
                    # Record success
                    span.set_attribute("function.success", True)
                    span.set_attribute("function.duration_ms", 
                                     (time.time() - start_time) * 1000)
                    
                    return result
                    
                except Exception as e:
                    # Record failure
                    span.set_attribute("function.success", False)
                    span.set_attribute("function.error", str(e))
                    span.set_attribute("function.error_type", type(e).__name__)
                    raise
                    
        return wrapper
    return decorator


def trace_sync(name: Optional[str] = None,
               kind: trace.SpanKind = trace.SpanKind.INTERNAL,
               attributes: Optional[Dict[str, Any]] = None,
               record_exception: bool = True):
    """Decorator for tracing sync functions"""
    def decorator(func: F) -> F:
        span_name = name or f"{func.__module__}.{func.__name__}"
        
        @wraps(func)
        def wrapper(*args, **kwargs):
            manager = get_tracing_manager()
            
            # Extract attributes from function arguments if needed
            span_attributes = attributes or {}
            
            with manager.span(
                span_name, 
                kind, 
                span_attributes, 
                record_exception
            ) as span:
                # Add function-specific attributes
                span.set_attribute("function.name", func.__name__)
                span.set_attribute("function.module", func.__module__)
                
                # Execute function
                start_time = time.time()
                try:
                    result = func(*args, **kwargs)
                    
                    # Record success
                    span.set_attribute("function.success", True)
                    span.set_attribute("function.duration_ms", 
                                     (time.time() - start_time) * 1000)
                    
                    return result
                    
                except Exception as e:
                    # Record failure
                    span.set_attribute("function.success", False)
                    span.set_attribute("function.error", str(e))
                    span.set_attribute("function.error_type", type(e).__name__)
                    raise
                    
        return wrapper
    return decorator


# Convenience decorators
def trace_query(func: F) -> F:
    """Decorator specifically for tracing query operations"""
    return trace_async(
        name=f"query.{func.__name__}",
        kind=trace.SpanKind.SERVER,
        attributes={"operation.type": "query"}
    )(func)


def trace_cache(func: F) -> F:
    """Decorator specifically for tracing cache operations"""
    return trace_async(
        name=f"cache.{func.__name__}",
        kind=trace.SpanKind.INTERNAL,
        attributes={"operation.type": "cache"}
    )(func)


def trace_external_call(service: str):
    """Decorator for tracing external service calls"""
    def decorator(func: F) -> F:
        return trace_async(
            name=f"external.{service}.{func.__name__}",
            kind=trace.SpanKind.CLIENT,
            attributes={
                "operation.type": "external_call",
                "external.service": service
            }
        )(func)
    return decorator