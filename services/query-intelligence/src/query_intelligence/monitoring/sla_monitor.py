"""
SLA Monitoring and Compliance System

Tracks service level agreements and ensures compliance with
performance, availability, and quality objectives.
"""

import time
import asyncio
from typing import Optional, Dict, Any, List, Callable, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import statistics
import structlog

logger = structlog.get_logger()


class SLAMetric(Enum):
    """Types of SLA metrics"""
    AVAILABILITY = "availability"          # Uptime percentage
    RESPONSE_TIME = "response_time"        # Average response time
    ERROR_RATE = "error_rate"              # Error percentage
    THROUGHPUT = "throughput"              # Requests per second
    PERCENTILE_95 = "percentile_95"        # 95th percentile response time
    PERCENTILE_99 = "percentile_99"        # 99th percentile response time
    SUCCESS_RATE = "success_rate"          # Successful request percentage
    CACHE_HIT_RATE = "cache_hit_rate"      # Cache effectiveness


class SLAStatus(Enum):
    """SLA compliance status"""
    COMPLIANT = "compliant"
    WARNING = "warning"
    BREACH = "breach"
    CRITICAL = "critical"


@dataclass
class SLAObjective:
    """Service Level Agreement objective definition"""
    name: str
    metric: SLAMetric
    target: float                          # Target value
    warning_threshold: Optional[float] = None  # Warning if exceeded
    measurement_window: int = 300          # Measurement window in seconds
    aggregation: str = "average"           # average, min, max, percentile
    unit: str = "percent"                  # percent, ms, rps, etc.
    
    def __post_init__(self):
        # Set warning threshold if not specified
        if self.warning_threshold is None:
            if self.metric in [SLAMetric.AVAILABILITY, SLAMetric.SUCCESS_RATE, SLAMetric.CACHE_HIT_RATE]:
                # For metrics where higher is better
                self.warning_threshold = self.target * 0.95
            else:
                # For metrics where lower is better (response time, error rate)
                self.warning_threshold = self.target * 1.05


@dataclass
class SLAMeasurement:
    """Single SLA measurement"""
    timestamp: float
    value: float
    status: SLAStatus
    details: Dict[str, Any] = field(default_factory=dict)


@dataclass
class SLAReport:
    """SLA compliance report"""
    objective: SLAObjective
    period_start: datetime
    period_end: datetime
    measurements: List[SLAMeasurement]
    overall_status: SLAStatus
    compliance_percentage: float
    average_value: float
    min_value: float
    max_value: float
    breach_count: int
    warning_count: int
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert report to dictionary"""
        return {
            "objective": {
                "name": self.objective.name,
                "metric": self.objective.metric.value,
                "target": self.objective.target,
                "unit": self.objective.unit
            },
            "period": {
                "start": self.period_start.isoformat(),
                "end": self.period_end.isoformat(),
                "duration_hours": (self.period_end - self.period_start).total_seconds() / 3600
            },
            "compliance": {
                "status": self.overall_status.value,
                "percentage": round(self.compliance_percentage, 2),
                "breach_count": self.breach_count,
                "warning_count": self.warning_count
            },
            "statistics": {
                "average": round(self.average_value, 2),
                "min": round(self.min_value, 2),
                "max": round(self.max_value, 2),
                "measurement_count": len(self.measurements)
            }
        }


class SLAMonitor:
    """Monitors SLA compliance and generates reports"""
    
    def __init__(self):
        self.objectives: Dict[str, SLAObjective] = {}
        self.measurements: Dict[str, List[SLAMeasurement]] = {}
        self.current_values: Dict[str, float] = {}
        self.breach_callbacks: List[Callable[[str, SLAObjective, float], None]] = []
        self.warning_callbacks: List[Callable[[str, SLAObjective, float], None]] = []
        
        # Tracking for uptime calculation
        self.start_time = time.time()
        self.downtime_start: Optional[float] = None
        self.total_downtime = 0.0
        
        # Response time tracking
        self.response_times: List[float] = []
        self.response_time_window = 300  # 5 minutes
        
        # Error tracking
        self.total_requests = 0
        self.error_count = 0
        self.success_count = 0
        
        # Cache tracking
        self.cache_hits = 0
        self.cache_misses = 0
        
        logger.info("sla_monitor_initialized")
        
    def register_objective(self, objective: SLAObjective):
        """Register an SLA objective"""
        self.objectives[objective.name] = objective
        self.measurements[objective.name] = []
        
        logger.info(
            "sla_objective_registered",
            name=objective.name,
            metric=objective.metric.value,
            target=objective.target,
            unit=objective.unit
        )
        
    def register_breach_callback(self, callback: Callable[[str, SLAObjective, float], None]):
        """Register callback for SLA breaches"""
        self.breach_callbacks.append(callback)
        
    def register_warning_callback(self, callback: Callable[[str, SLAObjective, float], None]):
        """Register callback for SLA warnings"""
        self.warning_callbacks.append(callback)
        
    def record_request(self, 
                      duration_ms: float,
                      success: bool,
                      cache_hit: bool = False):
        """Record a request for SLA tracking"""
        self.total_requests += 1
        
        if success:
            self.success_count += 1
        else:
            self.error_count += 1
            
        if cache_hit:
            self.cache_hits += 1
        else:
            self.cache_misses += 1
            
        # Add to response times
        self.response_times.append(duration_ms)
        
        # Clean old response times (keep last window)
        cutoff_time = time.time() - self.response_time_window
        self.response_times = [
            rt for rt in self.response_times[-1000:]  # Keep last 1000 for memory
        ]
        
    def mark_service_down(self):
        """Mark service as down"""
        if self.downtime_start is None:
            self.downtime_start = time.time()
            logger.warning("service_marked_down")
            
    def mark_service_up(self):
        """Mark service as up"""
        if self.downtime_start is not None:
            downtime_duration = time.time() - self.downtime_start
            self.total_downtime += downtime_duration
            self.downtime_start = None
            
            logger.info(
                "service_marked_up",
                downtime_duration_seconds=downtime_duration
            )
            
    async def check_compliance(self):
        """Check all SLA objectives for compliance"""
        current_time = time.time()
        
        for name, objective in self.objectives.items():
            value = self._calculate_metric_value(objective)
            
            if value is None:
                continue
                
            # Determine status
            status = self._determine_status(objective, value)
            
            # Create measurement
            measurement = SLAMeasurement(
                timestamp=current_time,
                value=value,
                status=status,
                details=self._get_measurement_details(objective)
            )
            
            # Store measurement
            self.measurements[name].append(measurement)
            self.current_values[name] = value
            
            # Trigger callbacks if needed
            if status == SLAStatus.BREACH:
                await self._trigger_breach_callbacks(name, objective, value)
            elif status == SLAStatus.WARNING:
                await self._trigger_warning_callbacks(name, objective, value)
                
            # Log status
            logger.info(
                "sla_check",
                objective=name,
                value=value,
                target=objective.target,
                status=status.value,
                unit=objective.unit
            )
            
    def _calculate_metric_value(self, objective: SLAObjective) -> Optional[float]:
        """Calculate current value for a metric"""
        if objective.metric == SLAMetric.AVAILABILITY:
            return self._calculate_availability()
            
        elif objective.metric == SLAMetric.RESPONSE_TIME:
            return self._calculate_average_response_time()
            
        elif objective.metric == SLAMetric.ERROR_RATE:
            return self._calculate_error_rate()
            
        elif objective.metric == SLAMetric.THROUGHPUT:
            return self._calculate_throughput()
            
        elif objective.metric == SLAMetric.PERCENTILE_95:
            return self._calculate_percentile(95)
            
        elif objective.metric == SLAMetric.PERCENTILE_99:
            return self._calculate_percentile(99)
            
        elif objective.metric == SLAMetric.SUCCESS_RATE:
            return self._calculate_success_rate()
            
        elif objective.metric == SLAMetric.CACHE_HIT_RATE:
            return self._calculate_cache_hit_rate()
            
        return None
        
    def _calculate_availability(self) -> float:
        """Calculate availability percentage"""
        total_time = time.time() - self.start_time
        
        # Add current downtime if service is down
        current_downtime = self.total_downtime
        if self.downtime_start is not None:
            current_downtime += time.time() - self.downtime_start
            
        uptime = total_time - current_downtime
        availability = (uptime / total_time) * 100 if total_time > 0 else 100
        
        return availability
        
    def _calculate_average_response_time(self) -> Optional[float]:
        """Calculate average response time"""
        if not self.response_times:
            return None
            
        return statistics.mean(self.response_times)
        
    def _calculate_error_rate(self) -> float:
        """Calculate error rate percentage"""
        if self.total_requests == 0:
            return 0.0
            
        return (self.error_count / self.total_requests) * 100
        
    def _calculate_success_rate(self) -> float:
        """Calculate success rate percentage"""
        if self.total_requests == 0:
            return 100.0
            
        return (self.success_count / self.total_requests) * 100
        
    def _calculate_throughput(self) -> float:
        """Calculate requests per second"""
        elapsed_time = time.time() - self.start_time
        if elapsed_time == 0:
            return 0.0
            
        return self.total_requests / elapsed_time
        
    def _calculate_percentile(self, percentile: int) -> Optional[float]:
        """Calculate response time percentile"""
        if not self.response_times:
            return None
            
        sorted_times = sorted(self.response_times)
        index = int(len(sorted_times) * (percentile / 100))
        
        if index >= len(sorted_times):
            index = len(sorted_times) - 1
            
        return sorted_times[index]
        
    def _calculate_cache_hit_rate(self) -> float:
        """Calculate cache hit rate percentage"""
        total_cache_requests = self.cache_hits + self.cache_misses
        if total_cache_requests == 0:
            return 0.0
            
        return (self.cache_hits / total_cache_requests) * 100
        
    def _determine_status(self, objective: SLAObjective, value: float) -> SLAStatus:
        """Determine SLA status based on value and thresholds"""
        # For metrics where higher is better
        if objective.metric in [SLAMetric.AVAILABILITY, SLAMetric.SUCCESS_RATE, 
                              SLAMetric.CACHE_HIT_RATE, SLAMetric.THROUGHPUT]:
            if value >= objective.target:
                return SLAStatus.COMPLIANT
            elif value >= objective.warning_threshold:
                return SLAStatus.WARNING
            else:
                return SLAStatus.BREACH
                
        # For metrics where lower is better
        else:
            if value <= objective.target:
                return SLAStatus.COMPLIANT
            elif value <= objective.warning_threshold:
                return SLAStatus.WARNING
            else:
                return SLAStatus.BREACH
                
    def _get_measurement_details(self, objective: SLAObjective) -> Dict[str, Any]:
        """Get detailed measurement information"""
        details = {
            "total_requests": self.total_requests,
            "measurement_window": objective.measurement_window
        }
        
        if objective.metric in [SLAMetric.RESPONSE_TIME, SLAMetric.PERCENTILE_95, SLAMetric.PERCENTILE_99]:
            details["sample_count"] = len(self.response_times)
            
        elif objective.metric == SLAMetric.AVAILABILITY:
            details["total_downtime_seconds"] = self.total_downtime
            details["uptime_seconds"] = time.time() - self.start_time - self.total_downtime
            
        elif objective.metric in [SLAMetric.ERROR_RATE, SLAMetric.SUCCESS_RATE]:
            details["error_count"] = self.error_count
            details["success_count"] = self.success_count
            
        elif objective.metric == SLAMetric.CACHE_HIT_RATE:
            details["cache_hits"] = self.cache_hits
            details["cache_misses"] = self.cache_misses
            
        return details
        
    async def _trigger_breach_callbacks(self, name: str, objective: SLAObjective, value: float):
        """Trigger breach callbacks"""
        for callback in self.breach_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(name, objective, value)
                else:
                    callback(name, objective, value)
            except Exception as e:
                logger.error(
                    "breach_callback_error",
                    callback=callback.__name__,
                    error=str(e)
                )
                
    async def _trigger_warning_callbacks(self, name: str, objective: SLAObjective, value: float):
        """Trigger warning callbacks"""
        for callback in self.warning_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(name, objective, value)
                else:
                    callback(name, objective, value)
            except Exception as e:
                logger.error(
                    "warning_callback_error",
                    callback=callback.__name__,
                    error=str(e)
                )
                
    def generate_report(self,
                       objective_name: str,
                       start_time: Optional[datetime] = None,
                       end_time: Optional[datetime] = None) -> SLAReport:
        """Generate SLA compliance report"""
        if objective_name not in self.objectives:
            raise ValueError(f"Unknown objective: {objective_name}")
            
        objective = self.objectives[objective_name]
        measurements = self.measurements[objective_name]
        
        # Filter measurements by time range
        if start_time or end_time:
            filtered_measurements = []
            for m in measurements:
                m_time = datetime.fromtimestamp(m.timestamp)
                if start_time and m_time < start_time:
                    continue
                if end_time and m_time > end_time:
                    continue
                filtered_measurements.append(m)
            measurements = filtered_measurements
            
        if not measurements:
            # Return empty report
            return SLAReport(
                objective=objective,
                period_start=start_time or datetime.now(),
                period_end=end_time or datetime.now(),
                measurements=[],
                overall_status=SLAStatus.COMPLIANT,
                compliance_percentage=100.0,
                average_value=0.0,
                min_value=0.0,
                max_value=0.0,
                breach_count=0,
                warning_count=0
            )
            
        # Calculate statistics
        values = [m.value for m in measurements]
        breach_count = sum(1 for m in measurements if m.status == SLAStatus.BREACH)
        warning_count = sum(1 for m in measurements if m.status == SLAStatus.WARNING)
        compliant_count = len(measurements) - breach_count - warning_count
        
        compliance_percentage = (compliant_count / len(measurements)) * 100
        
        # Determine overall status
        if breach_count > 0:
            overall_status = SLAStatus.BREACH
        elif warning_count > len(measurements) * 0.1:  # More than 10% warnings
            overall_status = SLAStatus.WARNING
        else:
            overall_status = SLAStatus.COMPLIANT
            
        return SLAReport(
            objective=objective,
            period_start=start_time or datetime.fromtimestamp(measurements[0].timestamp),
            period_end=end_time or datetime.fromtimestamp(measurements[-1].timestamp),
            measurements=measurements,
            overall_status=overall_status,
            compliance_percentage=compliance_percentage,
            average_value=statistics.mean(values),
            min_value=min(values),
            max_value=max(values),
            breach_count=breach_count,
            warning_count=warning_count
        )
        
    def get_current_status(self) -> Dict[str, Dict[str, Any]]:
        """Get current status of all SLA objectives"""
        status = {}
        
        for name, objective in self.objectives.items():
            current_value = self.current_values.get(name)
            
            if current_value is not None:
                status[name] = {
                    "metric": objective.metric.value,
                    "current_value": round(current_value, 2),
                    "target": objective.target,
                    "status": self._determine_status(objective, current_value).value,
                    "unit": objective.unit
                }
                
        return status
        
    def reset_counters(self):
        """Reset counters (useful for testing or periodic resets)"""
        self.total_requests = 0
        self.error_count = 0
        self.success_count = 0
        self.cache_hits = 0
        self.cache_misses = 0
        self.response_times.clear()
        
        logger.info("sla_counters_reset")


# Predefined SLA objectives for common scenarios
def create_standard_slas() -> List[SLAObjective]:
    """Create standard SLA objectives"""
    return [
        # Availability SLAs
        SLAObjective(
            name="availability_99_9",
            metric=SLAMetric.AVAILABILITY,
            target=99.9,
            unit="percent"
        ),
        
        # Response time SLAs
        SLAObjective(
            name="avg_response_time",
            metric=SLAMetric.RESPONSE_TIME,
            target=200,
            unit="ms"
        ),
        
        SLAObjective(
            name="p95_response_time",
            metric=SLAMetric.PERCENTILE_95,
            target=500,
            unit="ms"
        ),
        
        SLAObjective(
            name="p99_response_time",
            metric=SLAMetric.PERCENTILE_99,
            target=1000,
            unit="ms"
        ),
        
        # Error rate SLA
        SLAObjective(
            name="error_rate",
            metric=SLAMetric.ERROR_RATE,
            target=1.0,
            unit="percent"
        ),
        
        # Success rate SLA
        SLAObjective(
            name="success_rate",
            metric=SLAMetric.SUCCESS_RATE,
            target=99.0,
            unit="percent"
        ),
        
        # Cache effectiveness
        SLAObjective(
            name="cache_hit_rate",
            metric=SLAMetric.CACHE_HIT_RATE,
            target=80.0,
            unit="percent"
        )
    ]


# Global SLA monitor instance
_sla_monitor: Optional[SLAMonitor] = None


def get_sla_monitor() -> SLAMonitor:
    """Get global SLA monitor instance"""
    global _sla_monitor
    if _sla_monitor is None:
        _sla_monitor = SLAMonitor()
        
        # Register standard SLAs
        for sla in create_standard_slas():
            _sla_monitor.register_objective(sla)
            
    return _sla_monitor