"""
Dashboard Configuration and Export

Provides dashboard templates and exporters for various monitoring platforms
including Grafana, Datadog, and custom dashboards.
"""

import json
from typing import Dict, Any, List, Optional
from datetime import datetime
from pathlib import Path
import yaml
from enum import Enum
from dataclasses import dataclass, field
import structlog

from ..config import get_settings

logger = structlog.get_logger()


class DashboardType(Enum):
    """Supported dashboard types"""
    GRAFANA = "grafana"
    DATADOG = "datadog"
    CLOUDWATCH = "cloudwatch"
    CUSTOM = "custom"


@dataclass
class MetricPanel:
    """Dashboard panel configuration"""
    title: str
    panel_type: str  # graph, stat, table, heatmap, etc.
    metrics: List[str]
    query: Optional[str] = None
    description: Optional[str] = None
    unit: Optional[str] = None
    thresholds: Optional[Dict[str, float]] = None
    width: int = 12
    height: int = 8
    position: Optional[Dict[str, int]] = None


@dataclass 
class MetricsDashboard:
    """Dashboard configuration"""
    name: str
    title: str
    description: str
    panels: List[MetricPanel]
    variables: Dict[str, Any] = field(default_factory=dict)
    time_range: str = "6h"
    refresh_interval: str = "30s"
    tags: List[str] = field(default_factory=list)


class DashboardExporter:
    """Exports dashboard configurations for various platforms"""
    
    def __init__(self):
        self.settings = get_settings()
        self.dashboards = self._create_default_dashboards()
        
    def _create_default_dashboards(self) -> Dict[str, MetricsDashboard]:
        """Create default dashboard configurations"""
        dashboards = {}
        
        # Overview Dashboard
        dashboards["overview"] = MetricsDashboard(
            name="query_intelligence_overview",
            title="Query Intelligence - Overview",
            description="High-level overview of Query Intelligence service performance",
            tags=["query-intelligence", "overview", "sre"],
            panels=[
                # Request Rate
                MetricPanel(
                    title="Request Rate (QPS)",
                    panel_type="graph",
                    metrics=["query_intelligence_queries_total"],
                    query='rate(query_intelligence_queries_total[1m])',
                    unit="reqps",
                    width=12,
                    height=8
                ),
                
                # Success Rate
                MetricPanel(
                    title="Success Rate",
                    panel_type="stat",
                    metrics=["query_intelligence_queries_total"],
                    query='(sum(rate(query_intelligence_queries_total{status="success"}[5m])) / sum(rate(query_intelligence_queries_total[5m]))) * 100',
                    unit="percent",
                    thresholds={"critical": 95, "warning": 98},
                    width=6,
                    height=4
                ),
                
                # Error Rate
                MetricPanel(
                    title="Error Rate",
                    panel_type="stat",
                    metrics=["query_intelligence_errors_total"],
                    query='sum(rate(query_intelligence_errors_total[5m]))',
                    unit="errors/sec",
                    thresholds={"warning": 1, "critical": 5},
                    width=6,
                    height=4
                ),
                
                # Response Time
                MetricPanel(
                    title="Response Time (P95)",
                    panel_type="graph",
                    metrics=["query_intelligence_query_duration_seconds"],
                    query='histogram_quantile(0.95, rate(query_intelligence_query_duration_seconds_bucket[5m]))',
                    unit="ms",
                    thresholds={"warning": 100, "critical": 500},
                    width=12,
                    height=8
                ),
                
                # Active Queries
                MetricPanel(
                    title="Active Queries",
                    panel_type="stat",
                    metrics=["query_intelligence_active_queries"],
                    unit="queries",
                    width=4,
                    height=4
                ),
                
                # Queue Size
                MetricPanel(
                    title="Queue Size",
                    panel_type="stat",
                    metrics=["query_intelligence_query_queue_size"],
                    unit="queries",
                    thresholds={"warning": 50, "critical": 100},
                    width=4,
                    height=4
                ),
                
                # Cache Hit Rate
                MetricPanel(
                    title="Cache Hit Rate",
                    panel_type="stat",
                    metrics=["query_intelligence_cache_hits_total", "query_intelligence_cache_misses_total"],
                    query='(sum(rate(query_intelligence_cache_hits_total[5m])) / (sum(rate(query_intelligence_cache_hits_total[5m])) + sum(rate(query_intelligence_cache_misses_total[5m])))) * 100',
                    unit="percent",
                    thresholds={"critical": 50, "warning": 70},
                    width=4,
                    height=4
                )
            ]
        )
        
        # Performance Dashboard
        dashboards["performance"] = MetricsDashboard(
            name="query_intelligence_performance",
            title="Query Intelligence - Performance Deep Dive",
            description="Detailed performance metrics and analysis",
            tags=["query-intelligence", "performance", "optimization"],
            panels=[
                # Response Time Distribution
                MetricPanel(
                    title="Response Time Distribution",
                    panel_type="heatmap",
                    metrics=["query_intelligence_query_duration_seconds"],
                    description="Heatmap of response time distribution",
                    width=12,
                    height=10
                ),
                
                # Response Time by Repository
                MetricPanel(
                    title="Response Time by Repository",
                    panel_type="graph",
                    metrics=["query_intelligence_query_duration_seconds"],
                    query='histogram_quantile(0.95, sum by (repository) (rate(query_intelligence_query_duration_seconds_bucket[5m])))',
                    unit="ms",
                    width=12,
                    height=8
                ),
                
                # CPU Usage
                MetricPanel(
                    title="CPU Usage",
                    panel_type="graph",
                    metrics=["query_intelligence_cpu_usage_percent"],
                    unit="percent",
                    thresholds={"warning": 70, "critical": 85},
                    width=6,
                    height=8
                ),
                
                # Memory Usage
                MetricPanel(
                    title="Memory Usage",
                    panel_type="graph",
                    metrics=["query_intelligence_memory_usage_mb"],
                    unit="megabytes",
                    thresholds={"warning": 3500, "critical": 4000},
                    width=6,
                    height=8
                ),
                
                # Goroutines/Tasks
                MetricPanel(
                    title="Active Tasks",
                    panel_type="graph",
                    metrics=["query_intelligence_goroutines_count"],
                    unit="tasks",
                    width=6,
                    height=6
                ),
                
                # Throughput
                MetricPanel(
                    title="Current Throughput",
                    panel_type="stat",
                    metrics=["query_intelligence_throughput_qps"],
                    unit="qps",
                    width=6,
                    height=6
                ),
                
                # Confidence Score Distribution
                MetricPanel(
                    title="Confidence Score Distribution",
                    panel_type="graph",
                    metrics=["query_intelligence_confidence_score"],
                    query='histogram_quantile(0.5, rate(query_intelligence_confidence_score_bucket[5m]))',
                    unit="score",
                    width=12,
                    height=8
                )
            ]
        )
        
        # Cache Performance Dashboard
        dashboards["cache"] = MetricsDashboard(
            name="query_intelligence_cache",
            title="Query Intelligence - Cache Performance",
            description="Cache efficiency and optimization metrics",
            tags=["query-intelligence", "cache", "performance"],
            panels=[
                # Cache Hit Rate Over Time
                MetricPanel(
                    title="Cache Hit Rate Trend",
                    panel_type="graph",
                    metrics=["query_intelligence_cache_hits_total", "query_intelligence_cache_misses_total"],
                    query='(sum(rate(query_intelligence_cache_hits_total[5m])) / (sum(rate(query_intelligence_cache_hits_total[5m])) + sum(rate(query_intelligence_cache_misses_total[5m])))) * 100',
                    unit="percent",
                    width=12,
                    height=8
                ),
                
                # Cache Operations
                MetricPanel(
                    title="Cache Operations/sec",
                    panel_type="graph",
                    metrics=["query_intelligence_cache_hits_total", "query_intelligence_cache_misses_total"],
                    query='sum by (cache_type) (rate(query_intelligence_cache_hits_total[1m]) + rate(query_intelligence_cache_misses_total[1m]))',
                    unit="ops",
                    width=12,
                    height=8
                ),
                
                # Cache Size
                MetricPanel(
                    title="Cache Entries",
                    panel_type="graph",
                    metrics=["query_intelligence_cache_size_entries"],
                    unit="entries",
                    width=6,
                    height=6
                ),
                
                # Cache Memory
                MetricPanel(
                    title="Cache Memory Usage",
                    panel_type="graph",
                    metrics=["query_intelligence_cache_memory_mb"],
                    unit="megabytes",
                    width=6,
                    height=6
                ),
                
                # Evictions
                MetricPanel(
                    title="Cache Evictions",
                    panel_type="graph",
                    metrics=["query_intelligence_cache_evictions_total"],
                    query='sum by (reason) (rate(query_intelligence_cache_evictions_total[5m]))',
                    unit="evictions/sec",
                    width=12,
                    height=8
                )
            ]
        )
        
        # Health & Availability Dashboard
        dashboards["health"] = MetricsDashboard(
            name="query_intelligence_health",
            title="Query Intelligence - Health & Availability",
            description="Service health, dependencies, and SLA tracking",
            tags=["query-intelligence", "health", "sla"],
            panels=[
                # Service Availability
                MetricPanel(
                    title="Service Availability",
                    panel_type="stat",
                    metrics=["query_intelligence_availability_percent"],
                    unit="percent",
                    thresholds={"critical": 99, "warning": 99.5},
                    width=6,
                    height=4
                ),
                
                # Uptime
                MetricPanel(
                    title="Service Uptime",
                    panel_type="stat",
                    metrics=["query_intelligence_uptime_seconds"],
                    query='query_intelligence_uptime_seconds / 86400',
                    unit="days",
                    width=6,
                    height=4
                ),
                
                # Circuit Breaker States
                MetricPanel(
                    title="Circuit Breaker States",
                    panel_type="table",
                    metrics=["query_intelligence_circuit_breaker_state"],
                    description="0=closed, 1=open, 2=half-open",
                    width=12,
                    height=6
                ),
                
                # SLA Violations
                MetricPanel(
                    title="SLA Violations",
                    panel_type="graph",
                    metrics=["query_intelligence_sla_violations_total"],
                    query='sum by (sla_type, severity) (rate(query_intelligence_sla_violations_total[5m]))',
                    unit="violations/sec",
                    width=12,
                    height=8
                ),
                
                # Error Types
                MetricPanel(
                    title="Errors by Type",
                    panel_type="graph",
                    metrics=["query_intelligence_errors_total"],
                    query='sum by (error_type) (rate(query_intelligence_errors_total[5m]))',
                    unit="errors/sec",
                    width=12,
                    height=8
                ),
                
                # Timeouts
                MetricPanel(
                    title="Timeouts",
                    panel_type="graph",
                    metrics=["query_intelligence_timeouts_total"],
                    query='sum by (operation) (rate(query_intelligence_timeouts_total[5m]))',
                    unit="timeouts/sec",
                    width=6,
                    height=6
                ),
                
                # Retries
                MetricPanel(
                    title="Retry Operations",
                    panel_type="graph",
                    metrics=["query_intelligence_retries_total"],
                    query='sum by (operation, reason) (rate(query_intelligence_retries_total[5m]))',
                    unit="retries/sec",
                    width=6,
                    height=6
                )
            ]
        )
        
        # Business Metrics Dashboard
        dashboards["business"] = MetricsDashboard(
            name="query_intelligence_business",
            title="Query Intelligence - Business Metrics",
            description="Usage patterns and business-level metrics",
            tags=["query-intelligence", "business", "usage"],
            panels=[
                # Unique Repositories
                MetricPanel(
                    title="Active Repositories",
                    panel_type="stat",
                    metrics=["query_intelligence_repositories_queried_total"],
                    unit="repositories",
                    width=6,
                    height=4
                ),
                
                # Active Users
                MetricPanel(
                    title="Active Users (1h)",
                    panel_type="stat",
                    metrics=["query_intelligence_users_active"],
                    unit="users",
                    width=6,
                    height=4
                ),
                
                # Query Patterns
                MetricPanel(
                    title="Query Patterns",
                    panel_type="graph",
                    metrics=["query_intelligence_query_patterns_total"],
                    query='sum by (pattern_type) (rate(query_intelligence_query_patterns_total[5m]))',
                    unit="queries/sec",
                    width=12,
                    height=8
                ),
                
                # API Usage
                MetricPanel(
                    title="API Usage by Endpoint",
                    panel_type="table",
                    metrics=["query_intelligence_api_usage_total"],
                    query='sum by (endpoint, method) (rate(query_intelligence_api_usage_total[5m]))',
                    unit="requests/sec",
                    width=12,
                    height=10
                ),
                
                # Response Size Distribution
                MetricPanel(
                    title="Response Size Distribution",
                    panel_type="graph",
                    metrics=["query_intelligence_response_size_bytes"],
                    query='histogram_quantile(0.95, rate(query_intelligence_response_size_bytes_bucket[5m]))',
                    unit="bytes",
                    width=12,
                    height=8
                )
            ]
        )
        
        return dashboards
        
    def export_dashboard(self, 
                        dashboard_name: str,
                        dashboard_type: DashboardType,
                        output_path: Optional[Path] = None) -> Dict[str, Any]:
        """Export dashboard configuration for a specific platform"""
        
        if dashboard_name not in self.dashboards:
            raise ValueError(f"Unknown dashboard: {dashboard_name}")
            
        dashboard = self.dashboards[dashboard_name]
        
        if dashboard_type == DashboardType.GRAFANA:
            config = self._export_grafana(dashboard)
        elif dashboard_type == DashboardType.DATADOG:
            config = self._export_datadog(dashboard)
        elif dashboard_type == DashboardType.CLOUDWATCH:
            config = self._export_cloudwatch(dashboard)
        else:
            config = self._export_custom(dashboard)
            
        if output_path:
            # Determine file format
            if output_path.suffix == ".json":
                with open(output_path, 'w') as f:
                    json.dump(config, f, indent=2)
            elif output_path.suffix in [".yml", ".yaml"]:
                with open(output_path, 'w') as f:
                    yaml.dump(config, f, default_flow_style=False)
            else:
                # Default to JSON
                with open(output_path, 'w') as f:
                    json.dump(config, f, indent=2)
                    
            logger.info(
                "dashboard_exported",
                dashboard_name=dashboard_name,
                dashboard_type=dashboard_type.value,
                output_path=str(output_path)
            )
            
        return config
        
    def _export_grafana(self, dashboard: MetricsDashboard) -> Dict[str, Any]:
        """Export dashboard in Grafana format"""
        panels = []
        
        for i, panel in enumerate(dashboard.panels):
            grafana_panel = {
                "id": i + 1,
                "title": panel.title,
                "type": self._map_panel_type_grafana(panel.panel_type),
                "datasource": "Prometheus",
                "gridPos": {
                    "h": panel.height,
                    "w": panel.width,
                    "x": panel.position["x"] if panel.position else (i * 12) % 24,
                    "y": panel.position["y"] if panel.position else (i // 2) * 8
                },
                "targets": [
                    {
                        "expr": panel.query or f"{panel.metrics[0]}",
                        "refId": "A"
                    }
                ]
            }
            
            # Add thresholds if specified
            if panel.thresholds:
                grafana_panel["thresholds"] = {
                    "mode": "absolute",
                    "steps": [
                        {"color": "green", "value": None},
                        {"color": "yellow", "value": panel.thresholds.get("warning")},
                        {"color": "red", "value": panel.thresholds.get("critical")}
                    ]
                }
                
            # Add unit if specified
            if panel.unit:
                grafana_panel["fieldConfig"] = {
                    "defaults": {
                        "unit": self._map_unit_grafana(panel.unit)
                    }
                }
                
            panels.append(grafana_panel)
            
        return {
            "dashboard": {
                "title": dashboard.title,
                "description": dashboard.description,
                "tags": dashboard.tags,
                "timezone": "browser",
                "panels": panels,
                "time": {
                    "from": f"now-{dashboard.time_range}",
                    "to": "now"
                },
                "timepicker": {
                    "refresh_intervals": ["30s", "1m", "5m", "15m", "30m", "1h"],
                    "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]
                },
                "templating": {
                    "list": self._convert_variables_grafana(dashboard.variables)
                },
                "refresh": dashboard.refresh_interval,
                "schemaVersion": 27,
                "version": 1
            }
        }
        
    def _export_datadog(self, dashboard: MetricsDashboard) -> Dict[str, Any]:
        """Export dashboard in Datadog format"""
        widgets = []
        
        for panel in dashboard.panels:
            widget = {
                "definition": {
                    "title": panel.title,
                    "type": self._map_panel_type_datadog(panel.panel_type),
                    "requests": [
                        {
                            "q": self._convert_query_datadog(panel.query or panel.metrics[0])
                        }
                    ]
                }
            }
            
            widgets.append(widget)
            
        return {
            "title": dashboard.title,
            "description": dashboard.description,
            "widgets": widgets,
            "template_variables": self._convert_variables_datadog(dashboard.variables),
            "layout_type": "ordered",
            "is_read_only": False,
            "notify_list": []
        }
        
    def _export_cloudwatch(self, dashboard: MetricsDashboard) -> Dict[str, Any]:
        """Export dashboard in CloudWatch format"""
        widgets = []
        
        x, y = 0, 0
        for panel in dashboard.panels:
            widget = {
                "type": "metric",
                "x": x,
                "y": y,
                "width": panel.width,
                "height": panel.height,
                "properties": {
                    "metrics": [
                        ["QueryIntelligence", metric] for metric in panel.metrics
                    ],
                    "period": 300,
                    "stat": "Average",
                    "region": "us-central1",
                    "title": panel.title
                }
            }
            
            widgets.append(widget)
            
            # Update position
            x += panel.width
            if x >= 24:
                x = 0
                y += panel.height
                
        return {
            "widgets": widgets
        }
        
    def _export_custom(self, dashboard: MetricsDashboard) -> Dict[str, Any]:
        """Export dashboard in custom generic format"""
        return {
            "name": dashboard.name,
            "title": dashboard.title,
            "description": dashboard.description,
            "tags": dashboard.tags,
            "time_range": dashboard.time_range,
            "refresh_interval": dashboard.refresh_interval,
            "variables": dashboard.variables,
            "panels": [
                {
                    "title": panel.title,
                    "type": panel.panel_type,
                    "metrics": panel.metrics,
                    "query": panel.query,
                    "description": panel.description,
                    "unit": panel.unit,
                    "thresholds": panel.thresholds,
                    "dimensions": {
                        "width": panel.width,
                        "height": panel.height
                    },
                    "position": panel.position
                }
                for panel in dashboard.panels
            ]
        }
        
    def _map_panel_type_grafana(self, panel_type: str) -> str:
        """Map generic panel type to Grafana type"""
        mapping = {
            "graph": "graph",
            "stat": "stat",
            "table": "table",
            "heatmap": "heatmap",
            "gauge": "gauge",
            "bar": "bargauge"
        }
        return mapping.get(panel_type, "graph")
        
    def _map_panel_type_datadog(self, panel_type: str) -> str:
        """Map generic panel type to Datadog type"""
        mapping = {
            "graph": "timeseries",
            "stat": "query_value",
            "table": "query_table",
            "heatmap": "heatmap",
            "gauge": "query_value",
            "bar": "timeseries"
        }
        return mapping.get(panel_type, "timeseries")
        
    def _map_unit_grafana(self, unit: str) -> str:
        """Map generic unit to Grafana unit"""
        mapping = {
            "percent": "percent",
            "bytes": "bytes",
            "megabytes": "mbytes",
            "milliseconds": "ms",
            "seconds": "s",
            "requests/sec": "reqps",
            "ops": "ops",
            "errors/sec": "cps",
            "qps": "reqps"
        }
        return mapping.get(unit, unit)
        
    def _convert_query_datadog(self, query: str) -> str:
        """Convert Prometheus query to Datadog format"""
        # This is a simplified conversion
        # In production, you'd need more sophisticated query translation
        return f"avg:query_intelligence.{query}{{*}}"
        
    def _convert_variables_grafana(self, variables: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Convert variables to Grafana format"""
        grafana_vars = []
        
        for name, config in variables.items():
            grafana_vars.append({
                "name": name,
                "type": "query",
                "query": config.get("query", ""),
                "refresh": 1,
                "sort": 1
            })
            
        return grafana_vars
        
    def _convert_variables_datadog(self, variables: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Convert variables to Datadog format"""
        dd_vars = []
        
        for name, config in variables.items():
            dd_vars.append({
                "name": name,
                "prefix": name,
                "available_values": config.get("values", []),
                "default": config.get("default", "*")
            })
            
        return dd_vars
        
    def export_all_dashboards(self, 
                             dashboard_type: DashboardType,
                             output_dir: Path):
        """Export all dashboards to a directory"""
        output_dir.mkdir(parents=True, exist_ok=True)
        
        for name in self.dashboards:
            output_path = output_dir / f"{name}.json"
            self.export_dashboard(name, dashboard_type, output_path)
            
        logger.info(
            "all_dashboards_exported",
            dashboard_type=dashboard_type.value,
            output_dir=str(output_dir),
            count=len(self.dashboards)
        )