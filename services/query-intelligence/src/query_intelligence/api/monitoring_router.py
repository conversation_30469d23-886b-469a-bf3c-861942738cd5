"""
Monitoring API Router

Provides endpoints for metrics, health checks, and monitoring data.
"""

from typing import Dict, Any, Optional, List
from datetime import datetime
import json

from fastapi import APIRouter, HTTPException, Query, Response, Depends
from fastapi.responses import PlainTextResponse
from prometheus_client import CONTENT_TYPE_LATEST
import structlog

from ..monitoring import (
    MetricsCollector,
    HealthMonitor,
    AlertManager,
    DashboardExporter,
    HealthStatus,
    AlertSeverity,
    DashboardType
)
from ..dependencies import get_metrics_collector, get_health_monitor, get_alert_manager

logger = structlog.get_logger()

router = APIRouter(prefix="/monitoring", tags=["monitoring"])


@router.get("/metrics", 
           response_class=PlainTextResponse,
           summary="Prometheus metrics endpoint",
           description="Returns metrics in Prometheus exposition format")
async def get_metrics(
    metrics: MetricsCollector = Depends(get_metrics_collector)
) -> Response:
    """Get Prometheus metrics"""
    try:
        metrics_data = metrics.get_metrics()
        return Response(
            content=metrics_data,
            media_type=CONTENT_TYPE_LATEST
        )
    except Exception as e:
        logger.error("failed_to_get_metrics", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to retrieve metrics")


@router.get("/health",
           response_model=Dict[str, Any],
           summary="Detailed health check",
           description="Returns comprehensive health status of the service and its dependencies")
async def get_health(
    detailed: bool = Query(False, description="Include detailed metrics"),
    force_check: bool = Query(False, description="Force refresh of health checks"),
    health_monitor: HealthMonitor = Depends(get_health_monitor)
) -> Dict[str, Any]:
    """Get detailed health status"""
    try:
        health = await health_monitor.check_health(
            detailed=detailed,
            force_check=force_check
        )
        
        # Return appropriate status code based on health
        status_code = 200
        if health.status == HealthStatus.DEGRADED:
            status_code = 200  # Still operational but degraded
        elif health.status == HealthStatus.UNHEALTHY:
            status_code = 503  # Service unavailable
            
        return {
            "status": health.status.value,
            "version": health.version,
            "uptime_seconds": health.uptime_seconds,
            "components": {
                name: comp.to_dict() 
                for name, comp in health.components.items()
            },
            "metrics": health.metrics if detailed else {},
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error("health_check_failed", error=str(e))
        raise HTTPException(status_code=500, detail="Health check failed")


@router.get("/health/live",
           response_model=Dict[str, str],
           summary="Liveness probe",
           description="Simple liveness check for Kubernetes")
async def liveness_probe() -> Dict[str, str]:
    """Kubernetes liveness probe endpoint"""
    return {"status": "alive"}


@router.get("/health/ready",
           response_model=Dict[str, Any],
           summary="Readiness probe",
           description="Readiness check for Kubernetes")
async def readiness_probe(
    health_monitor: HealthMonitor = Depends(get_health_monitor)
) -> Dict[str, Any]:
    """Kubernetes readiness probe endpoint"""
    try:
        health = await health_monitor.check_health()
        
        # Service is ready if healthy or degraded
        ready = health.status in [HealthStatus.HEALTHY, HealthStatus.DEGRADED]
        
        if not ready:
            raise HTTPException(status_code=503, detail="Service not ready")
            
        return {
            "status": "ready",
            "health_status": health.status.value
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("readiness_check_failed", error=str(e))
        raise HTTPException(status_code=503, detail="Readiness check failed")


@router.get("/alerts",
           response_model=Dict[str, Any],
           summary="Get active alerts",
           description="Returns currently active alerts and alert statistics")
async def get_alerts(
    active_only: bool = Query(True, description="Only show active alerts"),
    severity: Optional[str] = Query(None, description="Filter by severity"),
    alert_manager: AlertManager = Depends(get_alert_manager)
) -> Dict[str, Any]:
    """Get alert information"""
    try:
        # Parse severity filter
        severity_filter = None
        if severity:
            try:
                severity_filter = AlertSeverity(severity.lower())
            except ValueError:
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid severity: {severity}. Valid values: {[s.value for s in AlertSeverity]}"
                )
                
        # Get alerts
        if active_only:
            alerts = alert_manager.get_active_alerts()
        else:
            alerts = alert_manager.get_alert_history(hours=24, severity=severity_filter)
            
        # Get statistics
        stats = alert_manager.get_alert_statistics()
        
        return {
            "alerts": [alert.to_dict() for alert in alerts],
            "statistics": stats,
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("failed_to_get_alerts", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to retrieve alerts")


@router.get("/alerts/rules",
           response_model=List[Dict[str, Any]],
           summary="Get alert rules",
           description="Returns configured alert rules")
async def get_alert_rules(
    enabled_only: bool = Query(False, description="Only show enabled rules"),
    alert_manager: AlertManager = Depends(get_alert_manager)
) -> List[Dict[str, Any]]:
    """Get alert rule configurations"""
    try:
        rules = []
        
        for rule_name, rule in alert_manager.rules.items():
            if enabled_only and not rule.enabled:
                continue
                
            rules.append({
                "name": rule.name,
                "description": rule.description,
                "severity": rule.severity.value,
                "enabled": rule.enabled,
                "cooldown_seconds": rule.cooldown_seconds,
                "conditions": [
                    {
                        "metric": cond.metric,
                        "operator": cond.operator,
                        "threshold": cond.threshold,
                        "duration_seconds": cond.duration_seconds
                    }
                    for cond in rule.conditions
                ],
                "metadata": rule.metadata,
                "last_triggered": rule.last_triggered.isoformat() if rule.last_triggered else None
            })
            
        return rules
        
    except Exception as e:
        logger.error("failed_to_get_alert_rules", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to retrieve alert rules")


@router.post("/alerts/rules/{rule_name}/toggle",
            response_model=Dict[str, Any],
            summary="Toggle alert rule",
            description="Enable or disable an alert rule")
async def toggle_alert_rule(
    rule_name: str,
    enabled: bool = Query(..., description="Enable or disable the rule"),
    alert_manager: AlertManager = Depends(get_alert_manager)
) -> Dict[str, Any]:
    """Toggle alert rule enabled state"""
    try:
        if rule_name not in alert_manager.rules:
            raise HTTPException(status_code=404, detail=f"Alert rule not found: {rule_name}")
            
        if enabled:
            alert_manager.enable_rule(rule_name)
        else:
            alert_manager.disable_rule(rule_name)
            
        return {
            "rule_name": rule_name,
            "enabled": enabled,
            "message": f"Alert rule {rule_name} {'enabled' if enabled else 'disabled'}"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("failed_to_toggle_alert_rule", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to toggle alert rule")


@router.get("/dashboards",
           response_model=List[Dict[str, Any]],
           summary="List available dashboards",
           description="Returns list of available dashboard configurations")
async def list_dashboards() -> List[Dict[str, Any]]:
    """List available dashboards"""
    try:
        exporter = DashboardExporter()
        
        dashboards = []
        for name, dashboard in exporter.dashboards.items():
            dashboards.append({
                "name": name,
                "title": dashboard.title,
                "description": dashboard.description,
                "tags": dashboard.tags,
                "panel_count": len(dashboard.panels)
            })
            
        return dashboards
        
    except Exception as e:
        logger.error("failed_to_list_dashboards", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to list dashboards")


@router.get("/dashboards/{dashboard_name}",
           response_model=Dict[str, Any],
           summary="Get dashboard configuration",
           description="Returns dashboard configuration in specified format")
async def get_dashboard(
    dashboard_name: str,
    format: str = Query("grafana", description="Dashboard format (grafana, datadog, cloudwatch, custom)")
) -> Dict[str, Any]:
    """Get dashboard configuration"""
    try:
        # Parse format
        try:
            dashboard_type = DashboardType(format.lower())
        except ValueError:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid format: {format}. Valid values: {[t.value for t in DashboardType]}"
            )
            
        exporter = DashboardExporter()
        
        if dashboard_name not in exporter.dashboards:
            raise HTTPException(status_code=404, detail=f"Dashboard not found: {dashboard_name}")
            
        config = exporter.export_dashboard(dashboard_name, dashboard_type)
        
        return {
            "name": dashboard_name,
            "format": format,
            "configuration": config
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("failed_to_get_dashboard", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get dashboard")


@router.get("/metrics/summary",
           response_model=Dict[str, Any],
           summary="Get metrics summary",
           description="Returns a human-readable summary of current metrics")
async def get_metrics_summary(
    metrics: MetricsCollector = Depends(get_metrics_collector)
) -> Dict[str, Any]:
    """Get metrics summary"""
    try:
        summary = metrics.get_metrics_summary()
        
        # Add calculated metrics
        if summary.get("queries_total", 0) > 0:
            errors = summary.get("error_rate", 0)
            summary["success_rate"] = 100 - errors
            
        return {
            "summary": summary,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error("failed_to_get_metrics_summary", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get metrics summary")


@router.post("/metrics/reset",
            response_model=Dict[str, str],
            summary="Reset metrics",
            description="Reset all metrics (testing only)")
async def reset_metrics(
    confirm: bool = Query(False, description="Confirm metric reset"),
    metrics: MetricsCollector = Depends(get_metrics_collector)
) -> Dict[str, str]:
    """Reset metrics (testing only)"""
    if not confirm:
        raise HTTPException(
            status_code=400,
            detail="Metric reset not confirmed. Set confirm=true to proceed."
        )
        
    try:
        metrics.reset_metrics()
        return {"message": "Metrics reset successfully"}
        
    except Exception as e:
        logger.error("failed_to_reset_metrics", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to reset metrics")