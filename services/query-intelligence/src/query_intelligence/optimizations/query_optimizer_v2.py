"""
Query Optimizer V2 - Performance optimizations for 1000+ QPS

Implements advanced optimization techniques:
- Query result streaming
- Predictive pre-fetching
- Request batching
- Semantic caching improvements
"""

import asyncio
from typing import Dict, List, Any, Optional, AsyncIterator
from datetime import datetime, timedelta
import hashlib
import json
from functools import lru_cache
import numpy as np
from collections import deque
import structlog

from ..models.query import QueryRequest, QueryResult, QueryStreamChunk
from ..services.cache_manager import CacheManager
from ..services.semantic_search import SemanticSearchService

logger = structlog.get_logger()


class QueryOptimizerV2:
    """Advanced query optimizer for high-performance processing."""
    
    def __init__(
        self,
        cache_manager: CacheManager,
        semantic_search: SemanticSearchService,
        batch_size: int = 10,
        prefetch_threshold: float = 0.8,
        stream_chunk_size: int = 512
    ):
        self.cache_manager = cache_manager
        self.semantic_search = semantic_search
        self.batch_size = batch_size
        self.prefetch_threshold = prefetch_threshold
        self.stream_chunk_size = stream_chunk_size
        
        # Query pattern tracking for predictive optimization
        self.query_patterns = deque(maxlen=1000)
        self.pattern_cache = {}
        
        # Batch processing queue
        self.batch_queue = asyncio.Queue(maxsize=100)
        self.batch_processor_task = None
        
        # Performance metrics
        self.metrics = {
            "cache_hits": 0,
            "cache_misses": 0,
            "prefetch_hits": 0,
            "batch_processed": 0,
            "stream_chunks_sent": 0
        }
    
    async def start(self):
        """Start the batch processor."""
        self.batch_processor_task = asyncio.create_task(self._batch_processor())
        logger.info("query_optimizer_v2_started")
    
    async def stop(self):
        """Stop the batch processor."""
        if self.batch_processor_task:
            self.batch_processor_task.cancel()
            try:
                await self.batch_processor_task
            except asyncio.CancelledError:
                pass
        logger.info("query_optimizer_v2_stopped", metrics=self.metrics)
    
    async def optimize_query(self, request: QueryRequest) -> Dict[str, Any]:
        """
        Optimize query processing with advanced techniques.
        
        Returns optimization hints and cached results if available.
        """
        optimization_start = asyncio.get_event_loop().time()
        
        # Generate query fingerprint for caching
        query_fingerprint = self._generate_query_fingerprint(request)
        
        # Check semantic cache first
        cached_result = await self._check_semantic_cache(request, query_fingerprint)
        if cached_result:
            self.metrics["cache_hits"] += 1
            return {
                "cached": True,
                "result": cached_result,
                "optimization_time_ms": (asyncio.get_event_loop().time() - optimization_start) * 1000
            }
        
        self.metrics["cache_misses"] += 1
        
        # Track query pattern
        self._track_query_pattern(request)
        
        # Check if we should prefetch related queries
        prefetch_suggestions = await self._get_prefetch_suggestions(request)
        
        # Prepare optimization hints
        hints = {
            "cached": False,
            "query_fingerprint": query_fingerprint,
            "prefetch_suggestions": prefetch_suggestions,
            "batch_eligible": self._is_batch_eligible(request),
            "stream_recommended": len(request.query) > 500 or request.stream,
            "optimization_time_ms": (asyncio.get_event_loop().time() - optimization_start) * 1000
        }
        
        # If batch eligible, add to batch queue
        if hints["batch_eligible"]:
            await self._add_to_batch_queue(request, query_fingerprint)
        
        return hints
    
    async def stream_optimized_response(
        self,
        result: QueryResult,
        request: QueryRequest
    ) -> AsyncIterator[QueryStreamChunk]:
        """
        Stream response in optimized chunks for better performance.
        """
        # Split answer into chunks
        answer_chunks = self._split_into_chunks(result.answer, self.stream_chunk_size)
        
        # Stream text chunks
        for i, chunk in enumerate(answer_chunks):
            yield QueryStreamChunk(
                type="text",
                content=chunk,
                done=False
            )
            self.metrics["stream_chunks_sent"] += 1
            
            # Small delay to prevent overwhelming the client
            if i % 5 == 0:
                await asyncio.sleep(0.01)
        
        # Stream references
        for ref in result.references[:5]:  # Limit initial references
            yield QueryStreamChunk(
                type="reference",
                reference=ref,
                done=False
            )
            self.metrics["stream_chunks_sent"] += 1
        
        # Stream metadata
        yield QueryStreamChunk(
            type="metadata",
            metadata={
                "total_references": len(result.references),
                "confidence": result.confidence,
                "execution_time_ms": result.execution_time_ms,
                "model_used": result.metadata.get("model_used"),
                "cache_status": result.metadata.get("cache_status", "miss")
            },
            done=False
        )
        
        # Final done chunk
        yield QueryStreamChunk(
            type="done",
            done=True
        )
    
    def _generate_query_fingerprint(self, request: QueryRequest) -> str:
        """Generate a unique fingerprint for the query."""
        # Include query, repository, and key filters
        fingerprint_data = {
            "query": request.query.lower().strip(),
            "repository_id": request.repository_id,
            "language_filter": request.filters.get("language"),
            "file_patterns": request.filters.get("file_pattern")
        }
        
        fingerprint_str = json.dumps(fingerprint_data, sort_keys=True)
        return hashlib.sha256(fingerprint_str.encode()).hexdigest()[:16]
    
    async def _check_semantic_cache(
        self,
        request: QueryRequest,
        fingerprint: str
    ) -> Optional[QueryResult]:
        """Check semantic cache for similar queries."""
        # First check exact match
        exact_cache_key = f"query_result:{fingerprint}"
        cached_data = await self.cache_manager.get(exact_cache_key)
        
        if cached_data:
            try:
                return QueryResult(**json.loads(cached_data))
            except Exception as e:
                logger.warning("cache_deserialization_failed", error=str(e))
        
        # Check semantic similarity cache
        if hasattr(self.semantic_search, 'find_similar_cached_queries'):
            similar_queries = await self.semantic_search.find_similar_cached_queries(
                request.query,
                threshold=0.95  # High similarity threshold
            )
            
            if similar_queries:
                # Return the most similar cached result
                return similar_queries[0].get("result")
        
        return None
    
    def _track_query_pattern(self, request: QueryRequest):
        """Track query patterns for predictive optimization."""
        pattern = {
            "timestamp": datetime.now(),
            "query_type": self._classify_query_type(request.query),
            "repository": request.repository_id,
            "filters": list(request.filters.keys()),
            "query_length": len(request.query)
        }
        
        self.query_patterns.append(pattern)
        
        # Update pattern cache
        pattern_key = f"{pattern['query_type']}:{pattern['repository']}"
        if pattern_key not in self.pattern_cache:
            self.pattern_cache[pattern_key] = {
                "count": 0,
                "avg_length": 0,
                "common_filters": {}
            }
        
        cache_entry = self.pattern_cache[pattern_key]
        cache_entry["count"] += 1
        cache_entry["avg_length"] = (
            (cache_entry["avg_length"] * (cache_entry["count"] - 1) + pattern["query_length"])
            / cache_entry["count"]
        )
        
        for filter_key in pattern["filters"]:
            cache_entry["common_filters"][filter_key] = (
                cache_entry["common_filters"].get(filter_key, 0) + 1
            )
    
    @lru_cache(maxsize=1000)
    def _classify_query_type(self, query: str) -> str:
        """Classify query type for pattern matching."""
        query_lower = query.lower()
        
        if any(word in query_lower for word in ["how", "explain", "what"]):
            return "explanation"
        elif any(word in query_lower for word in ["find", "search", "locate"]):
            return "search"
        elif any(word in query_lower for word in ["debug", "error", "issue"]):
            return "debugging"
        elif any(word in query_lower for word in ["optimize", "performance", "improve"]):
            return "optimization"
        elif any(word in query_lower for word in ["security", "vulnerability", "exploit"]):
            return "security"
        else:
            return "general"
    
    async def _get_prefetch_suggestions(self, request: QueryRequest) -> List[str]:
        """Get suggestions for queries to prefetch based on patterns."""
        suggestions = []
        
        query_type = self._classify_query_type(request.query)
        pattern_key = f"{query_type}:{request.repository_id}"
        
        if pattern_key in self.pattern_cache:
            pattern_data = self.pattern_cache[pattern_key]
            
            # If this pattern is common, suggest related queries
            if pattern_data["count"] > 5:
                # Suggest follow-up queries based on type
                if query_type == "explanation":
                    suggestions.extend([
                        "Show me examples",
                        "What are the alternatives?",
                        "How is this implemented?"
                    ])
                elif query_type == "search":
                    suggestions.extend([
                        "Show more results",
                        "Filter by recent changes",
                        "Include test files"
                    ])
                elif query_type == "debugging":
                    suggestions.extend([
                        "Show the stack trace",
                        "When was this last modified?",
                        "Who wrote this code?"
                    ])
                
                self.metrics["prefetch_hits"] += len(suggestions)
        
        return suggestions[:3]  # Limit prefetch suggestions
    
    def _is_batch_eligible(self, request: QueryRequest) -> bool:
        """Determine if query is eligible for batch processing."""
        # Simple queries without complex filters are batch eligible
        return (
            len(request.query) < 200 and
            not request.stream and
            len(request.filters) < 3 and
            not request.context_history
        )
    
    async def _add_to_batch_queue(self, request: QueryRequest, fingerprint: str):
        """Add query to batch processing queue."""
        try:
            await asyncio.wait_for(
                self.batch_queue.put((request, fingerprint)),
                timeout=0.1  # Don't wait if queue is full
            )
        except asyncio.TimeoutError:
            logger.debug("batch_queue_full", query_fingerprint=fingerprint)
    
    async def _batch_processor(self):
        """Process queries in batches for efficiency."""
        while True:
            try:
                # Collect batch
                batch = []
                
                # Wait for first item
                first_item = await self.batch_queue.get()
                batch.append(first_item)
                
                # Collect more items up to batch size
                deadline = asyncio.get_event_loop().time() + 0.1  # 100ms window
                
                while len(batch) < self.batch_size and asyncio.get_event_loop().time() < deadline:
                    try:
                        item = await asyncio.wait_for(
                            self.batch_queue.get(),
                            timeout=0.01
                        )
                        batch.append(item)
                    except asyncio.TimeoutError:
                        break
                
                if batch:
                    # Process batch
                    await self._process_batch(batch)
                    self.metrics["batch_processed"] += len(batch)
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error("batch_processor_error", error=str(e))
                await asyncio.sleep(1)
    
    async def _process_batch(self, batch: List[tuple]):
        """Process a batch of queries efficiently."""
        # In a real implementation, this would:
        # 1. Combine similar queries
        # 2. Fetch embeddings in bulk
        # 3. Process results in parallel
        # 4. Cache results
        
        logger.info("processing_batch", size=len(batch))
        
        # Simulate batch processing
        tasks = []
        for request, fingerprint in batch:
            # Each query would be processed in parallel
            pass
    
    def _split_into_chunks(self, text: str, chunk_size: int) -> List[str]:
        """Split text into chunks for streaming."""
        chunks = []
        
        # Try to split on sentence boundaries
        sentences = text.split('. ')
        current_chunk = ""
        
        for sentence in sentences:
            if len(current_chunk) + len(sentence) + 2 <= chunk_size:
                current_chunk += sentence + ". "
            else:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                current_chunk = sentence + ". "
        
        if current_chunk:
            chunks.append(current_chunk.strip())
        
        return chunks
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics."""
        total_requests = self.metrics["cache_hits"] + self.metrics["cache_misses"]
        
        return {
            "cache_hit_rate": (
                self.metrics["cache_hits"] / total_requests if total_requests > 0 else 0
            ),
            "prefetch_effectiveness": (
                self.metrics["prefetch_hits"] / total_requests if total_requests > 0 else 0
            ),
            "batch_processing_rate": self.metrics["batch_processed"],
            "stream_chunks_sent": self.metrics["stream_chunks_sent"],
            "pattern_cache_size": len(self.pattern_cache),
            "raw_metrics": self.metrics
        }