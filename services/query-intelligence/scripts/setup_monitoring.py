#!/usr/bin/env python3
"""
Setup Monitoring Infrastructure

This script sets up monitoring configurations, exports dashboards,
and generates monitoring documentation for the Query Intelligence service.
"""

import asyncio
import json
import yaml
from pathlib import Path
from datetime import datetime
import sys

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

from src.query_intelligence.monitoring import (
    DashboardExporter,
    DashboardType,
    AlertManager,
    AlertRule,
    AlertCondition,
    AlertSeverity
)


class MonitoringSetup:
    """Setup monitoring infrastructure"""
    
    def __init__(self, output_dir: str = "monitoring"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
    async def setup_all(self):
        """Setup all monitoring configurations"""
        print("🔧 Setting up Query Intelligence monitoring infrastructure...")
        
        # Export dashboards
        await self.export_dashboards()
        
        # Generate alert configurations
        await self.generate_alert_configs()
        
        # Create monitoring documentation
        await self.create_documentation()
        
        # Generate deployment configs
        await self.generate_deployment_configs()
        
        print("\n✅ Monitoring setup complete!")
        print(f"📁 Output directory: {self.output_dir}")
        
    async def export_dashboards(self):
        """Export all dashboards in various formats"""
        print("\n📊 Exporting dashboards...")
        
        exporter = DashboardExporter()
        
        # Create dashboard directories
        for dashboard_type in DashboardType:
            type_dir = self.output_dir / "dashboards" / dashboard_type.value
            type_dir.mkdir(parents=True, exist_ok=True)
            
            # Export all dashboards for this type
            exporter.export_all_dashboards(dashboard_type, type_dir)
            print(f"  ✓ Exported {len(exporter.dashboards)} dashboards for {dashboard_type.value}")
            
    async def generate_alert_configs(self):
        """Generate alert rule configurations"""
        print("\n🚨 Generating alert configurations...")
        
        # Create comprehensive alert rules
        alert_rules = {
            "performance_alerts": [
                {
                    "name": "high_error_rate",
                    "description": "Error rate exceeds 5% for 5 minutes",
                    "severity": "error",
                    "conditions": [
                        {
                            "metric": "error_rate",
                            "operator": "gt",
                            "threshold": 5.0,
                            "duration_seconds": 300
                        }
                    ],
                    "cooldown_seconds": 600,
                    "notifications": ["pagerduty", "slack"]
                },
                {
                    "name": "very_high_response_time",
                    "description": "P95 response time exceeds 1000ms for 10 minutes",
                    "severity": "critical",
                    "conditions": [
                        {
                            "metric": "p95_response_time_ms",
                            "operator": "gt",
                            "threshold": 1000,
                            "duration_seconds": 600
                        }
                    ],
                    "cooldown_seconds": 1800,
                    "notifications": ["pagerduty", "slack", "email"]
                },
                {
                    "name": "low_throughput",
                    "description": "Throughput below 100 QPS during business hours",
                    "severity": "warning",
                    "conditions": [
                        {
                            "metric": "throughput_qps",
                            "operator": "lt",
                            "threshold": 100,
                            "duration_seconds": 600
                        }
                    ],
                    "cooldown_seconds": 3600,
                    "business_hours_only": True,
                    "notifications": ["slack"]
                }
            ],
            "resource_alerts": [
                {
                    "name": "memory_critical",
                    "description": "Memory usage exceeds 95% for 5 minutes",
                    "severity": "critical",
                    "conditions": [
                        {
                            "metric": "memory_percent",
                            "operator": "gt",
                            "threshold": 95.0,
                            "duration_seconds": 300
                        }
                    ],
                    "cooldown_seconds": 600,
                    "notifications": ["pagerduty", "slack", "ops_team"]
                },
                {
                    "name": "disk_space_low",
                    "description": "Disk space below 10%",
                    "severity": "warning",
                    "conditions": [
                        {
                            "metric": "disk_free_percent",
                            "operator": "lt",
                            "threshold": 10.0,
                            "duration_seconds": 0
                        }
                    ],
                    "cooldown_seconds": 7200,
                    "notifications": ["ops_team"]
                },
                {
                    "name": "cpu_sustained_high",
                    "description": "CPU usage above 85% for 15 minutes",
                    "severity": "warning",
                    "conditions": [
                        {
                            "metric": "cpu_percent",
                            "operator": "gt",
                            "threshold": 85.0,
                            "duration_seconds": 900
                        }
                    ],
                    "cooldown_seconds": 1800,
                    "notifications": ["slack", "ops_team"]
                }
            ],
            "availability_alerts": [
                {
                    "name": "service_down",
                    "description": "Service health check failing",
                    "severity": "critical",
                    "conditions": [
                        {
                            "metric": "health_status",
                            "operator": "eq",
                            "threshold": 0,  # 0 = unhealthy
                            "duration_seconds": 60
                        }
                    ],
                    "cooldown_seconds": 300,
                    "notifications": ["pagerduty", "slack", "email", "sms"]
                },
                {
                    "name": "dependency_unavailable",
                    "description": "Critical dependency (Analysis Engine) unavailable",
                    "severity": "error",
                    "conditions": [
                        {
                            "metric": "dependency_health_analysis_engine",
                            "operator": "eq",
                            "threshold": 0,
                            "duration_seconds": 120
                        }
                    ],
                    "cooldown_seconds": 600,
                    "notifications": ["pagerduty", "slack"]
                },
                {
                    "name": "circuit_breaker_open",
                    "description": "Circuit breaker opened for external service",
                    "severity": "warning",
                    "conditions": [
                        {
                            "metric": "circuit_breaker_state",
                            "operator": "eq",
                            "threshold": 1,  # 1 = open
                            "duration_seconds": 0
                        }
                    ],
                    "cooldown_seconds": 300,
                    "notifications": ["slack"]
                }
            ],
            "business_alerts": [
                {
                    "name": "sla_violation_response_time",
                    "description": "SLA violation: P95 response time > 100ms for 30 minutes",
                    "severity": "error",
                    "conditions": [
                        {
                            "metric": "p95_response_time_ms",
                            "operator": "gt",
                            "threshold": 100,
                            "duration_seconds": 1800
                        }
                    ],
                    "cooldown_seconds": 3600,
                    "notifications": ["management", "slack", "email"]
                },
                {
                    "name": "sla_violation_availability",
                    "description": "SLA violation: Availability below 99.9% in last hour",
                    "severity": "critical",
                    "conditions": [
                        {
                            "metric": "availability_1h",
                            "operator": "lt",
                            "threshold": 99.9,
                            "duration_seconds": 0
                        }
                    ],
                    "cooldown_seconds": 3600,
                    "notifications": ["management", "pagerduty", "email"]
                },
                {
                    "name": "cache_ineffective",
                    "description": "Cache hit rate below 60% for 1 hour",
                    "severity": "warning",
                    "conditions": [
                        {
                            "metric": "cache_hit_rate",
                            "operator": "lt",
                            "threshold": 60.0,
                            "duration_seconds": 3600
                        }
                    ],
                    "cooldown_seconds": 7200,
                    "notifications": ["engineering", "slack"]
                }
            ]
        }
        
        # Save alert configurations
        alerts_dir = self.output_dir / "alerts"
        alerts_dir.mkdir(parents=True, exist_ok=True)
        
        # Save as YAML
        with open(alerts_dir / "alert_rules.yaml", 'w') as f:
            yaml.dump(alert_rules, f, default_flow_style=False)
            
        # Save as JSON
        with open(alerts_dir / "alert_rules.json", 'w') as f:
            json.dump(alert_rules, f, indent=2)
            
        # Generate Prometheus alert rules
        prom_rules = self._generate_prometheus_rules(alert_rules)
        with open(alerts_dir / "prometheus_rules.yml", 'w') as f:
            yaml.dump(prom_rules, f, default_flow_style=False)
            
        print(f"  ✓ Generated {sum(len(rules) for rules in alert_rules.values())} alert rules")
        
    def _generate_prometheus_rules(self, alert_rules):
        """Convert alert rules to Prometheus format"""
        groups = []
        
        for group_name, rules in alert_rules.items():
            prom_rules = []
            
            for rule in rules:
                # Convert conditions to PromQL
                condition = rule["conditions"][0]  # Simplify for example
                
                if condition["metric"] == "error_rate":
                    expr = f'(sum(rate(query_intelligence_errors_total[5m])) / sum(rate(query_intelligence_queries_total[5m]))) * 100 > {condition["threshold"]}'
                elif condition["metric"] == "p95_response_time_ms":
                    expr = f'histogram_quantile(0.95, rate(query_intelligence_query_duration_seconds_bucket[5m])) * 1000 > {condition["threshold"]}'
                elif condition["metric"] == "memory_percent":
                    expr = f'(query_intelligence_memory_usage_mb / 4096) * 100 > {condition["threshold"]}'
                else:
                    expr = f'{condition["metric"]} {condition["operator"]} {condition["threshold"]}'
                    
                prom_rule = {
                    "alert": rule["name"],
                    "expr": expr,
                    "for": f"{condition['duration_seconds']}s" if condition['duration_seconds'] > 0 else "0s",
                    "labels": {
                        "severity": rule["severity"],
                        "service": "query-intelligence"
                    },
                    "annotations": {
                        "summary": rule["description"],
                        "description": f"{{ $labels.instance }} {rule['description']}"
                    }
                }
                
                prom_rules.append(prom_rule)
                
            groups.append({
                "name": group_name,
                "interval": "30s",
                "rules": prom_rules
            })
            
        return {"groups": groups}
        
    async def create_documentation(self):
        """Create monitoring documentation"""
        print("\n📚 Creating monitoring documentation...")
        
        docs_dir = self.output_dir / "docs"
        docs_dir.mkdir(parents=True, exist_ok=True)
        
        # Create monitoring guide
        monitoring_guide = """# Query Intelligence Monitoring Guide

## Overview

This guide provides comprehensive information about monitoring the Query Intelligence service.

## Metrics

### Key Performance Indicators (KPIs)

1. **Request Rate (QPS)**
   - Metric: `query_intelligence_queries_total`
   - Target: 1000+ QPS sustained
   - Dashboard: Overview → Request Rate

2. **Response Time**
   - Metric: `query_intelligence_query_duration_seconds`
   - Target: P95 < 100ms, P99 < 500ms
   - Dashboard: Performance → Response Time Distribution

3. **Error Rate**
   - Metric: `query_intelligence_errors_total`
   - Target: < 1%
   - Dashboard: Overview → Error Rate

4. **Cache Hit Rate**
   - Metric: `query_intelligence_cache_hits_total` / `query_intelligence_cache_misses_total`
   - Target: > 70%
   - Dashboard: Cache → Cache Hit Rate Trend

### Resource Metrics

- **CPU Usage**: `query_intelligence_cpu_usage_percent`
- **Memory Usage**: `query_intelligence_memory_usage_mb`
- **Active Queries**: `query_intelligence_active_queries`
- **Queue Size**: `query_intelligence_query_queue_size`

## Alerts

### Critical Alerts (Immediate Action Required)

1. **Service Down**
   - Trigger: Health check failing for 60s
   - Action: Check service logs, restart if necessary

2. **Very High Response Time**
   - Trigger: P95 > 1000ms for 10 minutes
   - Action: Check load, scale up if needed

3. **Memory Critical**
   - Trigger: Memory > 95% for 5 minutes
   - Action: Investigate memory leaks, restart service

### Warning Alerts (Investigation Required)

1. **High Error Rate**
   - Trigger: Error rate > 5% for 5 minutes
   - Action: Check error logs, identify root cause

2. **Low Cache Hit Rate**
   - Trigger: Cache hit rate < 60% for 1 hour
   - Action: Review cache configuration, analyze query patterns

## Dashboards

### Available Dashboards

1. **Overview Dashboard**
   - High-level service health
   - Request rate and success rate
   - Response time trends
   - Active queries and queue size

2. **Performance Dashboard**
   - Detailed response time analysis
   - CPU and memory usage
   - Throughput metrics
   - Confidence score distribution

3. **Cache Performance Dashboard**
   - Cache hit rate trends
   - Cache operations per second
   - Memory usage by cache
   - Eviction patterns

4. **Health & Availability Dashboard**
   - Service availability percentage
   - Dependency health status
   - Circuit breaker states
   - SLA compliance tracking

5. **Business Metrics Dashboard**
   - Active repositories and users
   - Query pattern analysis
   - API usage by endpoint
   - Response size distribution

## Monitoring Endpoints

- **Metrics**: `GET /monitoring/metrics` (Prometheus format)
- **Health Check**: `GET /monitoring/health?detailed=true`
- **Liveness**: `GET /monitoring/health/live`
- **Readiness**: `GET /monitoring/health/ready`
- **Active Alerts**: `GET /monitoring/alerts`
- **Metrics Summary**: `GET /monitoring/metrics/summary`

## Troubleshooting

### High Response Times
1. Check current load: `GET /monitoring/metrics/summary`
2. Review cache hit rate
3. Check dependency health
4. Analyze slow queries in logs

### Memory Issues
1. Check memory metrics trend
2. Review cache size configuration
3. Look for memory leak patterns
4. Check goroutine/task count

### Low Availability
1. Check health endpoint
2. Review dependency status
3. Check circuit breaker states
4. Analyze error logs

## SLA Targets

- **Availability**: 99.9% (43.2 minutes downtime/month)
- **Response Time**: P95 < 100ms, P99 < 500ms
- **Error Rate**: < 1%
- **Throughput**: 1000+ QPS sustained

## Monitoring Best Practices

1. **Set up alerts** for all critical metrics
2. **Review dashboards** daily during business hours
3. **Conduct weekly** performance reviews
4. **Monitor trends** not just current values
5. **Document incidents** and learnings
6. **Test alert rules** regularly
7. **Keep dashboards** up to date
"""
        
        with open(docs_dir / "monitoring_guide.md", 'w') as f:
            f.write(monitoring_guide)
            
        # Create runbook
        runbook = """# Query Intelligence Runbook

## Service Information

- **Service**: Query Intelligence
- **Language**: Python
- **Framework**: FastAPI
- **Dependencies**: Analysis Engine, Pattern Mining Service, Redis, PostgreSQL

## Common Operations

### Restart Service

```bash
# Kubernetes
kubectl rollout restart deployment/query-intelligence -n ccl-platform

# Docker
docker restart query-intelligence

# Local development
./scripts/restart_service.sh
```

### Scale Service

```bash
# Scale up
kubectl scale deployment/query-intelligence --replicas=10 -n ccl-platform

# Scale down
kubectl scale deployment/query-intelligence --replicas=3 -n ccl-platform
```

### Check Logs

```bash
# Kubernetes
kubectl logs -f deployment/query-intelligence -n ccl-platform

# Docker
docker logs -f query-intelligence

# Local
tail -f logs/query_intelligence.log
```

## Alert Response Procedures

### Service Down

1. **Verify** the alert is accurate
   ```bash
   curl https://query-intelligence.ccl-platform.com/monitoring/health
   ```

2. **Check** pod/container status
   ```bash
   kubectl get pods -n ccl-platform | grep query-intelligence
   ```

3. **Review** recent deployments
   ```bash
   kubectl rollout history deployment/query-intelligence -n ccl-platform
   ```

4. **Restart** if necessary
   ```bash
   kubectl rollout restart deployment/query-intelligence -n ccl-platform
   ```

5. **Monitor** recovery
   - Watch health endpoint
   - Check metrics dashboard
   - Verify dependencies

### High Memory Usage

1. **Check** current memory usage
   ```bash
   kubectl top pods -n ccl-platform | grep query-intelligence
   ```

2. **Analyze** memory profile
   - Check cache sizes
   - Review active connections
   - Look for memory leaks

3. **Take action**
   - Clear caches if safe
   - Restart pods in rolling fashion
   - Scale horizontally if needed

4. **Monitor** after action
   - Watch memory metrics
   - Check for recurrence

### High Error Rate

1. **Identify** error types
   ```bash
   kubectl logs deployment/query-intelligence -n ccl-platform | grep ERROR | tail -100
   ```

2. **Check** dependencies
   - Analysis Engine health
   - Pattern Mining health
   - Database connectivity
   - Redis availability

3. **Analyze** error patterns
   - Specific endpoints affected?
   - Particular repositories?
   - Time correlation?

4. **Mitigate**
   - Enable circuit breakers
   - Reduce load if needed
   - Fix identified issues

### SLA Violation

1. **Assess** impact
   - Duration of violation
   - Number of affected users
   - Business impact

2. **Immediate** actions
   - Scale up if load-related
   - Check cache effectiveness
   - Review recent changes

3. **Communicate**
   - Update status page
   - Notify stakeholders
   - Document in incident tracker

4. **Post-incident**
   - Root cause analysis
   - Update monitoring
   - Improve automation

## Performance Tuning

### Cache Optimization

```python
# Adjust cache TTL
CACHE_TTL = 3600  # Increase for stable content

# Increase cache size
CACHE_MAX_MEMORY_MB = 2048  # Up from 1024

# Enable predictive caching
ENABLE_PREDICTIVE_CACHE = true
```

### Connection Pooling

```python
# Database connections
DATABASE_POOL_SIZE = 20
DATABASE_MAX_OVERFLOW = 10

# Redis connections  
REDIS_MAX_CONNECTIONS = 50

# HTTP client connections
HTTPX_MAX_CONNECTIONS = 100
```

### Query Optimization

1. **Analyze** slow queries
2. **Add** appropriate indexes
3. **Optimize** context window size
4. **Enable** query result streaming

## Deployment Procedures

### Rolling Update

```bash
# Update image
kubectl set image deployment/query-intelligence query-intelligence=gcr.io/project/query-intelligence:v1.2.3 -n ccl-platform

# Monitor rollout
kubectl rollout status deployment/query-intelligence -n ccl-platform
```

### Rollback

```bash
# Rollback to previous version
kubectl rollout undo deployment/query-intelligence -n ccl-platform

# Rollback to specific revision
kubectl rollout undo deployment/query-intelligence --to-revision=2 -n ccl-platform
```

## Contact Information

- **On-Call**: Use PagerDuty
- **Slack**: #query-intelligence-ops
- **Email**: <EMAIL>
"""
        
        with open(docs_dir / "runbook.md", 'w') as f:
            f.write(runbook)
            
        print("  ✓ Created monitoring guide and runbook")
        
    async def generate_deployment_configs(self):
        """Generate deployment configurations"""
        print("\n🚀 Generating deployment configurations...")
        
        deploy_dir = self.output_dir / "deployment"
        deploy_dir.mkdir(parents=True, exist_ok=True)
        
        # Prometheus configuration
        prometheus_config = """global:
  scrape_interval: 15s
  evaluation_interval: 15s

alerting:
  alertmanagers:
    - static_configs:
        - targets:
            - alertmanager:9093

rule_files:
  - /etc/prometheus/rules/*.yml

scrape_configs:
  - job_name: 'query-intelligence'
    metrics_path: '/monitoring/metrics'
    static_configs:
      - targets: ['query-intelligence:8002']
        labels:
          service: 'query-intelligence'
          environment: 'production'
    scrape_interval: 30s
    scrape_timeout: 10s
"""
        
        with open(deploy_dir / "prometheus.yml", 'w') as f:
            f.write(prometheus_config)
            
        # Alertmanager configuration
        alertmanager_config = """global:
  resolve_timeout: 5m

route:
  group_by: ['alertname', 'cluster', 'service']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 12h
  receiver: 'default'
  routes:
    - match:
        severity: critical
      receiver: pagerduty
      continue: true
    - match:
        severity: error
      receiver: slack
      continue: true
    - match:
        severity: warning
      receiver: slack

receivers:
  - name: 'default'
    # Default receiver (could be email, webhook, etc.)
    
  - name: 'pagerduty'
    pagerduty_configs:
      - service_key: 'YOUR_PAGERDUTY_SERVICE_KEY'
        description: 'Query Intelligence Alert: {{ .GroupLabels.alertname }}'
        
  - name: 'slack'
    slack_configs:
      - api_url: 'YOUR_SLACK_WEBHOOK_URL'
        channel: '#query-intelligence-alerts'
        title: 'Query Intelligence Alert'
        text: '{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
"""
        
        with open(deploy_dir / "alertmanager.yml", 'w') as f:
            f.write(alertmanager_config)
            
        # Kubernetes ServiceMonitor
        service_monitor = """apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: query-intelligence
  namespace: ccl-platform
  labels:
    app: query-intelligence
    prometheus: kube-prometheus
spec:
  selector:
    matchLabels:
      app: query-intelligence
  endpoints:
    - port: http
      path: /monitoring/metrics
      interval: 30s
      scrapeTimeout: 10s
"""
        
        with open(deploy_dir / "service-monitor.yaml", 'w') as f:
            f.write(service_monitor)
            
        # Docker Compose for local monitoring
        docker_compose = """version: '3.8'

services:
  prometheus:
    image: prom/prometheus:latest
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - ../alerts/prometheus_rules.yml:/etc/prometheus/rules/alerts.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/usr/share/prometheus/console_libraries'
      - '--web.console.templates=/usr/share/prometheus/consoles'
    ports:
      - "9090:9090"
    networks:
      - monitoring

  grafana:
    image: grafana/grafana:latest
    volumes:
      - grafana_data:/var/lib/grafana
      - ../dashboards/grafana:/etc/grafana/provisioning/dashboards
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    ports:
      - "3000:3000"
    networks:
      - monitoring

  alertmanager:
    image: prom/alertmanager:latest
    volumes:
      - ./alertmanager.yml:/etc/alertmanager/alertmanager.yml
      - alertmanager_data:/alertmanager
    command:
      - '--config.file=/etc/alertmanager/alertmanager.yml'
      - '--storage.path=/alertmanager'
    ports:
      - "9093:9093"
    networks:
      - monitoring

volumes:
  prometheus_data:
  grafana_data:
  alertmanager_data:

networks:
  monitoring:
    driver: bridge
"""
        
        with open(deploy_dir / "docker-compose.monitoring.yml", 'w') as f:
            f.write(docker_compose)
            
        print("  ✓ Generated deployment configurations")
        
    async def create_readme(self):
        """Create README for monitoring setup"""
        readme = f"""# Query Intelligence Monitoring

Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## Directory Structure

```
monitoring/
├── alerts/                    # Alert rule configurations
│   ├── alert_rules.yaml      # Human-readable alert rules
│   ├── alert_rules.json      # JSON format alert rules
│   └── prometheus_rules.yml  # Prometheus-compatible rules
├── dashboards/               # Dashboard configurations
│   ├── grafana/             # Grafana dashboard JSONs
│   ├── datadog/             # Datadog dashboard configs
│   ├── cloudwatch/          # AWS CloudWatch dashboards
│   └── custom/              # Generic dashboard format
├── deployment/              # Deployment configurations
│   ├── prometheus.yml       # Prometheus configuration
│   ├── alertmanager.yml     # Alertmanager configuration
│   ├── service-monitor.yaml # Kubernetes ServiceMonitor
│   └── docker-compose.monitoring.yml
└── docs/                    # Documentation
    ├── monitoring_guide.md  # Comprehensive monitoring guide
    └── runbook.md          # Operational runbook
```

## Quick Start

### Local Monitoring Stack

```bash
cd monitoring/deployment
docker-compose -f docker-compose.monitoring.yml up -d
```

Access:
- Prometheus: http://localhost:9090
- Grafana: http://localhost:3000 (admin/admin)
- Alertmanager: http://localhost:9093

### Import Dashboards to Grafana

1. Login to Grafana
2. Go to Dashboards → Import
3. Upload JSON files from `dashboards/grafana/`

### Deploy to Kubernetes

```bash
# Apply ServiceMonitor
kubectl apply -f deployment/service-monitor.yaml

# Create ConfigMap with alert rules
kubectl create configmap prometheus-rules \\
  --from-file=alerts/prometheus_rules.yml \\
  -n monitoring
```

## Key Metrics

- **Request Rate**: `query_intelligence_queries_total`
- **Response Time**: `query_intelligence_query_duration_seconds`
- **Error Rate**: `query_intelligence_errors_total`
- **Cache Hit Rate**: `query_intelligence_cache_hits_total`

## Support

For questions or issues, see the monitoring guide in `docs/monitoring_guide.md`
"""
        
        with open(self.output_dir / "README.md", 'w') as f:
            f.write(readme)


async def main():
    """Main execution"""
    setup = MonitoringSetup()
    await setup.setup_all()
    await setup.create_readme()


if __name__ == "__main__":
    asyncio.run(main()