#!/usr/bin/env python3
"""
Production Deployment Script for Query Intelligence Service

Handles automated deployment with health checks, rollback capabilities,
and zero-downtime deployment strategies.
"""

import os
import sys
import time
import json
import subprocess
import argparse
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime
import requests
import yaml
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.query_intelligence.config.config_manager import ConfigEnvironment


class DeploymentError(Exception):
    """Deployment-specific errors"""
    pass


class ProductionDeployer:
    """Handles production deployment of Query Intelligence service"""
    
    def __init__(self, environment: str = "production"):
        self.environment = ConfigEnvironment(environment)
        self.deployment_id = f"deploy_{int(time.time())}"
        self.health_check_retries = 30
        self.health_check_interval = 10
        self.rollback_enabled = True
        
        # Load deployment configuration
        self.config = self._load_deployment_config()
        
        # Deployment state
        self.previous_version: Optional[str] = None
        self.current_version: Optional[str] = None
        self.deployment_start: Optional[float] = None
        
        print(f"🚀 Production Deployer initialized")
        print(f"   Environment: {self.environment.value}")
        print(f"   Deployment ID: {self.deployment_id}")
        
    def _load_deployment_config(self) -> Dict[str, Any]:
        """Load deployment configuration"""
        config_path = Path("config/deployment.yaml")
        
        if not config_path.exists():
            # Default configuration
            return {
                "service_name": "query-intelligence",
                "docker_registry": "gcr.io/episteme-prod",
                "kubernetes_namespace": "episteme",
                "replicas": {
                    "production": 3,
                    "staging": 2
                },
                "resources": {
                    "requests": {
                        "cpu": "500m",
                        "memory": "1Gi"
                    },
                    "limits": {
                        "cpu": "2000m",
                        "memory": "4Gi"
                    }
                },
                "health_check": {
                    "path": "/health",
                    "port": 8001,
                    "initial_delay": 30,
                    "period": 10,
                    "timeout": 5,
                    "success_threshold": 1,
                    "failure_threshold": 3
                },
                "rolling_update": {
                    "max_surge": 1,
                    "max_unavailable": 0
                }
            }
            
        with open(config_path, 'r') as f:
            return yaml.safe_load(f)
            
    def validate_prerequisites(self):
        """Validate deployment prerequisites"""
        print("\n📋 Validating prerequisites...")
        
        # Check Docker
        if not self._check_command("docker", "--version"):
            raise DeploymentError("Docker is not installed or not in PATH")
            
        # Check kubectl
        if not self._check_command("kubectl", "version", "--client"):
            raise DeploymentError("kubectl is not installed or not in PATH")
            
        # Check gcloud (for GCP deployments)
        if self.config["docker_registry"].startswith("gcr.io"):
            if not self._check_command("gcloud", "--version"):
                raise DeploymentError("gcloud SDK is not installed or not in PATH")
                
        # Validate Kubernetes connection
        try:
            result = subprocess.run(
                ["kubectl", "get", "namespace", self.config["kubernetes_namespace"]],
                capture_output=True,
                text=True
            )
            if result.returncode != 0:
                raise DeploymentError(f"Kubernetes namespace '{self.config['kubernetes_namespace']}' not found")
        except Exception as e:
            raise DeploymentError(f"Cannot connect to Kubernetes cluster: {e}")
            
        print("✅ All prerequisites validated")
        
    def _check_command(self, command: str, *args) -> bool:
        """Check if a command is available"""
        try:
            subprocess.run([command, *args], capture_output=True)
            return True
        except FileNotFoundError:
            return False
            
    def build_docker_image(self, version: str) -> str:
        """Build Docker image"""
        print(f"\n🔨 Building Docker image (version: {version})...")
        
        image_tag = f"{self.config['docker_registry']}/{self.config['service_name']}:{version}"
        
        # Build image
        build_args = [
            "docker", "build",
            "-t", image_tag,
            "-f", "Dockerfile",
            "--build-arg", f"VERSION={version}",
            "--build-arg", f"BUILD_ID={self.deployment_id}",
            "--build-arg", f"BUILD_TIME={datetime.utcnow().isoformat()}",
            "."
        ]
        
        result = subprocess.run(build_args, capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"❌ Docker build failed:\n{result.stderr}")
            raise DeploymentError("Docker build failed")
            
        print(f"✅ Docker image built: {image_tag}")
        return image_tag
        
    def push_docker_image(self, image_tag: str):
        """Push Docker image to registry"""
        print(f"\n📤 Pushing Docker image to registry...")
        
        # Authenticate with registry if needed
        if self.config["docker_registry"].startswith("gcr.io"):
            auth_result = subprocess.run(
                ["gcloud", "auth", "configure-docker"],
                capture_output=True
            )
            if auth_result.returncode != 0:
                raise DeploymentError("Failed to authenticate with Docker registry")
                
        # Push image
        push_result = subprocess.run(
            ["docker", "push", image_tag],
            capture_output=True,
            text=True
        )
        
        if push_result.returncode != 0:
            print(f"❌ Docker push failed:\n{push_result.stderr}")
            raise DeploymentError("Docker push failed")
            
        print(f"✅ Docker image pushed successfully")
        
    def get_current_deployment(self) -> Optional[Dict[str, Any]]:
        """Get current deployment information"""
        result = subprocess.run(
            [
                "kubectl", "get", "deployment",
                self.config["service_name"],
                "-n", self.config["kubernetes_namespace"],
                "-o", "json"
            ],
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            return json.loads(result.stdout)
        return None
        
    def deploy_to_kubernetes(self, image_tag: str):
        """Deploy to Kubernetes with rolling update"""
        print(f"\n🚢 Deploying to Kubernetes...")
        
        # Get current deployment
        current_deployment = self.get_current_deployment()
        
        if current_deployment:
            # Store previous version for rollback
            containers = current_deployment["spec"]["template"]["spec"]["containers"]
            if containers:
                self.previous_version = containers[0]["image"].split(":")[-1]
                
        # Generate Kubernetes manifest
        manifest = self._generate_kubernetes_manifest(image_tag)
        
        # Apply manifest
        apply_result = subprocess.run(
            ["kubectl", "apply", "-f", "-"],
            input=json.dumps(manifest),
            capture_output=True,
            text=True
        )
        
        if apply_result.returncode != 0:
            print(f"❌ Kubernetes deployment failed:\n{apply_result.stderr}")
            raise DeploymentError("Kubernetes deployment failed")
            
        print("✅ Kubernetes deployment initiated")
        
        # Wait for rollout to complete
        self._wait_for_rollout()
        
    def _generate_kubernetes_manifest(self, image_tag: str) -> Dict[str, Any]:
        """Generate Kubernetes deployment manifest"""
        replicas = self.config["replicas"].get(
            self.environment.value,
            self.config["replicas"].get("production", 3)
        )
        
        return {
            "apiVersion": "apps/v1",
            "kind": "Deployment",
            "metadata": {
                "name": self.config["service_name"],
                "namespace": self.config["kubernetes_namespace"],
                "labels": {
                    "app": self.config["service_name"],
                    "version": image_tag.split(":")[-1],
                    "deployment-id": self.deployment_id
                }
            },
            "spec": {
                "replicas": replicas,
                "selector": {
                    "matchLabels": {
                        "app": self.config["service_name"]
                    }
                },
                "strategy": {
                    "type": "RollingUpdate",
                    "rollingUpdate": self.config["rolling_update"]
                },
                "template": {
                    "metadata": {
                        "labels": {
                            "app": self.config["service_name"],
                            "version": image_tag.split(":")[-1]
                        }
                    },
                    "spec": {
                        "containers": [{
                            "name": self.config["service_name"],
                            "image": image_tag,
                            "ports": [{
                                "containerPort": self.config["health_check"]["port"]
                            }],
                            "resources": self.config["resources"],
                            "livenessProbe": {
                                "httpGet": {
                                    "path": self.config["health_check"]["path"],
                                    "port": self.config["health_check"]["port"]
                                },
                                "initialDelaySeconds": self.config["health_check"]["initial_delay"],
                                "periodSeconds": self.config["health_check"]["period"],
                                "timeoutSeconds": self.config["health_check"]["timeout"],
                                "successThreshold": self.config["health_check"]["success_threshold"],
                                "failureThreshold": self.config["health_check"]["failure_threshold"]
                            },
                            "readinessProbe": {
                                "httpGet": {
                                    "path": self.config["health_check"]["path"],
                                    "port": self.config["health_check"]["port"]
                                },
                                "initialDelaySeconds": 10,
                                "periodSeconds": 5
                            },
                            "env": [
                                {
                                    "name": "ENVIRONMENT",
                                    "value": self.environment.value
                                },
                                {
                                    "name": "DEPLOYMENT_ID",
                                    "value": self.deployment_id
                                }
                            ]
                        }]
                    }
                }
            }
        }
        
    def _wait_for_rollout(self):
        """Wait for Kubernetes rollout to complete"""
        print("\n⏳ Waiting for rollout to complete...")
        
        rollout_cmd = [
            "kubectl", "rollout", "status",
            f"deployment/{self.config['service_name']}",
            "-n", self.config["kubernetes_namespace"],
            "--timeout=10m"
        ]
        
        result = subprocess.run(rollout_cmd, capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"❌ Rollout failed:\n{result.stderr}")
            
            if self.rollback_enabled and self.previous_version:
                print("\n🔄 Initiating automatic rollback...")
                self.rollback()
            
            raise DeploymentError("Rollout failed")
            
        print("✅ Rollout completed successfully")
        
    def verify_deployment(self) -> bool:
        """Verify deployment health"""
        print("\n🏥 Verifying deployment health...")
        
        # Get pod IPs
        pods_result = subprocess.run(
            [
                "kubectl", "get", "pods",
                "-l", f"app={self.config['service_name']}",
                "-n", self.config["kubernetes_namespace"],
                "-o", "json"
            ],
            capture_output=True,
            text=True
        )
        
        if pods_result.returncode != 0:
            print("❌ Failed to get pod information")
            return False
            
        pods = json.loads(pods_result.stdout)["items"]
        
        if not pods:
            print("❌ No pods found")
            return False
            
        # Check each pod
        healthy_pods = 0
        total_pods = len(pods)
        
        for pod in pods:
            pod_name = pod["metadata"]["name"]
            pod_ip = pod["status"].get("podIP")
            
            if not pod_ip:
                print(f"⚠️  Pod {pod_name} has no IP assigned")
                continue
                
            # Perform health check
            if self._check_pod_health(pod_name, pod_ip):
                healthy_pods += 1
                print(f"✅ Pod {pod_name} is healthy")
            else:
                print(f"❌ Pod {pod_name} is unhealthy")
                
        print(f"\n📊 Health Summary: {healthy_pods}/{total_pods} pods healthy")
        
        # Require all pods to be healthy
        return healthy_pods == total_pods
        
    def _check_pod_health(self, pod_name: str, pod_ip: str) -> bool:
        """Check individual pod health"""
        health_url = f"http://{pod_ip}:{self.config['health_check']['port']}{self.config['health_check']['path']}"
        
        try:
            response = requests.get(health_url, timeout=5)
            return response.status_code == 200
        except Exception as e:
            print(f"   Error checking pod {pod_name}: {e}")
            return False
            
    def rollback(self):
        """Rollback to previous version"""
        if not self.previous_version:
            print("⚠️  No previous version to rollback to")
            return
            
        print(f"\n🔄 Rolling back to version: {self.previous_version}")
        
        rollback_result = subprocess.run(
            [
                "kubectl", "rollout", "undo",
                f"deployment/{self.config['service_name']}",
                "-n", self.config["kubernetes_namespace"]
            ],
            capture_output=True,
            text=True
        )
        
        if rollback_result.returncode != 0:
            print(f"❌ Rollback failed:\n{rollback_result.stderr}")
            raise DeploymentError("Rollback failed")
            
        # Wait for rollback to complete
        self._wait_for_rollout()
        
        print("✅ Rollback completed successfully")
        
    def run_smoke_tests(self):
        """Run smoke tests against deployed service"""
        print("\n🔥 Running smoke tests...")
        
        # Get service endpoint
        service_result = subprocess.run(
            [
                "kubectl", "get", "service",
                self.config["service_name"],
                "-n", self.config["kubernetes_namespace"],
                "-o", "json"
            ],
            capture_output=True,
            text=True
        )
        
        if service_result.returncode != 0:
            print("⚠️  Could not get service endpoint for smoke tests")
            return
            
        service = json.loads(service_result.stdout)
        
        # Run basic smoke tests
        tests_passed = 0
        tests_total = 0
        
        # Test 1: Health check
        tests_total += 1
        if self._test_health_endpoint(service):
            tests_passed += 1
            print("✅ Health check passed")
        else:
            print("❌ Health check failed")
            
        # Test 2: API version
        tests_total += 1
        if self._test_api_version(service):
            tests_passed += 1
            print("✅ API version check passed")
        else:
            print("❌ API version check failed")
            
        # Test 3: Basic query
        tests_total += 1
        if self._test_basic_query(service):
            tests_passed += 1
            print("✅ Basic query test passed")
        else:
            print("❌ Basic query test failed")
            
        print(f"\n📊 Smoke Test Results: {tests_passed}/{tests_total} passed")
        
        if tests_passed < tests_total:
            print("⚠️  Some smoke tests failed")
            
    def _test_health_endpoint(self, service: Dict[str, Any]) -> bool:
        """Test health endpoint"""
        # Implementation depends on your service exposure method
        # This is a placeholder
        return True
        
    def _test_api_version(self, service: Dict[str, Any]) -> bool:
        """Test API version endpoint"""
        # Implementation depends on your service exposure method
        # This is a placeholder
        return True
        
    def _test_basic_query(self, service: Dict[str, Any]) -> bool:
        """Test basic query functionality"""
        # Implementation depends on your service exposure method
        # This is a placeholder
        return True
        
    def deploy(self, version: str):
        """Execute full deployment pipeline"""
        self.deployment_start = time.time()
        self.current_version = version
        
        try:
            print(f"\n{'='*60}")
            print(f"🚀 Starting deployment of Query Intelligence Service")
            print(f"   Version: {version}")
            print(f"   Environment: {self.environment.value}")
            print(f"   Time: {datetime.now().isoformat()}")
            print(f"{'='*60}")
            
            # 1. Validate prerequisites
            self.validate_prerequisites()
            
            # 2. Build Docker image
            image_tag = self.build_docker_image(version)
            
            # 3. Push to registry
            self.push_docker_image(image_tag)
            
            # 4. Deploy to Kubernetes
            self.deploy_to_kubernetes(image_tag)
            
            # 5. Verify deployment
            if not self.verify_deployment():
                raise DeploymentError("Deployment verification failed")
                
            # 6. Run smoke tests
            self.run_smoke_tests()
            
            # Calculate deployment time
            deployment_time = time.time() - self.deployment_start
            
            print(f"\n{'='*60}")
            print(f"✅ Deployment completed successfully!")
            print(f"   Version: {version}")
            print(f"   Duration: {deployment_time:.1f} seconds")
            print(f"   Deployment ID: {self.deployment_id}")
            print(f"{'='*60}")
            
            # Log deployment
            self._log_deployment(version, "success", deployment_time)
            
        except Exception as e:
            deployment_time = time.time() - self.deployment_start
            
            print(f"\n{'='*60}")
            print(f"❌ Deployment failed!")
            print(f"   Error: {str(e)}")
            print(f"   Duration: {deployment_time:.1f} seconds")
            print(f"{'='*60}")
            
            # Log deployment failure
            self._log_deployment(version, "failed", deployment_time, str(e))
            
            raise
            
    def _log_deployment(self, 
                       version: str,
                       status: str,
                       duration: float,
                       error: Optional[str] = None):
        """Log deployment information"""
        log_entry = {
            "deployment_id": self.deployment_id,
            "timestamp": datetime.utcnow().isoformat(),
            "version": version,
            "environment": self.environment.value,
            "status": status,
            "duration_seconds": duration,
            "error": error
        }
        
        # Write to deployment log
        log_file = Path("deployments.log")
        with open(log_file, "a") as f:
            f.write(json.dumps(log_entry) + "\n")


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description="Deploy Query Intelligence Service to production"
    )
    parser.add_argument(
        "version",
        help="Version to deploy (e.g., v1.2.3)"
    )
    parser.add_argument(
        "--environment",
        default="production",
        choices=["staging", "production"],
        help="Deployment environment"
    )
    parser.add_argument(
        "--no-rollback",
        action="store_true",
        help="Disable automatic rollback on failure"
    )
    
    args = parser.parse_args()
    
    try:
        deployer = ProductionDeployer(args.environment)
        
        if args.no_rollback:
            deployer.rollback_enabled = False
            
        deployer.deploy(args.version)
        
    except KeyboardInterrupt:
        print("\n\n⚠️  Deployment interrupted by user")
        sys.exit(1)
    except DeploymentError as e:
        print(f"\n❌ Deployment error: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()