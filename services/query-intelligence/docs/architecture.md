# Query Intelligence Service - Architecture Documentation

> **📋 Note**: This service architecture documentation has been consolidated into the main project documentation for better organization and maintenance.

## Consolidated Architecture Documentation

For the comprehensive Query Intelligence Service architecture documentation, please see:

**📐 [Main Architecture Documentation](../../../docs/query-intelligence/architecture/README.md)**

### Complete Documentation Navigation
- **[System Design](../../../docs/query-intelligence/architecture/README.md)** - Comprehensive architecture documentation
- **[API Reference](../../../docs/query-intelligence/api/README.md)** - Complete API documentation
- **[Performance Documentation](../../../docs/query-intelligence/performance/README.md)** - Performance targets and optimization
- **[Operations Guide](../../../docs/query-intelligence/operations/runbook.md)** - Production operations
- **[Service Overview](../../../docs/query-intelligence/README.md)** - Main documentation hub

This consolidated documentation includes:

- **Complete System Architecture** - High-level architecture diagrams and component details
- **Core Components** - Query processor, AI response generator, semantic cache, WebSocket streaming
- **Resilience Architecture** - Circuit breakers, rate limiting, retry strategies, bulkhead isolation
- **Scalability & Performance** - Horizontal scaling, performance targets, caching strategies
- **Security Architecture** - Defense in depth, authentication flows, threat mitigation
- **Monitoring Architecture** - Metrics collection, distributed tracing, key performance indicators
- **Deployment Architecture** - Cloud Run configuration, multi-region strategy, container strategy
- **Integration Points** - External service integrations and event schemas
- **Future Architecture Considerations** - Multi-region deployment, event-driven architecture, ML pipeline integration

## Quick Reference

### Service Structure
```
services/query-intelligence/
├── src/query_intelligence/
│   ├── api/           # FastAPI controllers and WebSocket handlers
│   ├── services/      # Business logic and AI integration
│   ├── models/        # Data models and schemas
│   └── utils/         # Utility functions and helpers
├── tests/             # Unit and integration tests
├── docs/              # Service-specific documentation
└── deployment/        # Deployment configurations
```

### Key Technologies
- **Language**: Python 3.11+
- **Framework**: FastAPI (async web framework)
- **AI/ML**: Google GenAI SDK (Gemini 2.5 models)
- **Cache**: Redis (distributed caching)
- **Container**: Docker with Cloud Run
- **Monitoring**: Cloud Logging, Prometheus, Grafana

### Performance Targets
- **Latency**: p50 <100ms, p95 <200ms, p99 <500ms
- **Throughput**: 1000 QPS sustained, 2000 QPS peak
- **Resource Usage**: <70% CPU, <2GB memory per instance

---

**Version**: 1.1.0  
**Last Updated**: July 2025  
**Consolidated**: Architecture documentation centralized to main docs