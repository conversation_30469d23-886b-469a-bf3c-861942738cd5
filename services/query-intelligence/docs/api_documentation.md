# Query Intelligence Service - API Documentation

> **📍 Moved to Main Documentation**
> 
> This API documentation has been consolidated into the main project documentation for better maintenance and consistency.

## 🔗 Current API Documentation

**Please refer to the comprehensive API documentation at:**

**[/docs/query-intelligence/api/README.md](../../../docs/query-intelligence/api/README.md)**

## ✅ What's Available

The consolidated documentation includes:

### 🚀 **Live Production Service**
- **Base URL**: `https://query-intelligence-l3nxty7oka-uc.a.run.app`
- **Status**: ✅ **OPERATIONAL** since July 2025
- **Health Check**: `{"status":"healthy","timestamp":"2025-07-14T12:00:00Z"}`

### 📋 **Complete API Reference**
- **Core Endpoints**: Query processing, history, feedback
- **Health & Monitoring**: Health checks, metrics, SLA status
- **Authentication**: JWT tokens, Firebase Auth integration
- **WebSocket API**: Real-time streaming, live updates
- **Error Handling**: Comprehensive error codes and responses
- **Rate Limiting**: Tier-based limits and quota management

### 💻 **Production-Ready Examples**
- **Python Client**: Complete client library with error handling
- **TypeScript/JavaScript**: Async/await patterns and WebSocket streaming
- **cURL Examples**: Ready-to-use command-line examples
- **Best Practices**: Security, performance, and integration guidelines

### 🔧 **Operational Features**
- **Query History**: `GET /api/v1/queries` with pagination
- **Feedback System**: `POST /api/v1/query/{query_id}/feedback`
- **Prometheus Metrics**: `GET /metrics` for monitoring
- **SLA Compliance**: `GET /api/v1/sla/status` for operational tracking
- **Kubernetes Probes**: Health, readiness, and liveness endpoints

## 🔗 Quick Links

- **[Main API Documentation](../../../docs/query-intelligence/api/README.md)** - Complete reference
- **[WebSocket API Documentation](../../../docs/query-intelligence/api/websocket-api.md)** - Real-time streaming API
- **[Integration Guide](../../../docs/query-intelligence/guides/integration-guide.md)** - Implementation patterns
- **[Service Overview](../../../docs/query-intelligence/README.md)** - Service architecture
- **[Developer Guide](../../../docs/query-intelligence/guides/developer-guide.md)** - Development setup

---

**This redirect ensures you always have access to the most current and comprehensive API documentation.**