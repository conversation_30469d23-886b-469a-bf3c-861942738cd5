"""Contract tests for Pattern Mining service integration."""

import pytest
from typing import Dict, List, Any, Optional
from pydantic import BaseModel, Field, ValidationError
from datetime import datetime
from enum import Enum


# Pattern Mining Contract Models
class PatternType(str, Enum):
    """Supported pattern types."""
    DESIGN_PATTERN = "design_pattern"
    ANTI_PATTERN = "anti_pattern"
    CODE_SMELL = "code_smell"
    BEST_PRACTICE = "best_practice"
    PERFORMANCE = "performance"
    SECURITY = "security"


class CodeChunkInput(BaseModel):
    """Contract for code chunk input to pattern detection."""
    id: str
    content: str = Field(..., min_length=1)
    file_path: str
    language: str
    ast_data: Optional[Dict[str, Any]] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)


class DetectPatternsRequest(BaseModel):
    """Contract for pattern detection request."""
    code_chunks: List[CodeChunkInput] = Field(..., min_items=1, max_items=100)
    context: Dict[str, Any] = Field(default_factory=dict)
    detection_mode: str = Field(default="comprehensive", pattern="^(fast|comprehensive|deep)$")


class Pattern(BaseModel):
    """Contract for detected pattern."""
    id: str
    type: PatternType
    name: str
    description: str
    confidence: float = Field(ge=0.0, le=1.0)
    occurrences: List[Dict[str, Any]] = Field(min_items=1)
    metadata: Dict[str, Any] = Field(default_factory=dict)
    recommendations: List[str] = Field(default_factory=list)


class DetectPatternsResponse(BaseModel):
    """Contract for pattern detection response."""
    patterns: List[Pattern]
    analysis_metadata: Dict[str, Any] = Field(default_factory=dict)
    processing_time_ms: int = Field(ge=0)


class ClassifyPatternsRequest(BaseModel):
    """Contract for pattern classification request."""
    patterns: List[Dict[str, Any]] = Field(..., min_items=1, max_items=50)
    classification_type: str = Field(default="design_pattern")


class ClassifiedPattern(BaseModel):
    """Contract for classified pattern."""
    pattern_id: str
    category: str
    subcategory: Optional[str] = None
    confidence: float = Field(ge=0.0, le=1.0)
    tags: List[str] = Field(default_factory=list)
    related_patterns: List[str] = Field(default_factory=list)


class ClassifyPatternsResponse(BaseModel):
    """Contract for pattern classification response."""
    classified_patterns: List[ClassifiedPattern]
    classification_metadata: Dict[str, Any] = Field(default_factory=dict)


class CodeContext(BaseModel):
    """Contract for code context in recommendations."""
    repository_id: str
    file_path: Optional[str] = None
    language: str
    framework: Optional[str] = None
    dependencies: List[str] = Field(default_factory=list)
    metrics: Dict[str, Any] = Field(default_factory=dict)


class RecommendationsRequest(BaseModel):
    """Contract for pattern recommendations request."""
    code_context: CodeContext
    intent: str = Field(..., min_length=1)
    limit: int = Field(default=5, ge=1, le=20)


class PatternRecommendation(BaseModel):
    """Contract for pattern recommendation."""
    pattern_id: str
    pattern_name: str
    pattern_type: PatternType
    relevance_score: float = Field(ge=0.0, le=1.0)
    explanation: str
    implementation_guide: str
    example_code: Optional[str] = None
    benefits: List[str] = Field(default_factory=list)
    considerations: List[str] = Field(default_factory=list)


class RecommendationsResponse(BaseModel):
    """Contract for pattern recommendations response."""
    recommendations: List[PatternRecommendation]
    context_analysis: Dict[str, Any] = Field(default_factory=dict)


class CodeQualityRequest(BaseModel):
    """Contract for code quality analysis request."""
    repository_id: str = Field(..., min_length=1)
    file_patterns: List[str] = Field(default_factory=list)


class QualityMetrics(BaseModel):
    """Contract for quality metrics."""
    maintainability_index: float = Field(ge=0.0, le=100.0)
    cyclomatic_complexity: float = Field(ge=0.0)
    code_duplication: float = Field(ge=0.0, le=100.0)
    test_coverage: Optional[float] = Field(default=None, ge=0.0, le=100.0)
    documentation_coverage: float = Field(ge=0.0, le=100.0)
    security_score: float = Field(ge=0.0, le=100.0)


class PatternUsage(BaseModel):
    """Contract for pattern usage statistics."""
    pattern_type: PatternType
    pattern_name: str
    count: int = Field(ge=0)
    files: List[str]
    quality_impact: str = Field(pattern="^(positive|negative|neutral)$")


class CodeQualityResponse(BaseModel):
    """Contract for code quality analysis response."""
    repository_id: str
    quality_metrics: QualityMetrics
    pattern_usage: List[PatternUsage]
    recommendations: List[str]
    analysis_timestamp: datetime
    files_analyzed: int = Field(ge=0)
    issues_found: Dict[str, int] = Field(default_factory=dict)


class TestPatternMiningContract:
    """Test Pattern Mining API contract compliance."""

    def test_detect_patterns_request_contract(self):
        """Test pattern detection request contract validation."""
        # Valid request
        chunks = [
            CodeChunkInput(
                id="chunk-1",
                content="def process_data(data):\n    return data",
                file_path="src/processor.py",
                language="python",
                ast_data={"type": "function", "name": "process_data"},
                metadata={"lines": 2}
            )
        ]
        
        valid_request = DetectPatternsRequest(
            code_chunks=chunks,
            context={"repository": "test-repo"},
            detection_mode="comprehensive"
        )
        assert len(valid_request.code_chunks) == 1
        
        # Invalid: empty code chunks
        with pytest.raises(ValidationError):
            DetectPatternsRequest(code_chunks=[])
        
        # Invalid: too many chunks
        with pytest.raises(ValidationError):
            DetectPatternsRequest(
                code_chunks=[
                    CodeChunkInput(
                        id=f"chunk-{i}",
                        content="code",
                        file_path="file.py",
                        language="python"
                    ) for i in range(101)  # Exceeds max
                ]
            )
        
        # Invalid: empty content
        with pytest.raises(ValidationError):
            DetectPatternsRequest(
                code_chunks=[
                    CodeChunkInput(
                        id="chunk-1",
                        content="",  # Empty not allowed
                        file_path="file.py",
                        language="python"
                    )
                ]
            )
        
        # Invalid: invalid detection mode
        with pytest.raises(ValidationError):
            DetectPatternsRequest(
                code_chunks=chunks,
                detection_mode="ultra"  # Not in allowed values
            )

    def test_pattern_contract(self):
        """Test pattern model contract validation."""
        # Valid pattern
        pattern = Pattern(
            id="pattern-123",
            type=PatternType.DESIGN_PATTERN,
            name="Singleton Pattern",
            description="Ensures a class has only one instance",
            confidence=0.95,
            occurrences=[{
                "file": "src/config.py",
                "line": 10,
                "snippet": "class Config:"
            }],
            metadata={"category": "creational"},
            recommendations=["Consider thread safety"]
        )
        assert pattern.confidence == 0.95
        
        # Invalid: confidence out of range
        with pytest.raises(ValidationError):
            Pattern(
                id="pattern-123",
                type=PatternType.ANTI_PATTERN,
                name="God Class",
                description="Class doing too much",
                confidence=1.5,  # Out of range
                occurrences=[{"file": "main.py"}]
            )
        
        # Invalid: empty occurrences
        with pytest.raises(ValidationError):
            Pattern(
                id="pattern-123",
                type=PatternType.CODE_SMELL,
                name="Long Method",
                description="Method too long",
                confidence=0.8,
                occurrences=[]  # Must have at least one
            )

    def test_pattern_recommendation_contract(self):
        """Test pattern recommendation contract validation."""
        # Valid recommendation
        recommendation = PatternRecommendation(
            pattern_id="factory-pattern",
            pattern_name="Factory Pattern",
            pattern_type=PatternType.DESIGN_PATTERN,
            relevance_score=0.88,
            explanation="Your code creates multiple similar objects",
            implementation_guide="Create a factory class to encapsulate object creation",
            example_code="class ObjectFactory:\n    def create(self, type):\n        ...",
            benefits=["Improved maintainability", "Easier testing"],
            considerations=["May add complexity for simple cases"]
        )
        assert recommendation.relevance_score == 0.88
        
        # Invalid: relevance score out of range
        with pytest.raises(ValidationError):
            PatternRecommendation(
                pattern_id="pattern-1",
                pattern_name="Pattern",
                pattern_type=PatternType.BEST_PRACTICE,
                relevance_score=-0.1,  # Negative not allowed
                explanation="Explanation",
                implementation_guide="Guide"
            )

    def test_quality_metrics_contract(self):
        """Test quality metrics contract validation."""
        # Valid metrics
        metrics = QualityMetrics(
            maintainability_index=75.5,
            cyclomatic_complexity=15.2,
            code_duplication=5.0,
            test_coverage=85.0,
            documentation_coverage=60.0,
            security_score=90.0
        )
        assert metrics.maintainability_index == 75.5
        
        # Invalid: percentage out of range
        with pytest.raises(ValidationError):
            QualityMetrics(
                maintainability_index=150.0,  # > 100
                cyclomatic_complexity=10.0,
                code_duplication=5.0,
                documentation_coverage=50.0,
                security_score=80.0
            )
        
        # Invalid: negative complexity
        with pytest.raises(ValidationError):
            QualityMetrics(
                maintainability_index=75.0,
                cyclomatic_complexity=-5.0,  # Negative not allowed
                code_duplication=5.0,
                documentation_coverage=50.0,
                security_score=80.0
            )

    def test_code_context_contract(self):
        """Test code context contract validation."""
        # Valid context
        context = CodeContext(
            repository_id="my-app",
            file_path="src/services/user_service.py",
            language="python",
            framework="django",
            dependencies=["requests", "celery", "redis"],
            metrics={"loc": 500, "classes": 5}
        )
        assert context.framework == "django"
        
        # Minimal valid context
        minimal_context = CodeContext(
            repository_id="app",
            language="javascript"
        )
        assert minimal_context.framework is None
        assert minimal_context.dependencies == []

    def test_request_response_flow(self):
        """Test complete request-response flow compatibility."""
        # Detection request
        detect_request = DetectPatternsRequest(
            code_chunks=[
                CodeChunkInput(
                    id="chunk-1",
                    content="class UserService:\n    _instance = None\n    def __new__(cls):\n        if not cls._instance:\n            cls._instance = super().__new__(cls)\n        return cls._instance",
                    file_path="services.py",
                    language="python",
                    ast_data={"type": "class", "patterns": ["singleton"]}
                )
            ],
            detection_mode="comprehensive"
        )
        
        # Detection response
        patterns = [
            Pattern(
                id="singleton-1",
                type=PatternType.DESIGN_PATTERN,
                name="Singleton Pattern",
                description="Class ensures single instance",
                confidence=0.92,
                occurrences=[{
                    "chunk_id": "chunk-1",
                    "line_start": 1,
                    "line_end": 6
                }],
                recommendations=["Consider thread safety", "Use @property decorator"]
            )
        ]
        
        detect_response = DetectPatternsResponse(
            patterns=patterns,
            analysis_metadata={"mode": detect_request.detection_mode},
            processing_time_ms=150
        )
        
        # Classification request using detected patterns
        classify_request = ClassifyPatternsRequest(
            patterns=[p.model_dump() for p in detect_response.patterns],
            classification_type="design_pattern"
        )
        
        # Verify flow consistency
        assert len(classify_request.patterns) == len(detect_response.patterns)

    def test_error_handling_contract(self):
        """Test error response contracts."""
        class PatternMiningError(BaseModel):
            error_code: str
            error_type: str
            message: str
            details: Optional[Dict[str, Any]] = None
            timestamp: datetime
            request_id: Optional[str] = None
        
        # Valid error
        error = PatternMiningError(
            error_code="PM-001",
            error_type="ValidationError",
            message="Invalid code chunk format",
            details={"chunk_id": "chunk-1", "issue": "missing AST data"},
            timestamp=datetime.now(),
            request_id="req-456"
        )
        assert error.error_code == "PM-001"

    def test_pagination_support(self):
        """Test pagination support for list endpoints."""
        class PaginatedPatternResponse(BaseModel):
            patterns: List[Pattern]
            pagination: Dict[str, Any]
        
        # Valid paginated response
        response = PaginatedPatternResponse(
            patterns=[
                Pattern(
                    id=f"pattern-{i}",
                    type=PatternType.DESIGN_PATTERN,
                    name=f"Pattern {i}",
                    description="Description",
                    confidence=0.8,
                    occurrences=[{"file": f"file{i}.py"}]
                ) for i in range(10)
            ],
            pagination={
                "page": 1,
                "per_page": 10,
                "total": 50,
                "total_pages": 5,
                "has_next": True,
                "has_prev": False
            }
        )
        assert len(response.patterns) == 10

    def test_versioning_compatibility(self):
        """Test API versioning compatibility."""
        # V1 pattern (older version)
        v1_pattern = {
            "id": "pattern-1",
            "type": "design_pattern",
            "name": "Factory Pattern",
            "confidence": 0.85,
            "occurrences": [{"file": "factory.py"}]
            # V1 doesn't have description, recommendations
        }
        
        # Should be compatible with validation adjustments
        # V2 adds required fields with defaults
        v2_pattern = Pattern(
            **v1_pattern,
            description="Factory pattern detected",  # Default for v1
            recommendations=[]  # Default empty
        )
        assert v2_pattern.name == "Factory Pattern"

    def test_comprehensive_pattern_types(self):
        """Test all pattern type enumerations."""
        # Test all valid pattern types
        for pattern_type in PatternType:
            pattern = Pattern(
                id=f"test-{pattern_type.value}",
                type=pattern_type,
                name=f"Test {pattern_type.value}",
                description=f"Testing {pattern_type.value}",
                confidence=0.75,
                occurrences=[{"test": True}]
            )
            assert pattern.type == pattern_type
        
        # Invalid pattern type
        with pytest.raises(ValidationError):
            Pattern(
                id="test",
                type="unknown_pattern",  # Not in enum
                name="Test",
                description="Test",
                confidence=0.5,
                occurrences=[{}]
            )

    def test_nested_object_validation(self):
        """Test deep validation of nested objects."""
        # Complex nested request
        request = RecommendationsRequest(
            code_context=CodeContext(
                repository_id="complex-app",
                file_path="src/complex/module.py",
                language="python",
                framework="fastapi",
                dependencies=["pydantic", "sqlalchemy", "redis"],
                metrics={
                    "complexity": {
                        "average": 12.5,
                        "max": 45,
                        "functions_above_threshold": 3
                    },
                    "coverage": {
                        "line": 85.5,
                        "branch": 78.0
                    }
                }
            ),
            intent="improve performance and reduce complexity",
            limit=10
        )
        
        # Verify nested validation
        assert request.code_context.metrics["complexity"]["average"] == 12.5
        assert len(request.code_context.dependencies) == 3