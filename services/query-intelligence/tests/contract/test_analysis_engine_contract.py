"""Contract tests for Analysis Engine service integration."""

import pytest
from typing import Dict, List, Any
import json
from pydantic import BaseModel, Field, ValidationError
from datetime import datetime


# Contract Models for Analysis Engine API
class EmbeddingSearchRequest(BaseModel):
    """Contract for embedding search request."""
    repository_id: str = Field(..., min_length=1)
    query_embedding: List[float] = Field(..., min_items=1, max_items=4096)
    limit: int = Field(default=20, ge=1, le=100)
    filters: Dict[str, Any] = Field(default_factory=dict)


class CodeChunk(BaseModel):
    """Contract for code chunk in search results."""
    id: str
    file_path: str
    start_line: int = Field(ge=1)
    end_line: int = Field(ge=1)
    content: str
    language: str
    embedding: List[float] = Field(min_items=1, max_items=4096)
    metadata: Dict[str, Any] = Field(default_factory=dict)
    similarity_score: float = Field(ge=0.0, le=1.0)


class EmbeddingSearchResponse(BaseModel):
    """Contract for embedding search response."""
    chunks: List[CodeChunk]
    total_count: int = Field(ge=0)
    metadata: Dict[str, Any] = Field(default_factory=dict)


class RepositoryMetadata(BaseModel):
    """Contract for repository metadata."""
    id: str
    name: str
    description: Optional[str] = None
    language_stats: Dict[str, int]
    total_files: int = Field(ge=0)
    total_lines: int = Field(ge=0)
    last_analyzed: datetime
    status: str = Field(pattern="^(analyzing|completed|failed)$")
    analysis_version: str


class AnalysisResult(BaseModel):
    """Contract for analysis result."""
    repository_id: str
    status: str = Field(pattern="^(pending|in_progress|completed|failed)$")
    started_at: datetime
    completed_at: Optional[datetime] = None
    statistics: Dict[str, Any]
    errors: List[str] = Field(default_factory=list)


class HealthCheckResponse(BaseModel):
    """Contract for health check response."""
    status: str = Field(pattern="^(healthy|unhealthy)$")
    version: str
    uptime: int = Field(ge=0)
    dependencies: Dict[str, bool]


class TestAnalysisEngineContract:
    """Test Analysis Engine API contract compliance."""

    def test_embedding_search_request_contract(self):
        """Test embedding search request contract validation."""
        # Valid request
        valid_request = EmbeddingSearchRequest(
            repository_id="test-repo",
            query_embedding=[0.1, 0.2, 0.3],
            limit=10,
            filters={"language": "python"}
        )
        assert valid_request.repository_id == "test-repo"
        assert len(valid_request.query_embedding) == 3
        
        # Invalid: empty repository_id
        with pytest.raises(ValidationError):
            EmbeddingSearchRequest(
                repository_id="",
                query_embedding=[0.1]
            )
        
        # Invalid: empty embedding
        with pytest.raises(ValidationError):
            EmbeddingSearchRequest(
                repository_id="test",
                query_embedding=[]
            )
        
        # Invalid: limit out of range
        with pytest.raises(ValidationError):
            EmbeddingSearchRequest(
                repository_id="test",
                query_embedding=[0.1],
                limit=0
            )
        
        with pytest.raises(ValidationError):
            EmbeddingSearchRequest(
                repository_id="test",
                query_embedding=[0.1],
                limit=101
            )

    def test_code_chunk_contract(self):
        """Test code chunk contract validation."""
        # Valid chunk
        valid_chunk = CodeChunk(
            id="chunk-123",
            file_path="src/main.py",
            start_line=10,
            end_line=20,
            content="def hello():\n    return 'world'",
            language="python",
            embedding=[0.1, 0.2, 0.3],
            metadata={"complexity": 5},
            similarity_score=0.95
        )
        assert valid_chunk.similarity_score == 0.95
        
        # Invalid: negative line numbers
        with pytest.raises(ValidationError):
            CodeChunk(
                id="chunk-123",
                file_path="src/main.py",
                start_line=0,
                end_line=10,
                content="code",
                language="python",
                embedding=[0.1],
                similarity_score=0.5
            )
        
        # Invalid: similarity score out of range
        with pytest.raises(ValidationError):
            CodeChunk(
                id="chunk-123",
                file_path="src/main.py",
                start_line=1,
                end_line=10,
                content="code",
                language="python",
                embedding=[0.1],
                similarity_score=1.5
            )

    def test_embedding_search_response_contract(self):
        """Test embedding search response contract validation."""
        # Valid response
        chunks = [
            CodeChunk(
                id=f"chunk-{i}",
                file_path=f"src/file{i}.py",
                start_line=i*10,
                end_line=(i+1)*10,
                content=f"code block {i}",
                language="python",
                embedding=[0.1 * i],
                similarity_score=0.9 - (i * 0.1)
            )
            for i in range(3)
        ]
        
        valid_response = EmbeddingSearchResponse(
            chunks=chunks,
            total_count=3,
            metadata={"search_time_ms": 50}
        )
        assert len(valid_response.chunks) == 3
        assert valid_response.total_count == 3
        
        # Invalid: negative total_count
        with pytest.raises(ValidationError):
            EmbeddingSearchResponse(
                chunks=[],
                total_count=-1
            )

    def test_repository_metadata_contract(self):
        """Test repository metadata contract validation."""
        # Valid metadata
        valid_metadata = RepositoryMetadata(
            id="repo-123",
            name="test-repo",
            description="Test repository",
            language_stats={"python": 1000, "javascript": 500},
            total_files=50,
            total_lines=1500,
            last_analyzed=datetime.now(),
            status="completed",
            analysis_version="1.0.0"
        )
        assert valid_metadata.total_lines == 1500
        
        # Invalid: negative file count
        with pytest.raises(ValidationError):
            RepositoryMetadata(
                id="repo-123",
                name="test-repo",
                language_stats={},
                total_files=-1,
                total_lines=0,
                last_analyzed=datetime.now(),
                status="completed",
                analysis_version="1.0.0"
            )
        
        # Invalid: invalid status
        with pytest.raises(ValidationError):
            RepositoryMetadata(
                id="repo-123",
                name="test-repo",
                language_stats={},
                total_files=0,
                total_lines=0,
                last_analyzed=datetime.now(),
                status="invalid_status",
                analysis_version="1.0.0"
            )

    def test_health_check_response_contract(self):
        """Test health check response contract validation."""
        # Valid response
        valid_health = HealthCheckResponse(
            status="healthy",
            version="1.0.0",
            uptime=3600,
            dependencies={"database": True, "cache": True}
        )
        assert valid_health.status == "healthy"
        
        # Invalid: invalid status
        with pytest.raises(ValidationError):
            HealthCheckResponse(
                status="ok",  # Should be "healthy" or "unhealthy"
                version="1.0.0",
                uptime=3600,
                dependencies={}
            )
        
        # Invalid: negative uptime
        with pytest.raises(ValidationError):
            HealthCheckResponse(
                status="healthy",
                version="1.0.0",
                uptime=-1,
                dependencies={}
            )

    def test_request_response_compatibility(self):
        """Test that request and response formats are compatible."""
        # Create a request
        request = EmbeddingSearchRequest(
            repository_id="test-repo",
            query_embedding=[0.1, 0.2, 0.3, 0.4, 0.5],
            limit=5,
            filters={"language": "python", "min_lines": 10}
        )
        
        # Simulate a response
        chunks = []
        for i in range(request.limit):
            chunk = CodeChunk(
                id=f"chunk-{i}",
                file_path=f"src/module{i}.py",
                start_line=i*20 + 1,
                end_line=(i+1)*20,
                content=f"# Module {i} code",
                language="python",
                embedding=[0.1] * len(request.query_embedding),  # Same dimension
                metadata={"lines": 20},
                similarity_score=0.95 - (i * 0.05)
            )
            chunks.append(chunk)
        
        response = EmbeddingSearchResponse(
            chunks=chunks,
            total_count=100,  # Total available chunks
            metadata={
                "search_time_ms": 25,
                "filters_applied": request.filters
            }
        )
        
        # Verify compatibility
        assert len(response.chunks) <= request.limit
        assert all(len(chunk.embedding) == len(request.query_embedding) for chunk in response.chunks)
        assert response.metadata.get("filters_applied") == request.filters

    def test_error_response_contract(self):
        """Test error response contract compliance."""
        # Standard error response format
        class ErrorResponse(BaseModel):
            error: str
            message: str
            status_code: int
            timestamp: datetime
            request_id: Optional[str] = None
            details: Optional[Dict[str, Any]] = None
        
        # Valid error response
        error = ErrorResponse(
            error="ValidationError",
            message="Invalid repository ID format",
            status_code=400,
            timestamp=datetime.now(),
            request_id="req-123",
            details={"field": "repository_id", "reason": "invalid_format"}
        )
        assert error.status_code == 400

    def test_pagination_contract(self):
        """Test pagination contract for list endpoints."""
        class PaginatedResponse(BaseModel):
            items: List[Any]
            total: int = Field(ge=0)
            page: int = Field(ge=1)
            per_page: int = Field(ge=1, le=100)
            has_next: bool
            has_prev: bool
        
        # Valid paginated response
        paginated = PaginatedResponse(
            items=[{"id": i} for i in range(20)],
            total=100,
            page=2,
            per_page=20,
            has_next=True,
            has_prev=True
        )
        assert len(paginated.items) == paginated.per_page

    def test_backward_compatibility(self):
        """Test backward compatibility with older API versions."""
        # V1 format (older)
        v1_request = {
            "repository_id": "test-repo",
            "query_embedding": [0.1, 0.2],
            "limit": 10
            # No filters field in v1
        }
        
        # Should be compatible with current contract
        request = EmbeddingSearchRequest(**v1_request)
        assert request.filters == {}  # Default value
        
        # V2 format (current) should work with v1 clients
        v2_response = EmbeddingSearchResponse(
            chunks=[],
            total_count=0,
            metadata={"version": "v2"}  # New field
        )
        
        # V1 clients can ignore metadata
        v1_compatible = {
            "chunks": v2_response.chunks,
            "total_count": v2_response.total_count
        }
        assert "metadata" not in v1_compatible or v1_compatible.get("metadata") is not None

    def test_field_constraints(self):
        """Test specific field constraints and business rules."""
        # Repository ID format
        with pytest.raises(ValidationError):
            EmbeddingSearchRequest(
                repository_id="",  # Empty not allowed
                query_embedding=[0.1]
            )
        
        # Embedding dimension limits
        large_embedding = [0.1] * 4096  # Max size
        request = EmbeddingSearchRequest(
            repository_id="test",
            query_embedding=large_embedding
        )
        assert len(request.query_embedding) == 4096
        
        # Too large embedding
        with pytest.raises(ValidationError):
            EmbeddingSearchRequest(
                repository_id="test",
                query_embedding=[0.1] * 4097  # Exceeds max
            )

    def test_json_serialization(self):
        """Test JSON serialization/deserialization of contracts."""
        # Create complex request
        request = EmbeddingSearchRequest(
            repository_id="test-repo-123",
            query_embedding=[0.1, 0.2, 0.3],
            limit=25,
            filters={
                "language": ["python", "javascript"],
                "min_lines": 10,
                "exclude_tests": True
            }
        )
        
        # Serialize to JSON
        json_str = request.model_dump_json()
        
        # Deserialize back
        request2 = EmbeddingSearchRequest.model_validate_json(json_str)
        
        # Verify identical
        assert request2.repository_id == request.repository_id
        assert request2.query_embedding == request.query_embedding
        assert request2.filters == request.filters

    def test_optional_fields(self):
        """Test handling of optional fields in contracts."""
        # Minimal valid metadata
        metadata = RepositoryMetadata(
            id="repo-123",
            name="test",
            language_stats={},
            total_files=0,
            total_lines=0,
            last_analyzed=datetime.now(),
            status="analyzing",
            analysis_version="1.0.0"
            # description is optional
        )
        assert metadata.description is None
        
        # With optional fields
        metadata_full = RepositoryMetadata(
            id="repo-123",
            name="test",
            description="A test repository",
            language_stats={"python": 100},
            total_files=10,
            total_lines=1000,
            last_analyzed=datetime.now(),
            status="completed",
            analysis_version="1.0.0"
        )
        assert metadata_full.description == "A test repository"