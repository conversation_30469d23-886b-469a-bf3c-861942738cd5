"""Integration contract tests for CCL service compatibility."""

import pytest
from typing import Dict, List, Any, Optional
from datetime import datetime
import json
from pydantic import BaseModel, Field, ValidationError

# Import contract models from other test files
from .test_analysis_engine_contract import (
    EmbeddingSearchRequest, EmbeddingSearchResponse,
    CodeChunk, RepositoryMetadata
)
from .test_pattern_mining_contract import (
    DetectPatternsRequest, DetectPatternsResponse,
    Pattern, PatternType, CodeChunkInput,
    RecommendationsRequest, PatternRecommendation
)
from .test_auth_contract import (
    TokenPayload, UserRole, TokenType
)


class CCLQueryRequest(BaseModel):
    """Unified query request format for CCL platform."""
    query: str = Field(..., min_length=1, max_length=10000)
    repository_id: str = Field(..., min_length=1)
    user_id: str
    session_id: Optional[str] = None
    context: Dict[str, Any] = Field(default_factory=dict)
    filters: Dict[str, Any] = Field(default_factory=dict)
    options: Dict[str, Any] = Field(default_factory=dict)


class CCLCodeReference(BaseModel):
    """Unified code reference format across CCL services."""
    service: str = Field(..., pattern="^(analysis-engine|pattern-mining|query-intelligence)$")
    file_path: str
    start_line: int = Field(ge=1)
    end_line: int = Field(ge=1)
    content: str
    language: str
    confidence: float = Field(ge=0.0, le=1.0)
    metadata: Dict[str, Any] = Field(default_factory=dict)


class CCLServiceResponse(BaseModel):
    """Unified service response format."""
    service: str
    status: str = Field(pattern="^(success|partial|error)$")
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    timestamp: datetime
    duration_ms: int = Field(ge=0)


class CCLAggregatedResponse(BaseModel):
    """Aggregated response from multiple CCL services."""
    query_id: str
    user_id: str
    repository_id: str
    answer: str
    confidence: float = Field(ge=0.0, le=1.0)
    references: List[CCLCodeReference]
    patterns: List[Dict[str, Any]] = Field(default_factory=list)
    recommendations: List[str] = Field(default_factory=list)
    service_responses: List[CCLServiceResponse]
    total_duration_ms: int = Field(ge=0)
    metadata: Dict[str, Any] = Field(default_factory=dict)


class TestCCLIntegrationContract:
    """Test CCL platform integration contracts."""

    def test_query_to_analysis_engine_flow(self):
        """Test query flow from Query Intelligence to Analysis Engine."""
        # User query
        user_query = CCLQueryRequest(
            query="How does the authentication middleware work?",
            repository_id="my-app",
            user_id="user-123",
            session_id="session-456",
            filters={"file_pattern": "*.py", "exclude": ["tests/*"]}
        )
        
        # Convert to embedding (simulated)
        query_embedding = [0.1, 0.2, 0.3, 0.4, 0.5]  # Would come from embedding service
        
        # Create Analysis Engine request
        ae_request = EmbeddingSearchRequest(
            repository_id=user_query.repository_id,
            query_embedding=query_embedding,
            limit=20,
            filters=user_query.filters
        )
        
        # Simulate Analysis Engine response
        ae_chunks = [
            CodeChunk(
                id="chunk-1",
                file_path="src/middleware/auth.py",
                start_line=10,
                end_line=30,
                content="def authenticate(request):\n    # JWT validation\n    token = request.headers.get('Authorization')",
                language="python",
                embedding=query_embedding,
                metadata={"function": "authenticate"},
                similarity_score=0.92
            )
        ]
        
        ae_response = EmbeddingSearchResponse(
            chunks=ae_chunks,
            total_count=1,
            metadata={"search_time_ms": 45}
        )
        
        # Convert to unified format
        ccl_references = [
            CCLCodeReference(
                service="analysis-engine",
                file_path=chunk.file_path,
                start_line=chunk.start_line,
                end_line=chunk.end_line,
                content=chunk.content,
                language=chunk.language,
                confidence=chunk.similarity_score,
                metadata=chunk.metadata
            )
            for chunk in ae_response.chunks
        ]
        
        # Verify contract compatibility
        assert len(ccl_references) == len(ae_response.chunks)
        assert ccl_references[0].confidence == ae_chunks[0].similarity_score

    def test_query_to_pattern_mining_flow(self):
        """Test query flow from Query Intelligence to Pattern Mining."""
        # User query about patterns
        user_query = CCLQueryRequest(
            query="What design patterns are used in the authentication module?",
            repository_id="my-app",
            user_id="user-123",
            options={"include_patterns": True}
        )
        
        # Convert Analysis Engine chunks to Pattern Mining format
        code_chunks_for_pm = [
            CodeChunkInput(
                id="chunk-1",
                content="class AuthMiddleware:\n    _instance = None\n    def __new__(cls):\n        if not cls._instance:\n            cls._instance = super().__new__(cls)\n        return cls._instance",
                file_path="src/middleware/auth.py",
                language="python",
                ast_data={"type": "class", "name": "AuthMiddleware"}
            )
        ]
        
        # Create Pattern Mining request
        pm_request = DetectPatternsRequest(
            code_chunks=code_chunks_for_pm,
            context={"query": user_query.query, "repository": user_query.repository_id}
        )
        
        # Simulate Pattern Mining response
        patterns = [
            Pattern(
                id="pattern-1",
                type=PatternType.DESIGN_PATTERN,
                name="Singleton Pattern",
                description="Ensures single instance of AuthMiddleware",
                confidence=0.88,
                occurrences=[{
                    "chunk_id": "chunk-1",
                    "file": "src/middleware/auth.py",
                    "lines": "2-6"
                }],
                recommendations=["Consider thread safety for multi-threaded environments"]
            )
        ]
        
        pm_response = DetectPatternsResponse(
            patterns=patterns,
            analysis_metadata={"patterns_found": 1},
            processing_time_ms=120
        )
        
        # Verify compatibility
        assert len(pm_response.patterns) == 1
        assert pm_response.patterns[0].type == PatternType.DESIGN_PATTERN

    def test_authenticated_service_communication(self):
        """Test authenticated communication between services."""
        # Service-to-service token
        service_token = TokenPayload(
            sub="svc-query-intelligence",
            roles=[UserRole.SERVICE_ACCOUNT],
            permissions=["analysis.read", "pattern.read"],
            exp=int((datetime.now().timestamp())) + 3600,
            iat=int(datetime.now().timestamp()),
            token_type=TokenType.SERVICE,
            client_id="query-intelligence"
        )
        
        # Headers for service communication
        auth_headers = {
            "Authorization": f"Bearer <token>",
            "X-Service-Name": "query-intelligence",
            "X-Request-ID": "req-123",
            "Content-Type": "application/json"
        }
        
        # Verify service has required permissions
        assert "analysis.read" in service_token.permissions
        assert "pattern.read" in service_token.permissions
        assert service_token.token_type == TokenType.SERVICE

    def test_aggregated_response_construction(self):
        """Test construction of aggregated response from multiple services."""
        # Service responses
        ae_service_response = CCLServiceResponse(
            service="analysis-engine",
            status="success",
            data={
                "chunks_found": 5,
                "chunks_returned": 5,
                "search_time_ms": 85
            },
            timestamp=datetime.now(),
            duration_ms=85
        )
        
        pm_service_response = CCLServiceResponse(
            service="pattern-mining",
            status="success",
            data={
                "patterns_detected": 3,
                "patterns_classified": 3
            },
            timestamp=datetime.now(),
            duration_ms=150
        )
        
        qi_service_response = CCLServiceResponse(
            service="query-intelligence",
            status="success",
            data={
                "llm_model": "gemini-2.5-flash",
                "tokens_used": 1500,
                "cache_hit": False
            },
            timestamp=datetime.now(),
            duration_ms=200
        )
        
        # Aggregated response
        aggregated = CCLAggregatedResponse(
            query_id="query-789",
            user_id="user-123",
            repository_id="my-app",
            answer="The authentication middleware uses a Singleton pattern to ensure a single instance...",
            confidence=0.91,
            references=[
                CCLCodeReference(
                    service="analysis-engine",
                    file_path="src/middleware/auth.py",
                    start_line=10,
                    end_line=30,
                    content="class AuthMiddleware implementation",
                    language="python",
                    confidence=0.92
                )
            ],
            patterns=[{
                "name": "Singleton Pattern",
                "type": "design_pattern",
                "confidence": 0.88
            }],
            recommendations=[
                "Consider thread safety for the Singleton implementation",
                "Add comprehensive error handling for authentication failures"
            ],
            service_responses=[ae_service_response, pm_service_response, qi_service_response],
            total_duration_ms=435,  # Sum of all service durations
            metadata={
                "model_used": "gemini-2.5-flash",
                "cache_hit": False,
                "services_called": 3
            }
        )
        
        # Verify aggregation
        assert len(aggregated.service_responses) == 3
        assert aggregated.total_duration_ms == sum(sr.duration_ms for sr in aggregated.service_responses)
        assert all(sr.status == "success" for sr in aggregated.service_responses)

    def test_error_propagation_across_services(self):
        """Test error handling and propagation across services."""
        # Analysis Engine error
        ae_error_response = CCLServiceResponse(
            service="analysis-engine",
            status="error",
            error="Repository not found",
            timestamp=datetime.now(),
            duration_ms=25
        )
        
        # Pattern Mining partial success
        pm_partial_response = CCLServiceResponse(
            service="pattern-mining",
            status="partial",
            data={"patterns_detected": 1, "errors": ["Some chunks failed processing"]},
            error="Partial processing failure",
            timestamp=datetime.now(),
            duration_ms=100
        )
        
        # Aggregated response with errors
        error_aggregated = CCLAggregatedResponse(
            query_id="query-error-1",
            user_id="user-123",
            repository_id="unknown-repo",
            answer="I encountered errors while processing your query. Limited results available.",
            confidence=0.3,  # Low confidence due to errors
            references=[],  # No references due to AE error
            patterns=[{"name": "Unknown", "confidence": 0.5}],  # Limited pattern data
            recommendations=["Please verify the repository exists and try again"],
            service_responses=[ae_error_response, pm_partial_response],
            total_duration_ms=125,
            metadata={"partial_failure": True, "errors": 2}
        )
        
        # Verify error handling
        assert error_aggregated.confidence < 0.5  # Low confidence
        assert any(sr.status == "error" for sr in error_aggregated.service_responses)
        assert error_aggregated.metadata.get("partial_failure") is True

    def test_data_format_compatibility(self):
        """Test data format compatibility across all services."""
        # Common timestamp format
        timestamp = datetime.now()
        
        # Common ID formats
        repository_id = "org-name/repo-name"
        user_id = "user-uuid-123"
        session_id = "session-uuid-456"
        
        # File path formats
        file_paths = [
            "src/main.py",
            "lib/utils/helper.js",
            "app/models/user.rb",
            "internal/service/handler.go"
        ]
        
        # Language identifiers (should match across services)
        languages = ["python", "javascript", "ruby", "go", "java", "csharp", "typescript"]
        
        # Confidence scores (all 0.0 to 1.0)
        confidence_scores = [0.0, 0.25, 0.5, 0.75, 0.95, 1.0]
        
        # Verify all services use same formats
        for file_path in file_paths:
            # Analysis Engine format
            ae_chunk = CodeChunk(
                id="chunk-1",
                file_path=file_path,
                start_line=1,
                end_line=10,
                content="code",
                language="python",
                embedding=[0.1],
                similarity_score=0.8
            )
            
            # Pattern Mining format
            pm_chunk = CodeChunkInput(
                id="chunk-1",
                content="code",
                file_path=file_path,
                language="python"
            )
            
            # Unified format
            ccl_ref = CCLCodeReference(
                service="analysis-engine",
                file_path=file_path,
                start_line=1,
                end_line=10,
                content="code",
                language="python",
                confidence=0.8
            )
            
            # All should have same file path format
            assert ae_chunk.file_path == pm_chunk.file_path == ccl_ref.file_path

    def test_performance_requirements(self):
        """Test performance requirements across service contracts."""
        # Maximum response times (ms)
        max_response_times = {
            "analysis-engine": 500,  # Embedding search
            "pattern-mining": 1000,  # Pattern detection
            "query-intelligence": 2000,  # LLM generation
            "total": 3000  # Total query processing
        }
        
        # Simulate service timings
        service_responses = [
            CCLServiceResponse(
                service="analysis-engine",
                status="success",
                timestamp=datetime.now(),
                duration_ms=450  # Under limit
            ),
            CCLServiceResponse(
                service="pattern-mining",
                status="success",
                timestamp=datetime.now(),
                duration_ms=800  # Under limit
            ),
            CCLServiceResponse(
                service="query-intelligence",
                status="success",
                timestamp=datetime.now(),
                duration_ms=1500  # Under limit
            )
        ]
        
        # Verify performance
        total_time = sum(sr.duration_ms for sr in service_responses)
        assert total_time < max_response_times["total"]
        
        for sr in service_responses:
            assert sr.duration_ms < max_response_times.get(sr.service, 1000)

    def test_versioning_compatibility(self):
        """Test API versioning compatibility across services."""
        # Version headers
        version_headers = {
            "API-Version": "2.0",
            "Accept-Version": ">=1.0,<3.0",
            "Min-Client-Version": "1.5"
        }
        
        # Service version info
        service_versions = {
            "analysis-engine": "2.1.0",
            "pattern-mining": "2.0.5",
            "query-intelligence": "2.0.0"
        }
        
        # All services should be compatible with v2.0
        for service, version in service_versions.items():
            major_version = int(version.split('.')[0])
            assert major_version == 2  # All on v2.x

    def test_batch_operations_contract(self):
        """Test batch operation contracts for efficiency."""
        # Batch query request
        class CCLBatchQueryRequest(BaseModel):
            queries: List[CCLQueryRequest] = Field(..., min_items=1, max_items=10)
            batch_options: Dict[str, Any] = Field(default_factory=dict)
        
        # Create batch request
        batch_request = CCLBatchQueryRequest(
            queries=[
                CCLQueryRequest(
                    query=f"Query {i}",
                    repository_id="repo-1",
                    user_id="user-123"
                ) for i in range(5)
            ],
            batch_options={"parallel": True, "max_concurrent": 3}
        )
        
        # Verify batch constraints
        assert len(batch_request.queries) <= 10
        assert all(q.repository_id == "repo-1" for q in batch_request.queries)