"""Contract tests for Authentication service integration."""

import pytest
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field, ValidationError, EmailStr
from datetime import datetime, timedelta
import jwt
from enum import Enum


# Authentication Contract Models
class UserRole(str, Enum):
    """Supported user roles."""
    ADMIN = "admin"
    USER = "user"
    DEVELOPER = "developer"
    VIEWER = "viewer"
    SERVICE_ACCOUNT = "service_account"


class TokenType(str, Enum):
    """Supported token types."""
    ACCESS = "access"
    REFRESH = "refresh"
    API_KEY = "api_key"
    SERVICE = "service"


class AuthRequest(BaseModel):
    """Contract for authentication request."""
    grant_type: str = Field(..., pattern="^(password|refresh_token|client_credentials|api_key)$")
    username: Optional[EmailStr] = None
    password: Optional[str] = Field(default=None, min_length=8)
    refresh_token: Optional[str] = None
    api_key: Optional[str] = None
    client_id: Optional[str] = None
    client_secret: Optional[str] = None
    scope: Optional[List[str]] = Field(default_factory=list)


class TokenPayload(BaseModel):
    """Contract for JWT token payload."""
    sub: str  # Subject (user ID)
    email: Optional[EmailStr] = None
    roles: List[UserRole] = Field(default_factory=list)
    permissions: List[str] = Field(default_factory=list)
    exp: int  # Expiration timestamp
    iat: int  # Issued at timestamp
    nbf: Optional[int] = None  # Not before timestamp
    jti: Optional[str] = None  # JWT ID
    token_type: TokenType = TokenType.ACCESS
    client_id: Optional[str] = None


class AuthResponse(BaseModel):
    """Contract for authentication response."""
    access_token: str
    token_type: str = Field(default="Bearer")
    expires_in: int = Field(gt=0)
    refresh_token: Optional[str] = None
    scope: List[str] = Field(default_factory=list)
    user_info: Optional[Dict[str, Any]] = None


class UserInfo(BaseModel):
    """Contract for user information."""
    id: str
    email: EmailStr
    username: str
    roles: List[UserRole]
    permissions: List[str] = Field(default_factory=list)
    metadata: Dict[str, Any] = Field(default_factory=dict)
    created_at: datetime
    last_login: Optional[datetime] = None
    is_active: bool = True
    is_verified: bool = True


class ServiceAccount(BaseModel):
    """Contract for service account."""
    id: str
    name: str
    description: Optional[str] = None
    roles: List[UserRole] = Field(default_factory=lambda: [UserRole.SERVICE_ACCOUNT])
    permissions: List[str]
    api_key_prefix: str = Field(min_length=8, max_length=16)
    created_at: datetime
    last_used: Optional[datetime] = None
    is_active: bool = True
    allowed_ips: List[str] = Field(default_factory=list)
    rate_limit: Optional[int] = Field(default=None, gt=0)


class TokenValidationRequest(BaseModel):
    """Contract for token validation request."""
    token: str
    token_type: TokenType = TokenType.ACCESS
    validate_permissions: List[str] = Field(default_factory=list)
    validate_roles: List[UserRole] = Field(default_factory=list)


class TokenValidationResponse(BaseModel):
    """Contract for token validation response."""
    valid: bool
    subject: Optional[str] = None
    roles: List[UserRole] = Field(default_factory=list)
    permissions: List[str] = Field(default_factory=list)
    expires_at: Optional[datetime] = None
    issued_at: Optional[datetime] = None
    validation_errors: List[str] = Field(default_factory=list)


class RateLimitInfo(BaseModel):
    """Contract for rate limit information."""
    limit: int = Field(gt=0)
    remaining: int = Field(ge=0)
    reset_at: datetime
    window_seconds: int = Field(gt=0)


class AuthError(BaseModel):
    """Contract for authentication error response."""
    error: str = Field(..., pattern="^(invalid_request|invalid_client|invalid_grant|unauthorized_client|unsupported_grant_type|invalid_scope)$")
    error_description: str
    error_uri: Optional[str] = None
    status_code: int = Field(ge=400, lt=600)
    timestamp: datetime
    request_id: Optional[str] = None


class TestAuthContract:
    """Test Authentication API contract compliance."""

    def test_auth_request_contract(self):
        """Test authentication request contract validation."""
        # Valid password grant
        password_request = AuthRequest(
            grant_type="password",
            username="<EMAIL>",
            password="securePassword123!",
            scope=["read", "write"]
        )
        assert password_request.grant_type == "password"
        
        # Valid refresh token grant
        refresh_request = AuthRequest(
            grant_type="refresh_token",
            refresh_token="refresh_token_value"
        )
        assert refresh_request.grant_type == "refresh_token"
        
        # Valid client credentials grant
        client_request = AuthRequest(
            grant_type="client_credentials",
            client_id="client-123",
            client_secret="secret-456",
            scope=["api.read", "api.write"]
        )
        assert client_request.grant_type == "client_credentials"
        
        # Invalid: short password
        with pytest.raises(ValidationError):
            AuthRequest(
                grant_type="password",
                username="<EMAIL>",
                password="short"  # Too short
            )
        
        # Invalid: invalid grant type
        with pytest.raises(ValidationError):
            AuthRequest(
                grant_type="invalid_grant",
                username="<EMAIL>"
            )
        
        # Invalid: invalid email
        with pytest.raises(ValidationError):
            AuthRequest(
                grant_type="password",
                username="not-an-email",
                password="validPassword123"
            )

    def test_token_payload_contract(self):
        """Test JWT token payload contract validation."""
        # Valid token payload
        now = int(datetime.now().timestamp())
        payload = TokenPayload(
            sub="user-123",
            email="<EMAIL>",
            roles=[UserRole.USER, UserRole.DEVELOPER],
            permissions=["repo.read", "repo.write", "query.execute"],
            exp=now + 3600,  # 1 hour
            iat=now,
            nbf=now,
            jti="token-unique-id",
            token_type=TokenType.ACCESS
        )
        assert payload.sub == "user-123"
        assert UserRole.DEVELOPER in payload.roles
        
        # Invalid: expired token
        with pytest.raises(ValidationError):
            TokenPayload(
                sub="user-123",
                roles=[],
                exp=now - 3600,  # Already expired
                iat=now
            )

    def test_auth_response_contract(self):
        """Test authentication response contract validation."""
        # Valid response with refresh token
        response = AuthResponse(
            access_token="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
            token_type="Bearer",
            expires_in=3600,
            refresh_token="refresh_token_value",
            scope=["read", "write"],
            user_info={
                "id": "user-123",
                "email": "<EMAIL>",
                "roles": ["user"]
            }
        )
        assert response.expires_in == 3600
        
        # Valid response without refresh token
        api_response = AuthResponse(
            access_token="api_token_value",
            expires_in=86400  # 24 hours
        )
        assert api_response.refresh_token is None
        
        # Invalid: non-positive expiration
        with pytest.raises(ValidationError):
            AuthResponse(
                access_token="token",
                expires_in=0  # Must be positive
            )

    def test_user_info_contract(self):
        """Test user information contract validation."""
        # Valid user info
        user = UserInfo(
            id="user-123",
            email="<EMAIL>",
            username="johndoe",
            roles=[UserRole.USER, UserRole.DEVELOPER],
            permissions=["repo.read", "repo.write", "query.execute"],
            metadata={"department": "Engineering", "team": "Backend"},
            created_at=datetime.now() - timedelta(days=365),
            last_login=datetime.now() - timedelta(hours=2),
            is_active=True,
            is_verified=True
        )
        assert UserRole.DEVELOPER in user.roles
        
        # Invalid: invalid email
        with pytest.raises(ValidationError):
            UserInfo(
                id="user-123",
                email="invalid-email",
                username="user",
                roles=[UserRole.USER],
                created_at=datetime.now()
            )

    def test_service_account_contract(self):
        """Test service account contract validation."""
        # Valid service account
        service_account = ServiceAccount(
            id="svc-123",
            name="analysis-engine-client",
            description="Service account for analysis engine",
            permissions=["analysis.read", "analysis.write"],
            api_key_prefix="ae_prod_",
            created_at=datetime.now() - timedelta(days=30),
            last_used=datetime.now() - timedelta(minutes=5),
            is_active=True,
            allowed_ips=["10.0.0.0/8", "**********/12"],
            rate_limit=1000
        )
        assert service_account.api_key_prefix == "ae_prod_"
        assert UserRole.SERVICE_ACCOUNT in service_account.roles
        
        # Invalid: api key prefix too short
        with pytest.raises(ValidationError):
            ServiceAccount(
                id="svc-123",
                name="service",
                permissions=["read"],
                api_key_prefix="short",  # Too short
                created_at=datetime.now()
            )

    def test_token_validation_contract(self):
        """Test token validation contract."""
        # Valid validation request
        validation_request = TokenValidationRequest(
            token="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
            token_type=TokenType.ACCESS,
            validate_permissions=["repo.read", "query.execute"],
            validate_roles=[UserRole.USER]
        )
        assert validation_request.token_type == TokenType.ACCESS
        
        # Valid validation response - success
        success_response = TokenValidationResponse(
            valid=True,
            subject="user-123",
            roles=[UserRole.USER, UserRole.DEVELOPER],
            permissions=["repo.read", "repo.write", "query.execute"],
            expires_at=datetime.now() + timedelta(hours=1),
            issued_at=datetime.now() - timedelta(minutes=5)
        )
        assert success_response.valid is True
        
        # Valid validation response - failure
        failure_response = TokenValidationResponse(
            valid=False,
            validation_errors=["Token expired", "Invalid signature"]
        )
        assert failure_response.valid is False
        assert len(failure_response.validation_errors) == 2

    def test_rate_limit_info_contract(self):
        """Test rate limit information contract."""
        # Valid rate limit info
        rate_limit = RateLimitInfo(
            limit=100,
            remaining=75,
            reset_at=datetime.now() + timedelta(minutes=10),
            window_seconds=3600  # 1 hour
        )
        assert rate_limit.remaining <= rate_limit.limit
        
        # Invalid: remaining exceeds limit
        with pytest.raises(ValidationError):
            RateLimitInfo(
                limit=100,
                remaining=150,  # Can't exceed limit
                reset_at=datetime.now(),
                window_seconds=3600
            )
        
        # Invalid: negative values
        with pytest.raises(ValidationError):
            RateLimitInfo(
                limit=-1,  # Must be positive
                remaining=0,
                reset_at=datetime.now(),
                window_seconds=3600
            )

    def test_auth_error_contract(self):
        """Test authentication error contract."""
        # Valid error response
        error = AuthError(
            error="invalid_grant",
            error_description="The provided credentials are invalid",
            error_uri="https://docs.example.com/auth/errors#invalid_grant",
            status_code=401,
            timestamp=datetime.now(),
            request_id="req-789"
        )
        assert error.error == "invalid_grant"
        assert error.status_code == 401
        
        # Invalid: unknown error type
        with pytest.raises(ValidationError):
            AuthError(
                error="unknown_error",  # Not in allowed values
                error_description="Unknown error",
                status_code=400,
                timestamp=datetime.now()
            )
        
        # Invalid: status code out of range
        with pytest.raises(ValidationError):
            AuthError(
                error="invalid_request",
                error_description="Bad request",
                status_code=200,  # Not an error code
                timestamp=datetime.now()
            )

    def test_jwt_token_generation_and_validation(self):
        """Test JWT token generation follows contract."""
        # Generate token
        secret_key = "test-secret-key"
        now = datetime.now()
        
        payload = TokenPayload(
            sub="user-123",
            email="<EMAIL>",
            roles=[UserRole.USER],
            permissions=["read"],
            exp=int((now + timedelta(hours=1)).timestamp()),
            iat=int(now.timestamp()),
            token_type=TokenType.ACCESS
        )
        
        # Create JWT
        token = jwt.encode(
            payload.model_dump(),
            secret_key,
            algorithm="HS256"
        )
        
        # Decode and validate
        decoded = jwt.decode(token, secret_key, algorithms=["HS256"])
        validated_payload = TokenPayload(**decoded)
        
        assert validated_payload.sub == payload.sub
        assert validated_payload.email == payload.email

    def test_permission_hierarchy(self):
        """Test permission hierarchy and inheritance."""
        # Admin has all permissions
        admin_user = UserInfo(
            id="admin-1",
            email="<EMAIL>",
            username="admin",
            roles=[UserRole.ADMIN],
            permissions=[],  # Admin implicitly has all permissions
            created_at=datetime.now()
        )
        
        # Developer has specific permissions
        dev_user = UserInfo(
            id="dev-1",
            email="<EMAIL>",
            username="developer",
            roles=[UserRole.DEVELOPER],
            permissions=["repo.read", "repo.write", "query.execute"],
            created_at=datetime.now()
        )
        
        # Viewer has limited permissions
        viewer_user = UserInfo(
            id="viewer-1",
            email="<EMAIL>",
            username="viewer",
            roles=[UserRole.VIEWER],
            permissions=["repo.read", "query.execute"],
            created_at=datetime.now()
        )
        
        # Verify role hierarchy
        assert UserRole.ADMIN in admin_user.roles
        assert len(dev_user.permissions) > len(viewer_user.permissions)

    def test_service_to_service_auth(self):
        """Test service-to-service authentication contract."""
        # Service account auth request
        service_auth = AuthRequest(
            grant_type="client_credentials",
            client_id="query-intelligence-service",
            client_secret="secure-secret-value",
            scope=["analysis.read", "pattern.read", "internal.api"]
        )
        
        # Service account response
        service_response = AuthResponse(
            access_token="service_token_value",
            token_type="Bearer",
            expires_in=3600,
            scope=service_auth.scope
        )
        
        # Service token payload
        service_payload = TokenPayload(
            sub="svc-query-intelligence",
            roles=[UserRole.SERVICE_ACCOUNT],
            permissions=service_auth.scope,
            exp=int((datetime.now() + timedelta(hours=1)).timestamp()),
            iat=int(datetime.now().timestamp()),
            token_type=TokenType.SERVICE,
            client_id="query-intelligence-service"
        )
        
        assert service_payload.token_type == TokenType.SERVICE
        assert UserRole.SERVICE_ACCOUNT in service_payload.roles