"""Comprehensive configuration settings tests."""

import os
import pytest
from unittest.mock import patch, MagicMock
from pydantic import ValidationError
from query_intelligence.config.settings import Settings, get_settings


class TestSettings:
    """Test Settings configuration."""

    def test_default_settings(self):
        """Test default settings initialization."""
        settings = Settings()
        assert settings.PROJECT_NAME == "Query Intelligence Service"
        assert settings.SERVICE_NAME == "query-intelligence"
        assert settings.PORT == 8002
        assert settings.ENVIRONMENT == "development"
        assert settings.LOG_LEVEL == "INFO"

    def test_service_url_defaults(self):
        """Test default service URL configurations."""
        settings = Settings()
        assert settings.REDIS_URL == "redis://localhost:6379"
        assert settings.ANALYSIS_ENGINE_URL == "http://localhost:8001"
        assert settings.PATTERN_MINING_URL == "http://localhost:8003"
        assert settings.COLLABORATION_URL == "http://localhost:8004"

    def test_model_configuration(self):
        """Test model configuration settings."""
        settings = Settings()
        assert settings.USE_VERTEX_AI is True
        assert settings.GEMINI_MODEL_NAME == "gemini-2.5-flash"
        assert settings.EMBEDDING_MODEL_NAME == "sentence-transformers/all-mpnet-base-v2"
        assert settings.USE_MODEL_ROUTING is True
        assert settings.SIMPLE_QUERY_MODEL == "gemini-2.5-flash-lite"
        assert settings.COMPLEX_QUERY_MODEL == "gemini-2.5-pro"

    def test_cache_configuration(self):
        """Test cache configuration settings."""
        settings = Settings()
        assert settings.CACHE_TTL_HOURS == 24
        assert settings.CACHE_MAX_SIZE == 10000
        assert settings.SEMANTIC_CACHE_ENABLED is True

    def test_performance_configuration(self):
        """Test performance configuration settings."""
        settings = Settings()
        assert settings.MAX_QUERY_LENGTH == 10000
        assert settings.MAX_CODE_CHUNKS == 20
        assert settings.MAX_RESPONSE_TOKENS == 2048
        assert settings.QUERY_TIMEOUT_SECONDS == 30

    def test_cloud_run_configuration(self):
        """Test Cloud Run optimization settings."""
        settings = Settings()
        assert settings.MIN_INSTANCES == 5
        assert settings.MAX_INSTANCES == 200
        assert settings.CONCURRENCY == 20
        assert settings.CPU_BOOST_ENABLED is True

    def test_security_configuration(self):
        """Test security configuration settings."""
        settings = Settings()
        assert settings.JWT_SECRET_KEY.startswith("dev-only-")
        assert settings.JWT_ALGORITHM == "HS256"
        assert settings.JWT_EXPIRATION_MINUTES == 60
        assert settings.API_KEY_HEADER == "X-API-Key"
        assert settings.USE_SECRET_MANAGER is False

    def test_rate_limiting_configuration(self):
        """Test rate limiting configuration."""
        settings = Settings()
        assert settings.RATE_LIMIT_REQUESTS == 100
        assert settings.RATE_LIMIT_WINDOW_SECONDS == 60
        assert settings.RATE_LIMIT_PER_USER is True

    def test_monitoring_configuration(self):
        """Test monitoring configuration."""
        settings = Settings()
        assert settings.ENABLE_METRICS is True
        assert settings.ENABLE_ADMIN_API is True
        assert settings.METRICS_PORT == 9090
        assert settings.ENABLE_TRACING is True

    def test_security_features(self):
        """Test security feature flags."""
        settings = Settings()
        assert settings.ENABLE_INPUT_VALIDATION is True
        assert settings.ENABLE_PROMPT_INJECTION_DETECTION is True
        assert settings.ENABLE_PII_DETECTION is True

    def test_cors_configuration(self):
        """Test CORS configuration."""
        settings = Settings()
        assert "http://localhost:3000" in settings.CORS_ALLOWED_ORIGINS
        assert settings.CORS_ALLOW_CREDENTIALS is True
        assert "POST" in settings.CORS_ALLOWED_METHODS
        assert "*" in settings.CORS_ALLOWED_HEADERS

    def test_circuit_breaker_configuration(self):
        """Test circuit breaker configuration."""
        settings = Settings()
        assert settings.CIRCUIT_BREAKER_FAILURE_THRESHOLD == 5
        assert settings.CIRCUIT_BREAKER_RECOVERY_TIMEOUT == 60
        assert settings.CIRCUIT_BREAKER_CALL_TIMEOUT == 10


class TestEnvironmentOverrides:
    """Test environment variable overrides."""

    def test_environment_override(self):
        """Test that environment variables override defaults."""
        with patch.dict(os.environ, {
            "PORT": "9000",
            "ENVIRONMENT": "production",
            "LOG_LEVEL": "DEBUG",
            "REDIS_URL": "redis://prod-redis:6379",
            "GEMINI_MODEL_NAME": "gemini-2.5-pro"
        }):
            settings = Settings()
            assert settings.PORT == 9000
            assert settings.ENVIRONMENT == "production"
            assert settings.LOG_LEVEL == "DEBUG"
            assert settings.REDIS_URL == "redis://prod-redis:6379"
            assert settings.GEMINI_MODEL_NAME == "gemini-2.5-pro"

    def test_boolean_environment_override(self):
        """Test boolean environment variable parsing."""
        with patch.dict(os.environ, {
            "USE_VERTEX_AI": "false",
            "USE_MODEL_ROUTING": "false",
            "SEMANTIC_CACHE_ENABLED": "false",
            "CPU_BOOST_ENABLED": "false"
        }):
            settings = Settings()
            assert settings.USE_VERTEX_AI is False
            assert settings.USE_MODEL_ROUTING is False
            assert settings.SEMANTIC_CACHE_ENABLED is False
            assert settings.CPU_BOOST_ENABLED is False

    def test_list_environment_override(self):
        """Test list environment variable parsing."""
        with patch.dict(os.environ, {
            "CORS_ALLOWED_ORIGINS": '["https://api.example.com", "https://app.example.com"]',
            "CORS_ALLOWED_METHODS": '["GET", "POST"]'
        }):
            settings = Settings()
            assert "https://api.example.com" in settings.CORS_ALLOWED_ORIGINS
            assert "https://app.example.com" in settings.CORS_ALLOWED_ORIGINS
            assert settings.CORS_ALLOWED_METHODS == ["GET", "POST"]

    def test_numeric_environment_override(self):
        """Test numeric environment variable parsing."""
        with patch.dict(os.environ, {
            "MAX_QUERY_LENGTH": "20000",
            "CACHE_TTL_HOURS": "48",
            "RATE_LIMIT_REQUESTS": "200",
            "MIN_INSTANCES": "10"
        }):
            settings = Settings()
            assert settings.MAX_QUERY_LENGTH == 20000
            assert settings.CACHE_TTL_HOURS == 48
            assert settings.RATE_LIMIT_REQUESTS == 200
            assert settings.MIN_INSTANCES == 10


class TestProductionValidation:
    """Test production configuration validation."""

    def test_development_settings_valid(self):
        """Test that development settings are valid."""
        settings = Settings(ENVIRONMENT="development")
        # Should not raise any errors
        settings.validate_production_settings()

    def test_production_jwt_validation(self):
        """Test JWT secret validation in production."""
        settings = Settings(ENVIRONMENT="production")
        with pytest.raises(ValueError, match="JWT_SECRET_KEY must not use development default"):
            settings.validate_production_settings()

    def test_production_secret_manager_required(self):
        """Test Secret Manager required in production."""
        settings = Settings(
            ENVIRONMENT="production",
            JWT_SECRET_KEY="proper-secret-key"
        )
        with pytest.raises(ValueError, match="USE_SECRET_MANAGER must be enabled"):
            settings.validate_production_settings()

    def test_production_google_api_key_forbidden(self):
        """Test Google API key forbidden in production."""
        settings = Settings(
            ENVIRONMENT="production",
            JWT_SECRET_KEY="proper-secret-key",
            USE_SECRET_MANAGER=True,
            GOOGLE_API_KEY="some-api-key"
        )
        with pytest.raises(ValueError, match="GOOGLE_API_KEY should not be used in production"):
            settings.validate_production_settings()

    def test_production_service_account_required(self):
        """Test service account required for Vertex AI in production."""
        settings = Settings(
            ENVIRONMENT="production",
            JWT_SECRET_KEY="proper-secret-key",
            USE_SECRET_MANAGER=True,
            USE_VERTEX_AI=True,
            SERVICE_ACCOUNT_PATH=None
        )
        with pytest.raises(ValueError, match="SERVICE_ACCOUNT_PATH required"):
            settings.validate_production_settings()

    def test_valid_production_configuration(self):
        """Test valid production configuration."""
        settings = Settings(
            ENVIRONMENT="production",
            JWT_SECRET_KEY="proper-secret-key",
            USE_SECRET_MANAGER=True,
            USE_VERTEX_AI=True,
            SERVICE_ACCOUNT_PATH="/path/to/service-account.json",
            GOOGLE_API_KEY=None
        )
        # Should not raise any errors
        settings.validate_production_settings()


class TestSecretManagerIntegration:
    """Test Secret Manager integration methods."""

    @patch("query_intelligence.services.secret_manager.get_jwt_secret")
    def test_get_jwt_secret_from_secret_manager(self, mock_get_jwt):
        """Test JWT secret retrieval from Secret Manager."""
        mock_get_jwt.return_value = "secret-from-manager"
        
        settings = Settings(USE_SECRET_MANAGER=True)
        secret = settings.get_jwt_secret()
        
        assert secret == "secret-from-manager"
        mock_get_jwt.assert_called_once()

    def test_get_jwt_secret_from_environment(self):
        """Test JWT secret retrieval from environment."""
        settings = Settings(
            USE_SECRET_MANAGER=False,
            JWT_SECRET_KEY="env-secret"
        )
        secret = settings.get_jwt_secret()
        assert secret == "env-secret"

    @patch("query_intelligence.services.secret_manager.get_pinecone_api_key")
    def test_get_pinecone_api_key_from_secret_manager(self, mock_get_pinecone):
        """Test Pinecone API key retrieval from Secret Manager."""
        mock_get_pinecone.return_value = "pinecone-key-from-manager"
        
        settings = Settings(USE_SECRET_MANAGER=True)
        key = settings.get_pinecone_api_key()
        
        assert key == "pinecone-key-from-manager"
        mock_get_pinecone.assert_called_once()

    def test_get_pinecone_api_key_from_environment(self):
        """Test Pinecone API key retrieval from environment."""
        settings = Settings(
            USE_SECRET_MANAGER=False,
            PINECONE_API_KEY="env-pinecone-key"
        )
        key = settings.get_pinecone_api_key()
        assert key == "env-pinecone-key"

    @patch("query_intelligence.services.secret_manager.get_google_api_key")
    def test_get_google_api_key_from_secret_manager(self, mock_get_google):
        """Test Google API key retrieval from Secret Manager."""
        mock_get_google.return_value = "google-key-from-manager"
        
        settings = Settings(USE_SECRET_MANAGER=True)
        key = settings.get_google_api_key()
        
        assert key == "google-key-from-manager"
        mock_get_google.assert_called_once()

    def test_get_google_api_key_from_environment(self):
        """Test Google API key retrieval from environment."""
        settings = Settings(
            USE_SECRET_MANAGER=False,
            GOOGLE_API_KEY="env-google-key"
        )
        key = settings.get_google_api_key()
        assert key == "env-google-key"


class TestHelperMethods:
    """Test helper methods on Settings class."""

    def test_is_production_true(self):
        """Test is_production returns True for production."""
        settings = Settings(ENVIRONMENT="production")
        assert settings.is_production() is True
        
        settings = Settings(ENVIRONMENT="PRODUCTION")
        assert settings.is_production() is True
        
        settings = Settings(ENVIRONMENT="Production")
        assert settings.is_production() is True

    def test_is_production_false(self):
        """Test is_production returns False for non-production."""
        settings = Settings(ENVIRONMENT="development")
        assert settings.is_production() is False
        
        settings = Settings(ENVIRONMENT="staging")
        assert settings.is_production() is False
        
        settings = Settings(ENVIRONMENT="test")
        assert settings.is_production() is False


class TestSettingsCache:
    """Test settings caching behavior."""

    @patch("query_intelligence.config.settings.Settings")
    def test_get_settings_cached(self, mock_settings_class):
        """Test that get_settings returns cached instance."""
        mock_instance = MagicMock()
        mock_settings_class.return_value = mock_instance
        
        # Clear the cache first
        get_settings.cache_clear()
        
        # First call
        settings1 = get_settings()
        # Second call
        settings2 = get_settings()
        
        # Should only create one instance
        mock_settings_class.assert_called_once()
        assert settings1 is settings2
        
        # Validate was called
        mock_instance.validate_production_settings.assert_called_once()


class TestEdgeCases:
    """Test edge cases and error conditions."""

    def test_empty_string_defaults(self):
        """Test handling of empty string environment variables."""
        with patch.dict(os.environ, {
            "REDIS_URL": "",
            "GOOGLE_API_KEY": "",
            "SERVICE_ACCOUNT_PATH": ""
        }):
            settings = Settings()
            # Empty strings should be preserved, not use defaults
            assert settings.REDIS_URL == ""
            assert settings.GOOGLE_API_KEY == ""
            assert settings.SERVICE_ACCOUNT_PATH == ""

    def test_invalid_numeric_values(self):
        """Test handling of invalid numeric environment variables."""
        with patch.dict(os.environ, {"PORT": "not-a-number"}):
            with pytest.raises(ValidationError):
                Settings()

    def test_invalid_boolean_values(self):
        """Test handling of invalid boolean environment variables."""
        with patch.dict(os.environ, {"USE_VERTEX_AI": "maybe"}):
            with pytest.raises(ValidationError):
                Settings()

    def test_malformed_json_lists(self):
        """Test handling of malformed JSON in list environment variables."""
        with patch.dict(os.environ, {"CORS_ALLOWED_ORIGINS": "not-json"}):
            with pytest.raises(ValidationError):
                Settings()

    def test_extreme_values(self):
        """Test handling of extreme configuration values."""
        settings = Settings(
            PORT=65535,  # Max port
            MAX_QUERY_LENGTH=1000000,  # Very large
            CACHE_TTL_HOURS=8760,  # One year
            RATE_LIMIT_REQUESTS=10000,
            MIN_INSTANCES=1000,
            MAX_INSTANCES=10000
        )
        # Should handle extreme values without error
        assert settings.PORT == 65535
        assert settings.MAX_QUERY_LENGTH == 1000000

    def test_negative_values_rejected(self):
        """Test that negative values are rejected where inappropriate."""
        with pytest.raises(ValidationError):
            Settings(PORT=-1)
        
        with pytest.raises(ValidationError):
            Settings(CACHE_TTL_HOURS=-1)
        
        with pytest.raises(ValidationError):
            Settings(MAX_QUERY_LENGTH=-1)