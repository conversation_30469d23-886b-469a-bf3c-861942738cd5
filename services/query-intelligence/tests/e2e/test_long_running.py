"""End-to-end tests for long-running operations."""

import asyncio
import pytest
import time
import psutil
import gc
from unittest.mock import patch, MagicMock, AsyncMock
from datetime import datetime, timedelta
from query_intelligence.services.query_processor import QueryProcessor
from query_intelligence.api.websocket import WebSocketManager
from query_intelligence.models.query import QueryRequest, QueryStreamChunk
from query_intelligence.middleware.auth import JWTBearer
import jwt


class TestWebSocketConnectionStability:
    """Test WebSocket connection stability over extended periods."""

    @pytest.mark.asyncio
    async def test_websocket_5_minute_streaming(self):
        """Test WebSocket streaming for 5 minutes continuously."""
        manager = WebSocketManager()
        mock_websocket = MagicMock()
        mock_websocket.send_json = AsyncMock()
        mock_websocket.close = AsyncMock()
        
        # Connect client
        await manager.connect(mock_websocket, "test-user")
        
        # Simulate 5 minutes of streaming
        start_time = time.time()
        chunks_sent = 0
        
        while time.time() - start_time < 0.5:  # Simulated 5 minutes (0.5s for test)
            chunk = QueryStreamChunk(
                type="text",
                content=f"Streaming chunk {chunks_sent}",
                done=False
            )
            
            await manager.send_to_user("test-user", chunk.model_dump())
            chunks_sent += 1
            
            # Simulate realistic streaming pace
            await asyncio.sleep(0.01)  # Would be 0.1s in production
            
            # Periodic ping to keep connection alive
            if chunks_sent % 50 == 0:
                await manager.send_ping("test-user")
        
        # Send completion
        final_chunk = QueryStreamChunk(type="done", done=True)
        await manager.send_to_user("test-user", final_chunk.model_dump())
        
        # Verify connection remained stable
        assert manager.is_connected("test-user")
        assert chunks_sent > 40  # Should have sent many chunks
        
        # Disconnect cleanly
        await manager.disconnect(mock_websocket)

    @pytest.mark.asyncio
    async def test_websocket_10_minute_idle_with_keepalive(self):
        """Test WebSocket connection idle for 10 minutes with keepalive."""
        manager = WebSocketManager()
        mock_websocket = MagicMock()
        mock_websocket.ping = AsyncMock()
        mock_websocket.close = AsyncMock()
        
        # Connect client
        await manager.connect(mock_websocket, "test-user")
        
        # Simulate 10 minutes with periodic keepalive
        keepalive_count = 0
        for minute in range(10):
            # Wait 1 minute (simulated as 0.1s)
            await asyncio.sleep(0.1)
            
            # Send keepalive ping
            await manager.send_ping("test-user")
            keepalive_count += 1
            
            # Verify connection still active
            assert manager.is_connected("test-user")
        
        assert keepalive_count == 10
        mock_websocket.ping.assert_called()

    @pytest.mark.asyncio
    async def test_websocket_30_minute_session(self):
        """Test WebSocket session lasting 30 minutes with activity."""
        manager = WebSocketManager()
        processor = QueryProcessor()
        mock_websocket = MagicMock()
        mock_websocket.send_json = AsyncMock()
        
        await manager.connect(mock_websocket, "test-user")
        
        # Simulate 30-minute session with periodic queries
        session_start = time.time()
        query_count = 0
        
        # Simulate 30 minutes as 3 seconds for testing
        while time.time() - session_start < 3:
            # Send a query every "5 minutes"
            request = QueryRequest(
                query=f"Query {query_count} at {datetime.now()}",
                repository_id="test-repo",
                stream=True
            )
            
            # Process with streaming
            async for chunk in processor.stream_query(request):
                await manager.send_to_user("test-user", chunk.model_dump())
            
            query_count += 1
            await asyncio.sleep(0.5)  # "5 minutes" between queries
        
        # Session should have processed multiple queries
        assert query_count >= 5
        assert manager.is_connected("test-user")


class TestMemoryLeakDetection:
    """Test for memory leaks during long-running operations."""

    @pytest.mark.asyncio
    async def test_no_memory_leak_1000_queries(self):
        """Test that processing 1000 queries doesn't leak memory."""
        processor = QueryProcessor()
        
        # Force garbage collection and get baseline
        gc.collect()
        process = psutil.Process()
        baseline_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Process 1000 queries
        for i in range(100):  # Reduced for test performance
            request = QueryRequest(
                query=f"Test query {i} with some content to process",
                repository_id=f"repo-{i % 10}"
            )
            
            result = await processor.process_query(request)
            
            # Periodically force garbage collection
            if i % 100 == 0:
                gc.collect()
        
        # Final garbage collection
        gc.collect()
        await asyncio.sleep(0.1)  # Allow cleanup
        
        # Check final memory
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - baseline_memory
        
        # Should not increase by more than 100MB
        assert memory_increase < 100, f"Memory increased by {memory_increase}MB"

    @pytest.mark.asyncio
    async def test_cache_memory_bounds(self):
        """Test that cache respects memory bounds over time."""
        from query_intelligence.services.cache_manager import CacheManager
        
        cache = CacheManager()
        process = psutil.Process()
        
        # Add many items to cache
        for i in range(1000):
            # Create varying size cache entries
            data_size = 1000 * (i % 10 + 1)  # 1KB to 10KB
            data = "x" * data_size
            
            await cache.set(f"key_{i}", data)
            
            # Check memory periodically
            if i % 100 == 0:
                memory_mb = process.memory_info().rss / 1024 / 1024
                cache_size = await cache.get_size()
                
                # Cache should self-regulate
                assert cache_size <= cache.max_size
                assert memory_mb < 1000  # Should stay under 1GB

    @pytest.mark.asyncio
    async def test_websocket_connection_cleanup(self):
        """Test that WebSocket connections are properly cleaned up."""
        manager = WebSocketManager()
        
        # Track initial state
        initial_connections = len(manager.active_connections)
        
        # Create and destroy many connections
        for i in range(100):
            mock_ws = MagicMock()
            mock_ws.close = AsyncMock()
            
            # Connect
            await manager.connect(mock_ws, f"user-{i}")
            
            # Send some data
            for j in range(10):
                await manager.send_to_user(f"user-{i}", {"data": f"message-{j}"})
            
            # Disconnect
            await manager.disconnect(mock_ws)
        
        # All connections should be cleaned up
        final_connections = len(manager.active_connections)
        assert final_connections == initial_connections


class TestTokenRefreshScenarios:
    """Test JWT token refresh during long operations."""

    @pytest.mark.asyncio
    async def test_token_refresh_during_5_minute_operation(self):
        """Test token refresh during a 5-minute operation."""
        jwt_bearer = JWTBearer()
        
        # Create token that expires in 2 minutes
        secret_key = "test-secret"
        initial_payload = {
            "sub": "test-user",
            "exp": int(time.time()) + 120  # 2 minutes
        }
        initial_token = jwt.encode(initial_payload, secret_key, algorithm="HS256")
        
        # Mock time progression
        time_progression = [0, 60, 120, 180, 240, 300]  # 5 minutes
        
        with patch('time.time', side_effect=time_progression):
            # Start long operation
            processor = QueryProcessor()
            
            # Mock long-running LLM call
            with patch.object(processor.llm_service, 'generate_response', new_callable=AsyncMock) as mock_llm:
                async def long_operation(*args, **kwargs):
                    # Simulate 5-minute operation
                    for _ in range(5):
                        await asyncio.sleep(0.1)  # Simulated minute
                        
                        # Check if token needs refresh
                        current_time = time.time()
                        if current_time > initial_payload["exp"]:
                            # Token expired, should trigger refresh
                            pass
                    
                    return "Long operation completed"
                
                mock_llm.side_effect = long_operation
                
                request = QueryRequest(
                    query="Long analysis task",
                    repository_id="test-repo"
                )
                
                # Process with token refresh handling
                with patch.object(jwt_bearer, 'refresh_token', return_value="refreshed-token"):
                    result = await processor.process_query(request, auth_token=initial_token)
                    assert result is not None

    @pytest.mark.asyncio
    async def test_token_refresh_failure_handling(self):
        """Test handling of token refresh failures."""
        jwt_bearer = JWTBearer()
        processor = QueryProcessor()
        
        # Create expired token
        expired_payload = {
            "sub": "test-user",
            "exp": int(time.time()) - 60  # Already expired
        }
        expired_token = jwt.encode(expired_payload, "test-secret", algorithm="HS256")
        
        # Mock refresh failure
        with patch.object(jwt_bearer, 'refresh_token', side_effect=Exception("Refresh failed")):
            request = QueryRequest(
                query="Test query",
                repository_id="test-repo"
            )
            
            # Should handle gracefully
            with pytest.raises(Exception, match="Refresh failed"):
                await processor.process_query(request, auth_token=expired_token)


class TestGracefulShutdownScenarios:
    """Test graceful shutdown in various scenarios."""

    @pytest.mark.asyncio
    async def test_shutdown_with_active_websockets(self):
        """Test shutdown with multiple active WebSocket connections."""
        manager = WebSocketManager()
        processor = QueryProcessor()
        
        # Create multiple active connections
        connections = []
        for i in range(10):
            mock_ws = MagicMock()
            mock_ws.close = AsyncMock()
            await manager.connect(mock_ws, f"user-{i}")
            connections.append((mock_ws, f"user-{i}"))
        
        # Start streaming operations
        stream_tasks = []
        for ws, user_id in connections:
            request = QueryRequest(
                query=f"Streaming query for {user_id}",
                repository_id="test-repo",
                stream=True
            )
            
            async def stream_to_user(req, uid):
                async for chunk in processor.stream_query(req):
                    await manager.send_to_user(uid, chunk.model_dump())
            
            task = asyncio.create_task(stream_to_user(request, user_id))
            stream_tasks.append(task)
        
        # Simulate shutdown after brief delay
        await asyncio.sleep(0.1)
        
        # Initiate graceful shutdown
        shutdown_start = time.time()
        
        # Cancel all streaming tasks
        for task in stream_tasks:
            task.cancel()
        
        # Close all WebSocket connections
        for ws, user_id in connections:
            await manager.disconnect(ws)
        
        # Wait for cleanup
        await asyncio.gather(*stream_tasks, return_exceptions=True)
        
        shutdown_duration = time.time() - shutdown_start
        
        # Shutdown should be quick
        assert shutdown_duration < 5.0
        assert len(manager.active_connections) == 0

    @pytest.mark.asyncio
    async def test_shutdown_during_cache_operations(self):
        """Test shutdown while cache operations are in progress."""
        from query_intelligence.services.cache_manager import CacheManager
        
        cache = CacheManager()
        
        # Start many cache operations
        cache_tasks = []
        for i in range(100):
            # Mix of read and write operations
            if i % 2 == 0:
                task = cache.set(f"key_{i}", f"value_{i}" * 100)
            else:
                task = cache.get(f"key_{i-1}")
            
            cache_tasks.append(asyncio.create_task(task))
        
        # Simulate shutdown after brief delay
        await asyncio.sleep(0.05)
        
        # Cancel all cache operations
        for task in cache_tasks:
            task.cancel()
        
        # Wait for cleanup
        results = await asyncio.gather(*cache_tasks, return_exceptions=True)
        
        # Count completed vs cancelled
        completed = sum(1 for r in results if not isinstance(r, asyncio.CancelledError))
        cancelled = sum(1 for r in results if isinstance(r, asyncio.CancelledError))
        
        # Some should complete, some should be cancelled
        assert completed > 0
        assert cancelled > 0

    @pytest.mark.asyncio
    async def test_shutdown_with_pending_llm_calls(self):
        """Test shutdown with pending LLM API calls."""
        processor = QueryProcessor()
        
        # Mock slow LLM responses
        with patch.object(processor.llm_service, 'generate_response', new_callable=AsyncMock) as mock_llm:
            async def slow_llm_response(*args, **kwargs):
                await asyncio.sleep(5)  # 5 second response time
                return "Slow response"
            
            mock_llm.side_effect = slow_llm_response
            
            # Start multiple LLM calls
            llm_tasks = []
            for i in range(5):
                request = QueryRequest(
                    query=f"Complex query {i}",
                    repository_id="test-repo"
                )
                task = asyncio.create_task(processor.process_query(request))
                llm_tasks.append(task)
            
            # Wait briefly then shutdown
            await asyncio.sleep(0.1)
            
            # Cancel all LLM operations
            for task in llm_tasks:
                task.cancel()
            
            # Verify graceful cancellation
            results = await asyncio.gather(*llm_tasks, return_exceptions=True)
            
            # All should be cancelled
            assert all(isinstance(r, asyncio.CancelledError) for r in results)


class TestExtendedStressScenarios:
    """Extended stress testing scenarios."""

    @pytest.mark.asyncio
    async def test_sustained_high_load_15_minutes(self):
        """Test sustained high load for 15 minutes."""
        processor = QueryProcessor()
        
        # Track metrics
        start_time = time.time()
        total_queries = 0
        successful_queries = 0
        failed_queries = 0
        response_times = []
        
        # Simulate 15 minutes as 1.5 seconds
        while time.time() - start_time < 1.5:
            # Batch of concurrent queries
            batch_size = 50
            batch_tasks = []
            
            for i in range(batch_size):
                request = QueryRequest(
                    query=f"Load test query {total_queries + i}",
                    repository_id=f"repo-{i % 10}"
                )
                
                query_start = time.time()
                task = asyncio.create_task(processor.process_query(request))
                batch_tasks.append((task, query_start))
            
            # Wait for batch completion
            for task, query_start in batch_tasks:
                try:
                    result = await task
                    response_time = time.time() - query_start
                    response_times.append(response_time)
                    successful_queries += 1
                except Exception:
                    failed_queries += 1
                
                total_queries += 1
            
            # Brief pause between batches
            await asyncio.sleep(0.01)
        
        # Calculate statistics
        avg_response_time = sum(response_times) / len(response_times) if response_times else 0
        success_rate = successful_queries / total_queries if total_queries > 0 else 0
        
        # Verify performance under sustained load
        assert success_rate > 0.95  # 95% success rate
        assert avg_response_time < 0.5  # Sub-500ms average response
        assert total_queries > 500  # Processed many queries