# Query Intelligence Monitoring Documentation

## 📍 Documentation Moved

The monitoring documentation has been consolidated and moved to a centralized location for better organization and accessibility.

## 🔗 New Location

The comprehensive monitoring documentation is now available at:

**Main Documentation**: `/docs/query-intelligence/monitoring/`

### Available Guides
- **[Monitoring Guide](../../../../docs/query-intelligence/monitoring/monitoring-guide.md)** - Complete monitoring setup and operations
- **[Alerting Guide](../../../../docs/query-intelligence/monitoring/alerting-guide.md)** - Alerting configuration and procedures  
- **[Dashboards Guide](../../../../docs/query-intelligence/monitoring/dashboards-guide.md)** - Dashboard setup and usage
- **[Performance Monitoring](../../../../docs/query-intelligence/performance/performance-monitoring.md)** - Performance metrics and analysis

## 📋 What's Still Here

The following infrastructure files remain in this location:
- **Infrastructure Configuration**: `/services/query-intelligence/monitoring/`
  - `alerting/` - Prometheus alerts and escalation procedures
  - `dashboards/` - Grafana dashboards (JSON format)
  - `observability/` - SLI/SLO configuration and enhanced metrics
  - `scripts/` - Deployment and management scripts

## 🚀 Quick Access

### Essential Links
- **Service Health**: https://query-intelligence-l3nxty7oka-uc.a.run.app/health
- **Service Readiness**: https://query-intelligence-l3nxty7oka-uc.a.run.app/ready
- **Operations Runbook**: [/docs/query-intelligence/operations/runbook.md](../../../../docs/query-intelligence/operations/runbook.md)

### Deployment Commands
```bash
# Deploy monitoring infrastructure
cd /services/query-intelligence/monitoring
./scripts/deploy-monitoring.sh

# Import dashboards
./scripts/import-dashboards.sh

# Configure alerting  
kubectl apply -f alerting/prometheus-alerts.yml
```

## 📚 Legacy Documentation

The original deployment and operations guides in this directory have been superseded by the consolidated documentation. Please use the new centralized location for the most up-to-date information.

### Migration Summary
- ✅ **Deployment Guide** → Consolidated into [Monitoring Guide](../../../../docs/query-intelligence/monitoring/monitoring-guide.md)
- ✅ **Operations Guide** → Consolidated into [Monitoring Guide](../../../../docs/query-intelligence/monitoring/monitoring-guide.md)
- ✅ **Alerting Configuration** → Dedicated [Alerting Guide](../../../../docs/query-intelligence/monitoring/alerting-guide.md)
- ✅ **Dashboard Management** → Dedicated [Dashboards Guide](../../../../docs/query-intelligence/monitoring/dashboards-guide.md)

## 🔧 Infrastructure Configuration

This directory still contains the actual infrastructure configuration files that are deployed to production:

```
monitoring/
├── alerting/
│   ├── alertmanager-config.yml
│   ├── escalation-procedures.yml
│   └── prometheus-alerts.yml
├── dashboards/
│   ├── executive-dashboard.json
│   ├── technical-performance-dashboard.json
│   ├── realtime-operations-dashboard.json
│   ├── security-monitoring-dashboard.json
│   └── capacity-planning-dashboard.json
├── observability/
│   ├── enhanced-metrics.py
│   ├── sli-slo-config.yml
│   └── tracing-config.py
└── scripts/
    ├── deploy-monitoring.sh
    ├── import-dashboards.sh
    └── setup-monitoring.py
```

## 💡 Need Help?

For monitoring-related issues:
1. **Check the new documentation**: [/docs/query-intelligence/monitoring/](../../../../docs/query-intelligence/monitoring/)
2. **Review the operations runbook**: [/docs/query-intelligence/operations/runbook.md](../../../../docs/query-intelligence/operations/runbook.md)
3. **Contact the Query Intelligence team**: #query-intelligence-alerts

---

**Last Updated**: July 18, 2025
**Next Review**: August 18, 2025
**Document Owner**: Query Intelligence Team