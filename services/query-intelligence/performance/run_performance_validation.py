#!/usr/bin/env python3
"""
Performance Validation Runner for Query Intelligence Service

Orchestrates comprehensive performance testing to validate 1000+ QPS capability.
Runs Artillery and K6 load tests, analyzes results, and generates performance report.
"""

import asyncio
import subprocess
import json
import os
import sys
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List, Optional
import statistics
import yaml
import shutil

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.query_intelligence.config import get_settings


class PerformanceValidator:
    """Orchestrates performance validation testing"""
    
    def __init__(self, 
                 results_dir: str = "performance_validation_results",
                 target_qps: int = 1000,
                 target_p95_ms: int = 100,
                 target_p99_ms: int = 500,
                 min_success_rate: float = 99.0):
        
        self.results_dir = Path(results_dir)
        self.results_dir.mkdir(parents=True, exist_ok=True)
        
        # Performance targets
        self.target_qps = target_qps
        self.target_p95_ms = target_p95_ms
        self.target_p99_ms = target_p99_ms
        self.min_success_rate = min_success_rate
        
        # Test configuration
        self.artillery_config = Path(__file__).parent / "load-tests" / "artillery-config.yml"
        self.k6_script = Path(__file__).parent / "load-tests" / "k6-load-test.js"
        
        # Results storage
        self.validation_results = {
            "timestamp": datetime.now().isoformat(),
            "targets": {
                "qps": target_qps,
                "p95_response_time_ms": target_p95_ms,
                "p99_response_time_ms": target_p99_ms,
                "min_success_rate_percent": min_success_rate
            },
            "results": {},
            "passed": False
        }
        
    def log(self, message: str, level: str = "INFO"):
        """Log message with timestamp"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] {level}: {message}")
        
    async def run_validation(self) -> Dict[str, Any]:
        """Run complete performance validation suite"""
        self.log("Starting Query Intelligence Performance Validation")
        self.log(f"Target: {self.target_qps}+ QPS with P95 < {self.target_p95_ms}ms")
        
        try:
            # Step 1: Check prerequisites
            self.log("Step 1: Checking prerequisites...")
            await self._check_prerequisites()
            
            # Step 2: Warm up the service
            self.log("Step 2: Warming up the service...")
            await self._warmup_service()
            
            # Step 3: Run baseline performance test
            self.log("Step 3: Running baseline performance test...")
            baseline_results = await self._run_baseline_test()
            self.validation_results["results"]["baseline"] = baseline_results
            
            # Step 4: Run Artillery sustained load test
            self.log("Step 4: Running Artillery sustained load test (1000+ QPS)...")
            artillery_results = await self._run_artillery_test()
            self.validation_results["results"]["artillery"] = artillery_results
            
            # Step 5: Run K6 load test
            self.log("Step 5: Running K6 comprehensive load test...")
            k6_results = await self._run_k6_test()
            self.validation_results["results"]["k6"] = k6_results
            
            # Step 6: Run spike test
            self.log("Step 6: Running spike test (2000 QPS)...")
            spike_results = await self._run_spike_test()
            self.validation_results["results"]["spike"] = spike_results
            
            # Step 7: Analyze comprehensive results
            self.log("Step 7: Analyzing comprehensive results...")
            analysis = await self._analyze_results()
            self.validation_results["analysis"] = analysis
            
            # Step 8: Generate performance report
            self.log("Step 8: Generating performance validation report...")
            report = await self._generate_report()
            self.validation_results["report"] = report
            
            # Determine if validation passed
            self.validation_results["passed"] = self._check_validation_passed(analysis)
            
            # Save results
            await self._save_results()
            
            # Print summary
            self._print_summary(analysis)
            
            return self.validation_results
            
        except Exception as e:
            self.log(f"Error during validation: {str(e)}", "ERROR")
            self.validation_results["error"] = str(e)
            await self._save_results()
            raise
            
    async def _check_prerequisites(self):
        """Check that required tools are installed"""
        tools = {
            "artillery": ["artillery", "--version"],
            "k6": ["k6", "version"],
            "docker": ["docker", "--version"]
        }
        
        for tool, cmd in tools.items():
            try:
                result = subprocess.run(cmd, capture_output=True, text=True)
                if result.returncode != 0:
                    raise Exception(f"{tool} is not installed or not in PATH")
                self.log(f"✓ {tool} is available")
            except FileNotFoundError:
                raise Exception(f"{tool} is required but not found. Please install it.")
                
    async def _warmup_service(self):
        """Warm up the service with initial requests"""
        settings = get_settings()
        base_url = os.getenv("BASE_URL", "http://localhost:8002")
        
        self.log("Sending warmup requests...")
        
        # Send 100 warmup requests
        for i in range(100):
            try:
                cmd = [
                    "curl", "-X", "POST",
                    f"{base_url}/api/v1/query",
                    "-H", "Content-Type: application/json",
                    "-H", f"Authorization: Bearer test-token",
                    "-d", json.dumps({
                        "query": f"Warmup query {i}: How does authentication work?",
                        "repository_id": "test-repo"
                    }),
                    "-s", "-o", "/dev/null"
                ]
                
                subprocess.run(cmd, check=False)
                
                if i % 20 == 0:
                    self.log(f"  Sent {i+1} warmup requests...")
                    
            except Exception as e:
                self.log(f"Warmup request failed: {e}", "WARNING")
                
        self.log("✓ Warmup completed")
        
    async def _run_baseline_test(self) -> Dict[str, Any]:
        """Run baseline performance test"""
        self.log("Running 60-second baseline test at 10 QPS...")
        
        baseline_config = {
            "config": {
                "target": os.getenv("BASE_URL", "http://localhost:8002"),
                "phases": [
                    {
                        "duration": 60,
                        "arrivalRate": 10,
                        "name": "Baseline"
                    }
                ],
                "processor": str(Path(__file__).parent / "load-tests" / "performance-processor.js")
            },
            "scenarios": [{
                "name": "Baseline Query",
                "weight": 100,
                "flow": [{
                    "post": {
                        "url": "/api/v1/query",
                        "headers": {
                            "Authorization": "Bearer test-token",
                            "Content-Type": "application/json"
                        },
                        "json": {
                            "query": "{{ queries[Math.floor(Math.random() * queries.length)] }}",
                            "repository_id": "test-repo"
                        },
                        "capture": [
                            {"json": "$.execution_time_ms", "as": "response_time"}
                        ]
                    }
                }]
            }]
        }
        
        # Write temporary config
        baseline_config_path = self.results_dir / "baseline-config.yml"
        with open(baseline_config_path, 'w') as f:
            yaml.dump(baseline_config, f)
            
        # Run baseline test
        baseline_output = self.results_dir / "baseline-results.json"
        
        cmd = [
            "artillery", "run",
            str(baseline_config_path),
            "--output", str(baseline_output)
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode != 0:
            self.log(f"Baseline test failed: {result.stderr}", "ERROR")
            return {"error": result.stderr}
            
        # Parse results
        try:
            with open(baseline_output, 'r') as f:
                results = json.load(f)
                
            summary = results.get("aggregate", {})
            
            return {
                "duration_seconds": 60,
                "target_qps": 10,
                "actual_qps": summary.get("rates", {}).get("http.request_rate", 0),
                "total_requests": summary.get("counters", {}).get("http.requests", 0),
                "success_rate": 100 - (summary.get("counters", {}).get("http.codes.4xx", 0) + 
                                     summary.get("counters", {}).get("http.codes.5xx", 0)) / 
                                     max(summary.get("counters", {}).get("http.requests", 1), 1) * 100,
                "response_times": {
                    "min": summary.get("summaries", {}).get("http.response_time", {}).get("min", 0),
                    "max": summary.get("summaries", {}).get("http.response_time", {}).get("max", 0),
                    "median": summary.get("summaries", {}).get("http.response_time", {}).get("median", 0),
                    "p95": summary.get("summaries", {}).get("http.response_time", {}).get("p95", 0),
                    "p99": summary.get("summaries", {}).get("http.response_time", {}).get("p99", 0)
                }
            }
            
        except Exception as e:
            self.log(f"Failed to parse baseline results: {e}", "ERROR")
            return {"error": str(e)}
            
    async def _run_artillery_test(self) -> Dict[str, Any]:
        """Run Artillery sustained load test"""
        self.log("Running Artillery sustained load test...")
        self.log("This will simulate 1000+ QPS for 30 minutes")
        
        # For actual testing, we'll run a shorter version
        # In production, this would run the full 30-minute test
        artillery_output = self.results_dir / "artillery-results.json"
        
        # Prepare environment variables
        env = os.environ.copy()
        env["BASE_URL"] = os.getenv("BASE_URL", "http://localhost:8002")
        env["AUTH_TOKEN"] = "test-token"
        
        cmd = [
            "artillery", "run",
            str(self.artillery_config),
            "--output", str(artillery_output),
            "--environment", "development"  # Use shorter test duration
        ]
        
        self.log("Starting Artillery test (this may take several minutes)...")
        
        result = subprocess.run(cmd, capture_output=True, text=True, env=env)
        
        if result.returncode != 0:
            self.log(f"Artillery test failed: {result.stderr}", "ERROR")
            return {"error": result.stderr}
            
        # Parse results
        try:
            with open(artillery_output, 'r') as f:
                results = json.load(f)
                
            # Extract key metrics
            aggregate = results.get("aggregate", {})
            
            # Calculate actual QPS
            duration = aggregate.get("testRunDurationMs", 1000) / 1000
            total_requests = aggregate.get("counters", {}).get("http.requests", 0)
            actual_qps = total_requests / duration if duration > 0 else 0
            
            # Calculate success rate
            total_responses = aggregate.get("counters", {}).get("http.responses", 0)
            error_responses = (aggregate.get("counters", {}).get("http.codes.4xx", 0) + 
                             aggregate.get("counters", {}).get("http.codes.5xx", 0))
            success_rate = ((total_responses - error_responses) / total_responses * 100) if total_responses > 0 else 0
            
            return {
                "test_type": "artillery_sustained_load",
                "duration_seconds": duration,
                "target_qps": self.target_qps,
                "actual_qps": actual_qps,
                "total_requests": total_requests,
                "total_responses": total_responses,
                "success_rate": success_rate,
                "error_count": error_responses,
                "response_times": {
                    "min": aggregate.get("summaries", {}).get("http.response_time", {}).get("min", 0),
                    "max": aggregate.get("summaries", {}).get("http.response_time", {}).get("max", 0),
                    "mean": aggregate.get("summaries", {}).get("http.response_time", {}).get("mean", 0),
                    "median": aggregate.get("summaries", {}).get("http.response_time", {}).get("median", 0),
                    "p95": aggregate.get("summaries", {}).get("http.response_time", {}).get("p95", 0),
                    "p99": aggregate.get("summaries", {}).get("http.response_time", {}).get("p99", 0),
                    "p999": aggregate.get("summaries", {}).get("http.response_time", {}).get("p999", 0)
                },
                "custom_metrics": {
                    "cache_hit_rate": aggregate.get("customStats", {}).get("cache.hit_rate", {}).get("mean", 0),
                    "query_confidence": aggregate.get("customStats", {}).get("query.confidence", {}).get("mean", 0)
                },
                "phases": results.get("phases", [])
            }
            
        except Exception as e:
            self.log(f"Failed to parse Artillery results: {e}", "ERROR")
            return {"error": str(e)}
            
    async def _run_k6_test(self) -> Dict[str, Any]:
        """Run K6 comprehensive load test"""
        self.log("Running K6 comprehensive load test...")
        
        k6_output = self.results_dir / "k6-results.json"
        
        # Prepare environment variables
        env = os.environ.copy()
        env["BASE_URL"] = os.getenv("BASE_URL", "http://localhost:8002")
        env["AUTH_TOKEN"] = "test-token"
        
        # For testing, we'll use a shorter duration
        # Override the stages for faster testing
        cmd = [
            "k6", "run",
            "--out", f"json={k6_output}",
            "--duration", "5m",  # Shorter duration for testing
            "--vus", "100",
            "--rps", "1000",
            str(self.k6_script)
        ]
        
        self.log("Starting K6 test (this may take several minutes)...")
        
        result = subprocess.run(cmd, capture_output=True, text=True, env=env)
        
        if result.returncode != 0:
            self.log(f"K6 test output: {result.stdout}", "DEBUG")
            self.log(f"K6 test failed: {result.stderr}", "ERROR")
            
            # Try to extract summary from stdout
            summary = self._parse_k6_stdout(result.stdout)
            if summary:
                return summary
            return {"error": result.stderr}
            
        # Parse K6 JSON output
        try:
            metrics = self._parse_k6_json(k6_output)
            return metrics
            
        except Exception as e:
            self.log(f"Failed to parse K6 results: {e}", "ERROR")
            # Try to parse stdout as fallback
            summary = self._parse_k6_stdout(result.stdout)
            if summary:
                return summary
            return {"error": str(e)}
            
    def _parse_k6_json(self, output_file: Path) -> Dict[str, Any]:
        """Parse K6 JSON output file"""
        metrics = {
            "test_type": "k6_comprehensive",
            "duration_seconds": 0,
            "total_requests": 0,
            "success_rate": 0,
            "response_times": {},
            "custom_metrics": {}
        }
        
        response_times = []
        errors = 0
        cache_hits = 0
        cache_total = 0
        confidence_scores = []
        
        with open(output_file, 'r') as f:
            for line in f:
                try:
                    data = json.loads(line)
                    
                    if data.get("type") == "Point":
                        metric = data.get("metric")
                        value = data.get("data", {}).get("value", 0)
                        
                        if metric == "http_req_duration":
                            response_times.append(value)
                        elif metric == "http_req_failed":
                            if value == 1:
                                errors += 1
                        elif metric == "query_response_time":
                            response_times.append(value)
                        elif metric == "query_confidence_score":
                            confidence_scores.append(value)
                        elif metric == "cache_hit_rate":
                            if value == 1:
                                cache_hits += 1
                            cache_total += 1
                            
                except json.JSONDecodeError:
                    continue
                    
        if response_times:
            response_times.sort()
            metrics["response_times"] = {
                "min": response_times[0],
                "max": response_times[-1],
                "mean": statistics.mean(response_times),
                "median": statistics.median(response_times),
                "p95": response_times[int(len(response_times) * 0.95)],
                "p99": response_times[int(len(response_times) * 0.99)]
            }
            
        metrics["total_requests"] = len(response_times)
        metrics["success_rate"] = ((len(response_times) - errors) / len(response_times) * 100) if response_times else 0
        
        if cache_total > 0:
            metrics["custom_metrics"]["cache_hit_rate"] = (cache_hits / cache_total) * 100
            
        if confidence_scores:
            metrics["custom_metrics"]["avg_confidence"] = statistics.mean(confidence_scores)
            
        return metrics
        
    def _parse_k6_stdout(self, stdout: str) -> Optional[Dict[str, Any]]:
        """Parse K6 stdout output as fallback"""
        # This is a simplified parser for K6 console output
        # In production, we'd use the JSON output
        
        metrics = {
            "test_type": "k6_comprehensive",
            "source": "stdout_parser"
        }
        
        # Extract key metrics using regex or string parsing
        # This is a placeholder - actual implementation would parse the K6 summary
        
        return metrics
        
    async def _run_spike_test(self) -> Dict[str, Any]:
        """Run spike test at 2000 QPS"""
        self.log("Running spike test at 2000 QPS...")
        
        # Create spike test configuration
        spike_config = {
            "config": {
                "target": os.getenv("BASE_URL", "http://localhost:8002"),
                "phases": [
                    {
                        "duration": 30,
                        "arrivalRate": 200,  # 200 per second = 2000 QPS with 10 workers
                        "name": "Spike"
                    }
                ],
                "processor": str(Path(__file__).parent / "load-tests" / "performance-processor.js")
            },
            "scenarios": [{
                "name": "Spike Query",
                "weight": 100,
                "flow": [{
                    "post": {
                        "url": "/api/v1/query",
                        "headers": {
                            "Authorization": "Bearer test-token",
                            "Content-Type": "application/json"
                        },
                        "json": {
                            "query": "Spike test: Find all API endpoints",
                            "repository_id": "test-repo"
                        }
                    }
                }]
            }]
        }
        
        spike_config_path = self.results_dir / "spike-config.yml"
        with open(spike_config_path, 'w') as f:
            yaml.dump(spike_config, f)
            
        spike_output = self.results_dir / "spike-results.json"
        
        cmd = [
            "artillery", "run",
            str(spike_config_path),
            "--output", str(spike_output)
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode != 0:
            self.log(f"Spike test failed: {result.stderr}", "WARNING")
            return {"error": result.stderr, "note": "Spike test failure is not critical"}
            
        # Parse results
        try:
            with open(spike_output, 'r') as f:
                results = json.load(f)
                
            aggregate = results.get("aggregate", {})
            
            return {
                "test_type": "spike_2000_qps",
                "duration_seconds": 30,
                "target_qps": 2000,
                "total_requests": aggregate.get("counters", {}).get("http.requests", 0),
                "success_rate": 100 - (aggregate.get("counters", {}).get("http.codes.4xx", 0) + 
                                     aggregate.get("counters", {}).get("http.codes.5xx", 0)) / 
                                     max(aggregate.get("counters", {}).get("http.requests", 1), 1) * 100,
                "response_times": {
                    "p95": aggregate.get("summaries", {}).get("http.response_time", {}).get("p95", 0),
                    "p99": aggregate.get("summaries", {}).get("http.response_time", {}).get("p99", 0)
                }
            }
            
        except Exception as e:
            self.log(f"Failed to parse spike results: {e}", "WARNING")
            return {"error": str(e), "note": "Spike test parsing failed"}
            
    async def _analyze_results(self) -> Dict[str, Any]:
        """Analyze all test results"""
        analysis = {
            "performance_achieved": {},
            "target_comparison": {},
            "recommendations": []
        }
        
        # Analyze Artillery results (primary test)
        artillery = self.validation_results["results"].get("artillery", {})
        
        if "actual_qps" in artillery:
            analysis["performance_achieved"]["sustained_qps"] = artillery["actual_qps"]
            analysis["performance_achieved"]["success_rate"] = artillery.get("success_rate", 0)
            analysis["performance_achieved"]["p95_response_time"] = artillery.get("response_times", {}).get("p95", 0)
            analysis["performance_achieved"]["p99_response_time"] = artillery.get("response_times", {}).get("p99", 0)
            
            # Compare against targets
            analysis["target_comparison"]["qps_achieved"] = artillery["actual_qps"] >= self.target_qps
            analysis["target_comparison"]["p95_achieved"] = artillery.get("response_times", {}).get("p95", 999) <= self.target_p95_ms
            analysis["target_comparison"]["p99_achieved"] = artillery.get("response_times", {}).get("p99", 999) <= self.target_p99_ms
            analysis["target_comparison"]["success_rate_achieved"] = artillery.get("success_rate", 0) >= self.min_success_rate
            
            # Cache performance
            cache_hit_rate = artillery.get("custom_metrics", {}).get("cache_hit_rate", 0)
            analysis["performance_achieved"]["cache_hit_rate"] = cache_hit_rate
            
        # Analyze baseline for comparison
        baseline = self.validation_results["results"].get("baseline", {})
        if "response_times" in baseline:
            analysis["baseline_comparison"] = {
                "response_time_increase": (
                    (artillery.get("response_times", {}).get("p95", 0) - 
                     baseline.get("response_times", {}).get("p95", 0)) / 
                    baseline.get("response_times", {}).get("p95", 1) * 100
                )
            }
            
        # Generate recommendations
        if analysis["target_comparison"].get("qps_achieved", False):
            analysis["recommendations"].append({
                "type": "success",
                "message": f"✓ Achieved {artillery['actual_qps']:.0f} QPS, exceeding target of {self.target_qps} QPS"
            })
        else:
            analysis["recommendations"].append({
                "type": "improvement",
                "message": f"QPS target not met: {artillery.get('actual_qps', 0):.0f} vs {self.target_qps}",
                "actions": [
                    "Increase connection pool size",
                    "Optimize database queries",
                    "Implement request batching",
                    "Scale horizontally"
                ]
            })
            
        if not analysis["target_comparison"].get("p95_achieved", False):
            analysis["recommendations"].append({
                "type": "improvement",
                "message": f"P95 response time exceeded: {artillery.get('response_times', {}).get('p95', 0):.0f}ms vs {self.target_p95_ms}ms",
                "actions": [
                    "Implement caching strategies",
                    "Optimize query processing",
                    "Add query result streaming",
                    "Profile and optimize hot paths"
                ]
            })
            
        if cache_hit_rate < 70:
            analysis["recommendations"].append({
                "type": "optimization",
                "message": f"Cache hit rate is low: {cache_hit_rate:.1f}%",
                "actions": [
                    "Implement cache warming",
                    "Increase cache TTL",
                    "Add semantic caching",
                    "Optimize cache key generation"
                ]
            })
            
        return analysis
        
    async def _generate_report(self) -> Dict[str, Any]:
        """Generate comprehensive performance report"""
        report = {
            "executive_summary": {
                "service": "Query Intelligence",
                "test_date": datetime.now().isoformat(),
                "overall_result": "PASS" if self.validation_results.get("passed", False) else "FAIL",
                "key_findings": []
            },
            "detailed_results": {},
            "recommendations": [],
            "next_steps": []
        }
        
        analysis = self.validation_results.get("analysis", {})
        achieved = analysis.get("performance_achieved", {})
        
        # Key findings
        if achieved.get("sustained_qps", 0) >= self.target_qps:
            report["executive_summary"]["key_findings"].append(
                f"✓ Successfully achieved {achieved['sustained_qps']:.0f} QPS sustained load"
            )
        else:
            report["executive_summary"]["key_findings"].append(
                f"✗ Failed to achieve target QPS: {achieved.get('sustained_qps', 0):.0f} vs {self.target_qps}"
            )
            
        if achieved.get("p95_response_time", 999) <= self.target_p95_ms:
            report["executive_summary"]["key_findings"].append(
                f"✓ P95 response time within target: {achieved['p95_response_time']:.0f}ms"
            )
        else:
            report["executive_summary"]["key_findings"].append(
                f"✗ P95 response time exceeded: {achieved.get('p95_response_time', 0):.0f}ms vs {self.target_p95_ms}ms"
            )
            
        # Detailed results
        report["detailed_results"] = {
            "performance_metrics": achieved,
            "target_comparison": analysis.get("target_comparison", {}),
            "test_configurations": {
                "artillery": self.validation_results["results"].get("artillery", {}),
                "k6": self.validation_results["results"].get("k6", {}),
                "baseline": self.validation_results["results"].get("baseline", {}),
                "spike": self.validation_results["results"].get("spike", {})
            }
        }
        
        # Recommendations
        report["recommendations"] = analysis.get("recommendations", [])
        
        # Next steps
        if self.validation_results.get("passed", False):
            report["next_steps"] = [
                "Deploy to production with confidence",
                "Set up continuous performance monitoring",
                "Establish performance regression detection",
                "Plan for future scaling needs"
            ]
        else:
            report["next_steps"] = [
                "Address identified performance bottlenecks",
                "Implement recommended optimizations",
                "Re-run performance validation after fixes",
                "Consider architectural improvements"
            ]
            
        return report
        
    def _check_validation_passed(self, analysis: Dict[str, Any]) -> bool:
        """Check if all validation criteria passed"""
        comparison = analysis.get("target_comparison", {})
        
        return (
            comparison.get("qps_achieved", False) and
            comparison.get("p95_achieved", False) and
            comparison.get("p99_achieved", False) and
            comparison.get("success_rate_achieved", False)
        )
        
    async def _save_results(self):
        """Save validation results to file"""
        results_file = self.results_dir / f"validation_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(results_file, 'w') as f:
            json.dump(self.validation_results, f, indent=2)
            
        self.log(f"Results saved to: {results_file}")
        
        # Also save a summary file
        summary_file = self.results_dir / "validation_summary.json"
        summary = {
            "timestamp": self.validation_results["timestamp"],
            "passed": self.validation_results["passed"],
            "achieved_qps": self.validation_results.get("analysis", {}).get("performance_achieved", {}).get("sustained_qps", 0),
            "p95_response_time": self.validation_results.get("analysis", {}).get("performance_achieved", {}).get("p95_response_time", 0),
            "success_rate": self.validation_results.get("analysis", {}).get("performance_achieved", {}).get("success_rate", 0)
        }
        
        with open(summary_file, 'w') as f:
            json.dump(summary, f, indent=2)
            
    def _print_summary(self, analysis: Dict[str, Any]):
        """Print validation summary"""
        print("\n" + "="*80)
        print("PERFORMANCE VALIDATION SUMMARY")
        print("="*80)
        
        achieved = analysis.get("performance_achieved", {})
        comparison = analysis.get("target_comparison", {})
        
        print(f"\nTarget: {self.target_qps}+ QPS with P95 < {self.target_p95_ms}ms")
        print(f"\nResults:")
        print(f"  Sustained QPS: {achieved.get('sustained_qps', 0):.0f} {'✓' if comparison.get('qps_achieved', False) else '✗'}")
        print(f"  P95 Response Time: {achieved.get('p95_response_time', 0):.0f}ms {'✓' if comparison.get('p95_achieved', False) else '✗'}")
        print(f"  P99 Response Time: {achieved.get('p99_response_time', 0):.0f}ms {'✓' if comparison.get('p99_achieved', False) else '✗'}")
        print(f"  Success Rate: {achieved.get('success_rate', 0):.1f}% {'✓' if comparison.get('success_rate_achieved', False) else '✗'}")
        print(f"  Cache Hit Rate: {achieved.get('cache_hit_rate', 0):.1f}%")
        
        print(f"\nOverall Result: {'PASS ✓' if self.validation_results['passed'] else 'FAIL ✗'}")
        
        if analysis.get("recommendations"):
            print("\nKey Recommendations:")
            for i, rec in enumerate(analysis["recommendations"][:3], 1):
                print(f"  {i}. {rec['message']}")
                
        print("="*80)


async def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Run performance validation for Query Intelligence service")
    parser.add_argument("--target-qps", type=int, default=1000, help="Target QPS (default: 1000)")
    parser.add_argument("--target-p95", type=int, default=100, help="Target P95 response time in ms (default: 100)")
    parser.add_argument("--target-p99", type=int, default=500, help="Target P99 response time in ms (default: 500)")
    parser.add_argument("--min-success-rate", type=float, default=99.0, help="Minimum success rate % (default: 99.0)")
    parser.add_argument("--results-dir", default="performance_validation_results", help="Results directory")
    
    args = parser.parse_args()
    
    validator = PerformanceValidator(
        results_dir=args.results_dir,
        target_qps=args.target_qps,
        target_p95_ms=args.target_p95,
        target_p99_ms=args.target_p99,
        min_success_rate=args.min_success_rate
    )
    
    try:
        results = await validator.run_validation()
        
        # Exit with appropriate code
        sys.exit(0 if results["passed"] else 1)
        
    except Exception as e:
        print(f"\nValidation failed with error: {e}", file=sys.stderr)
        sys.exit(2)


if __name__ == "__main__":
    asyncio.run(main())