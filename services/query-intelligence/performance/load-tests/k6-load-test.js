import http from 'k6/http';
import ws from 'k6/ws';
import { check, sleep } from 'k6';
import { Rate, Trend, Counter, Gauge } from 'k6/metrics';
import { randomItem, randomIntBetween } from 'https://jslib.k6.io/k6-utils/1.2.0/index.js';

// Custom metrics
const queryResponseTime = new Trend('query_response_time');
const queryConfidence = new Trend('query_confidence_score');
const cacheHitRate = new Rate('cache_hit_rate');
const errorRate = new Rate('error_rate');
const websocketConnections = new Gauge('websocket_connections');
const queriesPerSecond = new Counter('queries_per_second');

// Test configuration
export const options = {
  scenarios: {
    // Scenario 1: Ramp up to 1000+ QPS
    sustained_load: {
      executor: 'ramping-arrival-rate',
      startRate: 10,
      timeUnit: '1s',
      preAllocatedVUs: 500,
      maxVUs: 2000,
      stages: [
        { duration: '1m', target: 10 },    // Warm up
        { duration: '5m', target: 100 },   // Ramp up to 100 QPS
        { duration: '5m', target: 500 },   // Ramp up to 500 QPS
        { duration: '30m', target: 1000 }, // Sustain 1000 QPS
        { duration: '5m', target: 1200 },  // Peak load test
        { duration: '5m', target: 100 },   // Ramp down
      ],
    },
    
    // Scenario 2: Spike test
    spike_test: {
      executor: 'constant-arrival-rate',
      rate: 2000,
      timeUnit: '1s',
      duration: '2m',
      preAllocatedVUs: 1000,
      maxVUs: 3000,
      startTime: '45m', // Start after sustained load
    },
    
    // Scenario 3: WebSocket connections
    websocket_test: {
      executor: 'constant-vus',
      vus: 100,
      duration: '52m',
    },
  },
  
  thresholds: {
    'http_req_duration': ['p(95)<100', 'p(99)<500'], // Response time targets
    'http_req_failed': ['rate<0.01'],                 // Error rate < 1%
    'query_response_time': ['p(95)<85', 'p(99)<200'], // Query-specific targets
    'query_confidence_score': ['avg>0.8'],            // Average confidence > 80%
    'cache_hit_rate': ['rate>0.7'],                  // Cache hit rate > 70%
    'error_rate': ['rate<0.01'],                     // Custom error rate < 1%
  },
};

// Test data
const BASE_URL = __ENV.BASE_URL || 'https://query-intelligence-l3nxty7oka-uc.a.run.app';
const AUTH_TOKEN = __ENV.AUTH_TOKEN || 'test-token';

const repositories = [
  'test-repo-1',
  'test-repo-2',
  'enterprise-repo',
  'large-monorepo',
  'microservices-collection',
];

const queries = [
  'How does the authentication middleware work?',
  'Find all API endpoints in this service',
  'Explain the caching strategy used here',
  'What design patterns are implemented?',
  'Show me potential security vulnerabilities',
  'Analyze performance bottlenecks in this code',
  'Find duplicate or similar code blocks',
  'Explain the database schema and relationships',
  'What are the main dependencies of this project?',
  'How is error handling implemented?',
];

const languages = ['python', 'javascript', 'go', 'java', 'typescript'];

// Helper function to generate query payload
function generateQueryPayload() {
  return {
    query: randomItem(queries),
    repository_id: randomItem(repositories),
    filters: {
      language: randomItem(languages),
      max_results: randomIntBetween(5, 20),
      min_confidence: Math.random() * 0.3 + 0.5, // 0.5 to 0.8
    },
    options: {
      include_references: true,
      include_patterns: Math.random() > 0.5,
    },
  };
}

// Main test function for HTTP requests
export function sustained_load() {
  const payload = generateQueryPayload();
  
  const params = {
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${AUTH_TOKEN}`,
      'X-Request-ID': `k6-${__VU}-${__ITER}`,
    },
    timeout: '30s',
    tags: { name: 'QueryRequest' },
  };
  
  const start = new Date();
  const response = http.post(
    `${BASE_URL}/api/v1/query`,
    JSON.stringify(payload),
    params
  );
  const duration = new Date() - start;
  
  // Record custom metrics
  queryResponseTime.add(duration);
  queriesPerSecond.add(1);
  
  // Check response
  const success = check(response, {
    'status is 200': (r) => r.status === 200,
    'response has answer': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.answer !== undefined;
      } catch (e) {
        return false;
      }
    },
    'response time < 100ms': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.execution_time_ms < 100;
      } catch (e) {
        return false;
      }
    },
    'confidence score > 0.7': (r) => {
      try {
        const body = JSON.parse(r.body);
        queryConfidence.add(body.confidence || 0);
        return body.confidence > 0.7;
      } catch (e) {
        return false;
      }
    },
  });
  
  // Track cache hits
  try {
    const body = JSON.parse(response.body);
    if (body.metadata && body.metadata.cache_hit !== undefined) {
      cacheHitRate.add(body.metadata.cache_hit);
    }
  } catch (e) {
    // Ignore parsing errors
  }
  
  // Track errors
  errorRate.add(!success);
  
  // Think time between requests
  sleep(randomIntBetween(1, 3) / 10); // 0.1 to 0.3 seconds
}

// Spike test function
export function spike_test() {
  // Same as sustained_load but with no think time
  const payload = generateQueryPayload();
  
  const params = {
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${AUTH_TOKEN}`,
      'X-Request-ID': `k6-spike-${__VU}-${__ITER}`,
    },
    timeout: '10s', // Shorter timeout for spike test
  };
  
  const response = http.post(
    `${BASE_URL}/api/v1/query`,
    JSON.stringify(payload),
    params
  );
  
  check(response, {
    'spike test - status is 200': (r) => r.status === 200,
    'spike test - response time < 500ms': (r) => r.timings.duration < 500,
  });
}

// WebSocket test function
export function websocket_test() {
  const url = `${BASE_URL.replace('https://', 'wss://').replace('http://', 'ws://')}/api/v1/ws/query`;
  
  const params = {
    headers: {
      'Authorization': `Bearer ${AUTH_TOKEN}`,
    },
    tags: { name: 'WebSocketQuery' },
  };
  
  const response = ws.connect(url, params, function (socket) {
    socket.on('open', () => {
      websocketConnections.add(1);
      
      // Send query
      const query = generateQueryPayload();
      socket.send(JSON.stringify({
        type: 'query',
        data: query,
      }));
    });
    
    socket.on('message', (data) => {
      try {
        const message = JSON.parse(data);
        check(message, {
          'websocket - valid message type': (m) => ['chunk', 'error', 'done'].includes(m.type),
          'websocket - has content': (m) => m.content !== undefined || m.done === true,
        });
      } catch (e) {
        console.error('WebSocket message parse error:', e);
      }
    });
    
    socket.on('close', () => {
      websocketConnections.add(-1);
    });
    
    socket.on('error', (e) => {
      console.error('WebSocket error:', e);
      errorRate.add(1);
    });
    
    // Keep connection open for 30-60 seconds
    socket.setTimeout(() => {
      socket.close();
    }, randomIntBetween(30000, 60000));
  });
  
  check(response, {
    'websocket - connected successfully': (r) => r && r.status === 101,
  });
  
  sleep(5); // Wait before next connection
}

// Setup function
export function setup() {
  // Verify service is healthy before starting
  const healthCheck = http.get(`${BASE_URL}/health`);
  check(healthCheck, {
    'service is healthy': (r) => r.status === 200,
  });
  
  if (healthCheck.status !== 200) {
    throw new Error('Service is not healthy, aborting test');
  }
  
  return {
    startTime: new Date().toISOString(),
    testConfig: options,
  };
}

// Teardown function
export function teardown(data) {
  console.log('Test completed at:', new Date().toISOString());
  console.log('Test duration:', new Date() - new Date(data.startTime), 'ms');
  
  // Summary report would be generated here
}