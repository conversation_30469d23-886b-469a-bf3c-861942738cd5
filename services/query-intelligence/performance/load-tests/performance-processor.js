/**
 * Performance Processor for Artillery Load Tests
 * 
 * Provides custom processing functions for Artillery scenarios including:
 * - Dynamic data generation
 * - Response validation
 * - Custom metrics collection
 * - Performance tracking
 */

'use strict';

const crypto = require('crypto');

// Performance metrics storage
const metrics = {
  responseTime: [],
  confidenceScores: [],
  cacheHits: 0,
  cacheMisses: 0,
  errors: [],
  successfulQueries: 0,
  totalQueries: 0
};

// Test data pools
const queryTemplates = [
  // Architecture queries
  { template: "How does the {component} module handle {action}?", weight: 15 },
  { template: "Explain the architecture of {service}", weight: 10 },
  { template: "What design patterns are used in {component}?", weight: 10 },
  
  // Search queries
  { template: "Find all {type} in {scope}", weight: 15 },
  { template: "Search for {keyword} implementations", weight: 10 },
  { template: "Show me examples of {pattern}", weight: 10 },
  
  // Debugging queries
  { template: "Why is {feature} throwing {error}?", weight: 10 },
  { template: "Debug the {issue} in {component}", weight: 5 },
  { template: "Analyze performance of {function}", weight: 5 },
  
  // Security queries
  { template: "Find security vulnerabilities in {scope}", weight: 5 },
  { template: "Check authentication in {service}", weight: 5 }
];

const components = [
  'authentication', 'authorization', 'caching', 'database', 'api', 
  'frontend', 'backend', 'middleware', 'router', 'controller'
];

const services = [
  'user-service', 'auth-service', 'api-gateway', 'notification-service',
  'payment-service', 'inventory-service', 'order-service'
];

const actions = [
  'requests', 'errors', 'timeouts', 'connections', 'authentication',
  'validation', 'caching', 'logging', 'monitoring', 'scaling'
];

const types = [
  'endpoints', 'functions', 'classes', 'interfaces', 'constants',
  'configurations', 'tests', 'errors', 'warnings', 'TODOs'
];

const scopes = [
  'this file', 'this module', 'this service', 'the entire codebase',
  'the API layer', 'the database layer', 'the frontend'
];

const keywords = [
  'async', 'promise', 'callback', 'stream', 'buffer', 'cache',
  'queue', 'worker', 'webhook', 'middleware', 'interceptor'
];

const patterns = [
  'singleton', 'factory', 'observer', 'decorator', 'strategy',
  'adapter', 'facade', 'proxy', 'command', 'iterator'
];

const features = [
  'login', 'logout', 'upload', 'download', 'search', 'filter',
  'sort', 'paginate', 'validate', 'transform'
];

const errors = [
  'TypeError', 'ReferenceError', 'SyntaxError', '404', '500',
  'timeout', 'ECONNREFUSED', 'ENOTFOUND', 'ValidationError'
];

const issues = [
  'memory leak', 'slow query', 'high CPU usage', 'deadlock',
  'race condition', 'infinite loop', 'null pointer', 'buffer overflow'
];

// Helper functions
function randomChoice(array) {
  return array[Math.floor(Math.random() * array.length)];
}

function weightedRandomChoice(items) {
  const totalWeight = items.reduce((sum, item) => sum + item.weight, 0);
  let random = Math.random() * totalWeight;
  
  for (const item of items) {
    random -= item.weight;
    if (random <= 0) {
      return item;
    }
  }
  
  return items[items.length - 1];
}

function generateQuery() {
  const queryType = weightedRandomChoice(queryTemplates);
  let query = queryType.template;
  
  // Replace placeholders with random values
  query = query.replace('{component}', randomChoice(components));
  query = query.replace('{service}', randomChoice(services));
  query = query.replace('{action}', randomChoice(actions));
  query = query.replace('{type}', randomChoice(types));
  query = query.replace('{scope}', randomChoice(scopes));
  query = query.replace('{keyword}', randomChoice(keywords));
  query = query.replace('{pattern}', randomChoice(patterns));
  query = query.replace('{feature}', randomChoice(features));
  query = query.replace('{error}', randomChoice(errors));
  query = query.replace('{issue}', randomChoice(issues));
  query = query.replace('{function}', `${randomChoice(components)}_${randomChoice(actions)}`);
  
  return query;
}

function generateSessionId() {
  return crypto.randomBytes(16).toString('hex');
}

function generateRequestId() {
  return `artillery-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
}

// Processor functions for Artillery
module.exports = {
  // Before scenario hook
  setupScenario: function(context, ee, next) {
    // Initialize context variables
    context.vars.sessionId = generateSessionId();
    context.vars.queries = [];
    context.vars.repositories = [
      'monorepo-main',
      'microservices-collection',
      'frontend-apps',
      'backend-services',
      'shared-libraries'
    ];
    
    return next();
  },
  
  // Generate dynamic query
  generateDynamicQuery: function(context, ee, next) {
    const query = generateQuery();
    context.vars.currentQuery = query;
    context.vars.queries.push(query);
    
    // Generate other dynamic data
    context.vars.repository = randomChoice(context.vars.repositories);
    context.vars.requestId = generateRequestId();
    context.vars.includePatterns = Math.random() > 0.5;
    context.vars.maxResults = Math.floor(Math.random() * 15) + 5;
    context.vars.minConfidence = Math.random() * 0.3 + 0.5;
    
    metrics.totalQueries++;
    
    return next();
  },
  
  // Process query response
  processQueryResponse: function(context, ee, next) {
    const response = context.vars.response;
    
    if (response && response.body) {
      try {
        const data = JSON.parse(response.body);
        
        // Collect metrics
        if (data.execution_time_ms) {
          metrics.responseTime.push(data.execution_time_ms);
          ee.emit('customStat', 'query.response_time', data.execution_time_ms);
        }
        
        if (data.confidence) {
          metrics.confidenceScores.push(data.confidence);
          ee.emit('customStat', 'query.confidence', data.confidence);
        }
        
        if (data.metadata && data.metadata.cache_hit !== undefined) {
          if (data.metadata.cache_hit) {
            metrics.cacheHits++;
            ee.emit('counter', 'cache.hit', 1);
          } else {
            metrics.cacheMisses++;
            ee.emit('counter', 'cache.miss', 1);
          }
        }
        
        metrics.successfulQueries++;
        ee.emit('counter', 'query.success', 1);
        
        // Validate response structure
        if (!data.answer || !data.references) {
          ee.emit('counter', 'response.invalid_structure', 1);
        }
        
        // Check performance thresholds
        if (data.execution_time_ms > 100) {
          ee.emit('counter', 'performance.slow_query', 1);
        }
        
        if (data.confidence < 0.7) {
          ee.emit('counter', 'quality.low_confidence', 1);
        }
        
      } catch (e) {
        metrics.errors.push({
          type: 'parse_error',
          message: e.message,
          query: context.vars.currentQuery
        });
        ee.emit('counter', 'error.parse', 1);
      }
    }
    
    return next();
  },
  
  // Generate complex context history
  generateContextHistory: function(context, ee, next) {
    const history = [];
    const historyLength = Math.floor(Math.random() * 3) + 1;
    
    for (let i = 0; i < historyLength; i++) {
      history.push({
        role: 'user',
        content: generateQuery()
      });
      
      history.push({
        role: 'assistant',
        content: `Based on the analysis, ${randomChoice([
          'the implementation uses a factory pattern',
          'there are 5 relevant functions',
          'the service handles this through middleware',
          'authentication is implemented using JWT tokens',
          'the caching strategy uses Redis with TTL'
        ])}.`
      });
    }
    
    context.vars.contextHistory = history;
    return next();
  },
  
  // Simulate WebSocket message
  generateWebSocketMessage: function(context, ee, next) {
    const message = {
      type: 'query',
      data: {
        query: generateQuery(),
        repository_id: randomChoice(context.vars.repositories),
        stream: true,
        session_id: context.vars.sessionId,
        filters: {
          language: randomChoice(['python', 'javascript', 'go', 'java']),
          exclude_tests: Math.random() > 0.7,
          include_comments: Math.random() > 0.5
        }
      }
    };
    
    context.vars.wsMessage = JSON.stringify(message);
    return next();
  },
  
  // After scenario hook
  teardownScenario: function(context, ee, next) {
    // Calculate and emit final metrics
    if (metrics.responseTime.length > 0) {
      const avgResponseTime = metrics.responseTime.reduce((a, b) => a + b, 0) / metrics.responseTime.length;
      const p95ResponseTime = metrics.responseTime.sort((a, b) => a - b)[Math.floor(metrics.responseTime.length * 0.95)];
      
      ee.emit('customStat', 'query.avg_response_time', avgResponseTime);
      ee.emit('customStat', 'query.p95_response_time', p95ResponseTime);
    }
    
    if (metrics.confidenceScores.length > 0) {
      const avgConfidence = metrics.confidenceScores.reduce((a, b) => a + b, 0) / metrics.confidenceScores.length;
      ee.emit('customStat', 'query.avg_confidence', avgConfidence);
    }
    
    const cacheHitRate = metrics.totalQueries > 0 
      ? (metrics.cacheHits / (metrics.cacheHits + metrics.cacheMisses)) * 100 
      : 0;
    ee.emit('customStat', 'cache.hit_rate', cacheHitRate);
    
    const successRate = metrics.totalQueries > 0 
      ? (metrics.successfulQueries / metrics.totalQueries) * 100 
      : 0;
    ee.emit('customStat', 'query.success_rate', successRate);
    
    return next();
  },
  
  // Custom validation functions
  validateResponseTime: function(response, body, context, ee, next) {
    try {
      const data = JSON.parse(body);
      const isValid = data.execution_time_ms < 100;
      
      if (!isValid) {
        ee.emit('counter', 'validation.response_time_exceeded', 1);
      }
      
      return next(isValid);
    } catch (e) {
      return next(false);
    }
  },
  
  validateConfidenceScore: function(response, body, context, ee, next) {
    try {
      const data = JSON.parse(body);
      const isValid = data.confidence >= 0.7;
      
      if (!isValid) {
        ee.emit('counter', 'validation.low_confidence', 1);
      }
      
      return next(isValid);
    } catch (e) {
      return next(false);
    }
  },
  
  // Health check validation
  validateHealthCheck: function(response, body, context, ee, next) {
    try {
      const data = JSON.parse(body);
      const isHealthy = data.status === 'healthy' && 
                       data.services && 
                       Object.values(data.services).every(s => s === 'healthy');
      
      if (!isHealthy) {
        ee.emit('counter', 'health.unhealthy', 1);
      }
      
      return next(isHealthy);
    } catch (e) {
      return next(false);
    }
  }
};

// Export metrics for external analysis
module.exports.getMetrics = function() {
  return {
    summary: {
      totalQueries: metrics.totalQueries,
      successfulQueries: metrics.successfulQueries,
      failureRate: ((metrics.totalQueries - metrics.successfulQueries) / metrics.totalQueries * 100).toFixed(2) + '%',
      cacheHitRate: ((metrics.cacheHits / (metrics.cacheHits + metrics.cacheMisses)) * 100).toFixed(2) + '%',
      avgResponseTime: metrics.responseTime.length > 0 
        ? (metrics.responseTime.reduce((a, b) => a + b, 0) / metrics.responseTime.length).toFixed(2) + 'ms'
        : 'N/A',
      avgConfidence: metrics.confidenceScores.length > 0
        ? (metrics.confidenceScores.reduce((a, b) => a + b, 0) / metrics.confidenceScores.length).toFixed(3)
        : 'N/A'
    },
    detailed: metrics
  };
};