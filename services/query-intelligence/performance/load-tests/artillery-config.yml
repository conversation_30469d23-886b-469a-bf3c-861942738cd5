config:
  target: "https://query-intelligence-l3nxty7oka-uc.a.run.app"
  phases:
    # Warmup phase
    - duration: 60
      arrivalRate: 10
      name: "Warmup"
    
    # Ramp up to target load
    - duration: 300
      arrivalRate: 10
      rampTo: 100
      name: "Ramp up"
    
    # Sustained load test (1000+ QPS)
    - duration: 1800  # 30 minutes
      arrivalRate: 100  # 100 requests per second per instance
      name: "Sustained high load"
    
    # Spike test
    - duration: 60
      arrivalRate: 200
      name: "Spike test"
    
    # Cool down
    - duration: 120
      arrivalRate: 50
      rampTo: 10
      name: "Cool down"

  processor: "./performance-processor.js"
  
  # Environment-specific settings
  environments:
    development:
      target: "http://localhost:8002"
      phases:
        - duration: 60
          arrivalRate: 5
    
    staging:
      target: "https://query-intelligence-staging-l3nxty7oka-uc.a.run.app"
    
    production:
      target: "https://query-intelligence-l3nxty7oka-uc.a.run.app"

  # HTTP settings
  http:
    timeout: 30
    pool: 50  # Connection pool size

  # Metrics and reporting
  plugins:
    metrics-by-endpoint:
      useOnlyRequestNames: true
    
    expect:
      outputFormat: pretty
    
    publish-metrics:
      - type: cloudwatch
        region: us-central1

  # Variables for test data
  variables:
    repositories:
      - "test-repo-1"
      - "test-repo-2"
      - "test-repo-3"
      - "enterprise-repo"
      - "large-monorepo"
    
    queries:
      - "How does authentication work?"
      - "Find all API endpoints"
      - "Explain the caching strategy"
      - "What design patterns are used?"
      - "Show me security vulnerabilities"
      - "Analyze performance bottlenecks"
      - "Find duplicate code"
      - "Explain the database schema"

scenarios:
  # Scenario 1: Simple query (60% of traffic)
  - name: "Simple Query"
    weight: 60
    flow:
      - post:
          url: "/api/v1/query"
          headers:
            Authorization: "Bearer {{ $randomString() }}"
            Content-Type: "application/json"
          json:
            query: "{{ queries[Math.floor(Math.random() * queries.length)] }}"
            repository_id: "{{ repositories[Math.floor(Math.random() * repositories.length)] }}"
            filters:
              language: "python"
              max_results: 10
          
          capture:
            - json: "$.execution_time_ms"
              as: "response_time"
            - json: "$.confidence"
              as: "confidence_score"
          
          expect:
            - statusCode: 200
            - contentType: json
            - hasProperty: answer
            - hasProperty: references
            - js: "response.execution_time_ms < 100"
  
  # Scenario 2: Complex query with history (25% of traffic)
  - name: "Complex Query with Context"
    weight: 25
    flow:
      - post:
          url: "/api/v1/query"
          headers:
            Authorization: "Bearer {{ $randomString() }}"
            Content-Type: "application/json"
          json:
            query: "Based on our previous discussion, {{ queries[Math.floor(Math.random() * queries.length)] }}"
            repository_id: "{{ repositories[Math.floor(Math.random() * repositories.length)] }}"
            session_id: "{{ $uuid() }}"
            context_history:
              - role: "user"
                content: "What is the main architecture?"
              - role: "assistant"
                content: "The system uses a microservices architecture..."
            filters:
              language: ["python", "javascript"]
              exclude_paths: ["node_modules", "vendor"]
              min_confidence: 0.7
          
          expect:
            - statusCode: 200
            - js: "response.confidence > 0.7"
  
  # Scenario 3: WebSocket streaming query (10% of traffic)
  - name: "WebSocket Streaming"
    weight: 10
    engine: "ws"
    flow:
      - connect:
          url: "/api/v1/ws/query"
          headers:
            Authorization: "Bearer {{ $randomString() }}"
      
      - send:
          json:
            type: "query"
            data:
              query: "{{ queries[Math.floor(Math.random() * queries.length)] }}"
              repository_id: "{{ repositories[Math.floor(Math.random() * repositories.length)] }}"
              stream: true
      
      - think: 2
      
      - expect:
          - json:
              type: "chunk"
          - json:
              done: true
  
  # Scenario 4: Health check (5% of traffic)
  - name: "Health Check"
    weight: 5
    flow:
      - get:
          url: "/health"
          expect:
            - statusCode: 200
            - contentType: json
            - hasProperty: status
            - js: "response.status === 'healthy'"

# Performance thresholds
ensure:
  p95: 100  # 95th percentile response time < 100ms
  p99: 500  # 99th percentile response time < 500ms
  maxErrorRate: 1  # Max 1% error rate