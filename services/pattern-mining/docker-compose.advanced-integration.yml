# Phase 2 Enhancement: Advanced Integration Testing Infrastructure
# Comprehensive Docker Compose for complex multi-service integration testing
version: '3.8'

networks:
  # Primary application network
  integration-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
  
  # Isolated networks for complex testing scenarios
  service-mesh:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
        
  # Monitoring and observability network
  monitoring-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

services:
  # =============================================================================
  # Core Pattern Mining Services - Multi-Instance for Advanced Testing
  # =============================================================================
  
  pattern-mining-api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: pattern-mining-api
    ports:
      - "8001:8001"
    environment:
      - ENVIRONMENT=integration_test
      - SERVICE_NAME=api_service
      - REDIS_URL=redis://redis-cluster:6379
      - POSTGRES_URL=****************************************************/pattern_mining_integration
      - POSTGRES_READ_REPLICA_URL=****************************************************/pattern_mining_integration
      - BIGQUERY_PROJECT_ID=integration-test-project
      - GEMINI_API_KEY=${GEMINI_API_KEY_TEST:-test-api-key}
      - ML_SERVICE_URL=http://ml-service:8002
      - ANALYTICS_SERVICE_URL=http://analytics-service:8003
      - CACHE_SERVICE_URL=http://cache-service:8004
      - ENABLE_METRICS=true
      - ENABLE_TRACING=true
      - LOG_LEVEL=INFO
    networks:
      - integration-network
      - service-mesh
      - monitoring-network
    depends_on:
      - postgres-primary
      - postgres-replica
      - redis-cluster
      - ml-service
      - analytics-service
      - cache-service
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 30s

  # ML Service for complex AI processing scenarios
  ml-service:
    build:
      context: .
      dockerfile: Dockerfile
      target: ml-service
    container_name: ml-service
    ports:
      - "8002:8002"
    environment:
      - ENVIRONMENT=integration_test
      - SERVICE_NAME=ml_service
      - GEMINI_API_KEY=${GEMINI_API_KEY_TEST:-test-api-key}
      - REDIS_URL=redis://redis-cluster:6379
      - POSTGRES_URL=****************************************************/pattern_mining_integration
      - MODEL_CACHE_SIZE=1000
      - INFERENCE_TIMEOUT=30
      - BATCH_SIZE=10
      - ENABLE_METRICS=true
    networks:
      - integration-network
      - service-mesh
      - monitoring-network
    depends_on:
      - redis-cluster
      - postgres-primary
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8002/health"]
      interval: 15s
      timeout: 10s
      retries: 3

  # Analytics Service for performance metrics and reporting
  analytics-service:
    build:
      context: .
      dockerfile: Dockerfile
      target: analytics-service
    container_name: analytics-service
    ports:
      - "8003:8003"
    environment:
      - ENVIRONMENT=integration_test
      - SERVICE_NAME=analytics_service
      - POSTGRES_URL=****************************************************/pattern_mining_integration
      - REDIS_URL=redis://redis-cluster:6379
      - BIGQUERY_PROJECT_ID=integration-test-project
      - ANALYTICS_BATCH_SIZE=100
      - METRICS_RETENTION_DAYS=7
      - ENABLE_REAL_TIME_ANALYTICS=true
    networks:
      - integration-network
      - service-mesh
      - monitoring-network
    depends_on:
      - postgres-primary
      - redis-cluster
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8003/health"]
      interval: 15s
      timeout: 5s
      retries: 3

  # Cache Service for advanced caching scenarios
  cache-service:
    build:
      context: .
      dockerfile: Dockerfile
      target: cache-service
    container_name: cache-service
    ports:
      - "8004:8004"
    environment:
      - ENVIRONMENT=integration_test
      - SERVICE_NAME=cache_service
      - REDIS_URL=redis://redis-cluster:6379
      - CACHE_TTL_DEFAULT=3600
      - CACHE_MAX_SIZE=10000
      - ENABLE_CACHE_WARMING=true
      - ENABLE_CACHE_STATS=true
    networks:
      - integration-network
      - service-mesh
      - monitoring-network
    depends_on:
      - redis-cluster
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8004/health"]
      interval: 10s
      timeout: 3s
      retries: 3

  # =============================================================================
  # Data Layer - Advanced Database Configuration
  # =============================================================================
  
  postgres-primary:
    image: postgres:15-alpine
    container_name: postgres-primary
    ports:
      - "5433:5432"
    environment:
      - POSTGRES_DB=pattern_mining_integration
      - POSTGRES_USER=testuser
      - POSTGRES_PASSWORD=testpass
      - POSTGRES_INITDB_ARGS=--auth-host=scram-sha-256
      - POSTGRES_MAX_CONNECTIONS=200
      - POSTGRES_SHARED_BUFFERS=256MB
      - POSTGRES_EFFECTIVE_CACHE_SIZE=1GB
    volumes:
      - postgres_primary_data:/var/lib/postgresql/data
      - ./tests/fixtures/sql:/docker-entrypoint-initdb.d
    networks:
      - integration-network
      - service-mesh
    command: >
      postgres
      -c max_connections=200
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c maintenance_work_mem=64MB
      -c checkpoint_completion_target=0.9
      -c wal_buffers=16MB
      -c default_statistics_target=100
      -c random_page_cost=1.1
      -c effective_io_concurrency=200
      -c work_mem=4MB
      -c min_wal_size=1GB
      -c max_wal_size=4GB
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U testuser -d pattern_mining_integration"]
      interval: 10s
      timeout: 5s
      retries: 5

  postgres-replica:
    image: postgres:15-alpine
    container_name: postgres-replica
    ports:
      - "5434:5432"
    environment:
      - POSTGRES_DB=pattern_mining_integration
      - POSTGRES_USER=testuser
      - POSTGRES_PASSWORD=testpass
      - POSTGRES_INITDB_ARGS=--auth-host=scram-sha-256
    volumes:
      - postgres_replica_data:/var/lib/postgresql/data
      - ./tests/fixtures/sql:/docker-entrypoint-initdb.d
    networks:
      - integration-network
      - service-mesh
    depends_on:
      - postgres-primary
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U testuser -d pattern_mining_integration"]
      interval: 10s
      timeout: 5s
      retries: 5

  # =============================================================================
  # Caching Layer - Redis Cluster for Advanced Scenarios
  # =============================================================================
  
  redis-cluster:
    image: redis:7-alpine
    container_name: redis-cluster
    ports:
      - "6380:6379"
    environment:
      - REDIS_PASSWORD=testpass
    volumes:
      - redis_cluster_data:/data
      - ./config/redis-test.conf:/usr/local/etc/redis/redis.conf
    networks:
      - integration-network
      - service-mesh
    command: redis-server /usr/local/etc/redis/redis.conf --requirepass testpass
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "testpass", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3

  redis-secondary:
    image: redis:7-alpine
    container_name: redis-secondary
    ports:
      - "6381:6379"
    environment:
      - REDIS_PASSWORD=testpass
    volumes:
      - redis_secondary_data:/data
    networks:
      - integration-network
      - service-mesh
    command: redis-server --requirepass testpass --slaveof redis-cluster 6379
    depends_on:
      - redis-cluster
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "testpass", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3

  # =============================================================================
  # Testing and Integration Tools
  # =============================================================================
  
  # Integration Test Runner
  integration-test-runner:
    build:
      context: .
      dockerfile: Dockerfile.test
    container_name: integration-test-runner
    volumes:
      - .:/app
      - ./test-results:/app/test-results
      - ./coverage:/app/coverage
    environment:
      - ENVIRONMENT=integration_test
      - TEST_DATABASE_URL=****************************************************/pattern_mining_integration
      - TEST_REDIS_URL=redis://:testpass@redis-cluster:6379/0
      - GEMINI_API_KEY_TEST=${GEMINI_API_KEY_TEST:-test-api-key}
      - API_BASE_URL=http://pattern-mining-api:8001
      - ML_SERVICE_URL=http://ml-service:8002
      - ANALYTICS_SERVICE_URL=http://analytics-service:8003
      - CACHE_SERVICE_URL=http://cache-service:8004
      - PYTEST_TIMEOUT=600
      - INTEGRATION_TEST_CONCURRENCY=10
      - LOAD_TEST_ENABLED=true
    networks:
      - integration-network
      - service-mesh
      - monitoring-network
    depends_on:
      - pattern-mining-api
      - ml-service
      - analytics-service
      - cache-service
      - postgres-primary
      - postgres-replica
      - redis-cluster
    profiles:
      - integration-test

  # Performance Test Runner for Load Testing
  performance-test-runner:
    build:
      context: .
      dockerfile: Dockerfile.loadtest
    container_name: performance-test-runner
    volumes:
      - .:/app
      - ./performance-results:/app/performance-results
    environment:
      - ENVIRONMENT=performance_test
      - API_BASE_URL=http://pattern-mining-api:8001
      - CONCURRENT_USERS=50
      - TEST_DURATION=300
      - RAMP_UP_TIME=60
      - TARGET_RPS=100
      - PERFORMANCE_THRESHOLD_P95=500
      - MEMORY_LIMIT_MB=1000
    networks:
      - integration-network
      - service-mesh
      - monitoring-network
    depends_on:
      - pattern-mining-api
      - ml-service
      - analytics-service
      - cache-service
    profiles:
      - performance-test

  # =============================================================================
  # Monitoring and Observability Stack
  # =============================================================================
  
  # Prometheus for Metrics Collection
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus:/etc/prometheus
      - prometheus_data:/prometheus
    networks:
      - monitoring-network
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    profiles:
      - monitoring

  # Grafana for Metrics Visualization
  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - ./monitoring/grafana:/etc/grafana/provisioning
      - grafana_data:/var/lib/grafana
    networks:
      - monitoring-network
    depends_on:
      - prometheus
    profiles:
      - monitoring

  # Jaeger for Distributed Tracing
  jaeger:
    image: jaegertracing/all-in-one:latest
    container_name: jaeger
    ports:
      - "16686:16686"
      - "14268:14268"
    environment:
      - COLLECTOR_OTLP_ENABLED=true
    networks:
      - monitoring-network
    profiles:
      - monitoring

  # =============================================================================
  # Chaos Engineering and Failure Injection
  # =============================================================================
  
  # Chaos Toolkit for Failure Injection
  chaos-engineering:
    image: chaostoolkit/chaostoolkit:latest
    container_name: chaos-engineering
    volumes:
      - .:/app
      - ./chaos-experiments:/experiments
    environment:
      - CHAOS_EXPERIMENT_PATH=/experiments
      - TARGET_SERVICES=pattern-mining-api,ml-service,analytics-service
      - NETWORK_NAME=integration-network
    networks:
      - integration-network
      - service-mesh
    depends_on:
      - pattern-mining-api
      - ml-service
      - analytics-service
    profiles:
      - chaos-test

  # Network Simulation for Latency and Partition Testing
  network-simulator:
    image: nicolaka/netshoot:latest
    container_name: network-simulator
    cap_add:
      - NET_ADMIN
    volumes:
      - .:/app
    environment:
      - NETWORK_LATENCY_MS=50
      - PACKET_LOSS_PERCENT=1
      - BANDWIDTH_LIMIT_MBPS=100
    networks:
      - integration-network
      - service-mesh
    profiles:
      - network-test

  # =============================================================================
  # External Service Simulators
  # =============================================================================
  
  # Mock Gemini API for Consistent Testing
  mock-gemini-api:
    image: wiremock/wiremock:latest
    container_name: mock-gemini-api
    ports:
      - "8080:8080"
    volumes:
      - ./tests/fixtures/wiremock/gemini:/home/<USER>
    networks:
      - integration-network
      - service-mesh
    command: --global-response-templating --verbose
    profiles:
      - mock-services

  # Mock BigQuery for Analytics Testing
  mock-bigquery:
    image: wiremock/wiremock:latest
    container_name: mock-bigquery
    ports:
      - "8081:8080"
    volumes:
      - ./tests/fixtures/wiremock/bigquery:/home/<USER>
    networks:
      - integration-network
      - service-mesh
    command: --global-response-templating --verbose
    profiles:
      - mock-services

volumes:
  postgres_primary_data:
  postgres_replica_data:
  redis_cluster_data:
  redis_secondary_data:
  prometheus_data:
  grafana_data:

# =============================================================================
# Docker Compose Profiles for Different Testing Scenarios
# =============================================================================
# 
# Usage Examples:
# 
# Basic Integration Testing:
#   docker-compose -f docker-compose.advanced-integration.yml up -d
#   docker-compose -f docker-compose.advanced-integration.yml --profile integration-test up
# 
# Performance Testing:
#   docker-compose -f docker-compose.advanced-integration.yml --profile performance-test up
# 
# Monitoring and Observability:
#   docker-compose -f docker-compose.advanced-integration.yml --profile monitoring up -d
# 
# Chaos Engineering Testing:
#   docker-compose -f docker-compose.advanced-integration.yml --profile chaos-test up
# 
# Network Partition Testing:
#   docker-compose -f docker-compose.advanced-integration.yml --profile network-test up
# 
# Mock Services for Isolated Testing:
#   docker-compose -f docker-compose.advanced-integration.yml --profile mock-services up -d
# 
# Complete Testing Suite:
#   docker-compose -f docker-compose.advanced-integration.yml --profile integration-test --profile monitoring --profile mock-services up
#