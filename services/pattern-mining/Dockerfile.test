# Phase 2 Advanced Integration Testing Dockerfile
# Specialized container for running comprehensive integration tests
FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies for testing
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    netcat-openbsd \
    postgresql-client \
    redis-tools \
    jq \
    htop \
    tcpdump \
    iperf3 \
    && rm -rf /var/lib/apt/lists/*

# Install Python testing dependencies
COPY requirements.txt requirements-test.txt ./
RUN pip install --no-cache-dir -r requirements.txt -r requirements-test.txt

# Install additional testing tools
RUN pip install --no-cache-dir \
    pytest-xdist \
    pytest-html \
    pytest-cov \
    pytest-benchmark \
    pytest-asyncio \
    pytest-mock \
    pytest-timeout \
    pytest-sugar \
    pytest-clarity \
    locust \
    httpx[cli] \
    asyncpg \
    redis[hiredis] \
    psutil \
    memory_profiler \
    line_profiler

# Copy source code and tests
COPY src/ ./src/
COPY tests/ ./tests/
COPY config/ ./config/
COPY scripts/ ./scripts/
COPY setup.py ./

# Install the package in development mode
RUN pip install -e .

# Create test results directory
RUN mkdir -p /app/test-results

# Set environment for testing
ENV PYTHONPATH=/app/src
ENV ENVIRONMENT=integration-test
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

# Health check for test runner
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import sys; sys.exit(0)"

# Default command runs all advanced integration tests
CMD ["pytest", "tests/integration/", "-v", "--tb=short", "--html=/app/test-results/report.html", "--self-contained-html"]