# Pattern Mining Service Dependencies Configuration
# This file defines the critical dependencies for the pattern-mining service

service:
  name: pattern-mining
  language: python
  framework: fastapi
  port: 8003
  criticality: high

# Critical external dependencies
dependencies:
  databases:
    - name: bigquery
      type: database
      criticality: critical
      protocol: https
      description: "Primary data warehouse for pattern storage and analysis"
      health_check: "SELECT 1"
      timeout_ms: 5000
      retry_policy: "exponential_backoff"
      
  caches:
    - name: redis
      type: cache  
      criticality: critical
      protocol: redis
      port: 6379
      description: "Caching layer for pattern analysis results"
      health_check: "PING"
      timeout_ms: 1000
      retry_policy: "linear_backoff"
      
  ai_services:
    - name: vertex-ai
      type: external_service
      criticality: high
      protocol: https
      description: "Google AI Platform for ML model inference"
      health_check: "/health"
      timeout_ms: 10000
      retry_policy: "exponential_backoff"
      
    - name: gemini-api
      type: external_service
      criticality: high
      protocol: https
      description: "Gemini API for advanced pattern recognition"
      health_check: "/v1/models"
      timeout_ms: 15000
      retry_policy: "exponential_backoff"

  internal_services:
    - name: analysis-engine
      type: http_api
      criticality: high
      protocol: http
      port: 8001
      description: "Core analysis engine for AST processing"
      health_check: "/health"
      timeout_ms: 5000
      retry_policy: "circuit_breaker"
      
  monitoring:
    - name: prometheus
      type: monitoring
      criticality: medium
      protocol: http
      port: 9090
      description: "Metrics collection and monitoring"
      health_check: "/api/v1/status"
      timeout_ms: 2000
      retry_policy: "none"

# Service APIs exposed
exposed_apis:
  - "/health"
  - "/metrics"
  - "/patterns/detect"
  - "/patterns/analyze"
  - "/patterns/similarity"
  - "/patterns/export"

# Health check configuration
health_check:
  endpoint: "/health"
  interval_seconds: 30
  timeout_ms: 5000
  
# Metrics configuration  
metrics:
  endpoint: "/metrics"
  port: 8003