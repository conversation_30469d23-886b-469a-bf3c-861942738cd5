# Docker Compose for Phase 2 Load Testing Environment
# Comprehensive testing infrastructure with monitoring and resource management

version: '3.8'

services:
  # Main application service
  pattern-mining:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8001:8001"
    environment:
      - ENV=testing
      - LOG_LEVEL=INFO
      - DATABASE_URL=********************************************/pattern_mining_test
      - REDIS_URL=redis://redis:6379/0
      - GEMINI_API_KEY=${GEMINI_API_KEY:-test-key}
      - ENABLE_METRICS=true
      - ENABLE_PROFILING=true
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G
    networks:
      - loadtest-network

  # PostgreSQL database for testing
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=pattern_mining_test
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
      - POSTGRES_INITDB_ARGS=--auth-host=scram-sha-256
    ports:
      - "5432:5432"
    volumes:
      - postgres_test_data:/var/lib/postgresql/data
      - ./scripts/init-test-db.sql:/docker-entrypoint-initdb.d/init-test-db.sql:ro
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d pattern_mining_test"]
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 1G
    networks:
      - loadtest-network

  # Redis cache for testing
  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes --maxmemory 1gb --maxmemory-policy allkeys-lru
    ports:
      - "6379:6379"
    volumes:
      - redis_test_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 1.5G
        reservations:
          cpus: '0.25'
          memory: 512M
    networks:
      - loadtest-network

  # Prometheus for metrics collection during load testing
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus-loadtest.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=24h'
      - '--web.enable-lifecycle'
    depends_on:
      - pattern-mining
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 1G
    networks:
      - loadtest-network

  # Grafana for load testing dashboards
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=loadtest123
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-piechart-panel
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana-dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana-datasources:/etc/grafana/provisioning/datasources:ro
    depends_on:
      - prometheus
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 1G
        reservations:
          cpus: '0.25'
          memory: 512M
    networks:
      - loadtest-network

  # InfluxDB for time-series performance data
  influxdb:
    image: influxdb:2.7-alpine
    ports:
      - "8086:8086"
    environment:
      - DOCKER_INFLUXDB_INIT_MODE=setup
      - DOCKER_INFLUXDB_INIT_USERNAME=admin
      - DOCKER_INFLUXDB_INIT_PASSWORD=loadtest123
      - DOCKER_INFLUXDB_INIT_ORG=pattern-mining
      - DOCKER_INFLUXDB_INIT_BUCKET=load-testing
      - DOCKER_INFLUXDB_INIT_ADMIN_TOKEN=loadtest-token-123456789
    volumes:
      - influxdb_data:/var/lib/influxdb2
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
    networks:
      - loadtest-network

  # Jaeger for distributed tracing during load tests
  jaeger:
    image: jaegertracing/all-in-one:latest
    ports:
      - "16686:16686"
      - "14268:14268"
    environment:
      - COLLECTOR_OTLP_ENABLED=true
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 1G
        reservations:
          cpus: '0.25'
          memory: 512M
    networks:
      - loadtest-network

  # Load testing controller service
  loadtest-controller:
    build:
      context: .
      dockerfile: Dockerfile.loadtest
    environment:
      - TARGET_SERVICE_URL=http://pattern-mining:8001
      - POSTGRES_URL=********************************************/pattern_mining_test
      - REDIS_URL=redis://redis:6379/0
      - INFLUXDB_URL=http://influxdb:8086
      - INFLUXDB_TOKEN=loadtest-token-123456789
      - INFLUXDB_ORG=pattern-mining
      - INFLUXDB_BUCKET=load-testing
      - JAEGER_ENDPOINT=http://jaeger:14268/api/traces
    volumes:
      - ./tests:/app/tests:ro
      - ./config:/app/config:ro
      - ./scripts:/app/scripts:ro
      - ./test_results:/app/test_results
    depends_on:
      pattern-mining:
        condition: service_healthy
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    command: ["tail", "-f", "/dev/null"]  # Keep container running
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G
    networks:
      - loadtest-network

  # Resource monitor for system metrics during load testing
  resource-monitor:
    image: prom/node-exporter:latest
    ports:
      - "9100:9100"
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.1'
          memory: 128M
    networks:
      - loadtest-network

  # cAdvisor for container metrics
  cadvisor:
    image: gcr.io/cadvisor/cadvisor:latest
    ports:
      - "8080:8080"
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:ro
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
      - /dev/disk/:/dev/disk:ro
    privileged: true
    devices:
      - /dev/kmsg
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.1'
          memory: 128M
    networks:
      - loadtest-network

networks:
  loadtest-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  postgres_test_data:
    driver: local
  redis_test_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  influxdb_data:
    driver: local

# Health check and monitoring configuration
x-healthcheck-defaults: &healthcheck-defaults
  interval: 30s
  timeout: 10s
  retries: 3
  start_period: 60s

# Resource limits defaults
x-resource-defaults: &resource-defaults
  deploy:
    resources:
      limits:
        cpus: '1.0'
        memory: 2G
      reservations:
        cpus: '0.5'
        memory: 1G