# Phase 2 Load Testing Implementation

Comprehensive load testing framework for Pattern Mining service featuring advanced user behavior simulation, memory leak detection, realistic workload testing, and scalability analysis.

## 🚀 Overview

Phase 2 Load Testing enhances the existing Phase 1 performance testing infrastructure with enterprise-grade load testing capabilities designed to validate production readiness under realistic conditions.

### Key Features

- **Concurrent User Simulation**: Realistic user behavior patterns with geographic distribution
- **Sustained Load Testing**: Long-duration testing (4-24+ hours) with memory leak detection
- **Realistic Workload Scenarios**: Production-like testing including development teams, CI/CD pipelines, and enterprise audits
- **Scalability Analysis**: Breaking point identification and capacity planning
- **Advanced Memory Profiling**: Comprehensive memory leak detection using tracemalloc and statistical analysis
- **Performance SLA Validation**: Automated compliance checking against performance thresholds
- **Geographic Distribution**: Multi-region latency simulation
- **Cache Behavior Analysis**: Cache hit/miss patterns under load

## 📁 Implementation Structure

```
services/pattern-mining/
├── config/
│   └── load_testing_config.yaml          # Comprehensive testing configuration
├── tests/performance/
│   ├── test_concurrent_users.py          # Phase 2: User behavior simulation
│   ├── test_sustained_load.py            # Phase 2: Memory leak detection
│   ├── test_realistic_workloads.py       # Phase 2: Production scenarios
│   └── test_scalability_limits.py        # Phase 2: Capacity analysis
├── scripts/
│   ├── run_phase2_load_tests.py          # Advanced test runner
│   └── quick_load_test.sh                # Quick testing script
├── docker-compose.loadtest.yml           # Load testing infrastructure
├── Dockerfile.loadtest                   # Load testing controller
└── docs/PHASE2_LOAD_TESTING.md          # This documentation
```

## 🔧 Configuration

### Testing Profiles

Configure different testing scenarios in `config/load_testing_config.yaml`:

#### Development Profile
- **Duration**: 2 minutes
- **Max Users**: 10
- **Purpose**: Quick validation during development
- **Scenarios**: Basic load, spike test

#### Staging Profile  
- **Duration**: 15 minutes
- **Max Users**: 50
- **Purpose**: Production-like validation
- **Scenarios**: Concurrent users, realistic workloads, sustained load

#### Production Profile
- **Duration**: 4 hours
- **Max Users**: 200
- **Purpose**: Full enterprise load testing
- **Scenarios**: All scenarios with enterprise workloads

#### Endurance Profile
- **Duration**: 24 hours
- **Max Users**: 100
- **Purpose**: Memory leak detection and stability validation
- **Scenarios**: Sustained operations with periodic spikes

### User Behavior Patterns

Four realistic user personas with different interaction patterns:

1. **Developer** (40% weight): Code analysis focused, 30-120 minute sessions
2. **Architect** (25% weight): System design focused, 60-240 minute sessions  
3. **QA Engineer** (20% weight): Quality assurance focused, 45-180 minute sessions
4. **Researcher** (15% weight): Deep analysis focused, 90-300 minute sessions

### Performance Thresholds

SLA compliance validation with configurable thresholds:

- **Response Times**: P50, P95, P99 percentiles for each operation type
- **Throughput**: Minimum, target, and maximum requests per second
- **Error Rates**: Warning (1%), critical (5%), failure (10%) thresholds
- **Resource Utilization**: CPU, memory, disk usage limits
- **Memory Leak Detection**: Growth rate and total growth thresholds

## 🚀 Quick Start

### 1. Basic Setup

```bash
# Install dependencies
make install

# Validate Phase 2 setup
make loadtest-validate

# Run quick 5-minute test
make loadtest-quick
```

### 2. Development Testing

```bash
# Quick development validation
./scripts/quick_load_test.sh --quick

# More comprehensive development testing
make loadtest-dev
```

### 3. Staging Validation

```bash
# Staging environment validation
make loadtest-staging

# Or with custom duration
./scripts/quick_load_test.sh --validate --duration 1
```

### 4. Production Readiness

```bash
# Full production readiness test (4 hours)
make loadtest-production

# Or with custom configuration
python scripts/run_phase2_load_tests.py --profile production --all-scenarios --duration 4
```

## 📊 Test Scenarios

### 1. Concurrent Users Testing (`test_concurrent_users.py`)

**Features:**
- Realistic user behavior simulation with think times
- Geographic distribution (US West/East, Europe, Asia)
- Session-based testing with user journey mapping
- User satisfaction scoring
- Dynamic load adjustment based on system response

**Key Metrics:**
- Concurrent user capacity
- Response time under load
- User satisfaction scores
- Geographic performance variation

### 2. Sustained Load Testing (`test_sustained_load.py`)

**Features:**
- Long-duration testing (4-24+ hours configurable)
- Advanced memory leak detection using tracemalloc
- Progressive memory monitoring with statistical analysis
- Garbage collection efficiency tracking
- Performance degradation detection

**Key Metrics:**
- Memory growth rate (MB/hour)
- Potential memory leak detection
- GC efficiency over time
- Performance stability metrics

### 3. Realistic Workloads Testing (`test_realistic_workloads.py`)

**Features:**
- Development team collaboration simulation
- CI/CD pipeline integration testing
- Enterprise audit workload patterns
- Batch processing simulation
- Cache behavior analysis under realistic load

**Key Metrics:**
- SLA compliance percentages
- Cache hit/miss ratios
- Workload-specific performance metrics
- Resource utilization patterns

### 4. Scalability Limits Testing (`test_scalability_limits.py`)

**Features:**
- Breaking point identification
- Performance cliff detection
- Resource limit testing
- Efficiency scoring
- Bottleneck identification and analysis

**Key Metrics:**
- Maximum concurrent users
- Maximum throughput (req/s)
- Performance cliff points
- Efficiency scores
- Scaling recommendations

## 🐳 Docker Integration

### Setup Load Testing Infrastructure

```bash
# Start monitoring infrastructure
make loadtest-setup

# Available services:
# - Grafana: http://localhost:3000 (admin/loadtest123)
# - Prometheus: http://localhost:9090
# - InfluxDB: http://localhost:8086
```

### Run Docker-Based Load Tests

```bash
# Complete Docker-based testing
make loadtest-docker

# Manual Docker execution
docker-compose -f docker-compose.loadtest.yml up -d
docker-compose -f docker-compose.loadtest.yml exec loadtest-controller \
    python scripts/run_phase2_load_tests.py --profile staging --all-scenarios
```

### Monitoring Stack

The Docker setup includes comprehensive monitoring:

- **Prometheus**: Metrics collection and alerting
- **Grafana**: Real-time dashboards and visualization  
- **InfluxDB**: Time-series performance data storage
- **Jaeger**: Distributed tracing during load tests
- **cAdvisor**: Container resource monitoring
- **Node Exporter**: System metrics collection

## 📈 Results Analysis

### Automated Analysis

```bash
# Analyze test results
make loadtest-analyze

# Check for memory leaks
make loadtest-memory-check

# Validate SLA compliance
make loadtest-sla-check

# Generate comprehensive report
make loadtest-report
```

### Manual Analysis

Results are stored in structured JSON format in `test_results/` directory:

```
test_results/
├── session_YYYYMMDD_HHMMSS/
│   ├── concurrent_users/
│   │   ├── user_behavior_analysis.json
│   │   ├── geographic_performance.json
│   │   └── session_metrics.json
│   ├── sustained_load/
│   │   ├── memory_analysis.json
│   │   ├── performance_trends.json
│   │   └── stability_metrics.json
│   ├── realistic_workloads/
│   │   ├── development_team_performance.json
│   │   ├── cicd_pipeline_performance.json
│   │   └── enterprise_audit_performance.json
│   └── scalability_limits/
│       ├── scalability_analysis.json
│       ├── bottleneck_analysis.json
│       └── scaling_recommendations.json
```

### Key Metrics to Monitor

1. **Response Time Metrics**:
   - P50, P95, P99 response times
   - Response time distribution
   - Performance degradation over time

2. **Memory Metrics**:
   - Memory growth rate (MB/hour)
   - Total memory growth
   - GC efficiency
   - Heap fragmentation ratio

3. **Throughput Metrics**:
   - Requests per second
   - Concurrent user capacity
   - Peak throughput achieved

4. **Error Metrics**:
   - Error rate percentages
   - Error type distribution
   - Error recovery patterns

5. **SLA Compliance**:
   - Overall compliance percentage
   - SLA violations by operation type
   - Compliance trends over time

## 🔄 CI/CD Integration

### GitHub Actions Workflow

The Phase 2 load testing is integrated into the CI/CD pipeline via `.github/workflows/pattern-mining-load-testing.yml`:

**Triggers:**
- Manual execution with configurable profiles
- Scheduled daily runs (development profile)
- Automatic runs on performance-critical changes

**Execution Profiles:**
- **Development**: Quick validation on commits
- **Staging**: Comprehensive validation on PR merges
- **Production**: Full testing on main branch releases
- **Endurance**: Scheduled weekly long-duration testing

### Manual Trigger Examples

```bash
# Trigger via GitHub Actions
gh workflow run pattern-mining-load-testing.yml \
  --ref main \
  -f testing_profile=production \
  -f test_scenarios=concurrent_users,sustained_load \
  -f max_duration_hours=4

# Trigger endurance testing
gh workflow run pattern-mining-load-testing.yml \
  --ref main \
  -f testing_profile=endurance \
  -f max_duration_hours=24
```

## 🎯 Best Practices

### 1. Test Environment Preparation

- Ensure adequate system resources (CPU, memory, network)
- Use production-like data volumes and complexity
- Validate infrastructure dependencies before testing
- Set up proper monitoring and alerting

### 2. Test Execution

- Start with development profile for validation
- Run staging tests before production deployment
- Monitor system resources during long-duration tests
- Use appropriate test duration for scenario complexity

### 3. Results Interpretation

- Focus on trends rather than absolute values
- Compare results across test runs for regression detection
- Analyze memory leak patterns over multiple test cycles
- Validate SLA compliance against business requirements

### 4. Performance Optimization

- Use memory profiling results to identify optimization opportunities
- Analyze bottleneck identification for infrastructure scaling
- Monitor cache effectiveness and optimize cache strategies
- Implement performance improvements based on scalability analysis

## 🔍 Troubleshooting

### Common Issues

1. **Memory Profiling Overhead**:
   - Use `--skip-memory-profiling` flag to reduce resource usage
   - Run shorter duration tests when profiling is enabled
   - Monitor system resources during memory-intensive tests

2. **Test Timeouts**:
   - Increase timeout values for long-duration tests
   - Monitor system resources for capacity constraints
   - Use staged testing approach for complex scenarios

3. **Infrastructure Dependencies**:
   - Validate PostgreSQL and Redis connectivity
   - Check Docker container health status
   - Verify network connectivity for external services

4. **Resource Constraints**:
   - Monitor CPU and memory usage during testing
   - Adjust concurrent user limits based on system capacity
   - Use Docker resource limits to prevent system overload

### Debug Mode

Enable verbose logging for detailed troubleshooting:

```bash
# Verbose script execution
./scripts/quick_load_test.sh --verbose --profile development

# Python script with debug logging
export LOG_LEVEL=DEBUG
python scripts/run_phase2_load_tests.py --profile development --scenarios concurrent_users
```

## 📚 Advanced Configuration

### Custom Test Scenarios

Create custom test scenarios by modifying `config/load_testing_config.yaml`:

```yaml
# Add custom workload scenario
workload_scenarios:
  custom_workload:
    description: "Custom testing scenario"
    concurrent_users: [5, 25]
    duration_hours: 2
    operations:
      - type: custom_analysis
        frequency: 0.5
        batch_size: [1, 10]
```

### Environment-Specific Overrides

Configure environment-specific settings:

```yaml
environments:
  local_development:
    max_users_override: 5
    duration_override_minutes: 2
    
  ci_environment:
    max_users_override: 10
    duration_override_minutes: 5
    skip_memory_profiling: true
```

### Custom Performance Thresholds

Define custom SLA requirements:

```yaml
performance_thresholds:
  response_times:
    custom_operation:
      p50: 1000   # 1 second
      p95: 3000   # 3 seconds
      p99: 8000   # 8 seconds
```

## 🤝 Contributing

When contributing to Phase 2 load testing:

1. **Test New Features**: Add corresponding load test scenarios
2. **Validate Changes**: Run staging profile tests before submitting PRs
3. **Update Thresholds**: Adjust SLA thresholds based on performance improvements
4. **Document Changes**: Update configuration documentation for new features
5. **Monitor Results**: Review load test results in CI/CD pipeline

## 📝 Changelog

### Phase 2.0 - Initial Implementation
- Concurrent user behavior simulation
- Memory leak detection framework
- Realistic workload testing scenarios
- Scalability limits analysis
- Docker-based testing infrastructure
- Comprehensive CI/CD integration
- Advanced monitoring and reporting

## 🔗 Related Documentation

- [Phase 1 Performance Testing](./PERFORMANCE_TESTING.md)
- [API Documentation](./API.md)
- [Deployment Guide](./DEPLOYMENT.md)
- [Monitoring Setup](./MONITORING.md)
- [Contributing Guidelines](../CONTRIBUTING.md)

---

For questions or issues with Phase 2 load testing, please refer to the troubleshooting section or create an issue with the `load-testing` label.