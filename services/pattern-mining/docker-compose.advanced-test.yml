# Phase 2 Advanced Integration Testing Infrastructure
# Enhanced Docker Compose with network partitioning and failure injection capabilities
version: '3.8'

networks:
  # Main application network
  app-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
  
  # Isolated networks for partition testing
  partition-a:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
        
  partition-b:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
        
  # External services network
  external-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

services:
  # =============================================================================
  # Core Application Services
  # =============================================================================
  
  pattern-mining-primary:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: pattern-mining-primary
    ports:
      - "8001:8001"
    environment:
      - ENVIRONMENT=test
      - REDIS_URL=redis://redis-primary:6379
      - POSTGRES_URL=****************************************************/pattern_mining_test
      - BIGQUERY_PROJECT_ID=test-project
      - GEMINI_API_KEY=test-key
      - LOG_LEVEL=DEBUG
      - ENABLE_METRICS=true
      - CIRCUIT_BREAKER_ENABLED=true
      - BULKHEAD_ENABLED=true
      - RATE_LIMIT_ENABLED=true
    networks:
      - app-network
      - partition-a
    depends_on:
      - postgres-primary
      - redis-primary
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    deploy:
      resources:
        limits:
          cpus: "2.0"
          memory: 1G
        reservations:
          cpus: "0.5"
          memory: 256M

  pattern-mining-replica:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: pattern-mining-replica
    ports:
      - "8002:8001"
    environment:
      - ENVIRONMENT=test
      - REDIS_URL=redis://redis-replica:6379
      - POSTGRES_URL=****************************************************/pattern_mining_test
      - BIGQUERY_PROJECT_ID=test-project
      - GEMINI_API_KEY=test-key
      - LOG_LEVEL=DEBUG
      - ENABLE_METRICS=true
      - CIRCUIT_BREAKER_ENABLED=true
      - BULKHEAD_ENABLED=true
      - RATE_LIMIT_ENABLED=true
    networks:
      - app-network
      - partition-b
    depends_on:
      - postgres-replica
      - redis-replica
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    deploy:
      resources:
        limits:
          cpus: "2.0"
          memory: 1G
        reservations:
          cpus: "0.5"
          memory: 256M

  # =============================================================================
  # Database Services
  # =============================================================================
  
  postgres-primary:
    image: postgres:15-alpine
    container_name: postgres-primary
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=pattern_mining_test
      - POSTGRES_USER=testuser
      - POSTGRES_PASSWORD=testpass
      - POSTGRES_INITDB_ARGS=--data-checksums
    networks:
      - app-network
      - partition-a
    volumes:
      - postgres_primary_data:/var/lib/postgresql/data
      - ./tests/fixtures/sql:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U testuser -d pattern_mining_test"]
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          cpus: "1.0"
          memory: 512M
        reservations:
          cpus: "0.25"
          memory: 128M

  postgres-replica:
    image: postgres:15-alpine
    container_name: postgres-replica
    ports:
      - "5433:5432"
    environment:
      - POSTGRES_DB=pattern_mining_test
      - POSTGRES_USER=testuser
      - POSTGRES_PASSWORD=testpass
      - POSTGRES_INITDB_ARGS=--data-checksums
    networks:
      - app-network
      - partition-b
    volumes:
      - postgres_replica_data:/var/lib/postgresql/data
      - ./tests/fixtures/sql:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U testuser -d pattern_mining_test"]
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          cpus: "1.0"
          memory: 512M
        reservations:
          cpus: "0.25"
          memory: 128M

  # =============================================================================
  # Cache Services
  # =============================================================================
  
  redis-primary:
    image: redis:7-alpine
    container_name: redis-primary
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    networks:
      - app-network
      - partition-a
    volumes:
      - redis_primary_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          cpus: "0.5"
          memory: 256M
        reservations:
          cpus: "0.1"
          memory: 64M

  redis-replica:
    image: redis:7-alpine
    container_name: redis-replica
    ports:
      - "6380:6379"
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    networks:
      - app-network
      - partition-b
    volumes:
      - redis_replica_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          cpus: "0.5"
          memory: 256M
        reservations:
          cpus: "0.1"
          memory: 64M

  # =============================================================================
  # External Service Simulators
  # =============================================================================
  
  gemini-simulator:
    image: wiremock/wiremock:latest
    container_name: gemini-simulator
    ports:
      - "8080:8080"
    environment:
      - WIREMOCK_OPTIONS=--global-response-templating --verbose
    networks:
      - external-network
      - app-network
    volumes:
      - ./tests/fixtures/wiremock/gemini:/home/<USER>/mappings
      - ./tests/fixtures/wiremock/responses:/home/<USER>/__files
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/__admin/health"]
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          cpus: "0.5"
          memory: 256M

  bigquery-simulator:
    image: wiremock/wiremock:latest
    container_name: bigquery-simulator
    ports:
      - "8081:8080"
    environment:
      - WIREMOCK_OPTIONS=--global-response-templating --verbose
    networks:
      - external-network
      - app-network
    volumes:
      - ./tests/fixtures/wiremock/bigquery:/home/<USER>/mappings
      - ./tests/fixtures/wiremock/responses:/home/<USER>/__files
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/__admin/health"]
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          cpus: "0.5"
          memory: 256M

  # =============================================================================
  # Monitoring and Observability
  # =============================================================================
  
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    ports:
      - "9090:9090"
    networks:
      - app-network
    volumes:
      - ./tests/fixtures/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=24h'
      - '--web.enable-lifecycle'
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:9090/"]
      interval: 10s
      timeout: 5s
      retries: 5

  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=testpass
      - GF_USERS_ALLOW_SIGN_UP=false
    networks:
      - app-network
    volumes:
      - grafana_data:/var/lib/grafana
      - ./tests/fixtures/grafana/dashboards:/var/lib/grafana/dashboards
      - ./tests/fixtures/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000/api/health || exit 1"]
      interval: 10s
      timeout: 5s
      retries: 5

  # =============================================================================
  # Testing and Network Utilities
  # =============================================================================
  
  network-chaos:
    image: gaiaadm/pumba:latest
    container_name: network-chaos
    privileged: true
    networks:
      - app-network
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    command: --log-level info --interval 30s netem --duration 10s --tc-image gaiadocker/iproute2 delay --time 100 --jitter 50 pattern-mining-primary
    profiles:
      - chaos-testing
    deploy:
      resources:
        limits:
          cpus: "0.2"
          memory: 128M

  load-generator:
    image: loadimpact/k6:latest
    container_name: load-generator
    networks:
      - app-network
    volumes:
      - ./tests/performance:/scripts
    environment:
      - BASE_URL=http://pattern-mining-primary:8001
      - REPLICA_URL=http://pattern-mining-replica:8001
    profiles:
      - load-testing
    deploy:
      resources:
        limits:
          cpus: "1.0"
          memory: 512M

  # Network partition simulator
  toxiproxy:
    image: ghcr.io/shopify/toxiproxy:latest
    container_name: toxiproxy
    ports:
      - "8474:8474"  # API port
      - "5434:5434"  # Postgres proxy
      - "6381:6381"  # Redis proxy
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8474/version"]
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          cpus: "0.5"
          memory: 256M

  # Test runner for advanced integration tests
  test-runner:
    build:
      context: .
      dockerfile: Dockerfile.test
    container_name: test-runner
    networks:
      - app-network
      - partition-a
      - partition-b
      - external-network
    volumes:
      - .:/app
      - test_results:/app/test-results
    environment:
      - ENVIRONMENT=integration-test
      - PRIMARY_URL=http://pattern-mining-primary:8001
      - REPLICA_URL=http://pattern-mining-replica:8001
      - POSTGRES_PRIMARY_URL=****************************************************/pattern_mining_test
      - POSTGRES_REPLICA_URL=****************************************************/pattern_mining_test
      - REDIS_PRIMARY_URL=redis://redis-primary:6379
      - REDIS_REPLICA_URL=redis://redis-replica:6379
      - GEMINI_SIMULATOR_URL=http://gemini-simulator:8080
      - BIGQUERY_SIMULATOR_URL=http://bigquery-simulator:8080
      - TOXIPROXY_URL=http://toxiproxy:8474
      - PROMETHEUS_URL=http://prometheus:9090
      - TEST_TIMEOUT=3600
      - PARALLEL_WORKERS=4
    depends_on:
      - pattern-mining-primary
      - pattern-mining-replica
      - postgres-primary
      - postgres-replica
      - redis-primary
      - redis-replica
      - gemini-simulator
      - bigquery-simulator
      - toxiproxy
      - prometheus
    profiles:
      - testing
    deploy:
      resources:
        limits:
          cpus: "2.0"
          memory: 2G

volumes:
  postgres_primary_data:
    driver: local
  postgres_replica_data:
    driver: local
  redis_primary_data:
    driver: local
  redis_replica_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  test_results:
    driver: local

# =============================================================================
# Docker Compose Profiles for Different Testing Scenarios
# =============================================================================

# Usage Examples:
# 
# Basic integration testing:
# docker-compose -f docker-compose.advanced-test.yml up -d
#
# With chaos testing:
# docker-compose -f docker-compose.advanced-test.yml --profile chaos-testing up -d
#
# With load testing:
# docker-compose -f docker-compose.advanced-test.yml --profile load-testing up -d
#
# Full advanced testing suite:
# docker-compose -f docker-compose.advanced-test.yml --profile testing --profile chaos-testing --profile load-testing up -d
#
# Run specific test scenarios:
# docker-compose -f docker-compose.advanced-test.yml run --rm test-runner pytest tests/integration/test_advanced_integration.py -v
# docker-compose -f docker-compose.advanced-test.yml run --rm test-runner pytest tests/integration/test_cascading_failures.py -v
# docker-compose -f docker-compose.advanced-test.yml run --rm test-runner pytest tests/integration/test_service_resilience.py -v
# docker-compose -f docker-compose.advanced-test.yml run --rm test-runner pytest tests/integration/test_data_consistency.py -v
# docker-compose -f docker-compose.advanced-test.yml run --rm test-runner pytest tests/integration/test_performance_integration.py -v

# Network Partitioning Examples:
# 
# Create network partition between primary and replica:
# docker network disconnect pattern-mining_app-network pattern-mining-primary
# docker network disconnect pattern-mining_app-network pattern-mining-replica
#
# Restore network connectivity:
# docker network connect pattern-mining_app-network pattern-mining-primary
# docker network connect pattern-mining_app-network pattern-mining-replica
#
# Inject network latency using toxiproxy:
# curl -X POST http://localhost:8474/proxies/postgres/toxics -d '{"name":"latency","type":"latency","attributes":{"latency":1000}}'
# curl -X POST http://localhost:8474/proxies/redis/toxics -d '{"name":"bandwidth","type":"bandwidth","attributes":{"rate":1024}}'

# Resource Monitoring:
#
# Monitor container resources:
# docker stats pattern-mining-primary pattern-mining-replica
#
# View service logs:
# docker-compose -f docker-compose.advanced-test.yml logs -f pattern-mining-primary
# docker-compose -f docker-compose.advanced-test.yml logs -f pattern-mining-replica
#
# Access monitoring dashboards:
# Prometheus: http://localhost:9090
# Grafana: http://localhost:3000 (admin/testpass)