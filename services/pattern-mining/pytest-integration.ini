# PyTest Configuration for Integration Tests
# Specific configuration for integration testing with real services

[tool:pytest]
# Test discovery and execution
testpaths = tests/integration
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# Markers for test categorization
markers =
    integration: Integration tests with real services
    slow: Slow-running tests (> 30 seconds)
    gemini: Tests requiring Gemini API access
    database: Tests requiring database connection
    redis: Tests requiring Redis connection
    workflow: End-to-end workflow tests
    performance: Performance and load tests
    security: Security-focused integration tests
    batch: Batch processing tests
    error_recovery: Error handling and recovery tests
    real_services: Tests with minimal mocking using real services

# Async support
asyncio_mode = auto

# Timeout configuration
timeout = 300
timeout_method = thread

# Output and reporting
addopts = 
    --strict-markers
    --strict-config
    --tb=short
    --durations=10
    --verbose
    --capture=no
    --showlocals
    --junit-xml=test-results/integration-junit.xml

# Coverage configuration for integration tests
cov = pattern_mining
cov-report = html:coverage/integration-html
cov-report = term-missing
cov-report = xml:coverage/integration-coverage.xml
cov-config = .coveragerc

# Test execution configuration
maxfail = 5
junit_family = xunit2

# Logging configuration
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Filter warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::RuntimeWarning:redis
    ignore::UserWarning:google
    error::UserWarning:pattern_mining

# Minimum Python version
minversion = 3.11

# Parallel execution (use with pytest-xdist)
# -n auto for automatic worker detection
# -n 4 for specific number of workers
# Uncomment for CI/CD environments
# addopts = -n auto

# Test data configuration
testdata_dir = tests/data
testdata_formats = json yaml

# Integration test specific settings
integration_test_timeout = 300
integration_max_retries = 3
integration_retry_delay = 5