#!/bin/bash
set -euo pipefail

# Pattern Mining Service Pre-Deployment Validation Script
# Validates all prerequisites before production deployment
# Version: 2.0.0

# Color output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
PROJECT_ID="${PROJECT_ID:-vibe-match-463114}"
SERVICE_NAME="pattern-mining"
REGION="${REGION:-us-central1}"

# Service Account Configuration
SERVICE_ACCOUNT_KEY="${SERVICE_ACCOUNT_KEY:-vibe-match-463114-dbda8d8a6cb9.json}"
SERVICE_ACCOUNT_KEY_PATH="${SERVICE_ACCOUNT_KEY_PATH:-$(pwd)/../../..}" # Path to project root

# Validation results
VALIDATION_PASSED=true
VALIDATION_ERRORS=()
VALIDATION_WARNINGS=()

# Print functions
print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

print_check() {
    echo -n "  Checking $1... "
}

print_pass() {
    echo -e "${GREEN}PASS${NC}"
}

print_fail() {
    echo -e "${RED}FAIL${NC}"
    VALIDATION_PASSED=false
    VALIDATION_ERRORS+=("$1")
}

print_warning() {
    echo -e "${YELLOW}WARNING${NC}"
    VALIDATION_WARNINGS+=("$1")
}

# Validate Python version
validate_python() {
    print_header "Python Environment"
    print_check "Python version"
    
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 --version | cut -d' ' -f2)
        MAJOR=$(echo $PYTHON_VERSION | cut -d. -f1)
        MINOR=$(echo $PYTHON_VERSION | cut -d. -f2)
        
        if [ "$MAJOR" -eq 3 ] && [ "$MINOR" -ge 11 ]; then
            print_pass
            echo "    Python version: $PYTHON_VERSION"
        else
            print_fail "Python 3.11+ required, found $PYTHON_VERSION"
        fi
    else
        print_fail "Python 3 not found"
    fi
}

# Validate Docker
validate_docker() {
    print_header "Docker Environment"
    print_check "Docker installation"
    
    if command -v docker &> /dev/null; then
        print_pass
        
        print_check "Docker daemon"
        if docker ps &> /dev/null; then
            print_pass
        else
            print_fail "Docker daemon not running"
        fi
        
        print_check "Docker build context"
        if [ -f "Dockerfile" ]; then
            print_pass
        else
            print_fail "Dockerfile not found"
        fi
    else
        print_fail "Docker not installed"
    fi
}

# Validate GCP configuration
validate_gcp() {
    print_header "Google Cloud Platform"
    print_check "gcloud CLI"
    
    if command -v gcloud &> /dev/null; then
        print_pass
        
        print_check "GCP authentication"
        if gcloud auth list --filter=status:ACTIVE --format="value(account)" &> /dev/null; then
            print_pass
            ACTIVE_ACCOUNT=$(gcloud auth list --filter=status:ACTIVE --format="value(account)")
            echo "    Active account: $ACTIVE_ACCOUNT"
        else
            print_fail "Not authenticated with GCP"
        fi
        
        print_check "Project configuration"
        CURRENT_PROJECT=$(gcloud config get-value project 2>/dev/null)
        if [ -n "$CURRENT_PROJECT" ]; then
            print_pass
            echo "    Current project: $CURRENT_PROJECT"
            
            if [ "$CURRENT_PROJECT" != "$PROJECT_ID" ]; then
                print_warning "Project mismatch: expected $PROJECT_ID, got $CURRENT_PROJECT"
            fi
        else
            print_fail "No GCP project configured"
        fi
        
        print_check "Required APIs"
        REQUIRED_APIS=(
            "run.googleapis.com"
            "containerregistry.googleapis.com"
            "secretmanager.googleapis.com"
            "bigquery.googleapis.com"
            "storage-api.googleapis.com"
            "monitoring.googleapis.com"
        )
        
        ALL_APIS_ENABLED=true
        for api in "${REQUIRED_APIS[@]}"; do
            if gcloud services list --enabled --filter="name:$api" --format="value(name)" | grep -q "$api"; then
                :
            else
                ALL_APIS_ENABLED=false
                VALIDATION_WARNINGS+=("API not enabled: $api")
            fi
        done
        
        if [ "$ALL_APIS_ENABLED" = true ]; then
            print_pass
        else
            print_warning "Some required APIs not enabled"
        fi
    else
        print_fail "gcloud CLI not installed"
    fi
}

# Validate secrets
validate_secrets() {
    print_header "Secret Management"
    
    REQUIRED_SECRETS=(
        "gemini-api-key"
        "jwt-secret"
        "database-password"
        "redis-password"
    )
    
    for secret in "${REQUIRED_SECRETS[@]}"; do
        print_check "Secret: $secret"
        if gcloud secrets describe "$secret" --project="$PROJECT_ID" &> /dev/null; then
            print_pass
        else
            print_fail "Secret not found: $secret"
        fi
    done
}

# Validate service account
validate_service_account() {
    print_header "Service Account"
    
    SERVICE_ACCOUNT="${SERVICE_NAME}-sa@${PROJECT_ID}.iam.gserviceaccount.com"
    print_check "Service account existence"
    
    if gcloud iam service-accounts describe "$SERVICE_ACCOUNT" --project="$PROJECT_ID" &> /dev/null; then
        print_pass
        
        print_check "Required roles"
        REQUIRED_ROLES=(
            "roles/bigquery.dataEditor"
            "roles/secretmanager.secretAccessor"
            "roles/monitoring.metricWriter"
        )
        
        # Get current IAM policy
        POLICY=$(gcloud projects get-iam-policy "$PROJECT_ID" --format=json 2>/dev/null)
        
        MISSING_ROLES=()
        for role in "${REQUIRED_ROLES[@]}"; do
            if echo "$POLICY" | grep -q "\"role\": \"$role\"" && \
               echo "$POLICY" | grep -q "serviceAccount:$SERVICE_ACCOUNT"; then
                :
            else
                MISSING_ROLES+=("$role")
            fi
        done
        
        if [ ${#MISSING_ROLES[@]} -eq 0 ]; then
            print_pass
        else
            print_warning "Missing roles: ${MISSING_ROLES[*]}"
        fi
    else
        print_fail "Service account not found"
    fi
}

# Validate dependencies
validate_dependencies() {
    print_header "Application Dependencies"
    
    print_check "requirements.txt"
    if [ -f "requirements.txt" ]; then
        print_pass
        
        print_check "Security vulnerabilities"
        # Check for known vulnerable packages
        VULNERABLE_PACKAGES=()
        
        # Add known vulnerabilities here
        if grep -q "idna==0.4.0" requirements.txt; then
            VULNERABLE_PACKAGES+=("idna==0.4.0 (CVE-2024-3651)")
        fi
        
        if [ ${#VULNERABLE_PACKAGES[@]} -eq 0 ]; then
            print_pass
        else
            print_warning "Vulnerable packages found: ${VULNERABLE_PACKAGES[*]}"
        fi
    else
        print_fail "requirements.txt not found"
    fi
    
    print_check "pyproject.toml"
    if [ -f "pyproject.toml" ]; then
        print_pass
    else
        print_fail "pyproject.toml not found"
    fi
}

# Validate configuration
validate_configuration() {
    print_header "Application Configuration"
    
    print_check "Environment template"
    if [ -f ".env.example" ]; then
        print_pass
    else
        print_warning ".env.example not found"
    fi
    
    print_check "Secure defaults"
    if [ -f ".env.example" ]; then
        if grep -q "INSECURE_DEFAULT" .env.example; then
            print_warning "Insecure defaults found in .env.example"
        else
            print_pass
        fi
    fi
    
    print_check "Production config"
    if [ -f "deploy/production-config.yaml" ]; then
        print_pass
    else
        print_warning "Production config not found"
    fi
}

# Validate tests
validate_tests() {
    print_header "Test Suite"
    
    print_check "Test directory"
    if [ -d "tests" ]; then
        print_pass
        
        print_check "Unit tests"
        if find tests -name "test_*.py" | grep -q .; then
            print_pass
        else
            print_warning "No unit tests found"
        fi
        
        print_check "Integration tests"
        if [ -d "tests/integration" ]; then
            print_pass
        else
            print_warning "No integration tests found"
        fi
        
        print_check "Contract tests"
        if [ -d "tests/contract" ]; then
            print_pass
        else
            print_warning "No contract tests found"
        fi
    else
        print_fail "Test directory not found"
    fi
}

# Validate monitoring
validate_monitoring() {
    print_header "Monitoring Configuration"
    
    print_check "Prometheus alerts"
    if [ -f "monitoring/prometheus/alerts/ccl-contract-alerts.yaml" ]; then
        print_pass
    else
        print_warning "Prometheus alerts not found"
    fi
    
    print_check "Grafana dashboards"
    if [ -f "monitoring/grafana/dashboards/ccl-contract-compliance.json" ]; then
        print_pass
    else
        print_warning "Grafana dashboards not found"
    fi
}

# Run local tests
run_local_tests() {
    print_header "Local Validation Tests"
    
    print_check "Docker build test"
    if docker build --target production -t test-build -f Dockerfile . &> /dev/null; then
        print_pass
        docker rmi test-build &> /dev/null
    else
        print_fail "Docker build failed"
    fi
    
    print_check "Python syntax check"
    if find src -name "*.py" -exec python3 -m py_compile {} + 2>/dev/null; then
        print_pass
    else
        print_fail "Python syntax errors found"
    fi
}

# Display summary
display_summary() {
    echo -e "\n${BLUE}======== VALIDATION SUMMARY ========${NC}"
    
    if [ "$VALIDATION_PASSED" = true ]; then
        echo -e "${GREEN}✓ All critical checks passed${NC}"
    else
        echo -e "${RED}✗ Validation failed${NC}"
        echo -e "\n${RED}Errors:${NC}"
        for error in "${VALIDATION_ERRORS[@]}"; do
            echo "  - $error"
        done
    fi
    
    if [ ${#VALIDATION_WARNINGS[@]} -gt 0 ]; then
        echo -e "\n${YELLOW}Warnings:${NC}"
        for warning in "${VALIDATION_WARNINGS[@]}"; do
            echo "  - $warning"
        done
    fi
    
    echo -e "\n${BLUE}====================================${NC}"
    
    if [ "$VALIDATION_PASSED" = true ]; then
        echo -e "\n${GREEN}✓ Ready for deployment!${NC}"
        echo -e "\nNext step: Run ${BLUE}./scripts/deploy-production.sh${NC}"
        return 0
    else
        echo -e "\n${RED}✗ Please fix errors before deployment${NC}"
        return 1
    fi
}

# Main validation flow
main() {
    echo -e "${BLUE}Pattern Mining Service - Pre-Deployment Validation${NC}"
    echo -e "${BLUE}=================================================${NC}"
    
    # Check if in correct directory
    if [ ! -f "pyproject.toml" ] || [ ! -d "src/pattern_mining" ]; then
        echo -e "${RED}Error: Must run from pattern-mining service root directory${NC}"
        exit 1
    fi
    
    # Run all validations
    validate_python
    validate_docker
    validate_gcp
    validate_secrets
    validate_service_account
    validate_dependencies
    validate_configuration
    validate_tests
    validate_monitoring
    run_local_tests
    
    # Display summary and exit with appropriate code
    display_summary
}

# Run main function
main "$@"