#!/usr/bin/env python3
"""
Comprehensive performance validation for pattern mining service.

Combines Wave 2 (Production Validation) and Wave 2.5 (Contract Compliance).
"""

import asyncio
import logging
import sys
from pathlib import Path
from datetime import datetime

# Add the parent directory to the path
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.pattern_mining.performance import (
    PerformanceValidator,
    ContractPerformanceValidator,
)
from src.pattern_mining.cache.redis_client import RedisClient


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def run_wave_2_validation(redis_client):
    """Run Wave 2 production readiness validation."""
    logger.info("\n" + "=" * 80)
    logger.info("WAVE 2: PRODUCTION READINESS VALIDATION")
    logger.info("=" * 80)
    
    validator = PerformanceValidator(redis_client=redis_client)
    
    # Run production validation
    result = await validator.validate_production_readiness()
    
    logger.info(f"\nProduction Readiness Results:")
    logger.info(f"  Status: {result.overall_status.upper()}")
    logger.info(f"  Score: {result.performance_score:.1f}/100")
    logger.info(f"  Certification: {result.certification_level.upper()}")
    
    # Key metrics
    logger.info(f"\nKey Performance Metrics:")
    key_metrics = [
        ("LOC Processing", "loc_per_minute", "LOC/min"),
        ("Throughput", "throughput_rps", "RPS"),
        ("Latency P95", "latency_p95_ms", "ms"),
        ("Success Rate", "success_rate", "%"),
        ("Memory Usage", "memory_limit_mb", "MB"),
    ]
    
    for label, metric, unit in key_metrics:
        if metric in result.validation_criteria:
            data = result.validation_criteria[metric]
            status = "✅" if data["passed"] else "❌"
            value = data["actual"]
            if unit == "%":
                value = value * 100
            logger.info(f"  {label}: {status} {value:.2f} {unit}")
    
    return result


async def run_wave_2_5_validation(redis_client):
    """Run Wave 2.5 CCL contract compliance validation."""
    logger.info("\n" + "=" * 80)
    logger.info("WAVE 2.5: CCL CONTRACT COMPLIANCE VALIDATION")
    logger.info("=" * 80)
    
    validator = ContractPerformanceValidator(redis_client=redis_client)
    
    # Run contract validation
    result = await validator.validate_contract_compliance()
    
    logger.info(f"\nContract Compliance Results:")
    logger.info(f"  Status: {result.overall_status.upper()}")
    logger.info(f"  Score: {result.performance_score:.1f}/100")
    logger.info(f"  Compliance: {result.certification_level.upper()}")
    
    # Contract requirements
    logger.info(f"\nCCL Contract Requirements:")
    contract_metrics = [
        ("Processing Budget", "processing_time_seconds", "s"),
        ("File Processing Rate", "files_per_second", "files/s"),
        ("Pattern Detection Rate", "patterns_per_second", "patterns/s"),
        ("Integration Latency", "integration_latency_p95_ms", "ms"),
        ("Integration Throughput", "integration_throughput_rps", "RPS"),
    ]
    
    for label, metric, unit in contract_metrics:
        if metric in result.validation_criteria:
            data = result.validation_criteria[metric]
            status = "✅" if data["passed"] else "❌"
            logger.info(
                f"  {label}: {status} {data['actual']:.2f} {unit} "
                f"(target: {data['threshold']:.2f})"
            )
    
    return result


async def generate_summary_report(wave2_result, wave25_result):
    """Generate combined summary report."""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = Path(f"validation_results/combined_validation_{timestamp}.md")
    report_file.parent.mkdir(exist_ok=True)
    
    report = []
    report.append("# Pattern Mining Service - Combined Validation Report")
    report.append(f"\n**Timestamp**: {datetime.now().isoformat()}")
    report.append(f"**Service Version**: 1.0.0")
    report.append(f"**Contract Version**: 1.0.0")
    
    # Wave 2 Summary
    report.append("\n## Wave 2: Production Readiness")
    report.append(f"- **Status**: {wave2_result.overall_status.upper()}")
    report.append(f"- **Score**: {wave2_result.performance_score:.1f}/100")
    report.append(f"- **Certification**: {wave2_result.certification_level.upper()}")
    
    # Wave 2.5 Summary
    report.append("\n## Wave 2.5: CCL Contract Compliance")
    report.append(f"- **Status**: {wave25_result.overall_status.upper()}")
    report.append(f"- **Score**: {wave25_result.performance_score:.1f}/100")
    report.append(f"- **Compliance**: {wave25_result.certification_level.upper()}")
    
    # Overall Assessment
    report.append("\n## Overall Assessment")
    
    production_ready = wave2_result.certification_level == "production"
    contract_compliant = all(
        wave25_result.validation_criteria.get(c, {}).get("passed", False)
        for c in ["processing_time_seconds", "files_per_second", 
                  "patterns_per_second", "integration_latency_p95_ms",
                  "integration_throughput_rps"]
    )
    
    if production_ready and contract_compliant:
        report.append("### ✅ FULLY VALIDATED")
        report.append("- Production ready (Wave 2)")
        report.append("- CCL contract compliant (Wave 2.5)")
        report.append("- **Ready for deployment and integration**")
    elif production_ready:
        report.append("### ⚠️ PRODUCTION READY - CONTRACT OPTIMIZATION NEEDED")
        report.append("- Production validation passed")
        report.append("- Contract compliance needs improvement")
        report.append("- Focus on contract-specific optimizations")
    elif contract_compliant:
        report.append("### ⚠️ CONTRACT COMPLIANT - PRODUCTION OPTIMIZATION NEEDED")
        report.append("- Contract requirements met")
        report.append("- Production readiness needs improvement")
        report.append("- Focus on general performance optimizations")
    else:
        report.append("### ❌ OPTIMIZATION REQUIRED")
        report.append("- Both production and contract validations need improvement")
        report.append("- Comprehensive optimization required")
    
    # Combined recommendations
    all_recommendations = set()
    if wave2_result.recommendations:
        all_recommendations.update(wave2_result.recommendations)
    if wave25_result.recommendations:
        all_recommendations.update(wave25_result.recommendations)
    
    if all_recommendations:
        report.append("\n## Combined Recommendations")
        for i, rec in enumerate(sorted(all_recommendations), 1):
            report.append(f"{i}. {rec}")
    
    # Next steps
    report.append("\n## Next Steps")
    if production_ready and contract_compliant:
        report.append("1. Proceed to Wave 3: Context Engineering Alignment")
        report.append("2. Deploy to staging environment")
        report.append("3. Set up continuous performance monitoring")
    else:
        report.append("1. Address performance recommendations")
        report.append("2. Re-run failed validations")
        report.append("3. Iterate until all criteria are met")
    
    # Write report
    with open(report_file, 'w') as f:
        f.write("\n".join(report))
    
    logger.info(f"\nCombined report saved to: {report_file}")
    
    return production_ready and contract_compliant


async def main():
    """Run comprehensive performance validation."""
    start_time = datetime.now()
    logger.info("Starting Comprehensive Performance Validation")
    logger.info("Validating both Wave 2 (Production) and Wave 2.5 (Contract)")
    
    # Initialize Redis client
    redis_client = None
    try:
        redis_client = RedisClient()
        await redis_client.ping()
        logger.info("✓ Redis connection established")
    except Exception as e:
        logger.warning(f"✗ Redis not available: {e}")
        logger.info("  Continuing without Redis caching")
    
    try:
        # Run Wave 2 validation
        wave2_result = await run_wave_2_validation(redis_client)
        
        # Run Wave 2.5 validation
        wave25_result = await run_wave_2_5_validation(redis_client)
        
        # Generate combined report
        all_passed = await generate_summary_report(wave2_result, wave25_result)
        
        # Summary
        duration = (datetime.now() - start_time).total_seconds()
        logger.info("\n" + "=" * 80)
        logger.info("VALIDATION COMPLETE")
        logger.info(f"Total Duration: {duration:.2f} seconds")
        
        if all_passed:
            logger.info("✅ ALL VALIDATIONS PASSED")
            logger.info("   Ready for deployment and CCL integration")
        else:
            logger.info("❌ VALIDATION INCOMPLETE")
            logger.info("   See recommendations above for required improvements")
        
        logger.info("=" * 80)
        
        # Exit with appropriate code
        sys.exit(0 if all_passed else 1)
        
    except Exception as e:
        logger.error(f"Validation failed: {e}", exc_info=True)
        sys.exit(2)


if __name__ == "__main__":
    asyncio.run(main())