#!/bin/bash

# Phase 2 Advanced Integration Test Runner
# Orchestrated execution of comprehensive multi-service integration tests
# Supports network partitioning, failure injection, and performance validation

set -euo pipefail

# =============================================================================
# Configuration and Constants
# =============================================================================

# Script metadata
readonly SCRIPT_NAME="$(basename "$0")"
readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"
readonly TIMESTAMP="$(date +%Y%m%d_%H%M%S)"

# Test configuration
readonly DOCKER_COMPOSE_FILE="${PROJECT_ROOT}/docker-compose.advanced-test.yml"
readonly TEST_RESULTS_DIR="${PROJECT_ROOT}/test-results/phase2-${TIMESTAMP}"
readonly LOG_FILE="${TEST_RESULTS_DIR}/test-execution.log"
readonly METRICS_FILE="${TEST_RESULTS_DIR}/test-metrics.json"

# Container and service names
readonly PRIMARY_SERVICE="pattern-mining-primary"
readonly REPLICA_SERVICE="pattern-mining-replica"
readonly POSTGRES_PRIMARY="postgres-primary"
readonly POSTGRES_REPLICA="postgres-replica"
readonly REDIS_PRIMARY="redis-primary"
readonly REDIS_REPLICA="redis-replica"
readonly TEST_RUNNER="test-runner"

# Test categories
readonly BASIC_TESTS="tests/integration/test_advanced_integration.py"
readonly FAILURE_TESTS="tests/integration/test_cascading_failures.py"
readonly RESILIENCE_TESTS="tests/integration/test_service_resilience.py"
readonly CONSISTENCY_TESTS="tests/integration/test_data_consistency.py"
readonly PERFORMANCE_TESTS="tests/integration/test_performance_integration.py"

# Colors for output
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly PURPLE='\033[0;35m'
readonly CYAN='\033[0;36m'
readonly NC='\033[0m' # No Color

# =============================================================================
# Utility Functions
# =============================================================================

log() {
    local level="$1"
    shift
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] [${level}] $*" | tee -a "${LOG_FILE}"
}

log_info() {
    log "INFO" "${BLUE}$*${NC}"
}

log_success() {
    log "SUCCESS" "${GREEN}$*${NC}"
}

log_warning() {
    log "WARNING" "${YELLOW}$*${NC}"
}

log_error() {
    log "ERROR" "${RED}$*${NC}"
}

log_section() {
    echo "" | tee -a "${LOG_FILE}"
    echo "=============================================================================" | tee -a "${LOG_FILE}"
    log "SECTION" "${PURPLE}$*${NC}"
    echo "=============================================================================" | tee -a "${LOG_FILE}"
}

check_dependencies() {
    log_info "Checking dependencies..."
    
    local deps=("docker" "docker-compose" "curl" "jq" "timeout")
    for dep in "${deps[@]}"; do
        if ! command -v "$dep" &> /dev/null; then
            log_error "Required dependency '$dep' is not installed"
            return 1
        fi
    done
    
    if [[ ! -f "$DOCKER_COMPOSE_FILE" ]]; then
        log_error "Docker Compose file not found: $DOCKER_COMPOSE_FILE"
        return 1
    fi
    
    log_success "All dependencies satisfied"
}

setup_test_environment() {
    log_info "Setting up test environment..."
    
    # Create test results directory
    mkdir -p "$TEST_RESULTS_DIR"
    
    # Initialize log file
    echo "Phase 2 Advanced Integration Test Execution Log" > "$LOG_FILE"
    echo "Started at: $(date)" >> "$LOG_FILE"
    echo "Project Root: $PROJECT_ROOT" >> "$LOG_FILE"
    echo "" >> "$LOG_FILE"
    
    # Pull latest Docker images
    log_info "Pulling Docker images..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" pull --quiet
    
    log_success "Test environment setup complete"
}

wait_for_service() {
    local service_name="$1"
    local health_url="$2"
    local max_attempts="${3:-30}"
    local wait_interval="${4:-5}"
    
    log_info "Waiting for $service_name to be ready..."
    
    for ((i=1; i<=max_attempts; i++)); do
        if curl -sf "$health_url" >/dev/null 2>&1; then
            log_success "$service_name is ready"
            return 0
        fi
        
        if [[ $i -eq $max_attempts ]]; then
            log_error "$service_name failed to become ready after $((max_attempts * wait_interval)) seconds"
            return 1
        fi
        
        log_info "Attempt $i/$max_attempts: $service_name not ready, waiting ${wait_interval}s..."
        sleep "$wait_interval"
    done
}

start_infrastructure() {
    log_section "Starting Infrastructure Services"
    
    # Start core infrastructure
    log_info "Starting database and cache services..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" up -d \
        "$POSTGRES_PRIMARY" "$POSTGRES_REPLICA" \
        "$REDIS_PRIMARY" "$REDIS_REPLICA"
    
    # Wait for databases to be ready
    wait_for_service "PostgreSQL Primary" "http://localhost:5432" 20 3
    wait_for_service "PostgreSQL Replica" "http://localhost:5433" 20 3
    wait_for_service "Redis Primary" "http://localhost:6379" 15 2
    wait_for_service "Redis Replica" "http://localhost:6380" 15 2
    
    # Start external service simulators
    log_info "Starting external service simulators..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" up -d \
        gemini-simulator bigquery-simulator toxiproxy
    
    # Wait for simulators
    wait_for_service "Gemini Simulator" "http://localhost:8080/health" 15 2
    wait_for_service "BigQuery Simulator" "http://localhost:8081/health" 15 2
    wait_for_service "Toxiproxy" "http://localhost:8474/version" 15 2
    
    # Start monitoring stack
    log_info "Starting monitoring services..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" up -d prometheus grafana
    
    wait_for_service "Prometheus" "http://localhost:9090/-/ready" 20 3
    wait_for_service "Grafana" "http://localhost:3000/api/health" 20 3
    
    log_success "Infrastructure services started successfully"
}

start_application_services() {
    log_section "Starting Application Services"
    
    # Start primary and replica services
    log_info "Starting Pattern Mining services..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" up -d \
        "$PRIMARY_SERVICE" "$REPLICA_SERVICE"
    
    # Wait for application services
    wait_for_service "Pattern Mining Primary" "http://localhost:8001/health" 30 5
    wait_for_service "Pattern Mining Replica" "http://localhost:8002/health" 30 5
    
    # Verify services are responding
    log_info "Verifying application service connectivity..."
    
    local primary_status
    primary_status=$(curl -s "http://localhost:8001/health" | jq -r '.status' 2>/dev/null || echo "error")
    
    local replica_status
    replica_status=$(curl -s "http://localhost:8002/health" | jq -r '.status' 2>/dev/null || echo "error")
    
    if [[ "$primary_status" == "healthy" && "$replica_status" == "healthy" ]]; then
        log_success "Application services are healthy and ready"
    else
        log_error "Application services health check failed (Primary: $primary_status, Replica: $replica_status)"
        return 1
    fi
}

run_test_suite() {
    local test_category="$1"
    local test_file="$2"
    local test_name="$3"
    local additional_args="${4:-}"
    
    log_section "Running $test_name"
    
    local test_start_time
    test_start_time=$(date +%s)
    
    local test_output_file="${TEST_RESULTS_DIR}/${test_category}-results.xml"
    local test_html_file="${TEST_RESULTS_DIR}/${test_category}-report.html"
    local test_coverage_file="${TEST_RESULTS_DIR}/${test_category}-coverage.xml"
    
    # Run the test suite
    log_info "Executing $test_name tests..."
    
    local test_cmd="pytest $test_file -v --tb=short \
        --junitxml=$test_output_file \
        --html=$test_html_file --self-contained-html \
        --cov=src/pattern_mining --cov-report=xml:$test_coverage_file \
        --cov-report=term-missing \
        --durations=10 \
        $additional_args"
    
    if docker-compose -f "$DOCKER_COMPOSE_FILE" run --rm \
        -v "${TEST_RESULTS_DIR}:/app/test-results" \
        "$TEST_RUNNER" bash -c "$test_cmd"; then
        
        local test_end_time
        test_end_time=$(date +%s)
        local test_duration=$((test_end_time - test_start_time))
        
        log_success "$test_name completed successfully in ${test_duration}s"
        return 0
    else
        local test_end_time
        test_end_time=$(date +%s)
        local test_duration=$((test_end_time - test_start_time))
        
        log_error "$test_name failed after ${test_duration}s"
        return 1
    fi
}

collect_metrics() {
    log_section "Collecting System Metrics"
    
    log_info "Collecting container metrics..."
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}" \
        > "${TEST_RESULTS_DIR}/container-stats.txt"
    
    log_info "Collecting Prometheus metrics..."
    if curl -s "http://localhost:9090/api/v1/query?query=up" > "${TEST_RESULTS_DIR}/prometheus-metrics.json"; then
        log_success "Prometheus metrics collected"
    else
        log_warning "Failed to collect Prometheus metrics"
    fi
    
    log_info "Collecting service logs..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" logs --no-color "$PRIMARY_SERVICE" \
        > "${TEST_RESULTS_DIR}/primary-service.log" 2>&1
    docker-compose -f "$DOCKER_COMPOSE_FILE" logs --no-color "$REPLICA_SERVICE" \
        > "${TEST_RESULTS_DIR}/replica-service.log" 2>&1
    
    log_success "System metrics collection complete"
}

run_chaos_tests() {
    log_section "Running Chaos Engineering Tests"
    
    # Start chaos testing profile
    log_info "Starting chaos testing infrastructure..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" --profile chaos-testing up -d
    
    # Run failure injection tests
    log_info "Running network partition simulation..."
    run_test_suite "chaos-network" "$FAILURE_TESTS" "Network Partition Tests" \
        "-m 'network_partition'"
    
    log_info "Running service failure simulation..."
    run_test_suite "chaos-service" "$FAILURE_TESTS" "Service Failure Tests" \
        "-m 'service_failure'"
    
    log_info "Running resource exhaustion simulation..."
    run_test_suite "chaos-resource" "$RESILIENCE_TESTS" "Resource Exhaustion Tests" \
        "-m 'resource_exhaustion'"
    
    log_success "Chaos engineering tests completed"
}

run_performance_tests() {
    log_section "Running Performance Tests"
    
    # Start load testing profile
    log_info "Starting load testing infrastructure..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" --profile load-testing up -d
    
    # Run performance test suite
    run_test_suite "performance" "$PERFORMANCE_TESTS" "Performance Integration Tests" \
        "--timeout=1800 -m 'performance'"
    
    # Run extended load tests
    log_info "Running extended load tests..."
    run_test_suite "load-test" "$PERFORMANCE_TESTS" "Load Testing" \
        "--timeout=3600 -m 'load_test'"
    
    log_success "Performance testing completed"
}

generate_summary_report() {
    log_section "Generating Summary Report"
    
    local summary_file="${TEST_RESULTS_DIR}/execution-summary.json"
    local html_summary="${TEST_RESULTS_DIR}/execution-summary.html"
    
    # Collect test results
    local total_tests=0
    local passed_tests=0
    local failed_tests=0
    local test_files=()
    
    # Count results from XML files
    for xml_file in "${TEST_RESULTS_DIR}"/*-results.xml; do
        if [[ -f "$xml_file" ]]; then
            test_files+=("$xml_file")
            local file_tests
            file_tests=$(xmllint --xpath "count(//testcase)" "$xml_file" 2>/dev/null || echo "0")
            local file_failures
            file_failures=$(xmllint --xpath "count(//testcase/failure)" "$xml_file" 2>/dev/null || echo "0")
            
            total_tests=$((total_tests + file_tests))
            failed_tests=$((failed_tests + file_failures))
            passed_tests=$((total_tests - failed_tests))
        fi
    done
    
    # Generate JSON summary
    cat > "$summary_file" << EOF
{
    "execution_summary": {
        "timestamp": "$(date -Iseconds)",
        "total_tests": $total_tests,
        "passed_tests": $passed_tests,
        "failed_tests": $failed_tests,
        "success_rate": $(echo "scale=4; $passed_tests * 100 / $total_tests" | bc -l 2>/dev/null || echo "0"),
        "test_categories": {
            "advanced_integration": "$(ls "${TEST_RESULTS_DIR}"/basic-* 2>/dev/null | wc -l)",
            "cascading_failures": "$(ls "${TEST_RESULTS_DIR}"/failure-* 2>/dev/null | wc -l)",
            "service_resilience": "$(ls "${TEST_RESULTS_DIR}"/resilience-* 2>/dev/null | wc -l)",
            "data_consistency": "$(ls "${TEST_RESULTS_DIR}"/consistency-* 2>/dev/null | wc -l)",
            "performance_integration": "$(ls "${TEST_RESULTS_DIR}"/performance-* 2>/dev/null | wc -l)"
        }
    }
}
EOF
    
    # Generate HTML summary
    cat > "$html_summary" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>Phase 2 Advanced Integration Test Results</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f4f4f4; padding: 20px; border-radius: 5px; }
        .summary { display: flex; gap: 20px; margin: 20px 0; }
        .metric { background: #e8f4f8; padding: 15px; border-radius: 5px; flex: 1; text-align: center; }
        .success { background: #d4edda; color: #155724; }
        .failure { background: #f8d7da; color: #721c24; }
        .results { margin: 20px 0; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background: #f4f4f4; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Phase 2 Advanced Integration Test Results</h1>
        <p>Execution completed at: $(date)</p>
        <p>Results directory: ${TEST_RESULTS_DIR}</p>
    </div>
    
    <div class="summary">
        <div class="metric">
            <h3>Total Tests</h3>
            <div style="font-size: 2em; font-weight: bold;">$total_tests</div>
        </div>
        <div class="metric success">
            <h3>Passed</h3>
            <div style="font-size: 2em; font-weight: bold;">$passed_tests</div>
        </div>
        <div class="metric failure">
            <h3>Failed</h3>
            <div style="font-size: 2em; font-weight: bold;">$failed_tests</div>
        </div>
        <div class="metric">
            <h3>Success Rate</h3>
            <div style="font-size: 2em; font-weight: bold;">$(echo "scale=1; $passed_tests * 100 / $total_tests" | bc -l 2>/dev/null || echo "0")%</div>
        </div>
    </div>
    
    <div class="results">
        <h2>Test Categories</h2>
        <table>
            <tr><th>Category</th><th>Description</th><th>Status</th></tr>
            <tr><td>Advanced Integration</td><td>Complex multi-service orchestration</td><td>$(test -f "${TEST_RESULTS_DIR}/basic-results.xml" && echo "Completed" || echo "Not run")</td></tr>
            <tr><td>Cascading Failures</td><td>Failure propagation testing</td><td>$(test -f "${TEST_RESULTS_DIR}/failure-results.xml" && echo "Completed" || echo "Not run")</td></tr>
            <tr><td>Service Resilience</td><td>Circuit breaker and bulkhead patterns</td><td>$(test -f "${TEST_RESULTS_DIR}/resilience-results.xml" && echo "Completed" || echo "Not run")</td></tr>
            <tr><td>Data Consistency</td><td>Cross-service transaction testing</td><td>$(test -f "${TEST_RESULTS_DIR}/consistency-results.xml" && echo "Completed" || echo "Not run")</td></tr>
            <tr><td>Performance Integration</td><td>Multi-service performance testing</td><td>$(test -f "${TEST_RESULTS_DIR}/performance-results.xml" && echo "Completed" || echo "Not run")</td></tr>
        </table>
    </div>
</body>
</html>
EOF
    
    log_success "Summary report generated: $html_summary"
    
    # Display summary to console
    echo ""
    echo "============================================================================="
    echo "                    PHASE 2 EXECUTION SUMMARY"
    echo "============================================================================="
    echo "Total Tests:    $total_tests"
    echo "Passed Tests:   $passed_tests"
    echo "Failed Tests:   $failed_tests"
    echo "Success Rate:   $(echo "scale=1; $passed_tests * 100 / $total_tests" | bc -l 2>/dev/null || echo "0")%"
    echo "Results Dir:    $TEST_RESULTS_DIR"
    echo "============================================================================="
}

cleanup() {
    log_section "Cleaning Up Test Environment"
    
    log_info "Stopping and removing containers..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" \
        --profile chaos-testing --profile load-testing --profile testing \
        down -v --remove-orphans
    
    log_info "Cleaning up Docker networks..."
    docker network prune -f >/dev/null 2>&1 || true
    
    log_success "Cleanup completed"
}

# =============================================================================
# Main Execution Functions
# =============================================================================

run_basic_integration_tests() {
    log_section "Phase 2.1: Basic Advanced Integration Tests"
    
    run_test_suite "basic" "$BASIC_TESTS" "Advanced Integration Tests"
    run_test_suite "consistency" "$CONSISTENCY_TESTS" "Data Consistency Tests"
}

run_failure_resilience_tests() {
    log_section "Phase 2.2: Failure and Resilience Tests"
    
    run_test_suite "failure" "$FAILURE_TESTS" "Cascading Failure Tests"
    run_test_suite "resilience" "$RESILIENCE_TESTS" "Service Resilience Tests"
}

show_usage() {
    cat << EOF
Usage: $SCRIPT_NAME [OPTIONS] [TEST_CATEGORY]

Phase 2 Advanced Integration Test Runner

OPTIONS:
    -h, --help              Show this help message
    -c, --cleanup-only      Only perform cleanup operations
    -s, --skip-setup        Skip infrastructure setup
    -k, --keep-running      Keep services running after tests
    -m, --metrics-only      Only collect metrics from running services
    -r, --results-dir DIR   Specify custom results directory
    
TEST_CATEGORY:
    all                     Run all test categories (default)
    basic                   Run basic advanced integration tests
    failures                Run cascading failure tests
    resilience              Run service resilience tests
    consistency             Run data consistency tests
    performance             Run performance integration tests
    chaos                   Run chaos engineering tests
    
Examples:
    $SCRIPT_NAME                    # Run all tests
    $SCRIPT_NAME basic              # Run only basic integration tests
    $SCRIPT_NAME --cleanup-only     # Only cleanup existing containers
    $SCRIPT_NAME --keep-running     # Keep services running after tests

EOF
}

main() {
    local test_category="all"
    local cleanup_only=false
    local skip_setup=false
    local keep_running=false
    local metrics_only=false
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_usage
                exit 0
                ;;
            -c|--cleanup-only)
                cleanup_only=true
                shift
                ;;
            -s|--skip-setup)
                skip_setup=true
                shift
                ;;
            -k|--keep-running)
                keep_running=true
                shift
                ;;
            -m|--metrics-only)
                metrics_only=true
                shift
                ;;
            -r|--results-dir)
                readonly TEST_RESULTS_DIR="$2"
                shift 2
                ;;
            all|basic|failures|resilience|consistency|performance|chaos)
                test_category="$1"
                shift
                ;;
            *)
                log_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    # Handle cleanup-only mode
    if [[ "$cleanup_only" == true ]]; then
        cleanup
        exit 0
    fi
    
    # Handle metrics-only mode
    if [[ "$metrics_only" == true ]]; then
        setup_test_environment
        collect_metrics
        exit 0
    fi
    
    # Trap cleanup on exit
    trap cleanup EXIT
    
    # Main execution flow
    log_section "Phase 2 Advanced Integration Test Execution Started"
    
    check_dependencies
    setup_test_environment
    
    if [[ "$skip_setup" != true ]]; then
        start_infrastructure
        start_application_services
    fi
    
    # Execute requested test categories
    case "$test_category" in
        "all")
            run_basic_integration_tests
            run_failure_resilience_tests
            run_test_suite "performance" "$PERFORMANCE_TESTS" "Performance Integration Tests"
            run_chaos_tests
            run_performance_tests
            ;;
        "basic")
            run_basic_integration_tests
            ;;
        "failures")
            run_test_suite "failure" "$FAILURE_TESTS" "Cascading Failure Tests"
            ;;
        "resilience")
            run_test_suite "resilience" "$RESILIENCE_TESTS" "Service Resilience Tests"
            ;;
        "consistency")
            run_test_suite "consistency" "$CONSISTENCY_TESTS" "Data Consistency Tests"
            ;;
        "performance")
            run_performance_tests
            ;;
        "chaos")
            run_chaos_tests
            ;;
        *)
            log_error "Unknown test category: $test_category"
            exit 1
            ;;
    esac
    
    collect_metrics
    generate_summary_report
    
    if [[ "$keep_running" == true ]]; then
        log_info "Keeping services running as requested"
        trap - EXIT  # Remove cleanup trap
        log_info "To manually cleanup later, run: $SCRIPT_NAME --cleanup-only"
    fi
    
    log_section "Phase 2 Advanced Integration Test Execution Completed"
}

# Execute main function if script is run directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi