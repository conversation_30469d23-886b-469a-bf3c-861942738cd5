#!/usr/bin/env python3
"""
Validate CCL contract performance requirements.

Wave 2.5: CCL Contract Compliance Implementation - Phase 4
This script validates the pattern mining service against CCL contract requirements.
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add the parent directory to the path
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.pattern_mining.performance import ContractPerformanceValidator
from src.pattern_mining.cache.redis_client import RedisClient


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def main():
    """Run contract performance validation."""
    logger.info("Starting CCL Contract Performance Validation")
    logger.info("=" * 60)
    
    # Initialize Redis client (optional)
    redis_client = None
    try:
        redis_client = RedisClient()
        await redis_client.ping()
        logger.info("✓ Redis connection established")
    except Exception as e:
        logger.warning(f"✗ Redis not available: {e}")
        logger.info("  Continuing without Redis caching")
    
    # Initialize contract validator
    validator = ContractPerformanceValidator(redis_client=redis_client)
    
    # Run contract compliance validation
    logger.info("\nRunning CCL Contract Compliance Tests...")
    logger.info("-" * 60)
    
    try:
        result = await validator.validate_contract_compliance()
        
        # Display results
        logger.info("\nContract Compliance Results:")
        logger.info(f"Overall Status: {result.overall_status.upper()}")
        logger.info(f"Compliance Score: {result.performance_score:.1f}/100")
        logger.info(f"Certification Level: {result.certification_level.upper()}")
        
        # Display contract-specific results
        logger.info("\nCCL Contract Requirements:")
        contract_criteria = [
            "processing_time_seconds",
            "files_per_second",
            "patterns_per_second",
            "integration_latency_p95_ms",
            "integration_throughput_rps"
        ]
        
        for criterion in contract_criteria:
            if criterion in result.validation_criteria:
                data = result.validation_criteria[criterion]
                status = "✅ PASS" if data["passed"] else "❌ FAIL"
                logger.info(
                    f"  {criterion}: {status} "
                    f"(actual: {data['actual']:.2f}, target: {data['threshold']:.2f})"
                )
        
        # Display recommendations if any
        if result.recommendations:
            logger.info("\nRecommendations:")
            for i, rec in enumerate(result.recommendations, 1):
                logger.info(f"  {i}. {rec}")
        
        # Final compliance status
        logger.info("\n" + "=" * 60)
        contract_passed = all(
            result.validation_criteria.get(c, {}).get("passed", False)
            for c in contract_criteria
        )
        
        if contract_passed:
            logger.info("✅ FULLY COMPLIANT WITH CCL CONTRACT v1.0.0")
            logger.info("   All contract requirements met")
            logger.info("   Ready for CCL service integration")
        else:
            logger.info("❌ NOT COMPLIANT WITH CCL CONTRACT v1.0.0")
            logger.info("   Contract requirements not fully met")
            logger.info("   Optimization required before integration")
        
        logger.info("=" * 60)
        
        # Exit with appropriate code
        sys.exit(0 if contract_passed else 1)
        
    except Exception as e:
        logger.error(f"Validation failed: {e}", exc_info=True)
        sys.exit(2)


if __name__ == "__main__":
    asyncio.run(main())