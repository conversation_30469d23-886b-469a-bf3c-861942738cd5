#!/usr/bin/env python3
"""
Phase 2 Load Testing Execution Script

Comprehensive test runner for Pattern Mining service Phase 2 load testing implementation.
Supports multiple testing profiles, scenarios, and environments with advanced configuration.

Usage:
    python scripts/run_phase2_load_tests.py --profile development --scenarios concurrent_users
    python scripts/run_phase2_load_tests.py --profile production --all-scenarios --duration 4
    python scripts/run_phase2_load_tests.py --endurance --duration 24
"""

import argparse
import asyncio
import json
import logging
import os
import subprocess
import sys
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import yaml

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('phase2_load_tests.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class Phase2LoadTestRunner:
    """Advanced load test runner for Phase 2 testing scenarios."""
    
    def __init__(self):
        self.script_dir = Path(__file__).parent
        self.service_dir = self.script_dir.parent
        self.config_file = self.service_dir / "config" / "load_testing_config.yaml"
        self.results_dir = self.service_dir / "test_results" / "phase2_load_tests"
        self.results_dir.mkdir(parents=True, exist_ok=True)
        
        self.config = self._load_config()
        self.start_time = datetime.now()
        self.test_session_id = self.start_time.strftime("%Y%m%d_%H%M%S")
        
    def _load_config(self) -> Dict:
        """Load load testing configuration from YAML file."""
        try:
            with open(self.config_file, 'r') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            logger.error(f"Configuration file not found: {self.config_file}")
            return {}
        except yaml.YAMLError as e:
            logger.error(f"Error parsing configuration file: {e}")
            return {}
    
    def _validate_environment(self) -> bool:
        """Validate that the testing environment is properly set up."""
        logger.info("Validating testing environment...")
        
        # Check Python dependencies
        required_packages = [
            'pytest', 'psutil', 'memory_profiler', 'tracemalloc', 
            'asyncio', 'httpx', 'faker', 'numpy', 'scipy'
        ]
        
        for package in required_packages:
            try:
                __import__(package)
            except ImportError:
                logger.error(f"Required package not installed: {package}")
                return False
        
        # Check test files exist
        test_files = [
            'tests/performance/test_concurrent_users.py',
            'tests/performance/test_sustained_load.py',
            'tests/performance/test_realistic_workloads.py',
            'tests/performance/test_scalability_limits.py'
        ]
        
        for test_file in test_files:
            if not (self.service_dir / test_file).exists():
                logger.error(f"Test file not found: {test_file}")
                return False
        
        # Check configuration file
        if not self.config:
            logger.error("Configuration file is empty or invalid")
            return False
        
        logger.info("Environment validation passed!")
        return True
    
    def _prepare_environment(self, profile: str) -> bool:
        """Prepare the testing environment for the specified profile."""
        logger.info(f"Preparing environment for profile: {profile}")
        
        # Set environment variables based on profile
        profile_config = self.config.get('testing_profiles', {}).get(profile, {})
        
        os.environ['TESTING_PROFILE'] = profile
        os.environ['TEST_SESSION_ID'] = self.test_session_id
        os.environ['MAX_USERS'] = str(profile_config.get('max_users', 10))
        os.environ['DURATION_MINUTES'] = str(profile_config.get('duration_minutes', 5))
        os.environ['RAMP_UP_SECONDS'] = str(profile_config.get('ramp_up_seconds', 30))
        
        # Create results directory for this session
        session_dir = self.results_dir / f"session_{self.test_session_id}"
        session_dir.mkdir(exist_ok=True)
        os.environ['TEST_RESULTS_DIR'] = str(session_dir)
        
        return True
    
    def _run_pytest_command(self, test_file: str, markers: List[str] = None, 
                           timeout: int = 3600, maxfail: int = 1) -> Tuple[bool, str]:
        """Run a pytest command with specified parameters."""
        
        cmd = [
            'python', '-m', 'pytest', 
            f'tests/performance/{test_file}',
            '-v', '--tb=short',
            f'--maxfail={maxfail}',
            f'--timeout={timeout}',
            f'--junitxml=test_results/{test_file.replace(".py", "")}_results.xml'
        ]
        
        if markers:
            marker_expr = ' and '.join(markers)
            cmd.extend(['-m', marker_expr])
        
        logger.info(f"Running command: {' '.join(cmd)}")
        
        try:
            result = subprocess.run(
                cmd, 
                cwd=self.service_dir,
                capture_output=True, 
                text=True, 
                timeout=timeout + 60  # Add buffer time
            )
            
            return result.returncode == 0, result.stdout + result.stderr
            
        except subprocess.TimeoutExpired:
            logger.error(f"Test {test_file} timed out after {timeout} seconds")
            return False, "Test timed out"
        except Exception as e:
            logger.error(f"Error running test {test_file}: {e}")
            return False, str(e)
    
    async def run_concurrent_users_test(self, profile: str) -> bool:
        """Run concurrent users load testing."""
        logger.info("Starting concurrent users load testing...")
        
        profile_config = self.config.get('testing_profiles', {}).get(profile, {})
        timeout = profile_config.get('duration_minutes', 5) * 60 + 300  # Add 5 min buffer
        
        markers = [f"profile_{profile}"]
        if profile == 'development':
            markers.append("not long_running")
        
        success, output = self._run_pytest_command(
            'test_concurrent_users.py', 
            markers=markers, 
            timeout=timeout,
            maxfail=3
        )
        
        if success:
            logger.info("Concurrent users test completed successfully")
        else:
            logger.error(f"Concurrent users test failed: {output}")
        
        return success
    
    async def run_sustained_load_test(self, profile: str) -> bool:
        """Run sustained load testing with memory leak detection."""
        logger.info("Starting sustained load testing...")
        
        profile_config = self.config.get('testing_profiles', {}).get(profile, {})
        duration_minutes = profile_config.get('duration_minutes', 15)
        timeout = duration_minutes * 60 + 600  # Add 10 min buffer
        
        markers = [f"profile_{profile}"]
        if profile != 'endurance':
            markers.append("not endurance")
        
        success, output = self._run_pytest_command(
            'test_sustained_load.py',
            markers=markers,
            timeout=timeout,
            maxfail=1  # Strict for sustained testing
        )
        
        if success:
            logger.info("Sustained load test completed successfully")
            await self._analyze_memory_results()
        else:
            logger.error(f"Sustained load test failed: {output}")
        
        return success
    
    async def run_realistic_workloads_test(self, profile: str) -> bool:
        """Run realistic workloads testing."""
        logger.info("Starting realistic workloads testing...")
        
        profile_config = self.config.get('testing_profiles', {}).get(profile, {})
        timeout = profile_config.get('duration_minutes', 30) * 60 + 600
        
        markers = [f"profile_{profile}"]
        if profile in ['development', 'staging']:
            markers.append("not enterprise")
        
        success, output = self._run_pytest_command(
            'test_realistic_workloads.py',
            markers=markers,
            timeout=timeout,
            maxfail=2
        )
        
        if success:
            logger.info("Realistic workloads test completed successfully")
            await self._analyze_workload_results()
        else:
            logger.error(f"Realistic workloads test failed: {output}")
        
        return success
    
    async def run_scalability_limits_test(self, profile: str) -> bool:
        """Run scalability limits testing."""
        logger.info("Starting scalability limits testing...")
        
        profile_config = self.config.get('testing_profiles', {}).get(profile, {})
        timeout = profile_config.get('duration_minutes', 60) * 60 + 900  # Add 15 min buffer
        
        markers = [f"profile_{profile}"]
        
        success, output = self._run_pytest_command(
            'test_scalability_limits.py',
            markers=markers,
            timeout=timeout,
            maxfail=1
        )
        
        if success:
            logger.info("Scalability limits test completed successfully")
            await self._analyze_scalability_results()
        else:
            logger.error(f"Scalability limits test failed: {output}")
        
        return success
    
    async def _analyze_memory_results(self):
        """Analyze memory leak detection results."""
        logger.info("Analyzing memory leak detection results...")
        
        results_dir = Path(os.environ.get('TEST_RESULTS_DIR', self.results_dir))
        memory_files = list(results_dir.glob("**/sustained_load/*_memory_analysis.json"))
        
        for memory_file in memory_files:
            try:
                with open(memory_file, 'r') as f:
                    data = json.load(f)
                
                growth_rate = data.get('growth_rate_mb_per_hour', 0)
                potential_leak = data.get('potential_leak_detected', False)
                
                if potential_leak:
                    logger.warning(f"Potential memory leak detected in {memory_file.name}")
                    logger.warning(f"Growth rate: {growth_rate} MB/hour")
                else:
                    logger.info(f"No memory leak detected in {memory_file.name}")
                    
            except Exception as e:
                logger.error(f"Error analyzing memory file {memory_file}: {e}")
    
    async def _analyze_workload_results(self):
        """Analyze realistic workloads results."""
        logger.info("Analyzing realistic workloads results...")
        
        results_dir = Path(os.environ.get('TEST_RESULTS_DIR', self.results_dir))
        workload_files = list(results_dir.glob("**/realistic_workloads/*_performance.json"))
        
        for workload_file in workload_files:
            try:
                with open(workload_file, 'r') as f:
                    data = json.load(f)
                
                workload_name = workload_file.stem.replace('_performance', '')
                sla_compliance = data.get('sla_compliance', 0)
                error_rate = data.get('error_rate', 0)
                
                if sla_compliance < 95:
                    logger.warning(f"SLA compliance below 95% for {workload_name}: {sla_compliance}%")
                if error_rate > 1:
                    logger.warning(f"Error rate above 1% for {workload_name}: {error_rate}%")
                    
                logger.info(f"{workload_name}: SLA {sla_compliance}%, Errors {error_rate}%")
                
            except Exception as e:
                logger.error(f"Error analyzing workload file {workload_file}: {e}")
    
    async def _analyze_scalability_results(self):
        """Analyze scalability limits results."""
        logger.info("Analyzing scalability limits results...")
        
        results_dir = Path(os.environ.get('TEST_RESULTS_DIR', self.results_dir))
        analysis_file = results_dir / "scalability_limits" / "scalability_analysis.json"
        
        if analysis_file.exists():
            try:
                with open(analysis_file, 'r') as f:
                    data = json.load(f)
                
                max_users = data.get('max_concurrent_users', 'N/A')
                max_throughput = data.get('max_throughput_rps', 'N/A')
                efficiency_score = data.get('efficiency_score', 'N/A')
                
                logger.info(f"Maximum concurrent users: {max_users}")
                logger.info(f"Maximum throughput: {max_throughput} req/s")
                logger.info(f"Efficiency score: {efficiency_score}/100")
                
                bottlenecks = data.get('bottlenecks', [])
                if bottlenecks:
                    logger.info("Identified bottlenecks:")
                    for bottleneck in bottlenecks:
                        logger.info(f"  - {bottleneck}")
                
            except Exception as e:
                logger.error(f"Error analyzing scalability results: {e}")
    
    def _generate_session_report(self, results: Dict[str, bool], profile: str, 
                                scenarios: List[str], duration_hours: float):
        """Generate a comprehensive session report."""
        
        end_time = datetime.now()
        total_duration = end_time - self.start_time
        
        report = {
            'session_id': self.test_session_id,
            'profile': profile,
            'scenarios': scenarios,
            'start_time': self.start_time.isoformat(),
            'end_time': end_time.isoformat(),
            'total_duration_seconds': total_duration.total_seconds(),
            'planned_duration_hours': duration_hours,
            'results': results,
            'success_rate': sum(results.values()) / len(results) * 100,
            'environment': {
                'python_version': sys.version,
                'working_directory': str(self.service_dir),
                'config_file': str(self.config_file),
                'results_directory': str(self.results_dir)
            }
        }
        
        # Save session report
        report_file = self.results_dir / f"session_{self.test_session_id}_report.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        # Log summary
        logger.info("=" * 60)
        logger.info("PHASE 2 LOAD TESTING SESSION SUMMARY")
        logger.info("=" * 60)
        logger.info(f"Session ID: {self.test_session_id}")
        logger.info(f"Profile: {profile}")
        logger.info(f"Scenarios: {', '.join(scenarios)}")
        logger.info(f"Duration: {total_duration}")
        logger.info(f"Success Rate: {report['success_rate']:.1f}%")
        logger.info("")
        
        logger.info("Test Results:")
        for scenario, success in results.items():
            status = "✅ PASSED" if success else "❌ FAILED"
            logger.info(f"  {scenario}: {status}")
        
        logger.info("")
        logger.info(f"Detailed results available in: {report_file}")
        logger.info("=" * 60)
        
        return report
    
    async def run_test_scenarios(self, profile: str, scenarios: List[str], 
                               duration_hours: float = None) -> Dict[str, bool]:
        """Run specified test scenarios for the given profile."""
        
        if not self._validate_environment():
            logger.error("Environment validation failed")
            return {}
        
        if not self._prepare_environment(profile):
            logger.error("Environment preparation failed")
            return {}
        
        # Override duration if specified
        if duration_hours is not None:
            duration_minutes = int(duration_hours * 60)
            os.environ['DURATION_MINUTES'] = str(duration_minutes)
            logger.info(f"Duration override: {duration_hours} hours ({duration_minutes} minutes)")
        
        results = {}
        
        logger.info(f"Starting Phase 2 load testing session: {self.test_session_id}")
        logger.info(f"Profile: {profile}")
        logger.info(f"Scenarios: {', '.join(scenarios)}")
        
        # Run each scenario
        if 'concurrent_users' in scenarios:
            results['concurrent_users'] = await self.run_concurrent_users_test(profile)
        
        if 'sustained_load' in scenarios:
            results['sustained_load'] = await self.run_sustained_load_test(profile)
        
        if 'realistic_workloads' in scenarios:
            results['realistic_workloads'] = await self.run_realistic_workloads_test(profile)
        
        if 'scalability_limits' in scenarios:
            results['scalability_limits'] = await self.run_scalability_limits_test(profile)
        
        # Generate session report
        self._generate_session_report(results, profile, scenarios, duration_hours or 0)
        
        return results

def main():
    """Main entry point for the Phase 2 load testing script."""
    
    parser = argparse.ArgumentParser(
        description="Phase 2 Load Testing Execution Script for Pattern Mining Service",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python scripts/run_phase2_load_tests.py --profile development --scenarios concurrent_users
  python scripts/run_phase2_load_tests.py --profile staging --scenarios concurrent_users,realistic_workloads
  python scripts/run_phase2_load_tests.py --profile production --all-scenarios --duration 4
  python scripts/run_phase2_load_tests.py --endurance --duration 24
        """
    )
    
    parser.add_argument(
        '--profile', 
        choices=['development', 'staging', 'production', 'endurance'],
        default='development',
        help='Testing profile to use (default: development)'
    )
    
    parser.add_argument(
        '--scenarios',
        help='Comma-separated list of scenarios to run (concurrent_users,sustained_load,realistic_workloads,scalability_limits)'
    )
    
    parser.add_argument(
        '--all-scenarios',
        action='store_true',
        help='Run all available test scenarios'
    )
    
    parser.add_argument(
        '--endurance',
        action='store_true',
        help='Run endurance testing (equivalent to --profile endurance --all-scenarios)'
    )
    
    parser.add_argument(
        '--duration',
        type=float,
        help='Override test duration in hours'
    )
    
    parser.add_argument(
        '--skip-memory-profiling',
        action='store_true',
        help='Skip memory profiling to reduce resource usage'
    )
    
    args = parser.parse_args()
    
    # Handle endurance mode
    if args.endurance:
        args.profile = 'endurance'
        args.all_scenarios = True
        if not args.duration:
            args.duration = 24  # Default 24 hours for endurance
    
    # Determine scenarios to run
    if args.all_scenarios:
        scenarios = ['concurrent_users', 'sustained_load', 'realistic_workloads', 'scalability_limits']
    elif args.scenarios:
        scenarios = [s.strip() for s in args.scenarios.split(',')]
    else:
        scenarios = ['concurrent_users']  # Default scenario
    
    # Validate scenarios
    valid_scenarios = ['concurrent_users', 'sustained_load', 'realistic_workloads', 'scalability_limits']
    invalid_scenarios = [s for s in scenarios if s not in valid_scenarios]
    if invalid_scenarios:
        logger.error(f"Invalid scenarios: {', '.join(invalid_scenarios)}")
        logger.error(f"Valid scenarios: {', '.join(valid_scenarios)}")
        sys.exit(1)
    
    # Set environment variables for optional features
    if args.skip_memory_profiling:
        os.environ['SKIP_MEMORY_PROFILING'] = 'true'
    
    # Run tests
    runner = Phase2LoadTestRunner()
    
    try:
        results = asyncio.run(
            runner.run_test_scenarios(args.profile, scenarios, args.duration)
        )
        
        # Determine exit code based on results
        if not results:
            logger.error("No tests were executed")
            sys.exit(1)
        
        failed_tests = [test for test, success in results.items() if not success]
        if failed_tests:
            logger.error(f"Failed tests: {', '.join(failed_tests)}")
            sys.exit(1)
        
        logger.info("All tests completed successfully!")
        sys.exit(0)
        
    except KeyboardInterrupt:
        logger.warning("Testing interrupted by user")
        sys.exit(130)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()