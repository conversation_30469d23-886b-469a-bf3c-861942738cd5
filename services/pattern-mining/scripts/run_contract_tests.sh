#!/bin/bash
# Run CCL Contract Compliance Tests
# Wave 2.5: CCL Contract Compliance Implementation - Phase 5

set -e  # Exit on error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo -e "${BLUE}===================================================${NC}"
echo -e "${BLUE}CCL Contract Compliance Test Suite${NC}"
echo -e "${BLUE}Wave 2.5 - Phase 5: Integration Testing${NC}"
echo -e "${BLUE}===================================================${NC}"

# Change to project root
cd "$PROJECT_ROOT"

# Check if virtual environment is activated
if [[ -z "$VIRTUAL_ENV" ]]; then
    echo -e "${YELLOW}Warning: Virtual environment not detected${NC}"
    echo "Activating virtual environment..."
    if [ -f "venv/bin/activate" ]; then
        source venv/bin/activate
    else
        echo -e "${RED}Error: Virtual environment not found${NC}"
        exit 1
    fi
fi

# Install dependencies if needed
echo -e "\n${BLUE}Checking dependencies...${NC}"
pip install -q pytest pytest-asyncio pytest-cov pytest-mock

# Function to run tests with proper reporting
run_test_suite() {
    local test_type=$1
    local test_path=$2
    local extra_args=$3
    
    echo -e "\n${BLUE}Running $test_type tests...${NC}"
    echo -e "${BLUE}---------------------------------------------------${NC}"
    
    if pytest $test_path $extra_args; then
        echo -e "${GREEN}✓ $test_type tests PASSED${NC}"
        return 0
    else
        echo -e "${RED}✗ $test_type tests FAILED${NC}"
        return 1
    fi
}

# Track overall results
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Run contract validation tests
if run_test_suite "Contract Validation" "tests/contract/test_contract_validation.py" "-v -m contract"; then
    ((PASSED_TESTS++))
else
    ((FAILED_TESTS++))
fi
((TOTAL_TESTS++))

# Run contract integration tests
if run_test_suite "Contract Integration" "tests/contract/test_contract_integration.py" "-v -m 'contract and integration'"; then
    ((PASSED_TESTS++))
else
    ((FAILED_TESTS++))
fi
((TOTAL_TESTS++))

# Run contract performance tests
if run_test_suite "Contract Performance" "tests/contract/test_contract_performance.py" "-v -m 'contract and performance' --tb=short"; then
    ((PASSED_TESTS++))
else
    ((FAILED_TESTS++))
fi
((TOTAL_TESTS++))

# Run all contract tests with coverage
echo -e "\n${BLUE}Running all contract tests with coverage...${NC}"
echo -e "${BLUE}---------------------------------------------------${NC}"

pytest tests/contract/ -m contract --cov=pattern_mining.contracts --cov=pattern_mining.ast_processing --cov=pattern_mining.api.v1 --cov-report=term-missing --cov-report=html

# Run performance validation scripts
echo -e "\n${BLUE}Running performance validation scripts...${NC}"
echo -e "${BLUE}---------------------------------------------------${NC}"

# Contract performance validation
echo -e "\n${YELLOW}Contract Performance Validation:${NC}"
if python scripts/validate_contract_performance.py; then
    echo -e "${GREEN}✓ Contract performance validation PASSED${NC}"
    ((PASSED_TESTS++))
else
    echo -e "${RED}✗ Contract performance validation FAILED${NC}"
    ((FAILED_TESTS++))
fi
((TOTAL_TESTS++))

# Comprehensive validation (Wave 2 + Wave 2.5)
echo -e "\n${YELLOW}Comprehensive Performance Validation:${NC}"
if python scripts/validate_all_performance.py; then
    echo -e "${GREEN}✓ Comprehensive validation PASSED${NC}"
    ((PASSED_TESTS++))
else
    echo -e "${RED}✗ Comprehensive validation FAILED${NC}"
    ((FAILED_TESTS++))
fi
((TOTAL_TESTS++))

# Generate test report
echo -e "\n${BLUE}===================================================${NC}"
echo -e "${BLUE}Test Summary Report${NC}"
echo -e "${BLUE}===================================================${NC}"

TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')
REPORT_FILE="validation_results/contract_test_report_$(date '+%Y%m%d_%H%M%S').txt"
mkdir -p validation_results

# Write report
{
    echo "CCL Contract Compliance Test Report"
    echo "=================================="
    echo "Timestamp: $TIMESTAMP"
    echo ""
    echo "Test Results:"
    echo "- Total Test Suites: $TOTAL_TESTS"
    echo "- Passed: $PASSED_TESTS"
    echo "- Failed: $FAILED_TESTS"
    echo ""
    
    if [ $FAILED_TESTS -eq 0 ]; then
        echo "Status: ALL TESTS PASSED ✓"
        echo ""
        echo "The Pattern Mining Service is:"
        echo "- 100% CCL Contract Compliant"
        echo "- Meeting all performance requirements"
        echo "- Ready for integration"
    else
        echo "Status: TESTS FAILED ✗"
        echo ""
        echo "Issues found:"
        echo "- $FAILED_TESTS test suites failed"
        echo "- Review test output above for details"
        echo "- Fix issues before proceeding"
    fi
    
    echo ""
    echo "Coverage Report: htmlcov/index.html"
    echo ""
    echo "Next Steps:"
    if [ $FAILED_TESTS -eq 0 ]; then
        echo "1. Review coverage report for any gaps"
        echo "2. Deploy to staging environment"
        echo "3. Run integration tests with other CCL services"
        echo "4. Proceed to Wave 3: Context Engineering Alignment"
    else
        echo "1. Review failed tests above"
        echo "2. Fix identified issues"
        echo "3. Re-run this test suite"
        echo "4. Ensure all tests pass before proceeding"
    fi
} | tee "$REPORT_FILE"

echo -e "\n${BLUE}Report saved to: $REPORT_FILE${NC}"

# Display final status
echo -e "\n${BLUE}===================================================${NC}"
if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "${GREEN}✓ CCL CONTRACT COMPLIANCE: VALIDATED${NC}"
    echo -e "${GREEN}✓ Ready for CCL service integration${NC}"
    exit 0
else
    echo -e "${RED}✗ CCL CONTRACT COMPLIANCE: FAILED${NC}"
    echo -e "${RED}✗ $FAILED_TESTS test suites need attention${NC}"
    exit 1
fi