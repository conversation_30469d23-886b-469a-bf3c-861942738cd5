#!/bin/bash
# Quick Load Testing Script for Phase 2 Implementation
# Simple wrapper for common load testing scenarios

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
PROFILE="development"
SCENARIOS=""
DURATION=""
SKIP_MEMORY="false"
VERBOSE="false"

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SERVICE_DIR="$(dirname "$SCRIPT_DIR")"

# Help function
show_help() {
    cat << EOF
Phase 2 Load Testing Quick Script

Usage: $0 [OPTIONS]

OPTIONS:
    -p, --profile PROFILE       Testing profile (development|staging|production|endurance)
    -s, --scenarios SCENARIOS   Comma-separated scenarios (concurrent_users,sustained_load,realistic_workloads,scalability_limits)
    -a, --all-scenarios         Run all available scenarios
    -d, --duration HOURS        Test duration in hours
    -e, --endurance             Run 24-hour endurance testing
    -m, --skip-memory           Skip memory profiling
    -v, --verbose               Verbose output
    -h, --help                  Show this help

EXAMPLES:
    # Quick development test
    $0 -p development -s concurrent_users

    # Staging validation
    $0 -p staging -s concurrent_users,realistic_workloads

    # Production readiness test
    $0 -p production --all-scenarios -d 4

    # Full endurance test
    $0 --endurance

PRESETS:
    --quick         Development profile, concurrent_users only (5 minutes)
    --validate      Staging profile, concurrent_users + realistic_workloads (30 minutes)
    --production    Production profile, all scenarios (4 hours)
    --endurance     24-hour endurance testing with all scenarios
EOF
}

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check dependencies
check_dependencies() {
    log_info "Checking dependencies..."
    
    # Check Python
    if ! command -v python3 &> /dev/null; then
        log_error "Python 3 is required but not installed"
        exit 1
    fi
    
    # Check pytest
    if ! python3 -c "import pytest" 2>/dev/null; then
        log_error "pytest is required but not installed"
        exit 1
    fi
    
    # Check service directory
    if [ ! -d "$SERVICE_DIR/tests/performance" ]; then
        log_error "Performance tests directory not found: $SERVICE_DIR/tests/performance"
        exit 1
    fi
    
    # Check configuration file
    if [ ! -f "$SERVICE_DIR/config/load_testing_config.yaml" ]; then
        log_error "Load testing configuration not found: $SERVICE_DIR/config/load_testing_config.yaml"
        exit 1
    fi
    
    log_success "Dependencies check passed"
}

# Parse arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -p|--profile)
                PROFILE="$2"
                shift 2
                ;;
            -s|--scenarios)
                SCENARIOS="$2"
                shift 2
                ;;
            -a|--all-scenarios)
                SCENARIOS="concurrent_users,sustained_load,realistic_workloads,scalability_limits"
                shift
                ;;
            -d|--duration)
                DURATION="$2"
                shift 2
                ;;
            -e|--endurance)
                PROFILE="endurance"
                SCENARIOS="concurrent_users,sustained_load,realistic_workloads,scalability_limits"
                DURATION="24"
                shift
                ;;
            -m|--skip-memory)
                SKIP_MEMORY="true"
                shift
                ;;
            -v|--verbose)
                VERBOSE="true"
                shift
                ;;
            --quick)
                PROFILE="development"
                SCENARIOS="concurrent_users"
                shift
                ;;
            --validate)
                PROFILE="staging"
                SCENARIOS="concurrent_users,realistic_workloads"
                shift
                ;;
            --production)
                PROFILE="production"
                SCENARIOS="concurrent_users,sustained_load,realistic_workloads,scalability_limits"
                DURATION="4"
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # Set default scenarios if not specified
    if [ -z "$SCENARIOS" ]; then
        SCENARIOS="concurrent_users"
    fi
}

# Validate profile
validate_profile() {
    case $PROFILE in
        development|staging|production|endurance)
            log_info "Using profile: $PROFILE"
            ;;
        *)
            log_error "Invalid profile: $PROFILE"
            log_error "Valid profiles: development, staging, production, endurance"
            exit 1
            ;;
    esac
}

# Start infrastructure
start_infrastructure() {
    log_info "Starting test infrastructure..."
    
    cd "$SERVICE_DIR"
    
    # Check if docker-compose is available
    if command -v docker-compose &> /dev/null; then
        # Start PostgreSQL and Redis
        docker-compose up -d postgres redis || {
            log_warning "Failed to start Docker infrastructure, proceeding without it"
            return 0
        }
        
        # Wait for services
        log_info "Waiting for database to be ready..."
        timeout 60 bash -c 'until docker-compose exec -T postgres pg_isready 2>/dev/null; do sleep 2; done' || {
            log_warning "Database readiness check timed out"
        }
        
        log_info "Waiting for Redis to be ready..."
        timeout 30 bash -c 'until docker-compose exec -T redis redis-cli ping 2>/dev/null | grep -q PONG; do sleep 2; done' || {
            log_warning "Redis readiness check timed out"
        }
        
        log_success "Infrastructure started successfully"
    else
        log_warning "Docker Compose not available, skipping infrastructure setup"
    fi
}

# Stop infrastructure
stop_infrastructure() {
    log_info "Stopping test infrastructure..."
    
    cd "$SERVICE_DIR"
    
    if command -v docker-compose &> /dev/null; then
        docker-compose down --remove-orphans &>/dev/null || true
        log_success "Infrastructure stopped"
    fi
}

# Run specific test scenario
run_scenario() {
    local scenario=$1
    local test_file=""
    local timeout=3600
    local markers=""
    
    case $scenario in
        concurrent_users)
            test_file="test_concurrent_users.py"
            timeout=1800
            ;;
        sustained_load)
            test_file="test_sustained_load.py"
            timeout=7200
            ;;
        realistic_workloads)
            test_file="test_realistic_workloads.py"
            timeout=5400
            ;;
        scalability_limits)
            test_file="test_scalability_limits.py"
            timeout=10800
            ;;
        *)
            log_error "Unknown scenario: $scenario"
            return 1
            ;;
    esac
    
    # Adjust timeout based on profile
    case $PROFILE in
        production)
            timeout=$((timeout * 2))
            ;;
        endurance)
            timeout=$((timeout * 4))
            ;;
    esac
    
    # Build pytest command
    local pytest_cmd=(
        python3 -m pytest
        "tests/performance/$test_file"
        -v
        --tb=short
        --timeout=$timeout
        --junitxml="test_results/${scenario}_results.xml"
    )
    
    # Add markers
    markers="profile_$PROFILE"
    if [ "$PROFILE" = "development" ]; then
        markers="$markers and not long_running"
    fi
    if [ "$PROFILE" != "endurance" ] && [ "$scenario" = "sustained_load" ]; then
        markers="$markers and not endurance"
    fi
    if [[ "$PROFILE" =~ ^(development|staging)$ ]] && [ "$scenario" = "realistic_workloads" ]; then
        markers="$markers and not enterprise"
    fi
    
    pytest_cmd+=(-m "$markers")
    
    log_info "Running $scenario test..."
    log_info "Command: ${pytest_cmd[*]}"
    
    # Set environment variables
    export TESTING_PROFILE="$PROFILE"
    export SKIP_MEMORY_PROFILING="$SKIP_MEMORY"
    if [ -n "$DURATION" ]; then
        export DURATION_OVERRIDE_HOURS="$DURATION"
    fi
    
    # Run the test
    if [ "$VERBOSE" = "true" ]; then
        "${pytest_cmd[@]}"
    else
        "${pytest_cmd[@]}" 2>&1 | grep -E "(PASSED|FAILED|ERROR|::)"
    fi
    
    local exit_code=${PIPESTATUS[0]}
    
    if [ $exit_code -eq 0 ]; then
        log_success "$scenario test completed successfully"
        return 0
    else
        log_error "$scenario test failed"
        return 1
    fi
}

# Main execution
main() {
    local start_time=$(date +%s)
    local failed_scenarios=()
    local successful_scenarios=()
    
    log_info "Starting Phase 2 Load Testing"
    log_info "Timestamp: $(date)"
    
    # Parse arguments
    parse_args "$@"
    
    # Validate inputs
    validate_profile
    
    # Check dependencies
    check_dependencies
    
    # Setup cleanup trap
    trap stop_infrastructure EXIT
    
    # Start infrastructure
    start_infrastructure
    
    # Create results directory
    mkdir -p "$SERVICE_DIR/test_results"
    cd "$SERVICE_DIR"
    
    # Display test configuration
    log_info "Test Configuration:"
    log_info "  Profile: $PROFILE"
    log_info "  Scenarios: $SCENARIOS"
    if [ -n "$DURATION" ]; then
        log_info "  Duration: $DURATION hours"
    fi
    log_info "  Skip Memory Profiling: $SKIP_MEMORY"
    log_info ""
    
    # Run scenarios
    IFS=',' read -ra SCENARIO_ARRAY <<< "$SCENARIOS"
    for scenario in "${SCENARIO_ARRAY[@]}"; do
        scenario=$(echo "$scenario" | xargs)  # Trim whitespace
        
        if run_scenario "$scenario"; then
            successful_scenarios+=("$scenario")
        else
            failed_scenarios+=("$scenario")
        fi
        
        log_info ""
    done
    
    # Generate summary
    local end_time=$(date +%s)
    local total_time=$((end_time - start_time))
    local hours=$((total_time / 3600))
    local minutes=$(((total_time % 3600) / 60))
    local seconds=$((total_time % 60))
    
    log_info "============================================"
    log_info "PHASE 2 LOAD TESTING SUMMARY"
    log_info "============================================"
    log_info "Profile: $PROFILE"
    log_info "Total Duration: ${hours}h ${minutes}m ${seconds}s"
    log_info ""
    
    if [ ${#successful_scenarios[@]} -gt 0 ]; then
        log_success "Successful Scenarios (${#successful_scenarios[@]}):"
        for scenario in "${successful_scenarios[@]}"; do
            log_success "  ✅ $scenario"
        done
    fi
    
    if [ ${#failed_scenarios[@]} -gt 0 ]; then
        log_error "Failed Scenarios (${#failed_scenarios[@]}):"
        for scenario in "${failed_scenarios[@]}"; do
            log_error "  ❌ $scenario"
        done
    fi
    
    log_info ""
    log_info "Results available in: $SERVICE_DIR/test_results/"
    log_info "============================================"
    
    # Exit with appropriate code
    if [ ${#failed_scenarios[@]} -eq 0 ]; then
        log_success "All tests completed successfully!"
        exit 0
    else
        log_error "Some tests failed. Check the logs for details."
        exit 1
    fi
}

# Execute main function with all arguments
main "$@"