#!/bin/bash
set -euo pipefail

# Pattern Mining Service Production Deployment Script
# Author: Episteme CCL Team
# Version: 2.0.0
# Date: July 2025

# Color output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ID="${PROJECT_ID:-vibe-match-463114}"
SERVICE_NAME="pattern-mining"
REGION="${REGION:-us-central1}"
IMAGE_REGISTRY="gcr.io"
IMAGE_NAME="${IMAGE_REGISTRY}/${PROJECT_ID}/${SERVICE_NAME}"
ENVIRONMENT="production"

# Service Account Configuration
SERVICE_ACCOUNT_KEY="${SERVICE_ACCOUNT_KEY:-vibe-match-463114-dbda8d8a6cb9.json}"
SERVICE_ACCOUNT_KEY_PATH="${SERVICE_ACCOUNT_KEY_PATH:-$(pwd)/../../.secrets}" # Path to secrets directory

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check prerequisites
check_prerequisites() {
    print_info "Checking prerequisites..."
    
    # Check if gcloud is installed
    if ! command -v gcloud &> /dev/null; then
        print_error "gcloud CLI not found. Please install Google Cloud SDK."
        exit 1
    fi
    
    # Check if service account key exists
    if [ ! -f "${SERVICE_ACCOUNT_KEY_PATH}/${SERVICE_ACCOUNT_KEY}" ]; then
        print_error "Service account key not found: ${SERVICE_ACCOUNT_KEY_PATH}/${SERVICE_ACCOUNT_KEY}"
        print_info "Please ensure the service account key file exists in the project root."
        exit 1
    fi
    
    # Activate service account
    print_info "Activating service account..."
    gcloud auth activate-service-account --key-file="${SERVICE_ACCOUNT_KEY_PATH}/${SERVICE_ACCOUNT_KEY}"
    
    # Check if docker is installed
    if ! command -v docker &> /dev/null; then
        print_error "Docker not found. Please install Docker."
        exit 1
    fi
    
    # Check if authenticated with gcloud
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" &> /dev/null; then
        print_error "Not authenticated with gcloud. Please run 'gcloud auth login'"
        exit 1
    fi
    
    # Check if project is set
    CURRENT_PROJECT=$(gcloud config get-value project 2>/dev/null)
    if [ -z "$CURRENT_PROJECT" ]; then
        print_error "No GCP project set. Please run 'gcloud config set project PROJECT_ID'"
        exit 1
    fi
    
    print_info "Current GCP project: $CURRENT_PROJECT"
    
    # Verify it's the correct project
    if [ "$CURRENT_PROJECT" != "$PROJECT_ID" ]; then
        print_warning "Current project ($CURRENT_PROJECT) differs from expected ($PROJECT_ID)"
        read -p "Continue with $CURRENT_PROJECT? (y/N) " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
        PROJECT_ID=$CURRENT_PROJECT
    fi
    
    print_success "Prerequisites check passed"
}

# Function to validate secrets
validate_secrets() {
    print_info "Validating required secrets..."
    
    REQUIRED_SECRETS=(
        "gemini-api-key"
        "jwt-secret"
        "database-password"
        "redis-password"
    )
    
    MISSING_SECRETS=()
    
    for secret in "${REQUIRED_SECRETS[@]}"; do
        if ! gcloud secrets describe "$secret" --project="$PROJECT_ID" &> /dev/null; then
            MISSING_SECRETS+=("$secret")
        fi
    done
    
    if [ ${#MISSING_SECRETS[@]} -ne 0 ]; then
        print_error "Missing required secrets: ${MISSING_SECRETS[*]}"
        print_info "Create missing secrets with: gcloud secrets create SECRET_NAME --data-file=-"
        exit 1
    fi
    
    print_success "All required secrets found"
}

# Function to validate service account
validate_service_account() {
    print_info "Validating service account..."
    
    SERVICE_ACCOUNT="${SERVICE_NAME}-sa@${PROJECT_ID}.iam.gserviceaccount.com"
    
    if ! gcloud iam service-accounts describe "$SERVICE_ACCOUNT" --project="$PROJECT_ID" &> /dev/null; then
        print_warning "Service account $SERVICE_ACCOUNT not found. Creating..."
        
        gcloud iam service-accounts create "${SERVICE_NAME}-sa" \
            --display-name="Pattern Mining Service Account" \
            --project="$PROJECT_ID"
        
        # Grant necessary permissions
        ROLES=(
            "roles/bigquery.dataEditor"
            "roles/bigquery.jobUser"
            "roles/storage.objectViewer"
            "roles/secretmanager.secretAccessor"
            "roles/monitoring.metricWriter"
            "roles/cloudtrace.agent"
            "roles/logging.logWriter"
        )
        
        for role in "${ROLES[@]}"; do
            gcloud projects add-iam-policy-binding "$PROJECT_ID" \
                --member="serviceAccount:$SERVICE_ACCOUNT" \
                --role="$role" \
                --quiet
        done
        
        print_success "Service account created and configured"
    else
        print_success "Service account found"
    fi
}

# Function to validate VPC connector
validate_vpc_connector() {
    print_info "Validating VPC connector..."
    
    VPC_CONNECTOR="${SERVICE_NAME}-connector"
    
    if ! gcloud compute networks vpc-access connectors describe "$VPC_CONNECTOR" \
        --region="$REGION" --project="$PROJECT_ID" &> /dev/null; then
        print_warning "VPC connector not found. Please create it manually or skip VPC configuration."
        read -p "Continue without VPC connector? (y/N) " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
        VPC_CONNECTOR=""
    else
        print_success "VPC connector found"
    fi
}

# Function to build Docker image
build_docker_image() {
    print_info "Building Docker image..."
    
    cd "$(dirname "$0")/.."
    
    # Build production image
    docker build \
        --target production \
        --build-arg PYTHON_VERSION=3.13 \
        -t "${IMAGE_NAME}:latest" \
        -t "${IMAGE_NAME}:${ENVIRONMENT}" \
        -t "${IMAGE_NAME}:$(git rev-parse --short HEAD)" \
        -f Dockerfile \
        .
    
    print_success "Docker image built successfully"
}

# Function to push Docker image
push_docker_image() {
    print_info "Pushing Docker image to registry..."
    
    # Configure docker for GCR
    gcloud auth configure-docker --quiet
    
    # Push all tags
    docker push "${IMAGE_NAME}:latest"
    docker push "${IMAGE_NAME}:${ENVIRONMENT}"
    docker push "${IMAGE_NAME}:$(git rev-parse --short HEAD)"
    
    print_success "Docker image pushed successfully"
}

# Function to deploy to Cloud Run
deploy_to_cloud_run() {
    print_info "Deploying to Cloud Run..."
    
    DEPLOY_ARGS=(
        "run" "deploy" "${SERVICE_NAME}"
        "--image=${IMAGE_NAME}:$(git rev-parse --short HEAD)"
        "--platform=managed"
        "--region=${REGION}"
        "--project=${PROJECT_ID}"
        "--service-account=${SERVICE_NAME}-sa@${PROJECT_ID}.iam.gserviceaccount.com"
        "--max-instances=50"
        "--min-instances=2"
        "--memory=4Gi"
        "--cpu=2"
        "--timeout=300"
        "--concurrency=100"
        "--port=8000"
        "--allow-unauthenticated"
    )
    
    # Environment variables
    ENV_VARS=(
        "ENVIRONMENT=${ENVIRONMENT}"
        "LOG_LEVEL=INFO"
        "WORKERS=4"
        "MAX_WORKERS=8"
        "WORKER_CONNECTIONS=1000"
        "PRELOAD_MODELS=true"
        "PROMETHEUS_ENABLED=true"
        "TRACING_ENABLED=true"
        "GPU_ENABLED=false"
    )
    
    for env_var in "${ENV_VARS[@]}"; do
        DEPLOY_ARGS+=("--set-env-vars=${env_var}")
    done
    
    # Secrets
    SECRETS=(
        "GEMINI_API_KEY=gemini-api-key:latest"
        "JWT_SECRET=jwt-secret:latest"
        "DATABASE_PASSWORD=database-password:latest"
        "REDIS_PASSWORD=redis-password:latest"
    )
    
    for secret in "${SECRETS[@]}"; do
        DEPLOY_ARGS+=("--set-secrets=${secret}")
    done
    
    # VPC connector (if available)
    if [ -n "$VPC_CONNECTOR" ]; then
        DEPLOY_ARGS+=("--vpc-connector=${VPC_CONNECTOR}")
    fi
    
    # Deploy
    gcloud "${DEPLOY_ARGS[@]}"
    
    print_success "Service deployed successfully"
}

# Function to run smoke tests
run_smoke_tests() {
    print_info "Running smoke tests..."
    
    # Get service URL
    SERVICE_URL=$(gcloud run services describe "$SERVICE_NAME" \
        --region="$REGION" \
        --project="$PROJECT_ID" \
        --format="value(status.url)")
    
    if [ -z "$SERVICE_URL" ]; then
        print_error "Could not get service URL"
        exit 1
    fi
    
    print_info "Service URL: $SERVICE_URL"
    
    # Test health endpoint
    print_info "Testing health endpoint..."
    HEALTH_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" "$SERVICE_URL/health")
    
    if [ "$HEALTH_RESPONSE" != "200" ]; then
        print_error "Health check failed with status: $HEALTH_RESPONSE"
        exit 1
    fi
    
    print_success "Health check passed"
    
    # Test metrics endpoint
    print_info "Testing metrics endpoint..."
    METRICS_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" "$SERVICE_URL/metrics")
    
    if [ "$METRICS_RESPONSE" != "200" ]; then
        print_warning "Metrics endpoint returned status: $METRICS_RESPONSE"
    else
        print_success "Metrics endpoint available"
    fi
}

# Function to configure monitoring
configure_monitoring() {
    print_info "Configuring monitoring and alerting..."
    
    # Create notification channel if not exists
    NOTIFICATION_CHANNEL=$(gcloud alpha monitoring channels list \
        --filter="displayName:Pattern Mining Alerts" \
        --format="value(name)" \
        --project="$PROJECT_ID" | head -n1)
    
    if [ -z "$NOTIFICATION_CHANNEL" ]; then
        print_info "Creating notification channel..."
        # This would require additional configuration for email/slack
        print_warning "Notification channel creation skipped. Configure manually in Cloud Console."
    fi
    
    # Create uptime check
    print_info "Creating uptime check..."
    cat > /tmp/uptime-check.yaml <<EOF
displayName: "Pattern Mining Service Health Check"
monitoredResource:
  type: "uptime_url"
  labels:
    project_id: "${PROJECT_ID}"
    host: "${SERVICE_NAME}-${REGION}.a.run.app"
httpCheck:
  path: "/health"
  port: 443
  requestMethod: GET
  useSsl: true
period: "60s"
timeout: "10s"
selectedRegions:
- USA
- EUROPE
- ASIA_PACIFIC
EOF
    
    # Note: gcloud doesn't support creating uptime checks directly
    print_warning "Uptime check configuration generated at /tmp/uptime-check.yaml"
    print_info "Please create uptime check manually in Cloud Console"
    
    print_success "Monitoring configuration completed"
}

# Function to display deployment summary
display_summary() {
    print_info "Deployment Summary:"
    echo "===================="
    echo "Project: $PROJECT_ID"
    echo "Service: $SERVICE_NAME"
    echo "Region: $REGION"
    echo "Environment: $ENVIRONMENT"
    echo "Image: ${IMAGE_NAME}:$(git rev-parse --short HEAD)"
    echo "===================="
    
    SERVICE_URL=$(gcloud run services describe "$SERVICE_NAME" \
        --region="$REGION" \
        --project="$PROJECT_ID" \
        --format="value(status.url)")
    
    print_success "Service deployed at: $SERVICE_URL"
    
    print_info "Next steps:"
    echo "1. Monitor service health: $SERVICE_URL/health"
    echo "2. View metrics: $SERVICE_URL/metrics"
    echo "3. Check logs: gcloud logging read 'resource.type=\"cloud_run_revision\" resource.labels.service_name=\"$SERVICE_NAME\"' --limit 50"
    echo "4. Configure custom domain (optional)"
    echo "5. Set up monitoring alerts in Cloud Console"
}

# Main deployment flow
main() {
    print_info "Starting Pattern Mining Service production deployment..."
    
    # Check if running from correct directory
    if [ ! -f "pyproject.toml" ] || [ ! -d "src/pattern_mining" ]; then
        print_error "Must run from pattern-mining service root directory"
        exit 1
    fi
    
    # Execute deployment steps
    check_prerequisites
    validate_secrets
    validate_service_account
    validate_vpc_connector
    build_docker_image
    push_docker_image
    deploy_to_cloud_run
    run_smoke_tests
    configure_monitoring
    display_summary
    
    print_success "Deployment completed successfully!"
}

# Run main function
main "$@"