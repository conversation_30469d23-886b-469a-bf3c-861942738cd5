#!/bin/bash
set -euo pipefail

# Pattern Mining Service - GCP Resources Setup Script
# Sets up all required GCP resources for production deployment
# Version: 2.0.0

# Color output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
PROJECT_ID="${PROJECT_ID:-vibe-match-463114}"
REGION="${REGION:-us-central1}"
SERVICE_NAME="pattern-mining"

# Service Account Configuration
SERVICE_ACCOUNT_KEY="${SERVICE_ACCOUNT_KEY:-vibe-match-463114-dbda8d8a6cb9.json}"
SERVICE_ACCOUNT_KEY_PATH="${SERVICE_ACCOUNT_KEY_PATH:-$(pwd)/../../..}" # Path to project root

# Print functions
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to generate secure random string
generate_secret() {
    openssl rand -base64 32 | tr -d "=+/" | cut -c1-32
}

# Main setup function
main() {
    print_info "Pattern Mining Service - GCP Resources Setup"
    print_info "============================================"
    
    # Verify gcloud is installed
    if ! command -v gcloud &> /dev/null; then
        print_error "gcloud CLI not found. Please install Google Cloud SDK."
        exit 1
    fi
    
    # Check if service account key exists and activate it
    if [ -f "${SERVICE_ACCOUNT_KEY_PATH}/${SERVICE_ACCOUNT_KEY}" ]; then
        print_info "Activating service account..."
        gcloud auth activate-service-account --key-file="${SERVICE_ACCOUNT_KEY_PATH}/${SERVICE_ACCOUNT_KEY}"
        print_success "Service account activated"
    fi
    
    # Get current project
    CURRENT_PROJECT=$(gcloud config get-value project 2>/dev/null)
    if [ -z "$CURRENT_PROJECT" ]; then
        print_error "No GCP project set. Please run 'gcloud config set project PROJECT_ID'"
        exit 1
    fi
    
    print_info "Current project: $CURRENT_PROJECT"
    read -p "Continue with this project? (y/N) " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
    
    PROJECT_ID=$CURRENT_PROJECT
    
    # Enable required APIs
    print_info "Enabling required APIs..."
    APIS=(
        "run.googleapis.com"
        "containerregistry.googleapis.com"
        "secretmanager.googleapis.com"
        "bigquery.googleapis.com"
        "storage-api.googleapis.com"
        "monitoring.googleapis.com"
        "cloudtrace.googleapis.com"
        "logging.googleapis.com"
        "iamcredentials.googleapis.com"
    )
    
    for api in "${APIS[@]}"; do
        print_info "Enabling $api..."
        gcloud services enable "$api" --project="$PROJECT_ID" --quiet || true
    done
    print_success "APIs enabled"
    
    # Create service account
    print_info "Creating service account..."
    SERVICE_ACCOUNT="${SERVICE_NAME}-sa"
    SERVICE_ACCOUNT_EMAIL="${SERVICE_ACCOUNT}@${PROJECT_ID}.iam.gserviceaccount.com"
    
    if ! gcloud iam service-accounts describe "$SERVICE_ACCOUNT_EMAIL" --project="$PROJECT_ID" &> /dev/null; then
        gcloud iam service-accounts create "$SERVICE_ACCOUNT" \
            --display-name="Pattern Mining Service Account" \
            --description="Service account for Pattern Mining Cloud Run service" \
            --project="$PROJECT_ID"
        print_success "Service account created"
    else
        print_info "Service account already exists"
    fi
    
    # Grant IAM roles
    print_info "Granting IAM roles to service account..."
    ROLES=(
        "roles/bigquery.dataEditor"
        "roles/bigquery.jobUser"
        "roles/storage.objectViewer"
        "roles/secretmanager.secretAccessor"
        "roles/monitoring.metricWriter"
        "roles/cloudtrace.agent"
        "roles/logging.logWriter"
    )
    
    for role in "${ROLES[@]}"; do
        print_info "Granting $role..."
        gcloud projects add-iam-policy-binding "$PROJECT_ID" \
            --member="serviceAccount:$SERVICE_ACCOUNT_EMAIL" \
            --role="$role" \
            --quiet || true
    done
    print_success "IAM roles granted"
    
    # Create secrets
    print_info "Creating secrets in Secret Manager..."
    
    # Function to create or update secret
    create_secret() {
        local secret_name=$1
        local secret_value=$2
        
        if gcloud secrets describe "$secret_name" --project="$PROJECT_ID" &> /dev/null; then
            print_info "Secret $secret_name already exists, updating..."
            echo -n "$secret_value" | gcloud secrets versions add "$secret_name" \
                --data-file=- \
                --project="$PROJECT_ID"
        else
            print_info "Creating secret $secret_name..."
            echo -n "$secret_value" | gcloud secrets create "$secret_name" \
                --data-file=- \
                --replication-policy="automatic" \
                --project="$PROJECT_ID"
        fi
    }
    
    # Generate and create secrets
    print_warning "Generating secure secrets..."
    
    # JWT Secret
    JWT_SECRET=$(generate_secret)
    create_secret "jwt-secret" "$JWT_SECRET"
    
    # Database Password
    DB_PASSWORD=$(generate_secret)
    create_secret "database-password" "$DB_PASSWORD"
    
    # Redis Password
    REDIS_PASSWORD=$(generate_secret)
    create_secret "redis-password" "$REDIS_PASSWORD"
    
    # Gemini API Key (placeholder - needs to be updated with real key)
    print_warning "IMPORTANT: You must update the gemini-api-key secret with your actual API key!"
    create_secret "gemini-api-key" "REPLACE_WITH_ACTUAL_GEMINI_API_KEY"
    
    print_success "Secrets created"
    
    # Create BigQuery dataset
    print_info "Creating BigQuery dataset..."
    if ! bq ls -d --project_id="$PROJECT_ID" | grep -q "pattern_mining"; then
        bq mk -d \
            --location="$REGION" \
            --description="Pattern Mining Service dataset" \
            --project_id="$PROJECT_ID" \
            pattern_mining
        print_success "BigQuery dataset created"
    else
        print_info "BigQuery dataset already exists"
    fi
    
    # Create Cloud Storage bucket
    print_info "Creating Cloud Storage bucket..."
    BUCKET_NAME="${PROJECT_ID}-pattern-mining"
    if ! gsutil ls -p "$PROJECT_ID" | grep -q "gs://${BUCKET_NAME}"; then
        gsutil mb -p "$PROJECT_ID" -l "$REGION" "gs://${BUCKET_NAME}/"
        print_success "Cloud Storage bucket created"
    else
        print_info "Cloud Storage bucket already exists"
    fi
    
    # Configure Container Registry
    print_info "Configuring Container Registry..."
    gcloud auth configure-docker --quiet
    print_success "Container Registry configured"
    
    # Create artifact repository (if using Artifact Registry instead)
    print_info "Creating Artifact Registry repository..."
    if gcloud artifacts repositories describe "$SERVICE_NAME" \
        --location="$REGION" \
        --project="$PROJECT_ID" &> /dev/null; then
        print_info "Artifact Registry repository already exists"
    else
        gcloud artifacts repositories create "$SERVICE_NAME" \
            --repository-format=docker \
            --location="$REGION" \
            --description="Pattern Mining Service Docker images" \
            --project="$PROJECT_ID" || true
        print_success "Artifact Registry repository created"
    fi
    
    # Display summary
    print_info "Setup Summary"
    echo "============================================"
    echo "Project ID: $PROJECT_ID"
    echo "Region: $REGION"
    echo "Service Account: $SERVICE_ACCOUNT_EMAIL"
    echo "BigQuery Dataset: pattern_mining"
    echo "Cloud Storage Bucket: gs://${BUCKET_NAME}/"
    echo ""
    print_success "GCP resources setup completed!"
    echo ""
    print_warning "IMPORTANT NEXT STEPS:"
    echo "1. Update the gemini-api-key secret with your actual Gemini API key:"
    echo "   echo -n 'YOUR_ACTUAL_API_KEY' | gcloud secrets versions add gemini-api-key --data-file=-"
    echo ""
    echo "2. (Optional) Create a VPC connector for private networking:"
    echo "   gcloud compute networks vpc-access connectors create pattern-mining-connector \\"
    echo "     --region=$REGION \\"
    echo "     --subnet=YOUR_SUBNET \\"
    echo "     --subnet-project=$PROJECT_ID"
    echo ""
    echo "3. Save the generated secrets securely:"
    echo "   JWT Secret: $JWT_SECRET"
    echo "   Database Password: $DB_PASSWORD"
    echo "   Redis Password: $REDIS_PASSWORD"
    echo ""
    echo "4. Run the deployment script:"
    echo "   ./scripts/deploy-production.sh"
}

# Run main function
main "$@"