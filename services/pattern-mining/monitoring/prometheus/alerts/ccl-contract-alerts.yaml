groups:
  - name: ccl_contract_compliance
    interval: 30s
    rules:
      # Performance SLA Alerts
      - alert: CCLProcessingBudgetExceeded
        expr: ccl_processing_time_seconds{operation="full_request"} > 30
        for: 5m
        labels:
          severity: critical
          service: pattern-mining
          contract: ccl-v1
        annotations:
          summary: "CCL processing budget exceeded ({{ $value }}s > 30s)"
          description: "Pattern detection request took {{ $value }} seconds, exceeding the 30-second CCL contract budget."
          runbook_url: "https://docs.episteme.com/runbooks/pattern-mining/processing-budget"

      - alert: CCLFileProcessingRateLow
        expr: avg(ccl_file_processing_rate_per_second) < 50
        for: 10m
        labels:
          severity: warning
          service: pattern-mining
          contract: ccl-v1
        annotations:
          summary: "File processing rate below CCL target ({{ $value }} < 50 files/s)"
          description: "Current file processing rate is {{ $value }} files/second, below the CCL contract requirement of 50 files/second."

      - alert: CCLPatternDetectionRateLow
        expr: avg(ccl_pattern_detection_rate_per_second) < 100
        for: 10m
        labels:
          severity: warning
          service: pattern-mining
          contract: ccl-v1
        annotations:
          summary: "Pattern detection rate below CCL target ({{ $value }} < 100 patterns/s)"
          description: "Current pattern detection rate is {{ $value }} patterns/second, below the CCL contract requirement of 100 patterns/second."

      - alert: CCLAPILatencyHigh
        expr: ccl_api_latency_p95_milliseconds > 100
        for: 5m
        labels:
          severity: critical
          service: pattern-mining
          contract: ccl-v1
        annotations:
          summary: "API latency exceeds CCL SLA ({{ $value }}ms > 100ms)"
          description: "95th percentile API latency is {{ $value }}ms, exceeding the CCL contract limit of 100ms."

      - alert: CCLThroughputLow
        expr: ccl_throughput_requests_per_second < 20
        for: 10m
        labels:
          severity: warning
          service: pattern-mining
          contract: ccl-v1
        annotations:
          summary: "Throughput below CCL target ({{ $value }} < 20 RPS)"
          description: "Current throughput is {{ $value }} requests/second, below the CCL contract requirement of 20 RPS."

      # Error Rate Alerts
      - alert: CCLErrorRateHigh
        expr: ccl_error_rate_percentage > 5
        for: 5m
        labels:
          severity: critical
          service: pattern-mining
        annotations:
          summary: "High error rate detected ({{ $value }}%)"
          description: "Error rate is {{ $value }}%, indicating potential service issues."

      - alert: CCLValidationErrorsHigh
        expr: rate(ccl_schema_validation_errors_total[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
          service: pattern-mining
          contract: ccl-v1
        annotations:
          summary: "High rate of schema validation errors"
          description: "Seeing {{ $value }} schema validation errors per second, indicating potential contract compliance issues."

      # Resource Alerts
      - alert: CCLMemoryUsageHigh
        expr: ccl_memory_usage_megabytes{component="total"} > 4000
        for: 5m
        labels:
          severity: warning
          service: pattern-mining
        annotations:
          summary: "High memory usage detected ({{ $value }}MB)"
          description: "Total memory usage is {{ $value }}MB, approaching resource limits."

      - alert: CCLCacheHitRateLow
        expr: ccl_cache_hit_rate_percentage < 50
        for: 15m
        labels:
          severity: info
          service: pattern-mining
        annotations:
          summary: "Low cache hit rate ({{ $value }}%)"
          description: "Cache hit rate is {{ $value }}%, indicating potential performance degradation."

      # Integration Alerts
      - alert: CCLIntegrationLatencyHigh
        expr: histogram_quantile(0.95, rate(ccl_integration_latency_seconds_bucket[5m])) > 2
        for: 5m
        labels:
          severity: warning
          service: pattern-mining
        annotations:
          summary: "High integration latency with {{ $labels.service }}"
          description: "95th percentile latency for {{ $labels.service }} integration is {{ $value }}s."

      - alert: CCLIntegrationErrorsHigh
        expr: rate(ccl_integration_requests_total{status="error"}[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
          service: pattern-mining
        annotations:
          summary: "High integration error rate with {{ $labels.service }}"
          description: "Integration error rate with {{ $labels.service }} is {{ $value }} errors/second."

      # Contract Violation Alerts
      - alert: CCLContractViolation
        expr: rate(ccl_contract_violations_total[5m]) > 0
        for: 1m
        labels:
          severity: critical
          service: pattern-mining
          contract: ccl-v1
        annotations:
          summary: "CCL contract violation detected"
          description: "Contract violation of type {{ $labels.violation_type }} with severity {{ $labels.severity }} detected."

      - alert: CCLIDFormatErrors
        expr: rate(ccl_id_format_errors_total[5m]) > 0.05
        for: 5m
        labels:
          severity: warning
          service: pattern-mining
          contract: ccl-v1
        annotations:
          summary: "High rate of ID format errors"
          description: "Seeing {{ $value }} ID format errors per second for {{ $labels.id_type }}."