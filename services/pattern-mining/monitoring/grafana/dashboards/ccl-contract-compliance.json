{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": null, "links": [], "panels": [{"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 0, "y": 0}, "id": 1, "options": {"orientation": "auto", "reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "showThresholdLabels": false, "showThresholdMarkers": true, "text": {}}, "pluginVersion": "8.0.0", "targets": [{"expr": "ccl_contract_compliance_info", "refId": "A"}], "title": "Contract Compliance Status", "type": "stat"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"tooltip": false, "viz": false, "legend": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "reqps"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 6, "y": 0}, "id": 2, "options": {"tooltip": {"mode": "single"}}, "pluginVersion": "8.0.0", "targets": [{"expr": "rate(ccl_pattern_requests_total[5m])", "legendFormat": "{{status}}", "refId": "A"}], "title": "Pattern Detection Request Rate", "type": "timeseries"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 50}, {"color": "green", "value": 100}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 18, "y": 0}, "id": 3, "options": {"orientation": "auto", "reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "8.0.0", "targets": [{"expr": "(1 - (sum(rate(ccl_errors_total[5m])) / sum(rate(ccl_pattern_requests_total[5m])))) * 100", "refId": "A"}], "title": "Success Rate", "type": "gauge"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 85}, {"color": "red", "value": 100}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 0, "y": 4}, "id": 4, "options": {"orientation": "auto", "reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "showThresholdLabels": false, "showThresholdMarkers": true, "text": {}}, "pluginVersion": "8.0.0", "targets": [{"expr": "ccl_api_latency_p95_milliseconds", "refId": "A"}], "title": "API Latency (p95)", "type": "stat"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 50}, {"color": "green", "value": 55}]}, "unit": "files/s"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 18, "y": 4}, "id": 5, "options": {"orientation": "auto", "reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "8.0.0", "targets": [{"expr": "avg(ccl_file_processing_rate_per_second)", "refId": "A"}], "title": "File Processing Rate", "type": "gauge"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"tooltip": false, "viz": false, "legend": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "patterns/s"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "id": 6, "options": {"tooltip": {"mode": "single"}}, "pluginVersion": "8.0.0", "targets": [{"expr": "rate(ccl_patterns_detected_total[5m])", "legendFormat": "{{pattern_type}}", "refId": "A"}], "title": "Pattern Detection Rate by Type", "type": "timeseries"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"tooltip": false, "viz": false, "legend": false}}, "mappings": []}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "id": 7, "options": {"legend": {"displayMode": "list", "placement": "bottom"}, "pieType": "pie", "tooltip": {"mode": "single"}}, "targets": [{"expr": "sum by (severity) (ccl_patterns_detected_total)", "legendFormat": "{{severity}}", "refId": "A"}], "title": "Patterns by Severity", "type": "piechart"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"tooltip": false, "viz": false, "legend": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "id": 8, "options": {"tooltip": {"mode": "single"}}, "pluginVersion": "8.0.0", "targets": [{"expr": "ccl_cache_hit_rate_percentage", "legendFormat": "<PERSON><PERSON> Hit Rate", "refId": "A"}, {"expr": "ccl_error_rate_percentage", "legendFormat": "Error Rate", "refId": "B"}], "title": "Cache Hit Rate & Error Rate", "type": "timeseries"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"tooltip": false, "viz": false, "legend": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "decmbytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "id": 9, "options": {"tooltip": {"mode": "single"}}, "pluginVersion": "8.0.0", "targets": [{"expr": "ccl_memory_usage_megabytes", "legendFormat": "{{component}}", "refId": "A"}], "title": "Memory Usage by Component", "type": "timeseries"}], "refresh": "10s", "schemaVersion": 27, "style": "dark", "tags": ["ccl", "contract-compliance", "pattern-mining"], "templating": {"list": []}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "CCL Contract Compliance - Pattern Mining", "uid": "ccl-pattern-mining", "version": 1}