"""
Advanced Transaction Handling Integration Tests

Comprehensive testing of ACID compliance, complex transaction scenarios, 
concurrent transaction handling, deadlock detection/resolution, and 
transaction rollback consistency for the Pattern Mining service.

Phase 2 Enhancement: Advanced Database Integration Testing
"""

import pytest
import asyncio
import os
import uuid
import time
import threading
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
import json
from unittest.mock import patch

import asyncpg
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.pool import StaticPool
from sqlalchemy import text, select, insert, update, delete, func, and_, or_
from sqlalchemy.exc import Integ<PERSON><PERSON>rror, OperationalError, DBAPIError
from sqlalchemy.dialects.postgresql import insert as pg_insert

from pattern_mining.database.connection import DatabaseManager, get_database_session
from pattern_mining.database.repositories.pattern_repository import PatternRepository
from pattern_mining.database.repositories.analysis_repository import AnalysisRepository
from pattern_mining.database.models import <PERSON><PERSON><PERSON><PERSON><PERSON>, RepositoryAnalys<PERSON>, ModelReg<PERSON>ry, FeatureVector
from pattern_mining.models.patterns import PatternType, SeverityLevel, DetectionType
from pattern_mining.config.database import get_database_config
from tests.utils.database_fixtures import (
    generate_large_pattern_dataset,
    create_complex_analysis_scenario,
    simulate_concurrent_operations
)


@pytest.mark.integration
@pytest.mark.asyncio
class TestACIDCompliance:
    """Test ACID (Atomicity, Consistency, Isolation, Durability) properties."""
    
    @pytest.fixture
    def database_url(self):
        """Get test database URL from environment or use default."""
        return os.getenv(
            "TEST_DATABASE_URL",
            "postgresql://postgres:INSECURE_PASSWORD@localhost:5432/pattern_mining_test"
        )
    
    @pytest.fixture
    async def db_manager(self, database_url):
        """Create database manager for testing."""
        config = get_database_config()
        config.database_url = database_url
        config.pool_size = 10
        config.max_overflow = 20
        config.pool_timeout = 30
        
        manager = DatabaseManager(config)
        await manager.initialize()
        yield manager
        await manager.close()
    
    @pytest.fixture
    async def clean_database(self, db_manager):
        """Ensure clean database state before each test."""
        async with db_manager.get_session() as session:
            # Clean up test data
            await session.execute(text("DELETE FROM pattern_results WHERE repository_id LIKE 'test-%'"))
            await session.execute(text("DELETE FROM repository_analysis WHERE repository_id LIKE 'test-%'"))
            await session.execute(text("DELETE FROM model_registry WHERE model_id LIKE 'test-%'"))
            await session.execute(text("DELETE FROM feature_vectors WHERE repository_id LIKE 'test-%'"))
            await session.commit()
        yield
        # Cleanup after test
        async with db_manager.get_session() as session:
            await session.execute(text("DELETE FROM pattern_results WHERE repository_id LIKE 'test-%'"))
            await session.execute(text("DELETE FROM repository_analysis WHERE repository_id LIKE 'test-%'"))
            await session.execute(text("DELETE FROM model_registry WHERE model_id LIKE 'test-%'"))
            await session.execute(text("DELETE FROM feature_vectors WHERE repository_id LIKE 'test-%'"))
            await session.commit()
    
    async def test_atomicity_successful_transaction(self, db_manager: DatabaseManager, clean_database):
        """Test atomicity - all operations in transaction succeed or none do."""
        repository_id = "test-atomicity-success"
        analysis_id = str(uuid.uuid4())
        
        async with db_manager.get_session() as session:
            # Begin transaction - all operations should succeed together
            
            # 1. Insert repository analysis
            analysis = RepositoryAnalysis(
                repository_id=repository_id,
                repository_url=f"https://github.com/test/{repository_id}",
                repository_name=repository_id,
                owner="test-user",
                analysis_id=analysis_id,
                analysis_type="comprehensive",
                analysis_status="completed",
                total_files=50,
                total_lines=10000,
                patterns_detected=25,
                quality_score=85.5
            )
            session.add(analysis)
            
            # 2. Insert multiple pattern results
            patterns = []
            for i in range(5):
                pattern = PatternResult(
                    detection_id=f"{analysis_id}-pattern-{i}",
                    repository_id=repository_id,
                    file_path=f"src/file_{i}.py",
                    pattern_id=f"pattern-{i}",
                    pattern_name=f"Test Pattern {i}",
                    pattern_type=PatternType.DESIGN_PATTERN.value,
                    pattern_category="structural",
                    severity=SeverityLevel.MEDIUM.value,
                    confidence=0.8 + i * 0.02,
                    confidence_level="high",
                    line_start=10 + i * 5,
                    line_end=15 + i * 5,
                    detection_method=DetectionType.ML_INFERENCE.value,
                    language="python"
                )
                patterns.append(pattern)
                session.add(pattern)
            
            # 3. Update analysis with final counts
            analysis.patterns_detected = len(patterns)
            analysis.completed_at = datetime.utcnow()
            
            # Commit all operations atomically
            await session.commit()
        
        # Verify all operations were committed
        async with db_manager.get_session() as session:
            # Check analysis exists
            result = await session.execute(
                select(RepositoryAnalysis).where(
                    RepositoryAnalysis.analysis_id == analysis_id
                )
            )
            saved_analysis = result.scalar_one_or_none()
            assert saved_analysis is not None
            assert saved_analysis.patterns_detected == 5
            assert saved_analysis.completed_at is not None
            
            # Check all patterns exist
            result = await session.execute(
                select(func.count(PatternResult.id)).where(
                    PatternResult.repository_id == repository_id
                )
            )
            pattern_count = result.scalar()
            assert pattern_count == 5
    
    async def test_atomicity_failed_transaction(self, db_manager: DatabaseManager, clean_database):
        """Test atomicity - transaction rollback on failure."""
        repository_id = "test-atomicity-failure"
        analysis_id = str(uuid.uuid4())
        
        try:
            async with db_manager.get_session() as session:
                # 1. Insert valid repository analysis
                analysis = RepositoryAnalysis(
                    repository_id=repository_id,
                    repository_url=f"https://github.com/test/{repository_id}",
                    repository_name=repository_id,
                    owner="test-user",
                    analysis_id=analysis_id,
                    analysis_type="comprehensive",
                    analysis_status="completed",
                    total_files=30,
                    patterns_detected=10
                )
                session.add(analysis)
                
                # 2. Insert some valid patterns
                for i in range(3):
                    pattern = PatternResult(
                        detection_id=f"{analysis_id}-pattern-{i}",
                        repository_id=repository_id,
                        file_path=f"src/file_{i}.py",
                        pattern_id=f"pattern-{i}",
                        pattern_name=f"Valid Pattern {i}",
                        pattern_type=PatternType.DESIGN_PATTERN.value,
                        pattern_category="structural",
                        severity=SeverityLevel.LOW.value,
                        confidence=0.8,
                        confidence_level="high",
                        line_start=10,
                        line_end=15,
                        detection_method=DetectionType.ML_INFERENCE.value,
                        language="python"
                    )
                    session.add(pattern)
                
                # 3. Force an error with invalid data (confidence > 1.0)
                invalid_pattern = PatternResult(
                    detection_id=f"{analysis_id}-invalid",
                    repository_id=repository_id,
                    file_path="src/invalid.py",
                    pattern_id="invalid-pattern",
                    pattern_name="Invalid Pattern",
                    pattern_type=PatternType.DESIGN_PATTERN.value,
                    pattern_category="structural",
                    severity=SeverityLevel.LOW.value,
                    confidence=1.5,  # Invalid: > 1.0
                    confidence_level="high",
                    line_start=10,
                    line_end=15,
                    detection_method=DetectionType.ML_INFERENCE.value,
                    language="python"
                )
                session.add(invalid_pattern)
                
                # This should fail due to constraint violation
                await session.commit()
                
        except Exception:
            # Expected to fail
            pass
        
        # Verify nothing was committed (atomicity)
        async with db_manager.get_session() as session:
            # Check no analysis exists
            result = await session.execute(
                select(RepositoryAnalysis).where(
                    RepositoryAnalysis.analysis_id == analysis_id
                )
            )
            analysis = result.scalar_one_or_none()
            assert analysis is None
            
            # Check no patterns exist
            result = await session.execute(
                select(func.count(PatternResult.id)).where(
                    PatternResult.repository_id == repository_id
                )
            )
            pattern_count = result.scalar()
            assert pattern_count == 0
    
    async def test_consistency_constraint_enforcement(self, db_manager: DatabaseManager, clean_database):
        """Test consistency - database constraints are enforced."""
        repository_id = "test-consistency"
        
        # Test 1: Confidence constraint (0.0 <= confidence <= 1.0)
        async with db_manager.get_session() as session:
            # Valid confidence should work
            valid_pattern = PatternResult(
                detection_id=f"{repository_id}-valid",
                repository_id=repository_id,
                file_path="src/valid.py",
                pattern_id="valid-pattern",
                pattern_name="Valid Pattern",
                pattern_type=PatternType.DESIGN_PATTERN.value,
                pattern_category="structural",
                severity=SeverityLevel.LOW.value,
                confidence=0.85,  # Valid
                confidence_level="high",
                line_start=10,
                line_end=15,
                detection_method=DetectionType.ML_INFERENCE.value,
                language="python"
            )
            session.add(valid_pattern)
            await session.commit()
        
        # Test invalid confidence values
        invalid_confidences = [-0.1, 1.1, 2.0, -1.0]
        
        for invalid_confidence in invalid_confidences:
            with pytest.raises(Exception):  # Should raise constraint violation
                async with db_manager.get_session() as session:
                    invalid_pattern = PatternResult(
                        detection_id=f"{repository_id}-invalid-{invalid_confidence}",
                        repository_id=repository_id,
                        file_path="src/invalid.py",
                        pattern_id=f"invalid-pattern-{invalid_confidence}",
                        pattern_name="Invalid Confidence Pattern",
                        pattern_type=PatternType.DESIGN_PATTERN.value,
                        pattern_category="structural",
                        severity=SeverityLevel.LOW.value,
                        confidence=invalid_confidence,
                        confidence_level="high",
                        line_start=10,
                        line_end=15,
                        detection_method=DetectionType.ML_INFERENCE.value,
                        language="python"
                    )
                    session.add(invalid_pattern)
                    await session.commit()
        
        # Test 2: Quality score constraints (0.0 <= score <= 100.0)
        with pytest.raises(Exception):
            async with db_manager.get_session() as session:
                invalid_analysis = RepositoryAnalysis(
                    repository_id=f"{repository_id}-invalid-quality",
                    repository_url=f"https://github.com/test/{repository_id}",
                    repository_name=repository_id,
                    owner="test-user",
                    analysis_id=str(uuid.uuid4()),
                    analysis_type="comprehensive",
                    analysis_status="completed",
                    quality_score=150.0  # Invalid: > 100.0
                )
                session.add(invalid_analysis)
                await session.commit()
    
    async def test_isolation_concurrent_reads(self, db_manager: DatabaseManager, clean_database):
        """Test isolation - concurrent transactions don't interfere with reads."""
        repository_id = "test-isolation-reads"
        analysis_id = str(uuid.uuid4())
        
        # Setup initial data
        async with db_manager.get_session() as session:
            analysis = RepositoryAnalysis(
                repository_id=repository_id,
                repository_url=f"https://github.com/test/{repository_id}",
                repository_name=repository_id,
                owner="test-user",
                analysis_id=analysis_id,
                analysis_type="comprehensive",
                analysis_status="processing",
                total_files=10,
                patterns_detected=5,
                quality_score=75.0
            )
            session.add(analysis)
            await session.commit()
        
        # Test concurrent reads
        async def read_analysis(reader_id: int):
            """Read analysis data - should be consistent."""
            async with db_manager.get_session() as session:
                result = await session.execute(
                    select(RepositoryAnalysis).where(
                        RepositoryAnalysis.analysis_id == analysis_id
                    )
                )
                analysis = result.scalar_one_or_none()
                return {
                    'reader_id': reader_id,
                    'status': analysis.analysis_status if analysis else None,
                    'quality_score': analysis.quality_score if analysis else None,
                    'patterns_detected': analysis.patterns_detected if analysis else None
                }
        
        async def update_analysis():
            """Update analysis - should not affect concurrent reads."""
            await asyncio.sleep(0.1)  # Slight delay
            async with db_manager.get_session() as session:
                await session.execute(
                    update(RepositoryAnalysis)
                    .where(RepositoryAnalysis.analysis_id == analysis_id)
                    .values(
                        analysis_status="completed",
                        patterns_detected=8,
                        quality_score=82.0,
                        completed_at=datetime.utcnow()
                    )
                )
                await session.commit()
        
        # Run concurrent reads with one update
        tasks = [read_analysis(i) for i in range(5)]
        tasks.append(update_analysis())
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        read_results = [r for r in results if isinstance(r, dict)]
        
        # All reads should have succeeded
        assert len(read_results) == 5
        
        # All reads should have consistent data (either all old or all new values)
        statuses = [r['status'] for r in read_results]
        quality_scores = [r['quality_score'] for r in read_results]
        
        # All should be consistent within the read
        assert len(set(statuses)) <= 2  # Should be either "processing" or "completed"
        assert len(set(quality_scores)) <= 2  # Should be either 75.0 or 82.0
    
    async def test_durability_data_persistence(self, db_manager: DatabaseManager, clean_database):
        """Test durability - committed data survives system restarts."""
        repository_id = "test-durability"
        analysis_id = str(uuid.uuid4())
        test_data = {
            'repository_id': repository_id,
            'analysis_id': analysis_id,
            'patterns_detected': 15,
            'quality_score': 88.5
        }
        
        # Insert and commit data
        async with db_manager.get_session() as session:
            analysis = RepositoryAnalysis(
                repository_id=test_data['repository_id'],
                repository_url=f"https://github.com/test/{repository_id}",
                repository_name=repository_id,
                owner="test-user",
                analysis_id=test_data['analysis_id'],
                analysis_type="comprehensive",
                analysis_status="completed",
                patterns_detected=test_data['patterns_detected'],
                quality_score=test_data['quality_score'],
                completed_at=datetime.utcnow()
            )
            session.add(analysis)
            await session.commit()
        
        # Simulate connection restart by closing and reopening
        await db_manager.close()
        
        # Reinitialize connection
        config = get_database_config()
        config.database_url = db_manager.config.database_url
        new_manager = DatabaseManager(config)
        await new_manager.initialize()
        
        try:
            # Verify data persists after "restart"
            async with new_manager.get_session() as session:
                result = await session.execute(
                    select(RepositoryAnalysis).where(
                        RepositoryAnalysis.analysis_id == analysis_id
                    )
                )
                retrieved_analysis = result.scalar_one_or_none()
                
                assert retrieved_analysis is not None
                assert retrieved_analysis.repository_id == test_data['repository_id']
                assert retrieved_analysis.patterns_detected == test_data['patterns_detected']
                assert retrieved_analysis.quality_score == test_data['quality_score']
                assert retrieved_analysis.analysis_status == "completed"
                assert retrieved_analysis.completed_at is not None
        
        finally:
            await new_manager.close()


@pytest.mark.integration
@pytest.mark.asyncio
class TestComplexTransactionScenarios:
    """Test complex multi-table transaction scenarios."""
    
    @pytest.fixture
    def database_url(self):
        return os.getenv(
            "TEST_DATABASE_URL",
            "postgresql://postgres:INSECURE_PASSWORD@localhost:5432/pattern_mining_test"
        )
    
    @pytest.fixture
    async def db_manager(self, database_url):
        config = get_database_config()
        config.database_url = database_url
        config.pool_size = 15
        config.max_overflow = 30
        
        manager = DatabaseManager(config)
        await manager.initialize()
        yield manager
        await manager.close()
    
    @pytest.fixture
    async def clean_database(self, db_manager):
        """Clean database before and after tests."""
        async with db_manager.get_session() as session:
            await session.execute(text("DELETE FROM pattern_results WHERE repository_id LIKE 'test-%'"))
            await session.execute(text("DELETE FROM repository_analysis WHERE repository_id LIKE 'test-%'"))
            await session.execute(text("DELETE FROM model_registry WHERE model_id LIKE 'test-%'"))
            await session.execute(text("DELETE FROM feature_vectors WHERE repository_id LIKE 'test-%'"))
            await session.commit()
        yield
        async with db_manager.get_session() as session:
            await session.execute(text("DELETE FROM pattern_results WHERE repository_id LIKE 'test-%'"))
            await session.execute(text("DELETE FROM repository_analysis WHERE repository_id LIKE 'test-%'"))
            await session.execute(text("DELETE FROM model_registry WHERE model_id LIKE 'test-%'"))
            await session.execute(text("DELETE FROM feature_vectors WHERE repository_id LIKE 'test-%'"))
            await session.commit()
    
    async def test_comprehensive_analysis_workflow(self, db_manager: DatabaseManager, clean_database):
        """Test complex workflow: analysis creation → pattern detection → model training → embeddings."""
        repository_id = "test-complex-workflow"
        analysis_id = str(uuid.uuid4())
        model_id = f"test-model-{uuid.uuid4()}"
        
        async with db_manager.get_session() as session:
            # Step 1: Create repository analysis
            analysis = RepositoryAnalysis(
                repository_id=repository_id,
                repository_url=f"https://github.com/test/{repository_id}",
                repository_name=repository_id,
                owner="test-user",
                analysis_id=analysis_id,
                analysis_type="comprehensive",
                analysis_status="processing",
                total_files=100,
                total_lines=25000,
                languages=["python", "javascript", "typescript"],
                primary_language="python"
            )
            session.add(analysis)
            
            # Step 2: Create pattern detection results
            patterns = []
            for i in range(20):
                pattern = PatternResult(
                    detection_id=f"{analysis_id}-pattern-{i}",
                    repository_id=repository_id,
                    file_path=f"src/module_{i//5}/file_{i}.py",
                    pattern_id=f"pattern-{i}",
                    pattern_name=f"Pattern {i}",
                    pattern_type=[
                        PatternType.DESIGN_PATTERN.value,
                        PatternType.SECURITY_ISSUE.value,
                        PatternType.PERFORMANCE_ISSUE.value
                    ][i % 3],
                    pattern_category="structural",
                    severity=[
                        SeverityLevel.LOW.value,
                        SeverityLevel.MEDIUM.value,
                        SeverityLevel.HIGH.value
                    ][i % 3],
                    confidence=0.7 + (i % 20) * 0.015,
                    confidence_level="high",
                    line_start=10 + i * 5,
                    line_end=15 + i * 5,
                    function_name=f"function_{i}",
                    class_name=f"Class{i}" if i % 3 == 0 else None,
                    module_name=f"module_{i//5}",
                    detection_method=DetectionType.ML_INFERENCE.value,
                    model_version="1.0.0",
                    language="python",
                    lines_of_code=50 + i * 10,
                    cyclomatic_complexity=1.0 + i * 0.5,
                    processing_time_ms=100 + i * 20
                )
                patterns.append(pattern)
                session.add(pattern)
            
            # Step 3: Register ML model used for detection
            model = ModelRegistry(
                model_id=model_id,
                model_name="Pattern Detection Model v1.0",
                model_type="classification",
                version="1.0.0",
                architecture_json={
                    "type": "transformer",
                    "layers": 12,
                    "hidden_size": 768,
                    "attention_heads": 12
                },
                hyperparameters={
                    "learning_rate": 0.0001,
                    "batch_size": 32,
                    "epochs": 50,
                    "dropout": 0.1
                },
                precision=0.92,
                recall=0.88,
                f1_score=0.90,
                inference_time_ms=25.5,
                deployment_status="production",
                deployment_timestamp=datetime.utcnow(),
                traffic_percentage=1.0
            )
            session.add(model)
            
            # Step 4: Create feature vectors for ML training
            for i in range(10):
                feature_vector = FeatureVector(
                    feature_id=f"{repository_id}-feature-{i}",
                    repository_id=repository_id,
                    file_path=f"src/feature_file_{i}.py",
                    language="python",
                    structural_features=[0.1 * j for j in range(20)],
                    lexical_features=[0.05 * j for j in range(30)],
                    semantic_features=[0.02 * j for j in range(50)],
                    statistical_features=[0.01 * j for j in range(15)],
                    feature_count=115,
                    feature_version="1.0.0",
                    feature_quality_score=0.85 + i * 0.01,
                    extraction_success=True,
                    lines_of_code=200 + i * 50,
                    ast_depth=8 + i,
                    node_count=150 + i * 25,
                    extraction_time_ms=500 + i * 100
                )
                session.add(feature_vector)
            
            # Step 5: Update analysis with final results
            analysis.patterns_detected = len(patterns)
            analysis.critical_issues = sum(1 for p in patterns if p.severity == SeverityLevel.HIGH.value)
            analysis.medium_issues = sum(1 for p in patterns if p.severity == SeverityLevel.MEDIUM.value)
            analysis.low_issues = sum(1 for p in patterns if p.severity == SeverityLevel.LOW.value)
            analysis.quality_score = 85.0 - (analysis.critical_issues * 5) - (analysis.medium_issues * 2)
            analysis.security_score = 90.0 - (sum(1 for p in patterns if p.pattern_type == PatternType.SECURITY_ISSUE.value) * 10)
            analysis.analysis_status = "completed"
            analysis.completed_at = datetime.utcnow()
            analysis.processing_time_ms = 45000
            
            # Commit entire workflow atomically
            await session.commit()
        
        # Verify all components were created successfully
        async with db_manager.get_session() as session:
            # Verify analysis
            result = await session.execute(
                select(RepositoryAnalysis).where(RepositoryAnalysis.analysis_id == analysis_id)
            )
            saved_analysis = result.scalar_one_or_none()
            assert saved_analysis is not None
            assert saved_analysis.patterns_detected == 20
            assert saved_analysis.analysis_status == "completed"
            assert saved_analysis.quality_score > 0
            
            # Verify patterns
            result = await session.execute(
                select(func.count(PatternResult.id)).where(PatternResult.repository_id == repository_id)
            )
            pattern_count = result.scalar()
            assert pattern_count == 20
            
            # Verify model
            result = await session.execute(
                select(ModelRegistry).where(ModelRegistry.model_id == model_id)
            )
            saved_model = result.scalar_one_or_none()
            assert saved_model is not None
            assert saved_model.deployment_status == "production"
            
            # Verify feature vectors
            result = await session.execute(
                select(func.count(FeatureVector.id)).where(FeatureVector.repository_id == repository_id)
            )
            feature_count = result.scalar()
            assert feature_count == 10
    
    async def test_bulk_pattern_insertion_with_analysis_update(self, db_manager: DatabaseManager, clean_database):
        """Test bulk insertion of patterns with atomic analysis updates."""
        repository_id = "test-bulk-insertion"
        analysis_id = str(uuid.uuid4())
        batch_size = 100
        
        async with db_manager.get_session() as session:
            # Create initial analysis
            analysis = RepositoryAnalysis(
                repository_id=repository_id,
                repository_url=f"https://github.com/test/{repository_id}",
                repository_name=repository_id,
                owner="test-user",
                analysis_id=analysis_id,
                analysis_type="bulk_import",
                analysis_status="processing",
                total_files=200,
                total_lines=50000
            )
            session.add(analysis)
            
            # Bulk insert patterns using executemany for performance
            pattern_data = []
            for i in range(batch_size):
                pattern_data.append({
                    'detection_id': f"{analysis_id}-bulk-{i}",
                    'repository_id': repository_id,
                    'file_path': f"src/bulk/file_{i}.py",
                    'pattern_id': f"bulk-pattern-{i}",
                    'pattern_name': f"Bulk Pattern {i}",
                    'pattern_type': PatternType.DESIGN_PATTERN.value,
                    'pattern_category': 'structural',
                    'severity': SeverityLevel.LOW.value,
                    'confidence': 0.8 + (i % 20) * 0.01,
                    'confidence_level': 'high',
                    'line_start': 10 + i,
                    'line_end': 20 + i,
                    'detection_method': DetectionType.ML_INFERENCE.value,
                    'language': 'python',
                    'processing_time_ms': 50 + i,
                    'detected_at': datetime.utcnow(),
                    'detection_date': datetime.utcnow().date()
                })
            
            # Use bulk insert for performance
            await session.execute(
                insert(PatternResult).values(pattern_data)
            )
            
            # Update analysis with aggregated statistics
            stats_query = select(
                func.count(PatternResult.id).label('total_patterns'),
                func.count(PatternResult.id).filter(
                    PatternResult.severity == SeverityLevel.CRITICAL.value
                ).label('critical_issues'),
                func.count(PatternResult.id).filter(
                    PatternResult.severity == SeverityLevel.HIGH.value
                ).label('high_issues'),
                func.count(PatternResult.id).filter(
                    PatternResult.severity == SeverityLevel.MEDIUM.value
                ).label('medium_issues'),
                func.count(PatternResult.id).filter(
                    PatternResult.severity == SeverityLevel.LOW.value
                ).label('low_issues'),
                func.avg(PatternResult.confidence).label('avg_confidence')
            ).where(PatternResult.repository_id == repository_id)
            
            stats_result = await session.execute(stats_query)
            stats = stats_result.first()
            
            # Update analysis with calculated statistics
            analysis.patterns_detected = stats.total_patterns
            analysis.critical_issues = stats.critical_issues or 0
            analysis.high_issues = stats.high_issues or 0
            analysis.medium_issues = stats.medium_issues or 0
            analysis.low_issues = stats.low_issues or 0
            analysis.quality_score = min(100.0, 90.0 + float(stats.avg_confidence or 0.8) * 10)
            analysis.analysis_status = "completed"
            analysis.completed_at = datetime.utcnow()
            
            # Commit bulk operation atomically
            await session.commit()
        
        # Verify bulk insertion succeeded
        async with db_manager.get_session() as session:
            # Check pattern count
            result = await session.execute(
                select(func.count(PatternResult.id)).where(PatternResult.repository_id == repository_id)
            )
            pattern_count = result.scalar()
            assert pattern_count == batch_size
            
            # Check analysis update
            result = await session.execute(
                select(RepositoryAnalysis).where(RepositoryAnalysis.analysis_id == analysis_id)
            )
            updated_analysis = result.scalar_one_or_none()
            assert updated_analysis is not None
            assert updated_analysis.patterns_detected == batch_size
            assert updated_analysis.analysis_status == "completed"
            assert updated_analysis.quality_score > 0
    
    async def test_transaction_rollback_on_constraint_violation(self, db_manager: DatabaseManager, clean_database):
        """Test complex transaction rollback when constraints are violated."""
        repository_id = "test-constraint-rollback"
        analysis_id = str(uuid.uuid4())
        
        # This transaction should fail and rollback completely
        with pytest.raises(Exception):
            async with db_manager.get_session() as session:
                # Step 1: Valid analysis
                analysis = RepositoryAnalysis(
                    repository_id=repository_id,
                    repository_url=f"https://github.com/test/{repository_id}",
                    repository_name=repository_id,
                    owner="test-user",
                    analysis_id=analysis_id,
                    analysis_type="comprehensive",
                    analysis_status="completed",
                    total_files=50,
                    patterns_detected=10
                )
                session.add(analysis)
                
                # Step 2: Valid patterns
                for i in range(5):
                    pattern = PatternResult(
                        detection_id=f"{analysis_id}-valid-{i}",
                        repository_id=repository_id,
                        file_path=f"src/valid_{i}.py",
                        pattern_id=f"valid-pattern-{i}",
                        pattern_name=f"Valid Pattern {i}",
                        pattern_type=PatternType.DESIGN_PATTERN.value,
                        pattern_category="structural",
                        severity=SeverityLevel.LOW.value,
                        confidence=0.8,
                        confidence_level="high",
                        line_start=10,
                        line_end=15,
                        detection_method=DetectionType.ML_INFERENCE.value,
                        language="python"
                    )
                    session.add(pattern)
                
                # Step 3: Valid model
                model = ModelRegistry(
                    model_id=f"test-model-{analysis_id}",
                    model_name="Valid Model",
                    model_type="classification",
                    version="1.0.0",
                    precision=0.9,
                    recall=0.85,
                    f1_score=0.87,
                    deployment_status="staging"
                )
                session.add(model)
                
                # Step 4: INVALID feature vector (confidence > 1.0)
                invalid_feature = FeatureVector(
                    feature_id=f"{repository_id}-invalid-feature",
                    repository_id=repository_id,
                    file_path="src/invalid.py",
                    language="python",
                    structural_features=[0.1] * 20,
                    lexical_features=[0.05] * 30,
                    semantic_features=[0.02] * 50,
                    statistical_features=[0.01] * 15,
                    feature_count=115,
                    feature_version="1.0.0",
                    feature_quality_score=1.5,  # INVALID: > 1.0
                    extraction_success=True,
                    lines_of_code=200,
                    ast_depth=8,
                    node_count=150,
                    extraction_time_ms=500
                )
                session.add(invalid_feature)
                
                # This should fail on commit
                await session.commit()
        
        # Verify complete rollback - nothing should exist
        async with db_manager.get_session() as session:
            # Check no analysis
            result = await session.execute(
                select(func.count(RepositoryAnalysis.id)).where(
                    RepositoryAnalysis.repository_id == repository_id
                )
            )
            assert result.scalar() == 0
            
            # Check no patterns
            result = await session.execute(
                select(func.count(PatternResult.id)).where(
                    PatternResult.repository_id == repository_id
                )
            )
            assert result.scalar() == 0
            
            # Check no model
            result = await session.execute(
                select(func.count(ModelRegistry.id)).where(
                    ModelRegistry.model_id == f"test-model-{analysis_id}"
                )
            )
            assert result.scalar() == 0
            
            # Check no feature vectors
            result = await session.execute(
                select(func.count(FeatureVector.id)).where(
                    FeatureVector.repository_id == repository_id
                )
            )
            assert result.scalar() == 0


@pytest.mark.integration
@pytest.mark.asyncio
class TestConcurrentTransactions:
    """Test concurrent transaction handling and deadlock prevention."""
    
    @pytest.fixture
    def database_url(self):
        return os.getenv(
            "TEST_DATABASE_URL",
            "postgresql://postgres:INSECURE_PASSWORD@localhost:5432/pattern_mining_test"
        )
    
    @pytest.fixture
    async def db_manager(self, database_url):
        config = get_database_config()
        config.database_url = database_url
        config.pool_size = 20  # Higher pool for concurrency testing
        config.max_overflow = 40
        
        manager = DatabaseManager(config)
        await manager.initialize()
        yield manager
        await manager.close()
    
    @pytest.fixture
    async def setup_concurrent_test_data(self, db_manager):
        """Setup shared data for concurrent tests."""
        repository_id = "test-concurrent-base"
        analysis_id = str(uuid.uuid4())
        
        async with db_manager.get_session() as session:
            # Create base analysis
            analysis = RepositoryAnalysis(
                repository_id=repository_id,
                repository_url=f"https://github.com/test/{repository_id}",
                repository_name=repository_id,
                owner="test-user",
                analysis_id=analysis_id,
                analysis_type="concurrent_test",
                analysis_status="processing",
                total_files=100,
                patterns_detected=0,
                quality_score=50.0
            )
            session.add(analysis)
            await session.commit()
        
        yield repository_id, analysis_id
        
        # Cleanup
        async with db_manager.get_session() as session:
            await session.execute(text("DELETE FROM pattern_results WHERE repository_id LIKE 'test-concurrent%'"))
            await session.execute(text("DELETE FROM repository_analysis WHERE repository_id LIKE 'test-concurrent%'"))
            await session.commit()
    
    async def test_concurrent_pattern_insertions(self, db_manager: DatabaseManager, setup_concurrent_test_data):
        """Test concurrent pattern insertions without conflicts."""
        base_repository_id, base_analysis_id = setup_concurrent_test_data
        concurrent_workers = 10
        patterns_per_worker = 20
        
        async def insert_patterns_worker(worker_id: int):
            """Worker function to insert patterns concurrently."""
            repository_id = f"test-concurrent-worker-{worker_id}"
            analysis_id = str(uuid.uuid4())
            
            async with db_manager.get_session() as session:
                # Create worker-specific analysis
                analysis = RepositoryAnalysis(
                    repository_id=repository_id,
                    repository_url=f"https://github.com/test/{repository_id}",
                    repository_name=repository_id,
                    owner="test-user",
                    analysis_id=analysis_id,
                    analysis_type="concurrent_worker",
                    analysis_status="processing",
                    total_files=patterns_per_worker
                )
                session.add(analysis)
                
                # Insert patterns for this worker
                for i in range(patterns_per_worker):
                    pattern = PatternResult(
                        detection_id=f"{analysis_id}-worker-{worker_id}-pattern-{i}",
                        repository_id=repository_id,
                        file_path=f"src/worker_{worker_id}/file_{i}.py",
                        pattern_id=f"worker-{worker_id}-pattern-{i}",
                        pattern_name=f"Worker {worker_id} Pattern {i}",
                        pattern_type=PatternType.DESIGN_PATTERN.value,
                        pattern_category="structural",
                        severity=SeverityLevel.LOW.value,
                        confidence=0.8 + (i * 0.01),
                        confidence_level="high",
                        line_start=10 + i,
                        line_end=15 + i,
                        detection_method=DetectionType.ML_INFERENCE.value,
                        language="python",
                        processing_time_ms=100 + i * 10
                    )
                    session.add(pattern)
                
                # Update analysis
                analysis.patterns_detected = patterns_per_worker
                analysis.analysis_status = "completed"
                analysis.completed_at = datetime.utcnow()
                
                await session.commit()
                return worker_id, patterns_per_worker
        
        # Run concurrent workers
        tasks = [insert_patterns_worker(i) for i in range(concurrent_workers)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # All workers should succeed
        successful_results = [r for r in results if not isinstance(r, Exception)]
        assert len(successful_results) == concurrent_workers
        
        # Verify total data
        async with db_manager.get_session() as session:
            # Count patterns from all workers
            result = await session.execute(
                select(func.count(PatternResult.id)).where(
                    PatternResult.repository_id.like('test-concurrent-worker-%')
                )
            )
            total_patterns = result.scalar()
            assert total_patterns == concurrent_workers * patterns_per_worker
            
            # Count analyses from all workers
            result = await session.execute(
                select(func.count(RepositoryAnalysis.id)).where(
                    RepositoryAnalysis.repository_id.like('test-concurrent-worker-%')
                )
            )
            total_analyses = result.scalar()
            assert total_analyses == concurrent_workers
    
    async def test_concurrent_analysis_updates(self, db_manager: DatabaseManager, setup_concurrent_test_data):
        """Test concurrent updates to same analysis record."""
        base_repository_id, base_analysis_id = setup_concurrent_test_data
        concurrent_workers = 8
        
        async def update_analysis_worker(worker_id: int, field_name: str, increment: int):
            """Worker to update specific field of analysis."""
            max_retries = 3
            retry_delay = 0.1
            
            for attempt in range(max_retries):
                try:
                    async with db_manager.get_session() as session:
                        # Read current analysis
                        result = await session.execute(
                            select(RepositoryAnalysis).where(
                                RepositoryAnalysis.analysis_id == base_analysis_id
                            )
                        )
                        analysis = result.scalar_one()
                        
                        # Update specific field
                        if field_name == "patterns_detected":
                            analysis.patterns_detected += increment
                        elif field_name == "quality_score":
                            analysis.quality_score = min(100.0, analysis.quality_score + increment)
                        elif field_name == "total_files":
                            analysis.total_files += increment
                        
                        analysis.updated_at = datetime.utcnow()
                        
                        await session.commit()
                        return worker_id, field_name, increment
                        
                except Exception as e:
                    if attempt < max_retries - 1:
                        await asyncio.sleep(retry_delay * (2 ** attempt))  # Exponential backoff
                        continue
                    else:
                        raise e
        
        # Create update tasks for different fields
        tasks = []
        for i in range(concurrent_workers):
            field_name = ["patterns_detected", "quality_score", "total_files"][i % 3]
            increment = i + 1
            tasks.append(update_analysis_worker(i, field_name, increment))
        
        # Run concurrent updates
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Most updates should succeed (some may fail due to conflicts)
        successful_results = [r for r in results if not isinstance(r, Exception)]
        assert len(successful_results) >= concurrent_workers // 2  # At least half should succeed
        
        # Verify final state is consistent
        async with db_manager.get_session() as session:
            result = await session.execute(
                select(RepositoryAnalysis).where(
                    RepositoryAnalysis.analysis_id == base_analysis_id
                )
            )
            final_analysis = result.scalar_one()
            
            # Values should be updated (exact values depend on which updates succeeded)
            assert final_analysis.patterns_detected >= 0
            assert 0 <= final_analysis.quality_score <= 100
            assert final_analysis.total_files >= 100  # Original value
    
    async def test_deadlock_detection_and_recovery(self, db_manager: DatabaseManager, setup_concurrent_test_data):
        """Test deadlock detection and automatic recovery."""
        base_repository_id, base_analysis_id = setup_concurrent_test_data
        
        # Create two additional analyses for cross-updates
        additional_analyses = []
        async with db_manager.get_session() as session:
            for i in range(2):
                analysis_id = str(uuid.uuid4())
                analysis = RepositoryAnalysis(
                    repository_id=f"test-concurrent-deadlock-{i}",
                    repository_url=f"https://github.com/test/deadlock-{i}",
                    repository_name=f"deadlock-{i}",
                    owner="test-user",
                    analysis_id=analysis_id,
                    analysis_type="deadlock_test",
                    analysis_status="processing",
                    total_files=50,
                    patterns_detected=10,
                    quality_score=70.0
                )
                session.add(analysis)
                additional_analyses.append(analysis_id)
            await session.commit()
        
        deadlock_detected = []
        
        async def cross_update_worker(worker_id: int):
            """Worker that updates analyses in different orders to create deadlock potential."""
            analysis_ids = [base_analysis_id] + additional_analyses
            
            # Worker 0: updates A then B then C
            # Worker 1: updates C then A then B  
            # Worker 2: updates B then C then A
            update_order = [
                [analysis_ids[0], analysis_ids[1], analysis_ids[2]],
                [analysis_ids[2], analysis_ids[0], analysis_ids[1]],
                [analysis_ids[1], analysis_ids[2], analysis_ids[0]]
            ][worker_id % 3]
            
            max_retries = 5
            for attempt in range(max_retries):
                try:
                    async with db_manager.get_session() as session:
                        for analysis_id in update_order:
                            # Small delay to increase deadlock probability
                            await asyncio.sleep(0.01)
                            
                            # Update analysis
                            await session.execute(
                                update(RepositoryAnalysis)
                                .where(RepositoryAnalysis.analysis_id == analysis_id)
                                .values(
                                    patterns_detected=RepositoryAnalysis.patterns_detected + 1,
                                    updated_at=datetime.utcnow()
                                )
                            )
                            
                            # Another small delay
                            await asyncio.sleep(0.01)
                        
                        await session.commit()
                        return worker_id, "success"
                        
                except Exception as e:
                    error_msg = str(e).lower()
                    if "deadlock" in error_msg or "could not serialize" in error_msg:
                        deadlock_detected.append(worker_id)
                        if attempt < max_retries - 1:
                            # Exponential backoff with jitter
                            delay = 0.1 * (2 ** attempt) + (worker_id * 0.01)
                            await asyncio.sleep(delay)
                            continue
                    raise e
            
            return worker_id, "failed_after_retries"
        
        # Run workers that can cause deadlocks
        num_workers = 6
        tasks = [cross_update_worker(i) for i in range(num_workers)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Some operations should succeed despite potential deadlocks
        successful_results = [r for r in results if not isinstance(r, Exception) and r[1] == "success"]
        assert len(successful_results) >= 1  # At least one should succeed
        
        # Verify data integrity despite deadlocks
        async with db_manager.get_session() as session:
            for analysis_id in [base_analysis_id] + additional_analyses:
                result = await session.execute(
                    select(RepositoryAnalysis).where(RepositoryAnalysis.analysis_id == analysis_id)
                )
                analysis = result.scalar_one()
                
                # Values should be consistent (no partial updates)
                assert analysis.patterns_detected >= 10  # Original value
                assert analysis.quality_score >= 0
                assert analysis.updated_at is not None
        
        # Cleanup additional test data
        async with db_manager.get_session() as session:
            await session.execute(text("DELETE FROM repository_analysis WHERE repository_id LIKE 'test-concurrent-deadlock-%'"))
            await session.commit()


@pytest.mark.integration
@pytest.mark.asyncio
class TestTransactionPerformanceAndScaling:
    """Test transaction performance under load and scaling characteristics."""
    
    @pytest.fixture
    def database_url(self):
        return os.getenv(
            "TEST_DATABASE_URL",
            "postgresql://postgres:INSECURE_PASSWORD@localhost:5432/pattern_mining_test"
        )
    
    @pytest.fixture
    async def db_manager(self, database_url):
        config = get_database_config()
        config.database_url = database_url
        config.pool_size = 25
        config.max_overflow = 50
        
        manager = DatabaseManager(config)
        await manager.initialize()
        yield manager
        await manager.close()
    
    async def test_high_throughput_transaction_processing(self, db_manager: DatabaseManager):
        """Test system performance under high transaction throughput."""
        start_time = time.time()
        target_transactions = 1000
        batch_size = 50
        
        async def process_transaction_batch(batch_id: int):
            """Process a batch of transactions."""
            batch_start = time.time()
            repository_id = f"test-throughput-batch-{batch_id}"
            analysis_id = str(uuid.uuid4())
            
            async with db_manager.get_session() as session:
                # Create analysis
                analysis = RepositoryAnalysis(
                    repository_id=repository_id,
                    repository_url=f"https://github.com/test/{repository_id}",
                    repository_name=repository_id,
                    owner="test-user",
                    analysis_id=analysis_id,
                    analysis_type="throughput_test",
                    analysis_status="processing",
                    total_files=batch_size
                )
                session.add(analysis)
                
                # Bulk insert patterns
                patterns = []
                for i in range(batch_size):
                    pattern = PatternResult(
                        detection_id=f"{analysis_id}-pattern-{i}",
                        repository_id=repository_id,
                        file_path=f"src/batch_{batch_id}/file_{i}.py",
                        pattern_id=f"batch-{batch_id}-pattern-{i}",
                        pattern_name=f"Throughput Pattern {i}",
                        pattern_type=PatternType.DESIGN_PATTERN.value,
                        pattern_category="structural",
                        severity=SeverityLevel.LOW.value,
                        confidence=0.8,
                        confidence_level="high",
                        line_start=10 + i,
                        line_end=15 + i,
                        detection_method=DetectionType.ML_INFERENCE.value,
                        language="python"
                    )
                    patterns.append(pattern)
                    session.add(pattern)
                
                # Update analysis
                analysis.patterns_detected = len(patterns)
                analysis.analysis_status = "completed"
                analysis.completed_at = datetime.utcnow()
                
                await session.commit()
                
                batch_time = time.time() - batch_start
                return batch_id, batch_time, len(patterns)
        
        # Process batches concurrently
        num_batches = target_transactions // batch_size
        tasks = [process_transaction_batch(i) for i in range(num_batches)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        total_time = time.time() - start_time
        
        # Analyze results
        successful_batches = [r for r in results if not isinstance(r, Exception)]
        assert len(successful_batches) == num_batches
        
        total_patterns = sum(r[2] for r in successful_batches)
        avg_batch_time = sum(r[1] for r in successful_batches) / len(successful_batches)
        throughput = total_patterns / total_time  # patterns per second
        
        # Performance assertions
        assert total_patterns == target_transactions
        assert throughput > 500  # Should process > 500 patterns/second
        assert avg_batch_time < 1.0  # Average batch should complete in < 1 second
        
        print(f"Transaction Throughput Test Results:")
        print(f"Total Patterns: {total_patterns}")
        print(f"Total Time: {total_time:.2f}s")
        print(f"Throughput: {throughput:.2f} patterns/second")
        print(f"Average Batch Time: {avg_batch_time:.3f}s")
        
        # Cleanup
        async with db_manager.get_session() as session:
            await session.execute(text("DELETE FROM pattern_results WHERE repository_id LIKE 'test-throughput-batch-%'"))
            await session.execute(text("DELETE FROM repository_analysis WHERE repository_id LIKE 'test-throughput-batch-%'"))
            await session.commit()
    
    async def test_transaction_isolation_under_load(self, db_manager: DatabaseManager):
        """Test transaction isolation is maintained under high load."""
        shared_repository_id = "test-isolation-load"
        base_analysis_id = str(uuid.uuid4())
        
        # Setup shared analysis
        async with db_manager.get_session() as session:
            analysis = RepositoryAnalysis(
                repository_id=shared_repository_id,
                repository_url=f"https://github.com/test/{shared_repository_id}",
                repository_name=shared_repository_id,
                owner="test-user",
                analysis_id=base_analysis_id,
                analysis_type="isolation_load_test",
                analysis_status="processing",
                total_files=0,
                patterns_detected=0,
                quality_score=0.0
            )
            session.add(analysis)
            await session.commit()
        
        isolation_violations = []
        successful_operations = []
        
        async def isolated_update_worker(worker_id: int):
            """Worker that performs isolated updates."""
            for operation in range(10):
                max_retries = 3
                for attempt in range(max_retries):
                    try:
                        async with db_manager.get_session() as session:
                            # Read current state
                            result = await session.execute(
                                select(RepositoryAnalysis).where(
                                    RepositoryAnalysis.analysis_id == base_analysis_id
                                )
                            )
                            analysis = result.scalar_one()
                            
                            current_patterns = analysis.patterns_detected
                            current_quality = analysis.quality_score
                            
                            # Simulate processing time
                            await asyncio.sleep(0.01)
                            
                            # Update based on current values (should be isolated)
                            analysis.patterns_detected = current_patterns + 1
                            analysis.quality_score = min(100.0, current_quality + 0.5)
                            analysis.updated_at = datetime.utcnow()
                            
                            await session.commit()
                            
                            # Verify update was consistent
                            result = await session.execute(
                                select(RepositoryAnalysis).where(
                                    RepositoryAnalysis.analysis_id == base_analysis_id
                                )
                            )
                            updated_analysis = result.scalar_one()
                            
                            # Check for isolation violations
                            if updated_analysis.patterns_detected != current_patterns + 1:
                                isolation_violations.append({
                                    'worker_id': worker_id,
                                    'operation': operation,
                                    'expected': current_patterns + 1,
                                    'actual': updated_analysis.patterns_detected
                                })
                            else:
                                successful_operations.append((worker_id, operation))
                            
                            break  # Success
                            
                    except Exception as e:
                        if attempt < max_retries - 1:
                            await asyncio.sleep(0.01 * (2 ** attempt))
                            continue
                        raise e
        
        # Run concurrent isolated operations
        num_workers = 15
        tasks = [isolated_update_worker(i) for i in range(num_workers)]
        await asyncio.gather(*tasks, return_exceptions=True)
        
        # Verify isolation was maintained
        print(f"Isolation Test Results:")
        print(f"Successful Operations: {len(successful_operations)}")
        print(f"Isolation Violations: {len(isolation_violations)}")
        
        # Should have minimal isolation violations
        violation_rate = len(isolation_violations) / (len(successful_operations) + len(isolation_violations)) if (len(successful_operations) + len(isolation_violations)) > 0 else 0
        assert violation_rate < 0.05  # Less than 5% violation rate
        
        # Verify final state consistency
        async with db_manager.get_session() as session:
            result = await session.execute(
                select(RepositoryAnalysis).where(
                    RepositoryAnalysis.analysis_id == base_analysis_id
                )
            )
            final_analysis = result.scalar_one()
            
            # Final values should be consistent
            assert final_analysis.patterns_detected >= 0
            assert 0 <= final_analysis.quality_score <= 100
        
        # Cleanup
        async with db_manager.get_session() as session:
            await session.execute(text(f"DELETE FROM repository_analysis WHERE analysis_id = '{base_analysis_id}'"))
            await session.commit()