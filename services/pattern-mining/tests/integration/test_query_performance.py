"""
Advanced Query Performance Integration Tests

Comprehensive testing of query execution plans, index performance, 
large dataset query testing, concurrent query performance, and 
optimization validation for the Pattern Mining service database layer.

Phase 2 Enhancement: Advanced Database Integration Testing
"""

import pytest
import asyncio
import os
import time
import statistics
import random
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
import json
from unittest.mock import patch, MagicMock

import asyncpg
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy import text, select, func, and_, or_, desc, asc
from sqlalchemy.exc import Integrity<PERSON>rror, OperationalError, DBAPIError

from pattern_mining.database.connection import DatabaseManager, get_database_session
from pattern_mining.database.repositories.pattern_repository import PatternRepository
from pattern_mining.database.repositories.analysis_repository import AnalysisRepository
from pattern_mining.database.models import <PERSON><PERSON><PERSON><PERSON>ult, RepositoryAnalysis, ModelRegistry, FeatureVector
from pattern_mining.models.patterns import PatternType, SeverityLevel, DetectionType
from pattern_mining.config.database import get_database_config
from tests.utils.database_fixtures import (
    generate_large_dataset,
    create_query_performance_data,
    simulate_realistic_query_patterns
)


@pytest.mark.integration
@pytest.mark.asyncio
class TestQueryExecutionPlanAnalysis:
    """Test query execution plan analysis and optimization."""
    
    @pytest.fixture
    def database_url(self):
        """Get test database URL from environment or use default."""
        return os.getenv(
            "TEST_DATABASE_URL",
            "postgresql://postgres:INSECURE_PASSWORD@localhost:5432/pattern_mining_test"
        )
    
    @pytest.fixture
    async def performance_db_manager(self, database_url):
        """Create database manager for performance testing."""
        config = get_database_config()
        config.database_url = database_url
        config.pool_size = 10
        config.max_overflow = 20
        
        manager = DatabaseManager(config)
        await manager.initialize()
        yield manager
        await manager.close()
    
    @pytest.fixture
    async def large_dataset_setup(self, performance_db_manager):
        """Setup large dataset for performance testing."""
        print("Setting up large dataset for performance testing...")
        
        # Create large dataset with various characteristics
        setup_sql = """
        -- Clean existing test data
        DELETE FROM pattern_results WHERE repository_id LIKE 'test-perf-%';
        DELETE FROM repository_analysis WHERE repository_id LIKE 'test-perf-%';
        
        -- Generate large pattern results dataset
        INSERT INTO pattern_results (
            detection_id, repository_id, file_path, pattern_id, pattern_name,
            pattern_type, pattern_category, severity, confidence, confidence_level,
            line_start, line_end, function_name, class_name, module_name,
            detection_method, model_version, language, lines_of_code, 
            cyclomatic_complexity, processing_time_ms
        )
        SELECT 
            'perf-pattern-' || generate_series,
            'test-perf-repo-' || ((generate_series - 1) / 1000 + 1),
            'src/module_' || ((generate_series - 1) / 100 + 1) || '/file_' || generate_series || '.py',
            'pattern-' || generate_series,
            'Performance Pattern ' || generate_series,
            CASE (generate_series % 4)
                WHEN 0 THEN 'DESIGN_PATTERN'
                WHEN 1 THEN 'SECURITY_ISSUE'
                WHEN 2 THEN 'PERFORMANCE_ISSUE'
                ELSE 'CODE_SMELL'
            END,
            CASE (generate_series % 3)
                WHEN 0 THEN 'structural'
                WHEN 1 THEN 'behavioral'
                ELSE 'creational'
            END,
            CASE (generate_series % 4)
                WHEN 0 THEN 'CRITICAL'
                WHEN 1 THEN 'HIGH'
                WHEN 2 THEN 'MEDIUM'
                ELSE 'LOW'
            END,
            0.3 + (generate_series % 70) * 0.01,  -- Confidence 0.3 to 1.0
            CASE 
                WHEN (0.3 + (generate_series % 70) * 0.01) >= 0.9 THEN 'very_high'
                WHEN (0.3 + (generate_series % 70) * 0.01) >= 0.8 THEN 'high'
                WHEN (0.3 + (generate_series % 70) * 0.01) >= 0.6 THEN 'medium'
                ELSE 'low'
            END,
            generate_series * 2,
            generate_series * 2 + 10 + (generate_series % 20),
            'function_' || (generate_series % 500),
            CASE WHEN generate_series % 3 = 0 THEN 'Class' || (generate_series % 100) ELSE NULL END,
            'module_' || ((generate_series - 1) / 100 + 1),
            CASE (generate_series % 3)
                WHEN 0 THEN 'ML_INFERENCE'
                WHEN 1 THEN 'HEURISTIC'
                ELSE 'RULE_BASED'
            END,
            '1.' || (generate_series % 5) || '.0',
            CASE (generate_series % 3)
                WHEN 0 THEN 'python'
                WHEN 1 THEN 'javascript'
                ELSE 'typescript'
            END,
            50 + (generate_series % 1000),
            1.0 + (generate_series % 20) * 0.5,
            50 + (generate_series % 500)
        FROM generate_series(1, 50000);  -- 50K records for comprehensive testing
        
        -- Generate repository analysis data
        INSERT INTO repository_analysis (
            repository_id, repository_url, repository_name, owner,
            analysis_id, analysis_type, analysis_status,
            total_files, total_lines, languages, primary_language,
            patterns_detected, critical_issues, high_issues, medium_issues, low_issues,
            quality_score, maintainability_score, security_score,
            processing_time_ms, started_at, completed_at
        )
        SELECT 
            'test-perf-repo-' || generate_series,
            'https://github.com/test/perf-repo-' || generate_series,
            'perf-repo-' || generate_series,
            'test-user',
            'perf-analysis-' || generate_series,
            CASE (generate_series % 3)
                WHEN 0 THEN 'comprehensive'
                WHEN 1 THEN 'security_focused'
                ELSE 'performance_focused'
            END,
            'completed',
            100 + generate_series * 10,
            5000 + generate_series * 500,
            ARRAY['python', 'javascript', 'typescript'],
            CASE (generate_series % 3)
                WHEN 0 THEN 'python'
                WHEN 1 THEN 'javascript'
                ELSE 'typescript'
            END,
            1000,  -- Will be updated with actual counts
            generate_series % 10,
            (generate_series % 30) + 10,
            (generate_series % 50) + 20,
            (generate_series % 100) + 50,
            60.0 + (generate_series % 40),
            50.0 + (generate_series % 50),
            70.0 + (generate_series % 30),
            30000 + generate_series * 1000,
            NOW() - INTERVAL '1 hour',
            NOW() - INTERVAL '30 minutes'
        FROM generate_series(1, 50);  -- 50 repositories
        
        -- Update pattern counts in repository analysis
        UPDATE repository_analysis 
        SET patterns_detected = (
            SELECT COUNT(*) 
            FROM pattern_results 
            WHERE pattern_results.repository_id = repository_analysis.repository_id
        )
        WHERE repository_id LIKE 'test-perf-%';
        """
        
        async with performance_db_manager.get_session() as session:
            await session.execute(text(setup_sql))
            await session.commit()
        
        print("Large dataset setup completed.")
        yield
        
        # Cleanup after tests
        async with performance_db_manager.get_session() as session:
            await session.execute(text("DELETE FROM pattern_results WHERE repository_id LIKE 'test-perf-%'"))
            await session.execute(text("DELETE FROM repository_analysis WHERE repository_id LIKE 'test-perf-%'"))
            await session.commit()
    
    async def test_execution_plan_analysis(self, performance_db_manager: DatabaseManager, large_dataset_setup):
        """Test query execution plan analysis for optimization."""
        
        # Define test queries with different complexity levels
        test_queries = [
            {
                'name': 'simple_filter',
                'query': """
                    SELECT * FROM pattern_results 
                    WHERE repository_id = 'test-perf-repo-1'
                """,
                'expected_plan_features': ['Index Scan', 'Seq Scan']
            },
            {
                'name': 'compound_filter',
                'query': """
                    SELECT * FROM pattern_results 
                    WHERE repository_id LIKE 'test-perf-repo-%' 
                    AND confidence > 0.8 
                    AND severity IN ('HIGH', 'CRITICAL')
                """,
                'expected_plan_features': ['Index Scan', 'Filter', 'Bitmap']
            },
            {
                'name': 'aggregation_query',
                'query': """
                    SELECT 
                        pattern_type,
                        severity,
                        COUNT(*) as count,
                        AVG(confidence) as avg_confidence,
                        MAX(confidence) as max_confidence
                    FROM pattern_results 
                    WHERE repository_id LIKE 'test-perf-repo-%'
                    GROUP BY pattern_type, severity
                    ORDER BY count DESC
                """,
                'expected_plan_features': ['GroupAggregate', 'Sort', 'Hash']
            },
            {
                'name': 'complex_join',
                'query': """
                    SELECT 
                        ra.repository_name,
                        ra.quality_score,
                        COUNT(pr.id) as pattern_count,
                        AVG(pr.confidence) as avg_pattern_confidence
                    FROM repository_analysis ra
                    JOIN pattern_results pr ON ra.repository_id = pr.repository_id
                    WHERE ra.repository_id LIKE 'test-perf-%'
                    AND ra.quality_score > 70
                    AND pr.confidence > 0.7
                    GROUP BY ra.repository_id, ra.repository_name, ra.quality_score
                    HAVING COUNT(pr.id) > 500
                    ORDER BY pattern_count DESC
                    LIMIT 20
                """,
                'expected_plan_features': ['Hash Join', 'Nested Loop', 'GroupAggregate', 'Sort', 'Limit']
            },
            {
                'name': 'window_function',
                'query': """
                    SELECT 
                        repository_id,
                        pattern_name,
                        confidence,
                        ROW_NUMBER() OVER (PARTITION BY repository_id ORDER BY confidence DESC) as rank,
                        AVG(confidence) OVER (PARTITION BY repository_id) as repo_avg_confidence
                    FROM pattern_results 
                    WHERE repository_id LIKE 'test-perf-repo-%'
                    AND pattern_type = 'DESIGN_PATTERN'
                """,
                'expected_plan_features': ['WindowAgg', 'Sort', 'Partition']
            }
        ]
        
        execution_plan_results = {}
        
        for query_test in test_queries:
            print(f"\nAnalyzing execution plan for: {query_test['name']}")
            
            async with performance_db_manager.get_session() as session:
                # Get execution plan
                plan_query = f"EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON) {query_test['query']}"
                result = await session.execute(text(plan_query))
                plan_data = result.fetchone()[0]
                
                # Extract key metrics from plan
                plan_info = self._analyze_execution_plan(plan_data[0])
                
                # Execute query to measure actual performance
                start_time = time.time()
                result = await session.execute(text(query_test['query']))
                rows = result.fetchall()
                execution_time = time.time() - start_time
                
                execution_plan_results[query_test['name']] = {
                    'plan_info': plan_info,
                    'execution_time': execution_time,
                    'row_count': len(rows),
                    'query': query_test['query']
                }
                
                print(f"  Execution Time: {execution_time:.3f}s")
                print(f"  Rows Returned: {len(rows)}")
                print(f"  Total Cost: {plan_info.get('total_cost', 'N/A')}")
                print(f"  Planning Time: {plan_info.get('planning_time', 'N/A')}ms")
                print(f"  Execution Time (DB): {plan_info.get('execution_time', 'N/A')}ms")
                
                # Check for expected plan features
                plan_text = json.dumps(plan_data).lower()
                found_features = []
                for feature in query_test['expected_plan_features']:
                    if feature.lower() in plan_text:
                        found_features.append(feature)
                
                print(f"  Plan Features Found: {found_features}")
        
        # Performance assertions
        for query_name, results in execution_plan_results.items():
            # All queries should complete within reasonable time
            assert results['execution_time'] < 5.0, f"{query_name} took too long: {results['execution_time']:.3f}s"
            
            # Complex queries should return reasonable result sets
            if query_name in ['aggregation_query', 'complex_join']:
                assert results['row_count'] > 0, f"{query_name} returned no results"
                assert results['row_count'] < 10000, f"{query_name} returned too many results: {results['row_count']}"
        
        # Relative performance analysis
        simple_time = execution_plan_results['simple_filter']['execution_time'] 
        complex_time = execution_plan_results['complex_join']['execution_time']
        
        print(f"\nPerformance Analysis:")
        print(f"Simple query time: {simple_time:.3f}s")
        print(f"Complex query time: {complex_time:.3f}s")
        print(f"Complexity ratio: {complex_time / simple_time:.1f}x")
        
        # Complex queries should not be more than 50x slower than simple ones
        complexity_ratio = complex_time / simple_time if simple_time > 0 else float('inf')
        assert complexity_ratio < 50, f"Complex query too slow compared to simple: {complexity_ratio:.1f}x"
    
    def _analyze_execution_plan(self, plan_node: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze PostgreSQL execution plan and extract key metrics."""
        plan_info = {
            'node_type': plan_node.get('Node Type'),
            'total_cost': plan_node.get('Total Cost'),
            'startup_cost': plan_node.get('Startup Cost'),
            'actual_time': plan_node.get('Actual Total Time'),
            'actual_rows': plan_node.get('Actual Rows'),
            'planning_time': plan_node.get('Planning Time'),
            'execution_time': plan_node.get('Execution Time'),
            'buffers_hit': plan_node.get('Buffers', {}).get('Hit', 0),
            'buffers_read': plan_node.get('Buffers', {}).get('Read', 0),
            'buffers_written': plan_node.get('Buffers', {}).get('Written', 0)
        }
        
        # Extract planning and execution time from root node
        if 'Planning Time' in plan_node:
            plan_info['planning_time'] = plan_node['Planning Time']
        if 'Execution Time' in plan_node:
            plan_info['execution_time'] = plan_node['Execution Time']
        
        return plan_info
    
    async def test_index_performance_validation(self, performance_db_manager: DatabaseManager, large_dataset_setup):
        """Test index performance and utilization."""
        
        # Test queries that should use different indexes
        index_test_queries = [
            {
                'name': 'repository_id_index',
                'query': "SELECT * FROM pattern_results WHERE repository_id = 'test-perf-repo-5'",
                'expected_index': 'idx_pattern_detection_main'
            },
            {
                'name': 'confidence_range',
                'query': "SELECT * FROM pattern_results WHERE confidence BETWEEN 0.8 AND 1.0",
                'expected_index': 'idx_pattern_confidence'
            },
            {
                'name': 'language_filter',
                'query': "SELECT * FROM pattern_results WHERE language = 'python' AND detection_date >= CURRENT_DATE - INTERVAL '7 days'",
                'expected_index': 'idx_pattern_language_date'
            },
            {
                'name': 'compound_search',
                'query': """
                    SELECT * FROM pattern_results 
                    WHERE repository_id LIKE 'test-perf-%' 
                    AND pattern_type = 'SECURITY_ISSUE' 
                    AND detection_date >= CURRENT_DATE - INTERVAL '1 day'
                """,
                'expected_index': 'idx_pattern_detection_main'
            }
        ]
        
        index_performance_results = {}
        
        for test in index_test_queries:
            print(f"\nTesting index performance: {test['name']}")
            
            async with performance_db_manager.get_session() as session:
                # Test with indexes enabled (normal)
                start_time = time.time()
                result = await session.execute(text(test['query']))
                rows_with_index = result.fetchall()
                time_with_index = time.time() - start_time
                
                # Get execution plan to verify index usage
                plan_result = await session.execute(text(f"EXPLAIN (FORMAT JSON) {test['query']}"))
                plan_data = plan_result.fetchone()[0]
                plan_text = json.dumps(plan_data).lower()
                
                # Check if expected index is mentioned in plan
                index_used = test['expected_index'].lower() in plan_text or 'index' in plan_text
                
                index_performance_results[test['name']] = {
                    'execution_time': time_with_index,
                    'row_count': len(rows_with_index),
                    'index_used': index_used,
                    'plan_text': plan_text
                }
                
                print(f"  Execution Time: {time_with_index:.3f}s")
                print(f"  Rows Returned: {len(rows_with_index)}")
                print(f"  Index Used: {'Yes' if index_used else 'No'}")
        
        # Verify index performance
        for test_name, results in index_performance_results.items():
            # All queries should complete quickly with proper indexes
            assert results['execution_time'] < 1.0, f"{test_name} too slow: {results['execution_time']:.3f}s"
            
            # Most queries should use indexes (some might use seq scan if result set is large)
            if results['row_count'] < 10000:  # Only check index usage for selective queries
                print(f"Checking index usage for {test_name}: {results['index_used']}")
                # Note: Index usage depends on query selectivity and data distribution
        
        print(f"\nIndex Performance Summary:")
        for test_name, results in index_performance_results.items():
            print(f"  {test_name}: {results['execution_time']:.3f}s, {results['row_count']} rows, Index: {'Yes' if results['index_used'] else 'No'}")


@pytest.mark.integration
@pytest.mark.asyncio
class TestLargeDatasetQueryPerformance:
    """Test query performance with large datasets."""
    
    @pytest.fixture
    def database_url(self):
        return os.getenv(
            "TEST_DATABASE_URL",
            "postgresql://postgres:INSECURE_PASSWORD@localhost:5432/pattern_mining_test"
        )
    
    @pytest.fixture
    async def large_dataset_manager(self, database_url):
        """Create database manager for large dataset testing."""
        config = get_database_config()
        config.database_url = database_url
        config.pool_size = 15
        config.max_overflow = 25
        
        manager = DatabaseManager(config)
        await manager.initialize()
        yield manager
        await manager.close()
    
    async def test_large_dataset_scan_performance(self, large_dataset_manager: DatabaseManager):
        """Test performance of queries that scan large datasets."""
        
        # Create dataset with 100K records for comprehensive testing
        print("Creating large dataset (100K records)...")
        
        async with large_dataset_manager.get_session() as session:
            # Clean existing data
            await session.execute(text("DELETE FROM pattern_results WHERE repository_id LIKE 'test-large-%'"))
            
            # Create large dataset in batches for better performance
            batch_size = 10000
            total_records = 100000
            
            for batch_start in range(0, total_records, batch_size):
                batch_end = min(batch_start + batch_size, total_records)
                batch_sql = f"""
                INSERT INTO pattern_results (
                    detection_id, repository_id, file_path, pattern_id, pattern_name,
                    pattern_type, pattern_category, severity, confidence, confidence_level,
                    line_start, line_end, detection_method, language
                )
                SELECT 
                    'large-pattern-' || generate_series,
                    'test-large-repo-' || ((generate_series - 1) / 2000 + 1),
                    'src/large/file_' || generate_series || '.py',
                    'large-pattern-' || generate_series,
                    'Large Dataset Pattern ' || generate_series,
                    CASE (generate_series % 4)
                        WHEN 0 THEN 'DESIGN_PATTERN'
                        WHEN 1 THEN 'SECURITY_ISSUE'
                        WHEN 2 THEN 'PERFORMANCE_ISSUE'
                        ELSE 'CODE_SMELL'
                    END,
                    'structural',
                    CASE (generate_series % 4)
                        WHEN 0 THEN 'CRITICAL'
                        WHEN 1 THEN 'HIGH'
                        WHEN 2 THEN 'MEDIUM'
                        ELSE 'LOW'
                    END,
                    0.3 + (generate_series % 70) * 0.01,
                    'medium',
                    generate_series,
                    generate_series + 10,
                    'ML_INFERENCE',
                    CASE (generate_series % 3)
                        WHEN 0 THEN 'python'
                        WHEN 1 THEN 'javascript'
                        ELSE 'typescript'
                    END
                FROM generate_series({batch_start + 1}, {batch_end});
                """
                
                await session.execute(text(batch_sql))
                print(f"  Inserted batch: {batch_start + 1}-{batch_end}")
            
            await session.commit()
        
        print("Large dataset creation completed.")
        
        # Test various query patterns on large dataset
        large_dataset_queries = [
            {
                'name': 'full_table_count',
                'query': "SELECT COUNT(*) FROM pattern_results WHERE repository_id LIKE 'test-large-%'",
                'expected_result_range': (99000, 101000)
            },
            {
                'name': 'selective_filter',
                'query': "SELECT * FROM pattern_results WHERE repository_id = 'test-large-repo-1' AND confidence > 0.9",
                'expected_result_range': (0, 1000)
            },
            {
                'name': 'range_scan',
                'query': "SELECT * FROM pattern_results WHERE repository_id LIKE 'test-large-%' AND confidence BETWEEN 0.8 AND 0.9",
                'expected_result_range': (5000, 15000)
            },
            {
                'name': 'aggregation_by_type',
                'query': """
                    SELECT 
                        pattern_type, 
                        COUNT(*) as count,
                        AVG(confidence) as avg_confidence,
                        MIN(confidence) as min_confidence,
                        MAX(confidence) as max_confidence
                    FROM pattern_results 
                    WHERE repository_id LIKE 'test-large-%'
                    GROUP BY pattern_type
                    ORDER BY count DESC
                """,
                'expected_result_range': (3, 5)  # Should have 4 pattern types
            },
            {
                'name': 'top_patterns',
                'query': """
                    SELECT 
                        repository_id,
                        pattern_type,
                        COUNT(*) as pattern_count
                    FROM pattern_results 
                    WHERE repository_id LIKE 'test-large-%'
                    AND confidence > 0.7
                    GROUP BY repository_id, pattern_type
                    ORDER BY pattern_count DESC
                    LIMIT 50
                """,
                'expected_result_range': (20, 60)
            },
            {
                'name': 'percentile_analysis',
                'query': """
                    SELECT 
                        pattern_type,
                        PERCENTILE_CONT(0.25) WITHIN GROUP (ORDER BY confidence) as q1,
                        PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY confidence) as median,
                        PERCENTILE_CONT(0.75) WITHIN GROUP (ORDER BY confidence) as q3,
                        PERCENTILE_CONT(0.95) WITHIN GROUP (ORDER BY confidence) as p95
                    FROM pattern_results 
                    WHERE repository_id LIKE 'test-large-%'
                    GROUP BY pattern_type
                """,
                'expected_result_range': (3, 5)
            }
        ]
        
        performance_results = {}
        
        for query_test in large_dataset_queries:
            print(f"\nTesting large dataset query: {query_test['name']}")
            
            # Run query multiple times to get consistent measurements
            execution_times = []
            for run in range(3):
                async with large_dataset_manager.get_session() as session:
                    start_time = time.time()
                    result = await session.execute(text(query_test['query']))
                    rows = result.fetchall()
                    execution_time = time.time() - start_time
                    execution_times.append(execution_time)
            
            avg_time = statistics.mean(execution_times)
            min_time = min(execution_times)
            max_time = max(execution_times)
            
            performance_results[query_test['name']] = {
                'avg_execution_time': avg_time,
                'min_execution_time': min_time,
                'max_execution_time': max_time,
                'row_count': len(rows),
                'execution_times': execution_times
            }
            
            print(f"  Avg Execution Time: {avg_time:.3f}s")
            print(f"  Min/Max Time: {min_time:.3f}s / {max_time:.3f}s")
            print(f"  Rows Returned: {len(rows)}")
            
            # Verify result count is in expected range
            expected_min, expected_max = query_test['expected_result_range']
            assert expected_min <= len(rows) <= expected_max, f"{query_test['name']} returned {len(rows)} rows, expected {expected_min}-{expected_max}"
        
        # Performance assertions for large dataset
        
        # Full table count should complete within reasonable time
        assert performance_results['full_table_count']['avg_execution_time'] < 2.0, "Full table count too slow"
        
        # Selective queries should be fast
        assert performance_results['selective_filter']['avg_execution_time'] < 0.5, "Selective filter too slow"
        
        # Aggregations should complete within reasonable time
        assert performance_results['aggregation_by_type']['avg_execution_time'] < 3.0, "Aggregation too slow"
        
        # Complex analytical queries should complete within acceptable time
        assert performance_results['percentile_analysis']['avg_execution_time'] < 5.0, "Percentile analysis too slow"
        
        print(f"\nLarge Dataset Performance Summary:")
        for query_name, results in performance_results.items():
            throughput = results['row_count'] / results['avg_execution_time'] if results['avg_execution_time'] > 0 else 0
            print(f"  {query_name}: {results['avg_execution_time']:.3f}s avg, {results['row_count']} rows, {throughput:.0f} rows/sec")
        
        # Cleanup large dataset
        async with large_dataset_manager.get_session() as session:
            await session.execute(text("DELETE FROM pattern_results WHERE repository_id LIKE 'test-large-%'"))
            await session.commit()
        
        print("Large dataset cleanup completed.")
    
    async def test_pagination_performance(self, large_dataset_manager: DatabaseManager):
        """Test performance of pagination queries on large datasets."""
        
        # Setup moderate dataset for pagination testing
        dataset_size = 20000
        
        async with large_dataset_manager.get_session() as session:
            await session.execute(text("DELETE FROM pattern_results WHERE repository_id LIKE 'test-pagination-%'"))
            
            # Create dataset for pagination testing
            pagination_setup_sql = f"""
            INSERT INTO pattern_results (
                detection_id, repository_id, file_path, pattern_id, pattern_name,
                pattern_type, pattern_category, severity, confidence, confidence_level,
                line_start, line_end, detection_method, language
            )
            SELECT 
                'pagination-pattern-' || generate_series,
                'test-pagination-repo',
                'src/pagination/file_' || generate_series || '.py',
                'pagination-pattern-' || generate_series,
                'Pagination Pattern ' || generate_series,
                'DESIGN_PATTERN',
                'structural',
                'MEDIUM',
                0.5 + (generate_series % 50) * 0.01,
                'medium',
                generate_series,
                generate_series + 5,
                'ML_INFERENCE',
                'python'
            FROM generate_series(1, {dataset_size});
            """
            
            await session.execute(text(pagination_setup_sql))
            await session.commit()
        
        # Test pagination performance at different offsets
        page_size = 100
        test_offsets = [0, 1000, 5000, 10000, 15000]  # Test early, middle, and late pages
        
        pagination_results = {}
        
        for offset in test_offsets:
            pagination_query = f"""
            SELECT * FROM pattern_results 
            WHERE repository_id = 'test-pagination-repo'
            ORDER BY confidence DESC, detection_id
            LIMIT {page_size} OFFSET {offset}
            """
            
            # Test pagination performance
            execution_times = []
            for run in range(3):
                async with large_dataset_manager.get_session() as session:
                    start_time = time.time()
                    result = await session.execute(text(pagination_query))
                    rows = result.fetchall()
                    execution_time = time.time() - start_time
                    execution_times.append(execution_time)
            
            avg_time = statistics.mean(execution_times)
            
            pagination_results[offset] = {
                'avg_execution_time': avg_time,
                'row_count': len(rows),
                'page_number': offset // page_size + 1
            }
            
            print(f"Page {offset // page_size + 1} (offset {offset}): {avg_time:.3f}s, {len(rows)} rows")
        
        # Analyze pagination performance patterns
        first_page_time = pagination_results[0]['avg_execution_time']
        last_page_time = pagination_results[max(test_offsets)]['avg_execution_time']
        
        print(f"\nPagination Performance Analysis:")
        print(f"First Page Time: {first_page_time:.3f}s")
        print(f"Last Page Time: {last_page_time:.3f}s")
        print(f"Performance Degradation: {last_page_time / first_page_time:.1f}x")
        
        # Performance assertions for pagination
        for offset, results in pagination_results.items():
            # All pages should return correct number of rows
            expected_rows = min(page_size, max(0, dataset_size - offset))
            assert results['row_count'] == expected_rows, f"Page at offset {offset} returned {results['row_count']} rows, expected {expected_rows}"
            
            # All pages should complete within reasonable time
            assert results['avg_execution_time'] < 1.0, f"Page at offset {offset} too slow: {results['avg_execution_time']:.3f}s"
        
        # Pagination performance should not degrade significantly
        performance_degradation = last_page_time / first_page_time if first_page_time > 0 else 1
        assert performance_degradation < 10, f"Pagination performance degrades too much: {performance_degradation:.1f}x"
        
        # Cleanup pagination test data
        async with large_dataset_manager.get_session() as session:
            await session.execute(text("DELETE FROM pattern_results WHERE repository_id LIKE 'test-pagination-%'"))
            await session.commit()


@pytest.mark.integration
@pytest.mark.asyncio
class TestConcurrentQueryPerformance:
    """Test query performance under concurrent load."""
    
    @pytest.fixture
    def database_url(self):
        return os.getenv(
            "TEST_DATABASE_URL",
            "postgresql://postgres:INSECURE_PASSWORD@localhost:5432/pattern_mining_test"
        )
    
    @pytest.fixture
    async def concurrent_db_manager(self, database_url):
        """Create database manager for concurrent testing."""
        config = get_database_config()
        config.database_url = database_url
        config.pool_size = 20  # Large pool for concurrency testing
        config.max_overflow = 30
        
        manager = DatabaseManager(config)
        await manager.initialize()
        yield manager
        await manager.close()
    
    @pytest.fixture
    async def concurrent_test_data(self, concurrent_db_manager):
        """Setup data for concurrent testing."""
        async with concurrent_db_manager.get_session() as session:
            await session.execute(text("DELETE FROM pattern_results WHERE repository_id LIKE 'test-concurrent-%'"))
            
            # Create moderate dataset for concurrent testing
            concurrent_setup_sql = """
            INSERT INTO pattern_results (
                detection_id, repository_id, file_path, pattern_id, pattern_name,
                pattern_type, pattern_category, severity, confidence, confidence_level,
                line_start, line_end, detection_method, language
            )
            SELECT 
                'concurrent-pattern-' || generate_series,
                'test-concurrent-repo-' || ((generate_series - 1) / 1000 + 1),
                'src/concurrent/file_' || generate_series || '.py',
                'concurrent-pattern-' || generate_series,
                'Concurrent Pattern ' || generate_series,
                CASE (generate_series % 3)
                    WHEN 0 THEN 'DESIGN_PATTERN'
                    WHEN 1 THEN 'SECURITY_ISSUE'
                    ELSE 'PERFORMANCE_ISSUE'
                END,
                'structural',
                CASE (generate_series % 3)
                    WHEN 0 THEN 'HIGH'
                    WHEN 1 THEN 'MEDIUM'
                    ELSE 'LOW'
                END,
                0.5 + (generate_series % 50) * 0.01,
                'medium',
                generate_series,
                generate_series + 10,
                'ML_INFERENCE',
                'python'
            FROM generate_series(1, 15000);
            """
            
            await session.execute(text(concurrent_setup_sql))
            await session.commit()
        
        yield
        
        # Cleanup
        async with concurrent_db_manager.get_session() as session:
            await session.execute(text("DELETE FROM pattern_results WHERE repository_id LIKE 'test-concurrent-%'"))
            await session.commit()
    
    async def test_concurrent_read_performance(self, concurrent_db_manager: DatabaseManager, concurrent_test_data):
        """Test performance of concurrent read operations."""
        
        # Define different types of read queries
        read_queries = [
            {
                'name': 'simple_select',
                'query': "SELECT * FROM pattern_results WHERE repository_id = 'test-concurrent-repo-{}' LIMIT 100",
                'weight': 0.4  # 40% of queries
            },
            {
                'name': 'filtered_select',
                'query': "SELECT * FROM pattern_results WHERE repository_id LIKE 'test-concurrent-%' AND confidence > 0.{} ORDER BY confidence DESC LIMIT 50",
                'weight': 0.3  # 30% of queries
            },
            {
                'name': 'aggregation',
                'query': "SELECT pattern_type, COUNT(*), AVG(confidence) FROM pattern_results WHERE repository_id LIKE 'test-concurrent-%' GROUP BY pattern_type",
                'weight': 0.2  # 20% of queries
            },
            {
                'name': 'complex_filter',
                'query': """
                    SELECT repository_id, pattern_type, severity, COUNT(*) as count
                    FROM pattern_results 
                    WHERE repository_id LIKE 'test-concurrent-%' 
                    AND confidence BETWEEN 0.{} AND 0.{}
                    GROUP BY repository_id, pattern_type, severity
                    HAVING COUNT(*) > 10
                    ORDER BY count DESC
                """,
                'weight': 0.1  # 10% of queries
            }
        ]
        
        async def execute_concurrent_reads(worker_id: int, num_queries: int):
            """Execute multiple read queries concurrently."""
            query_results = []
            
            for query_num in range(num_queries):
                # Select query type based on weights
                query_choice = random.choices(read_queries, weights=[q['weight'] for q in read_queries])[0]
                
                # Parameterize query
                if query_choice['name'] == 'simple_select':
                    query = query_choice['query'].format(random.randint(1, 15))
                elif query_choice['name'] == 'filtered_select':
                    query = query_choice['query'].format(random.randint(70, 90))
                elif query_choice['name'] == 'aggregation':
                    query = query_choice['query']
                else:  # complex_filter
                    min_conf = random.randint(60, 75)
                    max_conf = random.randint(min_conf + 10, 95)
                    query = query_choice['query'].format(min_conf, max_conf)
                
                # Execute query
                start_time = time.time()
                try:
                    async with concurrent_db_manager.get_session() as session:
                        result = await session.execute(text(query))
                        rows = result.fetchall()
                        execution_time = time.time() - start_time
                        
                        query_results.append({
                            'worker_id': worker_id,
                            'query_num': query_num,
                            'query_type': query_choice['name'],
                            'execution_time': execution_time,
                            'row_count': len(rows),
                            'success': True
                        })
                
                except Exception as e:
                    execution_time = time.time() - start_time
                    query_results.append({
                        'worker_id': worker_id,
                        'query_num': query_num,
                        'query_type': query_choice['name'],
                        'execution_time': execution_time,
                        'error': str(e),
                        'success': False
                    })
                
                # Small delay between queries
                await asyncio.sleep(0.01)
            
            return query_results
        
        # Test concurrent read performance with different concurrency levels
        concurrency_levels = [5, 10, 20, 30]
        queries_per_worker = 20
        
        concurrent_results = {}
        
        for concurrency in concurrency_levels:
            print(f"\nTesting concurrent reads with {concurrency} workers...")
            
            start_time = time.time()
            
            # Create concurrent workers
            tasks = [
                execute_concurrent_reads(worker_id, queries_per_worker)
                for worker_id in range(concurrency)
            ]
            
            # Execute all workers concurrently
            worker_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            total_time = time.time() - start_time
            
            # Analyze results
            all_results = []
            for worker_result in worker_results:
                if not isinstance(worker_result, Exception):
                    all_results.extend(worker_result)
            
            successful_queries = [r for r in all_results if r['success']]
            failed_queries = [r for r in all_results if not r['success']]
            
            # Calculate performance metrics
            if successful_queries:
                avg_execution_time = statistics.mean([r['execution_time'] for r in successful_queries])
                p95_execution_time = sorted([r['execution_time'] for r in successful_queries])[int(0.95 * len(successful_queries))]
                total_queries = len(all_results)
                success_rate = len(successful_queries) / total_queries if total_queries > 0 else 0
                throughput = len(successful_queries) / total_time
                
                concurrent_results[concurrency] = {
                    'total_queries': total_queries,
                    'successful_queries': len(successful_queries),
                    'failed_queries': len(failed_queries),
                    'success_rate': success_rate,
                    'avg_execution_time': avg_execution_time,
                    'p95_execution_time': p95_execution_time,
                    'total_time': total_time,
                    'throughput': throughput
                }
                
                print(f"  Total Queries: {total_queries}")
                print(f"  Success Rate: {success_rate:.2%}")
                print(f"  Avg Execution Time: {avg_execution_time:.3f}s")
                print(f"  P95 Execution Time: {p95_execution_time:.3f}s")
                print(f"  Throughput: {throughput:.1f} queries/sec")
        
        # Performance assertions for concurrent reads
        for concurrency, results in concurrent_results.items():
            # Success rate should remain high under concurrent load
            assert results['success_rate'] >= 0.95, f"Low success rate at concurrency {concurrency}: {results['success_rate']:.2%}"
            
            # Average execution time should remain reasonable
            assert results['avg_execution_time'] < 0.5, f"High avg execution time at concurrency {concurrency}: {results['avg_execution_time']:.3f}s"
            
            # P95 should not be excessively high
            assert results['p95_execution_time'] < 2.0, f"High P95 execution time at concurrency {concurrency}: {results['p95_execution_time']:.3f}s"
        
        # Throughput should scale with concurrency (up to a point)
        if len(concurrent_results) >= 2:
            low_concurrency = min(concurrent_results.keys())
            high_concurrency = max(concurrent_results.keys())
            
            low_throughput = concurrent_results[low_concurrency]['throughput']
            high_throughput = concurrent_results[high_concurrency]['throughput']
            
            throughput_improvement = high_throughput / low_throughput if low_throughput > 0 else 1
            
            print(f"\nConcurrency Scaling Analysis:")
            print(f"Low Concurrency ({low_concurrency}): {low_throughput:.1f} queries/sec")
            print(f"High Concurrency ({high_concurrency}): {high_throughput:.1f} queries/sec")
            print(f"Throughput Improvement: {throughput_improvement:.1f}x")
            
            # Should see some throughput improvement with higher concurrency
            assert throughput_improvement > 1.5, f"Poor throughput scaling: {throughput_improvement:.1f}x"
    
    async def test_mixed_read_write_performance(self, concurrent_db_manager: DatabaseManager, concurrent_test_data):
        """Test performance with mixed read and write operations."""
        
        read_write_results = []
        
        async def mixed_workload_worker(worker_id: int, duration_seconds: int = 10):
            """Execute mixed read/write workload."""
            worker_results = []
            end_time = time.time() + duration_seconds
            operation_count = 0
            
            while time.time() < end_time:
                operation_count += 1
                
                # 80% reads, 20% writes
                if random.random() < 0.8:
                    # Read operation
                    query = """
                        SELECT * FROM pattern_results 
                        WHERE repository_id = 'test-concurrent-repo-{}' 
                        AND confidence > 0.{}
                        LIMIT 20
                    """.format(random.randint(1, 15), random.randint(60, 80))
                    
                    start_time = time.time()
                    try:
                        async with concurrent_db_manager.get_session() as session:
                            result = await session.execute(text(query))
                            rows = result.fetchall()
                            execution_time = time.time() - start_time
                            
                            worker_results.append({
                                'worker_id': worker_id,
                                'operation': 'read',
                                'execution_time': execution_time,
                                'row_count': len(rows),
                                'success': True
                            })
                    
                    except Exception as e:
                        execution_time = time.time() - start_time
                        worker_results.append({
                            'worker_id': worker_id,
                            'operation': 'read',
                            'execution_time': execution_time,
                            'error': str(e),
                            'success': False
                        })
                
                else:
                    # Write operation (insert new pattern)
                    insert_query = """
                        INSERT INTO pattern_results (
                            detection_id, repository_id, file_path, pattern_id, pattern_name,
                            pattern_type, pattern_category, severity, confidence, confidence_level,
                            line_start, line_end, detection_method, language
                        ) VALUES (
                            'mixed-workload-{}-{}', 'test-concurrent-repo-mixed', 
                            'src/mixed/file_{}.py', 'mixed-pattern-{}-{}', 'Mixed Pattern {} {}',
                            'DESIGN_PATTERN', 'structural', 'MEDIUM', 0.{}, 'medium',
                            {}, {}, 'ML_INFERENCE', 'python'
                        )
                    """.format(
                        worker_id, operation_count, operation_count, worker_id, operation_count,
                        worker_id, operation_count, random.randint(60, 90),
                        operation_count, operation_count + 10
                    )
                    
                    start_time = time.time()
                    try:
                        async with concurrent_db_manager.get_session() as session:
                            await session.execute(text(insert_query))
                            await session.commit()
                            execution_time = time.time() - start_time
                            
                            worker_results.append({
                                'worker_id': worker_id,
                                'operation': 'write',
                                'execution_time': execution_time,
                                'success': True
                            })
                    
                    except Exception as e:
                        execution_time = time.time() - start_time
                        worker_results.append({
                            'worker_id': worker_id,
                            'operation': 'write',
                            'execution_time': execution_time,
                            'error': str(e),
                            'success': False
                        })
                
                # Small delay between operations
                await asyncio.sleep(0.02)
            
            return worker_results
        
        # Test mixed workload with moderate concurrency
        num_workers = 10
        workload_duration = 10  # seconds
        
        print(f"\nTesting mixed read/write workload with {num_workers} workers for {workload_duration}s...")
        
        start_time = time.time()
        
        # Run mixed workload
        tasks = [
            mixed_workload_worker(worker_id, workload_duration)
            for worker_id in range(num_workers)
        ]
        
        worker_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        total_test_time = time.time() - start_time
        
        # Analyze mixed workload results
        all_operations = []
        for worker_result in worker_results:
            if not isinstance(worker_result, Exception):
                all_operations.extend(worker_result)
        
        successful_operations = [op for op in all_operations if op['success']]
        failed_operations = [op for op in all_operations if not op['success']]
        
        # Separate read and write operations
        read_operations = [op for op in successful_operations if op['operation'] == 'read']
        write_operations = [op for op in successful_operations if op['operation'] == 'write']
        
        print(f"Mixed Workload Results:")
        print(f"  Total Operations: {len(all_operations)}")
        print(f"  Successful Operations: {len(successful_operations)}")
        print(f"  Failed Operations: {len(failed_operations)}")
        print(f"  Success Rate: {len(successful_operations) / len(all_operations) * 100:.1f}%")
        print(f"  Read Operations: {len(read_operations)}")
        print(f"  Write Operations: {len(write_operations)}")
        
        if read_operations:
            avg_read_time = statistics.mean([op['execution_time'] for op in read_operations])
            print(f"  Avg Read Time: {avg_read_time:.3f}s")
        
        if write_operations:
            avg_write_time = statistics.mean([op['execution_time'] for op in write_operations])
            print(f"  Avg Write Time: {avg_write_time:.3f}s")
        
        # Performance assertions for mixed workload
        success_rate = len(successful_operations) / len(all_operations) if all_operations else 0
        assert success_rate >= 0.90, f"Low success rate in mixed workload: {success_rate:.2%}"
        
        # Read operations should remain fast under mixed load
        if read_operations:
            avg_read_time = statistics.mean([op['execution_time'] for op in read_operations])
            assert avg_read_time < 0.2, f"Read operations too slow under mixed load: {avg_read_time:.3f}s"
        
        # Write operations should complete within reasonable time
        if write_operations:
            avg_write_time = statistics.mean([op['execution_time'] for op in write_operations])
            assert avg_write_time < 0.5, f"Write operations too slow under mixed load: {avg_write_time:.3f}s"
        
        # Should maintain good throughput
        total_throughput = len(successful_operations) / total_test_time
        assert total_throughput > 50, f"Low throughput in mixed workload: {total_throughput:.1f} ops/sec"
        
        print(f"  Total Throughput: {total_throughput:.1f} operations/sec")
        
        # Cleanup mixed workload data
        async with concurrent_db_manager.get_session() as session:
            await session.execute(text("DELETE FROM pattern_results WHERE repository_id = 'test-concurrent-repo-mixed'"))
            await session.commit()