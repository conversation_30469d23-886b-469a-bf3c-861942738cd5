"""
Database Integration Tests

Real integration tests for PostgreSQL database operations, testing actual database
connections, pattern storage/retrieval, transaction handling, and connection pool behavior.
"""

import pytest
import asyncio
import os
import uuid
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import json

import asyncpg
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.pool import StaticPool
from sqlalchemy import text, select, insert, update, delete
from sqlalchemy.exc import IntegrityError, OperationalError

from pattern_mining.database.connection import DatabaseManager, get_database
from pattern_mining.database.repositories.pattern_repository import PatternRepository
from pattern_mining.database.repositories.analysis_repository import AnalysisRepository
from pattern_mining.database.models import Pattern, Analysis, Repository
from pattern_mining.models.patterns import PatternType, SeverityLevel, DetectionType
from pattern_mining.config.database import get_database_config


@pytest.mark.integration
@pytest.mark.asyncio
class TestDatabaseConnectionIntegration:
    """Integration tests for database connection management."""
    
    @pytest.fixture
    def database_url(self):
        """Get test database URL from environment or use default."""
        return os.getenv(
            "TEST_DATABASE_URL",
            "postgresql://postgres:INSECURE_PASSWORD@localhost:5432/pattern_mining_test"
        )
    
    @pytest.fixture
    async def db_manager(self, database_url):
        """Create database manager for testing."""
        config = get_database_config()
        config.database_url = database_url
        config.pool_size = 5
        config.max_overflow = 10
        
        manager = DatabaseManager(config)
        await manager.initialize()
        yield manager
        await manager.close()
    
    async def test_database_connection_basic(self, db_manager: DatabaseManager):
        """Test basic database connection."""
        # Test connection acquisition
        async with db_manager.get_session() as session:
            result = await session.execute(text("SELECT 1 as test"))
            row = result.fetchone()
            assert row.test == 1
    
    async def test_database_connection_pool(self, db_manager: DatabaseManager):
        """Test database connection pooling."""
        connections = []
        
        # Acquire multiple connections simultaneously
        async def acquire_connection(connection_id: int):
            async with db_manager.get_session() as session:
                result = await session.execute(text(f"SELECT {connection_id} as id"))
                row = result.fetchone()
                await asyncio.sleep(0.1)  # Hold connection briefly
                return row.id
        
        # Create multiple concurrent connections
        tasks = [acquire_connection(i) for i in range(10)]
        results = await asyncio.gather(*tasks)
        
        # All connections should succeed
        assert results == list(range(10))
    
    async def test_database_transaction_handling(self, db_manager: DatabaseManager):
        """Test database transaction management."""
        test_table = "test_transactions"
        
        async with db_manager.get_session() as session:
            # Create test table
            await session.execute(text(f"""
                CREATE TABLE IF NOT EXISTS {test_table} (
                    id SERIAL PRIMARY KEY,
                    value TEXT NOT NULL
                )
            """))
            await session.commit()
        
        # Test successful transaction
        async with db_manager.get_session() as session:
            await session.execute(
                text(f"INSERT INTO {test_table} (value) VALUES ('test1')")
            )
            await session.commit()
            
            # Verify insertion
            result = await session.execute(
                text(f"SELECT value FROM {test_table} WHERE value = 'test1'")
            )
            row = result.fetchone()
            assert row.value == "test1"
        
        # Test transaction rollback
        try:
            async with db_manager.get_session() as session:
                await session.execute(
                    text(f"INSERT INTO {test_table} (value) VALUES ('test2')")
                )
                # Force an error to trigger rollback
                await session.execute(text("INSERT INTO non_existent_table VALUES (1)"))
                await session.commit()
        except Exception:
            pass  # Expected to fail
        
        # Verify rollback - test2 should not exist
        async with db_manager.get_session() as session:
            result = await session.execute(
                text(f"SELECT value FROM {test_table} WHERE value = 'test2'")
            )
            row = result.fetchone()
            assert row is None
        
        # Cleanup
        async with db_manager.get_session() as session:
            await session.execute(text(f"DROP TABLE {test_table}"))
            await session.commit()
    
    async def test_database_connection_recovery(self, database_url):
        """Test database connection recovery after connection loss."""
        # Create initial connection
        config = get_database_config()
        config.database_url = database_url
        config.pool_recycle = 1  # Short recycle time for testing
        
        manager = DatabaseManager(config)
        await manager.initialize()
        
        try:
            # Test initial connection
            async with manager.get_session() as session:
                result = await session.execute(text("SELECT 'initial' as test"))
                row = result.fetchone()
                assert row.test == "initial"
            
            # Simulate connection recovery (connections should be recycled)
            await asyncio.sleep(2)  # Wait for recycle
            
            # Test connection after potential recycle
            async with manager.get_session() as session:
                result = await session.execute(text("SELECT 'recovered' as test"))
                row = result.fetchone()
                assert row.test == "recovered"
        
        finally:
            await manager.close()
    
    async def test_database_concurrent_operations(self, db_manager: DatabaseManager):
        """Test concurrent database operations."""
        test_table = "concurrent_test"
        
        # Setup test table
        async with db_manager.get_session() as session:
            await session.execute(text(f"""
                CREATE TABLE IF NOT EXISTS {test_table} (
                    id SERIAL PRIMARY KEY,
                    counter INT NOT NULL DEFAULT 0
                )
            """))
            await session.execute(text(f"INSERT INTO {test_table} (counter) VALUES (0)"))
            await session.commit()
        
        # Concurrent counter updates
        async def increment_counter():
            async with db_manager.get_session() as session:
                result = await session.execute(
                    text(f"SELECT counter FROM {test_table} WHERE id = 1")
                )
                current = result.fetchone().counter
                
                await asyncio.sleep(0.01)  # Simulate processing time
                
                await session.execute(
                    text(f"UPDATE {test_table} SET counter = {current + 1} WHERE id = 1")
                )
                await session.commit()
                return current + 1
        
        # Run concurrent updates
        tasks = [increment_counter() for _ in range(5)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Verify final counter value
        async with db_manager.get_session() as session:
            result = await session.execute(
                text(f"SELECT counter FROM {test_table} WHERE id = 1")
            )
            final_counter = result.fetchone().counter
            
            # Due to race conditions, final value may be less than 5
            # but should be at least 1
            assert 1 <= final_counter <= 5
        
        # Cleanup
        async with db_manager.get_session() as session:
            await session.execute(text(f"DROP TABLE {test_table}"))
            await session.commit()


@pytest.mark.integration
@pytest.mark.asyncio  
class TestPatternRepositoryIntegration:
    """Integration tests for pattern repository operations."""
    
    @pytest.fixture
    def database_url(self):
        """Get test database URL from environment or use default."""
        return os.getenv(
            "TEST_DATABASE_URL", 
            "postgresql://postgres:INSECURE_PASSWORD@localhost:5432/pattern_mining_test"
        )
    
    @pytest.fixture
    async def pattern_repository(self, database_url):
        """Create pattern repository for testing."""
        config = get_database_config()
        config.database_url = database_url
        
        db_manager = DatabaseManager(config)
        await db_manager.initialize()
        
        repo = PatternRepository(db_manager)
        yield repo
        
        await db_manager.close()
    
    @pytest.fixture
    def sample_pattern_data(self):
        """Create sample pattern data for testing."""
        return {
            "id": str(uuid.uuid4()),
            "repository_id": "test-repo-123",
            "pattern_name": "Iterator Pattern",
            "pattern_type": PatternType.DESIGN_PATTERN.value,
            "severity": SeverityLevel.LOW.value,
            "confidence": 0.92,
            "file_path": "src/main.py",
            "line_number": 10,
            "column_number": 5,
            "end_line_number": 15,
            "end_column_number": 20,
            "description": "Iterator pattern implementation in main module",
            "detection_method": DetectionType.ML_INFERENCE.value,
            "context": {
                "function_name": "process_items",
                "class_name": "ItemProcessor"
            },
            "metadata": {
                "model_version": "1.0.0",
                "confidence_factors": ["ast_structure", "naming_patterns"]
            },
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        }
    
    async def test_create_pattern_integration(
        self,
        pattern_repository: PatternRepository,
        sample_pattern_data: Dict[str, Any]
    ):
        """Test creating patterns in database."""
        # Create pattern
        pattern_id = await pattern_repository.create_pattern(sample_pattern_data)
        assert pattern_id == sample_pattern_data["id"]
        
        # Verify pattern was created
        pattern = await pattern_repository.get_pattern_by_id(pattern_id)
        assert pattern is not None
        assert pattern["pattern_name"] == sample_pattern_data["pattern_name"]
        assert pattern["repository_id"] == sample_pattern_data["repository_id"]
        assert pattern["confidence"] == sample_pattern_data["confidence"]
    
    async def test_get_patterns_by_repository_integration(
        self,
        pattern_repository: PatternRepository,
        sample_pattern_data: Dict[str, Any]
    ):
        """Test retrieving patterns by repository."""
        repository_id = "test-repo-patterns"
        
        # Create multiple patterns for the same repository
        patterns_data = []
        for i in range(3):
            pattern_data = sample_pattern_data.copy()
            pattern_data["id"] = str(uuid.uuid4())
            pattern_data["repository_id"] = repository_id
            pattern_data["pattern_name"] = f"Pattern {i+1}"
            pattern_data["line_number"] = 10 + i * 5
            patterns_data.append(pattern_data)
            
            await pattern_repository.create_pattern(pattern_data)
        
        # Retrieve patterns by repository
        result = await pattern_repository.get_patterns_by_repository(
            repository_id=repository_id,
            limit=10,
            offset=0
        )
        
        assert "patterns" in result
        assert "total" in result
        assert result["total"] == 3
        assert len(result["patterns"]) == 3
        
        # Verify pattern data
        retrieved_patterns = result["patterns"]
        for pattern in retrieved_patterns:
            assert pattern["repository_id"] == repository_id
            assert pattern["pattern_name"] in ["Pattern 1", "Pattern 2", "Pattern 3"]
    
    async def test_update_pattern_integration(
        self,
        pattern_repository: PatternRepository,
        sample_pattern_data: Dict[str, Any]
    ):
        """Test updating patterns in database."""
        # Create pattern
        pattern_id = await pattern_repository.create_pattern(sample_pattern_data)
        
        # Update pattern
        updates = {
            "confidence": 0.95,
            "description": "Updated pattern description",
            "metadata": {"updated": True, "version": "2.0.0"}
        }
        
        success = await pattern_repository.update_pattern(pattern_id, updates)
        assert success is True
        
        # Verify updates
        pattern = await pattern_repository.get_pattern_by_id(pattern_id)
        assert pattern["confidence"] == 0.95
        assert pattern["description"] == "Updated pattern description"
        assert pattern["metadata"]["updated"] is True
        assert pattern["metadata"]["version"] == "2.0.0"
    
    async def test_delete_pattern_integration(
        self,
        pattern_repository: PatternRepository,
        sample_pattern_data: Dict[str, Any]
    ):
        """Test deleting patterns from database."""
        # Create pattern
        pattern_id = await pattern_repository.create_pattern(sample_pattern_data)
        
        # Verify pattern exists
        pattern = await pattern_repository.get_pattern_by_id(pattern_id)
        assert pattern is not None
        
        # Delete pattern
        success = await pattern_repository.delete_pattern(pattern_id)
        assert success is True
        
        # Verify pattern is deleted
        pattern = await pattern_repository.get_pattern_by_id(pattern_id)
        assert pattern is None
    
    async def test_pattern_statistics_integration(
        self,
        pattern_repository: PatternRepository,
        sample_pattern_data: Dict[str, Any]
    ):
        """Test pattern statistics calculation."""
        repository_id = "test-repo-stats"
        
        # Create patterns with different types and severities
        pattern_configs = [
            (PatternType.DESIGN_PATTERN, SeverityLevel.LOW, 0.9),
            (PatternType.DESIGN_PATTERN, SeverityLevel.MEDIUM, 0.8),
            (PatternType.SECURITY_ISSUE, SeverityLevel.HIGH, 0.95),
            (PatternType.SECURITY_ISSUE, SeverityLevel.CRITICAL, 0.98),
            (PatternType.PERFORMANCE_ISSUE, SeverityLevel.MEDIUM, 0.85)
        ]
        
        for i, (pattern_type, severity, confidence) in enumerate(pattern_configs):
            pattern_data = sample_pattern_data.copy()
            pattern_data["id"] = str(uuid.uuid4())
            pattern_data["repository_id"] = repository_id
            pattern_data["pattern_type"] = pattern_type.value
            pattern_data["severity"] = severity.value
            pattern_data["confidence"] = confidence
            pattern_data["line_number"] = 10 + i * 5
            
            await pattern_repository.create_pattern(pattern_data)
        
        # Get statistics
        stats = await pattern_repository.get_pattern_statistics(repository_id)
        
        assert stats["total_patterns"] == 5
        assert stats["by_type"][PatternType.DESIGN_PATTERN.value] == 2
        assert stats["by_type"][PatternType.SECURITY_ISSUE.value] == 2
        assert stats["by_type"][PatternType.PERFORMANCE_ISSUE.value] == 1
        assert stats["by_severity"][SeverityLevel.LOW.value] == 1
        assert stats["by_severity"][SeverityLevel.MEDIUM.value] == 2
        assert stats["by_severity"][SeverityLevel.HIGH.value] == 1
        assert stats["by_severity"][SeverityLevel.CRITICAL.value] == 1
        assert 0.85 <= stats["average_confidence"] <= 0.95
    
    async def test_bulk_pattern_operations_integration(
        self,
        pattern_repository: PatternRepository,
        sample_pattern_data: Dict[str, Any]
    ):
        """Test bulk pattern operations."""
        repository_id = "test-repo-bulk"
        
        # Create multiple pattern data
        patterns_data = []
        for i in range(10):
            pattern_data = sample_pattern_data.copy()
            pattern_data["id"] = str(uuid.uuid4())
            pattern_data["repository_id"] = repository_id
            pattern_data["pattern_name"] = f"Bulk Pattern {i+1}"
            pattern_data["line_number"] = 10 + i * 3
            patterns_data.append(pattern_data)
        
        # Bulk create patterns
        created_ids = await pattern_repository.create_patterns_bulk(patterns_data)
        assert len(created_ids) == 10
        
        # Verify all patterns were created
        result = await pattern_repository.get_patterns_by_repository(
            repository_id=repository_id,
            limit=15,
            offset=0
        )
        
        assert result["total"] == 10
        assert len(result["patterns"]) == 10
        
        # Test bulk deletion
        pattern_ids = [p["id"] for p in patterns_data[:5]]  # Delete first 5
        deleted_count = await pattern_repository.delete_patterns_bulk(pattern_ids)
        assert deleted_count == 5
        
        # Verify deletion
        result = await pattern_repository.get_patterns_by_repository(
            repository_id=repository_id,
            limit=15,
            offset=0
        )
        
        assert result["total"] == 5


@pytest.mark.integration
@pytest.mark.asyncio
class TestAnalysisRepositoryIntegration:
    """Integration tests for analysis repository operations."""
    
    @pytest.fixture
    def database_url(self):
        """Get test database URL from environment or use default."""
        return os.getenv(
            "TEST_DATABASE_URL",
            "postgresql://postgres:INSECURE_PASSWORD@localhost:5432/pattern_mining_test"
        )
    
    @pytest.fixture
    async def analysis_repository(self, database_url):
        """Create analysis repository for testing."""
        config = get_database_config()
        config.database_url = database_url
        
        db_manager = DatabaseManager(config)
        await db_manager.initialize()
        
        repo = AnalysisRepository(db_manager)
        yield repo
        
        await db_manager.close()
    
    @pytest.fixture
    def sample_analysis_data(self):
        """Create sample analysis data for testing."""
        return {
            "id": str(uuid.uuid4()),
            "repository_id": "test-repo-analysis",
            "status": "completed",
            "patterns_detected": 25,
            "files_analyzed": 12,
            "lines_of_code": 5000,
            "quality_score": 85.5,
            "security_score": 92.3,
            "performance_score": 78.9,
            "processing_time_ms": 15000,
            "model_versions": {
                "pattern_detector": "1.0.0",
                "security_analyzer": "1.2.0"
            },
            "configuration": {
                "confidence_threshold": 0.8,
                "enable_ml_models": True,
                "max_patterns_per_file": 50
            },
            "metadata": {
                "analysis_type": "comprehensive",
                "triggered_by": "user_request"
            },
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        }
    
    async def test_create_analysis_integration(
        self,
        analysis_repository: AnalysisRepository,
        sample_analysis_data: Dict[str, Any]
    ):
        """Test creating analysis records in database."""
        # Create analysis
        analysis_id = await analysis_repository.create_analysis(sample_analysis_data)
        assert analysis_id == sample_analysis_data["id"]
        
        # Verify analysis was created
        analysis = await analysis_repository.get_analysis_by_id(analysis_id)
        assert analysis is not None
        assert analysis["repository_id"] == sample_analysis_data["repository_id"]
        assert analysis["status"] == sample_analysis_data["status"]
        assert analysis["patterns_detected"] == sample_analysis_data["patterns_detected"]
        assert analysis["quality_score"] == sample_analysis_data["quality_score"]
    
    async def test_update_analysis_progress_integration(
        self,
        analysis_repository: AnalysisRepository,
        sample_analysis_data: Dict[str, Any]
    ):
        """Test updating analysis progress."""
        # Create analysis
        sample_analysis_data["status"] = "processing"
        sample_analysis_data["patterns_detected"] = 0
        analysis_id = await analysis_repository.create_analysis(sample_analysis_data)
        
        # Update progress
        progress_update = {
            "status": "processing",
            "patterns_detected": 15,
            "files_analyzed": 8,
            "processing_progress": 65.0,
            "current_file": "src/utils.py"
        }
        
        success = await analysis_repository.update_analysis_progress(
            analysis_id, progress_update
        )
        assert success is True
        
        # Verify updates
        analysis = await analysis_repository.get_analysis_by_id(analysis_id)
        assert analysis["status"] == "processing"
        assert analysis["patterns_detected"] == 15
        assert analysis["files_analyzed"] == 8
        assert analysis["processing_progress"] == 65.0
        assert analysis["current_file"] == "src/utils.py"
    
    async def test_complete_analysis_integration(
        self,
        analysis_repository: AnalysisRepository,
        sample_analysis_data: Dict[str, Any]
    ):
        """Test completing analysis with final results."""
        # Create analysis in processing state
        sample_analysis_data["status"] = "processing"
        analysis_id = await analysis_repository.create_analysis(sample_analysis_data)
        
        # Complete analysis
        completion_data = {
            "status": "completed",
            "patterns_detected": 42,
            "files_analyzed": 18,
            "quality_score": 88.2,
            "security_score": 95.1,
            "performance_score": 82.7,
            "processing_time_ms": 22000,
            "completed_at": datetime.utcnow(),
            "summary": {
                "critical_issues": 2,
                "high_issues": 8,
                "medium_issues": 15,
                "low_issues": 17
            }
        }
        
        success = await analysis_repository.complete_analysis(analysis_id, completion_data)
        assert success is True
        
        # Verify completion
        analysis = await analysis_repository.get_analysis_by_id(analysis_id)
        assert analysis["status"] == "completed"
        assert analysis["patterns_detected"] == 42
        assert analysis["quality_score"] == 88.2
        assert analysis["processing_time_ms"] == 22000
        assert "completed_at" in analysis
        assert analysis["summary"]["critical_issues"] == 2
    
    async def test_get_analyses_by_repository_integration(
        self,
        analysis_repository: AnalysisRepository,
        sample_analysis_data: Dict[str, Any]
    ):
        """Test retrieving analyses by repository."""
        repository_id = "test-repo-multi-analysis"
        
        # Create multiple analyses for the same repository
        analyses_data = []
        for i in range(3):
            analysis_data = sample_analysis_data.copy()
            analysis_data["id"] = str(uuid.uuid4())
            analysis_data["repository_id"] = repository_id
            analysis_data["patterns_detected"] = 10 + i * 5
            analysis_data["created_at"] = datetime.utcnow() - timedelta(days=i)
            analyses_data.append(analysis_data)
            
            await analysis_repository.create_analysis(analysis_data)
        
        # Retrieve analyses by repository
        result = await analysis_repository.get_analyses_by_repository(
            repository_id=repository_id,
            limit=10,
            offset=0
        )
        
        assert "analyses" in result
        assert "total" in result
        assert result["total"] == 3
        assert len(result["analyses"]) == 3
        
        # Should be ordered by creation date (newest first)
        analyses = result["analyses"]
        created_dates = [a["created_at"] for a in analyses]
        assert created_dates == sorted(created_dates, reverse=True)
    
    async def test_analysis_metrics_integration(
        self,
        analysis_repository: AnalysisRepository,
        sample_analysis_data: Dict[str, Any]
    ):
        """Test analysis metrics calculation."""
        repository_id = "test-repo-metrics"
        
        # Create analyses with different metrics
        metrics_configs = [
            (85.0, 90.0, 80.0, 25),  # quality, security, performance, patterns
            (78.5, 85.5, 92.0, 18),
            (92.0, 88.0, 75.5, 33),
            (80.0, 95.0, 85.0, 22)
        ]
        
        for i, (quality, security, performance, patterns) in enumerate(metrics_configs):
            analysis_data = sample_analysis_data.copy()
            analysis_data["id"] = str(uuid.uuid4())
            analysis_data["repository_id"] = repository_id
            analysis_data["quality_score"] = quality
            analysis_data["security_score"] = security
            analysis_data["performance_score"] = performance
            analysis_data["patterns_detected"] = patterns
            analysis_data["created_at"] = datetime.utcnow() - timedelta(hours=i)
            
            await analysis_repository.create_analysis(analysis_data)
        
        # Get metrics
        metrics = await analysis_repository.get_repository_metrics(repository_id)
        
        assert metrics["total_analyses"] == 4
        assert metrics["total_patterns_detected"] == sum(p for _, _, _, p in metrics_configs)
        assert 80.0 <= metrics["average_quality_score"] <= 85.0
        assert 85.0 <= metrics["average_security_score"] <= 92.0
        assert 80.0 <= metrics["average_performance_score"] <= 85.0
        assert "latest_analysis" in metrics
        assert metrics["latest_analysis"]["patterns_detected"] == 25  # Most recent
    
    async def test_failed_analysis_handling_integration(
        self,
        analysis_repository: AnalysisRepository,
        sample_analysis_data: Dict[str, Any]
    ):
        """Test handling of failed analyses."""
        # Create analysis
        sample_analysis_data["status"] = "processing"
        analysis_id = await analysis_repository.create_analysis(sample_analysis_data)
        
        # Mark analysis as failed
        failure_data = {
            "status": "failed",
            "error_message": "Gemini API rate limit exceeded",
            "error_code": "RATE_LIMIT_ERROR",
            "failed_at": datetime.utcnow(),
            "retry_count": 3,
            "metadata": {
                "last_successful_file": "src/main.py",
                "files_processed": 5,
                "total_files": 12
            }
        }
        
        success = await analysis_repository.fail_analysis(analysis_id, failure_data)
        assert success is True
        
        # Verify failure
        analysis = await analysis_repository.get_analysis_by_id(analysis_id)
        assert analysis["status"] == "failed"
        assert analysis["error_message"] == "Gemini API rate limit exceeded"
        assert analysis["error_code"] == "RATE_LIMIT_ERROR"
        assert analysis["retry_count"] == 3
        assert "failed_at" in analysis
        assert analysis["metadata"]["files_processed"] == 5