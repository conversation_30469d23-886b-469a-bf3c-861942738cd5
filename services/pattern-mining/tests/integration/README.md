# Integration Tests - Pattern Mining Service

Comprehensive integration testing suite for the Pattern Mining service, designed to validate real service interactions and expose integration failures before production deployment.

## Overview

This integration test suite provides **essential integration testing** for core service dependencies:

- **Gemini API Integration**: Real API connections, rate limiting, error handling
- **Database Integration**: PostgreSQL connections, transactions, connection pooling  
- **Redis Integration**: Cache operations, session management, connection recovery
- **Multi-Service Workflows**: End-to-end pipelines and failure scenarios

## Test Architecture

### Real Service Integration
- **Minimal Mocking**: Tests use real service connections where possible
- **Failure Scenario Testing**: Validates graceful degradation and error recovery
- **Performance Under Load**: Tests concurrent operations and resource pressure
- **Data Consistency**: Verifies consistency across database, cache, and API layers

### Test Categories

#### Core Integration Tests
- `test_gemini_integration.py` - Gemini 2.5 Flash API integration
- `test_database_integration.py` - PostgreSQL database operations  
- `test_redis_integration.py` - Redis cache and session management
- `test_service_workflows.py` - Multi-service end-to-end workflows

#### Enhanced Workflow Tests
- `test_full_workflow.py` - Complete pattern detection workflows with real services

## Quick Start

### Prerequisites
- Docker and Docker Compose
- Python 3.11+
- Access to test database and Redis instance
- Gemini API key for full integration testing

### Environment Setup

```bash
# Set environment variables
export GEMINI_API_KEY_TEST="your-gemini-api-key"
export TEST_DATABASE_URL="postgresql://postgres:password@localhost:5433/pattern_mining_test"
export TEST_REDIS_URL="redis://localhost:6380/0"
```

### Running Tests

#### Using the Integration Test Script (Recommended)
```bash
# Run all integration tests
./run_integration_tests.sh

# Run specific test categories
./run_integration_tests.sh gemini       # Gemini API tests only
./run_integration_tests.sh database     # Database tests only
./run_integration_tests.sh redis        # Redis tests only
./run_integration_tests.sh workflows    # Workflow tests only

# Run with options
./run_integration_tests.sh --verbose    # Verbose output
./run_integration_tests.sh --fast       # Skip slow tests
./run_integration_tests.sh --keep       # Keep containers running
```

#### Using Docker Compose Directly
```bash
# Start test infrastructure
docker-compose -f docker-compose.integration-test.yml up -d postgres-test redis-test

# Run integration tests
docker-compose -f docker-compose.integration-test.yml --profile integration-test up --abort-on-container-exit

# Cleanup
docker-compose -f docker-compose.integration-test.yml down -v
```

#### Using PyTest Directly
```bash
# Run all integration tests
pytest tests/integration/ -m integration

# Run specific test files
pytest tests/integration/test_gemini_integration.py -v
pytest tests/integration/test_database_integration.py -v

# Run with markers
pytest tests/integration/ -m "integration and gemini"
pytest tests/integration/ -m "integration and not slow"
```

## Test Configuration

### Environment Variables
- `GEMINI_API_KEY_TEST` - Gemini API key for testing (required for Gemini tests)
- `TEST_DATABASE_URL` - PostgreSQL database URL for testing
- `TEST_REDIS_URL` - Redis URL for testing
- `PYTEST_TIMEOUT` - Test timeout in seconds (default: 300)

### Test Markers
- `@pytest.mark.integration` - Integration test marker
- `@pytest.mark.slow` - Slow-running tests (>30 seconds)
- `@pytest.mark.gemini` - Tests requiring Gemini API
- `@pytest.mark.database` - Tests requiring database connection
- `@pytest.mark.redis` - Tests requiring Redis connection
- `@pytest.mark.workflow` - End-to-end workflow tests
- `@pytest.mark.real_services` - Tests with minimal mocking

## Test Scenarios

### Gemini API Integration
- **Authentication & Connection**: Validates API key and connection setup
- **Code Analysis**: Tests real pattern detection with various code samples
- **Rate Limiting**: Validates rate limiting behavior and retry logic
- **Error Handling**: Tests timeout, quota limits, and API failures
- **Thinking Mode**: Tests Gemini 2.5 Flash thinking capabilities
- **Batch Processing**: Tests concurrent analysis requests

### Database Integration  
- **Connection Management**: Tests connection pooling and recovery
- **Transaction Handling**: Validates ACID properties and rollbacks
- **Pattern Storage**: Tests pattern CRUD operations
- **Repository Operations**: Tests analysis record management
- **Statistics Calculation**: Tests aggregation and metrics
- **Concurrent Operations**: Tests database under concurrent load

### Redis Integration
- **Connection Recovery**: Tests reconnection after failures
- **Cache Operations**: Tests set, get, delete, and expiration
- **Pattern Caching**: Tests pattern-specific caching strategies
- **Session Management**: Tests session storage and retrieval
- **Memory Pressure**: Tests behavior under memory constraints
- **Concurrent Access**: Tests concurrent cache operations

### Multi-Service Workflows
- **End-to-End Processing**: Complete pattern analysis pipeline
- **Service Failure Recovery**: Tests graceful degradation scenarios
- **Data Consistency**: Validates consistency across services
- **Performance Under Load**: Tests concurrent multi-service operations
- **Error Propagation**: Tests error handling across service boundaries

## Test Data and Fixtures

### Sample Code Patterns
The tests use realistic code samples designed to trigger specific patterns:

- **Singleton Pattern**: Thread-safe singleton implementation
- **Observer Pattern**: Subject-observer relationship
- **Security Issues**: SQL injection, command injection vulnerabilities
- **Performance Issues**: Inefficient algorithms, nested loops
- **Design Patterns**: Factory, builder, strategy patterns

### Database Fixtures
- Pre-configured test database with proper schema
- Sample pattern data with various types and severities
- Repository and analysis records for testing workflows

### Cache Fixtures
- Redis test instance with isolated databases
- Pre-loaded cache data for consistency tests
- Cache strategy configurations for different scenarios

## Performance Benchmarks

### Expected Performance Metrics
- **Gemini API Response**: < 10 seconds for code analysis
- **Database Operations**: < 100ms for CRUD operations
- **Redis Operations**: < 10ms for cache operations
- **End-to-End Workflow**: < 30 seconds for complete analysis

### Load Testing Scenarios
- **Concurrent Requests**: 10+ simultaneous pattern analyses
- **Database Pressure**: 50+ concurrent connections
- **Cache Pressure**: 1000+ cache operations per second
- **Memory Usage**: < 500MB peak memory during tests

## Troubleshooting

### Common Issues

#### Gemini API Tests Skipped
```
GEMINI_API_KEY_TEST not set - skipping Gemini integration tests
```
**Solution**: Set the `GEMINI_API_KEY_TEST` environment variable with a valid API key.

#### Database Connection Failed
```
Connection to database failed
```
**Solution**: Ensure PostgreSQL is running and accessible at the configured URL.

#### Redis Connection Failed
```
Redis connection refused
```
**Solution**: Ensure Redis is running and accessible at the configured URL.

#### Test Timeout
```
Test session timeout after 300 seconds
```
**Solution**: Increase timeout with `--timeout=600` or check service health.

### Debug Mode
Run tests with debug logging:
```bash
pytest tests/integration/ -v -s --log-cli-level=DEBUG
```

### Container Logs
Check service logs:
```bash
docker-compose -f docker-compose.integration-test.yml logs postgres-test
docker-compose -f docker-compose.integration-test.yml logs redis-test
```

## CI/CD Integration

### GitHub Actions Example
```yaml
name: Integration Tests
on: [push, pull_request]

jobs:
  integration-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Run Integration Tests
        env:
          GEMINI_API_KEY_TEST: ${{ secrets.GEMINI_API_KEY_TEST }}
        run: |
          chmod +x ./run_integration_tests.sh
          ./run_integration_tests.sh --verbose
      - name: Upload Test Results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: integration-test-results
          path: |
            test-results/
            coverage/
```

### Jenkins Pipeline Example
```groovy
pipeline {
    agent any
    environment {
        GEMINI_API_KEY_TEST = credentials('gemini-api-key-test')
    }
    stages {
        stage('Integration Tests') {
            steps {
                sh './run_integration_tests.sh --verbose'
            }
            post {
                always {
                    publishTestResults testResultsPattern: 'test-results/*.xml'
                    publishHTML([
                        allowMissing: false,
                        alwaysLinkToLastBuild: true,
                        keepAll: true,
                        reportDir: 'coverage/html',
                        reportFiles: 'index.html',
                        reportName: 'Integration Test Coverage'
                    ])
                }
            }
        }
    }
}
```

## Contributing

### Adding New Integration Tests
1. Create test file in `tests/integration/`
2. Use appropriate markers (`@pytest.mark.integration`, etc.)
3. Follow existing patterns for fixtures and assertions
4. Test both happy path and failure scenarios
5. Add documentation for new test scenarios

### Test Quality Guidelines
- **Real Service Integration**: Minimize mocking, test actual service interactions
- **Failure Scenarios**: Test error conditions and recovery mechanisms
- **Performance Validation**: Include performance assertions where relevant
- **Data Consistency**: Verify data integrity across service boundaries
- **Comprehensive Coverage**: Test all critical integration points

### Test Naming Convention
- `test_<service>_<scenario>_integration` for service-specific tests
- `test_<workflow>_workflow` for end-to-end workflow tests
- `test_<scenario>_error_handling` for error recovery tests
- `test_<feature>_performance` for performance tests

## Test Results and Reporting

### Coverage Reports
- HTML coverage report: `coverage/html/index.html`
- XML coverage report: `coverage/coverage.xml`
- Terminal coverage summary during test execution

### Test Results
- JUnit XML: `test-results/integration-results.xml`
- Detailed test output with execution times
- Performance metrics and benchmark results

### Continuous Monitoring
Integration tests should be run:
- On every pull request
- Before production deployments
- On a scheduled basis (daily/weekly)
- After infrastructure changes