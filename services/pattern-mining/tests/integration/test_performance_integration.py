"""
Performance Integration Tests - Phase 2 Enhancement

Advanced multi-service performance testing that validates performance characteristics
under complex integration scenarios. These tests ensure the Pattern Mining service
maintains performance SLAs while handling sophisticated multi-service workflows.

Key Performance Patterns Tested:
1. Multi-Service Performance - End-to-end latency and throughput
2. Resource Competition Impact - Performance under resource pressure
3. Scaling Behavior - Performance under increasing load
4. Bottleneck Identification - Finding performance constraints
5. Recovery Performance - Performance after failure recovery
6. Cache Performance Impact - Caching effectiveness under load

Advanced Scenarios Beyond Phase 1:
- Complex workflow performance under realistic conditions
- Resource contention impact on service performance
- Performance degradation patterns during failures
- Cache coherence performance impact
- Multi-tenant performance isolation
- Performance recovery after service restoration
"""

import asyncio
import pytest
import time
import json
import uuid
import statistics
import random
import threading
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple, Callable
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from contextlib import asynccontextmanager
from dataclasses import dataclass, field
from enum import Enum
import concurrent.futures

# FastAPI and HTTP testing
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

# Pattern mining imports
from pattern_mining.models.patterns import PatternType, SeverityLevel, DetectionType
from pattern_mining.models.api import PatternDetectionRequest

# Test utilities
from tests.utils.generators import TestDataGenerator
from tests.utils.performance import PerformanceTestRunner


@dataclass
class PerformanceMetrics:
    """Performance metrics collection."""
    response_times: List[float] = field(default_factory=list)
    throughput_rps: float = 0.0
    error_rate: float = 0.0
    resource_usage: Dict[str, float] = field(default_factory=dict)
    latency_percentiles: Dict[str, float] = field(default_factory=dict)
    bottlenecks_detected: List[str] = field(default_factory=list)
    scalability_score: float = 0.0


@dataclass
class LoadTestConfig:
    """Load test configuration."""
    duration_seconds: int = 30
    concurrent_users: int = 10
    ramp_up_time: int = 5
    target_rps: float = 50.0
    max_response_time_ms: float = 2000.0
    error_threshold_percent: float = 5.0
    resource_limit_percent: float = 80.0


@pytest.mark.integration
@pytest.mark.performance
class TestMultiServicePerformance:
    """Test multi-service performance characteristics."""
    
    @pytest.fixture
    async def performance_orchestrator(self):
        """Create performance testing orchestrator."""
        
        class PerformanceOrchestrator:
            def __init__(self):
                self.service_metrics = {
                    "api": PerformanceMetrics(),
                    "gemini": PerformanceMetrics(), 
                    "database": PerformanceMetrics(),
                    "redis": PerformanceMetrics()
                }
                self.system_metrics = PerformanceMetrics()
                self.test_results = []
                self._metrics_lock = asyncio.Lock()
            
            async def simulate_service_call(
                self, 
                service_name: str, 
                base_latency: float, 
                complexity_factor: float = 1.0,
                failure_rate: float = 0.0
            ) -> Tuple[Any, float, bool]:
                """Simulate a service call with realistic performance characteristics."""
                
                # Calculate latency with complexity and jitter
                jitter = random.uniform(-0.1, 0.1) * base_latency
                actual_latency = base_latency * complexity_factor + jitter
                
                # Add random network/processing variation
                variation = random.uniform(0.8, 1.2)
                actual_latency *= variation
                
                # Simulate failure scenarios
                if random.random() < failure_rate:
                    # Simulate timeout or error with higher latency
                    actual_latency *= 3
                    await asyncio.sleep(actual_latency)
                    return None, actual_latency, False
                
                # Simulate successful processing
                await asyncio.sleep(actual_latency)
                
                result = {
                    "service": service_name,
                    "timestamp": datetime.utcnow().isoformat(),
                    "processing_time": actual_latency,
                    "success": True
                }
                
                return result, actual_latency, True
            
            async def execute_complex_workflow(
                self, 
                workflow_id: str,
                complexity_level: str = "normal"
            ) -> Dict[str, Any]:
                """Execute a complex multi-service workflow."""
                workflow_start = time.time()
                
                # Define complexity factors
                complexity_factors = {
                    "simple": 0.5,
                    "normal": 1.0,
                    "complex": 2.0,
                    "extreme": 4.0
                }
                
                factor = complexity_factors.get(complexity_level, 1.0)
                
                # Workflow steps with realistic dependencies
                workflow_results = {}
                
                # Step 1: API Request Processing
                api_result, api_latency, api_success = await self.simulate_service_call(
                    "api", 0.05, factor, 0.02  # 50ms base, 2% failure rate
                )
                
                if not api_success:
                    return {"workflow_id": workflow_id, "status": "failed", "step": "api"}
                
                # Step 2: Gemini ML Processing (depends on API)
                gemini_complexity = factor * (1.5 if complexity_level == "extreme" else 1.0)
                gemini_result, gemini_latency, gemini_success = await self.simulate_service_call(
                    "gemini", 0.8, gemini_complexity, 0.05  # 800ms base, 5% failure rate
                )
                
                if not gemini_success:
                    return {"workflow_id": workflow_id, "status": "failed", "step": "gemini"}
                
                # Step 3: Database Operations (can run parallel with some Gemini processing)
                db_operations = []
                for i in range(2 if complexity_level in ["complex", "extreme"] else 1):
                    db_task = self.simulate_service_call(
                        "database", 0.1, factor, 0.01  # 100ms base, 1% failure rate
                    )
                    db_operations.append(db_task)
                
                db_results = await asyncio.gather(*db_operations, return_exceptions=True)
                
                # Step 4: Redis Cache Operations
                cache_operations = []
                cache_count = 3 if complexity_level == "extreme" else 2
                
                for i in range(cache_count):
                    cache_task = self.simulate_service_call(
                        "redis", 0.01, factor, 0.005  # 10ms base, 0.5% failure rate
                    )
                    cache_operations.append(cache_task)
                
                cache_results = await asyncio.gather(*cache_operations, return_exceptions=True)
                
                workflow_end = time.time()
                total_time = workflow_end - workflow_start
                
                # Analyze workflow performance
                all_successful = (
                    api_success and gemini_success and
                    all(not isinstance(r, Exception) and r[2] for r in db_results) and
                    all(not isinstance(r, Exception) and r[2] for r in cache_results)
                )
                
                # Record metrics
                async with self._metrics_lock:
                    self.service_metrics["api"].response_times.append(api_latency)
                    self.service_metrics["gemini"].response_times.append(gemini_latency)
                    
                    for result in db_results:
                        if not isinstance(result, Exception):
                            self.service_metrics["database"].response_times.append(result[1])
                    
                    for result in cache_results:
                        if not isinstance(result, Exception):
                            self.service_metrics["redis"].response_times.append(result[1])
                    
                    self.system_metrics.response_times.append(total_time)
                
                return {
                    "workflow_id": workflow_id,
                    "status": "success" if all_successful else "partial_failure",
                    "total_time": total_time,
                    "complexity_level": complexity_level,
                    "steps_completed": sum([
                        1 if api_success else 0,
                        1 if gemini_success else 0,
                        sum(1 for r in db_results if not isinstance(r, Exception) and r[2]),
                        sum(1 for r in cache_results if not isinstance(r, Exception) and r[2])
                    ]),
                    "service_times": {
                        "api": api_latency,
                        "gemini": gemini_latency,
                        "database": [r[1] for r in db_results if not isinstance(r, Exception)],
                        "redis": [r[1] for r in cache_results if not isinstance(r, Exception)]
                    }
                }
            
            async def run_load_test(
                self, 
                config: LoadTestConfig,
                workflow_complexity: str = "normal"
            ) -> Dict[str, Any]:
                """Run comprehensive load test."""
                print(f"Starting load test: {config.concurrent_users} users, {config.duration_seconds}s duration")
                
                # Ramp-up phase
                test_start = time.time()
                active_users = 0
                user_tasks = []
                
                # Gradual ramp-up
                ramp_interval = config.ramp_up_time / config.concurrent_users
                
                async def user_workflow_loop(user_id: int):
                    """Individual user workflow loop."""
                    user_workflows = 0
                    user_errors = 0
                    
                    while time.time() - test_start < config.duration_seconds:
                        try:
                            workflow_id = f"user_{user_id}_wf_{user_workflows}"
                            result = await self.execute_complex_workflow(workflow_id, workflow_complexity)
                            
                            if result["status"] != "success":
                                user_errors += 1
                            
                            user_workflows += 1
                            
                            # Calculate inter-request delay to maintain target RPS
                            target_delay = config.concurrent_users / config.target_rps
                            jitter = random.uniform(0.8, 1.2) * target_delay
                            await asyncio.sleep(max(0.01, jitter))
                            
                        except Exception as e:
                            user_errors += 1
                    
                    return {"user_id": user_id, "workflows": user_workflows, "errors": user_errors}
                
                # Start users with ramp-up
                for i in range(config.concurrent_users):
                    await asyncio.sleep(ramp_interval)
                    task = asyncio.create_task(user_workflow_loop(i))
                    user_tasks.append(task)
                
                # Wait for test completion
                remaining_time = config.duration_seconds - (time.time() - test_start)
                if remaining_time > 0:
                    await asyncio.sleep(remaining_time)
                
                # Cancel all user tasks
                for task in user_tasks:
                    task.cancel()
                
                # Gather results
                user_results = []
                for task in user_tasks:
                    try:
                        result = await task
                        if result:
                            user_results.append(result)
                    except asyncio.CancelledError:
                        pass
                
                test_end = time.time()
                actual_duration = test_end - test_start
                
                # Calculate metrics
                total_workflows = sum(r["workflows"] for r in user_results)
                total_errors = sum(r["errors"] for r in user_results)
                
                actual_rps = total_workflows / actual_duration
                error_rate = (total_errors / total_workflows) * 100 if total_workflows > 0 else 100
                
                return {
                    "config": config,
                    "actual_duration": actual_duration,
                    "total_workflows": total_workflows,
                    "total_errors": total_errors,
                    "actual_rps": actual_rps,
                    "error_rate": error_rate,
                    "user_results": user_results,
                    "target_met": actual_rps >= (config.target_rps * 0.8) and error_rate <= config.error_threshold_percent
                }
            
            def calculate_performance_summary(self) -> Dict[str, Any]:
                """Calculate comprehensive performance summary."""
                summary = {
                    "system_performance": {},
                    "service_performance": {},
                    "bottlenecks": [],
                    "scalability_analysis": {}
                }
                
                # System-level metrics
                if self.system_metrics.response_times:
                    response_times = self.system_metrics.response_times
                    summary["system_performance"] = {
                        "avg_response_time": statistics.mean(response_times),
                        "median_response_time": statistics.median(response_times),
                        "p95_response_time": statistics.quantile(response_times, 0.95),
                        "p99_response_time": statistics.quantile(response_times, 0.99),
                        "min_response_time": min(response_times),
                        "max_response_time": max(response_times),
                        "total_requests": len(response_times)
                    }
                
                # Service-level metrics
                for service_name, metrics in self.service_metrics.items():
                    if metrics.response_times:
                        times = metrics.response_times
                        avg_time = statistics.mean(times)
                        
                        summary["service_performance"][service_name] = {
                            "avg_response_time": avg_time,
                            "median_response_time": statistics.median(times),
                            "p95_response_time": statistics.quantile(times, 0.95),
                            "total_calls": len(times),
                            "performance_score": min(100, (2.0 / max(avg_time, 0.001)) * 100)  # Score based on 2s target
                        }
                        
                        # Identify bottlenecks
                        if avg_time > 1.0:  # Services taking > 1s on average
                            summary["bottlenecks"].append({
                                "service": service_name,
                                "issue": "high_avg_latency",
                                "avg_latency": avg_time,
                                "severity": "high" if avg_time > 2.0 else "medium"
                            })
                
                return summary
        
        return PerformanceOrchestrator()
    
    @pytest.mark.asyncio
    async def test_baseline_multi_service_performance(self, performance_orchestrator):
        """Test baseline multi-service performance characteristics."""
        print("Testing baseline multi-service performance...")
        
        # Execute baseline workflows
        baseline_workflows = []
        baseline_start = time.time()
        
        for i in range(20):  # 20 baseline workflows
            workflow_id = f"baseline_wf_{i}"
            result = await performance_orchestrator.execute_complex_workflow(workflow_id, "normal")
            baseline_workflows.append(result)
            
            # Small delay between workflows
            await asyncio.sleep(0.1)
        
        baseline_duration = time.time() - baseline_start
        
        # Analyze baseline performance
        successful_workflows = [wf for wf in baseline_workflows if wf["status"] == "success"]
        total_times = [wf["total_time"] for wf in successful_workflows]
        
        baseline_metrics = {
            "total_workflows": len(baseline_workflows),
            "successful_workflows": len(successful_workflows),
            "success_rate": len(successful_workflows) / len(baseline_workflows),
            "avg_response_time": statistics.mean(total_times) if total_times else 0,
            "p95_response_time": statistics.quantile(total_times, 0.95) if len(total_times) > 1 else 0,
            "throughput_wps": len(successful_workflows) / baseline_duration  # workflows per second
        }
        
        # Get detailed service performance
        performance_summary = performance_orchestrator.calculate_performance_summary()
        
        print(f"Baseline multi-service performance results:")
        print(f"  - Workflows executed: {baseline_metrics['total_workflows']}")
        print(f"  - Success rate: {baseline_metrics['success_rate']:.2%}")
        print(f"  - Avg response time: {baseline_metrics['avg_response_time']:.3f}s")
        print(f"  - P95 response time: {baseline_metrics['p95_response_time']:.3f}s")
        print(f"  - Throughput: {baseline_metrics['throughput_wps']:.1f} workflows/sec")
        print(f"  - Bottlenecks detected: {len(performance_summary['bottlenecks'])}")
        
        # Service-specific performance
        for service, perf in performance_summary["service_performance"].items():
            print(f"  - {service}: {perf['avg_response_time']:.3f}s avg, score: {perf['performance_score']:.1f}")
        
        return {
            "baseline_established": True,
            "success_rate": baseline_metrics["success_rate"],
            "avg_response_time": baseline_metrics["avg_response_time"],
            "throughput_wps": baseline_metrics["throughput_wps"],
            "performance_acceptable": (
                baseline_metrics["success_rate"] > 0.95 and
                baseline_metrics["avg_response_time"] < 2.0 and
                baseline_metrics["throughput_wps"] > 1.0
            )
        }
    
    @pytest.mark.asyncio
    async def test_load_testing_scalability(self, performance_orchestrator):
        """Test scalability under increasing load."""
        print("Testing scalability under increasing load...")
        
        # Define increasing load scenarios
        load_scenarios = [
            LoadTestConfig(duration_seconds=15, concurrent_users=5, target_rps=10),
            LoadTestConfig(duration_seconds=15, concurrent_users=10, target_rps=25),
            LoadTestConfig(duration_seconds=15, concurrent_users=20, target_rps=50),
            LoadTestConfig(duration_seconds=15, concurrent_users=40, target_rps=80)
        ]
        
        scalability_results = []
        
        for i, config in enumerate(load_scenarios):
            print(f"  Running load scenario {i+1}: {config.concurrent_users} users, {config.target_rps} RPS target")
            
            result = await performance_orchestrator.run_load_test(config, "normal")
            scalability_results.append(result)
            
            # Brief recovery time between tests
            await asyncio.sleep(2.0)
        
        # Analyze scalability
        scalability_analysis = {
            "scenarios": len(scalability_results),
            "results": []
        }
        
        for i, result in enumerate(scalability_results):
            scenario_analysis = {
                "scenario": i + 1,
                "target_rps": result["config"].target_rps,
                "actual_rps": result["actual_rps"],
                "error_rate": result["error_rate"],
                "target_met": result["target_met"],
                "efficiency": result["actual_rps"] / result["config"].target_rps,
                "concurrent_users": result["config"].concurrent_users
            }
            scalability_analysis["results"].append(scenario_analysis)
        
        # Calculate scalability metrics
        efficiencies = [r["efficiency"] for r in scalability_analysis["results"]]
        error_rates = [r["error_rate"] for r in scalability_analysis["results"]]
        
        scalability_score = statistics.mean(efficiencies) if efficiencies else 0
        stability_score = 100 - statistics.mean(error_rates) if error_rates else 0
        
        # Get final performance summary
        final_performance = performance_orchestrator.calculate_performance_summary()
        
        print(f"Scalability testing results:")
        print(f"  - Load scenarios tested: {len(scalability_results)}")
        print(f"  - Average efficiency: {scalability_score:.2%}")
        print(f"  - Average error rate: {statistics.mean(error_rates):.1f}%")
        print(f"  - Stability score: {stability_score:.1f}")
        
        for i, analysis in enumerate(scalability_analysis["results"]):
            print(f"  - Scenario {analysis['scenario']}: {analysis['actual_rps']:.1f}/{analysis['target_rps']} RPS, "
                  f"{analysis['error_rate']:.1f}% errors, {'✓' if analysis['target_met'] else '✗'}")
        
        return {
            "scalability_tested": True,
            "scenarios_completed": len(scalability_results),
            "scalability_score": scalability_score,
            "stability_score": stability_score,
            "acceptable_scalability": scalability_score > 0.7 and stability_score > 90,
            "final_bottlenecks": len(final_performance["bottlenecks"])
        }
    
    @pytest.mark.asyncio
    async def test_performance_under_resource_contention(self, performance_orchestrator):
        """Test performance under resource contention scenarios."""
        print("Testing performance under resource contention...")
        
        # Create resource contention by running multiple complex workflows simultaneously
        contention_config = LoadTestConfig(
            duration_seconds=20,
            concurrent_users=30,  # High concurrency
            target_rps=60,       # Aggressive target
            max_response_time_ms=5000,  # Higher tolerance
            error_threshold_percent=15   # Higher error tolerance
        )
        
        # Run with complex workflows to create resource pressure
        print("  Phase 1: Running under high resource contention...")
        contention_result = await performance_orchestrator.run_load_test(contention_config, "complex")
        
        # Brief recovery period
        print("  Recovery period...")
        await asyncio.sleep(5.0)
        
        # Run normal load test to compare performance recovery
        recovery_config = LoadTestConfig(
            duration_seconds=10,
            concurrent_users=10,
            target_rps=25
        )
        
        print("  Phase 2: Testing performance recovery...")
        recovery_result = await performance_orchestrator.run_load_test(recovery_config, "normal")
        
        # Analyze resource contention impact
        contention_analysis = {
            "contention_phase": {
                "target_rps": contention_result["config"].target_rps,
                "actual_rps": contention_result["actual_rps"],
                "error_rate": contention_result["error_rate"],
                "efficiency": contention_result["actual_rps"] / contention_result["config"].target_rps,
                "high_load_sustainable": contention_result["target_met"]
            },
            "recovery_phase": {
                "target_rps": recovery_result["config"].target_rps,
                "actual_rps": recovery_result["actual_rps"],
                "error_rate": recovery_result["error_rate"],
                "efficiency": recovery_result["actual_rps"] / recovery_result["config"].target_rps,
                "recovery_successful": recovery_result["target_met"]
            }
        }
        
        # Calculate resource contention metrics
        performance_degradation = 1.0 - contention_analysis["contention_phase"]["efficiency"]
        recovery_effectiveness = contention_analysis["recovery_phase"]["efficiency"]
        
        # Get performance summary
        contention_performance = performance_orchestrator.calculate_performance_summary()
        
        # Identify services most affected by contention
        affected_services = []
        for service, perf in contention_performance["service_performance"].items():
            if perf["avg_response_time"] > 1.0:  # Services with high latency under contention
                affected_services.append({
                    "service": service,
                    "avg_latency": perf["avg_response_time"],
                    "performance_score": perf["performance_score"]
                })
        
        print(f"Resource contention testing results:")
        print(f"  - Contention phase efficiency: {contention_analysis['contention_phase']['efficiency']:.2%}")
        print(f"  - Performance degradation: {performance_degradation:.2%}")
        print(f"  - Recovery phase efficiency: {recovery_effectiveness:.2%}")
        print(f"  - Services affected by contention: {len(affected_services)}")
        
        for service_info in affected_services:
            print(f"    - {service_info['service']}: {service_info['avg_latency']:.3f}s avg latency")
        
        print(f"  - Bottlenecks identified: {len(contention_performance['bottlenecks'])}")
        
        return {
            "contention_tested": True,
            "performance_degradation": performance_degradation,
            "recovery_effectiveness": recovery_effectiveness,
            "services_affected": len(affected_services),
            "bottlenecks_identified": len(contention_performance["bottlenecks"]),
            "contention_handled_gracefully": (
                performance_degradation < 0.5 and  # Less than 50% degradation
                recovery_effectiveness > 0.8 and   # Good recovery
                contention_analysis["contention_phase"]["error_rate"] < 20  # Reasonable error rate
            )
        }


@pytest.mark.integration
@pytest.mark.performance
class TestCachePerformanceIntegration:
    """Test cache performance impact on multi-service operations."""
    
    @pytest.fixture
    async def cache_performance_simulator(self):
        """Create cache performance simulation environment."""
        
        class CachePerformanceSimulator:
            def __init__(self):
                self.cache_store = {}
                self.cache_stats = {
                    "hits": 0,
                    "misses": 0,
                    "sets": 0,
                    "deletes": 0,
                    "evictions": 0
                }
                self.cache_config = {
                    "max_size": 1000,
                    "ttl_seconds": 300,
                    "hit_latency": 0.002,    # 2ms for cache hits
                    "miss_latency": 0.01,    # 10ms for cache misses
                    "set_latency": 0.005,    # 5ms for cache sets
                    "eviction_enabled": True
                }
                self._cache_lock = asyncio.Lock()
            
            async def get(self, key: str, default=None) -> Tuple[Any, float, bool]:
                """Get value from cache with performance tracking."""
                async with self._cache_lock:
                    start_time = time.time()
                    
                    if key in self.cache_store:
                        entry = self.cache_store[key]
                        
                        # Check TTL
                        if time.time() - entry["created_at"] < self.cache_config["ttl_seconds"]:
                            # Cache hit
                            await asyncio.sleep(self.cache_config["hit_latency"])
                            self.cache_stats["hits"] += 1
                            
                            latency = time.time() - start_time
                            return entry["value"], latency, True
                        else:
                            # Expired entry
                            del self.cache_store[key]
                    
                    # Cache miss
                    await asyncio.sleep(self.cache_config["miss_latency"])
                    self.cache_stats["misses"] += 1
                    
                    latency = time.time() - start_time
                    return default, latency, False
            
            async def set(self, key: str, value: Any) -> float:
                """Set value in cache with performance tracking."""
                async with self._cache_lock:
                    start_time = time.time()
                    
                    # Check if eviction is needed
                    if (len(self.cache_store) >= self.cache_config["max_size"] and
                        key not in self.cache_store and
                        self.cache_config["eviction_enabled"]):
                        
                        # Simple LRU eviction - remove oldest entry
                        oldest_key = min(
                            self.cache_store.keys(),
                            key=lambda k: self.cache_store[k]["created_at"]
                        )
                        del self.cache_store[oldest_key]
                        self.cache_stats["evictions"] += 1
                    
                    # Set the value
                    self.cache_store[key] = {
                        "value": value,
                        "created_at": time.time()
                    }
                    
                    await asyncio.sleep(self.cache_config["set_latency"])
                    self.cache_stats["sets"] += 1
                    
                    return time.time() - start_time
            
            async def simulate_cache_warming(self, num_entries: int = 500) -> Dict[str, Any]:
                """Simulate cache warming with performance tracking."""
                warming_start = time.time()
                
                warming_tasks = []
                for i in range(num_entries):
                    key = f"warm_key_{i}"
                    value = {
                        "pattern_id": f"pattern_{i}",
                        "confidence": random.uniform(0.7, 0.98),
                        "type": random.choice(["design_pattern", "security_issue", "performance_issue"])
                    }
                    warming_tasks.append(self.set(key, value))
                
                # Execute warming operations
                set_latencies = await asyncio.gather(*warming_tasks)
                warming_time = time.time() - warming_start
                
                return {
                    "entries_warmed": num_entries,
                    "warming_time": warming_time,
                    "avg_set_latency": statistics.mean(set_latencies),
                    "warming_rate": num_entries / warming_time
                }
            
            async def simulate_realistic_access_pattern(
                self, 
                duration_seconds: int = 30,
                operations_per_second: int = 100
            ) -> Dict[str, Any]:
                """Simulate realistic cache access patterns."""
                pattern_start = time.time()
                
                # Access pattern: 80% reads, 20% writes (typical web application)
                read_operations = []
                write_operations = []
                
                total_operations = duration_seconds * operations_per_second
                
                async def cache_operation_worker():
                    """Worker that performs cache operations."""
                    operation_count = 0
                    
                    while time.time() - pattern_start < duration_seconds:
                        operation_count += 1
                        
                        if random.random() < 0.8:  # 80% reads
                            # Read operation
                            key = f"pattern_{random.randint(1, 1000)}"
                            result, latency, hit = await self.get(key, default=None)
                            read_operations.append({"latency": latency, "hit": hit})
                        else:  # 20% writes
                            # Write operation
                            key = f"pattern_{random.randint(1, 1000)}"
                            value = {"data": f"updated_data_{operation_count}"}
                            latency = await self.set(key, value)
                            write_operations.append({"latency": latency})
                        
                        # Maintain target operations per second
                        await asyncio.sleep(1.0 / operations_per_second)
                
                # Run multiple workers to simulate concurrent access
                num_workers = min(10, operations_per_second // 10)
                workers = [cache_operation_worker() for _ in range(num_workers)]
                
                await asyncio.gather(*workers, return_exceptions=True)
                
                pattern_duration = time.time() - pattern_start
                
                # Analyze access pattern results
                total_reads = len(read_operations)
                total_writes = len(write_operations)
                cache_hits = sum(1 for op in read_operations if op["hit"])
                
                hit_rate = cache_hits / total_reads if total_reads > 0 else 0
                avg_read_latency = statistics.mean([op["latency"] for op in read_operations]) if read_operations else 0
                avg_write_latency = statistics.mean([op["latency"] for op in write_operations]) if write_operations else 0
                
                return {
                    "duration": pattern_duration,
                    "total_operations": total_reads + total_writes,
                    "read_operations": total_reads,
                    "write_operations": total_writes,
                    "cache_hit_rate": hit_rate,
                    "avg_read_latency": avg_read_latency,
                    "avg_write_latency": avg_write_latency,
                    "operations_per_second": (total_reads + total_writes) / pattern_duration
                }
            
            def get_cache_statistics(self) -> Dict[str, Any]:
                """Get comprehensive cache statistics."""
                total_operations = sum(self.cache_stats.values())
                hit_rate = self.cache_stats["hits"] / (self.cache_stats["hits"] + self.cache_stats["misses"]) if (self.cache_stats["hits"] + self.cache_stats["misses"]) > 0 else 0
                
                return {
                    "cache_stats": self.cache_stats.copy(),
                    "cache_size": len(self.cache_store),
                    "max_size": self.cache_config["max_size"],
                    "utilization": len(self.cache_store) / self.cache_config["max_size"],
                    "hit_rate": hit_rate,
                    "total_operations": total_operations,
                    "efficiency_score": hit_rate * 100
                }
        
        return CachePerformanceSimulator()
    
    @pytest.mark.asyncio
    async def test_cache_warming_performance(self, cache_performance_simulator):
        """Test cache warming performance characteristics."""
        print("Testing cache warming performance...")
        
        # Test different warming strategies
        warming_scenarios = [
            {"entries": 100, "name": "small_warm"},
            {"entries": 500, "name": "medium_warm"},
            {"entries": 800, "name": "large_warm"}
        ]
        
        warming_results = []
        
        for scenario in warming_scenarios:
            print(f"  Warming cache with {scenario['entries']} entries...")
            
            result = await cache_performance_simulator.simulate_cache_warming(scenario["entries"])
            result["scenario"] = scenario["name"]
            warming_results.append(result)
            
            # Brief pause between scenarios
            await asyncio.sleep(1.0)
        
        # Analyze warming performance
        warming_analysis = {
            "scenarios_tested": len(warming_results),
            "warming_efficiency": []
        }
        
        for result in warming_results:
            efficiency = {
                "scenario": result["scenario"],
                "entries": result["entries_warmed"],
                "time": result["warming_time"],
                "rate": result["warming_rate"],
                "avg_latency": result["avg_set_latency"]
            }
            warming_analysis["warming_efficiency"].append(efficiency)
        
        # Calculate performance metrics
        avg_warming_rate = statistics.mean([r["warming_rate"] for r in warming_results])
        avg_set_latency = statistics.mean([r["avg_set_latency"] for r in warming_results])
        
        print(f"Cache warming performance results:")
        print(f"  - Scenarios tested: {len(warming_results)}")
        print(f"  - Average warming rate: {avg_warming_rate:.1f} entries/sec")
        print(f"  - Average set latency: {avg_set_latency:.4f}s")
        
        for efficiency in warming_analysis["warming_efficiency"]:
            print(f"  - {efficiency['scenario']}: {efficiency['entries']} entries in {efficiency['time']:.2f}s "
                  f"({efficiency['rate']:.1f} entries/sec)")
        
        cache_stats = cache_performance_simulator.get_cache_statistics()
        
        return {
            "warming_tested": True,
            "scenarios_completed": len(warming_results),
            "avg_warming_rate": avg_warming_rate,
            "avg_set_latency": avg_set_latency,
            "final_cache_size": cache_stats["cache_size"],
            "warming_efficient": avg_warming_rate > 50 and avg_set_latency < 0.01  # 50+ entries/sec, <10ms latency
        }
    
    @pytest.mark.asyncio
    async def test_cache_performance_under_load(self, cache_performance_simulator):
        """Test cache performance under realistic load patterns."""
        print("Testing cache performance under load...")
        
        # Pre-warm cache for realistic testing
        print("  Pre-warming cache...")
        await cache_performance_simulator.simulate_cache_warming(300)
        
        # Test different load patterns
        load_patterns = [
            {"ops_per_sec": 50, "duration": 10, "name": "light_load"},
            {"ops_per_sec": 150, "duration": 15, "name": "medium_load"},
            {"ops_per_sec": 300, "duration": 20, "name": "heavy_load"}
        ]
        
        load_results = []
        
        for pattern in load_patterns:
            print(f"  Testing {pattern['name']}: {pattern['ops_per_sec']} ops/sec for {pattern['duration']}s")
            
            result = await cache_performance_simulator.simulate_realistic_access_pattern(
                duration_seconds=pattern["duration"],
                operations_per_second=pattern["ops_per_sec"]
            )
            result["pattern_name"] = pattern["name"]
            result["target_ops_per_sec"] = pattern["ops_per_sec"]
            load_results.append(result)
            
            # Recovery time between tests
            await asyncio.sleep(2.0)
        
        # Analyze load performance
        load_analysis = {
            "patterns_tested": len(load_results),
            "performance_degradation": {},
            "hit_rate_stability": []
        }
        
        baseline_hit_rate = None
        for result in load_results:
            hit_rate = result["cache_hit_rate"]
            actual_ops = result["operations_per_second"]
            target_ops = result["target_ops_per_sec"]
            
            efficiency = actual_ops / target_ops
            
            if baseline_hit_rate is None:
                baseline_hit_rate = hit_rate
            
            hit_rate_change = hit_rate - baseline_hit_rate
            
            load_analysis["performance_degradation"][result["pattern_name"]] = {
                "efficiency": efficiency,
                "hit_rate": hit_rate,
                "hit_rate_change": hit_rate_change,
                "avg_read_latency": result["avg_read_latency"],
                "avg_write_latency": result["avg_write_latency"]
            }
            
            load_analysis["hit_rate_stability"].append(hit_rate)
        
        # Final cache statistics
        final_stats = cache_performance_simulator.get_cache_statistics()
        
        # Calculate stability metrics
        hit_rate_variance = statistics.variance(load_analysis["hit_rate_stability"]) if len(load_analysis["hit_rate_stability"]) > 1 else 0
        avg_efficiency = statistics.mean([perf["efficiency"] for perf in load_analysis["performance_degradation"].values()])
        
        print(f"Cache performance under load results:")
        print(f"  - Load patterns tested: {len(load_results)}")
        print(f"  - Average efficiency: {avg_efficiency:.2%}")
        print(f"  - Hit rate variance: {hit_rate_variance:.4f}")
        print(f"  - Final cache utilization: {final_stats['utilization']:.2%}")
        print(f"  - Final hit rate: {final_stats['hit_rate']:.2%}")
        
        for pattern_name, perf in load_analysis["performance_degradation"].items():
            print(f"  - {pattern_name}: {perf['efficiency']:.2%} efficiency, "
                  f"{perf['hit_rate']:.2%} hit rate, "
                  f"{perf['avg_read_latency']:.4f}s read latency")
        
        return {
            "load_testing_completed": True,
            "patterns_tested": len(load_results),
            "avg_efficiency": avg_efficiency,
            "hit_rate_variance": hit_rate_variance,
            "final_hit_rate": final_stats["hit_rate"],
            "performance_stable": (
                avg_efficiency > 0.8 and           # Good efficiency
                hit_rate_variance < 0.01 and       # Stable hit rates
                final_stats["hit_rate"] > 0.6      # Reasonable hit rate
            )
        }


if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short", "-m", "performance"])