"""
Integration Tests for Failure Scenarios

End-to-end integration tests that simulate real-world failure scenarios
and verify that the Pattern Mining service handles them gracefully.

Key Test Categories:
1. External Service Failures - Gemini API, Database, Redis failures
2. System Resource Limits - Memory, CPU, disk space limitations  
3. Network Issues - Intermittent connectivity, slow responses
4. Data Pipeline Failures - Processing errors, validation failures
5. Recovery Scenarios - Service restart, failover, error recovery
"""

import asyncio
import pytest
import aiohttp
import time
import json
import tempfile
import os
import gc
import psutil
from datetime import datetime, timedelta
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from concurrent.futures import ThreadPoolExecutor
from contextlib import asynccontextmanager
from typing import Dict, Any, List, Optional

# FastAPI testing
from fastapi.testclient import TestClient
from httpx import AsyncClient

# Pattern mining imports  
from pattern_mining.api.main import create_app
from pattern_mining.config.settings import get_settings
from pattern_mining.database.connection import get_database_session
from pattern_mining.cache.redis_client import get_redis_client
from pattern_mining.ml.gemini_client import Gemini<PERSON>lient
from pattern_mining.ml.gemini_integration import GeminiIntegration
from pattern_mining.models.api import PatternDetectionRequest, PatternDetectionResponse
from pattern_mining.health.health_checker import HealthChecker

# Test utilities
from tests.utils.generators import generate_repository_data, generate_large_code_sample
from tests.utils.assertions import assert_error_response, assert_service_degraded


@pytest.mark.integration
class TestExternalServiceFailures:
    """Test external service failure scenarios."""
    
    @pytest.fixture
    async def app_with_mocked_services(self):
        """Create app with mocked external services for failure testing."""
        app = create_app()
        
        # Mock external services to simulate failures
        app.state.gemini_available = False
        app.state.database_available = True
        app.state.redis_available = True
        
        return app
    
    @pytest.fixture
    async def client_with_failures(self, app_with_mocked_services):
        """Create test client with simulated service failures."""
        async with AsyncClient(app=app_with_mocked_services, base_url="http://test") as client:
            yield client
    
    @pytest.mark.asyncio
    async def test_gemini_api_complete_failure(self, client_with_failures):
        """Test complete Gemini API failure and fallback behavior."""
        # Mock complete Gemini API failure
        with patch('pattern_mining.ml.gemini_client.GeminiClient.generate_content') as mock_generate:
            mock_generate.side_effect = aiohttp.ClientConnectorError(
                connection_key=Mock(),
                os_error=ConnectionRefusedError("Connection refused")
            )
            
            # Request should still work with local analysis fallback
            request_data = {
                "code": "def test_function():\n    return 'test'",
                "language": "python",
                "detection_types": ["ml_inference", "heuristic"]
            }
            
            response = await client_with_failures.post("/api/v1/patterns/detect", json=request_data)
            
            # Should succeed with degraded service
            assert response.status_code == 200
            
            result = response.json()
            assert "patterns" in result
            assert "metadata" in result
            
            # Should indicate service degradation
            metadata = result["metadata"]
            assert metadata.get("service_status") == "degraded"
            assert "gemini_unavailable" in metadata.get("notes", "")
    
    @pytest.mark.asyncio
    async def test_database_connection_failure(self, client_with_failures):
        """Test database connection failure handling."""
        with patch('pattern_mining.database.connection.get_database_session') as mock_db:
            mock_db.side_effect = Exception("Database connection failed")
            
            # Health check should fail
            response = await client_with_failures.get("/health/ready")
            assert response.status_code == 503
            
            result = response.json()
            assert "database" in result["detail"].lower()
    
    @pytest.mark.asyncio
    async def test_redis_cache_failure(self, client_with_failures):
        """Test Redis cache failure and fallback behavior."""
        with patch('pattern_mining.cache.redis_client.get_redis_client') as mock_redis:
            mock_redis.side_effect = Exception("Redis connection failed")
            
            # API should still work without caching
            request_data = {
                "code": "def test_function():\n    return 'test'",
                "language": "python",
                "detection_types": ["heuristic"]
            }
            
            response = await client_with_failures.post("/api/v1/patterns/detect", json=request_data)
            
            # Should succeed but indicate cache unavailable
            assert response.status_code == 200
            
            result = response.json()
            metadata = result["metadata"]
            assert "cache_unavailable" in metadata.get("notes", "")
    
    @pytest.mark.asyncio
    async def test_multiple_service_failures(self, client_with_failures):
        """Test multiple simultaneous service failures."""
        with patch('pattern_mining.ml.gemini_client.GeminiClient.generate_content') as mock_gemini:
            with patch('pattern_mining.cache.redis_client.get_redis_client') as mock_redis:
                # Both Gemini and Redis fail
                mock_gemini.side_effect = Exception("Gemini API unavailable")
                mock_redis.side_effect = Exception("Redis unavailable")
                
                request_data = {
                    "code": "def test_function():\n    return 'test'",
                    "language": "python",
                    "detection_types": ["ml_inference", "heuristic"]
                }
                
                response = await client_with_failures.post("/api/v1/patterns/detect", json=request_data)
                
                # Should still work with local analysis only
                assert response.status_code == 200
                
                result = response.json()
                metadata = result["metadata"]
                assert metadata.get("service_status") == "degraded"
                assert "gemini_unavailable" in metadata.get("notes", "")
                assert "cache_unavailable" in metadata.get("notes", "")
    
    @pytest.mark.asyncio
    async def test_intermittent_service_failures(self, client_with_failures):
        """Test intermittent service failures and recovery."""
        failure_count = 0
        
        async def intermittent_failure(*args, **kwargs):
            nonlocal failure_count
            failure_count += 1
            
            if failure_count % 3 == 0:  # Fail every 3rd request
                raise Exception("Intermittent service failure")
            
            return {
                "candidates": [{
                    "content": {
                        "parts": [{"text": json.dumps({"patterns": [], "confidence": 0.8})}]
                    }
                }]
            }
        
        with patch('pattern_mining.ml.gemini_client.GeminiClient.generate_content', side_effect=intermittent_failure):
            request_data = {
                "code": "def test_function():\n    return 'test'",
                "language": "python",
                "detection_types": ["ml_inference"]
            }
            
            # Make multiple requests
            success_count = 0
            failure_count_client = 0
            
            for i in range(9):  # 9 requests total
                response = await client_with_failures.post("/api/v1/patterns/detect", json=request_data)
                
                if response.status_code == 200:
                    success_count += 1
                else:
                    failure_count_client += 1
            
            # Should have some successes even with intermittent failures
            assert success_count > 0
            # Due to retry logic, some failures might be recovered
            assert success_count >= 6  # At least 2/3 should succeed


@pytest.mark.integration
class TestSystemResourceLimits:
    """Test system resource limitation scenarios."""
    
    @pytest.fixture
    async def resource_limited_app(self):
        """Create app with limited resources for testing."""
        settings = get_settings()
        settings.max_concurrent_requests = 2  # Very low limit
        settings.request_timeout = 5  # Short timeout
        
        app = create_app()
        return app
    
    @pytest.fixture
    async def limited_client(self, resource_limited_app):
        """Create client with resource-limited app."""
        async with AsyncClient(app=resource_limited_app, base_url="http://test") as client:
            yield client
    
    @pytest.mark.asyncio
    async def test_concurrent_request_limit(self, limited_client):
        """Test concurrent request limit handling."""
        async def make_request():
            request_data = {
                "code": "def test_function():\n    time.sleep(2)\n    return 'test'",
                "language": "python",
                "detection_types": ["heuristic"]
            }
            return await limited_client.post("/api/v1/patterns/detect", json=request_data)
        
        # Start multiple concurrent requests
        tasks = [asyncio.create_task(make_request()) for _ in range(5)]
        responses = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Some requests should be rejected due to rate limiting
        status_codes = []
        for response in responses:
            if isinstance(response, Exception):
                status_codes.append(500)  # Exception occurred
            else:
                status_codes.append(response.status_code)
        
        # Should have mix of successful and rate-limited responses
        assert 200 in status_codes  # Some succeeded
        assert any(code >= 400 for code in status_codes)  # Some failed/rate-limited
    
    @pytest.mark.asyncio
    async def test_memory_pressure_response(self, limited_client):
        """Test service response under memory pressure."""
        # Create memory pressure
        large_objects = []
        try:
            # Allocate large amounts of memory
            for i in range(50):
                large_obj = bytearray(10 * 1024 * 1024)  # 10MB each
                large_objects.append(large_obj)
            
            # Test API response under memory pressure
            request_data = {
                "code": "def simple_function(): return 42",
                "language": "python",
                "detection_types": ["heuristic"]
            }
            
            start_time = time.time()
            response = await limited_client.post("/api/v1/patterns/detect", json=request_data)
            end_time = time.time()
            
            # Service should still respond, even if slower
            assert response.status_code == 200
            
            # Response time might be slower but should be reasonable
            response_time = end_time - start_time
            assert response_time < 30.0  # Maximum 30 seconds under pressure
            
        finally:
            # Cleanup memory
            large_objects.clear()
            gc.collect()
    
    @pytest.mark.asyncio
    async def test_request_timeout_handling(self, limited_client):
        """Test request timeout handling."""
        # Mock slow processing
        with patch('pattern_mining.detectors.manager.DetectorManager.detect_patterns') as mock_detect:
            async def slow_processing(*args, **kwargs):
                await asyncio.sleep(10)  # Longer than timeout
                return []
            
            mock_detect.side_effect = slow_processing
            
            request_data = {
                "code": "def test_function(): return 'test'",
                "language": "python",
                "detection_types": ["heuristic"]
            }
            
            start_time = time.time()
            response = await limited_client.post("/api/v1/patterns/detect", json=request_data)
            end_time = time.time()
            
            # Should timeout and return appropriate error
            assert response.status_code == 408 or response.status_code == 500
            
            # Should not take longer than timeout + small buffer
            response_time = end_time - start_time
            assert response_time < 15.0  # Should timeout before 15 seconds
    
    @pytest.mark.asyncio
    async def test_disk_space_limitation_handling(self, limited_client):
        """Test disk space limitation handling."""
        # Mock disk space check
        with patch('psutil.disk_usage') as mock_disk_usage:
            # Simulate low disk space (95% full)
            mock_disk_usage.return_value = Mock(
                total=1000000000,  # 1GB total
                used=950000000,    # 950MB used (95%)
                free=50000000      # 50MB free (5%)
            )
            
            # Request with large code sample should be rejected
            large_code = "def test():\n" + "    pass\n" * 10000  # Large code
            request_data = {
                "code": large_code,
                "language": "python",
                "detection_types": ["heuristic"]
            }
            
            response = await limited_client.post("/api/v1/patterns/detect", json=request_data)
            
            # Should handle gracefully
            if response.status_code != 200:
                result = response.json()
                assert "disk space" in result.get("detail", "").lower() or "storage" in result.get("detail", "").lower()


@pytest.mark.integration  
class TestNetworkIssues:
    """Test network-related failure scenarios."""
    
    @pytest.fixture
    async def network_app(self):
        """Create app for network testing."""
        return create_app()
    
    @pytest.fixture
    async def network_client(self, network_app):
        """Create client for network testing."""
        async with AsyncClient(app=network_app, base_url="http://test") as client:
            yield client
    
    @pytest.mark.asyncio
    async def test_slow_external_api_response(self, network_client):
        """Test handling of slow external API responses."""
        with patch('pattern_mining.ml.gemini_client.GeminiClient.generate_content') as mock_generate:
            async def slow_response(*args, **kwargs):
                await asyncio.sleep(3)  # 3 second delay
                return {
                    "candidates": [{
                        "content": {
                            "parts": [{"text": json.dumps({"patterns": [], "confidence": 0.8})}]
                        }
                    }]
                }
            
            mock_generate.side_effect = slow_response
            
            request_data = {
                "code": "def test_function(): return 'test'",
                "language": "python", 
                "detection_types": ["ml_inference"]
            }
            
            start_time = time.time()
            response = await network_client.post("/api/v1/patterns/detect", json=request_data)
            end_time = time.time()
            
            # Should succeed but might take longer
            assert response.status_code == 200
            
            response_time = end_time - start_time
            assert response_time >= 2.5  # Should reflect the delay
            
            # Response should indicate slow external service
            result = response.json()
            metadata = result.get("metadata", {})
            processing_time = metadata.get("processing_time_ms", 0)
            assert processing_time > 2000  # Should be > 2 seconds
    
    @pytest.mark.asyncio
    async def test_network_partition_recovery(self, network_client):
        """Test recovery from network partition."""
        call_count = 0
        
        async def partition_simulation(*args, **kwargs):
            nonlocal call_count
            call_count += 1
            
            if call_count <= 2:
                # First 2 calls fail (simulating partition)
                raise aiohttp.ClientConnectorError(
                    connection_key=Mock(),
                    os_error=OSError("Network is unreachable")
                )
            else:
                # Subsequent calls succeed (partition recovered)
                return {
                    "candidates": [{
                        "content": {
                            "parts": [{"text": json.dumps({"patterns": [], "confidence": 0.8})}]
                        }
                    }]
                }
        
        with patch('pattern_mining.ml.gemini_client.GeminiClient.generate_content') as mock_generate:
            mock_generate.side_effect = partition_simulation
            
            request_data = {
                "code": "def test_function(): return 'test'",
                "language": "python",
                "detection_types": ["ml_inference"]
            }
            
            # Should eventually succeed after retries
            response = await network_client.post("/api/v1/patterns/detect", json=request_data)
            
            # Should succeed after partition recovery
            assert response.status_code == 200
            
            # Should have retried multiple times
            assert call_count > 2
    
    @pytest.mark.asyncio
    async def test_partial_response_handling(self, network_client):
        """Test handling of partial/corrupted responses."""
        with patch('pattern_mining.ml.gemini_client.GeminiClient.generate_content') as mock_generate:
            # Return partial/corrupted response
            mock_generate.return_value = {
                "candidates": [{
                    "content": {
                        "parts": [{"text": '{"patterns": [], "confidence": 0.8'}]  # Missing closing brace
                    }
                }]
            }
            
            request_data = {
                "code": "def test_function(): return 'test'",
                "language": "python",
                "detection_types": ["ml_inference"]
            }
            
            response = await network_client.post("/api/v1/patterns/detect", json=request_data)
            
            # Should handle gracefully with fallback
            assert response.status_code == 200
            
            result = response.json()
            metadata = result.get("metadata", {})
            
            # Should indicate parsing error
            assert "parsing_error" in metadata.get("notes", "") or "fallback" in metadata.get("notes", "")


@pytest.mark.integration
class TestDataPipelineFailures:
    """Test data pipeline failure scenarios."""
    
    @pytest.fixture
    async def pipeline_app(self):
        """Create app for pipeline testing."""
        return create_app()
    
    @pytest.fixture
    async def pipeline_client(self, pipeline_app):
        """Create client for pipeline testing."""
        async with AsyncClient(app=pipeline_app, base_url="http://test") as client:
            yield client
    
    @pytest.mark.asyncio
    async def test_code_parsing_failure(self, pipeline_client):
        """Test handling of code parsing failures."""
        # Submit syntactically invalid code
        invalid_codes = [
            "def incomplete_function(",  # Syntax error
            "import sys; sys.exit()",   # Potentially harmful code
            "class UnbalancedBraces { def method(self: }",  # Unbalanced braces
            "🙂🙂🙂 invalid unicode code 🙂🙂🙂",  # Invalid code with unicode
        ]
        
        for invalid_code in invalid_codes:
            request_data = {
                "code": invalid_code,
                "language": "python",
                "detection_types": ["heuristic", "ml_inference"]
            }
            
            response = await pipeline_client.post("/api/v1/patterns/detect", json=request_data)
            
            # Should handle gracefully
            if response.status_code == 200:
                result = response.json()
                # Should indicate parsing issues
                metadata = result.get("metadata", {})
                assert "parsing_error" in metadata.get("notes", "") or len(result.get("patterns", [])) == 0
            else:
                # Or return appropriate error
                assert response.status_code == 400
                result = response.json()
                assert "invalid" in result.get("detail", "").lower() or "parse" in result.get("detail", "").lower()
    
    @pytest.mark.asyncio
    async def test_feature_extraction_failure(self, pipeline_client):
        """Test feature extraction failure handling."""
        with patch('pattern_mining.features.extractor.FeatureExtractor.extract_features') as mock_extract:
            mock_extract.side_effect = Exception("Feature extraction failed")
            
            request_data = {
                "code": "def test_function(): return 'test'",
                "language": "python",
                "detection_types": ["heuristic"]
            }
            
            response = await pipeline_client.post("/api/v1/patterns/detect", json=request_data)
            
            # Should handle gracefully with degraded functionality
            if response.status_code == 200:
                result = response.json()
                metadata = result.get("metadata", {})
                assert "feature_extraction_failed" in metadata.get("notes", "")
            else:
                assert response.status_code >= 400
    
    @pytest.mark.asyncio
    async def test_pattern_detection_failure(self, pipeline_client):
        """Test pattern detection failure handling."""
        with patch('pattern_mining.detectors.manager.DetectorManager.detect_patterns') as mock_detect:
            mock_detect.side_effect = Exception("Pattern detection failed")
            
            request_data = {
                "code": "def test_function(): return 'test'",
                "language": "python",
                "detection_types": ["heuristic", "ml_inference"]
            }
            
            response = await pipeline_client.post("/api/v1/patterns/detect", json=request_data)
            
            # Should return error or empty results
            if response.status_code == 200:
                result = response.json()
                assert len(result.get("patterns", [])) == 0
                metadata = result.get("metadata", {})
                assert "detection_failed" in metadata.get("notes", "")
            else:
                assert response.status_code >= 400
    
    @pytest.mark.asyncio
    async def test_result_serialization_failure(self, pipeline_client):
        """Test result serialization failure handling."""
        with patch('pattern_mining.models.patterns.DetectedPattern.dict') as mock_dict:
            # Simulate serialization failure
            mock_dict.side_effect = Exception("Serialization failed")
            
            request_data = {
                "code": "def test_function(): return 'test'",
                "language": "python",
                "detection_types": ["heuristic"]
            }
            
            response = await pipeline_client.post("/api/v1/patterns/detect", json=request_data)
            
            # Should handle serialization error gracefully
            if response.status_code == 200:
                result = response.json()
                # Should return basic result structure
                assert "patterns" in result
                assert "metadata" in result
            else:
                assert response.status_code >= 400


@pytest.mark.integration
class TestRecoveryScenarios:
    """Test service recovery scenarios."""
    
    @pytest.fixture
    async def recovery_app(self):
        """Create app for recovery testing."""
        return create_app()
    
    @pytest.fixture
    async def recovery_client(self, recovery_app):
        """Create client for recovery testing."""
        async with AsyncClient(app=recovery_app, base_url="http://test") as client:
            yield client
    
    @pytest.mark.asyncio
    async def test_service_restart_recovery(self, recovery_client):
        """Test service recovery after restart."""
        # Simulate service restart by reinitializing components
        health_checker = HealthChecker()
        
        # Check initial health
        initial_health = await health_checker.check_health()
        assert initial_health.status == "healthy"
        
        # Simulate restart by clearing internal state
        with patch.object(health_checker, '_initialized', False):
            # Should reinitialize automatically
            post_restart_health = await health_checker.check_health()
            assert post_restart_health.status == "healthy"
    
    @pytest.mark.asyncio
    async def test_database_reconnection(self, recovery_client):
        """Test database reconnection after connection loss."""
        reconnect_count = 0
        
        async def mock_get_session():
            nonlocal reconnect_count
            reconnect_count += 1
            
            if reconnect_count == 1:
                # First call fails (connection lost)
                raise Exception("Database connection lost")
            else:
                # Subsequent calls succeed (reconnected)
                return Mock()
        
        with patch('pattern_mining.database.connection.get_database_session', side_effect=mock_get_session):
            # Health check should eventually succeed after reconnection
            for attempt in range(3):
                response = await recovery_client.get("/health/ready")
                if response.status_code == 200:
                    break
                await asyncio.sleep(0.1)  # Brief delay between attempts
            
            # Should eventually succeed
            assert response.status_code == 200
            assert reconnect_count > 1  # Should have retried
    
    @pytest.mark.asyncio
    async def test_cache_recovery_after_failure(self, recovery_client):
        """Test cache recovery after Redis failure."""
        cache_attempts = 0
        
        async def mock_redis_operation(*args, **kwargs):
            nonlocal cache_attempts
            cache_attempts += 1
            
            if cache_attempts <= 2:
                # First 2 attempts fail
                raise Exception("Redis connection failed")
            else:
                # Subsequent attempts succeed
                return True
        
        with patch('pattern_mining.cache.redis_client.RedisClient.ping', side_effect=mock_redis_operation):
            request_data = {
                "code": "def test_function(): return 'test'",
                "language": "python",
                "detection_types": ["heuristic"]
            }
            
            # Multiple requests should show cache recovery
            responses = []
            for i in range(5):
                response = await recovery_client.post("/api/v1/patterns/detect", json=request_data)
                responses.append(response)
                await asyncio.sleep(0.1)
            
            # All requests should succeed
            for response in responses:
                assert response.status_code == 200
            
            # Should have attempted cache operations multiple times
            assert cache_attempts > 2
    
    @pytest.mark.asyncio
    async def test_graceful_shutdown_and_startup(self, recovery_client):
        """Test graceful shutdown and startup procedures."""
        # Test health check during shutdown
        with patch('pattern_mining.api.main.cleanup_dependencies') as mock_cleanup:
            response = await recovery_client.get("/health")
            assert response.status_code == 200
            
            # Simulate shutdown
            mock_cleanup.return_value = None
            
            # Health should still work during graceful shutdown
            response = await recovery_client.get("/health")
            assert response.status_code == 200
    
    @pytest.mark.asyncio
    async def test_error_rate_recovery(self, recovery_client):
        """Test service recovery after high error rates."""
        error_rate = 0
        request_count = 0
        
        async def variable_error_response(*args, **kwargs):
            nonlocal error_rate, request_count
            request_count += 1
            
            # High error rate initially, then recovery
            if request_count <= 5:
                error_rate = 0.8  # 80% error rate
            else:
                error_rate = 0.1  # 10% error rate (recovered)
            
            if request_count * error_rate > request_count:
                raise Exception("Service error")
            
            return {
                "candidates": [{
                    "content": {
                        "parts": [{"text": json.dumps({"patterns": [], "confidence": 0.8})}]
                    }
                }]
            }
        
        with patch('pattern_mining.ml.gemini_client.GeminiClient.generate_content', side_effect=variable_error_response):
            request_data = {
                "code": "def test_function(): return 'test'",
                "language": "python",
                "detection_types": ["ml_inference"]
            }
            
            success_count = 0
            total_requests = 10
            
            # Make multiple requests to test recovery
            for i in range(total_requests):
                try:
                    response = await recovery_client.post("/api/v1/patterns/detect", json=request_data)
                    if response.status_code == 200:
                        success_count += 1
                except:
                    pass  # Count failures
                
                await asyncio.sleep(0.1)
            
            # Success rate should improve over time (service recovery)
            success_rate = success_count / total_requests
            assert success_rate > 0.3  # Should have some recovery


if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"])