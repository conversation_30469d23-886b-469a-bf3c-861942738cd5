"""
Service Workflows Integration Tests

Real integration tests for multi-service end-to-end workflows, testing actual service
interactions, failure scenarios, and service degradation handling.
"""

import pytest
import asyncio
import os
import time
import uuid
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from unittest.mock import patch, AsyncMock
import json

from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

from pattern_mining.database.connection import DatabaseManager
from pattern_mining.cache.redis_client import RedisClient, RedisConfig
from pattern_mining.ml.gemini_client import GeminiClient
from pattern_mining.ml.gemini_analyzer import GeminiPatternAnalyzer
from pattern_mining.config.database import get_database_config
from pattern_mining.config.gemini import get_gemini_config, GeminiModel
from pattern_mining.models.patterns import PatternType, SeverityLevel, DetectionType
from pattern_mining.api.main import create_app


@pytest.mark.integration
@pytest.mark.asyncio
class TestEndToEndWorkflowIntegration:
    """Integration tests for complete end-to-end workflows."""
    
    @pytest.fixture
    def database_url(self):
        """Get test database URL."""
        return os.getenv(
            "TEST_DATABASE_URL",
            "postgresql://postgres:INSECURE_PASSWORD@localhost:5432/pattern_mining_test"
        )
    
    @pytest.fixture
    def redis_url(self):
        """Get test Redis URL.""" 
        return os.getenv(
            "TEST_REDIS_URL",
            "redis://localhost:6379/4"  # Use DB 4 for workflow testing
        )
    
    @pytest.fixture
    def gemini_api_key(self):
        """Get test Gemini API key."""
        api_key = os.getenv("GEMINI_API_KEY_TEST")
        if not api_key:
            pytest.skip("GEMINI_API_KEY_TEST not set - skipping service workflow integration tests")
        return api_key
    
    @pytest.fixture
    async def integrated_services(self, database_url, redis_url, gemini_api_key):
        """Setup integrated services for testing."""
        # Database setup
        db_config = get_database_config()
        db_config.database_url = database_url
        db_manager = DatabaseManager(db_config)
        await db_manager.initialize()
        
        # Redis setup
        redis_config = RedisConfig(url=redis_url)
        redis_client = RedisClient(redis_config)
        await redis_client.initialize()
        await redis_client.flushdb()  # Clear test cache
        
        # Gemini setup
        gemini_config = get_gemini_config()
        gemini_config.api_key = gemini_api_key
        gemini_config.model = GeminiModel.GEMINI_2_5_FLASH
        gemini_client = GeminiClient(gemini_config)
        await gemini_client.initialize()
        
        services = {
            "database": db_manager,
            "redis": redis_client,
            "gemini": gemini_client
        }
        
        yield services
        
        # Cleanup
        await redis_client.flushdb()
        await redis_client.close()
        await gemini_client.close()
        await db_manager.close()
    
    @pytest.fixture
    async def test_app_client(self, integrated_services):
        """Create test application client with real services."""
        # Override service dependencies with test services
        app = create_app()
        
        async with AsyncClient(app=app, base_url="http://test") as client:
            yield client
    
    async def test_complete_pattern_analysis_workflow(self, integrated_services):
        """Test complete pattern analysis workflow with all services."""
        db_manager = integrated_services["database"]
        redis_client = integrated_services["redis"]
        gemini_client = integrated_services["gemini"]
        
        # Sample code for analysis
        code_sample = """
        class DatabaseConnection:
            _instance = None
            
            def __new__(cls):
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance.connection = None
                return cls._instance
            
            def connect(self):
                if self.connection is None:
                    self.connection = "database_connection"
                return self.connection
            
            def query(self, sql):
                # Potential SQL injection vulnerability
                return f"SELECT * FROM users WHERE id = {sql}"
        """
        
        repository_id = "integration-test-repo"
        
        # Step 1: Analyze code with Gemini
        analyzer = GeminiPatternAnalyzer(gemini_client)
        patterns = await analyzer.analyze_code(
            code=code_sample,
            language="python",
            file_path="database.py"
        )
        
        assert len(patterns) > 0
        print(f"Gemini detected {len(patterns)} patterns")
        
        # Step 2: Store patterns in database
        async with db_manager.get_session() as session:
            for pattern in patterns:
                pattern_data = {
                    "id": str(uuid.uuid4()),
                    "repository_id": repository_id,
                    "pattern_name": pattern["pattern_name"],
                    "pattern_type": pattern["pattern_type"],
                    "severity": pattern.get("severity", SeverityLevel.MEDIUM.value),
                    "confidence": pattern["confidence"],
                    "file_path": "database.py",
                    "line_number": pattern["location"]["line"],
                    "column_number": pattern["location"].get("column", 1),
                    "description": pattern["description"],
                    "detection_method": DetectionType.ML_INFERENCE.value,
                    "context": pattern.get("context", {}),
                    "metadata": pattern.get("metadata", {}),
                    "created_at": datetime.utcnow(),
                    "updated_at": datetime.utcnow()
                }
                
                # Insert into database
                await session.execute(
                    """
                    INSERT INTO patterns (
                        id, repository_id, pattern_name, pattern_type, severity,
                        confidence, file_path, line_number, column_number,
                        description, detection_method, context, metadata,
                        created_at, updated_at
                    ) VALUES (
                        :id, :repository_id, :pattern_name, :pattern_type, :severity,
                        :confidence, :file_path, :line_number, :column_number,
                        :description, :detection_method, :context, :metadata,
                        :created_at, :updated_at
                    )
                    """,
                    pattern_data
                )
            
            await session.commit()
        
        # Step 3: Cache patterns in Redis
        cache_key = f"repo:{repository_id}:patterns"
        await redis_client.set_json(cache_key, patterns, ex=300)  # 5 minute cache
        
        # Step 4: Verify end-to-end workflow
        # Check database storage
        async with db_manager.get_session() as session:
            result = await session.execute(
                "SELECT COUNT(*) as count FROM patterns WHERE repository_id = :repo_id",
                {"repo_id": repository_id}
            )
            db_count = result.fetchone().count
            assert db_count == len(patterns)
        
        # Check cache storage
        cached_patterns = await redis_client.get_json(cache_key)
        assert cached_patterns is not None
        assert len(cached_patterns) == len(patterns)
        
        # Verify pattern types detected
        pattern_types = set(p["pattern_type"] for p in patterns)
        # Should detect singleton pattern and security issues
        assert PatternType.DESIGN_PATTERN.value in pattern_types or \
               PatternType.SECURITY_ISSUE.value in pattern_types
        
        print(f"Successfully completed end-to-end workflow: {len(patterns)} patterns")
    
    async def test_service_failure_recovery_workflow(self, integrated_services):
        """Test workflow recovery when services fail."""
        db_manager = integrated_services["database"]
        redis_client = integrated_services["redis"]
        gemini_client = integrated_services["gemini"]
        
        code_sample = "def simple_function(): return 'test'"
        
        # Test 1: Redis cache failure (should continue without cache)
        analyzer = GeminiPatternAnalyzer(gemini_client)
        
        # Simulate Redis failure
        original_set_json = redis_client.set_json
        redis_client.set_json = AsyncMock(side_effect=Exception("Redis connection failed"))
        
        try:
            patterns = await analyzer.analyze_code(
                code=code_sample,
                language="python",
                file_path="simple.py"
            )
            
            # Should still get patterns despite cache failure
            assert isinstance(patterns, list)
            print(f"Workflow continued despite Redis failure: {len(patterns)} patterns")
            
        finally:
            # Restore Redis function
            redis_client.set_json = original_set_json
        
        # Test 2: Database transaction failure recovery
        repository_id = "failure-test-repo"
        
        async with db_manager.get_session() as session:
            try:
                # Start transaction
                pattern_data = {
                    "id": str(uuid.uuid4()),
                    "repository_id": repository_id,
                    "pattern_name": "Test Pattern",
                    "pattern_type": PatternType.DESIGN_PATTERN.value,
                    "severity": SeverityLevel.LOW.value,
                    "confidence": 0.8,
                    "file_path": "test.py",
                    "line_number": 1,
                    "column_number": 1,
                    "description": "Test pattern",
                    "detection_method": DetectionType.ML_INFERENCE.value,
                    "context": {},
                    "metadata": {},
                    "created_at": datetime.utcnow(),
                    "updated_at": datetime.utcnow()
                }
                
                # Insert valid data
                await session.execute(
                    """
                    INSERT INTO patterns (
                        id, repository_id, pattern_name, pattern_type, severity,
                        confidence, file_path, line_number, column_number,
                        description, detection_method, context, metadata,
                        created_at, updated_at
                    ) VALUES (
                        :id, :repository_id, :pattern_name, :pattern_type, :severity,
                        :confidence, :file_path, :line_number, :column_number,
                        :description, :detection_method, :context, :metadata,
                        :created_at, :updated_at
                    )
                    """,
                    pattern_data
                )
                
                # Force error to test rollback
                await session.execute("INSERT INTO non_existent_table VALUES (1)")
                await session.commit()
                
            except Exception:
                # Transaction should rollback automatically
                await session.rollback()
                print("Database transaction rolled back as expected")
        
        # Verify rollback worked - pattern should not exist
        async with db_manager.get_session() as session:
            result = await session.execute(
                "SELECT COUNT(*) as count FROM patterns WHERE repository_id = :repo_id",
                {"repo_id": repository_id}
            )
            count = result.fetchone().count
            assert count == 0, "Transaction rollback failed"
    
    async def test_concurrent_service_operations_workflow(self, integrated_services):
        """Test concurrent operations across services."""
        db_manager = integrated_services["database"]
        redis_client = integrated_services["redis"]
        gemini_client = integrated_services["gemini"]
        
        # Prepare multiple code samples for concurrent analysis
        code_samples = [
            ("def add(a, b): return a + b", "math.py"),
            ("class Counter: def __init__(self): self.count = 0", "counter.py"),
            ("for i in range(10): print(i)", "loop.py"),
            ("try: result = operation() except: pass", "error_handling.py"),
            ("import os; os.system('ls')", "system_call.py")
        ]
        
        analyzer = GeminiPatternAnalyzer(gemini_client)
        
        # Concurrent analysis tasks
        async def analyze_and_store(code: str, filename: str, task_id: int):
            repository_id = f"concurrent-repo-{task_id}"
            
            # Step 1: Analyze with Gemini
            patterns = await analyzer.analyze_code(
                code=code,
                language="python", 
                file_path=filename
            )
            
            # Step 2: Store in database
            async with db_manager.get_session() as session:
                for i, pattern in enumerate(patterns):
                    pattern_data = {
                        "id": str(uuid.uuid4()),
                        "repository_id": repository_id,
                        "pattern_name": pattern["pattern_name"],
                        "pattern_type": pattern["pattern_type"],
                        "severity": pattern.get("severity", SeverityLevel.MEDIUM.value),
                        "confidence": pattern["confidence"],
                        "file_path": filename,
                        "line_number": pattern["location"]["line"],
                        "column_number": pattern["location"].get("column", 1),
                        "description": pattern["description"],
                        "detection_method": DetectionType.ML_INFERENCE.value,
                        "context": pattern.get("context", {}),
                        "metadata": pattern.get("metadata", {}),
                        "created_at": datetime.utcnow(),
                        "updated_at": datetime.utcnow()
                    }
                    
                    await session.execute(
                        """
                        INSERT INTO patterns (
                            id, repository_id, pattern_name, pattern_type, severity,
                            confidence, file_path, line_number, column_number,
                            description, detection_method, context, metadata,
                            created_at, updated_at
                        ) VALUES (
                            :id, :repository_id, :pattern_name, :pattern_type, :severity,
                            :confidence, :file_path, :line_number, :column_number,
                            :description, :detection_method, :context, :metadata,
                            :created_at, :updated_at
                        )
                        """,
                        pattern_data
                    )
                
                await session.commit()
            
            # Step 3: Cache results
            cache_key = f"repo:{repository_id}:patterns"
            await redis_client.set_json(cache_key, patterns, ex=300)
            
            return (task_id, len(patterns))
        
        # Execute concurrent tasks
        start_time = time.time()
        tasks = [
            analyze_and_store(code, filename, i) 
            for i, (code, filename) in enumerate(code_samples)
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        end_time = time.time()
        
        # Verify results
        successful_results = [r for r in results if not isinstance(r, Exception)]
        assert len(successful_results) >= 3  # At least 3 should succeed
        
        total_patterns = sum(result[1] for result in successful_results)
        assert total_patterns > 0
        
        # Verify database storage
        total_db_patterns = 0
        for i in range(len(code_samples)):
            repository_id = f"concurrent-repo-{i}"
            async with db_manager.get_session() as session:
                result = await session.execute(
                    "SELECT COUNT(*) as count FROM patterns WHERE repository_id = :repo_id",
                    {"repo_id": repository_id}
                )
                count = result.fetchone().count
                total_db_patterns += count
        
        assert total_db_patterns == total_patterns
        
        # Verify cache storage
        cached_count = 0
        for i in range(len(code_samples)):
            repository_id = f"concurrent-repo-{i}"
            cache_key = f"repo:{repository_id}:patterns"
            cached_patterns = await redis_client.get_json(cache_key)
            if cached_patterns:
                cached_count += len(cached_patterns)
        
        assert cached_count == total_patterns
        
        print(f"Concurrent workflow completed: {total_patterns} patterns in {end_time - start_time:.2f}s")
    
    async def test_service_degradation_workflow(self, integrated_services):
        """Test workflow under service degradation conditions."""
        db_manager = integrated_services["database"]
        redis_client = integrated_services["redis"]
        gemini_client = integrated_services["gemini"]
        
        code_sample = """
        def process_data(data):
            results = []
            for item in data:
                if item.startswith('valid_'):
                    results.append(item.upper())
            return results
        """
        
        analyzer = GeminiPatternAnalyzer(gemini_client)
        
        # Test 1: Slow Gemini API response
        original_timeout = gemini_client.config.request_timeout
        gemini_client.config.request_timeout = 0.1  # Very short timeout
        
        try:
            # Should handle timeout gracefully
            with pytest.raises((asyncio.TimeoutError, Exception)):
                await analyzer.analyze_code(
                    code=code_sample,
                    language="python",
                    file_path="slow_test.py"
                )
            
            print("Handled Gemini API timeout gracefully")
        
        finally:
            # Restore timeout
            gemini_client.config.request_timeout = original_timeout
        
        # Test 2: Database connection pool exhaustion simulation
        connections = []
        try:
            # Try to exhaust connection pool
            for i in range(15):  # More than typical pool size
                session = db_manager.get_session()
                connections.append(session)
            
            # Should still be able to get patterns with proper pool management
            patterns = await analyzer.analyze_code(
                code=code_sample,
                language="python",
                file_path="pool_test.py"
            )
            
            assert isinstance(patterns, list)
            print("Handled database connection pool pressure")
        
        finally:
            # Clean up connections
            for session in connections:
                await session.close()
        
        # Test 3: Redis memory pressure simulation
        # Fill Redis with data to simulate memory pressure
        for i in range(1000):
            large_data = "x" * 10000  # 10KB per entry
            await redis_client.set(f"memory_test_{i}", large_data, ex=60)
        
        # Should still work with memory pressure
        cache_key = "memory_pressure_test"
        test_data = {"pattern": "test", "confidence": 0.9}
        
        await redis_client.set_json(cache_key, test_data, ex=60)
        retrieved_data = await redis_client.get_json(cache_key)
        
        assert retrieved_data is not None
        assert retrieved_data["pattern"] == "test"
        
        print("Handled Redis memory pressure")
    
    async def test_data_consistency_workflow(self, integrated_services):
        """Test data consistency across services."""
        db_manager = integrated_services["database"]
        redis_client = integrated_services["redis"]
        gemini_client = integrated_services["gemini"]
        
        repository_id = "consistency-test-repo"
        code_sample = "class SimpleClass: pass"
        
        analyzer = GeminiPatternAnalyzer(gemini_client)
        
        # Step 1: Analyze and store patterns
        patterns = await analyzer.analyze_code(
            code=code_sample,
            language="python",
            file_path="simple_class.py"
        )
        
        # Step 2: Store in database
        stored_pattern_ids = []
        async with db_manager.get_session() as session:
            for pattern in patterns:
                pattern_id = str(uuid.uuid4())
                pattern_data = {
                    "id": pattern_id,
                    "repository_id": repository_id,
                    "pattern_name": pattern["pattern_name"],
                    "pattern_type": pattern["pattern_type"],
                    "severity": pattern.get("severity", SeverityLevel.MEDIUM.value),
                    "confidence": pattern["confidence"],
                    "file_path": "simple_class.py",
                    "line_number": pattern["location"]["line"],
                    "column_number": pattern["location"].get("column", 1),
                    "description": pattern["description"],
                    "detection_method": DetectionType.ML_INFERENCE.value,
                    "context": pattern.get("context", {}),
                    "metadata": pattern.get("metadata", {}),
                    "created_at": datetime.utcnow(),
                    "updated_at": datetime.utcnow()
                }
                
                await session.execute(
                    """
                    INSERT INTO patterns (
                        id, repository_id, pattern_name, pattern_type, severity,
                        confidence, file_path, line_number, column_number,
                        description, detection_method, context, metadata,
                        created_at, updated_at
                    ) VALUES (
                        :id, :repository_id, :pattern_name, :pattern_type, :severity,
                        :confidence, :file_path, :line_number, :column_number,
                        :description, :detection_method, :context, :metadata,
                        :created_at, :updated_at
                    )
                    """,
                    pattern_data
                )
                stored_pattern_ids.append(pattern_id)
            
            await session.commit()
        
        # Step 3: Cache patterns
        cache_key = f"repo:{repository_id}:patterns"
        await redis_client.set_json(cache_key, patterns, ex=300)
        
        # Step 4: Verify consistency
        # Check database count
        async with db_manager.get_session() as session:
            result = await session.execute(
                "SELECT COUNT(*) as count FROM patterns WHERE repository_id = :repo_id",
                {"repo_id": repository_id}
            )
            db_count = result.fetchone().count
        
        # Check cache count
        cached_patterns = await redis_client.get_json(cache_key)
        cache_count = len(cached_patterns) if cached_patterns else 0
        
        # Verify consistency
        assert db_count == len(patterns), f"Database count mismatch: {db_count} != {len(patterns)}"
        assert cache_count == len(patterns), f"Cache count mismatch: {cache_count} != {len(patterns)}"
        
        # Step 5: Test cache invalidation and reload
        await redis_client.delete(cache_key)
        
        # Reload from database
        async with db_manager.get_session() as session:
            result = await session.execute(
                """
                SELECT pattern_name, pattern_type, confidence 
                FROM patterns 
                WHERE repository_id = :repo_id
                ORDER BY created_at
                """,
                {"repo_id": repository_id}
            )
            db_patterns = result.fetchall()
        
        # Re-cache from database
        reloaded_patterns = [
            {
                "pattern_name": p.pattern_name,
                "pattern_type": p.pattern_type,
                "confidence": p.confidence
            }
            for p in db_patterns
        ]
        
        await redis_client.set_json(cache_key, reloaded_patterns, ex=300)
        
        # Verify reloaded cache
        final_cached_patterns = await redis_client.get_json(cache_key)
        assert len(final_cached_patterns) == len(patterns)
        
        print(f"Data consistency verified: {len(patterns)} patterns across all services")