"""
Advanced Integration Tests - Phase 2 Enhancement

Complex multi-service orchestration scenarios that test sophisticated interactions
between Pattern Mining service components. These tests validate:

1. Complex Service Orchestration - Multi-step workflows across all services
2. Service Dependency Chains - Cascading dependencies and failure propagation  
3. Advanced Error Propagation - Complex error handling across service boundaries
4. Performance Under Complex Load - Resource competition and bottleneck identification
5. State Management Complexity - Complex state transitions across services

Key Differentiators from Phase 1:
- Real service complexity simulation (not just simple success/failure)
- Advanced dependency chain testing
- Complex state transitions and consistency validation
- Resource competition scenarios
- Sophisticated recovery patterns

Test Categories:
- Complex Multi-Service Workflows
- Advanced Dependency Chain Testing
- Complex State Management Scenarios
- Resource Competition Analysis
- Advanced Recovery Validation
"""

import asyncio
import pytest
import time
import json
import uuid
import threading
import concurrent.futures
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from contextlib import asynccontextmanager
from dataclasses import dataclass, field

# FastAPI and HTTP testing
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

# Pattern mining imports
from pattern_mining.models.patterns import Pat<PERSON><PERSON>ype, SeverityLevel, DetectionType
from pattern_mining.models.api import PatternDetectionRequest, BatchDetectionRequest
from pattern_mining.models.database import PatternRecord, AnalysisRecord
from pattern_mining.database.connection import DatabaseManager
from pattern_mining.cache.redis_client import RedisClient, RedisConfig
from pattern_mining.ml.gemini_client import GeminiClient
from pattern_mining.ml.gemini_analyzer import GeminiPatternAnalyzer
from pattern_mining.config.database import get_database_config
from pattern_mining.config.gemini import get_gemini_config, GeminiModel

# Test utilities
from tests.utils.generators import TestDataGenerator, CodeGenerator, PatternGenerator
from tests.utils.performance import PerformanceTestRunner, LatencyMeasurer
from tests.utils.assertions import assert_service_health, assert_data_consistency


@dataclass
class ServiceState:
    """Track complex service state for advanced testing."""
    service_name: str
    health_status: str = "healthy"
    performance_degradation: float = 0.0  # 0.0 = no degradation, 1.0 = completely degraded
    error_rate: float = 0.0  # 0.0 = no errors, 1.0 = all requests fail
    resource_usage: Dict[str, float] = field(default_factory=dict)
    last_response_time: float = 0.0
    dependency_states: Dict[str, str] = field(default_factory=dict)
    recovery_attempts: int = 0
    last_error: Optional[str] = None
    cumulative_errors: List[str] = field(default_factory=list)


@dataclass 
class ComplexWorkflowState:
    """Track complex workflow execution state."""
    workflow_id: str
    current_stage: str = "initialized" 
    completed_stages: List[str] = field(default_factory=list)
    failed_stages: List[str] = field(default_factory=list)
    service_states: Dict[str, ServiceState] = field(default_factory=dict)
    data_consistency_markers: Dict[str, Any] = field(default_factory=dict)
    performance_metrics: Dict[str, float] = field(default_factory=dict)
    error_chain: List[Dict[str, Any]] = field(default_factory=list)
    recovery_actions: List[str] = field(default_factory=list)


@pytest.mark.integration
@pytest.mark.advanced
class TestComplexMultiServiceOrchestration:
    """Test complex multi-service orchestration scenarios."""
    
    @pytest.fixture
    async def complex_workflow_manager(self):
        """Create complex workflow manager for orchestration testing."""
        
        class ComplexWorkflowManager:
            def __init__(self):
                self.workflows: Dict[str, ComplexWorkflowState] = {}
                self.service_states: Dict[str, ServiceState] = {
                    "gemini": ServiceState("gemini"),
                    "database": ServiceState("database"), 
                    "redis": ServiceState("redis"),
                    "api": ServiceState("api")
                }
                self._lock = asyncio.Lock()
            
            async def create_workflow(self, workflow_id: str) -> ComplexWorkflowState:
                """Create a new complex workflow."""
                async with self._lock:
                    workflow = ComplexWorkflowState(
                        workflow_id=workflow_id,
                        service_states=self.service_states.copy()
                    )
                    self.workflows[workflow_id] = workflow
                    return workflow
            
            async def execute_stage(
                self, 
                workflow_id: str, 
                stage_name: str,
                stage_function,
                dependencies: List[str] = None
            ) -> Dict[str, Any]:
                """Execute a workflow stage with dependency checking."""
                workflow = self.workflows[workflow_id]
                
                # Check dependencies
                if dependencies:
                    for dep in dependencies:
                        if dep not in workflow.completed_stages:
                            raise Exception(f"Dependency {dep} not completed for stage {stage_name}")
                
                # Update workflow state
                workflow.current_stage = stage_name
                
                try:
                    # Execute stage with performance tracking
                    start_time = time.time()
                    result = await stage_function(workflow)
                    end_time = time.time()
                    
                    # Track performance
                    workflow.performance_metrics[stage_name] = end_time - start_time
                    workflow.completed_stages.append(stage_name)
                    
                    return result
                    
                except Exception as e:
                    # Track failure
                    workflow.failed_stages.append(stage_name)
                    workflow.error_chain.append({
                        "stage": stage_name,
                        "error": str(e),
                        "timestamp": datetime.utcnow().isoformat(),
                        "service_states": {k: v.health_status for k, v in workflow.service_states.items()}
                    })
                    
                    # Attempt recovery
                    recovery_result = await self._attempt_stage_recovery(workflow, stage_name, e)
                    if not recovery_result:
                        raise
                    
                    return recovery_result
            
            async def _attempt_stage_recovery(
                self, 
                workflow: ComplexWorkflowState, 
                stage_name: str, 
                error: Exception
            ) -> Optional[Dict[str, Any]]:
                """Attempt to recover from stage failure."""
                workflow.recovery_actions.append(f"Attempting recovery for {stage_name}: {str(error)}")
                
                # Simulate recovery logic based on error type
                if "connection" in str(error).lower():
                    # Connection error - try to reconnect
                    await asyncio.sleep(0.1)  # Simulate reconnection time
                    workflow.recovery_actions.append(f"Reconnection attempted for {stage_name}")
                    return {"status": "recovered", "method": "reconnection"}
                
                elif "timeout" in str(error).lower():
                    # Timeout error - retry with longer timeout
                    await asyncio.sleep(0.05)
                    workflow.recovery_actions.append(f"Retry with extended timeout for {stage_name}")
                    return {"status": "recovered", "method": "timeout_extension"}
                
                elif "degraded" in str(error).lower():
                    # Performance degradation - use fallback
                    workflow.recovery_actions.append(f"Fallback mode activated for {stage_name}")
                    return {"status": "degraded", "method": "fallback"}
                
                # No recovery possible
                return None
        
        return ComplexWorkflowManager()
    
    @pytest.mark.asyncio
    async def test_complex_pattern_analysis_workflow(
        self,
        async_test_client: AsyncClient,
        complex_workflow_manager,
        sample_code_data: Dict[str, Any]
    ):
        """Test complex pattern analysis workflow with multiple service interactions."""
        workflow_id = f"complex_analysis_{uuid.uuid4().hex[:8]}"
        workflow = await complex_workflow_manager.create_workflow(workflow_id)
        
        # Define complex workflow stages
        async def stage_1_code_preprocessing(workflow_state):
            """Stage 1: Code preprocessing with validation."""
            # Simulate complex preprocessing
            code_content = sample_code_data["python"]["code"]
            
            # Complex validation logic
            if len(code_content) < 10:
                raise Exception("Code too short for meaningful analysis")
            
            # Simulate AST generation with complexity
            ast_complexity = len(code_content.split('\n')) * 1.5
            
            return {
                "preprocessed_code": code_content,
                "ast_complexity": ast_complexity,
                "validation_passed": True,
                "preprocessing_metrics": {
                    "lines": len(code_content.split('\n')),
                    "complexity_score": ast_complexity
                }
            }
        
        async def stage_2_feature_extraction(workflow_state):
            """Stage 2: Complex feature extraction."""
            # Simulate feature extraction dependencies
            preprocessing_result = workflow_state.data_consistency_markers.get("stage_1")
            if not preprocessing_result:
                raise Exception("Preprocessing results not available")
            
            # Complex feature extraction simulation
            features = {
                "syntactic_features": ["function_def", "if_statement", "loop"],
                "semantic_features": ["business_logic", "data_processing"],
                "complexity_features": {
                    "cyclomatic_complexity": 3.2,
                    "cognitive_complexity": 2.8,
                    "nesting_depth": 2
                }
            }
            
            # Simulate dependency on database for feature templates
            await asyncio.sleep(0.02)  # Database query simulation
            
            return {
                "extracted_features": features,
                "feature_count": len(features["syntactic_features"]) + len(features["semantic_features"]),
                "extraction_quality": 0.95
            }
        
        async def stage_3_ml_inference(workflow_state):
            """Stage 3: ML inference with Gemini integration."""
            # Check dependencies
            features = workflow_state.data_consistency_markers.get("stage_2")
            if not features:
                raise Exception("Feature extraction results not available")
            
            # Simulate Gemini API call with potential issues
            gemini_service = workflow_state.service_states["gemini"]
            
            # Simulate service degradation
            if gemini_service.performance_degradation > 0.5:
                await asyncio.sleep(gemini_service.performance_degradation * 2)  # Slower response
            
            if gemini_service.error_rate > 0.3:
                if asyncio.get_event_loop().time() % 3 < gemini_service.error_rate * 3:
                    raise Exception("Gemini API temporary unavailable")
            
            # Successful inference
            patterns = [
                {
                    "pattern_id": f"ml_pattern_{i}",
                    "pattern_name": f"ML Detected Pattern {i}",
                    "pattern_type": PatternType.DESIGN_PATTERN.value,
                    "confidence": 0.85 - (gemini_service.performance_degradation * 0.2),
                    "location": {"line": i * 5, "column": 1},
                    "detection_method": DetectionType.ML_INFERENCE.value
                }
                for i in range(1, 4)
            ]
            
            return {
                "ml_patterns": patterns,
                "inference_confidence": 0.88,
                "model_version": "gemini-2.5-flash",
                "inference_time_ms": 150 + (gemini_service.performance_degradation * 500)
            }
        
        async def stage_4_pattern_validation(workflow_state):
            """Stage 4: Pattern validation and consolidation."""
            # Get ML patterns
            ml_results = workflow_state.data_consistency_markers.get("stage_3")
            if not ml_results:
                raise Exception("ML inference results not available")
            
            # Complex validation logic
            validated_patterns = []
            for pattern in ml_results["ml_patterns"]:
                # Simulate validation complexity
                if pattern["confidence"] > 0.7:
                    validated_patterns.append({
                        **pattern,
                        "validated": True,
                        "validation_score": pattern["confidence"] * 1.1
                    })
            
            # Simulate cross-validation with database
            await asyncio.sleep(0.03)
            
            return {
                "validated_patterns": validated_patterns,
                "validation_quality": len(validated_patterns) / len(ml_results["ml_patterns"]),
                "consolidation_score": 0.92
            }
        
        async def stage_5_persistence(workflow_state):
            """Stage 5: Multi-service persistence."""
            # Check dependencies
            patterns = workflow_state.data_consistency_markers.get("stage_4")
            if not patterns:
                raise Exception("Pattern validation results not available")
            
            # Simulate database transaction complexity
            db_service = workflow_state.service_states["database"]
            
            if db_service.error_rate > 0.2:
                if asyncio.get_event_loop().time() % 5 < db_service.error_rate * 5:
                    raise Exception("Database transaction failed")
            
            # Simulate Redis caching
            redis_service = workflow_state.service_states["redis"]
            cache_success = redis_service.error_rate < 0.1
            
            # Complex persistence logic
            await asyncio.sleep(0.05)  # Database operations
            
            persistence_result = {
                "database_records": len(patterns["validated_patterns"]),
                "cache_enabled": cache_success,
                "transaction_id": f"txn_{uuid.uuid4().hex[:8]}",
                "consistency_hash": hash(json.dumps(patterns, sort_keys=True))
            }
            
            # Cache results if Redis is available
            if cache_success:
                await asyncio.sleep(0.01)  # Redis operations
                persistence_result["cache_key"] = f"analysis_{workflow_id}"
            
            return persistence_result
        
        async def stage_6_response_generation(workflow_state):
            """Stage 6: Complex response generation."""
            # Aggregate all previous results
            all_stages = ["stage_1", "stage_2", "stage_3", "stage_4", "stage_5"]
            for stage in all_stages:
                if stage not in workflow_state.data_consistency_markers:
                    raise Exception(f"Missing dependency: {stage}")
            
            # Complex response aggregation
            total_processing_time = sum(workflow_state.performance_metrics.values())
            
            response = {
                "workflow_id": workflow_id,
                "analysis_result": {
                    "patterns_detected": len(workflow_state.data_consistency_markers["stage_4"]["validated_patterns"]),
                    "confidence_score": workflow_state.data_consistency_markers["stage_3"]["inference_confidence"],
                    "processing_time_ms": total_processing_time * 1000,
                    "service_health": {
                        name: state.health_status 
                        for name, state in workflow_state.service_states.items()
                    }
                },
                "metadata": {
                    "completed_stages": len(workflow_state.completed_stages),
                    "failed_stages": len(workflow_state.failed_stages),
                    "recovery_actions": len(workflow_state.recovery_actions),
                    "consistency_validated": True
                }
            }
            
            return response
        
        # Execute complex workflow with stage dependencies
        try:
            # Stage 1: Code Preprocessing
            result_1 = await complex_workflow_manager.execute_stage(
                workflow_id, "stage_1", stage_1_code_preprocessing
            )
            workflow.data_consistency_markers["stage_1"] = result_1
            
            # Stage 2: Feature Extraction (depends on stage 1)
            result_2 = await complex_workflow_manager.execute_stage(
                workflow_id, "stage_2", stage_2_feature_extraction, ["stage_1"]
            )
            workflow.data_consistency_markers["stage_2"] = result_2
            
            # Stage 3: ML Inference (depends on stage 2)
            result_3 = await complex_workflow_manager.execute_stage(
                workflow_id, "stage_3", stage_3_ml_inference, ["stage_2"]
            )
            workflow.data_consistency_markers["stage_3"] = result_3
            
            # Stage 4: Pattern Validation (depends on stage 3)
            result_4 = await complex_workflow_manager.execute_stage(
                workflow_id, "stage_4", stage_4_pattern_validation, ["stage_3"]
            )
            workflow.data_consistency_markers["stage_4"] = result_4
            
            # Stage 5: Persistence (depends on stage 4)
            result_5 = await complex_workflow_manager.execute_stage(
                workflow_id, "stage_5", stage_5_persistence, ["stage_4"]
            )
            workflow.data_consistency_markers["stage_5"] = result_5
            
            # Stage 6: Response Generation (depends on all previous stages)
            final_result = await complex_workflow_manager.execute_stage(
                workflow_id, "stage_6", stage_6_response_generation, 
                ["stage_1", "stage_2", "stage_3", "stage_4", "stage_5"]
            )
            
            # Validate complex workflow completion
            assert len(workflow.completed_stages) == 6
            assert len(workflow.failed_stages) == 0
            assert "workflow_id" in final_result
            assert final_result["analysis_result"]["patterns_detected"] > 0
            assert final_result["metadata"]["consistency_validated"] is True
            
            # Validate cross-stage data consistency
            assert workflow.data_consistency_markers["stage_4"]["validation_quality"] > 0.5
            assert workflow.data_consistency_markers["stage_5"]["database_records"] > 0
            
            # Performance validation
            total_time = sum(workflow.performance_metrics.values())
            assert total_time < 5.0  # Should complete within 5 seconds
            
            print(f"Complex workflow {workflow_id} completed successfully:")
            print(f"  - Stages completed: {len(workflow.completed_stages)}")
            print(f"  - Total time: {total_time:.3f}s")
            print(f"  - Patterns detected: {final_result['analysis_result']['patterns_detected']}")
            print(f"  - Recovery actions: {len(workflow.recovery_actions)}")
            
            return final_result
            
        except Exception as e:
            # Analyze failure
            print(f"Complex workflow {workflow_id} failed:")
            print(f"  - Completed stages: {workflow.completed_stages}")
            print(f"  - Failed stages: {workflow.failed_stages}")
            print(f"  - Error chain: {workflow.error_chain}")
            print(f"  - Recovery actions: {workflow.recovery_actions}")
            raise
    
    @pytest.mark.asyncio
    async def test_service_dependency_chain_failure_propagation(
        self,
        async_test_client: AsyncClient,
        complex_workflow_manager
    ):
        """Test complex service dependency chain failure propagation."""
        workflow_id = f"dependency_chain_{uuid.uuid4().hex[:8]}"
        workflow = await complex_workflow_manager.create_workflow(workflow_id)
        
        # Create complex dependency chain: API → Gemini → Database → Redis → Response
        async def api_service_stage(workflow_state):
            """API service with dependency on downstream services."""
            api_service = workflow_state.service_states["api"]
            
            # API depends on all downstream services being healthy
            downstream_services = ["gemini", "database", "redis"]
            for service_name in downstream_services:
                service = workflow_state.service_states[service_name]
                if service.health_status != "healthy":
                    api_service.health_status = "degraded"
                    raise Exception(f"API degraded due to {service_name} service issues")
            
            return {"api_status": "healthy", "downstream_dependencies": downstream_services}
        
        async def gemini_service_stage(workflow_state):
            """Gemini service with database and cache dependencies."""
            gemini_service = workflow_state.service_states["gemini"]
            
            # Simulate Gemini depending on database for context and Redis for caching
            db_service = workflow_state.service_states["database"]
            redis_service = workflow_state.service_states["redis"]
            
            # Complex dependency logic
            if db_service.health_status != "healthy":
                gemini_service.performance_degradation = 0.7  # Significantly degraded
                await asyncio.sleep(0.1)  # Slower processing
            
            if redis_service.health_status != "healthy":
                gemini_service.performance_degradation += 0.3  # Additional degradation
                await asyncio.sleep(0.05)  # Cache miss penalty
            
            # If too degraded, fail
            if gemini_service.performance_degradation > 0.8:
                gemini_service.health_status = "unhealthy"
                raise Exception("Gemini service failed due to dependency issues")
            
            return {
                "gemini_status": "healthy" if gemini_service.performance_degradation < 0.5 else "degraded",
                "performance_impact": gemini_service.performance_degradation,
                "dependencies_checked": ["database", "redis"]
            }
        
        async def database_service_stage(workflow_state):
            """Database service with Redis dependency for connection pooling."""
            db_service = workflow_state.service_states["database"]
            redis_service = workflow_state.service_states["redis"]
            
            # Database uses Redis for connection pool management
            if redis_service.health_status != "healthy":
                db_service.performance_degradation = 0.4  # Connection pool issues
                db_service.error_rate = 0.2  # Some connection failures
            
            # Simulate database operation with potential failure
            if db_service.error_rate > 0.15:
                if asyncio.get_event_loop().time() % 4 < db_service.error_rate * 4:
                    db_service.health_status = "unhealthy"
                    raise Exception("Database connection pool exhausted")
            
            return {
                "database_status": db_service.health_status,
                "connection_pool_impact": db_service.performance_degradation,
                "error_rate": db_service.error_rate
            }
        
        async def redis_service_stage(workflow_state):
            """Redis service - foundational service."""
            redis_service = workflow_state.service_states["redis"]
            
            # Simulate Redis health check
            await asyncio.sleep(0.01)
            
            # Redis can fail independently
            base_failure_rate = 0.1
            if asyncio.get_event_loop().time() % 10 < base_failure_rate * 10:
                redis_service.health_status = "unhealthy"
                redis_service.error_rate = 1.0
                raise Exception("Redis service unavailable")
            
            return {
                "redis_status": "healthy",
                "cache_performance": "optimal"
            }
        
        # Test Case 1: Normal operation (all services healthy)
        print("Test Case 1: Normal operation")
        try:
            # Execute dependency chain in reverse order (foundational services first)
            await complex_workflow_manager.execute_stage(workflow_id, "redis", redis_service_stage)
            await complex_workflow_manager.execute_stage(workflow_id, "database", database_service_stage, ["redis"])
            await complex_workflow_manager.execute_stage(workflow_id, "gemini", gemini_service_stage, ["database", "redis"])
            await complex_workflow_manager.execute_stage(workflow_id, "api", api_service_stage, ["gemini", "database", "redis"])
            
            assert len(workflow.completed_stages) == 4
            assert all(service.health_status == "healthy" for service in workflow.service_states.values())
            print("  ✓ All services healthy, dependency chain successful")
            
        except Exception as e:
            print(f"  ✗ Unexpected failure in normal operation: {e}")
        
        # Test Case 2: Redis failure propagation
        print("Test Case 2: Redis failure propagation")
        workflow_2 = await complex_workflow_manager.create_workflow(f"{workflow_id}_redis_fail")
        
        # Force Redis failure
        workflow_2.service_states["redis"].health_status = "unhealthy"
        workflow_2.service_states["redis"].error_rate = 1.0
        
        failure_propagation = []
        
        try:
            await complex_workflow_manager.execute_stage(f"{workflow_id}_redis_fail", "redis", redis_service_stage)
        except Exception:
            failure_propagation.append("redis")
        
        try:
            await complex_workflow_manager.execute_stage(f"{workflow_id}_redis_fail", "database", database_service_stage, ["redis"])
        except Exception:
            failure_propagation.append("database")
        
        try:
            await complex_workflow_manager.execute_stage(f"{workflow_id}_redis_fail", "gemini", gemini_service_stage, ["database", "redis"])
        except Exception:
            failure_propagation.append("gemini")
        
        try:
            await complex_workflow_manager.execute_stage(f"{workflow_id}_redis_fail", "api", api_service_stage, ["gemini", "database", "redis"])
        except Exception:
            failure_propagation.append("api")
        
        # Validate failure propagation
        assert "redis" in failure_propagation
        assert len(failure_propagation) >= 2  # Redis failure should propagate
        print(f"  ✓ Redis failure propagated to: {failure_propagation}")
        
        # Test Case 3: Partial failure with degradation
        print("Test Case 3: Partial failure with degradation")
        workflow_3 = await complex_workflow_manager.create_workflow(f"{workflow_id}_partial")
        
        # Set up partial degradation
        workflow_3.service_states["database"].performance_degradation = 0.6
        workflow_3.service_states["database"].error_rate = 0.1
        
        degradation_effects = {}
        
        try:
            result = await complex_workflow_manager.execute_stage(f"{workflow_id}_partial", "redis", redis_service_stage)
            degradation_effects["redis"] = "healthy"
        except Exception:
            degradation_effects["redis"] = "failed"
        
        try:
            result = await complex_workflow_manager.execute_stage(f"{workflow_id}_partial", "database", database_service_stage, ["redis"])
            degradation_effects["database"] = f"degraded_{workflow_3.service_states['database'].performance_degradation}"
        except Exception:
            degradation_effects["database"] = "failed"
        
        try:
            result = await complex_workflow_manager.execute_stage(f"{workflow_id}_partial", "gemini", gemini_service_stage, ["database", "redis"])
            degradation_effects["gemini"] = f"degraded_{workflow_3.service_states['gemini'].performance_degradation}"
        except Exception:
            degradation_effects["gemini"] = "failed"
        
        # Validate degradation propagation
        assert degradation_effects["redis"] == "healthy"
        assert "degraded" in degradation_effects.get("database", "") or degradation_effects.get("database") == "failed"
        print(f"  ✓ Partial failure degradation effects: {degradation_effects}")
        
        # Validate error chain analysis
        total_error_events = sum(len(wf.error_chain) for wf in [workflow, workflow_2, workflow_3])
        total_recovery_attempts = sum(len(wf.recovery_actions) for wf in [workflow, workflow_2, workflow_3])
        
        print(f"Dependency chain analysis complete:")
        print(f"  - Total error events tracked: {total_error_events}")
        print(f"  - Total recovery attempts: {total_recovery_attempts}")
        print(f"  - Failure propagation patterns validated: 3")
        
        assert total_error_events > 0  # Should have captured failure events
        assert total_recovery_attempts >= total_error_events  # Should attempt recovery
    
    @pytest.mark.asyncio 
    async def test_complex_state_management_scenarios(
        self,
        async_test_client: AsyncClient,
        complex_workflow_manager
    ):
        """Test complex state management across multiple services."""
        workflow_id = f"state_mgmt_{uuid.uuid4().hex[:8]}"
        workflow = await complex_workflow_manager.create_workflow(workflow_id)
        
        # Create complex shared state that must remain consistent
        shared_state = {
            "analysis_session": {
                "session_id": f"session_{uuid.uuid4().hex[:8]}",
                "repository_id": "complex_repo_123",
                "analysis_config": {
                    "confidence_threshold": 0.8,
                    "max_patterns": 100,
                    "enable_ml": True,
                    "enable_caching": True
                },
                "progress": {
                    "total_files": 50,
                    "processed_files": 0,
                    "detected_patterns": 0,
                    "current_file": None
                },
                "consistency_markers": {}
            }
        }
        
        async def initialize_shared_state(workflow_state):
            """Initialize shared state across all services."""
            # Store in workflow data consistency markers
            workflow_state.data_consistency_markers["shared_state"] = shared_state
            
            # Simulate state initialization in each service
            services = ["database", "redis", "gemini", "api"]
            for service_name in services:
                service = workflow_state.service_states[service_name] 
                service.resource_usage[f"state_size"] = len(json.dumps(shared_state))
                
                # Add consistency marker
                shared_state["analysis_session"]["consistency_markers"][service_name] = {
                    "initialized_at": datetime.utcnow().isoformat(),
                    "state_hash": hash(json.dumps(shared_state, sort_keys=True)),
                    "service_version": f"{service_name}_v1.0"
                }
            
            return {"initialized_services": services, "state_size": len(json.dumps(shared_state))}
        
        async def concurrent_state_modifications(workflow_state):
            """Simulate concurrent state modifications from multiple services."""
            current_state = workflow_state.data_consistency_markers["shared_state"]
            
            # Simulate concurrent modifications
            async def modify_from_api_service():
                """API service modifies user-facing state."""
                await asyncio.sleep(0.01)
                current_state["analysis_session"]["progress"]["current_file"] = "api_processing_file.py"
                current_state["analysis_session"]["consistency_markers"]["api"]["last_update"] = datetime.utcnow().isoformat()
                return "api_modification"
            
            async def modify_from_ml_service():
                """ML service modifies pattern detection state.""" 
                await asyncio.sleep(0.015)
                current_state["analysis_session"]["progress"]["detected_patterns"] += 5
                current_state["analysis_session"]["consistency_markers"]["gemini"]["patterns_added"] = 5
                return "ml_modification"
            
            async def modify_from_database_service():
                """Database service modifies persistence state."""
                await asyncio.sleep(0.02)
                current_state["analysis_session"]["progress"]["processed_files"] += 3
                current_state["analysis_session"]["consistency_markers"]["database"]["files_persisted"] = 3
                return "db_modification"
            
            async def modify_from_cache_service():
                """Cache service modifies caching state."""
                await asyncio.sleep(0.005)
                current_state["analysis_session"]["analysis_config"]["cache_hits"] = 15
                current_state["analysis_session"]["consistency_markers"]["redis"]["cache_updates"] = 15
                return "cache_modification"
            
            # Execute modifications concurrently
            modifications = await asyncio.gather(
                modify_from_api_service(),
                modify_from_ml_service(), 
                modify_from_database_service(),
                modify_from_cache_service(),
                return_exceptions=True
            )
            
            # Validate state consistency after concurrent modifications
            consistency_check = {
                "total_modifications": len([m for m in modifications if not isinstance(m, Exception)]),
                "failed_modifications": len([m for m in modifications if isinstance(m, Exception)]),
                "state_hash_after": hash(json.dumps(current_state, sort_keys=True)),
                "consistency_markers_count": len(current_state["analysis_session"]["consistency_markers"])
            }
            
            return {
                "modifications_applied": modifications,
                "consistency_check": consistency_check,
                "final_state_size": len(json.dumps(current_state)) 
            }
        
        async def validate_cross_service_consistency(workflow_state):
            """Validate that state is consistent across all services."""
            current_state = workflow_state.data_consistency_markers["shared_state"]
            
            # Complex consistency validation
            consistency_issues = []
            
            # Check that progress counters are logical
            progress = current_state["analysis_session"]["progress"]
            if progress["processed_files"] > progress["total_files"]:
                consistency_issues.append("processed_files exceeds total_files")
            
            if progress["detected_patterns"] < 0:
                consistency_issues.append("negative pattern count")
            
            # Check that all services have consistency markers
            markers = current_state["analysis_session"]["consistency_markers"]
            expected_services = ["database", "redis", "gemini", "api"]
            for service in expected_services:
                if service not in markers:
                    consistency_issues.append(f"missing consistency marker for {service}")
                elif "initialized_at" not in markers[service]:
                    consistency_issues.append(f"incomplete consistency marker for {service}")
            
            # Check that modification timestamps are reasonable
            for service, marker in markers.items():
                if "last_update" in marker:
                    try:
                        update_time = datetime.fromisoformat(marker["last_update"])
                        if update_time > datetime.utcnow() + timedelta(seconds=1):
                            consistency_issues.append(f"future timestamp for {service}")
                    except ValueError:
                        consistency_issues.append(f"invalid timestamp format for {service}")
            
            # Simulate cross-service state validation
            await asyncio.sleep(0.03)
            
            return {
                "consistency_valid": len(consistency_issues) == 0,
                "consistency_issues": consistency_issues,
                "validated_services": len(expected_services),
                "final_state_hash": hash(json.dumps(current_state, sort_keys=True))
            }
        
        async def simulate_partial_failure_recovery(workflow_state):
            """Simulate recovery from partial state corruption."""
            current_state = workflow_state.data_consistency_markers["shared_state"]
            
            # Simulate partial state corruption
            original_progress = current_state["analysis_session"]["progress"].copy()
            
            # Corrupt some state
            current_state["analysis_session"]["progress"]["processed_files"] = -1  # Invalid
            current_state["analysis_session"]["progress"]["detected_patterns"] = None  # Invalid
            
            # Simulate detection and recovery
            recovery_actions = []
            
            # Detect corruption
            if current_state["analysis_session"]["progress"]["processed_files"] < 0:
                recovery_actions.append("detected_negative_processed_files")
                current_state["analysis_session"]["progress"]["processed_files"] = max(0, original_progress["processed_files"])
            
            if current_state["analysis_session"]["progress"]["detected_patterns"] is None:
                recovery_actions.append("detected_null_pattern_count")
                current_state["analysis_session"]["progress"]["detected_patterns"] = original_progress.get("detected_patterns", 0)
            
            # Add recovery timestamp
            current_state["analysis_session"]["consistency_markers"]["recovery"] = {
                "recovered_at": datetime.utcnow().isoformat(),
                "recovery_actions": recovery_actions,
                "state_restored": True
            }
            
            await asyncio.sleep(0.02)  # Recovery time
            
            return {
                "recovery_successful": len(recovery_actions) > 0,
                "recovery_actions": recovery_actions,
                "recovered_state_valid": all(
                    v >= 0 for v in [
                        current_state["analysis_session"]["progress"]["processed_files"],
                        current_state["analysis_session"]["progress"]["detected_patterns"]
                    ]
                )
            }
        
        # Execute complex state management test
        try:
            # Stage 1: Initialize shared state
            init_result = await complex_workflow_manager.execute_stage(
                workflow_id, "state_init", initialize_shared_state
            )
            assert init_result["initialized_services"] == ["database", "redis", "gemini", "api"]
            
            # Stage 2: Concurrent modifications
            concurrent_result = await complex_workflow_manager.execute_stage(
                workflow_id, "concurrent_mods", concurrent_state_modifications, ["state_init"]  
            )
            assert concurrent_result["consistency_check"]["total_modifications"] >= 3
            
            # Stage 3: Consistency validation
            validation_result = await complex_workflow_manager.execute_stage(
                workflow_id, "consistency_check", validate_cross_service_consistency, ["concurrent_mods"]
            )
            
            # Stage 4: Recovery testing
            recovery_result = await complex_workflow_manager.execute_stage(
                workflow_id, "recovery_test", simulate_partial_failure_recovery, ["consistency_check"]
            )
            assert recovery_result["recovery_successful"] is True
            assert recovery_result["recovered_state_valid"] is True
            
            # Final validation
            final_state = workflow.data_consistency_markers["shared_state"]
            
            # Validate complex state management
            assert len(workflow.completed_stages) == 4
            assert "consistency_markers" in final_state["analysis_session"]
            assert len(final_state["analysis_session"]["consistency_markers"]) >= 4  # All services + recovery
            assert "recovery" in final_state["analysis_session"]["consistency_markers"]
            
            # Performance validation
            total_time = sum(workflow.performance_metrics.values())
            assert total_time < 3.0  # Complex state operations should complete quickly
            
            print(f"Complex state management test completed:")
            print(f"  - Services with state: {len(final_state['analysis_session']['consistency_markers'])}")
            print(f"  - Concurrent modifications: {concurrent_result['consistency_check']['total_modifications']}")
            print(f"  - Recovery actions: {len(recovery_result['recovery_actions'])}")
            print(f"  - Final state valid: {validation_result['consistency_valid'] and recovery_result['recovered_state_valid']}")
            print(f"  - Total processing time: {total_time:.3f}s")
            
            return {
                "state_management_successful": True,
                "services_validated": len(final_state["analysis_session"]["consistency_markers"]),
                "recovery_tested": recovery_result["recovery_successful"],
                "performance_time": total_time
            }
            
        except Exception as e:
            print(f"Complex state management test failed: {e}")
            print(f"Completed stages: {workflow.completed_stages}")
            print(f"Error chain: {workflow.error_chain}")
            raise


@pytest.mark.integration
@pytest.mark.advanced
class TestResourceCompetitionScenarios:
    """Test resource competition scenarios between services."""
    
    @pytest.fixture
    async def resource_monitor(self):
        """Create resource monitoring for competition testing."""
        
        class ResourceMonitor:
            def __init__(self):
                self.resource_usage = {
                    "cpu": {"total": 100.0, "used": 0.0, "reserved": {}},
                    "memory": {"total": 1024.0, "used": 0.0, "reserved": {}},  # MB
                    "network": {"total": 1000.0, "used": 0.0, "reserved": {}},  # Mbps
                    "database_connections": {"total": 20, "used": 0, "reserved": {}},
                    "redis_connections": {"total": 100, "used": 0, "reserved": {}}
                }
                self.competition_events = []
                self._lock = asyncio.Lock()
            
            async def reserve_resources(self, service_name: str, resources: Dict[str, float]) -> bool:
                """Reserve resources for a service."""
                async with self._lock:
                    # Check if resources are available
                    for resource_type, amount in resources.items():
                        if resource_type in self.resource_usage:
                            resource_info = self.resource_usage[resource_type]
                            available = resource_info["total"] - resource_info["used"]
                            if available < amount:
                                # Resource competition detected
                                self.competition_events.append({
                                    "service": service_name,
                                    "resource": resource_type,
                                    "requested": amount,
                                    "available": available,
                                    "timestamp": datetime.utcnow().isoformat(),
                                    "competition": True
                                })
                                return False
                    
                    # Reserve resources
                    for resource_type, amount in resources.items():
                        if resource_type in self.resource_usage:
                            resource_info = self.resource_usage[resource_type]
                            resource_info["used"] += amount
                            resource_info["reserved"][service_name] = resource_info["reserved"].get(service_name, 0) + amount
                    
                    return True
            
            async def release_resources(self, service_name: str, resources: Dict[str, float]):
                """Release resources from a service."""
                async with self._lock:
                    for resource_type, amount in resources.items():
                        if resource_type in self.resource_usage:
                            resource_info = self.resource_usage[resource_type]
                            resource_info["used"] = max(0, resource_info["used"] - amount)
                            if service_name in resource_info["reserved"]:
                                resource_info["reserved"][service_name] = max(0, resource_info["reserved"][service_name] - amount)
            
            def get_competition_analysis(self) -> Dict[str, Any]:
                """Analyze resource competition patterns."""
                competition_by_resource = {}
                competition_by_service = {}
                
                for event in self.competition_events:
                    resource = event["resource"]
                    service = event["service"]
                    
                    if resource not in competition_by_resource:
                        competition_by_resource[resource] = []
                    competition_by_resource[resource].append(event)
                    
                    if service not in competition_by_service:
                        competition_by_service[service] = []
                    competition_by_service[service].append(event)
                
                return {
                    "total_competition_events": len(self.competition_events),
                    "competition_by_resource": {k: len(v) for k, v in competition_by_resource.items()},
                    "competition_by_service": {k: len(v) for k, v in competition_by_service.items()},
                    "resource_utilization": {
                        resource_type: {
                            "used_percentage": (info["used"] / info["total"]) * 100,
                            "reserved_by_service": info["reserved"]
                        }
                        for resource_type, info in self.resource_usage.items()
                    }
                }
        
        return ResourceMonitor()
    
    @pytest.mark.asyncio
    async def test_database_connection_pool_competition(
        self,
        async_test_client: AsyncClient,
        resource_monitor
    ):
        """Test database connection pool competition between services."""
        
        async def service_database_operations(service_name: str, operation_count: int, connection_time: float):
            """Simulate database operations for a service."""
            results = []
            
            for i in range(operation_count):
                # Try to reserve database connection
                resources_needed = {"database_connections": 1}
                reserved = await resource_monitor.reserve_resources(service_name, resources_needed)
                
                if not reserved:
                    results.append({
                        "operation": i,
                        "status": "blocked",
                        "reason": "connection_pool_exhausted"
                    })
                    continue
                
                try:
                    # Simulate database operation
                    await asyncio.sleep(connection_time)
                    results.append({
                        "operation": i,
                        "status": "completed",
                        "duration": connection_time
                    })
                    
                finally:
                    # Release connection
                    await resource_monitor.release_resources(service_name, resources_needed)
            
            return {
                "service": service_name,
                "operations_attempted": operation_count,
                "operations_completed": len([r for r in results if r["status"] == "completed"]),
                "operations_blocked": len([r for r in results if r["status"] == "blocked"]),
                "results": results
            }
        
        # Simulate multiple services competing for database connections
        services = [
            ("pattern_analyzer", 15, 0.1),    # 15 operations, 100ms each
            ("cache_manager", 25, 0.05),      # 25 operations, 50ms each
            ("api_handler", 30, 0.03),        # 30 operations, 30ms each
            ("background_processor", 20, 0.2) # 20 operations, 200ms each
        ]
        
        # Execute all services concurrently
        tasks = [
            service_database_operations(name, ops, duration)
            for name, ops, duration in services
        ]
        
        start_time = time.time()
        results = await asyncio.gather(*tasks, return_exceptions=True)
        end_time = time.time()
        
        # Analyze competition results
        successful_results = [r for r in results if not isinstance(r, Exception)]
        assert len(successful_results) == len(services)
        
        total_operations = sum(r["operations_attempted"] for r in successful_results)
        total_completed = sum(r["operations_completed"] for r in successful_results)
        total_blocked = sum(r["operations_blocked"] for r in successful_results)
        
        # Validate resource competition occurred
        competition_analysis = resource_monitor.get_competition_analysis()
        assert competition_analysis["total_competition_events"] > 0
        assert "database_connections" in competition_analysis["competition_by_resource"]
        
        # Validate that some operations were blocked due to competition
        assert total_blocked > 0, "Expected some operations to be blocked due to connection pool exhaustion"
        
        # Validate performance impact
        execution_time = end_time - start_time
        
        print(f"Database connection pool competition test results:")
        print(f"  - Total operations attempted: {total_operations}")
        print(f"  - Operations completed: {total_completed}")
        print(f"  - Operations blocked: {total_blocked}")
        print(f"  - Competition events: {competition_analysis['total_competition_events']}")
        print(f"  - Execution time: {execution_time:.3f}s")
        print(f"  - Resource utilization: {competition_analysis['resource_utilization']['database_connections']['used_percentage']:.1f}%")
        
        # Service-specific analysis
        for result in successful_results:
            service_name = result["service"]
            blocked_percentage = (result["operations_blocked"] / result["operations_attempted"]) * 100
            print(f"  - {service_name}: {blocked_percentage:.1f}% operations blocked")
        
        return {
            "competition_detected": competition_analysis["total_competition_events"] > 0,
            "operations_blocked": total_blocked,
            "execution_time": execution_time,
            "services_affected": len(competition_analysis["competition_by_service"])
        }
    
    @pytest.mark.asyncio
    async def test_memory_pressure_competition(
        self,
        async_test_client: AsyncClient,
        resource_monitor
    ):
        """Test memory pressure competition between services."""
        
        async def memory_intensive_service(service_name: str, memory_chunks: List[float], processing_time: float):
            """Simulate memory-intensive service operations."""
            allocated_memory = []
            results = []
            
            for i, chunk_size in enumerate(memory_chunks):
                # Try to allocate memory
                resources_needed = {"memory": chunk_size}
                reserved = await resource_monitor.reserve_resources(service_name, resources_needed)
                
                if not reserved:
                    results.append({
                        "chunk": i,
                        "size_mb": chunk_size,
                        "status": "allocation_failed",
                        "reason": "insufficient_memory"
                    })
                    continue
                
                # Successfully allocated
                allocated_memory.append(chunk_size)
                results.append({
                    "chunk": i,
                    "size_mb": chunk_size,
                    "status": "allocated",
                    "total_allocated": sum(allocated_memory)
                })
                
                # Simulate processing with allocated memory
                await asyncio.sleep(processing_time)
            
            # Release all allocated memory
            for chunk_size in allocated_memory:
                await resource_monitor.release_resources(service_name, {"memory": chunk_size})
            
            return {
                "service": service_name,
                "chunks_attempted": len(memory_chunks),
                "chunks_allocated": len(allocated_memory),
                "total_memory_used": sum(allocated_memory),
                "allocation_failures": len([r for r in results if r["status"] == "allocation_failed"]),
                "results": results
            }
        
        # Simulate services with different memory usage patterns
        memory_patterns = [
            ("ml_inference", [200, 300, 250, 100], 0.1),     # Large chunks, steady usage
            ("cache_warmer", [50, 50, 50, 50, 50], 0.05),    # Small frequent allocations
            ("batch_processor", [400, 300, 200], 0.2),       # Very large chunks
            ("api_buffer", [30, 40, 35, 45, 50], 0.02)       # Small variable chunks
        ]
        
        # Execute memory competition test
        tasks = [
            memory_intensive_service(name, chunks, proc_time)
            for name, chunks, proc_time in memory_patterns
        ]
        
        start_time = time.time()
        results = await asyncio.gather(*tasks, return_exceptions=True)
        end_time = time.time()
        
        # Analyze memory competition
        successful_results = [r for r in results if not isinstance(r, Exception)]
        assert len(successful_results) == len(memory_patterns)
        
        total_allocation_attempts = sum(r["chunks_attempted"] for r in successful_results)
        total_allocation_failures = sum(r["allocation_failures"] for r in successful_results)  
        
        competition_analysis = resource_monitor.get_competition_analysis()
        
        # Validate memory competition occurred
        assert competition_analysis["total_competition_events"] > 0
        assert "memory" in competition_analysis["competition_by_resource"]
        assert total_allocation_failures > 0
        
        # Analyze memory pressure patterns
        memory_utilization = competition_analysis["resource_utilization"]["memory"]
        peak_memory_percentage = memory_utilization["used_percentage"]
        
        print(f"Memory pressure competition test results:")
        print(f"  - Total allocation attempts: {total_allocation_attempts}")
        print(f"  - Allocation failures: {total_allocation_failures}")
        print(f"  - Competition events: {competition_analysis['total_competition_events']}")
        print(f"  - Peak memory utilization: {peak_memory_percentage:.1f}%")
        print(f"  - Execution time: {end_time - start_time:.3f}s")
        
        # Service-specific memory analysis
        for result in successful_results:
            service_name = result["service"]
            failure_rate = (result["allocation_failures"] / result["chunks_attempted"]) * 100
            max_memory = result["total_memory_used"]
            print(f"  - {service_name}: {failure_rate:.1f}% failures, max {max_memory:.1f}MB")
        
        return {
            "memory_competition_detected": total_allocation_failures > 0,
            "peak_utilization_percent": peak_memory_percentage,
            "services_affected": len(competition_analysis["competition_by_service"]),
            "execution_time": end_time - start_time
        }
    
    @pytest.mark.asyncio
    async def test_network_bandwidth_competition(
        self,
        async_test_client: AsyncClient,
        resource_monitor
    ):
        """Test network bandwidth competition between services."""
        
        async def network_intensive_operation(service_name: str, data_transfers: List[float], transfer_time: float):
            """Simulate network-intensive operations."""
            completed_transfers = []
            results = []
            
            for i, bandwidth_needed in enumerate(data_transfers):
                # Try to reserve network bandwidth
                resources_needed = {"network": bandwidth_needed}
                reserved = await resource_monitor.reserve_resources(service_name, resources_needed)
                
                if not reserved:
                    results.append({
                        "transfer": i,
                        "bandwidth_mbps": bandwidth_needed,
                        "status": "throttled",
                        "reason": "insufficient_bandwidth"
                    })
                    continue
                
                try:
                    # Simulate data transfer
                    await asyncio.sleep(transfer_time)
                    completed_transfers.append(bandwidth_needed)
                    results.append({
                        "transfer": i,
                        "bandwidth_mbps": bandwidth_needed,
                        "status": "completed",
                        "duration": transfer_time
                    })
                    
                finally:
                    # Release bandwidth
                    await resource_monitor.release_resources(service_name, resources_needed)
            
            return {
                "service": service_name,
                "transfers_attempted": len(data_transfers),
                "transfers_completed": len(completed_transfers),
                "total_bandwidth_used": sum(completed_transfers),
                "throttled_transfers": len([r for r in results if r["status"] == "throttled"]),
                "results": results
            }
        
        # Simulate services with different network usage patterns
        network_patterns = [
            ("api_responses", [100, 150, 120, 80, 200], 0.1),     # Variable API traffic
            ("ml_model_download", [400, 300], 0.3),               # Large model downloads
            ("log_streaming", [50, 50, 50, 50, 50, 50], 0.05),   # Continuous logging
            ("backup_service", [250, 200, 300], 0.4)              # Large backup transfers
        ]
        
        # Execute network competition test
        tasks = [
            network_intensive_operation(name, transfers, time_per_transfer)
            for name, transfers, time_per_transfer in network_patterns
        ]
        
        start_time = time.time()
        results = await asyncio.gather(*tasks, return_exceptions=True)
        end_time = time.time()
        
        # Analyze network competition
        successful_results = [r for r in results if not isinstance(r, Exception)]
        assert len(successful_results) == len(network_patterns)
        
        total_transfer_attempts = sum(r["transfers_attempted"] for r in successful_results)
        total_throttled = sum(r["throttled_transfers"] for r in successful_results)
        
        competition_analysis = resource_monitor.get_competition_analysis()
        
        # Validate network competition occurred
        assert competition_analysis["total_competition_events"] > 0
        assert "network" in competition_analysis["competition_by_resource"]
        assert total_throttled > 0
        
        # Analyze network utilization patterns
        network_utilization = competition_analysis["resource_utilization"]["network"]
        
        print(f"Network bandwidth competition test results:")
        print(f"  - Total transfer attempts: {total_transfer_attempts}")
        print(f"  - Throttled transfers: {total_throttled}")
        print(f"  - Competition events: {competition_analysis['total_competition_events']}")
        print(f"  - Execution time: {end_time - start_time:.3f}s")
        
        # Service-specific network analysis
        for result in successful_results:
            service_name = result["service"]
            throttle_rate = (result["throttled_transfers"] / result["transfers_attempted"]) * 100
            total_bandwidth = result["total_bandwidth_used"]
            print(f"  - {service_name}: {throttle_rate:.1f}% throttled, {total_bandwidth:.1f}Mbps total")
        
        return {
            "network_competition_detected": total_throttled > 0,
            "throttled_operations": total_throttled,
            "services_competing": len(competition_analysis["competition_by_service"]),
            "execution_time": end_time - start_time
        }


@pytest.mark.integration
@pytest.mark.advanced
class TestRealisticProductionWorkflows:
    """Test realistic production workflows that mirror actual usage patterns."""
    
    @pytest.fixture
    async def production_workflow_simulator(self):
        """Create production workflow simulator for realistic testing."""
        
        class ProductionWorkflowSimulator:
            def __init__(self):
                self.user_sessions = {}
                self.repository_analyses = {}
                self.system_load_metrics = {
                    "concurrent_analyses": 0,
                    "cache_hit_rate": 0.0,
                    "database_connections": 0,
                    "ml_inference_queue": 0,
                    "memory_usage_mb": 0.0
                }
                self._lock = asyncio.Lock()
            
            async def simulate_realistic_user_session(
                self, 
                user_id: str,
                repositories: List[Dict[str, Any]],
                session_duration_minutes: int = 30
            ) -> Dict[str, Any]:
                """Simulate a realistic user session with multiple repository analyses."""
                session_start = time.time()
                session_results = {
                    "user_id": user_id,
                    "session_start": datetime.utcnow().isoformat(),
                    "repositories_analyzed": 0,
                    "patterns_found": 0,
                    "cache_hits": 0,
                    "cache_misses": 0,
                    "ml_inferences": 0,
                    "database_queries": 0,
                    "errors_encountered": [],
                    "performance_metrics": {
                        "avg_analysis_time": 0.0,
                        "peak_memory_usage": 0.0,
                        "network_requests": 0
                    }
                }
                
                # Simulate realistic user behavior patterns
                for i, repo in enumerate(repositories):
                    if time.time() - session_start > session_duration_minutes * 60:
                        break  # Session timeout
                    
                    # Realistic timing between analyses (users don't analyze continuously)
                    if i > 0:
                        think_time = random.uniform(5.0, 30.0)  # 5-30 seconds between analyses
                        await asyncio.sleep(min(think_time / 100, 0.1))  # Scaled for testing
                    
                    # Simulate repository analysis with realistic complexity
                    analysis_start = time.time()
                    
                    try:
                        # Stage 1: Authentication and authorization
                        auth_result = await self._simulate_authentication(user_id)
                        if not auth_result["success"]:
                            session_results["errors_encountered"].append("Authentication failed")
                            continue
                        
                        # Stage 2: Repository loading and validation
                        repo_load_result = await self._simulate_repository_loading(repo)
                        if not repo_load_result["success"]:
                            session_results["errors_encountered"].append(f"Repository load failed: {repo['name']}")
                            continue
                        
                        # Stage 3: Cache check (realistic cache behavior)
                        cache_key = f"repo_{repo['id']}_{repo.get('version', 'latest')}"
                        cache_result = await self._simulate_cache_lookup(cache_key)
                        if cache_result["hit"]:
                            session_results["cache_hits"] += 1
                            session_results["patterns_found"] += cache_result["patterns_count"]
                            session_results["repositories_analyzed"] += 1
                            continue
                        else:
                            session_results["cache_misses"] += 1
                        
                        # Stage 4: ML Analysis (most resource-intensive)
                        async with self._lock:
                            self.system_load_metrics["ml_inference_queue"] += 1
                        
                        try:
                            ml_result = await self._simulate_ml_analysis(repo, session_results)
                            session_results["ml_inferences"] += 1
                            session_results["patterns_found"] += ml_result["patterns_count"]
                            
                            # Stage 5: Database storage with transactions
                            db_result = await self._simulate_database_storage(
                                user_id, repo, ml_result["patterns"]
                            )
                            session_results["database_queries"] += db_result["queries_executed"]
                            
                            # Stage 6: Cache storage for future requests
                            await self._simulate_cache_storage(cache_key, ml_result)
                            
                            session_results["repositories_analyzed"] += 1
                            
                        finally:
                            async with self._lock:
                                self.system_load_metrics["ml_inference_queue"] -= 1
                        
                        # Update performance metrics
                        analysis_time = time.time() - analysis_start
                        session_results["performance_metrics"]["avg_analysis_time"] = (
                            (session_results["performance_metrics"]["avg_analysis_time"] * i + analysis_time) / (i + 1)
                        )
                        
                    except Exception as e:
                        session_results["errors_encountered"].append(f"Analysis error: {str(e)}")
                
                # Calculate session summary
                session_duration = time.time() - session_start
                session_results["session_duration_seconds"] = session_duration
                session_results["analyses_per_minute"] = session_results["repositories_analyzed"] / (session_duration / 60)
                session_results["cache_hit_rate"] = (
                    session_results["cache_hits"] / 
                    max(1, session_results["cache_hits"] + session_results["cache_misses"])
                )
                
                return session_results
            
            async def _simulate_authentication(self, user_id: str) -> Dict[str, Any]:
                """Simulate realistic authentication with occasional failures."""
                await asyncio.sleep(0.01)  # Auth service latency
                
                # 2% authentication failure rate (realistic for production)
                if random.random() < 0.02:
                    return {"success": False, "reason": "token_expired"}
                
                return {"success": True, "user_id": user_id, "permissions": ["read", "analyze"]}
            
            async def _simulate_repository_loading(self, repo: Dict[str, Any]) -> Dict[str, Any]:
                """Simulate repository loading with realistic failure patterns."""
                load_time = random.uniform(0.1, 0.5)  # 100-500ms load time
                await asyncio.sleep(load_time / 100)  # Scaled for testing
                
                # Failure rate depends on repository size
                repo_size = repo.get("size_mb", 10)
                failure_rate = min(0.05, repo_size / 1000)  # Up to 5% for very large repos
                
                if random.random() < failure_rate:
                    return {"success": False, "reason": "repository_too_large"}
                
                return {
                    "success": True,
                    "repo_id": repo["id"],
                    "files_count": repo.get("files", 50),
                    "load_time_ms": load_time * 1000
                }
            
            async def _simulate_cache_lookup(self, cache_key: str) -> Dict[str, Any]:
                """Simulate realistic cache behavior with hit/miss patterns."""
                await asyncio.sleep(0.002)  # 2ms cache lookup time
                
                # Realistic cache hit rate: 60% for popular repos, 20% for new repos
                if cache_key in self.repository_analyses:
                    age_minutes = (time.time() - self.repository_analyses[cache_key]["cached_at"]) / 60
                    # Cache expires after 24 hours, hit rate decreases with age
                    hit_probability = max(0.1, 0.8 - (age_minutes / 1440))  # 24 hours = 1440 minutes
                else:
                    hit_probability = 0.2  # New repos have low hit rate
                
                if random.random() < hit_probability:
                    return {
                        "hit": True,
                        "patterns_count": random.randint(5, 25),
                        "cached_at": self.repository_analyses.get(cache_key, {}).get("cached_at", time.time())
                    }
                
                return {"hit": False}
            
            async def _simulate_ml_analysis(
                self, 
                repo: Dict[str, Any], 
                session_context: Dict[str, Any]
            ) -> Dict[str, Any]:
                """Simulate realistic ML analysis with Gemini 2.5 Flash."""
                # Analysis time depends on repository complexity
                base_time = 0.5  # 500ms base
                complexity_factor = repo.get("complexity_score", 1.0)
                analysis_time = base_time * complexity_factor
                
                # System load affects analysis time
                load_factor = 1.0 + (self.system_load_metrics["ml_inference_queue"] * 0.1)
                actual_time = analysis_time * load_factor
                
                await asyncio.sleep(actual_time / 100)  # Scaled for testing
                
                # Success rate decreases under high load
                success_rate = max(0.85, 0.98 - (self.system_load_metrics["ml_inference_queue"] * 0.02))
                
                if random.random() > success_rate:
                    raise Exception("ML inference timeout")
                
                # Generate realistic pattern detection results
                patterns_count = random.randint(3, 20)
                patterns = []
                
                for i in range(patterns_count):
                    patterns.append({
                        "pattern_id": f"ml_pattern_{uuid.uuid4().hex[:8]}",
                        "pattern_type": random.choice(list(PatternType)).value,
                        "severity": random.choice(list(SeverityLevel)).value,
                        "confidence": random.uniform(0.7, 0.95),
                        "location": {
                            "file": f"src/module_{random.randint(1, 10)}.py",
                            "line": random.randint(1, 100)
                        }
                    })
                
                return {
                    "patterns_count": patterns_count,
                    "patterns": patterns,
                    "analysis_time_ms": actual_time * 1000,
                    "model_version": "gemini-2.5-flash",
                    "confidence_score": statistics.mean([p["confidence"] for p in patterns]) if patterns else 0.0
                }
            
            async def _simulate_database_storage(
                self, 
                user_id: str, 
                repo: Dict[str, Any], 
                patterns: List[Dict[str, Any]]
            ) -> Dict[str, Any]:
                """Simulate realistic database storage with transactions."""
                # Database operation time depends on data volume
                base_time = 0.05  # 50ms base
                data_factor = len(patterns) * 0.01  # 10ms per pattern
                storage_time = base_time + data_factor
                
                await asyncio.sleep(storage_time / 100)  # Scaled for testing
                
                # Simulate transaction with rollback possibility (1% failure rate)
                if random.random() < 0.01:
                    raise Exception("Database transaction deadlock")
                
                # Track database load
                async with self._lock:
                    self.system_load_metrics["database_connections"] += 1
                
                try:
                    # Simulate multiple queries in transaction
                    queries = [
                        "INSERT INTO analyses",
                        "INSERT INTO patterns (batch)",
                        "UPDATE user_statistics",
                        "INSERT INTO audit_log"
                    ]
                    
                    return {
                        "success": True,
                        "queries_executed": len(queries),
                        "patterns_stored": len(patterns),
                        "storage_time_ms": storage_time * 1000
                    }
                    
                finally:
                    async with self._lock:
                        self.system_load_metrics["database_connections"] -= 1
            
            async def _simulate_cache_storage(
                self, 
                cache_key: str, 
                analysis_result: Dict[str, Any]
            ) -> Dict[str, Any]:
                """Simulate cache storage with realistic patterns."""
                await asyncio.sleep(0.005)  # 5ms cache write time
                
                # Store in simulated cache
                self.repository_analyses[cache_key] = {
                    "patterns_count": analysis_result["patterns_count"],
                    "cached_at": time.time(),
                    "confidence_score": analysis_result["confidence_score"]
                }
                
                return {"success": True, "cache_key": cache_key}
        
        return ProductionWorkflowSimulator()
    
    @pytest.mark.asyncio
    async def test_concurrent_multi_user_realistic_sessions(
        self,
        async_test_client: AsyncClient,
        production_workflow_simulator
    ):
        """Test concurrent multi-user sessions with realistic usage patterns."""
        
        # Define realistic repository test data
        test_repositories = [
            {"id": "repo_1", "name": "web-frontend", "size_mb": 15, "complexity_score": 1.2, "files": 85},
            {"id": "repo_2", "name": "api-backend", "size_mb": 8, "complexity_score": 1.5, "files": 45},
            {"id": "repo_3", "name": "ml-models", "size_mb": 120, "complexity_score": 2.0, "files": 200},
            {"id": "repo_4", "name": "mobile-app", "size_mb": 25, "complexity_score": 1.1, "files": 150},
            {"id": "repo_5", "name": "data-pipeline", "size_mb": 35, "complexity_score": 1.8, "files": 75}
        ]
        
        # Simulate realistic user session patterns
        user_sessions = [
            ("user_enterprise_1", test_repositories[:3], 20),  # Power user, 20-min session
            ("user_developer_1", test_repositories[1:3], 15),  # Regular dev, 15-min session  
            ("user_analyst_1", [test_repositories[0], test_repositories[3]], 10),  # Quick analysis, 10-min session
            ("user_enterprise_2", test_repositories[2:], 25),  # Another power user, 25-min session
            ("user_developer_2", [test_repositories[1]], 5),   # Quick single repo check
            ("user_security_1", test_repositories, 30),       # Security audit, full session
        ]
        
        # Execute concurrent user sessions
        print(f"Starting {len(user_sessions)} concurrent user sessions...")
        start_time = time.time()
        
        session_tasks = [
            production_workflow_simulator.simulate_realistic_user_session(
                user_id, repos, duration
            )
            for user_id, repos, duration in user_sessions
        ]
        
        # Execute with realistic timing
        session_results = await asyncio.gather(*session_tasks, return_exceptions=True)
        total_time = time.time() - start_time
        
        # Analyze multi-user session results
        successful_sessions = [r for r in session_results if not isinstance(r, Exception)]
        failed_sessions = [r for r in session_results if isinstance(r, Exception)]
        
        assert len(successful_sessions) >= 5, f"Expected at least 5 successful sessions, got {len(successful_sessions)}"
        
        # Validate realistic performance characteristics
        total_analyses = sum(s["repositories_analyzed"] for s in successful_sessions)
        total_patterns = sum(s["patterns_found"] for s in successful_sessions)
        total_errors = sum(len(s["errors_encountered"]) for s in successful_sessions)
        
        # Calculate system-wide metrics
        avg_cache_hit_rate = statistics.mean([s["cache_hit_rate"] for s in successful_sessions])
        avg_analysis_time = statistics.mean([
            s["performance_metrics"]["avg_analysis_time"] 
            for s in successful_sessions 
            if s["performance_metrics"]["avg_analysis_time"] > 0
        ])
        
        # Production-realistic assertions
        assert total_analyses > 10, f"Expected significant analysis volume, got {total_analyses}"
        assert total_patterns > 50, f"Expected substantial pattern detection, got {total_patterns}"
        assert avg_cache_hit_rate > 0.3, f"Cache hit rate too low: {avg_cache_hit_rate:.2f}"
        assert avg_analysis_time < 2.0, f"Average analysis time too high: {avg_analysis_time:.2f}s"
        
        # Error rate should be realistic but low
        error_rate = total_errors / max(1, total_analyses)
        assert error_rate < 0.1, f"Error rate too high: {error_rate:.2%}"
        
        # Validate concurrent execution efficiency
        theoretical_sequential_time = sum(s["session_duration_seconds"] for s in successful_sessions)
        concurrency_efficiency = theoretical_sequential_time / total_time
        assert concurrency_efficiency > 2.0, f"Concurrency efficiency too low: {concurrency_efficiency:.2f}"
        
        print(f"Multi-user concurrent session test results:")
        print(f"  - Successful sessions: {len(successful_sessions)}/{len(user_sessions)}")
        print(f"  - Total repositories analyzed: {total_analyses}")
        print(f"  - Total patterns detected: {total_patterns}")
        print(f"  - Average cache hit rate: {avg_cache_hit_rate:.2%}")
        print(f"  - Average analysis time: {avg_analysis_time:.3f}s")
        print(f"  - System error rate: {error_rate:.2%}")
        print(f"  - Concurrency efficiency: {concurrency_efficiency:.2f}x")
        print(f"  - Total execution time: {total_time:.2f}s")
        
        # Validate individual session characteristics
        for i, session in enumerate(successful_sessions):
            user_id = session["user_id"]
            analyses_per_min = session["analyses_per_minute"]
            print(f"  - {user_id}: {session['repositories_analyzed']} repos, {analyses_per_min:.1f} analyses/min")
        
        return {
            "concurrent_sessions_successful": len(successful_sessions) >= 5,
            "total_analyses": total_analyses,
            "system_performance": {
                "cache_hit_rate": avg_cache_hit_rate,
                "avg_analysis_time": avg_analysis_time,
                "error_rate": error_rate,
                "concurrency_efficiency": concurrency_efficiency
            },
            "execution_time": total_time
        }
    
    @pytest.mark.asyncio
    async def test_enterprise_scale_repository_batch_processing(
        self,
        async_test_client: AsyncClient,
        production_workflow_simulator
    ):
        """Test enterprise-scale batch processing of multiple repositories."""
        
        # Simulate enterprise batch job scenario
        enterprise_batch = {
            "batch_id": f"enterprise_batch_{uuid.uuid4().hex[:8]}",
            "repositories": [
                {"id": f"enterprise_repo_{i}", "name": f"project-{i}", "size_mb": random.randint(5, 200), 
                 "complexity_score": random.uniform(1.0, 3.0), "files": random.randint(20, 500)}
                for i in range(25)  # 25 repositories in batch
            ],
            "priority": "high",
            "deadline_minutes": 60,
            "quality_threshold": 0.85
        }
        
        batch_start_time = time.time()
        batch_results = {
            "batch_id": enterprise_batch["batch_id"],
            "repositories_processed": 0,
            "repositories_failed": 0,
            "total_patterns_detected": 0,
            "quality_scores": [],
            "processing_times": [],
            "resource_utilization": {
                "peak_memory_usage": 0.0,
                "peak_cpu_usage": 0.0,
                "database_connections_peak": 0,
                "cache_operations": 0
            },
            "errors": []
        }
        
        # Process repositories in realistic batches (not all at once)
        batch_size = 5  # Process 5 repositories concurrently
        batches = [
            enterprise_batch["repositories"][i:i + batch_size]
            for i in range(0, len(enterprise_batch["repositories"]), batch_size)
        ]
        
        print(f"Processing enterprise batch {enterprise_batch['batch_id']} with {len(enterprise_batch['repositories'])} repositories in {len(batches)} sub-batches...")
        
        for batch_num, repo_batch in enumerate(batches):
            print(f"  Processing sub-batch {batch_num + 1}/{len(batches)} with {len(repo_batch)} repositories...")
            sub_batch_start = time.time()
            
            # Process sub-batch concurrently
            batch_tasks = []
            for repo in repo_batch:
                # Create realistic user session for each repository
                task = production_workflow_simulator.simulate_realistic_user_session(
                    f"batch_processor_{batch_num}",
                    [repo],
                    session_duration_minutes=10  # 10 minutes per repo max
                )
                batch_tasks.append(task)
            
            # Execute sub-batch
            sub_batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
            sub_batch_time = time.time() - sub_batch_start
            
            # Analyze sub-batch results
            successful_results = [r for r in sub_batch_results if not isinstance(r, Exception)]
            failed_results = [r for r in sub_batch_results if isinstance(r, Exception)]
            
            batch_results["repositories_processed"] += len(successful_results)
            batch_results["repositories_failed"] += len(failed_results)
            
            for result in successful_results:
                batch_results["total_patterns_detected"] += result["patterns_found"]
                batch_results["processing_times"].append(result["session_duration_seconds"])
                
                # Track quality scores (confidence)
                if result["patterns_found"] > 0:
                    # Simulate quality score calculation
                    quality_score = random.uniform(0.8, 0.95)
                    batch_results["quality_scores"].append(quality_score)
            
            for error in failed_results:
                batch_results["errors"].append(f"Sub-batch {batch_num + 1}: {str(error)}")
            
            # Simulate resource monitoring
            batch_results["resource_utilization"]["peak_memory_usage"] = max(
                batch_results["resource_utilization"]["peak_memory_usage"],
                len(repo_batch) * 25.0  # 25MB per concurrent repo
            )
            batch_results["resource_utilization"]["database_connections_peak"] = max(
                batch_results["resource_utilization"]["database_connections_peak"],
                len(repo_batch) * 2  # 2 connections per repo
            )
            batch_results["resource_utilization"]["cache_operations"] += len(repo_batch) * 3  # 3 cache ops per repo
            
            print(f"    Sub-batch {batch_num + 1} completed: {len(successful_results)}/{len(repo_batch)} successful, {sub_batch_time:.2f}s")
            
            # Inter-batch delay to simulate realistic processing
            if batch_num < len(batches) - 1:
                await asyncio.sleep(0.1)  # 100ms between batches
        
        total_batch_time = time.time() - batch_start_time
        
        # Calculate enterprise batch metrics
        success_rate = batch_results["repositories_processed"] / len(enterprise_batch["repositories"])
        avg_processing_time = statistics.mean(batch_results["processing_times"]) if batch_results["processing_times"] else 0.0
        avg_quality_score = statistics.mean(batch_results["quality_scores"]) if batch_results["quality_scores"] else 0.0
        throughput_repos_per_minute = batch_results["repositories_processed"] / (total_batch_time / 60)
        
        # Enterprise-scale assertions
        assert success_rate >= 0.9, f"Enterprise batch success rate too low: {success_rate:.2%}"
        assert avg_quality_score >= enterprise_batch["quality_threshold"], f"Quality threshold not met: {avg_quality_score:.3f}"
        assert total_batch_time < enterprise_batch["deadline_minutes"] * 60, f"Batch processing exceeded deadline: {total_batch_time:.2f}s"
        assert batch_results["total_patterns_detected"] > 100, f"Insufficient pattern detection for enterprise scale: {batch_results['total_patterns_detected']}"
        assert throughput_repos_per_minute > 0.5, f"Throughput too low for enterprise scale: {throughput_repos_per_minute:.2f} repos/min"
        
        # Resource utilization should be reasonable
        assert batch_results["resource_utilization"]["peak_memory_usage"] < 500.0, "Memory usage too high"
        assert batch_results["resource_utilization"]["database_connections_peak"] < 50, "Too many database connections"
        
        print(f"Enterprise batch processing results:")
        print(f"  - Success rate: {success_rate:.2%} ({batch_results['repositories_processed']}/{len(enterprise_batch['repositories'])})")
        print(f"  - Average quality score: {avg_quality_score:.3f}")
        print(f"  - Total patterns detected: {batch_results['total_patterns_detected']}")
        print(f"  - Average processing time per repo: {avg_processing_time:.2f}s")
        print(f"  - Throughput: {throughput_repos_per_minute:.2f} repositories/minute")
        print(f"  - Total batch time: {total_batch_time:.2f}s (deadline: {enterprise_batch['deadline_minutes'] * 60}s)")
        print(f"  - Peak memory usage: {batch_results['resource_utilization']['peak_memory_usage']:.1f}MB")
        print(f"  - Peak database connections: {batch_results['resource_utilization']['database_connections_peak']}")
        print(f"  - Total cache operations: {batch_results['resource_utilization']['cache_operations']}")
        print(f"  - Errors encountered: {len(batch_results['errors'])}")
        
        return {
            "enterprise_batch_successful": success_rate >= 0.9,
            "quality_threshold_met": avg_quality_score >= enterprise_batch["quality_threshold"],
            "deadline_met": total_batch_time < enterprise_batch["deadline_minutes"] * 60,
            "performance_metrics": {
                "success_rate": success_rate,
                "avg_quality_score": avg_quality_score,
                "throughput_repos_per_minute": throughput_repos_per_minute,
                "total_batch_time": total_batch_time
            }
        }


if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short", "-m", "advanced"])