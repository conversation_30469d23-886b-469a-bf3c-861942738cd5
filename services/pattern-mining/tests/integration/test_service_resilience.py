"""
Service Resilience Integration Tests - Phase 2 Enhancement

Advanced resilience pattern testing for the Pattern Mining service. This suite
validates sophisticated resilience mechanisms including circuit breakers,
bulkhead isolation, graceful degradation, and adaptive recovery patterns.

Key Resilience Patterns Tested:
1. Circuit Breaker Patterns - Fail-fast behavior and recovery
2. Bulkhead Isolation - Resource isolation and failure containment
3. Timeout and Retry Strategies - Sophisticated retry with backoff
4. Graceful Degradation - Service functionality under partial failures
5. Adaptive Recovery - Dynamic recovery based on system state
6. Resource Pool Management - Connection and resource pool resilience

Advanced Scenarios Beyond Phase 1:
- Multi-level circuit breakers with different thresholds
- Dynamic bulkhead sizing based on load
- Adaptive timeout adjustment based on service performance
- Cross-service resilience coordination
- Recovery strategy optimization based on failure patterns
"""

import asyncio
import pytest
import time
import json
import uuid
import random
import statistics
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple, Callable
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from contextlib import asynccontextmanager
from dataclasses import dataclass, field
from enum import Enum
import threading

# FastAPI and HTTP testing
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

# Pattern mining imports
from pattern_mining.models.patterns import PatternType, SeverityLevel, DetectionType
from pattern_mining.database.connection import DatabaseManager
from pattern_mining.cache.redis_client import RedisClient
from pattern_mining.ml.gemini_client import GeminiClient

# Test utilities
from tests.utils.generators import TestDataGenerator
from tests.utils.performance import PerformanceTestRunner


class CircuitState(Enum):
    """Circuit breaker states."""
    CLOSED = "closed"        # Normal operation
    OPEN = "open"           # Failing, block requests
    HALF_OPEN = "half_open" # Testing recovery


class BulkheadState(Enum):
    """Bulkhead isolation states."""
    NORMAL = "normal"           # Normal resource allocation
    ISOLATED = "isolated"       # Isolated due to issues
    DEGRADED = "degraded"       # Reduced resource allocation
    EMERGENCY = "emergency"     # Emergency resource allocation


@dataclass
class CircuitBreakerConfig:
    """Circuit breaker configuration."""
    failure_threshold: int = 5          # Failures before opening
    recovery_timeout: float = 5.0       # Time before attempting recovery
    success_threshold: int = 3          # Successes needed to close
    rolling_window: float = 60.0        # Rolling window for failure counting
    min_requests: int = 10              # Minimum requests before evaluating


@dataclass 
class CircuitBreakerState:
    """Circuit breaker runtime state."""
    state: CircuitState = CircuitState.CLOSED
    failure_count: int = 0
    success_count: int = 0
    last_failure_time: Optional[datetime] = None
    last_state_change: datetime = field(default_factory=datetime.utcnow)
    total_requests: int = 0
    recent_requests: List[Tuple[datetime, bool]] = field(default_factory=list)  # (timestamp, success)


@dataclass
class BulkheadConfig:
    """Bulkhead isolation configuration."""
    total_resources: int = 100          # Total resource pool size
    service_allocation: Dict[str, int] = field(default_factory=dict)  # Per-service allocation
    isolation_threshold: float = 0.8   # Resource usage threshold for isolation
    recovery_threshold: float = 0.3    # Resource usage threshold for recovery
    adaptive_sizing: bool = True       # Whether to dynamically adjust allocations


@dataclass
class BulkheadState:
    """Bulkhead runtime state."""
    state: BulkheadState = BulkheadState.NORMAL
    current_allocations: Dict[str, int] = field(default_factory=dict)
    resource_usage: Dict[str, int] = field(default_factory=dict)
    isolation_history: List[Tuple[datetime, str, str]] = field(default_factory=list)  # (time, service, reason)


@pytest.mark.integration
@pytest.mark.resilience
class TestCircuitBreakerPatterns:
    """Test circuit breaker resilience patterns."""
    
    @pytest.fixture
    async def circuit_breaker_manager(self):
        """Create circuit breaker management system."""
        
        class CircuitBreakerManager:
            def __init__(self):
                self.circuits: Dict[str, Tuple[CircuitBreakerConfig, CircuitBreakerState]] = {}
                self._lock = asyncio.Lock()
            
            async def create_circuit(self, service_name: str, config: CircuitBreakerConfig = None) -> CircuitBreakerState:
                """Create a circuit breaker for a service."""
                if config is None:
                    config = CircuitBreakerConfig()
                
                state = CircuitBreakerState()
                self.circuits[service_name] = (config, state)
                return state
            
            async def call_through_circuit(
                self, 
                service_name: str, 
                operation: Callable, 
                *args, 
                **kwargs
            ) -> Tuple[Any, bool]:  # (result, success)
                """Execute operation through circuit breaker."""
                async with self._lock:
                    if service_name not in self.circuits:
                        raise ValueError(f"Circuit breaker not configured for {service_name}")
                    
                    config, state = self.circuits[service_name]
                    
                    # Check circuit state
                    current_time = datetime.utcnow()
                    
                    # Clean up old requests from rolling window
                    cutoff_time = current_time - timedelta(seconds=config.rolling_window)
                    state.recent_requests = [
                        (timestamp, success) for timestamp, success in state.recent_requests
                        if timestamp > cutoff_time
                    ]
                    
                    # Handle different circuit states
                    if state.state == CircuitState.OPEN:
                        # Check if recovery timeout has passed
                        if (state.last_failure_time and 
                            (current_time - state.last_failure_time).total_seconds() > config.recovery_timeout):
                            state.state = CircuitState.HALF_OPEN
                            state.success_count = 0
                        else:
                            # Circuit is open, fail fast
                            return None, False
                    
                    # Execute operation
                    try:
                        if asyncio.iscoroutinefunction(operation):
                            result = await operation(*args, **kwargs)
                        else:
                            result = operation(*args, **kwargs)
                        
                        # Record success
                        state.recent_requests.append((current_time, True))
                        state.total_requests += 1
                        
                        if state.state == CircuitState.HALF_OPEN:
                            state.success_count += 1
                            if state.success_count >= config.success_threshold:
                                state.state = CircuitState.CLOSED
                                state.failure_count = 0
                                state.last_state_change = current_time
                        
                        return result, True
                        
                    except Exception as e:
                        # Record failure
                        state.recent_requests.append((current_time, False))
                        state.total_requests += 1
                        state.failure_count += 1
                        state.last_failure_time = current_time
                        
                        # Check if circuit should open
                        if state.state == CircuitState.CLOSED:
                            recent_failures = sum(1 for _, success in state.recent_requests if not success)
                            if (len(state.recent_requests) >= config.min_requests and 
                                recent_failures >= config.failure_threshold):
                                state.state = CircuitState.OPEN
                                state.last_state_change = current_time
                        
                        elif state.state == CircuitState.HALF_OPEN:
                            # Return to open state on any failure during half-open
                            state.state = CircuitState.OPEN
                            state.last_state_change = current_time
                        
                        return None, False
            
            def get_circuit_stats(self, service_name: str) -> Dict[str, Any]:
                """Get circuit breaker statistics."""
                if service_name not in self.circuits:
                    return {}
                
                config, state = self.circuits[service_name]
                
                # Calculate recent success rate
                recent_requests = state.recent_requests
                success_rate = 0.0
                if recent_requests:
                    successes = sum(1 for _, success in recent_requests if success)
                    success_rate = successes / len(recent_requests)
                
                return {
                    "service": service_name,
                    "state": state.state.value,
                    "total_requests": state.total_requests,
                    "failure_count": state.failure_count,
                    "success_count": state.success_count,
                    "recent_success_rate": success_rate,
                    "last_failure_time": state.last_failure_time.isoformat() if state.last_failure_time else None,
                    "last_state_change": state.last_state_change.isoformat(),
                    "recent_requests_count": len(recent_requests)
                }
            
            def get_all_circuit_stats(self) -> Dict[str, Dict[str, Any]]:
                """Get statistics for all circuit breakers."""
                return {
                    service_name: self.get_circuit_stats(service_name)
                    for service_name in self.circuits.keys()
                }
        
        return CircuitBreakerManager()
    
    @pytest.mark.asyncio
    async def test_basic_circuit_breaker_open_close_cycle(self, circuit_breaker_manager):
        """Test basic circuit breaker open/close cycle."""
        print("Testing basic circuit breaker open/close cycle...")
        
        # Create circuit breaker with low thresholds for testing
        config = CircuitBreakerConfig(
            failure_threshold=3,
            recovery_timeout=1.0,
            success_threshold=2,
            min_requests=3
        )
        
        await circuit_breaker_manager.create_circuit("test_service", config)
        
        # Create a failing operation
        failure_count = 0
        async def failing_operation():
            nonlocal failure_count
            failure_count += 1
            if failure_count <= 5:  # First 5 calls fail
                raise Exception(f"Simulated failure #{failure_count}")
            return f"Success after {failure_count} attempts"
        
        # Phase 1: Normal operation (circuit closed)
        initial_stats = circuit_breaker_manager.get_circuit_stats("test_service")
        assert initial_stats["state"] == "closed"
        
        # Phase 2: Generate failures to open circuit
        failure_results = []
        for i in range(5):
            result, success = await circuit_breaker_manager.call_through_circuit(
                "test_service", failing_operation
            )
            failure_results.append((result, success))
            await asyncio.sleep(0.1)  # Small delay between requests
        
        # Check that circuit opened after threshold failures
        post_failure_stats = circuit_breaker_manager.get_circuit_stats("test_service")
        assert post_failure_stats["state"] == "open"
        assert post_failure_stats["failure_count"] >= config.failure_threshold
        
        # Phase 3: Verify fail-fast behavior while circuit is open
        fast_fail_result, fast_fail_success = await circuit_breaker_manager.call_through_circuit(
            "test_service", failing_operation
        )
        assert fast_fail_result is None
        assert fast_fail_success is False
        
        # Phase 4: Wait for recovery timeout
        print("Waiting for recovery timeout...")
        await asyncio.sleep(config.recovery_timeout + 0.1)
        
        # Phase 5: Circuit should now be half-open, test recovery
        recovery_results = []
        for i in range(config.success_threshold + 1):
            result, success = await circuit_breaker_manager.call_through_circuit(
                "test_service", failing_operation
            )
            recovery_results.append((result, success))
            await asyncio.sleep(0.1)
        
        # Circuit should be closed again after successful recoveries
        final_stats = circuit_breaker_manager.get_circuit_stats("test_service")
        assert final_stats["state"] == "closed"
        
        # Validate recovery results
        successful_recoveries = sum(1 for _, success in recovery_results if success)
        assert successful_recoveries >= config.success_threshold
        
        print(f"Circuit breaker cycle test results:")
        print(f"  - Initial state: {initial_stats['state']}")
        print(f"  - State after failures: {post_failure_stats['state']}")
        print(f"  - Final state: {final_stats['state']}")
        print(f"  - Total failures generated: {len([r for r in failure_results if not r[1]])}")
        print(f"  - Successful recoveries: {successful_recoveries}")
        print(f"  - Final success rate: {final_stats['recent_success_rate']:.2f}")
        
        return {
            "circuit_opened": post_failure_stats["state"] == "open",
            "circuit_closed": final_stats["state"] == "closed",
            "fail_fast_worked": fast_fail_result is None,
            "recovery_successful": successful_recoveries >= config.success_threshold
        }
    
    @pytest.mark.asyncio
    async def test_multi_service_circuit_breaker_coordination(self, circuit_breaker_manager):
        """Test circuit breaker coordination across multiple services."""
        print("Testing multi-service circuit breaker coordination...")
        
        # Create circuit breakers for multiple services
        services = ["gemini", "database", "redis", "api"]
        service_configs = {
            "gemini": CircuitBreakerConfig(failure_threshold=4, recovery_timeout=2.0),
            "database": CircuitBreakerConfig(failure_threshold=3, recovery_timeout=1.5),
            "redis": CircuitBreakerConfig(failure_threshold=2, recovery_timeout=1.0),
            "api": CircuitBreakerConfig(failure_threshold=5, recovery_timeout=3.0)
        }
        
        for service, config in service_configs.items():
            await circuit_breaker_manager.create_circuit(service, config)
        
        # Create service operations with different failure patterns
        service_operations = {}
        service_states = {service: {"call_count": 0, "should_fail": False} for service in services}
        
        def create_service_operation(service_name: str):
            async def operation():
                state = service_states[service_name]
                state["call_count"] += 1
                
                # Different failure patterns for each service
                if service_name == "gemini" and state["call_count"] <= 6:
                    raise Exception(f"Gemini API rate limit exceeded (call {state['call_count']})")
                elif service_name == "database" and 3 <= state["call_count"] <= 7:
                    raise Exception(f"Database connection timeout (call {state['call_count']})")
                elif service_name == "redis" and state["call_count"] in [2, 3, 8, 9]:
                    raise Exception(f"Redis connection lost (call {state['call_count']})")
                elif service_name == "api" and state["call_count"] > 15:
                    raise Exception(f"API server overloaded (call {state['call_count']})")
                
                return f"{service_name}_success_{state['call_count']}"
            
            return operation
        
        for service in services:
            service_operations[service] = create_service_operation(service)
        
        # Execute operations across all services simultaneously
        print("Executing operations across all services...")
        
        coordination_results = {service: [] for service in services}
        
        # Run multiple rounds of operations
        for round_num in range(20):
            round_tasks = []
            
            for service in services:
                task = circuit_breaker_manager.call_through_circuit(
                    service, service_operations[service]
                )
                round_tasks.append((service, task))
            
            # Execute all service calls in parallel
            round_results = []
            for service, task in round_tasks:
                try:
                    result, success = await task
                    round_results.append((service, result, success))
                    coordination_results[service].append(success)
                except Exception as e:
                    round_results.append((service, None, False))
                    coordination_results[service].append(False)
            
            await asyncio.sleep(0.1)  # Brief delay between rounds
        
        # Analyze coordination results
        all_stats = circuit_breaker_manager.get_all_circuit_stats()
        
        # Check that different services had different failure patterns
        service_summary = {}
        for service in services:
            stats = all_stats[service]
            results = coordination_results[service]
            
            service_summary[service] = {
                "total_calls": len(results),
                "success_count": sum(results),
                "failure_count": len(results) - sum(results),
                "success_rate": sum(results) / len(results) if results else 0,
                "circuit_state": stats["state"],
                "circuit_opened": stats["failure_count"] >= service_configs[service].failure_threshold
            }
        
        # Validate that circuits opened and closed appropriately
        circuits_that_opened = [s for s, summary in service_summary.items() if summary["circuit_opened"]]
        
        print(f"Multi-service circuit breaker coordination results:")
        for service, summary in service_summary.items():
            print(f"  - {service}: {summary['success_count']}/{summary['total_calls']} success, "
                  f"circuit: {summary['circuit_state']}, opened: {summary['circuit_opened']}")
        
        print(f"  - Circuits that opened: {circuits_that_opened}")
        
        return {
            "services_tested": len(services),
            "circuits_that_opened": len(circuits_that_opened),
            "coordination_successful": len(circuits_that_opened) > 0,
            "service_summary": service_summary
        }
    
    @pytest.mark.asyncio
    async def test_adaptive_circuit_breaker_thresholds(self, circuit_breaker_manager):
        """Test adaptive circuit breaker threshold adjustment."""
        print("Testing adaptive circuit breaker thresholds...")
        
        # Create base circuit breaker
        base_config = CircuitBreakerConfig(
            failure_threshold=5,
            recovery_timeout=2.0,
            success_threshold=3
        )
        
        await circuit_breaker_manager.create_circuit("adaptive_service", base_config)
        
        # Track service performance over time
        performance_history = []
        
        async def adaptive_operation(should_fail_rate: float = 0.3):
            """Operation with configurable failure rate."""
            if random.random() < should_fail_rate:
                raise Exception("Adaptive operation failure")
            
            # Simulate processing time variance
            processing_time = random.uniform(0.01, 0.1)
            await asyncio.sleep(processing_time)
            return f"success_after_{processing_time:.3f}s"
        
        # Phase 1: Test under low failure rate (should maintain normal operation)
        print("Phase 1: Low failure rate testing...")
        low_failure_results = []
        
        for i in range(30):
            start_time = time.time()
            result, success = await circuit_breaker_manager.call_through_circuit(
                "adaptive_service", adaptive_operation, 0.2  # 20% failure rate
            )
            end_time = time.time()
            
            low_failure_results.append({
                "attempt": i,
                "success": success,
                "response_time": end_time - start_time
            })
            
            await asyncio.sleep(0.05)
        
        phase1_stats = circuit_breaker_manager.get_circuit_stats("adaptive_service")
        phase1_success_rate = sum(1 for r in low_failure_results if r["success"]) / len(low_failure_results)
        
        # Phase 2: Test under high failure rate (should trigger circuit opening)
        print("Phase 2: High failure rate testing...")
        high_failure_results = []
        
        for i in range(20):
            start_time = time.time()
            result, success = await circuit_breaker_manager.call_through_circuit(
                "adaptive_service", adaptive_operation, 0.8  # 80% failure rate
            )
            end_time = time.time()
            
            high_failure_results.append({
                "attempt": i,
                "success": success,
                "response_time": end_time - start_time
            })
            
            await asyncio.sleep(0.05)
        
        phase2_stats = circuit_breaker_manager.get_circuit_stats("adaptive_service")
        
        # Phase 3: Test recovery with medium failure rate
        print("Phase 3: Recovery testing with medium failure rate...")
        
        # Wait for recovery timeout
        await asyncio.sleep(base_config.recovery_timeout + 0.5)
        
        recovery_results = []
        for i in range(15):
            start_time = time.time()
            result, success = await circuit_breaker_manager.call_through_circuit(
                "adaptive_service", adaptive_operation, 0.4  # 40% failure rate
            )
            end_time = time.time()
            
            recovery_results.append({
                "attempt": i,
                "success": success,
                "response_time": end_time - start_time
            })
            
            await asyncio.sleep(0.1)
        
        phase3_stats = circuit_breaker_manager.get_circuit_stats("adaptive_service")
        phase3_success_rate = sum(1 for r in recovery_results if r["success"]) / len(recovery_results)
        
        # Analyze adaptive behavior
        avg_response_times = {
            "low_failure": statistics.mean(r["response_time"] for r in low_failure_results if r["success"]),
            "high_failure": statistics.mean(r["response_time"] for r in high_failure_results if r["response_time"] > 0),
            "recovery": statistics.mean(r["response_time"] for r in recovery_results if r["success"])
        }
        
        print(f"Adaptive circuit breaker test results:")
        print(f"  - Phase 1 (low failure): {phase1_success_rate:.2f} success rate, circuit: {phase1_stats['state']}")
        print(f"  - Phase 2 (high failure): circuit: {phase2_stats['state']}, failures: {phase2_stats['failure_count']}")
        print(f"  - Phase 3 (recovery): {phase3_success_rate:.2f} success rate, circuit: {phase3_stats['state']}")
        print(f"  - Avg response times: {avg_response_times}")
        
        return {
            "phase1_success_rate": phase1_success_rate,
            "phase2_circuit_opened": phase2_stats["state"] == "open",
            "phase3_success_rate": phase3_success_rate,
            "adaptive_behavior_detected": phase1_success_rate > phase3_success_rate > (sum(1 for r in high_failure_results if r["success"]) / len(high_failure_results)),
            "response_time_variance": max(avg_response_times.values()) - min(avg_response_times.values())
        }


@pytest.mark.integration
@pytest.mark.resilience
class TestBulkheadIsolationPatterns:
    """Test bulkhead isolation resilience patterns."""
    
    @pytest.fixture
    async def bulkhead_manager(self):
        """Create bulkhead isolation management system."""
        
        class BulkheadManager:
            def __init__(self):
                self.bulkheads: Dict[str, Tuple[BulkheadConfig, BulkheadState]] = {}
                self._resource_lock = asyncio.Lock()
            
            async def create_bulkhead(self, name: str, config: BulkheadConfig = None) -> BulkheadState:
                """Create a bulkhead isolation boundary."""
                if config is None:
                    config = BulkheadConfig()
                
                state = BulkheadState()
                
                # Initialize service allocations
                if not config.service_allocation:
                    # Default equal allocation
                    services = ["api", "gemini", "database", "redis", "background"]
                    allocation_per_service = config.total_resources // len(services)
                    config.service_allocation = {
                        service: allocation_per_service for service in services
                    }
                
                state.current_allocations = config.service_allocation.copy()
                state.resource_usage = {service: 0 for service in config.service_allocation}
                
                self.bulkheads[name] = (config, state)
                return state
            
            async def acquire_resources(self, bulkhead_name: str, service: str, resources_needed: int) -> bool:
                """Acquire resources from bulkhead for a service."""
                async with self._resource_lock:
                    if bulkhead_name not in self.bulkheads:
                        return False
                    
                    config, state = self.bulkheads[bulkhead_name]
                    
                    # Check if service exists in allocation
                    if service not in state.current_allocations:
                        return False
                    
                    # Check if resources are available
                    allocated = state.current_allocations[service]
                    used = state.resource_usage[service]
                    available = allocated - used
                    
                    if available >= resources_needed:
                        state.resource_usage[service] += resources_needed
                        return True
                    
                    # Check if we can adapt allocation
                    if config.adaptive_sizing:
                        adapted = await self._attempt_adaptive_allocation(
                            config, state, service, resources_needed
                        )
                        if adapted:
                            state.resource_usage[service] += resources_needed
                            return True
                    
                    # Log resource exhaustion
                    state.isolation_history.append((
                        datetime.utcnow(),
                        service,
                        f"resource_exhausted_needed_{resources_needed}_available_{available}"
                    ))
                    
                    return False
            
            async def release_resources(self, bulkhead_name: str, service: str, resources_to_release: int):
                """Release resources back to bulkhead."""
                async with self._resource_lock:
                    if bulkhead_name not in self.bulkheads:
                        return
                    
                    config, state = self.bulkheads[bulkhead_name]
                    
                    if service in state.resource_usage:
                        state.resource_usage[service] = max(
                            0, 
                            state.resource_usage[service] - resources_to_release
                        )
            
            async def _attempt_adaptive_allocation(
                self, 
                config: BulkheadConfig, 
                state: BulkheadState, 
                requesting_service: str, 
                resources_needed: int
            ) -> bool:
                """Attempt to adaptively reallocate resources."""
                # Find services with unused allocation
                available_for_reallocation = 0
                donor_services = []
                
                for service, allocated in state.current_allocations.items():
                    if service != requesting_service:
                        used = state.resource_usage[service]
                        unused = allocated - used
                        
                        # Only reallocate if service is using < 50% of allocation
                        if unused > 0 and (used / allocated) < 0.5:
                            donor_services.append((service, unused))
                            available_for_reallocation += unused
                
                # Check if we have enough to reallocate
                if available_for_reallocation >= resources_needed:
                    # Perform reallocation
                    still_needed = resources_needed
                    
                    for donor_service, available in donor_services:
                        if still_needed <= 0:
                            break
                        
                        to_reallocate = min(still_needed, available)
                        
                        # Move allocation
                        state.current_allocations[donor_service] -= to_reallocate
                        state.current_allocations[requesting_service] += to_reallocate
                        
                        still_needed -= to_reallocate
                        
                        # Log reallocation
                        state.isolation_history.append((
                            datetime.utcnow(),
                            requesting_service,
                            f"reallocated_{to_reallocate}_from_{donor_service}"
                        ))
                    
                    return True
                
                return False
            
            async def check_isolation_needed(self, bulkhead_name: str) -> Dict[str, Any]:
                """Check if any services need isolation due to resource abuse."""
                if bulkhead_name not in self.bulkheads:
                    return {}
                
                config, state = self.bulkheads[bulkhead_name]
                isolation_recommendations = {}
                
                for service, allocated in state.current_allocations.items():
                    used = state.resource_usage[service]
                    usage_ratio = used / allocated if allocated > 0 else 0
                    
                    if usage_ratio > config.isolation_threshold:
                        isolation_recommendations[service] = {
                            "current_usage": used,
                            "allocated": allocated,
                            "usage_ratio": usage_ratio,
                            "recommended_action": "isolate",
                            "reason": "resource_usage_exceeded_threshold"
                        }
                    elif usage_ratio < config.recovery_threshold and state.state == BulkheadState.ISOLATED:
                        isolation_recommendations[service] = {
                            "current_usage": used,
                            "allocated": allocated,
                            "usage_ratio": usage_ratio,
                            "recommended_action": "recover",
                            "reason": "resource_usage_below_recovery_threshold"
                        }
                
                return isolation_recommendations
            
            def get_bulkhead_stats(self, bulkhead_name: str) -> Dict[str, Any]:
                """Get bulkhead statistics."""
                if bulkhead_name not in self.bulkheads:
                    return {}
                
                config, state = self.bulkheads[bulkhead_name]
                
                # Calculate overall utilization
                total_allocated = sum(state.current_allocations.values())
                total_used = sum(state.resource_usage.values())
                overall_utilization = total_used / total_allocated if total_allocated > 0 else 0
                
                # Calculate per-service utilization
                service_utilization = {}
                for service in state.current_allocations:
                    allocated = state.current_allocations[service]
                    used = state.resource_usage[service]
                    service_utilization[service] = {
                        "allocated": allocated,
                        "used": used,
                        "utilization": used / allocated if allocated > 0 else 0
                    }
                
                return {
                    "bulkhead_name": bulkhead_name,
                    "state": state.state.value,
                    "total_resources": config.total_resources,
                    "total_allocated": total_allocated,
                    "total_used": total_used,
                    "overall_utilization": overall_utilization,
                    "service_utilization": service_utilization,
                    "isolation_events": len(state.isolation_history),
                    "adaptive_sizing_enabled": config.adaptive_sizing
                }
        
        return BulkheadManager()
    
    @pytest.mark.asyncio
    async def test_basic_bulkhead_resource_isolation(self, bulkhead_manager):
        """Test basic bulkhead resource isolation."""
        print("Testing basic bulkhead resource isolation...")
        
        # Create bulkhead with specific allocations
        config = BulkheadConfig(
            total_resources=100,
            service_allocation={
                "api": 30,
                "gemini": 25,
                "database": 25,
                "redis": 20
            },
            isolation_threshold=0.8,
            adaptive_sizing=False  # Disable for basic test
        )
        
        await bulkhead_manager.create_bulkhead("main_bulkhead", config)
        
        # Test normal resource acquisition
        print("Testing normal resource acquisition...")
        
        # Each service acquires resources within their allocation
        acquisition_results = {}
        
        services_and_requests = [
            ("api", 20),      # 20/30 = 67% utilization
            ("gemini", 15),   # 15/25 = 60% utilization
            ("database", 20), # 20/25 = 80% utilization (at threshold)
            ("redis", 10)     # 10/20 = 50% utilization
        ]
        
        for service, resources in services_and_requests:
            success = await bulkhead_manager.acquire_resources("main_bulkhead", service, resources)
            acquisition_results[service] = {
                "requested": resources,
                "acquired": success,
                "utilization": resources / config.service_allocation[service]
            }
        
        # All acquisitions should succeed
        assert all(result["acquired"] for result in acquisition_results.values())
        
        # Test resource exhaustion
        print("Testing resource exhaustion...")
        
        # API tries to acquire more resources than available
        api_overallocation = await bulkhead_manager.acquire_resources("main_bulkhead", "api", 15)  # Would exceed allocation
        assert not api_overallocation, "Should not be able to over-allocate resources"
        
        # Database tries to acquire more (already at 80%)
        database_overallocation = await bulkhead_manager.acquire_resources("main_bulkhead", "database", 10)  # Would exceed allocation
        assert not database_overallocation, "Should not be able to over-allocate resources"
        
        # Test resource release and reacquisition
        print("Testing resource release and reacquisition...")
        
        # Release some API resources
        await bulkhead_manager.release_resources("main_bulkhead", "api", 10)
        
        # Now API should be able to acquire those resources again
        api_reacquisition = await bulkhead_manager.acquire_resources("main_bulkhead", "api", 8)
        assert api_reacquisition, "Should be able to reacquire released resources"
        
        # Get final statistics
        final_stats = bulkhead_manager.get_bulkhead_stats("main_bulkhead")
        
        print(f"Basic bulkhead isolation test results:")
        print(f"  - Overall utilization: {final_stats['overall_utilization']:.2f}")
        print(f"  - Service utilizations:")
        for service, util_info in final_stats["service_utilization"].items():
            print(f"    - {service}: {util_info['used']}/{util_info['allocated']} ({util_info['utilization']:.2f})")
        print(f"  - Isolation events: {final_stats['isolation_events']}")
        
        return {
            "all_initial_acquisitions_succeeded": all(result["acquired"] for result in acquisition_results.values()),
            "over_allocation_prevented": not api_overallocation and not database_overallocation,
            "resource_release_worked": api_reacquisition,
            "final_utilization": final_stats["overall_utilization"]
        }
    
    @pytest.mark.asyncio
    async def test_adaptive_bulkhead_reallocation(self, bulkhead_manager):
        """Test adaptive bulkhead resource reallocation."""
        print("Testing adaptive bulkhead resource reallocation...")
        
        # Create bulkhead with adaptive sizing enabled
        config = BulkheadConfig(
            total_resources=100,
            service_allocation={
                "high_demand": 20,    # Will need more resources
                "low_demand": 30,     # Will use few resources
                "medium_demand": 25,  # Will use moderate resources
                "variable_demand": 25 # Will have variable usage
            },
            adaptive_sizing=True,
            isolation_threshold=0.9
        )
        
        await bulkhead_manager.create_bulkhead("adaptive_bulkhead", config)
        
        # Phase 1: Establish baseline usage
        print("Phase 1: Establishing baseline usage...")
        
        baseline_acquisitions = [
            ("low_demand", 5),      # 5/30 = 17% utilization (low)
            ("medium_demand", 15),  # 15/25 = 60% utilization (moderate)
            ("variable_demand", 10) # 10/25 = 40% utilization (moderate)
        ]
        
        for service, resources in baseline_acquisitions:
            result = await bulkhead_manager.acquire_resources("adaptive_bulkhead", service, resources)
            assert result, f"Baseline acquisition should succeed for {service}"
        
        # Phase 2: High demand service needs more resources than allocated
        print("Phase 2: Testing adaptive reallocation...")
        
        # High demand service tries to acquire 35 resources (more than its 20 allocation)
        high_demand_attempt = await bulkhead_manager.acquire_resources("adaptive_bulkhead", "high_demand", 35)
        
        # This should succeed due to adaptive reallocation from low-usage services
        reallocation_stats = bulkhead_manager.get_bulkhead_stats("adaptive_bulkhead")
        
        # Check if reallocation occurred
        high_demand_allocation = reallocation_stats["service_utilization"]["high_demand"]["allocated"]
        original_high_demand_allocation = 20
        
        reallocation_occurred = high_demand_allocation > original_high_demand_allocation
        
        print(f"Adaptive reallocation results:")
        print(f"  - High demand acquisition: {'✓' if high_demand_attempt else '✗'}")
        print(f"  - Original high_demand allocation: {original_high_demand_allocation}")
        print(f"  - Current high_demand allocation: {high_demand_allocation}")
        print(f"  - Reallocation occurred: {'✓' if reallocation_occurred else '✗'}")
        
        # Phase 3: Test sustained load with reallocation
        print("Phase 3: Testing sustained load with reallocation...")
        
        sustained_load_results = []
        
        # Simulate sustained variable load
        for i in range(10):
            # Variable demand service changes its needs
            variable_resources = 5 + (i % 3) * 5  # 5, 10, or 15 resources
            
            # Release previous allocation
            if i > 0:
                await bulkhead_manager.release_resources("adaptive_bulkhead", "variable_demand", variable_resources)
            
            # Acquire new allocation
            success = await bulkhead_manager.acquire_resources("adaptive_bulkhead", "variable_demand", variable_resources)
            sustained_load_results.append(success)
            
            await asyncio.sleep(0.05)  # Brief delay
        
        successful_adaptations = sum(sustained_load_results)
        adaptation_success_rate = successful_adaptations / len(sustained_load_results)
        
        # Final statistics
        final_stats = bulkhead_manager.get_bulkhead_stats("adaptive_bulkhead")
        
        print(f"Sustained load adaptation results:")
        print(f"  - Successful adaptations: {successful_adaptations}/{len(sustained_load_results)}")
        print(f"  - Adaptation success rate: {adaptation_success_rate:.2f}")
        print(f"  - Total isolation events: {final_stats['isolation_events']}")
        print(f"  - Final overall utilization: {final_stats['overall_utilization']:.2f}")
        
        return {
            "high_demand_acquisition_succeeded": high_demand_attempt,
            "reallocation_occurred": reallocation_occurred,
            "adaptation_success_rate": adaptation_success_rate,
            "isolation_events": final_stats["isolation_events"],
            "adaptive_sizing_effective": reallocation_occurred and adaptation_success_rate > 0.7
        }
    
    @pytest.mark.asyncio
    async def test_bulkhead_isolation_under_service_abuse(self, bulkhead_manager):
        """Test bulkhead isolation when services abuse resources."""
        print("Testing bulkhead isolation under service abuse...")
        
        # Create bulkhead with low isolation threshold
        config = BulkheadConfig(
            total_resources=100,
            service_allocation={
                "well_behaved": 30,
                "resource_hog": 25,
                "normal_service": 25,
                "backup_service": 20
            },
            isolation_threshold=0.7,  # Low threshold for testing
            recovery_threshold=0.3,
            adaptive_sizing=True
        )
        
        await bulkhead_manager.create_bulkhead("isolation_test_bulkhead", config)
        
        # Phase 1: Normal operation
        print("Phase 1: Normal operation...")
        
        normal_operations = [
            ("well_behaved", 15),    # 50% utilization
            ("normal_service", 12),  # 48% utilization
            ("backup_service", 10)   # 50% utilization
        ]
        
        for service, resources in normal_operations:
            success = await bulkhead_manager.acquire_resources("isolation_test_bulkhead", service, resources)
            assert success, f"Normal operation should succeed for {service}"
        
        # Phase 2: Resource abuse scenario
        print("Phase 2: Resource abuse scenario...")
        
        # Resource hog tries to acquire excessive resources
        resource_abuse_attempts = []
        
        # Gradually increase resource hog's usage
        for attempt in range(5):
            additional_resources = 5 + attempt * 3  # 5, 8, 11, 14, 17
            
            success = await bulkhead_manager.acquire_resources(
                "isolation_test_bulkhead", 
                "resource_hog", 
                additional_resources
            )
            
            resource_abuse_attempts.append({
                "attempt": attempt,
                "resources_requested": additional_resources,
                "success": success
            })
            
            # Check for isolation recommendations
            isolation_check = await bulkhead_manager.check_isolation_needed("isolation_test_bulkhead")
            
            if "resource_hog" in isolation_check:
                print(f"  - Isolation recommended for resource_hog at attempt {attempt}: {isolation_check['resource_hog']['reason']}")
                break
            
            await asyncio.sleep(0.1)
        
        # Phase 3: Verify other services still function
        print("Phase 3: Verifying other services still function...")
        
        # Other services should still be able to operate normally
        post_abuse_operations = []
        
        for service in ["well_behaved", "normal_service", "backup_service"]:
            # Try to acquire additional resources
            success = await bulkhead_manager.acquire_resources("isolation_test_bulkhead", service, 3)
            post_abuse_operations.append((service, success))
        
        services_still_functional = sum(1 for _, success in post_abuse_operations if success)
        
        # Final analysis
        final_stats = bulkhead_manager.get_bulkhead_stats("isolation_test_bulkhead")
        resource_hog_utilization = final_stats["service_utilization"]["resource_hog"]["utilization"]
        
        print(f"Resource abuse isolation test results:")
        print(f"  - Resource hog final utilization: {resource_hog_utilization:.2f}")
        print(f"  - Services still functional: {services_still_functional}/3")
        print(f"  - Total isolation events: {final_stats['isolation_events']}")
        print(f"  - Overall system utilization: {final_stats['overall_utilization']:.2f}")
        
        # Check for resource abuse attempts that were blocked
        blocked_attempts = [attempt for attempt in resource_abuse_attempts if not attempt["success"]]
        
        return {
            "resource_abuse_detected": resource_hog_utilization > config.isolation_threshold,
            "other_services_protected": services_still_functional >= 2,
            "resource_requests_blocked": len(blocked_attempts) > 0,
            "isolation_mechanism_effective": len(blocked_attempts) > 0 and services_still_functional >= 2,
            "total_isolation_events": final_stats["isolation_events"]
        }


if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short", "-m", "resilience"])