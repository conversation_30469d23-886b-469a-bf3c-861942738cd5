"""
Advanced Connection Pool Integration Tests

Comprehensive testing of connection pool exhaustion, leak detection, 
performance under load, dynamic scaling, and health monitoring for 
the Pattern Mining service database layer.

Phase 2 Enhancement: Advanced Database Integration Testing
"""

import pytest
import asyncio
import os
import time
import threading
import psutil
import gc
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
import json
from unittest.mock import patch, MagicMock
from contextlib import asynccontextmanager

import asyncpg
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.pool import StaticPool, QueuePool, NullPool
from sqlalchemy import text, select, func, event
from sqlalchemy.exc import Integrity<PERSON><PERSON>r, OperationalError, DBAPIError, TimeoutError
from sqlalchemy.engine.events import PoolEvents

from pattern_mining.database.connection import DatabaseManager, get_database_session
from pattern_mining.database.repositories.pattern_repository import PatternRepository
from pattern_mining.database.models import <PERSON><PERSON><PERSON><PERSON>ult, RepositoryAnalysis
from pattern_mining.models.patterns import PatternType, SeverityLevel, DetectionType
from pattern_mining.config.database import get_database_config


@pytest.mark.integration
@pytest.mark.asyncio
class TestConnectionPoolExhaustion:
    """Test connection pool behavior under exhaustion conditions."""
    
    @pytest.fixture
    def database_url(self):
        """Get test database URL from environment or use default."""
        return os.getenv(
            "TEST_DATABASE_URL",
            "postgresql://postgres:INSECURE_PASSWORD@localhost:5432/pattern_mining_test"
        )
    
    @pytest.fixture
    async def small_pool_manager(self, database_url):
        """Create database manager with small pool for exhaustion testing."""
        config = get_database_config()
        config.database_url = database_url
        config.pool_size = 3  # Very small pool
        config.max_overflow = 2  # Limited overflow
        config.pool_timeout = 5  # Short timeout
        
        manager = DatabaseManager(config)
        await manager.initialize()
        yield manager
        await manager.close()
    
    @pytest.fixture
    async def pool_metrics_collector(self):
        """Collect connection pool metrics during tests."""
        metrics = {
            'connections_created': 0,
            'connections_closed': 0,
            'pool_checkouts': 0,
            'pool_checkins': 0,
            'pool_overflows': 0,
            'pool_timeouts': 0,
            'active_connections': [],
            'pool_size_samples': []
        }
        
        def on_connect(dbapi_conn, connection_record):
            metrics['connections_created'] += 1
        
        def on_checkout(dbapi_conn, connection_record, connection_proxy):
            metrics['pool_checkouts'] += 1
            metrics['active_connections'].append(time.time())
        
        def on_checkin(dbapi_conn, connection_record):
            metrics['pool_checkins'] += 1
            if metrics['active_connections']:
                metrics['active_connections'].pop()
        
        def on_overflow(dbapi_conn, connection_record):
            metrics['pool_overflows'] += 1
        
        yield metrics
        
        print(f"\nConnection Pool Metrics:")
        print(f"Connections Created: {metrics['connections_created']}")
        print(f"Connections Closed: {metrics['connections_closed']}")
        print(f"Pool Checkouts: {metrics['pool_checkouts']}")
        print(f"Pool Checkins: {metrics['pool_checkins']}")
        print(f"Pool Overflows: {metrics['pool_overflows']}")
        print(f"Pool Timeouts: {metrics['pool_timeouts']}")
    
    async def test_pool_exhaustion_timeout_behavior(self, small_pool_manager: DatabaseManager, pool_metrics_collector):
        """Test behavior when connection pool is exhausted."""
        pool_size = 3
        max_overflow = 2
        total_available = pool_size + max_overflow
        
        # Hold connections beyond pool capacity
        held_connections = []
        connection_tasks = []
        
        async def hold_connection(connection_id: int, hold_time: float):
            """Hold a database connection for specified time."""
            try:
                async with small_pool_manager.get_session() as session:
                    start_time = time.time()
                    await session.execute(text(f"SELECT {connection_id} as connection_id"))
                    
                    # Hold the connection
                    await asyncio.sleep(hold_time)
                    
                    end_time = time.time()
                    return connection_id, end_time - start_time, "success"
            except Exception as e:
                return connection_id, 0, f"error: {str(e)}"
        
        # Create tasks that exceed pool capacity
        num_connections = total_available + 3  # Exceed by 3
        hold_time = 2.0
        
        for i in range(num_connections):
            task = asyncio.create_task(hold_connection(i, hold_time))
            connection_tasks.append(task)
            # Small delay to stagger connection requests
            await asyncio.sleep(0.1)
        
        # Wait for all tasks to complete
        results = await asyncio.gather(*connection_tasks, return_exceptions=True)
        
        # Analyze results
        successful_connections = [r for r in results if not isinstance(r, Exception) and r[2] == "success"]
        failed_connections = [r for r in results if isinstance(r, Exception) or r[2] != "success"]
        
        print(f"Pool Exhaustion Test Results:")
        print(f"Total Requests: {num_connections}")
        print(f"Successful: {len(successful_connections)}")
        print(f"Failed: {len(failed_connections)}")
        print(f"Pool Size + Overflow: {total_available}")
        
        # Should successfully handle up to pool size + overflow
        assert len(successful_connections) <= total_available
        
        # Requests beyond capacity should timeout or fail
        assert len(failed_connections) >= (num_connections - total_available)
        
        # Verify timeout behavior
        timeout_errors = [r for r in failed_connections if isinstance(r, tuple) and "timeout" in r[2].lower()]
        assert len(timeout_errors) > 0  # Should have timeout errors
    
    async def test_connection_pool_recovery_after_exhaustion(self, small_pool_manager: DatabaseManager):
        """Test pool recovery after exhaustion."""
        # Phase 1: Exhaust the pool
        async def exhaust_pool():
            connections_held = []
            
            async def hold_connection_briefly(conn_id):
                async with small_pool_manager.get_session() as session:
                    await session.execute(text(f"SELECT {conn_id}"))
                    await asyncio.sleep(1.0)  # Hold for 1 second
                    return conn_id
            
            # Create more tasks than pool capacity
            tasks = [hold_connection_briefly(i) for i in range(8)]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            return results
        
        # Exhaust pool
        exhaustion_results = await exhaust_pool()
        
        # Phase 2: Wait for connections to be released
        await asyncio.sleep(2.0)
        
        # Phase 3: Test that pool has recovered
        async def test_recovery():
            recovery_tasks = []
            
            async def quick_connection(conn_id):
                async with small_pool_manager.get_session() as session:
                    result = await session.execute(text(f"SELECT {conn_id} as id"))
                    row = result.fetchone()
                    return row.id
            
            # Should be able to get connections again
            for i in range(5):
                task = asyncio.create_task(quick_connection(i))
                recovery_tasks.append(task)
            
            results = await asyncio.gather(*recovery_tasks, return_exceptions=True)
            return results
        
        recovery_results = await test_recovery()
        
        # All recovery connections should succeed
        successful_recovery = [r for r in recovery_results if not isinstance(r, Exception)]
        assert len(successful_recovery) >= 3  # Should get at least pool_size connections
        
        print(f"Pool Recovery Test:")
        print(f"Recovery Successful Connections: {len(successful_recovery)}")
    
    async def test_pool_overflow_and_shrinkage(self, small_pool_manager: DatabaseManager):
        """Test pool overflow behavior and shrinkage back to base size."""
        base_pool_size = 3
        max_overflow = 2
        
        # Monitor pool size over time
        pool_sizes = []
        
        async def monitor_pool_size():
            """Monitor the pool size during the test."""
            for _ in range(20):  # Monitor for 10 seconds
                try:
                    # Access pool stats if available
                    engine = small_pool_manager.engine
                    if hasattr(engine.pool, 'size'):
                        pool_size = engine.pool.size()
                        checked_out = engine.pool.checkedout()
                        overflow = engine.pool.overflow()
                        
                        pool_sizes.append({
                            'timestamp': time.time(),
                            'size': pool_size,
                            'checked_out': checked_out,
                            'overflow': overflow
                        })
                except Exception:
                    pass  # Pool stats not available
                
                await asyncio.sleep(0.5)
        
        # Start monitoring
        monitor_task = asyncio.create_task(monitor_pool_size())
        
        # Create load that requires overflow
        async def connection_burst():
            async def use_connection(conn_id, duration):
                async with small_pool_manager.get_session() as session:
                    await session.execute(text(f"SELECT {conn_id}"))
                    await asyncio.sleep(duration)
                    return conn_id
            
            # Create burst that exceeds base pool size
            burst_tasks = []
            for i in range(base_pool_size + max_overflow):
                task = asyncio.create_task(use_connection(i, 2.0))
                burst_tasks.append(task)
                await asyncio.sleep(0.1)  # Stagger requests
            
            results = await asyncio.gather(*burst_tasks, return_exceptions=True)
            return results
        
        # Execute burst load
        burst_results = await connection_burst()
        
        # Wait for connections to be released and pool to shrink
        await asyncio.sleep(3.0)
        
        # Stop monitoring
        monitor_task.cancel()
        
        if pool_sizes:
            print(f"Pool Size Monitoring:")
            for sample in pool_sizes[-5:]:  # Show last 5 samples
                print(f"  Size: {sample.get('size', 'N/A')}, Checked Out: {sample.get('checked_out', 'N/A')}, Overflow: {sample.get('overflow', 'N/A')}")
        
        # Verify burst handling
        successful_burst = [r for r in burst_results if not isinstance(r, Exception)]
        assert len(successful_burst) >= base_pool_size  # Should handle at least base pool size
    
    async def test_connection_timeout_configuration(self, database_url):
        """Test different timeout configurations."""
        timeout_configs = [
            {'pool_timeout': 1, 'expected_timeouts': True},
            {'pool_timeout': 10, 'expected_timeouts': False},
            {'pool_timeout': 30, 'expected_timeouts': False}
        ]
        
        for config in timeout_configs:
            # Create manager with specific timeout
            db_config = get_database_config()
            db_config.database_url = database_url
            db_config.pool_size = 2
            db_config.max_overflow = 1
            db_config.pool_timeout = config['pool_timeout']
            
            manager = DatabaseManager(db_config)
            await manager.initialize()
            
            try:
                # Create load that exceeds pool capacity
                async def connection_with_timeout(conn_id):
                    try:
                        async with manager.get_session() as session:
                            await session.execute(text(f"SELECT {conn_id}"))
                            await asyncio.sleep(2.0)  # Hold longer than short timeouts
                            return conn_id, "success"
                    except Exception as e:
                        return conn_id, f"timeout: {str(e)}"
                
                # Create more connections than pool can handle
                tasks = [connection_with_timeout(i) for i in range(5)]
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # Analyze timeout behavior
                timeouts = [r for r in results if isinstance(r, tuple) and "timeout" in r[1]]
                successes = [r for r in results if isinstance(r, tuple) and r[1] == "success"]
                
                print(f"Timeout Test (timeout={config['pool_timeout']}s):")
                print(f"  Successes: {len(successes)}")
                print(f"  Timeouts: {len(timeouts)}")
                
                if config['expected_timeouts']:
                    assert len(timeouts) > 0, f"Expected timeouts with {config['pool_timeout']}s timeout"
                
            finally:
                await manager.close()


@pytest.mark.integration
@pytest.mark.asyncio
class TestConnectionLeakDetection:
    """Test connection leak detection and prevention."""
    
    @pytest.fixture
    def database_url(self):
        return os.getenv(
            "TEST_DATABASE_URL",
            "postgresql://postgres:INSECURE_PASSWORD@localhost:5432/pattern_mining_test"
        )
    
    @pytest.fixture
    async def leak_detection_manager(self, database_url):
        """Create database manager with leak detection enabled."""
        config = get_database_config()
        config.database_url = database_url
        config.pool_size = 5
        config.max_overflow = 5
        config.pool_timeout = 10
        config.pool_pre_ping = True
        config.pool_recycle = 300  # 5 minutes
        config.echo = False  # Disable to reduce noise
        
        manager = DatabaseManager(config)
        await manager.initialize()
        yield manager
        await manager.close()
    
    async def test_proper_connection_cleanup(self, leak_detection_manager: DatabaseManager):
        """Test that connections are properly cleaned up."""
        initial_pool_stats = self._get_pool_stats(leak_detection_manager)
        
        # Phase 1: Use connections properly (with context managers)
        async def proper_connection_use(task_id):
            async with leak_detection_manager.get_session() as session:
                result = await session.execute(text(f"SELECT {task_id} as task_id"))
                row = result.fetchone()
                # Connection should be automatically returned to pool
                return row.task_id
        
        # Use multiple connections properly
        proper_tasks = [proper_connection_use(i) for i in range(10)]
        proper_results = await asyncio.gather(*proper_tasks)
        
        # Wait for connections to be returned
        await asyncio.sleep(1.0)
        
        # Check pool stats after proper usage
        after_proper_stats = self._get_pool_stats(leak_detection_manager)
        
        print(f"Proper Connection Cleanup Test:")
        print(f"Initial Pool Stats: {initial_pool_stats}")
        print(f"After Proper Usage: {after_proper_stats}")
        
        # All connections should be returned to pool
        assert len(proper_results) == 10
        # Pool should not have grown significantly
        if after_proper_stats and initial_pool_stats:
            assert after_proper_stats.get('checked_out', 0) <= initial_pool_stats.get('checked_out', 0) + 1
    
    async def test_connection_leak_simulation(self, leak_detection_manager: DatabaseManager):
        """Test detection of potential connection leaks."""
        leaked_connections = []
        
        # Simulate connection leaks by not using context managers properly
        async def simulate_leak(leak_id):
            try:
                # Get session but don't use context manager properly
                session_factory = leak_detection_manager._session_factory
                session = session_factory()
                
                # Use the session
                result = await session.execute(text(f"SELECT {leak_id} as leak_id"))
                row = result.fetchone()
                
                # Intentionally don't close session (simulate leak)
                leaked_connections.append(session)
                
                return leak_id, "leaked"
                
            except Exception as e:
                return leak_id, f"error: {str(e)}"
        
        # Create multiple "leaked" connections
        leak_tasks = [simulate_leak(i) for i in range(3)]
        leak_results = await asyncio.gather(*leak_tasks, return_exceptions=True)
        
        # Check pool stats after leaks
        after_leak_stats = self._get_pool_stats(leak_detection_manager)
        
        print(f"Connection Leak Simulation:")
        print(f"Simulated Leaks: {len(leaked_connections)}")
        print(f"Pool Stats After Leaks: {after_leak_stats}")
        
        # Manually clean up leaked connections
        for session in leaked_connections:
            try:
                await session.close()
            except Exception:
                pass
        
        # Wait for cleanup
        await asyncio.sleep(1.0)
        
        # Check pool stats after manual cleanup
        after_cleanup_stats = self._get_pool_stats(leak_detection_manager)
        
        print(f"Pool Stats After Cleanup: {after_cleanup_stats}")
        
        # Should show improvement after cleanup
        if after_cleanup_stats and after_leak_stats:
            if 'checked_out' in both:
                assert after_cleanup_stats['checked_out'] <= after_leak_stats['checked_out']
    
    async def test_connection_lifecycle_monitoring(self, leak_detection_manager: DatabaseManager):
        """Test comprehensive connection lifecycle monitoring."""
        connection_events = []
        
        # Monitor connection events
        def track_connection_event(event_type, conn_info=None):
            connection_events.append({
                'timestamp': time.time(),
                'event': event_type,
                'info': conn_info
            })
        
        # Test normal connection lifecycle
        async def monitored_connection_use(conn_id):
            track_connection_event('checkout_start', conn_id)
            
            try:
                async with leak_detection_manager.get_session() as session:
                    track_connection_event('session_acquired', conn_id)
                    
                    # Perform database operation
                    result = await session.execute(text(f"SELECT {conn_id} as conn_id"))
                    row = result.fetchone()
                    
                    track_connection_event('operation_complete', conn_id)
                    
                    # Simulate some processing time
                    await asyncio.sleep(0.1)
                    
                    return row.conn_id
                    
            except Exception as e:
                track_connection_event('error', {'conn_id': conn_id, 'error': str(e)})
                raise
            finally:
                track_connection_event('checkout_end', conn_id)
        
        # Use multiple connections with monitoring
        monitored_tasks = [monitored_connection_use(i) for i in range(5)]
        monitored_results = await asyncio.gather(*monitored_tasks, return_exceptions=True)
        
        # Analyze connection events
        checkout_starts = [e for e in connection_events if e['event'] == 'checkout_start']
        checkout_ends = [e for e in connection_events if e['event'] == 'checkout_end']
        errors = [e for e in connection_events if e['event'] == 'error']
        
        print(f"Connection Lifecycle Monitoring:")
        print(f"Checkout Starts: {len(checkout_starts)}")
        print(f"Checkout Ends: {len(checkout_ends)}")
        print(f"Errors: {len(errors)}")
        print(f"Successful Operations: {len([r for r in monitored_results if not isinstance(r, Exception)])}")
        
        # All checkouts should have matching ends (no leaks)
        assert len(checkout_starts) == len(checkout_ends)
        
        # Should have minimal errors
        assert len(errors) == 0
    
    def _get_pool_stats(self, manager: DatabaseManager) -> Dict[str, Any]:
        """Get connection pool statistics if available."""
        try:
            engine = manager.engine
            if hasattr(engine, 'pool'):
                pool = engine.pool
                return {
                    'size': getattr(pool, 'size', lambda: 0)(),
                    'checked_out': getattr(pool, 'checkedout', lambda: 0)(),
                    'overflow': getattr(pool, 'overflow', lambda: 0)(),
                    'checked_in': getattr(pool, 'checkedin', lambda: 0)()
                }
        except Exception:
            pass
        return {}


@pytest.mark.integration
@pytest.mark.asyncio
class TestConnectionPoolPerformance:
    """Test connection pool performance under various load conditions."""
    
    @pytest.fixture
    def database_url(self):
        return os.getenv(
            "TEST_DATABASE_URL",
            "postgresql://postgres:INSECURE_PASSWORD@localhost:5432/pattern_mining_test"
        )
    
    @pytest.fixture
    async def performance_manager(self, database_url):
        """Create database manager optimized for performance testing."""
        config = get_database_config()
        config.database_url = database_url
        config.pool_size = 10
        config.max_overflow = 20
        config.pool_timeout = 30
        config.pool_pre_ping = True
        config.pool_recycle = 3600
        
        manager = DatabaseManager(config)
        await manager.initialize()
        yield manager
        await manager.close()
    
    async def test_concurrent_connection_performance(self, performance_manager: DatabaseManager):
        """Test performance of concurrent connections."""
        concurrency_levels = [1, 5, 10, 20, 50]
        performance_results = {}
        
        for concurrency in concurrency_levels:
            print(f"\nTesting concurrency level: {concurrency}")
            
            async def performance_worker(worker_id):
                start_time = time.time()
                
                async with performance_manager.get_session() as session:
                    # Perform a simple but measurable operation
                    result = await session.execute(text("""
                        SELECT 
                            :worker_id as worker_id,
                            COUNT(*) as count,
                            AVG(RANDOM()) as avg_random
                        FROM generate_series(1, 100) as t(id)
                    """), {'worker_id': worker_id})
                    
                    row = result.fetchone()
                    
                end_time = time.time()
                return {
                    'worker_id': worker_id,
                    'duration': end_time - start_time,
                    'result': row.count
                }
            
            # Run concurrent workers
            start_time = time.time()
            tasks = [performance_worker(i) for i in range(concurrency)]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            total_time = time.time() - start_time
            
            # Analyze results
            successful_results = [r for r in results if not isinstance(r, Exception)]
            
            if successful_results:
                avg_duration = sum(r['duration'] for r in successful_results) / len(successful_results)
                min_duration = min(r['duration'] for r in successful_results)
                max_duration = max(r['duration'] for r in successful_results)
                throughput = len(successful_results) / total_time
                
                performance_results[concurrency] = {
                    'total_time': total_time,
                    'avg_duration': avg_duration,
                    'min_duration': min_duration,
                    'max_duration': max_duration,
                    'throughput': throughput,
                    'success_rate': len(successful_results) / concurrency
                }
                
                print(f"  Total Time: {total_time:.3f}s")
                print(f"  Avg Duration: {avg_duration:.3f}s")
                print(f"  Throughput: {throughput:.2f} ops/sec")
                print(f"  Success Rate: {performance_results[concurrency]['success_rate']:.2%}")
        
        # Verify performance characteristics
        assert performance_results[1]['success_rate'] == 1.0  # Single connection should always succeed
        
        # Higher concurrency should maintain good throughput
        if 10 in performance_results and 1 in performance_results:
            concurrency_10_throughput = performance_results[10]['throughput']
            single_throughput = performance_results[1]['throughput']
            
            # Should achieve at least 3x throughput improvement with 10x concurrency
            throughput_improvement = concurrency_10_throughput / single_throughput
            assert throughput_improvement >= 3.0, f"Expected throughput improvement, got {throughput_improvement:.2f}x"
    
    async def test_connection_acquisition_latency(self, performance_manager: DatabaseManager):
        """Test latency of connection acquisition under load."""
        latency_measurements = []
        
        async def measure_connection_latency(measurement_id):
            # Measure time to acquire connection
            acquisition_start = time.time()
            
            async with performance_manager.get_session() as session:
                acquisition_end = time.time()
                acquisition_latency = acquisition_end - acquisition_start
                
                # Measure time for simple query
                query_start = time.time()
                result = await session.execute(text("SELECT 1"))
                query_end = time.time()
                query_latency = query_end - query_start
                
                return {
                    'measurement_id': measurement_id,
                    'acquisition_latency': acquisition_latency,
                    'query_latency': query_latency,
                    'total_latency': acquisition_latency + query_latency
                }
        
        # Measure latency under different loads
        load_scenarios = [
            {'concurrent_connections': 5, 'measurements': 20},
            {'concurrent_connections': 15, 'measurements': 20},
            {'concurrent_connections': 25, 'measurements': 20}
        ]
        
        for scenario in load_scenarios:
            print(f"\nLatency Test - Concurrent Connections: {scenario['concurrent_connections']}")
            
            # Create background load
            async def background_load():
                async with performance_manager.get_session() as session:
                    await session.execute(text("SELECT pg_sleep(0.1)"))
            
            # Start background load
            background_tasks = [
                asyncio.create_task(background_load()) 
                for _ in range(scenario['concurrent_connections'])
            ]
            
            # Small delay to establish load
            await asyncio.sleep(0.1)
            
            # Measure latencies
            measurement_tasks = [
                measure_connection_latency(i) 
                for i in range(scenario['measurements'])
            ]
            measurements = await asyncio.gather(*measurement_tasks, return_exceptions=True)
            
            # Wait for background tasks to complete
            await asyncio.gather(*background_tasks, return_exceptions=True)
            
            # Analyze latency measurements
            successful_measurements = [m for m in measurements if not isinstance(m, Exception)]
            
            if successful_measurements:
                acquisition_latencies = [m['acquisition_latency'] for m in successful_measurements]
                query_latencies = [m['query_latency'] for m in successful_measurements]
                
                avg_acquisition = sum(acquisition_latencies) / len(acquisition_latencies)
                p95_acquisition = sorted(acquisition_latencies)[int(0.95 * len(acquisition_latencies))]
                avg_query = sum(query_latencies) / len(query_latencies)
                p95_query = sorted(query_latencies)[int(0.95 * len(query_latencies))]
                
                print(f"  Acquisition Latency - Avg: {avg_acquisition*1000:.2f}ms, P95: {p95_acquisition*1000:.2f}ms")
                print(f"  Query Latency - Avg: {avg_query*1000:.2f}ms, P95: {p95_query*1000:.2f}ms")
                
                # Performance assertions
                assert avg_acquisition < 0.1, f"Connection acquisition too slow: {avg_acquisition:.3f}s"
                assert p95_acquisition < 0.5, f"P95 connection acquisition too slow: {p95_acquisition:.3f}s"
                assert avg_query < 0.05, f"Query execution too slow: {avg_query:.3f}s"
    
    async def test_pool_scaling_efficiency(self, database_url):
        """Test efficiency of different pool size configurations."""
        pool_configurations = [
            {'pool_size': 2, 'max_overflow': 2},
            {'pool_size': 5, 'max_overflow': 5},
            {'pool_size': 10, 'max_overflow': 10},
            {'pool_size': 20, 'max_overflow': 20}
        ]
        
        scaling_results = {}
        
        for config in pool_configurations:
            print(f"\nTesting pool configuration: {config}")
            
            # Create manager with specific pool configuration
            db_config = get_database_config()
            db_config.database_url = database_url
            db_config.pool_size = config['pool_size']
            db_config.max_overflow = config['max_overflow']
            db_config.pool_timeout = 30
            
            manager = DatabaseManager(db_config)
            await manager.initialize()
            
            try:
                # Test with load that matches pool capacity
                target_concurrency = config['pool_size'] + config['max_overflow']
                
                async def pool_scaling_worker(worker_id):
                    start_time = time.time()
                    
                    async with manager.get_session() as session:
                        # Perform work that takes some time
                        result = await session.execute(text("""
                            SELECT COUNT(*) as count
                            FROM generate_series(1, 1000) as t(id)
                            WHERE t.id % 2 = :worker_mod
                        """), {'worker_mod': worker_id % 2})
                        
                        row = result.fetchone()
                        end_time = time.time()
                        
                        return {
                            'worker_id': worker_id,
                            'duration': end_time - start_time,
                            'result': row.count
                        }
                
                # Run workers matching pool capacity
                start_time = time.time()
                tasks = [pool_scaling_worker(i) for i in range(target_concurrency)]
                results = await asyncio.gather(*tasks, return_exceptions=True)
                total_time = time.time() - start_time
                
                # Analyze scaling efficiency
                successful_results = [r for r in results if not isinstance(r, Exception)]
                
                if successful_results:
                    avg_duration = sum(r['duration'] for r in successful_results) / len(successful_results)
                    throughput = len(successful_results) / total_time
                    efficiency = throughput / (config['pool_size'] + config['max_overflow'])
                    
                    scaling_results[f"pool_{config['pool_size']}"] = {
                        'pool_size': config['pool_size'],
                        'max_overflow': config['max_overflow'],
                        'total_capacity': config['pool_size'] + config['max_overflow'],
                        'throughput': throughput,
                        'efficiency': efficiency,
                        'avg_duration': avg_duration,
                        'success_rate': len(successful_results) / target_concurrency
                    }
                    
                    print(f"  Throughput: {throughput:.2f} ops/sec")
                    print(f"  Efficiency: {efficiency:.3f} ops/sec per connection")
                    print(f"  Success Rate: {scaling_results[f'pool_{config['pool_size']}']['success_rate']:.2%}")
            
            finally:
                await manager.close()
        
        # Verify scaling characteristics
        for config_name, results in scaling_results.items():
            # All configurations should achieve high success rates
            assert results['success_rate'] >= 0.9, f"{config_name} has low success rate: {results['success_rate']:.2%}"
            
            # Efficiency should be reasonable (> 0.1 ops/sec per connection)
            assert results['efficiency'] > 0.1, f"{config_name} has low efficiency: {results['efficiency']:.3f}"
        
        print(f"\nPool Scaling Summary:")
        for config_name, results in scaling_results.items():
            print(f"  {config_name}: {results['efficiency']:.3f} ops/sec per connection, {results['success_rate']:.1%} success")


@pytest.mark.integration
@pytest.mark.asyncio
class TestConnectionPoolHealthMonitoring:
    """Test connection pool health monitoring and alerting."""
    
    @pytest.fixture
    def database_url(self):
        return os.getenv(
            "TEST_DATABASE_URL",
            "postgresql://postgres:INSECURE_PASSWORD@localhost:5432/pattern_mining_test"
        )
    
    @pytest.fixture
    async def monitored_manager(self, database_url):
        """Create database manager with health monitoring."""
        config = get_database_config()
        config.database_url = database_url
        config.pool_size = 5
        config.max_overflow = 5
        config.pool_timeout = 10
        config.pool_pre_ping = True  # Enable connection health checks
        
        manager = DatabaseManager(config)
        await manager.initialize()
        yield manager
        await manager.close()
    
    async def test_connection_health_checks(self, monitored_manager: DatabaseManager):
        """Test connection health checking and recovery."""
        health_check_results = []
        
        async def perform_health_check(check_id):
            try:
                start_time = time.time()
                
                async with monitored_manager.get_session() as session:
                    # Simple health check query
                    result = await session.execute(text("SELECT 1 as health_check"))
                    row = result.fetchone()
                    
                    end_time = time.time()
                    return {
                        'check_id': check_id,
                        'status': 'healthy',
                        'response_time': end_time - start_time,
                        'result': row.health_check
                    }
                    
            except Exception as e:
                return {
                    'check_id': check_id,
                    'status': 'unhealthy',
                    'error': str(e),
                    'response_time': None
                }
        
        # Perform multiple health checks
        health_check_tasks = [perform_health_check(i) for i in range(10)]
        health_results = await asyncio.gather(*health_check_tasks, return_exceptions=True)
        
        # Analyze health check results
        healthy_checks = [r for r in health_results if not isinstance(r, Exception) and r['status'] == 'healthy']
        unhealthy_checks = [r for r in health_results if isinstance(r, Exception) or r.get('status') == 'unhealthy']
        
        print(f"Connection Health Check Results:")
        print(f"Healthy Checks: {len(healthy_checks)}")
        print(f"Unhealthy Checks: {len(unhealthy_checks)}")
        
        if healthy_checks:
            avg_response_time = sum(r['response_time'] for r in healthy_checks) / len(healthy_checks)
            max_response_time = max(r['response_time'] for r in healthy_checks)
            print(f"Average Response Time: {avg_response_time*1000:.2f}ms")
            print(f"Max Response Time: {max_response_time*1000:.2f}ms")
            
            # Health assertions
            assert len(healthy_checks) >= 8, "Should have mostly healthy connections"
            assert avg_response_time < 0.1, f"Health check response time too slow: {avg_response_time:.3f}s"
    
    async def test_connection_pool_metrics_collection(self, monitored_manager: DatabaseManager):
        """Test collection of detailed pool metrics."""
        metrics_history = []
        
        async def collect_pool_metrics():
            """Collect current pool metrics."""
            try:
                # Get pool statistics
                engine = monitored_manager.engine
                pool_stats = {}
                
                if hasattr(engine, 'pool'):
                    pool = engine.pool
                    pool_stats = {
                        'pool_size': getattr(pool, 'size', lambda: 0)(),
                        'checked_out': getattr(pool, 'checkedout', lambda: 0)(),
                        'overflow': getattr(pool, 'overflow', lambda: 0)(),
                        'checked_in': getattr(pool, 'checkedin', lambda: 0)()
                    }
                
                # Get system metrics
                process = psutil.Process()
                system_stats = {
                    'cpu_percent': process.cpu_percent(),
                    'memory_mb': process.memory_info().rss / 1024 / 1024,
                    'open_files': len(process.open_files()),
                    'connections': len(process.connections())
                }
                
                return {
                    'timestamp': time.time(),
                    'pool_stats': pool_stats,
                    'system_stats': system_stats
                }
                
            except Exception as e:
                return {
                    'timestamp': time.time(),
                    'error': str(e)
                }
        
        # Collect baseline metrics
        baseline_metrics = await collect_pool_metrics()
        metrics_history.append(baseline_metrics)
        
        # Create some database load
        async def database_workload():
            async with monitored_manager.get_session() as session:
                result = await session.execute(text("""
                    SELECT COUNT(*) as count
                    FROM generate_series(1, 10000) as t(id)
                    WHERE t.id % 100 = 0
                """))
                return result.scalar()
        
        # Run workload while collecting metrics
        workload_tasks = [database_workload() for _ in range(8)]
        
        # Start workload
        workload_task = asyncio.create_task(asyncio.gather(*workload_tasks))
        
        # Collect metrics during workload
        for i in range(5):
            await asyncio.sleep(0.5)
            metrics = await collect_pool_metrics()
            metrics_history.append(metrics)
        
        # Wait for workload to complete
        workload_results = await workload_task
        
        # Collect final metrics
        final_metrics = await collect_pool_metrics()
        metrics_history.append(final_metrics)
        
        # Analyze metrics history
        print(f"\nPool Metrics Analysis:")
        for i, metrics in enumerate(metrics_history):
            if 'error' not in metrics:
                pool_stats = metrics.get('pool_stats', {})
                system_stats = metrics.get('system_stats', {})
                
                print(f"Sample {i}:")
                print(f"  Pool - Size: {pool_stats.get('pool_size', 'N/A')}, Checked Out: {pool_stats.get('checked_out', 'N/A')}")
                print(f"  System - CPU: {system_stats.get('cpu_percent', 'N/A'):.1f}%, Memory: {system_stats.get('memory_mb', 'N/A'):.1f}MB")
        
        # Verify metrics collection
        successful_collections = [m for m in metrics_history if 'error' not in m]
        assert len(successful_collections) >= len(metrics_history) // 2, "Should collect metrics successfully"
        
        # Verify workload completed
        successful_workload = [r for r in workload_results if not isinstance(r, Exception)]
        assert len(successful_workload) >= 6, "Should complete most workload tasks"
    
    async def test_pool_alert_conditions(self, monitored_manager: DatabaseManager):
        """Test detection of pool alert conditions."""
        alert_conditions = []
        
        def check_alert_conditions(pool_stats, system_stats):
            """Check for alert conditions in pool metrics."""
            alerts = []
            
            # High pool utilization
            if pool_stats.get('checked_out', 0) > pool_stats.get('pool_size', 10) * 0.8:
                alerts.append({
                    'type': 'high_pool_utilization',
                    'severity': 'warning',
                    'message': f"Pool utilization high: {pool_stats.get('checked_out')}/{pool_stats.get('pool_size')}"
                })
            
            # Pool overflow usage
            if pool_stats.get('overflow', 0) > 0:
                alerts.append({
                    'type': 'pool_overflow_active',
                    'severity': 'info',
                    'message': f"Pool overflow active: {pool_stats.get('overflow')} connections"
                })
            
            # High memory usage
            if system_stats.get('memory_mb', 0) > 500:  # 500MB threshold
                alerts.append({
                    'type': 'high_memory_usage',
                    'severity': 'warning',
                    'message': f"High memory usage: {system_stats.get('memory_mb'):.1f}MB"
                })
            
            # Too many open files
            if system_stats.get('open_files', 0) > 100:
                alerts.append({
                    'type': 'high_open_files',
                    'severity': 'warning',
                    'message': f"Many open files: {system_stats.get('open_files')}"
                })
            
            return alerts
        
        # Create conditions that should trigger alerts
        async def create_alert_conditions():
            # Create high pool utilization
            held_sessions = []
            
            try:
                # Use most of the pool
                for i in range(4):  # Use 4 out of 5 pool connections
                    session_factory = monitored_manager._session_factory
                    session = session_factory()
                    await session.execute(text(f"SELECT {i}"))
                    held_sessions.append(session)
                
                # Collect metrics during high utilization
                process = psutil.Process()
                pool_stats = {
                    'pool_size': 5,
                    'checked_out': len(held_sessions),
                    'overflow': 0
                }
                system_stats = {
                    'memory_mb': process.memory_info().rss / 1024 / 1024,
                    'open_files': len(process.open_files())
                }
                
                alerts = check_alert_conditions(pool_stats, system_stats)
                alert_conditions.extend(alerts)
                
            finally:
                # Clean up held sessions
                for session in held_sessions:
                    try:
                        await session.close()
                    except Exception:
                        pass
        
        # Create alert conditions
        await create_alert_conditions()
        
        # Analyze alerts
        print(f"\nAlert Conditions Detected:")
        for alert in alert_conditions:
            print(f"  {alert['type']}: {alert['severity']} - {alert['message']}")
        
        # Should detect some alert conditions
        assert len(alert_conditions) > 0, "Should detect alert conditions during high utilization"
        
        # Should have high utilization alert
        utilization_alerts = [a for a in alert_conditions if a['type'] == 'high_pool_utilization']
        assert len(utilization_alerts) > 0, "Should detect high pool utilization"