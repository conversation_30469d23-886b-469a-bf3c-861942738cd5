"""
Data Consistency Integration Tests - Phase 2 Enhancement

Advanced data consistency testing for multi-service transactions and state
synchronization across the Pattern Mining service architecture. These tests
validate ACID properties, eventual consistency, and data integrity under
complex failure scenarios.

Key Consistency Patterns Tested:
1. Cross-Service Transactions - ACID properties across service boundaries
2. Eventual Consistency - Convergence after temporary inconsistencies
3. Conflict Resolution - Handling concurrent modifications
4. State Synchronization - Maintaining consistency across data stores
5. Compensating Transactions - Rollback strategies for distributed failures
6. Data Versioning - Optimistic and pessimistic concurrency control

Advanced Scenarios Beyond Phase 1:
- Multi-level transaction coordination across services
- Conflict resolution with complex business rules
- Performance impact of consistency guarantees
- Consistency during partial service availability
- Recovery from split-brain scenarios
"""

import asyncio
import pytest
import time
import json
import uuid
import hashlib
import threading
import random
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple, Set
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from contextlib import asynccontextmanager
from dataclasses import dataclass, field
from enum import Enum
import copy

# FastAPI and HTTP testing
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

# Pattern mining imports
from pattern_mining.models.patterns import PatternType, SeverityLevel, DetectionType
from pattern_mining.models.database import PatternRecord, AnalysisRecord
from pattern_mining.database.connection import DatabaseManager
from pattern_mining.cache.redis_client import RedisClient

# Test utilities
from tests.utils.generators import TestDataGenerator


class TransactionState(Enum):
    """Transaction states."""
    INITIATED = "initiated"
    PREPARING = "preparing"
    PREPARED = "prepared"
    COMMITTING = "committing"
    COMMITTED = "committed"
    ABORTING = "aborting"
    ABORTED = "aborted"


class ConsistencyLevel(Enum):
    """Data consistency levels."""
    STRONG = "strong"           # Immediate consistency
    EVENTUAL = "eventual"       # Eventually consistent
    WEAK = "weak"              # Best effort consistency


@dataclass
class TransactionOperation:
    """Individual operation within a transaction."""
    operation_id: str
    service: str
    operation_type: str  # "create", "update", "delete"
    resource_type: str   # "pattern", "analysis", "cache_entry"
    resource_id: str
    data: Dict[str, Any]
    compensation_data: Optional[Dict[str, Any]] = None  # For rollback
    timestamp: datetime = field(default_factory=datetime.utcnow)


@dataclass
class DistributedTransaction:
    """Distributed transaction across multiple services."""
    transaction_id: str
    state: TransactionState = TransactionState.INITIATED
    operations: List[TransactionOperation] = field(default_factory=list)
    participants: Set[str] = field(default_factory=set)
    coordinator: str = "transaction_coordinator"
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)
    timeout_seconds: int = 30
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ConsistencyViolation:
    """Record of consistency violation."""
    violation_id: str
    violation_type: str
    detected_at: datetime
    services_involved: List[str]
    resource_affected: str
    description: str
    severity: str  # "low", "medium", "high", "critical"
    resolved: bool = False
    resolution_method: Optional[str] = None


@pytest.mark.integration
@pytest.mark.data_consistency
class TestCrossServiceTransactions:
    """Test cross-service transaction coordination."""
    
    @pytest.fixture
    async def transaction_coordinator(self):
        """Create distributed transaction coordinator."""
        
        class TransactionCoordinator:
            def __init__(self):
                self.transactions: Dict[str, DistributedTransaction] = {}
                self.service_states: Dict[str, str] = {
                    "database": "active",
                    "cache": "active",
                    "api": "active",
                    "ml_service": "active"
                }
                self.consistency_violations: List[ConsistencyViolation] = []
                self._coordinator_lock = asyncio.Lock()
            
            async def begin_transaction(self, transaction_id: str = None) -> DistributedTransaction:
                """Begin a new distributed transaction."""
                if transaction_id is None:
                    transaction_id = f"txn_{uuid.uuid4().hex[:12]}"
                
                async with self._coordinator_lock:
                    transaction = DistributedTransaction(transaction_id=transaction_id)
                    self.transactions[transaction_id] = transaction
                    return transaction
            
            async def add_operation(
                self, 
                transaction_id: str, 
                service: str,
                operation_type: str,
                resource_type: str,
                resource_id: str,
                data: Dict[str, Any],
                compensation_data: Dict[str, Any] = None
            ) -> TransactionOperation:
                """Add operation to transaction."""
                async with self._coordinator_lock:
                    if transaction_id not in self.transactions:
                        raise ValueError(f"Transaction {transaction_id} not found")
                    
                    transaction = self.transactions[transaction_id]
                    
                    if transaction.state != TransactionState.INITIATED:
                        raise ValueError(f"Cannot add operations to transaction in state {transaction.state}")
                    
                    operation = TransactionOperation(
                        operation_id=f"op_{uuid.uuid4().hex[:8]}",
                        service=service,
                        operation_type=operation_type,
                        resource_type=resource_type,
                        resource_id=resource_id,
                        data=data,
                        compensation_data=compensation_data
                    )
                    
                    transaction.operations.append(operation)
                    transaction.participants.add(service)
                    transaction.updated_at = datetime.utcnow()
                    
                    return operation
            
            async def prepare_transaction(self, transaction_id: str) -> bool:
                """Prepare phase of two-phase commit."""
                async with self._coordinator_lock:
                    if transaction_id not in self.transactions:
                        return False
                    
                    transaction = self.transactions[transaction_id]
                    transaction.state = TransactionState.PREPARING
                    
                    # Simulate preparation phase
                    preparation_results = {}
                    
                    for service in transaction.participants:
                        # Check service availability
                        if self.service_states.get(service) != "active":
                            preparation_results[service] = False
                            continue
                        
                        # Simulate service-specific preparation
                        service_operations = [op for op in transaction.operations if op.service == service]
                        
                        try:
                            # Validate operations for service
                            service_preparation = await self._prepare_service_operations(service, service_operations)
                            preparation_results[service] = service_preparation
                        except Exception as e:
                            preparation_results[service] = False
                    
                    # Check if all services prepared successfully
                    all_prepared = all(preparation_results.values())
                    
                    if all_prepared:
                        transaction.state = TransactionState.PREPARED
                        return True
                    else:
                        transaction.state = TransactionState.ABORTING
                        return False
            
            async def commit_transaction(self, transaction_id: str) -> bool:
                """Commit phase of two-phase commit."""
                async with self._coordinator_lock:
                    if transaction_id not in self.transactions:
                        return False
                    
                    transaction = self.transactions[transaction_id]
                    
                    if transaction.state != TransactionState.PREPARED:
                        return False
                    
                    transaction.state = TransactionState.COMMITTING
                    
                    # Simulate commit phase
                    commit_results = {}
                    
                    for service in transaction.participants:
                        service_operations = [op for op in transaction.operations if op.service == service]
                        
                        try:
                            # Commit operations for service
                            commit_success = await self._commit_service_operations(service, service_operations)
                            commit_results[service] = commit_success
                        except Exception as e:
                            commit_results[service] = False
                    
                    # In real 2PC, commit should not fail after prepare succeeds
                    # Here we simulate potential commit issues
                    all_committed = all(commit_results.values())
                    
                    if all_committed:
                        transaction.state = TransactionState.COMMITTED
                        return True
                    else:
                        # This is a serious consistency issue - some services committed, others didn't
                        await self._handle_partial_commit_failure(transaction, commit_results)
                        return False
            
            async def abort_transaction(self, transaction_id: str) -> bool:
                """Abort transaction and run compensating actions."""
                async with self._coordinator_lock:
                    if transaction_id not in self.transactions:
                        return False
                    
                    transaction = self.transactions[transaction_id]
                    transaction.state = TransactionState.ABORTING
                    
                    # Run compensating actions for any operations that were applied
                    compensation_results = {}
                    
                    for service in transaction.participants:
                        service_operations = [op for op in transaction.operations if op.service == service]
                        
                        try:
                            compensation_success = await self._compensate_service_operations(service, service_operations)
                            compensation_results[service] = compensation_success
                        except Exception as e:
                            compensation_results[service] = False
                    
                    transaction.state = TransactionState.ABORTED
                    return all(compensation_results.values())
            
            async def _prepare_service_operations(self, service: str, operations: List[TransactionOperation]) -> bool:
                """Simulate preparing operations for a specific service."""
                # Simulate preparation time
                await asyncio.sleep(0.01)
                
                # Simulate failure scenarios
                if service == "database" and len(operations) > 5:
                    # Database can't handle too many operations in one transaction
                    return False
                
                if service == "cache" and any(op.operation_type == "delete" for op in operations):
                    # Cache service has issues with delete operations
                    return random.choice([True, False])  # 50% chance of failure
                
                # Check for resource conflicts
                resource_ids = set(op.resource_id for op in operations)
                if len(resource_ids) != len(operations):
                    # Duplicate resource IDs in same transaction
                    return False
                
                return True
            
            async def _commit_service_operations(self, service: str, operations: List[TransactionOperation]) -> bool:
                """Simulate committing operations for a specific service."""
                # Simulate commit time
                await asyncio.sleep(0.02)
                
                # Most commits should succeed after successful preparation
                # Simulate rare commit failures
                if service == "ml_service" and len(operations) > 3:
                    return random.choice([True, True, True, False])  # 25% chance of failure
                
                return True
            
            async def _compensate_service_operations(self, service: str, operations: List[TransactionOperation]) -> bool:
                """Simulate compensating operations for a specific service."""
                # Simulate compensation time
                await asyncio.sleep(0.015)
                
                # Compensation should generally succeed
                compensation_success = 0
                for operation in operations:
                    if operation.compensation_data:
                        # Has compensation data, should succeed
                        compensation_success += 1
                    else:
                        # No compensation data, might fail
                        if random.choice([True, False]):
                            compensation_success += 1
                
                # Consider compensation successful if > 80% operations compensated
                return compensation_success / len(operations) > 0.8
            
            async def _handle_partial_commit_failure(
                self, 
                transaction: DistributedTransaction, 
                commit_results: Dict[str, bool]
            ):
                """Handle partial commit failure - serious consistency issue."""
                violation = ConsistencyViolation(
                    violation_id=f"violation_{uuid.uuid4().hex[:8]}",
                    violation_type="partial_commit_failure",
                    detected_at=datetime.utcnow(),
                    services_involved=list(transaction.participants),
                    resource_affected=f"transaction_{transaction.transaction_id}",
                    description=f"Partial commit failure: {commit_results}",
                    severity="critical"
                )
                
                self.consistency_violations.append(violation)
                
                # Attempt recovery by compensating successful commits
                successful_services = [service for service, success in commit_results.items() if success]
                
                for service in successful_services:
                    service_operations = [op for op in transaction.operations if op.service == service]
                    await self._compensate_service_operations(service, service_operations)
            
            def get_consistency_report(self) -> Dict[str, Any]:
                """Generate consistency report."""
                total_transactions = len(self.transactions)
                
                by_state = {}
                for transaction in self.transactions.values():
                    state = transaction.state.value
                    by_state[state] = by_state.get(state, 0) + 1
                
                violations_by_severity = {}
                for violation in self.consistency_violations:
                    severity = violation.severity
                    violations_by_severity[severity] = violations_by_severity.get(severity, 0) + 1
                
                return {
                    "total_transactions": total_transactions,
                    "transactions_by_state": by_state,
                    "consistency_violations": len(self.consistency_violations),
                    "violations_by_severity": violations_by_severity,
                    "service_states": self.service_states.copy()
                }
        
        return TransactionCoordinator()
    
    @pytest.mark.asyncio
    async def test_successful_cross_service_transaction(self, transaction_coordinator):
        """Test successful cross-service transaction with ACID properties."""
        print("Testing successful cross-service transaction...")
        
        # Begin transaction
        transaction = await transaction_coordinator.begin_transaction()
        transaction_id = transaction.transaction_id
        
        # Add operations across multiple services
        operations = [
            # Database operations
            await transaction_coordinator.add_operation(
                transaction_id, "database", "create", "pattern",
                "pattern_123", {"name": "Iterator Pattern", "confidence": 0.95},
                {"operation": "delete", "resource_id": "pattern_123"}
            ),
            await transaction_coordinator.add_operation(
                transaction_id, "database", "create", "analysis",
                "analysis_456", {"repository_id": "repo_123", "status": "completed"},
                {"operation": "delete", "resource_id": "analysis_456"}
            ),
            
            # Cache operations
            await transaction_coordinator.add_operation(
                transaction_id, "cache", "create", "cache_entry",
                "cache_pattern_123", {"pattern_id": "pattern_123", "data": {"cached": True}},
                {"operation": "delete", "key": "cache_pattern_123"}
            ),
            
            # API operations
            await transaction_coordinator.add_operation(
                transaction_id, "api", "update", "response_cache",
                "response_123", {"response_data": {"patterns": 1}, "timestamp": datetime.utcnow().isoformat()},
                {"operation": "restore", "previous_data": None}
            )
        ]
        
        # Verify transaction setup
        assert len(transaction.operations) == 4
        assert len(transaction.participants) == 3  # database, cache, api
        assert transaction.state == TransactionState.INITIATED
        
        # Phase 1: Prepare
        prepare_start = time.time()
        prepare_success = await transaction_coordinator.prepare_transaction(transaction_id)
        prepare_time = time.time() - prepare_start
        
        assert prepare_success, "Transaction preparation should succeed"
        assert transaction.state == TransactionState.PREPARED
        
        # Phase 2: Commit
        commit_start = time.time()
        commit_success = await transaction_coordinator.commit_transaction(transaction_id)
        commit_time = time.time() - commit_start
        
        assert commit_success, "Transaction commit should succeed"
        assert transaction.state == TransactionState.COMMITTED
        
        # Verify consistency
        consistency_report = transaction_coordinator.get_consistency_report()
        
        print(f"Successful cross-service transaction results:")
        print(f"  - Transaction ID: {transaction_id}")
        print(f"  - Operations: {len(operations)}")
        print(f"  - Participants: {list(transaction.participants)}")
        print(f"  - Prepare time: {prepare_time:.3f}s")
        print(f"  - Commit time: {commit_time:.3f}s")
        print(f"  - Final state: {transaction.state.value}")
        print(f"  - Consistency violations: {consistency_report['consistency_violations']}")
        
        return {
            "transaction_committed": transaction.state == TransactionState.COMMITTED,
            "prepare_successful": prepare_success,
            "commit_successful": commit_success,
            "consistency_violations": consistency_report["consistency_violations"],
            "total_time": prepare_time + commit_time
        }
    
    @pytest.mark.asyncio
    async def test_transaction_abort_with_compensation(self, transaction_coordinator):
        """Test transaction abort with compensating actions."""
        print("Testing transaction abort with compensation...")
        
        # Begin transaction
        transaction = await transaction_coordinator.begin_transaction()
        transaction_id = transaction.transaction_id
        
        # Add operations that will cause prepare to fail
        await transaction_coordinator.add_operation(
            transaction_id, "database", "create", "pattern",
            "pattern_fail_1", {"name": "Pattern 1"}, {"operation": "delete"}
        )
        
        # Add many operations to trigger database preparation failure
        for i in range(10):  # This should exceed database operation limit
            await transaction_coordinator.add_operation(
                transaction_id, "database", "create", "pattern",
                f"pattern_bulk_{i}", {"name": f"Bulk Pattern {i}"},
                {"operation": "delete", "resource_id": f"pattern_bulk_{i}"}
            )
        
        # Add operations to other services
        await transaction_coordinator.add_operation(
            transaction_id, "cache", "create", "cache_entry",
            "cache_fail", {"data": "test"}, {"operation": "delete"}
        )
        
        # Attempt to prepare (should fail due to too many database operations)
        prepare_success = await transaction_coordinator.prepare_transaction(transaction_id)
        
        # Preparation should fail
        assert not prepare_success, "Transaction preparation should fail with too many operations"
        assert transaction.state == TransactionState.ABORTING
        
        # Abort transaction
        abort_start = time.time()
        abort_success = await transaction_coordinator.abort_transaction(transaction_id)
        abort_time = time.time() - abort_start
        
        assert transaction.state == TransactionState.ABORTED
        
        # Check consistency
        consistency_report = transaction_coordinator.get_consistency_report()
        
        print(f"Transaction abort with compensation results:")
        print(f"  - Transaction ID: {transaction_id}")
        print(f"  - Operations attempted: {len(transaction.operations)}")
        print(f"  - Prepare success: {prepare_success}")
        print(f"  - Abort success: {abort_success}")
        print(f"  - Abort time: {abort_time:.3f}s")
        print(f"  - Final state: {transaction.state.value}")
        print(f"  - Consistency violations: {consistency_report['consistency_violations']}")
        
        return {
            "prepare_failed_as_expected": not prepare_success,
            "abort_successful": abort_success,
            "final_state_correct": transaction.state == TransactionState.ABORTED,
            "compensation_completed": abort_success,
            "consistency_violations": consistency_report["consistency_violations"]
        }
    
    @pytest.mark.asyncio
    async def test_concurrent_transactions_conflict_resolution(self, transaction_coordinator):
        """Test concurrent transactions with conflict resolution."""
        print("Testing concurrent transactions with conflict resolution...")
        
        # Create two concurrent transactions that will conflict
        transaction_1 = await transaction_coordinator.begin_transaction("txn_concurrent_1")
        transaction_2 = await transaction_coordinator.begin_transaction("txn_concurrent_2")
        
        # Both transactions try to modify the same resource
        conflicting_resource_id = "pattern_conflict_test"
        
        # Transaction 1 operations
        await transaction_coordinator.add_operation(
            "txn_concurrent_1", "database", "update", "pattern",
            conflicting_resource_id, {"confidence": 0.95, "version": 1},
            {"operation": "restore", "data": {"confidence": 0.85, "version": 0}}
        )
        
        await transaction_coordinator.add_operation(
            "txn_concurrent_1", "cache", "update", "cache_entry",
            f"cache_{conflicting_resource_id}", {"confidence": 0.95},
            {"operation": "restore", "data": {"confidence": 0.85}}
        )
        
        # Transaction 2 operations (conflicts with transaction 1)
        await transaction_coordinator.add_operation(
            "txn_concurrent_2", "database", "update", "pattern",
            conflicting_resource_id, {"confidence": 0.88, "version": 2},
            {"operation": "restore", "data": {"confidence": 0.85, "version": 0}}
        )
        
        await transaction_coordinator.add_operation(
            "txn_concurrent_2", "cache", "delete", "cache_entry",
            f"cache_{conflicting_resource_id}", {},
            {"operation": "create", "data": {"confidence": 0.85}}
        )
        
        # Execute transactions concurrently
        async def execute_transaction(txn_id: str):
            prepare_success = await transaction_coordinator.prepare_transaction(txn_id)
            if prepare_success:
                commit_success = await transaction_coordinator.commit_transaction(txn_id)
                return commit_success
            else:
                await transaction_coordinator.abort_transaction(txn_id)
                return False
        
        # Run both transactions concurrently
        start_time = time.time()
        results = await asyncio.gather(
            execute_transaction("txn_concurrent_1"),
            execute_transaction("txn_concurrent_2"),
            return_exceptions=True
        )
        execution_time = time.time() - start_time
        
        # Analyze results
        txn1_success = results[0] if not isinstance(results[0], Exception) else False
        txn2_success = results[1] if not isinstance(results[1], Exception) else False
        
        txn1_final_state = transaction_coordinator.transactions["txn_concurrent_1"].state
        txn2_final_state = transaction_coordinator.transactions["txn_concurrent_2"].state
        
        # In a real system with proper conflict detection, only one should succeed
        # Here we simulate the outcome
        both_succeeded = txn1_success and txn2_success
        at_least_one_succeeded = txn1_success or txn2_success
        
        consistency_report = transaction_coordinator.get_consistency_report()
        
        print(f"Concurrent transactions conflict resolution results:")
        print(f"  - Transaction 1 success: {txn1_success}, state: {txn1_final_state.value}")
        print(f"  - Transaction 2 success: {txn2_success}, state: {txn2_final_state.value}")
        print(f"  - Both succeeded: {both_succeeded}")
        print(f"  - At least one succeeded: {at_least_one_succeeded}")
        print(f"  - Execution time: {execution_time:.3f}s")
        print(f"  - Consistency violations: {consistency_report['consistency_violations']}")
        
        # If both succeeded, it might indicate a consistency issue
        if both_succeeded:
            print("  - WARNING: Both conflicting transactions succeeded - potential consistency issue")
        
        return {
            "concurrent_execution_completed": True,
            "transaction_1_success": txn1_success,
            "transaction_2_success": txn2_success,
            "conflict_detected": not both_succeeded,
            "consistency_violations": consistency_report["consistency_violations"],
            "execution_time": execution_time
        }


@pytest.mark.integration
@pytest.mark.data_consistency
class TestEventualConsistency:
    """Test eventual consistency patterns."""
    
    @pytest.fixture
    async def consistency_monitor(self):
        """Create eventual consistency monitoring system."""
        
        class ConsistencyMonitor:
            def __init__(self):
                self.data_stores = {
                    "primary_db": {},
                    "cache": {},
                    "read_replica": {},
                    "search_index": {}
                }
                self.propagation_log = []
                self.consistency_checks = []
                self._monitor_lock = asyncio.Lock()
            
            async def write_to_primary(self, key: str, value: Any, metadata: Dict[str, Any] = None):
                """Write to primary data store."""
                async with self._monitor_lock:
                    timestamp = datetime.utcnow()
                    
                    entry = {
                        "value": value,
                        "timestamp": timestamp,
                        "version": self._get_next_version("primary_db", key),
                        "metadata": metadata or {}
                    }
                    
                    self.data_stores["primary_db"][key] = entry
                    
                    # Log the write
                    self.propagation_log.append({
                        "action": "write",
                        "store": "primary_db",
                        "key": key,
                        "timestamp": timestamp,
                        "version": entry["version"]
                    })
                    
                    # Trigger eventual propagation
                    await self._schedule_propagation(key, entry)
            
            async def _schedule_propagation(self, key: str, entry: Dict[str, Any]):
                """Schedule propagation to other data stores."""
                # Simulate different propagation delays
                propagation_tasks = [
                    self._propagate_to_store("cache", key, entry, delay=0.05),      # Fast cache update
                    self._propagate_to_store("read_replica", key, entry, delay=0.2), # Medium DB replication
                    self._propagate_to_store("search_index", key, entry, delay=0.8)  # Slow search indexing
                ]
                
                # Don't wait for propagation - eventual consistency
                asyncio.create_task(asyncio.gather(*propagation_tasks, return_exceptions=True))
            
            async def _propagate_to_store(self, store_name: str, key: str, entry: Dict[str, Any], delay: float):
                """Propagate data to a specific store with simulated delay."""
                await asyncio.sleep(delay)
                
                async with self._monitor_lock:
                    # Simulate potential propagation failures
                    if store_name == "search_index" and random.choice([True, False, False]):  # 33% failure rate
                        self.propagation_log.append({
                            "action": "propagation_failed",
                            "store": store_name,
                            "key": key,
                            "timestamp": datetime.utcnow(),
                            "reason": "search_index_timeout"
                        })
                        return
                    
                    # Apply propagation
                    self.data_stores[store_name][key] = entry.copy()
                    
                    self.propagation_log.append({
                        "action": "propagated",
                        "store": store_name,
                        "key": key,
                        "timestamp": datetime.utcnow(),
                        "version": entry["version"]
                    })
            
            async def read_from_store(self, store_name: str, key: str, consistency_level: ConsistencyLevel = ConsistencyLevel.EVENTUAL):
                """Read from a specific data store with consistency requirements."""
                async with self._monitor_lock:
                    if consistency_level == ConsistencyLevel.STRONG:
                        # Strong consistency requires reading from primary
                        store_name = "primary_db"
                    
                    if store_name in self.data_stores and key in self.data_stores[store_name]:
                        value = self.data_stores[store_name][key]
                        
                        # Log the read
                        self.propagation_log.append({
                            "action": "read",
                            "store": store_name,
                            "key": key,
                            "timestamp": datetime.utcnow(),
                            "version": value["version"]
                        })
                        
                        return value
                    
                    return None
            
            async def check_consistency(self, key: str) -> Dict[str, Any]:
                """Check consistency of a key across all data stores."""
                async with self._monitor_lock:
                    consistency_check = {
                        "key": key,
                        "timestamp": datetime.utcnow(),
                        "stores_with_data": [],
                        "versions": {},
                        "consistent": True,
                        "lag_times": {}
                    }
                    
                    primary_entry = self.data_stores["primary_db"].get(key)
                    if not primary_entry:
                        consistency_check["consistent"] = False
                        consistency_check["reason"] = "no_primary_data"
                        return consistency_check
                    
                    primary_version = primary_entry["version"]
                    primary_timestamp = primary_entry["timestamp"]
                    
                    # Check each store
                    for store_name, store_data in self.data_stores.items():
                        if key in store_data:
                            entry = store_data[key]
                            consistency_check["stores_with_data"].append(store_name)
                            consistency_check["versions"][store_name] = entry["version"]
                            
                            # Calculate lag time
                            if store_name != "primary_db":
                                lag_time = (entry["timestamp"] - primary_timestamp).total_seconds()
                                consistency_check["lag_times"][store_name] = lag_time
                            
                            # Check version consistency
                            if entry["version"] != primary_version:
                                consistency_check["consistent"] = False
                    
                    # Check if all stores have the data
                    expected_stores = len(self.data_stores)
                    actual_stores = len(consistency_check["stores_with_data"])
                    
                    if actual_stores < expected_stores:
                        consistency_check["consistent"] = False
                        consistency_check["missing_stores"] = [
                            store for store in self.data_stores.keys()
                            if store not in consistency_check["stores_with_data"]
                        ]
                    
                    self.consistency_checks.append(consistency_check)
                    return consistency_check
            
            def _get_next_version(self, store: str, key: str) -> int:
                """Get next version number for a key."""
                if store in self.data_stores and key in self.data_stores[store]:
                    return self.data_stores[store][key]["version"] + 1
                return 1
            
            async def wait_for_consistency(self, key: str, timeout: float = 5.0) -> bool:
                """Wait for eventual consistency to be achieved."""
                start_time = time.time()
                
                while time.time() - start_time < timeout:
                    consistency_check = await self.check_consistency(key)
                    if consistency_check["consistent"]:
                        return True
                    
                    await asyncio.sleep(0.1)  # Check every 100ms
                
                return False
            
            def get_propagation_summary(self) -> Dict[str, Any]:
                """Get summary of propagation activities."""
                actions_by_type = {}
                stores_by_activity = {}
                
                for log_entry in self.propagation_log:
                    action = log_entry["action"]
                    store = log_entry["store"]
                    
                    actions_by_type[action] = actions_by_type.get(action, 0) + 1
                    stores_by_activity[store] = stores_by_activity.get(store, 0) + 1
                
                # Calculate consistency metrics
                total_checks = len(self.consistency_checks)
                consistent_checks = sum(1 for check in self.consistency_checks if check["consistent"])
                
                return {
                    "total_propagation_events": len(self.propagation_log),
                    "actions_by_type": actions_by_type,
                    "stores_by_activity": stores_by_activity,
                    "consistency_checks": total_checks,
                    "consistent_checks": consistent_checks,
                    "consistency_rate": consistent_checks / total_checks if total_checks > 0 else 0,
                    "data_stores": list(self.data_stores.keys())
                }
        
        return ConsistencyMonitor()
    
    @pytest.mark.asyncio
    async def test_eventual_consistency_convergence(self, consistency_monitor):
        """Test eventual consistency convergence across data stores."""
        print("Testing eventual consistency convergence...")
        
        # Write data to primary store
        test_data = [
            ("pattern_001", {"name": "Singleton", "confidence": 0.95}),
            ("pattern_002", {"name": "Observer", "confidence": 0.88}),
            ("pattern_003", {"name": "Factory", "confidence": 0.92})
        ]
        
        write_start = time.time()
        
        for key, value in test_data:
            await consistency_monitor.write_to_primary(key, value, {"test": "eventual_consistency"})
        
        write_time = time.time() - write_start
        
        # Immediately check consistency (should be inconsistent)
        immediate_consistency_checks = []
        for key, _ in test_data:
            check = await consistency_monitor.check_consistency(key)
            immediate_consistency_checks.append(check)
        
        immediate_consistent_count = sum(1 for check in immediate_consistency_checks if check["consistent"])
        
        # Wait for eventual consistency
        print("Waiting for eventual consistency...")
        convergence_start = time.time()
        
        convergence_results = []
        for key, _ in test_data:
            converged = await consistency_monitor.wait_for_consistency(key, timeout=3.0)
            convergence_results.append(converged)
        
        convergence_time = time.time() - convergence_start
        
        # Final consistency check
        final_consistency_checks = []
        for key, _ in test_data:
            check = await consistency_monitor.check_consistency(key)
            final_consistency_checks.append(check)
        
        final_consistent_count = sum(1 for check in final_consistency_checks if check["consistent"])
        
        # Analyze propagation
        propagation_summary = consistency_monitor.get_propagation_summary()
        
        print(f"Eventual consistency convergence results:")
        print(f"  - Data items written: {len(test_data)}")
        print(f"  - Write time: {write_time:.3f}s")
        print(f"  - Immediately consistent: {immediate_consistent_count}/{len(test_data)}")
        print(f"  - Eventually consistent: {final_consistent_count}/{len(test_data)}")
        print(f"  - Convergence time: {convergence_time:.3f}s")
        print(f"  - Propagation events: {propagation_summary['total_propagation_events']}")
        print(f"  - Consistency rate: {propagation_summary['consistency_rate']:.2f}")
        
        return {
            "convergence_successful": final_consistent_count == len(test_data),
            "immediate_consistency_rate": immediate_consistent_count / len(test_data),
            "final_consistency_rate": final_consistent_count / len(test_data),
            "convergence_time": convergence_time,
            "propagation_events": propagation_summary["total_propagation_events"]
        }
    
    @pytest.mark.asyncio
    async def test_read_consistency_levels(self, consistency_monitor):
        """Test different read consistency levels."""
        print("Testing read consistency levels...")
        
        # Write test data
        test_key = "consistency_level_test"
        test_value = {"pattern": "Strategy", "confidence": 0.91}
        
        await consistency_monitor.write_to_primary(test_key, test_value)
        
        # Test different consistency levels immediately after write
        consistency_level_results = {}
        
        # Strong consistency (should always read from primary)
        strong_read = await consistency_monitor.read_from_store(
            "primary_db", test_key, ConsistencyLevel.STRONG
        )
        consistency_level_results["strong"] = strong_read is not None
        
        # Eventual consistency from cache (might not be available yet)
        eventual_cache_read = await consistency_monitor.read_from_store(
            "cache", test_key, ConsistencyLevel.EVENTUAL
        )
        consistency_level_results["eventual_cache"] = eventual_cache_read is not None
        
        # Eventual consistency from read replica (might not be available yet)
        eventual_replica_read = await consistency_monitor.read_from_store(
            "read_replica", test_key, ConsistencyLevel.EVENTUAL
        )
        consistency_level_results["eventual_replica"] = eventual_replica_read is not None
        
        # Wait for propagation and test again
        await asyncio.sleep(1.0)  # Wait for propagation
        
        # Test eventual consistency after propagation
        post_propagation_results = {}
        
        for store in ["cache", "read_replica", "search_index"]:
            read_result = await consistency_monitor.read_from_store(store, test_key)
            post_propagation_results[store] = read_result is not None
        
        # Analyze read patterns
        propagation_summary = consistency_monitor.get_propagation_summary()
        
        print(f"Read consistency levels test results:")
        print(f"  - Strong consistency read: {'✓' if consistency_level_results['strong'] else '✗'}")
        print(f"  - Immediate eventual reads:")
        print(f"    - Cache: {'✓' if consistency_level_results['eventual_cache'] else '✗'}")
        print(f"    - Replica: {'✓' if consistency_level_results['eventual_replica'] else '✗'}")
        print(f"  - Post-propagation reads:")
        for store, available in post_propagation_results.items():
            print(f"    - {store}: {'✓' if available else '✗'}")
        print(f"  - Total propagation events: {propagation_summary['total_propagation_events']}")
        
        return {
            "strong_consistency_reliable": consistency_level_results["strong"],
            "eventual_consistency_propagated": sum(post_propagation_results.values()) >= 2,
            "propagation_events": propagation_summary["total_propagation_events"],
            "consistency_behavior_correct": (
                consistency_level_results["strong"] and 
                sum(post_propagation_results.values()) >= len(post_propagation_results) // 2
            )
        }
    
    @pytest.mark.asyncio
    async def test_consistency_during_partial_failures(self, consistency_monitor):
        """Test consistency behavior during partial data store failures."""
        print("Testing consistency during partial failures...")
        
        # Write multiple pieces of data
        test_data = [
            ("failure_test_1", {"type": "command", "confidence": 0.87}),
            ("failure_test_2", {"type": "query", "confidence": 0.93}),
            ("failure_test_3", {"type": "event", "confidence": 0.89})
        ]
        
        # Write all data
        for key, value in test_data:
            await consistency_monitor.write_to_primary(key, value)
        
        # Wait for initial propagation
        await asyncio.sleep(0.5)
        
        # Check consistency before simulated failures
        pre_failure_consistency = []
        for key, _ in test_data:
            check = await consistency_monitor.check_consistency(key)
            pre_failure_consistency.append(check["consistent"])
        
        # Simulate partial store failures by removing data
        print("Simulating partial store failures...")
        
        # Simulate cache failure for some keys
        if "cache" in consistency_monitor.data_stores:
            if "failure_test_1" in consistency_monitor.data_stores["cache"]:
                del consistency_monitor.data_stores["cache"]["failure_test_1"]
            if "failure_test_2" in consistency_monitor.data_stores["cache"]:
                del consistency_monitor.data_stores["cache"]["failure_test_2"]
        
        # Simulate search index failure for other keys
        if "search_index" in consistency_monitor.data_stores:
            consistency_monitor.data_stores["search_index"].clear()  # Complete index failure
        
        # Check consistency after failures
        post_failure_consistency = []
        for key, _ in test_data:
            check = await consistency_monitor.check_consistency(key)
            post_failure_consistency.append(check)
        
        # Test read availability during failures
        read_availability = {}
        for key, _ in test_data:
            reads = {}
            for store in consistency_monitor.data_stores.keys():
                result = await consistency_monitor.read_from_store(store, key)
                reads[store] = result is not None
            read_availability[key] = reads
        
        # Analyze failure impact
        consistent_before = sum(pre_failure_consistency)
        consistent_after = sum(1 for check in post_failure_consistency if check["consistent"])
        
        # Calculate read availability
        total_reads = len(test_data) * len(consistency_monitor.data_stores)
        successful_reads = sum(
            sum(reads.values()) for reads in read_availability.values()
        )
        read_availability_rate = successful_reads / total_reads
        
        propagation_summary = consistency_monitor.get_propagation_summary()
        
        print(f"Consistency during partial failures results:")
        print(f"  - Data items: {len(test_data)}")
        print(f"  - Consistent before failures: {consistent_before}/{len(test_data)}")
        print(f"  - Consistent after failures: {consistent_after}/{len(test_data)}")
        print(f"  - Read availability rate: {read_availability_rate:.2f}")
        print(f"  - Primary always available: {all(avail['primary_db'] for avail in read_availability.values())}")
        print(f"  - Propagation events: {propagation_summary['total_propagation_events']}")
        
        return {
            "primary_availability_maintained": all(avail["primary_db"] for avail in read_availability.values()),
            "partial_availability_acceptable": read_availability_rate > 0.5,
            "consistency_degraded_gracefully": consistent_after < consistent_before,
            "system_remained_functional": read_availability_rate > 0.25,
            "failure_impact_contained": read_availability_rate > 0.0
        }


@pytest.mark.integration
@pytest.mark.data_consistency
class TestAdvancedCrossServiceConsistency:
    """Test advanced cross-service data consistency scenarios."""
    
    @pytest.fixture
    async def multi_service_state_manager(self):
        """Create multi-service state management for comprehensive consistency testing."""
        
        class MultiServiceStateManager:
            def __init__(self):
                self.service_states = {
                    "api_service": {"entities": {}, "version": 0, "last_sync": None},
                    "database_service": {"entities": {}, "version": 0, "last_sync": None},
                    "cache_service": {"entities": {}, "version": 0, "last_sync": None},
                    "ml_service": {"entities": {}, "version": 0, "last_sync": None},
                    "analytics_service": {"entities": {}, "version": 0, "last_sync": None}
                }
                self.consistency_log = []
                self.conflict_resolution_log = []
                self.synchronization_events = []
                self._state_lock = asyncio.Lock()
                self._version_counter = 0
            
            async def create_analysis_entity(
                self, 
                entity_id: str, 
                analysis_data: Dict[str, Any],
                initiating_service: str = "api_service"
            ) -> Dict[str, Any]:
                """Create an analysis entity across all services with consistency tracking."""
                async with self._state_lock:
                    self._version_counter += 1
                    transaction_id = f"txn_{uuid.uuid4().hex[:8]}"
                    
                    # Entity structure with comprehensive metadata
                    entity = {
                        "entity_id": entity_id,
                        "entity_type": "pattern_analysis",
                        "data": analysis_data,
                        "version": self._version_counter,
                        "created_at": datetime.utcnow().isoformat(),
                        "created_by": initiating_service,
                        "transaction_id": transaction_id,
                        "consistency_markers": {
                            service: {"status": "pending", "version": None, "timestamp": None}
                            for service in self.service_states.keys()
                        }
                    }
                    
                    # Simulate service-specific entity creation
                    service_creation_results = {}
                    
                    for service_name, service_state in self.service_states.items():
                        try:
                            # Simulate service-specific processing time and failure probability
                            processing_time = random.uniform(0.005, 0.02)  # 5-20ms
                            await asyncio.sleep(processing_time)
                            
                            # Different services have different failure rates
                            failure_rates = {
                                "api_service": 0.01,      # 1% failure rate
                                "database_service": 0.02, # 2% failure rate  
                                "cache_service": 0.05,    # 5% failure rate
                                "ml_service": 0.03,       # 3% failure rate
                                "analytics_service": 0.04 # 4% failure rate
                            }
                            
                            if random.random() < failure_rates.get(service_name, 0.02):
                                raise Exception(f"{service_name} temporarily unavailable")
                            
                            # Create service-specific representation
                            service_entity = self._create_service_specific_entity(
                                entity, service_name, analysis_data
                            )
                            
                            # Store in service state
                            service_state["entities"][entity_id] = service_entity
                            service_state["version"] += 1
                            service_state["last_sync"] = datetime.utcnow().isoformat()
                            
                            # Update consistency marker
                            entity["consistency_markers"][service_name] = {
                                "status": "committed",
                                "version": service_entity["version"],
                                "timestamp": datetime.utcnow().isoformat()
                            }
                            
                            service_creation_results[service_name] = {
                                "success": True,
                                "service_version": service_entity["version"],
                                "processing_time_ms": processing_time * 1000
                            }
                            
                        except Exception as e:
                            service_creation_results[service_name] = {
                                "success": False,
                                "error": str(e),
                                "processing_time_ms": processing_time * 1000 if 'processing_time' in locals() else 0
                            }
                            
                            # Mark as failed in consistency markers
                            entity["consistency_markers"][service_name] = {
                                "status": "failed",
                                "version": None,
                                "timestamp": datetime.utcnow().isoformat(),
                                "error": str(e)
                            }
                    
                    # Log consistency state
                    consistency_status = self._analyze_entity_consistency(entity)
                    self.consistency_log.append({
                        "transaction_id": transaction_id,
                        "entity_id": entity_id,
                        "operation": "create",
                        "consistency_status": consistency_status,
                        "service_results": service_creation_results,
                        "timestamp": datetime.utcnow().isoformat()
                    })
                    
                    return {
                        "entity": entity,
                        "transaction_id": transaction_id,
                        "service_results": service_creation_results,
                        "consistency_status": consistency_status
                    }
            
            async def update_analysis_entity(
                self, 
                entity_id: str, 
                updates: Dict[str, Any],
                initiating_service: str = "api_service"
            ) -> Dict[str, Any]:
                """Update an analysis entity with conflict detection and resolution."""
                async with self._state_lock:
                    transaction_id = f"update_txn_{uuid.uuid4().hex[:8]}"
                    
                    # Check if entity exists in all services
                    entity_versions = {}
                    conflicts_detected = []
                    
                    for service_name, service_state in self.service_states.items():
                        if entity_id in service_state["entities"]:
                            entity_versions[service_name] = service_state["entities"][entity_id]["version"]
                        else:
                            conflicts_detected.append(f"Entity missing from {service_name}")
                    
                    # Detect version conflicts (different versions across services)
                    if entity_versions:
                        min_version = min(entity_versions.values())
                        max_version = max(entity_versions.values())
                        if max_version - min_version > 0:
                            conflicts_detected.append(f"Version skew detected: {min_version}-{max_version}")
                    
                    # Apply conflict resolution strategy
                    resolution_strategy = "last_writer_wins"  # Could be configurable
                    update_results = {}
                    
                    for service_name, service_state in self.service_states.items():
                        try:
                            if entity_id not in service_state["entities"]:
                                # Entity doesn't exist in this service - potential consistency issue
                                update_results[service_name] = {
                                    "success": False,
                                    "error": "entity_not_found",
                                    "resolution": "skip_update"
                                }
                                continue
                            
                            # Simulate update processing
                            processing_time = random.uniform(0.01, 0.03)  # 10-30ms
                            await asyncio.sleep(processing_time)
                            
                            # Update entity with new data
                            entity = service_state["entities"][entity_id]
                            old_version = entity["version"]
                            
                            # Apply updates
                            entity["data"].update(updates)
                            entity["version"] += 1
                            entity["updated_at"] = datetime.utcnow().isoformat()
                            entity["updated_by"] = initiating_service
                            entity["transaction_id"] = transaction_id
                            
                            # Update service state
                            service_state["version"] += 1
                            service_state["last_sync"] = datetime.utcnow().isoformat()
                            
                            update_results[service_name] = {
                                "success": True,
                                "old_version": old_version,
                                "new_version": entity["version"],
                                "processing_time_ms": processing_time * 1000
                            }
                            
                        except Exception as e:
                            update_results[service_name] = {
                                "success": False,
                                "error": str(e),
                                "processing_time_ms": processing_time * 1000 if 'processing_time' in locals() else 0
                            }
                    
                    # Log conflict resolution if conflicts were detected
                    if conflicts_detected:
                        self.conflict_resolution_log.append({
                            "transaction_id": transaction_id,
                            "entity_id": entity_id,
                            "conflicts": conflicts_detected,
                            "resolution_strategy": resolution_strategy,
                            "timestamp": datetime.utcnow().isoformat()
                        })
                    
                    return {
                        "transaction_id": transaction_id,
                        "entity_id": entity_id,
                        "conflicts_detected": conflicts_detected,
                        "update_results": update_results,
                        "resolution_strategy": resolution_strategy
                    }
            
            async def validate_cross_service_consistency(self, entity_id: str) -> Dict[str, Any]:
                """Validate consistency of an entity across all services."""
                async with self._state_lock:
                    consistency_report = {
                        "entity_id": entity_id,
                        "services_with_entity": [],
                        "services_missing_entity": [],
                        "version_consistency": True,
                        "data_consistency": True,
                        "version_details": {},
                        "data_hash_details": {},
                        "inconsistencies": []
                    }
                    
                    # Check presence and versions across services
                    entity_versions = {}
                    entity_data_hashes = {}
                    
                    for service_name, service_state in self.service_states.items():
                        if entity_id in service_state["entities"]:
                            entity = service_state["entities"][entity_id]
                            consistency_report["services_with_entity"].append(service_name)
                            
                            # Track version
                            entity_versions[service_name] = entity["version"]
                            
                            # Create data hash for consistency checking
                            entity_data_str = json.dumps(entity["data"], sort_keys=True)
                            data_hash = hashlib.md5(entity_data_str.encode()).hexdigest()
                            entity_data_hashes[service_name] = data_hash
                            
                        else:
                            consistency_report["services_missing_entity"].append(service_name)
                    
                    consistency_report["version_details"] = entity_versions
                    consistency_report["data_hash_details"] = entity_data_hashes
                    
                    # Check version consistency
                    if entity_versions:
                        unique_versions = set(entity_versions.values())
                        if len(unique_versions) > 1:
                            consistency_report["version_consistency"] = False
                            consistency_report["inconsistencies"].append(
                                f"Version mismatch: {dict(entity_versions)}"
                            )
                    
                    # Check data consistency
                    if entity_data_hashes:
                        unique_hashes = set(entity_data_hashes.values())
                        if len(unique_hashes) > 1:
                            consistency_report["data_consistency"] = False
                            consistency_report["inconsistencies"].append(
                                f"Data mismatch: different content hashes across services"
                            )
                    
                    # Check for missing entities
                    if consistency_report["services_missing_entity"]:
                        consistency_report["inconsistencies"].append(
                            f"Entity missing from services: {consistency_report['services_missing_entity']}"
                        )
                    
                    return consistency_report
            
            def _create_service_specific_entity(
                self, 
                base_entity: Dict[str, Any], 
                service_name: str, 
                analysis_data: Dict[str, Any]
            ) -> Dict[str, Any]:
                """Create service-specific representation of entity."""
                service_entity = copy.deepcopy(base_entity)
                service_entity["service_name"] = service_name
                service_entity["version"] = 1  # Service-specific version
                
                # Add service-specific fields
                if service_name == "database_service":
                    service_entity["db_id"] = f"db_{uuid.uuid4().hex[:8]}"
                    service_entity["table_name"] = "pattern_analyses"
                elif service_name == "cache_service":
                    service_entity["cache_key"] = f"analysis:{base_entity['entity_id']}"
                    service_entity["ttl_seconds"] = 3600
                elif service_name == "ml_service":
                    service_entity["model_version"] = "gemini-2.5-flash"
                    service_entity["inference_id"] = f"ml_{uuid.uuid4().hex[:8]}"
                elif service_name == "analytics_service":
                    service_entity["metrics"] = {
                        "patterns_detected": len(analysis_data.get("patterns", [])),
                        "confidence_score": analysis_data.get("confidence", 0.0)
                    }
                
                return service_entity
            
            def _analyze_entity_consistency(self, entity: Dict[str, Any]) -> Dict[str, Any]:
                """Analyze consistency status of an entity."""
                markers = entity["consistency_markers"]
                
                committed_services = [
                    service for service, marker in markers.items() 
                    if marker["status"] == "committed"
                ]
                failed_services = [
                    service for service, marker in markers.items() 
                    if marker["status"] == "failed"
                ]
                
                total_services = len(markers)
                success_rate = len(committed_services) / total_services
                
                if success_rate == 1.0:
                    consistency_level = "strong"
                elif success_rate >= 0.8:
                    consistency_level = "good"
                elif success_rate >= 0.6:
                    consistency_level = "degraded"
                else:
                    consistency_level = "poor"
                
                return {
                    "consistency_level": consistency_level,
                    "success_rate": success_rate,
                    "committed_services": committed_services,
                    "failed_services": failed_services,
                    "total_services": total_services
                }
            
            def get_consistency_summary(self) -> Dict[str, Any]:
                """Get overall consistency summary."""
                total_transactions = len(self.consistency_log)
                
                if total_transactions == 0:
                    return {"total_transactions": 0, "no_data": True}
                
                # Analyze consistency levels
                consistency_levels = [log["consistency_status"]["consistency_level"] for log in self.consistency_log]
                level_counts = {level: consistency_levels.count(level) for level in ["strong", "good", "degraded", "poor"]}
                
                # Calculate average success rate
                success_rates = [log["consistency_status"]["success_rate"] for log in self.consistency_log]
                avg_success_rate = sum(success_rates) / len(success_rates)
                
                return {
                    "total_transactions": total_transactions,
                    "consistency_distribution": level_counts,
                    "average_success_rate": avg_success_rate,
                    "conflicts_detected": len(self.conflict_resolution_log),
                    "strong_consistency_rate": level_counts.get("strong", 0) / total_transactions
                }
        
        return MultiServiceStateManager()
    
    @pytest.mark.asyncio
    async def test_comprehensive_cross_service_entity_lifecycle(
        self,
        async_test_client: AsyncClient,
        multi_service_state_manager
    ):
        """Test complete entity lifecycle across all services with comprehensive consistency validation."""
        
        # Test data for comprehensive analysis entities
        test_entities = [
            {
                "entity_id": f"analysis_{i}",
                "analysis_data": {
                    "repository_id": f"repo_{i}",
                    "patterns": [
                        {
                            "pattern_type": random.choice(["security", "performance", "maintainability"]),
                            "severity": random.choice(["low", "medium", "high"]),
                            "confidence": random.uniform(0.7, 0.95)
                        }
                        for _ in range(random.randint(3, 8))
                    ],
                    "analysis_config": {
                        "ml_enabled": True,
                        "cache_enabled": True,
                        "quality_threshold": 0.8
                    },
                    "metadata": {
                        "created_by": f"user_{random.randint(1, 5)}",
                        "repository_size": random.randint(1000, 100000)
                    }
                }
            }
            for i in range(10)  # Test with 10 entities
        ]
        
        print(f"Testing comprehensive cross-service lifecycle for {len(test_entities)} analysis entities...")
        
        # Phase 1: Entity Creation
        creation_results = []
        start_time = time.time()
        
        for entity_spec in test_entities:
            creation_result = await multi_service_state_manager.create_analysis_entity(
                entity_spec["entity_id"],
                entity_spec["analysis_data"],
                "api_service"
            )
            creation_results.append(creation_result)
            
            # Small delay to simulate realistic timing
            await asyncio.sleep(0.01)
        
        creation_time = time.time() - start_time
        
        # Analyze creation results
        successful_creations = [r for r in creation_results if r["consistency_status"]["consistency_level"] in ["strong", "good"]]
        strong_consistency_creations = [r for r in creation_results if r["consistency_status"]["consistency_level"] == "strong"]
        
        assert len(successful_creations) >= 8, f"Expected at least 8 successful creations, got {len(successful_creations)}"
        
        print(f"Phase 1 - Entity Creation Results:")
        print(f"  - Total entities: {len(test_entities)}")
        print(f"  - Successful creations: {len(successful_creations)}")
        print(f"  - Strong consistency: {len(strong_consistency_creations)}")
        print(f"  - Creation time: {creation_time:.3f}s")
        
        # Phase 2: Concurrent Updates with Conflict Detection
        print(f"Phase 2 - Concurrent Updates...")
        update_start_time = time.time()
        
        # Select entities for concurrent updates
        entities_to_update = [r["entity"]["entity_id"] for r in successful_creations[:5]]
        
        update_tasks = []
        for entity_id in entities_to_update:
            # Create conflicting updates from different services
            update_1 = multi_service_state_manager.update_analysis_entity(
                entity_id,
                {"analysis_config": {"quality_threshold": 0.9}, "updated_reason": "user_request"},
                "api_service"
            )
            update_2 = multi_service_state_manager.update_analysis_entity(
                entity_id,
                {"analysis_config": {"quality_threshold": 0.7}, "updated_reason": "system_optimization"},
                "ml_service"
            )
            
            update_tasks.extend([update_1, update_2])
        
        # Execute concurrent updates
        update_results = await asyncio.gather(*update_tasks, return_exceptions=True)
        update_time = time.time() - update_start_time
        
        successful_updates = [r for r in update_results if not isinstance(r, Exception)]
        conflicts_detected = sum(1 for r in successful_updates if r.get("conflicts_detected"))
        
        print(f"  - Concurrent update tasks: {len(update_tasks)}")
        print(f"  - Successful updates: {len(successful_updates)}")
        print(f"  - Conflicts detected: {conflicts_detected}")
        print(f"  - Update time: {update_time:.3f}s")
        
        # Phase 3: Cross-Service Consistency Validation
        print(f"Phase 3 - Consistency Validation...")
        validation_start_time = time.time()
        
        consistency_reports = []
        for entity_spec in test_entities[:8]:  # Validate first 8 entities
            report = await multi_service_state_manager.validate_cross_service_consistency(
                entity_spec["entity_id"]
            )
            consistency_reports.append(report)
        
        validation_time = time.time() - validation_start_time
        
        # Analyze consistency results
        fully_consistent = [r for r in consistency_reports if r["version_consistency"] and r["data_consistency"]]
        partially_consistent = [r for r in consistency_reports if not (r["version_consistency"] and r["data_consistency"]) but r["services_with_entity"]]
        inconsistent = [r for r in consistency_reports if r["services_missing_entity"]]
        
        # Calculate consistency metrics
        total_services = 5  # api, database, cache, ml, analytics
        entity_availability = []
        
        for report in consistency_reports:
            availability = len(report["services_with_entity"]) / total_services
            entity_availability.append(availability)
        
        avg_availability = sum(entity_availability) / len(entity_availability) if entity_availability else 0.0
        
        print(f"  - Entities validated: {len(consistency_reports)}")
        print(f"  - Fully consistent: {len(fully_consistent)}")
        print(f"  - Partially consistent: {len(partially_consistent)}")
        print(f"  - Inconsistent: {len(inconsistent)}")
        print(f"  - Average availability: {avg_availability:.2%}")
        print(f"  - Validation time: {validation_time:.3f}s")
        
        # Phase 4: System-Wide Consistency Analysis
        consistency_summary = multi_service_state_manager.get_consistency_summary()
        
        print(f"Phase 4 - System-Wide Analysis:")
        print(f"  - Total transactions: {consistency_summary['total_transactions']}")
        print(f"  - Average success rate: {consistency_summary['average_success_rate']:.2%}")
        print(f"  - Strong consistency rate: {consistency_summary['strong_consistency_rate']:.2%}")
        print(f"  - Conflicts resolved: {consistency_summary['conflicts_detected']}")
        
        # Comprehensive assertions
        assert len(fully_consistent) >= 6, f"Expected at least 6 fully consistent entities, got {len(fully_consistent)}"
        assert avg_availability > 0.8, f"System availability too low: {avg_availability:.2%}"
        assert consistency_summary["average_success_rate"] > 0.8, f"Success rate too low: {consistency_summary['average_success_rate']:.2%}"
        assert consistency_summary["strong_consistency_rate"] > 0.6, f"Strong consistency rate too low: {consistency_summary['strong_consistency_rate']:.2%}"
        
        # Performance assertions
        total_time = creation_time + update_time + validation_time
        assert total_time < 10.0, f"Total test execution time too high: {total_time:.2f}s"
        
        return {
            "cross_service_lifecycle_successful": True,
            "entities_tested": len(test_entities),
            "consistency_metrics": {
                "fully_consistent": len(fully_consistent),
                "avg_availability": avg_availability,
                "strong_consistency_rate": consistency_summary["strong_consistency_rate"],
                "conflicts_resolved": consistency_summary["conflicts_detected"]
            },
            "performance_metrics": {
                "total_execution_time": total_time,
                "creation_time": creation_time,
                "update_time": update_time,
                "validation_time": validation_time
            }
        }
    
    @pytest.mark.asyncio
    async def test_high_frequency_concurrent_operations_consistency(
        self,
        async_test_client: AsyncClient,
        multi_service_state_manager
    ):
        """Test consistency under high-frequency concurrent operations across services."""
        
        # High-frequency operation scenario
        entity_id = f"high_freq_entity_{uuid.uuid4().hex[:8]}"
        base_analysis_data = {
            "repository_id": "high_traffic_repo",
            "patterns": [{"pattern_type": "performance", "severity": "medium", "confidence": 0.8}],
            "analysis_config": {"ml_enabled": True, "cache_enabled": True},
            "metadata": {"stress_test": True}
        }
        
        # Create initial entity
        creation_result = await multi_service_state_manager.create_analysis_entity(
            entity_id, base_analysis_data, "api_service"
        )
        
        assert creation_result["consistency_status"]["consistency_level"] in ["strong", "good"], \
            "Initial entity creation failed"
        
        print(f"High-frequency concurrent operations test for entity: {entity_id}")
        
        # Generate high-frequency concurrent operations
        operation_count = 50  # 50 concurrent operations
        services = ["api_service", "database_service", "cache_service", "ml_service", "analytics_service"]
        
        concurrent_operations = []
        for i in range(operation_count):
            initiating_service = random.choice(services)
            update_data = {
                "analysis_config": {"quality_threshold": random.uniform(0.6, 0.9)},
                "metadata": {
                    "update_sequence": i,
                    "timestamp": datetime.utcnow().isoformat(),
                    "operation_id": f"op_{i}"
                },
                "operation_type": "concurrent_update"
            }
            
            operation = multi_service_state_manager.update_analysis_entity(
                entity_id, update_data, initiating_service
            )
            concurrent_operations.append(operation)
        
        # Execute all operations concurrently
        print(f"Executing {operation_count} concurrent operations...")
        start_time = time.time()
        
        operation_results = await asyncio.gather(*concurrent_operations, return_exceptions=True)
        execution_time = time.time() - start_time
        
        # Analyze concurrent operation results
        successful_operations = [r for r in operation_results if not isinstance(r, Exception)]
        failed_operations = [r for r in operation_results if isinstance(r, Exception)]
        
        conflicts_detected = sum(1 for r in successful_operations if r.get("conflicts_detected"))
        total_service_updates = sum(
            len(r.get("update_results", {})) for r in successful_operations
        )
        successful_service_updates = sum(
            sum(1 for update in r.get("update_results", {}).values() if update.get("success"))
            for r in successful_operations
        )
        
        # Validate final consistency
        final_consistency_report = await multi_service_state_manager.validate_cross_service_consistency(entity_id)
        
        # Calculate performance metrics
        operations_per_second = operation_count / execution_time
        service_update_success_rate = successful_service_updates / max(1, total_service_updates)
        
        print(f"High-frequency operations results:")
        print(f"  - Operations attempted: {operation_count}")
        print(f"  - Successful operations: {len(successful_operations)}")
        print(f"  - Failed operations: {len(failed_operations)}")
        print(f"  - Conflicts detected: {conflicts_detected}")
        print(f"  - Operations per second: {operations_per_second:.1f}")
        print(f"  - Service update success rate: {service_update_success_rate:.2%}")
        print(f"  - Execution time: {execution_time:.3f}s")
        print(f"  - Final consistency: {final_consistency_report['version_consistency'] and final_consistency_report['data_consistency']}")
        
        # Assertions for high-frequency scenario
        assert len(successful_operations) >= operation_count * 0.8, \
            f"Too many failed operations: {len(failed_operations)}/{operation_count}"
        assert conflicts_detected > 0, "Expected conflicts in high-frequency scenario"
        assert service_update_success_rate > 0.7, \
            f"Service update success rate too low: {service_update_success_rate:.2%}"
        assert operations_per_second > 10.0, \
            f"Operations per second too low: {operations_per_second:.1f}"
        
        # Final consistency should be maintained or recoverable
        final_entity_present = len(final_consistency_report["services_with_entity"]) >= 3
        assert final_entity_present, "Entity should be present in at least 3 services after high-frequency operations"
        
        return {
            "high_frequency_test_successful": True,
            "operations_executed": operation_count,
            "performance_metrics": {
                "operations_per_second": operations_per_second,
                "execution_time": execution_time,
                "service_update_success_rate": service_update_success_rate
            },
            "consistency_metrics": {
                "conflicts_detected": conflicts_detected,
                "final_consistency": final_consistency_report["version_consistency"] and final_consistency_report["data_consistency"],
                "entity_availability": len(final_consistency_report["services_with_entity"])
            }
        }


if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short", "-m", "data_consistency"])