"""
Cascading Failures Integration Tests - Phase 2 Enhancement

Advanced failure propagation testing that validates how failures cascade through
the Pattern Mining service architecture. These tests simulate realistic failure
scenarios and verify proper failure isolation, recovery, and system resilience.

Key Test Categories:
1. Service Chain Failure Propagation - How failures spread through service dependencies
2. Partial Service Availability - System behavior when some services are degraded
3. Recovery Timing Scenarios - Services coming back online in different orders
4. Data Synchronization Failures - Consistency issues during cascading failures
5. Circuit Breaker and Bulkhead Validation - Failure isolation mechanisms

Advanced Scenarios Beyond Phase 1:
- Multi-stage failure propagation with timing dependencies
- Complex recovery sequences with ordering constraints
- Data consistency preservation during cascading failures
- Performance degradation cascades vs. complete failures
- Cross-service transaction rollback scenarios
"""

import asyncio
import pytest
import time
import json
import uuid
import random
import threading
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple, Set
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from contextlib import asynccontextmanager
from dataclasses import dataclass, field
from enum import Enum

# FastAPI and HTTP testing
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

# Pattern mining imports
from pattern_mining.models.patterns import PatternType, SeverityLevel, DetectionType
from pattern_mining.models.api import PatternDetectionRequest
from pattern_mining.database.connection import DatabaseManager
from pattern_mining.cache.redis_client import RedisClient
from pattern_mining.ml.gemini_client import GeminiClient
from pattern_mining.health.health_checker import HealthChecker

# Test utilities
from tests.utils.generators import TestDataGenerator
from tests.utils.assertions import assert_service_health, assert_failure_isolation


class FailureType(Enum):
    """Types of failures that can cascade."""
    CONNECTION_LOST = "connection_lost"
    TIMEOUT = "timeout"
    RESOURCE_EXHAUSTED = "resource_exhausted"
    AUTHENTICATION_FAILED = "authentication_failed"
    RATE_LIMITED = "rate_limited"
    DATA_CORRUPTED = "data_corrupted"
    CIRCUIT_BREAKER_OPEN = "circuit_breaker_open"
    PARTIAL_DEGRADATION = "partial_degradation"


class ServiceHealth(Enum):
    """Service health states."""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    FAILING = "failing"
    UNREACHABLE = "unreachable"
    RECOVERING = "recovering"


@dataclass
class FailureEvent:
    """Track individual failure events."""
    service_name: str
    failure_type: FailureType
    occurred_at: datetime
    caused_by: Optional[str] = None  # Which service caused this failure
    recovery_time: Optional[datetime] = None
    impact_severity: float = 0.0  # 0.0 = no impact, 1.0 = complete failure
    propagation_path: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class CascadeAnalysis:
    """Analysis of failure cascade patterns."""
    cascade_id: str
    initial_failure: FailureEvent
    propagated_failures: List[FailureEvent]
    cascade_depth: int = 0
    cascade_width: int = 0  # How many services affected at each level
    total_recovery_time: float = 0.0
    isolation_effective: bool = False
    services_affected: Set[str] = field(default_factory=set)
    recovery_sequence: List[str] = field(default_factory=list)


@pytest.mark.integration
@pytest.mark.cascading_failures
class TestServiceChainFailurePropagation:
    """Test how failures propagate through service dependency chains."""
    
    @pytest.fixture
    async def cascade_simulator(self):
        """Create cascade simulation environment."""
        
        class CascadeSimulator:
            def __init__(self):
                # Define service dependency graph
                self.service_dependencies = {
                    "api": ["gemini", "database", "redis"],
                    "gemini": ["database"],  # For context storage
                    "database": ["redis"],   # For connection pooling
                    "redis": []              # No dependencies
                }
                
                self.service_states = {
                    service: ServiceHealth.HEALTHY
                    for service in ["api", "gemini", "database", "redis"]
                }
                
                self.failure_events: List[FailureEvent] = []
                self.cascade_analyses: List[CascadeAnalysis] = []
                self._simulation_lock = asyncio.Lock()
            
            async def inject_failure(
                self, 
                service: str, 
                failure_type: FailureType,
                severity: float = 1.0,
                metadata: Dict[str, Any] = None
            ) -> FailureEvent:
                """Inject a failure into a service and track propagation."""
                async with self._simulation_lock:
                    # Create failure event
                    failure_event = FailureEvent(
                        service_name=service,
                        failure_type=failure_type,
                        occurred_at=datetime.utcnow(),
                        impact_severity=severity,
                        metadata=metadata or {}
                    )
                    
                    # Update service state based on severity
                    if severity >= 0.9:
                        self.service_states[service] = ServiceHealth.UNREACHABLE
                    elif severity >= 0.7:
                        self.service_states[service] = ServiceHealth.FAILING
                    elif severity >= 0.3:
                        self.service_states[service] = ServiceHealth.DEGRADED
                    
                    self.failure_events.append(failure_event)
                    
                    # Trigger cascade analysis
                    await self._analyze_failure_propagation(failure_event)
                    
                    return failure_event
            
            async def _analyze_failure_propagation(self, initial_failure: FailureEvent):
                """Analyze how a failure propagates through the system."""
                cascade = CascadeAnalysis(
                    cascade_id=f"cascade_{uuid.uuid4().hex[:8]}",
                    initial_failure=initial_failure
                )
                
                # Find services that depend on the failed service
                affected_services = []
                for service, dependencies in self.service_dependencies.items():
                    if initial_failure.service_name in dependencies:
                        affected_services.append(service)
                
                # Simulate cascade timing and severity
                propagation_delay = 0.1  # Base propagation delay
                
                for affected_service in affected_services:
                    # Calculate cascade severity (decreases with distance)
                    cascade_severity = initial_failure.impact_severity * 0.7
                    
                    if cascade_severity > 0.2:  # Only cascade if significant impact
                        # Simulate propagation delay
                        await asyncio.sleep(propagation_delay)
                        
                        # Create cascaded failure
                        cascaded_failure = FailureEvent(
                            service_name=affected_service,
                            failure_type=self._derive_cascade_failure_type(initial_failure.failure_type),
                            occurred_at=datetime.utcnow(),
                            caused_by=initial_failure.service_name,
                            impact_severity=cascade_severity,
                            propagation_path=[initial_failure.service_name, affected_service]
                        )
                        
                        # Update service state
                        if cascade_severity >= 0.7:
                            self.service_states[affected_service] = ServiceHealth.FAILING
                        else:
                            self.service_states[affected_service] = ServiceHealth.DEGRADED
                        
                        cascade.propagated_failures.append(cascaded_failure)
                        cascade.services_affected.add(affected_service)
                        self.failure_events.append(cascaded_failure)
                        
                        # Recursively propagate (limited depth to prevent infinite loops)
                        if len(cascaded_failure.propagation_path) < 4:
                            await self._analyze_failure_propagation(cascaded_failure)
                
                # Finalize cascade analysis
                cascade.cascade_depth = max(len(f.propagation_path) for f in cascade.propagated_failures) if cascade.propagated_failures else 0
                cascade.cascade_width = len(cascade.services_affected)
                cascade.services_affected.add(initial_failure.service_name)
                
                self.cascade_analyses.append(cascade)
            
            def _derive_cascade_failure_type(self, original_failure: FailureType) -> FailureType:
                """Derive the type of cascaded failure based on original failure."""
                cascade_mapping = {
                    FailureType.CONNECTION_LOST: FailureType.TIMEOUT,
                    FailureType.TIMEOUT: FailureType.CIRCUIT_BREAKER_OPEN,
                    FailureType.RESOURCE_EXHAUSTED: FailureType.PARTIAL_DEGRADATION,
                    FailureType.RATE_LIMITED: FailureType.PARTIAL_DEGRADATION,
                    FailureType.DATA_CORRUPTED: FailureType.PARTIAL_DEGRADATION
                }
                return cascade_mapping.get(original_failure, FailureType.PARTIAL_DEGRADATION)
            
            async def simulate_recovery(self, service: str, recovery_time: float = 0.5) -> bool:
                """Simulate service recovery."""
                if self.service_states[service] == ServiceHealth.HEALTHY:
                    return True
                
                # Simulate recovery time
                await asyncio.sleep(recovery_time)
                
                # Mark service as recovering
                self.service_states[service] = ServiceHealth.RECOVERING
                await asyncio.sleep(0.1)  # Brief recovery validation time
                
                # Full recovery
                self.service_states[service] = ServiceHealth.HEALTHY
                
                # Update failure events with recovery time
                for event in self.failure_events:
                    if event.service_name == service and not event.recovery_time:
                        event.recovery_time = datetime.utcnow()
                
                return True
            
            def get_cascade_summary(self) -> Dict[str, Any]:
                """Get comprehensive cascade analysis summary."""
                total_cascades = len(self.cascade_analyses)
                
                if total_cascades == 0:
                    return {"cascades_detected": 0}
                
                # Aggregate cascade metrics
                avg_depth = sum(c.cascade_depth for c in self.cascade_analyses) / total_cascades
                avg_width = sum(c.cascade_width for c in self.cascade_analyses) / total_cascades
                
                all_affected_services = set()
                for cascade in self.cascade_analyses:
                    all_affected_services.update(cascade.services_affected)
                
                return {
                    "cascades_detected": total_cascades,
                    "average_cascade_depth": avg_depth,
                    "average_cascade_width": avg_width,
                    "total_services_affected": len(all_affected_services),
                    "total_failure_events": len(self.failure_events),
                    "service_states": dict(self.service_states),
                    "cascade_patterns": [
                        {
                            "initial_service": c.initial_failure.service_name,
                            "affected_services": list(c.services_affected),
                            "depth": c.cascade_depth,
                            "width": c.cascade_width
                        }
                        for c in self.cascade_analyses
                    ]
                }
        
        return CascadeSimulator()
    
    @pytest.mark.asyncio
    async def test_redis_failure_cascade_propagation(self, cascade_simulator):
        """Test how Redis failure cascades through dependent services."""
        print("Testing Redis failure cascade propagation...")
        
        # Redis is a foundational service - its failure should cascade widely
        initial_failure = await cascade_simulator.inject_failure(
            "redis", 
            FailureType.CONNECTION_LOST, 
            severity=1.0,
            metadata={"cause": "network_partition", "expected_cascade": "high"}
        )
        
        # Allow time for cascade propagation
        await asyncio.sleep(0.5)
        
        # Analyze cascade results
        cascade_summary = cascade_simulator.get_cascade_summary()
        
        # Validate Redis failure cascaded properly
        assert cascade_summary["cascades_detected"] > 0
        assert "redis" in [s for cascade in cascade_summary["cascade_patterns"] for s in cascade["affected_services"]]
        
        # Redis failure should affect database (connection pooling) and API (caching)
        affected_services = set()
        for cascade in cascade_summary["cascade_patterns"]:
            affected_services.update(cascade["affected_services"])
        
        assert "database" in affected_services, "Database should be affected by Redis failure"
        assert cascade_summary["average_cascade_depth"] >= 1, "Should have multi-level cascade"
        
        # Validate service states reflect the cascade
        service_states = cascade_summary["service_states"]
        assert service_states["redis"] in [ServiceHealth.UNREACHABLE, ServiceHealth.FAILING]
        assert service_states["database"] in [ServiceHealth.DEGRADED, ServiceHealth.FAILING]
        
        print(f"Redis cascade results:")
        print(f"  - Cascades detected: {cascade_summary['cascades_detected']}")
        print(f"  - Services affected: {cascade_summary['total_services_affected']}")
        print(f"  - Average depth: {cascade_summary['average_cascade_depth']:.1f}")
        print(f"  - Service states: {service_states}")
        
        return cascade_summary
    
    @pytest.mark.asyncio
    async def test_gemini_api_failure_cascade_with_recovery(self, cascade_simulator):
        """Test Gemini API failure cascade and recovery propagation."""
        print("Testing Gemini API failure cascade with recovery...")
        
        # Inject Gemini API failure
        initial_failure = await cascade_simulator.inject_failure(
            "gemini",
            FailureType.RATE_LIMITED,
            severity=0.8,
            metadata={"quota_exceeded": True, "retry_after": "60s"}
        )
        
        # Allow cascade propagation
        await asyncio.sleep(0.3)
        
        # Analyze initial cascade
        initial_summary = cascade_simulator.get_cascade_summary()
        initial_affected = initial_summary["total_services_affected"]
        
        # Simulate recovery process
        print("Simulating Gemini recovery...")
        recovery_start = time.time()
        recovery_success = await cascade_simulator.simulate_recovery("gemini", recovery_time=0.3)
        recovery_time = time.time() - recovery_start
        
        assert recovery_success, "Gemini recovery should succeed"
        
        # Allow recovery propagation
        await asyncio.sleep(0.2)
        
        # Analyze post-recovery state
        final_summary = cascade_simulator.get_cascade_summary()
        
        # Validate recovery effectiveness
        service_states = final_summary["service_states"]
        assert service_states["gemini"] == ServiceHealth.HEALTHY
        
        # Services that were only affected by Gemini should recover
        # Services with independent issues may remain degraded
        healthy_services = [service for service, state in service_states.items() if state == ServiceHealth.HEALTHY]
        assert len(healthy_services) >= 2, "At least 2 services should be healthy after recovery"
        
        print(f"Gemini failure and recovery results:")
        print(f"  - Initial services affected: {initial_affected}")
        print(f"  - Recovery time: {recovery_time:.3f}s")
        print(f"  - Final healthy services: {len(healthy_services)}")
        print(f"  - Final service states: {service_states}")
        
        return {
            "initial_affected": initial_affected,
            "recovery_time": recovery_time,
            "recovery_successful": recovery_success,
            "final_healthy_count": len(healthy_services)
        }
    
    @pytest.mark.asyncio
    async def test_database_failure_with_partial_recovery(self, cascade_simulator):
        """Test database failure with partial recovery scenarios."""
        print("Testing database failure with partial recovery...")
        
        # Inject database failure
        await cascade_simulator.inject_failure(
            "database",
            FailureType.RESOURCE_EXHAUSTED,
            severity=0.9,
            metadata={"connection_pool_exhausted": True, "max_connections": 20}
        )
        
        # Allow cascade
        await asyncio.sleep(0.4)
        
        cascade_summary = cascade_simulator.get_cascade_summary()
        
        # Database failure should affect API service
        affected_services = set()
        for cascade in cascade_summary["cascade_patterns"]:
            affected_services.update(cascade["affected_services"])
        
        assert "api" in affected_services, "API should be affected by database failure"
        
        # Simulate partial recovery (database comes back but with reduced capacity)
        print("Simulating partial database recovery...")
        
        # Manually set database to degraded state (partial recovery)
        cascade_simulator.service_states["database"] = ServiceHealth.DEGRADED
        
        # Allow stabilization
        await asyncio.sleep(0.2)
        
        final_summary = cascade_simulator.get_cascade_summary()
        final_states = final_summary["service_states"]
        
        # Validate partial recovery state
        assert final_states["database"] == ServiceHealth.DEGRADED
        
        # Dependent services may still be affected
        degraded_or_better = [
            service for service, state in final_states.items() 
            if state in [ServiceHealth.HEALTHY, ServiceHealth.DEGRADED, ServiceHealth.RECOVERING]
        ]
        
        assert len(degraded_or_better) >= 2, "Most services should be degraded or better after partial recovery"
        
        print(f"Database partial recovery results:")
        print(f"  - Services in degraded+ state: {len(degraded_or_better)}")
        print(f"  - Final service states: {final_states}")
        
        return {
            "partial_recovery_achieved": final_states["database"] == ServiceHealth.DEGRADED,
            "services_degraded_or_better": len(degraded_or_better),
            "cascade_depth": cascade_summary["average_cascade_depth"]
        }


@pytest.mark.integration
@pytest.mark.cascading_failures
class TestComplexFailureTiming:
    """Test complex failure timing scenarios and recovery sequences."""
    
    @pytest.fixture
    async def timing_controller(self):
        """Create timing-aware failure controller."""
        
        class TimingController:
            def __init__(self):
                self.failure_timeline: List[Tuple[float, str, FailureType, float]] = []
                self.recovery_timeline: List[Tuple[float, str, bool]] = []
                self.service_states = {
                    "api": ServiceHealth.HEALTHY,
                    "gemini": ServiceHealth.HEALTHY,
                    "database": ServiceHealth.HEALTHY,
                    "redis": ServiceHealth.HEALTHY
                }
                self.timeline_start = None
            
            def schedule_failure(self, delay: float, service: str, failure_type: FailureType, severity: float):
                """Schedule a failure to occur after specified delay."""
                self.failure_timeline.append((delay, service, failure_type, severity))
            
            def schedule_recovery(self, delay: float, service: str):
                """Schedule a recovery to occur after specified delay."""
                self.recovery_timeline.append((delay, service, True))
            
            async def execute_timeline(self) -> Dict[str, Any]:
                """Execute the scheduled failure and recovery timeline."""
                self.timeline_start = time.time()
                
                # Combine and sort all timeline events
                events = []
                for delay, service, failure_type, severity in self.failure_timeline:
                    events.append((delay, "failure", service, failure_type, severity))
                
                for delay, service, success in self.recovery_timeline:
                    events.append((delay, "recovery", service, success, None))
                
                events.sort(key=lambda x: x[0])  # Sort by delay
                
                # Execute timeline
                execution_log = []
                last_delay = 0
                
                for event in events:
                    delay, event_type, service = event[0], event[1], event[2]
                    
                    # Wait for the scheduled time
                    wait_time = delay - last_delay
                    if wait_time > 0:
                        await asyncio.sleep(wait_time)
                    
                    current_time = time.time() - self.timeline_start
                    
                    if event_type == "failure":
                        failure_type, severity = event[3], event[4]
                        
                        # Apply failure
                        if severity >= 0.9:
                            self.service_states[service] = ServiceHealth.UNREACHABLE
                        elif severity >= 0.7:
                            self.service_states[service] = ServiceHealth.FAILING
                        else:
                            self.service_states[service] = ServiceHealth.DEGRADED
                        
                        execution_log.append({
                            "time": current_time,
                            "type": "failure",
                            "service": service,
                            "failure_type": failure_type.value,
                            "severity": severity,
                            "new_state": self.service_states[service].value
                        })
                    
                    elif event_type == "recovery":
                        # Apply recovery
                        self.service_states[service] = ServiceHealth.RECOVERING
                        await asyncio.sleep(0.1)  # Recovery time
                        self.service_states[service] = ServiceHealth.HEALTHY
                        
                        execution_log.append({
                            "time": current_time,
                            "type": "recovery",
                            "service": service,
                            "new_state": self.service_states[service].value
                        })
                    
                    last_delay = delay
                
                total_time = time.time() - self.timeline_start
                
                return {
                    "total_execution_time": total_time,
                    "events_executed": len(execution_log),
                    "execution_log": execution_log,
                    "final_service_states": {k: v.value for k, v in self.service_states.items()}
                }
        
        return TimingController()
    
    @pytest.mark.asyncio
    async def test_staggered_failure_recovery_sequence(self, timing_controller):
        """Test staggered failure and recovery sequences with timing dependencies."""
        print("Testing staggered failure and recovery sequence...")
        
        # Create complex timeline:
        # T+0.0s: Redis fails
        # T+0.2s: Database fails (due to Redis connection pooling issues)
        # T+0.5s: API starts failing (due to database issues)
        # T+0.8s: Redis recovers
        # T+1.2s: Database recovers (after Redis is stable)
        # T+1.8s: API recovers (after database is stable)
        
        timing_controller.schedule_failure(0.0, "redis", FailureType.CONNECTION_LOST, 1.0)
        timing_controller.schedule_failure(0.2, "database", FailureType.PARTIAL_DEGRADATION, 0.8)
        timing_controller.schedule_failure(0.5, "api", FailureType.TIMEOUT, 0.6)
        
        timing_controller.schedule_recovery(0.8, "redis")
        timing_controller.schedule_recovery(1.2, "database")
        timing_controller.schedule_recovery(1.8, "api")
        
        # Execute timeline
        start_time = time.time()
        timeline_result = await timing_controller.execute_timeline()
        execution_time = time.time() - start_time
        
        # Validate timing sequence
        execution_log = timeline_result["execution_log"]
        assert len(execution_log) == 6  # 3 failures + 3 recoveries
        
        # Validate failure sequence timing
        failure_events = [e for e in execution_log if e["type"] == "failure"]
        recovery_events = [e for e in execution_log if e["type"] == "recovery"]
        
        assert len(failure_events) == 3
        assert len(recovery_events) == 3
        
        # Validate recovery happens in correct order
        redis_recovery = next(e for e in recovery_events if e["service"] == "redis")
        database_recovery = next(e for e in recovery_events if e["service"] == "database")
        api_recovery = next(e for e in recovery_events if e["service"] == "api")
        
        # Redis should recover first, then database, then API
        assert redis_recovery["time"] < database_recovery["time"]
        assert database_recovery["time"] < api_recovery["time"]
        
        # Final state should be all healthy
        final_states = timeline_result["final_service_states"]
        assert all(state == "healthy" for state in final_states.values())
        
        print(f"Staggered sequence results:")
        print(f"  - Total execution time: {execution_time:.3f}s")
        print(f"  - Events executed: {timeline_result['events_executed']}")
        print(f"  - Final states: {final_states}")
        print(f"  - Recovery order validation: {'✓' if redis_recovery['time'] < database_recovery['time'] < api_recovery['time'] else '✗'}")
        
        return {
            "sequence_completed": True,
            "execution_time": execution_time,
            "recovery_order_correct": redis_recovery["time"] < database_recovery["time"] < api_recovery["time"],
            "all_services_recovered": all(state == "healthy" for state in final_states.values())
        }
    
    @pytest.mark.asyncio
    async def test_concurrent_failures_with_competing_recoveries(self, timing_controller):
        """Test concurrent failures with competing recovery attempts."""
        print("Testing concurrent failures with competing recoveries...")
        
        # Create competing failure/recovery scenario:
        # T+0.0s: Multiple services fail simultaneously
        # T+0.3s: Recovery attempts start
        # T+0.6s: Some recoveries succeed, others fail again
        
        # Simultaneous failures
        timing_controller.schedule_failure(0.0, "redis", FailureType.RESOURCE_EXHAUSTED, 0.9)
        timing_controller.schedule_failure(0.05, "database", FailureType.CONNECTION_LOST, 0.8)
        timing_controller.schedule_failure(0.1, "gemini", FailureType.RATE_LIMITED, 0.7)
        
        # Competing recovery attempts
        timing_controller.schedule_recovery(0.3, "redis")
        timing_controller.schedule_recovery(0.35, "database")
        timing_controller.schedule_recovery(0.4, "gemini")
        
        # Some services fail again during recovery
        timing_controller.schedule_failure(0.6, "database", FailureType.PARTIAL_DEGRADATION, 0.5)
        timing_controller.schedule_recovery(0.9, "database")  # Final recovery
        
        # Execute timeline
        timeline_result = await timing_controller.execute_timeline()
        
        # Analyze competing recovery patterns
        execution_log = timeline_result["execution_log"]
        
        # Count failures and recoveries per service
        service_events = {}
        for event in execution_log:
            service = event["service"]
            if service not in service_events:
                service_events[service] = {"failures": 0, "recoveries": 0}
            service_events[service][event["type"] + "s"] += 1
        
        # Database should have experienced multiple failure/recovery cycles
        assert service_events["database"]["failures"] >= 2
        assert service_events["database"]["recoveries"] >= 2
        
        # Validate final recovery
        final_states = timeline_result["final_service_states"]
        recovered_services = [service for service, state in final_states.items() if state == "healthy"]
        
        assert len(recovered_services) >= 3, "Most services should eventually recover"
        
        print(f"Concurrent failures with competing recoveries results:")
        print(f"  - Service events: {service_events}")
        print(f"  - Final recovered services: {len(recovered_services)}")
        print(f"  - Database failure/recovery cycles: {service_events['database']['failures']}/{service_events['database']['recoveries']}")
        
        return {
            "competing_recoveries_handled": True,
            "database_multiple_cycles": service_events["database"]["failures"] >= 2,
            "final_recovery_rate": len(recovered_services) / len(final_states),
            "total_events": len(execution_log)
        }
    
    @pytest.mark.asyncio
    async def test_recovery_dependency_validation(self, timing_controller):
        """Test that services don't recover if their dependencies are still failing."""
        print("Testing recovery dependency validation...")
        
        # Create scenario where dependent service tries to recover before its dependency:
        # T+0.0s: Redis fails (foundational service)
        # T+0.2s: Database fails (depends on Redis)
        # T+0.5s: Database tries to recover (but Redis still down - should fail/degrade)
        # T+0.8s: Redis recovers
        # T+1.0s: Database recovers successfully (now Redis is available)
        
        timing_controller.schedule_failure(0.0, "redis", FailureType.CONNECTION_LOST, 1.0)
        timing_controller.schedule_failure(0.2, "database", FailureType.PARTIAL_DEGRADATION, 0.8)
        
        # Attempt database recovery before Redis is back
        timing_controller.schedule_recovery(0.5, "database")
        
        # Redis recovers later
        timing_controller.schedule_recovery(0.8, "redis")
        
        # Database can now recover successfully
        timing_controller.schedule_recovery(1.0, "database")
        
        # Execute timeline
        timeline_result = await timing_controller.execute_timeline()
        execution_log = timeline_result["execution_log"]
        
        # Analyze dependency validation
        database_events = [e for e in execution_log if e["service"] == "database"]
        redis_events = [e for e in execution_log if e["service"] == "redis"]
        
        # Find the first database recovery attempt
        first_db_recovery = next(e for e in database_events if e["type"] == "recovery")
        redis_recovery = next(e for e in redis_events if e["type"] == "recovery")
        
        # Database recovery should happen before Redis recovery (but may not be fully successful)
        assert first_db_recovery["time"] < redis_recovery["time"]
        
        # Final state should have both services healthy
        final_states = timeline_result["final_service_states"]
        assert final_states["redis"] == "healthy"
        assert final_states["database"] == "healthy"
        
        print(f"Recovery dependency validation results:")
        print(f"  - Database recovery attempts: {len([e for e in database_events if e['type'] == 'recovery'])}")
        print(f"  - Redis recovery time: {redis_recovery['time']:.3f}s")
        print(f"  - First DB recovery time: {first_db_recovery['time']:.3f}s")
        print(f"  - Final states: {final_states}")
        
        return {
            "dependency_validation_tested": True,
            "premature_recovery_attempted": first_db_recovery["time"] < redis_recovery["time"],
            "final_recovery_successful": all(state == "healthy" for state in final_states.values()),
            "timeline_executed": len(execution_log) > 0
        }


@pytest.mark.integration
@pytest.mark.cascading_failures
class TestDataConsistencyDuringCascades:
    """Test data consistency preservation during cascading failures."""
    
    @pytest.fixture
    async def consistency_tracker(self):
        """Create data consistency tracking system."""
        
        class ConsistencyTracker:
            def __init__(self):
                self.data_states = {
                    "cache": {},           # Redis cache state
                    "database": {},        # Database state
                    "memory": {},          # In-memory state
                    "api_responses": {}    # API response cache
                }
                self.consistency_violations = []
                self.sync_operations = []
                self._lock = asyncio.Lock()
            
            async def write_data(self, location: str, key: str, value: Any, service: str = "unknown"):
                """Write data and track consistency."""
                async with self._lock:
                    if location not in self.data_states:
                        self.data_states[location] = {}
                    
                    old_value = self.data_states[location].get(key)
                    self.data_states[location][key] = {
                        "value": value,
                        "timestamp": datetime.utcnow(),
                        "service": service,
                        "version": self._get_next_version(location, key)
                    }
                    
                    # Track sync operation
                    self.sync_operations.append({
                        "location": location,
                        "key": key,
                        "operation": "write",
                        "service": service,
                        "timestamp": datetime.utcnow(),
                        "old_value": old_value,
                        "new_value": value
                    })
            
            async def read_data(self, location: str, key: str, service: str = "unknown") -> Any:
                """Read data and track access."""
                async with self._lock:
                    if location not in self.data_states or key not in self.data_states[location]:
                        return None
                    
                    data_entry = self.data_states[location][key]
                    
                    # Track read operation
                    self.sync_operations.append({
                        "location": location,
                        "key": key,
                        "operation": "read",
                        "service": service,
                        "timestamp": datetime.utcnow(),
                        "value": data_entry["value"],
                        "version": data_entry["version"]
                    })
                    
                    return data_entry["value"]
            
            async def simulate_service_failure(self, service: str, affected_locations: List[str]):
                """Simulate service failure affecting data consistency."""
                async with self._lock:
                    for location in affected_locations:
                        if location in self.data_states:
                            # Mark all data in this location as potentially inconsistent
                            for key, data_entry in self.data_states[location].items():
                                if data_entry["service"] == service:
                                    # Simulate data becoming stale or inaccessible
                                    self.consistency_violations.append({
                                        "type": "service_failure",
                                        "location": location,
                                        "key": key,
                                        "service": service,
                                        "timestamp": datetime.utcnow(),
                                        "impact": "data_potentially_stale"
                                    })
            
            async def check_cross_location_consistency(self, key: str) -> Dict[str, Any]:
                """Check if the same key has consistent values across locations."""
                consistency_check = {
                    "key": key,
                    "consistent": True,
                    "values_by_location": {},
                    "versions_by_location": {},
                    "inconsistencies": []
                }
                
                # Collect values from all locations
                for location, data in self.data_states.items():
                    if key in data:
                        entry = data[key]
                        consistency_check["values_by_location"][location] = entry["value"]
                        consistency_check["versions_by_location"][location] = entry["version"]
                
                # Check for inconsistencies
                values = list(consistency_check["values_by_location"].values())
                if len(set(str(v) for v in values)) > 1:  # Convert to string for comparison
                    consistency_check["consistent"] = False
                    
                    # Identify specific inconsistencies
                    for location, value in consistency_check["values_by_location"].items():
                        for other_location, other_value in consistency_check["values_by_location"].items():
                            if location != other_location and value != other_value:
                                consistency_check["inconsistencies"].append({
                                    "location_1": location,
                                    "value_1": value,
                                    "location_2": other_location,
                                    "value_2": other_value,
                                    "detected_at": datetime.utcnow()
                                })
                
                return consistency_check
            
            def _get_next_version(self, location: str, key: str) -> int:
                """Get next version number for data entry."""
                if location in self.data_states and key in self.data_states[location]:
                    return self.data_states[location][key]["version"] + 1
                return 1
            
            def get_consistency_report(self) -> Dict[str, Any]:
                """Generate comprehensive consistency report."""
                return {
                    "total_locations": len(self.data_states),
                    "total_keys": sum(len(data) for data in self.data_states.values()),
                    "sync_operations": len(self.sync_operations),
                    "consistency_violations": len(self.consistency_violations),
                    "violation_types": list(set(v["type"] for v in self.consistency_violations)),
                    "operations_by_service": self._group_operations_by_service(),
                    "data_distribution": {
                        location: len(data) for location, data in self.data_states.items()
                    }
                }
            
            def _group_operations_by_service(self) -> Dict[str, int]:
                """Group sync operations by service."""
                service_ops = {}
                for op in self.sync_operations:
                    service = op["service"]
                    service_ops[service] = service_ops.get(service, 0) + 1
                return service_ops
        
        return ConsistencyTracker()
    
    @pytest.mark.asyncio
    async def test_cache_database_consistency_during_redis_failure(self, consistency_tracker):
        """Test cache-database consistency when Redis fails."""
        print("Testing cache-database consistency during Redis failure...")
        
        # Initial data setup - synchronized across cache and database
        test_data = [
            ("pattern_123", {"type": "design_pattern", "confidence": 0.95}),
            ("analysis_456", {"status": "completed", "patterns_found": 15}),
            ("session_789", {"user_id": "user123", "last_activity": "2024-01-15T10:30:00Z"})
        ]
        
        # Write initial data to both locations
        for key, value in test_data:
            await consistency_tracker.write_data("cache", key, value, "api_service")
            await consistency_tracker.write_data("database", key, value, "api_service")
        
        # Verify initial consistency
        for key, _ in test_data:
            consistency_check = await consistency_tracker.check_cross_location_consistency(key)
            assert consistency_check["consistent"], f"Initial data should be consistent for {key}"
        
        # Simulate Redis (cache) failure
        await consistency_tracker.simulate_service_failure("redis", ["cache"])
        
        # API continues operating, writing to database but cache is unavailable
        new_data_updates = [
            ("pattern_123", {"type": "design_pattern", "confidence": 0.97, "updated": True}),
            ("analysis_456", {"status": "completed", "patterns_found": 18, "refined": True}),
            ("new_pattern_999", {"type": "security_issue", "confidence": 0.88, "new": True})
        ]
        
        # Write updates only to database (cache unavailable)
        for key, value in new_data_updates:
            await consistency_tracker.write_data("database", key, value, "api_service")
        
        # Check consistency after cache failure
        consistency_issues = 0
        for key, _ in new_data_updates:
            consistency_check = await consistency_tracker.check_cross_location_consistency(key)
            if not consistency_check["consistent"]:
                consistency_issues += 1
        
        # Should have consistency issues
        assert consistency_issues > 0, "Should detect consistency issues after cache failure"
        
        # Simulate cache recovery and resynchronization
        print("Simulating cache recovery and resync...")
        
        # Resync data from database to cache
        for key, _ in new_data_updates:
            db_value = await consistency_tracker.read_data("database", key, "recovery_service")
            if db_value:
                await consistency_tracker.write_data("cache", key, db_value, "recovery_service")
        
        # Verify consistency restored
        consistency_restored = 0
        for key, _ in new_data_updates:
            consistency_check = await consistency_tracker.check_cross_location_consistency(key)
            if consistency_check["consistent"]:
                consistency_restored += 1
        
        # Generate consistency report
        report = consistency_tracker.get_consistency_report()
        
        print(f"Cache-database consistency test results:")
        print(f"  - Initial data items: {len(test_data)}")
        print(f"  - Updates during failure: {len(new_data_updates)}")
        print(f"  - Consistency issues detected: {consistency_issues}")
        print(f"  - Consistency restored: {consistency_restored}")
        print(f"  - Total sync operations: {report['sync_operations']}")
        print(f"  - Consistency violations: {report['consistency_violations']}")
        
        return {
            "consistency_issues_detected": consistency_issues > 0,
            "consistency_restored": consistency_restored == len(new_data_updates),
            "sync_operations": report["sync_operations"],
            "violation_count": report["consistency_violations"]
        }
    
    @pytest.mark.asyncio
    async def test_transaction_rollback_during_cascade_failure(self, consistency_tracker):
        """Test transaction rollback behavior during cascading failures."""
        print("Testing transaction rollback during cascade failure...")
        
        # Simulate complex transaction involving multiple data locations
        transaction_id = f"txn_{uuid.uuid4().hex[:8]}"
        
        # Step 1: Begin transaction - write to memory (uncommitted)
        transaction_data = {
            "transaction_id": transaction_id,
            "operations": [
                {"type": "create_pattern", "pattern_id": "txn_pattern_1"},
                {"type": "update_analysis", "analysis_id": "txn_analysis_1"},
                {"type": "cache_result", "cache_key": "txn_cache_1"}
            ],
            "status": "in_progress"
        }
        
        await consistency_tracker.write_data("memory", transaction_id, transaction_data, "transaction_manager")
        
        # Step 2: Write individual operations (uncommitted)
        for i, operation in enumerate(transaction_data["operations"]):
            await consistency_tracker.write_data(
                "memory", 
                f"{transaction_id}_op_{i}", 
                operation, 
                "transaction_manager"
            )
        
        # Step 3: Begin committing to persistent storage
        await consistency_tracker.write_data("database", "txn_pattern_1", {"committed": False}, "database_service")
        
        # Step 4: Simulate cascade failure during commit
        await consistency_tracker.simulate_service_failure("database", ["database"])
        
        # Step 5: Transaction manager detects failure and initiates rollback
        rollback_operations = []
        
        # Read uncommitted operations from memory
        for i in range(len(transaction_data["operations"])):
            op = await consistency_tracker.read_data("memory", f"{transaction_id}_op_{i}", "transaction_manager")
            if op:
                rollback_operations.append(op)
        
        # Perform rollback - remove uncommitted data
        for i in range(len(rollback_operations)):
            # Mark as rolled back
            await consistency_tracker.write_data(
                "memory", 
                f"{transaction_id}_op_{i}_rollback", 
                {"rolled_back": True, "reason": "cascade_failure"}, 
                "transaction_manager"
            )
        
        # Update transaction status
        transaction_data["status"] = "rolled_back"
        transaction_data["rollback_reason"] = "database_cascade_failure"
        await consistency_tracker.write_data("memory", transaction_id, transaction_data, "transaction_manager")
        
        # Verify rollback consistency
        rollback_verification = []
        for i in range(len(rollback_operations)):
            rollback_marker = await consistency_tracker.read_data(
                "memory", 
                f"{transaction_id}_op_{i}_rollback", 
                "verification_service"
            )
            rollback_verification.append(rollback_marker is not None)
        
        # Check that no partial data remains in database
        db_pattern = await consistency_tracker.read_data("database", "txn_pattern_1", "verification_service")
        
        # Generate rollback report
        report = consistency_tracker.get_consistency_report()
        
        rollback_successful = all(rollback_verification)
        no_partial_commits = db_pattern is None or not db_pattern.get("committed", False)
        
        print(f"Transaction rollback test results:")
        print(f"  - Transaction operations: {len(transaction_data['operations'])}")
        print(f"  - Rollback markers created: {sum(rollback_verification)}")
        print(f"  - Rollback successful: {rollback_successful}")
        print(f"  - No partial commits: {no_partial_commits}")
        print(f"  - Total operations tracked: {report['sync_operations']}")
        
        return {
            "rollback_successful": rollback_successful,
            "no_partial_commits": no_partial_commits,
            "operations_rolled_back": sum(rollback_verification),
            "consistency_maintained": rollback_successful and no_partial_commits
        }


if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short", "-m", "cascading_failures"])