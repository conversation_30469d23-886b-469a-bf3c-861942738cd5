"""
Advanced Database Migration Integration Tests

Comprehensive testing of schema migrations, data migrations, zero-downtime 
procedures, rollback testing, and migration performance analysis for 
the Pattern Mining service database layer.

Phase 2 Enhancement: Advanced Database Integration Testing
"""

import pytest
import asyncio
import os
import time
import shutil
import tempfile
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
import json
import hashlib
from unittest.mock import patch, MagicMock

import asyncpg
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy import text, select, func, MetaData, Table, Column, String, Integer, DateTime, inspect
from sqlalchemy.exc import IntegrityError, OperationalError, DBAPIError
from sqlalchemy.schema import CreateTable, DropTable
from alembic.config import Config
from alembic import command
from alembic.script import ScriptDirectory
from alembic.runtime.migration import MigrationContext
from alembic.operations import Operations

from pattern_mining.database.connection import DatabaseManager, get_database_session
from pattern_mining.database.models import <PERSON><PERSON>R<PERSON>ult, RepositoryAnal<PERSON>is, <PERSON><PERSON><PERSON><PERSON><PERSON>, FeatureVector, Base
from pattern_mining.database.migrations.migration_manager import MigrationManager
from pattern_mining.config.database import get_database_config
from tests.utils.database_fixtures import (
    generate_migration_test_data,
    create_schema_validation_data,
    simulate_production_data_load
)


@pytest.mark.integration
@pytest.mark.asyncio
class TestSchemaMigrations:
    """Test schema migration scenarios and validations."""
    
    @pytest.fixture
    def database_url(self):
        """Get test database URL from environment or use default."""
        return os.getenv(
            "TEST_DATABASE_URL",
            "postgresql://postgres:INSECURE_PASSWORD@localhost:5432/pattern_mining_test"
        )
    
    @pytest.fixture
    async def migration_manager(self, database_url):
        """Create migration manager for testing."""
        config = get_database_config()
        config.database_url = database_url
        
        db_manager = DatabaseManager(config)
        await db_manager.initialize()
        
        migration_manager = MigrationManager(db_manager)
        yield migration_manager
        
        await db_manager.close()
    
    @pytest.fixture
    async def clean_migration_state(self, migration_manager):
        """Ensure clean migration state before tests."""
        # Drop all test tables and migration state
        async with migration_manager.db_manager.get_session() as session:
            await session.execute(text("DROP TABLE IF EXISTS alembic_version CASCADE"))
            await session.execute(text("DROP TABLE IF EXISTS migration_test_table CASCADE"))
            await session.execute(text("DROP TABLE IF EXISTS pattern_results_v2 CASCADE"))
            await session.execute(text("DROP TABLE IF EXISTS temp_migration_backup CASCADE"))
            await session.commit()
        yield
        # Cleanup after test
        async with migration_manager.db_manager.get_session() as session:
            await session.execute(text("DROP TABLE IF EXISTS alembic_version CASCADE"))
            await session.execute(text("DROP TABLE IF EXISTS migration_test_table CASCADE"))
            await session.execute(text("DROP TABLE IF EXISTS pattern_results_v2 CASCADE"))
            await session.execute(text("DROP TABLE IF EXISTS temp_migration_backup CASCADE"))
            await session.commit()
    
    async def test_schema_version_tracking(self, migration_manager: MigrationManager, clean_migration_state):
        """Test database schema version tracking."""
        # Check initial schema version
        initial_version = await migration_manager.get_current_schema_version()
        assert initial_version is None  # No migrations applied yet
        
        # Apply initial schema
        await migration_manager.initialize_schema()
        
        # Check version after initialization
        post_init_version = await migration_manager.get_current_schema_version()
        assert post_init_version is not None
        
        # Verify schema version format
        assert isinstance(post_init_version, str)
        assert len(post_init_version) >= 10  # Should be revision hash
        
        print(f"Schema Version Tracking:")
        print(f"Initial Version: {initial_version}")
        print(f"Post-Init Version: {post_init_version}")
    
    async def test_forward_schema_migration(self, migration_manager: MigrationManager, clean_migration_state):
        """Test forward schema migration with new columns."""
        # Initialize base schema
        await migration_manager.initialize_schema()
        initial_version = await migration_manager.get_current_schema_version()
        
        # Create test migration: Add new column to pattern_results
        migration_sql = """
        -- Forward migration: Add new columns
        ALTER TABLE pattern_results 
        ADD COLUMN IF NOT EXISTS test_confidence_v2 FLOAT DEFAULT 0.0,
        ADD COLUMN IF NOT EXISTS test_metadata_v2 JSONB DEFAULT '{}';
        
        -- Create index on new column
        CREATE INDEX IF NOT EXISTS idx_pattern_confidence_v2 
        ON pattern_results(test_confidence_v2);
        
        -- Update schema version
        INSERT INTO migration_test_table (migration_id, applied_at) 
        VALUES ('add_v2_columns', NOW())
        ON CONFLICT (migration_id) DO NOTHING;
        """
        
        # Apply forward migration
        migration_result = await migration_manager.apply_migration(
            migration_id="test_forward_v2",
            migration_sql=migration_sql,
            description="Add v2 columns to pattern_results"
        )
        
        assert migration_result.success is True
        assert migration_result.error is None
        
        # Verify new columns exist
        async with migration_manager.db_manager.get_session() as session:
            # Check column existence
            result = await session.execute(text("""
                SELECT column_name, data_type, is_nullable
                FROM information_schema.columns
                WHERE table_name = 'pattern_results'
                AND column_name IN ('test_confidence_v2', 'test_metadata_v2')
                ORDER BY column_name
            """))
            columns = result.fetchall()
            
            assert len(columns) == 2
            assert columns[0].column_name == 'test_confidence_v2'
            assert columns[0].data_type == 'double precision'
            assert columns[1].column_name == 'test_metadata_v2'
            assert 'json' in columns[1].data_type.lower()
            
            # Verify index was created
            result = await session.execute(text("""
                SELECT indexname FROM pg_indexes
                WHERE tablename = 'pattern_results'
                AND indexname = 'idx_pattern_confidence_v2'
            """))
            index = result.fetchone()
            assert index is not None
        
        print(f"Forward Migration Test:")
        print(f"Migration Success: {migration_result.success}")
        print(f"New Columns Added: {len(columns)}")
    
    async def test_schema_migration_rollback(self, migration_manager: MigrationManager, clean_migration_state):
        """Test schema migration rollback functionality."""
        # Initialize schema and apply a migration
        await migration_manager.initialize_schema()
        
        # Apply forward migration
        forward_migration = """
        ALTER TABLE pattern_results 
        ADD COLUMN test_rollback_column TEXT DEFAULT 'test_value';
        
        CREATE TABLE migration_test_table (
            id SERIAL PRIMARY KEY,
            migration_id VARCHAR(255) UNIQUE NOT NULL,
            applied_at TIMESTAMP DEFAULT NOW()
        );
        """
        
        migration_result = await migration_manager.apply_migration(
            migration_id="test_rollback_forward",
            migration_sql=forward_migration,
            description="Test rollback forward migration"
        )
        assert migration_result.success is True
        
        # Verify forward migration was applied
        async with migration_manager.db_manager.get_session() as session:
            result = await session.execute(text("""
                SELECT column_name FROM information_schema.columns
                WHERE table_name = 'pattern_results'
                AND column_name = 'test_rollback_column'
            """))
            column = result.fetchone()
            assert column is not None
        
        # Create rollback migration
        rollback_migration = """
        -- Rollback: Remove the added column and table
        ALTER TABLE pattern_results DROP COLUMN IF EXISTS test_rollback_column;
        DROP TABLE IF EXISTS migration_test_table;
        """
        
        # Apply rollback
        rollback_result = await migration_manager.rollback_migration(
            migration_id="test_rollback_forward",
            rollback_sql=rollback_migration,
            description="Rollback test migration"
        )
        assert rollback_result.success is True
        
        # Verify rollback was successful
        async with migration_manager.db_manager.get_session() as session:
            # Column should be gone
            result = await session.execute(text("""
                SELECT column_name FROM information_schema.columns
                WHERE table_name = 'pattern_results'
                AND column_name = 'test_rollback_column'
            """))
            column = result.fetchone()
            assert column is None
            
            # Table should be gone
            result = await session.execute(text("""
                SELECT table_name FROM information_schema.tables
                WHERE table_name = 'migration_test_table'
            """))
            table = result.fetchone()
            assert table is None
        
        print(f"Schema Rollback Test:")
        print(f"Forward Migration Success: {migration_result.success}")
        print(f"Rollback Success: {rollback_result.success}")
    
    async def test_concurrent_schema_migrations(self, migration_manager: MigrationManager, clean_migration_state):
        """Test concurrent schema migration handling."""
        await migration_manager.initialize_schema()
        
        migration_results = []
        
        async def apply_concurrent_migration(migration_id: str, column_name: str):
            """Apply a migration concurrently."""
            try:
                migration_sql = f"""
                -- Create migration tracking table if not exists
                CREATE TABLE IF NOT EXISTS migration_test_table (
                    id SERIAL PRIMARY KEY,
                    migration_id VARCHAR(255) UNIQUE NOT NULL,
                    applied_at TIMESTAMP DEFAULT NOW()
                );
                
                -- Add column if not exists
                DO $$
                BEGIN
                    IF NOT EXISTS (
                        SELECT 1 FROM information_schema.columns
                        WHERE table_name = 'pattern_results'
                        AND column_name = '{column_name}'
                    ) THEN
                        ALTER TABLE pattern_results ADD COLUMN {column_name} TEXT DEFAULT 'concurrent_test';
                    END IF;
                END $$;
                
                -- Record migration
                INSERT INTO migration_test_table (migration_id) VALUES ('{migration_id}')
                ON CONFLICT (migration_id) DO NOTHING;
                """
                
                result = await migration_manager.apply_migration(
                    migration_id=migration_id,
                    migration_sql=migration_sql,
                    description=f"Concurrent migration {migration_id}"
                )
                
                return {
                    'migration_id': migration_id,
                    'column_name': column_name,
                    'success': result.success,
                    'error': result.error
                }
                
            except Exception as e:
                return {
                    'migration_id': migration_id,
                    'column_name': column_name,
                    'success': False,
                    'error': str(e)
                }
        
        # Run concurrent migrations
        concurrent_tasks = [
            apply_concurrent_migration(f"concurrent_migration_{i}", f"test_concurrent_col_{i}")
            for i in range(5)
        ]
        
        results = await asyncio.gather(*concurrent_tasks, return_exceptions=True)
        
        # Analyze results
        successful_migrations = [r for r in results if not isinstance(r, Exception) and r['success']]
        failed_migrations = [r for r in results if isinstance(r, Exception) or not r['success']]
        
        print(f"Concurrent Migration Test:")
        print(f"Successful: {len(successful_migrations)}")
        print(f"Failed: {len(failed_migrations)}")
        
        # At least some migrations should succeed
        assert len(successful_migrations) >= 3
        
        # Verify schema state is consistent
        async with migration_manager.db_manager.get_session() as session:
            result = await session.execute(text("""
                SELECT column_name FROM information_schema.columns
                WHERE table_name = 'pattern_results'
                AND column_name LIKE 'test_concurrent_col_%'
                ORDER BY column_name
            """))
            columns = result.fetchall()
            
            print(f"Concurrent Columns Created: {len(columns)}")
            assert len(columns) >= 3  # Should have created multiple columns
    
    async def test_schema_validation_after_migration(self, migration_manager: MigrationManager, clean_migration_state):
        """Test schema validation after migrations."""
        await migration_manager.initialize_schema()
        
        # Apply migration that changes constraints
        migration_sql = """
        -- Add new table with constraints
        CREATE TABLE pattern_validation_test (
            id SERIAL PRIMARY KEY,
            pattern_id VARCHAR(255) NOT NULL,
            validation_score FLOAT CHECK (validation_score >= 0.0 AND validation_score <= 1.0),
            validated_at TIMESTAMP DEFAULT NOW(),
            CONSTRAINT unique_pattern_validation UNIQUE (pattern_id)
        );
        
        -- Add foreign key relationship
        -- ALTER TABLE pattern_validation_test 
        -- ADD CONSTRAINT fk_pattern_validation 
        -- FOREIGN KEY (pattern_id) REFERENCES pattern_results(pattern_id);
        """
        
        migration_result = await migration_manager.apply_migration(
            migration_id="test_schema_validation",
            migration_sql=migration_sql,
            description="Add validation table with constraints"
        )
        assert migration_result.success is True
        
        # Validate schema integrity
        validation_result = await migration_manager.validate_schema_integrity()
        
        print(f"Schema Validation Test:")
        print(f"Migration Success: {migration_result.success}")
        print(f"Schema Valid: {validation_result.valid}")
        print(f"Validation Errors: {validation_result.errors}")
        
        # Schema should be valid after successful migration
        assert validation_result.valid is True
        assert len(validation_result.errors) == 0
        
        # Test constraint enforcement
        async with migration_manager.db_manager.get_session() as session:
            # Valid insert should work
            await session.execute(text("""
                INSERT INTO pattern_validation_test (pattern_id, validation_score)
                VALUES ('test-pattern-1', 0.85)
            """))
            await session.commit()
            
            # Invalid constraint should fail
            with pytest.raises(Exception):
                await session.execute(text("""
                    INSERT INTO pattern_validation_test (pattern_id, validation_score)
                    VALUES ('test-pattern-2', 1.5)  -- Invalid: > 1.0
                """))
                await session.commit()


@pytest.mark.integration
@pytest.mark.asyncio
class TestDataMigrations:
    """Test data migration scenarios and transformations."""
    
    @pytest.fixture
    def database_url(self):
        return os.getenv(
            "TEST_DATABASE_URL",
            "postgresql://postgres:INSECURE_PASSWORD@localhost:5432/pattern_mining_test"
        )
    
    @pytest.fixture
    async def data_migration_manager(self, database_url):
        """Create migration manager with sample data for testing."""
        config = get_database_config()
        config.database_url = database_url
        
        db_manager = DatabaseManager(config)
        await db_manager.initialize()
        
        migration_manager = MigrationManager(db_manager)
        
        # Setup sample data for migration testing
        await self._setup_sample_data(db_manager)
        
        yield migration_manager
        await db_manager.close()
    
    async def _setup_sample_data(self, db_manager: DatabaseManager):
        """Setup sample data for migration testing."""
        async with db_manager.get_session() as session:
            # Clean existing data
            await session.execute(text("DELETE FROM pattern_results WHERE repository_id LIKE 'test-data-migration%'"))
            await session.execute(text("DELETE FROM repository_analysis WHERE repository_id LIKE 'test-data-migration%'"))
            
            # Insert sample pattern results
            for i in range(50):
                pattern = PatternResult(
                    detection_id=f"data-migration-pattern-{i}",
                    repository_id=f"test-data-migration-{i//10}",
                    file_path=f"src/test/file_{i}.py",
                    pattern_id=f"pattern-{i}",
                    pattern_name=f"Test Pattern {i}",
                    pattern_type="DESIGN_PATTERN",
                    pattern_category="structural",
                    severity="LOW",
                    confidence=0.7 + (i % 30) * 0.01,
                    confidence_level="medium",
                    line_start=10 + i,
                    line_end=20 + i,
                    detection_method="ML_INFERENCE",
                    language="python"
                )
                session.add(pattern)
            
            # Insert sample analyses
            for i in range(5):
                analysis = RepositoryAnalysis(
                    repository_id=f"test-data-migration-{i}",
                    repository_url=f"https://github.com/test/data-migration-{i}",
                    repository_name=f"data-migration-{i}",
                    owner="test-user",
                    analysis_id=f"data-migration-analysis-{i}",
                    analysis_type="comprehensive",
                    analysis_status="completed",
                    total_files=100 + i * 10,
                    patterns_detected=10,
                    quality_score=70.0 + i * 5
                )
                session.add(analysis)
            
            await session.commit()
    
    async def test_data_transformation_migration(self, data_migration_manager: MigrationManager):
        """Test data transformation during migration."""
        # Migration: Normalize confidence levels based on confidence scores
        data_migration_sql = """
        -- Create temporary backup of original data
        CREATE TEMP TABLE pattern_results_backup AS 
        SELECT * FROM pattern_results WHERE repository_id LIKE 'test-data-migration%';
        
        -- Transform confidence levels based on confidence scores
        UPDATE pattern_results 
        SET confidence_level = CASE
            WHEN confidence >= 0.9 THEN 'very_high'
            WHEN confidence >= 0.8 THEN 'high'
            WHEN confidence >= 0.7 THEN 'medium'
            WHEN confidence >= 0.6 THEN 'low'
            ELSE 'very_low'
        END
        WHERE repository_id LIKE 'test-data-migration%';
        
        -- Add migration tracking
        CREATE TABLE IF NOT EXISTS data_migration_log (
            migration_id VARCHAR(255) PRIMARY KEY,
            records_affected INTEGER,
            applied_at TIMESTAMP DEFAULT NOW()
        );
        
        INSERT INTO data_migration_log (migration_id, records_affected)
        SELECT 'confidence_level_normalization', COUNT(*)
        FROM pattern_results WHERE repository_id LIKE 'test-data-migration%';
        """
        
        # Count records before migration
        async with data_migration_manager.db_manager.get_session() as session:
            result = await session.execute(text("""
                SELECT COUNT(*) as count FROM pattern_results 
                WHERE repository_id LIKE 'test-data-migration%'
            """))
            initial_count = result.scalar()
        
        # Apply data migration
        migration_result = await data_migration_manager.apply_migration(
            migration_id="data_transformation_test",
            migration_sql=data_migration_sql,
            description="Transform confidence levels"
        )
        
        assert migration_result.success is True
        
        # Verify data transformation
        async with data_migration_manager.db_manager.get_session() as session:
            # Check confidence level distribution after migration
            result = await session.execute(text("""
                SELECT confidence_level, COUNT(*) as count
                FROM pattern_results 
                WHERE repository_id LIKE 'test-data-migration%'
                GROUP BY confidence_level
                ORDER BY confidence_level
            """))
            confidence_distribution = result.fetchall()
            
            # Verify migration log
            result = await session.execute(text("""
                SELECT records_affected FROM data_migration_log
                WHERE migration_id = 'confidence_level_normalization'
            """))
            records_affected = result.scalar()
        
        print(f"Data Transformation Migration:")
        print(f"Initial Records: {initial_count}")
        print(f"Records Affected: {records_affected}")
        print(f"Confidence Distribution: {[(row.confidence_level, row.count) for row in confidence_distribution]}")
        
        # Verify transformation results
        assert records_affected == initial_count
        assert len(confidence_distribution) > 1  # Should have multiple confidence levels
        
        # Verify specific transformations
        confidence_levels = {row.confidence_level for row in confidence_distribution}
        expected_levels = {'medium', 'high', 'very_high'}  # Based on sample data confidence range
        assert confidence_levels.intersection(expected_levels), "Should have expected confidence levels"
    
    async def test_bulk_data_migration_performance(self, data_migration_manager: MigrationManager):
        """Test performance of bulk data migrations."""
        # Create large dataset for performance testing
        bulk_data_setup = """
        -- Clean up existing performance test data
        DELETE FROM pattern_results WHERE repository_id LIKE 'test-bulk-migration%';
        
        -- Generate large dataset for performance testing
        INSERT INTO pattern_results (
            detection_id, repository_id, file_path, pattern_id, pattern_name,
            pattern_type, pattern_category, severity, confidence, confidence_level,
            line_start, line_end, detection_method, language
        )
        SELECT 
            'bulk-' || generate_series || '-' || (random() * 1000)::int,
            'test-bulk-migration-' || ((generate_series - 1) / 1000),
            'src/bulk/file_' || generate_series || '.py',
            'bulk-pattern-' || generate_series,
            'Bulk Pattern ' || generate_series,
            'DESIGN_PATTERN',
            'structural',
            CASE WHEN random() > 0.7 THEN 'HIGH' ELSE 'MEDIUM' END,
            0.5 + random() * 0.5,
            'medium',
            generate_series,
            generate_series + 10,
            'ML_INFERENCE',
            'python'
        FROM generate_series(1, 5000);
        """
        
        # Setup bulk data
        setup_start = time.time()
        async with data_migration_manager.db_manager.get_session() as session:
            await session.execute(text(bulk_data_setup))
            await session.commit()
        setup_time = time.time() - setup_start
        
        # Bulk migration: Add calculated fields and update patterns
        bulk_migration_sql = """
        -- Add new columns for calculated fields
        ALTER TABLE pattern_results 
        ADD COLUMN IF NOT EXISTS quality_score FLOAT DEFAULT 0.0,
        ADD COLUMN IF NOT EXISTS risk_score FLOAT DEFAULT 0.0;
        
        -- Bulk update with calculated values
        UPDATE pattern_results 
        SET 
            quality_score = CASE 
                WHEN confidence >= 0.8 AND severity = 'LOW' THEN 0.9
                WHEN confidence >= 0.7 AND severity = 'MEDIUM' THEN 0.7
                WHEN confidence >= 0.6 AND severity = 'HIGH' THEN 0.5
                ELSE 0.3
            END,
            risk_score = CASE 
                WHEN severity = 'HIGH' THEN confidence * 0.9
                WHEN severity = 'MEDIUM' THEN confidence * 0.6
                ELSE confidence * 0.3
            END
        WHERE repository_id LIKE 'test-bulk-migration%';
        
        -- Create index on new columns
        CREATE INDEX IF NOT EXISTS idx_pattern_quality_risk 
        ON pattern_results(quality_score, risk_score)
        WHERE repository_id LIKE 'test-bulk-migration%';
        """
        
        # Apply bulk migration with performance tracking
        migration_start = time.time()
        migration_result = await data_migration_manager.apply_migration(
            migration_id="bulk_performance_test",
            migration_sql=bulk_migration_sql,
            description="Bulk data migration performance test"
        )
        migration_time = time.time() - migration_start
        
        assert migration_result.success is True
        
        # Verify migration results and performance
        async with data_migration_manager.db_manager.get_session() as session:
            # Count updated records
            result = await session.execute(text("""
                SELECT COUNT(*) as count FROM pattern_results
                WHERE repository_id LIKE 'test-bulk-migration%'
                AND quality_score > 0.0
            """))
            updated_count = result.scalar()
            
            # Check performance statistics
            result = await session.execute(text("""
                SELECT 
                    AVG(quality_score) as avg_quality,
                    AVG(risk_score) as avg_risk,
                    COUNT(*) as total_records
                FROM pattern_results
                WHERE repository_id LIKE 'test-bulk-migration%'
            """))
            stats = result.fetchone()
        
        # Calculate performance metrics
        records_per_second = updated_count / migration_time if migration_time > 0 else 0
        
        print(f"Bulk Data Migration Performance:")
        print(f"Setup Time: {setup_time:.2f}s")
        print(f"Migration Time: {migration_time:.2f}s")
        print(f"Records Updated: {updated_count}")
        print(f"Records/Second: {records_per_second:.0f}")
        print(f"Avg Quality Score: {stats.avg_quality:.3f}")
        print(f"Avg Risk Score: {stats.avg_risk:.3f}")
        
        # Performance assertions
        assert updated_count == 5000  # Should update all test records
        assert records_per_second > 100  # Should process > 100 records/second
        assert migration_time < 60  # Should complete within 60 seconds
        
        # Cleanup performance test data
        async with data_migration_manager.db_manager.get_session() as session:
            await session.execute(text("DELETE FROM pattern_results WHERE repository_id LIKE 'test-bulk-migration%'"))
            await session.execute(text("ALTER TABLE pattern_results DROP COLUMN IF EXISTS quality_score"))
            await session.execute(text("ALTER TABLE pattern_results DROP COLUMN IF EXISTS risk_score"))
            await session.commit()
    
    async def test_data_migration_rollback_with_backup(self, data_migration_manager: MigrationManager):
        """Test data migration rollback using backup tables."""
        # Create migration with backup and rollback capability
        migration_with_backup = """
        -- Create backup table
        CREATE TABLE pattern_results_migration_backup AS 
        SELECT * FROM pattern_results WHERE repository_id LIKE 'test-data-migration%';
        
        -- Transform data (this change will be rolled back)
        UPDATE pattern_results 
        SET 
            pattern_name = 'MIGRATED: ' || pattern_name,
            confidence = LEAST(confidence * 1.2, 1.0)  -- Boost confidence but cap at 1.0
        WHERE repository_id LIKE 'test-data-migration%';
        
        -- Log migration
        CREATE TABLE IF NOT EXISTS migration_backup_log (
            migration_id VARCHAR(255) PRIMARY KEY,
            backup_table VARCHAR(255),
            records_migrated INTEGER,
            applied_at TIMESTAMP DEFAULT NOW()
        );
        
        INSERT INTO migration_backup_log (migration_id, backup_table, records_migrated)
        SELECT 'data_with_backup', 'pattern_results_migration_backup', COUNT(*)
        FROM pattern_results WHERE repository_id LIKE 'test-data-migration%';
        """
        
        # Capture original data state
        async with data_migration_manager.db_manager.get_session() as session:
            result = await session.execute(text("""
                SELECT pattern_name, confidence FROM pattern_results
                WHERE repository_id LIKE 'test-data-migration%'
                ORDER BY detection_id LIMIT 5
            """))
            original_data = result.fetchall()
        
        # Apply migration with backup
        migration_result = await data_migration_manager.apply_migration(
            migration_id="data_migration_with_backup",
            migration_sql=migration_with_backup,
            description="Data migration with backup"
        )
        assert migration_result.success is True
        
        # Verify migration was applied
        async with data_migration_manager.db_manager.get_session() as session:
            result = await session.execute(text("""
                SELECT pattern_name, confidence FROM pattern_results
                WHERE repository_id LIKE 'test-data-migration%'
                ORDER BY detection_id LIMIT 5
            """))
            migrated_data = result.fetchall()
        
        # Verify data was changed
        assert all(row.pattern_name.startswith('MIGRATED:') for row in migrated_data)
        assert any(row.confidence != orig.confidence for row, orig in zip(migrated_data, original_data))
        
        # Rollback using backup
        rollback_sql = """
        -- Restore from backup
        DELETE FROM pattern_results WHERE repository_id LIKE 'test-data-migration%';
        
        INSERT INTO pattern_results 
        SELECT * FROM pattern_results_migration_backup;
        
        -- Clean up backup
        DROP TABLE pattern_results_migration_backup;
        
        -- Log rollback
        INSERT INTO migration_backup_log (migration_id, backup_table, records_migrated)
        VALUES ('data_with_backup_rollback', 'restored_from_backup', 
                (SELECT COUNT(*) FROM pattern_results WHERE repository_id LIKE 'test-data-migration%'));
        """
        
        rollback_result = await data_migration_manager.rollback_migration(
            migration_id="data_migration_with_backup",
            rollback_sql=rollback_sql,
            description="Rollback with backup restore"
        )
        assert rollback_result.success is True
        
        # Verify rollback restored original data
        async with data_migration_manager.db_manager.get_session() as session:
            result = await session.execute(text("""
                SELECT pattern_name, confidence FROM pattern_results
                WHERE repository_id LIKE 'test-data-migration%'
                ORDER BY detection_id LIMIT 5
            """))
            restored_data = result.fetchall()
        
        print(f"Data Migration Rollback Test:")
        print(f"Original Data: {[(r.pattern_name[:20], r.confidence) for r in original_data[:3]]}")
        print(f"Migrated Data: {[(r.pattern_name[:20], r.confidence) for r in migrated_data[:3]]}")
        print(f"Restored Data: {[(r.pattern_name[:20], r.confidence) for r in restored_data[:3]]}")
        
        # Verify data was restored to original state
        assert all(not row.pattern_name.startswith('MIGRATED:') for row in restored_data)
        
        # Data should match original (allowing for small floating point differences)
        for restored, original in zip(restored_data, original_data):
            assert restored.pattern_name == original.pattern_name
            assert abs(restored.confidence - original.confidence) < 0.001


@pytest.mark.integration
@pytest.mark.asyncio
class TestZeroDowntimeMigrations:
    """Test zero-downtime migration procedures."""
    
    @pytest.fixture
    def database_url(self):
        return os.getenv(
            "TEST_DATABASE_URL",
            "postgresql://postgres:INSECURE_PASSWORD@localhost:5432/pattern_mining_test"
        )
    
    @pytest.fixture
    async def zero_downtime_manager(self, database_url):
        """Create migration manager for zero-downtime testing."""
        config = get_database_config()
        config.database_url = database_url
        
        db_manager = DatabaseManager(config)
        await db_manager.initialize()
        
        migration_manager = MigrationManager(db_manager)
        yield migration_manager
        await db_manager.close()
    
    async def test_online_column_addition(self, zero_downtime_manager: MigrationManager):
        """Test adding columns without blocking reads/writes."""
        # Simulate continuous application load during migration
        migration_completed = asyncio.Event()
        read_write_results = []
        
        async def simulate_application_load():
            """Simulate continuous application read/write operations."""
            operation_count = 0
            
            while not migration_completed.is_set():
                try:
                    async with zero_downtime_manager.db_manager.get_session() as session:
                        # Simulate read operation
                        result = await session.execute(text("""
                            SELECT COUNT(*) FROM pattern_results 
                            WHERE repository_id LIKE 'test-zero-downtime%'
                        """))
                        read_count = result.scalar()
                        
                        # Simulate write operation
                        await session.execute(text("""
                            INSERT INTO pattern_results (
                                detection_id, repository_id, file_path, pattern_id, pattern_name,
                                pattern_type, pattern_category, severity, confidence, confidence_level,
                                line_start, line_end, detection_method, language
                            ) VALUES (
                                :detection_id, :repository_id, :file_path, :pattern_id, :pattern_name,
                                'DESIGN_PATTERN', 'structural', 'LOW', 0.8, 'high',
                                10, 20, 'ML_INFERENCE', 'python'
                            )
                        """), {
                            'detection_id': f'zero-downtime-{operation_count}',
                            'repository_id': 'test-zero-downtime-load',
                            'file_path': f'src/load/file_{operation_count}.py',
                            'pattern_id': f'load-pattern-{operation_count}',
                            'pattern_name': f'Load Pattern {operation_count}'
                        })
                        await session.commit()
                        
                        operation_count += 1
                        read_write_results.append({
                            'operation': operation_count,
                            'read_count': read_count,
                            'success': True,
                            'timestamp': time.time()
                        })
                        
                except Exception as e:
                    read_write_results.append({
                        'operation': operation_count,
                        'success': False,
                        'error': str(e),
                        'timestamp': time.time()
                    })
                
                # Small delay between operations
                await asyncio.sleep(0.1)
        
        # Start application load simulation
        load_task = asyncio.create_task(simulate_application_load())
        
        # Wait a moment for load to start
        await asyncio.sleep(0.5)
        
        # Perform online column addition (should not block operations)
        online_migration_sql = """
        -- Add new columns with DEFAULT values (should be fast and non-blocking)
        ALTER TABLE pattern_results 
        ADD COLUMN IF NOT EXISTS online_added_column TEXT DEFAULT 'online_default',
        ADD COLUMN IF NOT EXISTS online_timestamp TIMESTAMP DEFAULT NOW();
        
        -- Create index CONCURRENTLY (PostgreSQL feature for non-blocking index creation)
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_pattern_online_timestamp 
        ON pattern_results(online_timestamp);
        """
        
        migration_start = time.time()
        migration_result = await zero_downtime_manager.apply_migration(
            migration_id="online_column_addition",
            migration_sql=online_migration_sql,
            description="Online column addition test"
        )
        migration_time = time.time() - migration_start
        
        # Signal migration completion
        migration_completed.set()
        
        # Wait for load simulation to finish
        await load_task
        
        # Analyze results
        successful_operations = [r for r in read_write_results if r['success']]
        failed_operations = [r for r in read_write_results if not r['success']]
        
        print(f"Zero-Downtime Migration Test:")
        print(f"Migration Success: {migration_result.success}")
        print(f"Migration Time: {migration_time:.2f}s")
        print(f"Successful Operations: {len(successful_operations)}")
        print(f"Failed Operations: {len(failed_operations)}")
        print(f"Success Rate: {len(successful_operations) / len(read_write_results) * 100:.1f}%")
        
        # Zero-downtime assertions
        assert migration_result.success is True
        assert len(successful_operations) > 0  # Should have completed some operations
        
        # Should have high success rate (>90%) during migration
        success_rate = len(successful_operations) / len(read_write_results)
        assert success_rate > 0.9, f"Success rate too low: {success_rate:.2%}"
        
        # Verify new columns exist and are usable
        async with zero_downtime_manager.db_manager.get_session() as session:
            result = await session.execute(text("""
                SELECT online_added_column, online_timestamp 
                FROM pattern_results 
                WHERE repository_id = 'test-zero-downtime-load'
                LIMIT 1
            """))
            row = result.fetchone()
            assert row is not None
            assert row.online_added_column == 'online_default'
            assert row.online_timestamp is not None
        
        # Cleanup
        async with zero_downtime_manager.db_manager.get_session() as session:
            await session.execute(text("DELETE FROM pattern_results WHERE repository_id LIKE 'test-zero-downtime%'"))
            await session.execute(text("ALTER TABLE pattern_results DROP COLUMN IF EXISTS online_added_column"))
            await session.execute(text("ALTER TABLE pattern_results DROP COLUMN IF EXISTS online_timestamp"))
            await session.commit()
    
    async def test_blue_green_table_migration(self, zero_downtime_manager: MigrationManager):
        """Test blue-green deployment pattern for table migrations."""
        # Setup: Create original table with data
        setup_sql = """
        -- Create original table (blue)
        CREATE TABLE IF NOT EXISTS pattern_results_blue AS
        SELECT * FROM pattern_results WHERE 1=0;  -- Copy structure only
        
        -- Insert test data into blue table
        INSERT INTO pattern_results_blue (
            detection_id, repository_id, file_path, pattern_id, pattern_name,
            pattern_type, pattern_category, severity, confidence, confidence_level,
            line_start, line_end, detection_method, language
        )
        SELECT 
            'blue-' || generate_series,
            'test-blue-green',
            'src/blue/file_' || generate_series || '.py',
            'blue-pattern-' || generate_series,
            'Blue Pattern ' || generate_series,
            'DESIGN_PATTERN',
            'structural',
            'MEDIUM',
            0.8,
            'high',
            generate_series,
            generate_series + 10,
            'ML_INFERENCE',
            'python'
        FROM generate_series(1, 100);
        """
        
        async with zero_downtime_manager.db_manager.get_session() as session:
            await session.execute(text(setup_sql))
            await session.commit()
        
        # Phase 1: Create green table with new schema
        green_creation_sql = """
        -- Create green table (new schema)
        CREATE TABLE pattern_results_green (
            id SERIAL PRIMARY KEY,
            detection_id VARCHAR(255) UNIQUE NOT NULL,
            repository_id VARCHAR(255) NOT NULL,
            file_path TEXT NOT NULL,
            pattern_id VARCHAR(255) NOT NULL,
            pattern_name VARCHAR(255) NOT NULL,
            pattern_type VARCHAR(50) NOT NULL,
            pattern_category VARCHAR(50) NOT NULL,
            severity VARCHAR(20) NOT NULL,
            confidence FLOAT NOT NULL CHECK (confidence >= 0.0 AND confidence <= 1.0),
            confidence_level VARCHAR(20) NOT NULL,
            line_start INTEGER NOT NULL,
            line_end INTEGER NOT NULL,
            detection_method VARCHAR(50) NOT NULL,
            language VARCHAR(50) NOT NULL,
            -- New columns in green schema
            migration_version INTEGER DEFAULT 2,
            enhanced_metadata JSONB DEFAULT '{}',
            created_at TIMESTAMP DEFAULT NOW(),
            updated_at TIMESTAMP DEFAULT NOW()
        );
        
        -- Create indexes on green table
        CREATE INDEX idx_green_repository ON pattern_results_green(repository_id);
        CREATE INDEX idx_green_confidence ON pattern_results_green(confidence);
        """
        
        phase1_result = await zero_downtime_manager.apply_migration(
            migration_id="blue_green_phase1",
            migration_sql=green_creation_sql,
            description="Create green table"
        )
        assert phase1_result.success is True
        
        # Phase 2: Migrate data from blue to green with transformations
        data_migration_sql = """
        -- Migrate data from blue to green with enhancements
        INSERT INTO pattern_results_green (
            detection_id, repository_id, file_path, pattern_id, pattern_name,
            pattern_type, pattern_category, severity, confidence, confidence_level,
            line_start, line_end, detection_method, language,
            migration_version, enhanced_metadata
        )
        SELECT 
            detection_id, repository_id, file_path, pattern_id, pattern_name,
            pattern_type, pattern_category, severity, confidence, confidence_level,
            line_start, line_end, detection_method, language,
            2 as migration_version,
            json_build_object(
                'original_table', 'blue',
                'migrated_at', NOW(),
                'confidence_category', CASE 
                    WHEN confidence >= 0.9 THEN 'excellent'
                    WHEN confidence >= 0.8 THEN 'good'
                    ELSE 'acceptable'
                END
            ) as enhanced_metadata
        FROM pattern_results_blue;
        """
        
        phase2_result = await zero_downtime_manager.apply_migration(
            migration_id="blue_green_phase2",
            migration_sql=data_migration_sql,
            description="Migrate data to green table"
        )
        assert phase2_result.success is True
        
        # Phase 3: Verify data integrity and perform cutover
        async with zero_downtime_manager.db_manager.get_session() as session:
            # Verify data counts match
            result = await session.execute(text("SELECT COUNT(*) FROM pattern_results_blue"))
            blue_count = result.scalar()
            
            result = await session.execute(text("SELECT COUNT(*) FROM pattern_results_green"))
            green_count = result.scalar()
            
            assert blue_count == green_count, f"Data count mismatch: blue={blue_count}, green={green_count}"
            
            # Verify enhanced metadata was added
            result = await session.execute(text("""
                SELECT enhanced_metadata->'confidence_category' as confidence_category
                FROM pattern_results_green 
                WHERE repository_id = 'test-blue-green'
                LIMIT 5
            """))
            metadata_samples = result.fetchall()
            
            assert len(metadata_samples) > 0
            assert all(row.confidence_category is not None for row in metadata_samples)
        
        # Phase 4: Simulate atomic cutover (in production, this would be a view/synonym switch)
        cutover_sql = """
        -- In production, this would typically be:
        -- DROP VIEW IF EXISTS pattern_results_current;
        -- CREATE VIEW pattern_results_current AS SELECT * FROM pattern_results_green;
        
        -- For testing, we'll rename tables to simulate cutover
        ALTER TABLE pattern_results_blue RENAME TO pattern_results_blue_old;
        ALTER TABLE pattern_results_green RENAME TO pattern_results_current;
        
        -- Log successful cutover
        CREATE TABLE IF NOT EXISTS blue_green_cutover_log (
            cutover_id VARCHAR(255) PRIMARY KEY,
            old_table VARCHAR(255),
            new_table VARCHAR(255),
            records_migrated INTEGER,
            cutover_time TIMESTAMP DEFAULT NOW()
        );
        
        INSERT INTO blue_green_cutover_log (cutover_id, old_table, new_table, records_migrated)
        VALUES ('test_cutover_1', 'pattern_results_blue', 'pattern_results_green', 
                (SELECT COUNT(*) FROM pattern_results_current));
        """
        
        cutover_result = await zero_downtime_manager.apply_migration(
            migration_id="blue_green_cutover",
            migration_sql=cutover_sql,
            description="Perform blue-green cutover"
        )
        assert cutover_result.success is True
        
        # Verify cutover success
        async with zero_downtime_manager.db_manager.get_session() as session:
            # Verify new table is active
            result = await session.execute(text("""
                SELECT migration_version, COUNT(*) as count
                FROM pattern_results_current
                GROUP BY migration_version
            """))
            version_stats = result.fetchall()
            
            # Verify cutover log
            result = await session.execute(text("""
                SELECT records_migrated FROM blue_green_cutover_log
                WHERE cutover_id = 'test_cutover_1'
            """))
            migrated_count = result.scalar()
        
        print(f"Blue-Green Migration Test:")
        print(f"Phase 1 (Green Creation): {phase1_result.success}")
        print(f"Phase 2 (Data Migration): {phase2_result.success}")
        print(f"Phase 3 (Cutover): {cutover_result.success}")
        print(f"Version Stats: {[(row.migration_version, row.count) for row in version_stats]}")
        print(f"Total Migrated: {migrated_count}")
        
        # Verify migration success
        assert len(version_stats) == 1 and version_stats[0].migration_version == 2
        assert migrated_count == 100  # Should have migrated all test records
        
        # Cleanup
        async with zero_downtime_manager.db_manager.get_session() as session:
            await session.execute(text("DROP TABLE IF EXISTS pattern_results_blue_old CASCADE"))
            await session.execute(text("DROP TABLE IF EXISTS pattern_results_current CASCADE"))
            await session.execute(text("DROP TABLE IF EXISTS blue_green_cutover_log CASCADE"))
            await session.commit()


@pytest.mark.integration
@pytest.mark.asyncio
class TestMigrationPerformanceAnalysis:
    """Test migration performance analysis and optimization."""
    
    @pytest.fixture
    def database_url(self):
        return os.getenv(
            "TEST_DATABASE_URL",
            "postgresql://postgres:INSECURE_PASSWORD@localhost:5432/pattern_mining_test"
        )
    
    @pytest.fixture
    async def performance_migration_manager(self, database_url):
        """Create migration manager for performance testing."""
        config = get_database_config()
        config.database_url = database_url
        
        db_manager = DatabaseManager(config)
        await db_manager.initialize()
        
        migration_manager = MigrationManager(db_manager)
        yield migration_manager
        await db_manager.close()
    
    async def test_migration_performance_benchmarking(self, performance_migration_manager: MigrationManager):
        """Test and benchmark migration performance characteristics."""
        # Setup large dataset for performance testing
        dataset_sizes = [1000, 5000, 10000]
        performance_results = {}
        
        for size in dataset_sizes:
            print(f"\nTesting migration performance with {size} records...")
            
            # Create dataset
            setup_start = time.time()
            setup_sql = f"""
            -- Clean existing data
            DELETE FROM pattern_results WHERE repository_id LIKE 'test-perf-%';
            
            -- Generate dataset of specified size
            INSERT INTO pattern_results (
                detection_id, repository_id, file_path, pattern_id, pattern_name,
                pattern_type, pattern_category, severity, confidence, confidence_level,
                line_start, line_end, detection_method, language
            )
            SELECT 
                'perf-' || generate_series,
                'test-perf-' || ((generate_series - 1) / 100),
                'src/perf/file_' || generate_series || '.py',
                'perf-pattern-' || generate_series,
                'Performance Pattern ' || generate_series,
                CASE WHEN generate_series % 3 = 0 THEN 'DESIGN_PATTERN'
                     WHEN generate_series % 3 = 1 THEN 'SECURITY_ISSUE'
                     ELSE 'PERFORMANCE_ISSUE' END,
                'structural',
                CASE WHEN generate_series % 4 = 0 THEN 'HIGH'
                     WHEN generate_series % 4 = 1 THEN 'MEDIUM'
                     ELSE 'LOW' END,
                0.5 + (generate_series % 50) * 0.01,
                'medium',
                generate_series,
                generate_series + 10,
                'ML_INFERENCE',
                'python'
            FROM generate_series(1, {size});
            """
            
            async with performance_migration_manager.db_manager.get_session() as session:
                await session.execute(text(setup_sql))
                await session.commit()
            setup_time = time.time() - setup_start
            
            # Test different types of migrations
            migration_tests = [
                {
                    'name': 'add_column',
                    'sql': """
                        ALTER TABLE pattern_results 
                        ADD COLUMN IF NOT EXISTS perf_test_column TEXT DEFAULT 'test_value';
                    """
                },
                {
                    'name': 'update_all_records',
                    'sql': """
                        UPDATE pattern_results 
                        SET perf_test_column = 'updated_' || pattern_id
                        WHERE repository_id LIKE 'test-perf-%';
                    """
                },
                {
                    'name': 'create_index',
                    'sql': """
                        CREATE INDEX IF NOT EXISTS idx_perf_test_column 
                        ON pattern_results(perf_test_column)
                        WHERE repository_id LIKE 'test-perf-%';
                    """
                },
                {
                    'name': 'bulk_calculation',
                    'sql': """
                        UPDATE pattern_results 
                        SET confidence = CASE 
                            WHEN severity = 'HIGH' THEN LEAST(confidence * 1.2, 1.0)
                            WHEN severity = 'MEDIUM' THEN confidence * 1.1
                            ELSE confidence
                        END
                        WHERE repository_id LIKE 'test-perf-%';
                    """
                }
            ]
            
            size_results = {'setup_time': setup_time, 'dataset_size': size, 'migrations': {}}
            
            for test in migration_tests:
                migration_start = time.time()
                
                migration_result = await performance_migration_manager.apply_migration(
                    migration_id=f"perf_test_{test['name']}_{size}",
                    migration_sql=test['sql'],
                    description=f"Performance test: {test['name']} with {size} records"
                )
                
                migration_time = time.time() - migration_start
                
                # Calculate performance metrics
                records_per_second = size / migration_time if migration_time > 0 else 0
                
                size_results['migrations'][test['name']] = {
                    'success': migration_result.success,
                    'duration': migration_time,
                    'records_per_second': records_per_second
                }
                
                print(f"  {test['name']}: {migration_time:.3f}s ({records_per_second:.0f} records/sec)")
            
            performance_results[size] = size_results
            
            # Cleanup for next test
            async with performance_migration_manager.db_manager.get_session() as session:
                await session.execute(text("DELETE FROM pattern_results WHERE repository_id LIKE 'test-perf-%'"))
                await session.execute(text("ALTER TABLE pattern_results DROP COLUMN IF EXISTS perf_test_column"))
                await session.execute(text("DROP INDEX IF EXISTS idx_perf_test_column"))
                await session.commit()
        
        # Analyze performance scaling
        print(f"\nMigration Performance Analysis:")
        for migration_type in ['add_column', 'update_all_records', 'create_index', 'bulk_calculation']:
            print(f"\n{migration_type}:")
            for size in dataset_sizes:
                if migration_type in performance_results[size]['migrations']:
                    metrics = performance_results[size]['migrations'][migration_type]
                    print(f"  {size:5d} records: {metrics['duration']:6.3f}s ({metrics['records_per_second']:6.0f} rec/sec)")
        
        # Performance assertions
        for size in dataset_sizes:
            size_data = performance_results[size]
            
            # Add column should be fast (mostly independent of data size)
            add_column_perf = size_data['migrations']['add_column']
            assert add_column_perf['success'] is True
            assert add_column_perf['duration'] < 5.0, f"Add column too slow for {size} records"
            
            # Update performance should scale reasonably
            update_perf = size_data['migrations']['update_all_records']
            assert update_perf['success'] is True
            assert update_perf['records_per_second'] > 100, f"Update too slow: {update_perf['records_per_second']} rec/sec"
        
        # Scaling analysis
        if len(dataset_sizes) >= 2:
            small_size = dataset_sizes[0]
            large_size = dataset_sizes[-1]
            
            # Update operations should scale sub-linearly (due to optimizations)
            small_update_time = performance_results[small_size]['migrations']['update_all_records']['duration']
            large_update_time = performance_results[large_size]['migrations']['update_all_records']['duration']
            
            size_ratio = large_size / small_size
            time_ratio = large_update_time / small_update_time
            
            print(f"\nScaling Analysis:")
            print(f"Size Ratio: {size_ratio:.1f}x")
            print(f"Time Ratio: {time_ratio:.1f}x")
            print(f"Scaling Efficiency: {(size_ratio / time_ratio) * 100:.1f}%")
            
            # Time should scale better than linearly (efficiency > 50%)
            scaling_efficiency = (size_ratio / time_ratio) * 100
            assert scaling_efficiency > 50, f"Poor scaling efficiency: {scaling_efficiency:.1f}%"