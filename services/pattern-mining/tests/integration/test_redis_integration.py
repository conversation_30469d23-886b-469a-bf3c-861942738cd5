"""
Redis Integration Tests

Real integration tests for Redis cache operations, testing actual Redis connections,
pattern caching functionality, session management, and connection recovery.
"""

import pytest
import asyncio
import os
import json
import time
import uuid
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta

import redis.asyncio as redis
from redis.exceptions import ConnectionError, TimeoutError, RedisError

from pattern_mining.cache.redis_client import RedisClient, RedisConfig
from pattern_mining.cache.pattern_cache import Pattern<PERSON>ache
from pattern_mining.cache.strategies import CacheStrategy, LRUCacheStrategy, TTLCacheStrategy
from pattern_mining.models.patterns import PatternType, SeverityLevel, DetectionType


@pytest.mark.integration
@pytest.mark.asyncio
class TestRedisConnectionIntegration:
    """Integration tests for Redis connection management."""
    
    @pytest.fixture
    def redis_url(self):
        """Get test Redis URL from environment or use default."""
        return os.getenv(
            "TEST_REDIS_URL",
            "redis://localhost:6379/1"  # Use DB 1 for testing
        )
    
    @pytest.fixture
    async def redis_client(self, redis_url):
        """Create Redis client for testing."""
        config = RedisConfig(
            url=redis_url,
            max_connections=10,
            retry_on_timeout=True,
            socket_timeout=5.0,
            socket_connect_timeout=5.0
        )
        
        client = RedisClient(config)
        await client.initialize()
        
        # Clear test database
        await client.flushdb()
        
        yield client
        
        # Cleanup
        await client.flushdb()
        await client.close()
    
    async def test_redis_basic_connection(self, redis_client: RedisClient):
        """Test basic Redis connection and operations."""
        # Test ping
        pong = await redis_client.ping()
        assert pong is True
        
        # Test basic set/get
        await redis_client.set("test_key", "test_value")
        value = await redis_client.get("test_key")
        assert value == "test_value"
        
        # Test key deletion
        deleted = await redis_client.delete("test_key")
        assert deleted == 1
        
        # Verify key is gone
        value = await redis_client.get("test_key")
        assert value is None
    
    async def test_redis_json_operations(self, redis_client: RedisClient):
        """Test Redis JSON serialization/deserialization."""
        test_data = {
            "pattern_id": "test-pattern",
            "pattern_type": "DESIGN_PATTERN",
            "confidence": 0.95,
            "metadata": {
                "model_version": "1.0.0",
                "analyzed_at": datetime.utcnow().isoformat()
            }
        }
        
        # Set JSON data
        await redis_client.set_json("json_test", test_data)
        
        # Get JSON data
        retrieved_data = await redis_client.get_json("json_test")
        assert retrieved_data["pattern_id"] == test_data["pattern_id"]
        assert retrieved_data["confidence"] == test_data["confidence"]
        assert retrieved_data["metadata"]["model_version"] == test_data["metadata"]["model_version"]
    
    async def test_redis_expiration_handling(self, redis_client: RedisClient):
        """Test Redis key expiration functionality."""
        # Set key with expiration
        await redis_client.setex("expiring_key", 2, "temporary_value")  # 2 second TTL
        
        # Verify key exists
        value = await redis_client.get("expiring_key")
        assert value == "temporary_value"
        
        # Check TTL
        ttl = await redis_client.ttl("expiring_key")
        assert 0 < ttl <= 2
        
        # Wait for expiration
        await asyncio.sleep(3)
        
        # Verify key is expired
        value = await redis_client.get("expiring_key")
        assert value is None
    
    async def test_redis_pipeline_operations(self, redis_client: RedisClient):
        """Test Redis pipeline for batch operations."""
        # Create pipeline
        async with redis_client.pipeline() as pipe:
            for i in range(5):
                pipe.set(f"batch_key_{i}", f"batch_value_{i}")
            
            # Execute pipeline
            results = await pipe.execute()
            assert len(results) == 5
            assert all(result is True for result in results)
        
        # Verify all keys were set
        for i in range(5):
            value = await redis_client.get(f"batch_key_{i}")
            assert value == f"batch_value_{i}"
    
    async def test_redis_concurrent_operations(self, redis_client: RedisClient):
        """Test concurrent Redis operations."""
        # Concurrent counter increments
        async def increment_counter(counter_key: str, count: int):
            for _ in range(count):
                await redis_client.incr(counter_key)
        
        # Run concurrent increments
        counter_key = "concurrent_counter"
        tasks = [increment_counter(counter_key, 10) for _ in range(5)]
        await asyncio.gather(*tasks)
        
        # Verify final count
        final_count = await redis_client.get(counter_key)
        assert int(final_count) == 50  # 5 tasks * 10 increments each
    
    async def test_redis_connection_recovery(self, redis_url):
        """Test Redis connection recovery after connection loss."""
        # Create client with short timeouts
        config = RedisConfig(
            url=redis_url,
            socket_timeout=1.0,
            socket_connect_timeout=1.0,
            retry_on_timeout=True,
            max_connections=5
        )
        
        client = RedisClient(config)
        await client.initialize()
        
        try:
            # Test initial connection
            await client.set("recovery_test", "initial_value")
            value = await client.get("recovery_test")
            assert value == "initial_value"
            
            # Test connection after brief delay (connection pooling)
            await asyncio.sleep(1.5)
            
            # Should still work (connection recovery)
            await client.set("recovery_test", "recovered_value")
            value = await client.get("recovery_test")
            assert value == "recovered_value"
        
        finally:
            await client.close()


@pytest.mark.integration
@pytest.mark.asyncio
class TestPatternCacheIntegration:
    """Integration tests for pattern caching functionality."""
    
    @pytest.fixture
    def redis_url(self):
        """Get test Redis URL from environment or use default."""
        return os.getenv(
            "TEST_REDIS_URL",
            "redis://localhost:6379/2"  # Use DB 2 for pattern cache testing
        )
    
    @pytest.fixture
    async def pattern_cache(self, redis_url):
        """Create pattern cache for testing."""
        redis_config = RedisConfig(url=redis_url)
        redis_client = RedisClient(redis_config)
        await redis_client.initialize()
        
        # Clear test database
        await redis_client.flushdb()
        
        cache = PatternCache(redis_client)
        yield cache
        
        # Cleanup
        await redis_client.flushdb()
        await redis_client.close()
    
    @pytest.fixture
    def sample_pattern_data(self):
        """Create sample pattern data for caching tests."""
        return {
            "pattern_id": "cache-pattern-123",
            "pattern_name": "Iterator Pattern",
            "pattern_type": PatternType.DESIGN_PATTERN.value,
            "severity": SeverityLevel.LOW.value,
            "confidence": 0.92,
            "file_path": "src/main.py",
            "line_number": 10,
            "description": "Iterator pattern implementation",
            "detection_method": DetectionType.ML_INFERENCE.value,
            "context": {
                "function_name": "process_items",
                "class_name": "ItemProcessor"
            },
            "metadata": {
                "model_version": "1.0.0",
                "cached_at": datetime.utcnow().isoformat()
            }
        }
    
    async def test_cache_pattern_basic_operations(
        self,
        pattern_cache: PatternCache,
        sample_pattern_data: Dict[str, Any]
    ):
        """Test basic pattern caching operations."""
        cache_key = "repo:test-repo:patterns"
        
        # Cache pattern
        await pattern_cache.cache_patterns(cache_key, [sample_pattern_data], ttl=300)
        
        # Retrieve cached pattern
        cached_patterns = await pattern_cache.get_cached_patterns(cache_key)
        assert cached_patterns is not None
        assert len(cached_patterns) == 1
        assert cached_patterns[0]["pattern_id"] == sample_pattern_data["pattern_id"]
        assert cached_patterns[0]["confidence"] == sample_pattern_data["confidence"]
    
    async def test_cache_pattern_multiple_patterns(
        self,
        pattern_cache: PatternCache,
        sample_pattern_data: Dict[str, Any]
    ):
        """Test caching multiple patterns."""
        cache_key = "repo:multi-pattern-repo:patterns"
        
        # Create multiple patterns
        patterns = []
        for i in range(5):
            pattern = sample_pattern_data.copy()
            pattern["pattern_id"] = f"pattern-{i}"
            pattern["pattern_name"] = f"Pattern {i}"
            pattern["line_number"] = 10 + i * 5
            patterns.append(pattern)
        
        # Cache patterns
        await pattern_cache.cache_patterns(cache_key, patterns, ttl=300)
        
        # Retrieve cached patterns
        cached_patterns = await pattern_cache.get_cached_patterns(cache_key)
        assert cached_patterns is not None
        assert len(cached_patterns) == 5
        
        # Verify pattern order and data
        for i, pattern in enumerate(cached_patterns):
            assert pattern["pattern_id"] == f"pattern-{i}"
            assert pattern["pattern_name"] == f"Pattern {i}"
            assert pattern["line_number"] == 10 + i * 5
    
    async def test_cache_pattern_expiration(
        self,
        pattern_cache: PatternCache,
        sample_pattern_data: Dict[str, Any]
    ):
        """Test pattern cache expiration."""
        cache_key = "repo:expiring-repo:patterns"
        
        # Cache pattern with short TTL
        await pattern_cache.cache_patterns(cache_key, [sample_pattern_data], ttl=2)
        
        # Verify pattern is cached
        cached_patterns = await pattern_cache.get_cached_patterns(cache_key)
        assert cached_patterns is not None
        assert len(cached_patterns) == 1
        
        # Wait for expiration
        await asyncio.sleep(3)
        
        # Verify pattern cache expired
        cached_patterns = await pattern_cache.get_cached_patterns(cache_key)
        assert cached_patterns is None
    
    async def test_cache_pattern_invalidation(
        self,
        pattern_cache: PatternCache,
        sample_pattern_data: Dict[str, Any]
    ):
        """Test pattern cache invalidation."""
        cache_key = "repo:invalidation-repo:patterns"
        
        # Cache pattern
        await pattern_cache.cache_patterns(cache_key, [sample_pattern_data], ttl=300)
        
        # Verify pattern is cached
        cached_patterns = await pattern_cache.get_cached_patterns(cache_key)
        assert cached_patterns is not None
        
        # Invalidate cache
        await pattern_cache.invalidate_cache(cache_key)
        
        # Verify cache is invalidated
        cached_patterns = await pattern_cache.get_cached_patterns(cache_key)
        assert cached_patterns is None
    
    async def test_cache_pattern_repository_invalidation(
        self,
        pattern_cache: PatternCache,
        sample_pattern_data: Dict[str, Any]
    ):
        """Test repository-wide cache invalidation."""
        repository_id = "bulk-invalidation-repo"
        
        # Cache patterns for different aspects of the same repository
        cache_keys = [
            f"repo:{repository_id}:patterns",
            f"repo:{repository_id}:stats",
            f"repo:{repository_id}:analysis"
        ]
        
        for cache_key in cache_keys:
            await pattern_cache.cache_patterns(cache_key, [sample_pattern_data], ttl=300)
        
        # Verify all caches exist
        for cache_key in cache_keys:
            cached_data = await pattern_cache.get_cached_patterns(cache_key)
            assert cached_data is not None
        
        # Invalidate entire repository cache
        await pattern_cache.invalidate_repository_cache(repository_id)
        
        # Verify all repository caches are invalidated
        for cache_key in cache_keys:
            cached_data = await pattern_cache.get_cached_patterns(cache_key)
            assert cached_data is None
    
    async def test_cache_pattern_statistics(
        self,
        pattern_cache: PatternCache,
        sample_pattern_data: Dict[str, Any]
    ):
        """Test caching of pattern statistics."""
        repository_id = "stats-repo"
        stats_cache_key = f"repo:{repository_id}:stats"
        
        # Create sample statistics
        stats_data = {
            "total_patterns": 25,
            "by_type": {
                PatternType.DESIGN_PATTERN.value: 10,
                PatternType.SECURITY_ISSUE.value: 8,
                PatternType.PERFORMANCE_ISSUE.value: 7
            },
            "by_severity": {
                SeverityLevel.LOW.value: 12,
                SeverityLevel.MEDIUM.value: 8,
                SeverityLevel.HIGH.value: 4,
                SeverityLevel.CRITICAL.value: 1
            },
            "average_confidence": 0.87,
            "last_updated": datetime.utcnow().isoformat()
        }
        
        # Cache statistics
        await pattern_cache.cache_statistics(stats_cache_key, stats_data, ttl=600)
        
        # Retrieve cached statistics
        cached_stats = await pattern_cache.get_cached_statistics(stats_cache_key)
        assert cached_stats is not None
        assert cached_stats["total_patterns"] == 25
        assert cached_stats["average_confidence"] == 0.87
        assert cached_stats["by_type"][PatternType.DESIGN_PATTERN.value] == 10
    
    async def test_cache_pattern_concurrent_access(
        self,
        pattern_cache: PatternCache,
        sample_pattern_data: Dict[str, Any]
    ):
        """Test concurrent access to pattern cache."""
        cache_key = "repo:concurrent-repo:patterns"
        
        # Concurrent cache operations
        async def cache_pattern(pattern_id: str):
            pattern_data = sample_pattern_data.copy()
            pattern_data["pattern_id"] = pattern_id
            await pattern_cache.cache_patterns(f"{cache_key}:{pattern_id}", [pattern_data], ttl=300)
            return pattern_id
        
        async def get_cached_pattern(pattern_id: str):
            cached_patterns = await pattern_cache.get_cached_patterns(f"{cache_key}:{pattern_id}")
            return cached_patterns[0]["pattern_id"] if cached_patterns else None
        
        # Cache multiple patterns concurrently
        cache_tasks = [cache_pattern(f"concurrent-{i}") for i in range(10)]
        cached_ids = await asyncio.gather(*cache_tasks)
        assert len(cached_ids) == 10
        
        # Retrieve patterns concurrently
        get_tasks = [get_cached_pattern(f"concurrent-{i}") for i in range(10)]
        retrieved_ids = await asyncio.gather(*get_tasks)
        
        # Verify all patterns were cached and retrieved correctly
        assert len(retrieved_ids) == 10
        assert all(f"concurrent-{i}" in retrieved_ids for i in range(10))
    
    async def test_cache_pattern_memory_usage(
        self,
        pattern_cache: PatternCache,
        sample_pattern_data: Dict[str, Any]
    ):
        """Test pattern cache memory usage patterns."""
        # Cache many patterns to test memory usage
        patterns = []
        for i in range(100):
            pattern = sample_pattern_data.copy()
            pattern["pattern_id"] = f"memory-test-{i}"
            pattern["description"] = f"Memory test pattern {i} " + "x" * 100  # Add bulk to test memory
            patterns.append(pattern)
        
        cache_key = "repo:memory-test-repo:patterns"
        
        # Cache large pattern set
        await pattern_cache.cache_patterns(cache_key, patterns, ttl=300)
        
        # Verify patterns are cached correctly
        cached_patterns = await pattern_cache.get_cached_patterns(cache_key)
        assert cached_patterns is not None
        assert len(cached_patterns) == 100
        
        # Test memory usage by checking first and last patterns
        assert cached_patterns[0]["pattern_id"] == "memory-test-0"
        assert cached_patterns[99]["pattern_id"] == "memory-test-99"
        assert "Memory test pattern 0" in cached_patterns[0]["description"]
        assert "Memory test pattern 99" in cached_patterns[99]["description"]


@pytest.mark.integration
@pytest.mark.asyncio
class TestRedisCacheStrategiesIntegration:
    """Integration tests for Redis cache strategies."""
    
    @pytest.fixture
    def redis_url(self):
        """Get test Redis URL from environment or use default."""
        return os.getenv(
            "TEST_REDIS_URL",
            "redis://localhost:6379/3"  # Use DB 3 for cache strategy testing
        )
    
    @pytest.fixture
    async def redis_client(self, redis_url):
        """Create Redis client for testing."""
        config = RedisConfig(url=redis_url)
        client = RedisClient(config)
        await client.initialize()
        
        # Clear test database
        await client.flushdb()
        
        yield client
        
        # Cleanup
        await client.flushdb()
        await client.close()
    
    async def test_lru_cache_strategy_integration(self, redis_client: RedisClient):
        """Test LRU cache strategy with Redis."""
        max_size = 5
        strategy = LRUCacheStrategy(redis_client, max_size=max_size)
        
        # Fill cache to capacity
        for i in range(max_size):
            await strategy.set(f"lru_key_{i}", f"value_{i}")
        
        # Verify all items are cached
        for i in range(max_size):
            value = await strategy.get(f"lru_key_{i}")
            assert value == f"value_{i}"
        
        # Add one more item (should evict least recently used)
        await strategy.set("lru_key_new", "new_value")
        
        # First item should be evicted (LRU)
        evicted_value = await strategy.get("lru_key_0")
        assert evicted_value is None
        
        # New item should be present
        new_value = await strategy.get("lru_key_new")
        assert new_value == "new_value"
        
        # Other items should still be present
        for i in range(1, max_size):
            value = await strategy.get(f"lru_key_{i}")
            assert value == f"value_{i}"
    
    async def test_ttl_cache_strategy_integration(self, redis_client: RedisClient):
        """Test TTL cache strategy with Redis."""
        default_ttl = 2  # 2 seconds
        strategy = TTLCacheStrategy(redis_client, default_ttl=default_ttl)
        
        # Set items with default TTL
        await strategy.set("ttl_key_1", "value_1")
        await strategy.set("ttl_key_2", "value_2", ttl=4)  # Custom TTL
        
        # Verify items are cached
        value1 = await strategy.get("ttl_key_1")
        value2 = await strategy.get("ttl_key_2")
        assert value1 == "value_1"
        assert value2 == "value_2"
        
        # Wait for default TTL expiration
        await asyncio.sleep(3)
        
        # First item should be expired
        value1 = await strategy.get("ttl_key_1")
        assert value1 is None
        
        # Second item should still be present (longer TTL)
        value2 = await strategy.get("ttl_key_2")
        assert value2 == "value_2"
        
        # Wait for second item expiration
        await asyncio.sleep(2)
        
        # Second item should now be expired
        value2 = await strategy.get("ttl_key_2")
        assert value2 is None
    
    async def test_cache_strategy_performance_integration(self, redis_client: RedisClient):
        """Test cache strategy performance with Redis."""
        strategy = LRUCacheStrategy(redis_client, max_size=1000)
        
        # Test batch operations performance
        start_time = time.time()
        
        # Batch set operations
        tasks = []
        for i in range(100):
            task = strategy.set(f"perf_key_{i}", f"performance_value_{i}")
            tasks.append(task)
        
        await asyncio.gather(*tasks)
        set_time = time.time() - start_time
        
        # Test batch get operations
        start_time = time.time()
        
        get_tasks = []
        for i in range(100):
            task = strategy.get(f"perf_key_{i}")
            get_tasks.append(task)
        
        results = await asyncio.gather(*get_tasks)
        get_time = time.time() - start_time
        
        # Verify results
        assert len(results) == 100
        assert all(f"performance_value_{i}" == results[i] for i in range(100))
        
        # Performance should be reasonable (under 1 second for 100 operations)
        assert set_time < 1.0
        assert get_time < 1.0