"""
Gemini API Integration Tests

Real integration tests for Gemini 2.5 Flash API, testing actual API connections,
rate limiting behavior, error handling, and authentication flows.
"""

import pytest
import asyncio
import os
import time
from typing import Dict, Any, List
from unittest.mock import patch
import json
from datetime import datetime, timedelta

import google.generativeai as genai
from google.generativeai.types import <PERSON>rmCategory, HarmBlockThreshold
import aiohttp

from pattern_mining.ml.gemini_client import GeminiClient, RequestStatus, GeminiRateLimiter
from pattern_mining.ml.gemini_analyzer import GeminiPatternAnalyzer
from pattern_mining.config.gemini import get_gemini_config, GeminiModel, SafetyLevel
from pattern_mining.models.patterns import PatternType, SeverityLevel
from tests.utils.generators import CodeGenerator


@pytest.mark.integration
@pytest.mark.asyncio
class TestGeminiAPIIntegration:
    """Integration tests for real Gemini API connections."""
    
    @pytest.fixture
    def gemini_api_key(self):
        """Get test API key from environment or skip test."""
        api_key = os.getenv("GEMINI_API_KEY_TEST")
        if not api_key:
            pytest.skip("GEMINI_API_KEY_TEST not set - skipping Gemini integration tests")
        return api_key
    
    @pytest.fixture
    async def gemini_client(self, gemini_api_key):
        """Create Gemini client for testing."""
        config = get_gemini_config()
        config.api_key = gemini_api_key
        config.model = GeminiModel.GEMINI_2_5_FLASH
        config.enable_thinking = True
        
        client = GeminiClient(config)
        await client.initialize()
        yield client
        await client.close()
    
    async def test_gemini_api_connection(self, gemini_client: GeminiClient):
        """Test basic Gemini API connection and authentication."""
        # Test simple text generation
        prompt = "Say 'Hello, integration test!' in exactly those words."
        
        response = await gemini_client.generate_text(
            prompt=prompt,
            model=GeminiModel.GEMINI_2_5_FLASH,
            temperature=0.0  # Make response deterministic
        )
        
        assert response is not None
        assert "request_id" in response
        assert "text" in response
        assert "metadata" in response
        assert response["metadata"]["model"] == GeminiModel.GEMINI_2_5_FLASH.value
        assert "Hello, integration test!" in response["text"]
        
        # Verify metadata
        metadata = response["metadata"]
        assert "tokens_used" in metadata
        assert "response_time_ms" in metadata
        assert metadata["tokens_used"] > 0
        assert metadata["response_time_ms"] > 0
    
    async def test_gemini_code_analysis_integration(self, gemini_client: GeminiClient):
        """Test Gemini code analysis with real API."""
        code_sample = """
def calculate_total(items):
    total = 0
    for item in items:
        if item['price'] > 0:
            total += item['price'] * item['quantity']
    return total

class ShoppingCart:
    def __init__(self):
        self.items = []
    
    def add_item(self, item):
        self.items.append(item)
    
    def get_total(self):
        return calculate_total(self.items)
"""
        
        analyzer = GeminiPatternAnalyzer(gemini_client)
        patterns = await analyzer.analyze_code(
            code=code_sample,
            language="python",
            file_path="shopping_cart.py"
        )
        
        assert isinstance(patterns, list)
        assert len(patterns) > 0
        
        # Verify pattern structure
        for pattern in patterns:
            assert "pattern_name" in pattern
            assert "pattern_type" in pattern
            assert "confidence" in pattern
            assert "location" in pattern
            assert "description" in pattern
            
        # Should detect iterator pattern or similar
        pattern_names = [p["pattern_name"].lower() for p in patterns]
        assert any("iterator" in name or "loop" in name or "accumulator" in name 
                  for name in pattern_names)
    
    async def test_gemini_rate_limiting_integration(self, gemini_client: GeminiClient):
        """Test real rate limiting behavior with Gemini API."""
        # Create multiple concurrent requests to test rate limiting
        requests = []
        for i in range(5):  # Send 5 requests simultaneously
            prompt = f"Count to {i+1}: "
            request = gemini_client.generate_text(
                prompt=prompt,
                model=GeminiModel.GEMINI_2_5_FLASH,
                temperature=0.0
            )
            requests.append(request)
        
        # Execute requests and measure timing
        start_time = time.time()
        responses = await asyncio.gather(*requests, return_exceptions=True)
        end_time = time.time()
        
        # Verify responses
        successful_responses = []
        rate_limited_responses = []
        
        for response in responses:
            if isinstance(response, Exception):
                # Check if it's a rate limiting error
                if "rate" in str(response).lower() or "quota" in str(response).lower():
                    rate_limited_responses.append(response)
                else:
                    # Re-raise unexpected exceptions
                    raise response
            else:
                successful_responses.append(response)
        
        # Should have some successful responses
        assert len(successful_responses) > 0
        
        # If rate limited, should be handled gracefully
        if rate_limited_responses:
            assert len(rate_limited_responses) < len(requests)  # Not all should fail
        
        # Total time should be reasonable (rate limiting may add delay)
        assert end_time - start_time < 30.0  # Should complete within 30 seconds
    
    async def test_gemini_error_handling_integration(self, gemini_client: GeminiClient):
        """Test error handling with real API scenarios."""
        # Test invalid prompt (too long)
        very_long_prompt = "Analyze this code: " + "x" * 100000  # Very long prompt
        
        with pytest.raises((ValueError, aiohttp.ClientError, Exception)) as exc_info:
            await gemini_client.generate_text(
                prompt=very_long_prompt,
                model=GeminiModel.GEMINI_2_5_FLASH
            )
        
        # Should get a meaningful error
        error_str = str(exc_info.value).lower()
        assert any(keyword in error_str for keyword in [
            "token", "length", "limit", "quota", "invalid"
        ])
    
    async def test_gemini_thinking_mode_integration(self, gemini_client: GeminiClient):
        """Test Gemini 2.5 Flash thinking mode integration."""
        complex_prompt = """
        Analyze this complex algorithm and identify patterns:
        
        def fibonacci_memo(n, memo={}):
            if n in memo:
                return memo[n]
            if n <= 2:
                return 1
            memo[n] = fibonacci_memo(n-1, memo) + fibonacci_memo(n-2, memo)
            return memo[n]
        
        def is_fibonacci(num):
            a, b = 0, 1
            while b < num:
                a, b = b, a + b
            return b == num
        
        Please think step by step about the patterns used here.
        """
        
        # Enable thinking mode
        response = await gemini_client.generate_text(
            prompt=complex_prompt,
            model=GeminiModel.GEMINI_2_5_FLASH,
            enable_thinking=True,
            temperature=0.3
        )
        
        assert response is not None
        assert "text" in response
        assert "thinking" in response  # Should include thinking process
        
        # Response should be comprehensive
        text = response["text"].lower()
        assert len(text) > 100  # Should be detailed
        
        # Should identify memoization pattern
        assert any(keyword in text for keyword in [
            "memoization", "memo", "dynamic programming", "cache", "optimization"
        ])
        
        # Thinking process should be present
        if response.get("thinking"):
            thinking_text = response["thinking"].lower()
            assert "step" in thinking_text or "think" in thinking_text
    
    async def test_gemini_safety_filtering_integration(self, gemini_client: GeminiClient):
        """Test safety filtering with real API."""
        # Test benign code analysis (should pass)
        safe_code = """
        def greet_user(name):
            return f"Hello, {name}!"
        """
        
        response = await gemini_client.generate_text(
            prompt=f"Analyze this code for patterns:\n{safe_code}",
            model=GeminiModel.GEMINI_2_5_FLASH,
            safety_level=SafetyLevel.HIGH
        )
        
        assert response is not None
        assert "text" in response
        assert len(response["text"]) > 0
    
    async def test_gemini_batch_processing_integration(self, gemini_client: GeminiClient):
        """Test batch processing with real API."""
        code_samples = [
            "def add(a, b): return a + b",
            "class Counter: def __init__(self): self.count = 0",
            "for i in range(10): print(i)",
            "try: result = risky_operation() except: pass"
        ]
        
        # Process multiple code samples
        tasks = []
        for i, code in enumerate(code_samples):
            task = gemini_client.generate_text(
                prompt=f"Identify the main pattern in this code:\n{code}",
                model=GeminiModel.GEMINI_2_5_FLASH,
                temperature=0.1
            )
            tasks.append(task)
        
        # Execute batch
        start_time = time.time()
        responses = await asyncio.gather(*tasks, return_exceptions=True)
        end_time = time.time()
        
        # Verify batch results
        successful_responses = [r for r in responses if not isinstance(r, Exception)]
        assert len(successful_responses) >= len(code_samples) // 2  # At least half should succeed
        
        # Check response quality
        for response in successful_responses:
            assert "text" in response
            assert len(response["text"]) > 10  # Should have meaningful content
        
        # Batch should complete in reasonable time
        assert end_time - start_time < 60.0  # Should complete within 1 minute
    
    async def test_gemini_retry_logic_integration(self, gemini_client: GeminiClient):
        """Test retry logic with real API failures."""
        # This test simulates network issues by using invalid configuration temporarily
        original_timeout = gemini_client.config.request_timeout
        
        try:
            # Set very short timeout to trigger retries
            gemini_client.config.request_timeout = 0.001  # 1ms - will likely timeout
            
            with pytest.raises((asyncio.TimeoutError, aiohttp.ClientError, Exception)):
                await gemini_client.generate_text(
                    prompt="This should timeout quickly",
                    model=GeminiModel.GEMINI_2_5_FLASH,
                    max_retries=2
                )
        
        finally:
            # Restore original timeout
            gemini_client.config.request_timeout = original_timeout
        
        # Verify normal operation still works
        response = await gemini_client.generate_text(
            prompt="Test normal operation",
            model=GeminiModel.GEMINI_2_5_FLASH
        )
        assert response is not None


@pytest.mark.integration
@pytest.mark.asyncio
class TestGeminiRateLimiterIntegration:
    """Integration tests for Gemini rate limiter."""
    
    @pytest.fixture
    def rate_limiter(self):
        """Create rate limiter for testing."""
        return GeminiRateLimiter(
            requests_per_minute=10,  # Low limit for testing
            tokens_per_minute=5000
        )
    
    async def test_rate_limiter_acquire_integration(self, rate_limiter: GeminiRateLimiter):
        """Test rate limiter acquire functionality."""
        # Should be able to acquire initially
        can_acquire = await rate_limiter.acquire(estimated_tokens=100)
        assert can_acquire is True
        
        # Should be able to acquire multiple times within limits
        acquisitions = []
        for i in range(9):  # 9 more requests (total 10)
            result = await rate_limiter.acquire(estimated_tokens=100)
            acquisitions.append(result)
        
        # All acquisitions within limit should succeed
        assert all(acquisitions)
        
        # Next acquisition should fail (exceeds limit)
        can_acquire = await rate_limiter.acquire(estimated_tokens=100)
        assert can_acquire is False
    
    async def test_rate_limiter_token_bucket_integration(self, rate_limiter: GeminiRateLimiter):
        """Test token bucket functionality."""
        # Consume most tokens
        can_acquire = await rate_limiter.acquire(estimated_tokens=4500)
        assert can_acquire is True
        
        # Should not be able to acquire more tokens
        can_acquire = await rate_limiter.acquire(estimated_tokens=1000)
        assert can_acquire is False
        
        # Small request should still fail
        can_acquire = await rate_limiter.acquire(estimated_tokens=100)
        assert can_acquire is False
    
    async def test_rate_limiter_refill_integration(self, rate_limiter: GeminiRateLimiter):
        """Test bucket refill over time."""
        # Exhaust request bucket
        for i in range(10):
            await rate_limiter.acquire(estimated_tokens=100)
        
        # Should be unable to acquire
        can_acquire = await rate_limiter.acquire(estimated_tokens=100)
        assert can_acquire is False
        
        # Wait for refill (simulate time passing)
        await asyncio.sleep(6.1)  # Wait slightly more than 6 seconds for refill
        
        # Should be able to acquire again
        can_acquire = await rate_limiter.acquire(estimated_tokens=100)
        assert can_acquire is True


@pytest.mark.integration
@pytest.mark.asyncio
class TestGeminiPatternAnalyzerIntegration:
    """Integration tests for Gemini pattern analyzer."""
    
    @pytest.fixture
    def gemini_api_key(self):
        """Get test API key from environment or skip test."""
        api_key = os.getenv("GEMINI_API_KEY_TEST")
        if not api_key:
            pytest.skip("GEMINI_API_KEY_TEST not set - skipping Gemini analyzer integration tests")
        return api_key
    
    @pytest.fixture
    async def pattern_analyzer(self, gemini_api_key):
        """Create pattern analyzer for testing."""
        config = get_gemini_config()
        config.api_key = gemini_api_key
        config.model = GeminiModel.GEMINI_2_5_FLASH
        
        client = GeminiClient(config)
        await client.initialize()
        
        analyzer = GeminiPatternAnalyzer(client)
        yield analyzer
        
        await client.close()
    
    async def test_analyze_design_patterns_integration(self, pattern_analyzer: GeminiPatternAnalyzer):
        """Test design pattern detection with real API."""
        singleton_code = """
        class DatabaseConnection:
            _instance = None
            _initialized = False
            
            def __new__(cls):
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                return cls._instance
            
            def __init__(self):
                if not self._initialized:
                    self.connection = self._create_connection()
                    self._initialized = True
            
            def _create_connection(self):
                return "database_connection"
        """
        
        patterns = await pattern_analyzer.analyze_code(
            code=singleton_code,
            language="python",
            file_path="database.py"
        )
        
        assert len(patterns) > 0
        
        # Should detect singleton pattern
        pattern_names = [p["pattern_name"].lower() for p in patterns]
        assert any("singleton" in name for name in pattern_names)
        
        # Verify pattern details
        singleton_pattern = next(
            (p for p in patterns if "singleton" in p["pattern_name"].lower()),
            None
        )
        
        if singleton_pattern:
            assert singleton_pattern["pattern_type"] == PatternType.DESIGN_PATTERN.value
            assert singleton_pattern["confidence"] > 0.7
            assert "location" in singleton_pattern
            assert singleton_pattern["location"]["file"] == "database.py"
    
    async def test_analyze_security_issues_integration(self, pattern_analyzer: GeminiPatternAnalyzer):
        """Test security issue detection with real API."""
        vulnerable_code = """
        import sqlite3
        
        def get_user_data(user_id):
            conn = sqlite3.connect('users.db')
            cursor = conn.cursor()
            
            # Vulnerable SQL injection
            query = f"SELECT * FROM users WHERE id = {user_id}"
            cursor.execute(query)
            
            result = cursor.fetchone()
            conn.close()
            return result
        
        def authenticate_user(username, password):
            # Weak password comparison
            if password == "admin123":
                return True
            return False
        """
        
        patterns = await pattern_analyzer.analyze_code(
            code=vulnerable_code,
            language="python",
            file_path="auth.py"
        )
        
        assert len(patterns) > 0
        
        # Should detect security issues
        security_patterns = [
            p for p in patterns 
            if p["pattern_type"] == PatternType.SECURITY_ISSUE.value
        ]
        
        assert len(security_patterns) > 0
        
        # Should identify SQL injection
        pattern_descriptions = [p["description"].lower() for p in security_patterns]
        assert any("sql" in desc and "injection" in desc for desc in pattern_descriptions)
    
    async def test_analyze_performance_issues_integration(self, pattern_analyzer: GeminiPatternAnalyzer):
        """Test performance issue detection with real API."""
        performance_code = """
        def inefficient_search(items, target):
            results = []
            for i in range(len(items)):
                for j in range(len(items)):
                    if items[i] == target and items[j] == target:
                        results.append((i, j))
            return results
        
        def nested_loops_problem(matrix):
            total = 0
            for i in range(len(matrix)):
                for j in range(len(matrix[i])):
                    for k in range(len(matrix)):
                        total += matrix[i][j] * matrix[k][0]
            return total
        """
        
        patterns = await pattern_analyzer.analyze_code(
            code=performance_code,
            language="python",
            file_path="performance.py"
        )
        
        assert len(patterns) > 0
        
        # Should detect performance issues
        performance_patterns = [
            p for p in patterns 
            if p["pattern_type"] == PatternType.PERFORMANCE_ISSUE.value
        ]
        
        assert len(performance_patterns) > 0
        
        # Should identify O(n²) or O(n³) complexity
        pattern_descriptions = [p["description"].lower() for p in performance_patterns]
        assert any(
            "nested" in desc or "complexity" in desc or "inefficient" in desc 
            for desc in pattern_descriptions
        )
    
    async def test_batch_analysis_integration(self, pattern_analyzer: GeminiPatternAnalyzer):
        """Test batch code analysis with real API."""
        code_files = [
            {
                "code": "def factorial(n): return 1 if n <= 1 else n * factorial(n-1)",
                "language": "python",
                "file_path": "recursion.py"
            },
            {
                "code": "class Observer: def update(self): pass",
                "language": "python", 
                "file_path": "observer.py"
            },
            {
                "code": "items = [x*2 for x in range(1000000)]",
                "language": "python",
                "file_path": "memory.py"
            }
        ]
        
        # Analyze multiple files
        tasks = []
        for file_data in code_files:
            task = pattern_analyzer.analyze_code(**file_data)
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Verify batch results
        successful_results = [r for r in results if not isinstance(r, Exception)]
        assert len(successful_results) >= 2  # At least 2 should succeed
        
        # Check that different patterns are detected
        all_patterns = []
        for result in successful_results:
            all_patterns.extend(result)
        
        assert len(all_patterns) > 0
        
        # Should have variety in pattern types
        pattern_types = set(p["pattern_type"] for p in all_patterns)
        assert len(pattern_types) > 1  # Should detect different types of patterns