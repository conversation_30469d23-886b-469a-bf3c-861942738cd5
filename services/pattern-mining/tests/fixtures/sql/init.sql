-- Phase 2 Advanced Integration Testing Database Initialization
-- Creates test data and schema for comprehensive integration testing

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- Create test schemas
CREATE SCHEMA IF NOT EXISTS pattern_mining;
CREATE SCHEMA IF NOT EXISTS test_data;

-- Set search path
SET search_path = pattern_mining, public;

-- =============================================================================
-- Core Tables for Pattern Mining
-- =============================================================================

-- Repositories table
CREATE TABLE IF NOT EXISTS repositories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    url VARCHAR(500) NOT NULL,
    language VARCHAR(50),
    size_bytes BIGINT DEFAULT 0,
    files_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    status VARCHAR(50) DEFAULT 'active'
);

-- Analysis results table
CREATE TABLE IF NOT EXISTS analysis_results (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    repository_id UUID NOT NULL REFERENCES repositories(id) ON DELETE CASCADE,
    analysis_type VARCHAR(100) NOT NULL,
    results JSONB NOT NULL,
    confidence_score DECIMAL(5,4) DEFAULT 0.0,
    processing_time_ms INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    status VARCHAR(50) DEFAULT 'completed'
);

-- Patterns table
CREATE TABLE IF NOT EXISTS patterns (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    repository_id UUID NOT NULL REFERENCES repositories(id) ON DELETE CASCADE,
    pattern_type VARCHAR(100) NOT NULL,
    pattern_data JSONB NOT NULL,
    frequency INTEGER DEFAULT 1,
    complexity_score DECIMAL(5,4) DEFAULT 0.0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Quality metrics table
CREATE TABLE IF NOT EXISTS quality_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    repository_id UUID NOT NULL REFERENCES repositories(id) ON DELETE CASCADE,
    metric_name VARCHAR(100) NOT NULL,
    metric_value DECIMAL(10,4) NOT NULL,
    threshold_value DECIMAL(10,4),
    status VARCHAR(50) DEFAULT 'pass',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================================================
-- Testing Support Tables
-- =============================================================================

-- Test scenarios table for tracking integration test execution
CREATE TABLE IF NOT EXISTS test_data.test_scenarios (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    scenario_name VARCHAR(255) NOT NULL,
    scenario_type VARCHAR(100) NOT NULL,
    configuration JSONB NOT NULL,
    expected_results JSONB,
    actual_results JSONB,
    execution_time_ms INTEGER,
    status VARCHAR(50) DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE
);

-- Test metrics for performance tracking
CREATE TABLE IF NOT EXISTS test_data.test_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    scenario_id UUID REFERENCES test_data.test_scenarios(id) ON DELETE CASCADE,
    metric_name VARCHAR(100) NOT NULL,
    metric_value DECIMAL(15,6) NOT NULL,
    unit VARCHAR(50),
    threshold_value DECIMAL(15,6),
    status VARCHAR(50) DEFAULT 'unknown',
    recorded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================================================
-- Indexes for Performance
-- =============================================================================

-- Repository indexes
CREATE INDEX IF NOT EXISTS idx_repositories_name ON repositories(name);
CREATE INDEX IF NOT EXISTS idx_repositories_language ON repositories(language);
CREATE INDEX IF NOT EXISTS idx_repositories_status ON repositories(status);
CREATE INDEX IF NOT EXISTS idx_repositories_created_at ON repositories(created_at);

-- Analysis results indexes
CREATE INDEX IF NOT EXISTS idx_analysis_results_repo_id ON analysis_results(repository_id);
CREATE INDEX IF NOT EXISTS idx_analysis_results_type ON analysis_results(analysis_type);
CREATE INDEX IF NOT EXISTS idx_analysis_results_created_at ON analysis_results(created_at);
CREATE INDEX IF NOT EXISTS idx_analysis_results_status ON analysis_results(status);

-- Patterns indexes
CREATE INDEX IF NOT EXISTS idx_patterns_repo_id ON patterns(repository_id);
CREATE INDEX IF NOT EXISTS idx_patterns_type ON patterns(pattern_type);
CREATE INDEX IF NOT EXISTS idx_patterns_frequency ON patterns(frequency);
CREATE INDEX IF NOT EXISTS idx_patterns_complexity ON patterns(complexity_score);

-- Quality metrics indexes
CREATE INDEX IF NOT EXISTS idx_quality_metrics_repo_id ON quality_metrics(repository_id);
CREATE INDEX IF NOT EXISTS idx_quality_metrics_name ON quality_metrics(metric_name);
CREATE INDEX IF NOT EXISTS idx_quality_metrics_status ON quality_metrics(status);

-- Test data indexes
CREATE INDEX IF NOT EXISTS idx_test_scenarios_name ON test_data.test_scenarios(scenario_name);
CREATE INDEX IF NOT EXISTS idx_test_scenarios_type ON test_data.test_scenarios(scenario_type);
CREATE INDEX IF NOT EXISTS idx_test_scenarios_status ON test_data.test_scenarios(status);
CREATE INDEX IF NOT EXISTS idx_test_metrics_scenario_id ON test_data.test_metrics(scenario_id);
CREATE INDEX IF NOT EXISTS idx_test_metrics_name ON test_data.test_metrics(metric_name);

-- =============================================================================
-- Test Data Population
-- =============================================================================

-- Insert sample repositories for testing
INSERT INTO repositories (id, name, url, language, size_bytes, files_count, status) VALUES
    ('550e8400-e29b-41d4-a716-446655440001', 'test-python-repo', 'https://github.com/test/python-repo', 'python', 1048576, 150, 'active'),
    ('550e8400-e29b-41d4-a716-446655440002', 'test-javascript-repo', 'https://github.com/test/js-repo', 'javascript', 2097152, 300, 'active'),
    ('550e8400-e29b-41d4-a716-446655440003', 'test-java-repo', 'https://github.com/test/java-repo', 'java', 5242880, 500, 'active'),
    ('550e8400-e29b-41d4-a716-446655440004', 'test-large-repo', 'https://github.com/test/large-repo', 'python', 52428800, 2000, 'active'),
    ('550e8400-e29b-41d4-a716-446655440005', 'test-archived-repo', 'https://github.com/test/archived-repo', 'python', 1048576, 100, 'archived')
ON CONFLICT (id) DO NOTHING;

-- Insert sample analysis results
INSERT INTO analysis_results (repository_id, analysis_type, results, confidence_score, processing_time_ms, status) VALUES
    ('550e8400-e29b-41d4-a716-446655440001', 'security_scan', '{"vulnerabilities": [], "security_score": 9.5}', 0.95, 1500, 'completed'),
    ('550e8400-e29b-41d4-a716-446655440001', 'quality_analysis', '{"code_quality": 8.2, "maintainability": 7.8}', 0.88, 2000, 'completed'),
    ('550e8400-e29b-41d4-a716-446655440002', 'performance_analysis', '{"performance_score": 7.5, "bottlenecks": ["async_operations"]}', 0.82, 3000, 'completed'),
    ('550e8400-e29b-41d4-a716-446655440003', 'architecture_analysis', '{"architecture_score": 8.9, "pattern_compliance": 0.92}', 0.91, 4500, 'completed'),
    ('550e8400-e29b-41d4-a716-446655440004', 'complexity_analysis', '{"complexity_score": 6.2, "cyclomatic_complexity": 45.2}', 0.78, 8000, 'completed')
ON CONFLICT DO NOTHING;

-- Insert sample patterns
INSERT INTO patterns (repository_id, pattern_type, pattern_data, frequency, complexity_score) VALUES
    ('550e8400-e29b-41d4-a716-446655440001', 'design_pattern', '{"pattern": "singleton", "confidence": 0.92}', 5, 0.6),
    ('550e8400-e29b-41d4-a716-446655440001', 'antipattern', '{"pattern": "god_object", "confidence": 0.78}', 2, 0.8),
    ('550e8400-e29b-41d4-a716-446655440002', 'performance_pattern', '{"pattern": "caching", "confidence": 0.85}', 8, 0.4),
    ('550e8400-e29b-41d4-a716-446655440003', 'security_pattern', '{"pattern": "input_validation", "confidence": 0.95}', 12, 0.7),
    ('550e8400-e29b-41d4-a716-446655440004', 'complexity_pattern', '{"pattern": "deep_nesting", "confidence": 0.88}', 25, 0.9)
ON CONFLICT DO NOTHING;

-- Insert sample quality metrics
INSERT INTO quality_metrics (repository_id, metric_name, metric_value, threshold_value, status) VALUES
    ('550e8400-e29b-41d4-a716-446655440001', 'code_coverage', 85.5, 80.0, 'pass'),
    ('550e8400-e29b-41d4-a716-446655440001', 'cyclomatic_complexity', 12.3, 15.0, 'pass'),
    ('550e8400-e29b-41d4-a716-446655440002', 'maintainability_index', 78.2, 70.0, 'pass'),
    ('550e8400-e29b-41d4-a716-446655440003', 'security_score', 92.1, 85.0, 'pass'),
    ('550e8400-e29b-41d4-a716-446655440004', 'performance_score', 65.8, 70.0, 'fail')
ON CONFLICT DO NOTHING;

-- =============================================================================
-- Views for Testing
-- =============================================================================

-- Repository summary view
CREATE OR REPLACE VIEW repository_summary AS
SELECT 
    r.id,
    r.name,
    r.language,
    r.size_bytes,
    r.files_count,
    COUNT(ar.id) as analysis_count,
    COUNT(p.id) as pattern_count,
    AVG(qm.metric_value) as avg_quality_score,
    r.status,
    r.created_at
FROM repositories r
LEFT JOIN analysis_results ar ON r.id = ar.repository_id
LEFT JOIN patterns p ON r.id = p.repository_id
LEFT JOIN quality_metrics qm ON r.id = qm.repository_id AND qm.metric_name = 'code_coverage'
GROUP BY r.id, r.name, r.language, r.size_bytes, r.files_count, r.status, r.created_at;

-- Analysis performance view
CREATE OR REPLACE VIEW analysis_performance AS
SELECT 
    analysis_type,
    COUNT(*) as total_analyses,
    AVG(processing_time_ms) as avg_processing_time,
    MIN(processing_time_ms) as min_processing_time,
    MAX(processing_time_ms) as max_processing_time,
    AVG(confidence_score) as avg_confidence,
    COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_count,
    COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_count
FROM analysis_results
GROUP BY analysis_type;

-- =============================================================================
-- Functions for Testing
-- =============================================================================

-- Function to generate test repository
CREATE OR REPLACE FUNCTION test_data.create_test_repository(
    p_name VARCHAR(255),
    p_language VARCHAR(50) DEFAULT 'python',
    p_size_bytes BIGINT DEFAULT 1048576,
    p_files_count INTEGER DEFAULT 100
) RETURNS UUID AS $$
DECLARE
    repo_id UUID;
BEGIN
    INSERT INTO repositories (name, url, language, size_bytes, files_count, status)
    VALUES (p_name, 'https://github.com/test/' || p_name, p_language, p_size_bytes, p_files_count, 'active')
    RETURNING id INTO repo_id;
    
    RETURN repo_id;
END;
$$ LANGUAGE plpgsql;

-- Function to simulate analysis results
CREATE OR REPLACE FUNCTION test_data.create_test_analysis(
    p_repository_id UUID,
    p_analysis_type VARCHAR(100),
    p_processing_time_ms INTEGER DEFAULT 1000,
    p_confidence_score DECIMAL(5,4) DEFAULT 0.85
) RETURNS UUID AS $$
DECLARE
    analysis_id UUID;
    test_results JSONB;
BEGIN
    -- Generate mock results based on analysis type
    CASE p_analysis_type
        WHEN 'security_scan' THEN
            test_results := '{"vulnerabilities": [], "security_score": 8.5, "scan_duration": ' || p_processing_time_ms || '}';
        WHEN 'quality_analysis' THEN
            test_results := '{"code_quality": 8.0, "maintainability": 7.5, "complexity": 12.3}';
        WHEN 'performance_analysis' THEN
            test_results := '{"performance_score": 7.8, "bottlenecks": [], "optimization_suggestions": []}';
        ELSE
            test_results := '{"analysis_type": "' || p_analysis_type || '", "score": ' || p_confidence_score * 10 || '}';
    END CASE;
    
    INSERT INTO analysis_results (repository_id, analysis_type, results, confidence_score, processing_time_ms, status)
    VALUES (p_repository_id, p_analysis_type, test_results, p_confidence_score, p_processing_time_ms, 'completed')
    RETURNING id INTO analysis_id;
    
    RETURN analysis_id;
END;
$$ LANGUAGE plpgsql;

-- Function to clean test data
CREATE OR REPLACE FUNCTION test_data.cleanup_test_data() RETURNS VOID AS $$
BEGIN
    DELETE FROM test_data.test_metrics;
    DELETE FROM test_data.test_scenarios;
    DELETE FROM quality_metrics WHERE repository_id IN (
        SELECT id FROM repositories WHERE name LIKE 'test-%'
    );
    DELETE FROM patterns WHERE repository_id IN (
        SELECT id FROM repositories WHERE name LIKE 'test-%'
    );
    DELETE FROM analysis_results WHERE repository_id IN (
        SELECT id FROM repositories WHERE name LIKE 'test-%'
    );
    DELETE FROM repositories WHERE name LIKE 'test-%';
END;
$$ LANGUAGE plpgsql;

-- =============================================================================
-- Performance Monitoring Setup
-- =============================================================================

-- Enable query performance tracking
SELECT pg_stat_statements_reset();

-- Create function to get query performance stats
CREATE OR REPLACE FUNCTION test_data.get_query_performance() 
RETURNS TABLE(
    query TEXT,
    calls BIGINT,
    total_time DOUBLE PRECISION,
    mean_time DOUBLE PRECISION,
    rows BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        pss.query,
        pss.calls,
        pss.total_exec_time,
        pss.mean_exec_time,
        pss.rows
    FROM pg_stat_statements pss
    WHERE pss.calls > 0
    ORDER BY pss.total_exec_time DESC
    LIMIT 20;
END;
$$ LANGUAGE plpgsql;

-- Grant permissions for test user
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA pattern_mining TO testuser;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA pattern_mining TO testuser;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA test_data TO testuser;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA test_data TO testuser;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA test_data TO testuser;

-- Final setup message
DO $$
BEGIN
    RAISE NOTICE 'Phase 2 Advanced Integration Testing Database initialized successfully!';
    RAISE NOTICE 'Created % repositories, % analysis results, % patterns, % quality metrics',
        (SELECT COUNT(*) FROM repositories),
        (SELECT COUNT(*) FROM analysis_results),
        (SELECT COUNT(*) FROM patterns),
        (SELECT COUNT(*) FROM quality_metrics);
END $$;