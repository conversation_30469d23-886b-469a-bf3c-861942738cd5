# Prometheus configuration for Phase 2 Advanced Integration Testing
global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    environment: 'integration-test'
    cluster: 'pattern-mining-test'

# Rule files for alerting
rule_files:
  - "alert_rules.yml"

# Scrape configurations
scrape_configs:
  # Prometheus self-monitoring
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 15s
    metrics_path: /metrics

  # Pattern Mining primary service
  - job_name: 'pattern-mining-primary'
    static_configs:
      - targets: ['pattern-mining-primary:8001']
    scrape_interval: 10s
    metrics_path: /metrics
    scrape_timeout: 5s
    honor_labels: true
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
        replacement: 'primary'
      - source_labels: [__address__]
        target_label: service
        replacement: 'pattern-mining'

  # Pattern Mining replica service
  - job_name: 'pattern-mining-replica'
    static_configs:
      - targets: ['pattern-mining-replica:8001']
    scrape_interval: 10s
    metrics_path: /metrics
    scrape_timeout: 5s
    honor_labels: true
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
        replacement: 'replica'
      - source_labels: [__address__]
        target_label: service
        replacement: 'pattern-mining'

  # PostgreSQL primary database
  - job_name: 'postgres-primary'
    static_configs:
      - targets: ['postgres-primary:5432']
    scrape_interval: 30s
    metrics_path: /metrics
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
        replacement: 'postgres-primary'
      - source_labels: [__address__]
        target_label: service
        replacement: 'postgresql'

  # PostgreSQL replica database
  - job_name: 'postgres-replica'
    static_configs:
      - targets: ['postgres-replica:5432']
    scrape_interval: 30s
    metrics_path: /metrics
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
        replacement: 'postgres-replica'
      - source_labels: [__address__]
        target_label: service
        replacement: 'postgresql'

  # Redis primary cache
  - job_name: 'redis-primary'
    static_configs:
      - targets: ['redis-primary:6379']
    scrape_interval: 30s
    metrics_path: /metrics
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
        replacement: 'redis-primary'
      - source_labels: [__address__]
        target_label: service
        replacement: 'redis'

  # Redis replica cache
  - job_name: 'redis-replica'
    static_configs:
      - targets: ['redis-replica:6379']
    scrape_interval: 30s
    metrics_path: /metrics
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
        replacement: 'redis-replica'
      - source_labels: [__address__]
        target_label: service
        replacement: 'redis'

  # External service simulators
  - job_name: 'gemini-simulator'
    static_configs:
      - targets: ['gemini-simulator:8080']
    scrape_interval: 30s
    metrics_path: /__admin/metrics
    relabel_configs:
      - source_labels: [__address__]
        target_label: service
        replacement: 'gemini-simulator'

  - job_name: 'bigquery-simulator'
    static_configs:
      - targets: ['bigquery-simulator:8080']
    scrape_interval: 30s
    metrics_path: /__admin/metrics
    relabel_configs:
      - source_labels: [__address__]
        target_label: service
        replacement: 'bigquery-simulator'

  # Network chaos and testing tools
  - job_name: 'toxiproxy'
    static_configs:
      - targets: ['toxiproxy:8474']
    scrape_interval: 30s
    metrics_path: /metrics
    relabel_configs:
      - source_labels: [__address__]
        target_label: service
        replacement: 'toxiproxy'

# Storage retention settings for testing
storage:
  tsdb:
    retention.time: 24h
    retention.size: 1GB

# Remote write for long-term storage (optional)
# remote_write:
#   - url: "http://remote-storage:9201/api/v1/write"
#     queue_config:
#       max_samples_per_send: 1000
#       max_shards: 10
#       capacity: 10000