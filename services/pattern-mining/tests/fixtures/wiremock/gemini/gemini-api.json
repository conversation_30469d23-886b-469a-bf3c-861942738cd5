{"mappings": [{"id": "gemini-generate-content", "name": "Gemini Generate Content - Success", "request": {"method": "POST", "urlPathPattern": "/v1beta/models/gemini-.*:generateContent", "headers": {"Content-Type": {"equalTo": "application/json"}}, "bodyPatterns": [{"matchesJsonPath": "$.contents"}]}, "response": {"status": 200, "headers": {"Content-Type": "application/json", "x-request-id": "{{randomValue type='UUID'}}"}, "bodyFileName": "gemini-success-response.json", "delayDistribution": {"type": "lognormal", "median": 500, "sigma": 0.1}}}, {"id": "gemini-generate-content-timeout", "name": "Gemini Generate Content - Timeout Simulation", "priority": 10, "request": {"method": "POST", "urlPathPattern": "/v1beta/models/gemini-.*:generateContent", "queryParameters": {"simulate": {"equalTo": "timeout"}}}, "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "bodyFileName": "gemini-success-response.json", "fixedDelayMilliseconds": 30000}}, {"id": "gemini-generate-content-rate-limit", "name": "Gemini Generate Content - Rate Limit", "priority": 5, "request": {"method": "POST", "urlPathPattern": "/v1beta/models/gemini-.*:generateContent", "queryParameters": {"simulate": {"equalTo": "rate_limit"}}}, "response": {"status": 429, "headers": {"Content-Type": "application/json", "Retry-After": "60"}, "bodyFileName": "gemini-rate-limit-response.json"}}, {"id": "gemini-generate-content-server-error", "name": "Gemini Generate Content - Server Error", "priority": 5, "request": {"method": "POST", "urlPathPattern": "/v1beta/models/gemini-.*:generateContent", "queryParameters": {"simulate": {"equalTo": "server_error"}}}, "response": {"status": 500, "headers": {"Content-Type": "application/json"}, "bodyFileName": "gemini-server-error-response.json"}}, {"id": "gemini-generate-content-partial-failure", "name": "Gemini Generate Content - Partial Content", "priority": 5, "request": {"method": "POST", "urlPathPattern": "/v1beta/models/gemini-.*:generateContent", "queryParameters": {"simulate": {"equalTo": "partial"}}}, "response": {"status": 206, "headers": {"Content-Type": "application/json"}, "bodyFileName": "gemini-partial-response.json"}}, {"id": "gemini-models-list", "name": "Gemini Models List", "request": {"method": "GET", "urlPathPattern": "/v1beta/models"}, "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "bodyFileName": "gemini-models-response.json"}}, {"id": "gemini-health-check", "name": "Gemini Health Check", "request": {"method": "GET", "urlPath": "/health"}, "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "body": "{\"status\": \"healthy\", \"timestamp\": \"{{now}}\", \"service\": \"gemini-simulator\"}"}}]}