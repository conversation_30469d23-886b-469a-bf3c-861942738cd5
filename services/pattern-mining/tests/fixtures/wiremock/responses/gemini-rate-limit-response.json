{"error": {"code": 429, "message": "Resource has been exhausted (e.g. check quota).", "status": "RESOURCE_EXHAUSTED", "details": [{"@type": "type.googleapis.com/google.rpc.ErrorInfo", "reason": "RATE_LIMIT_EXCEEDED", "domain": "googleapis.com", "metadata": {"service": "generativelanguage.googleapis.com", "quota_limit": "requests_per_minute", "quota_location": "us-central1"}}, {"@type": "type.googleapis.com/google.rpc.Help", "links": [{"description": "Request a higher quota limit", "url": "https://cloud.google.com/docs/quota"}]}]}}