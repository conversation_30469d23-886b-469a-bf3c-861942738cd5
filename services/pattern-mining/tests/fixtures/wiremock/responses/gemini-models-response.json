{"models": [{"name": "models/gemini-2.5-flash", "version": "2.5", "displayName": "Gemini 2.5 Flash", "description": "Fast and efficient model for code analysis and generation", "inputTokenLimit": 1048576, "outputTokenLimit": 8192, "supportedGenerationMethods": ["generateContent", "countTokens"], "temperature": 0.4, "topP": 0.95, "topK": 64}, {"name": "models/gemini-2.5-pro", "version": "2.5", "displayName": "Gemini 2.5 Pro", "description": "Advanced model for complex code analysis and reasoning", "inputTokenLimit": 2097152, "outputTokenLimit": 8192, "supportedGenerationMethods": ["generateContent", "countTokens"], "temperature": 0.2, "topP": 0.8, "topK": 40}]}