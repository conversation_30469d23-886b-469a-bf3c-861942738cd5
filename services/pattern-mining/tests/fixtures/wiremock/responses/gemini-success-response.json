{
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "{\n  \"analysis_results\": {\n    \"code_quality\": {\n      \"overall_score\": 8.5,\n      \"maintainability_index\": 82.3,\n      \"complexity_score\": 7.2,\n      \"documentation_coverage\": 0.78\n    },\n    \"security_analysis\": {\n      \"vulnerability_count\": 0,\n      \"security_score\": 9.1,\n      \"compliance_level\": \"high\",\n      \"recommendations\": [\n        \"Consider implementing additional input validation\",\n        \"Review authentication mechanisms\"\n      ]\n    },\n    \"performance_analysis\": {\n      \"performance_score\": 7.8,\n      \"bottlenecks\": [\n        {\n          \"type\": \"database_query\",\n          \"severity\": \"medium\",\n          \"location\": \"user_service.py:45\",\n          \"recommendation\": \"Add database indexing\"\n        }\n      ],\n      \"optimization_opportunities\": [\n        \"Implement caching for frequently accessed data\",\n        \"Consider async operations for I/O bound tasks\"\n      ]\n    },\n    \"architectural_patterns\": [\n      {\n        \"pattern_type\": \"MVC\",\n        \"confidence\": 0.92,\n        \"implementation_quality\": \"good\",\n        \"suggestions\": [\n          \"Separate business logic from controllers\"\n        ]\n      },\n      {\n        \"pattern_type\": \"Repository\",\n        \"confidence\": 0.85,\n        \"implementation_quality\": \"excellent\",\n        \"suggestions\": []\n      }\n    ],\n    \"test_coverage\": {\n      \"overall_coverage\": 0.87,\n      \"unit_test_coverage\": 0.92,\n      \"integration_test_coverage\": 0.78,\n      \"quality_assessment\": \"good\"\n    },\n    \"dependencies\": {\n      \"total_dependencies\": 45,\n      \"outdated_dependencies\": 3,\n      \"security_vulnerabilities\": 0,\n      \"license_compliance\": \"compliant\"\n    }\n  },\n  \"metadata\": {\n    \"analysis_timestamp\": \"{{now}}\",\n    \"model_version\": \"gemini-2.5-flash\",\n    \"processing_time_ms\": {{randomValue type='NUMBER' lower=200 upper=2000}},\n    \"confidence_score\": {{randomValue type='DECIMAL' lower=0.8 upper=0.98}},\n    \"analysis_id\": \"{{randomValue type='UUID'}}\"\n  }\n}"
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0,
      "safetyRatings": [
        {
          "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
          "probability": "NEGLIGIBLE"
        },
        {
          "category": "HARM_CATEGORY_HATE_SPEECH",
          "probability": "NEGLIGIBLE"
        },
        {
          "category": "HARM_CATEGORY_HARASSMENT",
          "probability": "NEGLIGIBLE"
        },
        {
          "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
          "probability": "NEGLIGIBLE"
        }
      ]
    }
  ],
  "usageMetadata": {
    "promptTokenCount": {{randomValue type='NUMBER' lower=100 upper=500}},
    "candidatesTokenCount": {{randomValue type='NUMBER' lower=800 upper=1200}},
    "totalTokenCount": {{randomValue type='NUMBER' lower=900 upper=1700}}
  }
}