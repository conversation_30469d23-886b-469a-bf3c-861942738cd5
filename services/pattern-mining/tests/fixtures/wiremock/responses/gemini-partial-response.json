{
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "{\n  \"analysis_results\": {\n    \"code_quality\": {\n      \"overall_score\": 6.2,\n      \"maintainability_index\": 65.1,\n      \"complexity_score\": 8.9,\n      \"documentation_coverage\": 0.45\n    },\n    \"security_analysis\": {\n      \"vulnerability_count\": 2,\n      \"security_score\": 7.3,\n      \"compliance_level\": \"medium\",\n      \"critical_issues\": [\n        \"SQL injection vulnerability detected in user_input.py:23\",\n        \"Hardcoded credentials found in config.py:15\"\n      ]\n    },\n    \"performance_analysis\": {\n      \"status\": \"incomplete\",\n      \"reason\": \"Analysis timed out due to large codebase\",\n      \"partial_results\": {\n        \"performance_score\": 5.8,\n        \"analyzed_files\": 156,\n        \"total_files\": 342\n      }\n    }\n  },\n  \"metadata\": {\n    \"analysis_timestamp\": \"{{now}}\",\n    \"model_version\": \"gemini-2.5-flash\",\n    \"processing_time_ms\": {{randomValue type='NUMBER' lower=8000 upper=15000}},\n    \"confidence_score\": {{randomValue type='DECIMAL' lower=0.6 upper=0.8}},\n    \"analysis_id\": \"{{randomValue type='UUID'}}\",\n    \"completion_status\": \"partial\",\n    \"completion_percentage\": {{randomValue type='NUMBER' lower=45 upper=75}}\n  }\n}"
          }
        ],
        "role": "model"
      },
      "finishReason": "MAX_TOKENS",
      "index": 0,
      "safetyRatings": [
        {
          "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
          "probability": "NEGLIGIBLE"
        },
        {
          "category": "HARM_CATEGORY_HATE_SPEECH",
          "probability": "NEGLIGIBLE"
        },
        {
          "category": "HARM_CATEGORY_HARASSMENT",
          "probability": "NEGLIGIBLE"
        },
        {
          "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
          "probability": "NEGLIGIBLE"
        }
      ]
    }
  ],
  "usageMetadata": {
    "promptTokenCount": {{randomValue type='NUMBER' lower=100 upper=500}},
    "candidatesTokenCount": {{randomValue type='NUMBER' lower=600 upper=900}},
    "totalTokenCount": {{randomValue type='NUMBER' lower=700 upper=1400}}
  }
}