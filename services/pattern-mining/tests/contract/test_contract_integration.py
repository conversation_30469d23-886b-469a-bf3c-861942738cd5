"""
CCL Contract Integration Tests

Wave 2.5: CCL Contract Compliance Implementation - Phase 5
Integration tests for contract-compliant pattern detection workflows.
"""

import pytest
import asyncio
import json
from datetime import datetime
from typing import Dict, Any, List
from unittest.mock import Mock, AsyncMock, patch
from pathlib import Path

from httpx import AsyncClient
from fastapi import Fast<PERSON><PERSON>
from fastapi.testclient import TestClient

from pattern_mining.contracts.models import (
    PatternInputV1,
    PatternOutputV1,
    DetectedPatternV1,
    ASTDataV1,
    FileASTDataV1,
    DetectionConfigV1,
    PatternTypeV1,
    SeverityLevelV1,
)
from pattern_mining.ast_processing import (
    ASTDataProcessor,
    EnhancedPatternDetector,
    ProcessedASTData,
)
from pattern_mining.api.v1.patterns import detect_patterns
from pattern_mining.api.main import create_app


@pytest.mark.contract
@pytest.mark.integration
class TestContractAPIIntegration:
    """Test contract-compliant API integration."""
    
    @pytest.fixture
    def app(self):
        """Create test FastAPI app."""
        return create_app()
    
    @pytest.fixture
    def client(self, app):
        """Create test client."""
        return TestClient(app)
    
    @pytest.fixture
    async def async_client(self, app):
        """Create async test client."""
        async with AsyncClient(app=app, base_url="http://test") as client:
            yield client
    
    @pytest.fixture
    def sample_ast_data(self):
        """Create sample AST data for testing."""
        return ASTDataV1(
            repository_id="repo_test1234567890ab",
            commit_hash="abc123def456",
            files=[
                FileASTDataV1(
                    path="src/main.py",
                    language="python",
                    ast_nodes=[
                        {
                            "type": "class_definition",
                            "name": "UserService",
                            "start_line": 10,
                            "end_line": 150,
                            "children": [
                                {
                                    "type": "function_definition",
                                    "name": "__init__",
                                    "start_line": 12,
                                    "end_line": 20,
                                },
                                {
                                    "type": "function_definition",
                                    "name": "get_user",
                                    "start_line": 22,
                                    "end_line": 35,
                                    "children": [
                                        {
                                            "type": "string_literal",
                                            "value": "SELECT * FROM users WHERE id = " + "{}",
                                            "start_line": 28,
                                            "end_line": 28,
                                        }
                                    ]
                                },
                                {
                                    "type": "function_definition", 
                                    "name": "create_user",
                                    "start_line": 37,
                                    "end_line": 60,
                                }
                            ]
                        }
                    ],
                    symbols=[
                        {
                            "name": "UserService",
                            "type": "class",
                            "line": 10,
                            "scope": "global"
                        },
                        {
                            "name": "__init__",
                            "type": "function",
                            "line": 12,
                            "scope": "UserService"
                        },
                        {
                            "name": "get_user",
                            "type": "function",
                            "line": 22,
                            "scope": "UserService"
                        }
                    ],
                    imports=[
                        {
                            "module": "typing",
                            "names": ["List", "Dict", "Optional"],
                            "line": 1
                        },
                        {
                            "module": "sqlalchemy",
                            "names": ["create_engine", "Session"],
                            "line": 2
                        }
                    ],
                    metrics={
                        "lines_of_code": 150,
                        "cyclomatic_complexity": 12,
                        "cognitive_complexity": 15,
                        "max_nesting_depth": 3
                    }
                ),
                FileASTDataV1(
                    path="src/utils.py",
                    language="python",
                    ast_nodes=[
                        {
                            "type": "function_definition",
                            "name": "validate_input",
                            "start_line": 5,
                            "end_line": 15,
                        }
                    ],
                    symbols=[
                        {
                            "name": "validate_input",
                            "type": "function",
                            "line": 5,
                            "scope": "global"
                        }
                    ],
                    imports=[],
                    metrics={
                        "lines_of_code": 50,
                        "cyclomatic_complexity": 3,
                        "cognitive_complexity": 2,
                        "max_nesting_depth": 1
                    }
                )
            ],
            metadata={
                "total_files": 2,
                "languages": {"python": 2},
                "analysis_timestamp": datetime.utcnow().isoformat()
            }
        )
    
    @pytest.mark.asyncio
    async def test_detect_patterns_endpoint(self, async_client, sample_ast_data):
        """Test /api/v1/patterns/detect endpoint with contract compliance."""
        # Prepare request
        request_data = {
            "repository_id": "repo_test1234567890ab",
            "analysis_id": "analysis_test123456",
            "request_id": "req_test1234567890ab",
            "ast_data": sample_ast_data.dict(),
            "detection_config": {
                "pattern_types": ["all"],
                "min_confidence": 0.7,
                "include_metrics": True,
                "include_recommendations": True,
                "timeout_seconds": 30
            },
            "metadata": {
                "test": True,
                "test_id": "integration_test_001"
            }
        }
        
        # Mock the pattern detection
        with patch("pattern_mining.api.v1.patterns.get_pattern_service") as mock_service:
            # Create mock pattern service
            mock_pattern_service = AsyncMock()
            mock_service.return_value = mock_pattern_service
            
            # Mock AST processing
            mock_pattern_service.ast_processor.process_ast_data.return_value = ProcessedASTData(
                repository_id="repo_test1234567890ab",
                analysis_id="analysis_test123456",
                files=[],  # Simplified for test
                total_features_extracted=100,
                processing_time_ms=50.0
            )
            
            # Mock pattern detection
            mock_pattern_service.pattern_detector.detect_patterns.return_value = [
                {
                    "pattern_id": "pat_detected12345678",
                    "pattern_type": PatternTypeV1.SECURITY_VULNERABILITY,
                    "pattern_name": "SQL Injection",
                    "confidence": 0.95,
                    "severity": SeverityLevelV1.CRITICAL,
                    "file_path": "src/main.py",
                    "line_start": 28,
                    "line_end": 28,
                    "description": "Potential SQL injection vulnerability",
                    "recommendation": "Use parameterized queries",
                    "tags": ["security", "sql", "injection"],
                    "metadata": {
                        "vulnerability_type": "sql_injection",
                        "cwe_id": "CWE-89"
                    }
                },
                {
                    "pattern_id": "pat_detected87654321",
                    "pattern_type": PatternTypeV1.DESIGN_PATTERN,
                    "pattern_name": "Service Pattern",
                    "confidence": 0.88,
                    "severity": SeverityLevelV1.INFO,
                    "file_path": "src/main.py",
                    "line_start": 10,
                    "line_end": 150,
                    "description": "Service pattern implementation",
                    "recommendation": "Well-structured service pattern",
                    "tags": ["design", "service", "architecture"],
                    "metadata": {
                        "class_name": "UserService"
                    }
                }
            ]
            
            # Send request
            response = await async_client.post(
                "/api/v1/patterns/detect",
                json=request_data
            )
        
        # Verify response
        assert response.status_code == 200
        data = response.json()
        
        # Verify contract compliance
        assert "repository_id" in data
        assert data["repository_id"] == "repo_test1234567890ab"
        assert "analysis_id" in data
        assert data["analysis_id"] == "analysis_test123456"
        assert "request_id" in data
        assert "patterns" in data
        assert "summary" in data
        assert "processing_time_ms" in data
        assert "model_version" in data
        assert "timestamp" in data
        
        # Verify patterns
        patterns = data["patterns"]
        assert len(patterns) == 2
        
        # Verify security pattern
        security_pattern = next(p for p in patterns if p["pattern_type"] == "security_vulnerability")
        assert security_pattern["pattern_name"] == "SQL Injection"
        assert security_pattern["confidence"] == 0.95
        assert security_pattern["severity"] == "critical"
        assert security_pattern["file_path"] == "src/main.py"
        
        # Verify design pattern
        design_pattern = next(p for p in patterns if p["pattern_type"] == "design_pattern")
        assert design_pattern["pattern_name"] == "Service Pattern"
        assert design_pattern["confidence"] == 0.88
        assert design_pattern["severity"] == "info"
        
        # Verify summary
        summary = data["summary"]
        assert summary["total_patterns"] == 2
        assert summary["patterns_by_type"]["security_vulnerability"] == 1
        assert summary["patterns_by_type"]["design_pattern"] == 1
        assert summary["patterns_by_severity"]["critical"] == 1
        assert summary["patterns_by_severity"]["info"] == 1
        assert summary["quality_score"] < 100  # Should be reduced due to security issue
    
    @pytest.mark.asyncio
    async def test_health_check_endpoint(self, async_client):
        """Test health check endpoint."""
        response = await async_client.get("/api/v1/health")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "status" in data
        assert "timestamp" in data
        assert "version" in data
        assert "uptime_seconds" in data
        assert "checks" in data
    
    @pytest.mark.asyncio
    async def test_readiness_check_endpoint(self, async_client):
        """Test readiness check endpoint."""
        response = await async_client.get("/api/v1/ready")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "ready" in data
        assert "timestamp" in data
        assert "checks" in data
    
    @pytest.mark.asyncio
    async def test_error_handling(self, async_client):
        """Test contract-compliant error handling."""
        # Test validation error
        invalid_request = {
            "repository_id": "invalid-format",  # Invalid ID format
            "analysis_id": "analysis_test123456",
            "ast_data": {
                "repository_id": "repo_test1234567890ab",
                "commit_hash": "abc123",
                "files": [],
                "metadata": {}
            },
            "detection_config": {
                "pattern_types": ["invalid_type"],  # Invalid pattern type
                "min_confidence": 1.5  # Invalid confidence
            }
        }
        
        response = await async_client.post("/api/v1/patterns/detect", json=invalid_request)
        
        assert response.status_code == 422
        data = response.json()
        
        # Verify error response structure
        assert "detail" in data  # FastAPI validation error format
        errors = data["detail"]
        assert isinstance(errors, list)
        assert len(errors) > 0
        
        # Should have errors for repository_id, pattern_types, and min_confidence
        error_fields = [error["loc"][-1] for error in errors]
        assert "repository_id" in error_fields
        assert "min_confidence" in error_fields
    
    @pytest.mark.asyncio
    async def test_request_id_tracking(self, async_client, sample_ast_data):
        """Test request ID tracking through the workflow."""
        # Send request with custom request ID
        request_data = {
            "repository_id": "repo_test1234567890ab",
            "analysis_id": "analysis_test123456",
            "request_id": "req_custom1234567890",
            "ast_data": sample_ast_data.dict(),
            "detection_config": {
                "pattern_types": ["all"],
                "min_confidence": 0.7
            }
        }
        
        with patch("pattern_mining.api.v1.patterns.get_pattern_service") as mock_service:
            mock_pattern_service = AsyncMock()
            mock_service.return_value = mock_pattern_service
            
            mock_pattern_service.ast_processor.process_ast_data.return_value = ProcessedASTData(
                repository_id="repo_test1234567890ab",
                analysis_id="analysis_test123456",
                files=[],
                total_features_extracted=50,
                processing_time_ms=25.0
            )
            
            mock_pattern_service.pattern_detector.detect_patterns.return_value = []
            
            response = await async_client.post(
                "/api/v1/patterns/detect",
                json=request_data,
                headers={"X-Request-ID": "req_header123456789"}
            )
        
        assert response.status_code == 200
        
        # Check response headers
        assert "X-Request-ID" in response.headers
        
        # Check response body
        data = response.json()
        assert data["request_id"] == "req_custom1234567890"  # Should use the one from request body


@pytest.mark.contract
@pytest.mark.integration
class TestASTProcessingIntegration:
    """Test AST processing integration with contract compliance."""
    
    @pytest.fixture
    def ast_processor(self):
        """Create AST processor instance."""
        return ASTDataProcessor()
    
    @pytest.fixture
    def pattern_detector(self):
        """Create pattern detector instance."""
        return EnhancedPatternDetector()
    
    @pytest.mark.asyncio
    async def test_ast_processing_pipeline(self, ast_processor, sample_ast_data):
        """Test complete AST processing pipeline."""
        # Process AST data
        processed_data = await ast_processor.process_ast_data(
            ast_data=sample_ast_data,
            repository_id="repo_test1234567890ab",
            analysis_id="analysis_test123456"
        )
        
        # Verify processed data
        assert processed_data.repository_id == "repo_test1234567890ab"
        assert processed_data.analysis_id == "analysis_test123456"
        assert len(processed_data.files) == 2
        assert processed_data.total_features_extracted > 0
        assert processed_data.processing_time_ms > 0
        
        # Verify file processing
        for file_data in processed_data.files:
            assert file_data.path in ["src/main.py", "src/utils.py"]
            assert file_data.language == "python"
            assert "structural" in file_data.features
            assert "semantic" in file_data.features
            assert "code" in file_data.features
            assert file_data.ast_summary is not None
    
    @pytest.mark.asyncio
    async def test_pattern_detection_from_ast(self, pattern_detector, ast_processor, sample_ast_data):
        """Test pattern detection from processed AST data."""
        # Process AST data first
        processed_data = await ast_processor.process_ast_data(
            ast_data=sample_ast_data,
            repository_id="repo_test1234567890ab",
            analysis_id="analysis_test123456"
        )
        
        # Detect patterns in each file
        all_patterns = []
        
        for file_data in processed_data.files:
            detection_config = DetectionConfigV1(
                pattern_types=["all"],
                min_confidence=0.1,  # Low threshold to ensure we get patterns
                include_metrics=True
            )
            
            patterns = await pattern_detector.detect_patterns(
                file_data=file_data,
                detection_config=detection_config
            )
            
            all_patterns.extend(patterns)
        
        # Should detect some patterns
        assert len(all_patterns) > 0
        
        # Verify pattern structure
        for pattern in all_patterns:
            assert "pattern_id" in pattern
            assert pattern["pattern_id"].startswith("pat_")
            assert "pattern_type" in pattern
            assert "pattern_name" in pattern
            assert "confidence" in pattern
            assert 0 <= pattern["confidence"] <= 1
            assert "severity" in pattern
            assert "file_path" in pattern
            assert "line_start" in pattern
            assert "line_end" in pattern
            assert "description" in pattern
    
    @pytest.mark.asyncio
    async def test_concurrent_ast_processing(self, ast_processor):
        """Test concurrent AST processing for multiple files."""
        # Create multiple AST data instances
        ast_data_list = []
        for i in range(5):
            ast_data = ASTDataV1(
                repository_id=f"repo_concurrent{i:08d}test",
                commit_hash=f"commit{i}",
                files=[
                    FileASTDataV1(
                        path=f"src/file_{i}.py",
                        language="python",
                        ast_nodes=[
                            {
                                "type": "function_definition",
                                "name": f"function_{i}",
                                "start_line": 1,
                                "end_line": 10,
                            }
                        ],
                        symbols=[
                            {
                                "name": f"function_{i}",
                                "type": "function",
                                "line": 1,
                                "scope": "global"
                            }
                        ],
                        imports=[],
                        metrics={
                            "lines_of_code": 50,
                            "cyclomatic_complexity": 2
                        }
                    )
                ],
                metadata={}
            )
            ast_data_list.append(ast_data)
        
        # Process concurrently
        tasks = []
        for i, ast_data in enumerate(ast_data_list):
            task = ast_processor.process_ast_data(
                ast_data=ast_data,
                repository_id=f"repo_concurrent{i:08d}test",
                analysis_id=f"analysis_concurrent{i:04d}"
            )
            tasks.append(task)
        
        # Wait for all to complete
        results = await asyncio.gather(*tasks)
        
        # Verify all processed successfully
        assert len(results) == 5
        for i, result in enumerate(results):
            assert result.repository_id == f"repo_concurrent{i:08d}test"
            assert len(result.files) == 1
            assert result.files[0].path == f"src/file_{i}.py"


@pytest.mark.contract
@pytest.mark.integration
class TestContractValidationIntegration:
    """Test contract validation integration."""
    
    @pytest.mark.asyncio
    async def test_input_validation_integration(self):
        """Test input validation with contract schemas."""
        from pattern_mining.contracts.validators import validate_pattern_input
        
        # Create valid input
        valid_input = PatternInputV1(
            repository_id="repo_valid1234567890",
            analysis_id="analysis_valid123456",
            ast_data=ASTDataV1(
                repository_id="repo_valid1234567890",
                commit_hash="abc123",
                files=[
                    FileASTDataV1(
                        path="test.py",
                        language="python",
                        ast_nodes=[],
                        symbols=[],
                        imports=[],
                        metrics={"lines_of_code": 10}
                    )
                ],
                metadata={}
            ),
            detection_config=DetectionConfigV1(
                pattern_types=["all"],
                min_confidence=0.7
            )
        )
        
        # Should pass validation
        is_valid, errors = validate_pattern_input(valid_input)
        assert is_valid is True
        assert len(errors) == 0
        
        # Test with invalid data
        invalid_input = PatternInputV1(
            repository_id="invalid",  # Wrong format
            analysis_id="analysis_valid123456",
            ast_data=ASTDataV1(
                repository_id="repo_valid1234567890",
                commit_hash="abc123",
                files=[],
                metadata={}
            ),
            detection_config=DetectionConfigV1(
                pattern_types=["all"],
                min_confidence=2.0  # Invalid confidence
            )
        )
        
        # Should fail validation
        is_valid, errors = validate_pattern_input(invalid_input)
        assert is_valid is False
        assert len(errors) > 0
    
    @pytest.mark.asyncio
    async def test_output_validation_integration(self):
        """Test output validation with contract schemas."""
        from pattern_mining.contracts.validators import validate_pattern_output
        from pattern_mining.contracts.models import PatternSummaryV1
        
        # Create valid output
        valid_output = PatternOutputV1(
            repository_id="repo_valid1234567890",
            analysis_id="analysis_valid123456",
            patterns=[
                DetectedPatternV1(
                    pattern_id="pat_valid1234567890",
                    pattern_type="code_smell",
                    pattern_name="Long Method",
                    confidence=0.85,
                    severity="medium",
                    file_path="test.py",
                    line_start=10,
                    line_end=50,
                    description="Method is too long"
                )
            ],
            summary=PatternSummaryV1(
                total_patterns=1,
                patterns_by_type={"code_smell": 1},
                patterns_by_severity={"medium": 1},
                patterns_by_file={"test.py": 1},
                quality_score=75.0,
                confidence_stats={
                    "mean": 0.85,
                    "min": 0.85,
                    "max": 0.85
                }
            ),
            processing_time_ms=100.0,
            model_version="1.0.0",
            timestamp=datetime.utcnow()
        )
        
        # Should pass validation
        is_valid, errors = validate_pattern_output(valid_output)
        assert is_valid is True
        assert len(errors) == 0
    
    @pytest.mark.asyncio
    async def test_error_response_integration(self):
        """Test error response creation and validation."""
        from pattern_mining.contracts.validators import create_contract_compliant_error
        
        # Create different types of errors
        validation_error = create_contract_compliant_error(
            service="pattern-mining",
            error_type="validation",
            message="Invalid repository ID format",
            retryable=False,
            user_message="Please provide a valid repository ID in the format repo_[16 characters]",
            correlation_id="req_test1234567890ab",
            context={
                "field": "repository_id",
                "provided": "bad-id",
                "expected": "repo_[16 alphanumeric]"
            }
        )
        
        # Verify error structure
        assert validation_error.error_id.startswith("err_")
        assert validation_error.service == "pattern-mining"
        assert validation_error.error_type == "validation"
        assert validation_error.retryable is False
        assert validation_error.correlation_id == "req_test1234567890ab"
        
        # Test serialization
        error_dict = validation_error.dict()
        assert all(key in error_dict for key in ["error_id", "service", "error_type", "message", "timestamp"])
        
        # Create internal error
        internal_error = create_contract_compliant_error(
            service="pattern-mining",
            error_type="internal",
            message="Database connection failed",
            retryable=True,
            user_message="Service temporarily unavailable. Please try again."
        )
        
        assert internal_error.error_type == "internal"
        assert internal_error.retryable is True


@pytest.mark.contract
@pytest.mark.integration 
class TestEndToEndWorkflow:
    """Test complete end-to-end workflow with contract compliance."""
    
    @pytest.mark.asyncio
    async def test_complete_pattern_detection_workflow(self):
        """Test complete workflow from AST input to pattern output."""
        # Initialize components
        ast_processor = ASTDataProcessor()
        pattern_detector = EnhancedPatternDetector()
        
        # Create comprehensive AST data
        ast_data = ASTDataV1(
            repository_id="repo_e2e12345678test",
            commit_hash="e2ecommit123",
            files=[
                FileASTDataV1(
                    path="src/auth/login.py",
                    language="python",
                    ast_nodes=[
                        {
                            "type": "function_definition",
                            "name": "login",
                            "start_line": 10,
                            "end_line": 50,
                            "children": [
                                {
                                    "type": "string_literal",
                                    "value": "SELECT * FROM users WHERE username = '" + "' AND password = '",
                                    "start_line": 25,
                                    "end_line": 25,
                                }
                            ]
                        }
                    ],
                    symbols=[
                        {
                            "name": "login",
                            "type": "function",
                            "line": 10,
                            "scope": "global"
                        }
                    ],
                    imports=[
                        {
                            "module": "flask",
                            "names": ["request", "session"],
                            "line": 1
                        }
                    ],
                    metrics={
                        "lines_of_code": 100,
                        "cyclomatic_complexity": 8,
                        "cognitive_complexity": 10
                    }
                ),
                FileASTDataV1(
                    path="src/models/user.py",
                    language="python",
                    ast_nodes=[
                        {
                            "type": "class_definition",
                            "name": "User",
                            "start_line": 5,
                            "end_line": 100,
                            "children": [
                                {
                                    "type": "function_definition",
                                    "name": "__init__",
                                    "start_line": 10,
                                    "end_line": 15,
                                }
                            ]
                        }
                    ],
                    symbols=[
                        {
                            "name": "User",
                            "type": "class",
                            "line": 5,
                            "scope": "global"
                        }
                    ],
                    imports=[
                        {
                            "module": "sqlalchemy",
                            "names": ["Column", "String", "Integer"],
                            "line": 1
                        }
                    ],
                    metrics={
                        "lines_of_code": 150,
                        "cyclomatic_complexity": 5,
                        "cognitive_complexity": 4
                    }
                )
            ],
            metadata={
                "total_files": 2,
                "languages": {"python": 2}
            }
        )
        
        # Create detection config
        detection_config = DetectionConfigV1(
            pattern_types=["all"],
            min_confidence=0.1,
            include_metrics=True,
            include_recommendations=True,
            timeout_seconds=30
        )
        
        # Process AST data
        processed_data = await ast_processor.process_ast_data(
            ast_data=ast_data,
            repository_id="repo_e2e12345678test",
            analysis_id="analysis_e2e123456"
        )
        
        # Detect patterns
        all_patterns = []
        for file_data in processed_data.files:
            patterns = await pattern_detector.detect_patterns(
                file_data=file_data,
                detection_config=detection_config
            )
            all_patterns.extend(patterns)
        
        # Create output
        from pattern_mining.contracts.models import PatternSummaryV1
        
        # Convert patterns to DetectedPatternV1
        detected_patterns = []
        for pattern in all_patterns:
            detected_pattern = DetectedPatternV1(
                pattern_id=pattern["pattern_id"],
                pattern_type=pattern["pattern_type"],
                pattern_name=pattern["pattern_name"],
                confidence=pattern["confidence"],
                severity=pattern["severity"],
                file_path=pattern["file_path"],
                line_start=pattern["line_start"],
                line_end=pattern["line_end"],
                column_start=pattern.get("column_start"),
                column_end=pattern.get("column_end"),
                description=pattern["description"],
                recommendation=pattern.get("recommendation"),
                tags=pattern.get("tags", []),
                metadata=pattern.get("metadata", {})
            )
            detected_patterns.append(detected_pattern)
        
        # Calculate summary
        patterns_by_type = {}
        patterns_by_severity = {}
        patterns_by_file = {}
        confidence_values = []
        
        for pattern in detected_patterns:
            # Count by type
            pattern_type = pattern.pattern_type.value
            patterns_by_type[pattern_type] = patterns_by_type.get(pattern_type, 0) + 1
            
            # Count by severity
            severity = pattern.severity.value
            patterns_by_severity[severity] = patterns_by_severity.get(severity, 0) + 1
            
            # Count by file
            file_path = pattern.file_path
            patterns_by_file[file_path] = patterns_by_file.get(file_path, 0) + 1
            
            # Collect confidence values
            confidence_values.append(pattern.confidence)
        
        # Calculate quality score (reduced for security issues)
        quality_score = 100.0
        if patterns_by_severity.get("critical", 0) > 0:
            quality_score -= 20.0
        if patterns_by_severity.get("high", 0) > 0:
            quality_score -= 10.0
        if patterns_by_severity.get("medium", 0) > 0:
            quality_score -= 5.0
        
        # Create output
        pattern_output = PatternOutputV1(
            repository_id="repo_e2e12345678test",
            analysis_id="analysis_e2e123456",
            patterns=detected_patterns,
            summary=PatternSummaryV1(
                total_patterns=len(detected_patterns),
                patterns_by_type=patterns_by_type,
                patterns_by_severity=patterns_by_severity,
                patterns_by_file=patterns_by_file,
                quality_score=max(0.0, quality_score),
                confidence_stats={
                    "mean": sum(confidence_values) / len(confidence_values) if confidence_values else 0.0,
                    "min": min(confidence_values) if confidence_values else 0.0,
                    "max": max(confidence_values) if confidence_values else 0.0
                }
            ),
            processing_time_ms=processed_data.processing_time_ms + 50.0,  # Add detection time
            model_version="1.0.0",
            timestamp=datetime.utcnow()
        )
        
        # Validate output
        from pattern_mining.contracts.validators import validate_pattern_output
        is_valid, errors = validate_pattern_output(pattern_output)
        assert is_valid is True
        assert len(errors) == 0
        
        # Verify we detected patterns
        assert pattern_output.summary.total_patterns > 0
        
        # Should detect SQL injection in login.py
        sql_injection_patterns = [
            p for p in pattern_output.patterns 
            if p.pattern_type == PatternTypeV1.SECURITY_VULNERABILITY and "sql" in p.pattern_name.lower()
        ]
        assert len(sql_injection_patterns) > 0