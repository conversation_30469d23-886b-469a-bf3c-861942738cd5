"""
CCL Contract Compliance Tests

Wave 2.5: CCL Contract Compliance Implementation - Phase 5
Tests for validating compliance with CCL contract schemas and requirements.
"""

import pytest
import json
from datetime import datetime
from typing import Dict, Any, List
from pathlib import Path

from pattern_mining.contracts.models import (
    PatternInputV1,
    PatternOutputV1,
    DetectedPatternV1,
    PatternSummaryV1,
    ASTDataV1,
    FileASTDataV1,
    DetectionConfigV1,
    <PERSON>ternTypeV1,
    <PERSON>verityLevelV1,
    ErrorResponseV1,
)
from pattern_mining.contracts.validators import (
    ContractSchemaValidator,
    validate_pattern_input,
    validate_pattern_output,
    create_contract_compliant_error,
)


@pytest.mark.contract
class TestContractModels:
    """Test CCL contract model compliance."""
    
    def test_pattern_input_v1_validation(self):
        """Test PatternInputV1 model validation."""
        # Valid input
        valid_input = {
            "repository_id": "repo_1234567890abcdef",
            "analysis_id": "analysis_123456789012",
            "request_id": "req_abcdef0123456789",
            "ast_data": {
                "repository_id": "repo_1234567890abcdef",
                "commit_hash": "abc123",
                "files": [
                    {
                        "path": "src/main.py",
                        "language": "python",
                        "ast_nodes": [
                            {
                                "type": "class_definition",
                                "name": "TestClass",
                                "start_line": 1,
                                "end_line": 10,
                            }
                        ],
                        "symbols": [
                            {
                                "name": "TestClass",
                                "type": "class",
                                "line": 1,
                            }
                        ],
                        "imports": [],
                        "metrics": {
                            "lines_of_code": 100,
                            "cyclomatic_complexity": 5,
                        }
                    }
                ],
                "metadata": {}
            },
            "detection_config": {
                "pattern_types": ["all"],
                "min_confidence": 0.7,
                "include_metrics": True,
                "timeout_seconds": 30,
            }
        }
        
        # Should create valid model
        model = PatternInputV1(**valid_input)
        assert model.repository_id == "repo_1234567890abcdef"
        assert model.analysis_id == "analysis_123456789012"
        assert model.request_id == "req_abcdef0123456789"
        assert len(model.ast_data.files) == 1
        assert model.detection_config.min_confidence == 0.7
        
        # Test invalid repository_id format
        invalid_input = valid_input.copy()
        invalid_input["repository_id"] = "invalid-repo-id"
        with pytest.raises(ValueError) as exc_info:
            PatternInputV1(**invalid_input)
        assert "repository_id" in str(exc_info.value)
        
        # Test invalid analysis_id format
        invalid_input = valid_input.copy()
        invalid_input["analysis_id"] = "bad_analysis_id"
        with pytest.raises(ValueError) as exc_info:
            PatternInputV1(**invalid_input)
        assert "analysis_id" in str(exc_info.value)
        
        # Test invalid confidence threshold
        invalid_input = valid_input.copy()
        invalid_input["detection_config"]["min_confidence"] = 1.5
        with pytest.raises(ValueError) as exc_info:
            PatternInputV1(**invalid_input)
        assert "min_confidence" in str(exc_info.value)
    
    def test_pattern_output_v1_validation(self):
        """Test PatternOutputV1 model validation."""
        # Valid output
        valid_output = {
            "repository_id": "repo_1234567890abcdef",
            "analysis_id": "analysis_123456789012",
            "request_id": "req_abcdef0123456789",
            "patterns": [
                {
                    "pattern_id": "pat_1234567890abcdef",
                    "pattern_type": "design_pattern",
                    "pattern_name": "Iterator Pattern",
                    "confidence": 0.92,
                    "severity": "low",
                    "file_path": "src/main.py",
                    "line_start": 10,
                    "line_end": 20,
                    "description": "Iterator pattern implementation",
                    "recommendation": "Consider using built-in iterators",
                    "tags": ["design", "iterator"],
                    "metadata": {
                        "detection_method": "ml_model",
                        "model_version": "1.0.0"
                    }
                }
            ],
            "summary": {
                "total_patterns": 1,
                "patterns_by_type": {
                    "design_pattern": 1
                },
                "patterns_by_severity": {
                    "low": 1
                },
                "patterns_by_file": {
                    "src/main.py": 1
                },
                "quality_score": 85.5,
                "confidence_stats": {
                    "mean": 0.92,
                    "min": 0.92,
                    "max": 0.92
                }
            },
            "processing_time_ms": 150.5,
            "model_version": "1.0.0",
            "timestamp": datetime.utcnow()
        }
        
        # Should create valid model
        model = PatternOutputV1(**valid_output)
        assert model.repository_id == "repo_1234567890abcdef"
        assert len(model.patterns) == 1
        assert model.patterns[0].pattern_type == PatternTypeV1.DESIGN_PATTERN
        assert model.summary.total_patterns == 1
        assert model.processing_time_ms == 150.5
        
        # Test pattern validation
        pattern = model.patterns[0]
        assert pattern.pattern_id == "pat_1234567890abcdef"
        assert pattern.confidence == 0.92
        assert pattern.severity == SeverityLevelV1.LOW
        
        # Test invalid pattern_id format
        invalid_output = valid_output.copy()
        invalid_output["patterns"][0]["pattern_id"] = "invalid-pattern"
        with pytest.raises(ValueError) as exc_info:
            PatternOutputV1(**invalid_output)
        assert "pattern_id" in str(exc_info.value)
    
    def test_detected_pattern_v1_validation(self):
        """Test DetectedPatternV1 model validation."""
        # Valid pattern
        valid_pattern = {
            "pattern_id": "pat_1234567890abcdef",
            "pattern_type": "security_vulnerability",
            "pattern_name": "SQL Injection",
            "confidence": 0.95,
            "severity": "critical",
            "file_path": "src/database.py",
            "line_start": 25,
            "line_end": 30,
            "column_start": 8,
            "column_end": 45,
            "description": "Potential SQL injection vulnerability",
            "recommendation": "Use parameterized queries",
            "tags": ["security", "sql", "injection"],
            "metadata": {
                "vulnerability_type": "sql_injection",
                "cwe_id": "CWE-89"
            }
        }
        
        # Should create valid model
        pattern = DetectedPatternV1(**valid_pattern)
        assert pattern.pattern_type == PatternTypeV1.SECURITY_VULNERABILITY
        assert pattern.severity == SeverityLevelV1.CRITICAL
        assert pattern.confidence == 0.95
        assert "security" in pattern.tags
        
        # Test all pattern types
        for pattern_type in PatternTypeV1:
            pattern_data = valid_pattern.copy()
            pattern_data["pattern_type"] = pattern_type.value
            pattern = DetectedPatternV1(**pattern_data)
            assert pattern.pattern_type == pattern_type
        
        # Test all severity levels
        for severity in SeverityLevelV1:
            pattern_data = valid_pattern.copy()
            pattern_data["severity"] = severity.value
            pattern = DetectedPatternV1(**pattern_data)
            assert pattern.severity == severity
        
        # Test invalid confidence
        invalid_pattern = valid_pattern.copy()
        invalid_pattern["confidence"] = 1.5
        with pytest.raises(ValueError) as exc_info:
            DetectedPatternV1(**invalid_pattern)
        assert "confidence" in str(exc_info.value)
    
    def test_ast_data_v1_validation(self):
        """Test ASTDataV1 model validation."""
        # Valid AST data
        valid_ast = {
            "repository_id": "repo_1234567890abcdef",
            "commit_hash": "abc123def456",
            "files": [
                {
                    "path": "src/main.py",
                    "language": "python",
                    "ast_nodes": [
                        {
                            "type": "function_definition",
                            "name": "process_data",
                            "start_line": 10,
                            "end_line": 25,
                            "children": [
                                {
                                    "type": "if_statement",
                                    "start_line": 12,
                                    "end_line": 15,
                                }
                            ]
                        }
                    ],
                    "symbols": [
                        {
                            "name": "process_data",
                            "type": "function",
                            "line": 10,
                            "scope": "global"
                        }
                    ],
                    "imports": [
                        {
                            "module": "pandas",
                            "names": ["DataFrame"],
                            "line": 1
                        }
                    ],
                    "metrics": {
                        "lines_of_code": 250,
                        "cyclomatic_complexity": 8,
                        "cognitive_complexity": 12,
                        "max_nesting_depth": 3
                    }
                }
            ],
            "metadata": {
                "total_files": 1,
                "languages": {"python": 1},
                "analysis_timestamp": datetime.utcnow().isoformat()
            }
        }
        
        # Should create valid model
        ast_data = ASTDataV1(**valid_ast)
        assert ast_data.repository_id == "repo_1234567890abcdef"
        assert len(ast_data.files) == 1
        assert ast_data.files[0].language == "python"
        assert ast_data.files[0].metrics["lines_of_code"] == 250
        
        # Test file AST validation
        file_ast = ast_data.files[0]
        assert len(file_ast.ast_nodes) == 1
        assert file_ast.ast_nodes[0]["type"] == "function_definition"
        assert len(file_ast.symbols) == 1
        assert file_ast.symbols[0]["name"] == "process_data"
    
    def test_detection_config_v1_validation(self):
        """Test DetectionConfigV1 model validation."""
        # Valid config
        valid_config = {
            "pattern_types": ["design_pattern", "anti_pattern", "security_vulnerability"],
            "min_confidence": 0.8,
            "max_patterns_per_file": 100,
            "include_metrics": True,
            "include_recommendations": True,
            "timeout_seconds": 30,
            "parallel_processing": True,
            "custom_rules": {
                "enable_cross_file_analysis": True,
                "enable_ml_models": True
            }
        }
        
        # Should create valid model
        config = DetectionConfigV1(**valid_config)
        assert len(config.pattern_types) == 3
        assert config.min_confidence == 0.8
        assert config.timeout_seconds == 30
        
        # Test "all" pattern types
        config_all = DetectionConfigV1(pattern_types=["all"])
        assert config_all.pattern_types == ["all"]
        
        # Test invalid pattern type
        invalid_config = valid_config.copy()
        invalid_config["pattern_types"] = ["invalid_type"]
        with pytest.raises(ValueError) as exc_info:
            DetectionConfigV1(**invalid_config)
        assert "pattern_types" in str(exc_info.value)
        
        # Test confidence bounds
        for invalid_confidence in [-0.1, 1.1, 2.0]:
            invalid_config = valid_config.copy()
            invalid_config["min_confidence"] = invalid_confidence
            with pytest.raises(ValueError) as exc_info:
                DetectionConfigV1(**invalid_config)
            assert "min_confidence" in str(exc_info.value)
    
    def test_error_response_v1_validation(self):
        """Test ErrorResponseV1 model validation."""
        # Valid error response
        valid_error = {
            "error_id": "err_1234567890abcdef",
            "service": "pattern-mining",
            "error_type": "validation",
            "message": "Invalid request format",
            "details": {
                "field": "repository_id",
                "reason": "Does not match required format"
            },
            "retryable": False,
            "timestamp": datetime.utcnow(),
            "correlation_id": "req_abcdef0123456789",
            "user_message": "Please check the repository ID format"
        }
        
        # Should create valid model
        error = ErrorResponseV1(**valid_error)
        assert error.error_id == "err_1234567890abcdef"
        assert error.error_type == "validation"
        assert error.retryable is False
        
        # Test error ID format
        invalid_error = valid_error.copy()
        invalid_error["error_id"] = "bad-error-id"
        with pytest.raises(ValueError) as exc_info:
            ErrorResponseV1(**invalid_error)
        assert "error_id" in str(exc_info.value)
        
        # Test error types
        for error_type in ["validation", "internal", "external", "timeout"]:
            error_data = valid_error.copy()
            error_data["error_type"] = error_type
            error = ErrorResponseV1(**error_data)
            assert error.error_type == error_type


@pytest.mark.contract
class TestContractValidators:
    """Test contract validation functions."""
    
    def test_contract_schema_validator(self):
        """Test ContractSchemaValidator."""
        # Load contract schemas
        contract_dir = Path(__file__).parent.parent.parent / "contracts"
        
        # Test pattern input schema validation
        input_schema_path = contract_dir / "pattern-input-v1.json"
        if input_schema_path.exists():
            validator = ContractSchemaValidator(str(input_schema_path))
            
            # Valid data should pass
            valid_data = {
                "repository_id": "repo_1234567890abcdef",
                "analysis_id": "analysis_123456789012",
                "ast_data": {
                    "repository_id": "repo_1234567890abcdef",
                    "commit_hash": "abc123",
                    "files": [],
                    "metadata": {}
                },
                "detection_config": {
                    "pattern_types": ["all"],
                    "min_confidence": 0.7
                }
            }
            
            is_valid, errors = validator.validate(valid_data)
            assert is_valid is True
            assert len(errors) == 0
            
            # Invalid data should fail
            invalid_data = {
                "repository_id": "invalid",
                "analysis_id": "analysis_123456789012"
            }
            
            is_valid, errors = validator.validate(invalid_data)
            assert is_valid is False
            assert len(errors) > 0
    
    def test_validate_pattern_input(self):
        """Test pattern input validation helper."""
        # Valid input
        valid_input = PatternInputV1(
            repository_id="repo_1234567890abcdef",
            analysis_id="analysis_123456789012",
            ast_data=ASTDataV1(
                repository_id="repo_1234567890abcdef",
                commit_hash="abc123",
                files=[],
                metadata={}
            ),
            detection_config=DetectionConfigV1(
                pattern_types=["all"],
                min_confidence=0.7
            )
        )
        
        # Should pass validation
        is_valid, errors = validate_pattern_input(valid_input)
        assert is_valid is True
        assert len(errors) == 0
    
    def test_validate_pattern_output(self):
        """Test pattern output validation helper."""
        # Valid output
        valid_output = PatternOutputV1(
            repository_id="repo_1234567890abcdef",
            analysis_id="analysis_123456789012",
            patterns=[],
            summary=PatternSummaryV1(
                total_patterns=0,
                patterns_by_type={},
                patterns_by_severity={},
                patterns_by_file={},
                quality_score=100.0,
                confidence_stats={
                    "mean": 0.0,
                    "min": 0.0,
                    "max": 0.0
                }
            ),
            processing_time_ms=50.0,
            model_version="1.0.0",
            timestamp=datetime.utcnow()
        )
        
        # Should pass validation
        is_valid, errors = validate_pattern_output(valid_output)
        assert is_valid is True
        assert len(errors) == 0
    
    def test_create_contract_compliant_error(self):
        """Test contract-compliant error creation."""
        # Create validation error
        error = create_contract_compliant_error(
            service="pattern-mining",
            error_type="validation",
            message="Invalid repository ID format",
            retryable=False,
            user_message="Please provide a valid repository ID",
            correlation_id="req_test123456789ab",
            context={
                "field": "repository_id",
                "provided": "invalid-id",
                "expected_format": "repo_[16 chars]"
            }
        )
        
        # Verify error structure
        assert error.service == "pattern-mining"
        assert error.error_type == "validation"
        assert error.retryable is False
        assert error.correlation_id == "req_test123456789ab"
        assert error.error_id.startswith("err_")
        assert len(error.error_id) == 20  # err_ + 16 chars
        
        # Verify error can be serialized
        error_dict = error.dict()
        assert "error_id" in error_dict
        assert "timestamp" in error_dict
        assert "context" in error_dict
        
        # Test different error types
        for error_type in ["internal", "external", "timeout"]:
            error = create_contract_compliant_error(
                service="pattern-mining",
                error_type=error_type,
                message=f"Test {error_type} error",
                retryable=True
            )
            assert error.error_type == error_type
            assert error.retryable is True


@pytest.mark.contract
class TestContractCompliance:
    """Test overall contract compliance."""
    
    def test_id_format_compliance(self):
        """Test ID format compliance across all models."""
        # Repository ID format: repo_[16 alphanumeric]
        valid_repo_ids = [
            "repo_1234567890abcdef",
            "repo_abcdef0123456789",
            "repo_ABCDEF0123456789"
        ]
        
        for repo_id in valid_repo_ids:
            # Should work in all models that use repository_id
            input_model = PatternInputV1(
                repository_id=repo_id,
                analysis_id="analysis_123456789012",
                ast_data=ASTDataV1(
                    repository_id=repo_id,
                    commit_hash="abc123",
                    files=[],
                    metadata={}
                ),
                detection_config=DetectionConfigV1(
                    pattern_types=["all"],
                    min_confidence=0.7
                )
            )
            assert input_model.repository_id == repo_id
        
        # Invalid repository IDs
        invalid_repo_ids = [
            "repo_123",  # Too short
            "repo_1234567890abcdefg",  # Too long
            "repository_1234567890abcdef",  # Wrong prefix
            "repo_123456789!abcdef",  # Invalid characters
            "REPO_1234567890abcdef",  # Wrong case for prefix
        ]
        
        for repo_id in invalid_repo_ids:
            with pytest.raises(ValueError):
                PatternInputV1(
                    repository_id=repo_id,
                    analysis_id="analysis_123456789012",
                    ast_data=ASTDataV1(
                        repository_id="repo_1234567890abcdef",
                        commit_hash="abc123",
                        files=[],
                        metadata={}
                    ),
                    detection_config=DetectionConfigV1(
                        pattern_types=["all"],
                        min_confidence=0.7
                    )
                )
    
    def test_enum_compliance(self):
        """Test enum value compliance."""
        # Pattern types
        valid_pattern_types = [
            "design_pattern",
            "anti_pattern",
            "security_vulnerability",
            "performance_issue",
            "code_smell",
            "architectural_pattern",
            "test_pattern",
            "concurrency_pattern"
        ]
        
        for pattern_type in valid_pattern_types:
            pattern = DetectedPatternV1(
                pattern_id="pat_1234567890abcdef",
                pattern_type=pattern_type,
                pattern_name="Test Pattern",
                confidence=0.8,
                severity="low",
                file_path="test.py",
                line_start=1,
                line_end=5,
                description="Test"
            )
            assert pattern.pattern_type.value == pattern_type
        
        # Severity levels
        valid_severities = ["info", "low", "medium", "high", "critical"]
        
        for severity in valid_severities:
            pattern = DetectedPatternV1(
                pattern_id="pat_1234567890abcdef",
                pattern_type="code_smell",
                pattern_name="Test Pattern",
                confidence=0.8,
                severity=severity,
                file_path="test.py",
                line_start=1,
                line_end=5,
                description="Test"
            )
            assert pattern.severity.value == severity
    
    def test_field_constraints(self):
        """Test field constraint compliance."""
        # Confidence must be between 0 and 1
        for valid_confidence in [0.0, 0.5, 0.99, 1.0]:
            pattern = DetectedPatternV1(
                pattern_id="pat_1234567890abcdef",
                pattern_type="code_smell",
                pattern_name="Test",
                confidence=valid_confidence,
                severity="low",
                file_path="test.py",
                line_start=1,
                line_end=5,
                description="Test"
            )
            assert pattern.confidence == valid_confidence
        
        for invalid_confidence in [-0.1, 1.1, 2.0]:
            with pytest.raises(ValueError):
                DetectedPatternV1(
                    pattern_id="pat_1234567890abcdef",
                    pattern_type="code_smell",
                    pattern_name="Test",
                    confidence=invalid_confidence,
                    severity="low",
                    file_path="test.py",
                    line_start=1,
                    line_end=5,
                    description="Test"
                )
        
        # Line numbers must be positive
        with pytest.raises(ValueError):
            DetectedPatternV1(
                pattern_id="pat_1234567890abcdef",
                pattern_type="code_smell",
                pattern_name="Test",
                confidence=0.8,
                severity="low",
                file_path="test.py",
                line_start=-1,
                line_end=5,
                description="Test"
            )
        
        # End line must be >= start line
        with pytest.raises(ValueError):
            DetectedPatternV1(
                pattern_id="pat_1234567890abcdef",
                pattern_type="code_smell",
                pattern_name="Test",
                confidence=0.8,
                severity="low",
                file_path="test.py",
                line_start=10,
                line_end=5,
                description="Test"
            )
    
    def test_complete_workflow_compliance(self):
        """Test complete workflow with contract-compliant models."""
        # Create input
        pattern_input = PatternInputV1(
            repository_id="repo_workflow12345678",
            analysis_id="analysis_workflow1234",
            request_id="req_workflow123456789",
            ast_data=ASTDataV1(
                repository_id="repo_workflow12345678",
                commit_hash="abc123def456",
                files=[
                    FileASTDataV1(
                        path="src/example.py",
                        language="python",
                        ast_nodes=[
                            {
                                "type": "class_definition",
                                "name": "ExampleClass",
                                "start_line": 1,
                                "end_line": 50,
                                "children": []
                            }
                        ],
                        symbols=[
                            {
                                "name": "ExampleClass",
                                "type": "class",
                                "line": 1,
                                "scope": "global"
                            }
                        ],
                        imports=[],
                        metrics={
                            "lines_of_code": 150,
                            "cyclomatic_complexity": 10
                        }
                    )
                ],
                metadata={
                    "total_files": 1,
                    "languages": {"python": 1}
                }
            ),
            detection_config=DetectionConfigV1(
                pattern_types=["all"],
                min_confidence=0.7,
                include_metrics=True,
                include_recommendations=True,
                timeout_seconds=30
            ),
            metadata={
                "source": "test",
                "test_id": "workflow_compliance"
            }
        )
        
        # Validate input
        input_dict = pattern_input.dict()
        assert input_dict["repository_id"] == "repo_workflow12345678"
        assert len(input_dict["ast_data"]["files"]) == 1
        
        # Create output
        pattern_output = PatternOutputV1(
            repository_id=pattern_input.repository_id,
            analysis_id=pattern_input.analysis_id,
            request_id=pattern_input.request_id,
            patterns=[
                DetectedPatternV1(
                    pattern_id="pat_detected12345678",
                    pattern_type="design_pattern",
                    pattern_name="Singleton Pattern",
                    confidence=0.85,
                    severity="info",
                    file_path="src/example.py",
                    line_start=5,
                    line_end=25,
                    column_start=4,
                    column_end=80,
                    description="Singleton pattern detected in ExampleClass",
                    recommendation="Consider if singleton is necessary",
                    tags=["design", "singleton", "creational"],
                    metadata={
                        "class_name": "ExampleClass",
                        "detection_method": "ml_model"
                    }
                ),
                DetectedPatternV1(
                    pattern_id="pat_detected87654321",
                    pattern_type="code_smell",
                    pattern_name="Long Method",
                    confidence=0.92,
                    severity="medium",
                    file_path="src/example.py",
                    line_start=30,
                    line_end=48,
                    description="Method exceeds recommended length",
                    recommendation="Consider breaking into smaller methods",
                    tags=["maintainability", "refactoring"],
                    metadata={
                        "method_lines": 19,
                        "complexity": 12
                    }
                )
            ],
            summary=PatternSummaryV1(
                total_patterns=2,
                patterns_by_type={
                    "design_pattern": 1,
                    "code_smell": 1
                },
                patterns_by_severity={
                    "info": 1,
                    "medium": 1
                },
                patterns_by_file={
                    "src/example.py": 2
                },
                quality_score=78.5,
                confidence_stats={
                    "mean": 0.885,
                    "min": 0.85,
                    "max": 0.92
                }
            ),
            processing_time_ms=245.8,
            model_version="1.0.0",
            timestamp=datetime.utcnow(),
            metadata={
                "models_used": ["pattern_detector_v1", "code_analyzer_v1"],
                "cache_hit": False
            }
        )
        
        # Validate output
        output_dict = pattern_output.dict()
        assert output_dict["repository_id"] == pattern_input.repository_id
        assert len(output_dict["patterns"]) == 2
        assert output_dict["summary"]["total_patterns"] == 2
        assert output_dict["processing_time_ms"] == 245.8
        
        # Verify JSON serialization
        output_json = pattern_output.model_dump_json()
        parsed = json.loads(output_json)
        assert parsed["repository_id"] == "repo_workflow12345678"
        assert len(parsed["patterns"]) == 2