"""
Contract Test Fixtures

Wave 2.5: CCL Contract Compliance Implementation - Phase 5
Shared fixtures for contract compliance tests.
"""

import pytest
import asyncio
from datetime import datetime
from typing import Dict, Any, List
from unittest.mock import Mock, AsyncMock
from pathlib import Path

from pattern_mining.contracts.models import (
    ASTDataV1,
    FileASTDataV1,
    DetectionConfigV1,
    PatternTypeV1,
    SeverityLevelV1,
)
from pattern_mining.ast_processing import ProcessingConfig


@pytest.fixture
def sample_ast_data():
    """Generate sample AST data for testing."""
    return ASTDataV1(
        repository_id="repo_test1234567890ab",
        commit_hash="abc123def456",
        files=[
            FileASTDataV1(
                path="src/main.py",
                language="python",
                ast_nodes=[
                    {
                        "type": "class_definition",
                        "name": "UserService",
                        "start_line": 10,
                        "end_line": 150,
                        "children": [
                            {
                                "type": "function_definition",
                                "name": "__init__",
                                "start_line": 12,
                                "end_line": 20,
                            },
                            {
                                "type": "function_definition",
                                "name": "get_user",
                                "start_line": 22,
                                "end_line": 35,
                                "children": [
                                    {
                                        "type": "string_literal",
                                        "value": "SELECT * FROM users WHERE id = {}",
                                        "start_line": 28,
                                        "end_line": 28,
                                    }
                                ]
                            }
                        ]
                    }
                ],
                symbols=[
                    {
                        "name": "UserService",
                        "type": "class",
                        "line": 10,
                        "scope": "global"
                    },
                    {
                        "name": "__init__",
                        "type": "function",
                        "line": 12,
                        "scope": "UserService"
                    },
                    {
                        "name": "get_user",
                        "type": "function",
                        "line": 22,
                        "scope": "UserService"
                    }
                ],
                imports=[
                    {
                        "module": "typing",
                        "names": ["List", "Dict", "Optional"],
                        "line": 1
                    },
                    {
                        "module": "sqlalchemy",
                        "names": ["create_engine", "Session"],
                        "line": 2
                    }
                ],
                metrics={
                    "lines_of_code": 150,
                    "cyclomatic_complexity": 12,
                    "cognitive_complexity": 15,
                    "max_nesting_depth": 3
                }
            )
        ],
        metadata={
            "total_files": 1,
            "languages": {"python": 1},
            "analysis_timestamp": datetime.utcnow().isoformat()
        }
    )


@pytest.fixture
def large_ast_data():
    """Generate large AST data for performance testing."""
    files = []
    
    for i in range(50):  # 50 files
        ast_nodes = []
        symbols = []
        
        # Generate classes and methods
        for j in range(5):  # 5 classes per file
            class_node = {
                "type": "class_definition",
                "name": f"Class{i}_{j}",
                "start_line": j * 100 + 1,
                "end_line": j * 100 + 95,
                "children": []
            }
            
            symbols.append({
                "name": f"Class{i}_{j}",
                "type": "class",
                "line": j * 100 + 1,
                "scope": "global"
            })
            
            # Add methods
            for k in range(10):  # 10 methods per class
                method_node = {
                    "type": "function_definition",
                    "name": f"method_{k}",
                    "start_line": j * 100 + k * 9 + 5,
                    "end_line": j * 100 + k * 9 + 12,
                }
                class_node["children"].append(method_node)
                
                symbols.append({
                    "name": f"method_{k}",
                    "type": "function",
                    "line": j * 100 + k * 9 + 5,
                    "scope": f"Class{i}_{j}"
                })
            
            ast_nodes.append(class_node)
        
        file_ast = FileASTDataV1(
            path=f"src/module_{i}/file_{i}.py",
            language="python",
            ast_nodes=ast_nodes,
            symbols=symbols,
            imports=[
                {"module": "typing", "names": ["List", "Dict"], "line": 1},
                {"module": "dataclasses", "names": ["dataclass"], "line": 2},
            ],
            metrics={
                "lines_of_code": 500,
                "cyclomatic_complexity": 25,
                "cognitive_complexity": 30,
                "max_nesting_depth": 4
            }
        )
        files.append(file_ast)
    
    return ASTDataV1(
        repository_id="repo_large1234567890",
        commit_hash="largetest123",
        files=files,
        metadata={
            "total_files": len(files),
            "languages": {"python": len(files)},
            "analysis_timestamp": datetime.utcnow().isoformat()
        }
    )


@pytest.fixture
def detection_config():
    """Create default detection configuration."""
    return DetectionConfigV1(
        pattern_types=["all"],
        min_confidence=0.7,
        max_patterns_per_file=100,
        include_metrics=True,
        include_recommendations=True,
        timeout_seconds=30,
        parallel_processing=True
    )


@pytest.fixture
def processing_config():
    """Create default processing configuration."""
    return ProcessingConfig(
        max_file_size_mb=5.0,
        max_ast_nodes=10000,
        enable_caching=True,
        parallel_processing=True,
        feature_extraction_timeout=30,
        pattern_detection_timeout=60,
        min_confidence_threshold=0.1,
        max_patterns_per_file=100,
        batch_size=10
    )


@pytest.fixture
def mock_patterns():
    """Generate mock detected patterns."""
    return [
        {
            "pattern_id": "pat_mock1234567890ab",
            "pattern_type": PatternTypeV1.SECURITY_VULNERABILITY,
            "pattern_name": "SQL Injection",
            "confidence": 0.95,
            "severity": SeverityLevelV1.CRITICAL,
            "file_path": "src/main.py",
            "line_start": 28,
            "line_end": 28,
            "description": "Potential SQL injection vulnerability",
            "recommendation": "Use parameterized queries",
            "tags": ["security", "sql", "injection"],
            "metadata": {
                "vulnerability_type": "sql_injection",
                "cwe_id": "CWE-89"
            }
        },
        {
            "pattern_id": "pat_mock0987654321dc",
            "pattern_type": PatternTypeV1.DESIGN_PATTERN,
            "pattern_name": "Service Pattern",
            "confidence": 0.88,
            "severity": SeverityLevelV1.INFO,
            "file_path": "src/main.py",
            "line_start": 10,
            "line_end": 150,
            "description": "Service pattern implementation",
            "recommendation": "Well-structured service pattern",
            "tags": ["design", "service", "architecture"],
            "metadata": {
                "class_name": "UserService"
            }
        },
        {
            "pattern_id": "pat_mockabcdef123456",
            "pattern_type": PatternTypeV1.CODE_SMELL,
            "pattern_name": "Long Method",
            "confidence": 0.82,
            "severity": SeverityLevelV1.MEDIUM,
            "file_path": "src/main.py",
            "line_start": 22,
            "line_end": 35,
            "description": "Method exceeds recommended length",
            "recommendation": "Consider breaking into smaller methods",
            "tags": ["maintainability", "refactoring"],
            "metadata": {
                "method_lines": 14,
                "complexity": 8
            }
        }
    ]


@pytest.fixture
def mock_redis_client():
    """Create mock Redis client."""
    mock = AsyncMock()
    mock.ping.return_value = True
    mock.get.return_value = None
    mock.set.return_value = True
    mock.sadd.return_value = 1
    mock.srem.return_value = 1
    mock.smembers.return_value = set()
    mock.exists.return_value = False
    mock.expire.return_value = True
    mock.ttl.return_value = 3600
    return mock


@pytest.fixture
def mock_pattern_service():
    """Create mock pattern service."""
    mock_service = Mock()
    
    # Mock AST processor
    mock_service.ast_processor = AsyncMock()
    mock_service.ast_processor.process_ast_data.return_value = Mock(
        repository_id="repo_test1234567890ab",
        analysis_id="analysis_test123456",
        files=[],
        total_features_extracted=100,
        processing_time_ms=50.0
    )
    
    # Mock pattern detector
    mock_service.pattern_detector = AsyncMock()
    mock_service.pattern_detector.detect_patterns.return_value = []
    
    # Mock performance stats
    mock_service.performance_stats = AsyncMock()
    mock_service.performance_stats.record_request.return_value = None
    
    # Mock Redis client
    mock_service.redis_client = mock_redis_client()
    
    return mock_service


@pytest.fixture
def test_data_generator():
    """Factory for generating test data."""
    
    class TestDataGenerator:
        @staticmethod
        def create_file_ast(
            path: str = "test.py",
            language: str = "python",
            lines: int = 100,
            complexity: int = 10
        ) -> FileASTDataV1:
            """Create a single file AST."""
            return FileASTDataV1(
                path=path,
                language=language,
                ast_nodes=[
                    {
                        "type": "function_definition",
                        "name": f"test_function",
                        "start_line": 1,
                        "end_line": lines,
                    }
                ],
                symbols=[
                    {
                        "name": "test_function",
                        "type": "function",
                        "line": 1,
                        "scope": "global"
                    }
                ],
                imports=[],
                metrics={
                    "lines_of_code": lines,
                    "cyclomatic_complexity": complexity,
                    "cognitive_complexity": complexity + 2,
                    "max_nesting_depth": 2
                }
            )
        
        @staticmethod
        def create_pattern(
            pattern_type: str = "code_smell",
            severity: str = "medium",
            confidence: float = 0.8
        ) -> Dict[str, Any]:
            """Create a single pattern."""
            return {
                "pattern_id": f"pat_{datetime.utcnow().timestamp():.0f}",
                "pattern_type": pattern_type,
                "pattern_name": f"Test {pattern_type}",
                "confidence": confidence,
                "severity": severity,
                "file_path": "test.py",
                "line_start": 10,
                "line_end": 20,
                "description": f"Test {pattern_type} pattern",
                "recommendation": "Test recommendation",
                "tags": ["test"],
                "metadata": {}
            }
    
    return TestDataGenerator()


@pytest.fixture
def performance_metrics():
    """Mock performance metrics."""
    return {
        "processing_time_seconds": 25.5,
        "files_per_second": 55.2,
        "patterns_per_second": 125.8,
        "integration_latency_p95_ms": 85.5,
        "integration_throughput_rps": 22.3,
        "loc_per_minute": 1_200_000,
        "throughput_rps": 1100,
        "success_rate": 0.995,
        "memory_limit_mb": 350,
        "cache_hit_rate": 0.85,
        "error_rate": 0.01
    }


@pytest.fixture
def contract_test_config():
    """Configuration for contract tests."""
    return {
        "timeouts": {
            "processing_budget": 30,  # seconds
            "api_timeout": 60,  # seconds
            "pattern_detection": 25,  # seconds
        },
        "performance_targets": {
            "files_per_second": 50,
            "patterns_per_second": 100,
            "integration_latency_p95_ms": 100,
            "integration_throughput_rps": 20,
        },
        "test_data": {
            "small_file_count": 10,
            "medium_file_count": 50,
            "large_file_count": 100,
            "pattern_density": 5,  # patterns per file
        }
    }


# Async fixtures for integration tests

@pytest.fixture
async def async_pattern_service(mock_pattern_service):
    """Async pattern service fixture."""
    yield mock_pattern_service


@pytest.fixture
async def async_redis_client(mock_redis_client):
    """Async Redis client fixture."""
    yield mock_redis_client


# Pytest configuration

def pytest_configure(config):
    """Configure pytest with custom markers."""
    config.addinivalue_line(
        "markers", "contract: mark test as contract compliance test"
    )


# Helper functions for tests

def create_contract_request(
    repository_id: str = "repo_test1234567890ab",
    analysis_id: str = "analysis_test123456",
    request_id: str = None,
    ast_data: ASTDataV1 = None,
    detection_config: DetectionConfigV1 = None
) -> Dict[str, Any]:
    """Create a contract-compliant request."""
    if ast_data is None:
        ast_data = ASTDataV1(
            repository_id=repository_id,
            commit_hash="test123",
            files=[],
            metadata={}
        )
    
    if detection_config is None:
        detection_config = DetectionConfigV1(
            pattern_types=["all"],
            min_confidence=0.7
        )
    
    request = {
        "repository_id": repository_id,
        "analysis_id": analysis_id,
        "ast_data": ast_data.dict(),
        "detection_config": detection_config.dict()
    }
    
    if request_id:
        request["request_id"] = request_id
    
    return request


def assert_contract_response(response_data: Dict[str, Any]):
    """Assert that response follows contract format."""
    # Required fields
    assert "repository_id" in response_data
    assert "analysis_id" in response_data
    assert "patterns" in response_data
    assert "summary" in response_data
    assert "processing_time_ms" in response_data
    assert "model_version" in response_data
    assert "timestamp" in response_data
    
    # Validate patterns
    patterns = response_data["patterns"]
    assert isinstance(patterns, list)
    
    for pattern in patterns:
        assert "pattern_id" in pattern
        assert pattern["pattern_id"].startswith("pat_")
        assert "pattern_type" in pattern
        assert "pattern_name" in pattern
        assert "confidence" in pattern
        assert 0 <= pattern["confidence"] <= 1
        assert "severity" in pattern
        assert "file_path" in pattern
        assert "line_start" in pattern
        assert "line_end" in pattern
        assert pattern["line_end"] >= pattern["line_start"]
        assert "description" in pattern
    
    # Validate summary
    summary = response_data["summary"]
    assert "total_patterns" in summary
    assert "patterns_by_type" in summary
    assert "patterns_by_severity" in summary
    assert "patterns_by_file" in summary
    assert "quality_score" in summary
    assert 0 <= summary["quality_score"] <= 100
    assert "confidence_stats" in summary
    
    # Validate metadata
    assert response_data["processing_time_ms"] >= 0
    assert response_data["model_version"]
    assert response_data["timestamp"]