"""
CCL Contract Performance Tests

Wave 2.5: CCL Contract Compliance Implementation - Phase 5
Performance tests for validating CCL contract requirements.
"""

import pytest
import asyncio
import time
import psutil
import statistics
from datetime import datetime
from typing import Dict, Any, List, Tuple
from unittest.mock import Mock, AsyncMock, patch
import random

from pattern_mining.contracts.models import (
    PatternInputV1,
    PatternOutputV1,
    ASTDataV1,
    FileASTDataV1,
    DetectionConfigV1,
    PatternTypeV1,
    SeverityLevelV1,
)
from pattern_mining.ast_processing import (
    ASTDataProcessor,
    EnhancedPatternDetector,
    ProcessingConfig,
)
from pattern_mining.performance import ContractPerformanceValidator
from pattern_mining.cache.redis_client import RedisClient


@pytest.mark.contract
@pytest.mark.performance
class TestContractPerformanceRequirements:
    """Test CCL contract performance requirements."""
    
    @pytest.fixture
    def processing_config(self):
        """Create processing configuration for tests."""
        return ProcessingConfig(
            max_file_size_mb=5.0,
            max_ast_nodes=10000,
            enable_caching=True,
            parallel_processing=True,
            feature_extraction_timeout=30,
            pattern_detection_timeout=60,
            min_confidence_threshold=0.1,
            max_patterns_per_file=100,
            batch_size=10
        )
    
    @pytest.fixture
    def ast_processor(self, processing_config):
        """Create AST processor with test config."""
        return ASTDataProcessor(config=processing_config)
    
    @pytest.fixture
    def pattern_detector(self):
        """Create pattern detector instance."""
        return EnhancedPatternDetector()
    
    @pytest.fixture
    def mock_redis(self):
        """Create mock Redis client."""
        mock = AsyncMock()
        mock.ping.return_value = True
        mock.get.return_value = None
        mock.set.return_value = True
        return mock
    
    def generate_test_ast_data(self, file_count: int) -> ASTDataV1:
        """Generate test AST data with specified number of files."""
        files = []
        
        for i in range(file_count):
            # Generate realistic AST nodes
            ast_nodes = []
            for j in range(5):  # 5 classes per file
                class_node = {
                    "type": "class_definition",
                    "name": f"Class{i}_{j}",
                    "start_line": j * 100 + 1,
                    "end_line": j * 100 + 95,
                    "children": []
                }
                
                # Add methods to class
                for k in range(10):  # 10 methods per class
                    method_node = {
                        "type": "function_definition",
                        "name": f"method_{k}",
                        "start_line": j * 100 + k * 9 + 5,
                        "end_line": j * 100 + k * 9 + 12,
                        "children": [
                            {
                                "type": "if_statement",
                                "start_line": j * 100 + k * 9 + 7,
                                "end_line": j * 100 + k * 9 + 10,
                            }
                        ]
                    }
                    class_node["children"].append(method_node)
                
                ast_nodes.append(class_node)
            
            # Generate symbols
            symbols = []
            for j in range(5):
                symbols.append({
                    "name": f"Class{i}_{j}",
                    "type": "class",
                    "line": j * 100 + 1,
                    "scope": "global"
                })
                for k in range(10):
                    symbols.append({
                        "name": f"method_{k}",
                        "type": "function",
                        "line": j * 100 + k * 9 + 5,
                        "scope": f"Class{i}_{j}"
                    })
            
            file_ast = FileASTDataV1(
                path=f"src/module_{i}/file_{i}.py",
                language="python",
                ast_nodes=ast_nodes,
                symbols=symbols,
                imports=[
                    {"module": "typing", "names": ["List", "Dict"], "line": 1},
                    {"module": "dataclasses", "names": ["dataclass"], "line": 2},
                ],
                metrics={
                    "lines_of_code": 500,
                    "cyclomatic_complexity": 25,
                    "cognitive_complexity": 30,
                    "max_nesting_depth": 4
                }
            )
            files.append(file_ast)
        
        return ASTDataV1(
            repository_id="repo_perf1234567890ab",
            commit_hash="perftest123",
            files=files,
            metadata={
                "total_files": file_count,
                "languages": {"python": file_count},
                "analysis_timestamp": datetime.utcnow().isoformat()
            }
        )
    
    @pytest.mark.asyncio
    async def test_30_second_processing_budget(self, ast_processor, pattern_detector):
        """Test that processing completes within 30-second budget."""
        # Generate large AST data (100 files)
        ast_data = self.generate_test_ast_data(100)
        
        start_time = time.time()
        
        # Process AST data
        processed_data = await ast_processor.process_ast_data(
            ast_data=ast_data,
            repository_id="repo_perf1234567890ab",
            analysis_id="analysis_perf123456"
        )
        
        # Detect patterns
        all_patterns = []
        detection_config = DetectionConfigV1(
            pattern_types=["all"],
            min_confidence=0.7,
            include_metrics=True,
            timeout_seconds=25  # Leave 5 seconds margin
        )
        
        for file_data in processed_data.files:
            patterns = await pattern_detector.detect_patterns(
                file_data=file_data,
                detection_config=detection_config
            )
            all_patterns.extend(patterns)
        
        total_time = time.time() - start_time
        
        # Should complete within 30 seconds
        assert total_time < 30.0, f"Processing took {total_time:.2f}s, exceeds 30s budget"
        
        # Should have processed all files
        assert len(processed_data.files) == 100
        
        # Should have detected patterns
        assert len(all_patterns) > 0
    
    @pytest.mark.asyncio
    async def test_50_files_per_second_rate(self, ast_processor):
        """Test 50 files/second processing rate."""
        # Test with batches of 50 files
        batch_sizes = [50, 50, 50]
        processing_times = []
        
        for batch_size in batch_sizes:
            ast_data = self.generate_test_ast_data(batch_size)
            
            start_time = time.time()
            
            await ast_processor.process_ast_data(
                ast_data=ast_data,
                repository_id=f"repo_batch{batch_size:08d}",
                analysis_id=f"analysis_batch{batch_size:04d}"
            )
            
            elapsed_time = time.time() - start_time
            processing_times.append(elapsed_time)
            
            files_per_second = batch_size / elapsed_time
            print(f"Batch {batch_size}: {files_per_second:.2f} files/s")
        
        # Calculate average rate
        total_files = sum(batch_sizes)
        total_time = sum(processing_times)
        avg_rate = total_files / total_time
        
        # Should achieve at least 50 files/second
        assert avg_rate >= 50.0, f"Processing rate {avg_rate:.2f} files/s is below 50 files/s target"
    
    @pytest.mark.asyncio
    async def test_100_patterns_per_second_rate(self, pattern_detector):
        """Test 100 patterns/second detection rate."""
        # Create pattern-rich file data
        from pattern_mining.ast_processing.processor import ProcessedFileData
        
        detection_times = []
        pattern_counts = []
        
        for i in range(10):
            # Create file with many pattern opportunities
            file_data = ProcessedFileData(
                path=f"test/pattern_rich_{i}.py",
                language="python",
                features={
                    "structural": {
                        "class_count": 10,
                        "method_count": 50,
                        "max_nesting": 6,
                        "cyclomatic_complexity": 40,
                        "duplicate_blocks": 5,
                    },
                    "semantic": {
                        "god_class_score": 0.9,
                        "long_method_score": 0.8,
                        "duplicate_code_score": 0.7,
                        "coupling_score": 0.85,
                    },
                    "code": {
                        "lines_of_code": 1000,
                        "comment_ratio": 0.05,
                        "test_coverage": 0.2,
                    }
                },
                ast_summary={
                    "total_nodes": 2000,
                    "node_types": {
                        "class_definition": 10,
                        "function_definition": 50,
                        "if_statement": 100,
                        "for_loop": 50,
                        "try_statement": 20,
                        "string_literal": 200,
                    }
                }
            )
            
            detection_config = DetectionConfigV1(
                pattern_types=["all"],
                min_confidence=0.1,  # Low threshold to get many patterns
                include_metrics=True
            )
            
            start_time = time.time()
            
            patterns = await pattern_detector.detect_patterns(
                file_data=file_data,
                detection_config=detection_config
            )
            
            elapsed_time = time.time() - start_time
            detection_times.append(elapsed_time)
            pattern_counts.append(len(patterns))
        
        # Calculate average rate
        total_patterns = sum(pattern_counts)
        total_time = sum(detection_times)
        avg_rate = total_patterns / total_time if total_time > 0 else 0
        
        print(f"Detected {total_patterns} patterns in {total_time:.2f}s")
        print(f"Average rate: {avg_rate:.2f} patterns/s")
        
        # Should achieve at least 100 patterns/second
        assert avg_rate >= 100.0, f"Detection rate {avg_rate:.2f} patterns/s is below 100 patterns/s target"
    
    @pytest.mark.asyncio
    async def test_integration_sla_latency(self):
        """Test integration SLA: p95 < 100ms."""
        # Simulate API endpoint calls
        latencies = []
        
        async def simulate_api_call():
            """Simulate minimal API processing."""
            start = time.time()
            
            # Simulate some processing
            await asyncio.sleep(random.uniform(0.01, 0.08))  # 10-80ms
            
            # Add some CPU work
            _ = sum(i * i for i in range(1000))
            
            latency = (time.time() - start) * 1000  # Convert to ms
            return latency
        
        # Run 200 requests
        tasks = []
        for _ in range(200):
            tasks.append(simulate_api_call())
            # Small delay to avoid overwhelming
            await asyncio.sleep(0.001)
        
        latencies = await asyncio.gather(*tasks)
        
        # Calculate p95
        sorted_latencies = sorted(latencies)
        p95_index = int(len(sorted_latencies) * 0.95)
        p95_latency = sorted_latencies[p95_index]
        
        # Calculate other metrics
        avg_latency = statistics.mean(latencies)
        min_latency = min(latencies)
        max_latency = max(latencies)
        
        print(f"Latency stats: min={min_latency:.2f}ms, avg={avg_latency:.2f}ms, p95={p95_latency:.2f}ms, max={max_latency:.2f}ms")
        
        # P95 should be under 100ms
        assert p95_latency < 100.0, f"P95 latency {p95_latency:.2f}ms exceeds 100ms SLA"
    
    @pytest.mark.asyncio
    async def test_integration_sla_throughput(self):
        """Test integration SLA: 20 RPS sustained throughput."""
        # Track request completion times
        completed_requests = 0
        start_time = time.time()
        target_duration = 10  # Run for 10 seconds
        target_rps = 20
        
        async def process_request():
            """Simulate request processing."""
            await asyncio.sleep(random.uniform(0.01, 0.05))  # 10-50ms processing
            return True
        
        # Generate requests at target rate
        request_interval = 1.0 / target_rps
        next_request_time = start_time
        
        while time.time() - start_time < target_duration:
            current_time = time.time()
            
            if current_time >= next_request_time:
                # Launch request
                asyncio.create_task(process_request())
                completed_requests += 1
                next_request_time += request_interval
            
            # Small sleep to avoid busy waiting
            await asyncio.sleep(0.001)
        
        # Calculate actual RPS
        actual_duration = time.time() - start_time
        actual_rps = completed_requests / actual_duration
        
        print(f"Sustained {actual_rps:.2f} RPS over {actual_duration:.2f} seconds")
        
        # Should sustain at least 20 RPS
        assert actual_rps >= 20.0, f"Throughput {actual_rps:.2f} RPS is below 20 RPS target"
    
    @pytest.mark.asyncio
    async def test_memory_efficiency(self, ast_processor):
        """Test memory usage stays within limits."""
        # Get initial memory usage
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Process large dataset
        ast_data = self.generate_test_ast_data(100)
        
        await ast_processor.process_ast_data(
            ast_data=ast_data,
            repository_id="repo_memory1234567890",
            analysis_id="analysis_memory123456"
        )
        
        # Check memory after processing
        peak_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = peak_memory - initial_memory
        
        print(f"Memory usage: initial={initial_memory:.2f}MB, peak={peak_memory:.2f}MB, increase={memory_increase:.2f}MB")
        
        # Memory increase should be reasonable (< 500MB for 100 files)
        assert memory_increase < 500.0, f"Memory increase {memory_increase:.2f}MB exceeds 500MB limit"
    
    @pytest.mark.asyncio
    async def test_concurrent_processing_performance(self, ast_processor, pattern_detector):
        """Test performance under concurrent load."""
        # Create multiple concurrent processing tasks
        concurrent_tasks = 5
        
        async def process_repository(repo_id: str):
            """Process a single repository."""
            ast_data = self.generate_test_ast_data(20)  # 20 files each
            
            start_time = time.time()
            
            # Process AST
            processed_data = await ast_processor.process_ast_data(
                ast_data=ast_data,
                repository_id=repo_id,
                analysis_id=f"analysis_{repo_id[-6:]}"
            )
            
            # Detect patterns
            detection_config = DetectionConfigV1(
                pattern_types=["all"],
                min_confidence=0.7
            )
            
            patterns = []
            for file_data in processed_data.files:
                file_patterns = await pattern_detector.detect_patterns(
                    file_data=file_data,
                    detection_config=detection_config
                )
                patterns.extend(file_patterns)
            
            elapsed_time = time.time() - start_time
            
            return {
                "repo_id": repo_id,
                "files": len(processed_data.files),
                "patterns": len(patterns),
                "time": elapsed_time
            }
        
        # Run concurrent processing
        start_time = time.time()
        
        tasks = []
        for i in range(concurrent_tasks):
            repo_id = f"repo_concurrent{i:08d}test"
            tasks.append(process_repository(repo_id))
        
        results = await asyncio.gather(*tasks)
        
        total_time = time.time() - start_time
        
        # Calculate metrics
        total_files = sum(r["files"] for r in results)
        total_patterns = sum(r["patterns"] for r in results)
        avg_time_per_repo = statistics.mean(r["time"] for r in results)
        
        print(f"Processed {concurrent_tasks} repositories concurrently:")
        print(f"  Total files: {total_files}")
        print(f"  Total patterns: {total_patterns}")
        print(f"  Total time: {total_time:.2f}s")
        print(f"  Avg time per repo: {avg_time_per_repo:.2f}s")
        
        # Should handle concurrent load efficiently
        assert total_time < 30.0, f"Concurrent processing took {total_time:.2f}s, too slow"
        assert avg_time_per_repo < 15.0, f"Average repo processing {avg_time_per_repo:.2f}s, too slow"


@pytest.mark.contract
@pytest.mark.performance
class TestContractPerformanceValidation:
    """Test contract performance validation framework."""
    
    @pytest.fixture
    def contract_validator(self, mock_redis):
        """Create contract performance validator."""
        return ContractPerformanceValidator(redis_client=mock_redis)
    
    @pytest.mark.asyncio
    async def test_contract_compliance_validation(self, contract_validator):
        """Test full contract compliance validation."""
        # Mock the benchmark results
        with patch.object(contract_validator, '_benchmark_processing_budget') as mock_budget:
            mock_budget.return_value = Mock(
                test_name="processing_budget",
                passed=True,
                metrics={
                    "processing_time_seconds": 25.5,
                    "files_processed": 100,
                    "patterns_detected": 450
                }
            )
        
        with patch.object(contract_validator, '_benchmark_file_processing_rate') as mock_file_rate:
            mock_file_rate.return_value = Mock(
                test_name="file_processing_rate",
                passed=True,
                metrics={
                    "files_per_second": 55.2,
                    "total_files": 250,
                    "total_time": 4.53
                }
            )
        
        with patch.object(contract_validator, '_benchmark_pattern_detection_rate') as mock_pattern_rate:
            mock_pattern_rate.return_value = Mock(
                test_name="pattern_detection_rate",
                passed=True,
                metrics={
                    "patterns_per_second": 125.8,
                    "total_patterns": 890,
                    "total_time": 7.08
                }
            )
        
        with patch.object(contract_validator, '_benchmark_integration_sla') as mock_sla:
            mock_sla.return_value = Mock(
                test_name="integration_sla",
                passed=True,
                metrics={
                    "integration_latency_p95_ms": 85.5,
                    "integration_throughput_rps": 22.3,
                    "total_requests": 200
                }
            )
        
        # Mock standard benchmarks
        with patch.object(contract_validator.benchmark, '_benchmark_loc_processing') as mock_loc:
            mock_loc.return_value = Mock(
                test_name="LOC Processing",
                loc_per_minute=1_200_000,
                memory_peak_mb=350
            )
        
        with patch.object(contract_validator.benchmark, '_benchmark_throughput') as mock_throughput:
            mock_throughput.return_value = Mock(
                test_name="Throughput",
                throughput_rps=1100,
                latency_p95_ms=90,
                success_rate=0.995
            )
        
        with patch.object(contract_validator.benchmark, '_benchmark_latency') as mock_latency:
            mock_latency.return_value = Mock(
                test_name="Latency",
                latency_p95_ms=90
            )
        
        with patch.object(contract_validator.benchmark, '_benchmark_memory_efficiency') as mock_memory:
            mock_memory.return_value = Mock(
                test_name="Memory Efficiency",
                memory_peak_mb=350
            )
        
        with patch.object(contract_validator.benchmark, '_benchmark_cache_performance') as mock_cache:
            mock_cache.return_value = Mock(
                test_name="Cache Performance",
                cache_hit_rate=0.85
            )
        
        # Mock load test results
        with patch.object(contract_validator.load_tester, '_run_baseline_test') as mock_baseline:
            mock_baseline.return_value = Mock(
                test_name="Baseline",
                actual_rps=25,
                latency_p95_ms=80,
                success_rate=0.99,
                error_rate=0.01
            )
        
        with patch.object(contract_validator.load_tester, '_run_target_rps_test') as mock_target_rps:
            mock_target_rps.return_value = Mock(
                test_name="Target RPS",
                actual_rps=22,
                latency_p95_ms=85,
                success_rate=0.99,
                error_rate=0.01
            )
        
        # Run validation
        result = await contract_validator.validate_contract_compliance()
        
        # Verify results
        assert result.test_suite == "contract_compliance_validation"
        assert result.overall_status == "pass"
        assert result.performance_score >= 90.0
        assert result.certification_level == "production"
        
        # Verify contract-specific criteria
        criteria = result.validation_criteria
        assert criteria["processing_time_seconds"]["passed"] is True
        assert criteria["files_per_second"]["passed"] is True
        assert criteria["patterns_per_second"]["passed"] is True
        assert criteria["integration_latency_p95_ms"]["passed"] is True
        assert criteria["integration_throughput_rps"]["passed"] is True
    
    @pytest.mark.asyncio
    async def test_performance_regression_detection(self, contract_validator):
        """Test performance regression detection."""
        # Add historical results
        contract_validator.historical_results = [
            {
                "timestamp": "2025-01-01T10:00:00",
                "performance_score": 92.0,
                "target_level": "contract_compliance",
                "overall_status": "pass"
            },
            {
                "timestamp": "2025-01-02T10:00:00", 
                "performance_score": 93.0,
                "target_level": "contract_compliance",
                "overall_status": "pass"
            },
            {
                "timestamp": "2025-01-03T10:00:00",
                "performance_score": 91.5,
                "target_level": "contract_compliance",
                "overall_status": "pass"
            }
        ]
        
        # Test with good performance (no regression)
        no_regression = contract_validator._detect_regression(92.0, "contract_compliance")
        assert no_regression is False
        
        # Test with degraded performance (regression)
        regression = contract_validator._detect_regression(80.0, "contract_compliance")
        assert regression is True  # 80 < 92.17 * 0.9
    
    @pytest.mark.asyncio
    async def test_contract_specific_recommendations(self, contract_validator):
        """Test contract-specific performance recommendations."""
        validation_results = {
            "processing_time_seconds": {"passed": False, "actual": 35.0, "threshold": 30.0},
            "files_per_second": {"passed": False, "actual": 40.0, "threshold": 50.0},
            "patterns_per_second": {"passed": False, "actual": 80.0, "threshold": 100.0},
            "integration_latency_p95_ms": {"passed": False, "actual": 120.0, "threshold": 100.0},
            "integration_throughput_rps": {"passed": False, "actual": 15.0, "threshold": 20.0},
        }
        
        failed_criteria = list(validation_results.keys())
        
        recommendations = contract_validator._generate_recommendations(
            validation_results,
            failed_criteria
        )
        
        # Should have recommendations for each failed criterion
        assert len(recommendations) >= 5
        
        # Check for specific recommendations
        rec_text = " ".join(recommendations)
        assert "processing time" in rec_text.lower()
        assert "file processing rate" in rec_text.lower()
        assert "pattern detection rate" in rec_text.lower()
        assert "integration latency" in rec_text.lower()
        assert "integration throughput" in rec_text.lower()