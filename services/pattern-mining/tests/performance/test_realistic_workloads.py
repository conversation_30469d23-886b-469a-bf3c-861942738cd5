"""
Realistic Workload Testing for Pattern Mining Service

This module provides production-realistic workload testing including:
- Mixed request pattern simulation
- Large repository analysis scenarios
- Batch processing load testing
- API usage distribution modeling
- Cache behavior analysis under load
- Real-world data volume testing
"""

import pytest
import asyncio
import time
import logging
import random
from typing import Dict, List, Any, Optional, Tuple, Union
from datetime import datetime, timed<PERSON><PERSON>
from dataclasses import dataclass, asdict
from concurrent.futures import ThreadPoolExecutor
import statistics
import json
import hashlib
from enum import Enum

from httpx import AsyncClient
from tests.utils.performance import (
    PerformanceTestRunner, LatencyMeasurer, MemoryProfiler, 
    ThroughputTester, performance_test
)
from tests.utils.generators import CodeGenerator, PatternGenerator

logger = logging.getLogger(__name__)


class WorkloadType(Enum):
    """Types of realistic workloads to simulate."""
    DEVELOPMENT_TEAM = "development_team"
    CODE_REVIEW = "code_review"
    CI_CD_PIPELINE = "ci_cd_pipeline"
    RESEARCH_ANALYSIS = "research_analysis"
    BATCH_PROCESSING = "batch_processing"
    ENTERPRISE_AUDIT = "enterprise_audit"
    REAL_TIME_ANALYSIS = "real_time_analysis"


@dataclass
class WorkloadScenario:
    """Definition of a realistic workload scenario."""
    
    scenario_name: str
    workload_type: WorkloadType
    description: str
    
    # Load characteristics
    concurrent_users: int
    duration_minutes: int
    requests_distribution: Dict[str, float]  # Request type -> percentage
    
    # Data characteristics
    repository_sizes: Dict[str, float]  # Size category -> percentage
    complexity_distribution: Dict[str, float]  # Complexity -> percentage
    language_distribution: Dict[str, float]  # Language -> percentage
    
    # Performance expectations
    expected_cache_hit_rate: float
    expected_avg_response_time_ms: float
    expected_p95_response_time_ms: float
    expected_throughput_rps: float
    max_error_rate_percent: float
    
    # Batch processing specific
    batch_size: Optional[int] = None
    batch_interval_seconds: Optional[int] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        result = asdict(self)
        result['workload_type'] = self.workload_type.value
        return result


@dataclass
class CacheAnalysis:
    """Cache behavior analysis results."""
    
    total_requests: int
    cache_hits: int
    cache_misses: int
    cache_hit_rate: float
    avg_cache_response_time_ms: float
    avg_miss_response_time_ms: float
    cache_effectiveness_score: float
    
    # Cache warming analysis
    cache_warming_detected: bool
    warmup_duration_minutes: float
    steady_state_hit_rate: float
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return asdict(self)


@dataclass
class WorkloadResult:
    """Results from realistic workload testing."""
    
    # Test metadata
    scenario: WorkloadScenario
    start_time: datetime
    end_time: datetime
    actual_duration_minutes: float
    
    # Request analysis
    total_requests: int
    successful_requests: int
    failed_requests: int
    requests_by_type: Dict[str, int]
    
    # Performance metrics
    overall_avg_response_time_ms: float
    overall_p95_response_time_ms: float
    overall_p99_response_time_ms: float
    actual_throughput_rps: float
    
    # Performance by request type
    performance_by_type: Dict[str, Dict[str, float]]
    
    # Data analysis
    repositories_processed: int
    total_code_lines_analyzed: int
    patterns_detected: int
    
    # Cache analysis
    cache_analysis: CacheAnalysis
    
    # Resource utilization
    peak_memory_usage_mb: float
    avg_cpu_usage_percent: float
    peak_concurrent_requests: int
    
    # Quality metrics
    error_rate_percent: float
    timeout_rate_percent: float
    performance_sla_compliance: float
    
    # Realistic scenario validation
    meets_expectations: bool
    expectation_gaps: List[str]
    workload_authenticity_score: float
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        result = asdict(self)
        result['scenario'] = self.scenario.to_dict()
        result['start_time'] = self.start_time.isoformat()
        result['end_time'] = self.end_time.isoformat()
        result['cache_analysis'] = self.cache_analysis.to_dict()
        return result


class RealisticDataGenerator:
    """Generate realistic code samples and repository data."""
    
    def __init__(self):
        self.code_generator = CodeGenerator()
        self.repository_cache = {}  # Cache generated repositories
    
    def generate_realistic_repository(
        self, 
        size_category: str, 
        primary_language: str,
        complexity_level: str
    ) -> Dict[str, Any]:
        """Generate a realistic repository based on parameters."""
        
        # Cache key for repository reuse
        cache_key = f"{size_category}_{primary_language}_{complexity_level}"
        if cache_key in self.repository_cache:
            return self.repository_cache[cache_key]
        
        # Repository size mapping
        size_configs = {
            "small": {"files": (5, 25), "lines_per_file": (50, 300), "patterns_per_file": (1, 5)},
            "medium": {"files": (25, 100), "lines_per_file": (100, 800), "patterns_per_file": (2, 12)},
            "large": {"files": (100, 500), "lines_per_file": (200, 1500), "patterns_per_file": (5, 25)},
            "enterprise": {"files": (500, 2000), "lines_per_file": (300, 3000), "patterns_per_file": (10, 50)}
        }
        
        config = size_configs.get(size_category, size_configs["medium"])
        
        # Generate repository structure
        file_count = random.randint(*config["files"])
        total_lines = 0
        total_patterns = 0
        files = []
        
        for i in range(file_count):
            lines_in_file = random.randint(*config["lines_per_file"])
            patterns_in_file = random.randint(*config["patterns_per_file"])
            
            # Adjust patterns based on complexity
            if complexity_level == "simple":
                patterns_in_file = max(1, patterns_in_file // 2)
            elif complexity_level == "complex":
                patterns_in_file = patterns_in_file * 2
            
            # Generate file content based on complexity
            if complexity_level == "simple":
                code_content = self.code_generator.generate_function()
            elif complexity_level == "moderate":
                code_content = self.code_generator.generate_file_content("module", pattern_count=patterns_in_file)
            else:  # complex
                code_content = self.code_generator.generate_file_content("module", pattern_count=patterns_in_file)
                # Add some extra complexity
                code_content += f"\n# Complex analysis required for file {i}\n"
                code_content += "# " + "x" * (lines_in_file - len(code_content.split('\n')))
            
            files.append({
                "path": f"src/{primary_language}_module_{i}.{self._get_file_extension(primary_language)}",
                "language": primary_language,
                "lines": lines_in_file,
                "estimated_patterns": patterns_in_file,
                "code_content": code_content,
                "complexity": complexity_level,
                "last_modified": (datetime.now() - timedelta(days=random.randint(1, 365))).isoformat()
            })
            
            total_lines += lines_in_file
            total_patterns += patterns_in_file
        
        repository = {
            "repository_id": f"{size_category}-{primary_language}-{complexity_level}-{hash(cache_key) % 10000}",
            "size_category": size_category,
            "primary_language": primary_language,
            "complexity_level": complexity_level,
            "total_files": file_count,
            "total_lines": total_lines,
            "estimated_total_patterns": total_patterns,
            "files": files,
            "created_at": (datetime.now() - timedelta(days=random.randint(30, 1000))).isoformat(),
            "last_updated": datetime.now().isoformat()
        }
        
        # Cache for reuse
        self.repository_cache[cache_key] = repository
        return repository
    
    def _get_file_extension(self, language: str) -> str:
        """Get file extension for language."""
        extensions = {
            "python": "py",
            "javascript": "js",
            "typescript": "ts",
            "java": "java",
            "go": "go",
            "rust": "rs",
            "cpp": "cpp"
        }
        return extensions.get(language, "txt")
    
    def generate_batch_processing_data(
        self, 
        batch_size: int,
        complexity_mix: bool = True
    ) -> List[Dict[str, Any]]:
        """Generate data for batch processing scenarios."""
        
        batch_data = []
        
        for i in range(batch_size):
            if complexity_mix:
                # Mix of complexities for realistic batch
                complexity = random.choice(["simple", "moderate", "complex"])
                size = random.choice(["small", "medium", "large"])
            else:
                # Uniform complexity for controlled testing
                complexity = "moderate"
                size = "medium"
            
            language = random.choice(["python", "javascript", "typescript", "java"])
            
            repository = self.generate_realistic_repository(size, language, complexity)
            
            # Create batch item from random file in repository
            file_data = random.choice(repository["files"])
            
            batch_data.append({
                "repository_id": repository["repository_id"],
                "ast_data": {"type": "Module", "children": []},
                "code_content": file_data["code_content"],
                "file_path": file_data["path"],
                "language": file_data["language"],
                "detection_config": {
                    "confidence_threshold": 0.7,
                    "enable_ml_models": True,
                    "enable_heuristic_detection": True
                },
                "batch_metadata": {
                    "batch_index": i,
                    "complexity": complexity,
                    "size_category": size,
                    "estimated_patterns": file_data["estimated_patterns"]
                }
            })
        
        return batch_data


class WorkloadExecutor:
    """Execute realistic workload scenarios."""
    
    def __init__(self, client: AsyncClient):
        self.client = client
        self.data_generator = RealisticDataGenerator()
        self.request_metrics = []
        self.cache_metrics = []
        
    async def execute_workload_scenario(self, scenario: WorkloadScenario) -> WorkloadResult:
        """Execute a realistic workload scenario."""
        
        logger.info(f"Starting workload scenario: {scenario.scenario_name}")
        logger.info(f"Load: {scenario.concurrent_users} users for {scenario.duration_minutes} minutes")
        
        start_time = datetime.utcnow()
        self.request_metrics = []
        self.cache_metrics = []
        
        # Execute scenario based on workload type
        if scenario.workload_type == WorkloadType.BATCH_PROCESSING:
            await self._execute_batch_processing_workload(scenario)
        elif scenario.workload_type == WorkloadType.CI_CD_PIPELINE:
            await self._execute_cicd_pipeline_workload(scenario)
        elif scenario.workload_type == WorkloadType.DEVELOPMENT_TEAM:
            await self._execute_development_team_workload(scenario)
        elif scenario.workload_type == WorkloadType.CODE_REVIEW:
            await self._execute_code_review_workload(scenario)
        elif scenario.workload_type == WorkloadType.RESEARCH_ANALYSIS:
            await self._execute_research_analysis_workload(scenario)
        elif scenario.workload_type == WorkloadType.ENTERPRISE_AUDIT:
            await self._execute_enterprise_audit_workload(scenario)
        else:
            await self._execute_generic_workload(scenario)
        
        end_time = datetime.utcnow()
        actual_duration = (end_time - start_time).total_seconds() / 60
        
        # Analyze results
        result = self._analyze_workload_results(scenario, start_time, end_time, actual_duration)
        
        logger.info(f"Workload scenario completed: {result.meets_expectations}")
        logger.info(f"Performance: {result.actual_throughput_rps:.1f} RPS, "
                   f"{result.overall_avg_response_time_ms:.1f}ms avg response time")
        
        return result
    
    async def _execute_batch_processing_workload(self, scenario: WorkloadScenario):
        """Execute batch processing workload."""
        
        if not scenario.batch_size or not scenario.batch_interval_seconds:
            raise ValueError("Batch processing requires batch_size and batch_interval_seconds")
        
        duration_seconds = scenario.duration_minutes * 60
        batch_interval = scenario.batch_interval_seconds
        
        start_time = time.perf_counter()
        end_time = start_time + duration_seconds
        
        batch_number = 0
        
        while time.perf_counter() < end_time:
            batch_start = time.perf_counter()
            batch_number += 1
            
            logger.info(f"Processing batch {batch_number} with {scenario.batch_size} items")
            
            # Generate batch data
            batch_data = self.data_generator.generate_batch_processing_data(
                scenario.batch_size,
                complexity_mix=True
            )
            
            # Process batch concurrently
            semaphore = asyncio.Semaphore(scenario.concurrent_users)
            
            async def process_batch_item(item_data: Dict[str, Any]) -> Dict[str, Any]:
                async with semaphore:
                    return await self._make_workload_request(item_data, "batch_processing")
            
            # Execute batch
            tasks = [process_batch_item(item) for item in batch_data]
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Process results
            for result in batch_results:
                if isinstance(result, Exception):
                    self.request_metrics.append({
                        "success": False,
                        "error": str(result),
                        "response_time_ms": 0,
                        "status_code": 0,
                        "timestamp": datetime.utcnow(),
                        "request_type": "batch_processing",
                        "batch_number": batch_number
                    })
                else:
                    result["batch_number"] = batch_number
                    self.request_metrics.append(result)
            
            # Wait for next batch interval
            batch_end = time.perf_counter()
            batch_duration = batch_end - batch_start
            
            if batch_duration < batch_interval:
                await asyncio.sleep(batch_interval - batch_duration)
            
            logger.info(f"Batch {batch_number} completed in {batch_duration:.1f}s")
    
    async def _execute_cicd_pipeline_workload(self, scenario: WorkloadScenario):
        """Execute CI/CD pipeline workload with build-like patterns."""
        
        duration_seconds = scenario.duration_minutes * 60
        
        # CI/CD typically has bursts of activity followed by quiet periods
        burst_duration = 300  # 5 minutes of intense activity
        quiet_duration = 180  # 3 minutes of low activity
        
        start_time = time.perf_counter()
        end_time = start_time + duration_seconds
        
        cycle_number = 0
        
        while time.perf_counter() < end_time:
            cycle_number += 1
            
            # Burst phase - simulate CI/CD build analyzing multiple files
            logger.info(f"CI/CD cycle {cycle_number}: Burst phase")
            await self._execute_burst_phase(
                scenario, 
                burst_duration, 
                scenario.concurrent_users,
                "cicd_burst"
            )
            
            if time.perf_counter() >= end_time:
                break
            
            # Quiet phase - minimal activity
            logger.info(f"CI/CD cycle {cycle_number}: Quiet phase")
            await self._execute_burst_phase(
                scenario,
                quiet_duration,
                max(1, scenario.concurrent_users // 4),
                "cicd_quiet"
            )
    
    async def _execute_development_team_workload(self, scenario: WorkloadScenario):
        """Execute development team workload with realistic patterns."""
        
        duration_seconds = scenario.duration_minutes * 60
        
        # Development teams have patterns: morning ramp-up, lunch dip, afternoon activity
        phases = [
            {"name": "morning_ramp", "duration_ratio": 0.3, "intensity": 0.8},
            {"name": "midday_peak", "duration_ratio": 0.4, "intensity": 1.0}, 
            {"name": "afternoon_taper", "duration_ratio": 0.3, "intensity": 0.6}
        ]
        
        for phase in phases:
            phase_duration = duration_seconds * phase["duration_ratio"]
            phase_users = int(scenario.concurrent_users * phase["intensity"])
            
            logger.info(f"Development team phase: {phase['name']} ({phase_users} users)")
            
            await self._execute_development_phase(
                scenario,
                phase_duration,
                phase_users,
                phase["name"]
            )
    
    async def _execute_code_review_workload(self, scenario: WorkloadScenario):
        """Execute code review workload with thorough analysis patterns."""
        
        # Code review involves examining multiple files in detail
        duration_seconds = scenario.duration_minutes * 60
        
        # Generate repository data for review
        repositories = []
        for size in ["medium", "large"]:
            for lang in ["python", "javascript", "typescript"]:
                repo = self.data_generator.generate_realistic_repository(size, lang, "moderate")
                repositories.append(repo)
        
        start_time = time.perf_counter()
        end_time = start_time + duration_seconds
        
        semaphore = asyncio.Semaphore(scenario.concurrent_users)
        
        while time.perf_counter() < end_time:
            # Select repository for review
            repo = random.choice(repositories)
            
            # Review multiple files from the repository
            files_to_review = random.sample(repo["files"], min(5, len(repo["files"])))
            
            # Create review tasks
            tasks = []
            for file_data in files_to_review:
                request_data = {
                    "repository_id": repo["repository_id"],
                    "ast_data": {"type": "Module", "children": []},
                    "code_content": file_data["code_content"],
                    "file_path": file_data["path"],
                    "language": file_data["language"],
                    "detection_config": {
                        "confidence_threshold": 0.8,  # Higher threshold for code review
                        "enable_ml_models": True,
                        "enable_heuristic_detection": True
                    },
                    "review_context": {
                        "review_type": "code_review",
                        "thoroughness": "high",
                        "repository_size": repo["size_category"]
                    }
                }
                
                async def review_file(data):
                    async with semaphore:
                        return await self._make_workload_request(data, "code_review")
                
                tasks.append(review_file(request_data))
            
            # Execute review batch
            if tasks:
                await asyncio.gather(*tasks, return_exceptions=True)
            
            # Think time between reviews
            await asyncio.sleep(random.uniform(5, 15))
    
    async def _execute_research_analysis_workload(self, scenario: WorkloadScenario):
        """Execute research analysis workload with deep analysis patterns."""
        
        # Research involves thorough analysis of large codebases
        duration_seconds = scenario.duration_minutes * 60
        
        # Generate enterprise-sized repositories for research
        research_repos = []
        for i in range(3):
            repo = self.data_generator.generate_realistic_repository(
                "enterprise", 
                random.choice(["python", "java", "cpp"]), 
                "complex"
            )
            research_repos.append(repo)
        
        start_time = time.perf_counter()
        end_time = start_time + duration_seconds
        
        research_session = 0
        
        while time.perf_counter() < end_time:
            research_session += 1
            repo = random.choice(research_repos)
            
            logger.info(f"Research session {research_session}: Analyzing {repo['repository_id']}")
            
            # Research analyzes many files with high thoroughness
            files_to_analyze = random.sample(repo["files"], min(20, len(repo["files"])))
            
            # Create research tasks with limited concurrency (researchers work methodically)
            semaphore = asyncio.Semaphore(min(5, scenario.concurrent_users))
            
            async def analyze_research_file(file_data):
                async with semaphore:
                    request_data = {
                        "repository_id": repo["repository_id"],
                        "ast_data": {"type": "Module", "children": []},
                        "code_content": file_data["code_content"],
                        "file_path": file_data["path"],
                        "language": file_data["language"],
                        "detection_config": {
                            "confidence_threshold": 0.6,  # Lower threshold for research
                            "enable_ml_models": True,
                            "enable_heuristic_detection": True,
                            "research_mode": True
                        },
                        "research_context": {
                            "analysis_depth": "comprehensive",
                            "pattern_types": "all",
                            "session_id": research_session
                        }
                    }
                    return await self._make_workload_request(request_data, "research_analysis")
            
            # Execute research analysis
            tasks = [analyze_research_file(file_data) for file_data in files_to_analyze]
            await asyncio.gather(*tasks, return_exceptions=True)
            
            # Research sessions have longer think time
            await asyncio.sleep(random.uniform(30, 90))
    
    async def _execute_enterprise_audit_workload(self, scenario: WorkloadScenario):
        """Execute enterprise audit workload with comprehensive coverage."""
        
        # Enterprise audit requires systematic analysis of large codebases
        duration_seconds = scenario.duration_minutes * 60
        
        # Generate multiple enterprise repositories
        audit_repos = []
        languages = ["python", "java", "javascript", "typescript", "cpp"]
        for lang in languages:
            repo = self.data_generator.generate_realistic_repository("enterprise", lang, "complex")
            audit_repos.append(repo)
        
        start_time = time.perf_counter()
        end_time = start_time + duration_seconds
        
        # Systematic audit approach
        repo_index = 0
        
        while time.perf_counter() < end_time:
            current_repo = audit_repos[repo_index % len(audit_repos)]
            repo_index += 1
            
            logger.info(f"Enterprise audit: Repository {current_repo['repository_id']}")
            
            # Audit entire repository systematically
            semaphore = asyncio.Semaphore(scenario.concurrent_users)
            
            async def audit_file(file_data):
                async with semaphore:
                    request_data = {
                        "repository_id": current_repo["repository_id"],
                        "ast_data": {"type": "Module", "children": []},
                        "code_content": file_data["code_content"],
                        "file_path": file_data["path"],
                        "language": file_data["language"],
                        "detection_config": {
                            "confidence_threshold": 0.7,
                            "enable_ml_models": True,
                            "enable_heuristic_detection": True,
                            "audit_mode": True
                        },
                        "audit_context": {
                            "audit_type": "enterprise_compliance",
                            "coverage_requirement": "complete",
                            "quality_threshold": "high"
                        }
                    }
                    return await self._make_workload_request(request_data, "enterprise_audit")
            
            # Process all files in repository
            tasks = [audit_file(file_data) for file_data in current_repo["files"]]
            
            # Process in chunks to avoid overwhelming the system
            chunk_size = 50
            for i in range(0, len(tasks), chunk_size):
                chunk = tasks[i:i + chunk_size]
                await asyncio.gather(*chunk, return_exceptions=True)
                
                # Brief pause between chunks
                await asyncio.sleep(5)
    
    async def _execute_generic_workload(self, scenario: WorkloadScenario):
        """Execute generic workload based on scenario configuration."""
        
        duration_seconds = scenario.duration_minutes * 60
        
        # Generate diverse repositories based on scenario distributions
        repositories = []
        
        # Create repositories according to size distribution
        total_repos = 20
        for size, percentage in scenario.repository_sizes.items():
            repo_count = int(total_repos * percentage)
            for i in range(repo_count):
                language = random.choices(
                    list(scenario.language_distribution.keys()),
                    weights=list(scenario.language_distribution.values())
                )[0]
                
                complexity = random.choices(
                    list(scenario.complexity_distribution.keys()),
                    weights=list(scenario.complexity_distribution.values())
                )[0]
                
                repo = self.data_generator.generate_realistic_repository(size, language, complexity)
                repositories.append(repo)
        
        # Execute workload
        start_time = time.perf_counter()
        end_time = start_time + duration_seconds
        
        semaphore = asyncio.Semaphore(scenario.concurrent_users)
        
        while time.perf_counter() < end_time:
            # Select request type based on distribution
            request_type = random.choices(
                list(scenario.requests_distribution.keys()),
                weights=list(scenario.requests_distribution.values())
            )[0]
            
            # Select repository and file
            repo = random.choice(repositories) if repositories else None
            if not repo:
                continue
            
            file_data = random.choice(repo["files"])
            
            request_data = {
                "repository_id": repo["repository_id"],
                "ast_data": {"type": "Module", "children": []},
                "code_content": file_data["code_content"],
                "file_path": file_data["path"],
                "language": file_data["language"],
                "detection_config": {
                    "confidence_threshold": 0.7,
                    "enable_ml_models": True,
                    "enable_heuristic_detection": True
                },
                "workload_context": {
                    "request_type": request_type,
                    "complexity": repo["complexity_level"],
                    "size_category": repo["size_category"]
                }
            }
            
            async def make_request():
                async with semaphore:
                    return await self._make_workload_request(request_data, request_type)
            
            # Create and start task
            asyncio.create_task(make_request())
            
            # Control request rate
            await asyncio.sleep(random.uniform(0.1, 2.0))
    
    async def _execute_burst_phase(self, scenario: WorkloadScenario, duration: float, users: int, phase_type: str):
        """Execute a burst phase with specified parameters."""
        
        start_time = time.perf_counter()
        end_time = start_time + duration
        
        semaphore = asyncio.Semaphore(users)
        
        while time.perf_counter() < end_time:
            # Generate request data based on phase type
            if phase_type.startswith("cicd"):
                # CI/CD analyzes multiple files from the same repository
                repo = self.data_generator.generate_realistic_repository("medium", "python", "moderate")
                file_data = random.choice(repo["files"])
            else:
                # Generic request
                repo = self.data_generator.generate_realistic_repository("small", "python", "simple")
                file_data = random.choice(repo["files"])
            
            request_data = {
                "repository_id": repo["repository_id"],
                "ast_data": {"type": "Module", "children": []},
                "code_content": file_data["code_content"],
                "file_path": file_data["path"],
                "language": file_data["language"],
                "detection_config": {
                    "confidence_threshold": 0.7,
                    "enable_ml_models": True,
                    "enable_heuristic_detection": True
                },
                "phase_context": {
                    "phase_type": phase_type,
                    "burst_intensity": users / scenario.concurrent_users
                }
            }
            
            async def make_burst_request():
                async with semaphore:
                    return await self._make_workload_request(request_data, phase_type)
            
            asyncio.create_task(make_burst_request())
            
            # High frequency during burst
            await asyncio.sleep(random.uniform(0.05, 0.5))
    
    async def _execute_development_phase(self, scenario: WorkloadScenario, duration: float, users: int, phase_name: str):
        """Execute a development team phase."""
        
        start_time = time.perf_counter()
        end_time = start_time + duration
        
        semaphore = asyncio.Semaphore(users)
        
        # Generate development repositories
        dev_repos = []
        for size in ["small", "medium"]:
            for lang in ["python", "javascript", "typescript"]:
                repo = self.data_generator.generate_realistic_repository(size, lang, "moderate")
                dev_repos.append(repo)
        
        while time.perf_counter() < end_time:
            repo = random.choice(dev_repos)
            file_data = random.choice(repo["files"])
            
            request_data = {
                "repository_id": repo["repository_id"],
                "ast_data": {"type": "Module", "children": []},
                "code_content": file_data["code_content"],
                "file_path": file_data["path"],
                "language": file_data["language"],
                "detection_config": {
                    "confidence_threshold": 0.7,
                    "enable_ml_models": True,
                    "enable_heuristic_detection": True
                },
                "development_context": {
                    "phase": phase_name,
                    "team_size": users,
                    "development_stage": "active"
                }
            }
            
            async def make_dev_request():
                async with semaphore:
                    return await self._make_workload_request(request_data, "development")
            
            asyncio.create_task(make_dev_request())
            
            # Variable think time based on phase
            if phase_name == "morning_ramp":
                await asyncio.sleep(random.uniform(2, 8))
            elif phase_name == "midday_peak":
                await asyncio.sleep(random.uniform(1, 4))
            else:  # afternoon_taper
                await asyncio.sleep(random.uniform(3, 10))
    
    async def _make_workload_request(self, request_data: Dict[str, Any], request_type: str) -> Dict[str, Any]:
        """Make a single workload request with detailed tracking."""
        
        start_time = time.perf_counter()
        timestamp = datetime.utcnow()
        
        # Generate request ID for cache tracking
        request_id = hashlib.md5(json.dumps(request_data, sort_keys=True).encode()).hexdigest()
        
        try:
            response = await asyncio.wait_for(
                self.client.post("/api/v1/patterns/detect", json=request_data),
                timeout=120.0  # Longer timeout for complex analysis
            )
            
            end_time = time.perf_counter()
            response_time_ms = (end_time - start_time) * 1000
            
            # Determine if this was likely a cache hit (fast response for same content)
            is_cache_hit = response_time_ms < 100 and response.status_code == 200
            
            result = {
                "success": response.status_code < 400,
                "status_code": response.status_code,
                "response_time_ms": response_time_ms,
                "timestamp": timestamp,
                "request_type": request_type,
                "request_id": request_id,
                "is_cache_hit": is_cache_hit,
                "repository_id": request_data.get("repository_id", "unknown"),
                "language": request_data.get("language", "unknown"),
                "complexity": request_data.get("workload_context", {}).get("complexity", "unknown"),
                "error": None if response.status_code < 400 else f"HTTP {response.status_code}"
            }
            
            # Track cache metrics
            self.cache_metrics.append({
                "request_id": request_id,
                "cache_hit": is_cache_hit,
                "response_time_ms": response_time_ms,
                "timestamp": timestamp
            })
            
            return result
            
        except asyncio.TimeoutError:
            end_time = time.perf_counter()
            response_time_ms = (end_time - start_time) * 1000
            
            return {
                "success": False,
                "status_code": 408,
                "response_time_ms": response_time_ms,
                "timestamp": timestamp,
                "request_type": request_type,
                "request_id": request_id,
                "is_cache_hit": False,
                "repository_id": request_data.get("repository_id", "unknown"),
                "language": request_data.get("language", "unknown"),
                "complexity": request_data.get("workload_context", {}).get("complexity", "unknown"),
                "error": "Request timeout"
            }
            
        except Exception as e:
            end_time = time.perf_counter()
            response_time_ms = (end_time - start_time) * 1000
            
            return {
                "success": False,
                "status_code": 500,
                "response_time_ms": response_time_ms,
                "timestamp": timestamp,
                "request_type": request_type,
                "request_id": request_id,
                "is_cache_hit": False,
                "repository_id": request_data.get("repository_id", "unknown"),
                "language": request_data.get("language", "unknown"),
                "complexity": request_data.get("workload_context", {}).get("complexity", "unknown"),
                "error": str(e)
            }
    
    def _analyze_workload_results(
        self,
        scenario: WorkloadScenario,
        start_time: datetime,
        end_time: datetime,
        actual_duration_minutes: float
    ) -> WorkloadResult:
        """Analyze workload execution results."""
        
        if not self.request_metrics:
            # Return empty result if no metrics
            return WorkloadResult(
                scenario=scenario,
                start_time=start_time,
                end_time=end_time,
                actual_duration_minutes=actual_duration_minutes,
                total_requests=0,
                successful_requests=0,
                failed_requests=0,
                requests_by_type={},
                overall_avg_response_time_ms=0,
                overall_p95_response_time_ms=0,
                overall_p99_response_time_ms=0,
                actual_throughput_rps=0,
                performance_by_type={},
                repositories_processed=0,
                total_code_lines_analyzed=0,
                patterns_detected=0,
                cache_analysis=CacheAnalysis(0, 0, 0, 0, 0, 0, 0, False, 0, 0),
                peak_memory_usage_mb=0,
                avg_cpu_usage_percent=0,
                peak_concurrent_requests=0,
                error_rate_percent=0,
                timeout_rate_percent=0,
                performance_sla_compliance=0,
                meets_expectations=False,
                expectation_gaps=[],
                workload_authenticity_score=0
            )
        
        # Basic metrics
        total_requests = len(self.request_metrics)
        successful_requests = sum(1 for r in self.request_metrics if r.get("success", False))
        failed_requests = total_requests - successful_requests
        
        # Requests by type
        requests_by_type = {}
        for request in self.request_metrics:
            req_type = request.get("request_type", "unknown")
            requests_by_type[req_type] = requests_by_type.get(req_type, 0) + 1
        
        # Overall performance metrics
        successful_times = [r["response_time_ms"] for r in self.request_metrics if r.get("success", False)]
        
        if successful_times:
            overall_avg_response_time = statistics.mean(successful_times)
            sorted_times = sorted(successful_times)
            overall_p95_response_time = sorted_times[int(len(sorted_times) * 0.95)] if sorted_times else 0
            overall_p99_response_time = sorted_times[int(len(sorted_times) * 0.99)] if sorted_times else 0
        else:
            overall_avg_response_time = overall_p95_response_time = overall_p99_response_time = 0
        
        # Throughput
        actual_throughput_rps = total_requests / (actual_duration_minutes * 60) if actual_duration_minutes > 0 else 0
        
        # Performance by request type
        performance_by_type = {}
        for req_type in requests_by_type.keys():
            type_times = [r["response_time_ms"] for r in self.request_metrics 
                         if r.get("request_type") == req_type and r.get("success", False)]
            if type_times:
                performance_by_type[req_type] = {
                    "avg_response_time_ms": statistics.mean(type_times),
                    "p95_response_time_ms": sorted(type_times)[int(len(type_times) * 0.95)] if type_times else 0,
                    "success_rate": sum(1 for r in self.request_metrics 
                                      if r.get("request_type") == req_type and r.get("success", False)) / 
                                   requests_by_type[req_type] * 100
                }
        
        # Data analysis
        unique_repos = len(set(r.get("repository_id", "") for r in self.request_metrics))
        total_code_lines = 0  # Would need to track this from request data
        patterns_detected = 0  # Would need to parse response data
        
        # Cache analysis
        cache_analysis = self._analyze_cache_behavior()
        
        # Resource utilization (simplified)
        peak_memory_usage = 0  # Would need actual memory monitoring
        avg_cpu_usage = 0  # Would need actual CPU monitoring
        peak_concurrent_requests = scenario.concurrent_users
        
        # Quality metrics
        error_rate_percent = (failed_requests / total_requests * 100) if total_requests > 0 else 0
        timeout_requests = sum(1 for r in self.request_metrics if r.get("error") == "Request timeout")
        timeout_rate_percent = (timeout_requests / total_requests * 100) if total_requests > 0 else 0
        
        # SLA compliance
        sla_compliance = self._calculate_sla_compliance(scenario, overall_avg_response_time, 
                                                       overall_p95_response_time, error_rate_percent)
        
        # Expectation validation
        meets_expectations, expectation_gaps = self._validate_expectations(
            scenario, overall_avg_response_time, overall_p95_response_time, 
            actual_throughput_rps, error_rate_percent, cache_analysis
        )
        
        # Workload authenticity score
        authenticity_score = self._calculate_authenticity_score(scenario, requests_by_type, performance_by_type)
        
        return WorkloadResult(
            scenario=scenario,
            start_time=start_time,
            end_time=end_time,
            actual_duration_minutes=actual_duration_minutes,
            total_requests=total_requests,
            successful_requests=successful_requests,
            failed_requests=failed_requests,
            requests_by_type=requests_by_type,
            overall_avg_response_time_ms=overall_avg_response_time,
            overall_p95_response_time_ms=overall_p95_response_time,
            overall_p99_response_time_ms=overall_p99_response_time,
            actual_throughput_rps=actual_throughput_rps,
            performance_by_type=performance_by_type,
            repositories_processed=unique_repos,
            total_code_lines_analyzed=total_code_lines,
            patterns_detected=patterns_detected,
            cache_analysis=cache_analysis,
            peak_memory_usage_mb=peak_memory_usage,
            avg_cpu_usage_percent=avg_cpu_usage,
            peak_concurrent_requests=peak_concurrent_requests,
            error_rate_percent=error_rate_percent,
            timeout_rate_percent=timeout_rate_percent,
            performance_sla_compliance=sla_compliance,
            meets_expectations=meets_expectations,
            expectation_gaps=expectation_gaps,
            workload_authenticity_score=authenticity_score
        )
    
    def _analyze_cache_behavior(self) -> CacheAnalysis:
        """Analyze cache behavior from request metrics."""
        
        if not self.cache_metrics:
            return CacheAnalysis(0, 0, 0, 0, 0, 0, 0, False, 0, 0)
        
        total_requests = len(self.cache_metrics)
        cache_hits = sum(1 for m in self.cache_metrics if m.get("cache_hit", False))
        cache_misses = total_requests - cache_hits
        cache_hit_rate = (cache_hits / total_requests * 100) if total_requests > 0 else 0
        
        # Response times
        hit_times = [m["response_time_ms"] for m in self.cache_metrics if m.get("cache_hit", False)]
        miss_times = [m["response_time_ms"] for m in self.cache_metrics if not m.get("cache_hit", False)]
        
        avg_cache_response_time = statistics.mean(hit_times) if hit_times else 0
        avg_miss_response_time = statistics.mean(miss_times) if miss_times else 0
        
        # Cache effectiveness (higher is better)
        cache_effectiveness = 0
        if avg_miss_response_time > 0 and avg_cache_response_time > 0:
            cache_effectiveness = min(100, (avg_miss_response_time - avg_cache_response_time) / avg_miss_response_time * 100)
        
        # Cache warming detection (simplified)
        cache_warming_detected = False
        warmup_duration = 0
        steady_state_hit_rate = cache_hit_rate
        
        if len(self.cache_metrics) > 20:
            # Check if hit rate improved over time
            early_metrics = self.cache_metrics[:len(self.cache_metrics)//3]
            late_metrics = self.cache_metrics[-len(self.cache_metrics)//3:]
            
            early_hit_rate = sum(1 for m in early_metrics if m.get("cache_hit", False)) / len(early_metrics) * 100
            late_hit_rate = sum(1 for m in late_metrics if m.get("cache_hit", False)) / len(late_metrics) * 100
            
            if late_hit_rate > early_hit_rate + 10:  # 10% improvement
                cache_warming_detected = True
                warmup_duration = 5.0  # Simplified
                steady_state_hit_rate = late_hit_rate
        
        return CacheAnalysis(
            total_requests=total_requests,
            cache_hits=cache_hits,
            cache_misses=cache_misses,
            cache_hit_rate=cache_hit_rate,
            avg_cache_response_time_ms=avg_cache_response_time,
            avg_miss_response_time_ms=avg_miss_response_time,
            cache_effectiveness_score=cache_effectiveness,
            cache_warming_detected=cache_warming_detected,
            warmup_duration_minutes=warmup_duration,
            steady_state_hit_rate=steady_state_hit_rate
        )
    
    def _calculate_sla_compliance(
        self, 
        scenario: WorkloadScenario,
        avg_response_time: float,
        p95_response_time: float,
        error_rate: float
    ) -> float:
        """Calculate SLA compliance score."""
        
        compliance_scores = []
        
        # Response time compliance
        if avg_response_time <= scenario.expected_avg_response_time_ms:
            compliance_scores.append(100)
        else:
            ratio = scenario.expected_avg_response_time_ms / avg_response_time
            compliance_scores.append(max(0, ratio * 100))
        
        # P95 compliance
        if p95_response_time <= scenario.expected_p95_response_time_ms:
            compliance_scores.append(100)
        else:
            ratio = scenario.expected_p95_response_time_ms / p95_response_time
            compliance_scores.append(max(0, ratio * 100))
        
        # Error rate compliance
        if error_rate <= scenario.max_error_rate_percent:
            compliance_scores.append(100)
        else:
            ratio = scenario.max_error_rate_percent / error_rate
            compliance_scores.append(max(0, ratio * 100))
        
        return statistics.mean(compliance_scores) if compliance_scores else 0
    
    def _validate_expectations(
        self,
        scenario: WorkloadScenario,
        avg_response_time: float,
        p95_response_time: float,
        throughput_rps: float,
        error_rate: float,
        cache_analysis: CacheAnalysis
    ) -> Tuple[bool, List[str]]:
        """Validate results against scenario expectations."""
        
        gaps = []
        
        # Check response time expectations
        if avg_response_time > scenario.expected_avg_response_time_ms * 1.2:
            gaps.append(f"Average response time ({avg_response_time:.1f}ms) exceeds expectation ({scenario.expected_avg_response_time_ms:.1f}ms)")
        
        if p95_response_time > scenario.expected_p95_response_time_ms * 1.2:
            gaps.append(f"P95 response time ({p95_response_time:.1f}ms) exceeds expectation ({scenario.expected_p95_response_time_ms:.1f}ms)")
        
        # Check throughput expectations
        if throughput_rps < scenario.expected_throughput_rps * 0.8:
            gaps.append(f"Throughput ({throughput_rps:.1f} RPS) below expectation ({scenario.expected_throughput_rps:.1f} RPS)")
        
        # Check error rate expectations
        if error_rate > scenario.max_error_rate_percent:
            gaps.append(f"Error rate ({error_rate:.1f}%) exceeds maximum ({scenario.max_error_rate_percent:.1f}%)")
        
        # Check cache hit rate expectations
        if cache_analysis.cache_hit_rate < scenario.expected_cache_hit_rate * 0.8:
            gaps.append(f"Cache hit rate ({cache_analysis.cache_hit_rate:.1f}%) below expectation ({scenario.expected_cache_hit_rate:.1f}%)")
        
        meets_expectations = len(gaps) == 0
        return meets_expectations, gaps
    
    def _calculate_authenticity_score(
        self,
        scenario: WorkloadScenario,
        requests_by_type: Dict[str, int],
        performance_by_type: Dict[str, Dict[str, float]]
    ) -> float:
        """Calculate how authentic/realistic the workload execution was."""
        
        authenticity_factors = []
        
        # Request type distribution authenticity
        if scenario.requests_distribution:
            expected_distribution = scenario.requests_distribution
            actual_distribution = {k: v / sum(requests_by_type.values()) for k, v in requests_by_type.items()}
            
            distribution_score = 100
            for req_type, expected_pct in expected_distribution.items():
                actual_pct = actual_distribution.get(req_type, 0)
                deviation = abs(expected_pct - actual_pct)
                distribution_score -= deviation * 100  # Penalize deviation
            
            authenticity_factors.append(max(0, distribution_score))
        
        # Performance variation authenticity (realistic workloads should have some variation)
        if performance_by_type:
            response_times = [perf["avg_response_time_ms"] for perf in performance_by_type.values()]
            if len(response_times) > 1:
                variation_coefficient = statistics.stdev(response_times) / statistics.mean(response_times)
                # Some variation is good (realistic), too much is bad
                if 0.1 <= variation_coefficient <= 0.5:
                    authenticity_factors.append(100)
                else:
                    authenticity_factors.append(max(0, 100 - abs(variation_coefficient - 0.3) * 200))
            else:
                authenticity_factors.append(50)  # Single request type is less realistic
        
        # Duration authenticity (did test run for expected duration)
        duration_score = 100  # Simplified - would check actual vs expected duration
        authenticity_factors.append(duration_score)
        
        return statistics.mean(authenticity_factors) if authenticity_factors else 0


@pytest.mark.performance
@pytest.mark.realistic
@pytest.mark.asyncio
class TestRealisticWorkloads:
    """Realistic workload testing suite."""
    
    async def test_development_team_workload(self, async_test_client: AsyncClient):
        """Test realistic development team workload."""
        scenario = WorkloadScenario(
            scenario_name="Development Team Daily Work",
            workload_type=WorkloadType.DEVELOPMENT_TEAM,
            description="Simulate daily development team activities",
            concurrent_users=12,
            duration_minutes=20,
            requests_distribution={
                "code_analysis": 0.4,
                "pattern_detection": 0.3,
                "quality_check": 0.2,
                "quick_scan": 0.1
            },
            repository_sizes={
                "small": 0.3,
                "medium": 0.6,
                "large": 0.1
            },
            complexity_distribution={
                "simple": 0.2,
                "moderate": 0.6,
                "complex": 0.2
            },
            language_distribution={
                "python": 0.4,
                "javascript": 0.3,
                "typescript": 0.2,
                "java": 0.1
            },
            expected_cache_hit_rate=60.0,
            expected_avg_response_time_ms=150.0,
            expected_p95_response_time_ms=400.0,
            expected_throughput_rps=8.0,
            max_error_rate_percent=3.0
        )
        
        executor = WorkloadExecutor(async_test_client)
        result = await executor.execute_workload_scenario(scenario)
        
        # Development team workload assertions
        assert result.meets_expectations, f"Development workload failed expectations: {result.expectation_gaps}"
        assert result.workload_authenticity_score >= 70.0, f"Workload not realistic enough: {result.workload_authenticity_score:.1f}/100"
        
        # Team productivity metrics
        assert result.overall_avg_response_time_ms <= 200.0, f"Response time too slow for development: {result.overall_avg_response_time_ms:.1f}ms"
        assert result.error_rate_percent <= 5.0, f"Too many errors for development team: {result.error_rate_percent:.1f}%"
        
        # Cache should be effective for repeated code analysis
        assert result.cache_analysis.cache_hit_rate >= 40.0, f"Cache hit rate too low: {result.cache_analysis.cache_hit_rate:.1f}%"
        
        logger.info(f"Development team workload: {result.actual_throughput_rps:.1f} RPS, "
                   f"{result.cache_analysis.cache_hit_rate:.1f}% cache hit rate")
    
    async def test_code_review_workload(self, async_test_client: AsyncClient):
        """Test code review workload with thorough analysis."""
        scenario = WorkloadScenario(
            scenario_name="Code Review Sessions",
            workload_type=WorkloadType.CODE_REVIEW,
            description="Simulate thorough code review sessions",
            concurrent_users=8,
            duration_minutes=15,
            requests_distribution={
                "thorough_analysis": 0.6,
                "pattern_detection": 0.3,
                "quality_assessment": 0.1
            },
            repository_sizes={
                "medium": 0.7,
                "large": 0.3
            },
            complexity_distribution={
                "moderate": 0.4,
                "complex": 0.6
            },
            language_distribution={
                "python": 0.3,
                "javascript": 0.3,
                "typescript": 0.2,
                "java": 0.2
            },
            expected_cache_hit_rate=30.0,  # Lower due to thorough analysis
            expected_avg_response_time_ms=300.0,  # Slower due to complexity
            expected_p95_response_time_ms=800.0,
            expected_throughput_rps=4.0,
            max_error_rate_percent=2.0
        )
        
        executor = WorkloadExecutor(async_test_client)
        result = await executor.execute_workload_scenario(scenario)
        
        # Code review workload assertions
        assert result.error_rate_percent <= 3.0, f"Code review error rate too high: {result.error_rate_percent:.1f}%"
        assert result.overall_avg_response_time_ms <= 500.0, f"Code review too slow: {result.overall_avg_response_time_ms:.1f}ms"
        
        # Code review should handle complex analysis
        if "thorough_analysis" in result.performance_by_type:
            thorough_perf = result.performance_by_type["thorough_analysis"]
            assert thorough_perf["success_rate"] >= 95.0, f"Thorough analysis success rate too low: {thorough_perf['success_rate']:.1f}%"
        
        # Should process multiple repositories
        assert result.repositories_processed >= 3, f"Too few repositories processed: {result.repositories_processed}"
        
        logger.info(f"Code review workload: {result.repositories_processed} repositories, "
                   f"{result.overall_avg_response_time_ms:.1f}ms avg response time")
    
    async def test_batch_processing_workload(self, async_test_client: AsyncClient):
        """Test batch processing workload."""
        scenario = WorkloadScenario(
            scenario_name="Batch Processing Pipeline",
            workload_type=WorkloadType.BATCH_PROCESSING,
            description="Simulate batch processing of multiple repositories",
            concurrent_users=20,
            duration_minutes=10,
            requests_distribution={
                "batch_analysis": 1.0
            },
            repository_sizes={
                "small": 0.2,
                "medium": 0.5,
                "large": 0.3
            },
            complexity_distribution={
                "simple": 0.3,
                "moderate": 0.4,
                "complex": 0.3
            },
            language_distribution={
                "python": 0.4,
                "java": 0.3,
                "javascript": 0.2,
                "cpp": 0.1
            },
            expected_cache_hit_rate=20.0,  # Low due to diverse batch content
            expected_avg_response_time_ms=200.0,
            expected_p95_response_time_ms=600.0,
            expected_throughput_rps=15.0,
            max_error_rate_percent=5.0,
            batch_size=25,
            batch_interval_seconds=30
        )
        
        executor = WorkloadExecutor(async_test_client)
        result = await executor.execute_workload_scenario(scenario)
        
        # Batch processing assertions
        assert result.total_requests >= 50, f"Insufficient batch processing: {result.total_requests} requests"
        assert result.error_rate_percent <= 8.0, f"Batch processing error rate too high: {result.error_rate_percent:.1f}%"
        
        # Batch processing should achieve high throughput
        assert result.actual_throughput_rps >= 8.0, f"Batch throughput too low: {result.actual_throughput_rps:.1f} RPS"
        
        # Should handle variety of content
        assert len(result.requests_by_type) >= 1, "Should have batch processing requests"
        
        logger.info(f"Batch processing: {result.total_requests} requests in {result.actual_duration_minutes:.1f} minutes")
    
    async def test_cicd_pipeline_workload(self, async_test_client: AsyncClient):
        """Test CI/CD pipeline workload with burst patterns."""
        scenario = WorkloadScenario(
            scenario_name="CI/CD Pipeline Analysis",
            workload_type=WorkloadType.CI_CD_PIPELINE,
            description="Simulate CI/CD pipeline code analysis",
            concurrent_users=15,
            duration_minutes=12,
            requests_distribution={
                "build_analysis": 0.5,
                "test_analysis": 0.3,
                "quality_gate": 0.2
            },
            repository_sizes={
                "medium": 0.8,
                "large": 0.2
            },
            complexity_distribution={
                "moderate": 0.7,
                "complex": 0.3
            },
            language_distribution={
                "python": 0.3,
                "javascript": 0.3,
                "java": 0.2,
                "typescript": 0.2
            },
            expected_cache_hit_rate=40.0,
            expected_avg_response_time_ms=180.0,
            expected_p95_response_time_ms=500.0,
            expected_throughput_rps=10.0,
            max_error_rate_percent=4.0
        )
        
        executor = WorkloadExecutor(async_test_client)
        result = await executor.execute_workload_scenario(scenario)
        
        # CI/CD pipeline assertions
        assert result.error_rate_percent <= 6.0, f"CI/CD error rate too high: {result.error_rate_percent:.1f}%"
        assert result.overall_p95_response_time_ms <= 800.0, f"CI/CD P95 too slow: {result.overall_p95_response_time_ms:.1f}ms"
        
        # Should handle burst patterns
        assert result.total_requests >= 50, f"CI/CD should generate substantial load: {result.total_requests} requests"
        
        # Performance should be consistent for automated systems
        if result.performance_by_type:
            for req_type, perf in result.performance_by_type.items():
                assert perf["success_rate"] >= 90.0, f"CI/CD {req_type} success rate too low: {perf['success_rate']:.1f}%"
        
        logger.info(f"CI/CD pipeline: {result.actual_throughput_rps:.1f} RPS, "
                   f"{result.overall_p95_response_time_ms:.1f}ms P95")
    
    async def test_research_analysis_workload(self, async_test_client: AsyncClient):
        """Test research analysis workload with deep analysis."""
        scenario = WorkloadScenario(
            scenario_name="Research Code Analysis",
            workload_type=WorkloadType.RESEARCH_ANALYSIS,
            description="Simulate deep research analysis of codebases",
            concurrent_users=6,  # Fewer concurrent users for deep analysis
            duration_minutes=18,
            requests_distribution={
                "deep_analysis": 0.5,
                "pattern_research": 0.3,
                "comparative_study": 0.2
            },
            repository_sizes={
                "large": 0.6,
                "enterprise": 0.4
            },
            complexity_distribution={
                "complex": 1.0
            },
            language_distribution={
                "python": 0.3,
                "java": 0.3,
                "cpp": 0.2,
                "rust": 0.2
            },
            expected_cache_hit_rate=15.0,  # Very low due to unique analysis
            expected_avg_response_time_ms=800.0,  # Slow due to deep analysis
            expected_p95_response_time_ms=2000.0,
            expected_throughput_rps=2.0,
            max_error_rate_percent=3.0
        )
        
        executor = WorkloadExecutor(async_test_client)
        result = await executor.execute_workload_scenario(scenario)
        
        # Research analysis assertions
        assert result.error_rate_percent <= 5.0, f"Research analysis error rate too high: {result.error_rate_percent:.1f}%"
        
        # Research should be thorough but slower
        assert result.overall_avg_response_time_ms >= 200.0, "Research analysis should be thorough (slower)"
        assert result.overall_avg_response_time_ms <= 1500.0, f"Research analysis too slow: {result.overall_avg_response_time_ms:.1f}ms"
        
        # Should process substantial codebases
        assert result.repositories_processed >= 2, f"Research should analyze multiple repositories: {result.repositories_processed}"
        
        # Timeout rate should be low despite complexity
        assert result.timeout_rate_percent <= 10.0, f"Research timeout rate too high: {result.timeout_rate_percent:.1f}%"
        
        logger.info(f"Research analysis: {result.repositories_processed} repositories, "
                   f"{result.overall_avg_response_time_ms:.1f}ms avg depth analysis")
    
    async def test_enterprise_audit_workload(self, async_test_client: AsyncClient):
        """Test enterprise audit workload with comprehensive coverage."""
        scenario = WorkloadScenario(
            scenario_name="Enterprise Code Audit",
            workload_type=WorkloadType.ENTERPRISE_AUDIT,
            description="Simulate comprehensive enterprise code audit",
            concurrent_users=25,
            duration_minutes=25,
            requests_distribution={
                "compliance_check": 0.4,
                "security_audit": 0.3,
                "quality_assessment": 0.2,
                "pattern_analysis": 0.1
            },
            repository_sizes={
                "large": 0.4,
                "enterprise": 0.6
            },
            complexity_distribution={
                "moderate": 0.3,
                "complex": 0.7
            },
            language_distribution={
                "java": 0.3,
                "python": 0.25,
                "javascript": 0.2,
                "typescript": 0.15,
                "cpp": 0.1
            },
            expected_cache_hit_rate=25.0,
            expected_avg_response_time_ms=400.0,
            expected_p95_response_time_ms=1000.0,
            expected_throughput_rps=12.0,
            max_error_rate_percent=2.0
        )
        
        executor = WorkloadExecutor(async_test_client)
        result = await executor.execute_workload_scenario(scenario)
        
        # Enterprise audit assertions
        assert result.error_rate_percent <= 3.0, f"Enterprise audit error rate too high: {result.error_rate_percent:.1f}%"
        assert result.performance_sla_compliance >= 80.0, f"Enterprise SLA compliance too low: {result.performance_sla_compliance:.1f}%"
        
        # Enterprise audit should be comprehensive
        assert result.total_requests >= 100, f"Enterprise audit insufficient coverage: {result.total_requests} requests"
        assert result.repositories_processed >= 5, f"Enterprise audit should cover multiple repositories: {result.repositories_processed}"
        
        # Quality should be high for enterprise
        assert result.overall_p95_response_time_ms <= 1500.0, f"Enterprise P95 too slow: {result.overall_p95_response_time_ms:.1f}ms"
        
        # All request types should be successful
        for req_type, perf in result.performance_by_type.items():
            assert perf["success_rate"] >= 95.0, f"Enterprise {req_type} success rate too low: {perf['success_rate']:.1f}%"
        
        logger.info(f"Enterprise audit: {result.total_requests} requests, {result.repositories_processed} repositories, "
                   f"{result.performance_sla_compliance:.1f}% SLA compliance")
    
    async def test_mixed_workload_cache_behavior(self, async_test_client: AsyncClient):
        """Test cache behavior under mixed realistic workloads."""
        scenario = WorkloadScenario(
            scenario_name="Mixed Workload Cache Test",
            workload_type=WorkloadType.DEVELOPMENT_TEAM,
            description="Test cache behavior with mixed request patterns",
            concurrent_users=20,
            duration_minutes=15,
            requests_distribution={
                "repeated_analysis": 0.4,  # Should hit cache
                "new_analysis": 0.3,      # Cache misses
                "variant_analysis": 0.3   # Mix of hits/misses
            },
            repository_sizes={
                "small": 0.5,
                "medium": 0.5
            },
            complexity_distribution={
                "simple": 0.4,
                "moderate": 0.6
            },
            language_distribution={
                "python": 0.6,
                "javascript": 0.4
            },
            expected_cache_hit_rate=50.0,
            expected_avg_response_time_ms=120.0,
            expected_p95_response_time_ms=300.0,
            expected_throughput_rps=12.0,
            max_error_rate_percent=3.0
        )
        
        executor = WorkloadExecutor(async_test_client)
        result = await executor.execute_workload_scenario(scenario)
        
        # Cache behavior assertions
        cache = result.cache_analysis
        assert cache.cache_hit_rate >= 20.0, f"Cache hit rate too low: {cache.cache_hit_rate:.1f}%"
        
        # Cache hits should be faster than misses
        if cache.cache_hits > 0 and cache.cache_misses > 0:
            cache_speedup = cache.avg_miss_response_time_ms / cache.avg_cache_response_time_ms
            assert cache_speedup >= 1.5, f"Cache not providing sufficient speedup: {cache_speedup:.1f}x"
        
        # Cache effectiveness should be meaningful
        assert cache.cache_effectiveness_score >= 30.0, f"Cache effectiveness too low: {cache.cache_effectiveness_score:.1f}/100"
        
        # Mixed workload should show variety
        assert len(result.requests_by_type) >= 2, "Mixed workload should have multiple request types"
        
        logger.info(f"Cache behavior: {cache.cache_hit_rate:.1f}% hit rate, "
                   f"{cache.cache_effectiveness_score:.1f}/100 effectiveness")
    
    async def test_large_repository_analysis(self, async_test_client: AsyncClient):
        """Test analysis of large repositories with realistic complexity."""
        scenario = WorkloadScenario(
            scenario_name="Large Repository Analysis",
            workload_type=WorkloadType.RESEARCH_ANALYSIS,
            description="Analyze large repositories with complex codebases",
            concurrent_users=10,
            duration_minutes=20,
            requests_distribution={
                "large_file_analysis": 0.6,
                "complex_pattern_detection": 0.4
            },
            repository_sizes={
                "large": 0.7,
                "enterprise": 0.3
            },
            complexity_distribution={
                "complex": 1.0
            },
            language_distribution={
                "python": 0.4,
                "java": 0.3,
                "cpp": 0.3
            },
            expected_cache_hit_rate=10.0,  # Low due to unique large files
            expected_avg_response_time_ms=600.0,
            expected_p95_response_time_ms=1500.0,
            expected_throughput_rps=4.0,
            max_error_rate_percent=5.0
        )
        
        executor = WorkloadExecutor(async_test_client)
        result = await executor.execute_workload_scenario(scenario)
        
        # Large repository analysis assertions
        assert result.error_rate_percent <= 8.0, f"Large repository error rate too high: {result.error_rate_percent:.1f}%"
        assert result.timeout_rate_percent <= 15.0, f"Large repository timeout rate too high: {result.timeout_rate_percent:.1f}%"
        
        # Should handle complexity without excessive slowdown
        assert result.overall_avg_response_time_ms <= 1000.0, f"Large repository analysis too slow: {result.overall_avg_response_time_ms:.1f}ms"
        
        # Should process substantial content
        assert result.total_requests >= 30, f"Large repository analysis insufficient: {result.total_requests} requests"
        
        # Performance should degrade gracefully with complexity
        if result.performance_by_type:
            for req_type, perf in result.performance_by_type.items():
                assert perf["avg_response_time_ms"] <= 2000.0, f"Large {req_type} too slow: {perf['avg_response_time_ms']:.1f}ms"
        
        logger.info(f"Large repository analysis: {result.overall_avg_response_time_ms:.1f}ms avg, "
                   f"{result.timeout_rate_percent:.1f}% timeout rate")