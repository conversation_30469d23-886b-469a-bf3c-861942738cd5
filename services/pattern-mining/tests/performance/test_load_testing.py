"""
Comprehensive Load Testing Infrastructure for Pattern Mining Service

This module provides production-ready load testing capabilities including:
- Concurrent user simulation (1-1000 users)
- Sustained load testing (duration-based)
- Ramp-up and ramp-down patterns
- Resource utilization monitoring during load
- Real-time metrics collection and analysis
"""

import pytest
import asyncio
import time
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from concurrent.futures import ThreadPoolExecutor
import statistics
import json
import psutil
import gc
from contextlib import asynccontextmanager

from httpx import AsyncClient
from tests.utils.performance import (
    PerformanceTestRunner, LatencyMeasurer, MemoryProfiler, 
    ThroughputTester, performance_test
)
from tests.utils.generators import CodeGenerator, PatternGenerator

logger = logging.getLogger(__name__)


@dataclass
class LoadTestConfiguration:
    """Load test configuration parameters."""
    
    # Test identification
    test_name: str
    test_description: str
    
    # Load parameters
    concurrent_users: int
    total_requests: int
    duration_seconds: int
    ramp_up_seconds: int = 0
    ramp_down_seconds: int = 0
    
    # Request parameters
    endpoint: str = "/api/v1/patterns/detect"
    method: str = "POST"
    request_timeout: float = 30.0
    
    # Performance thresholds
    max_avg_response_time_ms: float = 100.0
    max_p95_response_time_ms: float = 200.0
    max_error_rate_percent: float = 1.0
    min_throughput_rps: float = 50.0
    
    # Resource limits
    max_memory_usage_mb: float = 512.0
    max_cpu_usage_percent: float = 80.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return asdict(self)


@dataclass
class LoadTestResult:
    """Load test execution results."""
    
    # Test metadata
    test_config: LoadTestConfiguration
    start_time: datetime
    end_time: datetime
    actual_duration_seconds: float
    
    # Request metrics
    total_requests_sent: int
    successful_requests: int
    failed_requests: int
    timeout_requests: int
    
    # Response time metrics
    avg_response_time_ms: float
    median_response_time_ms: float
    p95_response_time_ms: float
    p99_response_time_ms: float
    min_response_time_ms: float
    max_response_time_ms: float
    
    # Throughput metrics
    requests_per_second: float
    successful_rps: float
    
    # Error metrics
    error_rate_percent: float
    error_breakdown: Dict[str, int]
    
    # Resource metrics
    peak_memory_usage_mb: float
    avg_cpu_usage_percent: float
    peak_cpu_usage_percent: float
    
    # Test result
    test_passed: bool
    failed_criteria: List[str]
    performance_score: float
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        result = asdict(self)
        result['test_config'] = self.test_config.to_dict()
        result['start_time'] = self.start_time.isoformat()
        result['end_time'] = self.end_time.isoformat()
        return result


class LoadTestExecutor:
    """Execute comprehensive load tests with resource monitoring."""
    
    def __init__(self, client: AsyncClient):
        self.client = client
        self.process = psutil.Process()
        self.memory_profiler = MemoryProfiler()
        self.latency_measurer = LatencyMeasurer()
        self.code_generator = CodeGenerator()
        
    async def execute_load_test(self, config: LoadTestConfiguration) -> LoadTestResult:
        """Execute a comprehensive load test."""
        logger.info(f"Starting load test: {config.test_name}")
        logger.info(f"Configuration: {config.concurrent_users} users, {config.duration_seconds}s duration")
        
        # Start monitoring
        start_time = datetime.utcnow()
        self.memory_profiler.start_profiling()
        self.memory_profiler.take_snapshot("test_start")
        
        # Initialize tracking
        request_results = []
        cpu_measurements = []
        memory_measurements = []
        
        # Prepare test data
        test_data = self._prepare_test_data(config.concurrent_users)
        
        try:
            # Execute load test with monitoring
            request_results, cpu_measurements, memory_measurements = await self._execute_with_monitoring(
                config, test_data
            )
            
        finally:
            # Stop monitoring
            end_time = datetime.utcnow()
            self.memory_profiler.take_snapshot("test_end")
            self.memory_profiler.stop_profiling()
        
        # Analyze results
        result = self._analyze_results(config, start_time, end_time, request_results, 
                                     cpu_measurements, memory_measurements)
        
        logger.info(f"Load test completed: {result.test_passed}")
        logger.info(f"Throughput: {result.requests_per_second:.1f} RPS")
        logger.info(f"Error rate: {result.error_rate_percent:.2f}%")
        
        return result
    
    def _prepare_test_data(self, user_count: int) -> List[Dict[str, Any]]:
        """Prepare test data for concurrent users."""
        test_data = []
        
        for i in range(user_count):
            # Generate varied test code to simulate real usage
            if i % 3 == 0:
                # Simple function
                code_content = self.code_generator.generate_function()
            elif i % 3 == 1:
                # Module with multiple patterns
                code_content = self.code_generator.generate_file_content("module", pattern_count=3)
            else:
                # Complex code with many patterns
                code_content = self.code_generator.generate_file_content("module", pattern_count=8)
            
            test_data.append({
                "repository_id": f"load-test-repo-{i}",
                "ast_data": {"type": "Module", "children": []},
                "code_content": code_content,
                "file_path": f"test_{i}.py",
                "language": "python",
                "detection_config": {
                    "confidence_threshold": 0.7,
                    "enable_ml_models": True,
                    "enable_heuristic_detection": True
                }
            })
        
        return test_data
    
    async def _execute_with_monitoring(
        self, 
        config: LoadTestConfiguration, 
        test_data: List[Dict[str, Any]]
    ) -> Tuple[List[Dict[str, Any]], List[float], List[float]]:
        """Execute load test with concurrent monitoring."""
        
        request_results = []
        cpu_measurements = []
        memory_measurements = []
        
        # Start resource monitoring task
        monitoring_task = asyncio.create_task(
            self._monitor_resources(cpu_measurements, memory_measurements)
        )
        
        try:
            # Execute the load test pattern
            if config.ramp_up_seconds > 0:
                # Ramp-up pattern
                await self._execute_ramp_up_test(config, test_data, request_results)
            else:
                # Immediate full load
                await self._execute_sustained_load_test(config, test_data, request_results)
            
        finally:
            # Stop monitoring
            monitoring_task.cancel()
            try:
                await monitoring_task
            except asyncio.CancelledError:
                pass
        
        return request_results, cpu_measurements, memory_measurements
    
    async def _execute_sustained_load_test(
        self, 
        config: LoadTestConfiguration, 
        test_data: List[Dict[str, Any]], 
        request_results: List[Dict[str, Any]]
    ):
        """Execute sustained load test."""
        
        start_time = time.perf_counter()
        end_time = start_time + config.duration_seconds
        
        # Create semaphore to limit concurrent requests
        semaphore = asyncio.Semaphore(config.concurrent_users)
        
        async def make_request_with_semaphore(data: Dict[str, Any]) -> Dict[str, Any]:
            """Make request with concurrency limiting."""
            async with semaphore:
                return await self._make_single_request(config, data)
        
        # Continue sending requests for the duration
        request_tasks = []
        data_index = 0
        
        while time.perf_counter() < end_time:
            # Create batch of requests
            batch_size = min(config.concurrent_users, len(test_data))
            batch_tasks = []
            
            for _ in range(batch_size):
                data = test_data[data_index % len(test_data)]
                task = make_request_with_semaphore(data)
                batch_tasks.append(task)
                data_index += 1
            
            # Execute batch
            batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
            
            # Process results
            for result in batch_results:
                if isinstance(result, Exception):
                    request_results.append({
                        "success": False,
                        "error": str(result),
                        "response_time_ms": 0,
                        "status_code": 0,
                        "timestamp": datetime.utcnow()
                    })
                else:
                    request_results.append(result)
            
            # Small delay to prevent overwhelming
            await asyncio.sleep(0.01)
    
    async def _execute_ramp_up_test(
        self, 
        config: LoadTestConfiguration, 
        test_data: List[Dict[str, Any]], 
        request_results: List[Dict[str, Any]]
    ):
        """Execute load test with ramp-up pattern."""
        
        total_duration = config.duration_seconds + config.ramp_up_seconds + config.ramp_down_seconds
        start_time = time.perf_counter()
        
        async def get_current_user_count() -> int:
            """Calculate current user count based on ramp pattern."""
            elapsed = time.perf_counter() - start_time
            
            if elapsed < config.ramp_up_seconds:
                # Ramp-up phase
                progress = elapsed / config.ramp_up_seconds
                return max(1, int(config.concurrent_users * progress))
            elif elapsed < config.ramp_up_seconds + config.duration_seconds:
                # Sustained load phase
                return config.concurrent_users
            elif elapsed < total_duration:
                # Ramp-down phase
                ramp_down_elapsed = elapsed - config.ramp_up_seconds - config.duration_seconds
                progress = 1.0 - (ramp_down_elapsed / config.ramp_down_seconds)
                return max(1, int(config.concurrent_users * progress))
            else:
                return 0
        
        # Execute with dynamic user count
        data_index = 0
        while time.perf_counter() - start_time < total_duration:
            current_users = await get_current_user_count()
            if current_users == 0:
                break
            
            # Create requests for current user count
            batch_tasks = []
            for _ in range(current_users):
                data = test_data[data_index % len(test_data)]
                task = self._make_single_request(config, data)
                batch_tasks.append(task)
                data_index += 1
            
            # Execute batch with timeout
            batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
            
            # Process results
            for result in batch_results:
                if isinstance(result, Exception):
                    request_results.append({
                        "success": False,
                        "error": str(result),
                        "response_time_ms": 0,
                        "status_code": 0,
                        "timestamp": datetime.utcnow()
                    })
                else:
                    request_results.append(result)
            
            # Adjust delay based on current load
            delay = max(0.01, 1.0 / (current_users * 2))
            await asyncio.sleep(delay)
    
    async def _make_single_request(
        self, 
        config: LoadTestConfiguration, 
        data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Make a single request and measure performance."""
        
        request_start = time.perf_counter()
        timestamp = datetime.utcnow()
        
        try:
            # Make request with timeout
            response = await asyncio.wait_for(
                self.client.post(config.endpoint, json=data),
                timeout=config.request_timeout
            )
            
            request_end = time.perf_counter()
            response_time_ms = (request_end - request_start) * 1000
            
            return {
                "success": response.status_code < 400,
                "status_code": response.status_code,
                "response_time_ms": response_time_ms,
                "timestamp": timestamp,
                "error": None if response.status_code < 400 else f"HTTP {response.status_code}"
            }
            
        except asyncio.TimeoutError:
            request_end = time.perf_counter()
            response_time_ms = (request_end - request_start) * 1000
            
            return {
                "success": False,
                "status_code": 408,
                "response_time_ms": response_time_ms,
                "timestamp": timestamp,
                "error": "Request timeout"
            }
            
        except Exception as e:
            request_end = time.perf_counter()
            response_time_ms = (request_end - request_start) * 1000
            
            return {
                "success": False,
                "status_code": 500,
                "response_time_ms": response_time_ms,
                "timestamp": timestamp,
                "error": str(e)
            }
    
    async def _monitor_resources(
        self, 
        cpu_measurements: List[float], 
        memory_measurements: List[float]
    ):
        """Monitor system resources during load test."""
        
        while True:
            try:
                # Measure CPU usage
                cpu_percent = self.process.cpu_percent()
                cpu_measurements.append(cpu_percent)
                
                # Measure memory usage
                memory_info = self.process.memory_info()
                memory_mb = memory_info.rss / 1024 / 1024
                memory_measurements.append(memory_mb)
                
                # Wait before next measurement
                await asyncio.sleep(1.0)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.warning(f"Error monitoring resources: {e}")
                await asyncio.sleep(1.0)
    
    def _analyze_results(
        self,
        config: LoadTestConfiguration,
        start_time: datetime,
        end_time: datetime,
        request_results: List[Dict[str, Any]],
        cpu_measurements: List[float],
        memory_measurements: List[float]
    ) -> LoadTestResult:
        """Analyze load test results and determine pass/fail."""
        
        # Basic metrics
        actual_duration = (end_time - start_time).total_seconds()
        total_requests = len(request_results)
        successful_requests = sum(1 for r in request_results if r["success"])
        failed_requests = total_requests - successful_requests
        timeout_requests = sum(1 for r in request_results if r.get("error") == "Request timeout")
        
        # Response time metrics
        successful_response_times = [
            r["response_time_ms"] for r in request_results if r["success"]
        ]
        
        if successful_response_times:
            avg_response_time = statistics.mean(successful_response_times)
            median_response_time = statistics.median(successful_response_times)
            min_response_time = min(successful_response_times)
            max_response_time = max(successful_response_times)
            
            # Calculate percentiles
            sorted_times = sorted(successful_response_times)
            p95_index = int(len(sorted_times) * 0.95)
            p99_index = int(len(sorted_times) * 0.99)
            p95_response_time = sorted_times[p95_index] if p95_index < len(sorted_times) else max_response_time
            p99_response_time = sorted_times[p99_index] if p99_index < len(sorted_times) else max_response_time
        else:
            avg_response_time = 0
            median_response_time = 0
            min_response_time = 0
            max_response_time = 0
            p95_response_time = 0
            p99_response_time = 0
        
        # Throughput metrics
        requests_per_second = total_requests / actual_duration if actual_duration > 0 else 0
        successful_rps = successful_requests / actual_duration if actual_duration > 0 else 0
        
        # Error metrics
        error_rate_percent = (failed_requests / total_requests * 100) if total_requests > 0 else 0
        error_breakdown = {}
        for result in request_results:
            if not result["success"] and result.get("error"):
                error_type = result["error"]
                error_breakdown[error_type] = error_breakdown.get(error_type, 0) + 1
        
        # Resource metrics
        peak_memory_usage = max(memory_measurements) if memory_measurements else 0
        avg_cpu_usage = statistics.mean(cpu_measurements) if cpu_measurements else 0
        peak_cpu_usage = max(cpu_measurements) if cpu_measurements else 0
        
        # Evaluate pass/fail criteria
        failed_criteria = []
        
        if avg_response_time > config.max_avg_response_time_ms:
            failed_criteria.append(f"avg_response_time: {avg_response_time:.1f}ms > {config.max_avg_response_time_ms}ms")
        
        if p95_response_time > config.max_p95_response_time_ms:
            failed_criteria.append(f"p95_response_time: {p95_response_time:.1f}ms > {config.max_p95_response_time_ms}ms")
        
        if error_rate_percent > config.max_error_rate_percent:
            failed_criteria.append(f"error_rate: {error_rate_percent:.2f}% > {config.max_error_rate_percent}%")
        
        if successful_rps < config.min_throughput_rps:
            failed_criteria.append(f"throughput: {successful_rps:.1f} RPS < {config.min_throughput_rps} RPS")
        
        if peak_memory_usage > config.max_memory_usage_mb:
            failed_criteria.append(f"memory_usage: {peak_memory_usage:.1f}MB > {config.max_memory_usage_mb}MB")
        
        if peak_cpu_usage > config.max_cpu_usage_percent:
            failed_criteria.append(f"cpu_usage: {peak_cpu_usage:.1f}% > {config.max_cpu_usage_percent}%")
        
        # Calculate performance score (0-100)
        performance_score = 100.0
        
        # Response time scoring (40% weight)
        if config.max_avg_response_time_ms > 0:
            response_time_score = max(0, 100 - (avg_response_time / config.max_avg_response_time_ms * 100))
            performance_score = performance_score * 0.6 + response_time_score * 0.4
        
        # Error rate scoring (30% weight)
        if config.max_error_rate_percent > 0:
            error_rate_score = max(0, 100 - (error_rate_percent / config.max_error_rate_percent * 100))
            performance_score = performance_score * 0.7 + error_rate_score * 0.3
        
        # Throughput scoring (30% weight)
        if config.min_throughput_rps > 0:
            throughput_score = min(100, (successful_rps / config.min_throughput_rps * 100))
            performance_score = performance_score * 0.7 + throughput_score * 0.3
        
        test_passed = len(failed_criteria) == 0
        
        return LoadTestResult(
            test_config=config,
            start_time=start_time,
            end_time=end_time,
            actual_duration_seconds=actual_duration,
            total_requests_sent=total_requests,
            successful_requests=successful_requests,
            failed_requests=failed_requests,
            timeout_requests=timeout_requests,
            avg_response_time_ms=avg_response_time,
            median_response_time_ms=median_response_time,
            p95_response_time_ms=p95_response_time,
            p99_response_time_ms=p99_response_time,
            min_response_time_ms=min_response_time,
            max_response_time_ms=max_response_time,
            requests_per_second=requests_per_second,
            successful_rps=successful_rps,
            error_rate_percent=error_rate_percent,
            error_breakdown=error_breakdown,
            peak_memory_usage_mb=peak_memory_usage,
            avg_cpu_usage_percent=avg_cpu_usage,
            peak_cpu_usage_percent=peak_cpu_usage,
            test_passed=test_passed,
            failed_criteria=failed_criteria,
            performance_score=performance_score
        )


@pytest.mark.performance
@pytest.mark.asyncio
class TestLoadTesting:
    """Comprehensive load testing suite."""
    
    async def test_baseline_load_10_users(self, async_test_client: AsyncClient):
        """Test baseline load with 10 concurrent users."""
        config = LoadTestConfiguration(
            test_name="Baseline Load - 10 Users",
            test_description="Baseline performance test with 10 concurrent users",
            concurrent_users=10,
            total_requests=100,
            duration_seconds=30,
            max_avg_response_time_ms=100.0,
            max_p95_response_time_ms=200.0,
            max_error_rate_percent=1.0,
            min_throughput_rps=5.0
        )
        
        executor = LoadTestExecutor(async_test_client)
        result = await executor.execute_load_test(config)
        
        # Assertions
        assert result.test_passed, f"Test failed: {result.failed_criteria}"
        assert result.error_rate_percent <= 1.0
        assert result.avg_response_time_ms <= 100.0
        assert result.successful_rps >= 5.0
        
        # Log results
        logger.info(f"Baseline test results: {result.performance_score:.1f}/100")
    
    async def test_moderate_load_50_users(self, async_test_client: AsyncClient):
        """Test moderate load with 50 concurrent users."""
        config = LoadTestConfiguration(
            test_name="Moderate Load - 50 Users",
            test_description="Moderate performance test with 50 concurrent users",
            concurrent_users=50,
            total_requests=500,
            duration_seconds=60,
            max_avg_response_time_ms=150.0,
            max_p95_response_time_ms=300.0,
            max_error_rate_percent=2.0,
            min_throughput_rps=20.0
        )
        
        executor = LoadTestExecutor(async_test_client)
        result = await executor.execute_load_test(config)
        
        # Assertions with slightly relaxed thresholds
        assert result.error_rate_percent <= 2.0, f"Error rate too high: {result.error_rate_percent}%"
        assert result.avg_response_time_ms <= 200.0, f"Average response time too high: {result.avg_response_time_ms}ms"
        assert result.successful_rps >= 15.0, f"Throughput too low: {result.successful_rps} RPS"
        
        # Performance degradation check
        assert result.performance_score >= 70.0, f"Performance score too low: {result.performance_score}"
    
    async def test_high_load_100_users(self, async_test_client: AsyncClient):
        """Test high load with 100 concurrent users."""
        config = LoadTestConfiguration(
            test_name="High Load - 100 Users",
            test_description="High performance test with 100 concurrent users",
            concurrent_users=100,
            total_requests=1000,
            duration_seconds=90,
            max_avg_response_time_ms=200.0,
            max_p95_response_time_ms=500.0,
            max_error_rate_percent=5.0,
            min_throughput_rps=30.0,
            max_memory_usage_mb=768.0,
            max_cpu_usage_percent=90.0
        )
        
        executor = LoadTestExecutor(async_test_client)
        result = await executor.execute_load_test(config)
        
        # Assertions with relaxed thresholds for high load
        assert result.error_rate_percent <= 10.0, f"Error rate too high: {result.error_rate_percent}%"
        assert result.successful_rps >= 20.0, f"Throughput too low: {result.successful_rps} RPS"
        
        # Resource usage checks
        assert result.peak_memory_usage_mb <= 1024.0, f"Memory usage too high: {result.peak_memory_usage_mb}MB"
        
        # Performance should degrade gracefully
        assert result.performance_score >= 50.0, f"Performance degraded too much: {result.performance_score}"
    
    async def test_ramp_up_load_pattern(self, async_test_client: AsyncClient):
        """Test load with ramp-up pattern."""
        config = LoadTestConfiguration(
            test_name="Ramp-up Load Pattern",
            test_description="Load test with gradual ramp-up to 50 users",
            concurrent_users=50,
            total_requests=500,
            duration_seconds=60,
            ramp_up_seconds=30,
            ramp_down_seconds=15,
            max_avg_response_time_ms=120.0,
            max_p95_response_time_ms=250.0,
            max_error_rate_percent=3.0,
            min_throughput_rps=15.0
        )
        
        executor = LoadTestExecutor(async_test_client)
        result = await executor.execute_load_test(config)
        
        # Ramp-up should show better performance than immediate full load
        assert result.error_rate_percent <= 5.0
        assert result.avg_response_time_ms <= 150.0
        assert result.successful_rps >= 10.0
        
        # Should handle ramp pattern gracefully
        assert result.performance_score >= 60.0
    
    async def test_sustained_load_5_minutes(self, async_test_client: AsyncClient):
        """Test sustained load over 5 minutes."""
        config = LoadTestConfiguration(
            test_name="Sustained Load - 5 Minutes",
            test_description="Sustained load test with 25 users for 5 minutes",
            concurrent_users=25,
            total_requests=1500,
            duration_seconds=300,  # 5 minutes
            max_avg_response_time_ms=100.0,
            max_p95_response_time_ms=200.0,
            max_error_rate_percent=2.0,
            min_throughput_rps=10.0,
            max_memory_usage_mb=512.0
        )
        
        executor = LoadTestExecutor(async_test_client)
        result = await executor.execute_load_test(config)
        
        # Long-running stability checks
        assert result.error_rate_percent <= 3.0, f"Error rate too high for sustained load: {result.error_rate_percent}%"
        assert result.successful_rps >= 8.0, f"Throughput degraded: {result.successful_rps} RPS"
        
        # Memory leak detection
        memory_growth_threshold = 200.0  # MB
        assert result.peak_memory_usage_mb <= config.max_memory_usage_mb + memory_growth_threshold
        
        # Sustained performance
        assert result.performance_score >= 70.0, f"Sustained performance too low: {result.performance_score}"
    
    async def test_burst_load_pattern(self, async_test_client: AsyncClient):
        """Test burst load pattern with rapid scaling."""
        config = LoadTestConfiguration(
            test_name="Burst Load Pattern",
            test_description="Burst pattern: 1 -> 100 -> 1 users quickly",
            concurrent_users=100,
            total_requests=300,
            duration_seconds=30,
            ramp_up_seconds=10,
            ramp_down_seconds=10,
            max_avg_response_time_ms=300.0,
            max_p95_response_time_ms=600.0,
            max_error_rate_percent=10.0,
            min_throughput_rps=5.0
        )
        
        executor = LoadTestExecutor(async_test_client)
        result = await executor.execute_load_test(config)
        
        # Burst patterns may have higher error rates
        assert result.error_rate_percent <= 15.0, f"Burst error rate too high: {result.error_rate_percent}%"
        assert result.successful_rps >= 3.0, f"Burst throughput too low: {result.successful_rps} RPS"
        
        # Should handle burst without crashing
        assert result.total_requests_sent > 100, "Not enough requests sent during burst"
        assert result.performance_score >= 40.0, f"Burst performance too low: {result.performance_score}"
    
    @pytest.mark.slow
    async def test_endurance_load_30_minutes(self, async_test_client: AsyncClient):
        """Test endurance load over 30 minutes (slow test)."""
        config = LoadTestConfiguration(
            test_name="Endurance Load - 30 Minutes",
            test_description="Endurance test with 15 users for 30 minutes",
            concurrent_users=15,
            total_requests=4500,
            duration_seconds=1800,  # 30 minutes
            max_avg_response_time_ms=100.0,
            max_p95_response_time_ms=200.0,
            max_error_rate_percent=2.0,
            min_throughput_rps=3.0,
            max_memory_usage_mb=512.0
        )
        
        executor = LoadTestExecutor(async_test_client)
        result = await executor.execute_load_test(config)
        
        # Endurance stability checks
        assert result.error_rate_percent <= 5.0, f"Endurance error rate too high: {result.error_rate_percent}%"
        assert result.successful_rps >= 2.0, f"Endurance throughput too low: {result.successful_rps} RPS"
        
        # Long-term memory stability
        assert result.peak_memory_usage_mb <= 768.0, f"Memory usage grew too much: {result.peak_memory_usage_mb}MB"
        
        # Endurance performance
        assert result.performance_score >= 60.0, f"Endurance performance degraded: {result.performance_score}"
    
    async def test_load_test_result_persistence(self, async_test_client: AsyncClient):
        """Test that load test results are properly persisted."""
        config = LoadTestConfiguration(
            test_name="Result Persistence Test",
            test_description="Test result persistence functionality",
            concurrent_users=5,
            total_requests=25,
            duration_seconds=15,
            max_avg_response_time_ms=100.0,
            max_error_rate_percent=1.0,
            min_throughput_rps=1.0
        )
        
        executor = LoadTestExecutor(async_test_client)
        result = await executor.execute_load_test(config)
        
        # Test serialization
        result_dict = result.to_dict()
        assert isinstance(result_dict, dict)
        assert "test_config" in result_dict
        assert "performance_score" in result_dict
        assert "start_time" in result_dict
        
        # Test JSON serialization
        json_str = json.dumps(result_dict, indent=2)
        assert len(json_str) > 100
        
        # Test deserialization
        parsed_result = json.loads(json_str)
        assert parsed_result["test_config"]["test_name"] == config.test_name
        assert parsed_result["performance_score"] == result.performance_score