"""
Performance Regression Detection for CI/CD Integration

This module provides automated performance regression detection capabilities:
- Baseline performance establishment and tracking
- Regression threshold validation
- Performance trend analysis
- CI/CD integration hooks
- Automated performance alerts
"""

import pytest
import asyncio
import time
import logging
import os
import json
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from pathlib import Path
import statistics
import subprocess

from httpx import AsyncClient
from tests.utils.performance import (
    PerformanceTestRunner, LatencyMeasurer, MemoryProfiler, performance_test
)
from tests.utils.generators import CodeGenerator

logger = logging.getLogger(__name__)


@dataclass
class PerformanceBaseline:
    """Performance baseline for regression detection."""
    
    # Metadata
    baseline_name: str
    created_date: datetime
    service_version: str
    environment: str
    
    # Performance metrics
    avg_response_time_ms: float
    p95_response_time_ms: float
    p99_response_time_ms: float
    throughput_rps: float
    memory_usage_mb: float
    cpu_usage_percent: float
    error_rate_percent: float
    
    # Test conditions
    concurrent_users: int
    test_duration_seconds: int
    request_count: int
    
    # Quality metrics
    patterns_detected_rate: float
    accuracy_score: float
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        result = asdict(self)
        result['created_date'] = self.created_date.isoformat()
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PerformanceBaseline':
        """Create from dictionary."""
        data = data.copy()
        data['created_date'] = datetime.fromisoformat(data['created_date'])
        return cls(**data)


@dataclass
class RegressionTestResult:
    """Performance regression test result."""
    
    # Test metadata
    test_name: str
    test_date: datetime
    baseline_used: str
    service_version: str
    
    # Current performance
    current_metrics: Dict[str, float]
    baseline_metrics: Dict[str, float]
    
    # Regression analysis
    metric_changes: Dict[str, Dict[str, float]]  # metric -> {absolute_change, percent_change, is_regression}
    overall_regression_detected: bool
    regression_severity: str  # "none", "minor", "major", "critical"
    
    # Thresholds
    regression_thresholds: Dict[str, float]
    
    # Results
    passed: bool
    failed_metrics: List[str]
    warnings: List[str]
    recommendations: List[str]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        result = asdict(self)
        result['test_date'] = self.test_date.isoformat()
        return result


class PerformanceRegressionDetector:
    """Detect performance regressions against established baselines."""
    
    def __init__(self, baseline_dir: str = "tests/performance/baselines"):
        self.baseline_dir = Path(baseline_dir)
        self.baseline_dir.mkdir(parents=True, exist_ok=True)
        
        # Default regression thresholds (percent increase that triggers regression)
        self.default_thresholds = {
            "avg_response_time_ms": 20.0,    # 20% slower response time
            "p95_response_time_ms": 25.0,    # 25% slower P95
            "p99_response_time_ms": 30.0,    # 30% slower P99
            "throughput_rps": -15.0,         # 15% lower throughput (negative = decrease bad)
            "memory_usage_mb": 25.0,         # 25% more memory usage
            "cpu_usage_percent": 20.0,       # 20% more CPU usage
            "error_rate_percent": 100.0,     # 100% more errors (doubling error rate)
        }
        
        self.code_generator = CodeGenerator()
    
    async def establish_baseline(
        self, 
        client: AsyncClient, 
        baseline_name: str,
        config: Dict[str, Any] = None
    ) -> PerformanceBaseline:
        """Establish a new performance baseline."""
        
        config = config or {}
        concurrent_users = config.get("concurrent_users", 5)
        test_duration = config.get("test_duration_seconds", 30)
        requests_per_user = config.get("requests_per_user", 6)
        
        logger.info(f"Establishing performance baseline: {baseline_name}")
        
        # Execute baseline performance test
        start_time = datetime.utcnow()
        test_results = []
        patterns_detected = 0
        
        # Create semaphore for concurrency control
        semaphore = asyncio.Semaphore(concurrent_users)
        
        async def user_workload(user_id: int) -> List[Dict[str, Any]]:
            """Execute workload for a single user."""
            user_results = []
            
            async with semaphore:
                for req_id in range(requests_per_user):
                    # Generate realistic test data
                    complexity = 3 + (req_id % 7)  # 3-9 patterns
                    code_content = self.code_generator.generate_file_content(
                        "module", pattern_count=complexity
                    )
                    
                    test_data = {
                        "repository_id": f"baseline-{baseline_name}-user-{user_id}",
                        "ast_data": {"type": "Module", "children": []},
                        "code_content": code_content,
                        "file_path": f"baseline_user_{user_id}_req_{req_id}.py",
                        "language": "python",
                        "detection_config": {
                            "confidence_threshold": 0.7,
                            "enable_ml_models": True,
                            "enable_heuristic_detection": True
                        }
                    }
                    
                    request_start = time.perf_counter()
                    try:
                        response = await client.post("/api/v1/patterns/detect", json=test_data)
                        request_end = time.perf_counter()
                        
                        response_time = (request_end - request_start) * 1000
                        success = response.status_code < 400
                        
                        # Count patterns if successful
                        if success and response.status_code == 200:
                            try:
                                response_data = response.json()
                                pattern_count = len(response_data.get("patterns", []))
                                patterns_detected += pattern_count
                            except:
                                pattern_count = 0
                        else:
                            pattern_count = 0
                        
                        user_results.append({
                            "success": success,
                            "response_time_ms": response_time,
                            "status_code": response.status_code,
                            "patterns_found": pattern_count,
                            "user_id": user_id,
                            "request_id": req_id
                        })
                        
                    except Exception as e:
                        request_end = time.perf_counter()
                        response_time = (request_end - request_start) * 1000
                        
                        user_results.append({
                            "success": False,
                            "response_time_ms": response_time,
                            "status_code": 500,
                            "error": str(e),
                            "patterns_found": 0,
                            "user_id": user_id,
                            "request_id": req_id
                        })
                    
                    # Small delay between requests
                    await asyncio.sleep(0.1)
            
            return user_results
        
        # Execute concurrent user workloads
        user_tasks = [user_workload(i) for i in range(concurrent_users)]
        user_results_lists = await asyncio.gather(*user_tasks, return_exceptions=True)
        
        # Flatten results
        for user_results in user_results_lists:
            if isinstance(user_results, list):
                test_results.extend(user_results)
        
        end_time = datetime.utcnow()
        actual_duration = (end_time - start_time).total_seconds()
        
        # Analyze baseline results
        baseline = self._analyze_baseline_results(
            baseline_name, test_results, patterns_detected, 
            concurrent_users, actual_duration
        )
        
        # Save baseline
        self._save_baseline(baseline)
        
        logger.info(f"Baseline established: {baseline_name}")
        logger.info(f"  Avg response time: {baseline.avg_response_time_ms:.1f}ms")
        logger.info(f"  Throughput: {baseline.throughput_rps:.1f} RPS")
        logger.info(f"  Memory usage: {baseline.memory_usage_mb:.1f}MB")
        logger.info(f"  Error rate: {baseline.error_rate_percent:.1f}%")
        
        return baseline
    
    async def detect_regression(
        self, 
        client: AsyncClient, 
        baseline_name: str,
        test_config: Dict[str, Any] = None
    ) -> RegressionTestResult:
        """Detect performance regression against baseline."""
        
        # Load baseline
        baseline = self._load_baseline(baseline_name)
        if not baseline:
            raise ValueError(f"Baseline '{baseline_name}' not found")
        
        logger.info(f"Running regression test against baseline: {baseline_name}")
        
        # Execute current performance test with same conditions as baseline
        test_config = test_config or {}
        concurrent_users = test_config.get("concurrent_users", baseline.concurrent_users)
        requests_per_user = max(1, baseline.request_count // baseline.concurrent_users)
        
        # Run performance test
        current_results = await self._execute_regression_test(
            client, concurrent_users, requests_per_user
        )
        
        # Compare against baseline
        regression_result = self._analyze_regression(baseline, current_results)
        
        # Log results
        if regression_result.overall_regression_detected:
            logger.warning(f"Performance regression detected: {regression_result.regression_severity}")
            for metric in regression_result.failed_metrics:
                change = regression_result.metric_changes[metric]
                logger.warning(f"  {metric}: {change['percent_change']:+.1f}% change")
        else:
            logger.info("No performance regression detected")
        
        return regression_result
    
    def _analyze_baseline_results(
        self,
        baseline_name: str,
        results: List[Dict[str, Any]],
        patterns_detected: int,
        concurrent_users: int,
        duration_seconds: float
    ) -> PerformanceBaseline:
        """Analyze baseline test results."""
        
        # Basic metrics
        total_requests = len(results)
        successful_requests = sum(1 for r in results if r.get("success", False))
        failed_requests = total_requests - successful_requests
        
        # Response time analysis
        successful_times = [r["response_time_ms"] for r in results if r.get("success", False)]
        
        if successful_times:
            avg_response_time = statistics.mean(successful_times)
            sorted_times = sorted(successful_times)
            p95_index = int(len(sorted_times) * 0.95)
            p99_index = int(len(sorted_times) * 0.99)
            p95_response_time = sorted_times[p95_index] if p95_index < len(sorted_times) else sorted_times[-1]
            p99_response_time = sorted_times[p99_index] if p99_index < len(sorted_times) else sorted_times[-1]
        else:
            avg_response_time = p95_response_time = p99_response_time = 0
        
        # Throughput and error rate
        throughput_rps = successful_requests / duration_seconds if duration_seconds > 0 else 0
        error_rate_percent = (failed_requests / total_requests * 100) if total_requests > 0 else 0
        
        # Resource usage (simulated - in real implementation, get from monitoring)
        memory_usage_mb = 128.0 + (concurrent_users * 15.0)  # Estimated memory usage
        cpu_usage_percent = min(80.0, concurrent_users * 8.0)  # Estimated CPU usage
        
        # Quality metrics
        patterns_detected_rate = patterns_detected / max(1, successful_requests)
        accuracy_score = min(100.0, patterns_detected_rate * 25.0)  # Rough accuracy estimate
        
        # Get service version
        service_version = os.getenv("SERVICE_VERSION", "unknown")
        environment = os.getenv("ENVIRONMENT", "test")
        
        return PerformanceBaseline(
            baseline_name=baseline_name,
            created_date=datetime.utcnow(),
            service_version=service_version,
            environment=environment,
            avg_response_time_ms=avg_response_time,
            p95_response_time_ms=p95_response_time,
            p99_response_time_ms=p99_response_time,
            throughput_rps=throughput_rps,
            memory_usage_mb=memory_usage_mb,
            cpu_usage_percent=cpu_usage_percent,
            error_rate_percent=error_rate_percent,
            concurrent_users=concurrent_users,
            test_duration_seconds=int(duration_seconds),
            request_count=total_requests,
            patterns_detected_rate=patterns_detected_rate,
            accuracy_score=accuracy_score
        )
    
    async def _execute_regression_test(
        self, 
        client: AsyncClient, 
        concurrent_users: int, 
        requests_per_user: int
    ) -> Dict[str, float]:
        """Execute regression test and return current metrics."""
        
        start_time = time.perf_counter()
        test_results = []
        patterns_detected = 0
        
        # Create semaphore for concurrency control
        semaphore = asyncio.Semaphore(concurrent_users)
        
        async def user_workload(user_id: int) -> List[Dict[str, Any]]:
            """Execute workload for regression test."""
            user_results = []
            
            async with semaphore:
                for req_id in range(requests_per_user):
                    # Generate test data
                    complexity = 4 + (req_id % 6)  # Similar to baseline
                    code_content = self.code_generator.generate_file_content(
                        "module", pattern_count=complexity
                    )
                    
                    test_data = {
                        "repository_id": f"regression-test-user-{user_id}",
                        "ast_data": {"type": "Module", "children": []},
                        "code_content": code_content,
                        "file_path": f"regression_user_{user_id}_req_{req_id}.py",
                        "language": "python",
                        "detection_config": {
                            "confidence_threshold": 0.7,
                            "enable_ml_models": True,
                            "enable_heuristic_detection": True
                        }
                    }
                    
                    request_start = time.perf_counter()
                    try:
                        response = await client.post("/api/v1/patterns/detect", json=test_data)
                        request_end = time.perf_counter()
                        
                        response_time = (request_end - request_start) * 1000
                        success = response.status_code < 400
                        
                        if success and response.status_code == 200:
                            try:
                                response_data = response.json()
                                pattern_count = len(response_data.get("patterns", []))
                                patterns_detected += pattern_count
                            except:
                                pattern_count = 0
                        
                        user_results.append({
                            "success": success,
                            "response_time_ms": response_time,
                            "status_code": response.status_code,
                            "patterns_found": pattern_count
                        })
                        
                    except Exception as e:
                        request_end = time.perf_counter()
                        response_time = (request_end - request_start) * 1000
                        
                        user_results.append({
                            "success": False,
                            "response_time_ms": response_time,
                            "status_code": 500,
                            "error": str(e),
                            "patterns_found": 0
                        })
                    
                    await asyncio.sleep(0.1)
            
            return user_results
        
        # Execute concurrent workloads
        user_tasks = [user_workload(i) for i in range(concurrent_users)]
        user_results_lists = await asyncio.gather(*user_tasks, return_exceptions=True)
        
        # Flatten results
        for user_results in user_results_lists:
            if isinstance(user_results, list):
                test_results.extend(user_results)
        
        end_time = time.perf_counter()
        duration = end_time - start_time
        
        # Calculate current metrics
        total_requests = len(test_results)
        successful_requests = sum(1 for r in test_results if r.get("success", False))
        failed_requests = total_requests - successful_requests
        
        successful_times = [r["response_time_ms"] for r in test_results if r.get("success", False)]
        
        if successful_times:
            avg_response_time = statistics.mean(successful_times)
            sorted_times = sorted(successful_times)
            p95_index = int(len(sorted_times) * 0.95)
            p99_index = int(len(sorted_times) * 0.99)
            p95_response_time = sorted_times[p95_index] if p95_index < len(sorted_times) else sorted_times[-1]
            p99_response_time = sorted_times[p99_index] if p99_index < len(sorted_times) else sorted_times[-1]
        else:
            avg_response_time = p95_response_time = p99_response_time = 0
        
        throughput_rps = successful_requests / duration if duration > 0 else 0
        error_rate_percent = (failed_requests / total_requests * 100) if total_requests > 0 else 0
        
        # Estimated resource usage
        memory_usage_mb = 128.0 + (concurrent_users * 15.0)
        cpu_usage_percent = min(80.0, concurrent_users * 8.0)
        
        return {
            "avg_response_time_ms": avg_response_time,
            "p95_response_time_ms": p95_response_time,
            "p99_response_time_ms": p99_response_time,
            "throughput_rps": throughput_rps,
            "memory_usage_mb": memory_usage_mb,
            "cpu_usage_percent": cpu_usage_percent,
            "error_rate_percent": error_rate_percent,
            "total_requests": total_requests,
            "successful_requests": successful_requests,
            "patterns_detected": patterns_detected
        }
    
    def _analyze_regression(
        self, 
        baseline: PerformanceBaseline, 
        current_metrics: Dict[str, float]
    ) -> RegressionTestResult:
        """Analyze current metrics against baseline for regression."""
        
        # Extract baseline metrics
        baseline_metrics = {
            "avg_response_time_ms": baseline.avg_response_time_ms,
            "p95_response_time_ms": baseline.p95_response_time_ms,
            "p99_response_time_ms": baseline.p99_response_time_ms,
            "throughput_rps": baseline.throughput_rps,
            "memory_usage_mb": baseline.memory_usage_mb,
            "cpu_usage_percent": baseline.cpu_usage_percent,
            "error_rate_percent": baseline.error_rate_percent,
        }
        
        # Calculate changes for each metric
        metric_changes = {}
        failed_metrics = []
        regression_count = 0
        
        for metric, threshold in self.default_thresholds.items():
            if metric in baseline_metrics and metric in current_metrics:
                baseline_value = baseline_metrics[metric]
                current_value = current_metrics[metric]
                
                if baseline_value == 0:
                    # Handle division by zero
                    absolute_change = current_value
                    percent_change = 100.0 if current_value > 0 else 0.0
                else:
                    absolute_change = current_value - baseline_value
                    percent_change = (absolute_change / baseline_value) * 100
                
                # Determine if this is a regression
                is_regression = False
                if threshold > 0:
                    # Higher values are bad (response time, memory, etc.)
                    is_regression = percent_change > threshold
                else:
                    # Lower values are bad (throughput)
                    is_regression = percent_change < threshold
                
                metric_changes[metric] = {
                    "baseline_value": baseline_value,
                    "current_value": current_value,
                    "absolute_change": absolute_change,
                    "percent_change": percent_change,
                    "threshold": threshold,
                    "is_regression": is_regression
                }
                
                if is_regression:
                    failed_metrics.append(metric)
                    regression_count += 1
        
        # Determine overall regression status
        overall_regression_detected = regression_count > 0
        
        # Determine regression severity
        if regression_count == 0:
            regression_severity = "none"
        elif regression_count <= 2 and all(
            abs(metric_changes[m]["percent_change"]) < abs(self.default_thresholds[m]) * 1.5 
            for m in failed_metrics
        ):
            regression_severity = "minor"
        elif regression_count <= 3:
            regression_severity = "major"
        else:
            regression_severity = "critical"
        
        # Generate warnings and recommendations
        warnings = []
        recommendations = []
        
        for metric in failed_metrics:
            change = metric_changes[metric]
            warnings.append(
                f"{metric}: {change['percent_change']:+.1f}% change "
                f"(threshold: {change['threshold']:+.1f}%)"
            )
        
        if "avg_response_time_ms" in failed_metrics:
            recommendations.append("Investigate response time increase - check for algorithmic changes or resource constraints")
        
        if "throughput_rps" in failed_metrics:
            recommendations.append("Throughput regression detected - review concurrency handling and resource scaling")
        
        if "memory_usage_mb" in failed_metrics:
            recommendations.append("Memory usage increased - check for memory leaks or inefficient data structures")
        
        if "error_rate_percent" in failed_metrics:
            recommendations.append("Error rate increased - investigate error handling and system stability")
        
        # Test passes if no regressions detected
        passed = not overall_regression_detected
        
        return RegressionTestResult(
            test_name=f"regression_vs_{baseline.baseline_name}",
            test_date=datetime.utcnow(),
            baseline_used=baseline.baseline_name,
            service_version=os.getenv("SERVICE_VERSION", "unknown"),
            current_metrics=current_metrics,
            baseline_metrics=baseline_metrics,
            metric_changes=metric_changes,
            overall_regression_detected=overall_regression_detected,
            regression_severity=regression_severity,
            regression_thresholds=self.default_thresholds,
            passed=passed,
            failed_metrics=failed_metrics,
            warnings=warnings,
            recommendations=recommendations
        )
    
    def _save_baseline(self, baseline: PerformanceBaseline):
        """Save baseline to file."""
        baseline_file = self.baseline_dir / f"{baseline.baseline_name}.json"
        
        with open(baseline_file, 'w') as f:
            json.dump(baseline.to_dict(), f, indent=2)
        
        logger.info(f"Baseline saved: {baseline_file}")
    
    def _load_baseline(self, baseline_name: str) -> Optional[PerformanceBaseline]:
        """Load baseline from file."""
        baseline_file = self.baseline_dir / f"{baseline_name}.json"
        
        if not baseline_file.exists():
            return None
        
        try:
            with open(baseline_file, 'r') as f:
                data = json.load(f)
            
            return PerformanceBaseline.from_dict(data)
        except Exception as e:
            logger.error(f"Failed to load baseline {baseline_name}: {e}")
            return None
    
    def list_baselines(self) -> List[str]:
        """List available baselines."""
        baseline_files = self.baseline_dir.glob("*.json")
        return [f.stem for f in baseline_files]
    
    def cleanup_old_baselines(self, days_old: int = 30):
        """Clean up baselines older than specified days."""
        cutoff_date = datetime.utcnow() - timedelta(days=days_old)
        
        for baseline_name in self.list_baselines():
            baseline = self._load_baseline(baseline_name)
            if baseline and baseline.created_date < cutoff_date:
                baseline_file = self.baseline_dir / f"{baseline_name}.json"
                baseline_file.unlink()
                logger.info(f"Cleaned up old baseline: {baseline_name}")


@pytest.mark.performance
@pytest.mark.regression
class TestPerformanceRegression:
    """Performance regression detection tests."""
    
    @pytest.fixture
    def regression_detector(self, tmp_path):
        """Create regression detector with temporary baseline directory."""
        return PerformanceRegressionDetector(baseline_dir=str(tmp_path / "baselines"))
    
    @pytest.mark.asyncio
    async def test_baseline_establishment(self, async_test_client: AsyncClient, regression_detector):
        """Test establishing a performance baseline."""
        
        baseline_config = {
            "concurrent_users": 3,
            "test_duration_seconds": 15,
            "requests_per_user": 4
        }
        
        baseline = await regression_detector.establish_baseline(
            async_test_client, 
            "test_baseline_v1", 
            baseline_config
        )
        
        # Assertions
        assert baseline.baseline_name == "test_baseline_v1"
        assert baseline.concurrent_users == 3
        assert baseline.request_count > 0
        assert baseline.avg_response_time_ms >= 0
        assert baseline.throughput_rps >= 0
        assert baseline.error_rate_percent >= 0
        
        # Verify baseline was saved
        assert "test_baseline_v1" in regression_detector.list_baselines()
        
        # Log baseline metrics
        logger.info(f"Established baseline: {baseline.baseline_name}")
        logger.info(f"  Avg response time: {baseline.avg_response_time_ms:.1f}ms")
        logger.info(f"  P95 response time: {baseline.p95_response_time_ms:.1f}ms")
        logger.info(f"  Throughput: {baseline.throughput_rps:.1f} RPS")
        logger.info(f"  Memory usage: {baseline.memory_usage_mb:.1f}MB")
        logger.info(f"  Error rate: {baseline.error_rate_percent:.1f}%")
    
    @pytest.mark.asyncio
    async def test_no_regression_detection(self, async_test_client: AsyncClient, regression_detector):
        """Test regression detection when no regression exists."""
        
        # First establish baseline
        baseline = await regression_detector.establish_baseline(
            async_test_client, 
            "no_regression_baseline",
            {"concurrent_users": 2, "requests_per_user": 3}
        )
        
        # Run regression test (should be similar performance)
        regression_result = await regression_detector.detect_regression(
            async_test_client, 
            "no_regression_baseline"
        )
        
        # Assertions
        assert regression_result.overall_regression_detected == False
        assert regression_result.regression_severity == "none"
        assert regression_result.passed == True
        assert len(regression_result.failed_metrics) == 0
        
        # Log results
        logger.info(f"No regression test results:")
        logger.info(f"  Regression detected: {regression_result.overall_regression_detected}")
        logger.info(f"  Test passed: {regression_result.passed}")
        
        # Check individual metric changes
        for metric, change in regression_result.metric_changes.items():
            logger.info(f"  {metric}: {change['percent_change']:+.1f}% change")
    
    @pytest.mark.asyncio
    async def test_simulated_regression_detection(self, async_test_client: AsyncClient, regression_detector):
        """Test regression detection with simulated regression."""
        
        # Establish baseline
        baseline = await regression_detector.establish_baseline(
            async_test_client, 
            "regression_simulation_baseline",
            {"concurrent_users": 2, "requests_per_user": 2}
        )
        
        # Simulate regression by modifying thresholds for testing
        original_thresholds = regression_detector.default_thresholds.copy()
        
        # Set very tight thresholds to trigger regression
        regression_detector.default_thresholds = {
            "avg_response_time_ms": 5.0,     # 5% slower triggers regression
            "p95_response_time_ms": 5.0,     # 5% slower triggers regression
            "p99_response_time_ms": 5.0,     # 5% slower triggers regression
            "throughput_rps": -5.0,          # 5% lower throughput triggers regression
            "memory_usage_mb": 5.0,          # 5% more memory triggers regression
            "cpu_usage_percent": 5.0,        # 5% more CPU triggers regression
            "error_rate_percent": 50.0,      # 50% more errors triggers regression
        }
        
        try:
            # Run regression test with tight thresholds
            regression_result = await regression_detector.detect_regression(
                async_test_client, 
                "regression_simulation_baseline"
            )
            
            # With tight thresholds, we expect some variation to trigger "regression"
            # This tests the detection mechanism
            
            # Log results
            logger.info(f"Simulated regression test results:")
            logger.info(f"  Regression detected: {regression_result.overall_regression_detected}")
            logger.info(f"  Severity: {regression_result.regression_severity}")
            logger.info(f"  Failed metrics: {len(regression_result.failed_metrics)}")
            
            for metric in regression_result.failed_metrics:
                change = regression_result.metric_changes[metric]
                logger.info(f"    {metric}: {change['percent_change']:+.1f}% change")
            
            # Verify regression analysis structure
            assert isinstance(regression_result.metric_changes, dict)
            assert isinstance(regression_result.failed_metrics, list)
            assert isinstance(regression_result.warnings, list)
            assert isinstance(regression_result.recommendations, list)
            
        finally:
            # Restore original thresholds
            regression_detector.default_thresholds = original_thresholds
    
    @pytest.mark.asyncio
    async def test_baseline_loading_and_comparison(self, async_test_client: AsyncClient, regression_detector):
        """Test baseline persistence and loading."""
        
        # Establish and save baseline
        baseline = await regression_detector.establish_baseline(
            async_test_client, 
            "persistence_test_baseline",
            {"concurrent_users": 2, "requests_per_user": 2}
        )
        
        original_baseline_name = baseline.baseline_name
        original_response_time = baseline.avg_response_time_ms
        
        # Load baseline back
        loaded_baseline = regression_detector._load_baseline("persistence_test_baseline")
        
        # Assertions
        assert loaded_baseline is not None
        assert loaded_baseline.baseline_name == original_baseline_name
        assert loaded_baseline.avg_response_time_ms == original_response_time
        assert loaded_baseline.concurrent_users == 2
        
        # Test baseline listing
        baselines = regression_detector.list_baselines()
        assert "persistence_test_baseline" in baselines
        
        logger.info(f"Baseline persistence test passed")
        logger.info(f"  Saved and loaded baseline: {loaded_baseline.baseline_name}")
        logger.info(f"  Response time preserved: {loaded_baseline.avg_response_time_ms:.1f}ms")
    
    @pytest.mark.asyncio
    async def test_regression_result_serialization(self, async_test_client: AsyncClient, regression_detector):
        """Test regression result serialization for CI/CD integration."""
        
        # Establish baseline
        baseline = await regression_detector.establish_baseline(
            async_test_client, 
            "serialization_baseline",
            {"concurrent_users": 1, "requests_per_user": 2}
        )
        
        # Run regression test
        regression_result = await regression_detector.detect_regression(
            async_test_client, 
            "serialization_baseline"
        )
        
        # Test serialization
        result_dict = regression_result.to_dict()
        assert isinstance(result_dict, dict)
        assert "test_name" in result_dict
        assert "overall_regression_detected" in result_dict
        assert "metric_changes" in result_dict
        assert "recommendations" in result_dict
        
        # Test JSON serialization
        json_str = json.dumps(result_dict, indent=2)
        assert len(json_str) > 100
        
        # Test deserialization
        parsed_result = json.loads(json_str)
        assert parsed_result["test_name"] == regression_result.test_name
        assert parsed_result["passed"] == regression_result.passed
        
        # Validate timestamp format
        datetime.fromisoformat(parsed_result["test_date"])
        
        logger.info(f"Regression result serialization test passed")
    
    @pytest.mark.asyncio
    async def test_baseline_cleanup(self, async_test_client: AsyncClient, regression_detector):
        """Test baseline cleanup functionality."""
        
        # Create a test baseline
        baseline = await regression_detector.establish_baseline(
            async_test_client, 
            "cleanup_test_baseline",
            {"concurrent_users": 1, "requests_per_user": 1}
        )
        
        # Verify baseline exists
        assert "cleanup_test_baseline" in regression_detector.list_baselines()
        
        # Test cleanup with future date (should not delete)
        regression_detector.cleanup_old_baselines(days_old=0)
        assert "cleanup_test_baseline" in regression_detector.list_baselines()
        
        # Test cleanup mechanism exists
        baselines_before = len(regression_detector.list_baselines())
        regression_detector.cleanup_old_baselines(days_old=365)  # Far future, shouldn't delete anything
        baselines_after = len(regression_detector.list_baselines())
        
        assert baselines_before == baselines_after
        
        logger.info(f"Baseline cleanup test passed")
    
    @pytest.mark.asyncio
    async def test_ci_cd_integration_format(self, async_test_client: AsyncClient, regression_detector):
        """Test CI/CD integration output format."""
        
        # Establish baseline
        baseline = await regression_detector.establish_baseline(
            async_test_client, 
            "cicd_test_baseline",
            {"concurrent_users": 2, "requests_per_user": 2}
        )
        
        # Run regression test
        regression_result = await regression_detector.detect_regression(
            async_test_client, 
            "cicd_test_baseline"
        )
        
        # Generate CI/CD friendly output
        cicd_output = {
            "test_status": "PASS" if regression_result.passed else "FAIL",
            "regression_detected": regression_result.overall_regression_detected,
            "severity": regression_result.regression_severity,
            "failed_metrics_count": len(regression_result.failed_metrics),
            "baseline_used": regression_result.baseline_used,
            "test_date": regression_result.test_date.isoformat(),
            "summary": {
                "avg_response_time_change": regression_result.metric_changes.get("avg_response_time_ms", {}).get("percent_change", 0),
                "throughput_change": regression_result.metric_changes.get("throughput_rps", {}).get("percent_change", 0),
                "error_rate_change": regression_result.metric_changes.get("error_rate_percent", {}).get("percent_change", 0)
            },
            "recommendations": regression_result.recommendations
        }
        
        # Validate CI/CD output structure
        assert "test_status" in cicd_output
        assert "regression_detected" in cicd_output
        assert "severity" in cicd_output
        assert "summary" in cicd_output
        
        # Log CI/CD output
        logger.info(f"CI/CD Integration Output:")
        logger.info(f"  Status: {cicd_output['test_status']}")
        logger.info(f"  Regression: {cicd_output['regression_detected']}")
        logger.info(f"  Severity: {cicd_output['severity']}")
        
        # Test exit code logic for CI/CD
        exit_code = 0 if regression_result.passed else 1
        if regression_result.overall_regression_detected and regression_result.regression_severity == "critical":
            exit_code = 2  # Critical regression
        
        logger.info(f"  Suggested exit code: {exit_code}")
        
        # Verify output can be serialized for CI systems
        cicd_json = json.dumps(cicd_output, indent=2)
        assert len(cicd_json) > 50
        
        parsed_cicd = json.loads(cicd_json)
        assert parsed_cicd["test_status"] == cicd_output["test_status"]