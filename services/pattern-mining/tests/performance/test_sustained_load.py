"""
Sustained Load Testing with Memory Leak Detection for Pattern Mining Service

This module provides long-duration load testing capabilities including:
- Extended load testing (4-24+ hours)
- Memory leak detection and analysis
- Performance degradation monitoring over time
- Resource accumulation tracking
- Service stability validation under extended load
- Progressive memory usage monitoring
"""

import pytest
import asyncio
import time
import logging
import psutil
import gc
import tracemalloc
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timed<PERSON>ta
from dataclasses import dataclass, asdict
import statistics
import json
import threading
from concurrent.futures import ThreadPoolExecutor
from contextlib import asynccontextmanager

from httpx import AsyncClient
from tests.utils.performance import (
    PerformanceTestRunner, LatencyMeasurer, MemoryProfiler, 
    ThroughputTester, performance_test
)
from tests.utils.generators import CodeGenerator, PatternGenerator

logger = logging.getLogger(__name__)


@dataclass
class MemorySnapshot:
    """Memory usage snapshot at a point in time."""
    
    timestamp: datetime
    rss_memory_mb: float  # Resident Set Size
    vms_memory_mb: float  # Virtual Memory Size
    heap_memory_mb: float  # Heap memory (from tracemalloc)
    peak_heap_mb: float   # Peak heap memory
    allocated_objects: int
    garbage_collections: Dict[int, int]  # GC stats by generation
    
    # Memory growth indicators
    memory_delta_mb: float = 0.0  # Change from baseline
    memory_growth_rate_mb_per_hour: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        result = asdict(self)
        result['timestamp'] = self.timestamp.isoformat()
        return result


@dataclass
class SustainedLoadConfiguration:
    """Configuration for sustained load testing."""
    
    # Test identification
    test_name: str
    test_description: str
    
    # Duration parameters
    test_duration_hours: float
    warmup_duration_minutes: int = 10
    cooldown_duration_minutes: int = 5
    
    # Load parameters
    concurrent_users: int
    target_requests_per_minute: int
    
    # Memory monitoring
    memory_snapshot_interval_minutes: int = 5
    memory_leak_threshold_mb_per_hour: float = 50.0
    max_memory_growth_mb: float = 500.0
    
    # Performance thresholds
    max_avg_response_time_ms: float = 150.0
    max_p95_response_time_ms: float = 300.0
    max_error_rate_percent: float = 5.0
    
    # Stability checks
    performance_degradation_threshold_percent: float = 25.0
    max_consecutive_failures: int = 10
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return asdict(self)


@dataclass
class SustainedLoadResult:
    """Results from sustained load testing."""
    
    # Test metadata
    test_config: SustainedLoadConfiguration
    start_time: datetime
    end_time: datetime
    actual_duration_hours: float
    
    # Request metrics
    total_requests: int
    successful_requests: int
    failed_requests: int
    average_response_time_ms: float
    p95_response_time_ms: float
    p99_response_time_ms: float
    
    # Memory analysis
    memory_snapshots: List[MemorySnapshot]
    memory_leak_detected: bool
    memory_growth_mb: float
    memory_growth_rate_mb_per_hour: float
    peak_memory_usage_mb: float
    
    # Performance degradation analysis
    performance_degraded: bool
    degradation_start_time: Optional[datetime]
    initial_performance_ms: float
    final_performance_ms: float
    performance_change_percent: float
    
    # Stability metrics
    service_stability_score: float
    consecutive_failure_episodes: int
    max_consecutive_failures: int
    uptime_percentage: float
    
    # Resource accumulation
    file_handles_growth: int
    database_connections_growth: int
    thread_count_growth: int
    
    # Test outcome
    test_passed: bool
    critical_issues: List[str]
    warnings: List[str]
    recommendations: List[str]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        result = asdict(self)
        result['test_config'] = self.test_config.to_dict()
        result['start_time'] = self.start_time.isoformat()
        result['end_time'] = self.end_time.isoformat()
        result['degradation_start_time'] = self.degradation_start_time.isoformat() if self.degradation_start_time else None
        result['memory_snapshots'] = [snapshot.to_dict() for snapshot in self.memory_snapshots]
        return result


class MemoryLeakDetector:
    """Advanced memory leak detection and analysis."""
    
    def __init__(self):
        self.snapshots: List[MemorySnapshot] = []
        self.baseline_snapshot: Optional[MemorySnapshot] = None
        self.process = psutil.Process()
        self.tracemalloc_started = False
        
    def start_monitoring(self):
        """Start memory monitoring."""
        if not self.tracemalloc_started:
            tracemalloc.start()
            self.tracemalloc_started = True
        
        # Force garbage collection for clean baseline
        gc.collect()
        
        # Take baseline snapshot
        self.baseline_snapshot = self._take_snapshot("baseline")
        self.snapshots.append(self.baseline_snapshot)
        
        logger.info(f"Memory monitoring started. Baseline: {self.baseline_snapshot.rss_memory_mb:.1f}MB RSS")
    
    def stop_monitoring(self):
        """Stop memory monitoring."""
        if self.tracemalloc_started:
            tracemalloc.stop()
            self.tracemalloc_started = False
    
    def take_periodic_snapshot(self, label: str = "periodic") -> MemorySnapshot:
        """Take a periodic memory snapshot."""
        snapshot = self._take_snapshot(label)
        
        # Calculate growth metrics
        if self.baseline_snapshot:
            snapshot.memory_delta_mb = snapshot.rss_memory_mb - self.baseline_snapshot.rss_memory_mb
            
            # Calculate growth rate
            time_diff_hours = (snapshot.timestamp - self.baseline_snapshot.timestamp).total_seconds() / 3600
            if time_diff_hours > 0:
                snapshot.memory_growth_rate_mb_per_hour = snapshot.memory_delta_mb / time_diff_hours
        
        self.snapshots.append(snapshot)
        return snapshot
    
    def _take_snapshot(self, label: str) -> MemorySnapshot:
        """Take a detailed memory snapshot."""
        timestamp = datetime.utcnow()
        
        # Process memory info
        memory_info = self.process.memory_info()
        rss_mb = memory_info.rss / 1024 / 1024
        vms_mb = memory_info.vms / 1024 / 1024
        
        # Tracemalloc info
        if tracemalloc.is_tracing():
            current_heap, peak_heap = tracemalloc.get_traced_memory()
            heap_mb = current_heap / 1024 / 1024
            peak_heap_mb = peak_heap / 1024 / 1024
        else:
            heap_mb = peak_heap_mb = 0
        
        # Object count (approximate)
        allocated_objects = len(gc.get_objects())
        
        # Garbage collection stats
        gc_stats = {}
        for i in range(3):  # Python has 3 GC generations
            gc_stats[i] = gc.get_count()[i]
        
        return MemorySnapshot(
            timestamp=timestamp,
            rss_memory_mb=rss_mb,
            vms_memory_mb=vms_mb,
            heap_memory_mb=heap_mb,
            peak_heap_mb=peak_heap_mb,
            allocated_objects=allocated_objects,
            garbage_collections=gc_stats
        )
    
    def analyze_memory_leaks(self) -> Dict[str, Any]:
        """Analyze memory usage patterns for leaks."""
        if len(self.snapshots) < 3:
            return {"error": "Insufficient snapshots for analysis"}
        
        # Calculate memory growth trend
        timestamps = [(s.timestamp - self.snapshots[0].timestamp).total_seconds() / 3600 for s in self.snapshots]
        rss_values = [s.rss_memory_mb for s in self.snapshots]
        heap_values = [s.heap_memory_mb for s in self.snapshots]
        
        # Linear regression for growth trend
        memory_trend = self._calculate_trend(timestamps, rss_values)
        heap_trend = self._calculate_trend(timestamps, heap_values)
        
        # Memory leak indicators
        total_growth = rss_values[-1] - rss_values[0]
        time_span_hours = timestamps[-1]
        growth_rate = total_growth / time_span_hours if time_span_hours > 0 else 0
        
        # Detect memory leak patterns
        leak_detected = self._detect_leak_patterns(rss_values, heap_values)
        
        # Object growth analysis
        object_counts = [s.allocated_objects for s in self.snapshots]
        object_growth = object_counts[-1] - object_counts[0]
        
        return {
            "total_snapshots": len(self.snapshots),
            "test_duration_hours": time_span_hours,
            "memory_growth_mb": total_growth,
            "memory_growth_rate_mb_per_hour": growth_rate,
            "memory_trend_slope": memory_trend["slope"],
            "heap_trend_slope": heap_trend["slope"],
            "memory_leak_detected": leak_detected,
            "object_growth": object_growth,
            "peak_memory_mb": max(rss_values),
            "baseline_memory_mb": rss_values[0],
            "final_memory_mb": rss_values[-1],
            "memory_efficiency": self._calculate_memory_efficiency(),
            "gc_pressure": self._analyze_gc_pressure()
        }
    
    def _calculate_trend(self, x_values: List[float], y_values: List[float]) -> Dict[str, float]:
        """Calculate linear trend using least squares."""
        if len(x_values) != len(y_values) or len(x_values) < 2:
            return {"slope": 0.0, "intercept": 0.0, "r_squared": 0.0}
        
        n = len(x_values)
        sum_x = sum(x_values)
        sum_y = sum(y_values)
        sum_xy = sum(x * y for x, y in zip(x_values, y_values))
        sum_x2 = sum(x * x for x in x_values)
        
        # Calculate slope and intercept
        denominator = n * sum_x2 - sum_x * sum_x
        if denominator == 0:
            return {"slope": 0.0, "intercept": 0.0, "r_squared": 0.0}
        
        slope = (n * sum_xy - sum_x * sum_y) / denominator
        intercept = (sum_y - slope * sum_x) / n
        
        # Calculate R-squared
        mean_y = sum_y / n
        ss_tot = sum((y - mean_y) ** 2 for y in y_values)
        ss_res = sum((y - (slope * x + intercept)) ** 2 for x, y in zip(x_values, y_values))
        r_squared = 1 - (ss_res / ss_tot) if ss_tot != 0 else 0
        
        return {"slope": slope, "intercept": intercept, "r_squared": r_squared}
    
    def _detect_leak_patterns(self, rss_values: List[float], heap_values: List[float]) -> bool:
        """Detect memory leak patterns."""
        if len(rss_values) < 5:
            return False
        
        # Pattern 1: Continuous upward trend
        continuous_growth = all(
            rss_values[i] >= rss_values[i-1] * 0.98  # Allow for small fluctuations
            for i in range(1, len(rss_values))
        )
        
        # Pattern 2: Significant growth over time
        total_growth = rss_values[-1] - rss_values[0]
        significant_growth = total_growth > 100  # More than 100MB growth
        
        # Pattern 3: Heap growth without corresponding RSS release
        heap_growth = heap_values[-1] - heap_values[0] if heap_values[-1] > 0 else 0
        heap_leak_indicator = heap_growth > 50  # More than 50MB heap growth
        
        return continuous_growth and (significant_growth or heap_leak_indicator)
    
    def _calculate_memory_efficiency(self) -> float:
        """Calculate memory usage efficiency score."""
        if len(self.snapshots) < 2:
            return 100.0
        
        # Efficiency based on memory stability and growth rate
        baseline_memory = self.snapshots[0].rss_memory_mb
        current_memory = self.snapshots[-1].rss_memory_mb
        
        if baseline_memory == 0:
            return 100.0 if current_memory == 0 else 0.0
        
        growth_ratio = current_memory / baseline_memory
        
        # Perfect efficiency (100) = no growth, poor efficiency (0) = 2x+ growth
        if growth_ratio <= 1.1:  # Less than 10% growth
            return 100.0
        elif growth_ratio >= 2.0:  # 2x or more growth
            return 0.0
        else:
            # Linear scale between 10% and 100% growth
            return max(0.0, 100.0 - ((growth_ratio - 1.1) / 0.9) * 100.0)
    
    def _analyze_gc_pressure(self) -> Dict[str, Any]:
        """Analyze garbage collection pressure."""
        if len(self.snapshots) < 2:
            return {"gc_pressure": "unknown"}
        
        # Compare GC counts between first and last snapshots
        initial_gc = self.snapshots[0].garbage_collections
        final_gc = self.snapshots[-1].garbage_collections
        
        gc_increases = {}
        total_increase = 0
        
        for generation in range(3):
            increase = final_gc.get(generation, 0) - initial_gc.get(generation, 0)
            gc_increases[f"gen_{generation}"] = increase
            total_increase += increase
        
        # Calculate GC pressure level
        time_hours = (self.snapshots[-1].timestamp - self.snapshots[0].timestamp).total_seconds() / 3600
        gc_rate = total_increase / time_hours if time_hours > 0 else 0
        
        if gc_rate < 10:
            pressure_level = "low"
        elif gc_rate < 50:
            pressure_level = "moderate"
        elif gc_rate < 100:
            pressure_level = "high"
        else:
            pressure_level = "extreme"
        
        return {
            "gc_pressure": pressure_level,
            "gc_rate_per_hour": gc_rate,
            "gc_increases": gc_increases,
            "total_gc_increase": total_increase
        }


class SustainedLoadExecutor:
    """Execute long-duration sustained load tests."""
    
    def __init__(self, client: AsyncClient):
        self.client = client
        self.code_generator = CodeGenerator()
        self.memory_detector = MemoryLeakDetector()
        self.process = psutil.Process()
        
        # Monitoring state
        self.is_monitoring = False
        self.monitoring_task: Optional[asyncio.Task] = None
        self.request_metrics: List[Dict[str, Any]] = []
        
    async def execute_sustained_load_test(self, config: SustainedLoadConfiguration) -> SustainedLoadResult:
        """Execute a comprehensive sustained load test."""
        logger.info(f"Starting sustained load test: {config.test_name}")
        logger.info(f"Duration: {config.test_duration_hours} hours, Load: {config.concurrent_users} users")
        
        start_time = datetime.utcnow()
        
        # Start memory monitoring
        self.memory_detector.start_monitoring()
        
        # Initialize tracking
        self.request_metrics = []
        self.is_monitoring = True
        
        # Start background monitoring
        self.monitoring_task = asyncio.create_task(
            self._background_monitoring(config)
        )
        
        try:
            # Phase 1: Warmup
            logger.info("Phase 1: Warmup phase")
            await self._execute_warmup_phase(config)
            
            # Phase 2: Sustained Load
            logger.info("Phase 2: Sustained load phase")
            await self._execute_sustained_load_phase(config)
            
            # Phase 3: Cooldown
            logger.info("Phase 3: Cooldown phase")
            await self._execute_cooldown_phase(config)
            
        finally:
            # Stop monitoring
            self.is_monitoring = False
            if self.monitoring_task:
                self.monitoring_task.cancel()
                try:
                    await self.monitoring_task
                except asyncio.CancelledError:
                    pass
            
            self.memory_detector.stop_monitoring()
        
        end_time = datetime.utcnow()
        actual_duration = (end_time - start_time).total_seconds() / 3600
        
        # Analyze results
        result = self._analyze_sustained_load_results(config, start_time, end_time, actual_duration)
        
        logger.info(f"Sustained load test completed: {result.test_passed}")
        logger.info(f"Memory growth: {result.memory_growth_mb:.1f}MB, Performance change: {result.performance_change_percent:.1f}%")
        
        return result
    
    async def _background_monitoring(self, config: SustainedLoadConfiguration):
        """Background monitoring task for memory and performance."""
        snapshot_interval = config.memory_snapshot_interval_minutes * 60
        
        while self.is_monitoring:
            try:
                # Take memory snapshot
                snapshot = self.memory_detector.take_periodic_snapshot()
                
                logger.debug(f"Memory snapshot: {snapshot.rss_memory_mb:.1f}MB RSS, "
                           f"growth: {snapshot.memory_delta_mb:.1f}MB")
                
                # Check for immediate issues
                if snapshot.memory_delta_mb > config.max_memory_growth_mb:
                    logger.warning(f"Memory growth exceeded threshold: {snapshot.memory_delta_mb:.1f}MB")
                
                # Wait for next snapshot
                await asyncio.sleep(snapshot_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Background monitoring error: {e}")
                await asyncio.sleep(60)  # Wait before retrying
    
    async def _execute_warmup_phase(self, config: SustainedLoadConfiguration):
        """Execute warmup phase to stabilize the system."""
        warmup_duration = config.warmup_duration_minutes * 60
        warmup_users = min(config.concurrent_users // 2, 10)  # Start with fewer users
        
        await self._execute_load_phase(
            duration_seconds=warmup_duration,
            concurrent_users=warmup_users,
            requests_per_minute=config.target_requests_per_minute // 2,
            phase_name="warmup"
        )
    
    async def _execute_sustained_load_phase(self, config: SustainedLoadConfiguration):
        """Execute the main sustained load phase."""
        load_duration = config.test_duration_hours * 3600
        
        await self._execute_load_phase(
            duration_seconds=load_duration,
            concurrent_users=config.concurrent_users,
            requests_per_minute=config.target_requests_per_minute,
            phase_name="sustained"
        )
    
    async def _execute_cooldown_phase(self, config: SustainedLoadConfiguration):
        """Execute cooldown phase for graceful completion."""
        cooldown_duration = config.cooldown_duration_minutes * 60
        cooldown_users = min(config.concurrent_users // 4, 5)  # Reduced load
        
        await self._execute_load_phase(
            duration_seconds=cooldown_duration,
            concurrent_users=cooldown_users,
            requests_per_minute=config.target_requests_per_minute // 4,
            phase_name="cooldown"
        )
    
    async def _execute_load_phase(
        self, 
        duration_seconds: float,
        concurrent_users: int,
        requests_per_minute: int,
        phase_name: str
    ):
        """Execute a specific load phase."""
        start_time = time.perf_counter()
        end_time = start_time + duration_seconds
        
        # Calculate request interval
        request_interval = 60.0 / requests_per_minute if requests_per_minute > 0 else 1.0
        
        # Create semaphore for concurrency control
        semaphore = asyncio.Semaphore(concurrent_users)
        
        # Generate test data
        test_data = self._prepare_sustained_test_data(concurrent_users)
        
        async def make_request_with_semaphore(data: Dict[str, Any]) -> Dict[str, Any]:
            """Make request with concurrency limiting."""
            async with semaphore:
                return await self._make_sustained_request(data, phase_name)
        
        # Execute load for the duration
        data_index = 0
        request_count = 0
        
        while time.perf_counter() < end_time:
            batch_start = time.perf_counter()
            
            # Create batch of requests
            batch_size = min(concurrent_users, len(test_data))
            batch_tasks = []
            
            for _ in range(batch_size):
                data = test_data[data_index % len(test_data)]
                task = make_request_with_semaphore(data)
                batch_tasks.append(task)
                data_index += 1
                request_count += 1
            
            # Execute batch
            batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
            
            # Process results
            for result in batch_results:
                if isinstance(result, Exception):
                    self.request_metrics.append({
                        "success": False,
                        "error": str(result),
                        "response_time_ms": 0,
                        "status_code": 0,
                        "timestamp": datetime.utcnow(),
                        "phase": phase_name
                    })
                else:
                    result["phase"] = phase_name
                    self.request_metrics.append(result)
            
            # Rate limiting - wait to maintain target RPS
            batch_end = time.perf_counter()
            batch_duration = batch_end - batch_start
            target_batch_duration = batch_size * request_interval
            
            if batch_duration < target_batch_duration:
                await asyncio.sleep(target_batch_duration - batch_duration)
        
        logger.info(f"Completed {phase_name} phase: {request_count} requests sent")
    
    def _prepare_sustained_test_data(self, user_count: int) -> List[Dict[str, Any]]:
        """Prepare test data for sustained testing with variety."""
        test_data = []
        
        for i in range(user_count):
            # Vary complexity throughout the test
            complexity_cycle = i % 6
            if complexity_cycle == 0:
                # Simple function
                code_content = self.code_generator.generate_function()
                expected_patterns = 1
            elif complexity_cycle == 1:
                # Small module
                code_content = self.code_generator.generate_file_content("module", pattern_count=3)
                expected_patterns = 3
            elif complexity_cycle == 2:
                # Medium module
                code_content = self.code_generator.generate_file_content("module", pattern_count=8)
                expected_patterns = 8
            elif complexity_cycle == 3:
                # Large module
                code_content = self.code_generator.generate_file_content("module", pattern_count=15)
                expected_patterns = 15
            elif complexity_cycle == 4:
                # Very large module
                code_content = self.code_generator.generate_file_content("module", pattern_count=25)
                expected_patterns = 25
            else:
                # Mixed complexity
                code_content = self.code_generator.generate_file_content("module", pattern_count=random.randint(1, 30))
                expected_patterns = random.randint(1, 30)
            
            test_data.append({
                "repository_id": f"sustained-test-repo-{i % 100}",  # Reuse repo IDs for caching
                "ast_data": {"type": "Module", "children": []},
                "code_content": code_content,
                "file_path": f"sustained_test_{complexity_cycle}_{i}.py",
                "language": "python",
                "detection_config": {
                    "confidence_threshold": 0.7,
                    "enable_ml_models": True,
                    "enable_heuristic_detection": True
                },
                "test_metadata": {
                    "complexity_level": complexity_cycle,
                    "expected_patterns": expected_patterns,
                    "data_index": i
                }
            })
        
        return test_data
    
    async def _make_sustained_request(self, data: Dict[str, Any], phase: str) -> Dict[str, Any]:
        """Make a single request for sustained testing."""
        start_time = time.perf_counter()
        timestamp = datetime.utcnow()
        
        try:
            response = await asyncio.wait_for(
                self.client.post("/api/v1/patterns/detect", json=data),
                timeout=60.0  # Longer timeout for sustained testing
            )
            
            end_time = time.perf_counter()
            response_time_ms = (end_time - start_time) * 1000
            
            return {
                "success": response.status_code < 400,
                "status_code": response.status_code,
                "response_time_ms": response_time_ms,
                "timestamp": timestamp,
                "phase": phase,
                "complexity_level": data.get("test_metadata", {}).get("complexity_level", 0),
                "error": None if response.status_code < 400 else f"HTTP {response.status_code}"
            }
            
        except asyncio.TimeoutError:
            end_time = time.perf_counter()
            response_time_ms = (end_time - start_time) * 1000
            
            return {
                "success": False,
                "status_code": 408,
                "response_time_ms": response_time_ms,
                "timestamp": timestamp,
                "phase": phase,
                "complexity_level": data.get("test_metadata", {}).get("complexity_level", 0),
                "error": "Request timeout"
            }
            
        except Exception as e:
            end_time = time.perf_counter()
            response_time_ms = (end_time - start_time) * 1000
            
            return {
                "success": False,
                "status_code": 500,
                "response_time_ms": response_time_ms,
                "timestamp": timestamp,
                "phase": phase,
                "complexity_level": data.get("test_metadata", {}).get("complexity_level", 0),
                "error": str(e)
            }
    
    def _analyze_sustained_load_results(
        self,
        config: SustainedLoadConfiguration,
        start_time: datetime,
        end_time: datetime,
        actual_duration_hours: float
    ) -> SustainedLoadResult:
        """Analyze sustained load test results."""
        
        # Basic request metrics
        total_requests = len(self.request_metrics)
        successful_requests = sum(1 for r in self.request_metrics if r.get("success", False))
        failed_requests = total_requests - successful_requests
        
        # Response time analysis
        successful_times = [r["response_time_ms"] for r in self.request_metrics if r.get("success", False)]
        
        if successful_times:
            avg_response_time = statistics.mean(successful_times)
            sorted_times = sorted(successful_times)
            p95_response_time = sorted_times[int(len(sorted_times) * 0.95)] if sorted_times else 0
            p99_response_time = sorted_times[int(len(sorted_times) * 0.99)] if sorted_times else 0
        else:
            avg_response_time = p95_response_time = p99_response_time = 0
        
        # Memory analysis
        memory_analysis = self.memory_detector.analyze_memory_leaks()
        memory_leak_detected = memory_analysis.get("memory_leak_detected", False)
        memory_growth_mb = memory_analysis.get("memory_growth_mb", 0)
        memory_growth_rate = memory_analysis.get("memory_growth_rate_mb_per_hour", 0)
        peak_memory_mb = memory_analysis.get("peak_memory_mb", 0)
        
        # Performance degradation analysis
        degradation_analysis = self._analyze_performance_degradation(config)
        
        # Stability analysis
        stability_analysis = self._analyze_service_stability()
        
        # Resource accumulation analysis
        resource_analysis = self._analyze_resource_accumulation()
        
        # Determine test outcome
        critical_issues = []
        warnings = []
        recommendations = []
        
        # Check critical issues
        if memory_leak_detected:
            critical_issues.append(f"Memory leak detected: {memory_growth_mb:.1f}MB growth")
        
        if memory_growth_rate > config.memory_leak_threshold_mb_per_hour:
            critical_issues.append(f"Memory growth rate too high: {memory_growth_rate:.1f}MB/hour")
        
        if degradation_analysis["performance_degraded"]:
            critical_issues.append(f"Performance degraded by {degradation_analysis['performance_change_percent']:.1f}%")
        
        if stability_analysis["max_consecutive_failures"] > config.max_consecutive_failures:
            critical_issues.append(f"Too many consecutive failures: {stability_analysis['max_consecutive_failures']}")
        
        # Generate recommendations
        if memory_growth_mb > 100:
            recommendations.append("Investigate memory usage patterns and implement memory optimization")
        
        if avg_response_time > config.max_avg_response_time_ms:
            recommendations.append("Optimize response time - consider caching, database optimization, or code profiling")
        
        if stability_analysis["uptime_percentage"] < 99.5:
            recommendations.append("Improve service stability and error handling")
        
        if resource_analysis["file_handles_growth"] > 10:
            recommendations.append("Investigate file handle leaks")
        
        test_passed = len(critical_issues) == 0
        
        return SustainedLoadResult(
            test_config=config,
            start_time=start_time,
            end_time=end_time,
            actual_duration_hours=actual_duration_hours,
            total_requests=total_requests,
            successful_requests=successful_requests,
            failed_requests=failed_requests,
            average_response_time_ms=avg_response_time,
            p95_response_time_ms=p95_response_time,
            p99_response_time_ms=p99_response_time,
            memory_snapshots=self.memory_detector.snapshots,
            memory_leak_detected=memory_leak_detected,
            memory_growth_mb=memory_growth_mb,
            memory_growth_rate_mb_per_hour=memory_growth_rate,
            peak_memory_usage_mb=peak_memory_mb,
            performance_degraded=degradation_analysis["performance_degraded"],
            degradation_start_time=degradation_analysis["degradation_start_time"],
            initial_performance_ms=degradation_analysis["initial_performance_ms"],
            final_performance_ms=degradation_analysis["final_performance_ms"],
            performance_change_percent=degradation_analysis["performance_change_percent"],
            service_stability_score=stability_analysis["stability_score"],
            consecutive_failure_episodes=stability_analysis["consecutive_failure_episodes"],
            max_consecutive_failures=stability_analysis["max_consecutive_failures"],
            uptime_percentage=stability_analysis["uptime_percentage"],
            file_handles_growth=resource_analysis["file_handles_growth"],
            database_connections_growth=resource_analysis["database_connections_growth"],
            thread_count_growth=resource_analysis["thread_count_growth"],
            test_passed=test_passed,
            critical_issues=critical_issues,
            warnings=warnings,
            recommendations=recommendations
        )
    
    def _analyze_performance_degradation(self, config: SustainedLoadConfiguration) -> Dict[str, Any]:
        """Analyze performance degradation over time."""
        if len(self.request_metrics) < 100:  # Need sufficient data
            return {
                "performance_degraded": False,
                "degradation_start_time": None,
                "initial_performance_ms": 0,
                "final_performance_ms": 0,
                "performance_change_percent": 0
            }
        
        # Divide requests into time windows
        successful_requests = [r for r in self.request_metrics if r.get("success", False)]
        if not successful_requests:
            return {
                "performance_degraded": False,
                "degradation_start_time": None,
                "initial_performance_ms": 0,
                "final_performance_ms": 0,
                "performance_change_percent": 0
            }
        
        # Calculate initial and final performance (first/last 10% of requests)
        window_size = max(10, len(successful_requests) // 10)
        
        initial_requests = successful_requests[:window_size]
        final_requests = successful_requests[-window_size:]
        
        initial_avg = statistics.mean([r["response_time_ms"] for r in initial_requests])
        final_avg = statistics.mean([r["response_time_ms"] for r in final_requests])
        
        # Calculate performance change percentage
        performance_change = ((final_avg - initial_avg) / initial_avg * 100) if initial_avg > 0 else 0
        
        # Determine if performance degraded significantly
        performance_degraded = performance_change > config.performance_degradation_threshold_percent
        
        # Find degradation start time (simplified - when performance first exceeded threshold)
        degradation_start_time = None
        if performance_degraded:
            # Find first significant increase
            rolling_window = window_size
            for i in range(rolling_window, len(successful_requests) - rolling_window):
                current_window = successful_requests[i:i+rolling_window]
                current_avg = statistics.mean([r["response_time_ms"] for r in current_window])
                
                if current_avg > initial_avg * (1 + config.performance_degradation_threshold_percent / 100):
                    degradation_start_time = current_window[0]["timestamp"]
                    break
        
        return {
            "performance_degraded": performance_degraded,
            "degradation_start_time": degradation_start_time,
            "initial_performance_ms": initial_avg,
            "final_performance_ms": final_avg,
            "performance_change_percent": performance_change
        }
    
    def _analyze_service_stability(self) -> Dict[str, Any]:
        """Analyze service stability metrics."""
        if not self.request_metrics:
            return {
                "stability_score": 0,
                "consecutive_failure_episodes": 0,
                "max_consecutive_failures": 0,
                "uptime_percentage": 0
            }
        
        # Analyze consecutive failures
        consecutive_failures = 0
        max_consecutive_failures = 0
        failure_episodes = 0
        
        for request in self.request_metrics:
            if not request.get("success", False):
                consecutive_failures += 1
                max_consecutive_failures = max(max_consecutive_failures, consecutive_failures)
            else:
                if consecutive_failures > 0:
                    failure_episodes += 1
                consecutive_failures = 0
        
        # Calculate uptime percentage
        successful_requests = sum(1 for r in self.request_metrics if r.get("success", False))
        uptime_percentage = (successful_requests / len(self.request_metrics) * 100) if self.request_metrics else 0
        
        # Calculate stability score (0-100)
        stability_score = min(100, uptime_percentage * 0.7 + (100 - min(100, max_consecutive_failures * 10)) * 0.3)
        
        return {
            "stability_score": stability_score,
            "consecutive_failure_episodes": failure_episodes,
            "max_consecutive_failures": max_consecutive_failures,
            "uptime_percentage": uptime_percentage
        }
    
    def _analyze_resource_accumulation(self) -> Dict[str, Any]:
        """Analyze resource accumulation over test duration."""
        # For this implementation, we'll use simplified resource tracking
        # In a real scenario, you'd track file handles, database connections, etc.
        
        current_handles = len(self.process.open_files()) if hasattr(self.process, 'open_files') else 0
        current_threads = self.process.num_threads()
        
        # Simplified growth calculation (would need baseline measurements)
        return {
            "file_handles_growth": max(0, current_handles - 10),  # Assuming baseline of 10
            "database_connections_growth": 0,  # Would need actual DB connection tracking
            "thread_count_growth": max(0, current_threads - 5)   # Assuming baseline of 5
        }


@pytest.mark.performance
@pytest.mark.sustained
@pytest.mark.asyncio
class TestSustainedLoad:
    """Sustained load testing with memory leak detection."""
    
    async def test_4_hour_sustained_load(self, async_test_client: AsyncClient):
        """Test 4-hour sustained load for memory leak detection."""
        config = SustainedLoadConfiguration(
            test_name="4-Hour Sustained Load",
            test_description="Long-duration test for memory leak detection",
            test_duration_hours=4.0,
            warmup_duration_minutes=5,
            cooldown_duration_minutes=5,
            concurrent_users=15,
            target_requests_per_minute=30,
            memory_snapshot_interval_minutes=10,
            memory_leak_threshold_mb_per_hour=30.0,
            max_memory_growth_mb=300.0,
            max_avg_response_time_ms=200.0,
            max_p95_response_time_ms=400.0,
            max_error_rate_percent=3.0
        )
        
        executor = SustainedLoadExecutor(async_test_client)
        result = await executor.execute_sustained_load_test(config)
        
        # Memory leak assertions
        assert not result.memory_leak_detected, f"Memory leak detected: {result.memory_growth_mb:.1f}MB growth"
        assert result.memory_growth_rate_mb_per_hour <= config.memory_leak_threshold_mb_per_hour, \
            f"Memory growth rate too high: {result.memory_growth_rate_mb_per_hour:.1f}MB/hour"
        
        # Performance stability assertions
        assert not result.performance_degraded, f"Performance degraded by {result.performance_change_percent:.1f}%"
        assert result.average_response_time_ms <= config.max_avg_response_time_ms * 1.2, \
            f"Average response time too high: {result.average_response_time_ms:.1f}ms"
        
        # Service stability assertions
        assert result.service_stability_score >= 90.0, f"Service stability too low: {result.service_stability_score:.1f}/100"
        assert result.uptime_percentage >= 99.0, f"Uptime too low: {result.uptime_percentage:.1f}%"
        
        # Overall test outcome
        assert result.test_passed, f"Sustained load test failed: {result.critical_issues}"
        
        logger.info(f"4-hour test completed: {result.memory_growth_mb:.1f}MB growth, "
                   f"{result.average_response_time_ms:.1f}ms avg response time")
    
    @pytest.mark.slow
    async def test_24_hour_endurance_load(self, async_test_client: AsyncClient):
        """Test 24-hour endurance load for production validation."""
        config = SustainedLoadConfiguration(
            test_name="24-Hour Endurance Load",
            test_description="Full day endurance test for production readiness",
            test_duration_hours=24.0,
            warmup_duration_minutes=15,
            cooldown_duration_minutes=15,
            concurrent_users=10,
            target_requests_per_minute=20,
            memory_snapshot_interval_minutes=30,
            memory_leak_threshold_mb_per_hour=10.0,
            max_memory_growth_mb=500.0,
            max_avg_response_time_ms=150.0,
            max_p95_response_time_ms=300.0,
            max_error_rate_percent=2.0,
            performance_degradation_threshold_percent=15.0
        )
        
        executor = SustainedLoadExecutor(async_test_client)
        result = await executor.execute_sustained_load_test(config)
        
        # Long-term memory stability
        assert not result.memory_leak_detected, "Memory leak detected in 24-hour test"
        assert result.memory_growth_mb <= config.max_memory_growth_mb, \
            f"Excessive memory growth: {result.memory_growth_mb:.1f}MB"
        
        # Long-term performance stability
        assert abs(result.performance_change_percent) <= config.performance_degradation_threshold_percent, \
            f"Performance changed too much: {result.performance_change_percent:.1f}%"
        
        # Service reliability over 24 hours
        assert result.uptime_percentage >= 99.9, f"24-hour uptime too low: {result.uptime_percentage:.1f}%"
        assert result.max_consecutive_failures <= 5, f"Too many consecutive failures: {result.max_consecutive_failures}"
        
        # Resource management
        assert result.file_handles_growth <= 20, f"File handle growth too high: {result.file_handles_growth}"
        assert result.thread_count_growth <= 10, f"Thread count growth too high: {result.thread_count_growth}"
        
        logger.info(f"24-hour endurance completed: {result.service_stability_score:.1f}/100 stability score")
    
    async def test_memory_leak_detection_sensitivity(self, async_test_client: AsyncClient):
        """Test memory leak detection algorithm sensitivity."""
        config = SustainedLoadConfiguration(
            test_name="Memory Leak Detection Test",
            test_description="Test memory leak detection algorithm",
            test_duration_hours=1.0,
            warmup_duration_minutes=5,
            cooldown_duration_minutes=2,
            concurrent_users=20,
            target_requests_per_minute=60,
            memory_snapshot_interval_minutes=2,
            memory_leak_threshold_mb_per_hour=25.0,
            max_memory_growth_mb=100.0
        )
        
        executor = SustainedLoadExecutor(async_test_client)
        result = await executor.execute_sustained_load_test(config)
        
        # Memory monitoring should be active
        assert len(result.memory_snapshots) >= 5, f"Insufficient memory snapshots: {len(result.memory_snapshots)}"
        
        # Memory snapshots should show progression
        baseline_memory = result.memory_snapshots[0].rss_memory_mb
        final_memory = result.memory_snapshots[-1].rss_memory_mb
        
        assert final_memory > 0, "Final memory measurement should be positive"
        assert baseline_memory > 0, "Baseline memory measurement should be positive"
        
        # Memory growth should be tracked
        memory_growth = final_memory - baseline_memory
        assert abs(memory_growth - result.memory_growth_mb) < 10, "Memory growth calculation inconsistent"
        
        # Memory leak detection should work
        if result.memory_growth_mb > 50:  # If significant growth occurred
            assert result.memory_leak_detected, "Memory leak should be detected with significant growth"
        
        logger.info(f"Memory monitoring test: {len(result.memory_snapshots)} snapshots, "
                   f"{result.memory_growth_mb:.1f}MB growth detected")
    
    async def test_performance_degradation_detection(self, async_test_client: AsyncClient):
        """Test performance degradation detection over time."""
        config = SustainedLoadConfiguration(
            test_name="Performance Degradation Test",
            test_description="Test performance degradation detection",
            test_duration_hours=2.0,
            warmup_duration_minutes=10,
            cooldown_duration_minutes=5,
            concurrent_users=25,
            target_requests_per_minute=50,
            memory_snapshot_interval_minutes=5,
            performance_degradation_threshold_percent=20.0,
            max_avg_response_time_ms=300.0
        )
        
        executor = SustainedLoadExecutor(async_test_client)
        result = await executor.execute_sustained_load_test(config)
        
        # Performance tracking should be active
        assert result.total_requests >= 100, f"Insufficient requests for analysis: {result.total_requests}"
        
        # Performance metrics should be calculated
        assert result.initial_performance_ms > 0, "Initial performance should be measured"
        assert result.final_performance_ms > 0, "Final performance should be measured"
        
        # Performance change should be calculated
        expected_change = ((result.final_performance_ms - result.initial_performance_ms) / 
                          result.initial_performance_ms * 100) if result.initial_performance_ms > 0 else 0
        
        assert abs(result.performance_change_percent - expected_change) < 5, \
            "Performance change calculation should be accurate"
        
        # If degradation detected, should have start time
        if result.performance_degraded:
            assert result.degradation_start_time is not None, "Degradation start time should be recorded"
            logger.warning(f"Performance degraded by {result.performance_change_percent:.1f}% "
                          f"starting at {result.degradation_start_time}")
        
        logger.info(f"Performance analysis: {result.initial_performance_ms:.1f}ms → "
                   f"{result.final_performance_ms:.1f}ms ({result.performance_change_percent:+.1f}%)")
    
    async def test_resource_accumulation_tracking(self, async_test_client: AsyncClient):
        """Test resource accumulation tracking during sustained load."""
        config = SustainedLoadConfiguration(
            test_name="Resource Accumulation Test",
            test_description="Test resource accumulation tracking",
            test_duration_hours=0.5,
            warmup_duration_minutes=3,
            cooldown_duration_minutes=2,
            concurrent_users=30,
            target_requests_per_minute=90,
            memory_snapshot_interval_minutes=1
        )
        
        executor = SustainedLoadExecutor(async_test_client)
        result = await executor.execute_sustained_load_test(config)
        
        # Resource tracking should be measured
        assert hasattr(result, 'file_handles_growth'), "File handles growth should be tracked"
        assert hasattr(result, 'thread_count_growth'), "Thread count growth should be tracked"
        assert hasattr(result, 'database_connections_growth'), "Database connections growth should be tracked"
        
        # Resource growth should be reasonable for sustained load
        assert result.file_handles_growth <= 50, f"File handles grew too much: {result.file_handles_growth}"
        assert result.thread_count_growth <= 20, f"Thread count grew too much: {result.thread_count_growth}"
        
        # Service should clean up resources properly
        if result.file_handles_growth > 10:
            assert "file handle" in " ".join(result.recommendations).lower(), \
                "Should recommend investigating file handle leaks"
        
        logger.info(f"Resource tracking: {result.file_handles_growth} file handles, "
                   f"{result.thread_count_growth} threads growth")
    
    async def test_service_stability_under_sustained_load(self, async_test_client: AsyncClient):
        """Test service stability metrics during sustained load."""
        config = SustainedLoadConfiguration(
            test_name="Service Stability Test",
            test_description="Test service stability under sustained load",
            test_duration_hours=1.5,
            warmup_duration_minutes=5,
            cooldown_duration_minutes=3,
            concurrent_users=35,
            target_requests_per_minute=70,
            memory_snapshot_interval_minutes=3,
            max_consecutive_failures=8
        )
        
        executor = SustainedLoadExecutor(async_test_client)
        result = await executor.execute_sustained_load_test(config)
        
        # Stability metrics should be calculated
        assert result.service_stability_score >= 0, "Stability score should be non-negative"
        assert result.service_stability_score <= 100, "Stability score should not exceed 100"
        
        # Uptime should be high
        assert result.uptime_percentage >= 95.0, f"Uptime too low for sustained load: {result.uptime_percentage:.1f}%"
        
        # Consecutive failures should be managed
        assert result.max_consecutive_failures <= config.max_consecutive_failures, \
            f"Too many consecutive failures: {result.max_consecutive_failures}"
        
        # Failure episodes should be minimal
        assert result.consecutive_failure_episodes <= 10, \
            f"Too many failure episodes: {result.consecutive_failure_episodes}"
        
        # High stability should correlate with high uptime
        if result.uptime_percentage >= 99.0:
            assert result.service_stability_score >= 90.0, \
                "High uptime should result in high stability score"
        
        logger.info(f"Stability metrics: {result.service_stability_score:.1f}/100 score, "
                   f"{result.uptime_percentage:.1f}% uptime, {result.consecutive_failure_episodes} failure episodes")