"""
Comprehensive Performance Test Scenarios for Pattern Mining Service

This module provides realistic performance test scenarios including:
- Single user performance validation
- Concurrent user testing with various load patterns
- Real-world code complexity testing
- Database performance under load
- Cache effectiveness testing
- Memory efficiency validation
"""

import pytest
import asyncio
import time
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
import statistics
import json

from httpx import AsyncClient
from tests.utils.performance import (
    PerformanceTestRunner, LatencyMeasurer, MemoryProfiler, 
    ThroughputTester, performance_test, benchmark
)
from tests.utils.generators import CodeGenerator, PatternGenerator
from tests.performance.test_resource_monitoring import resource_monitoring_context

logger = logging.getLogger(__name__)


@dataclass
class ScenarioResult:
    """Performance test scenario result."""
    
    scenario_name: str
    test_type: str
    start_time: datetime
    end_time: datetime
    duration_seconds: float
    
    # Performance metrics
    total_requests: int
    successful_requests: int
    failed_requests: int
    average_response_time_ms: float
    p95_response_time_ms: float
    p99_response_time_ms: float
    throughput_rps: float
    
    # Resource metrics
    peak_memory_mb: float
    average_cpu_percent: float
    
    # Quality metrics
    error_rate_percent: float
    patterns_detected_total: int
    accuracy_score: float
    
    # Test result
    passed: bool
    performance_score: float
    issues: List[str]
    recommendations: List[str]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        result = asdict(self)
        result['start_time'] = self.start_time.isoformat()
        result['end_time'] = self.end_time.isoformat()
        return result


class PerformanceScenarioExecutor:
    """Execute realistic performance test scenarios."""
    
    def __init__(self, client: AsyncClient):
        self.client = client
        self.code_generator = CodeGenerator()
        self.pattern_generator = PatternGenerator()
        
    async def execute_scenario(self, scenario_config: Dict[str, Any]) -> ScenarioResult:
        """Execute a performance test scenario."""
        scenario_name = scenario_config["name"]
        test_type = scenario_config.get("type", "load_test")
        
        logger.info(f"Executing scenario: {scenario_name}")
        start_time = datetime.utcnow()
        
        # Execute scenario based on type
        if test_type == "single_user":
            result = await self._execute_single_user_scenario(scenario_config, start_time)
        elif test_type == "concurrent_users":
            result = await self._execute_concurrent_users_scenario(scenario_config, start_time)
        elif test_type == "complexity_scaling":
            result = await self._execute_complexity_scaling_scenario(scenario_config, start_time)
        elif test_type == "endurance":
            result = await self._execute_endurance_scenario(scenario_config, start_time)
        elif test_type == "burst_traffic":
            result = await self._execute_burst_traffic_scenario(scenario_config, start_time)
        else:
            raise ValueError(f"Unknown scenario type: {test_type}")
        
        logger.info(f"Scenario completed: {scenario_name}, Score: {result.performance_score:.1f}/100")
        return result
    
    async def _execute_single_user_scenario(
        self, 
        config: Dict[str, Any], 
        start_time: datetime
    ) -> ScenarioResult:
        """Execute single user performance scenario."""
        
        test_requests = config.get("requests", 50)
        complexity_levels = config.get("complexity_levels", ["simple", "medium", "complex"])
        
        results = []
        patterns_detected = 0
        
        async with resource_monitoring_context("single_user_scenario", 0.5) as monitor:
            for i in range(test_requests):
                # Vary complexity across requests
                complexity = complexity_levels[i % len(complexity_levels)]
                
                if complexity == "simple":
                    code_content = self.code_generator.generate_function()
                elif complexity == "medium":
                    code_content = self.code_generator.generate_file_content("module", pattern_count=5)
                else:  # complex
                    code_content = self.code_generator.generate_file_content("module", pattern_count=12)
                
                test_data = {
                    "repository_id": f"single-user-{i}",
                    "ast_data": {"type": "Module", "children": []},
                    "code_content": code_content,
                    "file_path": f"single_user_test_{i}.py",
                    "language": "python",
                    "detection_config": {
                        "confidence_threshold": 0.7,
                        "enable_ml_models": True,
                        "enable_heuristic_detection": True
                    }
                }
                
                request_start = time.perf_counter()
                try:
                    response = await self.client.post("/api/v1/patterns/detect", json=test_data)
                    request_end = time.perf_counter()
                    
                    response_time = (request_end - request_start) * 1000
                    success = response.status_code < 400
                    
                    if success and response.status_code == 200:
                        try:
                            response_data = response.json()
                            patterns_detected += len(response_data.get("patterns", []))
                        except:
                            pass
                    
                    results.append({
                        "success": success,
                        "response_time_ms": response_time,
                        "status_code": response.status_code,
                        "complexity": complexity
                    })
                    
                except Exception as e:
                    request_end = time.perf_counter()
                    response_time = (request_end - request_start) * 1000
                    
                    results.append({
                        "success": False,
                        "response_time_ms": response_time,
                        "status_code": 500,
                        "error": str(e),
                        "complexity": complexity
                    })
                
                # Small delay between requests
                await asyncio.sleep(0.05)
        
        # Get monitoring results
        monitoring_result = monitor.stop_monitoring()
        
        return self._analyze_scenario_results(
            config["name"], "single_user", start_time, results, 
            patterns_detected, monitoring_result
        )
    
    async def _execute_concurrent_users_scenario(
        self, 
        config: Dict[str, Any], 
        start_time: datetime
    ) -> ScenarioResult:
        """Execute concurrent users scenario."""
        
        concurrent_users = config.get("concurrent_users", 10)
        requests_per_user = config.get("requests_per_user", 5)
        test_duration = config.get("duration_seconds", 30)
        
        patterns_detected = 0
        
        async with resource_monitoring_context("concurrent_users_scenario", 0.3) as monitor:
            # Create semaphore to limit concurrency
            semaphore = asyncio.Semaphore(concurrent_users)
            
            async def user_session(user_id: int) -> List[Dict[str, Any]]:
                """Simulate a single user session."""
                user_results = []
                
                async with semaphore:
                    for req_id in range(requests_per_user):
                        # Generate realistic request
                        code_content = self.code_generator.generate_file_content(
                            "module", pattern_count=3 + (req_id % 5)
                        )
                        
                        test_data = {
                            "repository_id": f"concurrent-user-{user_id}",
                            "ast_data": {"type": "Module", "children": []},
                            "code_content": code_content,
                            "file_path": f"user_{user_id}_request_{req_id}.py",
                            "language": "python",
                            "detection_config": {
                                "confidence_threshold": 0.7,
                                "enable_ml_models": True
                            }
                        }
                        
                        request_start = time.perf_counter()
                        try:
                            response = await self.client.post("/api/v1/patterns/detect", json=test_data)
                            request_end = time.perf_counter()
                            
                            response_time = (request_end - request_start) * 1000
                            success = response.status_code < 400
                            
                            user_results.append({
                                "user_id": user_id,
                                "request_id": req_id,
                                "success": success,
                                "response_time_ms": response_time,
                                "status_code": response.status_code
                            })
                            
                        except Exception as e:
                            request_end = time.perf_counter()
                            response_time = (request_end - request_start) * 1000
                            
                            user_results.append({
                                "user_id": user_id,
                                "request_id": req_id,
                                "success": False,
                                "response_time_ms": response_time,
                                "status_code": 500,
                                "error": str(e)
                            })
                        
                        # Small delay between user requests
                        await asyncio.sleep(0.1)
                
                return user_results
            
            # Execute all user sessions concurrently
            user_tasks = [user_session(i) for i in range(concurrent_users)]
            user_results_lists = await asyncio.gather(*user_tasks, return_exceptions=True)
            
            # Flatten results
            results = []
            for user_results in user_results_lists:
                if isinstance(user_results, list):
                    results.extend(user_results)
                else:
                    logger.warning(f"User session failed: {user_results}")
        
        monitoring_result = monitor.stop_monitoring()
        
        return self._analyze_scenario_results(
            config["name"], "concurrent_users", start_time, results, 
            patterns_detected, monitoring_result
        )
    
    async def _execute_complexity_scaling_scenario(
        self, 
        config: Dict[str, Any], 
        start_time: datetime
    ) -> ScenarioResult:
        """Execute code complexity scaling scenario."""
        
        complexity_levels = [1, 3, 5, 8, 12, 20, 30]  # Pattern counts
        requests_per_level = config.get("requests_per_level", 5)
        
        results = []
        patterns_detected = 0
        
        async with resource_monitoring_context("complexity_scaling_scenario", 0.4) as monitor:
            for complexity in complexity_levels:
                logger.info(f"Testing complexity level: {complexity} patterns")
                
                for req_id in range(requests_per_level):
                    code_content = self.code_generator.generate_file_content(
                        "module", pattern_count=complexity
                    )
                    
                    test_data = {
                        "repository_id": f"complexity-{complexity}",
                        "ast_data": {"type": "Module", "children": []},
                        "code_content": code_content,
                        "file_path": f"complexity_{complexity}_req_{req_id}.py",
                        "language": "python",
                        "detection_config": {
                            "confidence_threshold": 0.7,
                            "enable_ml_models": True,
                            "enable_heuristic_detection": True
                        }
                    }
                    
                    request_start = time.perf_counter()
                    try:
                        response = await self.client.post("/api/v1/patterns/detect", json=test_data)
                        request_end = time.perf_counter()
                        
                        response_time = (request_end - request_start) * 1000
                        success = response.status_code < 400
                        
                        if success and response.status_code == 200:
                            try:
                                response_data = response.json()
                                patterns_found = len(response_data.get("patterns", []))
                                patterns_detected += patterns_found
                            except:
                                patterns_found = 0
                        else:
                            patterns_found = 0
                        
                        results.append({
                            "success": success,
                            "response_time_ms": response_time,
                            "status_code": response.status_code,
                            "complexity_level": complexity,
                            "patterns_found": patterns_found
                        })
                        
                    except Exception as e:
                        request_end = time.perf_counter()
                        response_time = (request_end - request_start) * 1000
                        
                        results.append({
                            "success": False,
                            "response_time_ms": response_time,
                            "status_code": 500,
                            "error": str(e),
                            "complexity_level": complexity,
                            "patterns_found": 0
                        })
                    
                    await asyncio.sleep(0.1)
        
        monitoring_result = monitor.stop_monitoring()
        
        return self._analyze_scenario_results(
            config["name"], "complexity_scaling", start_time, results, 
            patterns_detected, monitoring_result
        )
    
    async def _execute_endurance_scenario(
        self, 
        config: Dict[str, Any], 
        start_time: datetime
    ) -> ScenarioResult:
        """Execute endurance testing scenario."""
        
        duration_minutes = config.get("duration_minutes", 10)
        requests_per_minute = config.get("requests_per_minute", 20)
        
        total_duration = duration_minutes * 60
        request_interval = 60.0 / requests_per_minute
        
        results = []
        patterns_detected = 0
        
        async with resource_monitoring_context("endurance_scenario", 1.0) as monitor:
            end_time = time.perf_counter() + total_duration
            request_count = 0
            
            while time.perf_counter() < end_time:
                # Generate varied content for sustained testing
                complexity = 3 + (request_count % 8)  # 3-10 patterns
                code_content = self.code_generator.generate_file_content(
                    "module", pattern_count=complexity
                )
                
                test_data = {
                    "repository_id": f"endurance-test",
                    "ast_data": {"type": "Module", "children": []},
                    "code_content": code_content,
                    "file_path": f"endurance_req_{request_count}.py",
                    "language": "python",
                    "detection_config": {
                        "confidence_threshold": 0.7,
                        "enable_ml_models": True
                    }
                }
                
                request_start = time.perf_counter()
                try:
                    response = await self.client.post("/api/v1/patterns/detect", json=test_data)
                    request_end = time.perf_counter()
                    
                    response_time = (request_end - request_start) * 1000
                    success = response.status_code < 400
                    
                    results.append({
                        "success": success,
                        "response_time_ms": response_time,
                        "status_code": response.status_code,
                        "request_number": request_count,
                        "elapsed_minutes": (time.perf_counter() - (end_time - total_duration)) / 60
                    })
                    
                except Exception as e:
                    request_end = time.perf_counter()
                    response_time = (request_end - request_start) * 1000
                    
                    results.append({
                        "success": False,
                        "response_time_ms": response_time,
                        "status_code": 500,
                        "error": str(e),
                        "request_number": request_count
                    })
                
                request_count += 1
                
                # Wait for next request
                await asyncio.sleep(request_interval)
        
        monitoring_result = monitor.stop_monitoring()
        
        return self._analyze_scenario_results(
            config["name"], "endurance", start_time, results, 
            patterns_detected, monitoring_result
        )
    
    async def _execute_burst_traffic_scenario(
        self, 
        config: Dict[str, Any], 
        start_time: datetime
    ) -> ScenarioResult:
        """Execute burst traffic scenario."""
        
        burst_requests = config.get("burst_requests", 50)
        burst_duration = config.get("burst_duration_seconds", 10)
        
        results = []
        patterns_detected = 0
        
        async with resource_monitoring_context("burst_traffic_scenario", 0.2) as monitor:
            # Create all burst requests at once
            tasks = []
            
            for i in range(burst_requests):
                code_content = self.code_generator.generate_file_content(
                    "module", pattern_count=4 + (i % 6)
                )
                
                test_data = {
                    "repository_id": f"burst-test-{i}",
                    "ast_data": {"type": "Module", "children": []},
                    "code_content": code_content,
                    "file_path": f"burst_req_{i}.py",
                    "language": "python",
                    "detection_config": {
                        "confidence_threshold": 0.7,
                        "enable_ml_models": True
                    }
                }
                
                task = self._make_timed_request(test_data, i)
                tasks.append(task)
            
            # Execute all requests concurrently
            logger.info(f"Sending {burst_requests} requests in burst")
            burst_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Process results
            for result in burst_results:
                if isinstance(result, Exception):
                    results.append({
                        "success": False,
                        "response_time_ms": 0,
                        "status_code": 500,
                        "error": str(result)
                    })
                else:
                    results.append(result)
        
        monitoring_result = monitor.stop_monitoring()
        
        return self._analyze_scenario_results(
            config["name"], "burst_traffic", start_time, results, 
            patterns_detected, monitoring_result
        )
    
    async def _make_timed_request(self, test_data: Dict[str, Any], request_id: int) -> Dict[str, Any]:
        """Make a timed request for burst testing."""
        request_start = time.perf_counter()
        
        try:
            response = await self.client.post("/api/v1/patterns/detect", json=test_data)
            request_end = time.perf_counter()
            
            response_time = (request_end - request_start) * 1000
            success = response.status_code < 400
            
            return {
                "success": success,
                "response_time_ms": response_time,
                "status_code": response.status_code,
                "request_id": request_id
            }
            
        except Exception as e:
            request_end = time.perf_counter()
            response_time = (request_end - request_start) * 1000
            
            return {
                "success": False,
                "response_time_ms": response_time,
                "status_code": 500,
                "error": str(e),
                "request_id": request_id
            }
    
    def _analyze_scenario_results(
        self,
        scenario_name: str,
        test_type: str,
        start_time: datetime,
        results: List[Dict[str, Any]],
        patterns_detected: int,
        monitoring_result: Optional[Any]
    ) -> ScenarioResult:
        """Analyze scenario results and generate performance score."""
        
        end_time = datetime.utcnow()
        duration = (end_time - start_time).total_seconds()
        
        # Basic metrics
        total_requests = len(results)
        successful_requests = sum(1 for r in results if r.get("success", False))
        failed_requests = total_requests - successful_requests
        
        # Response time analysis
        successful_times = [r["response_time_ms"] for r in results if r.get("success", False)]
        
        if successful_times:
            avg_response_time = statistics.mean(successful_times)
            sorted_times = sorted(successful_times)
            p95_index = int(len(sorted_times) * 0.95)
            p99_index = int(len(sorted_times) * 0.99)
            p95_response_time = sorted_times[p95_index] if p95_index < len(sorted_times) else sorted_times[-1]
            p99_response_time = sorted_times[p99_index] if p99_index < len(sorted_times) else sorted_times[-1]
        else:
            avg_response_time = 0
            p95_response_time = 0
            p99_response_time = 0
        
        # Throughput
        throughput_rps = successful_requests / duration if duration > 0 else 0
        
        # Error rate
        error_rate = (failed_requests / total_requests * 100) if total_requests > 0 else 0
        
        # Resource metrics from monitoring
        peak_memory = 0
        avg_cpu = 0
        if monitoring_result:
            peak_memory = monitoring_result.memory_peak_usage_mb
            avg_cpu = monitoring_result.cpu_avg_percent
        
        # Performance scoring
        performance_score = self._calculate_performance_score(
            avg_response_time, p95_response_time, throughput_rps, error_rate,
            peak_memory, test_type
        )
        
        # Quality assessment
        accuracy_score = min(100, (patterns_detected / max(1, successful_requests)) * 20)  # Rough accuracy estimate
        
        # Issue identification
        issues = []
        if avg_response_time > 200:
            issues.append(f"High average response time: {avg_response_time:.1f}ms")
        if error_rate > 5:
            issues.append(f"High error rate: {error_rate:.1f}%")
        if throughput_rps < 5:
            issues.append(f"Low throughput: {throughput_rps:.1f} RPS")
        if peak_memory > 1000:
            issues.append(f"High memory usage: {peak_memory:.1f}MB")
        
        # Recommendations
        recommendations = []
        if avg_response_time > 100:
            recommendations.append("Optimize response time through caching or algorithm improvements")
        if error_rate > 2:
            recommendations.append("Improve error handling and system stability")
        if throughput_rps < 10:
            recommendations.append("Consider horizontal scaling or performance optimization")
        if peak_memory > 512:
            recommendations.append("Optimize memory usage and implement memory pooling")
        
        # Test pass/fail
        passed = (
            avg_response_time < 500 and
            error_rate < 10 and
            throughput_rps > 1 and
            peak_memory < 2000  # 2GB limit
        )
        
        return ScenarioResult(
            scenario_name=scenario_name,
            test_type=test_type,
            start_time=start_time,
            end_time=end_time,
            duration_seconds=duration,
            total_requests=total_requests,
            successful_requests=successful_requests,
            failed_requests=failed_requests,
            average_response_time_ms=avg_response_time,
            p95_response_time_ms=p95_response_time,
            p99_response_time_ms=p99_response_time,
            throughput_rps=throughput_rps,
            peak_memory_mb=peak_memory,
            average_cpu_percent=avg_cpu,
            error_rate_percent=error_rate,
            patterns_detected_total=patterns_detected,
            accuracy_score=accuracy_score,
            passed=passed,
            performance_score=performance_score,
            issues=issues,
            recommendations=recommendations
        )
    
    def _calculate_performance_score(
        self,
        avg_response_time: float,
        p95_response_time: float,
        throughput_rps: float,
        error_rate: float,
        peak_memory_mb: float,
        test_type: str
    ) -> float:
        """Calculate overall performance score (0-100)."""
        
        # Response time scoring (30% weight)
        response_time_score = max(0, 100 - (avg_response_time / 5))  # 500ms = 0 points
        
        # P95 response time scoring (20% weight)
        p95_score = max(0, 100 - (p95_response_time / 10))  # 1000ms = 0 points
        
        # Throughput scoring (25% weight)
        throughput_score = min(100, throughput_rps * 10)  # 10 RPS = 100 points
        
        # Error rate scoring (15% weight)
        error_score = max(0, 100 - (error_rate * 10))  # 10% error = 0 points
        
        # Memory efficiency scoring (10% weight)
        memory_score = max(0, 100 - (peak_memory_mb / 20))  # 2000MB = 0 points
        
        # Weighted average
        total_score = (
            response_time_score * 0.30 +
            p95_score * 0.20 +
            throughput_score * 0.25 +
            error_score * 0.15 +
            memory_score * 0.10
        )
        
        return max(0, min(100, total_score))


@pytest.mark.performance
@pytest.mark.scenarios
class TestPerformanceScenarios:
    """Comprehensive performance scenario tests."""
    
    @pytest.mark.asyncio
    async def test_single_user_performance_scenario(self, async_test_client: AsyncClient):
        """Test single user performance across different complexity levels."""
        
        scenario_config = {
            "name": "Single User Performance",
            "type": "single_user",
            "requests": 30,
            "complexity_levels": ["simple", "medium", "complex"]
        }
        
        executor = PerformanceScenarioExecutor(async_test_client)
        result = await executor.execute_scenario(scenario_config)
        
        # Assertions
        assert result.total_requests == 30
        assert result.successful_requests > 0
        assert result.average_response_time_ms > 0
        assert result.performance_score >= 0
        
        # Single user should achieve good performance
        assert result.average_response_time_ms < 1000, f"Single user response time too high: {result.average_response_time_ms}ms"
        assert result.error_rate_percent < 20, f"Single user error rate too high: {result.error_rate_percent}%"
        
        # Log results
        logger.info(f"Single user scenario results:")
        logger.info(f"  Average response time: {result.average_response_time_ms:.1f}ms")
        logger.info(f"  P95 response time: {result.p95_response_time_ms:.1f}ms")
        logger.info(f"  Throughput: {result.throughput_rps:.1f} RPS")
        logger.info(f"  Error rate: {result.error_rate_percent:.1f}%")
        logger.info(f"  Performance score: {result.performance_score:.1f}/100")
    
    @pytest.mark.asyncio
    async def test_concurrent_users_scenario(self, async_test_client: AsyncClient):
        """Test concurrent users performance scenario."""
        
        scenario_config = {
            "name": "Concurrent Users Performance",
            "type": "concurrent_users",
            "concurrent_users": 8,
            "requests_per_user": 4
        }
        
        executor = PerformanceScenarioExecutor(async_test_client)
        result = await executor.execute_scenario(scenario_config)
        
        # Assertions
        expected_requests = 8 * 4  # 32 requests
        assert result.total_requests > 0
        assert result.successful_requests > 0
        
        # Concurrent users should still maintain reasonable performance
        assert result.average_response_time_ms < 2000, f"Concurrent response time too high: {result.average_response_time_ms}ms"
        assert result.error_rate_percent < 30, f"Concurrent error rate too high: {result.error_rate_percent}%"
        assert result.throughput_rps > 1, f"Concurrent throughput too low: {result.throughput_rps} RPS"
        
        # Log results
        logger.info(f"Concurrent users scenario results:")
        logger.info(f"  Total requests: {result.total_requests}")
        logger.info(f"  Success rate: {(result.successful_requests/result.total_requests)*100:.1f}%")
        logger.info(f"  Average response time: {result.average_response_time_ms:.1f}ms")
        logger.info(f"  Throughput: {result.throughput_rps:.1f} RPS")
        logger.info(f"  Performance score: {result.performance_score:.1f}/100")
    
    @pytest.mark.asyncio
    async def test_complexity_scaling_scenario(self, async_test_client: AsyncClient):
        """Test performance scaling with code complexity."""
        
        scenario_config = {
            "name": "Complexity Scaling Performance",
            "type": "complexity_scaling",
            "requests_per_level": 3
        }
        
        executor = PerformanceScenarioExecutor(async_test_client)
        result = await executor.execute_scenario(scenario_config)
        
        # Assertions
        assert result.total_requests > 0
        assert result.successful_requests > 0
        
        # Performance should degrade gracefully with complexity
        assert result.average_response_time_ms < 5000, f"Complexity scaling response time too high: {result.average_response_time_ms}ms"
        assert result.error_rate_percent < 40, f"Complexity scaling error rate too high: {result.error_rate_percent}%"
        
        # Should detect patterns
        if result.patterns_detected_total > 0:
            logger.info(f"Patterns detected: {result.patterns_detected_total}")
            assert result.accuracy_score > 0
        
        # Log results
        logger.info(f"Complexity scaling scenario results:")
        logger.info(f"  Requests processed: {result.total_requests}")
        logger.info(f"  Patterns detected: {result.patterns_detected_total}")
        logger.info(f"  Average response time: {result.average_response_time_ms:.1f}ms")
        logger.info(f"  Performance score: {result.performance_score:.1f}/100")
    
    @pytest.mark.slow
    @pytest.mark.asyncio
    async def test_endurance_scenario(self, async_test_client: AsyncClient):
        """Test endurance performance scenario (slow test)."""
        
        scenario_config = {
            "name": "Endurance Performance Test",
            "type": "endurance",
            "duration_minutes": 3,  # 3 minutes for testing
            "requests_per_minute": 10
        }
        
        executor = PerformanceScenarioExecutor(async_test_client)
        result = await executor.execute_scenario(scenario_config)
        
        # Assertions
        assert result.duration_seconds >= 150  # At least 2.5 minutes
        assert result.total_requests > 20  # Should have made multiple requests
        
        # Endurance test should maintain stability
        assert result.error_rate_percent < 50, f"Endurance error rate too high: {result.error_rate_percent}%"
        
        # Memory usage should be reasonable for long-running test
        assert result.peak_memory_mb < 2000, f"Endurance memory usage too high: {result.peak_memory_mb}MB"
        
        # Log results
        logger.info(f"Endurance scenario results:")
        logger.info(f"  Duration: {result.duration_seconds:.1f} seconds")
        logger.info(f"  Total requests: {result.total_requests}")
        logger.info(f"  Average response time: {result.average_response_time_ms:.1f}ms")
        logger.info(f"  Peak memory: {result.peak_memory_mb:.1f}MB")
        logger.info(f"  Performance score: {result.performance_score:.1f}/100")
    
    @pytest.mark.asyncio
    async def test_burst_traffic_scenario(self, async_test_client: AsyncClient):
        """Test burst traffic handling scenario."""
        
        scenario_config = {
            "name": "Burst Traffic Performance",
            "type": "burst_traffic",
            "burst_requests": 25,
            "burst_duration_seconds": 5
        }
        
        executor = PerformanceScenarioExecutor(async_test_client)
        result = await executor.execute_scenario(scenario_config)
        
        # Assertions
        assert result.total_requests == 25
        
        # Burst traffic handling
        success_rate = (result.successful_requests / result.total_requests) * 100
        assert success_rate >= 50, f"Burst traffic success rate too low: {success_rate:.1f}%"
        
        # Should handle burst without complete failure
        assert result.successful_requests > 5, f"Too few successful requests in burst: {result.successful_requests}"
        
        # Log results
        logger.info(f"Burst traffic scenario results:")
        logger.info(f"  Requests sent: {result.total_requests}")
        logger.info(f"  Success rate: {success_rate:.1f}%")
        logger.info(f"  Average response time: {result.average_response_time_ms:.1f}ms")
        logger.info(f"  P95 response time: {result.p95_response_time_ms:.1f}ms")
        logger.info(f"  Performance score: {result.performance_score:.1f}/100")
    
    @pytest.mark.asyncio
    async def test_scenario_result_serialization(self, async_test_client: AsyncClient):
        """Test scenario result serialization and persistence."""
        
        scenario_config = {
            "name": "Serialization Test Scenario",
            "type": "single_user",
            "requests": 5,
            "complexity_levels": ["simple"]
        }
        
        executor = PerformanceScenarioExecutor(async_test_client)
        result = await executor.execute_scenario(scenario_config)
        
        # Test serialization
        result_dict = result.to_dict()
        assert isinstance(result_dict, dict)
        assert "scenario_name" in result_dict
        assert "performance_score" in result_dict
        assert "start_time" in result_dict
        assert "issues" in result_dict
        assert "recommendations" in result_dict
        
        # Test JSON serialization
        json_str = json.dumps(result_dict, indent=2)
        assert len(json_str) > 100
        
        # Test deserialization
        parsed_result = json.loads(json_str)
        assert parsed_result["scenario_name"] == scenario_config["name"]
        assert parsed_result["performance_score"] == result.performance_score
        
        # Validate timestamp format
        datetime.fromisoformat(parsed_result["start_time"])
        datetime.fromisoformat(parsed_result["end_time"])
        
        logger.info(f"Serialization test completed successfully")
    
    @pytest.mark.asyncio
    async def test_comprehensive_scenario_suite(self, async_test_client: AsyncClient):
        """Run a comprehensive suite of performance scenarios."""
        
        scenarios = [
            {
                "name": "Quick Single User",
                "type": "single_user",
                "requests": 10,
                "complexity_levels": ["simple", "medium"]
            },
            {
                "name": "Small Concurrent Load",
                "type": "concurrent_users",
                "concurrent_users": 3,
                "requests_per_user": 2
            },
            {
                "name": "Mini Burst Test",
                "type": "burst_traffic",
                "burst_requests": 10,
                "burst_duration_seconds": 3
            }
        ]
        
        executor = PerformanceScenarioExecutor(async_test_client)
        suite_results = []
        
        for scenario_config in scenarios:
            logger.info(f"Running scenario: {scenario_config['name']}")
            result = await executor.execute_scenario(scenario_config)
            suite_results.append(result)
        
        # Analyze suite results
        total_scenarios = len(suite_results)
        passed_scenarios = sum(1 for r in suite_results if r.passed)
        avg_performance_score = statistics.mean([r.performance_score for r in suite_results])
        
        # Suite should have reasonable success rate
        success_rate = (passed_scenarios / total_scenarios) * 100
        assert success_rate >= 60, f"Scenario suite success rate too low: {success_rate:.1f}%"
        
        # Log suite summary
        logger.info(f"Comprehensive scenario suite results:")
        logger.info(f"  Scenarios run: {total_scenarios}")
        logger.info(f"  Scenarios passed: {passed_scenarios}")
        logger.info(f"  Success rate: {success_rate:.1f}%")
        logger.info(f"  Average performance score: {avg_performance_score:.1f}/100")
        
        # Log individual results
        for result in suite_results:
            status = "PASS" if result.passed else "FAIL"
            logger.info(f"  {result.scenario_name}: {status} - Score: {result.performance_score:.1f}/100")
            
            if result.issues:
                for issue in result.issues:
                    logger.warning(f"    Issue: {issue}")
            
            if result.recommendations:
                for rec in result.recommendations:
                    logger.info(f"    Recommendation: {rec}")