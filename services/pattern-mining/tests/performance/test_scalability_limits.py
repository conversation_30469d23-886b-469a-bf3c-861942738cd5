"""
Scalability Limits Testing for Pattern Mining Service

This module provides comprehensive scalability testing including:
- Maximum capacity determination
- Breaking point identification under controlled conditions
- Resource limit testing (CPU, memory, I/O)
- Performance cliff detection
- Scalability curve mapping
- Horizontal and vertical scaling analysis
"""

import pytest
import asyncio
import time
import logging
import psutil
import gc
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
import statistics
import json
import threading
from concurrent.futures import ThreadPoolExecutor
from contextlib import asynccontextmanager

from httpx import AsyncClient
from tests.utils.performance import (
    PerformanceTestRunner, LatencyMeasurer, MemoryProfiler, 
    ThroughputTester, performance_test
)
from tests.utils.generators import CodeGenerator, PatternGenerator

logger = logging.getLogger(__name__)


@dataclass
class ScalabilityTestPoint:
    """Single data point in scalability testing."""
    
    # Load parameters
    concurrent_users: int
    target_rps: float
    test_duration_seconds: int
    
    # Performance results
    actual_rps: float
    avg_response_time_ms: float
    p95_response_time_ms: float
    p99_response_time_ms: float
    
    # Success metrics
    total_requests: int
    successful_requests: int
    failed_requests: int
    timeout_requests: int
    error_rate_percent: float
    
    # Resource utilization
    peak_memory_mb: float
    avg_cpu_percent: float
    peak_cpu_percent: float
    active_connections: int
    
    # System health indicators
    system_stable: bool
    performance_acceptable: bool
    resource_exhaustion_detected: bool
    
    # Scalability metrics
    efficiency_score: float  # How well resources are utilized
    scalability_coefficient: float  # Performance per unit of resource
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return asdict(self)


@dataclass
class ScalabilityAnalysis:
    """Analysis of scalability test results."""
    
    # Capacity findings
    maximum_sustainable_users: int
    maximum_throughput_rps: float
    breaking_point_users: Optional[int]
    performance_cliff_detected: bool
    cliff_threshold_users: Optional[int]
    
    # Resource limits
    memory_limit_reached: bool
    cpu_limit_reached: bool
    connection_limit_reached: bool
    io_limit_reached: bool
    
    # Scalability characteristics
    scalability_curve_type: str  # "linear", "logarithmic", "exponential_decay", "cliff"
    optimal_load_range: Tuple[int, int]  # (min_users, max_users) for optimal performance
    efficiency_peak_users: int
    
    # Performance predictions
    predicted_max_users: int
    confidence_interval: Tuple[int, int]
    recommended_production_limit: int
    
    # Bottleneck analysis
    primary_bottleneck: str  # "cpu", "memory", "io", "network", "application"
    bottleneck_threshold: float
    scaling_recommendations: List[str]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        result = asdict(self)
        result['optimal_load_range'] = list(self.optimal_load_range)
        result['confidence_interval'] = list(self.confidence_interval)
        return result


@dataclass
class ScalabilityTestConfiguration:
    """Configuration for scalability testing."""
    
    test_name: str
    test_description: str
    
    # Test progression
    start_users: int = 1
    max_users: int = 1000
    user_increment_pattern: str = "exponential"  # "linear", "exponential", "custom"
    custom_user_steps: Optional[List[int]] = None
    
    # Test duration at each level
    test_duration_per_level_seconds: int = 60
    stabilization_time_seconds: int = 10
    
    # Breaking point detection
    failure_threshold_percent: float = 50.0
    performance_degradation_threshold_percent: float = 30.0
    resource_exhaustion_threshold_percent: float = 90.0
    
    # Safety limits
    max_memory_usage_mb: float = 2048.0
    max_cpu_usage_percent: float = 95.0
    max_test_duration_minutes: int = 120
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return asdict(self)


class ScalabilityTester:
    """Advanced scalability testing with resource monitoring."""
    
    def __init__(self, client: AsyncClient):
        self.client = client
        self.code_generator = CodeGenerator()
        self.memory_profiler = MemoryProfiler()
        self.process = psutil.Process()
        
        # Test state
        self.test_points: List[ScalabilityTestPoint] = []
        self.baseline_performance: Optional[ScalabilityTestPoint] = None
        self.breaking_point_detected = False
        
    async def execute_scalability_test(
        self, 
        config: ScalabilityTestConfiguration
    ) -> Tuple[List[ScalabilityTestPoint], ScalabilityAnalysis]:
        """Execute comprehensive scalability testing."""
        
        logger.info(f"Starting scalability test: {config.test_name}")
        logger.info(f"Testing from {config.start_users} to {config.max_users} users")
        
        # Initialize monitoring
        self.memory_profiler.start_profiling()
        self.test_points = []
        self.breaking_point_detected = False
        
        try:
            # Generate user progression
            user_steps = self._generate_user_progression(config)
            
            # Establish baseline
            baseline = await self._test_single_load_level(
                users=1,
                duration_seconds=config.test_duration_per_level_seconds,
                stabilization_time=config.stabilization_time_seconds,
                level_name="baseline"
            )
            self.baseline_performance = baseline
            self.test_points.append(baseline)
            
            logger.info(f"Baseline established: {baseline.actual_rps:.1f} RPS, {baseline.avg_response_time_ms:.1f}ms")
            
            # Test each load level
            for i, users in enumerate(user_steps):
                if self.breaking_point_detected:
                    logger.info("Breaking point detected, stopping scalability test")
                    break
                
                logger.info(f"Testing load level {i+1}/{len(user_steps)}: {users} users")
                
                # Test this load level
                test_point = await self._test_single_load_level(
                    users=users,
                    duration_seconds=config.test_duration_per_level_seconds,
                    stabilization_time=config.stabilization_time_seconds,
                    level_name=f"level_{i+1}"
                )
                
                self.test_points.append(test_point)
                
                # Check for breaking point
                if self._is_breaking_point(test_point, config):
                    self.breaking_point_detected = True
                    logger.warning(f"Breaking point detected at {users} users")
                
                # Safety checks
                if test_point.peak_memory_mb > config.max_memory_usage_mb:
                    logger.error(f"Memory usage exceeded safety limit: {test_point.peak_memory_mb:.1f}MB")
                    break
                
                if test_point.peak_cpu_percent > config.max_cpu_usage_percent:
                    logger.error(f"CPU usage exceeded safety limit: {test_point.peak_cpu_percent:.1f}%")
                    break
                
                # Log progress
                logger.info(f"Level {i+1} results: {test_point.actual_rps:.1f} RPS, "
                          f"{test_point.avg_response_time_ms:.1f}ms avg, "
                          f"{test_point.error_rate_percent:.1f}% errors")
                
                # Brief recovery period between levels
                await asyncio.sleep(5)
        
        finally:
            self.memory_profiler.stop_profiling()
        
        # Analyze results
        analysis = self._analyze_scalability_results(config)
        
        logger.info(f"Scalability test completed: Max sustainable users: {analysis.maximum_sustainable_users}")
        logger.info(f"Primary bottleneck: {analysis.primary_bottleneck}")
        
        return self.test_points, analysis
    
    def _generate_user_progression(self, config: ScalabilityTestConfiguration) -> List[int]:
        """Generate user progression based on configuration."""
        
        if config.custom_user_steps:
            return [u for u in config.custom_user_steps if config.start_users <= u <= config.max_users]
        
        steps = []
        current = config.start_users
        
        if config.user_increment_pattern == "linear":
            # Linear progression
            increment = max(1, (config.max_users - config.start_users) // 20)
            while current <= config.max_users:
                steps.append(current)
                current += increment
        
        elif config.user_increment_pattern == "exponential":
            # Exponential progression (slower at first, faster later)
            while current <= config.max_users:
                steps.append(current)
                current = min(config.max_users, int(current * 1.5))
        
        else:  # Default to smart progression
            # Smart progression: small increments at first, larger later
            while current <= config.max_users:
                steps.append(current)
                if current < 10:
                    current += 2
                elif current < 50:
                    current += 5
                elif current < 200:
                    current += 10
                else:
                    current += 25
        
        return steps
    
    async def _test_single_load_level(
        self,
        users: int,
        duration_seconds: int,
        stabilization_time: int,
        level_name: str
    ) -> ScalabilityTestPoint:
        """Test a single load level and collect comprehensive metrics."""
        
        logger.debug(f"Testing {level_name}: {users} users for {duration_seconds}s")
        
        # Prepare test data
        test_data = self._prepare_scalability_test_data(users)
        
        # Initialize metrics tracking
        request_results = []
        resource_measurements = []
        
        # Start resource monitoring
        monitoring_task = asyncio.create_task(
            self._monitor_resources_during_test(resource_measurements)
        )
        
        try:
            # Stabilization period
            if stabilization_time > 0:
                await asyncio.sleep(stabilization_time)
            
            # Execute load test
            start_time = time.perf_counter()
            end_time = start_time + duration_seconds
            
            # Create semaphore for concurrency control
            semaphore = asyncio.Semaphore(users)
            
            async def make_request_with_tracking(data: Dict[str, Any]) -> Dict[str, Any]:
                async with semaphore:
                    return await self._make_scalability_request(data)
            
            # Send requests for the duration
            data_index = 0
            while time.perf_counter() < end_time:
                # Create batch of concurrent requests
                batch_size = min(users, len(test_data))
                batch_tasks = []
                
                for _ in range(batch_size):
                    data = test_data[data_index % len(test_data)]
                    task = make_request_with_tracking(data)
                    batch_tasks.append(task)
                    data_index += 1
                
                # Execute batch
                batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
                
                # Process results
                for result in batch_results:
                    if isinstance(result, Exception):
                        request_results.append({
                            "success": False,
                            "error": str(result),
                            "response_time_ms": 0,
                            "status_code": 0,
                            "timestamp": datetime.utcnow(),
                            "is_timeout": "timeout" in str(result).lower()
                        })
                    else:
                        request_results.append(result)
                
                # Brief pause to prevent overwhelming
                await asyncio.sleep(0.01)
            
            actual_test_duration = time.perf_counter() - start_time
            
        finally:
            # Stop resource monitoring
            monitoring_task.cancel()
            try:
                await monitoring_task
            except asyncio.CancelledError:
                pass
        
        # Calculate metrics
        return self._calculate_test_point_metrics(
            users, actual_test_duration, request_results, resource_measurements
        )
    
    async def _monitor_resources_during_test(self, measurements: List[Dict[str, Any]]):
        """Monitor system resources during testing."""
        
        while True:
            try:
                # CPU and memory metrics
                cpu_percent = self.process.cpu_percent()
                memory_info = self.process.memory_info()
                memory_mb = memory_info.rss / 1024 / 1024
                
                # Connection count (approximate)
                try:
                    connections = len(self.process.connections())
                except (psutil.AccessDenied, psutil.NoSuchProcess):
                    connections = 0
                
                # System-wide metrics
                system_memory = psutil.virtual_memory()
                system_cpu = psutil.cpu_percent()
                
                measurements.append({
                    "timestamp": datetime.utcnow(),
                    "process_cpu_percent": cpu_percent,
                    "process_memory_mb": memory_mb,
                    "process_connections": connections,
                    "system_cpu_percent": system_cpu,
                    "system_memory_percent": system_memory.percent,
                    "system_memory_available_mb": system_memory.available / 1024 / 1024
                })
                
                await asyncio.sleep(1.0)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.warning(f"Resource monitoring error: {e}")
                await asyncio.sleep(1.0)
    
    def _prepare_scalability_test_data(self, user_count: int) -> List[Dict[str, Any]]:
        """Prepare test data for scalability testing."""
        
        test_data = []
        
        # Generate variety of complexities for realistic scalability testing
        for i in range(user_count):
            complexity_index = i % 5
            
            if complexity_index == 0:
                # Simple function
                code_content = self.code_generator.generate_function()
                patterns = 1
            elif complexity_index == 1:
                # Small module
                code_content = self.code_generator.generate_file_content("module", pattern_count=5)
                patterns = 5
            elif complexity_index == 2:
                # Medium module
                code_content = self.code_generator.generate_file_content("module", pattern_count=12)
                patterns = 12
            elif complexity_index == 3:
                # Large module
                code_content = self.code_generator.generate_file_content("module", pattern_count=20)
                patterns = 20
            else:
                # Very large module for stress
                code_content = self.code_generator.generate_file_content("module", pattern_count=35)
                patterns = 35
            
            test_data.append({
                "repository_id": f"scalability-test-repo-{i % 50}",  # Reuse some repos for caching
                "ast_data": {"type": "Module", "children": []},
                "code_content": code_content,
                "file_path": f"scalability_test_{complexity_index}_{i}.py",
                "language": "python",
                "detection_config": {
                    "confidence_threshold": 0.7,
                    "enable_ml_models": True,
                    "enable_heuristic_detection": True
                },
                "scalability_metadata": {
                    "complexity_index": complexity_index,
                    "expected_patterns": patterns,
                    "user_index": i
                }
            })
        
        return test_data
    
    async def _make_scalability_request(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Make a single scalability test request."""
        
        start_time = time.perf_counter()
        timestamp = datetime.utcnow()
        
        try:
            response = await asyncio.wait_for(
                self.client.post("/api/v1/patterns/detect", json=data),
                timeout=60.0
            )
            
            end_time = time.perf_counter()
            response_time_ms = (end_time - start_time) * 1000
            
            return {
                "success": response.status_code < 400,
                "status_code": response.status_code,
                "response_time_ms": response_time_ms,
                "timestamp": timestamp,
                "complexity_index": data.get("scalability_metadata", {}).get("complexity_index", 0),
                "is_timeout": False,
                "error": None if response.status_code < 400 else f"HTTP {response.status_code}"
            }
            
        except asyncio.TimeoutError:
            end_time = time.perf_counter()
            response_time_ms = (end_time - start_time) * 1000
            
            return {
                "success": False,
                "status_code": 408,
                "response_time_ms": response_time_ms,
                "timestamp": timestamp,
                "complexity_index": data.get("scalability_metadata", {}).get("complexity_index", 0),
                "is_timeout": True,
                "error": "Request timeout"
            }
            
        except Exception as e:
            end_time = time.perf_counter()
            response_time_ms = (end_time - start_time) * 1000
            
            return {
                "success": False,
                "status_code": 500,
                "response_time_ms": response_time_ms,
                "timestamp": timestamp,
                "complexity_index": data.get("scalability_metadata", {}).get("complexity_index", 0),
                "is_timeout": False,
                "error": str(e)
            }
    
    def _calculate_test_point_metrics(
        self,
        users: int,
        duration_seconds: float,
        request_results: List[Dict[str, Any]],
        resource_measurements: List[Dict[str, Any]]
    ) -> ScalabilityTestPoint:
        """Calculate comprehensive metrics for a test point."""
        
        # Basic request metrics
        total_requests = len(request_results)
        successful_requests = sum(1 for r in request_results if r.get("success", False))
        failed_requests = total_requests - successful_requests
        timeout_requests = sum(1 for r in request_results if r.get("is_timeout", False))
        
        error_rate_percent = (failed_requests / total_requests * 100) if total_requests > 0 else 0
        actual_rps = total_requests / duration_seconds if duration_seconds > 0 else 0
        
        # Response time metrics
        successful_times = [r["response_time_ms"] for r in request_results if r.get("success", False)]
        
        if successful_times:
            avg_response_time = statistics.mean(successful_times)
            sorted_times = sorted(successful_times)
            p95_response_time = sorted_times[int(len(sorted_times) * 0.95)] if sorted_times else 0
            p99_response_time = sorted_times[int(len(sorted_times) * 0.99)] if sorted_times else 0
        else:
            avg_response_time = p95_response_time = p99_response_time = 0
        
        # Resource metrics
        if resource_measurements:
            peak_memory_mb = max(m["process_memory_mb"] for m in resource_measurements)
            avg_cpu_percent = statistics.mean([m["process_cpu_percent"] for m in resource_measurements])
            peak_cpu_percent = max(m["process_cpu_percent"] for m in resource_measurements)
            max_connections = max(m["process_connections"] for m in resource_measurements)
        else:
            peak_memory_mb = avg_cpu_percent = peak_cpu_percent = max_connections = 0
        
        # System health indicators
        system_stable = error_rate_percent < 50.0 and timeout_requests < total_requests * 0.3
        performance_acceptable = avg_response_time < 2000 and p95_response_time < 5000
        resource_exhaustion_detected = peak_memory_mb > 1536 or peak_cpu_percent > 90
        
        # Efficiency calculations
        if users > 0 and peak_memory_mb > 0:
            efficiency_score = min(100, (actual_rps / users) * (1000 / peak_memory_mb) * 100)
        else:
            efficiency_score = 0
        
        # Scalability coefficient (performance per resource unit)
        if peak_memory_mb > 0 and avg_cpu_percent > 0:
            scalability_coefficient = actual_rps / (peak_memory_mb * avg_cpu_percent / 100)
        else:
            scalability_coefficient = 0
        
        return ScalabilityTestPoint(
            concurrent_users=users,
            target_rps=users * 1.0,  # Simplified target
            test_duration_seconds=int(duration_seconds),
            actual_rps=actual_rps,
            avg_response_time_ms=avg_response_time,
            p95_response_time_ms=p95_response_time,
            p99_response_time_ms=p99_response_time,
            total_requests=total_requests,
            successful_requests=successful_requests,
            failed_requests=failed_requests,
            timeout_requests=timeout_requests,
            error_rate_percent=error_rate_percent,
            peak_memory_mb=peak_memory_mb,
            avg_cpu_percent=avg_cpu_percent,
            peak_cpu_percent=peak_cpu_percent,
            active_connections=max_connections,
            system_stable=system_stable,
            performance_acceptable=performance_acceptable,
            resource_exhaustion_detected=resource_exhaustion_detected,
            efficiency_score=efficiency_score,
            scalability_coefficient=scalability_coefficient
        )
    
    def _is_breaking_point(self, test_point: ScalabilityTestPoint, config: ScalabilityTestConfiguration) -> bool:
        """Determine if this test point represents a breaking point."""
        
        # Error rate breaking point
        if test_point.error_rate_percent > config.failure_threshold_percent:
            return True
        
        # Performance degradation breaking point
        if self.baseline_performance:
            performance_degradation = ((test_point.avg_response_time_ms - self.baseline_performance.avg_response_time_ms) 
                                     / self.baseline_performance.avg_response_time_ms * 100)
            if performance_degradation > config.performance_degradation_threshold_percent:
                return True
        
        # Resource exhaustion breaking point
        if test_point.resource_exhaustion_detected:
            return True
        
        # System instability breaking point
        if not test_point.system_stable:
            return True
        
        return False
    
    def _analyze_scalability_results(self, config: ScalabilityTestConfiguration) -> ScalabilityAnalysis:
        """Analyze scalability test results comprehensively."""
        
        if len(self.test_points) < 2:
            # Insufficient data for analysis
            return ScalabilityAnalysis(
                maximum_sustainable_users=1,
                maximum_throughput_rps=0,
                breaking_point_users=None,
                performance_cliff_detected=False,
                cliff_threshold_users=None,
                memory_limit_reached=False,
                cpu_limit_reached=False,
                connection_limit_reached=False,
                io_limit_reached=False,
                scalability_curve_type="unknown",
                optimal_load_range=(1, 1),
                efficiency_peak_users=1,
                predicted_max_users=1,
                confidence_interval=(1, 1),
                recommended_production_limit=1,
                primary_bottleneck="unknown",
                bottleneck_threshold=0,
                scaling_recommendations=[]
            )
        
        # Find maximum sustainable capacity
        sustainable_points = [p for p in self.test_points 
                            if p.system_stable and p.performance_acceptable and p.error_rate_percent < 10.0]
        
        if sustainable_points:
            max_sustainable = max(sustainable_points, key=lambda p: p.actual_rps)
            maximum_sustainable_users = max_sustainable.concurrent_users
            maximum_throughput_rps = max_sustainable.actual_rps
        else:
            maximum_sustainable_users = 1
            maximum_throughput_rps = self.test_points[0].actual_rps if self.test_points else 0
        
        # Find breaking point
        breaking_point_users = None
        for point in self.test_points:
            if not point.system_stable or point.error_rate_percent > config.failure_threshold_percent:
                breaking_point_users = point.concurrent_users
                break
        
        # Detect performance cliff
        performance_cliff_detected, cliff_threshold_users = self._detect_performance_cliff()
        
        # Analyze resource limits
        memory_limit_reached = any(p.peak_memory_mb > config.max_memory_usage_mb * 0.9 for p in self.test_points)
        cpu_limit_reached = any(p.peak_cpu_percent > config.max_cpu_usage_percent * 0.9 for p in self.test_points)
        connection_limit_reached = any(p.active_connections > 1000 for p in self.test_points)  # Simplified
        io_limit_reached = False  # Would need I/O monitoring
        
        # Determine scalability curve type
        scalability_curve_type = self._determine_scalability_curve_type()
        
        # Find optimal load range
        optimal_load_range = self._find_optimal_load_range()
        
        # Find efficiency peak
        efficiency_peak_users = max(self.test_points, key=lambda p: p.efficiency_score).concurrent_users
        
        # Predict maximum capacity
        predicted_max_users, confidence_interval = self._predict_maximum_capacity()
        
        # Recommend production limit (with safety margin)
        recommended_production_limit = int(maximum_sustainable_users * 0.8)
        
        # Identify primary bottleneck
        primary_bottleneck, bottleneck_threshold = self._identify_primary_bottleneck()
        
        # Generate scaling recommendations
        scaling_recommendations = self._generate_scaling_recommendations(
            primary_bottleneck, memory_limit_reached, cpu_limit_reached
        )
        
        return ScalabilityAnalysis(
            maximum_sustainable_users=maximum_sustainable_users,
            maximum_throughput_rps=maximum_throughput_rps,
            breaking_point_users=breaking_point_users,
            performance_cliff_detected=performance_cliff_detected,
            cliff_threshold_users=cliff_threshold_users,
            memory_limit_reached=memory_limit_reached,
            cpu_limit_reached=cpu_limit_reached,
            connection_limit_reached=connection_limit_reached,
            io_limit_reached=io_limit_reached,
            scalability_curve_type=scalability_curve_type,
            optimal_load_range=optimal_load_range,
            efficiency_peak_users=efficiency_peak_users,
            predicted_max_users=predicted_max_users,
            confidence_interval=confidence_interval,
            recommended_production_limit=recommended_production_limit,
            primary_bottleneck=primary_bottleneck,
            bottleneck_threshold=bottleneck_threshold,
            scaling_recommendations=scaling_recommendations
        )
    
    def _detect_performance_cliff(self) -> Tuple[bool, Optional[int]]:
        """Detect sudden performance degradation (performance cliff)."""
        
        if len(self.test_points) < 3:
            return False, None
        
        # Look for sudden increase in response time or error rate
        for i in range(1, len(self.test_points)):
            prev_point = self.test_points[i-1]
            curr_point = self.test_points[i]
            
            # Response time cliff
            if (curr_point.avg_response_time_ms > prev_point.avg_response_time_ms * 2 and
                prev_point.avg_response_time_ms > 0):
                return True, curr_point.concurrent_users
            
            # Error rate cliff
            if (curr_point.error_rate_percent > prev_point.error_rate_percent + 25 and
                prev_point.error_rate_percent < 10):
                return True, curr_point.concurrent_users
            
            # Throughput cliff (throughput stops scaling or decreases)
            if (curr_point.actual_rps < prev_point.actual_rps * 0.8):
                return True, curr_point.concurrent_users
        
        return False, None
    
    def _determine_scalability_curve_type(self) -> str:
        """Determine the type of scalability curve from test data."""
        
        if len(self.test_points) < 3:
            return "insufficient_data"
        
        # Analyze throughput progression
        throughputs = [p.actual_rps for p in self.test_points]
        user_counts = [p.concurrent_users for p in self.test_points]
        
        # Calculate rate of change
        throughput_changes = []
        for i in range(1, len(throughputs)):
            if user_counts[i] > user_counts[i-1]:
                change_rate = (throughputs[i] - throughputs[i-1]) / (user_counts[i] - user_counts[i-1])
                throughput_changes.append(change_rate)
        
        if not throughput_changes:
            return "unknown"
        
        # Classify curve type
        avg_change = statistics.mean(throughput_changes)
        change_variance = statistics.variance(throughput_changes) if len(throughput_changes) > 1 else 0
        
        # Linear scaling: consistent rate of change
        if change_variance < avg_change * 0.5 and avg_change > 0:
            return "linear"
        
        # Check for cliff: large drop in later changes
        if len(throughput_changes) >= 3:
            early_changes = throughput_changes[:len(throughput_changes)//2]
            late_changes = throughput_changes[len(throughput_changes)//2:]
            
            if statistics.mean(early_changes) > 0 and statistics.mean(late_changes) < 0:
                return "cliff"
        
        # Logarithmic: decreasing rate of change but still positive
        if all(change >= 0 for change in throughput_changes):
            if throughput_changes[0] > throughput_changes[-1]:
                return "logarithmic"
        
        # Exponential decay: performance gets worse as load increases
        if any(change < 0 for change in throughput_changes[-2:]):
            return "exponential_decay"
        
        return "complex"
    
    def _find_optimal_load_range(self) -> Tuple[int, int]:
        """Find the optimal load range for best efficiency."""
        
        # Find points with good efficiency and acceptable performance
        optimal_points = [
            p for p in self.test_points
            if (p.efficiency_score >= 70 and 
                p.system_stable and 
                p.performance_acceptable and
                p.error_rate_percent < 5.0)
        ]
        
        if not optimal_points:
            # Fallback to stable points
            optimal_points = [p for p in self.test_points if p.system_stable and p.performance_acceptable]
        
        if not optimal_points:
            return (1, 1)
        
        min_users = min(p.concurrent_users for p in optimal_points)
        max_users = max(p.concurrent_users for p in optimal_points)
        
        return (min_users, max_users)
    
    def _predict_maximum_capacity(self) -> Tuple[int, Tuple[int, int]]:
        """Predict maximum capacity with confidence interval."""
        
        if not self.test_points:
            return 1, (1, 1)
        
        # Simple prediction based on last stable point
        stable_points = [p for p in self.test_points if p.system_stable and p.performance_acceptable]
        
        if stable_points:
            max_stable = max(stable_points, key=lambda p: p.concurrent_users)
            predicted_max = int(max_stable.concurrent_users * 1.2)  # 20% extrapolation
            
            # Confidence interval (±30% of prediction)
            lower_bound = int(predicted_max * 0.7)
            upper_bound = int(predicted_max * 1.3)
            
            return predicted_max, (lower_bound, upper_bound)
        
        # Fallback
        last_point = self.test_points[-1]
        return last_point.concurrent_users, (1, last_point.concurrent_users)
    
    def _identify_primary_bottleneck(self) -> Tuple[str, float]:
        """Identify the primary system bottleneck."""
        
        if not self.test_points:
            return "unknown", 0
        
        # Analyze resource usage patterns
        avg_memory_usage = statistics.mean([p.peak_memory_mb for p in self.test_points])
        avg_cpu_usage = statistics.mean([p.avg_cpu_percent for p in self.test_points])
        max_memory_usage = max(p.peak_memory_mb for p in self.test_points)
        max_cpu_usage = max(p.peak_cpu_percent for p in self.test_points)
        
        # Determine primary bottleneck
        if max_memory_usage > 1500:  # High memory usage
            return "memory", max_memory_usage
        elif max_cpu_usage > 80:  # High CPU usage
            return "cpu", max_cpu_usage
        elif any(p.timeout_requests > p.total_requests * 0.1 for p in self.test_points):
            return "io", 0  # I/O bottleneck indicated by timeouts
        elif any(p.active_connections > 500 for p in self.test_points):
            return "network", max(p.active_connections for p in self.test_points)
        else:
            # Application bottleneck (not resource-bound)
            avg_error_rate = statistics.mean([p.error_rate_percent for p in self.test_points])
            return "application", avg_error_rate
    
    def _generate_scaling_recommendations(
        self, 
        primary_bottleneck: str, 
        memory_limit_reached: bool, 
        cpu_limit_reached: bool
    ) -> List[str]:
        """Generate scaling recommendations based on analysis."""
        
        recommendations = []
        
        if primary_bottleneck == "memory":
            recommendations.append("Increase available memory or optimize memory usage")
            recommendations.append("Implement memory pooling and object reuse patterns")
            if memory_limit_reached:
                recommendations.append("Consider horizontal scaling to distribute memory load")
        
        elif primary_bottleneck == "cpu":
            recommendations.append("Optimize CPU-intensive operations")
            recommendations.append("Consider horizontal scaling with load balancing")
            if cpu_limit_reached:
                recommendations.append("Profile and optimize hot code paths")
        
        elif primary_bottleneck == "io":
            recommendations.append("Optimize I/O operations with connection pooling")
            recommendations.append("Implement caching to reduce I/O load")
            recommendations.append("Consider asynchronous I/O patterns")
        
        elif primary_bottleneck == "network":
            recommendations.append("Optimize network usage and implement connection pooling")
            recommendations.append("Consider request batching to reduce connection overhead")
        
        elif primary_bottleneck == "application":
            recommendations.append("Profile application code for performance bottlenecks")
            recommendations.append("Optimize algorithmic complexity and data structures")
            recommendations.append("Implement caching for frequently accessed data")
        
        # General recommendations
        if len(self.test_points) > 5:
            last_efficient_point = max(
                [p for p in self.test_points if p.efficiency_score > 50],
                key=lambda p: p.efficiency_score,
                default=self.test_points[0]
            )
            recommendations.append(f"Optimal load appears to be around {last_efficient_point.concurrent_users} concurrent users")
        
        return recommendations


@pytest.mark.performance
@pytest.mark.scalability
@pytest.mark.asyncio
class TestScalabilityLimits:
    """Comprehensive scalability limits testing."""
    
    async def test_maximum_capacity_determination(self, async_test_client: AsyncClient):
        """Test to determine maximum system capacity."""
        config = ScalabilityTestConfiguration(
            test_name="Maximum Capacity Determination",
            test_description="Find maximum sustainable capacity",
            start_users=1,
            max_users=200,
            user_increment_pattern="exponential",
            test_duration_per_level_seconds=45,
            stabilization_time_seconds=5,
            failure_threshold_percent=25.0,
            performance_degradation_threshold_percent=50.0
        )
        
        tester = ScalabilityTester(async_test_client)
        test_points, analysis = await tester.execute_scalability_test(config)
        
        # Should have tested multiple load levels
        assert len(test_points) >= 3, f"Insufficient test points: {len(test_points)}"
        
        # Should find meaningful capacity limits
        assert analysis.maximum_sustainable_users >= 1, "Should find at least minimal capacity"
        assert analysis.maximum_throughput_rps > 0, "Should achieve some throughput"
        
        # Should provide scaling recommendations
        assert len(analysis.scaling_recommendations) > 0, "Should provide scaling recommendations"
        
        # Should identify primary bottleneck
        assert analysis.primary_bottleneck in ["cpu", "memory", "io", "network", "application", "unknown"], \
            f"Invalid bottleneck type: {analysis.primary_bottleneck}"
        
        logger.info(f"Maximum capacity: {analysis.maximum_sustainable_users} users, "
                   f"{analysis.maximum_throughput_rps:.1f} RPS")
        logger.info(f"Primary bottleneck: {analysis.primary_bottleneck}")
        logger.info(f"Scalability curve: {analysis.scalability_curve_type}")
    
    async def test_performance_cliff_detection(self, async_test_client: AsyncClient):
        """Test detection of performance cliffs."""
        config = ScalabilityTestConfiguration(
            test_name="Performance Cliff Detection",
            test_description="Detect sudden performance degradation",
            start_users=5,
            max_users=150,
            user_increment_pattern="linear",
            test_duration_per_level_seconds=30,
            stabilization_time_seconds=3,
            failure_threshold_percent=40.0,
            performance_degradation_threshold_percent=100.0  # Allow more degradation to find cliffs
        )
        
        tester = ScalabilityTester(async_test_client)
        test_points, analysis = await tester.execute_scalability_test(config)
        
        # Should test enough points to detect patterns
        assert len(test_points) >= 4, f"Need more test points for cliff detection: {len(test_points)}"
        
        # If cliff detected, should have threshold
        if analysis.performance_cliff_detected:
            assert analysis.cliff_threshold_users is not None, "Cliff detected but no threshold recorded"
            logger.warning(f"Performance cliff detected at {analysis.cliff_threshold_users} users")
        
        # Response times should show some progression
        response_times = [p.avg_response_time_ms for p in test_points]
        assert max(response_times) > min(response_times), "Response times should vary with load"
        
        # Should classify scalability curve
        valid_curve_types = ["linear", "logarithmic", "exponential_decay", "cliff", "complex", "insufficient_data"]
        assert analysis.scalability_curve_type in valid_curve_types, \
            f"Invalid curve type: {analysis.scalability_curve_type}"
        
        logger.info(f"Scalability curve type: {analysis.scalability_curve_type}")
        if analysis.performance_cliff_detected:
            logger.info(f"Performance cliff at {analysis.cliff_threshold_users} users")
    
    async def test_resource_limit_identification(self, async_test_client: AsyncClient):
        """Test identification of resource limits."""
        config = ScalabilityTestConfiguration(
            test_name="Resource Limit Identification",
            test_description="Identify resource bottlenecks",
            start_users=10,
            max_users=100,
            user_increment_pattern="exponential",
            test_duration_per_level_seconds=60,
            stabilization_time_seconds=10,
            max_memory_usage_mb=1024.0,  # Lower limit to test memory constraints
            max_cpu_usage_percent=85.0
        )
        
        tester = ScalabilityTester(async_test_client)
        test_points, analysis = await tester.execute_scalability_test(config)
        
        # Should track resource usage
        memory_usages = [p.peak_memory_mb for p in test_points]
        cpu_usages = [p.peak_cpu_percent for p in test_points]
        
        assert max(memory_usages) > 0, "Should track memory usage"
        assert max(cpu_usages) >= 0, "Should track CPU usage"
        
        # Resource usage should generally increase with load
        if len(memory_usages) >= 3:
            early_memory = statistics.mean(memory_usages[:len(memory_usages)//2])
            late_memory = statistics.mean(memory_usages[len(memory_usages)//2:])
            # Memory usage should generally increase (allowing some variance)
            assert late_memory >= early_memory * 0.8, "Memory usage should generally increase with load"
        
        # Should identify bottleneck
        assert analysis.primary_bottleneck != "unknown", "Should identify primary bottleneck"
        
        # If resource limits reached, should be flagged
        if analysis.memory_limit_reached:
            logger.warning("Memory limit reached during testing")
        if analysis.cpu_limit_reached:
            logger.warning("CPU limit reached during testing")
        
        logger.info(f"Peak memory usage: {max(memory_usages):.1f}MB")
        logger.info(f"Peak CPU usage: {max(cpu_usages):.1f}%")
        logger.info(f"Primary bottleneck: {analysis.primary_bottleneck}")
    
    async def test_optimal_load_range_determination(self, async_test_client: AsyncClient):
        """Test determination of optimal load range."""
        config = ScalabilityTestConfiguration(
            test_name="Optimal Load Range",
            test_description="Find optimal operating range",
            start_users=1,
            max_users=120,
            user_increment_pattern="exponential",
            test_duration_per_level_seconds=40,
            stabilization_time_seconds=5
        )
        
        tester = ScalabilityTester(async_test_client)
        test_points, analysis = await tester.execute_scalability_test(config)
        
        # Should identify optimal range
        min_optimal, max_optimal = analysis.optimal_load_range
        assert min_optimal <= max_optimal, "Optimal range should be valid"
        assert min_optimal >= 1, "Optimal range minimum should be reasonable"
        
        # Efficiency peak should be within reasonable bounds
        assert 1 <= analysis.efficiency_peak_users <= config.max_users, "Efficiency peak should be reasonable"
        
        # Production recommendation should be conservative
        assert analysis.recommended_production_limit <= analysis.maximum_sustainable_users, \
            "Production limit should be conservative"
        
        # Confidence interval should be reasonable
        lower_ci, upper_ci = analysis.confidence_interval
        assert lower_ci <= analysis.predicted_max_users <= upper_ci, "Prediction should be within confidence interval"
        
        logger.info(f"Optimal load range: {min_optimal}-{max_optimal} users")
        logger.info(f"Efficiency peak: {analysis.efficiency_peak_users} users")
        logger.info(f"Recommended production limit: {analysis.recommended_production_limit} users")
        logger.info(f"Predicted maximum: {analysis.predicted_max_users} users (CI: {lower_ci}-{upper_ci})")
    
    async def test_scalability_coefficient_analysis(self, async_test_client: AsyncClient):
        """Test scalability coefficient calculation and analysis."""
        config = ScalabilityTestConfiguration(
            test_name="Scalability Coefficient Analysis",
            test_description="Analyze scalability coefficients",
            start_users=2,
            max_users=80,
            user_increment_pattern="linear",
            test_duration_per_level_seconds=35,
            stabilization_time_seconds=5
        )
        
        tester = ScalabilityTester(async_test_client)
        test_points, analysis = await tester.execute_scalability_test(config)
        
        # Should calculate scalability coefficients
        coefficients = [p.scalability_coefficient for p in test_points]
        efficiency_scores = [p.efficiency_score for p in test_points]
        
        # Coefficients should be reasonable
        valid_coefficients = [c for c in coefficients if c > 0]
        assert len(valid_coefficients) > 0, "Should have some valid scalability coefficients"
        
        # Efficiency scores should be calculated
        valid_efficiency = [e for e in efficiency_scores if e > 0]
        assert len(valid_efficiency) > 0, "Should have some valid efficiency scores"
        
        # Find peak efficiency point
        peak_efficiency_point = max(test_points, key=lambda p: p.efficiency_score)
        assert peak_efficiency_point.efficiency_score > 0, "Peak efficiency should be positive"
        
        # Efficiency should generally decline at very high loads
        if len(test_points) >= 5:
            early_efficiency = statistics.mean(efficiency_scores[:len(efficiency_scores)//2])
            late_efficiency = statistics.mean(efficiency_scores[len(efficiency_scores)//2:])
            
            # Efficiency might decline at high loads (not always, but often)
            efficiency_change = (late_efficiency - early_efficiency) / early_efficiency * 100
            logger.info(f"Efficiency change from early to late loads: {efficiency_change:+.1f}%")
        
        logger.info(f"Peak efficiency: {peak_efficiency_point.efficiency_score:.1f} at {peak_efficiency_point.concurrent_users} users")
        logger.info(f"Efficiency range: {min(valid_efficiency):.1f} - {max(valid_efficiency):.1f}")
    
    async def test_breaking_point_characterization(self, async_test_client: AsyncClient):
        """Test detailed characterization of breaking points."""
        config = ScalabilityTestConfiguration(
            test_name="Breaking Point Characterization",
            test_description="Characterize system breaking points",
            start_users=5,
            max_users=250,
rm            user_increment_pattern="exponential",
            test_duration_per_level_seconds=30,
            stabilization_time_seconds=5,
            failure_threshold_percent=30.0,  # Lower threshold to find breaking point
            performance_degradation_threshold_percent=75.0
        )
        
        tester = ScalabilityTester(async_test_client)
        test_points, analysis = await tester.execute_scalability_test(config)
        
        # Should reach some kind of limit
        error_rates = [p.error_rate_percent for p in test_points]
        max_error_rate = max(error_rates)
        
        # Should show degradation under load
        if len(test_points) >= 3:
            # Error rates should increase with load (generally)
            early_errors = statistics.mean(error_rates[:len(error_rates)//2])
            late_errors = statistics.mean(error_rates[len(error_rates)//2:])
            
            if late_errors > early_errors:
                logger.info(f"Error rate increased with load: {early_errors:.1f}% → {late_errors:.1f}%")
        
        # If breaking point found, should be meaningful
        if analysis.breaking_point_users:
            assert analysis.breaking_point_users > config.start_users, "Breaking point should be above start level"
            logger.warning(f"Breaking point identified at {analysis.breaking_point_users} users")
            
            # Find the breaking point test result
            breaking_point = next((p for p in test_points if p.concurrent_users == analysis.breaking_point_users), None)
            if breaking_point:
                logger.info(f"Breaking point characteristics:")
                logger.info(f"  Error rate: {breaking_point.error_rate_percent:.1f}%")
                logger.info(f"  Avg response time: {breaking_point.avg_response_time_ms:.1f}ms")
                logger.info(f"  System stable: {breaking_point.system_stable}")
                logger.info(f"  Resource exhaustion: {breaking_point.resource_exhaustion_detected}")
        
        # Should provide actionable recommendations
        assert len(analysis.scaling_recommendations) > 0, "Should provide scaling recommendations"
        
        logger.info(f"Maximum error rate observed: {max_error_rate:.1f}%")
        logger.info(f"Scaling recommendations: {len(analysis.scaling_recommendations)} items")
    
    @pytest.mark.slow
    async def test_extended_scalability_analysis(self, async_test_client: AsyncClient):
        """Test extended scalability analysis with fine-grained steps."""
        config = ScalabilityTestConfiguration(
            test_name="Extended Scalability Analysis",
            test_description="Fine-grained scalability analysis",
            start_users=1,
            max_users=300,
            user_increment_pattern="exponential",
            test_duration_per_level_seconds=90,  # Longer duration for stability
            stabilization_time_seconds=15,
            failure_threshold_percent=50.0,
            max_test_duration_minutes=180  # 3 hour limit
        )
        
        tester = ScalabilityTester(async_test_client)
        test_points, analysis = await tester.execute_scalability_test(config)
        
        # Extended test should have many data points
        assert len(test_points) >= 5, f"Extended test should have many points: {len(test_points)}"
        
        # Should reach substantial load levels
        max_users_tested = max(p.concurrent_users for p in test_points)
        assert max_users_tested >= 20, f"Should test substantial load: {max_users_tested} users"
        
        # Should have detailed scalability curve characterization
        assert analysis.scalability_curve_type != "insufficient_data", "Should characterize scalability curve"
        
        # Should provide comprehensive recommendations
        assert len(analysis.scaling_recommendations) >= 2, "Should provide multiple recommendations"
        
        # Performance metrics should show realistic patterns
        throughputs = [p.actual_rps for p in test_points]
        response_times = [p.avg_response_time_ms for p in test_points]
        
        # Throughput should generally increase initially
        if len(throughputs) >= 3:
            initial_throughput = throughputs[0]
            peak_throughput = max(throughputs)
            assert peak_throughput >= initial_throughput, "Peak throughput should exceed initial"
        
        # Response times may increase under high load
        if len(response_times) >= 3:
            response_time_range = max(response_times) - min(response_times)
            logger.info(f"Response time range: {response_time_range:.1f}ms")
        
        # Log comprehensive results
        logger.info(f"Extended scalability test results:")
        logger.info(f"  Test points: {len(test_points)}")
        logger.info(f"  Max users tested: {max_users_tested}")
        logger.info(f"  Max sustainable users: {analysis.maximum_sustainable_users}")
        logger.info(f"  Max throughput: {analysis.maximum_throughput_rps:.1f} RPS")
        logger.info(f"  Scalability curve: {analysis.scalability_curve_type}")
        logger.info(f"  Primary bottleneck: {analysis.primary_bottleneck}")
        logger.info(f"  Recommended production limit: {analysis.recommended_production_limit} users")
    
    async def test_custom_user_progression_scalability(self, async_test_client: AsyncClient):
        """Test scalability with custom user progression."""
        # Custom progression focusing on critical load points
        custom_steps = [1, 3, 5, 10, 15, 25, 40, 60, 90, 130, 180]
        
        config = ScalabilityTestConfiguration(
            test_name="Custom Progression Scalability",
            test_description="Scalability test with custom user progression",
            start_users=1,
            max_users=200,
            user_increment_pattern="custom",
            custom_user_steps=custom_steps,
            test_duration_per_level_seconds=45,
            stabilization_time_seconds=8
        )
        
        tester = ScalabilityTester(async_test_client)
        test_points, analysis = await tester.execute_scalability_test(config)
        
        # Should follow custom progression
        tested_users = [p.concurrent_users for p in test_points]
        expected_progression = [u for u in custom_steps if u >= config.start_users]
        
        # Should test most of the custom steps (allowing for early termination)
        assert len(tested_users) >= min(5, len(expected_progression)), \
            f"Should test multiple custom steps: {tested_users}"
        
        # First few steps should match custom progression
        for i in range(min(3, len(tested_users), len(expected_progression))):
            assert tested_users[i] == expected_progression[i], \
                f"Custom progression not followed: {tested_users} vs {expected_progression}"
        
        # Should provide meaningful analysis
        assert analysis.maximum_sustainable_users > 0, "Should find sustainable capacity"
        assert analysis.scalability_curve_type != "insufficient_data", "Should characterize curve"
        
        logger.info(f"Custom progression tested: {tested_users}")
        logger.info(f"Analysis with custom steps: {analysis.maximum_sustainable_users} max users, "
                   f"{analysis.scalability_curve_type} curve")