"""
Comprehensive Stress Testing Infrastructure for Pattern Mining Service

This module provides production-ready stress testing capabilities including:
- Breaking point identification (finding system limits)
- Resource exhaustion testing (memory, CPU, connections)
- Recovery testing (behavior after overload)
- Spike testing (sudden load increases)
- Resource leak detection under extreme conditions
"""

import pytest
import asyncio
import time
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timed<PERSON><PERSON>
from dataclasses import dataclass, asdict
import statistics
import json
import psutil
import gc
from contextlib import asynccontextmanager

from httpx import AsyncClient
from tests.utils.performance import (
    PerformanceTestRunner, LatencyMeasurer, MemoryProfiler, 
    ThroughputTester, performance_test
)
from tests.utils.generators import CodeGenerator, PatternGenerator

logger = logging.getLogger(__name__)


@dataclass
class StressTestConfiguration:
    """Stress test configuration parameters."""
    
    # Test identification
    test_name: str
    test_description: str
    test_type: str  # "breaking_point", "spike", "resource_exhaustion", "recovery"
    
    # Stress parameters
    max_concurrent_users: int
    user_increment: int = 10
    test_duration_per_level: int = 30
    failure_threshold_percent: float = 50.0  # % of requests that can fail
    
    # Spike test specific
    spike_multiplier: float = 5.0  # How much to multiply base load
    spike_duration_seconds: int = 10
    
    # Resource limits to test
    memory_stress_mb: Optional[int] = None
    cpu_stress_percent: Optional[float] = None
    connection_stress_count: Optional[int] = None
    
    # Request parameters
    endpoint: str = "/api/v1/patterns/detect"
    method: str = "POST"
    request_timeout: float = 30.0
    
    # Recovery testing
    recovery_time_seconds: int = 60
    expected_recovery_threshold: float = 90.0  # % of normal performance after recovery
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return asdict(self)


@dataclass
class StressTestResult:
    """Stress test execution results."""
    
    # Test metadata
    test_config: StressTestConfiguration
    start_time: datetime
    end_time: datetime
    test_duration_seconds: float
    
    # Breaking point results
    breaking_point_users: Optional[int]
    max_stable_users: int
    max_successful_rps: float
    performance_degradation_curve: List[Dict[str, Any]]
    
    # Failure analysis
    failure_modes: Dict[str, int]
    error_progression: List[Dict[str, Any]]
    
    # Resource exhaustion results
    peak_memory_usage_mb: float
    peak_cpu_usage_percent: float
    resource_exhaustion_detected: bool
    memory_leak_detected: bool
    
    # Recovery results
    recovery_successful: bool
    recovery_time_seconds: float
    post_recovery_performance_percent: float
    
    # System stability
    service_crashed: bool
    connection_pool_exhausted: bool
    database_connection_issues: bool
    
    # Test outcome
    test_passed: bool
    critical_issues: List[str]
    recommendations: List[str]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        result = asdict(self)
        result['test_config'] = self.test_config.to_dict()
        result['start_time'] = self.start_time.isoformat()
        result['end_time'] = self.end_time.isoformat()
        return result


class StressTestExecutor:
    """Execute comprehensive stress tests with system monitoring."""
    
    def __init__(self, client: AsyncClient):
        self.client = client
        self.process = psutil.Process()
        self.memory_profiler = MemoryProfiler()
        self.code_generator = CodeGenerator()
        self.baseline_performance = {}
        
    async def execute_stress_test(self, config: StressTestConfiguration) -> StressTestResult:
        """Execute a comprehensive stress test."""
        logger.info(f"Starting stress test: {config.test_name}")
        logger.info(f"Test type: {config.test_type}")
        
        start_time = datetime.utcnow()
        
        # Establish baseline performance
        await self._establish_baseline_performance()
        
        # Execute appropriate stress test
        if config.test_type == "breaking_point":
            result = await self._execute_breaking_point_test(config, start_time)
        elif config.test_type == "spike":
            result = await self._execute_spike_test(config, start_time)
        elif config.test_type == "resource_exhaustion":
            result = await self._execute_resource_exhaustion_test(config, start_time)
        elif config.test_type == "recovery":
            result = await self._execute_recovery_test(config, start_time)
        else:
            raise ValueError(f"Unknown stress test type: {config.test_type}")
        
        logger.info(f"Stress test completed: {result.test_passed}")
        logger.info(f"Critical issues: {len(result.critical_issues)}")
        
        return result
    
    async def _establish_baseline_performance(self):
        """Establish baseline performance metrics."""
        logger.info("Establishing baseline performance...")
        
        # Test with single user
        baseline_data = self._prepare_test_data(1)[0]
        
        # Measure baseline response time and success rate
        baseline_times = []
        baseline_successes = 0
        
        for _ in range(10):
            start_time = time.perf_counter()
            try:
                response = await self.client.post("/api/v1/patterns/detect", json=baseline_data)
                end_time = time.perf_counter()
                
                response_time = (end_time - start_time) * 1000
                baseline_times.append(response_time)
                
                if response.status_code < 400:
                    baseline_successes += 1
                    
            except Exception as e:
                logger.warning(f"Baseline request failed: {e}")
        
        self.baseline_performance = {
            "avg_response_time_ms": statistics.mean(baseline_times) if baseline_times else 0,
            "success_rate": baseline_successes / 10 * 100,
            "p95_response_time_ms": statistics.quantiles(baseline_times, n=20)[18] if len(baseline_times) >= 20 else 0
        }
        
        logger.info(f"Baseline established: {self.baseline_performance}")
    
    async def _execute_breaking_point_test(
        self, 
        config: StressTestConfiguration, 
        start_time: datetime
    ) -> StressTestResult:
        """Execute breaking point stress test."""
        logger.info("Executing breaking point test...")
        
        performance_curve = []
        breaking_point_users = None
        max_stable_users = 0
        max_successful_rps = 0
        error_progression = []
        failure_modes = {}
        
        # Start monitoring
        self.memory_profiler.start_profiling()
        memory_measurements = []
        cpu_measurements = []
        
        # Test increasing user loads
        current_users = config.user_increment
        
        while current_users <= config.max_concurrent_users:
            logger.info(f"Testing with {current_users} concurrent users...")
            
            # Prepare test data for current level
            test_data = self._prepare_test_data(current_users)
            
            # Execute load test at current level
            level_results = await self._execute_load_level(
                current_users, 
                test_data, 
                config.test_duration_per_level,
                config.request_timeout
            )
            
            # Record resource usage
            memory_usage = self.process.memory_info().rss / 1024 / 1024
            cpu_usage = self.process.cpu_percent()
            memory_measurements.append(memory_usage)
            cpu_measurements.append(cpu_usage)
            
            # Analyze level results
            successful_requests = sum(1 for r in level_results if r["success"])
            total_requests = len(level_results)
            success_rate = (successful_requests / total_requests * 100) if total_requests > 0 else 0
            
            if level_results:
                successful_times = [r["response_time_ms"] for r in level_results if r["success"]]
                avg_response_time = statistics.mean(successful_times) if successful_times else 0
                rps = successful_requests / config.test_duration_per_level
            else:
                avg_response_time = 0
                rps = 0
            
            # Record performance curve point
            performance_point = {
                "concurrent_users": current_users,
                "success_rate_percent": success_rate,
                "avg_response_time_ms": avg_response_time,
                "requests_per_second": rps,
                "memory_usage_mb": memory_usage,
                "cpu_usage_percent": cpu_usage,
                "timestamp": datetime.utcnow().isoformat()
            }
            performance_curve.append(performance_point)
            
            # Track error progression
            failed_requests = [r for r in level_results if not r["success"]]
            level_errors = {}
            for req in failed_requests:
                error_type = req.get("error", "Unknown")
                level_errors[error_type] = level_errors.get(error_type, 0) + 1
                failure_modes[error_type] = failure_modes.get(error_type, 0) + 1
            
            error_progression.append({
                "concurrent_users": current_users,
                "total_errors": len(failed_requests),
                "error_breakdown": level_errors,
                "timestamp": datetime.utcnow().isoformat()
            })
            
            # Check if this is still acceptable performance
            if success_rate >= (100 - config.failure_threshold_percent):
                max_stable_users = current_users
                max_successful_rps = max(max_successful_rps, rps)
            else:
                # Found breaking point
                if breaking_point_users is None:
                    breaking_point_users = current_users
                    logger.info(f"Breaking point detected at {current_users} users")
            
            # Safety check - if system is completely failing, stop
            if success_rate < 10 and current_users > config.user_increment:
                logger.warning(f"System severely degraded at {current_users} users, stopping test")
                break
            
            # Increment user count
            current_users += config.user_increment
            
            # Brief recovery period between levels
            await asyncio.sleep(5)
        
        # Stop monitoring
        self.memory_profiler.stop_profiling()
        
        # Check for service crash
        service_crashed = await self._check_service_health()
        
        # Analyze results
        end_time = datetime.utcnow()
        test_duration = (end_time - start_time).total_seconds()
        
        # Resource analysis
        peak_memory = max(memory_measurements) if memory_measurements else 0
        peak_cpu = max(cpu_measurements) if cpu_measurements else 0
        memory_analysis = self.memory_profiler.get_memory_analysis()
        
        # Generate recommendations
        recommendations = self._generate_stress_recommendations(
            breaking_point_users, max_stable_users, failure_modes, peak_memory, peak_cpu
        )
        
        # Identify critical issues
        critical_issues = []
        if service_crashed:
            critical_issues.append("Service crashed during stress test")
        if breaking_point_users and breaking_point_users < 50:
            critical_issues.append(f"Low breaking point: only {breaking_point_users} concurrent users")
        if peak_memory > 1024:  # 1GB
            critical_issues.append(f"Excessive memory usage: {peak_memory:.1f}MB")
        if "timeout" in failure_modes and failure_modes["timeout"] > 100:
            critical_issues.append(f"High timeout rate: {failure_modes['timeout']} requests")
        
        test_passed = len(critical_issues) == 0 and not service_crashed
        
        return StressTestResult(
            test_config=config,
            start_time=start_time,
            end_time=end_time,
            test_duration_seconds=test_duration,
            breaking_point_users=breaking_point_users,
            max_stable_users=max_stable_users,
            max_successful_rps=max_successful_rps,
            performance_degradation_curve=performance_curve,
            failure_modes=failure_modes,
            error_progression=error_progression,
            peak_memory_usage_mb=peak_memory,
            peak_cpu_usage_percent=peak_cpu,
            resource_exhaustion_detected=peak_memory > 1024 or peak_cpu > 95,
            memory_leak_detected=memory_analysis.get("memory_leak_detected", False),
            recovery_successful=not service_crashed,
            recovery_time_seconds=0,
            post_recovery_performance_percent=100.0 if not service_crashed else 0.0,
            service_crashed=service_crashed,
            connection_pool_exhausted="connection" in " ".join(failure_modes.keys()).lower(),
            database_connection_issues="database" in " ".join(failure_modes.keys()).lower(),
            test_passed=test_passed,
            critical_issues=critical_issues,
            recommendations=recommendations
        )
    
    async def _execute_spike_test(
        self, 
        config: StressTestConfiguration, 
        start_time: datetime
    ) -> StressTestResult:
        """Execute spike stress test."""
        logger.info("Executing spike test...")
        
        # Calculate base load and spike load
        base_users = max(1, config.max_concurrent_users // config.spike_multiplier)
        spike_users = int(base_users * config.spike_multiplier)
        
        logger.info(f"Base load: {base_users} users, Spike load: {spike_users} users")
        
        # Start monitoring
        self.memory_profiler.start_profiling()
        self.memory_profiler.take_snapshot("spike_start")
        
        all_results = []
        performance_curve = []
        failure_modes = {}
        
        # Phase 1: Establish base load
        logger.info("Phase 1: Establishing base load...")
        base_data = self._prepare_test_data(base_users)
        base_results = await self._execute_load_level(base_users, base_data, 30, config.request_timeout)
        all_results.extend(base_results)
        
        base_success_rate = sum(1 for r in base_results if r["success"]) / len(base_results) * 100
        performance_curve.append({
            "phase": "base_load",
            "concurrent_users": base_users,
            "success_rate_percent": base_success_rate,
            "timestamp": datetime.utcnow().isoformat()
        })
        
        self.memory_profiler.take_snapshot("base_load_complete")
        
        # Phase 2: Execute spike
        logger.info("Phase 2: Executing spike...")
        spike_data = self._prepare_test_data(spike_users)
        spike_results = await self._execute_load_level(
            spike_users, spike_data, config.spike_duration_seconds, config.request_timeout
        )
        all_results.extend(spike_results)
        
        spike_success_rate = sum(1 for r in spike_results if r["success"]) / len(spike_results) * 100
        performance_curve.append({
            "phase": "spike",
            "concurrent_users": spike_users,
            "success_rate_percent": spike_success_rate,
            "timestamp": datetime.utcnow().isoformat()
        })
        
        self.memory_profiler.take_snapshot("spike_complete")
        
        # Phase 3: Recovery period
        logger.info("Phase 3: Recovery testing...")
        recovery_start = time.perf_counter()
        recovery_data = self._prepare_test_data(base_users)
        recovery_results = await self._execute_load_level(
            base_users, recovery_data, config.recovery_time_seconds, config.request_timeout
        )
        all_results.extend(recovery_results)
        recovery_end = time.perf_counter()
        
        recovery_success_rate = sum(1 for r in recovery_results if r["success"]) / len(recovery_results) * 100
        performance_curve.append({
            "phase": "recovery",
            "concurrent_users": base_users,
            "success_rate_percent": recovery_success_rate,
            "timestamp": datetime.utcnow().isoformat()
        })
        
        self.memory_profiler.take_snapshot("recovery_complete")
        self.memory_profiler.stop_profiling()
        
        # Analyze failure modes
        for result in all_results:
            if not result["success"]:
                error_type = result.get("error", "Unknown")
                failure_modes[error_type] = failure_modes.get(error_type, 0) + 1
        
        # Check service health
        service_crashed = await self._check_service_health()
        
        # Calculate recovery metrics
        recovery_time = recovery_end - recovery_start
        recovery_successful = recovery_success_rate >= config.expected_recovery_threshold
        post_recovery_performance = (recovery_success_rate / base_success_rate * 100) if base_success_rate > 0 else 0
        
        # Resource analysis
        memory_analysis = self.memory_profiler.get_memory_analysis()
        peak_memory = memory_analysis.get("peak_memory_mb", 0)
        
        # Generate recommendations
        recommendations = []
        if spike_success_rate < 50:
            recommendations.append("Implement rate limiting to handle traffic spikes")
        if not recovery_successful:
            recommendations.append("Improve graceful degradation and recovery mechanisms")
        if memory_analysis.get("memory_leak_detected", False):
            recommendations.append("Fix memory leaks detected during spike testing")
        
        # Identify critical issues
        critical_issues = []
        if service_crashed:
            critical_issues.append("Service crashed during spike test")
        if spike_success_rate < 25:
            critical_issues.append(f"Severe spike performance degradation: {spike_success_rate:.1f}% success rate")
        if not recovery_successful:
            critical_issues.append("Service failed to recover to baseline performance")
        
        end_time = datetime.utcnow()
        test_duration = (end_time - start_time).total_seconds()
        
        return StressTestResult(
            test_config=config,
            start_time=start_time,
            end_time=end_time,
            test_duration_seconds=test_duration,
            breaking_point_users=spike_users if spike_success_rate < 50 else None,
            max_stable_users=base_users,
            max_successful_rps=len(recovery_results) / recovery_time if recovery_time > 0 else 0,
            performance_degradation_curve=performance_curve,
            failure_modes=failure_modes,
            error_progression=[],
            peak_memory_usage_mb=peak_memory,
            peak_cpu_usage_percent=0,  # Not measured in spike test
            resource_exhaustion_detected=peak_memory > 1024,
            memory_leak_detected=memory_analysis.get("memory_leak_detected", False),
            recovery_successful=recovery_successful,
            recovery_time_seconds=recovery_time,
            post_recovery_performance_percent=post_recovery_performance,
            service_crashed=service_crashed,
            connection_pool_exhausted=False,
            database_connection_issues=False,
            test_passed=len(critical_issues) == 0,
            critical_issues=critical_issues,
            recommendations=recommendations
        )
    
    async def _execute_resource_exhaustion_test(
        self, 
        config: StressTestConfiguration, 
        start_time: datetime
    ) -> StressTestResult:
        """Execute resource exhaustion stress test."""
        logger.info("Executing resource exhaustion test...")
        
        # This test gradually increases resource usage until exhaustion
        recommendations = ["Resource exhaustion test implementation needed"]
        critical_issues = ["Resource exhaustion test not fully implemented"]
        
        # Placeholder implementation
        end_time = datetime.utcnow()
        
        return StressTestResult(
            test_config=config,
            start_time=start_time,
            end_time=end_time,
            test_duration_seconds=(end_time - start_time).total_seconds(),
            breaking_point_users=None,
            max_stable_users=0,
            max_successful_rps=0,
            performance_degradation_curve=[],
            failure_modes={},
            error_progression=[],
            peak_memory_usage_mb=0,
            peak_cpu_usage_percent=0,
            resource_exhaustion_detected=False,
            memory_leak_detected=False,
            recovery_successful=True,
            recovery_time_seconds=0,
            post_recovery_performance_percent=100.0,
            service_crashed=False,
            connection_pool_exhausted=False,
            database_connection_issues=False,
            test_passed=False,
            critical_issues=critical_issues,
            recommendations=recommendations
        )
    
    async def _execute_recovery_test(
        self, 
        config: StressTestConfiguration, 
        start_time: datetime
    ) -> StressTestResult:
        """Execute recovery stress test."""
        logger.info("Executing recovery test...")
        
        # This test intentionally overloads the system then tests recovery
        recommendations = ["Recovery test implementation needed"]
        critical_issues = ["Recovery test not fully implemented"]
        
        # Placeholder implementation
        end_time = datetime.utcnow()
        
        return StressTestResult(
            test_config=config,
            start_time=start_time,
            end_time=end_time,
            test_duration_seconds=(end_time - start_time).total_seconds(),
            breaking_point_users=None,
            max_stable_users=0,
            max_successful_rps=0,
            performance_degradation_curve=[],
            failure_modes={},
            error_progression=[],
            peak_memory_usage_mb=0,
            peak_cpu_usage_percent=0,
            resource_exhaustion_detected=False,
            memory_leak_detected=False,
            recovery_successful=True,
            recovery_time_seconds=0,
            post_recovery_performance_percent=100.0,
            service_crashed=False,
            connection_pool_exhausted=False,
            database_connection_issues=False,
            test_passed=False,
            critical_issues=critical_issues,
            recommendations=recommendations
        )
    
    async def _execute_load_level(
        self, 
        concurrent_users: int, 
        test_data: List[Dict[str, Any]], 
        duration_seconds: int,
        request_timeout: float
    ) -> List[Dict[str, Any]]:
        """Execute load at a specific level."""
        
        results = []
        start_time = time.perf_counter()
        end_time = start_time + duration_seconds
        
        # Create semaphore for concurrency control
        semaphore = asyncio.Semaphore(concurrent_users)
        
        async def make_request_with_semaphore(data: Dict[str, Any]) -> Dict[str, Any]:
            """Make request with concurrency limiting."""
            async with semaphore:
                return await self._make_single_request(data, request_timeout)
        
        # Send requests for the specified duration
        data_index = 0
        
        while time.perf_counter() < end_time:
            # Create batch of concurrent requests
            batch_tasks = []
            for _ in range(min(concurrent_users, len(test_data))):
                data = test_data[data_index % len(test_data)]
                task = make_request_with_semaphore(data)
                batch_tasks.append(task)
                data_index += 1
            
            # Execute batch
            batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
            
            # Process results
            for result in batch_results:
                if isinstance(result, Exception):
                    results.append({
                        "success": False,
                        "error": str(result),
                        "response_time_ms": 0,
                        "status_code": 0,
                        "timestamp": datetime.utcnow()
                    })
                else:
                    results.append(result)
            
            # Brief pause to prevent overwhelming
            await asyncio.sleep(0.01)
        
        return results
    
    async def _make_single_request(
        self, 
        data: Dict[str, Any],
        timeout: float
    ) -> Dict[str, Any]:
        """Make a single request with error handling."""
        
        request_start = time.perf_counter()
        timestamp = datetime.utcnow()
        
        try:
            response = await asyncio.wait_for(
                self.client.post("/api/v1/patterns/detect", json=data),
                timeout=timeout
            )
            
            request_end = time.perf_counter()
            response_time_ms = (request_end - request_start) * 1000
            
            return {
                "success": response.status_code < 400,
                "status_code": response.status_code,
                "response_time_ms": response_time_ms,
                "timestamp": timestamp,
                "error": None if response.status_code < 400 else f"HTTP {response.status_code}"
            }
            
        except asyncio.TimeoutError:
            request_end = time.perf_counter()
            response_time_ms = (request_end - request_start) * 1000
            
            return {
                "success": False,
                "status_code": 408,
                "response_time_ms": response_time_ms,
                "timestamp": timestamp,
                "error": "Request timeout"
            }
            
        except Exception as e:
            request_end = time.perf_counter()
            response_time_ms = (request_end - request_start) * 1000
            
            return {
                "success": False,
                "status_code": 500,
                "response_time_ms": response_time_ms,
                "timestamp": timestamp,
                "error": str(e)
            }
    
    def _prepare_test_data(self, user_count: int) -> List[Dict[str, Any]]:
        """Prepare test data for stress testing."""
        test_data = []
        
        for i in range(user_count):
            # Generate varied complexity to stress different code paths
            if i % 4 == 0:
                # Simple code
                code_content = self.code_generator.generate_function()
            elif i % 4 == 1:
                # Medium complexity
                code_content = self.code_generator.generate_file_content("module", pattern_count=5)
            elif i % 4 == 2:
                # High complexity
                code_content = self.code_generator.generate_file_content("module", pattern_count=10)
            else:
                # Very high complexity for stress
                code_content = self.code_generator.generate_file_content("module", pattern_count=20)
            
            test_data.append({
                "repository_id": f"stress-test-repo-{i}",
                "ast_data": {"type": "Module", "children": []},
                "code_content": code_content,
                "file_path": f"stress_test_{i}.py",
                "language": "python",
                "detection_config": {
                    "confidence_threshold": 0.7,
                    "enable_ml_models": True,
                    "enable_heuristic_detection": True
                }
            })
        
        return test_data
    
    async def _check_service_health(self) -> bool:
        """Check if service crashed during test."""
        try:
            response = await asyncio.wait_for(
                self.client.get("/health"),
                timeout=5.0
            )
            return response.status_code != 200
        except Exception:
            return True  # Service is not responding, likely crashed
    
    def _generate_stress_recommendations(
        self,
        breaking_point_users: Optional[int],
        max_stable_users: int,
        failure_modes: Dict[str, int],
        peak_memory_mb: float,
        peak_cpu_percent: float
    ) -> List[str]:
        """Generate recommendations based on stress test results."""
        
        recommendations = []
        
        if breaking_point_users and breaking_point_users < 100:
            recommendations.append(f"Increase system capacity: breaking point at only {breaking_point_users} users")
        
        if peak_memory_mb > 1024:
            recommendations.append(f"Optimize memory usage: peaked at {peak_memory_mb:.1f}MB")
        
        if peak_cpu_percent > 90:
            recommendations.append(f"Optimize CPU usage: peaked at {peak_cpu_percent:.1f}%")
        
        if "timeout" in failure_modes:
            recommendations.append("Implement request timeout handling and queuing")
        
        if "connection" in " ".join(failure_modes.keys()).lower():
            recommendations.append("Increase connection pool size and implement connection recycling")
        
        if max_stable_users < 50:
            recommendations.append("Implement horizontal scaling or optimize request processing")
        
        return recommendations


@pytest.mark.performance
@pytest.mark.stress
class TestStressTesting:
    """Comprehensive stress testing suite."""
    
    @pytest.mark.asyncio
    async def test_breaking_point_identification(self, async_test_client: AsyncClient):
        """Test to identify system breaking point."""
        config = StressTestConfiguration(
            test_name="Breaking Point Identification",
            test_description="Find the maximum concurrent users the system can handle",
            test_type="breaking_point",
            max_concurrent_users=100,
            user_increment=10,
            test_duration_per_level=20,
            failure_threshold_percent=50.0
        )
        
        executor = StressTestExecutor(async_test_client)
        result = await executor.execute_stress_test(config)
        
        # Assertions
        assert result.max_stable_users > 0, "Should find at least some stable capacity"
        assert len(result.performance_degradation_curve) > 0, "Should record performance degradation"
        
        # Log important findings
        logger.info(f"Breaking point: {result.breaking_point_users} users")
        logger.info(f"Max stable capacity: {result.max_stable_users} users")
        logger.info(f"Peak memory usage: {result.peak_memory_usage_mb:.1f}MB")
        
        # Verify we found meaningful limits
        if result.breaking_point_users:
            assert result.breaking_point_users >= result.max_stable_users
        
        # Check for critical issues
        if result.critical_issues:
            logger.warning(f"Critical issues found: {result.critical_issues}")
    
    @pytest.mark.asyncio
    async def test_spike_load_handling(self, async_test_client: AsyncClient):
        """Test system behavior under sudden load spikes."""
        config = StressTestConfiguration(
            test_name="Spike Load Handling",
            test_description="Test response to sudden 5x load increase",
            test_type="spike",
            max_concurrent_users=50,
            spike_multiplier=5.0,
            spike_duration_seconds=15,
            recovery_time_seconds=30,
            expected_recovery_threshold=80.0
        )
        
        executor = StressTestExecutor(async_test_client)
        result = await executor.execute_stress_test(config)
        
        # Assertions
        assert not result.service_crashed, "Service should not crash during spike"
        assert len(result.performance_degradation_curve) >= 3, "Should have base, spike, and recovery phases"
        
        # Check recovery
        if result.recovery_successful:
            assert result.post_recovery_performance_percent >= 70.0, "Should recover reasonable performance"
        
        # Log spike handling results
        spike_phase = next((p for p in result.performance_degradation_curve if p.get("phase") == "spike"), None)
        if spike_phase:
            logger.info(f"Spike success rate: {spike_phase['success_rate_percent']:.1f}%")
        
        logger.info(f"Recovery successful: {result.recovery_successful}")
        logger.info(f"Post-recovery performance: {result.post_recovery_performance_percent:.1f}%")
    
    @pytest.mark.asyncio
    async def test_memory_stress_detection(self, async_test_client: AsyncClient):
        """Test for memory leaks and excessive memory usage under stress."""
        config = StressTestConfiguration(
            test_name="Memory Stress Detection",
            test_description="Test for memory leaks and excessive usage",
            test_type="breaking_point",
            max_concurrent_users=50,
            user_increment=15,
            test_duration_per_level=45,  # Longer duration to detect leaks
            failure_threshold_percent=75.0
        )
        
        executor = StressTestExecutor(async_test_client)
        result = await executor.execute_stress_test(config)
        
        # Memory-specific assertions
        assert result.peak_memory_usage_mb > 0, "Should record memory usage"
        
        # Check for memory leaks
        if result.memory_leak_detected:
            logger.warning("Memory leak detected during stress test")
            assert "memory" in " ".join(result.recommendations).lower(), "Should recommend memory optimization"
        
        # Check for excessive memory usage
        if result.peak_memory_usage_mb > 1024:  # 1GB threshold
            logger.warning(f"High memory usage detected: {result.peak_memory_usage_mb:.1f}MB")
            assert result.resource_exhaustion_detected, "Should detect resource exhaustion"
        
        # Performance should degrade gracefully with memory pressure
        if len(result.performance_degradation_curve) > 1:
            first_point = result.performance_degradation_curve[0]
            last_point = result.performance_degradation_curve[-1]
            
            # Memory usage should not grow excessively
            memory_growth = last_point.get("memory_usage_mb", 0) - first_point.get("memory_usage_mb", 0)
            assert memory_growth < 500, f"Excessive memory growth: {memory_growth:.1f}MB"
    
    @pytest.mark.asyncio
    async def test_connection_pool_exhaustion(self, async_test_client: AsyncClient):
        """Test behavior when connection pools are exhausted."""
        config = StressTestConfiguration(
            test_name="Connection Pool Exhaustion",
            test_description="Test connection pool limits and handling",
            test_type="breaking_point",
            max_concurrent_users=200,  # High concurrency to stress connections
            user_increment=25,
            test_duration_per_level=15,
            failure_threshold_percent=60.0
        )
        
        executor = StressTestExecutor(async_test_client)
        result = await executor.execute_stress_test(config)
        
        # Check for connection-related failures
        connection_errors = sum(
            count for error_type, count in result.failure_modes.items()
            if "connection" in error_type.lower() or "pool" in error_type.lower()
        )
        
        if connection_errors > 0:
            logger.info(f"Connection-related errors detected: {connection_errors}")
            assert "connection" in " ".join(result.recommendations).lower(), "Should recommend connection optimization"
        
        # High concurrency should not cause complete failure
        if result.breaking_point_users:
            assert result.breaking_point_users >= 50, "Should handle at least 50 concurrent connections"
    
    @pytest.mark.asyncio
    async def test_gradual_degradation_pattern(self, async_test_client: AsyncClient):
        """Test that performance degrades gracefully, not catastrophically."""
        config = StressTestConfiguration(
            test_name="Gradual Degradation Pattern",
            test_description="Verify graceful performance degradation",
            test_type="breaking_point",
            max_concurrent_users=80,
            user_increment=8,
            test_duration_per_level=25,
            failure_threshold_percent=40.0
        )
        
        executor = StressTestExecutor(async_test_client)
        result = await executor.execute_stress_test(config)
        
        # Analyze degradation pattern
        if len(result.performance_degradation_curve) >= 3:
            success_rates = [point["success_rate_percent"] for point in result.performance_degradation_curve]
            
            # Check for gradual degradation (no cliff-drops)
            for i in range(1, len(success_rates)):
                previous_rate = success_rates[i-1]
                current_rate = success_rates[i]
                
                # Success rate shouldn't drop by more than 30% in one step
                if previous_rate > 50:  # Only check if we started with decent performance
                    drop_percent = previous_rate - current_rate
                    assert drop_percent <= 40, f"Too steep degradation: {drop_percent:.1f}% drop at step {i}"
        
        # System should maintain some level of service even near breaking point
        if result.breaking_point_users and result.max_stable_users:
            capacity_utilization = result.max_stable_users / result.breaking_point_users
            assert capacity_utilization >= 0.5, "Should maintain 50%+ capacity before breaking point"
    
    @pytest.mark.asyncio
    async def test_error_handling_under_stress(self, async_test_client: AsyncClient):
        """Test that error handling remains functional under stress."""
        config = StressTestConfiguration(
            test_name="Error Handling Under Stress",
            test_description="Verify error handling doesn't break under load",
            test_type="breaking_point",
            max_concurrent_users=60,
            user_increment=12,
            test_duration_per_level=20,
            failure_threshold_percent=70.0  # Allow more failures to test error handling
        )
        
        executor = StressTestExecutor(async_test_client)
        result = await executor.execute_stress_test(config)
        
        # Check error mode distribution
        if result.failure_modes:
            total_errors = sum(result.failure_modes.values())
            logger.info(f"Total errors during stress test: {total_errors}")
            
            # Should have meaningful error messages, not just generic failures
            unknown_errors = result.failure_modes.get("Unknown", 0)
            unknown_percentage = (unknown_errors / total_errors * 100) if total_errors > 0 else 0
            
            assert unknown_percentage < 50, f"Too many unknown errors: {unknown_percentage:.1f}%"
            
            # Timeout errors are acceptable under stress
            timeout_errors = result.failure_modes.get("Request timeout", 0)
            if timeout_errors > 0:
                logger.info(f"Timeout errors under stress: {timeout_errors}")
        
        # Service should not crash even with many errors
        assert not result.service_crashed, "Service crashed during stress test with errors"
    
    @pytest.mark.slow
    @pytest.mark.asyncio
    async def test_sustained_stress_stability(self, async_test_client: AsyncClient):
        """Test system stability under sustained stress (slow test)."""
        config = StressTestConfiguration(
            test_name="Sustained Stress Stability",
            test_description="Test stability under sustained stress load",
            test_type="breaking_point",
            max_concurrent_users=40,
            user_increment=20,
            test_duration_per_level=120,  # 2 minutes per level
            failure_threshold_percent=30.0
        )
        
        executor = StressTestExecutor(async_test_client)
        result = await executor.execute_stress_test(config)
        
        # Sustained stress should not cause crashes
        assert not result.service_crashed, "Service should remain stable under sustained stress"
        
        # Memory should not grow unbounded
        assert not result.memory_leak_detected, "Memory leaks detected during sustained stress"
        
        # Performance should remain relatively stable within each level
        for i, point in enumerate(result.performance_degradation_curve):
            logger.info(f"Level {i}: {point['concurrent_users']} users, {point['success_rate_percent']:.1f}% success")
        
        # Should achieve some level of sustained throughput
        assert result.max_successful_rps > 0, "Should achieve some sustained throughput"
    
    @pytest.mark.asyncio
    async def test_stress_test_result_analysis(self, async_test_client: AsyncClient):
        """Test stress test result analysis and reporting."""
        config = StressTestConfiguration(
            test_name="Result Analysis Test",
            test_description="Test result analysis functionality",
            test_type="breaking_point",
            max_concurrent_users=30,
            user_increment=10,
            test_duration_per_level=15,
            failure_threshold_percent=50.0
        )
        
        executor = StressTestExecutor(async_test_client)
        result = await executor.execute_stress_test(config)
        
        # Test serialization
        result_dict = result.to_dict()
        assert isinstance(result_dict, dict)
        assert "test_config" in result_dict
        assert "performance_degradation_curve" in result_dict
        assert "critical_issues" in result_dict
        assert "recommendations" in result_dict
        
        # Test JSON serialization
        json_str = json.dumps(result_dict, indent=2)
        assert len(json_str) > 100
        
        # Test deserialization
        parsed_result = json.loads(json_str)
        assert parsed_result["test_config"]["test_name"] == config.test_name
        
        # Verify recommendations are actionable
        if result.recommendations:
            for recommendation in result.recommendations:
                assert len(recommendation) > 10, "Recommendations should be meaningful"
                logger.info(f"Recommendation: {recommendation}")
        
        # Verify critical issues are specific
        if result.critical_issues:
            for issue in result.critical_issues:
                assert len(issue) > 10, "Critical issues should be specific"
                logger.warning(f"Critical issue: {issue}")