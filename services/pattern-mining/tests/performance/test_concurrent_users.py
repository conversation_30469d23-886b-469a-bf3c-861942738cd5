"""
Advanced Concurrent User Simulation for Pattern Mining Service

This module provides realistic user behavior simulation capabilities including:
- Realistic user session patterns and think times
- Geographic distribution simulation
- Variable load profiles (peak hours, off-peak, weekend patterns)
- User journey mapping and behavior analytics
- Session-based concurrent testing with state management
"""

import pytest
import asyncio
import time
import logging
import random
from typing import Dict, List, Any, Optional, Tu<PERSON>
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from concurrent.futures import ThreadPoolExecutor
import statistics
import json
import uuid
from contextlib import asynccontextmanager

from httpx import AsyncClient
from tests.utils.performance import (
    PerformanceTestRunner, LatencyMeasurer, MemoryProfiler, 
    ThroughputTester, performance_test
)
from tests.utils.generators import CodeGenerator, PatternGenerator

logger = logging.getLogger(__name__)


@dataclass
class UserBehaviorProfile:
    """User behavior profile for realistic simulation."""
    
    # User characteristics
    user_type: str  # "developer", "architect", "qa_engineer", "researcher"
    experience_level: str  # "junior", "mid", "senior", "expert"
    geographic_region: str  # "us_west", "us_east", "europe", "asia_pacific"
    
    # Behavior patterns
    session_duration_minutes: Tuple[int, int]  # min, max session duration
    requests_per_session: Tuple[int, int]  # min, max requests per session
    think_time_seconds: Tuple[float, float]  # min, max think time between requests
    peak_hours: List[int]  # Hours when user is most active (0-23)
    
    # Request patterns
    analysis_complexity_preference: str  # "simple", "moderate", "complex", "mixed"
    repository_size_preference: str  # "small", "medium", "large", "enterprise"
    error_tolerance: float  # How likely to retry on errors (0.0-1.0)
    
    # Performance expectations
    expected_response_time_ms: float
    timeout_patience_seconds: float

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return asdict(self)


@dataclass
class UserSession:
    """Individual user session state."""
    
    session_id: str
    user_profile: UserBehaviorProfile
    start_time: datetime
    end_time: Optional[datetime]
    
    # Session metrics
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    average_response_time_ms: float = 0.0
    total_think_time_seconds: float = 0.0
    
    # Request history
    request_history: List[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.request_history is None:
            self.request_history = []
    
    def add_request_result(self, result: Dict[str, Any]):
        """Add a request result to the session."""
        self.request_history.append(result)
        self.total_requests += 1
        
        if result.get("success", False):
            self.successful_requests += 1
        else:
            self.failed_requests += 1
        
        # Update average response time
        if result.get("response_time_ms", 0) > 0:
            response_times = [r.get("response_time_ms", 0) for r in self.request_history if r.get("success", False)]
            self.average_response_time_ms = statistics.mean(response_times) if response_times else 0.0
    
    def calculate_session_metrics(self) -> Dict[str, Any]:
        """Calculate comprehensive session metrics."""
        session_duration = (self.end_time - self.start_time).total_seconds() if self.end_time else 0
        
        return {
            "session_id": self.session_id,
            "user_type": self.user_profile.user_type,
            "geographic_region": self.user_profile.geographic_region,
            "session_duration_seconds": session_duration,
            "total_requests": self.total_requests,
            "successful_requests": self.successful_requests,
            "failed_requests": self.failed_requests,
            "success_rate_percent": (self.successful_requests / self.total_requests * 100) if self.total_requests > 0 else 0,
            "average_response_time_ms": self.average_response_time_ms,
            "total_think_time_seconds": self.total_think_time_seconds,
            "requests_per_minute": (self.total_requests / (session_duration / 60)) if session_duration > 0 else 0,
            "user_satisfaction_score": self._calculate_satisfaction_score()
        }
    
    def _calculate_satisfaction_score(self) -> float:
        """Calculate user satisfaction score based on performance and success rate."""
        if self.total_requests == 0:
            return 0.0
        
        # Base score from success rate
        success_score = (self.successful_requests / self.total_requests) * 60
        
        # Performance score based on response time vs expectations
        if self.average_response_time_ms > 0:
            expected_time = self.user_profile.expected_response_time_ms
            if self.average_response_time_ms <= expected_time:
                performance_score = 40  # Full performance score
            elif self.average_response_time_ms <= expected_time * 2:
                performance_score = 20  # Degraded but acceptable
            else:
                performance_score = 0  # Poor performance
        else:
            performance_score = 0
        
        return min(100.0, success_score + performance_score)


class UserBehaviorSimulator:
    """Simulate realistic user behavior patterns."""
    
    def __init__(self, client: AsyncClient):
        self.client = client
        self.code_generator = CodeGenerator()
        self.active_sessions: Dict[str, UserSession] = {}
        
    def create_user_profiles(self, count: int) -> List[UserBehaviorProfile]:
        """Create diverse user behavior profiles."""
        profiles = []
        
        user_types = [
            ("developer", "mid", "simple", "medium"),
            ("architect", "senior", "complex", "large"),
            ("qa_engineer", "mid", "moderate", "small"),
            ("researcher", "expert", "complex", "enterprise"),
            ("developer", "junior", "simple", "small"),
            ("developer", "senior", "mixed", "large"),
        ]
        
        regions = ["us_west", "us_east", "europe", "asia_pacific"]
        
        for i in range(count):
            user_type, experience, complexity_pref, size_pref = random.choice(user_types)
            region = random.choice(regions)
            
            # Adjust behavior based on user type and experience
            if user_type == "developer":
                session_duration = (15, 45) if experience == "junior" else (30, 90)
                requests_per_session = (5, 15) if experience == "junior" else (10, 30)
                think_time = (3.0, 10.0) if experience == "junior" else (1.0, 5.0)
                expected_response = 200.0 if experience == "junior" else 100.0
                timeout_patience = 15.0 if experience == "junior" else 30.0
            elif user_type == "architect":
                session_duration = (45, 120)
                requests_per_session = (15, 40)
                think_time = (5.0, 15.0)  # More thinking time for analysis
                expected_response = 500.0  # More patient with complex analysis
                timeout_patience = 60.0
            elif user_type == "qa_engineer":
                session_duration = (20, 60)
                requests_per_session = (20, 50)  # More testing requests
                think_time = (1.0, 3.0)  # Quick testing cycles
                expected_response = 150.0
                timeout_patience = 20.0
            else:  # researcher
                session_duration = (60, 180)  # Long research sessions
                requests_per_session = (30, 100)
                think_time = (10.0, 30.0)  # Deep analysis time
                expected_response = 1000.0  # Very patient
                timeout_patience = 120.0
            
            # Peak hours based on geographic region
            if region == "us_west":
                peak_hours = [10, 11, 14, 15, 16]  # PST business hours
            elif region == "us_east":
                peak_hours = [9, 10, 13, 14, 15]  # EST business hours
            elif region == "europe":
                peak_hours = [8, 9, 12, 13, 14]  # CET business hours
            else:  # asia_pacific
                peak_hours = [9, 10, 13, 14, 15]  # JST business hours
            
            profiles.append(UserBehaviorProfile(
                user_type=user_type,
                experience_level=experience,
                geographic_region=region,
                session_duration_minutes=session_duration,
                requests_per_session=requests_per_session,
                think_time_seconds=think_time,
                peak_hours=peak_hours,
                analysis_complexity_preference=complexity_pref,
                repository_size_preference=size_pref,
                error_tolerance=0.8 if experience in ["senior", "expert"] else 0.5,
                expected_response_time_ms=expected_response,
                timeout_patience_seconds=timeout_patience
            ))
        
        return profiles
    
    async def simulate_user_session(self, profile: UserBehaviorProfile) -> UserSession:
        """Simulate a complete user session."""
        session_id = str(uuid.uuid4())
        session = UserSession(
            session_id=session_id,
            user_profile=profile,
            start_time=datetime.utcnow()
        )
        
        self.active_sessions[session_id] = session
        
        # Determine session parameters
        session_duration = random.randint(*profile.session_duration_minutes) * 60  # Convert to seconds
        total_requests = random.randint(*profile.requests_per_session)
        
        logger.info(f"Starting session {session_id} ({profile.user_type}): {total_requests} requests over {session_duration/60:.1f} minutes")
        
        start_time = time.perf_counter()
        end_time = start_time + session_duration
        
        request_count = 0
        
        while time.perf_counter() < end_time and request_count < total_requests:
            # Generate request based on user preferences
            request_data = self._generate_user_request(profile)
            
            # Make request
            result = await self._make_user_request(request_data, profile)
            session.add_request_result(result)
            
            request_count += 1
            
            # Simulate think time between requests
            if request_count < total_requests:
                think_time = random.uniform(*profile.think_time_seconds)
                session.total_think_time_seconds += think_time
                await asyncio.sleep(think_time)
            
            # Handle errors based on user tolerance
            if not result.get("success", False) and random.random() > profile.error_tolerance:
                logger.warning(f"Session {session_id} ending early due to errors")
                break
        
        session.end_time = datetime.utcnow()
        
        # Log session completion
        metrics = session.calculate_session_metrics()
        logger.info(f"Session {session_id} completed: {metrics['success_rate_percent']:.1f}% success, "
                   f"satisfaction: {metrics['user_satisfaction_score']:.1f}/100")
        
        return session
    
    def _generate_user_request(self, profile: UserBehaviorProfile) -> Dict[str, Any]:
        """Generate a request based on user profile preferences."""
        
        # Determine code complexity based on user preference
        if profile.analysis_complexity_preference == "simple":
            pattern_count = random.randint(1, 3)
            file_type = "function"
        elif profile.analysis_complexity_preference == "moderate":
            pattern_count = random.randint(3, 8)
            file_type = "module"
        elif profile.analysis_complexity_preference == "complex":
            pattern_count = random.randint(8, 20) 
            file_type = "module"
        else:  # mixed
            pattern_count = random.randint(1, 20)
            file_type = random.choice(["function", "module"])
        
        # Generate code content
        if file_type == "function":
            code_content = self.code_generator.generate_function()
        else:
            code_content = self.code_generator.generate_file_content("module", pattern_count=pattern_count)
        
        # Repository size affects the context
        if profile.repository_size_preference == "small":
            repository_id = f"small-repo-{random.randint(1, 100)}"
        elif profile.repository_size_preference == "medium":
            repository_id = f"medium-repo-{random.randint(1, 500)}"
        elif profile.repository_size_preference == "large":
            repository_id = f"large-repo-{random.randint(1, 1000)}"
        else:  # enterprise
            repository_id = f"enterprise-repo-{random.randint(1, 10000)}"
        
        return {
            "repository_id": repository_id,
            "ast_data": {"type": "Module", "children": []},
            "code_content": code_content,
            "file_path": f"{profile.user_type}_{profile.experience_level}_{random.randint(1,1000)}.py",
            "language": "python",
            "detection_config": {
                "confidence_threshold": 0.7 if profile.experience_level in ["senior", "expert"] else 0.8,
                "enable_ml_models": True,
                "enable_heuristic_detection": True,
                "timeout_seconds": profile.timeout_patience_seconds
            },
            "user_context": {
                "user_type": profile.user_type,
                "experience_level": profile.experience_level,
                "expected_patterns": pattern_count,
                "analysis_intent": self._get_analysis_intent(profile)
            }
        }
    
    def _get_analysis_intent(self, profile: UserBehaviorProfile) -> str:
        """Get analysis intent based on user type."""
        intents = {
            "developer": ["code_review", "refactoring", "bug_detection", "optimization"],
            "architect": ["system_design", "pattern_analysis", "quality_assessment", "scalability_review"],
            "qa_engineer": ["test_analysis", "bug_detection", "quality_verification", "regression_testing"],
            "researcher": ["pattern_research", "academic_analysis", "algorithm_study", "comparative_analysis"]
        }
        
        return random.choice(intents.get(profile.user_type, ["general_analysis"]))
    
    async def _make_user_request(self, request_data: Dict[str, Any], profile: UserBehaviorProfile) -> Dict[str, Any]:
        """Make a request with user-specific error handling and timeouts."""
        start_time = time.perf_counter()
        timestamp = datetime.utcnow()
        
        try:
            # Use user-specific timeout
            response = await asyncio.wait_for(
                self.client.post("/api/v1/patterns/detect", json=request_data),
                timeout=profile.timeout_patience_seconds
            )
            
            end_time = time.perf_counter()
            response_time_ms = (end_time - start_time) * 1000
            
            success = response.status_code < 400
            
            # Check if response time meets user expectations
            performance_acceptable = response_time_ms <= profile.expected_response_time_ms
            
            return {
                "success": success,
                "status_code": response.status_code,
                "response_time_ms": response_time_ms,
                "timestamp": timestamp,
                "performance_acceptable": performance_acceptable,
                "user_context": request_data.get("user_context", {}),
                "error": None if success else f"HTTP {response.status_code}"
            }
            
        except asyncio.TimeoutError:
            end_time = time.perf_counter()
            response_time_ms = (end_time - start_time) * 1000
            
            return {
                "success": False,
                "status_code": 408,
                "response_time_ms": response_time_ms,
                "timestamp": timestamp,
                "performance_acceptable": False,
                "user_context": request_data.get("user_context", {}),
                "error": "User patience timeout exceeded"
            }
            
        except Exception as e:
            end_time = time.perf_counter()
            response_time_ms = (end_time - start_time) * 1000
            
            return {
                "success": False,
                "status_code": 500,
                "response_time_ms": response_time_ms,
                "timestamp": timestamp,
                "performance_acceptable": False,
                "user_context": request_data.get("user_context", {}),
                "error": str(e)
            }


@dataclass
class ConcurrentUserTestResult:
    """Results from concurrent user simulation."""
    
    # Test configuration
    total_users: int
    test_duration_seconds: float
    user_profiles_used: List[str]
    
    # Session results
    completed_sessions: int
    total_requests: int
    successful_requests: int
    failed_requests: int
    
    # Performance metrics
    average_response_time_ms: float
    p95_response_time_ms: float
    p99_response_time_ms: float
    
    # User experience metrics
    average_user_satisfaction: float
    satisfaction_by_user_type: Dict[str, float]
    geographic_performance: Dict[str, Dict[str, float]]
    
    # System metrics
    peak_concurrent_requests: int
    requests_per_second: float
    error_rate_percent: float
    
    # Session analytics
    session_duration_stats: Dict[str, float]
    think_time_stats: Dict[str, float]
    user_behavior_insights: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for reporting."""
        return asdict(self)


class ConcurrentUserTestExecutor:
    """Execute realistic concurrent user simulation tests."""
    
    def __init__(self, client: AsyncClient):
        self.client = client
        self.simulator = UserBehaviorSimulator(client)
    
    async def execute_concurrent_user_test(
        self, 
        user_count: int,
        test_duration_minutes: int = 30,
        user_distribution: Optional[Dict[str, float]] = None
    ) -> ConcurrentUserTestResult:
        """Execute concurrent user simulation test."""
        
        logger.info(f"Starting concurrent user test: {user_count} users for {test_duration_minutes} minutes")
        
        start_time = datetime.utcnow()
        test_duration_seconds = test_duration_minutes * 60
        
        # Create user profiles
        profiles = self.simulator.create_user_profiles(user_count)
        
        # Apply user distribution if specified
        if user_distribution:
            profiles = self._apply_user_distribution(profiles, user_distribution)
        
        # Start all user sessions concurrently
        tasks = []
        for profile in profiles:
            task = asyncio.create_task(self.simulator.simulate_user_session(profile))
            tasks.append(task)
        
        # Wait for all sessions to complete or timeout
        completed_sessions = []
        try:
            # Use asyncio.wait_for with the test duration as timeout
            await asyncio.wait_for(
                asyncio.gather(*tasks, return_exceptions=True),
                timeout=test_duration_seconds + 60  # Extra buffer for cleanup
            )
            
            # Collect completed sessions
            for task in tasks:
                if task.done() and not task.exception():
                    completed_sessions.append(task.result())
                    
        except asyncio.TimeoutError:
            logger.warning("Some user sessions timed out")
            # Collect sessions that completed before timeout
            for task in tasks:
                if task.done() and not task.exception():
                    completed_sessions.append(task.result())
        
        end_time = datetime.utcnow()
        actual_duration = (end_time - start_time).total_seconds()
        
        # Analyze results
        result = self._analyze_concurrent_user_results(
            completed_sessions, 
            actual_duration, 
            user_count,
            profiles
        )
        
        logger.info(f"Concurrent user test completed: {result.completed_sessions}/{user_count} sessions, "
                   f"{result.average_user_satisfaction:.1f}/100 avg satisfaction")
        
        return result
    
    def _apply_user_distribution(
        self, 
        profiles: List[UserBehaviorProfile], 
        distribution: Dict[str, float]
    ) -> List[UserBehaviorProfile]:
        """Apply user type distribution to profiles."""
        # This is a simplified implementation
        # In practice, you'd reorganize profiles to match the distribution
        return profiles
    
    def _analyze_concurrent_user_results(
        self,
        sessions: List[UserSession],
        duration_seconds: float,
        total_users: int,
        original_profiles: List[UserBehaviorProfile]
    ) -> ConcurrentUserTestResult:
        """Analyze concurrent user test results."""
        
        if not sessions:
            return ConcurrentUserTestResult(
                total_users=total_users,
                test_duration_seconds=duration_seconds,
                user_profiles_used=[],
                completed_sessions=0,
                total_requests=0,
                successful_requests=0,
                failed_requests=0,
                average_response_time_ms=0.0,
                p95_response_time_ms=0.0,
                p99_response_time_ms=0.0,
                average_user_satisfaction=0.0,
                satisfaction_by_user_type={},
                geographic_performance={},
                peak_concurrent_requests=0,
                requests_per_second=0.0,
                error_rate_percent=100.0,
                session_duration_stats={},
                think_time_stats={},
                user_behavior_insights={}
            )
        
        # Collect all metrics
        session_metrics = [session.calculate_session_metrics() for session in sessions]
        
        # Calculate aggregate metrics
        total_requests = sum(metrics["total_requests"] for metrics in session_metrics)
        successful_requests = sum(metrics["successful_requests"] for metrics in session_metrics)
        failed_requests = total_requests - successful_requests
        
        # Response time analysis
        all_response_times = []
        for session in sessions:
            response_times = [r.get("response_time_ms", 0) for r in session.request_history if r.get("success", False)]
            all_response_times.extend(response_times)
        
        if all_response_times:
            avg_response_time = statistics.mean(all_response_times)
            sorted_times = sorted(all_response_times)
            p95_response_time = sorted_times[int(len(sorted_times) * 0.95)] if sorted_times else 0
            p99_response_time = sorted_times[int(len(sorted_times) * 0.99)] if sorted_times else 0
        else:
            avg_response_time = p95_response_time = p99_response_time = 0
        
        # User satisfaction analysis
        satisfaction_scores = [metrics["user_satisfaction_score"] for metrics in session_metrics]
        avg_satisfaction = statistics.mean(satisfaction_scores) if satisfaction_scores else 0
        
        # Satisfaction by user type
        satisfaction_by_type = {}
        for user_type in ["developer", "architect", "qa_engineer", "researcher"]:
            type_scores = [
                metrics["user_satisfaction_score"] 
                for metrics in session_metrics 
                if metrics["user_type"] == user_type
            ]
            if type_scores:
                satisfaction_by_type[user_type] = statistics.mean(type_scores)
        
        # Geographic performance analysis
        geographic_performance = {}
        for region in ["us_west", "us_east", "europe", "asia_pacific"]:
            region_sessions = [
                session for session in sessions 
                if session.user_profile.geographic_region == region
            ]
            if region_sessions:
                region_response_times = []
                for session in region_sessions:
                    response_times = [r.get("response_time_ms", 0) for r in session.request_history if r.get("success", False)]
                    region_response_times.extend(response_times)
                
                if region_response_times:
                    geographic_performance[region] = {
                        "avg_response_time_ms": statistics.mean(region_response_times),
                        "user_count": len(region_sessions),
                        "total_requests": sum(len(s.request_history) for s in region_sessions)
                    }
        
        # Session duration stats
        session_durations = [metrics["session_duration_seconds"] for metrics in session_metrics]
        duration_stats = {
            "avg_duration_seconds": statistics.mean(session_durations) if session_durations else 0,
            "min_duration_seconds": min(session_durations) if session_durations else 0,
            "max_duration_seconds": max(session_durations) if session_durations else 0
        }
        
        # Think time stats
        think_times = [session.total_think_time_seconds for session in sessions]
        think_time_stats = {
            "avg_think_time_seconds": statistics.mean(think_times) if think_times else 0,
            "total_think_time_seconds": sum(think_times)
        }
        
        # Behavior insights
        behavior_insights = {
            "most_active_user_type": max(satisfaction_by_type.keys(), key=lambda k: len([m for m in session_metrics if m["user_type"] == k])) if satisfaction_by_type else "unknown",
            "highest_satisfaction_user_type": max(satisfaction_by_type.keys(), key=satisfaction_by_type.get) if satisfaction_by_type else "unknown",
            "avg_requests_per_session": total_requests / len(sessions) if sessions else 0,
            "completion_rate": len(sessions) / total_users * 100
        }
        
        return ConcurrentUserTestResult(
            total_users=total_users,
            test_duration_seconds=duration_seconds,
            user_profiles_used=list(set(profile.user_type for profile in original_profiles)),
            completed_sessions=len(sessions),
            total_requests=total_requests,
            successful_requests=successful_requests,
            failed_requests=failed_requests,
            average_response_time_ms=avg_response_time,
            p95_response_time_ms=p95_response_time,
            p99_response_time_ms=p99_response_time,
            average_user_satisfaction=avg_satisfaction,
            satisfaction_by_user_type=satisfaction_by_type,
            geographic_performance=geographic_performance,
            peak_concurrent_requests=total_users,  # Simplified - all users active concurrently
            requests_per_second=total_requests / duration_seconds if duration_seconds > 0 else 0,
            error_rate_percent=(failed_requests / total_requests * 100) if total_requests > 0 else 0,
            session_duration_stats=duration_stats,
            think_time_stats=think_time_stats,
            user_behavior_insights=behavior_insights
        )


@pytest.mark.performance
@pytest.mark.concurrent
@pytest.mark.asyncio
class TestConcurrentUsers:
    """Advanced concurrent user simulation tests."""
    
    async def test_realistic_developer_workload(self, async_test_client: AsyncClient):
        """Test with realistic developer workload patterns."""
        executor = ConcurrentUserTestExecutor(async_test_client)
        
        # Simulate typical development team
        result = await executor.execute_concurrent_user_test(
            user_count=25,
            test_duration_minutes=15,
            user_distribution={
                "developer": 0.6,    # 60% developers
                "architect": 0.15,   # 15% architects
                "qa_engineer": 0.20, # 20% QA engineers
                "researcher": 0.05   # 5% researchers
            }
        )
        
        # Assertions
        assert result.completed_sessions >= 20, f"Too many sessions failed to complete: {result.completed_sessions}/25"
        assert result.error_rate_percent <= 10.0, f"Error rate too high: {result.error_rate_percent}%"
        assert result.average_user_satisfaction >= 70.0, f"User satisfaction too low: {result.average_user_satisfaction}/100"
        
        # Developer-specific assertions
        if "developer" in result.satisfaction_by_user_type:
            dev_satisfaction = result.satisfaction_by_user_type["developer"]
            assert dev_satisfaction >= 75.0, f"Developer satisfaction too low: {dev_satisfaction}/100"
        
        # Performance should be reasonable for development workload
        assert result.average_response_time_ms <= 300.0, f"Average response time too high: {result.average_response_time_ms}ms"
        
        logger.info(f"Developer workload test results: {result.user_behavior_insights}")
    
    async def test_peak_hours_simulation(self, async_test_client: AsyncClient):
        """Test simulation of peak business hours load."""
        executor = ConcurrentUserTestExecutor(async_test_client)
        
        # Simulate peak hours with higher load
        result = await executor.execute_concurrent_user_test(
            user_count=50,
            test_duration_minutes=20
        )
        
        # Peak hours should handle higher load
        assert result.completed_sessions >= 40, f"Too many sessions failed during peak hours: {result.completed_sessions}/50"
        assert result.error_rate_percent <= 15.0, f"Peak hours error rate too high: {result.error_rate_percent}%"
        
        # System should maintain reasonable performance under peak load
        assert result.p95_response_time_ms <= 1000.0, f"P95 response time too high for peak hours: {result.p95_response_time_ms}ms"
        assert result.requests_per_second >= 5.0, f"Throughput too low for peak hours: {result.requests_per_second} RPS"
        
        # User satisfaction should still be acceptable during peak hours
        assert result.average_user_satisfaction >= 60.0, f"Peak hours user satisfaction too low: {result.average_user_satisfaction}/100"
        
        logger.info(f"Peak hours simulation: {result.requests_per_second:.1f} RPS, {result.average_user_satisfaction:.1f}/100 satisfaction")
    
    async def test_geographic_distribution_performance(self, async_test_client: AsyncClient):
        """Test performance across different geographic regions."""
        executor = ConcurrentUserTestExecutor(async_test_client)
        
        result = await executor.execute_concurrent_user_test(
            user_count=40,
            test_duration_minutes=10
        )
        
        # Should have users from multiple regions
        assert len(result.geographic_performance) >= 2, "Should have users from multiple geographic regions"
        
        # Check performance consistency across regions
        region_response_times = [
            perf["avg_response_time_ms"] 
            for perf in result.geographic_performance.values()
        ]
        
        if len(region_response_times) > 1:
            response_time_variance = statistics.variance(region_response_times)
            max_response_time = max(region_response_times)
            min_response_time = min(region_response_times)
            
            # Response times shouldn't vary too dramatically across regions
            assert max_response_time <= min_response_time * 3, f"Too much geographic performance variation: {min_response_time}ms - {max_response_time}ms"
        
        # All regions should get reasonable performance
        for region, perf in result.geographic_performance.items():
            assert perf["avg_response_time_ms"] <= 2000.0, f"Region {region} performance too slow: {perf['avg_response_time_ms']}ms"
        
        logger.info(f"Geographic performance: {result.geographic_performance}")
    
    async def test_user_experience_quality(self, async_test_client: AsyncClient):
        """Test user experience quality across different user types."""
        executor = ConcurrentUserTestExecutor(async_test_client)
        
        result = await executor.execute_concurrent_user_test(
            user_count=30,
            test_duration_minutes=12
        )
        
        # Different user types should have appropriate satisfaction levels
        for user_type, satisfaction in result.satisfaction_by_user_type.items():
            if user_type == "developer":
                # Developers need quick feedback
                assert satisfaction >= 70.0, f"{user_type} satisfaction too low: {satisfaction}/100"
            elif user_type == "architect":
                # Architects are more patient but expect comprehensive analysis
                assert satisfaction >= 65.0, f"{user_type} satisfaction too low: {satisfaction}/100"
            elif user_type == "qa_engineer":
                # QA engineers need reliable, fast responses
                assert satisfaction >= 75.0, f"{user_type} satisfaction too low: {satisfaction}/100"
            elif user_type == "researcher":
                # Researchers are most patient but need thorough analysis
                assert satisfaction >= 60.0, f"{user_type} satisfaction too low: {satisfaction}/100"
        
        # Overall user experience should be good
        assert result.average_user_satisfaction >= 70.0, f"Overall user satisfaction too low: {result.average_user_satisfaction}/100"
        
        # Session completion rate should be high
        completion_rate = result.user_behavior_insights.get("completion_rate", 0)
        assert completion_rate >= 80.0, f"Session completion rate too low: {completion_rate}%"
        
        logger.info(f"User experience results: {result.satisfaction_by_user_type}")
    
    async def test_session_behavior_patterns(self, async_test_client: AsyncClient):
        """Test that user session behavior patterns are realistic."""
        executor = ConcurrentUserTestExecutor(async_test_client)
        
        result = await executor.execute_concurrent_user_test(
            user_count=20,
            test_duration_minutes=8
        )
        
        # Session durations should be reasonable
        avg_session_duration = result.session_duration_stats["avg_duration_seconds"]
        assert 300 <= avg_session_duration <= 3600, f"Average session duration unrealistic: {avg_session_duration/60:.1f} minutes"
        
        # Think time should be realistic (users need time to analyze results)
        avg_think_time = result.think_time_stats["avg_think_time_seconds"]
        assert avg_think_time >= 1.0, f"Average think time too low: {avg_think_time}s"
        
        # Request patterns should be reasonable
        avg_requests_per_session = result.user_behavior_insights["avg_requests_per_session"]
        assert 3 <= avg_requests_per_session <= 100, f"Average requests per session unrealistic: {avg_requests_per_session}"
        
        # Error tolerance should be working (users shouldn't quit immediately on first error)
        if result.failed_requests > 0:
            error_tolerance_effective = result.completed_sessions / result.total_users
            assert error_tolerance_effective >= 0.5, f"Users not showing enough error tolerance: {error_tolerance_effective*100:.1f}% completion rate"
        
        logger.info(f"Session behavior patterns: avg {avg_session_duration/60:.1f}min sessions, {avg_requests_per_session:.1f} requests/session")
    
    async def test_mixed_complexity_workload(self, async_test_client: AsyncClient):
        """Test mixed complexity workload simulation."""
        simulator = UserBehaviorSimulator(async_test_client)
        
        # Create profiles with different complexity preferences
        profiles = []
        for i in range(15):
            if i < 5:
                complexity = "simple"
            elif i < 10:
                complexity = "moderate"
            else:
                complexity = "complex"
            
            profiles.append(UserBehaviorProfile(
                user_type="developer",
                experience_level="mid",
                geographic_region="us_west",
                session_duration_minutes=(10, 20),
                requests_per_session=(5, 15),
                think_time_seconds=(1.0, 5.0),
                peak_hours=[14, 15, 16],
                analysis_complexity_preference=complexity,
                repository_size_preference="medium",
                error_tolerance=0.7,
                expected_response_time_ms=200.0,
                timeout_patience_seconds=30.0
            ))
        
        # Run sessions concurrently
        tasks = [simulator.simulate_user_session(profile) for profile in profiles]
        sessions = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Filter successful sessions
        successful_sessions = [s for s in sessions if isinstance(s, UserSession)]
        
        assert len(successful_sessions) >= 12, f"Too many mixed complexity sessions failed: {len(successful_sessions)}/15"
        
        # Analyze complexity impact on performance
        simple_sessions = [s for s in successful_sessions if s.user_profile.analysis_complexity_preference == "simple"]
        complex_sessions = [s for s in successful_sessions if s.user_profile.analysis_complexity_preference == "complex"]
        
        if simple_sessions and complex_sessions:
            simple_avg_time = statistics.mean([s.average_response_time_ms for s in simple_sessions])
            complex_avg_time = statistics.mean([s.average_response_time_ms for s in complex_sessions])
            
            # Complex analysis should take longer but not excessively
            assert complex_avg_time >= simple_avg_time, "Complex analysis should take longer than simple analysis"
            assert complex_avg_time <= simple_avg_time * 5, f"Complex analysis too slow compared to simple: {complex_avg_time/simple_avg_time:.1f}x"
        
        logger.info(f"Mixed complexity test: {len(successful_sessions)} sessions completed")
    
    @pytest.mark.slow
    async def test_long_duration_user_sessions(self, async_test_client: AsyncClient):
        """Test long-duration user sessions for endurance testing."""
        executor = ConcurrentUserTestExecutor(async_test_client)
        
        # Simulate longer research sessions
        result = await executor.execute_concurrent_user_test(
            user_count=10,  # Fewer users for longer duration
            test_duration_minutes=45,  # Longer test duration
            user_distribution={
                "researcher": 0.5,   # More researchers for long sessions
                "architect": 0.3,
                "developer": 0.2
            }
        )
        
        # Long sessions should complete successfully
        assert result.completed_sessions >= 8, f"Too many long sessions failed: {result.completed_sessions}/10"
        
        # Performance should remain stable over long duration
        assert result.error_rate_percent <= 5.0, f"Error rate too high for long sessions: {result.error_rate_percent}%"
        
        # Long sessions should show patience (longer think times)
        avg_think_time = result.think_time_stats["avg_think_time_seconds"]
        assert avg_think_time >= 5.0, f"Think time too low for long sessions: {avg_think_time}s"
        
        # Session durations should be substantial
        avg_session_duration = result.session_duration_stats["avg_duration_seconds"]
        assert avg_session_duration >= 1200, f"Average session duration too short for long test: {avg_session_duration/60:.1f} minutes"
        
        logger.info(f"Long duration test: {avg_session_duration/60:.1f} min avg sessions, {result.average_user_satisfaction:.1f}/100 satisfaction")