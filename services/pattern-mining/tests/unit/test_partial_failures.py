"""
Partial Service Failures Recovery Tests

Tests for partial service failure scenarios and recovery mechanisms.
Validates that the Pattern Mining service handles intermittent service availability,
performance degradation, data inconsistency, and split-brain scenarios gracefully.

Key Test Categories:
1. Intermittent Service Failures - Services going up/down randomly
2. Performance Degradation - Slow responses vs complete failures
3. Partial Functionality - Some endpoints working, others failing
4. Data Inconsistency - Partial writes and recovery scenarios
5. Split-Brain Scenarios - Service cluster consistency issues
6. Network Partition Recovery - Coordinated recovery after partitions
"""

import asyncio
import pytest
import time
import random
import json
import hashlib
from typing import Dict, Any, List, Optional, Tuple, Callable
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum

# Pattern mining imports
from pattern_mining.api.main import create_app
from pattern_mining.config.settings import get_settings
from pattern_mining.database.connection import DatabaseConnection
from pattern_mining.cache.redis_client import RedisClient
from pattern_mining.ml.gemini_client import Gemini<PERSON>lient
from pattern_mining.models.api import PatternDetectionRequest, PatternDetectionResponse
from pattern_mining.health.health_checker import HealthChecker

# Test utilities
from tests.utils.failure_injection import (
    NetworkFailureSimulator, ServiceFailureOrchestrator, FailureScenario,
    FailureType, FailurePattern, ResourceUsageSnapshot
)
from tests.utils.recovery_validation import (
    RecoveryValidator, RecoveryStage, StateConsistencyValidator,
    ConsistencyLevel, DataIntegrityCheckpoint, validate_service_recovery
)
from tests.utils.assertions import assert_error_response, assert_service_degraded
from tests.utils.generators import generate_large_code_sample


class ServiceState(Enum):
    """States of service availability."""
    FULLY_AVAILABLE = "fully_available"
    PARTIALLY_AVAILABLE = "partially_available"
    DEGRADED = "degraded"
    INTERMITTENT = "intermittent"
    UNAVAILABLE = "unavailable"


@dataclass
class ServiceAvailabilityPattern:
    """Pattern of service availability over time."""
    service_name: str
    states: List[Tuple[ServiceState, float]]  # (state, duration)
    total_duration: float
    failure_probability: float = 0.0
    recovery_probability: float = 0.8
    
    def get_current_state(self, elapsed_time: float) -> ServiceState:
        """Get current service state based on elapsed time."""
        current_time = 0.0
        for state, duration in self.states:
            if elapsed_time <= current_time + duration:
                return state
            current_time += duration
        
        # If we've exceeded the pattern, repeat it
        return self.states[0][0] if self.states else ServiceState.UNAVAILABLE


@pytest.mark.asyncio
class TestIntermittentServiceFailures:
    """Test intermittent service failure scenarios."""
    
    @pytest.fixture
    async def network_simulator(self):
        """Create network failure simulator."""
        return NetworkFailureSimulator()
    
    @pytest.fixture
    async def consistency_validator(self):
        """Create state consistency validator."""
        return StateConsistencyValidator()
    
    async def test_gemini_intermittent_availability(self, network_simulator, consistency_validator):
        """Test Gemini API intermittent availability and recovery patterns."""
        # Create service availability pattern
        availability_pattern = ServiceAvailabilityPattern(
            service_name="gemini",
            states=[
                (ServiceState.FULLY_AVAILABLE, 3.0),
                (ServiceState.UNAVAILABLE, 2.0),
                (ServiceState.DEGRADED, 2.0),
                (ServiceState.FULLY_AVAILABLE, 3.0)
            ],
            total_duration=10.0,
            failure_probability=0.3
        )
        
        # Track operation results
        operation_results = []
        start_time = time.time()
        
        async with network_simulator.intermittent_failures(failure_rate=0.3, recovery_pattern="exponential") as failure_config:
            intermittent_operation = failure_config["operation"]
            
            # Simulate multiple operations over time
            for i in range(15):
                elapsed = time.time() - start_time
                current_state = availability_pattern.get_current_state(elapsed)
                
                try:
                    if current_state == ServiceState.FULLY_AVAILABLE:
                        # Normal operation
                        result = await self._simulate_gemini_request(f"test code {i}", success_probability=0.9)
                    elif current_state == ServiceState.DEGRADED:
                        # Degraded operation - slower and less reliable
                        result = await self._simulate_gemini_request(f"test code {i}", success_probability=0.6, latency_ms=1000)
                    else:
                        # Intermittent failure
                        result = await intermittent_operation()
                    
                    operation_results.append({
                        "operation_id": i,
                        "elapsed_time": elapsed,
                        "service_state": current_state.value,
                        "result": result,
                        "success": True,
                        "timestamp": time.time()
                    })
                    
                except Exception as e:
                    operation_results.append({
                        "operation_id": i,
                        "elapsed_time": elapsed,
                        "service_state": current_state.value,
                        "error": str(e),
                        "success": False,
                        "timestamp": time.time()
                    })
                
                # Create consistency checkpoint periodically
                if i % 5 == 0:
                    checkpoint_data = {f"operation_{j}": res for j, res in enumerate(operation_results)}
                    consistency_validator.create_checkpoint(checkpoint_data, f"checkpoint_{i//5}")
                
                await asyncio.sleep(0.6)  # Operations every 600ms
        
        # Analyze results
        successful_operations = [r for r in operation_results if r["success"]]
        failed_operations = [r for r in operation_results if not r["success"]]
        
        success_rate = len(successful_operations) / len(operation_results)
        
        print(f"Intermittent failure test: {len(successful_operations)}/{len(operation_results)} succeeded "
              f"({success_rate:.1%} success rate)")
        
        # Should have some successes despite intermittent failures  
        assert success_rate > 0.4, f"Success rate too low: {success_rate:.1%}"
        
        # Should have some failures to confirm intermittent behavior
        assert len(failed_operations) > 0, "No failures detected - intermittent behavior not working"
        
        # Validate final consistency
        final_checkpoint_data = {f"operation_{i}": res for i, res in enumerate(operation_results)}
        final_checkpoint = consistency_validator.create_checkpoint(final_checkpoint_data, "final_checkpoint")
        
        assert final_checkpoint.record_count == len(operation_results)
    
    async def test_database_intermittent_connectivity(self, consistency_validator):
        """Test database intermittent connectivity and transaction recovery."""
        connection_attempts = []
        transaction_results = []
        
        # Simulate intermittent database connectivity
        async def simulate_db_operations():
            for i in range(12):
                connection_success = random.random() > 0.3  # 70% success rate
                
                try:
                    if connection_success:
                        # Successful database operation
                        async with self._mock_database_session() as session:
                            # Simulate transaction
                            transaction_data = {
                                "operation_id": i,
                                "data": f"test_data_{i}",
                                "timestamp": datetime.now().isoformat(),
                                "status": "committed"
                            }
                            
                            # Create checkpoint before transaction
                            pre_tx_checkpoint = consistency_validator.create_checkpoint(
                                transaction_data, f"pre_tx_{i}", ConsistencyLevel.STRICT
                            )
                            
                            # Simulate transaction processing
                            await asyncio.sleep(0.1)
                            
                            # Validate post-transaction consistency
                            post_tx_validation = consistency_validator.validate_checkpoint(
                                transaction_data, f"pre_tx_{i}"
                            )
                            
                            transaction_results.append({
                                "transaction_id": i,
                                "success": True,
                                "data": transaction_data,
                                "consistency_valid": post_tx_validation["is_consistent"]
                            })
                    else:
                        # Connection failure
                        raise Exception(f"Database connection failed for operation {i}")
                
                except Exception as e:
                    transaction_results.append({
                        "transaction_id": i,
                        "success": False,
                        "error": str(e),
                        "consistency_valid": False
                    })
                
                connection_attempts.append({
                    "attempt_id": i,
                    "success": connection_success,
                    "timestamp": time.time()
                })
                
                await asyncio.sleep(0.3)
        
        await simulate_db_operations()
        
        # Analyze database operation results
        successful_transactions = [r for r in transaction_results if r["success"]]
        failed_transactions = [r for r in transaction_results if not r["success"]]
        consistent_transactions = [r for r in successful_transactions if r["consistency_valid"]]
        
        success_rate = len(successful_transactions) / len(transaction_results)
        consistency_rate = len(consistent_transactions) / len(successful_transactions) if successful_transactions else 0
        
        print(f"Database intermittent test: {len(successful_transactions)}/{len(transaction_results)} "
              f"transactions succeeded ({success_rate:.1%})")
        print(f"Consistency rate: {len(consistent_transactions)}/{len(successful_transactions)} "
              f"({consistency_rate:.1%})")
        
        # Should have reasonable success rate with intermittent failures
        assert success_rate > 0.5, f"Database success rate too low: {success_rate:.1%}"
        
        # Successful transactions should maintain consistency
        assert consistency_rate > 0.9, f"Consistency rate too low: {consistency_rate:.1%}"
        
        # Should have some failures to confirm intermittent behavior
        assert len(failed_transactions) > 0, "No failures detected in database operations"
    
    async def test_redis_cache_intermittent_availability(self):
        """Test Redis cache intermittent availability and fallback behavior."""
        cache_operations = []
        fallback_usage = []
        
        # Simulate cache operations with intermittent Redis availability
        for i in range(20):
            cache_available = random.random() > 0.4  # 60% availability
            
            try:
                if cache_available:
                    # Cache hit/miss simulation
                    cache_hit = random.random() > 0.3  # 70% hit rate
                    
                    if cache_hit:
                        # Cache hit
                        cached_result = {
                            "patterns": [f"cached_pattern_{i}"],
                            "confidence": 0.85,
                            "source": "cache",
                            "cache_key": f"pattern_cache_{i}"
                        }
                        
                        cache_operations.append({
                            "operation_id": i,
                            "cache_available": True,
                            "cache_hit": True,
                            "result": cached_result,
                            "fallback_used": False,
                            "response_time_ms": 50
                        })
                    else:
                        # Cache miss - compute and store
                        computed_result = await self._simulate_pattern_computation(f"code_{i}")
                        
                        # Simulate caching the result
                        cache_operations.append({
                            "operation_id": i,
                            "cache_available": True,
                            "cache_hit": False,
                            "result": computed_result,
                            "fallback_used": False,
                            "response_time_ms": 200
                        })
                else:
                    # Cache unavailable - fallback to direct computation
                    fallback_result = await self._simulate_pattern_computation(f"code_{i}")
                    fallback_result["source"] = "fallback"
                    
                    cache_operations.append({
                        "operation_id": i,
                        "cache_available": False,
                        "cache_hit": False,
                        "result": fallback_result,
                        "fallback_used": True,
                        "response_time_ms": 300
                    })
                    
                    fallback_usage.append(i)
            
            except Exception as e:
                cache_operations.append({
                    "operation_id": i,
                    "cache_available": False,
                    "error": str(e),
                    "fallback_used": True,
                    "response_time_ms": 1000
                })
            
            await asyncio.sleep(0.1)
        
        # Analyze cache behavior
        cache_available_ops = [op for op in cache_operations if op.get("cache_available", False)]
        cache_hits = [op for op in cache_available_ops if op.get("cache_hit", False)]
        fallback_ops = [op for op in cache_operations if op.get("fallback_used", False)]
        
        availability_rate = len(cache_available_ops) / len(cache_operations)
        hit_rate = len(cache_hits) / len(cache_available_ops) if cache_available_ops else 0
        fallback_rate = len(fallback_ops) / len(cache_operations)
        
        print(f"Cache availability: {len(cache_available_ops)}/{len(cache_operations)} ({availability_rate:.1%})")
        print(f"Cache hit rate: {len(cache_hits)}/{len(cache_available_ops)} ({hit_rate:.1%})")
        print(f"Fallback usage: {len(fallback_ops)}/{len(cache_operations)} ({fallback_rate:.1%})")
        
        # Should have reasonable availability
        assert availability_rate > 0.4, f"Cache availability too low: {availability_rate:.1%}"
        
        # Should have reasonable hit rate when available
        if cache_available_ops:
            assert hit_rate > 0.5, f"Cache hit rate too low: {hit_rate:.1%}"
        
        # Should use fallback when cache unavailable
        assert len(fallback_ops) > 0, "Fallback not used when cache unavailable"
        
        # All operations should complete (either cached or fallback)
        completed_ops = [op for op in cache_operations if "result" in op]
        completion_rate = len(completed_ops) / len(cache_operations)
        assert completion_rate > 0.9, f"Operation completion rate too low: {completion_rate:.1%}"
    
    async def _simulate_gemini_request(self, code: str, success_probability: float = 0.9, latency_ms: int = 200) -> Dict[str, Any]:
        """Simulate Gemini API request with configurable success rate and latency."""
        await asyncio.sleep(latency_ms / 1000.0)
        
        if random.random() < success_probability:
            return {
                "patterns": [{"type": "function", "confidence": 0.9}],
                "confidence": 0.85,
                "processing_time_ms": latency_ms,
                "source": "gemini"
            }
        else:
            raise Exception("Gemini API request failed")
    
    async def _mock_database_session(self):
        """Mock database session context manager."""
        class MockSession:
            async def __aenter__(self):
                return self
            
            async def __aexit__(self, exc_type, exc_val, exc_tb):
                pass
        
        return MockSession()
    
    async def _simulate_pattern_computation(self, code: str) -> Dict[str, Any]:
        """Simulate pattern computation without cache."""
        await asyncio.sleep(0.15)  # Simulation processing time
        
        return {
            "patterns": [{"type": "computed", "confidence": 0.8}],
            "confidence": 0.8,
            "processing_time_ms": 150,
            "source": "computed"
        }


@pytest.mark.asyncio
class TestPerformanceDegradation:
    """Test performance degradation scenarios."""
    
    async def test_progressive_performance_degradation(self):
        """Test progressive performance degradation and recovery."""
        performance_metrics = []
        
        # Simulate progressive degradation over time
        for i in range(20):
            # Performance degrades over time, then recovers
            if i < 5:
                # Normal performance
                base_latency = 100
                degradation_factor = 1.0
            elif i < 12:
                # Progressive degradation
                degradation_factor = 1.0 + (i - 5) * 0.3  # Up to 3.1x slower
                base_latency = 100
            else:
                # Recovery phase
                recovery_progress = (i - 12) / 8.0  # Recovery over 8 steps
                degradation_factor = 3.1 - (2.1 * recovery_progress)  # Back to 1.0x
                base_latency = 100
            
            # Simulate operation with degraded performance
            actual_latency = base_latency * degradation_factor
            
            start_time = time.time()
            await asyncio.sleep(actual_latency / 1000.0)  # Convert to seconds
            end_time = time.time()
            
            measured_latency = (end_time - start_time) * 1000  # Convert to ms
            
            # Simulate pattern detection result
            result = await self._simulate_degraded_pattern_detection(degradation_factor)
            
            performance_metrics.append({
                "operation_id": i,
                "expected_latency_ms": actual_latency,
                "measured_latency_ms": measured_latency,
                "degradation_factor": degradation_factor,
                "patterns_found": len(result.get("patterns", [])),
                "quality_score": result.get("confidence", 0.0),
                "timestamp": time.time()
            })
            
            print(f"Operation {i}: {degradation_factor:.1f}x degradation, "
                  f"{measured_latency:.0f}ms latency, "
                  f"{result.get('confidence', 0.0):.2f} quality")
        
        # Analyze performance trend
        degradation_phase = [m for m in performance_metrics if 5 <= m["operation_id"] < 12]
        recovery_phase = [m for m in performance_metrics if m["operation_id"] >= 12]
        
        # Verify degradation was detected
        max_degradation = max(m["degradation_factor"] for m in degradation_phase)
        assert max_degradation > 2.0, f"Insufficient performance degradation: {max_degradation:.1f}x"
        
        # Verify recovery occurred
        if recovery_phase:
            final_degradation = recovery_phase[-1]["degradation_factor"]
            assert final_degradation < 1.5, f"Performance did not recover: {final_degradation:.1f}x"
        
        # Verify quality maintained reasonable levels during degradation
        avg_quality_degraded = sum(m["quality_score"] for m in degradation_phase) / len(degradation_phase)
        assert avg_quality_degraded > 0.6, f"Quality too low during degradation: {avg_quality_degraded:.2f}"
    
    async def test_latency_spike_handling(self):
        """Test handling of sudden latency spikes."""
        latency_scenarios = [
            {"name": "normal", "latency_ms": 100, "duration": 3},
            {"name": "spike_1", "latency_ms": 2000, "duration": 2},
            {"name": "recovery_1", "latency_ms": 300, "duration": 2},
            {"name": "spike_2", "latency_ms": 5000, "duration": 1},
            {"name": "recovery_2", "latency_ms": 150, "duration": 3}
        ]
        
        operation_results = []
        timeout_threshold = 3000  # 3 second timeout
        
        for scenario in latency_scenarios:
            for i in range(scenario["duration"]):
                operation_start = time.time()
                
                try:
                    # Simulate operation with specified latency
                    if scenario["latency_ms"] > timeout_threshold:
                        # Should timeout
                        await asyncio.wait_for(
                            self._simulate_slow_operation(scenario["latency_ms"]),
                            timeout=timeout_threshold / 1000.0
                        )
                        operation_result = {"status": "completed", "latency_ms": scenario["latency_ms"]}
                    else:
                        # Should complete normally
                        operation_result = await self._simulate_slow_operation(scenario["latency_ms"])
                    
                    operation_end = time.time()
                    actual_duration = (operation_end - operation_start) * 1000
                    
                    operation_results.append({
                        "scenario": scenario["name"],
                        "expected_latency_ms": scenario["latency_ms"],
                        "actual_duration_ms": actual_duration,
                        "result": operation_result,
                        "success": True,
                        "timed_out": False
                    })
                
                except asyncio.TimeoutError:
                    operation_end = time.time()
                    actual_duration = (operation_end - operation_start) * 1000
                    
                    operation_results.append({
                        "scenario": scenario["name"],
                        "expected_latency_ms": scenario["latency_ms"],
                        "actual_duration_ms": actual_duration,
                        "success": False,
                        "timed_out": True,
                        "error": "Operation timed out"
                    })
                
                await asyncio.sleep(0.1)  # Brief pause between operations
        
        # Analyze timeout behavior
        normal_ops = [r for r in operation_results if r["scenario"] == "normal"]
        spike_ops = [r for r in operation_results if "spike" in r["scenario"]]
        recovery_ops = [r for r in operation_results if "recovery" in r["scenario"]]
        
        # Normal operations should succeed
        normal_success_rate = sum(1 for r in normal_ops if r["success"]) / len(normal_ops)
        assert normal_success_rate == 1.0, f"Normal operations failed: {normal_success_rate:.1%}"
        
        # Spike operations should timeout appropriately
        spike_timeout_rate = sum(1 for r in spike_ops if r["timed_out"]) / len(spike_ops)
        print(f"Spike timeout rate: {spike_timeout_rate:.1%}")
        
        # At least some spike operations should timeout for protection
        assert spike_timeout_rate > 0.5, f"Insufficient timeout protection: {spike_timeout_rate:.1%}"
        
        # Recovery operations should succeed
        recovery_success_rate = sum(1 for r in recovery_ops if r["success"]) / len(recovery_ops)
        assert recovery_success_rate > 0.8, f"Recovery success rate too low: {recovery_success_rate:.1%}"
    
    async def test_throughput_degradation_handling(self):
        """Test handling of throughput degradation."""
        # Simulate varying system load and throughput
        load_scenarios = [
            {"load_level": "low", "concurrent_ops": 2, "success_rate": 0.95},
            {"load_level": "medium", "concurrent_ops": 5, "success_rate": 0.90},
            {"load_level": "high", "concurrent_ops": 10, "success_rate": 0.80},
            {"load_level": "overload", "concurrent_ops": 15, "success_rate": 0.60}
        ]
        
        throughput_results = []
        
        for scenario in load_scenarios:
            scenario_start = time.time()
            
            # Launch concurrent operations
            async def operation_with_load(op_id: int):
                try:
                    # Simulate processing under load
                    processing_time = 0.2 + (scenario["concurrent_ops"] * 0.05)  # Increases with load
                    await asyncio.sleep(processing_time)
                    
                    # Simulate occasional failures under high load
                    if random.random() < scenario["success_rate"]:
                        return {"op_id": op_id, "status": "success", "processing_time": processing_time}
                    else:
                        raise Exception(f"Operation failed under {scenario['load_level']} load")
                
                except Exception as e:
                    return {"op_id": op_id, "status": "failed", "error": str(e)}
            
            # Run concurrent operations
            tasks = [operation_with_load(i) for i in range(scenario["concurrent_ops"])]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            scenario_end = time.time()
            scenario_duration = scenario_end - scenario_start
            
            # Analyze results
            successful_ops = [r for r in results if isinstance(r, dict) and r.get("status") == "success"]
            failed_ops = [r for r in results if isinstance(r, dict) and r.get("status") == "failed"]
            exception_ops = [r for r in results if isinstance(r, Exception)]
            
            actual_success_rate = len(successful_ops) / len(results)
            throughput = len(successful_ops) / scenario_duration  # Operations per second
            
            throughput_results.append({
                "load_level": scenario["load_level"],
                "concurrent_ops": scenario["concurrent_ops"],
                "expected_success_rate": scenario["success_rate"],
                "actual_success_rate": actual_success_rate,
                "throughput_ops_per_sec": throughput,
                "scenario_duration": scenario_duration,
                "successful_operations": len(successful_ops),
                "failed_operations": len(failed_ops) + len(exception_ops)
            })
            
            print(f"Load {scenario['load_level']}: {actual_success_rate:.1%} success rate, "
                  f"{throughput:.1f} ops/sec throughput")
        
        # Verify throughput degradation handling
        low_load_result = next(r for r in throughput_results if r["load_level"] == "low")
        high_load_result = next(r for r in throughput_results if r["load_level"] == "high")
        overload_result = next(r for r in throughput_results if r["load_level"] == "overload")
        
        # Success rate should degrade gracefully under load
        assert low_load_result["actual_success_rate"] > 0.9, "Low load success rate too low"
        assert high_load_result["actual_success_rate"] > 0.7, "High load success rate too low"
        
        # System should still process some operations even under overload
        assert overload_result["actual_success_rate"] > 0.3, "System completely failed under overload"
        assert overload_result["successful_operations"] > 0, "No operations succeeded under overload"
    
    async def _simulate_degraded_pattern_detection(self, degradation_factor: float) -> Dict[str, Any]:
        """Simulate pattern detection with performance degradation."""
        # Quality decreases with performance degradation
        base_confidence = 0.9
        degraded_confidence = max(0.5, base_confidence - (degradation_factor - 1.0) * 0.1)
        
        # Number of patterns found may decrease under degradation
        base_patterns = 5
        degraded_patterns = max(1, int(base_patterns / degradation_factor))
        
        return {
            "patterns": [f"pattern_{i}" for i in range(degraded_patterns)],
            "confidence": degraded_confidence,
            "degradation_factor": degradation_factor,
            "processing_status": "degraded" if degradation_factor > 1.5 else "normal"
        }
    
    async def _simulate_slow_operation(self, latency_ms: int) -> Dict[str, Any]:
        """Simulate operation with specified latency."""
        await asyncio.sleep(latency_ms / 1000.0)
        return {
            "status": "completed",
            "latency_ms": latency_ms,
            "result": "operation_success"
        }


@pytest.mark.asyncio
class TestPartialFunctionalityFailures:
    """Test partial functionality failure scenarios."""
    
    async def test_selective_endpoint_failures(self):
        """Test scenarios where some API endpoints fail while others work."""
        # Define endpoint availability patterns
        endpoint_patterns = {
            "/health": {"availability": 0.95, "degradation": 1.0},
            "/api/v1/patterns/detect": {"availability": 0.8, "degradation": 1.5},
            "/api/v1/patterns/batch": {"availability": 0.6, "degradation": 2.0},
            "/api/v1/analysis/detailed": {"availability": 0.4, "degradation": 3.0},
            "/api/v1/metrics": {"availability": 0.9, "degradation": 1.2}
        }
        
        endpoint_results = {}
        
        # Test each endpoint multiple times
        for endpoint, pattern in endpoint_patterns.items():
            endpoint_results[endpoint] = []
            
            for i in range(15):
                try:
                    # Determine if endpoint is available
                    is_available = random.random() < pattern["availability"]
                    
                    if is_available:
                        # Simulate endpoint operation with potential degradation
                        response_time = 100 * pattern["degradation"]
                        await asyncio.sleep(response_time / 1000.0)
                        
                        result = {
                            "endpoint": endpoint,
                            "request_id": i,
                            "status_code": 200,
                            "response_time_ms": response_time,
                            "success": True,
                            "degraded": pattern["degradation"] > 1.5
                        }
                    else:
                        # Endpoint unavailable
                        raise Exception(f"Endpoint {endpoint} temporarily unavailable")
                
                except Exception as e:
                    result = {
                        "endpoint": endpoint,
                        "request_id": i,
                        "status_code": 503,
                        "error": str(e),
                        "success": False
                    }
                
                endpoint_results[endpoint].append(result)
                await asyncio.sleep(0.1)
        
        # Analyze endpoint availability
        for endpoint, results in endpoint_results.items():
            successful_requests = [r for r in results if r["success"]]
            failed_requests = [r for r in results if not r["success"]]
            
            success_rate = len(successful_requests) / len(results)
            expected_rate = endpoint_patterns[endpoint]["availability"]
            
            print(f"Endpoint {endpoint}: {success_rate:.1%} success rate "
                  f"(expected {expected_rate:.1%})")
            
            # Success rate should be reasonably close to expected
            assert abs(success_rate - expected_rate) < 0.3, \
                f"Endpoint {endpoint} success rate {success_rate:.1%} far from expected {expected_rate:.1%}"
        
        # Critical endpoints should have higher availability
        health_success_rate = len([r for r in endpoint_results["/health"] if r["success"]]) / 15
        assert health_success_rate > 0.8, f"Health endpoint too unreliable: {health_success_rate:.1%}"
        
        # System should gracefully handle partial endpoint failures
        total_requests = sum(len(results) for results in endpoint_results.values())
        total_successes = sum(len([r for r in results if r["success"]]) for results in endpoint_results.values())
        overall_success_rate = total_successes / total_requests
        
        assert overall_success_rate > 0.5, f"Overall system success rate too low: {overall_success_rate:.1%}"
    
    async def test_feature_specific_failures(self):
        """Test failures in specific features while core functionality works."""
        # Define feature availability
        features = {
            "basic_pattern_detection": {"available": True, "performance": 1.0},
            "ml_inference": {"available": False, "performance": 0.0},  # ML service down
            "advanced_analysis": {"available": False, "performance": 0.0},  # Advanced features down
            "caching": {"available": True, "performance": 0.8},  # Cache degraded
            "batch_processing": {"available": True, "performance": 1.2},  # Slightly slower
            "detailed_metrics": {"available": False, "performance": 0.0}  # Metrics service down
        }
        
        # Test requests with different feature requirements
        test_requests = [
            {
                "name": "basic_request",
                "required_features": ["basic_pattern_detection"],
                "optional_features": ["caching"]
            },
            {
                "name": "ml_request",
                "required_features": ["basic_pattern_detection", "ml_inference"],
                "optional_features": ["caching", "detailed_metrics"]
            },
            {
                "name": "advanced_request",
                "required_features": ["basic_pattern_detection", "advanced_analysis"],
                "optional_features": ["ml_inference", "detailed_metrics"]
            },
            {
                "name": "batch_request",
                "required_features": ["basic_pattern_detection", "batch_processing"],
                "optional_features": ["caching", "ml_inference"]
            }
        ]
        
        request_results = []
        
        for request_config in test_requests:
            for i in range(8):
                try:
                    # Check if all required features are available
                    required_available = all(
                        features[feature]["available"] 
                        for feature in request_config["required_features"]
                    )
                    
                    if required_available:
                        # Calculate overall performance impact
                        performance_factors = [
                            features[feature]["performance"] 
                            for feature in request_config["required_features"]
                            if features[feature]["available"]
                        ]
                        
                        avg_performance = sum(performance_factors) / len(performance_factors) if performance_factors else 1.0
                        response_time = 200 * avg_performance  # Base 200ms
                        
                        await asyncio.sleep(response_time / 1000.0)
                        
                        # Determine available optional features
                        available_optional = [
                            feature for feature in request_config["optional_features"]
                            if features[feature]["available"]
                        ]
                        
                        result = {
                            "request_type": request_config["name"],
                            "request_id": i,
                            "success": True,
                            "response_time_ms": response_time,
                            "features_used": request_config["required_features"] + available_optional,
                            "features_unavailable": [
                                feature for feature in request_config["optional_features"]
                                if not features[feature]["available"]
                            ],
                            "degraded": avg_performance > 1.1
                        }
                    else:
                        # Required features unavailable - request fails
                        unavailable_required = [
                            feature for feature in request_config["required_features"]
                            if not features[feature]["available"]
                        ]
                        
                        raise Exception(f"Required features unavailable: {unavailable_required}")
                
                except Exception as e:
                    result = {
                        "request_type": request_config["name"],
                        "request_id": i,
                        "success": False,
                        "error": str(e)
                    }
                
                request_results.append(result)
                await asyncio.sleep(0.1)
        
        # Analyze results by request type
        for request_config in test_requests:
            request_type = request_config["name"]
            type_results = [r for r in request_results if r["request_type"] == request_type]
            successful_results = [r for r in type_results if r["success"]]
            
            success_rate = len(successful_results) / len(type_results)
            
            print(f"Request type {request_type}: {success_rate:.1%} success rate")
            
            # Analyze expected behavior based on feature availability
            required_features = request_config["required_features"]
            all_required_available = all(features[f]["available"] for f in required_features)
            
            if all_required_available:
                # Should succeed since required features are available
                assert success_rate > 0.8, f"Request type {request_type} should succeed with available features"
            else:
                # Should fail since required features are unavailable
                assert success_rate < 0.2, f"Request type {request_type} should fail with unavailable features"
        
        # Basic requests should always work
        basic_results = [r for r in request_results if r["request_type"] == "basic_request"]
        basic_success_rate = len([r for r in basic_results if r["success"]]) / len(basic_results)
        assert basic_success_rate > 0.9, f"Basic requests should work: {basic_success_rate:.1%}"
    
    async def test_data_consistency_with_partial_writes(self, consistency_validator):
        """Test data consistency when some write operations fail."""
        # Simulate multi-step data operations with partial failures
        batch_operations = []
        
        for batch_id in range(5):
            batch_data = {
                f"record_{i}": {
                    "id": f"batch_{batch_id}_record_{i}",
                    "data": f"test_data_{batch_id}_{i}",
                    "timestamp": datetime.now().isoformat(),
                    "batch_id": batch_id
                }
                for i in range(8)  # 8 records per batch
            }
            
            # Create checkpoint before batch operation
            pre_batch_checkpoint = consistency_validator.create_checkpoint(
                batch_data, f"pre_batch_{batch_id}", ConsistencyLevel.EVENTUAL
            )
            
            # Simulate batch write with partial failures
            successful_writes = {}
            failed_writes = {}
            
            for record_id, record_data in batch_data.items():
                # Simulate write success/failure (70% success rate)
                write_success = random.random() < 0.7
                
                if write_success:
                    # Simulate successful write
                    await asyncio.sleep(0.02)  # Write latency
                    successful_writes[record_id] = record_data
                else:
                    # Write failure
                    failed_writes[record_id] = {
                        "error": "Write operation failed",
                        "original_data": record_data
                    }
            
            # Check partial write consistency
            partial_data_checkpoint = consistency_validator.create_checkpoint(
                successful_writes, f"partial_batch_{batch_id}", ConsistencyLevel.EVENTUAL
            )
            
            batch_operations.append({
                "batch_id": batch_id,
                "total_records": len(batch_data),
                "successful_writes": len(successful_writes),
                "failed_writes": len(failed_writes),
                "success_rate": len(successful_writes) / len(batch_data),
                "pre_checkpoint": pre_batch_checkpoint,
                "partial_checkpoint": partial_data_checkpoint,
                "consistent_partial_write": len(successful_writes) > 0  # At least some data written
            })
            
            print(f"Batch {batch_id}: {len(successful_writes)}/{len(batch_data)} writes succeeded")
        
        # Analyze batch consistency
        successful_batches = [b for b in batch_operations if b["success_rate"] > 0]
        completely_failed_batches = [b for b in batch_operations if b["success_rate"] == 0]
        partial_batches = [b for b in batch_operations if 0 < b["success_rate"] < 1.0]
        
        # Should have some successful partial writes
        assert len(successful_batches) > 0, "No batches had any successful writes"
        
        # Partial writes should maintain consistency
        for batch in partial_batches:
            assert batch["consistent_partial_write"], f"Batch {batch['batch_id']} partial write inconsistent"
        
        # Validate final state consistency
        all_successful_data = {}
        for batch in batch_operations:
            if batch["success_rate"] > 0:
                # Add successful writes to final dataset
                for i in range(batch["successful_writes"]):
                    record_key = f"batch_{batch['batch_id']}_record_{i}"
                    all_successful_data[record_key] = {
                        "batch_id": batch["batch_id"],
                        "record_index": i,
                        "status": "written"
                    }
        
        final_consistency_check = consistency_validator.validate_checkpoint(
            all_successful_data, "pre_batch_0"  # Compare against first batch structure
        )
        
        # Final state should be eventually consistent
        print(f"Final consistency check: {final_consistency_check['consistency_level']}")
        print(f"Partial write batches: {len(partial_batches)}/{len(batch_operations)}")


if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"])