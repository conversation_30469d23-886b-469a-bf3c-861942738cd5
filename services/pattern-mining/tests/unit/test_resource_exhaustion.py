"""
Resource Exhaustion Recovery Tests

Tests for resource exhaustion scenarios and recovery mechanisms.
Validates that the Pattern Mining service handles resource pressure gracefully
and recovers properly when resources become available again.

Key Test Categories:
1. Memory Pressure & Recovery - Progressive memory exhaustion, GC recovery
2. Connection Pool Exhaustion - Database/Redis connection recovery  
3. Thread Pool Saturation - Worker thread exhaustion and recovery
4. Disk Space Exhaustion - Temporary file cleanup and recovery
5. File Descriptor Limits - Handle limit exhaustion and cleanup
6. Resource Cleanup Validation - Ensure proper resource cleanup after pressure
"""

import asyncio
import pytest
import time
import psutil
import tempfile
import os
import gc
import threading
import resource
from typing import Dict, Any, List, Optional
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime, timedelta

# Pattern mining imports
from pattern_mining.api.main import create_app
from pattern_mining.config.settings import get_settings
from pattern_mining.database.connection import DatabaseConnection, get_database_session
from pattern_mining.cache.redis_client import RedisClient, get_redis_client
from pattern_mining.ml.gemini_client import Gemini<PERSON><PERSON>
from pattern_mining.models.api import PatternDetectionRequest
from pattern_mining.health.health_checker import HealthChecker

# Test utilities
from tests.utils.failure_injection import (
    MemoryPressureInjector, ConnectionPoolExhauster, ThreadPoolSaturator,
    DiskSpaceExhauster, FailureScenario, FailureType, FailurePattern,
    ServiceFailureOrchestrator, ResourceUsageSnapshot
)
from tests.utils.recovery_validation import (
    RecoveryValidator, RecoveryStage, validate_service_recovery,
    ConsistencyLevel, PerformanceMonitor
)
from tests.utils.assertions import assert_cleanup_completed, get_current_resource_usage
from tests.utils.generators import generate_large_code_sample


@pytest.mark.asyncio
class TestMemoryPressureRecovery:
    """Test memory pressure scenarios and recovery."""
    
    @pytest.fixture
    async def memory_injector(self):
        """Create memory pressure injector."""
        return MemoryPressureInjector()
    
    @pytest.fixture
    async def recovery_validator(self):
        """Create recovery validator."""
        return RecoveryValidator()
    
    async def test_progressive_memory_exhaustion_recovery(self, memory_injector, recovery_validator):
        """Test progressive memory exhaustion and cleanup recovery."""
        initial_resources = get_current_resource_usage()
        
        # Define service operation for baseline
        async def service_operation():
            # Simulate pattern detection under memory pressure
            request_data = PatternDetectionRequest(
                code="def test(): return 'small_test'",
                language="python",
                detection_types=["heuristic"]
            )
            return {"patterns": [], "processing_time_ms": 100}
        
        async with recovery_validator.validate_recovery(baseline_operation=service_operation):
            recovery_validator.mark_stage_complete(RecoveryStage.FAILURE_DETECTED)
            
            # Apply progressive memory pressure
            async with memory_injector.memory_pressure(target_mb=400, progressive=True) as pressure_result:
                recovery_validator.mark_stage_complete(RecoveryStage.SERVICES_RESTARTING)
                
                # Verify memory pressure is applied
                current_memory = psutil.Process().memory_info().rss / 1024 / 1024
                assert pressure_result["allocated_mb"] > 300, f"Insufficient memory pressure: {pressure_result['allocated_mb']}MB"
                
                # Test service operation under pressure
                start_time = time.time()
                result = await service_operation()
                end_time = time.time()
                
                # Service should still work but may be slower
                assert result is not None
                response_time = (end_time - start_time) * 1000
                assert response_time < 5000, f"Service too slow under memory pressure: {response_time}ms"
                
                recovery_validator.mark_stage_complete(RecoveryStage.CONNECTIONS_RESTORED)
                
                # Wait for potential memory stabilization
                await asyncio.sleep(2.0)
                
                recovery_validator.mark_stage_complete(RecoveryStage.PERFORMANCE_RESTORED)
            
            # Memory should be cleaned up after context exit
            recovery_validator.mark_stage_complete(RecoveryStage.FULLY_OPERATIONAL)
            
            # Allow GC time to work
            await asyncio.sleep(1.0)
            gc.collect()
            await asyncio.sleep(0.5)
        
        # Validate recovery quality
        recovery_metrics = recovery_validator.get_comprehensive_metrics()
        
        # Memory should be mostly recovered
        final_resources = get_current_resource_usage()
        assert_cleanup_completed(initial_resources, final_resources, tolerance={"memory_mb": 50.0})
        
        # Recovery should be timely
        assert recovery_metrics.total_recovery_time < 20.0, f"Recovery too slow: {recovery_metrics.total_recovery_time}s"
        
        # Performance should recover
        assert recovery_metrics.performance_recovery_ratio > 0.7, "Performance did not recover adequately"
    
    async def test_sudden_memory_exhaustion_recovery(self, memory_injector):
        """Test sudden memory exhaustion and emergency recovery."""
        initial_memory = psutil.Process().memory_info().rss / 1024 / 1024
        
        # Apply sudden memory pressure
        async with memory_injector.memory_pressure(target_mb=600, progressive=False) as pressure_result:
            # Verify sudden memory allocation
            current_memory = psutil.Process().memory_info().rss / 1024 / 1024
            memory_increase = current_memory - initial_memory
            
            assert memory_increase > 400, f"Insufficient sudden memory pressure: {memory_increase}MB"
            
            # Test system behavior under sudden pressure
            try:
                # Should still be able to perform basic operations
                small_data = {"test": "data"}
                assert small_data["test"] == "data"
                
                # GC should be triggered automatically
                gc.collect()
                
                # Memory usage might reduce slightly after GC
                post_gc_memory = psutil.Process().memory_info().rss / 1024 / 1024
                gc_effect = current_memory - post_gc_memory
                
                # Log GC effectiveness
                print(f"GC reduced memory by {gc_effect:.1f}MB")
                
            except MemoryError:
                # This is acceptable for extreme memory pressure
                pytest.skip("System reached actual memory limits")
        
        # Verify memory cleanup after pressure release
        await asyncio.sleep(2.0)
        gc.collect()
        await asyncio.sleep(0.5)
        
        final_memory = psutil.Process().memory_info().rss / 1024 / 1024
        memory_recovered = current_memory - final_memory
        
        assert memory_recovered > 400, f"Memory not properly recovered: only {memory_recovered}MB cleaned up"
    
    async def test_memory_leak_detection_and_cleanup(self, memory_injector):
        """Test memory leak detection and cleanup mechanisms."""
        initial_resources = ResourceUsageSnapshot.capture()
        
        # Simulate memory leak scenario
        leaked_objects = []
        try:
            # Create objects that might leak
            for i in range(100):
                # Large objects that should be cleaned up
                large_obj = {
                    'data': bytearray(5 * 1024 * 1024),  # 5MB each
                    'id': i,
                    'timestamp': datetime.now(),
                    'references': [j for j in range(1000)]  # Additional memory usage
                }
                leaked_objects.append(large_obj)
                
                # Check memory growth every 10 objects
                if i % 10 == 0:
                    current_resources = ResourceUsageSnapshot.capture()
                    memory_growth = current_resources.memory_mb - initial_resources.memory_mb
                    
                    # If memory growth exceeds threshold, trigger cleanup
                    if memory_growth > 300:  # 300MB threshold
                        print(f"Memory leak detected at {memory_growth:.1f}MB growth")
                        
                        # Simulate cleanup mechanism
                        objects_to_clear = len(leaked_objects) // 2
                        for _ in range(objects_to_clear):
                            if leaked_objects:
                                leaked_objects.pop()
                        
                        # Force garbage collection
                        gc.collect()
                        await asyncio.sleep(0.1)
                        
                        # Verify cleanup effectiveness
                        post_cleanup_resources = ResourceUsageSnapshot.capture()
                        cleanup_effect = current_resources.memory_mb - post_cleanup_resources.memory_mb
                        
                        assert cleanup_effect > 50, f"Cleanup ineffective: only {cleanup_effect:.1f}MB freed"
                        break
        
        finally:
            # Final cleanup
            leaked_objects.clear()
            gc.collect()
            await asyncio.sleep(0.5)
        
        # Verify final cleanup
        final_resources = ResourceUsageSnapshot.capture()
        total_memory_change = final_resources.memory_mb - initial_resources.memory_mb
        
        # Should have cleaned up most memory
        assert abs(total_memory_change) < 100, f"Memory leak not fully cleaned: {total_memory_change:.1f}MB remaining"
    
    async def test_memory_pressure_service_degradation(self):
        """Test service degradation under memory pressure."""
        injector = MemoryPressureInjector()
        
        # Test service behavior under different memory pressure levels
        test_cases = [
            {"target_mb": 200, "expected_degradation": "minimal"},
            {"target_mb": 400, "expected_degradation": "moderate"},
            {"target_mb": 600, "expected_degradation": "significant"}
        ]
        
        for case in test_cases:
            async with injector.memory_pressure(target_mb=case["target_mb"], progressive=True):
                # Test basic service operation
                start_time = time.time()
                
                try:
                    # Simulate pattern detection
                    test_code = "def simple_function(): return 'test'"
                    result = await self._simulate_pattern_detection(test_code)
                    
                    end_time = time.time()
                    response_time = (end_time - start_time) * 1000
                    
                    # Service should work but may be degraded
                    assert result is not None, f"Service failed under {case['target_mb']}MB pressure"
                    
                    # Check degradation level
                    if case["expected_degradation"] == "minimal":
                        assert response_time < 1000, f"Unexpected degradation: {response_time}ms"
                    elif case["expected_degradation"] == "moderate":
                        assert response_time < 3000, f"Excessive degradation: {response_time}ms"
                    else:  # significant
                        assert response_time < 10000, f"Service timeout: {response_time}ms"
                    
                    print(f"Memory pressure {case['target_mb']}MB: {response_time:.1f}ms response time")
                
                except Exception as e:
                    if case["expected_degradation"] == "significant":
                        # Significant degradation may cause failures
                        print(f"Expected service failure under high memory pressure: {e}")
                    else:
                        raise
    
    async def _simulate_pattern_detection(self, code: str) -> Dict[str, Any]:
        """Simulate pattern detection operation."""
        # Simple simulation that uses some memory
        patterns = []
        
        # Simulate analysis work
        for i in range(10):
            pattern_data = {
                "type": f"pattern_{i}",
                "confidence": 0.8,
                "location": {"line": i, "column": 0},
                "code_snippet": code
            }
            patterns.append(pattern_data)
        
        return {
            "patterns": patterns,
            "processing_time_ms": 150,
            "memory_usage_mb": psutil.Process().memory_info().rss / 1024 / 1024
        }


@pytest.mark.asyncio
class TestConnectionPoolExhaustionRecovery:
    """Test connection pool exhaustion and recovery."""
    
    @pytest.fixture
    async def connection_exhauster(self):
        """Create connection pool exhauster."""
        return ConnectionPoolExhauster()
    
    async def test_database_connection_pool_exhaustion(self, connection_exhauster):
        """Test database connection pool exhaustion and recovery."""
        initial_resources = get_current_resource_usage()
        
        # Mock database operations
        mock_operations = []
        
        async with connection_exhauster.exhaust_pool(pool_size=8, connection_type="database") as pool_result:
            # Verify pool exhaustion
            assert pool_result["connections_created"] >= 8, "Connection pool not sufficiently exhausted"
            
            # Test database operations under pool exhaustion
            try:
                # First few operations might succeed with existing connections
                with patch('pattern_mining.database.connection.get_database_session') as mock_session:
                    mock_session.return_value.__aenter__ = AsyncMock()
                    mock_session.return_value.__aexit__ = AsyncMock()
                    
                    # Simulate multiple concurrent database operations
                    async def db_operation(op_id: int):
                        try:
                            async with mock_session() as session:
                                # Simulate query
                                await asyncio.sleep(0.1)
                                return f"operation_{op_id}_success"
                        except Exception as e:
                            return f"operation_{op_id}_failed: {str(e)}"
                    
                    # Run concurrent operations
                    tasks = [db_operation(i) for i in range(15)]  # More than pool size
                    results = await asyncio.gather(*tasks, return_exceptions=True)
                    
                    # Some operations should succeed, some might fail due to pool exhaustion
                    successful_ops = [r for r in results if isinstance(r, str) and "success" in r]
                    failed_ops = [r for r in results if isinstance(r, str) and "failed" in r]
                    
                    print(f"Database operations: {len(successful_ops)} succeeded, {len(failed_ops)} failed")
                    
                    # Should have at least some successful operations
                    assert len(successful_ops) > 0, "All database operations failed"
                    
            except Exception as e:
                # Pool exhaustion may cause various connection errors
                assert "connection" in str(e).lower() or "pool" in str(e).lower()
        
        # Verify cleanup after pool exhaustion ends
        await asyncio.sleep(1.0)
        final_resources = get_current_resource_usage()
        
        # Connection cleanup should not significantly affect other resources  
        assert_cleanup_completed(initial_resources, final_resources, 
                                tolerance={"memory_mb": 20.0, "threads": 3})
    
    async def test_redis_connection_pool_exhaustion(self, connection_exhauster):
        """Test Redis connection pool exhaustion and recovery."""
        async with connection_exhauster.exhaust_pool(pool_size=6, connection_type="redis") as pool_result:
            # Verify Redis pool exhaustion
            assert pool_result["connections_created"] >= 6, "Redis pool not sufficiently exhausted"
            
            # Test Redis operations under pool exhaustion
            with patch('pattern_mining.cache.redis_client.get_redis_client') as mock_redis:
                # Configure mock to simulate pool exhaustion
                mock_client = Mock()
                mock_client.ping = AsyncMock(side_effect=Exception("Connection pool exhausted"))
                mock_client.get = AsyncMock(side_effect=Exception("Connection pool exhausted"))
                mock_client.set = AsyncMock(side_effect=Exception("Connection pool exhausted"))
                mock_redis.return_value = mock_client
                
                # Test cache operations
                redis_client = RedisClient()
                
                # Operations should fail gracefully
                with pytest.raises(Exception) as exc_info:
                    await redis_client.ping()
                
                assert "connection" in str(exc_info.value).lower() or "pool" in str(exc_info.value).lower()
                
                # Service should handle Redis unavailability gracefully
                cache_result = await self._simulate_cache_operation_with_fallback()
                assert cache_result["fallback_used"] is True
                assert cache_result["operation_succeeded"] is True
    
    async def test_connection_pool_recovery_timing(self, connection_exhauster):
        """Test connection pool recovery timing and stages."""
        validator = RecoveryValidator()
        
        async def db_operation():
            # Simulate database query
            await asyncio.sleep(0.05)
            return {"query_result": "success"}
        
        async with validator.validate_recovery(baseline_operation=db_operation):
            validator.mark_stage_complete(RecoveryStage.FAILURE_DETECTED)
            
            # Exhaust connection pool
            async with connection_exhauster.exhaust_pool(pool_size=5, connection_type="database"):
                validator.mark_stage_complete(RecoveryStage.SERVICES_RESTARTING)
                
                # Wait for pool exhaustion to take effect
                await asyncio.sleep(1.0)
                validator.mark_stage_complete(RecoveryStage.CONNECTIONS_RESTORED)
                
                # Test operations during recovery
                for i in range(3):
                    try:
                        result = await db_operation()
                        assert result is not None
                    except Exception as e:
                        print(f"Operation {i} failed during recovery: {e}")
                    
                    await asyncio.sleep(0.5)
                
                validator.mark_stage_complete(RecoveryStage.PERFORMANCE_RESTORED)
            
            # Pool should recover after exhaustion ends
            validator.mark_stage_complete(RecoveryStage.FULLY_OPERATIONAL)
        
        # Validate recovery metrics
        recovery_quality = validator.assert_recovery_quality(
            max_recovery_time=15.0,
            min_performance_recovery=0.8
        )
        
        assert recovery_quality["overall_passed"], "Connection pool recovery quality check failed"
    
    async def _simulate_cache_operation_with_fallback(self) -> Dict[str, Any]:
        """Simulate cache operation with fallback handling."""
        try:
            # Try cache operation
            redis_client = RedisClient()
            await redis_client.get("test_key")
            
            return {"operation_succeeded": True, "fallback_used": False}
            
        except Exception:
            # Fallback to direct computation
            return {"operation_succeeded": True, "fallback_used": True, "result": "fallback_value"}


@pytest.mark.asyncio
class TestThreadPoolSaturationRecovery:
    """Test thread pool saturation and recovery."""
    
    @pytest.fixture
    async def thread_saturator(self):
        """Create thread pool saturator."""
        return ThreadPoolSaturator()
    
    async def test_thread_pool_saturation_and_recovery(self, thread_saturator):
        """Test thread pool saturation and recovery process."""
        initial_resources = get_current_resource_usage()
        
        async with thread_saturator.saturate_thread_pool(max_workers=4, task_duration=3.0) as saturation_result:
            # Verify thread pool saturation
            assert saturation_result["running_tasks"] > 0, "No tasks running"
            assert saturation_result["pending_tasks"] > 0, "No tasks pending"
            
            print(f"Thread pool saturated: {saturation_result['running_tasks']} running, "
                  f"{saturation_result['pending_tasks']} pending")
            
            # Test service responsiveness during saturation
            start_time = time.time()
            
            # Simulate non-blocking async operation
            async def async_operation():
                await asyncio.sleep(0.1)
                return {"status": "completed"}
            
            result = await async_operation()
            end_time = time.time()
            
            # Async operations should not be significantly affected
            response_time = (end_time - start_time) * 1000
            assert response_time < 500, f"Async operation too slow during thread saturation: {response_time}ms"
            assert result["status"] == "completed"
            
            # Test blocking operation (should be affected)
            def blocking_operation():
                time.sleep(0.2)
                return {"status": "blocking_completed"}
            
            # This might be slow due to thread pool saturation
            executor = ThreadPoolExecutor(max_workers=1)
            try:
                future = executor.submit(blocking_operation)
                blocking_result = future.result(timeout=5.0)  # Generous timeout
                assert blocking_result["status"] == "blocking_completed"
            except Exception as e:
                print(f"Blocking operation affected by saturation: {e}")
            finally:
                executor.shutdown(wait=False)
        
        # Verify thread pool recovery
        await asyncio.sleep(2.0)  # Allow time for cleanup
        
        final_resources = get_current_resource_usage()
        
        # Thread count should return to normal levels
        thread_difference = final_resources["threads"] - initial_resources["threads"]
        assert abs(thread_difference) <= 5, f"Thread count not properly recovered: {thread_difference} difference"
    
    async def test_thread_pool_queue_overflow_handling(self, thread_saturator):
        """Test handling of thread pool queue overflow."""
        # Create executor with small queue
        executor = ThreadPoolExecutor(max_workers=2)
        
        def long_running_task(task_id: int):
            time.sleep(1.0)  # 1 second task
            return f"task_{task_id}_completed"
        
        try:
            # Submit many tasks to cause queue overflow
            futures = []
            for i in range(10):  # More tasks than can be handled immediately
                try:
                    future = executor.submit(long_running_task, i)
                    futures.append(future)
                except Exception as e:
                    print(f"Task {i} rejected due to queue overflow: {e}")
            
            # Wait for some tasks to complete
            completed_tasks = 0
            failed_tasks = 0
            
            for future in futures:
                try:
                    result = future.result(timeout=3.0)
                    if "completed" in result:
                        completed_tasks += 1
                except Exception as e:
                    failed_tasks += 1
                    print(f"Task failed: {e}")
            
            print(f"Thread pool overflow test: {completed_tasks} completed, {failed_tasks} failed")
            
            # Some tasks should complete even with overflow
            assert completed_tasks > 0, "No tasks completed despite thread pool"
            
        finally:
            executor.shutdown(wait=True)
    
    async def test_thread_pool_deadlock_prevention(self):
        """Test prevention of thread pool deadlocks."""
        executor = ThreadPoolExecutor(max_workers=2)
        
        # Simulate potential deadlock scenario
        lock1 = threading.Lock()
        lock2 = threading.Lock()
        
        def task_with_locks(task_id: int, first_lock, second_lock):
            try:
                # Acquire locks in different order to simulate deadlock potential
                if task_id % 2 == 0:
                    with first_lock:
                        time.sleep(0.1)
                        with second_lock:
                            return f"task_{task_id}_completed"
                else:
                    with second_lock:
                        time.sleep(0.1)
                        with first_lock:
                            return f"task_{task_id}_completed"
            except Exception as e:
                return f"task_{task_id}_failed: {str(e)}"
        
        try:
            # Submit tasks that could potentially deadlock
            futures = []
            for i in range(4):
                future = executor.submit(task_with_locks, i, lock1, lock2)
                futures.append(future)
            
            # Wait for completion with timeout
            results = []
            for future in futures:
                try:
                    result = future.result(timeout=2.0)  # Reasonable timeout
                    results.append(result)
                except Exception as e:
                    results.append(f"timeout_or_error: {str(e)}")
            
            # At least some tasks should complete (no total deadlock)
            completed_count = sum(1 for r in results if "completed" in r)
            assert completed_count > 0, f"Potential deadlock detected: {results}"
            
            print(f"Deadlock prevention test: {completed_count}/4 tasks completed")
            
        finally:
            executor.shutdown(wait=True)


@pytest.mark.asyncio
class TestDiskSpaceExhaustionRecovery:
    """Test disk space exhaustion and recovery."""
    
    @pytest.fixture
    async def disk_exhauster(self):
        """Create disk space exhauster."""
        return DiskSpaceExhauster()
    
    async def test_disk_space_exhaustion_and_cleanup(self, disk_exhauster):
        """Test disk space exhaustion and cleanup recovery."""
        initial_resources = get_current_resource_usage()
        
        async with disk_exhauster.exhaust_disk_space(target_mb=100, temp_location=True) as disk_result:
            # Verify disk space usage
            assert disk_result["files_created"] > 0, "No files created for disk exhaustion"
            assert disk_result["total_size_mb"] > 50, f"Insufficient disk usage: {disk_result['total_size_mb']:.1f}MB"
            
            print(f"Disk space exhausted: {disk_result['total_size_mb']:.1f}MB in {disk_result['files_created']} files")
            
            # Test service behavior with limited disk space
            try:
                # Try to create temporary file for processing
                temp_file = tempfile.NamedTemporaryFile(mode='w', delete=False)
                temp_file.write("test data for processing")
                temp_file.close()
                
                # Should be able to create small files
                assert os.path.exists(temp_file.name)
                
                # Cleanup test file
                os.unlink(temp_file.name)
                
            except OSError as e:
                if "no space left" in str(e).lower():
                    print("Disk space genuinely exhausted - this is expected")
                else:
                    raise
            
            # Test large file operations (should fail gracefully)
            with pytest.raises(Exception) as exc_info:
                large_data = "x" * (50 * 1024 * 1024)  # 50MB string
                with tempfile.NamedTemporaryFile(mode='w') as f:
                    f.write(large_data)
            
            # Should get disk-related error
            error_msg = str(exc_info.value).lower()
            assert any(keyword in error_msg for keyword in ["space", "disk", "storage", "memory"]), \
                f"Expected disk/storage error, got: {exc_info.value}"
        
        # Verify cleanup after disk space release
        await asyncio.sleep(1.0)
        
        # Check that temp directory is cleaned up
        if "temp_dir" in disk_result:
            temp_dir = disk_result["temp_dir"]
            if temp_dir != "/tmp":  # Don't check /tmp as it's system-wide
                assert not os.path.exists(temp_dir) or len(os.listdir(temp_dir)) == 0, \
                    "Temporary directory not properly cleaned up"
        
        final_resources = get_current_resource_usage()
        assert_cleanup_completed(initial_resources, final_resources, 
                                tolerance={"memory_mb": 10.0, "threads": 1})
    
    async def test_disk_space_monitoring_and_alerts(self):
        """Test disk space monitoring and alerting."""
        # Mock disk usage monitoring
        with patch('psutil.disk_usage') as mock_disk_usage:
            # Simulate low disk space scenario
            mock_disk_usage.return_value = Mock(
                total=1000000000,  # 1GB total
                used=950000000,    # 950MB used (95%)
                free=50000000      # 50MB free (5%)
            )
            
            # Check disk space availability
            disk_info = psutil.disk_usage('/')
            usage_percent = (disk_info.used / disk_info.total) * 100
            free_mb = disk_info.free / 1024 / 1024
            
            assert usage_percent > 90, f"Disk usage simulation failed: {usage_percent}%"
            assert free_mb < 100, f"Free space simulation failed: {free_mb}MB"
            
            # Test service behavior with low disk space
            health_checker = HealthChecker()
            
            # Mock health check to include disk space
            with patch.object(health_checker, 'check_disk_space') as mock_disk_check:
                mock_disk_check.return_value = {
                    "status": "warning",
                    "free_space_mb": free_mb,
                    "usage_percent": usage_percent,
                    "message": "Low disk space detected"
                }
                
                disk_health = mock_disk_check()
                
                assert disk_health["status"] == "warning"
                assert "low" in disk_health["message"].lower()
    
    async def test_temporary_file_cleanup_on_error(self):
        """Test temporary file cleanup during error conditions."""
        temp_files_created = []
        
        try:
            # Simulate error during file processing
            for i in range(10):
                temp_file = tempfile.NamedTemporaryFile(mode='w', delete=False)
                temp_file.write(f"test data {i}" * 1000)  # Some content
                temp_file.close()
                temp_files_created.append(temp_file.name)
                
                if i == 5:
                    # Simulate error during processing
                    raise Exception("Simulated processing error")
        
        except Exception as e:
            # Cleanup should happen even during errors
            print(f"Error occurred: {e}")
            
            # Cleanup all temporary files
            for temp_file_path in temp_files_created:
                try:
                    if os.path.exists(temp_file_path):
                        os.unlink(temp_file_path)
                except Exception as cleanup_error:
                    print(f"Cleanup error for {temp_file_path}: {cleanup_error}")
        
        # Verify all files are cleaned up
        remaining_files = [f for f in temp_files_created if os.path.exists(f)]
        assert len(remaining_files) == 0, f"Temporary files not cleaned up: {remaining_files}"


@pytest.mark.asyncio
class TestFileDescriptorExhaustionRecovery:
    """Test file descriptor exhaustion and recovery."""
    
    async def test_file_descriptor_limit_handling(self):
        """Test file descriptor limit handling and recovery."""
        initial_resources = get_current_resource_usage()
        
        # Get current file descriptor limit
        soft_limit, hard_limit = resource.getrlimit(resource.RLIMIT_NOFILE)
        print(f"File descriptor limits: soft={soft_limit}, hard={hard_limit}")
        
        opened_files = []
        
        try:
            # Try to open many files to approach the limit
            target_files = min(soft_limit - 100, 200)  # Leave some buffer and cap at reasonable number
            
            for i in range(target_files):
                try:
                    temp_file = tempfile.NamedTemporaryFile(mode='w', delete=False)
                    temp_file.write(f"test data {i}")
                    opened_files.append(temp_file)
                    
                    # Check if we're approaching limits
                    if i % 50 == 0:
                        current_fds = psutil.Process().num_fds() if hasattr(psutil.Process(), 'num_fds') else len(opened_files)
                        print(f"Opened {i} files, current FDs: {current_fds}")
                        
                        if current_fds > soft_limit * 0.8:  # 80% of limit
                            print(f"Approaching FD limit at {i} files")
                            break
                
                except OSError as e:
                    if "too many open files" in str(e).lower():
                        print(f"Hit file descriptor limit after {i} files")
                        break
                    else:
                        raise
            
            print(f"Successfully opened {len(opened_files)} files")
            
            # Test service operations with many files open
            async def test_operation_with_high_fd_usage():
                # Should still work with some file operations
                with tempfile.NamedTemporaryFile(mode='w') as temp:
                    temp.write("test data")
                    return {"status": "success", "fd_count": len(opened_files)}
            
            result = await test_operation_with_high_fd_usage()
            assert result["status"] == "success"
        
        finally:
            # Cleanup all opened files
            for temp_file in opened_files:
                try:
                    temp_file.close()
                    if hasattr(temp_file, 'name') and os.path.exists(temp_file.name):
                        os.unlink(temp_file.name)
                except Exception as e:
                    print(f"Error cleaning up file: {e}")
        
        # Verify file descriptor cleanup
        await asyncio.sleep(1.0)
        final_resources = get_current_resource_usage()
        
        fd_difference = final_resources.get("file_descriptors", 0) - initial_resources.get("file_descriptors", 0)
        assert abs(fd_difference) <= 10, f"File descriptors not properly cleaned up: {fd_difference} difference"
    
    async def test_file_descriptor_leak_detection(self):
        """Test file descriptor leak detection and prevention."""
        initial_fd_count = psutil.Process().num_fds() if hasattr(psutil.Process(), 'num_fds') else 0
        
        # Simulate operations that might leak file descriptors
        operations_with_files = []
        
        for i in range(20):
            # Simulate file operation that might leak
            try:
                temp_file = tempfile.NamedTemporaryFile(mode='w', delete=False)
                temp_file.write(f"operation {i} data")
                temp_file.close()
                
                operations_with_files.append(temp_file.name)
                
                # Simulate forgetting to close/cleanup some files (potential leak)
                if i % 5 == 0:
                    # Keep reference to simulate leak
                    pass
                else:
                    # Proper cleanup
                    os.unlink(temp_file.name)
                    operations_with_files.remove(temp_file.name)
                
                # Monitor FD count periodically
                if i % 5 == 0:
                    current_fd_count = psutil.Process().num_fds() if hasattr(psutil.Process(), 'num_fds') else 0
                    fd_growth = current_fd_count - initial_fd_count
                    
                    print(f"Operation {i}: FD growth = {fd_growth}")
                    
                    # If FD growth is excessive, trigger cleanup
                    if fd_growth > 15:
                        print(f"FD leak detected, triggering cleanup")
                        
                        # Cleanup leaked files
                        for leaked_file in operations_with_files[:]:
                            try:
                                if os.path.exists(leaked_file):
                                    os.unlink(leaked_file)
                                operations_with_files.remove(leaked_file)
                            except Exception as e:
                                print(f"Error cleaning leaked file {leaked_file}: {e}")
                        
                        # Verify cleanup effectiveness
                        post_cleanup_fd_count = psutil.Process().num_fds() if hasattr(psutil.Process(), 'num_fds') else 0
                        cleanup_effect = current_fd_count - post_cleanup_fd_count
                        
                        assert cleanup_effect > 0, "FD cleanup had no effect"
                        print(f"FD cleanup freed {cleanup_effect} descriptors")
                        break
            except Exception as e:
                print(f"Error in file operation {i}: {e}")
                continue
        
        # Final cleanup
        for remaining_file in operations_with_files:
            try:
                if os.path.exists(remaining_file):
                    os.unlink(remaining_file)
            except Exception:
                pass
        
        # Verify final FD count is reasonable
        final_fd_count = psutil.Process().num_fds() if hasattr(psutil.Process(), 'num_fds') else 0
        final_fd_growth = final_fd_count - initial_fd_count
        
        assert final_fd_growth <= 5, f"Excessive FD growth after cleanup: {final_fd_growth}"


@pytest.mark.asyncio
class TestComprehensiveResourceRecovery:
    """Test comprehensive resource recovery scenarios."""
    
    async def test_multiple_resource_exhaustion_recovery(self):
        """Test recovery from multiple simultaneous resource exhaustions."""
        orchestrator = ServiceFailureOrchestrator()
        
        # Create multiple resource exhaustion scenarios
        scenarios = [
            FailureScenario(
                failure_type=FailureType.MEMORY_EXHAUSTION,
                pattern=FailurePattern.PROGRESSIVE,
                duration_seconds=8.0,
                parameters={"target_mb": 200},
                description="Memory pressure during multi-resource test"
            ),
            FailureScenario(
                failure_type=FailureType.CONNECTION_POOL_EXHAUSTION,
                pattern=FailurePattern.IMMEDIATE,
                duration_seconds=6.0,
                parameters={"pool_size": 5, "connection_type": "database"},
                description="Database connection exhaustion"
            ),
            FailureScenario(
                failure_type=FailureType.THREAD_POOL_SATURATION,
                pattern=FailurePattern.IMMEDIATE,
                duration_seconds=5.0,
                parameters={"max_workers": 3, "task_duration": 2.0},
                description="Thread pool saturation"
            )
        ]
        
        # Execute cascading failures
        cascade_result = await orchestrator.execute_cascading_failures(scenarios, cascade_delay=1.0)
        
        # Verify cascading execution
        assert cascade_result["total_scenarios"] == 3
        assert cascade_result["summary"]["successful_scenarios"] >= 2, "Most scenarios should succeed"
        
        # Verify each scenario's resource impact
        for i, scenario_result in enumerate(cascade_result["scenario_results"]):
            assert scenario_result["execution"]["success"], f"Scenario {i} failed"
            
            resource_changes = scenario_result["resources"]["changes"]
            print(f"Scenario {i} resource changes: memory={resource_changes['memory_mb']:+.1f}MB, "
                  f"threads={resource_changes['threads']:+d}")
        
        # Overall system should recover
        total_duration = cascade_result["execution"]["total_duration"]
        assert total_duration < 20.0, f"Cascading recovery too slow: {total_duration}s"
    
    async def test_resource_recovery_under_load(self):
        """Test resource recovery while system is under load."""
        memory_injector = MemoryPressureInjector()
        
        # Create background load
        async def background_load():
            operations = []
            for i in range(10):
                # Simulate ongoing pattern detection requests
                result = await self._simulate_pattern_detection_load(f"load_operation_{i}")
                operations.append(result)
                await asyncio.sleep(0.2)
            return operations
        
        # Start background load
        load_task = asyncio.create_task(background_load())
        
        try:
            # Apply memory pressure while load is running
            async with memory_injector.memory_pressure(target_mb=300, progressive=True):
                await asyncio.sleep(3.0)
                
                # System should handle both load and resource pressure
                current_memory = psutil.Process().memory_info().rss / 1024 / 1024
                print(f"Memory usage under load + pressure: {current_memory:.1f}MB")
                
                # Background load should continue to work
                assert not load_task.done() or not load_task.exception(), "Background load failed under pressure"
            
            # Wait for background load to complete
            load_results = await load_task
            
            # Verify load operations succeeded despite resource pressure
            successful_operations = [r for r in load_results if r and r.get("status") == "success"]
            assert len(successful_operations) >= 5, f"Too many operations failed under pressure: {len(successful_operations)}/10"
            
        except Exception as e:
            load_task.cancel()
            raise
    
    async def _simulate_pattern_detection_load(self, operation_id: str) -> Dict[str, Any]:
        """Simulate pattern detection operation under load."""
        try:
            # Simulate some processing work
            await asyncio.sleep(0.1)
            
            # Return success result
            return {
                "operation_id": operation_id,
                "status": "success",
                "patterns_found": 3,
                "processing_time_ms": 100
            }
            
        except Exception as e:
            return {
                "operation_id": operation_id,
                "status": "failed",
                "error": str(e)
            }


if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"])