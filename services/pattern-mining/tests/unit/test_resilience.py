"""
Unit Tests for System Resilience

Tests for resilience patterns including circuit breakers, retry logic,
bulkhead isolation, rate limiting, and graceful degradation mechanisms.

Key Test Categories:
1. Circuit Breaker Patterns - Failure detection, state management, recovery
2. Retry Logic - Exponential backoff, jitter, maximum attempts  
3. Bulkhead Isolation - Resource isolation, failure containment
4. Rate Limiting - Token bucket, sliding window, backpressure
5. Graceful Degradation - Fallback mechanisms, service reduction
6. Health Monitoring - Health checks, dependency monitoring
"""

import asyncio
import pytest
import time
import random
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass
from enum import Enum
import threading
from concurrent.futures import ThreadPoolExecutor

# Pattern mining imports
from pattern_mining.utils.circuit_breaker import CircuitBreaker, CircuitState
from pattern_mining.utils.retry import RetryPolicy, ExponentialBackoff, RetryableError
from pattern_mining.utils.rate_limiter import TokenBucket, SlidingWindowRateLimiter
from pattern_mining.utils.bulkhead import BulkheadIsolator, ResourcePool
from pattern_mining.health.health_checker import <PERSON>Checker, HealthStatus
from pattern_mining.monitoring.performance_metrics import PerformanceMonitor
from pattern_mining.ml.gemini_client import GeminiClient, GeminiRateLimiter


class CircuitBreakerState(Enum):
    """Circuit breaker states."""
    CLOSED = "closed"
    OPEN = "open"
    HALF_OPEN = "half_open"


class TestCircuitBreakerPatterns:
    """Test circuit breaker implementation."""
    
    @pytest.fixture
    def circuit_breaker(self):
        """Create circuit breaker for testing."""
        return CircuitBreaker(
            failure_threshold=3,
            recovery_timeout=1.0,
            expected_exception=Exception
        )
    
    @pytest.mark.asyncio
    async def test_circuit_breaker_closed_state(self, circuit_breaker):
        """Test circuit breaker in closed state."""
        success_count = 0
        
        async def successful_operation():
            nonlocal success_count
            success_count += 1
            return f"success_{success_count}"
        
        # Circuit should start in closed state
        assert circuit_breaker.state == CircuitBreakerState.CLOSED.value
        
        # Successful calls should work normally
        for i in range(5):
            result = await circuit_breaker.call(successful_operation)
            assert result == f"success_{i+1}"
        
        # Circuit should remain closed
        assert circuit_breaker.state == CircuitBreakerState.CLOSED.value
        assert circuit_breaker.failure_count == 0
    
    @pytest.mark.asyncio
    async def test_circuit_breaker_open_state(self, circuit_breaker):
        """Test circuit breaker opening after failures."""
        failure_count = 0
        
        async def failing_operation():
            nonlocal failure_count
            failure_count += 1
            raise Exception(f"failure_{failure_count}")
        
        # Fail until circuit opens
        for i in range(3):
            with pytest.raises(Exception):
                await circuit_breaker.call(failing_operation)
        
        # Circuit should be open now
        assert circuit_breaker.state == CircuitBreakerState.OPEN.value
        
        # Subsequent calls should fail fast without calling the operation
        operation_calls = failure_count
        
        with pytest.raises(Exception) as exc_info:
            await circuit_breaker.call(failing_operation)
        
        # Operation should not have been called (fail fast)
        assert failure_count == operation_calls
        assert "circuit breaker" in str(exc_info.value).lower()
    
    @pytest.mark.asyncio
    async def test_circuit_breaker_half_open_state(self, circuit_breaker):
        """Test circuit breaker half-open state and recovery."""
        call_count = 0
        
        async def variable_operation():
            nonlocal call_count
            call_count += 1
            
            if call_count <= 3:
                # First 3 calls fail
                raise Exception(f"failure_{call_count}")
            else:
                # Subsequent calls succeed
                return f"success_{call_count}"
        
        # Open the circuit
        for i in range(3):
            with pytest.raises(Exception):
                await circuit_breaker.call(variable_operation)
        
        assert circuit_breaker.state == CircuitBreakerState.OPEN.value
        
        # Wait for recovery timeout
        await asyncio.sleep(1.1)
        
        # Next call should put circuit in half-open state
        result = await circuit_breaker.call(variable_operation)
        assert result == "success_4"
        
        # Circuit should be closed again after successful call
        assert circuit_breaker.state == CircuitBreakerState.CLOSED.value
    
    @pytest.mark.asyncio
    async def test_circuit_breaker_metrics(self, circuit_breaker):
        """Test circuit breaker metrics collection."""
        async def test_operation():
            return "success"
        
        # Make some successful calls
        for _ in range(5):
            await circuit_breaker.call(test_operation)
        
        metrics = circuit_breaker.get_metrics()
        
        assert metrics["total_calls"] == 5
        assert metrics["successful_calls"] == 5
        assert metrics["failed_calls"] == 0
        assert metrics["success_rate"] == 1.0
        assert metrics["current_state"] == CircuitBreakerState.CLOSED.value
    
    @pytest.mark.asyncio
    async def test_circuit_breaker_concurrent_access(self, circuit_breaker):
        """Test circuit breaker under concurrent access."""
        call_count = 0
        lock = threading.Lock()
        
        async def concurrent_operation():
            nonlocal call_count
            with lock:
                call_count += 1
                return f"call_{call_count}"
        
        # Make concurrent calls
        tasks = [circuit_breaker.call(concurrent_operation) for _ in range(10)]
        results = await asyncio.gather(*tasks)
        
        # All calls should succeed
        assert len(results) == 10
        assert all("call_" in result for result in results)
        
        # Circuit should remain closed
        assert circuit_breaker.state == CircuitBreakerState.CLOSED.value


class TestRetryLogic:
    """Test retry mechanisms and backoff strategies."""
    
    @pytest.fixture
    def retry_policy(self):
        """Create retry policy for testing."""
        return RetryPolicy(
            max_attempts=3,
            backoff_strategy=ExponentialBackoff(
                initial_delay=0.1,
                max_delay=2.0,
                multiplier=2.0,
                jitter=True
            )
        )
    
    @pytest.mark.asyncio
    async def test_retry_success_after_failures(self, retry_policy):
        """Test successful retry after initial failures."""
        attempt_count = 0
        
        async def flaky_operation():
            nonlocal attempt_count
            attempt_count += 1
            
            if attempt_count < 3:
                raise RetryableError(f"Attempt {attempt_count} failed")
            return f"success on attempt {attempt_count}"
        
        result = await retry_policy.execute(flaky_operation)
        
        assert result == "success on attempt 3"
        assert attempt_count == 3
    
    @pytest.mark.asyncio
    async def test_retry_max_attempts_exceeded(self, retry_policy):
        """Test retry failure when max attempts exceeded."""
        attempt_count = 0
        
        async def always_failing_operation():
            nonlocal attempt_count
            attempt_count += 1
            raise RetryableError(f"Attempt {attempt_count} failed")
        
        with pytest.raises(RetryableError):
            await retry_policy.execute(always_failing_operation)
        
        # Should have attempted max_attempts times
        assert attempt_count == 3
    
    @pytest.mark.asyncio
    async def test_exponential_backoff_timing(self, retry_policy):
        """Test exponential backoff timing."""
        attempt_times = []
        
        async def failing_operation():
            attempt_times.append(time.time())
            raise RetryableError("Always fails")
        
        start_time = time.time()
        
        with pytest.raises(RetryableError):
            await retry_policy.execute(failing_operation)
        
        # Verify exponential backoff delays
        assert len(attempt_times) == 3
        
        # First attempt should be immediate
        assert attempt_times[0] - start_time < 0.01
        
        # Second attempt should be after initial delay (~0.1s)
        delay_1 = attempt_times[1] - attempt_times[0]
        assert 0.05 < delay_1 < 0.3  # With jitter
        
        # Third attempt should be after exponential delay (~0.2s)
        delay_2 = attempt_times[2] - attempt_times[1]
        assert 0.1 < delay_2 < 0.6  # With jitter and multiplier
    
    @pytest.mark.asyncio
    async def test_non_retryable_error(self, retry_policy):
        """Test that non-retryable errors are not retried."""
        attempt_count = 0
        
        async def non_retryable_operation():
            nonlocal attempt_count
            attempt_count += 1
            raise ValueError("Non-retryable error")
        
        with pytest.raises(ValueError):
            await retry_policy.execute(non_retryable_operation)
        
        # Should only attempt once for non-retryable errors
        assert attempt_count == 1
    
    @pytest.mark.asyncio
    async def test_retry_with_jitter(self):
        """Test that jitter adds randomness to backoff delays."""
        backoff = ExponentialBackoff(
            initial_delay=0.1,
            max_delay=1.0,
            multiplier=2.0,
            jitter=True
        )
        
        delays = []
        for _ in range(10):
            delay = backoff.calculate_delay(attempt=1)
            delays.append(delay)
        
        # With jitter, delays should vary
        assert len(set(delays)) > 1  # Should have some variation
        
        # All delays should be within reasonable bounds
        for delay in delays:
            assert 0.05 <= delay <= 0.3  # Initial delay * (0.5 to 1.5 with jitter)


class TestBulkheadIsolation:
    """Test bulkhead isolation patterns."""
    
    @pytest.fixture
    def bulkhead(self):
        """Create bulkhead isolator for testing."""
        return BulkheadIsolator(
            pools={
                "gemini": ResourcePool(max_concurrent=3, queue_size=5),
                "database": ResourcePool(max_concurrent=5, queue_size=10),
                "cache": ResourcePool(max_concurrent=10, queue_size=20)
            }
        )
    
    @pytest.mark.asyncio
    async def test_resource_pool_isolation(self, bulkhead):
        """Test that resource pools are isolated."""
        gemini_calls = 0
        database_calls = 0
        
        async def gemini_operation():
            nonlocal gemini_calls
            gemini_calls += 1
            await asyncio.sleep(0.1)  # Simulate work
            return f"gemini_{gemini_calls}"
        
        async def database_operation():
            nonlocal database_calls
            database_calls += 1
            await asyncio.sleep(0.1)  # Simulate work
            return f"database_{database_calls}"
        
        # Start operations that exceed gemini pool capacity
        gemini_tasks = [
            bulkhead.execute("gemini", gemini_operation)
            for _ in range(6)  # More than max_concurrent (3)
        ]
        
        # Start database operations simultaneously
        database_tasks = [
            bulkhead.execute("database", database_operation)
            for _ in range(3)  # Within database pool capacity (5)
        ]
        
        # Database operations should complete normally
        database_results = await asyncio.gather(*database_tasks)
        assert len(database_results) == 3
        assert all("database_" in result for result in database_results)
        
        # Gemini operations should be throttled but eventually complete
        gemini_results = await asyncio.gather(*gemini_tasks)
        assert len(gemini_results) == 6
        assert all("gemini_" in result for result in gemini_results)
    
    @pytest.mark.asyncio
    async def test_queue_overflow_handling(self, bulkhead):
        """Test handling of queue overflow."""
        async def slow_operation():
            await asyncio.sleep(1.0)  # Very slow operation
            return "completed"
        
        # Fill up the pool and queue for gemini
        # Pool capacity: 3, Queue capacity: 5, Total: 8
        tasks = []
        
        # Start 10 operations (more than pool + queue capacity)
        for i in range(10):
            task = asyncio.create_task(
                bulkhead.execute("gemini", slow_operation)
            )
            tasks.append(task)
        
        # Wait a bit for queue to fill
        await asyncio.sleep(0.1)
        
        # Some operations should be rejected due to queue overflow
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        completed_count = sum(1 for r in results if r == "completed")
        rejected_count = sum(1 for r in results if isinstance(r, Exception))
        
        # Should have some completions and some rejections
        assert completed_count > 0
        assert rejected_count > 0
        assert completed_count + rejected_count == 10
    
    @pytest.mark.asyncio
    async def test_bulkhead_metrics(self, bulkhead):
        """Test bulkhead metrics collection."""
        async def test_operation():
            await asyncio.sleep(0.05)
            return "success"
        
        # Execute some operations
        tasks = [
            bulkhead.execute("database", test_operation)
            for _ in range(3)
        ]
        
        await asyncio.gather(*tasks)
        
        metrics = bulkhead.get_metrics()
        
        assert "database" in metrics
        db_metrics = metrics["database"]
        assert db_metrics["total_requests"] == 3
        assert db_metrics["successful_requests"] == 3
        assert db_metrics["current_active"] == 0  # Should be done
        assert db_metrics["queue_size"] == 0


class TestRateLimiting:
    """Test rate limiting mechanisms."""
    
    @pytest.fixture
    def token_bucket(self):
        """Create token bucket rate limiter."""
        return TokenBucket(
            capacity=10,
            refill_rate=2.0,  # 2 tokens per second
            refill_period=1.0
        )
    
    @pytest.fixture
    def sliding_window(self):
        """Create sliding window rate limiter."""
        return SlidingWindowRateLimiter(
            max_requests=5,
            window_size=1.0  # 5 requests per second
        )
    
    def test_token_bucket_basic_operation(self, token_bucket):
        """Test basic token bucket operation."""
        # Should start with full capacity
        assert token_bucket.tokens == 10
        
        # Consume some tokens
        assert token_bucket.consume(3) is True
        assert token_bucket.tokens == 7
        
        # Try to consume more than available
        assert token_bucket.consume(8) is False
        assert token_bucket.tokens == 7  # Should remain unchanged
        
        # Consume remaining tokens
        assert token_bucket.consume(7) is True
        assert token_bucket.tokens == 0
    
    def test_token_bucket_refill(self, token_bucket):
        """Test token bucket refill mechanism."""
        # Consume all tokens
        token_bucket.consume(10)
        assert token_bucket.tokens == 0
        
        # Wait for refill
        time.sleep(1.1)  # Slightly more than refill period
        
        # Should have refilled 2 tokens (refill_rate * time)
        token_bucket._refill()
        assert token_bucket.tokens >= 2
        assert token_bucket.tokens <= 3  # Account for timing precision
    
    def test_sliding_window_basic_operation(self, sliding_window):
        """Test basic sliding window operation."""
        current_time = time.time()
        
        # Should allow requests within limit
        for i in range(5):
            assert sliding_window.is_allowed("user1", current_time) is True
        
        # Next request should be denied
        assert sliding_window.is_allowed("user1", current_time) is False
    
    def test_sliding_window_time_advance(self, sliding_window):
        """Test sliding window with time advancement."""
        current_time = time.time()
        
        # Fill the window
        for i in range(5):
            sliding_window.is_allowed("user1", current_time)
        
        # Should be at limit
        assert sliding_window.is_allowed("user1", current_time) is False
        
        # Advance time by window size
        future_time = current_time + 1.1
        
        # Should allow requests again
        assert sliding_window.is_allowed("user1", future_time) is True
    
    def test_rate_limiter_per_user_isolation(self, sliding_window):
        """Test that rate limiting is isolated per user."""
        current_time = time.time()
        
        # User1 fills their limit
        for i in range(5):
            sliding_window.is_allowed("user1", current_time)
        
        # User1 should be limited
        assert sliding_window.is_allowed("user1", current_time) is False
        
        # User2 should still have full quota
        assert sliding_window.is_allowed("user2", current_time) is True
    
    @pytest.mark.asyncio
    async def test_gemini_rate_limiter_integration(self):
        """Test Gemini rate limiter integration."""
        rate_limiter = GeminiRateLimiter(
            requests_per_minute=6,  # 0.1 requests per second
            tokens_per_minute=6000   # 100 tokens per second
        )
        
        # Should allow initial requests
        assert await rate_limiter.acquire(1000) is True
        assert await rate_limiter.acquire(1000) is True
        
        # Check rate limiting
        start_time = time.time()
        result = await rate_limiter.acquire(1000)
        end_time = time.time()
        
        # Should either be rate limited or have waited
        if not result:
            # Rate limited
            assert True
        else:
            # Waited for rate limit
            assert end_time - start_time > 0.1


class TestGracefulDegradation:
    """Test graceful degradation patterns."""
    
    @pytest.mark.asyncio
    async def test_service_level_degradation(self):
        """Test service level degradation based on load."""
        class ServiceManager:
            def __init__(self):
                self.load_level = 0
                self.degradation_level = 0
            
            def update_load(self, requests_per_second):
                self.load_level = requests_per_second
                
                if requests_per_second > 100:
                    self.degradation_level = 3  # Severe degradation
                elif requests_per_second > 50:
                    self.degradation_level = 2  # Moderate degradation
                elif requests_per_second > 20:
                    self.degradation_level = 1  # Light degradation
                else:
                    self.degradation_level = 0  # No degradation
            
            async def process_request(self, request_type):
                if self.degradation_level >= 3:
                    # Severe degradation - only basic requests
                    if request_type != "basic":
                        raise Exception("Service degraded: advanced features unavailable")
                    return "basic_result"
                
                elif self.degradation_level >= 2:
                    # Moderate degradation - reduced features
                    if request_type == "advanced":
                        return "simplified_result"
                    return f"{request_type}_result"
                
                elif self.degradation_level >= 1:
                    # Light degradation - slower processing
                    await asyncio.sleep(0.1)  # Simulated slowdown
                    return f"{request_type}_result"
                
                else:
                    # Normal operation
                    return f"{request_type}_result"
        
        service = ServiceManager()
        
        # Test normal operation
        service.update_load(10)
        result = await service.process_request("advanced")
        assert result == "advanced_result"
        
        # Test light degradation
        service.update_load(30)
        start_time = time.time()
        result = await service.process_request("standard")
        end_time = time.time()
        assert result == "standard_result"
        assert end_time - start_time >= 0.1  # Should be slower
        
        # Test moderate degradation
        service.update_load(60)
        result = await service.process_request("advanced")
        assert result == "simplified_result"  # Downgraded response
        
        # Test severe degradation
        service.update_load(120)
        with pytest.raises(Exception, match="Service degraded"):
            await service.process_request("advanced")
        
        # Basic requests should still work
        result = await service.process_request("basic")
        assert result == "basic_result"
    
    @pytest.mark.asyncio
    async def test_feature_flag_degradation(self):
        """Test feature flag-based degradation."""
        class FeatureManager:
            def __init__(self):
                self.features = {
                    "ml_analysis": True,
                    "advanced_patterns": True,
                    "caching": True,
                    "detailed_metrics": True
                }
            
            def degrade_service(self, level):
                if level >= 1:
                    self.features["detailed_metrics"] = False
                if level >= 2:
                    self.features["advanced_patterns"] = False
                if level >= 3:
                    self.features["ml_analysis"] = False
                if level >= 4:
                    self.features["caching"] = False
            
            async def analyze_code(self, code):
                result = {"basic_patterns": ["function", "class"]}
                
                if self.features["advanced_patterns"]:
                    result["advanced_patterns"] = ["singleton", "factory"]
                
                if self.features["ml_analysis"]:
                    result["ml_confidence"] = 0.95
                    result["ml_patterns"] = ["observer", "strategy"]
                
                if self.features["caching"]:
                    result["cached"] = True
                
                if self.features["detailed_metrics"]:
                    result["metrics"] = {"processing_time": 150}
                
                return result
        
        feature_manager = FeatureManager()
        
        # Normal operation
        result = await feature_manager.analyze_code("def test(): pass")
        assert "basic_patterns" in result
        assert "advanced_patterns" in result
        assert "ml_patterns" in result
        assert "cached" in result
        assert "metrics" in result
        
        # Level 1 degradation
        feature_manager.degrade_service(1)
        result = await feature_manager.analyze_code("def test(): pass")
        assert "basic_patterns" in result
        assert "advanced_patterns" in result
        assert "ml_patterns" in result
        assert "metrics" not in result  # Detailed metrics disabled
        
        # Level 3 degradation
        feature_manager.degrade_service(3)
        result = await feature_manager.analyze_code("def test(): pass")
        assert "basic_patterns" in result
        assert "advanced_patterns" not in result  # Advanced patterns disabled
        assert "ml_patterns" not in result  # ML analysis disabled
    
    @pytest.mark.asyncio
    async def test_fallback_chain(self):
        """Test fallback chain implementation."""
        class FallbackChain:
            def __init__(self):
                self.primary_available = True
                self.secondary_available = True
                self.tertiary_available = True
            
            async def get_result(self, query):
                # Try primary service
                if self.primary_available:
                    try:
                        return await self._primary_service(query)
                    except Exception:
                        pass
                
                # Try secondary service
                if self.secondary_available:
                    try:
                        return await self._secondary_service(query)
                    except Exception:
                        pass
                
                # Try tertiary service
                if self.tertiary_available:
                    try:
                        return await self._tertiary_service(query)
                    except Exception:
                        pass
                
                # All services failed
                return {"error": "All services unavailable", "query": query}
            
            async def _primary_service(self, query):
                if not self.primary_available:
                    raise Exception("Primary service unavailable")
                return {"source": "primary", "result": f"primary_result_{query}"}
            
            async def _secondary_service(self, query):
                if not self.secondary_available:
                    raise Exception("Secondary service unavailable")
                return {"source": "secondary", "result": f"secondary_result_{query}"}
            
            async def _tertiary_service(self, query):
                if not self.tertiary_available:
                    raise Exception("Tertiary service unavailable")
                return {"source": "tertiary", "result": f"tertiary_result_{query}"}
        
        fallback = FallbackChain()
        
        # All services available - should use primary
        result = await fallback.get_result("test")
        assert result["source"] == "primary"
        
        # Primary unavailable - should use secondary
        fallback.primary_available = False
        result = await fallback.get_result("test")
        assert result["source"] == "secondary"
        
        # Primary and secondary unavailable - should use tertiary
        fallback.secondary_available = False
        result = await fallback.get_result("test")
        assert result["source"] == "tertiary"
        
        # All unavailable - should return error
        fallback.tertiary_available = False
        result = await fallback.get_result("test")
        assert "error" in result
        assert result["error"] == "All services unavailable"


class TestHealthMonitoring:
    """Test health monitoring and dependency checks."""
    
    @pytest.fixture
    def health_checker(self):
        """Create health checker for testing."""
        return HealthChecker()
    
    @pytest.mark.asyncio
    async def test_individual_health_checks(self, health_checker):
        """Test individual component health checks."""
        # Mock healthy database
        with patch('pattern_mining.database.connection.get_database_session') as mock_db:
            mock_db.return_value.__aenter__ = AsyncMock()
            mock_db.return_value.__aexit__ = AsyncMock()
            
            db_health = await health_checker.check_database_health()
            assert db_health.status == HealthStatus.HEALTHY
            assert db_health.response_time_ms > 0
    
    @pytest.mark.asyncio
    async def test_aggregate_health_status(self, health_checker):
        """Test aggregate health status calculation."""
        # Mock various component states
        with patch.object(health_checker, 'check_database_health') as mock_db:
            with patch.object(health_checker, 'check_cache_health') as mock_cache:
                with patch.object(health_checker, 'check_ml_service_health') as mock_ml:
                    
                    # All healthy
                    mock_db.return_value = Mock(status=HealthStatus.HEALTHY)
                    mock_cache.return_value = Mock(status=HealthStatus.HEALTHY)
                    mock_ml.return_value = Mock(status=HealthStatus.HEALTHY)
                    
                    overall_health = await health_checker.get_overall_health()
                    assert overall_health.status == HealthStatus.HEALTHY
                    
                    # One degraded
                    mock_cache.return_value = Mock(status=HealthStatus.DEGRADED)
                    
                    overall_health = await health_checker.get_overall_health()
                    assert overall_health.status == HealthStatus.DEGRADED
                    
                    # One unhealthy
                    mock_db.return_value = Mock(status=HealthStatus.UNHEALTHY)
                    
                    overall_health = await health_checker.get_overall_health()
                    assert overall_health.status == HealthStatus.UNHEALTHY
    
    @pytest.mark.asyncio
    async def test_health_check_caching(self, health_checker):
        """Test health check result caching."""
        call_count = 0
        
        async def mock_expensive_check():
            nonlocal call_count
            call_count += 1
            await asyncio.sleep(0.1)  # Simulate expensive check
            return Mock(status=HealthStatus.HEALTHY, response_time_ms=100)
        
        with patch.object(health_checker, 'check_database_health', side_effect=mock_expensive_check):
            # First call should execute the check
            result1 = await health_checker.check_database_health()
            assert call_count == 1
            
            # Second call should be cached (if caching is enabled)
            result2 = await health_checker.check_database_health()
            # Depending on implementation, might be cached
            assert call_count <= 2  # Allow for no caching or short cache
    
    @pytest.mark.asyncio
    async def test_health_check_timeout(self, health_checker):
        """Test health check timeout handling."""
        async def slow_check():
            await asyncio.sleep(5)  # Longer than typical timeout
            return Mock(status=HealthStatus.HEALTHY)
        
        with patch.object(health_checker, 'check_database_health', side_effect=slow_check):
            start_time = time.time()
            
            # Should timeout and return unhealthy status
            result = await health_checker.check_database_health()
            
            end_time = time.time()
            elapsed = end_time - start_time
            
            # Should not take full 5 seconds due to timeout
            assert elapsed < 3.0
            assert result.status == HealthStatus.UNHEALTHY or result.status == HealthStatus.DEGRADED


if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"])