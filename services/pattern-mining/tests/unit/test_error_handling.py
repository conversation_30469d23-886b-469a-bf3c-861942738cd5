"""
Unit Tests for Error Handling

Comprehensive error handling tests for the Pattern Mining service.
Tests cover network failures, service degradation, data validation failures,
authentication issues, and resource exhaustion scenarios.

Key Test Categories:
1. Network Failures - Connection timeouts, DNS failures, network partitions
2. Service Degradation - Rate limiting, resource exhaustion, memory pressure
3. Data Validation - Malformed input, invalid formats, oversized payloads
4. Authentication/Authorization - Invalid keys, expired tokens, permissions
5. Resource Exhaustion - Memory leaks, connection leaks, thread pool saturation
"""

import asyncio
import pytest
import aiohttp
import time
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from datetime import datetime, timedelta
import json
import tempfile
import os
import gc
import threading
from concurrent.futures import ThreadPoolExecutor
import psutil
from typing import Dict, Any, List, Optional

# Pattern mining imports
from pattern_mining.ml.gemini_client import GeminiClient, GeminiRateLimiter, RequestStatus
from pattern_mining.database.connection import DatabaseConnection
from pattern_mining.cache.redis_client import RedisClient
from pattern_mining.api.main import create_app
from pattern_mining.config.settings import get_settings
from pattern_mining.models.api import PatternDetectionRequest
from pattern_mining.utils.validation import DataValidator
from pattern_mining.security.auth import <PERSON><PERSON><PERSON><PERSON><PERSON>
from pattern_mining.ml.gemini_integration import GeminiIntegration

# Test utilities
from tests.utils.generators import generate_large_code_sample
from tests.utils.assertions import assert_error_response, assert_cleanup_completed


class TestNetworkFailures:
    """Test network failure scenarios."""
    
    @pytest.fixture
    def mock_gemini_config(self):
        """Create mock Gemini configuration."""
        config = Mock()
        config.api_key = Mock()
        config.api_key.get_secret_value.return_value = "test_key"
        config.requests_per_minute = 60
        config.tokens_per_minute = 60000
        config.concurrent_requests = 10
        config.max_retries = 3
        config.retry_delay = 1.0
        config.retry_exponential_base = 2.0
        config.request_timeout = 30
        return config
    
    @pytest.mark.asyncio
    async def test_gemini_connection_timeout(self, mock_gemini_config):
        """Test Gemini API connection timeout handling."""
        with patch('pattern_mining.ml.gemini_client.get_gemini_config', return_value=mock_gemini_config):
            with patch('pattern_mining.ml.gemini_client.genai.configure'):
                client = GeminiClient(mock_gemini_config)
                
                # Mock aiohttp session timeout
                with patch.object(client, 'session') as mock_session:
                    mock_session.post.side_effect = asyncio.TimeoutError("Connection timeout")
                    
                    with pytest.raises(Exception) as exc_info:
                        await client.generate_content("test prompt")
                    
                    # Verify timeout error is properly handled
                    assert "timeout" in str(exc_info.value).lower()
                    
                    # Verify metrics are updated
                    metrics = client.get_metrics()
                    assert metrics["failed_requests"] > 0
                    assert metrics["success_rate"] == 0.0
    
    @pytest.mark.asyncio
    async def test_gemini_connection_refused(self, mock_gemini_config):
        """Test Gemini API connection refused handling."""
        with patch('pattern_mining.ml.gemini_client.get_gemini_config', return_value=mock_gemini_config):
            with patch('pattern_mining.ml.gemini_client.genai.configure'):
                client = GeminiClient(mock_gemini_config)
                
                # Mock connection refused error
                connection_error = aiohttp.ClientConnectorError(
                    connection_key=Mock(), 
                    os_error=ConnectionRefusedError("Connection refused")
                )
                
                with patch.object(client, 'session') as mock_session:
                    mock_session.post.side_effect = connection_error
                    
                    with pytest.raises(Exception) as exc_info:
                        await client.generate_content("test prompt")
                    
                    # Verify connection error is properly handled
                    assert "connection" in str(exc_info.value).lower()
    
    @pytest.mark.asyncio
    async def test_dns_resolution_failure(self, mock_gemini_config):
        """Test DNS resolution failure handling."""
        with patch('pattern_mining.ml.gemini_client.get_gemini_config', return_value=mock_gemini_config):
            with patch('pattern_mining.ml.gemini_client.genai.configure'):
                client = GeminiClient(mock_gemini_config)
                
                # Mock DNS resolution error
                dns_error = aiohttp.ClientConnectorError(
                    connection_key=Mock(),
                    os_error=OSError("Name or service not known")
                )
                
                with patch.object(client, 'session') as mock_session:
                    mock_session.post.side_effect = dns_error
                    
                    with pytest.raises(Exception) as exc_info:
                        await client.generate_content("test prompt")
                    
                    # Verify DNS error is properly handled
                    assert "name" in str(exc_info.value).lower() or "dns" in str(exc_info.value).lower()
    
    @pytest.mark.asyncio
    async def test_database_connection_drop(self, async_db_engine):
        """Test database connection drop handling."""
        db_connection = DatabaseConnection()
        
        # Simulate connection drop by closing the engine
        await async_db_engine.dispose()
        
        with pytest.raises(Exception) as exc_info:
            async with db_connection.get_session() as session:
                # Attempt to execute a query with dropped connection
                await session.execute("SELECT 1")
        
        # Verify connection error is properly handled
        assert "connection" in str(exc_info.value).lower() or "database" in str(exc_info.value).lower()
    
    @pytest.mark.asyncio
    async def test_redis_connection_failure(self):
        """Test Redis connection failure handling."""
        # Use invalid Redis URL to simulate connection failure
        redis_client = RedisClient(redis_url="redis://invalid-host:6379")
        
        with pytest.raises(Exception) as exc_info:
            await redis_client.ping()
        
        # Verify Redis connection error is properly handled
        assert "connection" in str(exc_info.value).lower() or "redis" in str(exc_info.value).lower()
    
    @pytest.mark.asyncio
    async def test_network_partition_simulation(self, mock_gemini_config):
        """Test network partition handling."""
        with patch('pattern_mining.ml.gemini_client.get_gemini_config', return_value=mock_gemini_config):
            with patch('pattern_mining.ml.gemini_client.genai.configure'):
                client = GeminiClient(mock_gemini_config)
                
                # Simulate network partition with intermittent failures
                failure_count = 0
                async def mock_request(*args, **kwargs):
                    nonlocal failure_count
                    failure_count += 1
                    if failure_count <= 2:
                        raise aiohttp.ClientConnectorError(
                            connection_key=Mock(),
                            os_error=OSError("Network is unreachable")
                        )
                    return Mock(status=200, json=AsyncMock(return_value={"candidates": [{"content": {"parts": [{"text": "response"}]}}]}))
                
                with patch.object(client, 'session') as mock_session:
                    mock_session.post = mock_request
                    
                    # Should succeed after retries
                    result = await client.generate_content("test prompt")
                    assert result is not None
                    
                    # Verify retry mechanism worked
                    assert failure_count > 1


class TestServiceDegradation:
    """Test service degradation scenarios."""
    
    @pytest.mark.asyncio
    async def test_gemini_rate_limiting(self):
        """Test Gemini API rate limiting handling."""
        limiter = GeminiRateLimiter(requests_per_minute=1, tokens_per_minute=1000)
        
        # Exhaust rate limit
        await limiter.acquire(1000)
        
        # Next request should be rate limited
        start_time = time.time()
        result = await limiter.acquire(100)
        end_time = time.time()
        
        # Should either fail or wait
        if not result:
            # Rate limit exceeded - this is expected behavior
            assert True
        else:
            # If it succeeded, it should have waited
            assert end_time - start_time > 0.5
    
    @pytest.mark.asyncio
    async def test_database_connection_pool_exhaustion(self, async_db_engine):
        """Test database connection pool exhaustion."""
        # Create many concurrent database connections
        tasks = []
        
        async def create_connection():
            try:
                async with async_db_engine.connect() as conn:
                    await asyncio.sleep(2)  # Hold connection for 2 seconds
                    return True
            except Exception as e:
                return str(e)
        
        # Create more connections than pool size
        for _ in range(15):  # Default pool size is usually 10
            tasks.append(asyncio.create_task(create_connection()))
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Some connections should fail due to pool exhaustion
        failed_connections = [r for r in results if isinstance(r, str) and "pool" in r.lower()]
        assert len(failed_connections) > 0 or any(isinstance(r, Exception) for r in results)
    
    @pytest.mark.asyncio
    async def test_memory_pressure_handling(self):
        """Test memory pressure scenario."""
        initial_memory = psutil.Process().memory_info().rss
        large_objects = []
        
        try:
            # Allocate large amounts of memory
            for i in range(100):
                # Create 10MB objects
                large_obj = bytearray(10 * 1024 * 1024)
                large_objects.append(large_obj)
            
            current_memory = psutil.Process().memory_info().rss
            memory_increase = (current_memory - initial_memory) / 1024 / 1024
            
            # Verify memory increased significantly (>500MB)
            assert memory_increase > 500
            
            # Test that service still responds under memory pressure
            validator = DataValidator()
            result = validator.validate_code_input("def test(): pass", "python")
            assert result is not None
            
        finally:
            # Cleanup memory
            large_objects.clear()
            gc.collect()
            
            # Verify memory was released
            final_memory = psutil.Process().memory_info().rss
            memory_released = (current_memory - final_memory) / 1024 / 1024
            assert memory_released > 400  # Most memory should be released
    
    @pytest.mark.asyncio
    async def test_cpu_resource_exhaustion(self):
        """Test CPU resource exhaustion scenario."""
        # Create CPU-intensive tasks
        def cpu_intensive_task():
            # Perform CPU-intensive computation
            result = 0
            for i in range(1000000):
                result += i ** 2
            return result
        
        # Start multiple CPU-intensive threads
        with ThreadPoolExecutor(max_workers=psutil.cpu_count() * 2) as executor:
            futures = [executor.submit(cpu_intensive_task) for _ in range(psutil.cpu_count() * 2)]
            
            # While CPU is under pressure, test service responsiveness
            start_time = time.time()
            validator = DataValidator()
            result = validator.validate_code_input("def test(): pass", "python")
            end_time = time.time()
            
            # Service should still respond, even if slower
            assert result is not None
            response_time = end_time - start_time
            
            # Response time might be slower but should still be reasonable
            assert response_time < 10.0  # Maximum 10 seconds
            
            # Wait for CPU tasks to complete
            for future in futures:
                future.result()
    
    @pytest.mark.asyncio
    async def test_disk_space_limitation(self):
        """Test disk space limitation handling."""
        # Create temporary directory
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_file = os.path.join(temp_dir, "large_file.tmp")
            
            try:
                # Try to write a large file (100MB)
                with open(temp_file, "wb") as f:
                    # Write in chunks to avoid memory issues
                    chunk = b"0" * 1024 * 1024  # 1MB chunk
                    for _ in range(100):  # 100MB total
                        f.write(chunk)
                
                # Verify file was created
                assert os.path.exists(temp_file)
                assert os.path.getsize(temp_file) == 100 * 1024 * 1024
                
            except OSError as e:
                # If disk space is limited, this is expected
                if "no space left" in str(e).lower():
                    pytest.skip("Insufficient disk space for test")
                else:
                    raise
            
            # Test service behavior with limited disk space
            validator = DataValidator()
            result = validator.validate_code_input("def test(): pass", "python")
            assert result is not None


class TestDataValidationFailures:
    """Test data validation failure scenarios."""
    
    @pytest.fixture
    def validator(self):
        """Create data validator instance."""
        return DataValidator()
    
    def test_malformed_json_input(self, validator):
        """Test malformed JSON input handling."""
        malformed_json = '{"code": "def test():", "language": "python"'  # Missing closing brace
        
        with pytest.raises(Exception) as exc_info:
            json.loads(malformed_json)
        
        # Verify JSON parsing error is properly handled
        assert "json" in str(exc_info.value).lower() or "parse" in str(exc_info.value).lower()
    
    def test_invalid_code_format(self, validator):
        """Test invalid code format handling."""
        # Test with various invalid code samples
        invalid_codes = [
            "",  # Empty code
            None,  # None value
            123,  # Non-string type
            "def unclosed_function(",  # Syntax error
            "import os; os.system('rm -rf /')",  # Malicious code
        ]
        
        for invalid_code in invalid_codes:
            try:
                result = validator.validate_code_input(invalid_code, "python")
                # If validation passes, result should indicate issues
                if result:
                    assert result.get("valid") is False or result.get("errors")
            except Exception as e:
                # Exception is acceptable for invalid input
                assert "invalid" in str(e).lower() or "error" in str(e).lower()
    
    def test_oversized_payload(self, validator):
        """Test oversized payload handling."""
        # Generate very large code sample (>10MB)
        large_code = generate_large_code_sample(size_mb=10)
        
        with pytest.raises(Exception) as exc_info:
            validator.validate_code_input(large_code, "python")
        
        # Verify size limit error is properly handled
        assert "size" in str(exc_info.value).lower() or "large" in str(exc_info.value).lower()
    
    def test_invalid_language_specification(self, validator):
        """Test invalid language specification handling."""
        invalid_languages = [
            "",  # Empty language
            None,  # None value
            "invalid_lang",  # Unsupported language
            123,  # Non-string type
            "javascript;python",  # Multiple languages
        ]
        
        for invalid_lang in invalid_languages:
            with pytest.raises(Exception) as exc_info:
                validator.validate_code_input("def test(): pass", invalid_lang)
            
            # Verify language validation error
            assert "language" in str(exc_info.value).lower() or "invalid" in str(exc_info.value).lower()
    
    def test_encoding_issues(self, validator):
        """Test encoding issue handling."""
        # Test with various problematic encodings
        problematic_strings = [
            b"\xff\xfe\x00\x00",  # Invalid UTF-8 bytes
            "café🌟测试",  # Unicode characters
            "\x00\x01\x02\x03",  # Control characters
            "def test():\n\tprint('hello\udcworld')",  # Surrogate characters
        ]
        
        for problematic_string in problematic_strings:
            try:
                result = validator.validate_code_input(problematic_string, "python")
                # Should either handle gracefully or raise appropriate error
                assert result is not None or isinstance(result, dict)
            except (UnicodeDecodeError, UnicodeEncodeError) as e:
                # Unicode errors are acceptable
                assert "unicode" in str(e).lower() or "encoding" in str(e).lower()
    
    def test_schema_violations(self, validator):
        """Test schema violation handling."""
        # Test PatternDetectionRequest with invalid schemas
        invalid_requests = [
            {"code": "test", "language": "python", "invalid_field": "value"},
            {"language": "python"},  # Missing required code field
            {"code": "test"},  # Missing required language field
            {"code": "test", "language": "python", "detection_types": ["invalid_type"]},
        ]
        
        for invalid_req in invalid_requests:
            with pytest.raises(Exception) as exc_info:
                PatternDetectionRequest(**invalid_req)
            
            # Verify validation error
            assert "validation" in str(exc_info.value).lower() or "field" in str(exc_info.value).lower()


class TestAuthenticationFailures:
    """Test authentication and authorization failure scenarios."""
    
    @pytest.fixture
    def jwt_handler(self):
        """Create JWT handler instance."""
        return JWTHandler(secret_key="test-secret-key")
    
    def test_invalid_api_key(self, jwt_handler):
        """Test invalid API key handling."""
        invalid_keys = [
            "",  # Empty key
            None,  # None value
            "invalid-key",  # Wrong format
            "Bearer invalid-token",  # Invalid bearer token
            "Basic invalid-basic",  # Invalid basic auth
        ]
        
        for invalid_key in invalid_keys:
            with pytest.raises(Exception) as exc_info:
                jwt_handler.verify_token(invalid_key)
            
            # Verify authentication error
            assert "authentication" in str(exc_info.value).lower() or "invalid" in str(exc_info.value).lower()
    
    def test_expired_jwt_token(self, jwt_handler):
        """Test expired JWT token handling."""
        # Create expired token
        expired_payload = {
            "user_id": "test-user",
            "exp": datetime.utcnow() - timedelta(hours=1)  # Expired 1 hour ago
        }
        
        # This would normally require actual JWT encoding
        # For testing, we'll simulate the expired token scenario
        with pytest.raises(Exception) as exc_info:
            # Simulate expired token verification
            if expired_payload["exp"] < datetime.utcnow():
                raise ValueError("Token has expired")
        
        assert "expired" in str(exc_info.value).lower()
    
    def test_insufficient_permissions(self, jwt_handler):
        """Test insufficient permissions handling."""
        # Simulate user without required permissions
        user_payload = {
            "user_id": "test-user",
            "permissions": ["read"],  # Missing 'write' permission
            "exp": datetime.utcnow() + timedelta(hours=1)
        }
        
        required_permission = "write"
        
        # Check if user has required permission
        if required_permission not in user_payload.get("permissions", []):
            with pytest.raises(Exception) as exc_info:
                raise PermissionError(f"Insufficient permissions: {required_permission} required")
        
        assert "permission" in str(exc_info.value).lower()
    
    def test_rate_limit_exceeded(self):
        """Test rate limit exceeded handling."""
        from pattern_mining.security.rate_limiting import RateLimiter
        
        # Create strict rate limiter (1 request per minute)
        rate_limiter = RateLimiter(max_requests=1, window_seconds=60)
        
        # First request should succeed
        assert rate_limiter.is_allowed("test-user")
        
        # Second request should be blocked
        with pytest.raises(Exception) as exc_info:
            if not rate_limiter.is_allowed("test-user"):
                raise Exception("Rate limit exceeded")
        
        assert "rate limit" in str(exc_info.value).lower()
    
    def test_blocked_ip_address(self):
        """Test blocked IP address handling."""
        blocked_ips = ["*************", "*********", "***********"]
        test_ip = "*************"
        
        if test_ip in blocked_ips:
            with pytest.raises(Exception) as exc_info:
                raise Exception(f"IP address {test_ip} is blocked")
        
        assert "blocked" in str(exc_info.value).lower()


class TestResourceExhaustion:
    """Test resource exhaustion scenarios."""
    
    @pytest.mark.asyncio
    async def test_memory_leak_detection(self):
        """Test memory leak detection and handling."""
        initial_memory = psutil.Process().memory_info().rss
        
        # Simulate memory leak by creating objects that aren't properly cleaned up
        leaked_objects = []
        
        for i in range(1000):
            # Create objects that might leak
            obj = {
                'data': bytearray(1024 * 1024),  # 1MB per object
                'id': i,
                'timestamp': datetime.utcnow()
            }
            leaked_objects.append(obj)
        
        current_memory = psutil.Process().memory_info().rss
        memory_increase = (current_memory - initial_memory) / 1024 / 1024
        
        # Verify memory increased significantly
        assert memory_increase > 500  # Should be ~1GB increase
        
        # Test memory leak detection mechanism
        memory_threshold = 1000 * 1024 * 1024  # 1GB threshold
        if current_memory > memory_threshold:
            # Trigger cleanup mechanism
            leaked_objects.clear()
            gc.collect()
            
            # Verify cleanup worked
            final_memory = psutil.Process().memory_info().rss
            memory_released = (current_memory - final_memory) / 1024 / 1024
            assert memory_released > 400  # Most memory should be released
    
    @pytest.mark.asyncio
    async def test_connection_leak_detection(self, mock_gemini_config):
        """Test connection leak detection and handling."""
        connection_count = 0
        max_connections = 10
        
        # Simulate connection leak
        connections = []
        
        with patch('pattern_mining.ml.gemini_client.get_gemini_config', return_value=mock_gemini_config):
            with patch('pattern_mining.ml.gemini_client.genai.configure'):
                try:
                    for i in range(max_connections + 5):  # Exceed max connections
                        client = GeminiClient(mock_gemini_config)
                        connections.append(client)
                        connection_count += 1
                        
                        if connection_count > max_connections:
                            # Should trigger connection limit error
                            raise Exception(f"Connection limit exceeded: {connection_count}/{max_connections}")
                
                except Exception as e:
                    assert "connection limit" in str(e).lower()
                
                finally:
                    # Cleanup connections
                    for client in connections:
                        if hasattr(client, 'close'):
                            await client.close()
    
    def test_file_descriptor_exhaustion(self):
        """Test file descriptor exhaustion handling."""
        import resource
        
        # Get current file descriptor limit
        soft_limit, hard_limit = resource.getrlimit(resource.RLIMIT_NOFILE)
        
        opened_files = []
        try:
            # Try to open many files to exhaust file descriptors
            for i in range(min(soft_limit + 100, 1000)):  # Don't go crazy with file creation
                try:
                    temp_file = tempfile.NamedTemporaryFile(mode='w', delete=False)
                    opened_files.append(temp_file)
                except OSError as e:
                    if "too many open files" in str(e).lower():
                        # Expected behavior when file descriptors are exhausted
                        assert len(opened_files) > 100  # Should have opened many files
                        break
                    else:
                        raise
        
        finally:
            # Cleanup opened files
            for file_obj in opened_files:
                try:
                    file_obj.close()
                    os.unlink(file_obj.name)
                except:
                    pass  # Ignore cleanup errors
    
    @pytest.mark.asyncio
    async def test_thread_pool_saturation(self):
        """Test thread pool saturation handling."""
        max_workers = 5
        
        # Create thread pool executor with limited workers
        executor = ThreadPoolExecutor(max_workers=max_workers)
        
        def blocking_task():
            time.sleep(2)  # Block for 2 seconds
            return "completed"
        
        # Submit more tasks than available workers
        futures = []
        for i in range(max_workers * 2):  # Double the worker count
            future = executor.submit(blocking_task)
            futures.append(future)
        
        # Some tasks should be queued
        completed_immediately = 0
        for future in futures:
            if future.done():
                completed_immediately += 1
        
        # Not all tasks should be completed immediately
        assert completed_immediately < len(futures)
        
        # Wait for all tasks to complete
        for future in futures:
            result = future.result(timeout=10)
            assert result == "completed"
        
        executor.shutdown(wait=True)
    
    @pytest.mark.asyncio
    async def test_queue_overflow(self):
        """Test queue overflow handling."""
        from asyncio import Queue
        
        # Create small queue
        queue = Queue(maxsize=5)
        
        # Fill the queue
        for i in range(5):
            await queue.put(f"item_{i}")
        
        # Queue should be full
        assert queue.full()
        
        # Try to add one more item - should block or raise exception
        try:
            await asyncio.wait_for(queue.put("overflow_item"), timeout=0.1)
            # If it succeeded without timeout, the queue wasn't actually full
            assert False, "Queue should have been full"
        except asyncio.TimeoutError:
            # Expected behavior - queue is full and put() would block
            assert True
        
        # Verify queue is still functional after overflow attempt
        item = await queue.get()
        assert item == "item_0"
        
        # Now we should be able to add the overflow item
        await queue.put("overflow_item")
        assert queue.qsize() == 5


class TestErrorRecoveryAndGracefulDegradation:
    """Test error recovery and graceful degradation scenarios."""
    
    @pytest.mark.asyncio
    async def test_circuit_breaker_pattern(self, mock_gemini_config):
        """Test circuit breaker pattern implementation."""
        from pattern_mining.utils.circuit_breaker import CircuitBreaker
        
        # Create circuit breaker with low thresholds for testing
        circuit_breaker = CircuitBreaker(
            failure_threshold=3,
            recovery_timeout=1,
            expected_exception=Exception
        )
        
        failure_count = 0
        
        async def failing_operation():
            nonlocal failure_count
            failure_count += 1
            if failure_count <= 5:  # Fail first 5 attempts
                raise Exception("Service unavailable")
            return "success"
        
        # First few calls should fail, then circuit should open
        for i in range(6):
            try:
                result = await circuit_breaker.call(failing_operation)
                if result == "success":
                    break
            except Exception as e:
                if "circuit breaker" in str(e).lower():
                    # Circuit breaker opened - this is expected
                    assert i >= 3  # Should open after failure threshold
                    break
        
        # Wait for recovery timeout
        await asyncio.sleep(1.1)
        
        # Circuit should be half-open and allow one attempt
        result = await circuit_breaker.call(failing_operation)
        assert result == "success"
    
    @pytest.mark.asyncio
    async def test_retry_logic_with_exponential_backoff(self, mock_gemini_config):
        """Test retry logic with exponential backoff."""
        attempt_count = 0
        
        async def flaky_operation():
            nonlocal attempt_count
            attempt_count += 1
            if attempt_count < 3:
                raise Exception("Temporary failure")
            return "success"
        
        # Implement retry with exponential backoff
        max_retries = 3
        base_delay = 0.1
        
        for attempt in range(max_retries):
            try:
                result = await flaky_operation()
                assert result == "success"
                break
            except Exception as e:
                if attempt == max_retries - 1:
                    # Last attempt failed
                    raise
                
                # Exponential backoff delay
                delay = base_delay * (2 ** attempt)
                await asyncio.sleep(delay)
        
        # Verify it took multiple attempts
        assert attempt_count == 3
    
    @pytest.mark.asyncio
    async def test_fallback_mechanism(self, mock_gemini_config):
        """Test fallback mechanism when primary service fails."""
        primary_service_available = False
        fallback_service_available = True
        
        async def primary_service():
            if not primary_service_available:
                raise Exception("Primary service unavailable")
            return {"source": "primary", "result": "primary_result"}
        
        async def fallback_service():
            if not fallback_service_available:
                raise Exception("Fallback service unavailable")
            return {"source": "fallback", "result": "fallback_result"}
        
        # Try primary service, fall back to secondary
        try:
            result = await primary_service()
        except Exception:
            result = await fallback_service()
        
        assert result["source"] == "fallback"
        assert result["result"] == "fallback_result"
    
    @pytest.mark.asyncio
    async def test_graceful_degradation_gemini_unavailable(self):
        """Test graceful degradation when Gemini API is unavailable."""
        # Simulate Gemini API unavailable
        gemini_available = False
        
        # Mock integration that falls back to local analysis
        integration = Mock()
        integration.gemini_available = gemini_available
        
        if not integration.gemini_available:
            # Fall back to local analysis
            result = {
                "source": "local",
                "patterns": [],
                "confidence": 0.7,
                "note": "Gemini API unavailable, using local analysis"
            }
        else:
            result = {
                "source": "gemini",
                "patterns": [],
                "confidence": 0.9
            }
        
        assert result["source"] == "local"
        assert "unavailable" in result["note"]
    
    @pytest.mark.asyncio
    async def test_cache_as_fallback(self, mock_redis_client):
        """Test using cache as fallback when services are unavailable."""
        # Simulate service unavailable but cache available
        service_available = False
        cache_data = {
            "patterns": ["cached_pattern"],
            "timestamp": datetime.utcnow().isoformat(),
            "source": "cache"
        }
        
        # Mock Redis client with cached data
        mock_redis_client.get.return_value = json.dumps(cache_data)
        
        if not service_available:
            # Try to get from cache
            cached_result = await mock_redis_client.get("analysis:test")
            if cached_result:
                result = json.loads(cached_result)
                result["note"] = "Service unavailable, serving from cache"
            else:
                result = {"error": "Service and cache unavailable"}
        
        assert result["source"] == "cache"
        assert "cache" in result["note"]
    
    @pytest.mark.asyncio
    async def test_partial_service_availability(self):
        """Test handling partial service availability."""
        services = {
            "gemini": False,  # Unavailable
            "database": True,  # Available
            "redis": True,    # Available
            "local_ml": True  # Available
        }
        
        available_services = [name for name, status in services.items() if status]
        unavailable_services = [name for name, status in services.items() if not status]
        
        # Service should work with partial availability
        if "database" in available_services and "local_ml" in available_services:
            result = {
                "status": "degraded",
                "available_services": available_services,
                "unavailable_services": unavailable_services,
                "capabilities": ["local_analysis", "data_storage"]
            }
        else:
            result = {
                "status": "unavailable",
                "error": "Critical services unavailable"
            }
        
        assert result["status"] == "degraded"
        assert "gemini" in result["unavailable_services"]
        assert "database" in result["available_services"]


if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"])