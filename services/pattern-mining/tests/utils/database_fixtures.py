"""
Database Fixtures Utility for Advanced Integration Testing
Phase 2 Enhancement: Large dataset generators and performance test data

This module provides comprehensive database fixture generation for advanced
integration testing, including large datasets, complex relationships, 
performance data creators, migration test data, and corruption simulation.

Key Features:
- Large dataset generation with realistic patterns
- Complex relationship modeling
- Performance test data with varying characteristics
- Migration test scenarios with before/after states
- Corruption simulation for integrity testing
- Memory-efficient batch processing
- Configurable data distribution patterns

Usage:
    fixtures = DatabaseFixtures()
    
    # Generate large repository dataset
    repos = await fixtures.generate_repositories(count=10000)
    
    # Generate patterns with complex relationships
    patterns = await fixtures.generate_patterns_with_relationships(
        repository_count=100, patterns_per_repo=50
    )
    
    # Create performance test data
    perf_data = await fixtures.generate_performance_test_data(
        size="large", complexity="high"
    )
"""

import asyncio
import random
import string
import json
import hashlib
import uuid
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple, Generator, AsyncGenerator
from dataclasses import dataclass, field
from enum import Enum
import numpy as np
from faker import Faker

from pattern_mining.database.models import (
    <PERSON><PERSON><PERSON><PERSON>ult, RepositoryAnalysis, ModelRegistry, FeatureVector
)


class DatasetSize(Enum):
    """Dataset size categories."""
    SMALL = "small"      # < 1K records
    MEDIUM = "medium"    # 1K - 10K records  
    LARGE = "large"      # 10K - 100K records
    XLARGE = "xlarge"    # 100K+ records


class DataComplexity(Enum):
    """Data complexity levels."""
    SIMPLE = "simple"        # Basic structures, minimal relationships
    MODERATE = "moderate"    # Some relationships, varied patterns
    COMPLEX = "complex"      # Deep relationships, complex patterns
    EXTREME = "extreme"      # Maximum complexity, stress test scenarios


@dataclass
class DataGenerationConfig:
    """Configuration for data generation."""
    size: DatasetSize = DatasetSize.MEDIUM
    complexity: DataComplexity = DataComplexity.MODERATE
    distribution: str = "normal"  # normal, uniform, skewed, bimodal
    relationship_density: float = 0.7  # 0.0 = no relationships, 1.0 = maximum relationships
    corruption_rate: float = 0.0  # 0.0 = no corruption, 1.0 = all corrupted
    batch_size: int = 1000  # For memory-efficient processing
    seed: Optional[int] = None  # For reproducible data generation
    include_metadata: bool = True
    include_timestamps: bool = True
    include_checksums: bool = False


@dataclass
class GeneratedDataset:
    """Container for generated dataset."""
    repositories: List[Dict[str, Any]] = field(default_factory=list)
    patterns: List[Dict[str, Any]] = field(default_factory=list)
    models: List[Dict[str, Any]] = field(default_factory=list)
    vectors: List[Dict[str, Any]] = field(default_factory=list)
    relationships: Dict[str, List[str]] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)
    generation_time: float = 0.0
    total_records: int = 0


class DatabaseFixtures:
    """Advanced database fixture generator for integration testing."""
    
    def __init__(self, config: Optional[DataGenerationConfig] = None):
        """Initialize fixture generator with configuration."""
        self.config = config or DataGenerationConfig()
        self.fake = Faker()
        
        if self.config.seed:
            Faker.seed(self.config.seed)
            random.seed(self.config.seed)
            np.random.seed(self.config.seed)
        
        # Data distribution mappings
        self._size_mappings = {
            DatasetSize.SMALL: {"repos": 50, "patterns_per_repo": 10, "models": 3},
            DatasetSize.MEDIUM: {"repos": 500, "patterns_per_repo": 20, "models": 10},
            DatasetSize.LARGE: {"repos": 2000, "patterns_per_repo": 50, "models": 25},
            DatasetSize.XLARGE: {"repos": 10000, "patterns_per_repo": 100, "models": 100}
        }
        
        # Pattern types with realistic distributions
        self._pattern_types = [
            "design_pattern", "code_smell", "antipattern", "architecture_pattern",
            "behavioral_pattern", "creational_pattern", "structural_pattern",
            "concurrency_pattern", "security_pattern", "performance_pattern"
        ]
        
        # Programming languages with realistic usage distribution
        self._languages = {
            "python": 0.25, "javascript": 0.20, "java": 0.15, "typescript": 0.12,
            "go": 0.08, "rust": 0.06, "cpp": 0.05, "csharp": 0.04, "ruby": 0.03, "php": 0.02
        }
        
        # Repository characteristics
        self._repo_characteristics = {
            "open_source": 0.7, "enterprise": 0.2, "academic": 0.1
        }
        
    async def generate_comprehensive_dataset(self) -> GeneratedDataset:
        """Generate comprehensive dataset with all entity types."""
        start_time = asyncio.get_event_loop().time()
        
        print(f"Generating comprehensive dataset (size: {self.config.size.value}, complexity: {self.config.complexity.value})...")
        
        dataset = GeneratedDataset()
        
        # Get size configuration
        size_config = self._size_mappings[self.config.size]
        
        # Generate repositories
        print(f"Generating {size_config['repos']} repositories...")
        dataset.repositories = await self._generate_repositories(size_config["repos"])
        
        # Generate patterns with relationships
        print(f"Generating patterns ({size_config['patterns_per_repo']} per repository)...")
        dataset.patterns = await self._generate_patterns_with_relationships(
            dataset.repositories, size_config["patterns_per_repo"]
        )
        
        # Generate models
        print(f"Generating {size_config['models']} ML models...")
        dataset.models = await self._generate_models(size_config["models"])
        
        # Generate feature vectors (subset of patterns)
        vector_count = min(len(dataset.patterns), size_config["repos"] * 10)
        print(f"Generating {vector_count} feature vectors...")
        dataset.vectors = await self._generate_feature_vectors(dataset.patterns, vector_count)
        
        # Generate relationships
        dataset.relationships = await self._generate_relationships(dataset)
        
        # Calculate metrics
        dataset.total_records = (
            len(dataset.repositories) + len(dataset.patterns) + 
            len(dataset.models) + len(dataset.vectors)
        )
        dataset.generation_time = asyncio.get_event_loop().time() - start_time
        
        # Add metadata
        dataset.metadata = {
            "config": {
                "size": self.config.size.value,
                "complexity": self.config.complexity.value,
                "distribution": self.config.distribution,
                "relationship_density": self.config.relationship_density,
                "corruption_rate": self.config.corruption_rate
            },
            "statistics": {
                "repositories": len(dataset.repositories),
                "patterns": len(dataset.patterns),
                "models": len(dataset.models),
                "vectors": len(dataset.vectors),
                "relationships": sum(len(v) for v in dataset.relationships.values()),
                "generation_time_seconds": dataset.generation_time,
                "records_per_second": dataset.total_records / dataset.generation_time if dataset.generation_time > 0 else 0
            },
            "quality_metrics": await self._calculate_quality_metrics(dataset)
        }
        
        print(f"Dataset generation complete: {dataset.total_records} records in {dataset.generation_time:.2f}s")
        return dataset
    
    async def _generate_repositories(self, count: int) -> List[Dict[str, Any]]:
        """Generate repository analysis records with realistic characteristics."""
        repositories = []
        
        # Pre-generate realistic repository names
        org_names = [self.fake.company().replace(" ", "").lower() for _ in range(count // 10)]
        project_names = [self.fake.catch_phrase().replace(" ", "-").lower() for _ in range(count)]
        
        for i in range(count):
            # Select language based on realistic distribution
            language = self._weighted_choice(self._languages)
            
            # Repository characteristics based on language and type
            if language in ["python", "javascript", "java"]:
                files_range = (50, 2000)
                lines_range = (2000, 500000)
            elif language in ["go", "rust", "cpp"]:
                files_range = (20, 800)
                lines_range = (1000, 200000)
            else:
                files_range = (10, 500)
                lines_range = (500, 100000)
            
            # Generate realistic repository URL
            org = random.choice(org_names)
            project = random.choice(project_names) + (f"-{i}" if random.random() < 0.3 else "")
            repository_url = f"https://github.com/{org}/{project}"
            
            # File and line counts with realistic correlation
            total_files = self._generate_correlated_value(files_range, self.config.distribution)
            total_lines = self._generate_correlated_value(
                lines_range, self.config.distribution, correlation_base=total_files
            )
            
            # Analysis timestamp with realistic distribution (more recent analyses)
            days_ago = max(1, int(np.random.exponential(30)))  # Exponential distribution, mean 30 days
            analysis_timestamp = datetime.utcnow() - timedelta(days=days_ago)
            
            # Repository status with realistic distribution
            status_weights = {"completed": 0.8, "in_progress": 0.1, "failed": 0.05, "queued": 0.05}
            status = self._weighted_choice(status_weights)
            
            # Generate metadata based on complexity level
            metadata = {}
            if self.config.include_metadata:
                metadata = await self._generate_repository_metadata(language, total_files, total_lines)
            
            # Apply corruption if configured
            repository_data = {
                "repository_url": repository_url,
                "language": language,
                "total_files": total_files,
                "total_lines": total_lines,
                "analysis_timestamp": analysis_timestamp,
                "status": status,
                "metadata_": metadata
            }
            
            if self.config.corruption_rate > 0 and random.random() < self.config.corruption_rate:
                repository_data = await self._corrupt_repository_data(repository_data)
            
            if self.config.include_checksums:
                repository_data["_checksum"] = self._calculate_checksum(repository_data)
            
            repositories.append(repository_data)
            
            # Yield control for memory management
            if i % self.config.batch_size == 0:
                await asyncio.sleep(0)
        
        return repositories
    
    async def _generate_patterns_with_relationships(
        self, repositories: List[Dict[str, Any]], patterns_per_repo: int
    ) -> List[Dict[str, Any]]:
        """Generate pattern results with complex relationships."""
        patterns = []
        
        for repo_idx, repository in enumerate(repositories):
            # Vary patterns per repository based on repository characteristics
            if self.config.complexity == DataComplexity.SIMPLE:
                pattern_count = max(1, patterns_per_repo // 2)
            elif self.config.complexity == DataComplexity.EXTREME:
                pattern_count = patterns_per_repo * 2
            else:
                pattern_count = int(np.random.normal(patterns_per_repo, patterns_per_repo * 0.2))
                pattern_count = max(1, min(pattern_count, patterns_per_repo * 3))
            
            # Generate patterns for this repository
            repo_patterns = await self._generate_patterns_for_repository(
                repository, pattern_count, repo_idx
            )
            patterns.extend(repo_patterns)
            
            # Memory management
            if repo_idx % 100 == 0:
                await asyncio.sleep(0)
        
        return patterns
    
    async def _generate_patterns_for_repository(
        self, repository: Dict[str, Any], pattern_count: int, repo_idx: int
    ) -> List[Dict[str, Any]]:
        """Generate patterns for a specific repository."""
        patterns = []
        
        # Generate file structure for realistic pattern placement
        file_structure = await self._generate_file_structure(
            repository["language"], repository["total_files"]
        )
        
        for i in range(pattern_count):
            # Select pattern type based on language and complexity
            pattern_type = await self._select_pattern_type(repository["language"])
            
            # Generate pattern name with realistic naming
            pattern_name = await self._generate_pattern_name(pattern_type, i)
            
            # Confidence score with realistic distribution
            confidence_score = await self._generate_confidence_score(pattern_type)
            
            # Select file and line range
            file_info = random.choice(file_structure)
            line_start = random.randint(1, max(1, file_info["lines"] - 50))
            line_end = line_start + random.randint(5, min(50, file_info["lines"] - line_start))
            
            # Generate realistic code context
            context = await self._generate_code_context(repository["language"], pattern_type)
            
            # Generate metadata based on complexity
            metadata = {}
            if self.config.include_metadata:
                metadata = await self._generate_pattern_metadata(
                    pattern_type, repository, confidence_score
                )
            
            pattern_data = {
                "repository_id": repo_idx + 1,  # Will be replaced with actual ID in real usage
                "pattern_type": pattern_type,
                "pattern_name": pattern_name,
                "confidence_score": confidence_score,
                "file_path": file_info["path"],
                "line_start": line_start,
                "line_end": line_end,
                "context_": context,
                "metadata_": metadata
            }
            
            # Apply corruption if configured
            if self.config.corruption_rate > 0 and random.random() < self.config.corruption_rate:
                pattern_data = await self._corrupt_pattern_data(pattern_data)
            
            if self.config.include_checksums:
                pattern_data["_checksum"] = self._calculate_checksum(pattern_data)
            
            patterns.append(pattern_data)
        
        return patterns
    
    async def _generate_models(self, count: int) -> List[Dict[str, Any]]:
        """Generate ML model registry entries."""
        models = []
        
        model_types = ["classification", "regression", "clustering", "embedding", "anomaly_detection"]
        frameworks = ["scikit-learn", "tensorflow", "pytorch", "xgboost", "lightgbm"]
        
        for i in range(count):
            model_type = random.choice(model_types)
            framework = random.choice(frameworks)
            
            # Version with semantic versioning
            major = random.randint(1, 3)
            minor = random.randint(0, 20)
            patch = random.randint(0, 10)
            model_version = f"{major}.{minor}.{patch}"
            
            # Performance metrics with realistic correlations
            if model_type == "classification":
                accuracy = random.uniform(0.75, 0.98)
                f1_score = accuracy * random.uniform(0.85, 0.98)  # F1 typically lower than accuracy
            elif model_type == "regression":
                accuracy = None  # Not applicable for regression
                f1_score = None
            else:
                accuracy = random.uniform(0.65, 0.95)
                f1_score = accuracy * random.uniform(0.80, 0.95)
            
            # Training date with realistic distribution
            days_ago = max(1, int(np.random.exponential(60)))  # Mean 60 days ago
            training_date = datetime.utcnow() - timedelta(days=days_ago)
            
            # Model name
            model_name = f"{model_type}_{framework}_v{i+1}"
            
            # Model path
            model_path = f"/models/{model_type}/{framework}/{model_name}.pkl"
            
            # Generate metadata
            metadata = {}
            if self.config.include_metadata:
                metadata = await self._generate_model_metadata(model_type, framework, accuracy)
            
            model_data = {
                "model_name": model_name,
                "model_version": model_version,
                "model_type": model_type,
                "training_date": training_date,
                "accuracy": accuracy,
                "f1_score": f1_score,
                "model_path": model_path,
                "metadata_": metadata
            }
            
            if self.config.include_checksums:
                model_data["_checksum"] = self._calculate_checksum(model_data)
            
            models.append(model_data)
        
        return models
    
    async def _generate_feature_vectors(
        self, patterns: List[Dict[str, Any]], count: int
    ) -> List[Dict[str, Any]]:
        """Generate feature vectors for patterns."""
        vectors = []
        
        # Select subset of patterns for vector generation
        selected_patterns = random.sample(patterns, min(count, len(patterns)))
        
        vector_types = ["embedding", "tfidf", "word2vec", "bert", "code2vec"]
        extraction_methods = ["transformer", "cnn", "lstm", "bow", "ast_based"]
        
        for i, pattern in enumerate(selected_patterns):
            vector_type = random.choice(vector_types)
            extraction_method = random.choice(extraction_methods)
            
            # Dimensions based on vector type
            dimension_ranges = {
                "embedding": (64, 512),
                "tfidf": (100, 1000),
                "word2vec": (100, 300),
                "bert": (384, 1024),
                "code2vec": (128, 384)
            }
            
            dimensions = random.randint(*dimension_ranges[vector_type])
            
            # Generate realistic vector data
            if vector_type in ["embedding", "word2vec", "bert", "code2vec"]:
                # Normalized embeddings
                vector_data = np.random.normal(0, 1, dimensions)
                vector_data = vector_data / np.linalg.norm(vector_data)
                vector_data = vector_data.tolist()
            else:
                # Sparse vectors (TF-IDF style)
                vector_data = [0.0] * dimensions
                num_non_zero = random.randint(dimensions // 10, dimensions // 3)
                indices = random.sample(range(dimensions), num_non_zero)
                for idx in indices:
                    vector_data[idx] = random.uniform(0.1, 1.0)
            
            # Generate metadata
            metadata = {}
            if self.config.include_metadata:
                metadata = {
                    "extraction_timestamp": datetime.utcnow().isoformat(),
                    "model_version": f"1.{random.randint(0, 10)}.0",
                    "preprocessing_steps": random.choice([
                        ["tokenize", "normalize"],
                        ["tokenize", "normalize", "stem"],
                        ["tokenize", "normalize", "lemmatize"],
                        ["ast_parse", "flatten", "embed"]
                    ]),
                    "quality_score": random.uniform(0.7, 0.95)
                }
            
            vector_data_entry = {
                "pattern_id": i + 1,  # Will be replaced with actual pattern ID
                "feature_type": vector_type,
                "dimensions": dimensions,
                "vector_data": vector_data,
                "extraction_method": extraction_method,
                "metadata_": metadata
            }
            
            if self.config.include_checksums:
                vector_data_entry["_checksum"] = self._calculate_checksum(vector_data_entry)
            
            vectors.append(vector_data_entry)
        
        return vectors
    
    async def _generate_relationships(self, dataset: GeneratedDataset) -> Dict[str, List[str]]:
        """Generate relationship mappings between entities."""
        relationships = {
            "repo_to_patterns": {},
            "pattern_to_vectors": {},
            "model_dependencies": {},
            "pattern_similarities": {}
        }
        
        # Repository to patterns relationships
        for pattern in dataset.patterns:
            repo_id = str(pattern["repository_id"])
            if repo_id not in relationships["repo_to_patterns"]:
                relationships["repo_to_patterns"][repo_id] = []
            relationships["repo_to_patterns"][repo_id].append(str(pattern.get("id", len(relationships["repo_to_patterns"][repo_id]))))
        
        # Pattern to vector relationships
        for i, vector in enumerate(dataset.vectors):
            pattern_id = str(vector["pattern_id"])
            relationships["pattern_to_vectors"][pattern_id] = str(i + 1)
        
        # Model dependencies (which models were used for which patterns)
        if self.config.complexity in [DataComplexity.COMPLEX, DataComplexity.EXTREME]:
            for pattern in dataset.patterns[:100]:  # Limit for performance
                if random.random() < self.config.relationship_density:
                    pattern_id = str(pattern.get("id", random.randint(1, len(dataset.patterns))))
                    model_id = str(random.randint(1, len(dataset.models)))
                    
                    if pattern_id not in relationships["model_dependencies"]:
                        relationships["model_dependencies"][pattern_id] = []
                    relationships["model_dependencies"][pattern_id].append(model_id)
        
        # Pattern similarities (for complex relationship testing)
        if self.config.complexity == DataComplexity.EXTREME:
            similarity_count = min(1000, len(dataset.patterns) // 10)
            for _ in range(similarity_count):
                pattern1_id = str(random.randint(1, len(dataset.patterns)))
                pattern2_id = str(random.randint(1, len(dataset.patterns)))
                
                if pattern1_id != pattern2_id:
                    if pattern1_id not in relationships["pattern_similarities"]:
                        relationships["pattern_similarities"][pattern1_id] = []
                    relationships["pattern_similarities"][pattern1_id].append(pattern2_id)
        
        return relationships
    
    # Helper methods for data generation
    
    def _weighted_choice(self, choices: Dict[str, float]) -> str:
        """Make weighted random choice from dictionary."""
        total = sum(choices.values())
        r = random.uniform(0, total)
        upto = 0
        for choice, weight in choices.items():
            if upto + weight >= r:
                return choice
            upto += weight
        return list(choices.keys())[-1]
    
    def _generate_correlated_value(
        self, value_range: Tuple[int, int], distribution: str, correlation_base: Optional[int] = None
    ) -> int:
        """Generate value with specified distribution and optional correlation."""
        min_val, max_val = value_range
        
        if distribution == "uniform":
            base_value = random.randint(min_val, max_val)
        elif distribution == "normal":
            mean = (min_val + max_val) / 2
            std = (max_val - min_val) / 6  # 99.7% within range
            base_value = int(np.random.normal(mean, std))
            base_value = max(min_val, min(max_val, base_value))
        elif distribution == "skewed":
            # Right-skewed distribution (more small values)
            base_value = int(np.random.gamma(2, (max_val - min_val) / 4) + min_val)
            base_value = max(min_val, min(max_val, base_value))
        else:  # bimodal
            if random.random() < 0.5:
                base_value = random.randint(min_val, min_val + (max_val - min_val) // 3)
            else:
                base_value = random.randint(max_val - (max_val - min_val) // 3, max_val)
        
        # Apply correlation if base value provided
        if correlation_base is not None:
            correlation_factor = 0.7  # Strong correlation
            base_normalized = (correlation_base - min_val) / (max_val - min_val)
            correlated_value = base_value * (1 - correlation_factor) + (base_normalized * (max_val - min_val) + min_val) * correlation_factor
            base_value = int(correlated_value)
            base_value = max(min_val, min(max_val, base_value))
        
        return base_value
    
    async def _generate_repository_metadata(
        self, language: str, total_files: int, total_lines: int
    ) -> Dict[str, Any]:
        """Generate realistic repository metadata."""
        metadata = {
            "complexity_score": random.uniform(0.1, 1.0),
            "maintainability_index": random.uniform(0.3, 0.9),
            "test_coverage": random.uniform(0.0, 0.95),
            "documentation_coverage": random.uniform(0.1, 0.8),
            "dependency_count": random.randint(5, 200),
            "last_commit_days_ago": random.randint(1, 365),
            "contributor_count": random.randint(1, 50),
            "star_count": max(0, int(np.random.exponential(100))),
            "fork_count": max(0, int(np.random.exponential(20))),
        }
        
        # Language-specific metadata
        if language == "python":
            metadata.update({
                "python_version": random.choice(["3.8", "3.9", "3.10", "3.11", "3.12"]),
                "framework": random.choice([None, "django", "flask", "fastapi", "tornado"]),
                "package_manager": "pip"
            })
        elif language == "javascript":
            metadata.update({
                "node_version": random.choice(["16", "18", "20", "21"]),
                "framework": random.choice([None, "react", "vue", "angular", "express"]),
                "package_manager": random.choice(["npm", "yarn", "pnpm"])
            })
        elif language == "java":
            metadata.update({
                "java_version": random.choice(["11", "17", "21"]),
                "framework": random.choice([None, "spring", "struts", "hibernate"]),
                "build_tool": random.choice(["maven", "gradle"])
            })
        
        if self.config.complexity in [DataComplexity.COMPLEX, DataComplexity.EXTREME]:
            metadata.update({
                "code_quality_metrics": {
                    "cyclomatic_complexity": random.uniform(1.0, 15.0),
                    "cognitive_complexity": random.uniform(1.0, 25.0),
                    "technical_debt_ratio": random.uniform(0.0, 0.3),
                    "code_smells": random.randint(0, 100),
                    "duplicated_lines": random.uniform(0.0, 0.2)
                },
                "security_metrics": {
                    "vulnerability_count": random.randint(0, 20),
                    "security_hotspots": random.randint(0, 10),
                    "security_rating": random.choice(["A", "B", "C", "D", "E"])
                }
            })
        
        return metadata
    
    async def _select_pattern_type(self, language: str) -> str:
        """Select pattern type based on language and realistic distributions."""
        # Language-specific pattern distributions
        language_patterns = {
            "python": {
                "design_pattern": 0.25, "code_smell": 0.20, "architecture_pattern": 0.15,
                "behavioral_pattern": 0.15, "antipattern": 0.10, "security_pattern": 0.10,
                "performance_pattern": 0.05
            },
            "javascript": {
                "behavioral_pattern": 0.25, "design_pattern": 0.20, "code_smell": 0.15,
                "antipattern": 0.15, "architecture_pattern": 0.15, "performance_pattern": 0.10
            },
            "java": {
                "design_pattern": 0.30, "architecture_pattern": 0.20, "creational_pattern": 0.15,
                "structural_pattern": 0.15, "behavioral_pattern": 0.10, "code_smell": 0.10
            }
        }
        
        patterns = language_patterns.get(language, {
            pattern: 1.0 / len(self._pattern_types) for pattern in self._pattern_types
        })
        
        return self._weighted_choice(patterns)
    
    async def _generate_pattern_name(self, pattern_type: str, index: int) -> str:
        """Generate realistic pattern name."""
        type_names = {
            "design_pattern": ["Singleton", "Factory", "Observer", "Strategy", "Command", "Decorator"],
            "code_smell": ["Long Method", "Large Class", "Duplicate Code", "Dead Code", "God Class"],
            "antipattern": ["Golden Hammer", "Spaghetti Code", "Copy-Paste Programming", "Hard Coding"],
            "architecture_pattern": ["MVC", "MVP", "MVVM", "Layered", "Microservices", "Event-Driven"],
            "behavioral_pattern": ["Iterator", "Template Method", "State", "Chain of Responsibility"],
            "creational_pattern": ["Builder", "Abstract Factory", "Prototype"],
            "structural_pattern": ["Adapter", "Facade", "Composite", "Proxy"],
            "security_pattern": ["Authentication", "Authorization", "Input Validation", "Encryption"],
            "performance_pattern": ["Caching", "Lazy Loading", "Object Pooling", "Flyweight"]
        }
        
        names = type_names.get(pattern_type, ["Generic Pattern"])
        base_name = random.choice(names)
        
        # Add variation for uniqueness
        if random.random() < 0.3:
            variations = ["Variant", "Implementation", "Usage", "Instance", "Application"]
            return f"{base_name} {random.choice(variations)} {index + 1}"
        else:
            return base_name
    
    async def _generate_confidence_score(self, pattern_type: str) -> float:
        """Generate realistic confidence score based on pattern type."""
        # Different pattern types have different confidence distributions
        confidence_ranges = {
            "design_pattern": (0.7, 0.95),  # High confidence for well-defined patterns
            "code_smell": (0.6, 0.9),      # Moderate to high confidence
            "antipattern": (0.5, 0.85),    # Lower confidence, harder to detect
            "architecture_pattern": (0.8, 0.98),  # High confidence for architectural patterns
            "security_pattern": (0.6, 0.92),      # Variable confidence
            "performance_pattern": (0.55, 0.88)   # Performance patterns can be subtle
        }
        
        min_conf, max_conf = confidence_ranges.get(pattern_type, (0.5, 0.9))
        
        # Use beta distribution for realistic confidence scores
        # Beta(2, 2) gives a symmetric distribution peaked in the middle
        raw_score = np.random.beta(2, 2)
        confidence = min_conf + (max_conf - min_conf) * raw_score
        
        return round(confidence, 3)
    
    async def _generate_file_structure(self, language: str, total_files: int) -> List[Dict[str, Any]]:
        """Generate realistic file structure for a repository."""
        files = []
        
        # Language-specific file patterns
        extensions = {
            "python": [".py", ".pyx", ".pyi"],
            "javascript": [".js", ".jsx", ".mjs"],
            "typescript": [".ts", ".tsx"],
            "java": [".java"],
            "go": [".go"],
            "rust": [".rs"],
            "cpp": [".cpp", ".hpp", ".cc", ".h"],
            "csharp": [".cs"],
            "ruby": [".rb"],
            "php": [".php"]
        }
        
        lang_extensions = extensions.get(language, [".txt"])
        
        # Directory structure patterns
        directories = ["src", "lib", "utils", "models", "services", "controllers", "views", "tests"]
        
        for i in range(total_files):
            directory = random.choice(directories)
            filename = f"{self.fake.word()}{random.choice(lang_extensions)}"
            file_path = f"{directory}/{filename}"
            
            # Realistic line counts based on file type and location
            if "test" in directory:
                lines = random.randint(10, 200)
            elif directory in ["models", "services"]:
                lines = random.randint(50, 500)
            else:
                lines = random.randint(20, 300)
            
            files.append({
                "path": file_path,
                "lines": lines,
                "directory": directory
            })
        
        return files
    
    async def _generate_code_context(self, language: str, pattern_type: str) -> str:
        """Generate realistic code context snippet."""
        contexts = {
            "python": {
                "design_pattern": "class Singleton:\n    _instance = None\n    def __new__(cls):",
                "code_smell": "def very_long_method_name_that_does_too_many_things():\n    # 50+ lines of code",
                "architecture_pattern": "from flask import Flask\napp = Flask(__name__)\<EMAIL>('/')",
                "security_pattern": "import hashlib\ndef hash_password(password):\n    return hashlib.sha256(password.encode()).hexdigest()"
            },
            "javascript": {
                "design_pattern": "const singleton = (function() {\n    let instance;\n    return function() {",
                "behavioral_pattern": "class Observer {\n    constructor() {\n        this.observers = [];",
                "antipattern": "// TODO: Fix this spaghetti code\nif (x) { if (y) { if (z) {"
            },
            "java": {
                "design_pattern": "public class SingletonPattern {\n    private static SingletonPattern instance;",
                "architecture_pattern": "@Controller\npublic class UserController {\n    @Autowired",
                "creational_pattern": "public abstract class AbstractFactory {\n    abstract Product createProduct();"
            }
        }
        
        lang_contexts = contexts.get(language, {})
        return lang_contexts.get(pattern_type, f"// {pattern_type} implementation in {language}")
    
    async def _generate_pattern_metadata(
        self, pattern_type: str, repository: Dict[str, Any], confidence_score: float
    ) -> Dict[str, Any]:
        """Generate pattern-specific metadata."""
        metadata = {
            "detection_method": random.choice(["ast_analysis", "ml_classification", "rule_based", "hybrid"]),
            "validation_status": random.choice(["validated", "pending", "needs_review"]),
            "impact_score": random.uniform(0.1, 1.0),
            "complexity_level": random.choice(["low", "medium", "high"]),
        }
        
        if self.config.complexity in [DataComplexity.COMPLEX, DataComplexity.EXTREME]:
            metadata.update({
                "related_patterns": [random.choice(self._pattern_types) for _ in range(random.randint(0, 3))],
                "code_metrics": {
                    "cyclomatic_complexity": random.randint(1, 20),
                    "lines_of_code": random.randint(5, 200),
                    "nesting_depth": random.randint(1, 8)
                },
                "quality_indicators": {
                    "maintainability": random.uniform(0.0, 1.0),
                    "readability": random.uniform(0.0, 1.0),
                    "testability": random.uniform(0.0, 1.0)
                }
            })
        
        return metadata
    
    async def _generate_model_metadata(
        self, model_type: str, framework: str, accuracy: Optional[float]
    ) -> Dict[str, Any]:
        """Generate ML model metadata."""
        metadata = {
            "framework": framework,
            "training_duration_minutes": random.randint(5, 480),
            "dataset_size": random.randint(1000, 1000000),
            "feature_count": random.randint(10, 1000),
            "hyperparameters": {
                "learning_rate": random.uniform(0.0001, 0.1),
                "batch_size": random.choice([16, 32, 64, 128, 256]),
                "epochs": random.randint(10, 200)
            }
        }
        
        if model_type == "classification":
            metadata.update({
                "class_count": random.randint(2, 20),
                "precision": accuracy * random.uniform(0.85, 0.98) if accuracy else None,
                "recall": accuracy * random.uniform(0.80, 0.95) if accuracy else None
            })
        elif model_type == "regression":
            metadata.update({
                "mse": random.uniform(0.01, 1.0),
                "mae": random.uniform(0.01, 0.5),
                "r2_score": random.uniform(0.5, 0.98)
            })
        
        return metadata
    
    async def _corrupt_repository_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Apply corruption to repository data for testing."""
        corrupted = data.copy()
        
        corruption_types = [
            lambda d: d.update({"total_files": -1}),  # Negative file count
            lambda d: d.update({"total_lines": 0}),   # Zero lines
            lambda d: d.update({"status": "invalid_status"}),  # Invalid status
            lambda d: d.update({"language": ""}),     # Empty language
            lambda d: d.update({"repository_url": "not-a-url"}),  # Invalid URL
        ]
        
        random.choice(corruption_types)(corrupted)
        return corrupted
    
    async def _corrupt_pattern_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Apply corruption to pattern data for testing."""
        corrupted = data.copy()
        
        corruption_types = [
            lambda d: d.update({"confidence_score": 1.5}),  # Invalid confidence score
            lambda d: d.update({"line_start": d["line_end"] + 10}),  # Start after end
            lambda d: d.update({"pattern_type": ""}),  # Empty type
            lambda d: d.update({"file_path": ""}),     # Empty file path
        ]
        
        random.choice(corruption_types)(corrupted)
        return corrupted
    
    def _calculate_checksum(self, data: Dict[str, Any]) -> str:
        """Calculate checksum for data integrity validation."""
        # Remove internal fields for checksum calculation
        clean_data = {k: v for k, v in data.items() if not k.startswith("_")}
        data_string = json.dumps(clean_data, sort_keys=True, default=str)
        return hashlib.sha256(data_string.encode()).hexdigest()
    
    async def _calculate_quality_metrics(self, dataset: GeneratedDataset) -> Dict[str, Any]:
        """Calculate quality metrics for the generated dataset."""
        metrics = {}
        
        if dataset.repositories:
            repo_languages = [repo["language"] for repo in dataset.repositories]
            metrics["language_distribution"] = {
                lang: repo_languages.count(lang) / len(repo_languages)
                for lang in set(repo_languages)
            }
        
        if dataset.patterns:
            pattern_types = [pattern["pattern_type"] for pattern in dataset.patterns]
            metrics["pattern_type_distribution"] = {
                ptype: pattern_types.count(ptype) / len(pattern_types)
                for ptype in set(pattern_types)
            }
            
            confidence_scores = [pattern["confidence_score"] for pattern in dataset.patterns]
            metrics["confidence_score_stats"] = {
                "mean": np.mean(confidence_scores),
                "std": np.std(confidence_scores),
                "min": np.min(confidence_scores),
                "max": np.max(confidence_scores)
            }
        
        if dataset.vectors:
            vector_dimensions = [vector["dimensions"] for vector in dataset.vectors]
            metrics["vector_dimension_stats"] = {
                "mean": np.mean(vector_dimensions),
                "std": np.std(vector_dimensions),
                "min": np.min(vector_dimensions),
                "max": np.max(vector_dimensions)
            }
        
        return metrics
    
    # Performance and migration test data generators
    
    async def generate_performance_test_data(
        self, size: DatasetSize, complexity: DataComplexity
    ) -> GeneratedDataset:
        """Generate data specifically optimized for performance testing."""
        old_config = self.config
        self.config = DataGenerationConfig(
            size=size,
            complexity=complexity,
            distribution="normal",
            relationship_density=0.8,
            corruption_rate=0.0,
            batch_size=5000,  # Larger batches for performance testing
            include_metadata=True,
            include_timestamps=True,
            include_checksums=True
        )
        
        try:
            dataset = await self.generate_comprehensive_dataset()
            dataset.metadata["purpose"] = "performance_testing"
            return dataset
        finally:
            self.config = old_config
    
    async def generate_migration_test_data(self) -> Tuple[GeneratedDataset, GeneratedDataset]:
        """Generate before and after datasets for migration testing."""
        # Before migration (old schema/structure)
        before_config = DataGenerationConfig(
            size=DatasetSize.MEDIUM,
            complexity=DataComplexity.SIMPLE,
            include_metadata=False,
            include_checksums=True
        )
        
        old_config = self.config
        self.config = before_config
        
        try:
            before_dataset = await self.generate_comprehensive_dataset()
            before_dataset.metadata["migration_state"] = "before"
            
            # After migration (new schema/structure with enhancements)
            after_config = DataGenerationConfig(
                size=DatasetSize.MEDIUM,
                complexity=DataComplexity.MODERATE,
                include_metadata=True,
                include_checksums=True
            )
            
            self.config = after_config
            after_dataset = await self.generate_comprehensive_dataset()
            after_dataset.metadata["migration_state"] = "after"
            
            return before_dataset, after_dataset
            
        finally:
            self.config = old_config
    
    async def generate_corruption_test_scenarios(self) -> List[GeneratedDataset]:
        """Generate datasets with various corruption scenarios for testing."""
        scenarios = []
        
        corruption_rates = [0.01, 0.05, 0.1, 0.25]  # 1%, 5%, 10%, 25% corruption
        
        for rate in corruption_rates:
            config = DataGenerationConfig(
                size=DatasetSize.SMALL,
                complexity=DataComplexity.MODERATE,
                corruption_rate=rate,
                include_checksums=True
            )
            
            old_config = self.config
            self.config = config
            
            try:
                dataset = await self.generate_comprehensive_dataset()
                dataset.metadata["corruption_rate"] = rate
                dataset.metadata["purpose"] = "corruption_testing"
                scenarios.append(dataset)
            finally:
                self.config = old_config
        
        return scenarios


# Utility functions for easy fixture usage

async def create_small_dataset() -> GeneratedDataset:
    """Create small dataset for quick testing."""
    fixtures = DatabaseFixtures(DataGenerationConfig(size=DatasetSize.SMALL))
    return await fixtures.generate_comprehensive_dataset()


async def create_performance_dataset() -> GeneratedDataset:
    """Create large dataset for performance testing."""
    fixtures = DatabaseFixtures()
    return await fixtures.generate_performance_test_data(
        DatasetSize.LARGE, DataComplexity.COMPLEX
    )


async def create_migration_datasets() -> Tuple[GeneratedDataset, GeneratedDataset]:
    """Create before/after datasets for migration testing."""
    fixtures = DatabaseFixtures()
    return await fixtures.generate_migration_test_data()


async def create_corruption_scenarios() -> List[GeneratedDataset]:
    """Create datasets with corruption for integrity testing."""
    fixtures = DatabaseFixtures()
    return await fixtures.generate_corruption_test_scenarios()


if __name__ == "__main__":
    # Example usage
    async def main():
        print("Generating database fixtures...")
        
        # Create comprehensive dataset
        fixtures = DatabaseFixtures(DataGenerationConfig(
            size=DatasetSize.MEDIUM,
            complexity=DataComplexity.MODERATE,
            seed=42  # For reproducible results
        ))
        
        dataset = await fixtures.generate_comprehensive_dataset()
        
        print(f"\nGenerated dataset:")
        print(f"  Repositories: {len(dataset.repositories)}")
        print(f"  Patterns: {len(dataset.patterns)}")
        print(f"  Models: {len(dataset.models)}")
        print(f"  Vectors: {len(dataset.vectors)}")
        print(f"  Total records: {dataset.total_records}")
        print(f"  Generation time: {dataset.generation_time:.2f}s")
        print(f"  Records/second: {dataset.metadata['statistics']['records_per_second']:.0f}")
        
        # Show quality metrics
        quality = dataset.metadata.get("quality_metrics", {})
        if "language_distribution" in quality:
            print(f"  Language distribution: {quality['language_distribution']}")
        if "confidence_score_stats" in quality:
            conf_stats = quality["confidence_score_stats"]
            print(f"  Confidence scores: mean={conf_stats['mean']:.3f}, std={conf_stats['std']:.3f}")
    
    asyncio.run(main())