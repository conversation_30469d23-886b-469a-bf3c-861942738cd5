"""
Database Monitoring Utility for Advanced Integration Testing
Phase 2 Enhancement: Real-time database performance and health monitoring

This module provides comprehensive database monitoring capabilities for advanced
integration testing, including connection pool monitoring, query performance
tracking, transaction timing, lock contention detection, and resource usage
monitoring.

Key Features:
- Real-time connection pool health monitoring
- Query execution performance tracking with detailed metrics
- Transaction timing and deadlock detection
- Lock contention and blocking query identification  
- Resource usage monitoring (CPU, memory, I/O)
- Performance threshold alerting and automatic scaling detection
- Historical performance trend analysis
- Integration with PostgreSQL system views and statistics

Usage:
    monitor = DatabaseMonitor()
    
    # Start monitoring
    await monitor.start_monitoring()
    
    # Track query performance
    async with monitor.track_query("SELECT * FROM patterns") as tracker:
        result = await execute_query(query)
    
    # Get performance report
    report = await monitor.get_performance_report()
    
    # Stop monitoring
    await monitor.stop_monitoring()
"""

import asyncio
import time
import json
import psutil
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple, AsyncContextManager
from dataclasses import dataclass, field
from contextlib import asynccontextmanager
from collections import defaultdict, deque
import statistics
from enum import Enum

import sqlalchemy
from sqlalchemy import text, select, func
from sqlalchemy.ext.asyncio import AsyncSession, AsyncEngine
from sqlalchemy.pool import QueuePool

from pattern_mining.database.connection import get_database_connection


class AlertLevel(Enum):
    """Alert severity levels."""
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"
    EMERGENCY = "emergency"


class QueryType(Enum):
    """Query type classification."""
    SELECT = "SELECT"
    INSERT = "INSERT"
    UPDATE = "UPDATE"
    DELETE = "DELETE"
    DDL = "DDL"  # Data Definition Language
    TRANSACTION = "TRANSACTION"
    UNKNOWN = "UNKNOWN"


@dataclass
class QueryMetrics:
    """Individual query execution metrics."""
    query_id: str
    query_text: str
    query_type: QueryType
    start_time: datetime
    end_time: Optional[datetime] = None
    execution_time_ms: Optional[float] = None
    rows_affected: Optional[int] = None
    rows_returned: Optional[int] = None
    connection_id: Optional[str] = None
    transaction_id: Optional[str] = None
    error: Optional[str] = None
    explain_plan: Optional[Dict[str, Any]] = None
    resource_usage: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ConnectionPoolMetrics:
    """Connection pool health metrics."""
    timestamp: datetime
    pool_size: int
    checked_out: int
    checked_in: int
    overflow: int
    invalidated: int
    pool_utilization: float  # checked_out / pool_size
    wait_queue_size: int = 0
    average_wait_time_ms: float = 0.0
    peak_connections: int = 0
    connection_errors: int = 0


@dataclass
class TransactionMetrics:
    """Transaction execution metrics."""
    transaction_id: str
    start_time: datetime
    end_time: Optional[datetime] = None
    duration_ms: Optional[float] = None
    query_count: int = 0
    status: str = "active"  # active, committed, aborted
    isolation_level: str = "read_committed"
    lock_wait_time_ms: float = 0.0
    deadlock_detected: bool = False
    rollback_reason: Optional[str] = None


@dataclass
class ResourceMetrics:
    """System resource usage metrics."""
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    memory_usage_mb: float
    disk_io_read_mb: float
    disk_io_write_mb: float
    network_io_sent_mb: float
    network_io_recv_mb: float
    active_connections: int
    database_size_mb: float


@dataclass
class PerformanceAlert:
    """Performance alert information."""
    alert_id: str
    level: AlertLevel
    timestamp: datetime
    category: str
    message: str
    metrics: Dict[str, Any]
    threshold_violated: str
    suggested_action: Optional[str] = None
    resolved: bool = False
    resolution_time: Optional[datetime] = None


@dataclass
class LockContentionInfo:
    """Lock contention detection information."""
    timestamp: datetime
    blocking_pid: int
    blocked_pid: int
    blocking_query: str
    blocked_query: str
    lock_type: str
    relation_name: str
    wait_duration_ms: float
    transaction_ids: Tuple[str, str]  # (blocking_txn, blocked_txn)


class QueryTracker:
    """Context manager for tracking individual query performance."""
    
    def __init__(self, monitor: 'DatabaseMonitor', query_text: str, connection_id: str = None):
        self.monitor = monitor
        self.query_text = query_text
        self.connection_id = connection_id
        self.metrics: Optional[QueryMetrics] = None
    
    async def __aenter__(self):
        """Start query tracking."""
        query_id = f"query_{int(time.time() * 1000000)}"
        query_type = self._classify_query(self.query_text)
        
        self.metrics = QueryMetrics(
            query_id=query_id,
            query_text=self.query_text,
            query_type=query_type,
            start_time=datetime.utcnow(),
            connection_id=self.connection_id
        )
        
        # Record resource usage at start
        self.metrics.resource_usage["start_cpu"] = psutil.cpu_percent()
        self.metrics.resource_usage["start_memory"] = psutil.virtual_memory().percent
        
        await self.monitor._record_query_start(self.metrics)
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Complete query tracking."""
        if self.metrics:
            self.metrics.end_time = datetime.utcnow()
            self.metrics.execution_time_ms = (
                self.metrics.end_time - self.metrics.start_time
            ).total_seconds() * 1000
            
            # Record resource usage at end
            self.metrics.resource_usage["end_cpu"] = psutil.cpu_percent()
            self.metrics.resource_usage["end_memory"] = psutil.virtual_memory().percent
            
            if exc_type:
                self.metrics.error = str(exc_val)
            
            await self.monitor._record_query_completion(self.metrics)
    
    def _classify_query(self, query_text: str) -> QueryType:
        """Classify query by type."""
        query_upper = query_text.strip().upper()
        
        if query_upper.startswith("SELECT"):
            return QueryType.SELECT
        elif query_upper.startswith(("INSERT", "COPY")):
            return QueryType.INSERT
        elif query_upper.startswith("UPDATE"):
            return QueryType.UPDATE
        elif query_upper.startswith("DELETE"):
            return QueryType.DELETE
        elif query_upper.startswith(("CREATE", "ALTER", "DROP")):
            return QueryType.DDL
        elif query_upper.startswith(("BEGIN", "COMMIT", "ROLLBACK", "SAVEPOINT")):
            return QueryType.TRANSACTION
        else:
            return QueryType.UNKNOWN
    
    def set_rows_affected(self, count: int):
        """Set number of rows affected by query."""
        if self.metrics:
            self.metrics.rows_affected = count
    
    def set_rows_returned(self, count: int):
        """Set number of rows returned by query."""
        if self.metrics:
            self.metrics.rows_returned = count
    
    async def add_explain_plan(self, session: AsyncSession):
        """Add query execution plan to metrics."""
        if self.metrics and self.metrics.query_type == QueryType.SELECT:
            try:
                explain_query = f"EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON) {self.query_text}"
                result = await session.execute(text(explain_query))
                plan_data = result.fetchone()
                if plan_data:
                    self.metrics.explain_plan = plan_data[0][0] if plan_data[0] else None
            except Exception as e:
                # Don't fail the original query if EXPLAIN fails
                self.metrics.resource_usage["explain_error"] = str(e)


class DatabaseMonitor:
    """Comprehensive database performance and health monitor."""
    
    def __init__(self, engine: Optional[AsyncEngine] = None, monitoring_interval: float = 1.0):
        """Initialize database monitor."""
        self.engine = engine
        self.monitoring_interval = monitoring_interval
        self.is_monitoring = False
        self._monitoring_task: Optional[asyncio.Task] = None
        
        # Metrics storage
        self.query_metrics: deque = deque(maxlen=10000)  # Store last 10K queries
        self.connection_pool_metrics: deque = deque(maxlen=1440)  # 24 hours at 1-minute intervals
        self.transaction_metrics: Dict[str, TransactionMetrics] = {}
        self.resource_metrics: deque = deque(maxlen=1440)  # 24 hours at 1-minute intervals
        self.performance_alerts: List[PerformanceAlert] = []
        self.lock_contentions: List[LockContentionInfo] = []
        
        # Performance thresholds
        self.thresholds = {
            "slow_query_ms": 1000,
            "very_slow_query_ms": 5000,
            "pool_utilization_warning": 0.8,
            "pool_utilization_critical": 0.95,
            "cpu_warning": 80.0,
            "cpu_critical": 95.0,
            "memory_warning": 80.0,
            "memory_critical": 95.0,
            "transaction_duration_warning_ms": 30000,
            "transaction_duration_critical_ms": 120000,
            "lock_wait_warning_ms": 5000,
            "lock_wait_critical_ms": 30000
        }
        
        # Statistical tracking
        self.query_stats = defaultdict(lambda: {
            "count": 0, "total_time_ms": 0, "min_time_ms": float('inf'),
            "max_time_ms": 0, "errors": 0, "times": deque(maxlen=100)
        })
        
        self._setup_logging()
    
    def _setup_logging(self):
        """Set up logging for monitoring."""
        self.logger = logging.getLogger(__name__)
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
    
    async def start_monitoring(self):
        """Start background monitoring."""
        if self.is_monitoring:
            return
        
        self.is_monitoring = True
        self._monitoring_task = asyncio.create_task(self._monitoring_loop())
        self.logger.info("Database monitoring started")
    
    async def stop_monitoring(self):
        """Stop background monitoring."""
        if not self.is_monitoring:
            return
        
        self.is_monitoring = False
        if self._monitoring_task:
            self._monitoring_task.cancel()
            try:
                await self._monitoring_task
            except asyncio.CancelledError:
                pass
        
        self.logger.info("Database monitoring stopped")
    
    async def _monitoring_loop(self):
        """Main monitoring loop."""
        try:
            while self.is_monitoring:
                await self._collect_metrics()
                await self._check_performance_thresholds()
                await self._detect_lock_contention()
                await asyncio.sleep(self.monitoring_interval)
        except asyncio.CancelledError:
            pass
        except Exception as e:
            self.logger.error(f"Monitoring loop error: {e}")
    
    async def _collect_metrics(self):
        """Collect various performance metrics."""
        timestamp = datetime.utcnow()
        
        # Collect connection pool metrics
        if self.engine and hasattr(self.engine.pool, 'size'):
            await self._collect_connection_pool_metrics(timestamp)
        
        # Collect system resource metrics
        await self._collect_resource_metrics(timestamp)
        
        # Clean up old completed transactions
        self._cleanup_completed_transactions()
    
    async def _collect_connection_pool_metrics(self, timestamp: datetime):
        """Collect connection pool health metrics."""
        try:
            pool = self.engine.pool
            
            if isinstance(pool, QueuePool):
                metrics = ConnectionPoolMetrics(
                    timestamp=timestamp,
                    pool_size=pool.size(),
                    checked_out=pool.checkedout(),
                    checked_in=pool.checkedin(),
                    overflow=pool.overflow(),
                    invalidated=pool.invalidated(),
                    pool_utilization=pool.checkedout() / max(pool.size(), 1)
                )
                
                # Calculate peak connections
                if self.connection_pool_metrics:
                    previous_peak = max(m.peak_connections for m in self.connection_pool_metrics)
                    metrics.peak_connections = max(previous_peak, metrics.checked_out)
                else:
                    metrics.peak_connections = metrics.checked_out
                
                self.connection_pool_metrics.append(metrics)
        
        except Exception as e:
            self.logger.error(f"Error collecting connection pool metrics: {e}")
    
    async def _collect_resource_metrics(self, timestamp: datetime):
        """Collect system resource usage metrics."""
        try:
            # System metrics
            cpu_percent = psutil.cpu_percent(interval=None)
            memory = psutil.virtual_memory()
            disk_io = psutil.disk_io_counters()
            network_io = psutil.net_io_counters()
            
            # Database-specific metrics (if we can connect)
            active_connections = 0
            database_size_mb = 0.0
            
            try:
                async with get_database_connection() as session:
                    # Get active connection count
                    result = await session.execute(text(
                        "SELECT count(*) FROM pg_stat_activity WHERE state = 'active'"
                    ))
                    active_connections = result.scalar() or 0
                    
                    # Get database size
                    result = await session.execute(text(
                        "SELECT pg_size_pretty(pg_database_size(current_database()))"
                    ))
                    size_str = result.scalar() or "0"
                    # Parse size string (e.g., "123 MB", "1.5 GB")
                    if "MB" in size_str:
                        database_size_mb = float(size_str.split()[0])
                    elif "GB" in size_str:
                        database_size_mb = float(size_str.split()[0]) * 1024
                    elif "kB" in size_str:
                        database_size_mb = float(size_str.split()[0]) / 1024
            
            except Exception as e:
                self.logger.debug(f"Could not collect database-specific metrics: {e}")
            
            metrics = ResourceMetrics(
                timestamp=timestamp,
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                memory_usage_mb=memory.used / (1024 * 1024),
                disk_io_read_mb=(disk_io.read_bytes / (1024 * 1024)) if disk_io else 0,
                disk_io_write_mb=(disk_io.write_bytes / (1024 * 1024)) if disk_io else 0,
                network_io_sent_mb=(network_io.bytes_sent / (1024 * 1024)) if network_io else 0,
                network_io_recv_mb=(network_io.bytes_recv / (1024 * 1024)) if network_io else 0,
                active_connections=active_connections,
                database_size_mb=database_size_mb
            )
            
            self.resource_metrics.append(metrics)
        
        except Exception as e:
            self.logger.error(f"Error collecting resource metrics: {e}")
    
    async def _check_performance_thresholds(self):
        """Check metrics against performance thresholds and generate alerts."""
        current_time = datetime.utcnow()
        
        # Check connection pool utilization
        if self.connection_pool_metrics:
            latest_pool = self.connection_pool_metrics[-1]
            
            if latest_pool.pool_utilization >= self.thresholds["pool_utilization_critical"]:
                await self._create_alert(
                    AlertLevel.CRITICAL, "connection_pool",
                    f"Connection pool utilization critical: {latest_pool.pool_utilization:.1%}",
                    {"utilization": latest_pool.pool_utilization, "checked_out": latest_pool.checked_out},
                    "pool_utilization_critical",
                    "Consider increasing pool size or investigating connection leaks"
                )
            elif latest_pool.pool_utilization >= self.thresholds["pool_utilization_warning"]:
                await self._create_alert(
                    AlertLevel.WARNING, "connection_pool",
                    f"Connection pool utilization high: {latest_pool.pool_utilization:.1%}",
                    {"utilization": latest_pool.pool_utilization},
                    "pool_utilization_warning"
                )
        
        # Check system resource usage
        if self.resource_metrics:
            latest_resource = self.resource_metrics[-1]
            
            if latest_resource.cpu_percent >= self.thresholds["cpu_critical"]:
                await self._create_alert(
                    AlertLevel.CRITICAL, "system_resources",
                    f"CPU usage critical: {latest_resource.cpu_percent:.1f}%",
                    {"cpu_percent": latest_resource.cpu_percent},
                    "cpu_critical",
                    "Investigate high CPU usage processes"
                )
            elif latest_resource.cpu_percent >= self.thresholds["cpu_warning"]:
                await self._create_alert(
                    AlertLevel.WARNING, "system_resources",
                    f"CPU usage high: {latest_resource.cpu_percent:.1f}%",
                    {"cpu_percent": latest_resource.cpu_percent},
                    "cpu_warning"
                )
            
            if latest_resource.memory_percent >= self.thresholds["memory_critical"]:
                await self._create_alert(
                    AlertLevel.CRITICAL, "system_resources",
                    f"Memory usage critical: {latest_resource.memory_percent:.1f}%",
                    {"memory_percent": latest_resource.memory_percent},
                    "memory_critical",
                    "Consider increasing available memory or optimizing memory usage"
                )
            elif latest_resource.memory_percent >= self.thresholds["memory_warning"]:
                await self._create_alert(
                    AlertLevel.WARNING, "system_resources",
                    f"Memory usage high: {latest_resource.memory_percent:.1f}%",
                    {"memory_percent": latest_resource.memory_percent},
                    "memory_warning"
                )
        
        # Check active transactions for long duration
        for txn_id, txn_metrics in self.transaction_metrics.items():
            if txn_metrics.status == "active":
                duration_ms = (current_time - txn_metrics.start_time).total_seconds() * 1000
                
                if duration_ms >= self.thresholds["transaction_duration_critical_ms"]:
                    await self._create_alert(
                        AlertLevel.CRITICAL, "transaction_duration",
                        f"Long-running transaction: {duration_ms:.0f}ms",
                        {"transaction_id": txn_id, "duration_ms": duration_ms},
                        "transaction_duration_critical",
                        "Investigate potential deadlock or inefficient query"
                    )
                elif duration_ms >= self.thresholds["transaction_duration_warning_ms"]:
                    await self._create_alert(
                        AlertLevel.WARNING, "transaction_duration",
                        f"Long-running transaction: {duration_ms:.0f}ms",
                        {"transaction_id": txn_id, "duration_ms": duration_ms},
                        "transaction_duration_warning"
                    )
    
    async def _detect_lock_contention(self):
        """Detect lock contention and blocking queries."""
        try:
            async with get_database_connection() as session:
                # Query to detect lock contention
                lock_query = text("""
                    SELECT 
                        blocked_locks.pid AS blocked_pid,
                        blocked_activity.usename AS blocked_user,
                        blocking_locks.pid AS blocking_pid,
                        blocking_activity.usename AS blocking_user,
                        blocked_activity.query AS blocked_statement,
                        blocking_activity.query AS blocking_statement,
                        blocked_locks.locktype AS lock_type,
                        blocked_locks.relation::regclass AS relation_name,
                        EXTRACT(EPOCH FROM (now() - blocked_activity.query_start)) * 1000 AS wait_duration_ms
                    FROM pg_catalog.pg_locks blocked_locks
                    JOIN pg_catalog.pg_stat_activity blocked_activity 
                        ON blocked_activity.pid = blocked_locks.pid
                    JOIN pg_catalog.pg_locks blocking_locks 
                        ON blocking_locks.locktype = blocked_locks.locktype
                        AND blocking_locks.database IS NOT DISTINCT FROM blocked_locks.database
                        AND blocking_locks.relation IS NOT DISTINCT FROM blocked_locks.relation
                        AND blocking_locks.page IS NOT DISTINCT FROM blocked_locks.page
                        AND blocking_locks.tuple IS NOT DISTINCT FROM blocked_locks.tuple
                        AND blocking_locks.virtualxid IS NOT DISTINCT FROM blocked_locks.virtualxid
                        AND blocking_locks.transactionid IS NOT DISTINCT FROM blocked_locks.transactionid
                        AND blocking_locks.classid IS NOT DISTINCT FROM blocked_locks.classid
                        AND blocking_locks.objid IS NOT DISTINCT FROM blocked_locks.objid
                        AND blocking_locks.objsubid IS NOT DISTINCT FROM blocked_locks.objsubid
                        AND blocking_locks.pid != blocked_locks.pid
                    JOIN pg_catalog.pg_stat_activity blocking_activity 
                        ON blocking_activity.pid = blocking_locks.pid
                    WHERE NOT blocked_locks.granted
                """)
                
                result = await session.execute(lock_query)
                lock_info = result.fetchall()
                
                for row in lock_info:
                    contention = LockContentionInfo(
                        timestamp=datetime.utcnow(),
                        blocking_pid=row.blocking_pid,
                        blocked_pid=row.blocked_pid,
                        blocking_query=row.blocking_statement or "Unknown",
                        blocked_query=row.blocked_statement or "Unknown",
                        lock_type=row.lock_type or "Unknown",
                        relation_name=str(row.relation_name) if row.relation_name else "Unknown",
                        wait_duration_ms=row.wait_duration_ms or 0,
                        transaction_ids=("unknown", "unknown")  # Could be enhanced to get actual txn IDs
                    )
                    
                    self.lock_contentions.append(contention)
                    
                    # Generate alert for significant lock waits
                    if contention.wait_duration_ms >= self.thresholds["lock_wait_critical_ms"]:
                        await self._create_alert(
                            AlertLevel.CRITICAL, "lock_contention",
                            f"Critical lock wait: {contention.wait_duration_ms:.0f}ms",
                            {
                                "blocking_pid": contention.blocking_pid,
                                "blocked_pid": contention.blocked_pid,
                                "wait_duration_ms": contention.wait_duration_ms,
                                "lock_type": contention.lock_type
                            },
                            "lock_wait_critical",
                            "Investigate blocking query and consider query optimization"
                        )
                    elif contention.wait_duration_ms >= self.thresholds["lock_wait_warning_ms"]:
                        await self._create_alert(
                            AlertLevel.WARNING, "lock_contention",
                            f"Lock contention detected: {contention.wait_duration_ms:.0f}ms",
                            {"wait_duration_ms": contention.wait_duration_ms},
                            "lock_wait_warning"
                        )
        
        except Exception as e:
            self.logger.debug(f"Could not detect lock contention: {e}")
    
    async def _create_alert(
        self, level: AlertLevel, category: str, message: str,
        metrics: Dict[str, Any], threshold_violated: str,
        suggested_action: str = None
    ):
        """Create performance alert."""
        alert_id = f"alert_{int(time.time() * 1000000)}"
        
        alert = PerformanceAlert(
            alert_id=alert_id,
            level=level,
            timestamp=datetime.utcnow(),
            category=category,
            message=message,
            metrics=metrics,
            threshold_violated=threshold_violated,
            suggested_action=suggested_action
        )
        
        self.performance_alerts.append(alert)
        
        # Log alert
        log_level = {
            AlertLevel.INFO: logging.INFO,
            AlertLevel.WARNING: logging.WARNING,
            AlertLevel.CRITICAL: logging.ERROR,
            AlertLevel.EMERGENCY: logging.CRITICAL
        }[level]
        
        self.logger.log(log_level, f"ALERT [{level.value.upper()}] {category}: {message}")
    
    def _cleanup_completed_transactions(self):
        """Clean up old completed transactions."""
        cutoff_time = datetime.utcnow() - timedelta(hours=1)
        completed_txns = [
            txn_id for txn_id, txn in self.transaction_metrics.items()
            if txn.status in ["committed", "aborted"] and txn.end_time and txn.end_time < cutoff_time
        ]
        
        for txn_id in completed_txns:
            del self.transaction_metrics[txn_id]
    
    # Public API methods
    
    def track_query(self, query_text: str, connection_id: str = None) -> QueryTracker:
        """Create query tracker context manager."""
        return QueryTracker(self, query_text, connection_id)
    
    async def _record_query_start(self, metrics: QueryMetrics):
        """Record query start (internal method)."""
        pass  # Metrics already created in QueryTracker
    
    async def _record_query_completion(self, metrics: QueryMetrics):
        """Record query completion (internal method)."""
        self.query_metrics.append(metrics)
        
        # Update query statistics
        query_type = metrics.query_type.value
        stats = self.query_stats[query_type]
        stats["count"] += 1
        
        if metrics.execution_time_ms:
            stats["total_time_ms"] += metrics.execution_time_ms
            stats["min_time_ms"] = min(stats["min_time_ms"], metrics.execution_time_ms)
            stats["max_time_ms"] = max(stats["max_time_ms"], metrics.execution_time_ms)
            stats["times"].append(metrics.execution_time_ms)
            
            # Check for slow queries
            if metrics.execution_time_ms >= self.thresholds["very_slow_query_ms"]:
                await self._create_alert(
                    AlertLevel.CRITICAL, "slow_query",
                    f"Very slow query: {metrics.execution_time_ms:.0f}ms",
                    {
                        "query_id": metrics.query_id,
                        "execution_time_ms": metrics.execution_time_ms,
                        "query_type": query_type
                    },
                    "very_slow_query_ms",
                    "Investigate query performance and consider optimization"
                )
            elif metrics.execution_time_ms >= self.thresholds["slow_query_ms"]:
                await self._create_alert(
                    AlertLevel.WARNING, "slow_query",
                    f"Slow query: {metrics.execution_time_ms:.0f}ms",
                    {"execution_time_ms": metrics.execution_time_ms},
                    "slow_query_ms"
                )
        
        if metrics.error:
            stats["errors"] += 1
    
    async def start_transaction_tracking(self, transaction_id: str = None) -> str:
        """Start tracking a transaction."""
        if transaction_id is None:
            transaction_id = f"txn_{int(time.time() * 1000000)}"
        
        self.transaction_metrics[transaction_id] = TransactionMetrics(
            transaction_id=transaction_id,
            start_time=datetime.utcnow()
        )
        
        return transaction_id
    
    async def complete_transaction_tracking(
        self, transaction_id: str, status: str, rollback_reason: str = None
    ):
        """Complete transaction tracking."""
        if transaction_id in self.transaction_metrics:
            txn_metrics = self.transaction_metrics[transaction_id]
            txn_metrics.end_time = datetime.utcnow()
            txn_metrics.duration_ms = (
                txn_metrics.end_time - txn_metrics.start_time
            ).total_seconds() * 1000
            txn_metrics.status = status
            if rollback_reason:
                txn_metrics.rollback_reason = rollback_reason
    
    async def get_performance_report(self) -> Dict[str, Any]:
        """Generate comprehensive performance report."""
        current_time = datetime.utcnow()
        
        # Query statistics
        query_summary = {}
        for query_type, stats in self.query_stats.items():
            if stats["count"] > 0:
                avg_time = stats["total_time_ms"] / stats["count"]
                query_summary[query_type] = {
                    "total_queries": stats["count"],
                    "total_time_ms": stats["total_time_ms"],
                    "average_time_ms": avg_time,
                    "min_time_ms": stats["min_time_ms"] if stats["min_time_ms"] != float('inf') else 0,
                    "max_time_ms": stats["max_time_ms"],
                    "error_rate": stats["errors"] / stats["count"] if stats["count"] > 0 else 0,
                    "p95_time_ms": statistics.quantiles(stats["times"], n=20)[18] if len(stats["times"]) >= 20 else 0
                }
        
        # Connection pool status
        pool_status = {}
        if self.connection_pool_metrics:
            latest_pool = self.connection_pool_metrics[-1]
            pool_status = {
                "current_utilization": latest_pool.pool_utilization,
                "pool_size": latest_pool.pool_size,
                "checked_out": latest_pool.checked_out,
                "checked_in": latest_pool.checked_in,
                "overflow": latest_pool.overflow,
                "peak_connections": latest_pool.peak_connections
            }
        
        # Resource usage
        resource_status = {}
        if self.resource_metrics:
            latest_resource = self.resource_metrics[-1]
            resource_status = {
                "cpu_percent": latest_resource.cpu_percent,
                "memory_percent": latest_resource.memory_percent,
                "memory_usage_mb": latest_resource.memory_usage_mb,
                "active_connections": latest_resource.active_connections,
                "database_size_mb": latest_resource.database_size_mb
            }
        
        # Transaction summary
        transaction_summary = {
            "active_transactions": len([t for t in self.transaction_metrics.values() if t.status == "active"]),
            "total_transactions": len(self.transaction_metrics),
            "average_duration_ms": statistics.mean([
                t.duration_ms for t in self.transaction_metrics.values() 
                if t.duration_ms is not None
            ]) if self.transaction_metrics else 0
        }
        
        # Alert summary
        alert_summary = {
            "total_alerts": len(self.performance_alerts),
            "unresolved_alerts": len([a for a in self.performance_alerts if not a.resolved]),
            "alerts_by_level": {}
        }
        
        for alert in self.performance_alerts:
            level = alert.level.value
            alert_summary["alerts_by_level"][level] = alert_summary["alerts_by_level"].get(level, 0) + 1
        
        # Lock contention summary
        lock_summary = {
            "total_contentions": len(self.lock_contentions),
            "recent_contentions": len([
                l for l in self.lock_contentions 
                if l.timestamp > current_time - timedelta(minutes=5)
            ])
        }
        
        return {
            "report_timestamp": current_time.isoformat(),
            "monitoring_duration_seconds": (current_time - (self.resource_metrics[0].timestamp if self.resource_metrics else current_time)).total_seconds(),
            "query_performance": query_summary,
            "connection_pool": pool_status,
            "resource_usage": resource_status,
            "transaction_performance": transaction_summary,
            "alerts": alert_summary,
            "lock_contention": lock_summary,
            "performance_thresholds": self.thresholds
        }
    
    async def get_slow_queries(self, limit: int = 10) -> List[QueryMetrics]:
        """Get slowest queries in descending order."""
        slow_queries = [
            m for m in self.query_metrics 
            if m.execution_time_ms and m.execution_time_ms >= self.thresholds["slow_query_ms"]
        ]
        
        return sorted(slow_queries, key=lambda q: q.execution_time_ms or 0, reverse=True)[:limit]
    
    async def get_recent_alerts(self, hours: int = 1) -> List[PerformanceAlert]:
        """Get recent alerts within specified hours."""
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        return [alert for alert in self.performance_alerts if alert.timestamp > cutoff_time]
    
    async def get_lock_contentions(self, minutes: int = 30) -> List[LockContentionInfo]:
        """Get recent lock contentions."""
        cutoff_time = datetime.utcnow() - timedelta(minutes=minutes)
        return [lock for lock in self.lock_contentions if lock.timestamp > cutoff_time]
    
    async def resolve_alert(self, alert_id: str):
        """Mark alert as resolved."""
        for alert in self.performance_alerts:
            if alert.alert_id == alert_id:
                alert.resolved = True
                alert.resolution_time = datetime.utcnow()
                break
    
    def set_threshold(self, threshold_name: str, value: float):
        """Update performance threshold."""
        if threshold_name in self.thresholds:
            self.thresholds[threshold_name] = value
            self.logger.info(f"Updated threshold {threshold_name} = {value}")
    
    async def reset_metrics(self):
        """Reset all collected metrics (useful for testing)."""
        self.query_metrics.clear()
        self.connection_pool_metrics.clear()
        self.transaction_metrics.clear()
        self.resource_metrics.clear()
        self.performance_alerts.clear()
        self.lock_contentions.clear()
        self.query_stats.clear()
        self.logger.info("All metrics reset")


# Utility functions for easy monitor usage

async def create_monitor_with_defaults() -> DatabaseMonitor:
    """Create database monitor with default settings."""
    monitor = DatabaseMonitor(monitoring_interval=1.0)
    await monitor.start_monitoring()
    return monitor


@asynccontextmanager
async def temporary_monitoring(duration_seconds: float = 60.0) -> AsyncContextManager[DatabaseMonitor]:
    """Context manager for temporary monitoring session."""
    monitor = DatabaseMonitor(monitoring_interval=0.5)  # Higher frequency for short sessions
    
    try:
        await monitor.start_monitoring()
        yield monitor
        
        # Wait for monitoring duration
        await asyncio.sleep(duration_seconds)
        
    finally:
        await monitor.stop_monitoring()


async def run_performance_benchmark(
    queries: List[str], 
    concurrent_connections: int = 10,
    duration_seconds: float = 30.0
) -> Dict[str, Any]:
    """Run performance benchmark with monitoring."""
    monitor = DatabaseMonitor(monitoring_interval=0.1)  # High frequency monitoring
    await monitor.start_monitoring()
    
    try:
        # Run concurrent queries
        async def execute_queries():
            async with get_database_connection() as session:
                for query in queries:
                    async with monitor.track_query(query) as tracker:
                        try:
                            result = await session.execute(text(query))
                            tracker.set_rows_returned(result.rowcount or 0)
                        except Exception as e:
                            pass  # Error already recorded by tracker
        
        # Start concurrent tasks
        tasks = [
            asyncio.create_task(execute_queries())
            for _ in range(concurrent_connections)
        ]
        
        # Wait for benchmark duration
        await asyncio.sleep(duration_seconds)
        
        # Cancel remaining tasks
        for task in tasks:
            if not task.done():
                task.cancel()
        
        # Wait for all tasks to complete
        await asyncio.gather(*tasks, return_exceptions=True)
        
        # Generate final report
        return await monitor.get_performance_report()
        
    finally:
        await monitor.stop_monitoring()


if __name__ == "__main__":
    # Example usage
    async def main():
        print("Starting database monitoring example...")
        
        # Create monitor
        monitor = DatabaseMonitor(monitoring_interval=2.0)
        await monitor.start_monitoring()
        
        try:
            # Simulate some database operations
            async with get_database_connection() as session:
                # Track a query
                async with monitor.track_query("SELECT COUNT(*) FROM pg_stat_activity") as tracker:
                    result = await session.execute(text("SELECT COUNT(*) FROM pg_stat_activity"))
                    count = result.scalar()
                    tracker.set_rows_returned(1)
                    print(f"Active connections: {count}")
                
                # Track transaction
                txn_id = await monitor.start_transaction_tracking()
                
                # Simulate some work
                await asyncio.sleep(5)
                
                await monitor.complete_transaction_tracking(txn_id, "committed")
            
            # Wait for some monitoring data
            await asyncio.sleep(10)
            
            # Generate report
            report = await monitor.get_performance_report()
            print(f"\nPerformance Report:")
            print(f"Query Performance: {report.get('query_performance', {})}")
            print(f"Resource Usage: {report.get('resource_usage', {})}")
            print(f"Alerts: {report.get('alerts', {})}")
            
            # Get slow queries
            slow_queries = await monitor.get_slow_queries(5)
            print(f"Slow queries: {len(slow_queries)}")
            
        finally:
            await monitor.stop_monitoring()
    
    asyncio.run(main())