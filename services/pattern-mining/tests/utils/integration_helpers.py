"""
Integration Test Helpers - Phase 2 Enhancement

Advanced utilities for multi-service orchestration and complex integration testing.
These helpers provide sophisticated testing patterns for validating complex interactions
between Pattern Mining service components.

Key Features:
1. Multi-Service Orchestration - Coordinate complex service interactions
2. Service Health Monitoring - Monitor service health during testing
3. Data Consistency Validation - Validate data consistency across services
4. Performance Measurement - Measure performance across service boundaries
5. Failure Injection - Simulate realistic failure scenarios
6. State Management - Manage complex test state across services
"""

import asyncio
import time
import json
import uuid
import hashlib
import random
import statistics
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple, Set, Callable, Union
from dataclasses import dataclass, field
from enum import Enum
from contextlib import asynccontextmanager
import concurrent.futures
import threading

# HTTP and async utilities
import httpx
from httpx import AsyncClient, Response

# Pattern mining imports
from pattern_mining.models.patterns import PatternType, SeverityLevel, DetectionType
from pattern_mining.models.api import PatternDetectionRequest


class ServiceHealthStatus(Enum):
    """Service health status enumeration."""
    HEALTHY = "healthy"
    DEGRADED = "degraded" 
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"


class TestExecutionPhase(Enum):
    """Test execution phases."""
    SETUP = "setup"
    EXECUTION = "execution"
    VALIDATION = "validation"
    CLEANUP = "cleanup"


@dataclass
class ServiceEndpoint:
    """Service endpoint configuration."""
    name: str
    base_url: str
    health_endpoint: str = "/health"
    timeout_seconds: int = 30
    retry_attempts: int = 3
    expected_response_time_ms: float = 500.0


@dataclass
class ServiceHealthCheck:
    """Service health check result."""
    service_name: str
    status: ServiceHealthStatus
    response_time_ms: float
    timestamp: datetime
    details: Dict[str, Any] = field(default_factory=dict)
    error_message: Optional[str] = None


@dataclass
class IntegrationTestContext:
    """Integration test execution context."""
    test_id: str
    test_name: str
    phase: TestExecutionPhase
    services: Dict[str, ServiceEndpoint]
    test_data: Dict[str, Any] = field(default_factory=dict)
    performance_metrics: Dict[str, List[float]] = field(default_factory=dict)
    health_checks: List[ServiceHealthCheck] = field(default_factory=list)
    consistency_validations: List[Dict[str, Any]] = field(default_factory=list)
    errors: List[str] = field(default_factory=list)
    started_at: datetime = field(default_factory=datetime.utcnow)
    completed_at: Optional[datetime] = None


class MultiServiceOrchestrator:
    """Advanced multi-service orchestration for integration testing."""
    
    def __init__(
        self,
        services: Dict[str, ServiceEndpoint],
        default_timeout: int = 30,
        health_check_interval: int = 10
    ):
        self.services = services
        self.default_timeout = default_timeout
        self.health_check_interval = health_check_interval
        self.active_contexts: Dict[str, IntegrationTestContext] = {}
        self._http_client: Optional[AsyncClient] = None
        self._health_monitor_task: Optional[asyncio.Task] = None
        self._lock = asyncio.Lock()
    
    async def __aenter__(self):
        """Async context manager entry."""
        self._http_client = AsyncClient(timeout=self.default_timeout)
        await self._start_health_monitoring()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self._health_monitor_task:
            self._health_monitor_task.cancel()
            try:
                await self._health_monitor_task
            except asyncio.CancelledError:
                pass
        
        if self._http_client:
            await self._http_client.aclose()
    
    async def _start_health_monitoring(self):
        """Start background health monitoring."""
        async def monitor_health():
            while True:
                try:
                    await asyncio.sleep(self.health_check_interval)
                    await self._perform_health_checks()
                except asyncio.CancelledError:
                    break
                except Exception as e:
                    print(f"Health monitoring error: {e}")
        
        self._health_monitor_task = asyncio.create_task(monitor_health())
    
    async def _perform_health_checks(self):
        """Perform health checks on all services."""
        if not self._http_client:
            return
        
        health_tasks = []
        for service_name, service_config in self.services.items():
            health_tasks.append(self._check_service_health(service_name, service_config))
        
        health_results = await asyncio.gather(*health_tasks, return_exceptions=True)
        
        # Update active contexts with health check results
        async with self._lock:
            for context in self.active_contexts.values():
                for result in health_results:
                    if isinstance(result, ServiceHealthCheck):
                        context.health_checks.append(result)
    
    async def _check_service_health(
        self,
        service_name: str,
        service_config: ServiceEndpoint
    ) -> ServiceHealthCheck:
        """Check health of a specific service."""
        start_time = time.time()
        
        try:
            health_url = f"{service_config.base_url.rstrip('/')}{service_config.health_endpoint}"
            response = await self._http_client.get(
                health_url,
                timeout=service_config.timeout_seconds
            )
            
            response_time_ms = (time.time() - start_time) * 1000
            
            if response.status_code == 200:
                status = ServiceHealthStatus.HEALTHY
                details = response.json() if response.headers.get("content-type", "").startswith("application/json") else {}
            elif response.status_code in [429, 503]:
                status = ServiceHealthStatus.DEGRADED
                details = {"status_code": response.status_code}
            else:
                status = ServiceHealthStatus.UNHEALTHY
                details = {"status_code": response.status_code}
            
            return ServiceHealthCheck(
                service_name=service_name,
                status=status,
                response_time_ms=response_time_ms,
                timestamp=datetime.utcnow(),
                details=details
            )
            
        except Exception as e:
            response_time_ms = (time.time() - start_time) * 1000
            return ServiceHealthCheck(
                service_name=service_name,
                status=ServiceHealthStatus.UNHEALTHY,
                response_time_ms=response_time_ms,
                timestamp=datetime.utcnow(),
                error_message=str(e)
            )
    
    @asynccontextmanager
    async def create_test_context(
        self,
        test_name: str,
        test_data: Optional[Dict[str, Any]] = None
    ):
        """Create and manage integration test context."""
        test_id = f"test_{uuid.uuid4().hex[:8]}"
        context = IntegrationTestContext(
            test_id=test_id,
            test_name=test_name,
            phase=TestExecutionPhase.SETUP,
            services=self.services,
            test_data=test_data or {}
        )
        
        async with self._lock:
            self.active_contexts[test_id] = context
        
        try:
            # Setup phase
            await self._setup_test_context(context)
            context.phase = TestExecutionPhase.EXECUTION
            
            yield context
            
            # Validation phase
            context.phase = TestExecutionPhase.VALIDATION
            await self._validate_test_context(context)
            
        finally:
            # Cleanup phase
            context.phase = TestExecutionPhase.CLEANUP
            await self._cleanup_test_context(context)
            context.completed_at = datetime.utcnow()
            
            async with self._lock:
                if test_id in self.active_contexts:
                    del self.active_contexts[test_id]
    
    async def _setup_test_context(self, context: IntegrationTestContext):
        """Setup test context with initial health checks."""
        print(f"Setting up test context: {context.test_name}")
        
        # Perform initial health checks
        setup_tasks = []
        for service_name, service_config in self.services.items():
            setup_tasks.append(self._check_service_health(service_name, service_config))
        
        health_results = await asyncio.gather(*setup_tasks, return_exceptions=True)
        
        for result in health_results:
            if isinstance(result, ServiceHealthCheck):
                context.health_checks.append(result)
                if result.status == ServiceHealthStatus.UNHEALTHY:
                    context.errors.append(f"Service {result.service_name} is unhealthy during setup")
    
    async def _validate_test_context(self, context: IntegrationTestContext):
        """Validate test context after execution."""
        print(f"Validating test context: {context.test_name}")
        
        # Validate service health
        recent_health_checks = [
            hc for hc in context.health_checks
            if hc.timestamp > context.started_at
        ]
        
        unhealthy_services = [
            hc.service_name for hc in recent_health_checks
            if hc.status == ServiceHealthStatus.UNHEALTHY
        ]
        
        if unhealthy_services:
            context.errors.append(f"Services became unhealthy during test: {unhealthy_services}")
        
        # Validate performance metrics
        for metric_name, values in context.performance_metrics.items():
            if values:
                avg_value = statistics.mean(values)
                max_value = max(values)
                
                # Basic performance validation
                if "response_time" in metric_name and avg_value > 1000:  # 1 second threshold
                    context.errors.append(f"High average {metric_name}: {avg_value:.2f}ms")
                
                if "error_rate" in metric_name and avg_value > 0.05:  # 5% error rate threshold
                    context.errors.append(f"High {metric_name}: {avg_value:.2%}")
    
    async def _cleanup_test_context(self, context: IntegrationTestContext):
        """Cleanup test context resources."""
        print(f"Cleaning up test context: {context.test_name}")
        
        # Perform final health checks
        cleanup_tasks = []
        for service_name, service_config in self.services.items():
            cleanup_tasks.append(self._check_service_health(service_name, service_config))
        
        final_health_results = await asyncio.gather(*cleanup_tasks, return_exceptions=True)
        
        for result in final_health_results:
            if isinstance(result, ServiceHealthCheck):
                context.health_checks.append(result)
    
    async def execute_coordinated_operation(
        self,
        context: IntegrationTestContext,
        operation_name: str,
        service_calls: List[Tuple[str, str, Dict[str, Any]]],  # (service_name, endpoint, data)
        execution_strategy: str = "parallel"  # "parallel", "sequential", "pipeline"
    ) -> Dict[str, Any]:
        """Execute coordinated operation across multiple services."""
        print(f"Executing coordinated operation: {operation_name} (strategy: {execution_strategy})")
        
        operation_start = time.time()
        operation_results = {}
        
        try:
            if execution_strategy == "parallel":
                results = await self._execute_parallel_calls(context, service_calls)
            elif execution_strategy == "sequential":
                results = await self._execute_sequential_calls(context, service_calls)
            elif execution_strategy == "pipeline":
                results = await self._execute_pipeline_calls(context, service_calls)
            else:
                raise ValueError(f"Unknown execution strategy: {execution_strategy}")
            
            operation_results = {
                "operation_name": operation_name,
                "execution_strategy": execution_strategy,
                "results": results,
                "success": all(r.get("success", False) for r in results.values()),
                "execution_time_ms": (time.time() - operation_start) * 1000
            }
            
            # Record performance metrics
            if "response_time" not in context.performance_metrics:
                context.performance_metrics["response_time"] = []
            context.performance_metrics["response_time"].append(operation_results["execution_time_ms"])
            
        except Exception as e:
            operation_results = {
                "operation_name": operation_name,
                "execution_strategy": execution_strategy,
                "success": False,
                "error": str(e),
                "execution_time_ms": (time.time() - operation_start) * 1000
            }
            context.errors.append(f"Coordinated operation failed: {operation_name} - {str(e)}")
        
        return operation_results
    
    async def _execute_parallel_calls(
        self,
        context: IntegrationTestContext,
        service_calls: List[Tuple[str, str, Dict[str, Any]]]
    ) -> Dict[str, Any]:
        """Execute service calls in parallel."""
        tasks = []
        for service_name, endpoint, data in service_calls:
            task = self._make_service_call(context, service_name, endpoint, data)
            tasks.append((service_name, task))
        
        results = {}
        for service_name, task in tasks:
            try:
                result = await task
                results[service_name] = result
            except Exception as e:
                results[service_name] = {"success": False, "error": str(e)}
        
        return results
    
    async def _execute_sequential_calls(
        self,
        context: IntegrationTestContext,
        service_calls: List[Tuple[str, str, Dict[str, Any]]]
    ) -> Dict[str, Any]:
        """Execute service calls sequentially."""
        results = {}
        
        for service_name, endpoint, data in service_calls:
            try:
                result = await self._make_service_call(context, service_name, endpoint, data)
                results[service_name] = result
                
                # If this call failed, stop the sequence
                if not result.get("success", False):
                    context.errors.append(f"Sequential operation stopped at {service_name}")
                    break
                    
            except Exception as e:
                results[service_name] = {"success": False, "error": str(e)}
                context.errors.append(f"Sequential operation failed at {service_name}: {str(e)}")
                break
        
        return results
    
    async def _execute_pipeline_calls(
        self,
        context: IntegrationTestContext,
        service_calls: List[Tuple[str, str, Dict[str, Any]]]
    ) -> Dict[str, Any]:
        """Execute service calls in pipeline mode (output feeds next input)."""
        results = {}
        pipeline_data = None
        
        for service_name, endpoint, initial_data in service_calls:
            try:
                # Use pipeline data if available, otherwise use initial data
                call_data = pipeline_data if pipeline_data is not None else initial_data
                
                result = await self._make_service_call(context, service_name, endpoint, call_data)
                results[service_name] = result
                
                if result.get("success", False):
                    # Extract data to pass to next service
                    pipeline_data = result.get("data", {})
                else:
                    context.errors.append(f"Pipeline stopped at {service_name}")
                    break
                    
            except Exception as e:
                results[service_name] = {"success": False, "error": str(e)}
                context.errors.append(f"Pipeline failed at {service_name}: {str(e)}")
                break
        
        return results
    
    async def _make_service_call(
        self,
        context: IntegrationTestContext,
        service_name: str,
        endpoint: str,
        data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Make a call to a specific service."""
        if service_name not in self.services:
            raise ValueError(f"Unknown service: {service_name}")
        
        service_config = self.services[service_name]
        url = f"{service_config.base_url.rstrip('/')}{endpoint}"
        
        start_time = time.time()
        
        try:
            if data:
                response = await self._http_client.post(url, json=data)
            else:
                response = await self._http_client.get(url)
            
            response_time_ms = (time.time() - start_time) * 1000
            
            result = {
                "success": response.status_code < 400,
                "status_code": response.status_code,
                "response_time_ms": response_time_ms,
                "timestamp": datetime.utcnow().isoformat()
            }
            
            # Parse response data if JSON
            if response.headers.get("content-type", "").startswith("application/json"):
                try:
                    result["data"] = response.json()
                except:
                    result["data"] = response.text
            else:
                result["data"] = response.text
            
            return result
            
        except Exception as e:
            response_time_ms = (time.time() - start_time) * 1000
            return {
                "success": False,
                "error": str(e),
                "response_time_ms": response_time_ms,
                "timestamp": datetime.utcnow().isoformat()
            }
    
    async def validate_data_consistency(
        self,
        context: IntegrationTestContext,
        entity_id: str,
        validation_endpoints: Dict[str, str]  # service_name -> endpoint
    ) -> Dict[str, Any]:
        """Validate data consistency across multiple services."""
        print(f"Validating data consistency for entity: {entity_id}")
        
        consistency_report = {
            "entity_id": entity_id,
            "timestamp": datetime.utcnow().isoformat(),
            "services_checked": [],
            "data_hashes": {},
            "consistent": True,
            "inconsistencies": [],
            "validation_time_ms": 0.0
        }
        
        start_time = time.time()
        
        try:
            # Retrieve data from all services
            service_data = {}
            validation_tasks = []
            
            for service_name, endpoint in validation_endpoints.items():
                task = self._make_service_call(
                    context, 
                    service_name, 
                    endpoint.replace("{entity_id}", entity_id),
                    {}
                )
                validation_tasks.append((service_name, task))
            
            # Execute validation calls
            for service_name, task in validation_tasks:
                try:
                    result = await task
                    if result.get("success", False):
                        service_data[service_name] = result.get("data", {})
                        consistency_report["services_checked"].append(service_name)
                    else:
                        consistency_report["inconsistencies"].append(
                            f"Failed to retrieve data from {service_name}: {result.get('error', 'Unknown error')}"
                        )
                except Exception as e:
                    consistency_report["inconsistencies"].append(
                        f"Exception retrieving data from {service_name}: {str(e)}"
                    )
            
            # Compare data consistency
            if len(service_data) > 1:
                # Create data hashes for comparison
                for service_name, data in service_data.items():
                    # Normalize data for comparison (remove timestamps, etc.)
                    normalized_data = self._normalize_data_for_comparison(data)
                    data_hash = hashlib.md5(
                        json.dumps(normalized_data, sort_keys=True).encode()
                    ).hexdigest()
                    consistency_report["data_hashes"][service_name] = data_hash
                
                # Check for consistency
                unique_hashes = set(consistency_report["data_hashes"].values())
                if len(unique_hashes) > 1:
                    consistency_report["consistent"] = False
                    consistency_report["inconsistencies"].append(
                        f"Data mismatch detected: {dict(consistency_report['data_hashes'])}"
                    )
            
            consistency_report["validation_time_ms"] = (time.time() - start_time) * 1000
            
            # Add to context
            context.consistency_validations.append(consistency_report)
            
            return consistency_report
            
        except Exception as e:
            consistency_report["consistent"] = False
            consistency_report["inconsistencies"].append(f"Validation failed: {str(e)}")
            consistency_report["validation_time_ms"] = (time.time() - start_time) * 1000
            return consistency_report
    
    def _normalize_data_for_comparison(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Normalize data for consistency comparison."""
        normalized = {}
        
        for key, value in data.items():
            # Skip timestamp fields and other volatile data
            if key.lower() in ["timestamp", "created_at", "updated_at", "last_modified"]:
                continue
            
            # Skip version fields that might differ
            if key.lower() in ["version", "revision", "etag"]:
                continue
            
            # Recursively normalize nested dictionaries
            if isinstance(value, dict):
                normalized[key] = self._normalize_data_for_comparison(value)
            else:
                normalized[key] = value
        
        return normalized
    
    def get_context_summary(self, context: IntegrationTestContext) -> Dict[str, Any]:
        """Get comprehensive summary of test context."""
        execution_time = (
            (context.completed_at or datetime.utcnow()) - context.started_at
        ).total_seconds()
        
        # Analyze health checks
        health_summary = {}
        for service_name in self.services.keys():
            service_health_checks = [
                hc for hc in context.health_checks
                if hc.service_name == service_name
            ]
            
            if service_health_checks:
                latest_check = max(service_health_checks, key=lambda x: x.timestamp)
                avg_response_time = statistics.mean([hc.response_time_ms for hc in service_health_checks])
                
                health_summary[service_name] = {
                    "status": latest_check.status.value,
                    "checks_performed": len(service_health_checks),
                    "avg_response_time_ms": avg_response_time,
                    "last_check": latest_check.timestamp.isoformat()
                }
        
        # Analyze performance metrics
        performance_summary = {}
        for metric_name, values in context.performance_metrics.items():
            if values:
                performance_summary[metric_name] = {
                    "count": len(values),
                    "avg": statistics.mean(values),
                    "min": min(values),
                    "max": max(values),
                    "p95": statistics.quantiles(values, n=20)[18] if len(values) >= 20 else max(values)
                }
        
        # Analyze consistency validations
        consistency_summary = {
            "total_validations": len(context.consistency_validations),
            "consistent_validations": len([
                v for v in context.consistency_validations if v["consistent"]  
            ]),
            "inconsistencies_found": sum(
                len(v["inconsistencies"]) for v in context.consistency_validations
            )
        }
        
        return {
            "test_id": context.test_id,
            "test_name": context.test_name,
            "execution_time_seconds": execution_time,
            "phase": context.phase.value,
            "success": len(context.errors) == 0,
            "error_count": len(context.errors),
            "errors": context.errors,
            "health_summary": health_summary,
            "performance_summary": performance_summary,
            "consistency_summary": consistency_summary,
            "services_tested": len(self.services)
        }


class IntegrationTestValidator:
    """Advanced validation utilities for integration testing."""
    
    @staticmethod
    def validate_service_response(
        response: Dict[str, Any],
        expected_schema: Dict[str, Any],
        response_time_threshold_ms: float = 1000.0
    ) -> Dict[str, Any]:
        """Validate service response against expected schema and performance."""
        validation_result = {
            "schema_valid": True,
            "performance_valid": True,
            "errors": []
        }
        
        # Validate response time
        response_time = response.get("response_time_ms", 0)
        if response_time > response_time_threshold_ms:
            validation_result["performance_valid"] = False
            validation_result["errors"].append(
                f"Response time {response_time}ms exceeds threshold {response_time_threshold_ms}ms"
            )
        
        # Validate schema (basic validation)
        response_data = response.get("data", {})
        for field, expected_type in expected_schema.items():
            if field not in response_data:
                validation_result["schema_valid"] = False
                validation_result["errors"].append(f"Missing required field: {field}")
            elif not isinstance(response_data[field], expected_type):
                validation_result["schema_valid"] = False
                validation_result["errors"].append(
                    f"Field {field} has incorrect type: expected {expected_type.__name__}, got {type(response_data[field]).__name__}"
                )
        
        return validation_result
    
    @staticmethod
    def validate_end_to_end_workflow(
        workflow_results: List[Dict[str, Any]],
        expected_workflow_steps: List[str]
    ) -> Dict[str, Any]:
        """Validate end-to-end workflow execution."""
        validation_result = {
            "workflow_complete": True,
            "steps_completed": [],
            "steps_failed": [],
            "total_execution_time_ms": 0.0,
            "errors": []
        }
        
        total_time = 0.0
        
        for i, (result, expected_step) in enumerate(zip(workflow_results, expected_workflow_steps)):
            step_name = f"step_{i+1}_{expected_step}"
            step_time = result.get("execution_time_ms", 0)
            total_time += step_time
            
            if result.get("success", False):
                validation_result["steps_completed"].append(step_name)
            else:
                validation_result["steps_failed"].append(step_name)
                validation_result["workflow_complete"] = False
                validation_result["errors"].append(
                    f"Workflow step {step_name} failed: {result.get('error', 'Unknown error')}"
                )
        
        validation_result["total_execution_time_ms"] = total_time
        
        return validation_result


# Convenience functions for common integration testing patterns

async def execute_realistic_user_workflow(
    orchestrator: MultiServiceOrchestrator,
    user_scenario: Dict[str, Any]
) -> Dict[str, Any]:
    """Execute a realistic user workflow across multiple services."""
    async with orchestrator.create_test_context(
        test_name=f"user_workflow_{user_scenario.get('user_type', 'unknown')}"
    ) as context:
        
        workflow_steps = user_scenario.get("workflow_steps", [])
        workflow_results = []
        
        for step in workflow_steps:
            step_result = await orchestrator.execute_coordinated_operation(
                context,
                step["operation_name"],
                step["service_calls"],
                step.get("execution_strategy", "sequential")
            )
            workflow_results.append(step_result)
            
            # If step failed and it's critical, stop workflow
            if not step_result["success"] and step.get("critical", False):
                context.errors.append(f"Critical workflow step failed: {step['operation_name']}")
                break
        
        # Validate data consistency if specified
        consistency_results = []
        if user_scenario.get("validate_consistency"):
            for entity_id in user_scenario.get("entities_to_validate", []):
                consistency_result = await orchestrator.validate_data_consistency(
                    context,
                    entity_id,
                    user_scenario.get("consistency_endpoints", {})
                )
                consistency_results.append(consistency_result)
        
        summary = orchestrator.get_context_summary(context)
        summary["workflow_results"] = workflow_results
        summary["consistency_results"] = consistency_results
        
        return summary


async def measure_cross_service_performance(
    orchestrator: MultiServiceOrchestrator,
    performance_scenario: Dict[str, Any]
) -> Dict[str, Any]:
    """Measure performance across multiple services under load."""
    async with orchestrator.create_test_context(
        test_name=f"performance_test_{performance_scenario.get('scenario_name', 'unknown')}"
    ) as context:
        
        concurrent_operations = performance_scenario.get("concurrent_operations", 10)
        operation_duration = performance_scenario.get("duration_seconds", 60)
        service_calls = performance_scenario.get("service_calls", [])
        
        print(f"Starting performance test: {concurrent_operations} concurrent operations for {operation_duration}s")
        
        # Execute concurrent operations
        start_time = time.time()
        operation_tasks = []
        
        for i in range(concurrent_operations):
            task = orchestrator.execute_coordinated_operation(
                context,
                f"perf_operation_{i}",
                service_calls,
                "parallel"
            )
            operation_tasks.append(task)
        
        # Wait for operations to complete or timeout
        try:
            results = await asyncio.wait_for(
                asyncio.gather(*operation_tasks, return_exceptions=True),
                timeout=operation_duration
            )
        except asyncio.TimeoutError:
            results = ["timeout"] * len(operation_tasks)
        
        execution_time = time.time() - start_time
        
        # Analyze performance results
        successful_operations = [r for r in results if isinstance(r, dict) and r.get("success")]
        failed_operations = [r for r in results if not (isinstance(r, dict) and r.get("success"))]
        
        performance_summary = {
            "scenario_name": performance_scenario.get("scenario_name"),
            "concurrent_operations": concurrent_operations,
            "execution_time_seconds": execution_time,
            "successful_operations": len(successful_operations),
            "failed_operations": len(failed_operations),
            "success_rate": len(successful_operations) / concurrent_operations,
            "operations_per_second": len(successful_operations) / execution_time,
            "context_summary": orchestrator.get_context_summary(context)
        }
        
        return performance_summary