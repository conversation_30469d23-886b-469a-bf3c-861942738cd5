"""
Failure Injection Utilities

Programmatic failure injection utilities for testing error recovery scenarios.
Provides tools for simulating resource pressure, network failures, service degradation,
and various failure modes to validate resilience patterns.

Key Features:
1. Resource Pressure Simulation - Memory, CPU, disk, connection exhaustion
2. Network Failure Simulation - Intermittent failures, latency, partitions
3. Service Degradation Tools - Performance degradation, partial failures
4. Recovery Timing Controllers - Coordinated failure and recovery scenarios
5. Realistic Failure Patterns - Based on real-world failure modes
"""

import asyncio
import time
import threading
import psutil
import tempfile
import os
import random
import socket
import gc
from typing import Dict, Any, List, Optional, Callable, AsyncIterator, Tuple
from contextlib import asynccontextmanager, contextmanager
from dataclasses import dataclass, field
from enum import Enum
from concurrent.futures import Thread<PERSON>oolExecutor, Future
from unittest.mock import Mock, AsyncMock, patch, MagicMock
import logging
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class FailureType(Enum):
    """Types of failures that can be injected."""
    MEMORY_EXHAUSTION = "memory_exhaustion"
    CONNECTION_POOL_EXHAUSTION = "connection_pool_exhaustion"
    THREAD_POOL_SATURATION = "thread_pool_saturation"
    DISK_SPACE_EXHAUSTION = "disk_space_exhaustion"
    FILE_DESCRIPTOR_EXHAUSTION = "file_descriptor_exhaustion"
    NETWORK_TIMEOUT = "network_timeout"
    NETWORK_PARTITION = "network_partition"
    SERVICE_UNAVAILABLE = "service_unavailable"
    PERFORMANCE_DEGRADATION = "performance_degradation"
    INTERMITTENT_FAILURE = "intermittent_failure"
    CASCADING_FAILURE = "cascading_failure"


class FailurePattern(Enum):
    """Patterns of failure behavior."""
    IMMEDIATE = "immediate"
    PROGRESSIVE = "progressive"
    INTERMITTENT = "intermittent"
    CASCADING = "cascading"
    RECOVERY_AFTER = "recovery_after"


@dataclass
class FailureScenario:
    """Configuration for a failure scenario."""
    failure_type: FailureType
    pattern: FailurePattern
    duration_seconds: float = 10.0
    intensity: float = 1.0  # 0.0 to 1.0
    recovery_time_seconds: float = 5.0
    parameters: Dict[str, Any] = field(default_factory=dict)
    description: str = ""


@dataclass 
class ResourceUsageSnapshot:
    """Snapshot of current resource usage."""
    timestamp: float
    memory_mb: float
    cpu_percent: float
    file_descriptors: int
    threads: int
    connections: int = 0
    disk_usage_mb: float = 0.0
    
    @classmethod
    def capture(cls) -> 'ResourceUsageSnapshot':
        """Capture current resource usage."""
        process = psutil.Process()
        memory_info = process.memory_info()
        
        return cls(
            timestamp=time.time(),
            memory_mb=memory_info.rss / 1024 / 1024,
            cpu_percent=process.cpu_percent(),
            file_descriptors=process.num_fds() if hasattr(process, 'num_fds') else 0,
            threads=process.num_threads(),
            connections=len(process.connections()) if hasattr(process, 'connections') else 0,
            disk_usage_mb=psutil.disk_usage('/').used / 1024 / 1024
        )


class MemoryPressureInjector:
    """Injects memory pressure scenarios."""
    
    def __init__(self):
        self.leaked_objects: List[Any] = []
        self.pressure_active = False
        
    @asynccontextmanager
    async def memory_pressure(self, target_mb: int = 500, progressive: bool = True):
        """
        Create memory pressure by allocating large objects.
        
        Args:
            target_mb: Target memory allocation in MB
            progressive: Whether to allocate progressively or all at once
        """
        initial_memory = psutil.Process().memory_info().rss / 1024 / 1024
        logger.info(f"Starting memory pressure injection: {target_mb}MB target")
        
        self.pressure_active = True
        
        try:
            if progressive:
                # Allocate progressively over time
                chunk_size = min(50, target_mb // 10)  # 50MB chunks
                chunks_needed = target_mb // chunk_size
                
                for i in range(chunks_needed):
                    if not self.pressure_active:
                        break
                        
                    # Allocate chunk
                    chunk = bytearray(chunk_size * 1024 * 1024)
                    self.leaked_objects.append(chunk)
                    
                    current_memory = psutil.Process().memory_info().rss / 1024 / 1024
                    logger.debug(f"Memory pressure chunk {i+1}/{chunks_needed}: "
                               f"{current_memory - initial_memory:.1f}MB allocated")
                    
                    await asyncio.sleep(0.1)  # Brief pause between allocations
            else:
                # Allocate all at once
                for i in range(target_mb // 10):  # 10MB objects
                    obj = bytearray(10 * 1024 * 1024)
                    self.leaked_objects.append(obj)
            
            current_memory = psutil.Process().memory_info().rss / 1024 / 1024
            actual_increase = current_memory - initial_memory
            logger.info(f"Memory pressure established: {actual_increase:.1f}MB allocated")
            
            yield {
                "initial_memory_mb": initial_memory,
                "current_memory_mb": current_memory,
                "allocated_mb": actual_increase,
                "objects_count": len(self.leaked_objects)
            }
            
        finally:
            self.pressure_active = False
            await self._cleanup_memory()
    
    async def _cleanup_memory(self):
        """Clean up allocated memory objects."""
        logger.info(f"Cleaning up {len(self.leaked_objects)} memory objects")
        
        objects_to_clear = len(self.leaked_objects)
        self.leaked_objects.clear()
        gc.collect()
        
        # Give GC time to work
        await asyncio.sleep(0.5)
        
        logger.info(f"Memory cleanup completed: {objects_to_clear} objects cleared")


class ConnectionPoolExhauster:
    """Exhausts connection pools to test connection handling."""
    
    def __init__(self):
        self.active_connections: List[Any] = []
        self.mock_connections: List[Mock] = []
    
    @asynccontextmanager
    async def exhaust_pool(self, pool_size: int = 10, connection_type: str = "database"):
        """
        Exhaust a connection pool by creating and holding connections.
        
        Args:
            pool_size: Maximum pool size to exhaust
            connection_type: Type of connections to exhaust
        """
        logger.info(f"Exhausting {connection_type} connection pool: {pool_size} connections")
        
        try:
            # Create mock connections that hold resources
            for i in range(pool_size + 2):  # Exceed pool size
                if connection_type == "database":
                    conn = Mock()
                    conn.close = AsyncMock()
                    conn.is_connected = False
                    self.mock_connections.append(conn)
                elif connection_type == "redis":
                    conn = Mock()
                    conn.ping = AsyncMock(side_effect=Exception("Connection exhausted"))
                    conn.close = AsyncMock()
                    self.mock_connections.append(conn)
                else:
                    # Generic connection mock
                    conn = Mock()
                    conn.close = Mock()
                    self.mock_connections.append(conn)
                
                await asyncio.sleep(0.01)  # Small delay between connections
            
            logger.info(f"Connection pool exhausted: {len(self.mock_connections)} connections created")
            
            yield {
                "connections_created": len(self.mock_connections),
                "pool_size": pool_size,
                "connection_type": connection_type
            }
            
        finally:
            await self._cleanup_connections()
    
    async def _cleanup_connections(self):
        """Clean up mock connections."""
        logger.info(f"Cleaning up {len(self.mock_connections)} connections")
        
        for conn in self.mock_connections:
            if hasattr(conn, 'close'):
                if asyncio.iscoroutinefunction(conn.close):
                    await conn.close()
                else:
                    conn.close()
        
        self.mock_connections.clear()
        logger.info("Connection cleanup completed")


class ThreadPoolSaturator:
    """Saturates thread pools to test threading limits."""
    
    def __init__(self):
        self.executors: List[ThreadPoolExecutor] = []
        self.futures: List[Future] = []
        self.blocking_tasks_active = False
    
    @asynccontextmanager
    async def saturate_thread_pool(self, max_workers: int = 5, task_duration: float = 2.0):
        """
        Saturate a thread pool with blocking tasks.
        
        Args:
            max_workers: Maximum number of worker threads
            task_duration: Duration each task should block
        """
        logger.info(f"Saturating thread pool: {max_workers} workers, {task_duration}s tasks")
        
        self.blocking_tasks_active = True
        executor = ThreadPoolExecutor(max_workers=max_workers)
        self.executors.append(executor)
        
        def blocking_task(task_id: int) -> str:
            """A CPU-intensive blocking task."""
            start_time = time.time()
            
            # CPU-intensive work
            result = 0
            while time.time() - start_time < task_duration and self.blocking_tasks_active:
                for i in range(100000):
                    result += i ** 2
                time.sleep(0.01)  # Brief pause
            
            return f"task_{task_id}_completed"
        
        try:
            # Submit more tasks than workers to create saturation
            num_tasks = max_workers * 3
            
            for i in range(num_tasks):
                future = executor.submit(blocking_task, i)
                self.futures.append(future)
            
            # Wait a moment for saturation to take effect
            await asyncio.sleep(0.2)
            
            # Check how many tasks are running vs queued
            running_tasks = sum(1 for f in self.futures if f.running())
            pending_tasks = sum(1 for f in self.futures if not f.done() and not f.running())
            
            logger.info(f"Thread pool saturated: {running_tasks} running, {pending_tasks} queued")
            
            yield {
                "max_workers": max_workers,
                "total_tasks": num_tasks,
                "running_tasks": running_tasks,
                "pending_tasks": pending_tasks
            }
            
        finally:
            self.blocking_tasks_active = False
            await self._cleanup_thread_pools()
    
    async def _cleanup_thread_pools(self):
        """Clean up thread pools and futures."""
        logger.info(f"Cleaning up {len(self.executors)} thread pools")
        
        # Cancel remaining futures
        for future in self.futures:
            if not future.done():
                future.cancel()
        
        # Shutdown executors
        for executor in self.executors:
            executor.shutdown(wait=False)
        
        self.futures.clear()
        self.executors.clear()
        logger.info("Thread pool cleanup completed")


class DiskSpaceExhauster:
    """Exhausts disk space to test storage limits."""
    
    def __init__(self):
        self.temp_files: List[str] = []
        self.temp_dir: Optional[str] = None
    
    @asynccontextmanager
    async def exhaust_disk_space(self, target_mb: int = 100, temp_location: bool = True):
        """
        Exhaust disk space by creating large temporary files.
        
        Args:
            target_mb: Target disk usage in MB
            temp_location: Whether to use temp directory
        """
        if temp_location:
            self.temp_dir = tempfile.mkdtemp(prefix="disk_exhaustion_")
        else:
            self.temp_dir = "/tmp"
        
        logger.info(f"Exhausting disk space: {target_mb}MB in {self.temp_dir}")
        
        try:
            # Create large files in chunks
            chunk_size_mb = 10  # 10MB chunks
            chunks_needed = target_mb // chunk_size_mb
            
            for i in range(chunks_needed):
                temp_file = os.path.join(self.temp_dir, f"large_file_{i}.tmp")
                
                try:
                    with open(temp_file, "wb") as f:
                        # Write chunk_size_mb of data
                        data = b"0" * (chunk_size_mb * 1024 * 1024)
                        f.write(data)
                    
                    self.temp_files.append(temp_file)
                    logger.debug(f"Created large file {i+1}/{chunks_needed}: {temp_file}")
                    
                except OSError as e:
                    if "no space left" in str(e).lower():
                        logger.info(f"Disk space exhausted after {i} files")
                        break
                    else:
                        raise
                
                await asyncio.sleep(0.01)  # Brief pause
            
            total_size_mb = sum(os.path.getsize(f) for f in self.temp_files) / 1024 / 1024
            logger.info(f"Disk space exhaustion completed: {total_size_mb:.1f}MB in {len(self.temp_files)} files")
            
            yield {
                "files_created": len(self.temp_files),
                "total_size_mb": total_size_mb,
                "temp_dir": self.temp_dir
            }
            
        finally:
            await self._cleanup_disk_files()
    
    async def _cleanup_disk_files(self):
        """Clean up temporary files."""
        logger.info(f"Cleaning up {len(self.temp_files)} temporary files")
        
        for temp_file in self.temp_files:
            try:
                if os.path.exists(temp_file):
                    os.unlink(temp_file)
            except Exception as e:
                logger.warning(f"Failed to remove {temp_file}: {e}")
        
        if self.temp_dir and os.path.exists(self.temp_dir):
            try:
                os.rmdir(self.temp_dir)
            except Exception as e:
                logger.warning(f"Failed to remove temp directory {self.temp_dir}: {e}")
        
        self.temp_files.clear()
        logger.info("Disk space cleanup completed")


class NetworkFailureSimulator:
    """Simulates various network failure scenarios."""
    
    def __init__(self):
        self.active_patches: List[Any] = []
        self.failure_active = False
    
    @asynccontextmanager
    async def intermittent_failures(self, failure_rate: float = 0.3, recovery_pattern: str = "exponential"):
        """
        Simulate intermittent network failures.
        
        Args:
            failure_rate: Probability of failure (0.0 to 1.0)
            recovery_pattern: Recovery pattern (exponential, linear, immediate)
        """
        logger.info(f"Simulating intermittent failures: {failure_rate:.1%} failure rate")
        
        self.failure_active = True
        failure_count = 0
        success_count = 0
        
        async def intermittent_operation(*args, **kwargs):
            nonlocal failure_count, success_count
            
            if not self.failure_active:
                success_count += 1
                return {"success": True, "attempt": success_count}
            
            # Apply failure rate with recovery pattern
            if recovery_pattern == "exponential":
                # Exponential recovery: failures become less likely over time
                current_rate = failure_rate * (0.8 ** success_count)
            elif recovery_pattern == "linear":
                # Linear recovery: fixed reduction in failure rate
                current_rate = max(0.1, failure_rate - (success_count * 0.05))
            else:
                # Immediate recovery: constant failure rate
                current_rate = failure_rate
            
            if random.random() < current_rate:
                failure_count += 1
                raise Exception(f"Intermittent network failure #{failure_count}")
            else:
                success_count += 1
                return {"success": True, "attempt": success_count}
        
        try:
            yield {
                "failure_rate": failure_rate,
                "recovery_pattern": recovery_pattern,
                "operation": intermittent_operation
            }
            
        finally:
            self.failure_active = False
            logger.info(f"Intermittent failure simulation ended: "
                       f"{failure_count} failures, {success_count} successes")
    
    @asynccontextmanager
    async def network_partition(self, partition_duration: float = 5.0, recovery_time: float = 2.0):
        """
        Simulate network partition with recovery.
        
        Args:
            partition_duration: Duration of partition in seconds
            recovery_time: Time for full recovery in seconds
        """
        logger.info(f"Simulating network partition: {partition_duration}s partition, {recovery_time}s recovery")
        
        partition_start = time.time()
        
        async def partitioned_operation(*args, **kwargs):
            current_time = time.time()
            elapsed = current_time - partition_start
            
            if elapsed < partition_duration:
                # During partition
                raise Exception("Network partition: destination unreachable")
            elif elapsed < partition_duration + recovery_time:
                # During recovery - intermittent failures
                if random.random() < 0.5:
                    raise Exception("Network recovering: intermittent failure")
                else:
                    return {"success": True, "recovery_phase": True}
            else:
                # Full recovery
                return {"success": True, "recovery_phase": False}
        
        try:
            yield {
                "partition_duration": partition_duration,
                "recovery_time": recovery_time,
                "operation": partitioned_operation
            }
            
        finally:
            logger.info("Network partition simulation completed")
    
    @asynccontextmanager
    async def performance_degradation(self, latency_multiplier: float = 5.0, progressive: bool = True):
        """
        Simulate network performance degradation.
        
        Args:
            latency_multiplier: Multiplier for response times
            progressive: Whether degradation is progressive
        """
        logger.info(f"Simulating performance degradation: {latency_multiplier}x latency")
        
        start_time = time.time()
        
        async def degraded_operation(*args, **kwargs):
            current_time = time.time()
            
            if progressive:
                # Progressive degradation over time
                elapsed = current_time - start_time
                current_multiplier = min(latency_multiplier, 1.0 + (elapsed * 0.5))
            else:
                current_multiplier = latency_multiplier
            
            # Base latency + degradation
            base_latency = 0.1
            actual_latency = base_latency * current_multiplier
            
            await asyncio.sleep(actual_latency)
            
            return {
                "success": True,
                "latency_ms": actual_latency * 1000,
                "multiplier": current_multiplier
            }
        
        try:
            yield {
                "latency_multiplier": latency_multiplier,
                "progressive": progressive,
                "operation": degraded_operation
            }
            
        finally:
            logger.info("Performance degradation simulation completed")


class ServiceFailureOrchestrator:
    """Orchestrates complex failure scenarios across multiple services."""
    
    def __init__(self):
        self.injectors = {
            "memory": MemoryPressureInjector(),
            "connections": ConnectionPoolExhauster(),
            "threads": ThreadPoolSaturator(),
            "disk": DiskSpaceExhauster(),
            "network": NetworkFailureSimulator()
        }
        self.active_scenarios: List[FailureScenario] = []
    
    async def execute_scenario(self, scenario: FailureScenario) -> Dict[str, Any]:
        """
        Execute a specific failure scenario.
        
        Args:
            scenario: Failure scenario configuration
            
        Returns:
            Scenario execution results
        """
        logger.info(f"Executing failure scenario: {scenario.description or scenario.failure_type.value}")
        
        start_time = time.time()
        initial_resources = ResourceUsageSnapshot.capture()
        
        try:
            if scenario.failure_type == FailureType.MEMORY_EXHAUSTION:
                target_mb = scenario.parameters.get("target_mb", 500)
                progressive = scenario.pattern == FailurePattern.PROGRESSIVE
                
                async with self.injectors["memory"].memory_pressure(target_mb, progressive) as result:
                    await asyncio.sleep(scenario.duration_seconds)
                    return self._build_result(scenario, result, start_time, initial_resources)
            
            elif scenario.failure_type == FailureType.CONNECTION_POOL_EXHAUSTION:
                pool_size = scenario.parameters.get("pool_size", 10)
                conn_type = scenario.parameters.get("connection_type", "database")
                
                async with self.injectors["connections"].exhaust_pool(pool_size, conn_type) as result:
                    await asyncio.sleep(scenario.duration_seconds)
                    return self._build_result(scenario, result, start_time, initial_resources)
            
            elif scenario.failure_type == FailureType.THREAD_POOL_SATURATION:
                max_workers = scenario.parameters.get("max_workers", 5)
                task_duration = scenario.parameters.get("task_duration", 2.0)
                
                async with self.injectors["threads"].saturate_thread_pool(max_workers, task_duration) as result:
                    await asyncio.sleep(scenario.duration_seconds)
                    return self._build_result(scenario, result, start_time, initial_resources)
            
            elif scenario.failure_type == FailureType.DISK_SPACE_EXHAUSTION:
                target_mb = scenario.parameters.get("target_mb", 100)
                
                async with self.injectors["disk"].exhaust_disk_space(target_mb) as result:
                    await asyncio.sleep(scenario.duration_seconds)
                    return self._build_result(scenario, result, start_time, initial_resources)
            
            elif scenario.failure_type == FailureType.INTERMITTENT_FAILURE:
                failure_rate = scenario.parameters.get("failure_rate", 0.3)
                recovery_pattern = scenario.parameters.get("recovery_pattern", "exponential")
                
                async with self.injectors["network"].intermittent_failures(failure_rate, recovery_pattern) as result:
                    await asyncio.sleep(scenario.duration_seconds)
                    return self._build_result(scenario, result, start_time, initial_resources)
            
            else:
                raise ValueError(f"Unsupported failure type: {scenario.failure_type}")
                
        except Exception as e:
            logger.error(f"Scenario execution failed: {e}")
            return self._build_error_result(scenario, str(e), start_time, initial_resources)
    
    def _build_result(self, scenario: FailureScenario, injector_result: Dict[str, Any], 
                     start_time: float, initial_resources: ResourceUsageSnapshot) -> Dict[str, Any]:
        """Build scenario execution result."""
        end_time = time.time()
        final_resources = ResourceUsageSnapshot.capture()
        
        return {
            "scenario": {
                "failure_type": scenario.failure_type.value,
                "pattern": scenario.pattern.value,
                "duration_seconds": scenario.duration_seconds,
                "intensity": scenario.intensity,
                "description": scenario.description
            },
            "execution": {
                "start_time": start_time,
                "end_time": end_time,
                "actual_duration": end_time - start_time,
                "success": True
            },
            "resources": {
                "initial": initial_resources.__dict__,
                "final": final_resources.__dict__,
                "changes": {
                    "memory_mb": final_resources.memory_mb - initial_resources.memory_mb,
                    "cpu_percent": final_resources.cpu_percent - initial_resources.cpu_percent,
                    "file_descriptors": final_resources.file_descriptors - initial_resources.file_descriptors,
                    "threads": final_resources.threads - initial_resources.threads
                }
            },
            "injector_result": injector_result
        }
    
    def _build_error_result(self, scenario: FailureScenario, error: str, 
                           start_time: float, initial_resources: ResourceUsageSnapshot) -> Dict[str, Any]:
        """Build error result for failed scenario."""
        end_time = time.time()
        final_resources = ResourceUsageSnapshot.capture()
        
        return {
            "scenario": {
                "failure_type": scenario.failure_type.value,
                "pattern": scenario.pattern.value,
                "description": scenario.description
            },
            "execution": {
                "start_time": start_time,
                "end_time": end_time,
                "actual_duration": end_time - start_time,
                "success": False,
                "error": error
            },
            "resources": {
                "initial": initial_resources.__dict__,
                "final": final_resources.__dict__
            }
        }
    
    async def execute_cascading_failures(self, scenarios: List[FailureScenario], 
                                       cascade_delay: float = 1.0) -> Dict[str, Any]:
        """
        Execute cascading failure scenarios.
        
        Args:
            scenarios: List of scenarios to execute in sequence
            cascade_delay: Delay between scenario triggers
            
        Returns:
            Cascading failure results
        """
        logger.info(f"Executing cascading failures: {len(scenarios)} scenarios")
        
        start_time = time.time()
        results = []
        
        for i, scenario in enumerate(scenarios):
            logger.info(f"Triggering cascade scenario {i+1}/{len(scenarios)}: {scenario.failure_type.value}")
            
            # Execute scenario
            result = await self.execute_scenario(scenario)
            results.append(result)
            
            # Wait before next scenario (except for last one)
            if i < len(scenarios) - 1:
                await asyncio.sleep(cascade_delay)
        
        end_time = time.time()
        
        return {
            "cascade_type": "sequential",
            "total_scenarios": len(scenarios),
            "cascade_delay": cascade_delay,
            "execution": {
                "start_time": start_time,
                "end_time": end_time,
                "total_duration": end_time - start_time
            },
            "scenario_results": results,
            "summary": {
                "successful_scenarios": sum(1 for r in results if r["execution"]["success"]),
                "failed_scenarios": sum(1 for r in results if not r["execution"]["success"])
            }
        }


# Convenience functions for common scenarios

async def create_memory_pressure(target_mb: int = 500, duration: float = 10.0) -> Dict[str, Any]:
    """Create memory pressure for specified duration."""
    injector = MemoryPressureInjector()
    async with injector.memory_pressure(target_mb, progressive=True) as result:
        await asyncio.sleep(duration)
        return result


async def exhaust_connection_pool(pool_size: int = 10, duration: float = 5.0) -> Dict[str, Any]:
    """Exhaust connection pool for specified duration."""
    exhauster = ConnectionPoolExhauster()
    async with exhauster.exhaust_pool(pool_size, "database") as result:
        await asyncio.sleep(duration)
        return result


async def saturate_threads(max_workers: int = 5, duration: float = 5.0) -> Dict[str, Any]:
    """Saturate thread pool for specified duration."""
    saturator = ThreadPoolSaturator()
    async with saturator.saturate_thread_pool(max_workers, 2.0) as result:
        await asyncio.sleep(duration)
        return result


def create_standard_failure_scenarios() -> List[FailureScenario]:
    """Create a set of standard failure scenarios for testing."""
    return [
        FailureScenario(
            failure_type=FailureType.MEMORY_EXHAUSTION,
            pattern=FailurePattern.PROGRESSIVE,
            duration_seconds=10.0,
            parameters={"target_mb": 300},
            description="Progressive memory exhaustion - 300MB over 10 seconds"
        ),
        FailureScenario(
            failure_type=FailureType.CONNECTION_POOL_EXHAUSTION,
            pattern=FailurePattern.IMMEDIATE,
            duration_seconds=5.0,
            parameters={"pool_size": 8, "connection_type": "database"},
            description="Database connection pool exhaustion"
        ),
        FailureScenario(
            failure_type=FailureType.THREAD_POOL_SATURATION,
            pattern=FailurePattern.IMMEDIATE,
            duration_seconds=8.0,
            parameters={"max_workers": 4, "task_duration": 3.0},
            description="Thread pool saturation with blocking tasks"
        ),
        FailureScenario(
            failure_type=FailureType.INTERMITTENT_FAILURE,
            pattern=FailurePattern.INTERMITTENT,
            duration_seconds=15.0,
            parameters={"failure_rate": 0.4, "recovery_pattern": "exponential"},
            description="Intermittent network failures with exponential recovery"
        ),
        FailureScenario(
            failure_type=FailureType.DISK_SPACE_EXHAUSTION,
            pattern=FailurePattern.PROGRESSIVE,
            duration_seconds=6.0,
            parameters={"target_mb": 50},
            description="Disk space exhaustion - 50MB temp files"
        )
    ]


if __name__ == "__main__":
    async def test_failure_injection():
        """Test failure injection utilities."""
        print("Testing failure injection utilities...")
        
        orchestrator = ServiceFailureOrchestrator()
        scenarios = create_standard_failure_scenarios()
        
        # Test individual scenarios
        for scenario in scenarios[:2]:  # Test first 2 scenarios
            print(f"\nTesting: {scenario.description}")
            result = await orchestrator.execute_scenario(scenario)
            
            if result["execution"]["success"]:
                print(f"✓ Scenario completed in {result['execution']['actual_duration']:.2f}s")
                changes = result["resources"]["changes"]
                print(f"  Memory change: {changes['memory_mb']:+.1f}MB")
                print(f"  Thread change: {changes['threads']:+d}")
            else:
                print(f"✗ Scenario failed: {result['execution']['error']}")
        
        print("\nAll tests completed!")
    
    # Run test
    asyncio.run(test_failure_injection())