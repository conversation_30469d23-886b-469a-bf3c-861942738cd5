-- Migration: 003 - Add Table Partitioning (UP)
-- Phase 2 Enhancement: Add partitioning for large dataset performance testing
-- Purpose: Implement date-based partitioning for pattern_results table

-- Create partitioned table for pattern_results_partitioned
CREATE TABLE IF NOT EXISTS pattern_results_partitioned (
    LIKE pattern_results INCLUDING ALL
) PARTITION BY RANGE (created_at);

-- Create partitions for the last 6 months and next 6 months
DO $$
DECLARE
    start_date DATE;
    end_date DATE;
    partition_name TEXT;
    month_start DATE;
    month_end DATE;
BEGIN
    -- Start from 6 months ago
    start_date := DATE_TRUNC('month', CURRENT_DATE - INTERVAL '6 months');
    end_date := DATE_TRUNC('month', CURRENT_DATE + INTERVAL '6 months');
    
    month_start := start_date;
    
    WHILE month_start < end_date LOOP
        month_end := month_start + INTERVAL '1 month';
        partition_name := 'pattern_results_y' || TO_CHAR(month_start, 'YYYY') || 'm' || TO_CHAR(month_start, 'MM');
        
        -- Create partition
        EXECUTE format('CREATE TABLE IF NOT EXISTS %I PARTITION OF pattern_results_partitioned 
                       FOR VALUES FROM (%L) TO (%L)', 
                      partition_name, month_start, month_end);
        
        -- Create indices on partition
        EXECUTE format('CREATE INDEX IF NOT EXISTS %I ON %I (repository_id, pattern_type)', 
                      partition_name || '_repo_type_idx', partition_name);
        
        EXECUTE format('CREATE INDEX IF NOT EXISTS %I ON %I (confidence_score DESC)', 
                      partition_name || '_confidence_idx', partition_name);
        
        month_start := month_end;
    END LOOP;
END $$;

-- Create default partition for any dates outside the range
CREATE TABLE IF NOT EXISTS pattern_results_default PARTITION OF pattern_results_partitioned DEFAULT;

-- Create view to maintain backwards compatibility
CREATE OR REPLACE VIEW pattern_results_view AS
SELECT * FROM pattern_results_partitioned;

-- Create similar partitioning for repository_analysis if needed for large datasets
CREATE TABLE IF NOT EXISTS repository_analysis_partitioned (
    LIKE repository_analysis INCLUDING ALL
) PARTITION BY RANGE (analysis_timestamp);

-- Create quarterly partitions for repository_analysis
DO $$
DECLARE
    start_date DATE;
    end_date DATE;
    partition_name TEXT;
    quarter_start DATE;
    quarter_end DATE;
BEGIN
    -- Start from 2 years ago
    start_date := DATE_TRUNC('quarter', CURRENT_DATE - INTERVAL '2 years');
    end_date := DATE_TRUNC('quarter', CURRENT_DATE + INTERVAL '1 year');
    
    quarter_start := start_date;
    
    WHILE quarter_start < end_date LOOP
        quarter_end := quarter_start + INTERVAL '3 months';
        partition_name := 'repository_analysis_y' || TO_CHAR(quarter_start, 'YYYY') || 'q' || TO_CHAR(quarter_start, 'Q');
        
        -- Create partition
        EXECUTE format('CREATE TABLE IF NOT EXISTS %I PARTITION OF repository_analysis_partitioned 
                       FOR VALUES FROM (%L) TO (%L)', 
                      partition_name, quarter_start, quarter_end);
        
        -- Create indices on partition
        EXECUTE format('CREATE INDEX IF NOT EXISTS %I ON %I (language, status)', 
                      partition_name || '_lang_status_idx', partition_name);
        
        quarter_start := quarter_end;
    END LOOP;
END $$;

-- Create default partition for repository_analysis
CREATE TABLE IF NOT EXISTS repository_analysis_default PARTITION OF repository_analysis_partitioned DEFAULT;

-- Create function to automatically create future partitions
CREATE OR REPLACE FUNCTION create_monthly_partition(table_name TEXT, partition_date DATE)
RETURNS BOOLEAN AS $$
DECLARE
    partition_name TEXT;
    start_date DATE;
    end_date DATE;
BEGIN
    start_date := DATE_TRUNC('month', partition_date);
    end_date := start_date + INTERVAL '1 month';
    partition_name := table_name || '_y' || TO_CHAR(start_date, 'YYYY') || 'm' || TO_CHAR(start_date, 'MM');
    
    -- Check if partition already exists
    IF EXISTS (SELECT 1 FROM pg_tables WHERE tablename = partition_name) THEN
        RETURN FALSE;
    END IF;
    
    -- Create partition
    EXECUTE format('CREATE TABLE %I PARTITION OF %I FOR VALUES FROM (%L) TO (%L)', 
                  partition_name, table_name, start_date, end_date);
    
    -- Create indices
    EXECUTE format('CREATE INDEX %I ON %I (repository_id, pattern_type)', 
                  partition_name || '_repo_type_idx', partition_name);
    EXECUTE format('CREATE INDEX %I ON %I (confidence_score DESC)', 
                  partition_name || '_confidence_idx', partition_name);
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Create maintenance function to drop old partitions
CREATE OR REPLACE FUNCTION drop_old_partitions(table_name TEXT, retention_months INTEGER DEFAULT 12)
RETURNS INTEGER AS $$
DECLARE
    partition_record RECORD;
    dropped_count INTEGER := 0;
    cutoff_date DATE;
BEGIN
    cutoff_date := DATE_TRUNC('month', CURRENT_DATE - INTERVAL '1 month' * retention_months);
    
    FOR partition_record IN 
        SELECT schemaname, tablename 
        FROM pg_tables 
        WHERE tablename LIKE table_name || '_y%'
        AND tablename ~ '_y\d{4}m\d{2}$'
    LOOP
        -- Extract date from partition name and check if it's old enough
        DECLARE
            year_month TEXT;
            partition_date DATE;
        BEGIN
            year_month := substring(partition_record.tablename from '_y(\d{4}m\d{2})$');
            partition_date := TO_DATE(year_month, 'YYYYMM');
            
            IF partition_date < cutoff_date THEN
                EXECUTE format('DROP TABLE IF EXISTS %I', partition_record.tablename);
                dropped_count := dropped_count + 1;
            END IF;
        END;
    END LOOP;
    
    RETURN dropped_count;
END;
$$ LANGUAGE plpgsql;

-- Record migration timing
INSERT INTO migration_log (migration_id, direction, executed_at, execution_time_ms)
SELECT '003', 'up', NOW(), 
       EXTRACT(EPOCH FROM (NOW() - pg_stat_activity.query_start)) * 1000
FROM pg_stat_activity 
WHERE pid = pg_backend_pid()
ON CONFLICT (migration_id, direction) DO UPDATE SET
    executed_at = EXCLUDED.executed_at,
    execution_time_ms = EXCLUDED.execution_time_ms;