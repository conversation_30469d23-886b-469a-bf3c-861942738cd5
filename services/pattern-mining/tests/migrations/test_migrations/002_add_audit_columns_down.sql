-- Migration: 002 - Add Audit Columns (DOWN)
-- Phase 2 Enhancement: Rollback audit columns migration
-- Purpose: Remove audit trail columns and triggers

-- Drop triggers first
DROP TRIGGER IF EXISTS pattern_results_audit_trigger ON pattern_results;
DROP TRIGGER IF EXISTS repository_analysis_audit_trigger ON repository_analysis;
DROP TRIGGER IF EXISTS model_registry_audit_trigger ON model_registry;
DROP TRIGGER IF EXISTS feature_vectors_audit_trigger ON feature_vectors;

-- Drop trigger function
DROP FUNCTION IF EXISTS update_audit_columns();

-- Drop indices on audit columns
DROP INDEX CONCURRENTLY IF EXISTS idx_pattern_results_created_at;
DROP INDEX CONCURRENTLY IF EXISTS idx_pattern_results_updated_at;
DROP INDEX CONCURRENTLY IF EXISTS idx_repository_analysis_created_at;
DROP INDEX CONCURRENTLY IF EXISTS idx_repository_analysis_updated_at;

-- Remove audit columns from pattern_results
ALTER TABLE pattern_results 
DROP COLUMN IF EXISTS created_at,
DROP COLUMN IF EXISTS updated_at,
DROP COLUMN IF EXISTS version,
DROP COLUMN IF EXISTS created_by,
DROP COLUMN IF EXISTS updated_by;

-- Remove audit columns from repository_analysis
ALTER TABLE repository_analysis
DROP COLUMN IF EXISTS created_at,
DROP COLUMN IF EXISTS updated_at,
DROP COLUMN IF EXISTS version,
DROP COLUMN IF EXISTS created_by,
DROP COLUMN IF EXISTS updated_by;

-- Remove audit columns from model_registry
ALTER TABLE model_registry
DROP COLUMN IF EXISTS created_at,
DROP COLUMN IF EXISTS updated_at,
DROP COLUMN IF EXISTS version,
DROP COLUMN IF EXISTS created_by,
DROP COLUMN IF EXISTS updated_by;

-- Remove audit columns from feature_vectors
ALTER TABLE feature_vectors
DROP COLUMN IF EXISTS created_at,
DROP COLUMN IF EXISTS updated_at,
DROP COLUMN IF EXISTS version,
DROP COLUMN IF EXISTS created_by,
DROP COLUMN IF EXISTS updated_by;

-- Record rollback timing
INSERT INTO migration_log (migration_id, direction, executed_at, execution_time_ms)
SELECT '002', 'down', NOW(), 
       EXTRACT(EPOCH FROM (NOW() - pg_stat_activity.query_start)) * 1000
FROM pg_stat_activity 
WHERE pid = pg_backend_pid()
ON CONFLICT (migration_id, direction) DO UPDATE SET
    executed_at = EXCLUDED.executed_at,
    execution_time_ms = EXCLUDED.execution_time_ms;