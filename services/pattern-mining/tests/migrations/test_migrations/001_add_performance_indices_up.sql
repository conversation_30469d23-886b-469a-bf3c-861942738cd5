-- Migration: 001 - Add Performance Indices (UP)
-- Phase 2 Enhancement: Performance optimization indices for advanced testing
-- Purpose: Add comprehensive indices for query performance testing

-- Add index on pattern_results for common query patterns
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_pattern_results_repo_type_confidence
ON pattern_results (repository_id, pattern_type, confidence_score DESC)
WHERE confidence_score >= 0.5;

-- Add index for time-based queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_repository_analysis_timestamp_language
ON repository_analysis (analysis_timestamp DESC, language)
WHERE status = 'completed';

-- Add partial index for high-confidence patterns
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_pattern_results_high_confidence
ON pattern_results (pattern_type, file_path, line_start)
WHERE confidence_score >= 0.8;

-- Add index for feature vector queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_feature_vectors_pattern_type_dimensions
ON feature_vectors (pattern_id, feature_type, dimensions)
WHERE dimensions > 0;

-- Add composite index for model registry
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_model_registry_type_version_accuracy
ON model_registry (model_type, model_version, accuracy DESC NULLS LAST)
WHERE accuracy IS NOT NULL;

-- Add GIN index for metadata JSON queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_pattern_results_metadata_gin
ON pattern_results USING GIN (metadata_)
WHERE metadata_ IS NOT NULL;

-- Add index for pattern similarity queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_pattern_results_file_lines
ON pattern_results (file_path, line_start, line_end)
WHERE line_end > line_start;

-- Record migration timing
INSERT INTO migration_log (migration_id, direction, executed_at, execution_time_ms)
SELECT '001', 'up', NOW(), 
       EXTRACT(EPOCH FROM (NOW() - pg_stat_activity.query_start)) * 1000
FROM pg_stat_activity 
WHERE pid = pg_backend_pid()
ON CONFLICT (migration_id, direction) DO UPDATE SET
    executed_at = EXCLUDED.executed_at,
    execution_time_ms = EXCLUDED.execution_time_ms;