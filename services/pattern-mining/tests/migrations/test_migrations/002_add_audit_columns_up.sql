-- Migration: 002 - Add Audit Columns (UP)
-- Phase 2 Enhancement: Add audit trail columns for data consistency testing
-- Purpose: Add created_at, updated_at, and version columns for optimistic locking

-- Add audit columns to pattern_results
ALTER TABLE pattern_results 
ADD COLUMN IF NOT EXISTS created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
ADD COLUMN IF NOT EXISTS version INTEGER DEFAULT 1,
ADD COLUMN IF NOT EXISTS created_by <PERSON><PERSON><PERSON><PERSON>(255),
ADD COLUMN IF NOT EXISTS updated_by VARCHAR(255);

-- Add audit columns to repository_analysis
ALTER TABLE repository_analysis
ADD COLUMN IF NOT EXISTS created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
ADD COLUMN IF NOT EXISTS version INTEGER DEFAULT 1,
ADD COLUMN IF NOT EXISTS created_by <PERSON><PERSON>HA<PERSON>(255),
ADD COLUMN IF NOT EXISTS updated_by VARCHAR(255);

-- Add audit columns to model_registry
ALTER TABLE model_registry
ADD COLUMN IF NOT EXISTS created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
ADD COLUMN IF NOT EXISTS version INTEGER DEFAULT 1,
ADD COLUMN IF NOT EXISTS created_by VARCHAR(255),
ADD COLUMN IF NOT EXISTS updated_by VARCHAR(255);

-- Add audit columns to feature_vectors
ALTER TABLE feature_vectors
ADD COLUMN IF NOT EXISTS created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
ADD COLUMN IF NOT EXISTS version INTEGER DEFAULT 1,
ADD COLUMN IF NOT EXISTS created_by VARCHAR(255),
ADD COLUMN IF NOT EXISTS updated_by VARCHAR(255);

-- Create trigger function for automatic updated_at and version increment
CREATE OR REPLACE FUNCTION update_audit_columns()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    NEW.version = OLD.version + 1;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for automatic audit column updates
CREATE TRIGGER pattern_results_audit_trigger
    BEFORE UPDATE ON pattern_results
    FOR EACH ROW
    EXECUTE FUNCTION update_audit_columns();

CREATE TRIGGER repository_analysis_audit_trigger
    BEFORE UPDATE ON repository_analysis
    FOR EACH ROW
    EXECUTE FUNCTION update_audit_columns();

CREATE TRIGGER model_registry_audit_trigger
    BEFORE UPDATE ON model_registry
    FOR EACH ROW
    EXECUTE FUNCTION update_audit_columns();

CREATE TRIGGER feature_vectors_audit_trigger
    BEFORE UPDATE ON feature_vectors
    FOR EACH ROW
    EXECUTE FUNCTION update_audit_columns();

-- Create indices on audit columns for performance
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_pattern_results_created_at
ON pattern_results (created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_pattern_results_updated_at
ON pattern_results (updated_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_repository_analysis_created_at
ON repository_analysis (created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_repository_analysis_updated_at
ON repository_analysis (updated_at DESC);

-- Update existing records with current timestamp (for testing)
UPDATE pattern_results 
SET created_at = COALESCE(created_at, NOW() - INTERVAL '1 day' * random() * 30),
    updated_at = COALESCE(updated_at, NOW() - INTERVAL '1 hour' * random() * 24)
WHERE created_at IS NULL OR updated_at IS NULL;

UPDATE repository_analysis
SET created_at = COALESCE(created_at, analysis_timestamp),
    updated_at = COALESCE(updated_at, analysis_timestamp + INTERVAL '1 minute' * random() * 60)
WHERE created_at IS NULL OR updated_at IS NULL;

UPDATE model_registry
SET created_at = COALESCE(created_at, training_date),
    updated_at = COALESCE(updated_at, training_date + INTERVAL '1 hour' * random() * 24)
WHERE created_at IS NULL OR updated_at IS NULL;

UPDATE feature_vectors
SET created_at = COALESCE(created_at, NOW() - INTERVAL '1 day' * random() * 7),
    updated_at = COALESCE(updated_at, NOW() - INTERVAL '1 hour' * random() * 24)
WHERE created_at IS NULL OR updated_at IS NULL;

-- Record migration timing
INSERT INTO migration_log (migration_id, direction, executed_at, execution_time_ms)
SELECT '002', 'up', NOW(), 
       EXTRACT(EPOCH FROM (NOW() - pg_stat_activity.query_start)) * 1000
FROM pg_stat_activity 
WHERE pid = pg_backend_pid()
ON CONFLICT (migration_id, direction) DO UPDATE SET
    executed_at = EXCLUDED.executed_at,
    execution_time_ms = EXCLUDED.execution_time_ms;