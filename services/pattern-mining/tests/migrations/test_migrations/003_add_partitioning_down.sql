-- Migration: 003 - Add Table Partitioning (DOWN)
-- Phase 2 Enhancement: Rollback table partitioning migration
-- Purpose: Remove partitioned tables and restore original structure

-- Drop maintenance functions
DROP FUNCTION IF EXISTS create_monthly_partition(TEXT, DATE);
DROP FUNCTION IF EXISTS drop_old_partitions(TEXT, INTEGER);

-- Drop views
DROP VIEW IF EXISTS pattern_results_view;

-- Drop all partitioned tables and their partitions
-- PostgreSQL will automatically drop child partitions when parent is dropped
DROP TABLE IF EXISTS pattern_results_partitioned CASCADE;
DROP TABLE IF EXISTS repository_analysis_partitioned CASCADE;

-- The original tables (pattern_results, repository_analysis) should still exist
-- as we created new partitioned tables rather than modifying existing ones

-- Record rollback timing
INSERT INTO migration_log (migration_id, direction, executed_at, execution_time_ms)
SELECT '003', 'down', NOW(), 
       EXTRACT(EPOCH FROM (NOW() - pg_stat_activity.query_start)) * 1000
FROM pg_stat_activity 
WHERE pid = pg_backend_pid()
ON CONFLICT (migration_id, direction) DO UPDATE SET
    executed_at = EXCLUDED.executed_at,
    execution_time_ms = EXCLUDED.execution_time_ms;