-- Migration: 001 - Add Performance Indices (DOWN)
-- Phase 2 Enhancement: Rollback performance indices migration
-- Purpose: Remove performance indices for rollback testing

-- Drop indices in reverse order of creation
DROP INDEX CONCURRENTLY IF EXISTS idx_pattern_results_file_lines;
DROP INDEX CONCURRENTLY IF EXISTS idx_pattern_results_metadata_gin;
DROP INDEX CONCURRENTLY IF EXISTS idx_model_registry_type_version_accuracy;
DROP INDEX CONCURRENTLY IF EXISTS idx_feature_vectors_pattern_type_dimensions;
DROP INDEX CONCURRENTLY IF EXISTS idx_pattern_results_high_confidence;
DROP INDEX CONCURRENTLY IF EXISTS idx_repository_analysis_timestamp_language;
DROP INDEX CONCURRENTLY IF EXISTS idx_pattern_results_repo_type_confidence;

-- Record rollback timing
INSERT INTO migration_log (migration_id, direction, executed_at, execution_time_ms)
SELECT '001', 'down', NOW(), 
       EXTRACT(EPOCH FROM (NOW() - pg_stat_activity.query_start)) * 1000
FROM pg_stat_activity 
WHERE pid = pg_backend_pid()
ON CONFLICT (migration_id, direction) DO UPDATE SET
    executed_at = EXCLUDED.executed_at,
    execution_time_ms = EXCLUDED.execution_time_ms;