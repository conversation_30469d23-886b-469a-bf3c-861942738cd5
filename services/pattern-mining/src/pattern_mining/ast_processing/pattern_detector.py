"""
Pattern Mining Service - Enhanced Pattern Detector

Detects code patterns using AST features and ML models.
Supports all contract-defined pattern types.

Wave 2.5: CCL Contract Compliance Implementation - Phase 2
"""

import logging
import asyncio
from typing import Dict, List, Optional, Any, Set, Tuple
from datetime import datetime
from dataclasses import dataclass, field
import uuid

from ..contracts.models import (
    DetectedPatternV1,
    PatternLocation,
    PatternImpact,
    PatternMetrics,
    DetectionMethod,
    CodeExample,
    PatternRange,
)
from .processor import ProcessedFileData, ProcessedASTData
from .feature_extractor import PatternFeatures

logger = logging.getLogger(__name__)


@dataclass
class PatternMatch:
    """Internal representation of a pattern match"""
    pattern_type: str
    pattern_name: str
    confidence: float
    severity: str
    location: PatternLocation
    description: str
    explanation: str
    recommendations: List[str] = field(default_factory=list)
    impact: Optional[PatternImpact] = None
    metrics: Optional[PatternMetrics] = None
    detection_method: Optional[DetectionMethod] = None
    examples: List[CodeExample] = field(default_factory=list)
    related_patterns: List[str] = field(default_factory=list)
    tags: List[str] = field(default_factory=list)


@dataclass
class DetectionContext:
    """Context for pattern detection"""
    file_path: str
    language: str
    repository_features: Dict[str, Any]
    detection_config: Dict[str, Any]
    confidence_threshold: float = 0.7
    max_patterns_per_file: int = 100


@dataclass
class PatternDetectionResult:
    """Result of pattern detection for a file"""
    file_path: str
    patterns: List[DetectedPatternV1]
    detection_time_ms: int
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)


class EnhancedPatternDetector:
    """
    Enhanced pattern detector with contract compliance.
    
    Detects all pattern types defined in CCL contracts:
    - Design patterns
    - Anti-patterns
    - Security vulnerabilities
    - Performance issues
    - Code smells
    - Architectural patterns
    - Test patterns
    - Concurrency patterns
    """
    
    def __init__(self):
        self.pattern_rules = self._initialize_pattern_rules()
        self.ml_models = {}  # Placeholder for ML models
        self._detection_cache = {}
        
    async def detect_patterns(
        self,
        processed_data: ProcessedASTData,
        features: Dict[str, PatternFeatures],
        detection_config: Dict[str, Any]
    ) -> Dict[str, PatternDetectionResult]:
        """
        Detect patterns in all files.
        
        Args:
            processed_data: Processed AST data
            features: Extracted features for each file
            detection_config: Detection configuration from contract
            
        Returns:
            Dictionary mapping file paths to detection results
        """
        results = {}
        
        # Get enabled detectors
        enabled_detectors = detection_config.get('enabled_detectors', [])
        confidence_threshold = detection_config.get('confidence_threshold', 0.7)
        
        # Process files
        tasks = []
        for file_path, file_features in features.items():
            if file_path in processed_data.files:
                context = DetectionContext(
                    file_path=file_path,
                    language=file_features.language,
                    repository_features=processed_data.repository_features,
                    detection_config=detection_config,
                    confidence_threshold=confidence_threshold
                )
                
                task = self._detect_patterns_in_file(
                    processed_data.files[file_path],
                    file_features,
                    context,
                    enabled_detectors
                )
                tasks.append(task)
        
        # Run detection in parallel
        if tasks:
            file_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            for result in file_results:
                if isinstance(result, PatternDetectionResult):
                    results[result.file_path] = result
                elif isinstance(result, Exception):
                    logger.error(f"Pattern detection error: {result}")
        
        return results
    
    async def _detect_patterns_in_file(
        self,
        file_data: ProcessedFileData,
        features: PatternFeatures,
        context: DetectionContext,
        enabled_detectors: List[str]
    ) -> PatternDetectionResult:
        """Detect patterns in a single file"""
        start_time = datetime.utcnow()
        
        result = PatternDetectionResult(
            file_path=context.file_path,
            patterns=[]
        )
        
        try:
            # Check cache
            cache_key = f"{file_data.content_hash}:{','.join(sorted(enabled_detectors))}"
            if cache_key in self._detection_cache:
                cached_patterns = self._detection_cache[cache_key]
                result.patterns = cached_patterns
            else:
                # Detect patterns by type
                pattern_matches = []
                
                if 'design_patterns' in enabled_detectors:
                    matches = await self._detect_design_patterns(file_data, features, context)
                    pattern_matches.extend(matches)
                
                if 'anti_patterns' in enabled_detectors:
                    matches = await self._detect_anti_patterns(file_data, features, context)
                    pattern_matches.extend(matches)
                
                if 'security_vulnerabilities' in enabled_detectors:
                    matches = await self._detect_security_vulnerabilities(file_data, features, context)
                    pattern_matches.extend(matches)
                
                if 'performance_issues' in enabled_detectors:
                    matches = await self._detect_performance_issues(file_data, features, context)
                    pattern_matches.extend(matches)
                
                if 'code_smells' in enabled_detectors:
                    matches = await self._detect_code_smells(file_data, features, context)
                    pattern_matches.extend(matches)
                
                if 'architectural_patterns' in enabled_detectors:
                    matches = await self._detect_architectural_patterns(file_data, features, context)
                    pattern_matches.extend(matches)
                
                if 'test_patterns' in enabled_detectors:
                    matches = await self._detect_test_patterns(file_data, features, context)
                    pattern_matches.extend(matches)
                
                if 'concurrency_patterns' in enabled_detectors:
                    matches = await self._detect_concurrency_patterns(file_data, features, context)
                    pattern_matches.extend(matches)
                
                # Filter by confidence threshold
                pattern_matches = [
                    m for m in pattern_matches
                    if m.confidence >= context.confidence_threshold
                ]
                
                # Limit patterns per file
                if len(pattern_matches) > context.max_patterns_per_file:
                    # Sort by confidence and severity
                    pattern_matches.sort(
                        key=lambda m: (self._severity_score(m.severity), m.confidence),
                        reverse=True
                    )
                    pattern_matches = pattern_matches[:context.max_patterns_per_file]
                    result.warnings.append(
                        f"Pattern count exceeded limit, showing top {context.max_patterns_per_file}"
                    )
                
                # Convert to contract format
                for match in pattern_matches:
                    pattern = self._convert_to_detected_pattern(match)
                    result.patterns.append(pattern)
                
                # Cache results
                self._detection_cache[cache_key] = result.patterns
            
        except Exception as e:
            logger.error(f"Error detecting patterns in {context.file_path}: {e}")
            result.errors.append(str(e))
        
        # Calculate detection time
        end_time = datetime.utcnow()
        result.detection_time_ms = int((end_time - start_time).total_seconds() * 1000)
        
        return result
    
    async def _detect_design_patterns(
        self,
        file_data: ProcessedFileData,
        features: PatternFeatures,
        context: DetectionContext
    ) -> List[PatternMatch]:
        """Detect design patterns"""
        matches = []
        
        # Singleton pattern detection
        if features.structural.has_singleton_hints:
            match = PatternMatch(
                pattern_type="design_pattern",
                pattern_name="Singleton",
                confidence=0.85,
                severity="info",
                location=self._get_pattern_location(file_data, "singleton"),
                description="Singleton design pattern detected",
                explanation="Class implements singleton pattern with static instance",
                recommendations=[
                    "Consider if singleton is necessary",
                    "Ensure thread-safety if used in concurrent environment"
                ],
                impact=PatternImpact(
                    maintainability="neutral",
                    performance="neutral",
                    security="negative",
                    readability="positive",
                    testability="negative"
                ),
                tags=["creational", "gang-of-four"]
            )
            matches.append(match)
        
        # Factory pattern detection
        if features.structural.has_factory_hints:
            match = PatternMatch(
                pattern_type="design_pattern",
                pattern_name="Factory",
                confidence=0.80,
                severity="info",
                location=self._get_pattern_location(file_data, "factory"),
                description="Factory design pattern detected",
                explanation="Methods follow factory pattern for object creation",
                recommendations=[
                    "Consider using abstract factory for more flexibility",
                    "Document the types of objects created"
                ],
                impact=PatternImpact(
                    maintainability="positive",
                    performance="neutral",
                    security="neutral",
                    readability="positive",
                    testability="positive"
                ),
                tags=["creational", "gang-of-four"]
            )
            matches.append(match)
        
        # MVC pattern detection
        if features.structural.follows_mvc_pattern:
            match = PatternMatch(
                pattern_type="architectural_pattern",
                pattern_name="MVC",
                confidence=0.75,
                severity="info",
                location=self._get_file_location(file_data),
                description="Model-View-Controller pattern detected",
                explanation="File structure follows MVC architectural pattern",
                recommendations=[
                    "Maintain clear separation of concerns",
                    "Keep controllers thin and models fat"
                ],
                impact=PatternImpact(
                    maintainability="positive",
                    performance="neutral",
                    security="neutral",
                    readability="positive",
                    testability="positive"
                ),
                tags=["architectural", "separation-of-concerns"]
            )
            matches.append(match)
        
        return matches
    
    async def _detect_anti_patterns(
        self,
        file_data: ProcessedFileData,
        features: PatternFeatures,
        context: DetectionContext
    ) -> List[PatternMatch]:
        """Detect anti-patterns"""
        matches = []
        
        # God class detection
        if features.structural.has_large_classes:
            match = PatternMatch(
                pattern_type="anti_pattern",
                pattern_name="God Class",
                confidence=0.90,
                severity="high",
                location=self._get_pattern_location(file_data, "large_class"),
                description="God Class anti-pattern detected",
                explanation="Class is too large and handles too many responsibilities",
                recommendations=[
                    "Split class into smaller, focused classes",
                    "Apply Single Responsibility Principle",
                    "Extract related functionality into separate classes"
                ],
                impact=PatternImpact(
                    maintainability="negative",
                    performance="negative",
                    security="neutral",
                    readability="negative",
                    testability="negative"
                ),
                metrics=PatternMetrics(
                    complexity_increase=20.0,
                    lines_affected=300,
                    files_affected=1,
                    estimated_fix_time_minutes=180
                ),
                tags=["complexity", "maintainability"]
            )
            matches.append(match)
        
        # Long method detection
        if features.structural.has_long_methods:
            match = PatternMatch(
                pattern_type="code_smell",
                pattern_name="Long Method",
                confidence=0.95,
                severity="medium",
                location=self._get_pattern_location(file_data, "long_method"),
                description="Long Method code smell detected",
                explanation="Method is too long and complex",
                recommendations=[
                    "Extract method into smaller functions",
                    "Use descriptive names for extracted methods",
                    "Consider using method object pattern for very complex methods"
                ],
                impact=PatternImpact(
                    maintainability="negative",
                    performance="neutral",
                    security="neutral",
                    readability="negative",
                    testability="negative"
                ),
                metrics=PatternMetrics(
                    complexity_increase=10.0,
                    lines_affected=100,
                    files_affected=1,
                    estimated_fix_time_minutes=60
                ),
                tags=["complexity", "readability"]
            )
            matches.append(match)
        
        # High coupling detection
        if features.structural.coupling_score > 0.8:
            match = PatternMatch(
                pattern_type="anti_pattern",
                pattern_name="High Coupling",
                confidence=0.85,
                severity="high",
                location=self._get_file_location(file_data),
                description="High coupling between classes detected",
                explanation="Classes are too tightly coupled, making changes difficult",
                recommendations=[
                    "Use dependency injection",
                    "Apply Dependency Inversion Principle",
                    "Introduce interfaces to reduce coupling"
                ],
                impact=PatternImpact(
                    maintainability="negative",
                    performance="neutral",
                    security="neutral",
                    readability="negative",
                    testability="negative"
                ),
                tags=["architecture", "maintainability"]
            )
            matches.append(match)
        
        return matches
    
    async def _detect_security_vulnerabilities(
        self,
        file_data: ProcessedFileData,
        features: PatternFeatures,
        context: DetectionContext
    ) -> List[PatternMatch]:
        """Detect security vulnerabilities"""
        matches = []
        
        # SQL injection detection (simplified)
        if features.semantic.uses_database_patterns and not features.semantic.has_error_handling:
            match = PatternMatch(
                pattern_type="security_vulnerability",
                pattern_name="Potential SQL Injection",
                confidence=0.70,
                severity="critical",
                location=self._get_pattern_location(file_data, "database"),
                description="Potential SQL injection vulnerability",
                explanation="Database operations without proper input validation",
                recommendations=[
                    "Use parameterized queries",
                    "Validate and sanitize all user input",
                    "Use an ORM with built-in protection"
                ],
                impact=PatternImpact(
                    maintainability="neutral",
                    performance="neutral",
                    security="negative",
                    readability="neutral",
                    testability="neutral"
                ),
                detection_method=DetectionMethod(
                    algorithm="rule_based",
                    rule_set="owasp_top_10",
                    features_used=["database_patterns", "error_handling"]
                ),
                tags=["security", "owasp", "injection"]
            )
            matches.append(match)
        
        # Weak authentication detection
        if features.semantic.uses_security_patterns and features.structural.coupling_score < 0.3:
            match = PatternMatch(
                pattern_type="security_vulnerability",
                pattern_name="Weak Authentication",
                confidence=0.65,
                severity="high",
                location=self._get_file_location(file_data),
                description="Potential weak authentication implementation",
                explanation="Authentication logic appears to be loosely coupled or incomplete",
                recommendations=[
                    "Implement proper authentication middleware",
                    "Use established authentication frameworks",
                    "Add multi-factor authentication support"
                ],
                impact=PatternImpact(
                    maintainability="neutral",
                    performance="neutral",
                    security="negative",
                    readability="neutral",
                    testability="neutral"
                ),
                tags=["security", "authentication"]
            )
            matches.append(match)
        
        return matches
    
    async def _detect_performance_issues(
        self,
        file_data: ProcessedFileData,
        features: PatternFeatures,
        context: DetectionContext
    ) -> List[PatternMatch]:
        """Detect performance issues"""
        matches = []
        
        # N+1 query detection
        if features.semantic.uses_database_patterns and features.structural.has_nested_functions:
            match = PatternMatch(
                pattern_type="performance_issue",
                pattern_name="N+1 Query",
                confidence=0.75,
                severity="high",
                location=self._get_pattern_location(file_data, "nested_db"),
                description="Potential N+1 query problem detected",
                explanation="Database queries inside loops can cause performance issues",
                recommendations=[
                    "Use eager loading or joins",
                    "Batch database queries",
                    "Consider using query optimization techniques"
                ],
                impact=PatternImpact(
                    maintainability="neutral",
                    performance="negative",
                    security="neutral",
                    readability="neutral",
                    testability="neutral"
                ),
                metrics=PatternMetrics(
                    complexity_increase=5.0,
                    lines_affected=50,
                    files_affected=1,
                    estimated_fix_time_minutes=120
                ),
                tags=["performance", "database", "scalability"]
            )
            matches.append(match)
        
        # High complexity performance issue
        if features.structural.cyclomatic_complexity > 20:
            match = PatternMatch(
                pattern_type="performance_issue",
                pattern_name="High Complexity",
                confidence=0.85,
                severity="medium",
                location=self._get_file_location(file_data),
                description="High cyclomatic complexity affecting performance",
                explanation="Complex logic paths can impact performance and maintainability",
                recommendations=[
                    "Simplify complex conditionals",
                    "Extract complex logic into separate methods",
                    "Consider using strategy pattern for complex branching"
                ],
                impact=PatternImpact(
                    maintainability="negative",
                    performance="negative",
                    security="neutral",
                    readability="negative",
                    testability="negative"
                ),
                tags=["performance", "complexity"]
            )
            matches.append(match)
        
        return matches
    
    async def _detect_code_smells(
        self,
        file_data: ProcessedFileData,
        features: PatternFeatures,
        context: DetectionContext
    ) -> List[PatternMatch]:
        """Detect code smells"""
        matches = []
        
        # Deep nesting code smell
        if features.structural.has_deep_nesting:
            match = PatternMatch(
                pattern_type="code_smell",
                pattern_name="Deep Nesting",
                confidence=0.90,
                severity="medium",
                location=self._get_pattern_location(file_data, "deep_nesting"),
                description="Deep nesting code smell detected",
                explanation="Code has too many nested levels, reducing readability",
                recommendations=[
                    "Extract nested logic into separate methods",
                    "Use early returns to reduce nesting",
                    "Consider using guard clauses"
                ],
                impact=PatternImpact(
                    maintainability="negative",
                    performance="neutral",
                    security="neutral",
                    readability="negative",
                    testability="negative"
                ),
                tags=["readability", "complexity"]
            )
            matches.append(match)
        
        # Missing error handling
        if not features.semantic.has_error_handling and features.semantic.uses_database_patterns:
            match = PatternMatch(
                pattern_type="code_smell",
                pattern_name="Missing Error Handling",
                confidence=0.80,
                severity="high",
                location=self._get_file_location(file_data),
                description="Missing error handling detected",
                explanation="Code lacks proper error handling mechanisms",
                recommendations=[
                    "Add try-catch blocks for error-prone operations",
                    "Implement proper error logging",
                    "Define clear error handling strategy"
                ],
                impact=PatternImpact(
                    maintainability="negative",
                    performance="neutral",
                    security="negative",
                    readability="neutral",
                    testability="negative"
                ),
                tags=["reliability", "error-handling"]
            )
            matches.append(match)
        
        return matches
    
    async def _detect_architectural_patterns(
        self,
        file_data: ProcessedFileData,
        features: PatternFeatures,
        context: DetectionContext
    ) -> List[PatternMatch]:
        """Detect architectural patterns"""
        matches = []
        
        # Layered architecture detection
        if features.structural.follows_layered_pattern:
            match = PatternMatch(
                pattern_type="architectural_pattern",
                pattern_name="Layered Architecture",
                confidence=0.75,
                severity="info",
                location=self._get_file_location(file_data),
                description="Layered architecture pattern detected",
                explanation="Code follows layered architectural pattern",
                recommendations=[
                    "Maintain clear layer boundaries",
                    "Avoid circular dependencies between layers"
                ],
                impact=PatternImpact(
                    maintainability="positive",
                    performance="neutral",
                    security="positive",
                    readability="positive",
                    testability="positive"
                ),
                tags=["architecture", "structure"]
            )
            matches.append(match)
        
        return matches
    
    async def _detect_test_patterns(
        self,
        file_data: ProcessedFileData,
        features: PatternFeatures,
        context: DetectionContext
    ) -> List[PatternMatch]:
        """Detect test patterns"""
        matches = []
        
        # Test pattern detection based on file naming and structure
        if 'test' in file_data.file_path.lower():
            # Arrange-Act-Assert pattern
            if self._has_aaa_pattern(file_data):
                match = PatternMatch(
                    pattern_type="test_pattern",
                    pattern_name="Arrange-Act-Assert",
                    confidence=0.85,
                    severity="info",
                    location=self._get_file_location(file_data),
                    description="Arrange-Act-Assert test pattern detected",
                    explanation="Tests follow AAA pattern for clarity",
                    recommendations=[
                        "Maintain clear separation between arrange, act, and assert phases",
                        "Keep each phase focused and minimal"
                    ],
                    impact=PatternImpact(
                        maintainability="positive",
                        performance="neutral",
                        security="neutral",
                        readability="positive",
                        testability="positive"
                    ),
                    tags=["testing", "best-practice"]
                )
                matches.append(match)
        
        return matches
    
    async def _detect_concurrency_patterns(
        self,
        file_data: ProcessedFileData,
        features: PatternFeatures,
        context: DetectionContext
    ) -> List[PatternMatch]:
        """Detect concurrency patterns"""
        matches = []
        
        # Async/await pattern detection
        if self._has_async_pattern(file_data):
            match = PatternMatch(
                pattern_type="concurrency_pattern",
                pattern_name="Async/Await",
                confidence=0.90,
                severity="info",
                location=self._get_file_location(file_data),
                description="Async/Await concurrency pattern detected",
                explanation="Code uses modern async/await pattern for concurrency",
                recommendations=[
                    "Ensure proper error handling in async functions",
                    "Avoid mixing callbacks with async/await"
                ],
                impact=PatternImpact(
                    maintainability="positive",
                    performance="positive",
                    security="neutral",
                    readability="positive",
                    testability="neutral"
                ),
                tags=["concurrency", "async", "modern"]
            )
            matches.append(match)
        
        return matches
    
    def _convert_to_detected_pattern(self, match: PatternMatch) -> DetectedPatternV1:
        """Convert internal match to contract-compliant format"""
        pattern = DetectedPatternV1(
            id=f"pattern_{uuid.uuid4().hex[:16]}",
            pattern_type=match.pattern_type,
            pattern_name=match.pattern_name,
            confidence=match.confidence,
            severity=match.severity,
            locations=[match.location],
            description=match.description,
            explanation=match.explanation,
            recommendations=match.recommendations,
            impact=match.impact,
            metrics=match.metrics,
            detection_method=match.detection_method,
            examples=match.examples,
            related_patterns=match.related_patterns,
            tags=match.tags
        )
        
        return pattern
    
    def _get_pattern_location(
        self,
        file_data: ProcessedFileData,
        pattern_hint: str
    ) -> PatternLocation:
        """Get location for a specific pattern hint"""
        # Try to find relevant symbol
        for symbol_name, symbol in file_data.symbol_index.items():
            if pattern_hint.lower() in symbol_name.lower():
                return PatternLocation(
                    file_path=file_data.file_path,
                    range=symbol.range,
                    symbol_name=symbol_name,
                    context={
                        "symbol_type": symbol.type,
                        "visibility": symbol.visibility
                    }
                )
        
        # Default to file location
        return self._get_file_location(file_data)
    
    def _get_file_location(self, file_data: ProcessedFileData) -> PatternLocation:
        """Get location for entire file"""
        # Calculate file range from metrics
        total_lines = file_data.complexity_metrics.get('lines_of_code', 100)
        
        return PatternLocation(
            file_path=file_data.file_path,
            range=PatternRange(
                start_line=1,
                end_line=int(total_lines),
                start_column=0,
                end_column=0
            ),
            context={
                "file_type": "source",
                "language": file_data.language
            }
        )
    
    def _severity_score(self, severity: str) -> int:
        """Convert severity to numeric score for sorting"""
        severity_map = {
            "critical": 5,
            "high": 4,
            "medium": 3,
            "low": 2,
            "info": 1
        }
        return severity_map.get(severity, 0)
    
    def _has_aaa_pattern(self, file_data: ProcessedFileData) -> bool:
        """Check if file has Arrange-Act-Assert pattern"""
        # Simplified check - look for test-related symbols
        test_keywords = ['test', 'assert', 'expect', 'should']
        
        for symbol_name in file_data.symbol_index:
            if any(keyword in symbol_name.lower() for keyword in test_keywords):
                return True
        
        return False
    
    def _has_async_pattern(self, file_data: ProcessedFileData) -> bool:
        """Check if file uses async patterns"""
        async_keywords = ['async', 'await', 'promise', 'future']
        
        # Check node types
        for node_type in file_data.node_types:
            if any(keyword in node_type.lower() for keyword in async_keywords):
                return True
        
        return False
    
    def _initialize_pattern_rules(self) -> Dict[str, Any]:
        """Initialize pattern detection rules"""
        # This would contain detailed rules for each pattern type
        # For now, returning empty dict as rules are embedded in detection methods
        return {}