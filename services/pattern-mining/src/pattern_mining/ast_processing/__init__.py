"""
Pattern Mining Service - AST Data Processing Pipeline

This module processes structured AST data from Repository Analysis service
for efficient pattern detection and feature extraction.

Components:
- ASTDataProcessor: Main processor for FileASTData handling
- FeatureExtractor: Extracts pattern detection features from AST
- PatternDetector: Enhanced pattern detection using AST features
- ASTAnalyzer: Analyzes AST structure for pattern context

Wave 2.5: CCL Contract Compliance Implementation - Phase 2
"""

from .processor import (
    ASTDataProcessor,
    ProcessedASTData,
    ProcessingStats,
    ProcessingConfig,
)

from .feature_extractor import (
    PatternFeatureExtractor,
    PatternFeatures,
    StructuralFeatures,
    SemanticFeatures,
    CodeFeatures,
)

from .pattern_detector import (
    EnhancedPatternDetector,
    PatternDetectionResult,
    PatternMatch,
    DetectionContext,
)

from .analyzer import (
    ASTAnalyzer,
    ASTAnalysisResult,
    StructuralAnalysis,
    ComplexityAnalysis,
    DependencyAnalysis,
)

__all__ = [
    # Main Processor
    "ASTDataProcessor",
    "ProcessedASTData",
    "ProcessingStats",
    "ProcessingConfig",
    
    # Feature Extraction
    "PatternFeatureExtractor",
    "PatternFeatures",
    "StructuralFeatures",
    "SemanticFeatures",
    "CodeFeatures",
    
    # Pattern Detection
    "EnhancedPatternDetector",
    "PatternDetectionResult",
    "PatternMatch",
    "DetectionContext",
    
    # AST Analysis
    "ASTAnalyzer",
    "ASTAnalysisResult",
    "StructuralAnalysis",
    "ComplexityAnalysis",
    "DependencyAnalysis",
]

# Processing Configuration
DEFAULT_PROCESSING_CONFIG = {
    "max_file_size_mb": 5,
    "max_ast_nodes": 10000,
    "enable_caching": True,
    "parallel_processing": True,
    "feature_extraction_timeout": 30,
    "pattern_detection_timeout": 60,
    "min_confidence_threshold": 0.1,
    "max_patterns_per_file": 100,
}

# Supported Languages
SUPPORTED_LANGUAGES = [
    "python",
    "rust",
    "javascript",
    "typescript",
    "java",
    "go",
    "csharp",
    "cpp",
    "c",
    "kotlin",
    "swift",
    "ruby",
    "php",
    "scala",
    "kotlin",
]

# Pattern Detection Capabilities
PATTERN_DETECTION_CAPABILITIES = {
    "design_patterns": {
        "singleton", "factory", "observer", "strategy", "command", "adapter",
        "decorator", "facade", "proxy", "builder", "prototype", "chain_of_responsibility",
        "interpreter", "iterator", "mediator", "memento", "state", "template_method",
        "visitor", "composite", "flyweight", "bridge", "abstract_factory"
    },
    "anti_patterns": {
        "god_class", "god_method", "code_duplication", "long_parameter_list",
        "feature_envy", "inappropriate_intimacy", "message_chains", "middle_man",
        "primitive_obsession", "refused_bequest", "speculative_generality",
        "temporary_field", "parallel_inheritance", "lazy_class", "data_class",
        "duplicate_code", "large_class", "long_method", "divergent_change"
    },
    "security_vulnerabilities": {
        "sql_injection", "xss", "csrf", "insecure_deserialization",
        "buffer_overflow", "format_string", "race_condition", "use_after_free",
        "null_pointer_dereference", "integer_overflow", "path_traversal",
        "weak_cryptography", "hardcoded_credentials", "insecure_random",
        "improper_input_validation", "broken_authentication", "session_fixation"
    },
    "performance_issues": {
        "n_plus_one_query", "inefficient_loop", "memory_leak", "resource_leak",
        "blocking_operation", "synchronous_io", "excessive_allocation",
        "inefficient_algorithm", "cache_miss", "database_connection_leak",
        "thread_pool_exhaustion", "deadlock", "livelocks", "busy_waiting",
        "premature_optimization", "over_synchronization", "gc_pressure"
    },
    "code_smells": {
        "long_method", "large_class", "long_parameter_list", "feature_envy",
        "data_clumps", "primitive_obsession", "switch_statements", "parallel_inheritance",
        "lazy_class", "speculative_generality", "temporary_field", "message_chains",
        "middle_man", "inappropriate_intimacy", "alternative_classes", "incomplete_library",
        "data_class", "refused_bequest", "comments", "duplicate_code"
    },
    "architectural_patterns": {
        "mvc", "mvp", "mvvm", "layered", "hexagonal", "microservices",
        "event_sourcing", "cqrs", "saga", "circuit_breaker", "bulkhead",
        "strangler_fig", "ambassador", "anti_corruption_layer", "gateway",
        "proxy", "load_balancer", "service_mesh", "api_gateway", "bff"
    },
    "test_patterns": {
        "arrange_act_assert", "given_when_then", "test_double", "mock_object",
        "stub", "fake", "spy", "builder_pattern", "object_mother", "test_data_builder",
        "parameterized_tests", "test_fixture", "teardown", "setup", "test_suite",
        "integration_test", "unit_test", "acceptance_test", "regression_test"
    },
    "concurrency_patterns": {
        "thread_pool", "producer_consumer", "reader_writer", "monitor",
        "semaphore", "mutex", "condition_variable", "barrier", "latch",
        "future_promise", "actor_model", "reactive_streams", "async_await",
        "coroutines", "green_threads", "work_stealing", "lock_free", "wait_free"
    }
}