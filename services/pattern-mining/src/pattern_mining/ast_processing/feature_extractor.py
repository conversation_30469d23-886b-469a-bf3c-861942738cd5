"""
Pattern Mining Service - Pattern Feature Extractor

Extracts pattern detection features from processed AST data.
Supports multiple feature types: structural, semantic, and code features.

Wave 2.5: CCL Contract Compliance Implementation - Phase 2
"""

import logging
from typing import Dict, List, Optional, Any, Set, Tuple
from dataclasses import dataclass, field
from collections import defaultdict
import numpy as np

from .processor import ProcessedFileData, ProcessedASTData
from ..contracts.models import PatternASTNode, PatternSymbol

logger = logging.getLogger(__name__)


@dataclass
class StructuralFeatures:
    """Structural features extracted from AST"""
    # Basic metrics
    node_count: int = 0
    symbol_count: int = 0
    max_depth: int = 0
    avg_branching_factor: float = 0.0
    
    # Complexity indicators
    cyclomatic_complexity: float = 0.0
    nesting_complexity: float = 0.0
    inheritance_depth: int = 0
    coupling_score: float = 0.0
    cohesion_score: float = 0.0
    
    # Pattern indicators
    has_nested_functions: bool = False
    has_nested_classes: bool = False
    has_multiple_inheritance: bool = False
    has_deep_nesting: bool = False
    has_long_methods: bool = False
    has_large_classes: bool = False
    
    # Distribution metrics
    node_type_entropy: float = 0.0
    symbol_type_entropy: float = 0.0
    
    # Architectural hints
    follows_mvc_pattern: bool = False
    follows_layered_pattern: bool = False
    has_singleton_hints: bool = False
    has_factory_hints: bool = False


@dataclass
class SemanticFeatures:
    """Semantic features extracted from code"""
    # Token analysis
    unique_token_count: int = 0
    token_frequency_vector: List[float] = field(default_factory=list)
    identifier_patterns: Dict[str, int] = field(default_factory=dict)
    
    # Naming conventions
    uses_camel_case: bool = False
    uses_snake_case: bool = False
    uses_hungarian_notation: bool = False
    
    # Code patterns
    has_error_handling: bool = False
    has_logging: bool = False
    has_comments: bool = False
    has_documentation: bool = False
    
    # Import patterns
    import_count: int = 0
    external_dependencies: List[str] = field(default_factory=list)
    circular_dependencies: List[Tuple[str, str]] = field(default_factory=list)
    
    # Domain indicators
    uses_database_patterns: bool = False
    uses_web_patterns: bool = False
    uses_ml_patterns: bool = False
    uses_security_patterns: bool = False


@dataclass
class CodeFeatures:
    """Code-level features for ML models"""
    # Feature vectors
    structural_vector: np.ndarray = field(default_factory=lambda: np.zeros(100))
    semantic_vector: np.ndarray = field(default_factory=lambda: np.zeros(100))
    combined_vector: np.ndarray = field(default_factory=lambda: np.zeros(200))
    
    # Pattern scores
    pattern_confidence_scores: Dict[str, float] = field(default_factory=dict)
    
    # ML features
    feature_names: List[str] = field(default_factory=list)
    feature_values: List[float] = field(default_factory=list)
    feature_importance: Dict[str, float] = field(default_factory=dict)


@dataclass
class PatternFeatures:
    """Complete feature set for pattern detection"""
    file_path: str
    language: str
    
    # Feature categories
    structural: StructuralFeatures = field(default_factory=StructuralFeatures)
    semantic: SemanticFeatures = field(default_factory=SemanticFeatures)
    code: CodeFeatures = field(default_factory=CodeFeatures)
    
    # Raw features for custom processing
    raw_features: Dict[str, Any] = field(default_factory=dict)
    
    # Feature quality
    feature_completeness: float = 0.0
    extraction_errors: List[str] = field(default_factory=list)


class PatternFeatureExtractor:
    """
    Extracts features from processed AST data for pattern detection.
    
    Features include:
    - Structural features (AST-based)
    - Semantic features (code meaning)
    - Code features (ML-ready vectors)
    """
    
    def __init__(self):
        self.feature_cache: Dict[str, PatternFeatures] = {}
        
    def extract_features(
        self,
        processed_data: ProcessedASTData
    ) -> Dict[str, PatternFeatures]:
        """
        Extract features from all files in processed AST data.
        
        Args:
            processed_data: Processed AST data from ASTDataProcessor
            
        Returns:
            Dictionary mapping file paths to extracted features
        """
        features = {}
        
        for file_path, file_data in processed_data.files.items():
            try:
                file_features = self.extract_file_features(
                    file_data,
                    processed_data.repository_features
                )
                features[file_path] = file_features
                
            except Exception as e:
                logger.error(f"Error extracting features from {file_path}: {e}")
                features[file_path] = PatternFeatures(
                    file_path=file_path,
                    language=file_data.language,
                    extraction_errors=[str(e)]
                )
        
        return features
    
    def extract_file_features(
        self,
        file_data: ProcessedFileData,
        repository_features: Dict[str, Any]
    ) -> PatternFeatures:
        """Extract features from a single file"""
        
        # Check cache
        cache_key = f"{file_data.file_path}:{file_data.content_hash}"
        if cache_key in self.feature_cache:
            return self.feature_cache[cache_key]
        
        # Initialize features
        features = PatternFeatures(
            file_path=file_data.file_path,
            language=file_data.language
        )
        
        try:
            # Extract structural features
            features.structural = self._extract_structural_features(
                file_data, repository_features
            )
            
            # Extract semantic features
            features.semantic = self._extract_semantic_features(
                file_data, repository_features
            )
            
            # Generate code features
            features.code = self._generate_code_features(
                features.structural,
                features.semantic,
                file_data
            )
            
            # Store raw features
            features.raw_features = {
                'node_types': file_data.node_types,
                'symbol_types': file_data.symbol_types,
                'complexity_metrics': file_data.complexity_metrics,
                'structural_features': file_data.structural_features,
                'semantic_features': file_data.semantic_features,
            }
            
            # Calculate feature completeness
            features.feature_completeness = self._calculate_completeness(features)
            
            # Cache features
            self.feature_cache[cache_key] = features
            
        except Exception as e:
            logger.error(f"Feature extraction error: {e}")
            features.extraction_errors.append(str(e))
        
        return features
    
    def _extract_structural_features(
        self,
        file_data: ProcessedFileData,
        repository_features: Dict[str, Any]
    ) -> StructuralFeatures:
        """Extract structural features from file data"""
        features = StructuralFeatures()
        
        # Basic metrics
        features.node_count = len(file_data.ast_node_index)
        features.symbol_count = len(file_data.symbol_index)
        
        # Get from pre-calculated features
        if file_data.structural_features:
            features.max_depth = file_data.structural_features.get('ast_depth', 0)
            features.avg_branching_factor = file_data.structural_features.get('branching_factor', 0.0)
            features.has_nested_functions = file_data.structural_features.get('has_nested_functions', False)
            features.has_nested_classes = file_data.structural_features.get('has_nested_classes', False)
        
        # Complexity metrics
        if file_data.complexity_metrics:
            features.cyclomatic_complexity = file_data.complexity_metrics.get('cyclomatic_complexity', 0.0)
            features.nesting_complexity = file_data.complexity_metrics.get('nesting_depth', 0) / 10.0
            features.coupling_score = file_data.complexity_metrics.get('coupling_score', 0.0)
            features.cohesion_score = file_data.complexity_metrics.get('cohesion_score', 0.0)
        
        # Pattern indicators
        features.has_deep_nesting = features.max_depth > 10
        features.has_long_methods = self._has_long_methods(file_data)
        features.has_large_classes = self._has_large_classes(file_data)
        
        # Calculate entropy
        features.node_type_entropy = self._calculate_entropy(
            file_data.structural_features.get('node_type_distribution', {})
        )
        features.symbol_type_entropy = self._calculate_entropy(
            file_data.structural_features.get('symbol_type_distribution', {})
        )
        
        # Architectural pattern hints
        features.follows_mvc_pattern = self._detect_mvc_pattern(file_data, repository_features)
        features.has_singleton_hints = self._detect_singleton_hints(file_data)
        features.has_factory_hints = self._detect_factory_hints(file_data)
        
        return features
    
    def _extract_semantic_features(
        self,
        file_data: ProcessedFileData,
        repository_features: Dict[str, Any]
    ) -> SemanticFeatures:
        """Extract semantic features from file data"""
        features = SemanticFeatures()
        
        # Token analysis from semantic features
        if file_data.semantic_features:
            token_features = [f for f in file_data.semantic_features if f['name'].startswith('token_freq_')]
            features.unique_token_count = len(token_features)
            features.token_frequency_vector = [f['value'] for f in token_features]
        
        # Import analysis
        features.import_count = file_data.structural_features.get('import_count', 0)
        
        # Naming convention detection
        features.uses_camel_case = self._uses_camel_case(file_data)
        features.uses_snake_case = self._uses_snake_case(file_data)
        
        # Code pattern detection
        features.has_error_handling = self._has_error_handling(file_data)
        features.has_logging = self._has_logging(file_data)
        
        # Domain indicators
        features.uses_database_patterns = self._uses_database_patterns(file_data)
        features.uses_web_patterns = self._uses_web_patterns(file_data)
        features.uses_security_patterns = self._uses_security_patterns(file_data)
        
        return features
    
    def _generate_code_features(
        self,
        structural: StructuralFeatures,
        semantic: SemanticFeatures,
        file_data: ProcessedFileData
    ) -> CodeFeatures:
        """Generate ML-ready code features"""
        features = CodeFeatures()
        
        # Create structural feature vector
        structural_values = [
            structural.node_count / 1000.0,  # Normalize
            structural.symbol_count / 100.0,
            structural.max_depth / 20.0,
            structural.avg_branching_factor / 5.0,
            structural.cyclomatic_complexity / 50.0,
            structural.nesting_complexity,
            structural.coupling_score,
            structural.cohesion_score,
            float(structural.has_nested_functions),
            float(structural.has_nested_classes),
            float(structural.has_deep_nesting),
            float(structural.has_long_methods),
            float(structural.has_large_classes),
            structural.node_type_entropy,
            structural.symbol_type_entropy,
        ]
        
        # Create semantic feature vector
        semantic_values = [
            semantic.unique_token_count / 100.0,
            semantic.import_count / 20.0,
            float(semantic.uses_camel_case),
            float(semantic.uses_snake_case),
            float(semantic.has_error_handling),
            float(semantic.has_logging),
            float(semantic.uses_database_patterns),
            float(semantic.uses_web_patterns),
            float(semantic.uses_security_patterns),
        ]
        
        # Pad vectors to fixed size
        structural_vector = np.zeros(100)
        structural_vector[:len(structural_values)] = structural_values
        
        semantic_vector = np.zeros(100)
        semantic_vector[:len(semantic_values)] = semantic_values
        
        # Add token frequency features
        if semantic.token_frequency_vector:
            token_vector = np.array(semantic.token_frequency_vector[:50])  # Top 50 tokens
            semantic_vector[20:20+len(token_vector)] = token_vector / np.max(token_vector + 1e-8)
        
        features.structural_vector = structural_vector
        features.semantic_vector = semantic_vector
        features.combined_vector = np.concatenate([structural_vector, semantic_vector])
        
        # Create feature names
        features.feature_names = [
            'node_count', 'symbol_count', 'max_depth', 'avg_branching_factor',
            'cyclomatic_complexity', 'nesting_complexity', 'coupling_score', 'cohesion_score',
            'has_nested_functions', 'has_nested_classes', 'has_deep_nesting',
            'has_long_methods', 'has_large_classes', 'node_type_entropy', 'symbol_type_entropy',
            'unique_token_count', 'import_count', 'uses_camel_case', 'uses_snake_case',
            'has_error_handling', 'has_logging', 'uses_database_patterns',
            'uses_web_patterns', 'uses_security_patterns',
        ]
        
        features.feature_values = structural_values + semantic_values
        
        # Calculate feature importance (placeholder - would be from ML model)
        for i, name in enumerate(features.feature_names):
            features.feature_importance[name] = 1.0 / (i + 1)
        
        return features
    
    def _calculate_entropy(self, distribution: Dict[str, float]) -> float:
        """Calculate Shannon entropy of a distribution"""
        if not distribution:
            return 0.0
        
        values = list(distribution.values())
        total = sum(values)
        
        if total == 0:
            return 0.0
        
        probabilities = [v / total for v in values]
        entropy = 0.0
        
        for p in probabilities:
            if p > 0:
                entropy -= p * np.log2(p)
        
        return entropy
    
    def _has_long_methods(self, file_data: ProcessedFileData) -> bool:
        """Check if file has long methods (>50 lines)"""
        max_method_length = file_data.structural_features.get('max_method_length', 0)
        return max_method_length > 50
    
    def _has_large_classes(self, file_data: ProcessedFileData) -> bool:
        """Check if file has large classes (>300 lines or >20 methods)"""
        class_symbols = [
            s for s in file_data.symbol_index.values()
            if s.type == 'class'
        ]
        
        for cls in class_symbols:
            # Check class size
            class_size = cls.range.end_line - cls.range.start_line + 1
            if class_size > 300:
                return True
            
            # Count methods in class
            method_count = sum(
                1 for s in file_data.symbol_index.values()
                if s.type == 'method' and
                s.range.start_line >= cls.range.start_line and
                s.range.end_line <= cls.range.end_line
            )
            
            if method_count > 20:
                return True
        
        return False
    
    def _detect_mvc_pattern(
        self,
        file_data: ProcessedFileData,
        repository_features: Dict[str, Any]
    ) -> bool:
        """Detect MVC architectural pattern"""
        # Check file path for MVC indicators
        path_lower = file_data.file_path.lower()
        mvc_indicators = ['model', 'view', 'controller', 'mvc']
        
        if any(indicator in path_lower for indicator in mvc_indicators):
            return True
        
        # Check repository architecture hints
        arch_style = repository_features.get('architecture_style', '')
        if arch_style == 'mvc':
            return True
        
        return False
    
    def _detect_singleton_hints(self, file_data: ProcessedFileData) -> bool:
        """Detect singleton pattern hints"""
        # Look for singleton indicators in symbols
        for symbol_name in file_data.symbol_index:
            name_lower = symbol_name.lower()
            if 'singleton' in name_lower or 'instance' in name_lower:
                return True
        
        # Look for static instance patterns
        for node in file_data.ast_node_index.values():
            if node.type in ['static_field', 'static_property', 'class_variable']:
                if node.name and 'instance' in node.name.lower():
                    return True
        
        return False
    
    def _detect_factory_hints(self, file_data: ProcessedFileData) -> bool:
        """Detect factory pattern hints"""
        # Look for factory indicators
        factory_keywords = ['factory', 'create', 'build', 'make']
        
        for symbol_name in file_data.symbol_index:
            name_lower = symbol_name.lower()
            if any(keyword in name_lower for keyword in factory_keywords):
                # Check if it's a method that returns objects
                symbol = file_data.symbol_index[symbol_name]
                if symbol.type in ['function', 'method'] and symbol.return_type:
                    return True
        
        return False
    
    def _uses_camel_case(self, file_data: ProcessedFileData) -> bool:
        """Check if file uses camelCase naming"""
        camel_case_count = 0
        total_count = 0
        
        for symbol_name in file_data.symbol_index:
            if symbol_name and not symbol_name.startswith('_'):
                total_count += 1
                # Check for camelCase pattern
                if (symbol_name[0].islower() and 
                    any(c.isupper() for c in symbol_name[1:])):
                    camel_case_count += 1
        
        return camel_case_count > total_count * 0.3 if total_count > 0 else False
    
    def _uses_snake_case(self, file_data: ProcessedFileData) -> bool:
        """Check if file uses snake_case naming"""
        snake_case_count = 0
        total_count = 0
        
        for symbol_name in file_data.symbol_index:
            if symbol_name:
                total_count += 1
                # Check for snake_case pattern
                if '_' in symbol_name and symbol_name.islower():
                    snake_case_count += 1
        
        return snake_case_count > total_count * 0.3 if total_count > 0 else False
    
    def _has_error_handling(self, file_data: ProcessedFileData) -> bool:
        """Check if file has error handling patterns"""
        error_keywords = ['try', 'catch', 'except', 'finally', 'error', 'exception']
        
        # Check node types
        for node_type in file_data.node_types:
            if any(keyword in node_type.lower() for keyword in error_keywords):
                return True
        
        # Check symbols
        for symbol_name in file_data.symbol_index:
            if any(keyword in symbol_name.lower() for keyword in error_keywords):
                return True
        
        return False
    
    def _has_logging(self, file_data: ProcessedFileData) -> bool:
        """Check if file has logging patterns"""
        logging_keywords = ['log', 'logger', 'logging', 'debug', 'info', 'warn', 'error']
        
        # Check imports
        if hasattr(file_data, 'imports'):
            for imp in getattr(file_data, 'imports', []):
                if any(keyword in str(imp).lower() for keyword in logging_keywords):
                    return True
        
        # Check symbols
        for symbol_name in file_data.symbol_index:
            if any(keyword in symbol_name.lower() for keyword in logging_keywords[:4]):
                return True
        
        return False
    
    def _uses_database_patterns(self, file_data: ProcessedFileData) -> bool:
        """Check if file uses database patterns"""
        db_keywords = ['database', 'db', 'sql', 'query', 'table', 'column',
                      'orm', 'model', 'repository', 'dao']
        
        # Check symbols and nodes
        all_names = list(file_data.symbol_index.keys()) + list(file_data.node_types.keys())
        
        for name in all_names:
            if any(keyword in name.lower() for keyword in db_keywords):
                return True
        
        return False
    
    def _uses_web_patterns(self, file_data: ProcessedFileData) -> bool:
        """Check if file uses web patterns"""
        web_keywords = ['http', 'request', 'response', 'route', 'controller',
                       'api', 'rest', 'endpoint', 'web', 'server']
        
        # Check symbols and nodes
        all_names = list(file_data.symbol_index.keys()) + list(file_data.node_types.keys())
        
        for name in all_names:
            if any(keyword in name.lower() for keyword in web_keywords):
                return True
        
        return False
    
    def _uses_security_patterns(self, file_data: ProcessedFileData) -> bool:
        """Check if file uses security patterns"""
        security_keywords = ['auth', 'security', 'encrypt', 'decrypt', 'hash',
                           'token', 'jwt', 'oauth', 'permission', 'role']
        
        # Check symbols and nodes
        all_names = list(file_data.symbol_index.keys()) + list(file_data.node_types.keys())
        
        for name in all_names:
            if any(keyword in name.lower() for keyword in security_keywords):
                return True
        
        return False
    
    def _calculate_completeness(self, features: PatternFeatures) -> float:
        """Calculate feature completeness score"""
        total_features = 0
        completed_features = 0
        
        # Check structural features
        structural_fields = [
            'node_count', 'symbol_count', 'max_depth', 'avg_branching_factor',
            'cyclomatic_complexity', 'node_type_entropy', 'symbol_type_entropy'
        ]
        
        for field in structural_fields:
            total_features += 1
            if getattr(features.structural, field, 0) != 0:
                completed_features += 1
        
        # Check semantic features
        semantic_fields = [
            'unique_token_count', 'import_count'
        ]
        
        for field in semantic_fields:
            total_features += 1
            if getattr(features.semantic, field, 0) != 0:
                completed_features += 1
        
        # Check code features
        if features.code.combined_vector.any():
            completed_features += 1
        total_features += 1
        
        return completed_features / total_features if total_features > 0 else 0.0