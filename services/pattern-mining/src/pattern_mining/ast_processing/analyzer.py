"""
Pattern Mining Service - AST Analyzer

Analyzes AST structure for pattern context and architectural insights.
Provides structural, complexity, and dependency analysis.

Wave 2.5: CCL Contract Compliance Implementation - Phase 2
"""

import logging
from typing import Dict, List, Optional, Any, Set, Tuple
from dataclasses import dataclass, field
from collections import defaultdict
import networkx as nx

from .processor import ProcessedFileData, ProcessedASTData
from ..contracts.models import PatternASTNode, PatternSymbol

logger = logging.getLogger(__name__)


@dataclass
class StructuralAnalysis:
    """Structural analysis results"""
    # Graph metrics
    node_count: int = 0
    edge_count: int = 0
    connected_components: int = 0
    strongly_connected_components: int = 0
    
    # Hierarchy metrics
    max_depth: int = 0
    avg_depth: float = 0.0
    max_breadth: int = 0
    avg_breadth: float = 0.0
    
    # Structural patterns
    has_circular_dependencies: bool = False
    has_god_objects: bool = False
    has_feature_envy: bool = False
    has_inappropriate_intimacy: bool = False
    
    # Architecture indicators
    follows_solid_principles: Dict[str, bool] = field(default_factory=dict)
    design_pattern_matches: List[str] = field(default_factory=list)
    architectural_style: str = "unknown"


@dataclass
class ComplexityAnalysis:
    """Complexity analysis results"""
    # Complexity metrics
    cyclomatic_complexity: float = 0.0
    cognitive_complexity: float = 0.0
    halstead_complexity: float = 0.0
    maintainability_index: float = 0.0
    
    # Risk indicators
    complexity_risk_level: str = "low"  # low, medium, high, critical
    hotspot_methods: List[str] = field(default_factory=list)
    refactoring_candidates: List[str] = field(default_factory=list)
    
    # Complexity distribution
    complexity_distribution: Dict[str, int] = field(default_factory=dict)
    method_complexity_scores: Dict[str, float] = field(default_factory=dict)


@dataclass
class DependencyAnalysis:
    """Dependency analysis results"""
    # Dependency metrics
    afferent_coupling: Dict[str, int] = field(default_factory=dict)  # Incoming dependencies
    efferent_coupling: Dict[str, int] = field(default_factory=dict)  # Outgoing dependencies
    instability: Dict[str, float] = field(default_factory=dict)
    abstractness: Dict[str, float] = field(default_factory=dict)
    
    # Dependency patterns
    circular_dependencies: List[List[str]] = field(default_factory=list)
    hub_classes: List[str] = field(default_factory=list)
    isolated_classes: List[str] = field(default_factory=list)
    
    # Package/module metrics
    package_dependencies: Dict[str, Set[str]] = field(default_factory=dict)
    layering_violations: List[Tuple[str, str]] = field(default_factory=list)


@dataclass
class ASTAnalysisResult:
    """Complete AST analysis result"""
    repository_id: str
    analysis_id: str
    
    # Analysis components
    structural: StructuralAnalysis = field(default_factory=StructuralAnalysis)
    complexity: ComplexityAnalysis = field(default_factory=ComplexityAnalysis)
    dependencies: DependencyAnalysis = field(default_factory=DependencyAnalysis)
    
    # File-level analysis
    file_analyses: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    
    # Insights and recommendations
    insights: List[str] = field(default_factory=list)
    recommendations: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)


class ASTAnalyzer:
    """
    Analyzes AST structure for architectural insights and pattern context.
    
    Provides:
    - Structural analysis (graph metrics, patterns)
    - Complexity analysis (various complexity metrics)
    - Dependency analysis (coupling, cohesion, cycles)
    """
    
    def __init__(self):
        self.analysis_cache = {}
        
    def analyze_ast(self, processed_data: ProcessedASTData) -> ASTAnalysisResult:
        """
        Perform comprehensive AST analysis.
        
        Args:
            processed_data: Processed AST data from ASTDataProcessor
            
        Returns:
            ASTAnalysisResult with structural, complexity, and dependency analysis
        """
        result = ASTAnalysisResult(
            repository_id=processed_data.repository_id,
            analysis_id=processed_data.analysis_id
        )
        
        try:
            # Build dependency graph
            dep_graph = self._build_dependency_graph(processed_data)
            
            # Perform structural analysis
            result.structural = self._analyze_structure(processed_data, dep_graph)
            
            # Perform complexity analysis
            result.complexity = self._analyze_complexity(processed_data)
            
            # Perform dependency analysis
            result.dependencies = self._analyze_dependencies(processed_data, dep_graph)
            
            # Analyze individual files
            for file_path, file_data in processed_data.files.items():
                result.file_analyses[file_path] = self._analyze_file(file_data)
            
            # Generate insights and recommendations
            self._generate_insights(result, processed_data)
            
        except Exception as e:
            logger.error(f"Error in AST analysis: {e}")
            result.warnings.append(f"Analysis error: {str(e)}")
        
        return result
    
    def _build_dependency_graph(self, processed_data: ProcessedASTData) -> nx.DiGraph:
        """Build dependency graph from processed data"""
        graph = nx.DiGraph()
        
        # Add nodes for all symbols
        for file_path, file_data in processed_data.files.items():
            for symbol_name, symbol in file_data.symbol_index.items():
                node_id = f"{file_path}:{symbol_name}"
                graph.add_node(
                    node_id,
                    file_path=file_path,
                    symbol_name=symbol_name,
                    symbol_type=symbol.type,
                    complexity=symbol.complexity or 1
                )
        
        # Add edges for dependencies
        for file_path, file_data in processed_data.files.items():
            for symbol_name, references in file_data.symbol_references.items():
                source_id = f"{file_path}:{symbol_name}"
                
                for ref in references:
                    # Try to find the referenced symbol
                    target_id = self._find_symbol_id(ref, processed_data)
                    if target_id and target_id != source_id:
                        graph.add_edge(source_id, target_id)
        
        return graph
    
    def _find_symbol_id(self, symbol_name: str, processed_data: ProcessedASTData) -> Optional[str]:
        """Find symbol ID by name across all files"""
        for file_path, file_data in processed_data.files.items():
            if symbol_name in file_data.symbol_index:
                return f"{file_path}:{symbol_name}"
        return None
    
    def _analyze_structure(
        self,
        processed_data: ProcessedASTData,
        dep_graph: nx.DiGraph
    ) -> StructuralAnalysis:
        """Perform structural analysis"""
        analysis = StructuralAnalysis()
        
        # Basic graph metrics
        analysis.node_count = dep_graph.number_of_nodes()
        analysis.edge_count = dep_graph.number_of_edges()
        
        # Connected components
        undirected = dep_graph.to_undirected()
        analysis.connected_components = nx.number_connected_components(undirected)
        analysis.strongly_connected_components = nx.number_strongly_connected_components(dep_graph)
        
        # Check for circular dependencies
        try:
            cycles = list(nx.simple_cycles(dep_graph))
            analysis.has_circular_dependencies = len(cycles) > 0
            
            # Store first few cycles as examples
            if cycles:
                for cycle in cycles[:5]:
                    logger.warning(f"Circular dependency detected: {' -> '.join(cycle)}")
        except:
            # Graph might be too large for cycle detection
            pass
        
        # Detect god objects (nodes with high degree)
        degrees = dict(dep_graph.degree())
        avg_degree = sum(degrees.values()) / len(degrees) if degrees else 0
        god_threshold = avg_degree * 3  # Nodes with 3x average connections
        
        god_objects = [
            node for node, degree in degrees.items()
            if degree > god_threshold
        ]
        analysis.has_god_objects = len(god_objects) > 0
        
        # Architecture style detection
        analysis.architectural_style = self._detect_architectural_style(
            processed_data, dep_graph
        )
        
        # SOLID principles check (simplified)
        analysis.follows_solid_principles = {
            "single_responsibility": not analysis.has_god_objects,
            "open_closed": True,  # Placeholder
            "liskov_substitution": True,  # Placeholder
            "interface_segregation": True,  # Placeholder
            "dependency_inversion": not analysis.has_circular_dependencies
        }
        
        return analysis
    
    def _analyze_complexity(self, processed_data: ProcessedASTData) -> ComplexityAnalysis:
        """Perform complexity analysis"""
        analysis = ComplexityAnalysis()
        
        total_complexity = 0
        complexity_scores = {}
        
        # Analyze each file
        for file_path, file_data in processed_data.files.items():
            file_complexity = file_data.complexity_metrics.get('cyclomatic_complexity', 0)
            total_complexity += file_complexity
            
            # Analyze methods
            for symbol_name, symbol in file_data.symbol_index.items():
                if symbol.type in ['function', 'method']:
                    method_complexity = symbol.complexity or 1
                    complexity_scores[f"{file_path}:{symbol_name}"] = method_complexity
                    
                    # Track hotspots (high complexity methods)
                    if method_complexity > 10:
                        analysis.hotspot_methods.append(f"{file_path}:{symbol_name}")
                    
                    # Track refactoring candidates
                    if method_complexity > 15:
                        analysis.refactoring_candidates.append(f"{file_path}:{symbol_name}")
        
        # Calculate overall metrics
        file_count = len(processed_data.files)
        analysis.cyclomatic_complexity = total_complexity / file_count if file_count > 0 else 0
        
        # Determine risk level
        if analysis.cyclomatic_complexity > 20:
            analysis.complexity_risk_level = "critical"
        elif analysis.cyclomatic_complexity > 10:
            analysis.complexity_risk_level = "high"
        elif analysis.cyclomatic_complexity > 5:
            analysis.complexity_risk_level = "medium"
        else:
            analysis.complexity_risk_level = "low"
        
        # Calculate complexity distribution
        for score in complexity_scores.values():
            if score <= 5:
                bucket = "low"
            elif score <= 10:
                bucket = "medium"
            elif score <= 20:
                bucket = "high"
            else:
                bucket = "critical"
            
            analysis.complexity_distribution[bucket] = \
                analysis.complexity_distribution.get(bucket, 0) + 1
        
        analysis.method_complexity_scores = complexity_scores
        
        # Calculate maintainability index (simplified)
        # MI = 171 - 5.2 * ln(V) - 0.23 * CC - 16.2 * ln(LOC)
        # Using simplified version
        avg_loc = sum(
            f.complexity_metrics.get('lines_of_code', 0)
            for f in processed_data.files.values()
        ) / file_count if file_count > 0 else 1
        
        import math
        analysis.maintainability_index = max(
            0,
            171 - 5.2 * math.log(max(1, avg_loc)) - 0.23 * analysis.cyclomatic_complexity
        )
        
        return analysis
    
    def _analyze_dependencies(
        self,
        processed_data: ProcessedASTData,
        dep_graph: nx.DiGraph
    ) -> DependencyAnalysis:
        """Perform dependency analysis"""
        analysis = DependencyAnalysis()
        
        # Calculate coupling for each node
        for node in dep_graph.nodes():
            # Afferent coupling (incoming dependencies)
            analysis.afferent_coupling[node] = dep_graph.in_degree(node)
            
            # Efferent coupling (outgoing dependencies)
            analysis.efferent_coupling[node] = dep_graph.out_degree(node)
            
            # Instability: Ce / (Ca + Ce)
            ca = analysis.afferent_coupling[node]
            ce = analysis.efferent_coupling[node]
            if ca + ce > 0:
                analysis.instability[node] = ce / (ca + ce)
            else:
                analysis.instability[node] = 0.0
        
        # Find hub classes (high coupling)
        avg_coupling = sum(
            analysis.afferent_coupling.values()
        ) / len(analysis.afferent_coupling) if analysis.afferent_coupling else 0
        
        analysis.hub_classes = [
            node for node, coupling in analysis.afferent_coupling.items()
            if coupling > avg_coupling * 2
        ]
        
        # Find isolated classes (no dependencies)
        analysis.isolated_classes = [
            node for node in dep_graph.nodes()
            if dep_graph.degree(node) == 0
        ]
        
        # Detect circular dependencies
        try:
            cycles = list(nx.simple_cycles(dep_graph))
            analysis.circular_dependencies = [
                cycle for cycle in cycles
                if len(cycle) > 1  # Ignore self-cycles
            ][:10]  # Limit to first 10
        except:
            pass
        
        # Analyze package dependencies
        package_graph = nx.DiGraph()
        
        for edge in dep_graph.edges():
            source_file = dep_graph.nodes[edge[0]]['file_path']
            target_file = dep_graph.nodes[edge[1]]['file_path']
            
            if source_file != target_file:
                source_pkg = self._get_package(source_file)
                target_pkg = self._get_package(target_file)
                
                if source_pkg != target_pkg:
                    package_graph.add_edge(source_pkg, target_pkg)
                    
                    if source_pkg not in analysis.package_dependencies:
                        analysis.package_dependencies[source_pkg] = set()
                    analysis.package_dependencies[source_pkg].add(target_pkg)
        
        # Detect layering violations (simplified)
        layers = ['ui', 'controller', 'service', 'repository', 'model']
        for source_pkg, targets in analysis.package_dependencies.items():
            source_layer = self._get_layer(source_pkg)
            
            for target_pkg in targets:
                target_layer = self._get_layer(target_pkg)
                
                # Check if dependency goes upward in layers
                if (source_layer in layers and target_layer in layers and
                    layers.index(source_layer) > layers.index(target_layer)):
                    analysis.layering_violations.append((source_pkg, target_pkg))
        
        return analysis
    
    def _analyze_file(self, file_data: ProcessedFileData) -> Dict[str, Any]:
        """Analyze individual file"""
        return {
            'node_count': len(file_data.ast_node_index),
            'symbol_count': len(file_data.symbol_index),
            'complexity': file_data.complexity_metrics.get('cyclomatic_complexity', 0),
            'coupling': file_data.complexity_metrics.get('coupling_score', 0),
            'cohesion': file_data.complexity_metrics.get('cohesion_score', 0),
            'loc': file_data.complexity_metrics.get('lines_of_code', 0),
            'has_tests': 'test' in file_data.file_path.lower(),
            'language': file_data.language
        }
    
    def _generate_insights(
        self,
        result: ASTAnalysisResult,
        processed_data: ProcessedASTData
    ):
        """Generate insights and recommendations"""
        
        # Structural insights
        if result.structural.has_circular_dependencies:
            result.insights.append("Circular dependencies detected, indicating tight coupling")
            result.recommendations.append("Break circular dependencies by introducing interfaces")
        
        if result.structural.has_god_objects:
            result.insights.append("God objects detected with too many responsibilities")
            result.recommendations.append("Apply Single Responsibility Principle to split large classes")
        
        # Complexity insights
        if result.complexity.complexity_risk_level in ['high', 'critical']:
            result.insights.append(f"High complexity detected ({result.complexity.complexity_risk_level})")
            result.recommendations.append("Refactor complex methods to improve maintainability")
        
        if result.complexity.hotspot_methods:
            result.insights.append(f"Found {len(result.complexity.hotspot_methods)} complexity hotspots")
            result.recommendations.append("Focus refactoring efforts on high-complexity methods")
        
        # Dependency insights
        if result.dependencies.hub_classes:
            result.insights.append(f"Found {len(result.dependencies.hub_classes)} hub classes with high coupling")
            result.recommendations.append("Consider using dependency injection to reduce coupling")
        
        if result.dependencies.isolated_classes:
            result.insights.append(f"Found {len(result.dependencies.isolated_classes)} isolated classes")
            result.recommendations.append("Review isolated classes for potential dead code")
        
        if result.dependencies.layering_violations:
            result.insights.append("Layering violations detected in architecture")
            result.recommendations.append("Restructure dependencies to follow layered architecture")
        
        # Architecture insights
        if result.structural.architectural_style != "unknown":
            result.insights.append(f"Architecture follows {result.structural.architectural_style} pattern")
        
        # Overall health
        solid_compliance = sum(result.structural.follows_solid_principles.values())
        if solid_compliance < 3:
            result.insights.append("Low SOLID principles compliance detected")
            result.recommendations.append("Review and apply SOLID principles for better design")
    
    def _detect_architectural_style(
        self,
        processed_data: ProcessedASTData,
        dep_graph: nx.DiGraph
    ) -> str:
        """Detect architectural style from structure"""
        
        # Check for MVC pattern
        mvc_indicators = 0
        for file_path in processed_data.files:
            path_lower = file_path.lower()
            if 'model' in path_lower:
                mvc_indicators += 1
            if 'view' in path_lower:
                mvc_indicators += 1
            if 'controller' in path_lower:
                mvc_indicators += 1
        
        if mvc_indicators >= 2:
            return "mvc"
        
        # Check for layered architecture
        layer_indicators = 0
        for file_path in processed_data.files:
            path_lower = file_path.lower()
            if any(layer in path_lower for layer in ['service', 'repository', 'controller']):
                layer_indicators += 1
        
        if layer_indicators >= 2:
            return "layered"
        
        # Check for microservices hints
        if 'service' in str(processed_data.repository_features.get('framework_hints', [])):
            return "microservices"
        
        return "unknown"
    
    def _get_package(self, file_path: str) -> str:
        """Extract package name from file path"""
        parts = file_path.split('/')
        # Return first meaningful directory
        for i, part in enumerate(parts):
            if part in ['src', 'lib', 'app']:
                if i + 1 < len(parts):
                    return parts[i + 1]
        
        # Default to first directory
        return parts[0] if parts else "root"
    
    def _get_layer(self, package_name: str) -> str:
        """Determine architectural layer from package name"""
        name_lower = package_name.lower()
        
        if 'ui' in name_lower or 'view' in name_lower:
            return 'ui'
        elif 'controller' in name_lower:
            return 'controller'
        elif 'service' in name_lower:
            return 'service'
        elif 'repository' in name_lower or 'dao' in name_lower:
            return 'repository'
        elif 'model' in name_lower or 'entity' in name_lower:
            return 'model'
        
        return 'unknown'