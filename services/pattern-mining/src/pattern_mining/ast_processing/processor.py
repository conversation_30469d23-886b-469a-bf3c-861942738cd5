"""
Pattern Mining Service - AST Data Processor

Main processor for handling FileASTData from Repository Analysis service.
Converts structured AST data into pattern detection features.

Wave 2.5: CCL Contract Compliance Implementation - Phase 2
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Set, Tuple
from datetime import datetime
from dataclasses import dataclass, field
import hashlib

from ..contracts.models import (
    ASTDataV1,
    FileASTData,
    PatternASTNode,
    PatternSymbol,
)

logger = logging.getLogger(__name__)


@dataclass
class ProcessingStats:
    """Statistics for AST processing"""
    total_files: int = 0
    processed_files: int = 0
    failed_files: int = 0
    total_nodes: int = 0
    total_symbols: int = 0
    processing_time_ms: int = 0
    memory_usage_mb: float = 0.0
    cache_hits: int = 0
    cache_misses: int = 0


@dataclass
class ProcessingConfig:
    """Configuration for AST processing"""
    max_file_size_mb: float = 5.0
    max_ast_nodes: int = 10000
    enable_caching: bool = True
    parallel_processing: bool = True
    feature_extraction_timeout: int = 30
    pattern_detection_timeout: int = 60
    min_confidence_threshold: float = 0.1
    max_patterns_per_file: int = 100
    batch_size: int = 10


@dataclass
class ProcessedFileData:
    """Processed data for a single file"""
    file_path: str
    language: str
    content_hash: str
    
    # Extracted features
    node_types: Dict[str, int] = field(default_factory=dict)
    symbol_types: Dict[str, int] = field(default_factory=dict)
    complexity_metrics: Dict[str, float] = field(default_factory=dict)
    structural_features: Dict[str, Any] = field(default_factory=dict)
    semantic_features: List[Dict[str, Any]] = field(default_factory=list)
    
    # Pattern detection context
    ast_node_index: Dict[str, PatternASTNode] = field(default_factory=dict)
    symbol_index: Dict[str, PatternSymbol] = field(default_factory=dict)
    parent_child_map: Dict[str, List[str]] = field(default_factory=dict)
    symbol_references: Dict[str, List[str]] = field(default_factory=dict)
    
    # Metrics
    processing_time_ms: int = 0
    memory_usage_mb: float = 0.0
    errors: List[str] = field(default_factory=list)


@dataclass
class ProcessedASTData:
    """Processed AST data ready for pattern detection"""
    repository_id: str
    analysis_id: str
    
    # Processed files
    files: Dict[str, ProcessedFileData] = field(default_factory=dict)
    
    # Repository-level features
    repository_features: Dict[str, Any] = field(default_factory=dict)
    language_distribution: Dict[str, float] = field(default_factory=dict)
    
    # Processing statistics
    stats: ProcessingStats = field(default_factory=ProcessingStats)
    
    # Errors and warnings
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)


class ASTDataProcessor:
    """
    Processes structured AST data from Repository Analysis service.
    
    Responsibilities:
    - Convert FileASTData to pattern detection features
    - Build indexes for efficient pattern matching
    - Extract structural and semantic features
    - Handle large AST data efficiently
    """
    
    def __init__(self, config: Optional[ProcessingConfig] = None):
        self.config = config or ProcessingConfig()
        self.cache: Dict[str, ProcessedFileData] = {}
        self._processing_lock = asyncio.Lock()
        
    async def process_ast_data(
        self,
        ast_data: ASTDataV1,
        repository_id: str,
        analysis_id: str
    ) -> ProcessedASTData:
        """
        Process AST data for pattern detection.
        
        Args:
            ast_data: Structured AST data from Repository Analysis
            repository_id: Repository identifier
            analysis_id: Analysis identifier
            
        Returns:
            ProcessedASTData ready for pattern detection
        """
        start_time = datetime.utcnow()
        
        # Initialize result
        result = ProcessedASTData(
            repository_id=repository_id,
            analysis_id=analysis_id
        )
        
        try:
            # Extract repository features
            result.repository_features = self._extract_repository_features(
                ast_data.repository_metrics
            )
            
            # Calculate language distribution
            result.language_distribution = self._calculate_language_distribution(
                ast_data.repository_metrics
            )
            
            # Process files in batches
            if self.config.parallel_processing:
                await self._process_files_parallel(ast_data.files, result)
            else:
                await self._process_files_sequential(ast_data.files, result)
            
            # Calculate final statistics
            end_time = datetime.utcnow()
            result.stats.processing_time_ms = int(
                (end_time - start_time).total_seconds() * 1000
            )
            
            logger.info(
                f"Processed AST data for repository {repository_id}: "
                f"{result.stats.processed_files}/{result.stats.total_files} files "
                f"in {result.stats.processing_time_ms}ms"
            )
            
        except Exception as e:
            logger.error(f"Error processing AST data: {e}")
            result.errors.append(f"AST processing error: {str(e)}")
            
        return result
    
    async def _process_files_parallel(
        self,
        files: List[FileASTData],
        result: ProcessedASTData
    ):
        """Process files in parallel batches"""
        result.stats.total_files = len(files)
        
        # Process in batches to avoid overwhelming resources
        for i in range(0, len(files), self.config.batch_size):
            batch = files[i:i + self.config.batch_size]
            
            tasks = [
                self._process_single_file(file_data, result)
                for file_data in batch
            ]
            
            await asyncio.gather(*tasks, return_exceptions=True)
    
    async def _process_files_sequential(
        self,
        files: List[FileASTData],
        result: ProcessedASTData
    ):
        """Process files sequentially"""
        result.stats.total_files = len(files)
        
        for file_data in files:
            await self._process_single_file(file_data, result)
    
    async def _process_single_file(
        self,
        file_data: FileASTData,
        result: ProcessedASTData
    ) -> Optional[ProcessedFileData]:
        """Process a single file's AST data"""
        try:
            # Check cache
            cache_key = f"{file_data.file_path}:{file_data.content_hash}"
            if self.config.enable_caching and cache_key in self.cache:
                result.stats.cache_hits += 1
                processed = self.cache[cache_key]
            else:
                result.stats.cache_misses += 1
                
                # Process file
                processed = await self._extract_file_features(file_data)
                
                # Update cache
                if self.config.enable_caching:
                    self.cache[cache_key] = processed
            
            # Add to results
            result.files[file_data.file_path] = processed
            result.stats.processed_files += 1
            result.stats.total_nodes += len(file_data.ast_nodes)
            result.stats.total_symbols += len(file_data.symbols or [])
            
            return processed
            
        except Exception as e:
            logger.error(f"Error processing file {file_data.file_path}: {e}")
            result.stats.failed_files += 1
            result.errors.append(f"File processing error ({file_data.file_path}): {str(e)}")
            return None
    
    async def _extract_file_features(self, file_data: FileASTData) -> ProcessedFileData:
        """Extract features from a single file"""
        processed = ProcessedFileData(
            file_path=file_data.file_path,
            language=file_data.language,
            content_hash=file_data.content_hash
        )
        
        # Check file size limit
        if file_data.size_bytes and file_data.size_bytes > self.config.max_file_size_mb * 1024 * 1024:
            processed.warnings.append(f"File exceeds size limit: {file_data.size_bytes} bytes")
            return processed
        
        # Check AST node limit
        if len(file_data.ast_nodes) > self.config.max_ast_nodes:
            processed.warnings.append(f"AST node count exceeds limit: {len(file_data.ast_nodes)}")
            # Process only up to the limit
            file_data.ast_nodes = file_data.ast_nodes[:self.config.max_ast_nodes]
        
        # Build AST node index
        for node in file_data.ast_nodes:
            processed.ast_node_index[node.id] = node
            
            # Count node types
            processed.node_types[node.type] = processed.node_types.get(node.type, 0) + 1
            
            # Build parent-child map
            if node.parent_id:
                if node.parent_id not in processed.parent_child_map:
                    processed.parent_child_map[node.parent_id] = []
                processed.parent_child_map[node.parent_id].append(node.id)
        
        # Build symbol index
        if file_data.symbols:
            for symbol in file_data.symbols:
                processed.symbol_index[symbol.name] = symbol
                
                # Count symbol types
                processed.symbol_types[symbol.type] = processed.symbol_types.get(symbol.type, 0) + 1
                
                # Extract symbol references
                if symbol.references:
                    for ref in symbol.references:
                        ref_name = ref.get('symbol_name')
                        if ref_name:
                            if symbol.name not in processed.symbol_references:
                                processed.symbol_references[symbol.name] = []
                            processed.symbol_references[symbol.name].append(ref_name)
        
        # Extract complexity metrics from file metrics
        if file_data.metrics:
            processed.complexity_metrics = {
                'cyclomatic_complexity': file_data.metrics.get('complexity', 0),
                'lines_of_code': file_data.metrics.get('lines_of_code', 0),
                'function_count': file_data.metrics.get('function_count', 0),
                'class_count': file_data.metrics.get('class_count', 0),
                'nesting_depth': file_data.metrics.get('nesting_depth', 0),
                'coupling_score': file_data.metrics.get('coupling_score', 0),
                'cohesion_score': file_data.metrics.get('cohesion_score', 0),
            }
        
        # Extract structural features
        processed.structural_features = self._extract_structural_features(
            file_data, processed
        )
        
        # Extract semantic features from code_features
        if file_data.code_features:
            processed.semantic_features = self._extract_semantic_features(
                file_data.code_features
            )
        
        return processed
    
    def _extract_repository_features(
        self,
        repository_metrics: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Extract repository-level features for pattern context"""
        features = {
            'total_files': repository_metrics.get('total_files', 0),
            'total_lines': repository_metrics.get('total_lines', 0),
            'total_complexity': repository_metrics.get('total_complexity', 0),
            'architecture_style': 'unknown',
            'primary_language': None,
            'framework_hints': [],
            'design_pattern_hints': [],
        }
        
        # Extract architecture hints
        if 'architecture_hints' in repository_metrics:
            hints = repository_metrics['architecture_hints']
            features['architecture_style'] = hints.get('architecture_style', 'unknown')
            features['framework_hints'] = hints.get('framework_detected', [])
            features['design_pattern_hints'] = hints.get('design_patterns_hint', [])
        
        # Determine primary language
        if 'languages' in repository_metrics:
            languages = repository_metrics['languages']
            if languages:
                # Find language with most files
                primary_lang = max(
                    languages.items(),
                    key=lambda x: x[1].get('files', 0) if isinstance(x[1], dict) else 0
                )
                features['primary_language'] = primary_lang[0]
        
        return features
    
    def _calculate_language_distribution(
        self,
        repository_metrics: Dict[str, Any]
    ) -> Dict[str, float]:
        """Calculate language distribution percentages"""
        distribution = {}
        
        if 'languages' in repository_metrics:
            languages = repository_metrics['languages']
            total_lines = sum(
                lang_data.get('lines', 0) if isinstance(lang_data, dict) else 0
                for lang_data in languages.values()
            )
            
            if total_lines > 0:
                for lang, lang_data in languages.items():
                    if isinstance(lang_data, dict):
                        lines = lang_data.get('lines', 0)
                        distribution[lang] = (lines / total_lines) * 100
        
        return distribution
    
    def _extract_structural_features(
        self,
        file_data: FileASTData,
        processed: ProcessedFileData
    ) -> Dict[str, Any]:
        """Extract structural features from AST"""
        features = {
            'ast_depth': self._calculate_ast_depth(processed),
            'branching_factor': self._calculate_branching_factor(processed),
            'node_type_distribution': self._calculate_node_type_distribution(processed),
            'symbol_type_distribution': self._calculate_symbol_type_distribution(processed),
            'import_count': len(file_data.imports or []),
            'has_nested_functions': self._has_nested_functions(processed),
            'has_nested_classes': self._has_nested_classes(processed),
            'max_method_length': self._calculate_max_method_length(processed),
            'avg_method_length': self._calculate_avg_method_length(processed),
        }
        
        # Add language-specific features
        if file_data.code_features and 'structural_features' in file_data.code_features:
            features.update(file_data.code_features['structural_features'])
        
        return features
    
    def _extract_semantic_features(
        self,
        code_features: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Extract semantic features from code features"""
        semantic_features = []
        
        if 'semantic_features' in code_features:
            for feature in code_features['semantic_features']:
                semantic_features.append({
                    'name': feature.get('feature_name'),
                    'value': feature.get('feature_value'),
                    'confidence': feature.get('confidence', 1.0),
                })
        
        # Extract token frequency features
        if 'token_frequency' in code_features:
            token_freq = code_features['token_frequency']
            # Get top N most frequent tokens
            top_tokens = sorted(
                token_freq.items(),
                key=lambda x: x[1],
                reverse=True
            )[:20]
            
            for token, freq in top_tokens:
                semantic_features.append({
                    'name': f'token_freq_{token}',
                    'value': freq,
                    'confidence': 1.0,
                })
        
        return semantic_features
    
    def _calculate_ast_depth(self, processed: ProcessedFileData) -> int:
        """Calculate maximum depth of AST"""
        if not processed.ast_node_index:
            return 0
        
        max_depth = 0
        
        def calculate_depth(node_id: str, current_depth: int = 0):
            nonlocal max_depth
            max_depth = max(max_depth, current_depth)
            
            if node_id in processed.parent_child_map:
                for child_id in processed.parent_child_map[node_id]:
                    calculate_depth(child_id, current_depth + 1)
        
        # Find root nodes (nodes without parents)
        root_nodes = [
            node_id for node_id, node in processed.ast_node_index.items()
            if not node.parent_id
        ]
        
        for root_id in root_nodes:
            calculate_depth(root_id, 1)
        
        return max_depth
    
    def _calculate_branching_factor(self, processed: ProcessedFileData) -> float:
        """Calculate average branching factor of AST"""
        if not processed.parent_child_map:
            return 0.0
        
        total_children = sum(len(children) for children in processed.parent_child_map.values())
        parent_count = len(processed.parent_child_map)
        
        return total_children / parent_count if parent_count > 0 else 0.0
    
    def _calculate_node_type_distribution(self, processed: ProcessedFileData) -> Dict[str, float]:
        """Calculate distribution of node types"""
        total_nodes = sum(processed.node_types.values())
        if total_nodes == 0:
            return {}
        
        return {
            node_type: (count / total_nodes) * 100
            for node_type, count in processed.node_types.items()
        }
    
    def _calculate_symbol_type_distribution(self, processed: ProcessedFileData) -> Dict[str, float]:
        """Calculate distribution of symbol types"""
        total_symbols = sum(processed.symbol_types.values())
        if total_symbols == 0:
            return {}
        
        return {
            symbol_type: (count / total_symbols) * 100
            for symbol_type, count in processed.symbol_types.items()
        }
    
    def _has_nested_functions(self, processed: ProcessedFileData) -> bool:
        """Check if file has nested functions"""
        function_symbols = [
            s for s in processed.symbol_index.values()
            if s.type in ['function', 'method']
        ]
        
        for func in function_symbols:
            # Check if any function is defined within the range of this function
            for other_func in function_symbols:
                if func == other_func:
                    continue
                    
                if (other_func.range.start_line > func.range.start_line and
                    other_func.range.end_line < func.range.end_line):
                    return True
        
        return False
    
    def _has_nested_classes(self, processed: ProcessedFileData) -> bool:
        """Check if file has nested classes"""
        class_symbols = [
            s for s in processed.symbol_index.values()
            if s.type == 'class'
        ]
        
        for cls in class_symbols:
            # Check if any class is defined within the range of this class
            for other_cls in class_symbols:
                if cls == other_cls:
                    continue
                    
                if (other_cls.range.start_line > cls.range.start_line and
                    other_cls.range.end_line < cls.range.end_line):
                    return True
        
        return False
    
    def _calculate_max_method_length(self, processed: ProcessedFileData) -> int:
        """Calculate maximum method length in lines"""
        method_symbols = [
            s for s in processed.symbol_index.values()
            if s.type in ['function', 'method']
        ]
        
        if not method_symbols:
            return 0
        
        return max(
            s.range.end_line - s.range.start_line + 1
            for s in method_symbols
        )
    
    def _calculate_avg_method_length(self, processed: ProcessedFileData) -> float:
        """Calculate average method length in lines"""
        method_symbols = [
            s for s in processed.symbol_index.values()
            if s.type in ['function', 'method']
        ]
        
        if not method_symbols:
            return 0.0
        
        total_length = sum(
            s.range.end_line - s.range.start_line + 1
            for s in method_symbols
        )
        
        return total_length / len(method_symbols)