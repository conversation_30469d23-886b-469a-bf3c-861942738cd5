"""
Pattern Mining Service - Contract Schema Validators

Implements JSON Schema validation for CCL contract compliance.
Validates all input/output data against contract schemas.

Wave 2.5: CCL Contract Compliance Implementation
"""

import json
import logging
from typing import Dict, Any, Optional, List, Union
from pathlib import Path

import jsonschema
from jsonschema import ValidationError, Draft7Validator
from pydantic import ValidationError as PydanticValidationError

from .models import (
    PatternInputV1,
    PatternOutputV1,
    ErrorResponseV1,
    DetectedPatternV1,
    PatternSummaryV1,
    PatternMetadataV1,
)

logger = logging.getLogger(__name__)


class ContractValidationError(Exception):
    """Custom exception for contract validation errors"""
    
    def __init__(self, message: str, validation_errors: List[str] = None, schema_path: str = None):
        super().__init__(message)
        self.validation_errors = validation_errors or []
        self.schema_path = schema_path
        self.message = message
    
    def __str__(self):
        error_summary = f"Contract validation failed: {self.message}"
        if self.schema_path:
            error_summary += f" (Schema: {self.schema_path})"
        
        if self.validation_errors:
            error_summary += f"\nValidation errors:\n"
            for i, error in enumerate(self.validation_errors, 1):
                error_summary += f"  {i}. {error}\n"
        
        return error_summary


class ContractSchemaValidator:
    """JSON Schema validator for CCL contract compliance"""
    
    def __init__(self, schema_directory: Optional[str] = None):
        """Initialize validator with schema directory"""
        self.schema_directory = schema_directory or self._get_default_schema_directory()
        self.schemas = {}
        self.validators = {}
        self._load_schemas()
    
    def _get_default_schema_directory(self) -> str:
        """Get default schema directory path"""
        # Look for schemas in the project contracts directory
        base_dir = Path(__file__).parent.parent.parent.parent.parent
        schema_dir = base_dir / "contracts" / "schemas"
        
        if schema_dir.exists():
            return str(schema_dir)
        
        # Fallback to local schemas directory
        local_schema_dir = Path(__file__).parent / "schemas"
        if not local_schema_dir.exists():
            local_schema_dir.mkdir(parents=True, exist_ok=True)
        
        return str(local_schema_dir)
    
    def _load_schemas(self):
        """Load all contract schemas from directory"""
        schema_dir = Path(self.schema_directory)
        
        # Contract schema files
        schema_files = {
            "pattern-input": "pattern-input-v1.json",
            "pattern-output": "pattern-output-v1.json",
            "error-response": "error-response-v1.json",
        }
        
        for schema_name, filename in schema_files.items():
            schema_path = schema_dir / filename
            
            if schema_path.exists():
                try:
                    with open(schema_path, 'r') as f:
                        schema = json.load(f)
                    
                    self.schemas[schema_name] = schema
                    self.validators[schema_name] = Draft7Validator(schema)
                    logger.info(f"Loaded schema: {schema_name} from {schema_path}")
                    
                except (json.JSONDecodeError, FileNotFoundError) as e:
                    logger.warning(f"Failed to load schema {schema_name}: {e}")
                    # Create minimal fallback schema
                    self.schemas[schema_name] = {"type": "object"}
                    self.validators[schema_name] = Draft7Validator({"type": "object"})
            else:
                logger.warning(f"Schema file not found: {schema_path}")
                # Create minimal fallback schema
                self.schemas[schema_name] = {"type": "object"}
                self.validators[schema_name] = Draft7Validator({"type": "object"})
    
    def validate_against_schema(self, data: Dict[str, Any], schema_name: str) -> List[str]:
        """Validate data against specific schema"""
        if schema_name not in self.validators:
            raise ContractValidationError(f"Schema '{schema_name}' not found")
        
        validator = self.validators[schema_name]
        errors = []
        
        try:
            # Validate against JSON Schema
            for error in validator.iter_errors(data):
                error_msg = f"Path: {'.'.join(str(p) for p in error.path)} - {error.message}"
                errors.append(error_msg)
        
        except Exception as e:
            errors.append(f"Schema validation error: {str(e)}")
        
        return errors
    
    def validate_pattern_input(self, data: Dict[str, Any]) -> List[str]:
        """Validate pattern input data"""
        return self.validate_against_schema(data, "pattern-input")
    
    def validate_pattern_output(self, data: Dict[str, Any]) -> List[str]:
        """Validate pattern output data"""
        return self.validate_against_schema(data, "pattern-output")
    
    def validate_error_response(self, data: Dict[str, Any]) -> List[str]:
        """Validate error response data"""
        return self.validate_against_schema(data, "error-response")


class PatternInputValidator:
    """Validator for PatternInputV1 model"""
    
    def __init__(self, schema_validator: Optional[ContractSchemaValidator] = None):
        self.schema_validator = schema_validator or ContractSchemaValidator()
    
    def validate(self, data: Union[Dict[str, Any], PatternInputV1]) -> PatternInputV1:
        """Validate and return PatternInputV1 instance"""
        
        # Convert to dict if needed
        if isinstance(data, PatternInputV1):
            data_dict = data.dict()
        else:
            data_dict = data
        
        # JSON Schema validation
        schema_errors = self.schema_validator.validate_pattern_input(data_dict)
        if schema_errors:
            raise ContractValidationError(
                "Pattern input schema validation failed",
                schema_errors,
                "pattern-input-v1.json"
            )
        
        # Pydantic model validation
        try:
            if isinstance(data, PatternInputV1):
                return data
            return PatternInputV1(**data_dict)
        
        except PydanticValidationError as e:
            pydantic_errors = []
            for error in e.errors():
                field_path = ".".join(str(p) for p in error['loc'])
                pydantic_errors.append(f"Field: {field_path} - {error['msg']}")
            
            raise ContractValidationError(
                "Pattern input model validation failed",
                pydantic_errors,
                "PatternInputV1"
            )
    
    def validate_repository_id(self, repository_id: str) -> bool:
        """Validate repository ID format"""
        import re
        pattern = r"^repo_[a-zA-Z0-9]{16}$"
        return bool(re.match(pattern, repository_id))
    
    def validate_analysis_id(self, analysis_id: str) -> bool:
        """Validate analysis ID format"""
        import re
        pattern = r"^analysis_[a-zA-Z0-9]{16}$"
        return bool(re.match(pattern, analysis_id))
    
    def validate_request_id(self, request_id: str) -> bool:
        """Validate request ID format"""
        import re
        pattern = r"^req_[a-zA-Z0-9]{16}$"
        return bool(re.match(pattern, request_id))
    
    def validate_ast_data_completeness(self, ast_data: Dict[str, Any]) -> List[str]:
        """Validate AST data completeness"""
        errors = []
        
        # Check required fields
        if 'files' not in ast_data:
            errors.append("AST data missing 'files' field")
        elif not isinstance(ast_data['files'], list):
            errors.append("AST data 'files' must be a list")
        elif len(ast_data['files']) == 0:
            errors.append("AST data 'files' cannot be empty")
        
        if 'repository_metrics' not in ast_data:
            errors.append("AST data missing 'repository_metrics' field")
        else:
            repo_metrics = ast_data['repository_metrics']
            required_metrics = ['total_files', 'total_lines', 'languages']
            for metric in required_metrics:
                if metric not in repo_metrics:
                    errors.append(f"Repository metrics missing required field: {metric}")
        
        return errors
    
    def validate_detection_config(self, detection_config: Dict[str, Any]) -> List[str]:
        """Validate detection configuration"""
        errors = []
        
        # Check required fields
        if 'enabled_detectors' not in detection_config:
            errors.append("Detection config missing 'enabled_detectors' field")
        elif not isinstance(detection_config['enabled_detectors'], list):
            errors.append("Detection config 'enabled_detectors' must be a list")
        elif len(detection_config['enabled_detectors']) == 0:
            errors.append("Detection config 'enabled_detectors' cannot be empty")
        
        if 'confidence_threshold' not in detection_config:
            errors.append("Detection config missing 'confidence_threshold' field")
        elif not isinstance(detection_config['confidence_threshold'], (int, float)):
            errors.append("Detection config 'confidence_threshold' must be a number")
        elif not (0 <= detection_config['confidence_threshold'] <= 1):
            errors.append("Detection config 'confidence_threshold' must be between 0 and 1")
        
        return errors


class PatternOutputValidator:
    """Validator for PatternOutputV1 model"""
    
    def __init__(self, schema_validator: Optional[ContractSchemaValidator] = None):
        self.schema_validator = schema_validator or ContractSchemaValidator()
    
    def validate(self, data: Union[Dict[str, Any], PatternOutputV1]) -> PatternOutputV1:
        """Validate and return PatternOutputV1 instance"""
        
        # Convert to dict if needed
        if isinstance(data, PatternOutputV1):
            data_dict = data.dict()
        else:
            data_dict = data
        
        # JSON Schema validation
        schema_errors = self.schema_validator.validate_pattern_output(data_dict)
        if schema_errors:
            raise ContractValidationError(
                "Pattern output schema validation failed",
                schema_errors,
                "pattern-output-v1.json"
            )
        
        # Pydantic model validation
        try:
            if isinstance(data, PatternOutputV1):
                return data
            return PatternOutputV1(**data_dict)
        
        except PydanticValidationError as e:
            pydantic_errors = []
            for error in e.errors():
                field_path = ".".join(str(p) for p in error['loc'])
                pydantic_errors.append(f"Field: {field_path} - {error['msg']}")
            
            raise ContractValidationError(
                "Pattern output model validation failed",
                pydantic_errors,
                "PatternOutputV1"
            )
    
    def validate_patterns_completeness(self, patterns: List[Dict[str, Any]]) -> List[str]:
        """Validate patterns completeness"""
        errors = []
        
        for i, pattern in enumerate(patterns):
            pattern_prefix = f"Pattern {i+1}"
            
            # Check required fields
            required_fields = ['id', 'pattern_type', 'pattern_name', 'confidence', 'severity', 'locations']
            for field in required_fields:
                if field not in pattern:
                    errors.append(f"{pattern_prefix} missing required field: {field}")
            
            # Validate pattern ID format
            if 'id' in pattern:
                import re
                if not re.match(r"^pattern_[a-zA-Z0-9]{16}$", pattern['id']):
                    errors.append(f"{pattern_prefix} has invalid ID format")
            
            # Validate confidence score
            if 'confidence' in pattern:
                if not isinstance(pattern['confidence'], (int, float)):
                    errors.append(f"{pattern_prefix} confidence must be a number")
                elif not (0 <= pattern['confidence'] <= 1):
                    errors.append(f"{pattern_prefix} confidence must be between 0 and 1")
            
            # Validate locations
            if 'locations' in pattern:
                if not isinstance(pattern['locations'], list):
                    errors.append(f"{pattern_prefix} locations must be a list")
                elif len(pattern['locations']) == 0:
                    errors.append(f"{pattern_prefix} must have at least one location")
        
        return errors
    
    def validate_summary_completeness(self, summary: Dict[str, Any]) -> List[str]:
        """Validate summary completeness"""
        errors = []
        
        # Check required fields
        required_fields = ['total_patterns', 'by_type', 'by_severity', 'quality_scores']
        for field in required_fields:
            if field not in summary:
                errors.append(f"Summary missing required field: {field}")
        
        # Validate quality scores
        if 'quality_scores' in summary:
            if 'overall_score' not in summary['quality_scores']:
                errors.append("Summary quality_scores missing 'overall_score'")
            
            for score_name, score_value in summary['quality_scores'].items():
                if not isinstance(score_value, (int, float)):
                    errors.append(f"Quality score '{score_name}' must be a number")
                elif not (0 <= score_value <= 100):
                    errors.append(f"Quality score '{score_name}' must be between 0 and 100")
        
        return errors


# ==================== CONVENIENCE FUNCTIONS ====================

def validate_pattern_input(data: Union[Dict[str, Any], PatternInputV1]) -> PatternInputV1:
    """Validate pattern input data"""
    validator = PatternInputValidator()
    return validator.validate(data)


def validate_pattern_output(data: Union[Dict[str, Any], PatternOutputV1]) -> PatternOutputV1:
    """Validate pattern output data"""
    validator = PatternOutputValidator()
    return validator.validate(data)


def validate_error_response(data: Union[Dict[str, Any], ErrorResponseV1]) -> ErrorResponseV1:
    """Validate error response data"""
    schema_validator = ContractSchemaValidator()
    
    # Convert to dict if needed
    if isinstance(data, ErrorResponseV1):
        data_dict = data.dict()
    else:
        data_dict = data
    
    # JSON Schema validation
    schema_errors = schema_validator.validate_error_response(data_dict)
    if schema_errors:
        raise ContractValidationError(
            "Error response schema validation failed",
            schema_errors,
            "error-response-v1.json"
        )
    
    # Pydantic model validation
    try:
        if isinstance(data, ErrorResponseV1):
            return data
        return ErrorResponseV1(**data_dict)
    
    except PydanticValidationError as e:
        pydantic_errors = []
        for error in e.errors():
            field_path = ".".join(str(p) for p in error['loc'])
            pydantic_errors.append(f"Field: {field_path} - {error['msg']}")
        
        raise ContractValidationError(
            "Error response model validation failed",
            pydantic_errors,
            "ErrorResponseV1"
        )


def create_contract_compliant_error(
    service: str,
    error_type: str,
    message: str,
    retryable: bool = False,
    user_message: Optional[str] = None,
    correlation_id: Optional[str] = None,
    context: Optional[Dict[str, Any]] = None
) -> ErrorResponseV1:
    """Create a contract-compliant error response"""
    
    return ErrorResponseV1(
        service=service,
        error_type=error_type,
        message=message,
        retryable=retryable,
        user_message=user_message,
        correlation_id=correlation_id,
        context=context or {}
    )


def validate_contract_compliance(
    input_data: Optional[Dict[str, Any]] = None,
    output_data: Optional[Dict[str, Any]] = None,
    error_data: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """Validate multiple contract data types and return validation results"""
    
    results = {
        "valid": True,
        "errors": {},
        "validated_data": {}
    }
    
    # Validate input data
    if input_data is not None:
        try:
            validated_input = validate_pattern_input(input_data)
            results["validated_data"]["input"] = validated_input
        except ContractValidationError as e:
            results["valid"] = False
            results["errors"]["input"] = str(e)
    
    # Validate output data
    if output_data is not None:
        try:
            validated_output = validate_pattern_output(output_data)
            results["validated_data"]["output"] = validated_output
        except ContractValidationError as e:
            results["valid"] = False
            results["errors"]["output"] = str(e)
    
    # Validate error data
    if error_data is not None:
        try:
            validated_error = validate_error_response(error_data)
            results["validated_data"]["error"] = validated_error
        except ContractValidationError as e:
            results["valid"] = False
            results["errors"]["error"] = str(e)
    
    return results