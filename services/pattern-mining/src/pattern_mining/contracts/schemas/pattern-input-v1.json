{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "https://ccl.dev/schemas/pattern-input-v1.json", "title": "Pattern Detection Input", "description": "Input format for Pattern Detection service, derived from Repository Analysis output", "version": "1.0.0", "type": "object", "required": ["repository_id", "analysis_id", "ast_data", "detection_config"], "properties": {"repository_id": {"type": "string", "description": "Repository identifier", "pattern": "^repo_[a-zA-Z0-9]{16}$"}, "analysis_id": {"type": "string", "description": "Analysis identifier from Repository Analysis", "pattern": "^analysis_[a-zA-Z0-9]{16}$"}, "request_id": {"type": "string", "description": "Unique request identifier for tracking", "pattern": "^req_[a-zA-Z0-9]{16}$"}, "ast_data": {"type": "object", "description": "AST data optimized for pattern detection", "required": ["files", "repository_metrics"], "properties": {"files": {"type": "array", "description": "File-level AST data", "items": {"$ref": "#/definitions/FileASTData"}, "maxItems": 10000}, "repository_metrics": {"type": "object", "description": "Repository-level metrics for pattern context", "required": ["total_files", "total_lines", "languages"], "properties": {"total_files": {"type": "integer", "minimum": 0}, "total_lines": {"type": "integer", "minimum": 0}, "total_complexity": {"type": "integer", "minimum": 0}, "languages": {"type": "object", "patternProperties": {"^[a-zA-Z][a-zA-Z0-9+#-]*$": {"type": "object", "properties": {"files": {"type": "integer", "minimum": 0}, "lines": {"type": "integer", "minimum": 0}, "percentage": {"type": "number", "minimum": 0, "maximum": 100}}}}}, "architecture_hints": {"type": "object", "description": "Architecture pattern hints", "properties": {"framework_detected": {"type": "array", "items": {"type": "string"}}, "architecture_style": {"type": "string", "enum": ["mvc", "mvp", "mvvm", "microservices", "monolith", "layered", "hexagonal", "unknown"]}, "design_patterns_hint": {"type": "array", "items": {"type": "string"}}}}}}}}, "detection_config": {"type": "object", "description": "Pattern detection configuration", "required": ["enabled_detectors", "confidence_threshold"], "properties": {"enabled_detectors": {"type": "array", "description": "List of enabled pattern detectors", "items": {"type": "string", "enum": ["design_patterns", "anti_patterns", "security_vulnerabilities", "performance_issues", "code_smells", "architectural_patterns", "test_patterns", "concurrency_patterns"]}, "minItems": 1}, "confidence_threshold": {"type": "number", "minimum": 0, "maximum": 1, "description": "Minimum confidence threshold for pattern detection"}, "language_specific": {"type": "object", "description": "Language-specific detection settings", "patternProperties": {"^[a-zA-Z][a-zA-Z0-9+#-]*$": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "custom_patterns": {"type": "array", "items": {"type": "string"}}, "exclusions": {"type": "array", "items": {"type": "string"}}}}}}, "performance_limits": {"type": "object", "description": "Performance and resource limits", "properties": {"max_processing_time_ms": {"type": "integer", "minimum": 1000, "maximum": 300000}, "max_memory_mb": {"type": "integer", "minimum": 100, "maximum": 8192}, "max_patterns_per_file": {"type": "integer", "minimum": 1, "maximum": 100}}}, "output_preferences": {"type": "object", "description": "Output formatting preferences", "properties": {"include_code_examples": {"type": "boolean", "default": true}, "include_recommendations": {"type": "boolean", "default": true}, "group_by_severity": {"type": "boolean", "default": false}, "max_examples_per_pattern": {"type": "integer", "minimum": 1, "maximum": 10}}}}}, "context": {"type": "object", "description": "Additional context for pattern detection", "properties": {"user_id": {"type": "string"}, "organization_id": {"type": "string"}, "priority": {"type": "string", "enum": ["low", "normal", "high", "urgent"], "default": "normal"}, "callback_url": {"type": "string", "format": "uri", "description": "Webhook URL for completion notification"}, "tags": {"type": "array", "description": "User-defined tags for categorization", "items": {"type": "string"}}}}}, "definitions": {"FileASTData": {"type": "object", "description": "AST data for a single file optimized for pattern detection", "required": ["file_path", "language", "content_hash", "ast_nodes", "metrics"], "properties": {"file_path": {"type": "string", "description": "Relative file path from repository root"}, "language": {"type": "string", "description": "Programming language"}, "content_hash": {"type": "string", "description": "SHA-256 hash of file content", "pattern": "^[a-f0-9]{64}$"}, "size_bytes": {"type": "integer", "minimum": 0, "description": "File size in bytes"}, "ast_nodes": {"type": "array", "description": "Flattened AST nodes for efficient pattern matching", "items": {"$ref": "#/definitions/PatternASTNode"}}, "metrics": {"type": "object", "description": "File-level metrics for pattern context", "required": ["lines_of_code", "complexity", "function_count"], "properties": {"lines_of_code": {"type": "integer", "minimum": 0}, "total_lines": {"type": "integer", "minimum": 0}, "complexity": {"type": "integer", "minimum": 0}, "function_count": {"type": "integer", "minimum": 0}, "class_count": {"type": "integer", "minimum": 0}, "method_count": {"type": "integer", "minimum": 0}, "variable_count": {"type": "integer", "minimum": 0}, "comment_ratio": {"type": "number", "minimum": 0, "maximum": 1}, "nesting_depth": {"type": "integer", "minimum": 0}, "coupling_score": {"type": "number", "minimum": 0}, "cohesion_score": {"type": "number", "minimum": 0, "maximum": 1}}}, "symbols": {"type": "array", "description": "Extracted symbols for pattern analysis", "items": {"$ref": "#/definitions/PatternSymbol"}}, "imports": {"type": "array", "description": "Import/dependency information", "items": {"type": "object", "required": ["name", "type"], "properties": {"name": {"type": "string"}, "type": {"type": "string", "enum": ["module", "class", "function", "variable", "namespace"]}, "source": {"type": "string"}, "alias": {"type": "string"}}}}, "code_features": {"type": "object", "description": "Extracted code features for ML models", "properties": {"token_frequency": {"type": "object", "description": "Token frequency distribution", "patternProperties": {"^[a-zA-Z_][a-zA-Z0-9_]*$": {"type": "integer", "minimum": 0}}}, "structural_features": {"type": "object", "description": "Structural code features", "properties": {"max_nesting_depth": {"type": "integer", "minimum": 0}, "avg_method_length": {"type": "number", "minimum": 0}, "inheritance_depth": {"type": "integer", "minimum": 0}, "interface_count": {"type": "integer", "minimum": 0}}}, "semantic_features": {"type": "array", "description": "Semantic features extracted from code", "items": {"type": "object", "properties": {"feature_name": {"type": "string"}, "feature_value": {"type": "number"}, "confidence": {"type": "number", "minimum": 0, "maximum": 1}}}}}}}}, "PatternASTNode": {"type": "object", "description": "AST node optimized for pattern detection", "required": ["id", "type", "range"], "properties": {"id": {"type": "string", "description": "Unique node identifier within file"}, "type": {"type": "string", "description": "AST node type"}, "name": {"type": "string", "description": "Node name/identifier (if applicable)"}, "range": {"type": "object", "description": "Source code position range", "required": ["start_line", "end_line", "start_column", "end_column"], "properties": {"start_line": {"type": "integer", "minimum": 0}, "end_line": {"type": "integer", "minimum": 0}, "start_column": {"type": "integer", "minimum": 0}, "end_column": {"type": "integer", "minimum": 0}}}, "parent_id": {"type": "string", "description": "Parent node ID"}, "children_ids": {"type": "array", "description": "Child node IDs", "items": {"type": "string"}}, "properties": {"type": "object", "description": "Node-specific properties", "additionalProperties": true}, "text": {"type": "string", "description": "Source text for this node", "maxLength": 10000}, "annotations": {"type": "array", "description": "Code annotations/decorators", "items": {"type": "string"}}}}, "PatternSymbol": {"type": "object", "description": "Symbol information for pattern detection", "required": ["name", "type", "range"], "properties": {"name": {"type": "string", "description": "Symbol name"}, "type": {"type": "string", "enum": ["function", "method", "class", "interface", "variable", "constant", "type", "namespace"], "description": "Symbol type"}, "range": {"type": "object", "required": ["start_line", "end_line"], "properties": {"start_line": {"type": "integer", "minimum": 0}, "end_line": {"type": "integer", "minimum": 0}}}, "visibility": {"type": "string", "enum": ["public", "private", "protected", "internal"], "description": "Symbol visibility"}, "modifiers": {"type": "array", "description": "Symbol modifiers (static, abstract, etc.)", "items": {"type": "string"}}, "signature": {"type": "string", "description": "Full symbol signature"}, "parameters": {"type": "array", "description": "Function/method parameters", "items": {"type": "object", "properties": {"name": {"type": "string"}, "type": {"type": "string"}, "default_value": {"type": "string"}}}}, "return_type": {"type": "string", "description": "Return type (for functions/methods)"}, "complexity": {"type": "integer", "minimum": 0, "description": "Symbol complexity score"}, "references": {"type": "array", "description": "References to other symbols", "items": {"type": "object", "properties": {"symbol_name": {"type": "string"}, "reference_type": {"type": "string", "enum": ["call", "inheritance", "composition", "dependency"]}, "line_number": {"type": "integer", "minimum": 0}}}}, "documentation": {"type": "string", "description": "Associated documentation"}, "annotations": {"type": "array", "description": "Symbol annotations/attributes", "items": {"type": "string"}}}}}}