{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "https://ccl.dev/schemas/pattern-output-v1.json", "title": "Pattern Detection Output", "description": "Output format from Pattern Detection service for consumption by Query Intelligence and Marketplace services", "version": "1.0.0", "type": "object", "required": ["request_id", "repository_id", "patterns", "summary", "metadata"], "properties": {"request_id": {"type": "string", "description": "Original request identifier", "pattern": "^req_[a-zA-Z0-9]{16}$"}, "repository_id": {"type": "string", "description": "Repository identifier", "pattern": "^repo_[a-zA-Z0-9]{16}$"}, "analysis_id": {"type": "string", "description": "Source analysis identifier", "pattern": "^analysis_[a-zA-Z0-9]{16}$"}, "patterns": {"type": "array", "description": "Detected patterns", "items": {"$ref": "#/definitions/DetectedPattern"}, "maxItems": 1000}, "summary": {"$ref": "#/definitions/PatternSummary"}, "metadata": {"type": "object", "description": "Detection metadata and performance info", "required": ["version", "timestamp", "processing_time_ms"], "properties": {"version": {"type": "string", "description": "Pattern Detection service version", "pattern": "^\\d+\\.\\d+\\.\\d+$"}, "timestamp": {"type": "string", "format": "date-time", "description": "Detection completion timestamp"}, "processing_time_ms": {"type": "integer", "minimum": 0, "description": "Total processing time in milliseconds"}, "model_versions": {"type": "object", "description": "ML model versions used", "patternProperties": {"^[a-zA-Z_][a-zA-Z0-9_]*$": {"type": "string"}}}, "performance_stats": {"type": "object", "description": "Performance statistics", "properties": {"files_processed": {"type": "integer", "minimum": 0}, "patterns_detected": {"type": "integer", "minimum": 0}, "avg_confidence": {"type": "number", "minimum": 0, "maximum": 1}, "memory_peak_mb": {"type": "number", "minimum": 0}, "cache_hit_ratio": {"type": "number", "minimum": 0, "maximum": 1}}}, "detection_config": {"type": "object", "description": "Configuration used for detection", "properties": {"enabled_detectors": {"type": "array", "items": {"type": "string"}}, "confidence_threshold": {"type": "number", "minimum": 0, "maximum": 1}, "language_filters": {"type": "array", "items": {"type": "string"}}}}}}}, "definitions": {"DetectedPattern": {"type": "object", "description": "A detected code pattern", "required": ["id", "pattern_type", "pattern_name", "confidence", "locations", "severity"], "properties": {"id": {"type": "string", "description": "Unique pattern instance identifier", "pattern": "^pattern_[a-zA-Z0-9]{16}$"}, "pattern_type": {"type": "string", "enum": ["design_pattern", "anti_pattern", "security_vulnerability", "performance_issue", "code_smell", "architectural_pattern", "test_pattern", "concurrency_pattern"], "description": "Type of pattern detected"}, "pattern_name": {"type": "string", "description": "Human-readable pattern name", "examples": ["<PERSON><PERSON>", "God Class", "SQL Injection", "N+1 Query", "Long Method"]}, "confidence": {"type": "number", "minimum": 0, "maximum": 1, "description": "Detection confidence score"}, "severity": {"type": "string", "enum": ["info", "low", "medium", "high", "critical"], "description": "Pattern severity level"}, "locations": {"type": "array", "description": "Code locations where pattern is detected", "items": {"$ref": "#/definitions/PatternLocation"}, "minItems": 1}, "description": {"type": "string", "description": "Detailed pattern description"}, "explanation": {"type": "string", "description": "Why this pattern was detected"}, "recommendations": {"type": "array", "description": "Improvement recommendations", "items": {"type": "string"}}, "examples": {"type": "array", "description": "Code examples demonstrating the pattern", "items": {"$ref": "#/definitions/CodeExample"}}, "related_patterns": {"type": "array", "description": "Related pattern IDs", "items": {"type": "string"}}, "tags": {"type": "array", "description": "Semantic tags for categorization", "items": {"type": "string"}}, "impact": {"type": "object", "description": "Pattern impact assessment", "properties": {"maintainability": {"type": "string", "enum": ["positive", "neutral", "negative"]}, "performance": {"type": "string", "enum": ["positive", "neutral", "negative"]}, "security": {"type": "string", "enum": ["positive", "neutral", "negative"]}, "readability": {"type": "string", "enum": ["positive", "neutral", "negative"]}, "testability": {"type": "string", "enum": ["positive", "neutral", "negative"]}}}, "metrics": {"type": "object", "description": "Pattern-specific metrics", "properties": {"complexity_increase": {"type": "number"}, "lines_affected": {"type": "integer", "minimum": 0}, "files_affected": {"type": "integer", "minimum": 0}, "estimated_fix_time_minutes": {"type": "integer", "minimum": 0}}}, "detection_method": {"type": "object", "description": "How the pattern was detected", "properties": {"algorithm": {"type": "string", "enum": ["rule_based", "ml_classification", "ml_clustering", "hybrid"]}, "model_name": {"type": "string"}, "rule_set": {"type": "string"}, "features_used": {"type": "array", "items": {"type": "string"}}}}}}, "PatternLocation": {"type": "object", "description": "Location of a pattern in the codebase", "required": ["file_path", "range"], "properties": {"file_path": {"type": "string", "description": "Relative file path from repository root"}, "range": {"type": "object", "description": "Line range where pattern occurs", "required": ["start_line", "end_line"], "properties": {"start_line": {"type": "integer", "minimum": 1}, "end_line": {"type": "integer", "minimum": 1}, "start_column": {"type": "integer", "minimum": 0}, "end_column": {"type": "integer", "minimum": 0}}}, "symbol_name": {"type": "string", "description": "Primary symbol involved in the pattern"}, "context": {"type": "object", "description": "Additional location context", "properties": {"function_name": {"type": "string"}, "class_name": {"type": "string"}, "namespace": {"type": "string"}, "module_name": {"type": "string"}}}, "snippet": {"type": "string", "description": "Code snippet showing the pattern", "maxLength": 2000}}}, "CodeExample": {"type": "object", "description": "Code example demonstrating the pattern", "required": ["title", "code", "language"], "properties": {"title": {"type": "string", "description": "Example title"}, "description": {"type": "string", "description": "Example description"}, "code": {"type": "string", "description": "Code snippet", "maxLength": 5000}, "language": {"type": "string", "description": "Programming language"}, "file_path": {"type": "string", "description": "Source file path (if from actual code)"}, "line_range": {"type": "object", "description": "Line range in source file", "properties": {"start": {"type": "integer", "minimum": 1}, "end": {"type": "integer", "minimum": 1}}}, "annotations": {"type": "array", "description": "Code annotations explaining the pattern", "items": {"type": "object", "properties": {"line": {"type": "integer", "minimum": 1}, "message": {"type": "string"}}}}}}, "PatternSummary": {"type": "object", "description": "Summary of all detected patterns", "required": ["total_patterns", "by_type", "by_severity", "quality_scores"], "properties": {"total_patterns": {"type": "integer", "minimum": 0, "description": "Total number of patterns detected"}, "by_type": {"type": "object", "description": "Pattern count by type", "patternProperties": {"^(design_pattern|anti_pattern|security_vulnerability|performance_issue|code_smell|architectural_pattern|test_pattern|concurrency_pattern)$": {"type": "integer", "minimum": 0}}}, "by_severity": {"type": "object", "description": "Pattern count by severity", "properties": {"info": {"type": "integer", "minimum": 0}, "low": {"type": "integer", "minimum": 0}, "medium": {"type": "integer", "minimum": 0}, "high": {"type": "integer", "minimum": 0}, "critical": {"type": "integer", "minimum": 0}}}, "by_language": {"type": "object", "description": "Pattern count by programming language", "patternProperties": {"^[a-zA-Z][a-zA-Z0-9+#-]*$": {"type": "integer", "minimum": 0}}}, "top_patterns": {"type": "array", "description": "Most frequently detected patterns", "items": {"type": "object", "required": ["pattern_name", "count"], "properties": {"pattern_name": {"type": "string"}, "count": {"type": "integer", "minimum": 0}, "avg_confidence": {"type": "number", "minimum": 0, "maximum": 1}}}, "maxItems": 10}, "quality_scores": {"type": "object", "description": "Overall code quality scores", "required": ["overall_score"], "properties": {"overall_score": {"type": "number", "minimum": 0, "maximum": 100, "description": "Overall code quality score (0-100)"}, "maintainability_score": {"type": "number", "minimum": 0, "maximum": 100}, "security_score": {"type": "number", "minimum": 0, "maximum": 100}, "performance_score": {"type": "number", "minimum": 0, "maximum": 100}, "readability_score": {"type": "number", "minimum": 0, "maximum": 100}, "testability_score": {"type": "number", "minimum": 0, "maximum": 100}}}, "recommendations": {"type": "array", "description": "Top-level improvement recommendations", "items": {"type": "object", "required": ["priority", "category", "description"], "properties": {"priority": {"type": "string", "enum": ["low", "medium", "high", "critical"]}, "category": {"type": "string"}, "description": {"type": "string"}, "affected_files": {"type": "integer", "minimum": 0}, "estimated_effort_hours": {"type": "number", "minimum": 0}}}}, "trends": {"type": "object", "description": "Pattern trends (if historical data available)", "properties": {"improving_areas": {"type": "array", "items": {"type": "string"}}, "degrading_areas": {"type": "array", "items": {"type": "string"}}, "new_issues": {"type": "integer", "minimum": 0}, "resolved_issues": {"type": "integer", "minimum": 0}}}}}}}