{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "https://ccl.dev/schemas/error-response-v1.json", "title": "Unified Error Response", "description": "Standardized error response format for all CCL services", "version": "1.0.0", "type": "object", "required": ["error_id", "service", "error_type", "message", "retryable", "timestamp"], "properties": {"error_id": {"type": "string", "description": "Unique error identifier"}, "service": {"type": "string", "description": "Service name that generated the error"}, "error_type": {"type": "string", "enum": ["validation", "timeout", "internal", "external"], "description": "Type of error that occurred"}, "message": {"type": "string", "description": "Human-readable error message"}, "retryable": {"type": "boolean", "description": "Whether the error is retryable"}, "user_message": {"type": "string", "description": "User-friendly error message"}, "correlation_id": {"type": "string", "description": "Request correlation ID for tracing"}, "timestamp": {"type": "string", "format": "date-time", "description": "Error timestamp in ISO 8601 format"}, "context": {"type": "object", "description": "Additional error context", "additionalProperties": true}}}