"""
CCL Contract Compliance Metrics

Wave 2.5: Phase 6 - Monitoring for contract compliance.
Prometheus metrics for tracking CCL contract adherence.
"""

from prometheus_client import Counter, Histogram, Gauge, Info
from typing import Dict, Any, Optional
import time
from functools import wraps

# Contract Compliance Info
contract_info = Info(
    'ccl_contract_compliance',
    'CCL contract compliance information'
)
contract_info.info({
    'contract_version': '1.0.0',
    'service_version': '1.0.0',
    'compliance_status': 'certified',
    'certification_date': '2025-01-18'
})

# Request Metrics
ccl_requests_total = Counter(
    'ccl_pattern_requests_total',
    'Total number of pattern detection requests',
    ['repository_id', 'status', 'pattern_type']
)

ccl_request_duration = Histogram(
    'ccl_pattern_request_duration_seconds',
    'Pattern detection request duration',
    ['endpoint', 'status'],
    buckets=(0.01, 0.025, 0.05, 0.075, 0.1, 0.25, 0.5, 0.75, 1.0, 2.5, 5.0, 7.5, 10.0)
)

# Processing Metrics
ccl_files_processed = Counter(
    'ccl_files_processed_total',
    'Total number of files processed',
    ['repository_id', 'language', 'status']
)

ccl_patterns_detected = Counter(
    'ccl_patterns_detected_total',
    'Total number of patterns detected',
    ['pattern_type', 'severity', 'repository_id']
)

ccl_processing_time = Histogram(
    'ccl_processing_time_seconds',
    'Time spent processing AST data',
    ['operation', 'repository_id'],
    buckets=(0.1, 0.5, 1.0, 2.5, 5.0, 10.0, 25.0, 30.0)
)

# Performance SLA Metrics
ccl_file_processing_rate = Gauge(
    'ccl_file_processing_rate_per_second',
    'Current file processing rate',
    ['repository_id']
)

ccl_pattern_detection_rate = Gauge(
    'ccl_pattern_detection_rate_per_second',
    'Current pattern detection rate',
    ['repository_id']
)

ccl_api_latency_p95 = Gauge(
    'ccl_api_latency_p95_milliseconds',
    'API latency 95th percentile in milliseconds'
)

ccl_throughput_rps = Gauge(
    'ccl_throughput_requests_per_second',
    'Current throughput in requests per second'
)

# Contract Validation Metrics
ccl_id_format_errors = Counter(
    'ccl_id_format_errors_total',
    'Total ID format validation errors',
    ['id_type', 'error_type']
)

ccl_schema_validation_errors = Counter(
    'ccl_schema_validation_errors_total',
    'Total schema validation errors',
    ['schema_type', 'field']
)

ccl_contract_violations = Counter(
    'ccl_contract_violations_total',
    'Total contract violations detected',
    ['violation_type', 'severity']
)

# Resource Usage Metrics
ccl_memory_usage_mb = Gauge(
    'ccl_memory_usage_megabytes',
    'Current memory usage in MB',
    ['component']
)

ccl_cache_operations = Counter(
    'ccl_cache_operations_total',
    'Cache operations',
    ['operation', 'status']
)

ccl_cache_hit_rate = Gauge(
    'ccl_cache_hit_rate_percentage',
    'Cache hit rate percentage'
)

# Error Metrics
ccl_errors_total = Counter(
    'ccl_errors_total',
    'Total errors by type',
    ['error_type', 'severity', 'retryable']
)

ccl_error_rate = Gauge(
    'ccl_error_rate_percentage',
    'Current error rate percentage'
)

# Integration Metrics
ccl_integration_requests = Counter(
    'ccl_integration_requests_total',
    'Requests to/from other CCL services',
    ['service', 'operation', 'status']
)

ccl_integration_latency = Histogram(
    'ccl_integration_latency_seconds',
    'Latency for CCL service integration',
    ['service', 'operation'],
    buckets=(0.01, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0)
)


class ContractMetrics:
    """Helper class for recording CCL contract metrics."""
    
    def __init__(self):
        self.request_times = []
        self.error_count = 0
        self.total_requests = 0
        
    def record_request(
        self,
        repository_id: str,
        status: str,
        pattern_type: str = "all",
        duration: Optional[float] = None
    ):
        """Record a pattern detection request."""
        ccl_requests_total.labels(
            repository_id=repository_id,
            status=status,
            pattern_type=pattern_type
        ).inc()
        
        self.total_requests += 1
        if status != "success":
            self.error_count += 1
            
        if duration:
            self.request_times.append(duration)
            ccl_request_duration.labels(
                endpoint="/api/v1/patterns/detect",
                status=status
            ).observe(duration)
            
    def record_files_processed(
        self,
        repository_id: str,
        language: str,
        count: int,
        status: str = "success"
    ):
        """Record files processed."""
        ccl_files_processed.labels(
            repository_id=repository_id,
            language=language,
            status=status
        ).inc(count)
        
    def record_patterns_detected(
        self,
        patterns: list,
        repository_id: str
    ):
        """Record patterns detected."""
        for pattern in patterns:
            ccl_patterns_detected.labels(
                pattern_type=pattern.get("pattern_type", "unknown"),
                severity=pattern.get("severity", "unknown"),
                repository_id=repository_id
            ).inc()
            
    def record_processing_time(
        self,
        operation: str,
        repository_id: str,
        duration: float
    ):
        """Record processing time."""
        ccl_processing_time.labels(
            operation=operation,
            repository_id=repository_id
        ).observe(duration)
        
    def update_performance_metrics(
        self,
        file_rate: float,
        pattern_rate: float,
        api_latency_p95: float,
        throughput_rps: float,
        repository_id: str = "aggregate"
    ):
        """Update performance SLA metrics."""
        ccl_file_processing_rate.labels(repository_id=repository_id).set(file_rate)
        ccl_pattern_detection_rate.labels(repository_id=repository_id).set(pattern_rate)
        ccl_api_latency_p95.set(api_latency_p95)
        ccl_throughput_rps.set(throughput_rps)
        
    def record_validation_error(
        self,
        error_type: str,
        details: Dict[str, Any]
    ):
        """Record validation errors."""
        if "id_format" in error_type:
            ccl_id_format_errors.labels(
                id_type=details.get("id_type", "unknown"),
                error_type=error_type
            ).inc()
        elif "schema" in error_type:
            ccl_schema_validation_errors.labels(
                schema_type=details.get("schema_type", "unknown"),
                field=details.get("field", "unknown")
            ).inc()
        else:
            ccl_contract_violations.labels(
                violation_type=error_type,
                severity=details.get("severity", "unknown")
            ).inc()
            
    def record_error(
        self,
        error_type: str,
        severity: str = "medium",
        retryable: bool = False
    ):
        """Record general errors."""
        ccl_errors_total.labels(
            error_type=error_type,
            severity=severity,
            retryable=str(retryable)
        ).inc()
        
        # Update error rate
        if self.total_requests > 0:
            error_rate = (self.error_count / self.total_requests) * 100
            ccl_error_rate.set(error_rate)
            
    def record_cache_operation(
        self,
        operation: str,
        hit: bool = False
    ):
        """Record cache operations."""
        status = "hit" if hit else "miss"
        ccl_cache_operations.labels(
            operation=operation,
            status=status
        ).inc()
        
    def update_cache_hit_rate(self, hit_rate: float):
        """Update cache hit rate."""
        ccl_cache_hit_rate.set(hit_rate * 100)
        
    def record_integration_request(
        self,
        service: str,
        operation: str,
        status: str,
        duration: Optional[float] = None
    ):
        """Record integration with other CCL services."""
        ccl_integration_requests.labels(
            service=service,
            operation=operation,
            status=status
        ).inc()
        
        if duration:
            ccl_integration_latency.labels(
                service=service,
                operation=operation
            ).observe(duration)
            
    def update_memory_usage(self, component: str, usage_mb: float):
        """Update memory usage metrics."""
        ccl_memory_usage_mb.labels(component=component).set(usage_mb)


# Decorator for timing operations
def track_processing_time(operation: str, repository_id: str = "unknown"):
    """Decorator to track processing time."""
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                duration = time.time() - start_time
                metrics.record_processing_time(operation, repository_id, duration)
                return result
            except Exception as e:
                duration = time.time() - start_time
                metrics.record_processing_time(operation, repository_id, duration)
                raise
                
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                metrics.record_processing_time(operation, repository_id, duration)
                return result
            except Exception as e:
                duration = time.time() - start_time
                metrics.record_processing_time(operation, repository_id, duration)
                raise
                
        # Return appropriate wrapper based on function type
        import asyncio
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
            
    return decorator


# Global metrics instance
metrics = ContractMetrics()