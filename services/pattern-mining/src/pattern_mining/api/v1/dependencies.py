"""
Pattern Mining Service - API Dependencies

FastAPI dependency injection for API endpoints.

Wave 2.5: CCL Contract Compliance Implementation - Phase 3
"""

import logging
import os
from typing import Optional

import redis
from fastapi import Depends

from ...ast_processing import (
    ASTDataProcessor,
    PatternFeatureExtractor,
    EnhancedPatternDetector,
    ProcessingConfig,
)
from ...performance import PerformanceStats

logger = logging.getLogger(__name__)

# Global instances (created on startup)
_redis_client: Optional[redis.Redis] = None
_ast_processor: Optional[ASTDataProcessor] = None
_feature_extractor: Optional[PatternFeatureExtractor] = None
_pattern_detector: Optional[EnhancedPatternDetector] = None
_performance_stats: Optional[PerformanceStats] = None


def init_dependencies():
    """Initialize global dependencies on startup"""
    global _redis_client, _ast_processor, _feature_extractor, _pattern_detector, _performance_stats
    
    # Initialize Redis client
    redis_url = os.getenv("REDIS_URL", "redis://localhost:6379")
    if redis_url and redis_url != "none":
        try:
            _redis_client = redis.from_url(
                redis_url,
                decode_responses=True,
                socket_timeout=5,
                socket_connect_timeout=5,
                retry_on_timeout=True,
                health_check_interval=30,
            )
            _redis_client.ping()
            logger.info(f"Connected to Redis at {redis_url}")
        except Exception as e:
            logger.warning(f"Failed to connect to Redis: {e}")
            _redis_client = None
    
    # Initialize AST processor
    processing_config = ProcessingConfig(
        max_file_size_mb=float(os.getenv("MAX_FILE_SIZE_MB", "5.0")),
        max_ast_nodes=int(os.getenv("MAX_AST_NODES", "10000")),
        enable_caching=os.getenv("ENABLE_CACHING", "true").lower() == "true",
        parallel_processing=os.getenv("PARALLEL_PROCESSING", "true").lower() == "true",
        feature_extraction_timeout=int(os.getenv("FEATURE_EXTRACTION_TIMEOUT", "30")),
        pattern_detection_timeout=int(os.getenv("PATTERN_DETECTION_TIMEOUT", "60")),
        min_confidence_threshold=float(os.getenv("MIN_CONFIDENCE_THRESHOLD", "0.1")),
        max_patterns_per_file=int(os.getenv("MAX_PATTERNS_PER_FILE", "100")),
        batch_size=int(os.getenv("BATCH_SIZE", "10")),
    )
    _ast_processor = ASTDataProcessor(config=processing_config)
    logger.info("Initialized AST processor")
    
    # Initialize feature extractor
    _feature_extractor = PatternFeatureExtractor()
    logger.info("Initialized feature extractor")
    
    # Initialize pattern detector
    _pattern_detector = EnhancedPatternDetector()
    logger.info("Initialized pattern detector")
    
    # Initialize performance stats
    _performance_stats = PerformanceStats(redis_client=_redis_client)
    logger.info("Initialized performance stats")


def cleanup_dependencies():
    """Cleanup dependencies on shutdown"""
    global _redis_client
    
    if _redis_client:
        try:
            _redis_client.close()
            logger.info("Closed Redis connection")
        except Exception as e:
            logger.error(f"Error closing Redis connection: {e}")


# Dependency injection functions

async def get_redis_client() -> Optional[redis.Redis]:
    """Get Redis client instance"""
    return _redis_client


async def get_ast_processor() -> ASTDataProcessor:
    """Get AST processor instance"""
    if _ast_processor is None:
        raise RuntimeError("AST processor not initialized")
    return _ast_processor


async def get_feature_extractor() -> PatternFeatureExtractor:
    """Get feature extractor instance"""
    if _feature_extractor is None:
        raise RuntimeError("Feature extractor not initialized")
    return _feature_extractor


async def get_pattern_detector() -> EnhancedPatternDetector:
    """Get pattern detector instance"""
    if _pattern_detector is None:
        raise RuntimeError("Pattern detector not initialized")
    return _pattern_detector


async def get_performance_stats() -> PerformanceStats:
    """Get performance stats instance"""
    if _performance_stats is None:
        raise RuntimeError("Performance stats not initialized")
    return _performance_stats


# Composite dependencies

class PatternService:
    """Composite service for pattern detection"""
    
    def __init__(
        self,
        ast_processor: ASTDataProcessor,
        feature_extractor: PatternFeatureExtractor,
        pattern_detector: EnhancedPatternDetector,
        performance_stats: PerformanceStats,
        redis_client: Optional[redis.Redis] = None,
    ):
        self.ast_processor = ast_processor
        self.feature_extractor = feature_extractor
        self.pattern_detector = pattern_detector
        self.performance_stats = performance_stats
        self.redis_client = redis_client


async def get_pattern_service(
    ast_processor: ASTDataProcessor = Depends(get_ast_processor),
    feature_extractor: PatternFeatureExtractor = Depends(get_feature_extractor),
    pattern_detector: EnhancedPatternDetector = Depends(get_pattern_detector),
    performance_stats: PerformanceStats = Depends(get_performance_stats),
    redis_client: Optional[redis.Redis] = Depends(get_redis_client),
) -> PatternService:
    """Get composite pattern service"""
    return PatternService(
        ast_processor=ast_processor,
        feature_extractor=feature_extractor,
        pattern_detector=pattern_detector,
        performance_stats=performance_stats,
        redis_client=redis_client,
    )