"""
Pattern Mining Service - API v1 Module

Contract-compliant API v1 implementation.

Wave 2.5: CCL Contract Compliance Implementation - Phase 3
"""

from .patterns import (
    router as patterns_router,
    detect_patterns,
)

from .health import (
    router as health_router,
    get_health,
    get_readiness,
)

from .dependencies import (
    get_pattern_service,
    get_redis_client,
    get_ast_processor,
    get_feature_extractor,
    get_pattern_detector,
)

from .middleware import (
    request_id_middleware,
    error_handler_middleware,
    performance_tracking_middleware,
    contract_validation_middleware,
)

__all__ = [
    # Routers
    "patterns_router",
    "health_router",
    
    # Handlers
    "detect_patterns",
    "get_health",
    "get_readiness",
    
    # Dependencies
    "get_pattern_service",
    "get_redis_client",
    "get_ast_processor",
    "get_feature_extractor",
    "get_pattern_detector",
    
    # Middleware
    "request_id_middleware",
    "error_handler_middleware",
    "performance_tracking_middleware",
    "contract_validation_middleware",
]