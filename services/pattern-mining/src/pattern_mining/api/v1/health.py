"""
Pattern Mining Service - Health Check API Endpoints

Health and readiness check endpoints for service monitoring.

Wave 2.5: CCL Contract Compliance Implementation - Phase 3
"""

import logging
import time
from datetime import datetime
from typing import Dict, Any, Optional

from fastapi import APIRouter, Depends, status
from pydantic import BaseModel, Field
import redis
import psutil

from .dependencies import get_redis_client, get_performance_stats
from ...performance import PerformanceStats

logger = logging.getLogger(__name__)

# Create router
router = APIRouter(
    tags=["health"],
    responses={
        503: {"description": "Service unhealthy"},
    }
)


class HealthCheckResponse(BaseModel):
    """Health check response model"""
    status: str = Field(..., description="Health status (healthy/unhealthy)")
    timestamp: datetime = Field(..., description="Check timestamp")
    version: str = Field(..., description="Service version")
    uptime_seconds: float = Field(..., description="Service uptime in seconds")
    checks: Dict[str, Dict[str, Any]] = Field(..., description="Individual health checks")


class ReadinessCheckResponse(BaseModel):
    """Readiness check response model"""
    ready: bool = Field(..., description="Service readiness status")
    timestamp: datetime = Field(..., description="Check timestamp")
    checks: Dict[str, bool] = Field(..., description="Individual readiness checks")
    message: Optional[str] = Field(None, description="Readiness message")


# Track service start time
SERVICE_START_TIME = time.time()
SERVICE_VERSION = "1.0.0"


@router.get(
    "/health",
    response_model=HealthCheckResponse,
    status_code=status.HTTP_200_OK,
    summary="Health check",
    description="Check service health including dependencies"
)
async def get_health(
    redis_client: Optional[redis.Redis] = Depends(get_redis_client),
    performance_stats: PerformanceStats = Depends(get_performance_stats),
) -> HealthCheckResponse:
    """
    Comprehensive health check endpoint.
    
    Checks:
    - Service status
    - Redis connectivity
    - Memory usage
    - CPU usage
    - Disk usage
    
    Returns:
        HealthCheckResponse with detailed health information
    """
    timestamp = datetime.utcnow()
    uptime = time.time() - SERVICE_START_TIME
    
    checks = {}
    overall_status = "healthy"
    
    # Check Redis
    redis_check = {
        "status": "unknown",
        "latency_ms": None,
        "error": None
    }
    
    if redis_client:
        try:
            start = time.time()
            redis_client.ping()
            latency = (time.time() - start) * 1000
            
            redis_check["status"] = "healthy"
            redis_check["latency_ms"] = round(latency, 2)
            
            if latency > 100:  # High latency warning
                redis_check["status"] = "degraded"
                overall_status = "degraded"
                
        except Exception as e:
            redis_check["status"] = "unhealthy"
            redis_check["error"] = str(e)
            overall_status = "unhealthy"
    else:
        redis_check["status"] = "not_configured"
    
    checks["redis"] = redis_check
    
    # Check memory usage
    memory = psutil.virtual_memory()
    memory_check = {
        "status": "healthy",
        "usage_percent": memory.percent,
        "available_mb": round(memory.available / 1024 / 1024, 2),
        "total_mb": round(memory.total / 1024 / 1024, 2)
    }
    
    if memory.percent > 90:
        memory_check["status"] = "critical"
        overall_status = "unhealthy"
    elif memory.percent > 80:
        memory_check["status"] = "warning"
        if overall_status == "healthy":
            overall_status = "degraded"
    
    checks["memory"] = memory_check
    
    # Check CPU usage
    cpu_percent = psutil.cpu_percent(interval=0.1)
    cpu_check = {
        "status": "healthy",
        "usage_percent": cpu_percent,
        "core_count": psutil.cpu_count()
    }
    
    if cpu_percent > 90:
        cpu_check["status"] = "critical"
        overall_status = "unhealthy"
    elif cpu_percent > 80:
        cpu_check["status"] = "warning"
        if overall_status == "healthy":
            overall_status = "degraded"
    
    checks["cpu"] = cpu_check
    
    # Check disk usage
    disk = psutil.disk_usage('/')
    disk_check = {
        "status": "healthy",
        "usage_percent": disk.percent,
        "free_gb": round(disk.free / 1024 / 1024 / 1024, 2),
        "total_gb": round(disk.total / 1024 / 1024 / 1024, 2)
    }
    
    if disk.percent > 90:
        disk_check["status"] = "critical"
        overall_status = "unhealthy"
    elif disk.percent > 80:
        disk_check["status"] = "warning"
        if overall_status == "healthy":
            overall_status = "degraded"
    
    checks["disk"] = disk_check
    
    # Check performance stats
    perf_check = {
        "status": "healthy",
        "requests_per_minute": await performance_stats.get_requests_per_minute(),
        "avg_response_time_ms": await performance_stats.get_avg_response_time(),
        "error_rate": await performance_stats.get_error_rate()
    }
    
    if perf_check["error_rate"] > 0.05:  # 5% error rate
        perf_check["status"] = "warning"
        if overall_status == "healthy":
            overall_status = "degraded"
    
    checks["performance"] = perf_check
    
    return HealthCheckResponse(
        status=overall_status,
        timestamp=timestamp,
        version=SERVICE_VERSION,
        uptime_seconds=uptime,
        checks=checks
    )


@router.get(
    "/ready",
    response_model=ReadinessCheckResponse,
    status_code=status.HTTP_200_OK,
    summary="Readiness check",
    description="Check if service is ready to handle requests"
)
async def get_readiness(
    redis_client: Optional[redis.Redis] = Depends(get_redis_client),
) -> ReadinessCheckResponse:
    """
    Readiness check endpoint.
    
    Checks if the service is ready to handle pattern detection requests:
    - Service is running
    - Redis is accessible (if configured)
    - Models are loaded
    
    Returns:
        ReadinessCheckResponse indicating readiness status
    """
    timestamp = datetime.utcnow()
    checks = {}
    ready = True
    message = None
    
    # Check service uptime (wait for initialization)
    uptime = time.time() - SERVICE_START_TIME
    checks["initialized"] = uptime > 5.0  # 5 seconds initialization time
    
    if not checks["initialized"]:
        ready = False
        message = "Service is still initializing"
    
    # Check Redis if configured
    if redis_client:
        try:
            redis_client.ping()
            checks["redis"] = True
        except Exception:
            checks["redis"] = False
            ready = False
            message = "Redis connection failed"
    else:
        checks["redis"] = True  # Not required
    
    # Check if pattern detection models are loaded
    # For now, assume they're always loaded
    checks["models_loaded"] = True
    
    # Check disk space
    disk = psutil.disk_usage('/')
    checks["disk_space"] = disk.percent < 95
    
    if not checks["disk_space"]:
        ready = False
        message = "Insufficient disk space"
    
    # Check memory
    memory = psutil.virtual_memory()
    checks["memory_available"] = memory.percent < 95
    
    if not checks["memory_available"]:
        ready = False
        message = "Insufficient memory"
    
    return ReadinessCheckResponse(
        ready=ready,
        timestamp=timestamp,
        checks=checks,
        message=message
    )