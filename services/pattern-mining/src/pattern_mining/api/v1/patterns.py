"""
Pattern Mining Service - Pattern Detection API Endpoints

Contract-compliant pattern detection endpoints implementing /api/v1/patterns/detect.

Wave 2.5: CCL Contract Compliance Implementation - Phase 3
"""

import logging
import time
from datetime import datetime
from typing import Dict, Any, Optional

from fastapi import APIRouter, HTTPException, Depends, Header, status
from fastapi.responses import JSONResponse

from ...contracts.models import (
    PatternInputV1,
    PatternOutputV1,
    PatternSummaryV1,
    PatternMetadataV1,
    ErrorResponseV1,
)
from ...contracts.validators import (
    validate_pattern_input,
    validate_pattern_output,
    create_contract_compliant_error,
)
from ...ast_processing import (
    ASTDataProcessor,
    PatternFeatureExtractor,
    EnhancedPatternDetector,
)
from ...performance import PerformanceStats
from .dependencies import (
    get_ast_processor,
    get_feature_extractor,
    get_pattern_detector,
    get_performance_stats,
)

logger = logging.getLogger(__name__)

# Create router
router = APIRouter(
    prefix="/patterns",
    tags=["patterns"],
    responses={
        400: {"description": "Bad request - Invalid input format"},
        422: {"description": "Validation error - Input doesn't match schema"},
        500: {"description": "Internal server error"},
        503: {"description": "Service temporarily unavailable"},
    }
)


@router.post(
    "/detect",
    response_model=PatternOutputV1,
    status_code=status.HTTP_200_OK,
    summary="Detect patterns in code",
    description="Detect design patterns, anti-patterns, security vulnerabilities, "
                "performance issues, code smells, and other patterns in AST data."
)
async def detect_patterns(
    request: PatternInputV1,
    x_correlation_id: Optional[str] = Header(None, description="Request correlation ID"),
    ast_processor: ASTDataProcessor = Depends(get_ast_processor),
    feature_extractor: PatternFeatureExtractor = Depends(get_feature_extractor),
    pattern_detector: EnhancedPatternDetector = Depends(get_pattern_detector),
    performance_stats: PerformanceStats = Depends(get_performance_stats),
) -> PatternOutputV1:
    """
    Contract-compliant pattern detection endpoint.
    
    Implements the pattern detection flow:
    1. Validate input against contract schema
    2. Process AST data to extract features
    3. Detect patterns using ML and rule-based methods
    4. Generate contract-compliant output
    
    Args:
        request: PatternInputV1 request from Repository Analysis service
        x_correlation_id: Optional correlation ID for request tracing
        
    Returns:
        PatternOutputV1 response with detected patterns
        
    Raises:
        HTTPException: For validation errors or processing failures
    """
    start_time = time.time()
    correlation_id = x_correlation_id or request.request_id or f"req_{datetime.utcnow().timestamp()}"
    
    try:
        # Log request
        logger.info(
            f"Pattern detection request received: "
            f"repository_id={request.repository_id}, "
            f"analysis_id={request.analysis_id}, "
            f"request_id={request.request_id}, "
            f"correlation_id={correlation_id}"
        )
        
        # Validate input
        try:
            validated_input = validate_pattern_input(request)
        except Exception as e:
            logger.error(f"Input validation failed: {e}")
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail=str(e)
            )
        
        # Process AST data
        logger.info("Processing AST data...")
        processed_data = await ast_processor.process_ast_data(
            ast_data=validated_input.ast_data,
            repository_id=validated_input.repository_id,
            analysis_id=validated_input.analysis_id
        )
        
        if processed_data.errors:
            logger.error(f"AST processing errors: {processed_data.errors}")
            if processed_data.stats.processed_files == 0:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Failed to process any files from AST data"
                )
        
        # Extract features
        logger.info("Extracting pattern features...")
        features = feature_extractor.extract_features(processed_data)
        
        # Detect patterns
        logger.info("Detecting patterns...")
        detection_results = await pattern_detector.detect_patterns(
            processed_data=processed_data,
            features=features,
            detection_config=validated_input.detection_config.dict()
        )
        
        # Aggregate patterns from all files
        all_patterns = []
        total_detection_time = 0
        
        for file_path, result in detection_results.items():
            all_patterns.extend(result.patterns)
            total_detection_time += result.detection_time_ms
        
        # Apply global limits
        max_patterns = validated_input.detection_config.performance_limits.get(
            'max_patterns_total', 1000
        )
        if len(all_patterns) > max_patterns:
            # Sort by severity and confidence
            all_patterns.sort(
                key=lambda p: (
                    _severity_score(p.severity),
                    p.confidence
                ),
                reverse=True
            )
            all_patterns = all_patterns[:max_patterns]
        
        # Generate summary
        summary = _generate_pattern_summary(all_patterns)
        
        # Generate metadata
        processing_time_ms = int((time.time() - start_time) * 1000)
        metadata = PatternMetadataV1(
            version="1.0.0",
            timestamp=datetime.utcnow(),
            processing_time_ms=processing_time_ms,
            model_versions={
                "pattern_detector": "1.0.0",
                "ast_processor": "1.0.0",
                "feature_extractor": "1.0.0",
            },
            performance_stats={
                "files_processed": processed_data.stats.processed_files,
                "patterns_detected": len(all_patterns),
                "avg_confidence": sum(p.confidence for p in all_patterns) / len(all_patterns) if all_patterns else 0.0,
                "memory_peak_mb": processed_data.stats.memory_usage_mb,
                "cache_hit_ratio": processed_data.stats.cache_hits / (processed_data.stats.cache_hits + processed_data.stats.cache_misses) if (processed_data.stats.cache_hits + processed_data.stats.cache_misses) > 0 else 0.0,
            },
            detection_config={
                "enabled_detectors": validated_input.detection_config.enabled_detectors,
                "confidence_threshold": validated_input.detection_config.confidence_threshold,
                "language_filters": list(processed_data.language_distribution.keys()),
            }
        )
        
        # Create response
        response = PatternOutputV1(
            request_id=validated_input.request_id,
            repository_id=validated_input.repository_id,
            analysis_id=validated_input.analysis_id,
            patterns=all_patterns,
            summary=summary,
            metadata=metadata
        )
        
        # Validate output
        try:
            validated_output = validate_pattern_output(response)
        except Exception as e:
            logger.error(f"Output validation failed: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal error: Invalid output format"
            )
        
        # Update performance stats
        await performance_stats.record_request(
            endpoint="/api/v1/patterns/detect",
            method="POST",
            status_code=200,
            response_time_ms=processing_time_ms,
            patterns_detected=len(all_patterns)
        )
        
        logger.info(
            f"Pattern detection completed: "
            f"patterns={len(all_patterns)}, "
            f"processing_time={processing_time_ms}ms, "
            f"request_id={validated_output.request_id}"
        )
        
        return validated_output
        
    except HTTPException:
        # Re-raise FastAPI exceptions
        raise
        
    except Exception as e:
        logger.error(f"Pattern detection error: {e}", exc_info=True)
        
        # Create contract-compliant error response
        error = create_contract_compliant_error(
            service="pattern-mining",
            error_type="internal",
            message=f"Pattern detection failed: {str(e)}",
            retryable=True,
            user_message="An error occurred while detecting patterns. Please try again.",
            correlation_id=correlation_id,
            context={
                "repository_id": request.repository_id,
                "analysis_id": request.analysis_id,
                "request_id": request.request_id,
            }
        )
        
        # Update performance stats
        processing_time_ms = int((time.time() - start_time) * 1000)
        await performance_stats.record_request(
            endpoint="/api/v1/patterns/detect",
            method="POST",
            status_code=500,
            response_time_ms=processing_time_ms,
            error=str(e)
        )
        
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=error.dict()
        )


def _generate_pattern_summary(patterns: list) -> PatternSummaryV1:
    """Generate pattern summary from detected patterns"""
    
    # Count patterns by type
    by_type = {}
    by_severity = {"info": 0, "low": 0, "medium": 0, "high": 0, "critical": 0}
    by_language = {}
    
    for pattern in patterns:
        # By type
        pattern_type = pattern.pattern_type
        by_type[pattern_type] = by_type.get(pattern_type, 0) + 1
        
        # By severity
        by_severity[pattern.severity] = by_severity.get(pattern.severity, 0) + 1
        
        # By language (extract from file path)
        for location in pattern.locations:
            # Simple language detection from file extension
            ext = location.file_path.split('.')[-1] if '.' in location.file_path else 'unknown'
            lang = _extension_to_language(ext)
            by_language[lang] = by_language.get(lang, 0) + 1
    
    # Calculate top patterns
    pattern_counts = {}
    for pattern in patterns:
        pattern_counts[pattern.pattern_name] = pattern_counts.get(pattern.pattern_name, 0) + 1
    
    top_patterns = [
        {
            "pattern_name": name,
            "count": count,
            "avg_confidence": sum(p.confidence for p in patterns if p.pattern_name == name) / count
        }
        for name, count in sorted(pattern_counts.items(), key=lambda x: x[1], reverse=True)[:10]
    ]
    
    # Calculate quality scores
    total_patterns = len(patterns)
    critical_patterns = by_severity.get("critical", 0)
    high_patterns = by_severity.get("high", 0)
    
    # Simple scoring algorithm
    overall_score = max(0, 100 - (critical_patterns * 10) - (high_patterns * 5) - (total_patterns * 0.5))
    security_score = max(0, 100 - (sum(1 for p in patterns if p.pattern_type == "security_vulnerability") * 20))
    maintainability_score = max(0, 100 - (sum(1 for p in patterns if p.pattern_type in ["anti_pattern", "code_smell"]) * 5))
    
    # Generate recommendations
    recommendations = []
    
    if critical_patterns > 0:
        recommendations.append({
            "priority": "critical",
            "category": "security",
            "description": f"Address {critical_patterns} critical security vulnerabilities immediately",
            "affected_files": len(set(loc.file_path for p in patterns if p.severity == "critical" for loc in p.locations)),
            "estimated_effort_hours": critical_patterns * 4.0
        })
    
    if by_type.get("anti_pattern", 0) > 5:
        recommendations.append({
            "priority": "high",
            "category": "architecture",
            "description": "Refactor code to eliminate anti-patterns and improve design",
            "affected_files": len(set(loc.file_path for p in patterns if p.pattern_type == "anti_pattern" for loc in p.locations)),
            "estimated_effort_hours": by_type["anti_pattern"] * 2.0
        })
    
    if by_type.get("performance_issue", 0) > 3:
        recommendations.append({
            "priority": "medium",
            "category": "performance",
            "description": "Optimize performance bottlenecks for better scalability",
            "affected_files": len(set(loc.file_path for p in patterns if p.pattern_type == "performance_issue" for loc in p.locations)),
            "estimated_effort_hours": by_type["performance_issue"] * 3.0
        })
    
    return PatternSummaryV1(
        total_patterns=total_patterns,
        by_type=by_type,
        by_severity=by_severity,
        by_language=by_language,
        top_patterns=top_patterns,
        quality_scores={
            "overall_score": overall_score,
            "maintainability_score": maintainability_score,
            "security_score": security_score,
            "performance_score": max(0, 100 - (by_type.get("performance_issue", 0) * 10)),
            "readability_score": max(0, 100 - (by_type.get("code_smell", 0) * 3)),
            "testability_score": 75.0,  # Placeholder
        },
        recommendations=recommendations,
        trends=None  # No historical data available
    )


def _severity_score(severity: str) -> int:
    """Convert severity to numeric score for sorting"""
    return {"critical": 5, "high": 4, "medium": 3, "low": 2, "info": 1}.get(severity, 0)


def _extension_to_language(ext: str) -> str:
    """Map file extension to language"""
    mapping = {
        "py": "python",
        "js": "javascript",
        "ts": "typescript",
        "java": "java",
        "rs": "rust",
        "go": "go",
        "cs": "csharp",
        "cpp": "cpp",
        "c": "c",
        "rb": "ruby",
        "php": "php",
        "kt": "kotlin",
        "swift": "swift",
        "scala": "scala",
    }
    return mapping.get(ext, "unknown")