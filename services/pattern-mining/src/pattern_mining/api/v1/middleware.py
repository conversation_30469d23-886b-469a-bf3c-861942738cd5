"""
Pattern Mining Service - API Middleware

Middleware for request processing, error handling, and performance tracking.

Wave 2.5: CCL Contract Compliance Implementation - Phase 3
"""

import logging
import time
import uuid
from datetime import datetime
from typing import Optional, Dict, Any

from fastapi import Request, Response
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

from ...contracts.models import ErrorResponseV1
from ...contracts.validators import create_contract_compliant_error

logger = logging.getLogger(__name__)


class RequestIDMiddleware(BaseHTTPMiddleware):
    """Add request ID to all requests for tracing"""
    
    async def dispatch(self, request: Request, call_next):
        # Get or generate request ID
        request_id = request.headers.get("X-Request-ID")
        if not request_id:
            request_id = f"req_{uuid.uuid4().hex[:16]}"
        
        # Store in request state
        request.state.request_id = request_id
        
        # Process request
        response = await call_next(request)
        
        # Add request ID to response headers
        response.headers["X-Request-ID"] = request_id
        
        return response


class ErrorHandlerMiddleware(BaseHTTPMiddleware):
    """Handle errors and return contract-compliant error responses"""
    
    async def dispatch(self, request: Request, call_next):
        try:
            response = await call_next(request)
            return response
            
        except Exception as e:
            logger.error(f"Unhandled error: {e}", exc_info=True)
            
            # Get request ID
            request_id = getattr(request.state, "request_id", None)
            
            # Create contract-compliant error
            error = create_contract_compliant_error(
                service="pattern-mining",
                error_type="internal",
                message=f"Internal server error: {str(e)}",
                retryable=True,
                user_message="An unexpected error occurred. Please try again later.",
                correlation_id=request_id,
                context={
                    "path": str(request.url.path),
                    "method": request.method,
                }
            )
            
            return JSONResponse(
                status_code=500,
                content=error.dict(),
                headers={"X-Request-ID": request_id} if request_id else {}
            )


class PerformanceTrackingMiddleware(BaseHTTPMiddleware):
    """Track request performance metrics"""
    
    def __init__(self, app: ASGIApp, service_name: str = "pattern-mining"):
        super().__init__(app)
        self.service_name = service_name
        self.performance_metrics: Dict[str, Any] = {}
    
    async def dispatch(self, request: Request, call_next):
        # Skip health check endpoints
        if request.url.path in ["/health", "/ready", "/api/v1/health", "/api/v1/ready"]:
            return await call_next(request)
        
        # Start timing
        start_time = time.time()
        
        # Add performance tracking headers
        request.state.start_time = start_time
        
        # Process request
        response = await call_next(request)
        
        # Calculate response time
        response_time = (time.time() - start_time) * 1000  # Convert to milliseconds
        
        # Add performance headers
        response.headers["X-Response-Time"] = f"{response_time:.2f}ms"
        response.headers["X-Service-Name"] = self.service_name
        response.headers["X-Service-Version"] = "1.0.0"
        
        # Log slow requests
        if response_time > 1000:  # 1 second
            logger.warning(
                f"Slow request: {request.method} {request.url.path} "
                f"took {response_time:.2f}ms"
            )
        
        # Update metrics (in-memory for now)
        endpoint = f"{request.method} {request.url.path}"
        if endpoint not in self.performance_metrics:
            self.performance_metrics[endpoint] = {
                "count": 0,
                "total_time": 0,
                "min_time": float('inf'),
                "max_time": 0,
                "errors": 0,
            }
        
        metrics = self.performance_metrics[endpoint]
        metrics["count"] += 1
        metrics["total_time"] += response_time
        metrics["min_time"] = min(metrics["min_time"], response_time)
        metrics["max_time"] = max(metrics["max_time"], response_time)
        
        if response.status_code >= 400:
            metrics["errors"] += 1
        
        return response


class ContractValidationMiddleware(BaseHTTPMiddleware):
    """Validate requests against contract schemas"""
    
    async def dispatch(self, request: Request, call_next):
        # Only validate pattern detection endpoint
        if request.url.path == "/api/v1/patterns/detect" and request.method == "POST":
            try:
                # The actual validation happens in the endpoint handler
                # This middleware just ensures proper error formatting
                response = await call_next(request)
                
                # Check if response is an error
                if response.status_code >= 400:
                    # Ensure error response follows contract format
                    # (This is handled by the error handler middleware)
                    pass
                
                return response
                
            except Exception as e:
                logger.error(f"Contract validation error: {e}")
                
                # Create contract-compliant validation error
                error = create_contract_compliant_error(
                    service="pattern-mining",
                    error_type="validation",
                    message=f"Request validation failed: {str(e)}",
                    retryable=False,
                    user_message="Invalid request format. Please check the API documentation.",
                    correlation_id=getattr(request.state, "request_id", None),
                    context={
                        "path": str(request.url.path),
                        "method": request.method,
                        "validation_error": str(e),
                    }
                )
                
                return JSONResponse(
                    status_code=422,
                    content=error.dict()
                )
        
        # Pass through for other endpoints
        return await call_next(request)


# Convenience functions for adding middleware

def request_id_middleware(app: ASGIApp) -> RequestIDMiddleware:
    """Create request ID middleware"""
    return RequestIDMiddleware(app)


def error_handler_middleware(app: ASGIApp) -> ErrorHandlerMiddleware:
    """Create error handler middleware"""
    return ErrorHandlerMiddleware(app)


def performance_tracking_middleware(app: ASGIApp, service_name: str = "pattern-mining") -> PerformanceTrackingMiddleware:
    """Create performance tracking middleware"""
    return PerformanceTrackingMiddleware(app, service_name)


def contract_validation_middleware(app: ASGIApp) -> ContractValidationMiddleware:
    """Create contract validation middleware"""
    return ContractValidationMiddleware(app)