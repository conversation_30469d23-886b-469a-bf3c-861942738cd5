"""
Pattern Mining Service - API Models

Additional API models for request/response handling.

Wave 2.5: CCL Contract Compliance Implementation - Phase 3
"""

from typing import Dict, Any, Optional, List
from datetime import datetime
from pydantic import BaseModel, Field

# Re-export contract models
from ..contracts.models import (
    PatternInputV1 as PatternDetectionRequest,
    PatternOutputV1 as PatternDetectionResponse,
    ErrorResponseV1 as ErrorResponse,
)


class ValidationErrorResponse(BaseModel):
    """Validation error response for 422 errors"""
    error_id: str = Field(..., description="Unique error identifier")
    service: str = Field(default="pattern-mining", description="Service name")
    error_type: str = Field(default="validation", description="Error type")
    message: str = Field(..., description="Error message")
    retryable: bool = Field(default=False, description="Whether error is retryable")
    user_message: str = Field(..., description="User-friendly error message")
    correlation_id: Optional[str] = Field(None, description="Request correlation ID")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Error timestamp")
    validation_errors: List[Dict[str, Any]] = Field(..., description="Detailed validation errors")
    context: Optional[Dict[str, Any]] = Field(None, description="Additional context")


class HealthCheckResponse(BaseModel):
    """Health check response model"""
    status: str = Field(..., description="Health status (healthy/unhealthy/degraded)")
    timestamp: datetime = Field(..., description="Check timestamp")
    version: str = Field(..., description="Service version")
    uptime_seconds: float = Field(..., description="Service uptime in seconds")
    checks: Dict[str, Dict[str, Any]] = Field(..., description="Individual health checks")


class ReadinessCheckResponse(BaseModel):
    """Readiness check response model"""
    ready: bool = Field(..., description="Service readiness status")
    timestamp: datetime = Field(..., description="Check timestamp")
    checks: Dict[str, bool] = Field(..., description="Individual readiness checks")
    message: Optional[str] = Field(None, description="Readiness message")


class PerformanceMetrics(BaseModel):
    """Performance metrics response"""
    endpoint: str = Field(..., description="API endpoint")
    method: str = Field(..., description="HTTP method")
    count: int = Field(..., description="Total request count")
    avg_response_time_ms: float = Field(..., description="Average response time in milliseconds")
    min_response_time_ms: float = Field(..., description="Minimum response time in milliseconds")
    max_response_time_ms: float = Field(..., description="Maximum response time in milliseconds")
    error_rate: float = Field(..., description="Error rate (0.0-1.0)")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Metrics timestamp")


class ServiceInfo(BaseModel):
    """Service information response"""
    name: str = Field(default="pattern-mining", description="Service name")
    version: str = Field(..., description="Service version")
    description: str = Field(..., description="Service description")
    contract_version: str = Field(..., description="CCL contract version")
    supported_pattern_types: List[str] = Field(..., description="Supported pattern types")
    supported_languages: List[str] = Field(..., description="Supported programming languages")
    api_endpoints: List[str] = Field(..., description="Available API endpoints")


# Alias for backward compatibility
PatternExtractionRequest = PatternDetectionRequest
PatternExtractionResponse = PatternDetectionResponse