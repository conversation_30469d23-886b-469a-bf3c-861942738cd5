"""
Machine Learning Routes

API endpoints for ML model management and training operations.
"""

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from typing import List, Dict, Any, Optional
import asyncio
from uuid import uuid4

from ...config.settings import get_settings, Settings
from ...models.ml import (
    ModelTrainingRequest,
    ModelTrainingResponse,
    ModelInfo,
    TrainingJob,
    EvaluationMetrics
)
from ...ml.manager import get_ml_manager
from ...ml.trainer import get_model_trainer

router = APIRouter()


@router.get("/models", response_model=List[ModelInfo])
async def list_models() -> List[ModelInfo]:
    """List all available ML models."""
    ml_manager = get_ml_manager()
    return await ml_manager.list_models()


@router.get("/models/{model_id}", response_model=ModelInfo)
async def get_model_info(model_id: str) -> ModelInfo:
    """Get detailed information about a specific model."""
    ml_manager = get_ml_manager()
    model_info = await ml_manager.get_model_info(model_id)
    
    if not model_info:
        raise HTTPException(
            status_code=404,
            detail=f"Model {model_id} not found"
        )
    
    return model_info


@router.post("/models/{model_id}/train", response_model=ModelTrainingResponse)
async def train_model(
    model_id: str,
    request: ModelTrainingRequest,
    background_tasks: BackgroundTasks,
    settings: Settings = Depends(get_settings)
) -> ModelTrainingResponse:
    """Start training a model with new data."""
    # Validate training request
    if not request.training_data:
        raise HTTPException(
            status_code=400,
            detail="Training data is required"
        )
    
    # Generate training job ID
    job_id = str(uuid4())
    
    # Get trainer
    trainer = get_model_trainer()
    
    # Schedule background training
    background_tasks.add_task(
        run_model_training,
        job_id,
        model_id,
        request
    )
    
    return ModelTrainingResponse(
        job_id=job_id,
        model_id=model_id,
        status="queued",
        estimated_duration="30-60 minutes"
    )


@router.get("/training/{job_id}", response_model=TrainingJob)
async def get_training_job(job_id: str) -> TrainingJob:
    """Get the status and progress of a training job."""
    # This would query the training job status from database
    # Placeholder implementation
    return TrainingJob(
        job_id=job_id,
        model_id="pattern-detector-v1",
        status="running",
        progress=45,
        metrics=EvaluationMetrics(
            accuracy=0.89,
            precision=0.87,
            recall=0.91,
            f1_score=0.89
        ),
        created_at="2025-07-09T00:00:00Z",
        updated_at="2025-07-09T00:00:00Z"
    )


@router.post("/models/{model_id}/evaluate")
async def evaluate_model(
    model_id: str,
    test_data: Dict[str, Any]
) -> EvaluationMetrics:
    """Evaluate a model against test data."""
    ml_manager = get_ml_manager()
    
    try:
        metrics = await ml_manager.evaluate_model(model_id, test_data)
        return metrics
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Model evaluation failed: {str(e)}"
        )


@router.post("/models/{model_id}/predict")
async def predict_with_model(
    model_id: str,
    input_data: Dict[str, Any]
) -> Dict[str, Any]:
    """Make predictions using a specific model."""
    ml_manager = get_ml_manager()
    
    try:
        prediction = await ml_manager.predict(model_id, input_data)
        return {
            "model_id": model_id,
            "prediction": prediction,
            "confidence": 0.92  # Would be calculated
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Prediction failed: {str(e)}"
        )


@router.delete("/models/{model_id}")
async def delete_model(model_id: str) -> Dict[str, str]:
    """Delete a model."""
    ml_manager = get_ml_manager()
    
    try:
        await ml_manager.delete_model(model_id)
        return {"message": f"Model {model_id} deleted successfully"}
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Model deletion failed: {str(e)}"
        )


async def run_model_training(
    job_id: str,
    model_id: str,
    request: ModelTrainingRequest
) -> None:
    """Background task for model training."""
    # Implement model training logic
    trainer = get_model_trainer()
    await trainer.train_model(job_id, model_id, request)