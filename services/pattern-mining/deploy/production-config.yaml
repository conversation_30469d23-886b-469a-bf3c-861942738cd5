# Pattern Mining Service Production Configuration
# Version: 2.0.0
# Environment: Production
# Last Updated: July 2025

# Service Configuration
service:
  name: pattern-mining
  environment: production
  version: 2.0.0
  
# Google Cloud Configuration
gcp:
  project_id: vibe-match-463114
  region: us-central1
  zone: us-central1-a
  
# Cloud Run Configuration
cloud_run:
  service_name: pattern-mining
  platform: managed
  
  # Resource Allocation
  resources:
    cpu: "2"
    memory: "4Gi"
    
  # Scaling Configuration
  scaling:
    min_instances: 2
    max_instances: 50
    
  # Concurrency
  concurrency:
    max_requests_per_instance: 100
    
  # Timeout
  timeout_seconds: 300
  
  # Service Account
  service_account: <EMAIL>
  
  # Networking
  ingress: all
  vpc_connector: pattern-mining-connector
  
# Environment Variables
environment_variables:
  # Application Settings
  ENVIRONMENT: production
  LOG_LEVEL: INFO
  DEBUG: "false"
  
  # Worker Configuration
  WORKERS: "4"
  MAX_WORKERS: "8"
  WORKER_CONNECTIONS: "1000"
  TIMEOUT: "120"
  KEEPALIVE: "5"
  GRACEFUL_TIMEOUT: "30"
  
  # Feature Flags
  PRELOAD_MODELS: "true"
  GPU_ENABLED: "false"
  DISTRIBUTED_PROCESSING: "true"
  
  # Monitoring
  PROMETHEUS_ENABLED: "true"
  TRACING_ENABLED: "true"
  METRICS_PORT: "8001"
  
  # Security
  SECURE_COOKIES: "true"
  CORS_ALLOW_ORIGINS: "https://pattern-mining.ccl-platform.com"
  
  # Rate Limiting
  RATE_LIMIT_ENABLED: "true"
  RATE_LIMIT_REQUESTS: "100"
  RATE_LIMIT_PERIOD: "60"
  
  # Cache Configuration
  CACHE_ENABLED: "true"
  CACHE_TTL: "3600"
  
  # Database
  DATABASE_POOL_SIZE: "20"
  DATABASE_MAX_OVERFLOW: "10"
  DATABASE_POOL_TIMEOUT: "30"
  
  # Redis
  REDIS_POOL_SIZE: "10"
  REDIS_DECODE_RESPONSES: "true"
  
# Secret References
secrets:
  # Authentication
  - name: JWT_SECRET
    key: jwt-secret
    version: latest
    
  # API Keys
  - name: GEMINI_API_KEY
    key: gemini-api-key
    version: latest
    
  # Database
  - name: DATABASE_PASSWORD
    key: database-password
    version: latest
    
  # Redis
  - name: REDIS_PASSWORD
    key: redis-password
    version: latest
    
  # Google Cloud
  - name: GOOGLE_APPLICATION_CREDENTIALS_JSON
    key: gcp-service-account-key
    version: latest
    
# Health Checks
health_checks:
  liveness:
    path: /health
    initial_delay_seconds: 60
    period_seconds: 30
    timeout_seconds: 10
    failure_threshold: 3
    
  readiness:
    path: /ready
    initial_delay_seconds: 30
    period_seconds: 10
    timeout_seconds: 5
    failure_threshold: 3
    
# Monitoring Configuration
monitoring:
  # Uptime Checks
  uptime_checks:
    - name: pattern-mining-health
      path: /health
      interval: 60s
      regions:
        - USA
        - EUROPE
        - ASIA_PACIFIC
        
  # Alert Policies
  alert_policies:
    - name: high-error-rate
      condition: error_rate > 5%
      duration: 5m
      
    - name: high-latency
      condition: response_time_p95 > 1000ms
      duration: 5m
      
    - name: low-availability
      condition: uptime < 99.5%
      duration: 10m
      
    - name: high-memory-usage
      condition: memory_usage > 80%
      duration: 5m
      
# Security Configuration
security:
  # TLS Configuration
  tls:
    min_version: "1.2"
    
  # Authentication
  authentication:
    jwt_algorithm: HS256
    jwt_expiry: 3600
    
  # Rate Limiting
  rate_limiting:
    global:
      requests_per_minute: 1000
      burst: 100
      
    per_ip:
      requests_per_minute: 100
      burst: 20
      
  # WAF Rules
  waf_rules:
    - sql_injection_protection: enabled
    - xss_protection: enabled
    - path_traversal_protection: enabled
    
# Backup Configuration
backup:
  # Database Backups
  database:
    enabled: true
    schedule: "0 2 * * *"  # 2 AM daily
    retention_days: 30
    
  # Log Retention
  logs:
    retention_days: 90
    
# Deployment Strategy
deployment:
  strategy: rolling
  
  # Canary Configuration
  canary:
    enabled: true
    initial_traffic_percent: 10
    increment: 10
    interval: 5m
    
  # Rollback Configuration
  rollback:
    automatic: true
    error_threshold: 10%
    latency_threshold: 2000ms
    
# Cost Optimization
cost_optimization:
  # Auto-scaling based on traffic
  auto_scaling:
    target_cpu_utilization: 70
    target_memory_utilization: 80
    
  # Scheduled scaling
  scheduled_scaling:
    - schedule: "0 8 * * 1-5"  # Weekdays 8 AM
      min_instances: 5
      max_instances: 50
      
    - schedule: "0 20 * * 1-5"  # Weekdays 8 PM
      min_instances: 2
      max_instances: 20
      
    - schedule: "0 0 * * 0,6"  # Weekends
      min_instances: 1
      max_instances: 10
      
# Compliance
compliance:
  # Data Residency
  data_residency: US
  
  # Encryption
  encryption:
    at_rest: enabled
    in_transit: enabled
    
  # Audit Logging
  audit_logging:
    enabled: true
    retention_days: 365
    
# Performance Targets
performance_targets:
  # Response Time
  response_time:
    p50: 100ms
    p95: 500ms
    p99: 1000ms
    
  # Throughput
  throughput:
    target: 1000 req/s
    
  # Availability
  availability:
    target: 99.9%
    
# Dependencies
dependencies:
  # External Services
  external_services:
    - name: Google BigQuery
      required: true
      
    - name: Google Cloud Storage
      required: true
      
    - name: Redis
      required: true
      
    - name: Gemini API
      required: true
      
  # Internal Services
  internal_services:
    - name: analysis-engine
      required: false
      
    - name: repository-indexer
      required: false