# Redis Configuration for Integration Testing
# Optimized for test performance and reliability

# Basic configuration
port 6379
bind 0.0.0.0
protected-mode no
timeout 0
tcp-keepalive 300

# Memory configuration for testing
maxmemory 128mb
maxmemory-policy allkeys-lru

# Persistence (disabled for testing)
save ""
appendonly no

# Logging
loglevel notice
logfile ""

# Database configuration
databases 16

# Network and connection limits
tcp-backlog 511
maxclients 100

# Performance optimizations for tests
hz 10
dynamic-hz yes

# Security (minimal for testing)
# requirepass test_password

# Slow log for debugging
slowlog-log-slower-than 10000
slowlog-max-len 128

# Latency monitoring
latency-monitor-threshold 100

# Client output buffer limits
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60

# Hash configuration
hash-max-ziplist-entries 512
hash-max-ziplist-value 64

# List configuration
list-max-ziplist-size -2
list-compress-depth 0

# Set configuration
set-max-intset-entries 512

# Sorted set configuration
zset-max-ziplist-entries 128
zset-max-ziplist-value 64

# Streams configuration
stream-node-max-bytes 4096
stream-node-max-entries 100

# Active rehashing
activerehashing yes

# Jemalloc background thread
jemalloc-bg-thread yes

# Threading (if supported)
# io-threads 2
# io-threads-do-reads yes

# Test-specific settings
replica-read-only no
stop-writes-on-bgsave-error no

# Disable some features for faster testing
rdbcompression no
rdbchecksum no

# Module loading (if needed)
# loadmodule /path/to/redis-module.so