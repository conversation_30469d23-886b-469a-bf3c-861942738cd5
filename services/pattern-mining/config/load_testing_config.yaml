# Phase 2 Load Testing Configuration
# Comprehensive load testing scenarios for Pattern Mining service

# =============================================================================
# TESTING PROFILES
# =============================================================================

testing_profiles:
  # Development testing - Quick validation
  development:
    max_users: 10
    duration_minutes: 2
    ramp_up_seconds: 30
    think_time_range: [1, 3]
    memory_monitoring: basic
    scenarios:
      - name: basic_load
        weight: 80
        pattern: steady
      - name: spike_test
        weight: 20
        pattern: burst

  # Staging testing - Production-like but shorter
  staging:
    max_users: 50
    duration_minutes: 15
    ramp_up_seconds: 120
    think_time_range: [2, 8]
    memory_monitoring: detailed
    scenarios:
      - name: concurrent_users
        weight: 60
        pattern: gradual_ramp
      - name: realistic_workload
        weight: 30
        pattern: mixed_load
      - name: sustained_load
        weight: 10
        pattern: constant

  # Production testing - Full enterprise load
  production:
    max_users: 200
    duration_minutes: 240  # 4 hours
    ramp_up_seconds: 600   # 10 minutes
    think_time_range: [5, 30]
    memory_monitoring: comprehensive
    scenarios:
      - name: enterprise_workload
        weight: 50
        pattern: business_hours
      - name: batch_processing
        weight: 25
        pattern: scheduled_peaks
      - name: developer_teams
        weight: 20
        pattern: collaboration_bursts
      - name: stress_limits
        weight: 5
        pattern: breaking_point

  # Extended endurance testing
  endurance:
    max_users: 100
    duration_minutes: 1440  # 24 hours
    ramp_up_seconds: 1800   # 30 minutes
    think_time_range: [10, 60]
    memory_monitoring: memory_leak_detection
    scenarios:
      - name: sustained_operations
        weight: 90
        pattern: steady_state
      - name: periodic_spikes
        weight: 10
        pattern: daily_cycles

# =============================================================================
# USER BEHAVIOR PATTERNS
# =============================================================================

user_behaviors:
  # Developer persona - Code analysis focused
  developer:
    weight: 40
    session_duration_minutes: [30, 120]
    actions:
      - action: analyze_repository
        frequency: 0.4
        complexity: medium
      - action: search_patterns
        frequency: 0.3
        complexity: low
      - action: generate_reports
        frequency: 0.2
        complexity: high
      - action: review_insights
        frequency: 0.1
        complexity: low
    geographic_distribution:
      us_west: 0.3
      us_east: 0.25
      europe: 0.25
      asia: 0.2

  # Architect persona - System design focused
  architect:
    weight: 25
    session_duration_minutes: [60, 240]
    actions:
      - action: architectural_analysis
        frequency: 0.5
        complexity: high
      - action: pattern_discovery
        frequency: 0.3
        complexity: high
      - action: compliance_checks
        frequency: 0.2
        complexity: medium
    geographic_distribution:
      us_west: 0.4
      us_east: 0.3
      europe: 0.2
      asia: 0.1

  # QA Engineer persona - Quality assurance focused
  qa_engineer:
    weight: 20
    session_duration_minutes: [45, 180]
    actions:
      - action: quality_analysis
        frequency: 0.4
        complexity: medium
      - action: defect_detection
        frequency: 0.35
        complexity: medium
      - action: compliance_validation
        frequency: 0.25
        complexity: high
    geographic_distribution:
      us_west: 0.25
      us_east: 0.35
      europe: 0.25
      asia: 0.15

  # Researcher persona - Deep analysis focused
  researcher:
    weight: 15
    session_duration_minutes: [90, 300]
    actions:
      - action: deep_analysis
        frequency: 0.6
        complexity: very_high
      - action: trend_analysis
        frequency: 0.25
        complexity: high
      - action: experimental_queries
        frequency: 0.15
        complexity: very_high
    geographic_distribution:
      us_west: 0.2
      us_east: 0.2
      europe: 0.35
      asia: 0.25

# =============================================================================
# WORKLOAD SCENARIOS
# =============================================================================

workload_scenarios:
  # Development team collaboration
  development_team:
    description: "Simulates daily development team usage"
    concurrent_users: [10, 50]
    duration_hours: 8
    peak_hours: [9, 11, 14, 16]  # Typical collaboration peaks
    operations:
      - type: repository_analysis
        frequency: 0.35
        batch_size: [1, 5]
      - type: code_review_support
        frequency: 0.25
        batch_size: [1, 3]
      - type: pattern_search
        frequency: 0.25
        batch_size: [1, 10]
      - type: report_generation
        frequency: 0.15
        batch_size: [1, 2]

  # CI/CD pipeline integration
  cicd_pipeline:
    description: "Automated CI/CD analysis requests"
    concurrent_users: [5, 25] 
    duration_hours: 24
    peak_hours: [8, 12, 18, 22]  # Build deployment times
    operations:
      - type: automated_analysis
        frequency: 0.5
        batch_size: [1, 1]  # Single repo per pipeline
      - type: quality_gates
        frequency: 0.3
        batch_size: [1, 1]
      - type: compliance_checks
        frequency: 0.2
        batch_size: [1, 1]

  # Enterprise audit workload
  enterprise_audit:
    description: "Large-scale enterprise audit operations"
    concurrent_users: [2, 10]
    duration_hours: 6
    operations:
      - type: bulk_repository_analysis
        frequency: 0.4
        batch_size: [10, 100]
      - type: comprehensive_reporting
        frequency: 0.3
        batch_size: [1, 5]
      - type: compliance_assessment
        frequency: 0.3
        batch_size: [5, 50]

  # Batch processing simulation
  batch_processing:
    description: "Scheduled batch analysis operations"
    concurrent_users: [1, 5]
    duration_hours: 4
    operations:
      - type: nightly_analysis
        frequency: 0.6
        batch_size: [50, 200]
      - type: weekly_reports
        frequency: 0.25
        batch_size: [10, 30]
      - type: trend_analysis
        frequency: 0.15
        batch_size: [100, 500]

# =============================================================================
# PERFORMANCE THRESHOLDS
# =============================================================================

performance_thresholds:
  # Response time SLAs (milliseconds)
  response_times:
    analyze_repository:
      p50: 2000   # 50th percentile
      p95: 8000   # 95th percentile
      p99: 15000  # 99th percentile
    search_patterns:
      p50: 500
      p95: 2000
      p99: 5000
    generate_reports:
      p50: 5000
      p95: 20000
      p99: 45000
    quality_analysis:
      p50: 3000
      p95: 12000
      p99: 25000

  # Throughput requirements (requests per second)
  throughput:
    minimum_rps: 10
    target_rps: 50
    maximum_rps: 200

  # Error rate thresholds (percentage)
  error_rates:
    warning_threshold: 1.0   # 1%
    critical_threshold: 5.0  # 5%
    failure_threshold: 10.0  # 10%

  # Resource utilization limits
  resources:
    cpu_warning: 70    # 70%
    cpu_critical: 85   # 85%
    memory_warning: 80 # 80%
    memory_critical: 90# 90%
    disk_warning: 75   # 75%
    disk_critical: 85  # 85%

  # Memory leak detection thresholds
  memory_leak:
    growth_rate_mb_per_hour: 50    # 50MB/hour growth rate threshold
    total_growth_mb: 500           # 500MB total growth threshold
    gc_efficiency_threshold: 0.8   # Garbage collection efficiency
    heap_fragmentation_threshold: 0.3  # Heap fragmentation ratio

# =============================================================================
# MONITORING CONFIGURATION
# =============================================================================

monitoring:
  # Metrics collection intervals
  collection_intervals:
    basic_metrics: 5      # seconds
    detailed_metrics: 15  # seconds
    memory_profiling: 30  # seconds
    gc_monitoring: 60     # seconds

  # Data retention periods
  retention_periods:
    raw_metrics: 24       # hours
    aggregated_data: 168  # hours (1 week)
    summary_reports: 8760 # hours (1 year)

  # Alert configurations
  alerts:
    response_time_degradation: 20  # % increase threshold
    error_rate_spike: 5           # % absolute increase
    memory_leak_detection: true
    performance_regression: true
    availability_monitoring: true

# =============================================================================
# TEST DATA CONFIGURATION
# =============================================================================

test_data:
  # Repository simulation settings
  repositories:
    small_repo:
      files: [10, 100]
      lines_of_code: [1000, 10000]
      languages: ["python", "javascript", "java"]
    medium_repo: 
      files: [100, 1000]
      lines_of_code: [10000, 100000]
      languages: ["python", "javascript", "java", "go", "rust"]
    large_repo:
      files: [1000, 10000]
      lines_of_code: [100000, 1000000]
      languages: ["python", "javascript", "java", "go", "rust", "cpp", "c#"]

  # Content generation settings
  code_patterns:
    complexity_levels: ["simple", "moderate", "complex", "very_complex"]
    pattern_types: ["design_patterns", "antipatterns", "security_issues", "performance_issues"]
    defect_injection_rate: 0.15  # 15% of generated code has intentional issues

# =============================================================================
# INFRASTRUCTURE CONFIGURATION
# =============================================================================

infrastructure:
  # Container resource limits for testing
  container_limits:
    cpu_cores: 4
    memory_gb: 8
    disk_gb: 20

  # Network simulation settings
  network_conditions:
    fast_connection:
      latency_ms: [10, 50]
      bandwidth_mbps: [100, 1000]
    typical_connection:
      latency_ms: [50, 150]
      bandwidth_mbps: [10, 100]
    slow_connection:
      latency_ms: [150, 500]
      bandwidth_mbps: [1, 10]

  # Geographic distribution simulation
  geographic_regions:
    us_west:
      latency_base_ms: 20
      weight: 0.3
    us_east:
      latency_base_ms: 50
      weight: 0.25
    europe:
      latency_base_ms: 100
      weight: 0.25
    asia:
      latency_base_ms: 150
      weight: 0.2

# =============================================================================
# REPORTING CONFIGURATION
# =============================================================================

reporting:
  # Report generation settings
  formats: ["json", "html", "csv", "pdf"]
  
  # Content sections to include
  sections:
    executive_summary: true
    performance_metrics: true
    user_behavior_analysis: true
    resource_utilization: true
    error_analysis: true
    recommendations: true
    raw_data: false  # Exclude from default reports

  # Visualization settings
  charts:
    response_time_trends: true
    throughput_analysis: true
    error_rate_tracking: true
    memory_usage_patterns: true
    user_journey_maps: true

# =============================================================================
# VALIDATION RULES
# =============================================================================

validation:
  # Pre-test validation
  pre_test:
    verify_service_health: true
    check_dependencies: true
    validate_test_data: true
    confirm_resource_availability: true

  # During test validation
  runtime:
    monitor_service_stability: true
    track_error_patterns: true
    detect_performance_anomalies: true
    validate_data_consistency: true

  # Post-test validation
  post_test:
    verify_service_recovery: true
    check_data_integrity: true
    validate_cleanup: true
    generate_insights: true

# =============================================================================
# ENVIRONMENT-SPECIFIC OVERRIDES
# =============================================================================

environments:
  docker_compose:
    max_users_override: 20
    duration_override_minutes: 10
    resource_monitoring: basic
    
  kubernetes:
    scaling_enabled: true
    resource_quotas: true
    monitoring_enhanced: true
    
  cloud_run:
    cold_start_simulation: true
    auto_scaling_validation: true
    regional_distribution: true