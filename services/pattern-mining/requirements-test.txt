# Phase 2 Advanced Integration Testing Requirements
# Comprehensive testing dependencies for multi-service integration testing

# Core testing framework
pytest>=8.0.0
pytest-asyncio>=0.23.0
pytest-mock>=3.12.0
pytest-timeout>=2.2.0
pytest-xdist>=3.5.0
pytest-sugar>=0.9.7
pytest-clarity>=1.0.1

# Test reporting and coverage
pytest-html>=4.1.1
pytest-cov>=4.0.0
pytest-benchmark>=4.0.0
coverage[toml]>=7.4.0

# HTTP and API testing
httpx>=0.26.0
requests>=2.31.0
aiohttp>=3.9.0
responses>=0.24.0

# Database testing
asyncpg>=0.29.0
psycopg2-binary>=2.9.9
sqlalchemy[asyncio]>=2.0.25
alembic>=1.13.0

# Cache testing
redis[hiredis]>=5.0.1
fakeredis>=2.20.1

# Performance and load testing
locust>=2.20.0
memory-profiler>=0.61.0
psutil>=5.9.7
py-spy>=0.3.14

# Monitoring and metrics
prometheus-client>=0.19.0
grafana-api>=1.0.3

# Mock and simulation
wiremock>=2.6.0
testcontainers>=3.7.1
docker>=7.0.0

# Network and chaos testing
toxiproxy-python>=0.2.1
netifaces>=0.11.0
pyroute2>=0.7.12

# Data validation and manipulation
pydantic>=2.5.0
jsonschema>=4.20.0
faker>=22.0.0
factory-boy>=3.3.0

# Utility libraries
tenacity>=8.2.3
backoff>=2.2.1
structlog>=23.2.0
colorama>=0.4.6
click>=8.1.7
tabulate>=0.9.0

# Development and debugging
ipdb>=0.13.13
pytest-pdb>=0.2.0
pytest-pudb>=0.7.0

# Async testing utilities
pytest-asyncio-cooperative>=0.31.0
asynctest>=0.13.0
aioresponses>=0.7.6

# Time and date testing
freezegun>=1.4.0
time-machine>=2.13.0

# File and I/O testing
pyfakefs>=5.3.4
pytest-datafiles>=3.0.0

# Configuration testing
pytest-env>=1.1.3
python-dotenv>=1.0.0

# Logging testing
pytest-logging>=2016.11.4
loguru>=0.7.2

# Security testing
bandit[toml]>=1.7.5
safety>=2.3.5

# Performance profiling
line-profiler>=4.1.1
scalene>=1.5.26

# Additional test utilities
hypothesis>=6.92.0
model-bakery>=1.17.0
mimesis>=12.1.0
pytest-lazy-fixture>=0.6.3
pytest-subtests>=0.11.0
pytest-parametrize-plus>=1.4.0