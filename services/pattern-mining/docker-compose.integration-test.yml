# Docker Compose for Integration Testing
# Isolated environment for running integration tests with real services

version: '3.8'

services:
  # PostgreSQL Test Database
  postgres-test:
    image: postgres:16-alpine
    container_name: pattern-mining-postgres-test
    environment:
      POSTGRES_DB: pattern_mining_test
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: INSECURE_TEST_PASSWORD
      POSTGRES_INITDB_ARGS: "--auth-host=scram-sha-256"
    ports:
      - "5433:5432"  # Use different port to avoid conflicts
    volumes:
      - postgres_test_data:/var/lib/postgresql/data
      - ./scripts/postgres/init_test.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - integration-test-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d pattern_mining_test"]
      interval: 5s
      timeout: 5s
      retries: 10
      start_period: 10s
    command: >
      postgres
      -c shared_preload_libraries=pg_stat_statements
      -c pg_stat_statements.max=10000
      -c pg_stat_statements.track=all
      -c max_connections=100
      -c shared_buffers=128MB
      -c effective_cache_size=512MB
      -c work_mem=2MB
      -c maintenance_work_mem=32MB
      -c log_statement=all
      -c log_duration=on
      -c log_min_duration_statement=50ms

  # Redis Test Cache
  redis-test:
    image: redis:7-alpine
    container_name: pattern-mining-redis-test
    ports:
      - "6380:6379"  # Use different port to avoid conflicts
    volumes:
      - redis_test_data:/data
      - ./config/redis-test.conf:/usr/local/etc/redis/redis.conf:ro
    networks:
      - integration-test-network
    restart: unless-stopped
    command: redis-server /usr/local/etc/redis/redis.conf
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 3s
      retries: 10
      start_period: 5s

  # Integration Test Runner
  integration-test-runner:
    build:
      context: .
      dockerfile: Dockerfile.prod
      target: development
    image: pattern-mining:integration-test
    container_name: pattern-mining-integration-test-runner
    environment:
      # Test Environment Configuration
      ENVIRONMENT: integration_test
      LOG_LEVEL: DEBUG
      DEBUG: "true"
      
      # Test Database Configuration
      TEST_DATABASE_URL: ***************************************************************/pattern_mining_test
      TEST_REDIS_URL: redis://redis-test:6379/0
      
      # Test-specific Environment Variables
      GEMINI_API_KEY_TEST: ${GEMINI_API_KEY_TEST:-}
      
      # Test Configuration
      PYTEST_TIMEOUT: 300
      PYTEST_MARKERS: integration
      PYTEST_VERBOSE: "true"
      PYTEST_COVERAGE: "true"
      
      # Service Configuration for Tests
      API_BASE_URL: http://localhost:8000
      TEST_CONCURRENCY: 5
      TEST_TIMEOUT_SECONDS: 60
      
      # Performance Test Configuration
      PERFORMANCE_TEST_DURATION: 30
      PERFORMANCE_MAX_REQUESTS: 100
      PERFORMANCE_CONCURRENT_USERS: 10
      
      # Cache Configuration for Tests
      CACHE_TTL: 30
      CACHE_SIZE: 100
      
      # Rate Limiting for Tests (more lenient)
      RATE_LIMIT_REQUESTS: 100
      RATE_LIMIT_WINDOW: 60
    volumes:
      - ./src:/app/src:ro
      - ./tests:/app/tests:ro
      - ./config:/app/config:ro
      - ./coverage:/app/coverage
      - ./test-results:/app/test-results
      - ./logs:/app/logs
    depends_on:
      postgres-test:
        condition: service_healthy
      redis-test:
        condition: service_healthy
    networks:
      - integration-test-network
    profiles:
      - integration-test
    working_dir: /app
    command: >
      sh -c "
        echo 'Starting integration tests...' &&
        pip install pytest pytest-cov pytest-asyncio pytest-mock pytest-timeout pytest-xdist &&
        pytest tests/integration/ 
          -v 
          --tb=short
          --cov=pattern_mining 
          --cov-report=html:/app/coverage/html 
          --cov-report=term
          --cov-report=xml:/app/coverage/coverage.xml
          --junit-xml=/app/test-results/integration-results.xml
          --timeout=300
          --durations=10
          -m integration
          --maxfail=5
          --capture=no
      "

  # Lightweight API Server for Testing
  api-test-server:
    build:
      context: .
      dockerfile: Dockerfile.prod
      target: development
    image: pattern-mining:api-test
    container_name: pattern-mining-api-test-server
    environment:
      # API Test Server Configuration
      ENVIRONMENT: integration_test
      LOG_LEVEL: INFO
      DEBUG: "false"
      
      # Database Configuration
      DATABASE_URL: ***************************************************************/pattern_mining_test
      REDIS_URL: redis://redis-test:6379/1
      
      # API Configuration
      HOST: "0.0.0.0"
      PORT: 8000
      WORKERS: 1
      
      # Test API Key (if needed)
      GEMINI_API_KEY: ${GEMINI_API_KEY_TEST:-}
      
      # Disable heavy features for testing
      PRELOAD_MODELS: "false"
      ENABLE_MONITORING: "false"
      ENABLE_TRACING: "false"
    ports:
      - "8001:8000"  # Use different port for test API
    volumes:
      - ./src:/app/src:ro
      - ./config:/app/config:ro
      - ./logs:/app/logs
    depends_on:
      postgres-test:
        condition: service_healthy
      redis-test:
        condition: service_healthy
    networks:
      - integration-test-network
    profiles:
      - api-test
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    command: >
      sh -c "
        echo 'Starting test API server...' &&
        python -m uvicorn pattern_mining.api.main:app 
          --host 0.0.0.0 
          --port 8000 
          --log-level info
          --access-log
      "

  # Test Data Generator
  test-data-generator:
    build:
      context: .
      dockerfile: Dockerfile.prod
      target: development
    image: pattern-mining:test-data
    container_name: pattern-mining-test-data-generator
    environment:
      # Test Data Configuration
      DATABASE_URL: ***************************************************************/pattern_mining_test
      REDIS_URL: redis://redis-test:6379/2
      
      # Data Generation Settings
      GENERATE_PATTERNS: 100
      GENERATE_REPOSITORIES: 10
      GENERATE_ANALYSES: 20
    volumes:
      - ./src:/app/src:ro
      - ./tests:/app/tests:ro
      - ./scripts:/app/scripts:ro
    depends_on:
      postgres-test:
        condition: service_healthy
      redis-test:
        condition: service_healthy
    networks:
      - integration-test-network
    profiles:
      - test-data
    command: >
      sh -c "
        echo 'Generating test data...' &&
        python scripts/generate_test_data.py
      "

  # Performance Test Runner
  performance-test-runner:
    build:
      context: .
      dockerfile: Dockerfile.prod
      target: development
    image: pattern-mining:performance-test
    container_name: pattern-mining-performance-test-runner
    environment:
      # Performance Test Configuration
      API_BASE_URL: http://api-test-server:8000
      TEST_DURATION: 60
      CONCURRENT_USERS: 20
      RAMP_UP_TIME: 10
      
      # Test Database and Cache
      DATABASE_URL: ***************************************************************/pattern_mining_test
      REDIS_URL: redis://redis-test:6379/3
      
      # Performance Test Settings
      REQUESTS_PER_SECOND: 50
      MAX_RESPONSE_TIME: 2000
      ERROR_THRESHOLD: 5
    volumes:
      - ./tests:/app/tests:ro
      - ./test-results:/app/test-results
      - ./scripts:/app/scripts:ro
    depends_on:
      api-test-server:
        condition: service_healthy
    networks:
      - integration-test-network
    profiles:
      - performance-test
    command: >
      sh -c "
        echo 'Starting performance tests...' &&
        python tests/performance/run_performance_tests.py
      "

networks:
  integration-test-network:
    driver: bridge
    ipam:
      driver: default
      config:
        - subnet: **********/16

volumes:
  postgres_test_data:
    driver: local
  redis_test_data:
    driver: local

# Test execution profiles
# docker-compose -f docker-compose.integration-test.yml --profile integration-test up
# docker-compose -f docker-compose.integration-test.yml --profile api-test up  
# docker-compose -f docker-compose.integration-test.yml --profile performance-test up
# docker-compose -f docker-compose.integration-test.yml --profile test-data up