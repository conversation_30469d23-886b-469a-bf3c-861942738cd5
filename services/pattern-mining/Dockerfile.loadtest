# Dockerfile for Phase 2 Load Testing Controller
# Specialized container for running comprehensive load tests

FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies for load testing
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    git \
    htop \
    jq \
    postgresql-client \
    redis-tools \
    unzip \
    wget \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies for load testing
COPY requirements-dev.txt .
RUN pip install --no-cache-dir -r requirements-dev.txt

# Install additional load testing packages
RUN pip install --no-cache-dir \
    locust==2.17.0 \
    k6==0.1.0 \
    artillery \
    httpx[http2] \
    asyncio-mqtt \
    influxdb-client \
    jaeger-client \
    py-spy \
    memory-profiler \
    psutil \
    numpy \
    scipy \
    matplotlib \
    seaborn \
    pandas \
    plotly \
    dash \
    streamlit \
    gradio

# Install monitoring and analysis tools
RUN pip install --no-cache-dir \
    prometheus-client \
    grafana-api \
    datadog \
    newrelic \
    elastic-apm

# Create directories
RUN mkdir -p /app/test_results /app/logs /app/reports /app/monitoring

# Copy application source code
COPY src/ /app/src/
COPY tests/ /app/tests/
COPY config/ /app/config/
COPY scripts/ /app/scripts/

# Copy Phase 2 specific configurations
COPY config/load_testing_config.yaml /app/config/
COPY scripts/run_phase2_load_tests.py /app/scripts/
COPY scripts/quick_load_test.sh /app/scripts/

# Make scripts executable
RUN chmod +x /app/scripts/*.sh /app/scripts/*.py

# Set environment variables for load testing
ENV PYTHONPATH=/app/src:/app
ENV TESTING_MODE=true
ENV LOAD_TEST_ENVIRONMENT=docker
ENV RESULTS_DIR=/app/test_results
ENV LOG_LEVEL=INFO

# Install Python application in development mode
RUN pip install -e /app

# Create load testing user
RUN groupadd -r loadtest && useradd -r -g loadtest loadtest
RUN chown -R loadtest:loadtest /app
USER loadtest

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD python -c "import sys; sys.exit(0)"

# Default command - keep container running for interactive testing
CMD ["tail", "-f", "/dev/null"]

# Labels for container management
LABEL maintainer="Pattern Mining Team"
LABEL version="2.0"
LABEL description="Phase 2 Load Testing Controller for Pattern Mining Service"
LABEL load-testing="true"
LABEL phase="2"