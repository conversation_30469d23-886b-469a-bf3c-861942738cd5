#!/bin/bash

# Integration Test Runner for Pattern Mining Service
# Comprehensive integration testing with real services

set -e  # Exit on any error

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="pattern-mining"
COMPOSE_FILE="docker-compose.integration-test.yml"
COMPOSE_PROJECT="${PROJECT_NAME}-integration-test"
TEST_TIMEOUT=600  # 10 minutes
CLEANUP_ON_EXIT=true

# Directories
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$SCRIPT_DIR"
RESULTS_DIR="$PROJECT_ROOT/test-results"
COVERAGE_DIR="$PROJECT_ROOT/coverage"
LOGS_DIR="$PROJECT_ROOT/logs"

# Create directories
mkdir -p "$RESULTS_DIR" "$COVERAGE_DIR" "$LOGS_DIR"

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] ✓${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] ⚠${NC} $1"
}

log_error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ✗${NC} $1"
}

# Cleanup function
cleanup() {
    if [ "$CLEANUP_ON_EXIT" = true ]; then
        log "Cleaning up test environment..."
        docker-compose -f "$COMPOSE_FILE" -p "$COMPOSE_PROJECT" down -v --remove-orphans 2>/dev/null || true
        docker system prune -f --volumes 2>/dev/null || true
    fi
}

# Set trap for cleanup
trap cleanup EXIT

# Help function
show_help() {
    cat << EOF
Integration Test Runner for Pattern Mining Service

Usage: $0 [OPTIONS] [TEST_PATTERN]

Options:
    -h, --help              Show this help message
    -c, --cleanup           Clean up and exit (don't run tests)
    -k, --keep              Keep containers running after tests
    -v, --verbose           Verbose output
    -f, --fast              Skip slow tests
    -s, --specific          Run specific test file or pattern
    -p, --performance       Run performance tests instead
    -a, --api-test          Start API test server only
    -d, --data              Generate test data only
    --no-build              Skip building containers
    --gemini-key KEY        Set Gemini API key for tests

Test Patterns:
    all                     Run all integration tests (default)
    gemini                  Run Gemini API integration tests
    database                Run database integration tests  
    redis                   Run Redis integration tests
    workflows               Run workflow integration tests
    real-services           Run real service integration tests
    batch                   Run batch processing tests
    error-recovery          Run error recovery tests
    performance             Run performance tests

Examples:
    $0                      # Run all integration tests
    $0 gemini              # Run only Gemini integration tests
    $0 -s test_gemini_integration.py  # Run specific test file
    $0 -p                  # Run performance tests
    $0 --gemini-key sk-... # Run with specific Gemini API key
    $0 -k                  # Keep containers running after tests
    $0 -c                  # Clean up containers and exit

Environment Variables:
    GEMINI_API_KEY_TEST     Gemini API key for testing
    INTEGRATION_TEST_DB     Database URL for integration tests
    INTEGRATION_TEST_REDIS  Redis URL for integration tests
    PYTEST_TIMEOUT          Test timeout in seconds (default: 300)
    PARALLEL_TESTS          Enable parallel test execution
EOF
}

# Parse command line arguments
VERBOSE=false
FAST=false
SPECIFIC=""
PERFORMANCE=false
API_TEST=false
DATA_ONLY=false
NO_BUILD=false
KEEP_CONTAINERS=false
TEST_PATTERN="all"
GEMINI_KEY=""

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -c|--cleanup)
            log "Cleaning up test environment..."
            cleanup
            exit 0
            ;;
        -k|--keep)
            KEEP_CONTAINERS=true
            CLEANUP_ON_EXIT=false
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -f|--fast)
            FAST=true
            shift
            ;;
        -s|--specific)
            SPECIFIC="$2"
            shift 2
            ;;
        -p|--performance)
            PERFORMANCE=true
            shift
            ;;
        -a|--api-test)
            API_TEST=true
            shift
            ;;
        -d|--data)
            DATA_ONLY=true
            shift
            ;;
        --no-build)
            NO_BUILD=true
            shift
            ;;
        --gemini-key)
            GEMINI_KEY="$2"
            shift 2
            ;;
        -*)
            log_error "Unknown option: $1"
            show_help
            exit 1
            ;;
        *)
            TEST_PATTERN="$1"
            shift
            ;;
    esac
done

# Set environment variables
if [ -n "$GEMINI_KEY" ]; then
    export GEMINI_API_KEY_TEST="$GEMINI_KEY"
fi

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed or not in PATH"
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed or not in PATH"
        exit 1
    fi
    
    # Check if Docker is running
    if ! docker info &> /dev/null; then
        log_error "Docker is not running"
        exit 1
    fi
    
    # Check for Gemini API key if running Gemini tests
    if [[ "$TEST_PATTERN" == "all" || "$TEST_PATTERN" == "gemini" || "$TEST_PATTERN" == "real-services" ]]; then
        if [ -z "$GEMINI_API_KEY_TEST" ]; then
            log_warning "GEMINI_API_KEY_TEST not set. Gemini integration tests will be skipped."
        fi
    fi
    
    log_success "Prerequisites check passed"
}

# Build containers
build_containers() {
    if [ "$NO_BUILD" = true ]; then
        log "Skipping container build"
        return
    fi
    
    log "Building test containers..."
    if ! docker-compose -f "$COMPOSE_FILE" -p "$COMPOSE_PROJECT" build --no-cache; then
        log_error "Failed to build containers"
        exit 1
    fi
    log_success "Containers built successfully"
}

# Start infrastructure services
start_infrastructure() {
    log "Starting infrastructure services..."
    
    # Start PostgreSQL and Redis
    if ! docker-compose -f "$COMPOSE_FILE" -p "$COMPOSE_PROJECT" up -d postgres-test redis-test; then
        log_error "Failed to start infrastructure services"
        exit 1
    fi
    
    # Wait for services to be healthy
    log "Waiting for services to be ready..."
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if docker-compose -f "$COMPOSE_FILE" -p "$COMPOSE_PROJECT" ps postgres-test | grep -q "healthy" && \
           docker-compose -f "$COMPOSE_FILE" -p "$COMPOSE_PROJECT" ps redis-test | grep -q "healthy"; then
            log_success "Infrastructure services are ready"
            return
        fi
        
        log "Waiting for services... (attempt $attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    done
    
    log_error "Infrastructure services failed to start within timeout"
    docker-compose -f "$COMPOSE_FILE" -p "$COMPOSE_PROJECT" logs postgres-test redis-test
    exit 1
}

# Generate test data
generate_test_data() {
    log "Generating test data..."
    if ! docker-compose -f "$COMPOSE_FILE" -p "$COMPOSE_PROJECT" run --rm test-data-generator; then
        log_warning "Test data generation failed, continuing with tests..."
    else
        log_success "Test data generated successfully"
    fi
}

# Run integration tests
run_integration_tests() {
    log "Running integration tests..."
    
    # Build pytest command
    local pytest_cmd="pytest tests/integration/"
    local pytest_args=""
    
    # Add test pattern filtering
    case "$TEST_PATTERN" in
        "all")
            pytest_args="-m integration"
            ;;
        "gemini")
            pytest_args="-m 'integration and gemini'"
            ;;
        "database")
            pytest_args="-m 'integration and database'"
            ;;
        "redis")
            pytest_args="-m 'integration and redis'"
            ;;
        "workflows")
            pytest_args="-m 'integration and workflow'"
            ;;
        "real-services")
            pytest_args="-m 'integration and real_services'"
            ;;
        "batch")
            pytest_args="-m 'integration and batch'"
            ;;
        "error-recovery")
            pytest_args="-m 'integration and error_recovery'"
            ;;
        *)
            if [ -n "$SPECIFIC" ]; then
                pytest_cmd="pytest tests/integration/$SPECIFIC"
            else
                pytest_args="-k $TEST_PATTERN"
            fi
            ;;
    esac
    
    # Add additional options
    if [ "$FAST" = true ]; then
        pytest_args="$pytest_args -m 'not slow'"
    fi
    
    if [ "$VERBOSE" = true ]; then
        pytest_args="$pytest_args -v -s"
    fi
    
    # Run tests
    local test_command="$pytest_cmd $pytest_args --tb=short --durations=10 --junit-xml=/app/test-results/integration-results.xml"
    
    log "Executing: $test_command"
    
    if docker-compose -f "$COMPOSE_FILE" -p "$COMPOSE_PROJECT" run --rm integration-test-runner bash -c "$test_command"; then
        log_success "Integration tests completed successfully"
        return 0
    else
        log_error "Integration tests failed"
        return 1
    fi
}

# Run performance tests
run_performance_tests() {
    log "Starting API test server for performance tests..."
    
    # Start API server
    if ! docker-compose -f "$COMPOSE_FILE" -p "$COMPOSE_PROJECT" up -d api-test-server; then
        log_error "Failed to start API test server"
        exit 1
    fi
    
    # Wait for API server to be ready
    log "Waiting for API server to be ready..."
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if docker-compose -f "$COMPOSE_FILE" -p "$COMPOSE_PROJECT" ps api-test-server | grep -q "healthy"; then
            log_success "API test server is ready"
            break
        fi
        
        log "Waiting for API server... (attempt $attempt/$max_attempts)"
        sleep 2
        ((attempt++))
        
        if [ $attempt -gt $max_attempts ]; then
            log_error "API test server failed to start within timeout"
            docker-compose -f "$COMPOSE_FILE" -p "$COMPOSE_PROJECT" logs api-test-server
            exit 1
        fi
    done
    
    # Run performance tests
    log "Running performance tests..."
    if docker-compose -f "$COMPOSE_FILE" -p "$COMPOSE_PROJECT" run --rm performance-test-runner; then
        log_success "Performance tests completed successfully"
        return 0
    else
        log_error "Performance tests failed"
        return 1
    fi
}

# Main execution
main() {
    log "Starting Pattern Mining Service Integration Tests"
    log "Test Pattern: $TEST_PATTERN"
    log "Project: $COMPOSE_PROJECT"
    
    # Check prerequisites
    check_prerequisites
    
    # Handle special modes
    if [ "$DATA_ONLY" = true ]; then
        build_containers
        start_infrastructure
        generate_test_data
        exit 0
    fi
    
    if [ "$API_TEST" = true ]; then
        build_containers
        start_infrastructure
        log "Starting API test server..."
        docker-compose -f "$COMPOSE_FILE" -p "$COMPOSE_PROJECT" up api-test-server
        exit 0
    fi
    
    # Build containers
    build_containers
    
    # Start infrastructure
    start_infrastructure
    
    # Generate test data
    generate_test_data
    
    # Run tests
    local exit_code=0
    if [ "$PERFORMANCE" = true ]; then
        run_performance_tests || exit_code=$?
    else
        run_integration_tests || exit_code=$?
    fi
    
    # Copy test results
    log "Copying test results..."
    docker-compose -f "$COMPOSE_FILE" -p "$COMPOSE_PROJECT" run --rm --entrypoint="" integration-test-runner \
        sh -c "cp -r /app/test-results/* /app/test-results/ 2>/dev/null || true; cp -r /app/coverage/* /app/coverage/ 2>/dev/null || true" || true
    
    # Display results
    if [ -f "$RESULTS_DIR/integration-results.xml" ]; then
        log_success "Test results saved to: $RESULTS_DIR/integration-results.xml"
    fi
    
    if [ -d "$COVERAGE_DIR/html" ]; then
        log_success "Coverage report saved to: $COVERAGE_DIR/html/index.html"
    fi
    
    # Keep containers if requested
    if [ "$KEEP_CONTAINERS" = true ]; then
        log "Keeping containers running as requested"
        log "To stop containers later, run: docker-compose -f $COMPOSE_FILE -p $COMPOSE_PROJECT down -v"
        trap - EXIT  # Remove cleanup trap
    fi
    
    if [ $exit_code -eq 0 ]; then
        log_success "Integration tests completed successfully"
    else
        log_error "Integration tests failed with exit code $exit_code"
    fi
    
    exit $exit_code
}

# Run main function
main "$@"