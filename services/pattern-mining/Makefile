# Pattern Mining Service Makefile
# Wave 2.5: CCL Contract Compliance Implementation

.PHONY: help install test test-unit test-integration test-contract test-performance \
        test-all coverage lint format clean docker-build docker-run validate-contract \
        validate-performance validate-all dev run

# Default target
.DEFAULT_GOAL := help

# Variables
PYTHON := python3
PIP := pip3
PROJECT_NAME := pattern-mining
SRC_DIR := src/pattern_mining
TEST_DIR := tests
DOCKER_IMAGE := episteme/pattern-mining:latest
PORT := 8001

# Help target
help:
	@echo "Pattern Mining Service - Available Commands"
	@echo "=========================================="
	@echo ""
	@echo "Development:"
	@echo "  make install          Install dependencies"
	@echo "  make dev              Run development server"
	@echo "  make run              Run production server"
	@echo ""
	@echo "Testing:"
	@echo "  make test             Run all tests"
	@echo "  make test-unit        Run unit tests"
	@echo "  make test-integration Run integration tests"
	@echo "  make test-contract    Run contract compliance tests"
	@echo "  make test-performance Run performance tests"
	@echo "  make coverage         Generate coverage report"
	@echo ""
	@echo "Phase 2 Load Testing:"
	@echo "  make loadtest-quick       Quick 5-minute load test"
	@echo "  make loadtest-dev         Development profile load tests"
	@echo "  make loadtest-staging     Staging validation load tests"
	@echo "  make loadtest-production  Production readiness (4 hours)"
	@echo "  make loadtest-endurance   24-hour endurance testing"
	@echo "  make loadtest-docker      Docker-based load testing"
	@echo "  make loadtest-setup       Setup load test infrastructure"
	@echo "  make loadtest-teardown    Teardown load test infrastructure"
	@echo ""
	@echo "Load Test Analysis:"
	@echo "  make loadtest-validate    Validate Phase 2 setup"
	@echo "  make loadtest-analyze     Analyze test results"
	@echo "  make loadtest-memory-check Check for memory leaks"
	@echo "  make loadtest-sla-check   Check SLA compliance"
	@echo "  make loadtest-report      Generate comprehensive report"
	@echo ""
	@echo "Validation:"
	@echo "  make validate-contract    Run contract validation"
	@echo "  make validate-performance Run performance validation"
	@echo "  make validate-all         Run all validations"
	@echo ""
	@echo "Code Quality:"
	@echo "  make lint             Run linters"
	@echo "  make format           Format code"
	@echo ""
	@echo "Docker:"
	@echo "  make docker-build     Build Docker image"
	@echo "  make docker-run       Run Docker container"
	@echo ""
	@echo "Utilities:"
	@echo "  make clean            Clean generated files"

# Installation
install:
	@echo "Installing dependencies..."
	$(PIP) install -e ".[dev,test]"
	@echo "Dependencies installed successfully!"

# Development server
dev:
	@echo "Starting development server..."
	cd src && $(PYTHON) -m pattern_mining.main

# Production server
run:
	@echo "Starting production server..."
	cd src && uvicorn pattern_mining.api.main:app --host 0.0.0.0 --port $(PORT) --workers 4

# Testing targets
test:
	@echo "Running all tests..."
	pytest $(TEST_DIR) -v

test-unit:
	@echo "Running unit tests..."
	pytest $(TEST_DIR)/unit -v -m "unit"

test-integration:
	@echo "Running integration tests..."
	pytest $(TEST_DIR)/integration -v -m "integration"

test-contract:
	@echo "Running contract compliance tests..."
	pytest $(TEST_DIR)/contract -v -m "contract"

test-performance:
	@echo "Running performance tests..."
	pytest $(TEST_DIR)/performance -v -m "performance" --tb=short

test-all: test-unit test-integration test-contract test-performance
	@echo "All test suites completed!"

# Coverage
coverage:
	@echo "Generating coverage report..."
	pytest $(TEST_DIR) --cov=$(SRC_DIR) --cov-report=term-missing --cov-report=html
	@echo "Coverage report generated in htmlcov/index.html"

# Contract validation
validate-contract:
	@echo "Running CCL contract validation..."
	./scripts/validate_contract_performance.py

validate-performance:
	@echo "Running performance validation..."
	./scripts/validate_all_performance.py

validate-all: validate-contract validate-performance
	@echo "All validations completed!"

# Run complete contract test suite
test-contract-full:
	@echo "Running complete contract test suite..."
	./scripts/run_contract_tests.sh

# Code quality
lint:
	@echo "Running linters..."
	ruff check $(SRC_DIR) $(TEST_DIR)
	mypy $(SRC_DIR) --ignore-missing-imports

format:
	@echo "Formatting code..."
	black $(SRC_DIR) $(TEST_DIR)
	ruff check --fix $(SRC_DIR) $(TEST_DIR)

# Docker
docker-build:
	@echo "Building Docker image..."
	docker build -t $(DOCKER_IMAGE) .

docker-run:
	@echo "Running Docker container..."
	docker run -p $(PORT):$(PORT) \
		-e ENVIRONMENT=production \
		-e REDIS_URL=redis://redis:6379 \
		$(DOCKER_IMAGE)

# Clean
clean:
	@echo "Cleaning generated files..."
	find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
	find . -type f -name "*.pyc" -delete
	find . -type f -name "*.pyo" -delete
	find . -type f -name "*.coverage" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} + 2>/dev/null || true
	find . -type d -name ".pytest_cache" -exec rm -rf {} + 2>/dev/null || true
	find . -type d -name ".mypy_cache" -exec rm -rf {} + 2>/dev/null || true
	find . -type d -name "htmlcov" -exec rm -rf {} + 2>/dev/null || true
	find . -type d -name ".ruff_cache" -exec rm -rf {} + 2>/dev/null || true
	@echo "Cleanup complete!"

# Development shortcuts
.PHONY: t tc tp tv tca

# Quick test shortcuts
t: test
tc: test-contract
tp: test-performance
tv: validate-all
tca: test-contract-full

# CI/CD targets
.PHONY: ci cd

ci: lint test coverage
	@echo "CI checks completed!"

cd: docker-build
	@echo "CD build completed!"

# Wave-specific targets
.PHONY: wave2 wave25 wave3

wave2:
	@echo "Running Wave 2 validation..."
	$(PYTHON) scripts/validate_performance.py

wave25:
	@echo "Running Wave 2.5 contract compliance..."
	make test-contract-full

wave3:
	@echo "Wave 3: Context Engineering Alignment"
	@echo "Not yet implemented"

# Phase 2 Load Testing targets
.PHONY: loadtest-quick loadtest-dev loadtest-staging loadtest-production loadtest-endurance \
        loadtest-concurrent loadtest-sustained loadtest-realistic loadtest-scalability \
        loadtest-setup loadtest-teardown loadtest-report loadtest-docker

# Quick load testing presets
loadtest-quick:
	@echo "Running quick load test (development profile, 5 minutes)..."
	./scripts/quick_load_test.sh --quick

loadtest-dev:
	@echo "Running development load tests..."
	$(PYTHON) scripts/run_phase2_load_tests.py --profile development --scenarios concurrent_users

loadtest-staging:
	@echo "Running staging validation load tests..."
	$(PYTHON) scripts/run_phase2_load_tests.py --profile staging --scenarios concurrent_users,realistic_workloads

loadtest-production:
	@echo "Running production readiness load tests..."
	$(PYTHON) scripts/run_phase2_load_tests.py --profile production --all-scenarios --duration 4

loadtest-endurance:
	@echo "Running 24-hour endurance load tests..."
	$(PYTHON) scripts/run_phase2_load_tests.py --endurance --duration 24

# Individual Phase 2 test scenarios
loadtest-concurrent:
	@echo "Running concurrent users load tests..."
	pytest tests/performance/test_concurrent_users.py -v -m "not long_running"

loadtest-sustained:
	@echo "Running sustained load tests with memory leak detection..."
	pytest tests/performance/test_sustained_load.py -v -m "not endurance"

loadtest-realistic:
	@echo "Running realistic workloads tests..."
	pytest tests/performance/test_realistic_workloads.py -v -m "not enterprise"

loadtest-scalability:
	@echo "Running scalability limits tests..."
	pytest tests/performance/test_scalability_limits.py -v

# Docker-based load testing
loadtest-docker:
	@echo "Setting up Docker-based load testing environment..."
	docker-compose -f docker-compose.loadtest.yml up -d
	@echo "Waiting for services to be ready..."
	sleep 30
	@echo "Running load tests in Docker environment..."
	docker-compose -f docker-compose.loadtest.yml exec loadtest-controller python scripts/run_phase2_load_tests.py --profile staging --all-scenarios
	@echo "Load testing completed. View results at http://localhost:3000 (Grafana)"

loadtest-setup:
	@echo "Setting up load testing infrastructure..."
	docker-compose -f docker-compose.loadtest.yml up -d postgres redis prometheus grafana influxdb
	@echo "Waiting for infrastructure to be ready..."
	sleep 30
	@echo "Infrastructure setup complete!"
	@echo "Grafana available at: http://localhost:3000 (admin/loadtest123)"
	@echo "Prometheus available at: http://localhost:9090"
	@echo "InfluxDB available at: http://localhost:8086"

loadtest-teardown:
	@echo "Tearing down load testing infrastructure..."
	docker-compose -f docker-compose.loadtest.yml down -v
	@echo "Infrastructure teardown complete!"

loadtest-report:
	@echo "Generating load testing report..."
	$(PYTHON) scripts/generate_load_test_report.py
	@echo "Report generated in test_results/latest_report.html"

# Phase 2 validation and analysis
.PHONY: loadtest-validate loadtest-analyze loadtest-memory-check loadtest-sla-check

loadtest-validate:
	@echo "Validating Phase 2 load testing setup..."
	$(PYTHON) -c "import pytest, psutil, memory_profiler, tracemalloc, asyncio, httpx; print('All required packages available')"
	@test -f config/load_testing_config.yaml || (echo "Config file missing" && exit 1)
	@test -f tests/performance/test_concurrent_users.py || (echo "Concurrent users tests missing" && exit 1)
	@test -f tests/performance/test_sustained_load.py || (echo "Sustained load tests missing" && exit 1)
	@test -f tests/performance/test_realistic_workloads.py || (echo "Realistic workloads tests missing" && exit 1)
	@test -f tests/performance/test_scalability_limits.py || (echo "Scalability limits tests missing" && exit 1)
	@echo "Phase 2 load testing validation passed!"

loadtest-analyze:
	@echo "Analyzing load testing results..."
	@if [ -d "test_results" ]; then \
		find test_results -name "*.json" -type f | head -10 | while read file; do \
			echo "Analyzing: $$file"; \
			$(PYTHON) -c "import json; data=json.load(open('$$file')); print(f'File: $$file'); print(f'Keys: {list(data.keys()) if isinstance(data, dict) else \"Not a dict\"}')"; \
		done; \
	else \
		echo "No test results found. Run load tests first."; \
	fi

loadtest-memory-check:
	@echo "Checking for memory leaks in recent test results..."
	@if [ -d "test_results" ]; then \
		find test_results -name "*memory_analysis.json" -type f | while read file; do \
			$(PYTHON) -c "import json; data=json.load(open('$$file')); leak=data.get('potential_leak_detected', False); rate=data.get('growth_rate_mb_per_hour', 0); print(f'$$file: Memory Leak = {leak}, Growth Rate = {rate} MB/hour')"; \
		done; \
	else \
		echo "No memory analysis results found."; \
	fi

loadtest-sla-check:
	@echo "Checking SLA compliance in recent test results..."
	@if [ -d "test_results" ]; then \
		find test_results -name "*_performance.json" -type f | while read file; do \
			$(PYTHON) -c "import json; data=json.load(open('$$file')); sla=data.get('sla_compliance', 0); error_rate=data.get('error_rate', 0); print(f'$$file: SLA Compliance = {sla}%, Error Rate = {error_rate}%')"; \
		done; \
	else \
		echo "No performance results found."; \
	fi

# Database migrations (if needed)
.PHONY: db-upgrade db-downgrade

db-upgrade:
	@echo "Running database migrations..."
	alembic upgrade head

db-downgrade:
	@echo "Rolling back database migration..."
	alembic downgrade -1

# Performance benchmarks
.PHONY: benchmark

benchmark:
	@echo "Running performance benchmarks..."
	$(PYTHON) -m pattern_mining.performance.benchmark

# Documentation
.PHONY: docs

docs:
	@echo "Building documentation..."
	mkdocs build

# Environment setup
.PHONY: setup-env

setup-env:
	@echo "Setting up development environment..."
	$(PYTHON) -m venv venv
	./venv/bin/pip install --upgrade pip
	./venv/bin/pip install -e ".[dev,test,gpu]"
	@echo "Environment setup complete! Activate with: source venv/bin/activate"