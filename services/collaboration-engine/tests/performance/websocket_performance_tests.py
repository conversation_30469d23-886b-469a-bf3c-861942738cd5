"""
WebSocket Performance Testing
Real-time performance validation for WebSocket connections

This module provides comprehensive WebSocket performance testing including:
- Connection establishment latency
- Message latency measurement (P50, P95, P99)
- Concurrent connection capacity testing
- Message throughput testing
- Real-time collaboration feature performance

Author: Wave 5 Load & Performance Testing Agent
Date: 2025-08-03
"""

import asyncio
import websockets
import json
import time
import logging
import statistics
import uuid
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import numpy as np
from datetime import datetime
import concurrent.futures
import ssl

logger = logging.getLogger(__name__)

@dataclass
class WebSocketConnectionMetrics:
    """Metrics for WebSocket connection performance"""
    connection_id: str
    connection_time_ms: float
    handshake_time_ms: float
    first_message_latency_ms: float
    avg_message_latency_ms: float
    p95_message_latency_ms: float
    p99_message_latency_ms: float
    messages_sent: int
    messages_received: int
    connection_duration_sec: float
    disconnection_reason: Optional[str]
    errors_encountered: List[str]

@dataclass
class WebSocketLoadTestResult:
    """Results from WebSocket load testing"""
    test_name: str
    concurrent_connections: int
    test_duration_sec: float
    total_connections_attempted: int
    successful_connections: int
    failed_connections: int
    connection_success_rate: float
    avg_connection_time_ms: float
    p95_connection_time_ms: float
    avg_message_latency_ms: float
    p95_message_latency_ms: float
    p99_message_latency_ms: float
    total_messages_sent: int
    total_messages_received: int
    message_throughput_per_sec: float
    max_concurrent_achieved: int
    errors_by_type: Dict[str, int]
    timestamp: str

class WebSocketPerformanceTester:
    """Comprehensive WebSocket performance testing framework"""
    
    def __init__(self, websocket_url: str, auth_token: Optional[str] = None):
        self.websocket_url = websocket_url
        self.auth_token = auth_token
        self.connection_metrics: List[WebSocketConnectionMetrics] = []
        
    async def run_comprehensive_websocket_tests(self) -> Dict[str, Any]:
        """
        Run comprehensive WebSocket performance tests
        
        Tests include:
        - Connection establishment performance
        - Message latency testing
        - Concurrent connection capacity
        - Sustained load performance
        - Rate limiting validation
        """
        logger.info("Starting comprehensive WebSocket performance testing...")
        
        test_results = {}
        
        # Test 1: Connection Establishment Performance
        logger.info("Testing WebSocket connection establishment performance...")
        connection_results = await self._test_connection_establishment()
        test_results['connection_establishment'] = connection_results
        
        # Test 2: Message Latency Performance
        logger.info("Testing WebSocket message latency performance...")
        latency_results = await self._test_message_latency()
        test_results['message_latency'] = latency_results
        
        # Test 3: Concurrent Connection Capacity
        logger.info("Testing WebSocket concurrent connection capacity...")
        capacity_results = await self._test_concurrent_connections()
        test_results['concurrent_capacity'] = capacity_results
        
        # Test 4: Sustained Load Performance
        logger.info("Testing WebSocket sustained load performance...")
        sustained_results = await self._test_sustained_load()
        test_results['sustained_load'] = sustained_results
        
        # Test 5: Rate Limiting Performance
        logger.info("Testing WebSocket rate limiting performance...")
        rate_limit_results = await self._test_rate_limiting_performance()
        test_results['rate_limiting'] = rate_limit_results
        
        return test_results
    
    async def _test_connection_establishment(self) -> Dict[str, Any]:
        """Test WebSocket connection establishment performance"""
        
        connection_attempts = 50
        connection_times = []
        handshake_times = []
        successful_connections = 0
        failed_connections = 0
        
        logger.info(f"Testing {connection_attempts} connection establishments...")
        
        for i in range(connection_attempts):
            try:
                start_time = time.perf_counter()
                
                # Create SSL context if needed
                ssl_context = None
                if self.websocket_url.startswith('wss://'):
                    ssl_context = ssl.create_default_context()
                    ssl_context.check_hostname = False
                    ssl_context.verify_mode = ssl.CERT_NONE
                
                # Establish connection
                async with websockets.connect(
                    self.websocket_url,
                    ssl=ssl_context,
                    timeout=10
                ) as websocket:
                    handshake_time = time.perf_counter()
                    handshake_duration = (handshake_time - start_time) * 1000
                    
                    # Send a test message to complete the connection test
                    test_message = {
                        "type": "ping",
                        "data": f"connection_test_{i}",
                        "timestamp": time.time()
                    }
                    
                    await websocket.send(json.dumps(test_message))
                    
                    # Wait for response or timeout
                    try:
                        response = await asyncio.wait_for(websocket.recv(), timeout=2.0)
                        connection_complete_time = time.perf_counter()
                        total_connection_time = (connection_complete_time - start_time) * 1000
                        
                        connection_times.append(total_connection_time)
                        handshake_times.append(handshake_duration)
                        successful_connections += 1
                        
                    except asyncio.TimeoutError:
                        # Even if no response, handshake was successful
                        total_connection_time = (handshake_time - start_time) * 1000
                        connection_times.append(total_connection_time)
                        handshake_times.append(handshake_duration)
                        successful_connections += 1
                
            except Exception as e:
                failed_connections += 1
                logger.debug(f"Connection {i} failed: {e}")
            
            # Brief pause between attempts
            await asyncio.sleep(0.1)
        
        return {
            'total_attempts': connection_attempts,
            'successful_connections': successful_connections,
            'failed_connections': failed_connections,
            'success_rate': successful_connections / connection_attempts,
            'avg_connection_time_ms': statistics.mean(connection_times) if connection_times else 0,
            'p50_connection_time_ms': self._percentile(connection_times, 50) if connection_times else 0,
            'p95_connection_time_ms': self._percentile(connection_times, 95) if connection_times else 0,
            'p99_connection_time_ms': self._percentile(connection_times, 99) if connection_times else 0,
            'avg_handshake_time_ms': statistics.mean(handshake_times) if handshake_times else 0,
            'min_connection_time_ms': min(connection_times) if connection_times else 0,
            'max_connection_time_ms': max(connection_times) if connection_times else 0,
            'connection_time_std_ms': statistics.stdev(connection_times) if len(connection_times) > 1 else 0
        }
    
    async def _test_message_latency(self) -> Dict[str, Any]:
        """Test WebSocket message latency performance"""
        
        message_count = 100
        latencies = []
        successful_messages = 0
        failed_messages = 0
        
        logger.info(f"Testing {message_count} message latencies...")
        
        try:
            # Create SSL context if needed
            ssl_context = None
            if self.websocket_url.startswith('wss://'):
                ssl_context = ssl.create_default_context()
                ssl_context.check_hostname = False
                ssl_context.verify_mode = ssl.CERT_NONE
            
            async with websockets.connect(
                self.websocket_url,
                ssl=ssl_context,
                timeout=10
            ) as websocket:
                
                for i in range(message_count):
                    try:
                        # Send message with timestamp
                        message_id = str(uuid.uuid4())
                        send_time = time.perf_counter()
                        
                        test_message = {
                            "type": "ping",
                            "id": message_id,
                            "data": f"latency_test_{i}",
                            "timestamp": send_time
                        }
                        
                        await websocket.send(json.dumps(test_message))
                        
                        # Wait for response
                        try:
                            response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                            receive_time = time.perf_counter()
                            
                            latency = (receive_time - send_time) * 1000  # Convert to ms
                            latencies.append(latency)
                            successful_messages += 1
                            
                        except asyncio.TimeoutError:
                            failed_messages += 1
                            logger.debug(f"Message {i} timeout")
                        
                        # Pace messages to avoid overwhelming
                        await asyncio.sleep(0.05)  # 50ms between messages
                        
                    except Exception as e:
                        failed_messages += 1
                        logger.debug(f"Message {i} failed: {e}")
                
        except Exception as e:
            logger.error(f"WebSocket connection failed during latency test: {e}")
            return {
                'error': str(e),
                'successful_messages': 0,
                'failed_messages': message_count
            }
        
        return {
            'total_messages': message_count,
            'successful_messages': successful_messages,
            'failed_messages': failed_messages,
            'success_rate': successful_messages / message_count,
            'avg_latency_ms': statistics.mean(latencies) if latencies else 0,
            'p50_latency_ms': self._percentile(latencies, 50) if latencies else 0,
            'p95_latency_ms': self._percentile(latencies, 95) if latencies else 0,
            'p99_latency_ms': self._percentile(latencies, 99) if latencies else 0,
            'min_latency_ms': min(latencies) if latencies else 0,
            'max_latency_ms': max(latencies) if latencies else 0,
            'latency_std_ms': statistics.stdev(latencies) if len(latencies) > 1 else 0,
            'phase3_target_met': self._percentile(latencies, 95) < 50 if latencies else False  # P95 < 50ms target
        }
    
    async def _test_concurrent_connections(self) -> List[WebSocketLoadTestResult]:
        """Test concurrent WebSocket connection capacity"""
        
        # Test different concurrency levels
        concurrency_levels = [5, 10, 25, 50, 100, 200]
        results = []
        
        for concurrency in concurrency_levels:
            logger.info(f"Testing {concurrency} concurrent connections...")
            
            try:
                result = await self._run_concurrent_connection_test(concurrency)
                results.append(result)
                
                # Log key metrics
                logger.info(
                    f"Concurrency {concurrency}: Success Rate={result.connection_success_rate:.2%}, "
                    f"Avg Latency={result.avg_message_latency_ms:.2f}ms"
                )
                
                # If success rate drops below 80%, stop escalating
                if result.connection_success_rate < 0.8:
                    logger.warning(f"Success rate dropped below 80% at {concurrency} connections")
                    break
                
                # Cool down between tests
                await asyncio.sleep(10)
                
            except Exception as e:
                logger.error(f"Concurrent test with {concurrency} connections failed: {e}")
                break
        
        return results
    
    async def _run_concurrent_connection_test(self, concurrency: int) -> WebSocketLoadTestResult:
        """Run a single concurrent connection test"""
        
        test_duration = 60  # 1 minute per test
        start_time = time.time()
        
        # Create connection tasks
        connection_tasks = []
        for i in range(concurrency):
            task = asyncio.create_task(
                self._concurrent_connection_worker(i, test_duration)
            )
            connection_tasks.append(task)
        
        # Run all connections concurrently
        try:
            worker_results = await asyncio.gather(*connection_tasks, return_exceptions=True)
        except Exception as e:
            logger.error(f"Concurrent connection test failed: {e}")
            # Cancel remaining tasks
            for task in connection_tasks:
                if not task.done():
                    task.cancel()
            raise
        
        end_time = time.time()
        actual_duration = end_time - start_time
        
        # Aggregate results
        total_connections_attempted = len(connection_tasks)
        successful_connections = 0
        failed_connections = 0
        connection_times = []
        message_latencies = []
        total_messages_sent = 0
        total_messages_received = 0
        errors_by_type = {}
        
        for result in worker_results:
            if isinstance(result, Exception):
                failed_connections += 1
                error_type = type(result).__name__
                errors_by_type[error_type] = errors_by_type.get(error_type, 0) + 1
            elif isinstance(result, dict):
                if result.get('connected', False):
                    successful_connections += 1
                    connection_times.append(result.get('connection_time_ms', 0))
                    message_latencies.extend(result.get('message_latencies', []))
                    total_messages_sent += result.get('messages_sent', 0)
                    total_messages_received += result.get('messages_received', 0)
                else:
                    failed_connections += 1
                    error_type = result.get('error_type', 'Unknown')
                    errors_by_type[error_type] = errors_by_type.get(error_type, 0) + 1
        
        # Calculate metrics
        connection_success_rate = successful_connections / total_connections_attempted
        avg_connection_time = statistics.mean(connection_times) if connection_times else 0
        p95_connection_time = self._percentile(connection_times, 95) if connection_times else 0
        avg_message_latency = statistics.mean(message_latencies) if message_latencies else 0
        p95_message_latency = self._percentile(message_latencies, 95) if message_latencies else 0
        p99_message_latency = self._percentile(message_latencies, 99) if message_latencies else 0
        message_throughput = (total_messages_sent + total_messages_received) / actual_duration
        
        return WebSocketLoadTestResult(
            test_name=f"concurrent_{concurrency}",
            concurrent_connections=concurrency,
            test_duration_sec=actual_duration,
            total_connections_attempted=total_connections_attempted,
            successful_connections=successful_connections,
            failed_connections=failed_connections,
            connection_success_rate=connection_success_rate,
            avg_connection_time_ms=avg_connection_time,
            p95_connection_time_ms=p95_connection_time,
            avg_message_latency_ms=avg_message_latency,
            p95_message_latency_ms=p95_message_latency,
            p99_message_latency_ms=p99_message_latency,
            total_messages_sent=total_messages_sent,
            total_messages_received=total_messages_received,
            message_throughput_per_sec=message_throughput,
            max_concurrent_achieved=successful_connections,
            errors_by_type=errors_by_type,
            timestamp=datetime.now().isoformat()
        )
    
    async def _concurrent_connection_worker(self, worker_id: int, duration: int) -> Dict[str, Any]:
        """Worker for concurrent connection testing"""
        
        connection_start = time.perf_counter()
        message_latencies = []
        messages_sent = 0
        messages_received = 0
        connected = False
        
        try:
            # Create SSL context if needed
            ssl_context = None
            if self.websocket_url.startswith('wss://'):
                ssl_context = ssl.create_default_context()
                ssl_context.check_hostname = False
                ssl_context.verify_mode = ssl.CERT_NONE
            
            async with websockets.connect(
                self.websocket_url,
                ssl=ssl_context,
                timeout=10
            ) as websocket:
                
                connection_time = (time.perf_counter() - connection_start) * 1000
                connected = True
                
                # Send messages periodically
                end_time = time.time() + duration
                message_interval = 2 + (worker_id % 3)  # Stagger messages: 2-4 seconds
                
                while time.time() < end_time:
                    try:
                        # Send message with latency tracking
                        send_time = time.perf_counter()
                        message = {
                            "type": "test_message",
                            "worker_id": worker_id,
                            "data": f"concurrent_test_{messages_sent}",
                            "timestamp": send_time
                        }
                        
                        await websocket.send(json.dumps(message))
                        messages_sent += 1
                        
                        # Try to receive response (with timeout)
                        try:
                            response = await asyncio.wait_for(websocket.recv(), timeout=1.0)
                            receive_time = time.perf_counter()
                            
                            latency = (receive_time - send_time) * 1000
                            message_latencies.append(latency)
                            messages_received += 1
                            
                        except asyncio.TimeoutError:
                            pass  # No response is acceptable
                        
                        await asyncio.sleep(message_interval)
                        
                    except Exception as e:
                        logger.debug(f"Worker {worker_id} message error: {e}")
                        break
                
                return {
                    'worker_id': worker_id,
                    'connected': connected,
                    'connection_time_ms': connection_time,
                    'messages_sent': messages_sent,
                    'messages_received': messages_received,
                    'message_latencies': message_latencies,
                    'error_type': None
                }
                
        except Exception as e:
            return {
                'worker_id': worker_id,
                'connected': connected,
                'connection_time_ms': (time.perf_counter() - connection_start) * 1000,
                'messages_sent': messages_sent,
                'messages_received': messages_received,
                'message_latencies': message_latencies,
                'error_type': type(e).__name__,
                'error_message': str(e)
            }
    
    async def _test_sustained_load(self) -> Dict[str, Any]:
        """Test WebSocket performance under sustained load"""
        
        concurrency = 50  # Moderate concurrency for sustained test
        duration = 15 * 60  # 15 minutes
        
        logger.info(f"Running sustained load test: {concurrency} connections for {duration/60:.1f} minutes...")
        
        # Run sustained load test
        result = await self._run_concurrent_connection_test(concurrency)
        
        # Enhance result with sustained load specific metrics
        result.test_name = "sustained_load"
        
        # Additional analysis for sustained performance
        stability_metrics = {
            'performance_stability': 'stable' if result.connection_success_rate > 0.95 else 'degraded',
            'latency_stability': 'stable' if result.p95_message_latency_ms < 100 else 'degraded',
            'throughput_stability': result.message_throughput_per_sec,
            'sustained_duration_minutes': duration / 60
        }
        
        return {
            **result.__dict__,
            **stability_metrics
        }
    
    async def _test_rate_limiting_performance(self) -> Dict[str, Any]:
        """Test WebSocket rate limiting performance impact"""
        
        logger.info("Testing rate limiting performance impact...")
        
        # Test with different message rates to understand rate limiting behavior
        message_rates = [30, 60, 90, 120]  # messages per minute
        rate_limit_results = {}
        
        for rate in message_rates:
            logger.info(f"Testing message rate: {rate} messages/minute...")
            
            try:
                result = await self._test_message_rate(rate)
                rate_limit_results[f"rate_{rate}_per_min"] = result
                
                # Brief pause between rate tests
                await asyncio.sleep(5)
                
            except Exception as e:
                logger.error(f"Rate limit test at {rate} msg/min failed: {e}")
        
        return rate_limit_results
    
    async def _test_message_rate(self, messages_per_minute: int) -> Dict[str, Any]:
        """Test a specific message rate"""
        
        test_duration = 120  # 2 minutes
        message_interval = 60.0 / messages_per_minute  # seconds between messages
        
        messages_sent = 0
        messages_received = 0
        rate_limited_responses = 0
        latencies = []
        
        try:
            # Create SSL context if needed
            ssl_context = None
            if self.websocket_url.startswith('wss://'):
                ssl_context = ssl.create_default_context()
                ssl_context.check_hostname = False
                ssl_context.verify_mode = ssl.CERT_NONE
            
            async with websockets.connect(
                self.websocket_url,
                ssl=ssl_context,
                timeout=10
            ) as websocket:
                
                start_time = time.time()
                end_time = start_time + test_duration
                
                while time.time() < end_time:
                    try:
                        # Send message
                        send_time = time.perf_counter()
                        message = {
                            "type": "rate_test",
                            "data": f"rate_test_{messages_sent}",
                            "timestamp": send_time
                        }
                        
                        await websocket.send(json.dumps(message))
                        messages_sent += 1
                        
                        # Try to receive response
                        try:
                            response = await asyncio.wait_for(websocket.recv(), timeout=2.0)
                            receive_time = time.perf_counter()
                            
                            latency = (receive_time - send_time) * 1000
                            latencies.append(latency)
                            messages_received += 1
                            
                            # Check if response indicates rate limiting
                            try:
                                response_data = json.loads(response)
                                if response_data.get('type') == 'error' and 'rate' in response_data.get('message', '').lower():
                                    rate_limited_responses += 1
                            except json.JSONDecodeError:
                                pass
                            
                        except asyncio.TimeoutError:
                            pass  # No response is acceptable
                        
                        # Wait for next message interval
                        await asyncio.sleep(message_interval)
                        
                    except Exception as e:
                        logger.debug(f"Rate test message error: {e}")
                        break
                
        except Exception as e:
            logger.error(f"Rate limit test connection failed: {e}")
            return {
                'error': str(e),
                'messages_sent': messages_sent,
                'messages_received': messages_received
            }
        
        actual_rate = messages_sent / (test_duration / 60)  # actual messages per minute
        
        return {
            'target_rate_per_min': messages_per_minute,
            'actual_rate_per_min': actual_rate,
            'messages_sent': messages_sent,
            'messages_received': messages_received,
            'rate_limited_responses': rate_limited_responses,
            'success_rate': messages_received / messages_sent if messages_sent > 0 else 0,
            'avg_latency_ms': statistics.mean(latencies) if latencies else 0,
            'p95_latency_ms': self._percentile(latencies, 95) if latencies else 0,
            'rate_limiting_detected': rate_limited_responses > 0,
            'rate_limiting_percentage': (rate_limited_responses / messages_sent) * 100 if messages_sent > 0 else 0
        }
    
    def _percentile(self, data: List[float], percentile: float) -> float:
        """Calculate percentile value"""
        if not data:
            return 0.0
        sorted_data = sorted(data)
        index = int((percentile / 100.0) * len(sorted_data))
        if index >= len(sorted_data):
            index = len(sorted_data) - 1
        return sorted_data[index]

# Example usage
if __name__ == "__main__":
    async def main():
        websocket_url = "wss://collaboration-engine-production-xxxxx.run.app/ws"
        tester = WebSocketPerformanceTester(websocket_url)
        
        logger.info("Starting comprehensive WebSocket performance testing...")
        
        try:
            results = await tester.run_comprehensive_websocket_tests()
            
            print("\n=== WEBSOCKET PERFORMANCE TEST RESULTS ===")
            
            # Connection establishment results
            if 'connection_establishment' in results:
                conn_results = results['connection_establishment']
                print(f"\nCONNECTION ESTABLISHMENT:")
                print(f"  Success Rate: {conn_results['success_rate']:.2%}")
                print(f"  Avg Connection Time: {conn_results['avg_connection_time_ms']:.2f}ms")
                print(f"  P95 Connection Time: {conn_results['p95_connection_time_ms']:.2f}ms")
            
            # Message latency results
            if 'message_latency' in results:
                latency_results = results['message_latency']
                print(f"\nMESSAGE LATENCY:")
                print(f"  Success Rate: {latency_results['success_rate']:.2%}")
                print(f"  Avg Latency: {latency_results['avg_latency_ms']:.2f}ms")
                print(f"  P95 Latency: {latency_results['p95_latency_ms']:.2f}ms")
                print(f"  P99 Latency: {latency_results['p99_latency_ms']:.2f}ms")
                print(f"  Phase 3 Target Met (P95 < 50ms): {latency_results['phase3_target_met']}")
            
            # Concurrent connection results
            if 'concurrent_capacity' in results:
                concurrent_results = results['concurrent_capacity']
                print(f"\nCONCURRENT CONNECTION CAPACITY:")
                for result in concurrent_results:
                    print(f"  {result.concurrent_connections} connections: "
                          f"Success Rate={result.connection_success_rate:.2%}, "
                          f"P95 Latency={result.p95_message_latency_ms:.2f}ms")
            
            # Sustained load results
            if 'sustained_load' in results:
                sustained_results = results['sustained_load']
                print(f"\nSUSTAINED LOAD:")
                print(f"  Performance Stability: {sustained_results['performance_stability']}")
                print(f"  Latency Stability: {sustained_results['latency_stability']}")
                print(f"  Throughput: {sustained_results['message_throughput_per_sec']:.2f} msg/sec")
            
            # Rate limiting results
            if 'rate_limiting' in results:
                rate_results = results['rate_limiting']
                print(f"\nRATE LIMITING PERFORMANCE:")
                for rate_test, result in rate_results.items():
                    if not isinstance(result, dict) or 'error' in result:
                        continue
                    print(f"  {rate_test}: "
                          f"Success Rate={result['success_rate']:.2%}, "
                          f"Rate Limited={result['rate_limiting_percentage']:.1f}%")
            
        except Exception as e:
            logger.error(f"WebSocket performance testing failed: {e}")
    
    asyncio.run(main())