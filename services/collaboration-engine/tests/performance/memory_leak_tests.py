"""
Memory Leak Detection and Analysis
Addresses EPIS-011: Memory leaks in parser pool

This module provides comprehensive memory leak detection for the collaboration engine,
focusing on long-running operations, WebSocket connections, and parser pool usage.

Author: Wave 5 Load & Performance Testing Agent
Date: 2025-08-03
"""

import asyncio
import aiohttp
import psutil
import time
import logging
import gc
import sys
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import statistics
import numpy as np
from datetime import datetime, timedelta
import websockets
import json

logger = logging.getLogger(__name__)

@dataclass
class MemorySnapshot:
    """Memory usage snapshot at a point in time"""
    timestamp: float
    rss_mb: float
    vms_mb: float
    percent: float
    available_mb: float
    gc_objects: int
    gc_collections: Tuple[int, int, int]  # Gen 0, 1, 2 collections

@dataclass
class MemoryLeakResult:
    """Results from memory leak testing"""
    test_name: str
    duration_minutes: float
    initial_memory_mb: float
    final_memory_mb: float
    peak_memory_mb: float
    memory_growth_mb: float
    growth_rate_mb_per_hour: float
    leak_detected: bool
    leak_severity: str  # 'none', 'minor', 'moderate', 'severe'
    memory_pattern: str  # 'stable', 'linear_growth', 'exponential_growth', 'saw_tooth'
    snapshots: List[MemorySnapshot]
    statistics: Dict[str, float]
    timestamp: str

class MemoryLeakDetector:
    """Detect memory leaks through sustained operations and monitoring"""
    
    def __init__(self, base_url: str):
        self.base_url = base_url
        self.websocket_url = base_url.replace('https://', 'wss://').replace('http://', 'ws://') + '/ws'
        self.auth_token: Optional[str] = None
        self.process = psutil.Process()
        
    async def run_comprehensive_memory_tests(self) -> Dict[str, MemoryLeakResult]:
        """
        Run comprehensive memory leak detection tests
        
        Tests multiple scenarios that could cause memory leaks:
        - HTTP connection handling over time
        - WebSocket connection lifecycle
        - Parser pool operations (simulated through API calls)
        - Long-running sustained operations
        """
        logger.info("Starting comprehensive memory leak detection...")
        
        test_results = {}
        
        # Test 1: HTTP Connection Memory Leaks
        logger.info("Testing HTTP connection memory patterns...")
        http_result = await self._test_http_connection_leaks()
        test_results['http_connections'] = http_result
        
        # Allow memory to stabilize between tests
        await self._stabilize_memory()
        
        # Test 2: WebSocket Connection Memory Leaks  
        logger.info("Testing WebSocket connection memory patterns...")
        websocket_result = await self._test_websocket_connection_leaks()
        test_results['websocket_connections'] = websocket_result
        
        # Allow memory to stabilize between tests
        await self._stabilize_memory()
        
        # Test 3: Parser Pool Simulation (through API calls)
        logger.info("Testing parser pool memory patterns...")
        parser_result = await self._test_parser_pool_leaks()
        test_results['parser_pool'] = parser_result
        
        # Allow memory to stabilize between tests
        await self._stabilize_memory()
        
        # Test 4: Sustained Operations
        logger.info("Testing sustained operations memory patterns...")
        sustained_result = await self._test_sustained_operations_leaks()
        test_results['sustained_operations'] = sustained_result
        
        return test_results
    
    async def _stabilize_memory(self):
        """Allow memory to stabilize and run garbage collection"""
        logger.info("Stabilizing memory state...")
        
        # Force garbage collection
        for _ in range(3):
            gc.collect()
        
        # Wait for memory to stabilize
        await asyncio.sleep(30)
        
        # Log current memory state
        memory_info = self.process.memory_info()
        logger.info(f"Memory stabilized at {memory_info.rss / 1024 / 1024:.2f} MB RSS")
    
    async def _test_http_connection_leaks(self) -> MemoryLeakResult:
        """Test for memory leaks in HTTP connection handling"""
        
        test_duration = 30 * 60  # 30 minutes
        sample_interval = 60    # Sample every minute
        
        snapshots = []
        start_time = time.time()
        
        # Take initial memory snapshot
        initial_snapshot = self._take_memory_snapshot()
        snapshots.append(initial_snapshot)
        
        logger.info(f"Starting HTTP connection leak test for {test_duration/60:.1f} minutes...")
        
        # Simulate continuous HTTP operations
        operations_count = 0
        
        while time.time() - start_time < test_duration:
            try:
                # Create and destroy HTTP connections repeatedly
                async with aiohttp.ClientSession() as session:
                    tasks = []
                    
                    # Create multiple concurrent requests
                    for _ in range(10):
                        task = asyncio.create_task(
                            self._make_http_request(session)
                        )
                        tasks.append(task)
                    
                    await asyncio.gather(*tasks, return_exceptions=True)
                    operations_count += len(tasks)
                
                # Take memory snapshot every minute
                current_time = time.time()
                if (current_time - start_time) % sample_interval < 1:
                    snapshot = self._take_memory_snapshot()
                    snapshots.append(snapshot)
                    
                    logger.debug(
                        f"HTTP test: {(current_time - start_time)/60:.1f}min, "
                        f"Memory: {snapshot.rss_mb:.2f}MB, "
                        f"Operations: {operations_count}"
                    )
                
                # Brief pause to allow processing
                await asyncio.sleep(5)
                
            except Exception as e:
                logger.warning(f"HTTP operation failed: {e}")
                await asyncio.sleep(1)
        
        # Take final snapshot
        final_snapshot = self._take_memory_snapshot()
        snapshots.append(final_snapshot)
        
        # Analyze memory pattern
        result = self._analyze_memory_pattern(
            "http_connections",
            snapshots,
            test_duration / 60,
            operations_count
        )
        
        logger.info(
            f"HTTP leak test completed: {result.memory_growth_mb:.2f}MB growth, "
            f"Leak detected: {result.leak_detected}"
        )
        
        return result
    
    async def _test_websocket_connection_leaks(self) -> MemoryLeakResult:
        """Test for memory leaks in WebSocket connection handling"""
        
        test_duration = 20 * 60  # 20 minutes
        sample_interval = 60    # Sample every minute
        
        snapshots = []
        start_time = time.time()
        
        # Take initial memory snapshot
        initial_snapshot = self._take_memory_snapshot()
        snapshots.append(initial_snapshot)
        
        logger.info(f"Starting WebSocket connection leak test for {test_duration/60:.1f} minutes...")
        
        connections_created = 0
        
        while time.time() - start_time < test_duration:
            try:
                # Create and destroy WebSocket connections
                await self._create_websocket_connection_cycle()
                connections_created += 1
                
                # Take memory snapshot every minute
                current_time = time.time()
                if (current_time - start_time) % sample_interval < 1:
                    snapshot = self._take_memory_snapshot()
                    snapshots.append(snapshot)
                    
                    logger.debug(
                        f"WebSocket test: {(current_time - start_time)/60:.1f}min, "
                        f"Memory: {snapshot.rss_mb:.2f}MB, "
                        f"Connections: {connections_created}"
                    )
                
                # Pause between connection cycles
                await asyncio.sleep(10)
                
            except Exception as e:
                logger.warning(f"WebSocket operation failed: {e}")
                await asyncio.sleep(5)
        
        # Take final snapshot
        final_snapshot = self._take_memory_snapshot()
        snapshots.append(final_snapshot)
        
        # Analyze memory pattern
        result = self._analyze_memory_pattern(
            "websocket_connections",
            snapshots,
            test_duration / 60,
            connections_created
        )
        
        logger.info(
            f"WebSocket leak test completed: {result.memory_growth_mb:.2f}MB growth, "
            f"Leak detected: {result.leak_detected}"
        )
        
        return result
    
    async def _create_websocket_connection_cycle(self):
        """Create, use, and destroy a WebSocket connection"""
        
        try:
            # Connect to WebSocket
            async with websockets.connect(
                self.websocket_url,
                timeout=10
            ) as websocket:
                
                # Send some messages
                for i in range(5):
                    message = {
                        "type": "ping",
                        "data": f"test_message_{i}",
                        "timestamp": time.time()
                    }
                    
                    await websocket.send(json.dumps(message))
                    
                    # Try to receive response (with timeout)
                    try:
                        response = await asyncio.wait_for(
                            websocket.recv(),
                            timeout=2.0
                        )
                    except asyncio.TimeoutError:
                        pass  # No response is ok for this test
                    
                    await asyncio.sleep(0.1)  # Brief pause between messages
                
        except Exception as e:
            # Connection failures are expected and logged for analysis
            logger.debug(f"WebSocket connection cycle failed: {e}")
    
    async def _test_parser_pool_leaks(self) -> MemoryLeakResult:
        """
        Test for EPIS-011: Memory leaks in parser pool
        
        Simulates parser pool usage through API calls that likely
        trigger parsing operations.
        """
        
        test_duration = 25 * 60  # 25 minutes
        sample_interval = 60    # Sample every minute
        
        snapshots = []
        start_time = time.time()
        
        # Take initial memory snapshot
        initial_snapshot = self._take_memory_snapshot()
        snapshots.append(initial_snapshot)
        
        logger.info(f"Starting parser pool leak test for {test_duration/60:.1f} minutes...")
        
        parsing_operations = 0
        
        while time.time() - start_time < test_duration:
            try:
                # Simulate parser-heavy operations
                await self._exercise_parser_operations()
                parsing_operations += 1
                
                # Take memory snapshot every minute
                current_time = time.time()
                if (current_time - start_time) % sample_interval < 1:
                    snapshot = self._take_memory_snapshot()
                    snapshots.append(snapshot)
                    
                    logger.debug(
                        f"Parser test: {(current_time - start_time)/60:.1f}min, "
                        f"Memory: {snapshot.rss_mb:.2f}MB, "
                        f"Operations: {parsing_operations}"
                    )
                
                # Brief pause between operations
                await asyncio.sleep(8)
                
            except Exception as e:
                logger.warning(f"Parser operation failed: {e}")
                await asyncio.sleep(2)
        
        # Take final snapshot
        final_snapshot = self._take_memory_snapshot()
        snapshots.append(final_snapshot)
        
        # Analyze memory pattern
        result = self._analyze_memory_pattern(
            "parser_pool",
            snapshots,
            test_duration / 60,
            parsing_operations
        )
        
        logger.info(
            f"Parser pool leak test completed: {result.memory_growth_mb:.2f}MB growth, "
            f"Leak detected: {result.leak_detected}"
        )
        
        return result
    
    async def _exercise_parser_operations(self):
        """Exercise operations that likely use the parser pool"""
        
        # These operations potentially trigger parsing:
        # - Team creation (JSON parsing)
        # - Session creation (JSON parsing) 
        # - Message handling (JSON parsing)
        # - Configuration updates (JSON parsing)
        
        operations = [
            self._simulate_team_operation,
            self._simulate_session_operation,
            self._simulate_message_operation,
            self._simulate_config_operation
        ]
        
        # Execute multiple operations in parallel
        tasks = []
        for operation in operations:
            task = asyncio.create_task(operation())
            tasks.append(task)
        
        await asyncio.gather(*tasks, return_exceptions=True)
    
    async def _simulate_team_operation(self):
        """Simulate team-related operations that might use parsers"""
        
        async with aiohttp.ClientSession() as session:
            # GET request (response parsing)
            try:
                headers = self.auth_headers if self.auth_token else {}
                async with session.get(
                    f"{self.base_url}/api/v1/teams",
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    await response.text()  # Force parsing
            except Exception:
                pass  # Failures are ok for this test
    
    async def _simulate_session_operation(self):
        """Simulate session-related operations that might use parsers"""
        
        async with aiohttp.ClientSession() as session:
            try:
                headers = self.auth_headers if self.auth_token else {}
                async with session.get(
                    f"{self.base_url}/api/v1/sessions",
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    await response.text()  # Force parsing
            except Exception:
                pass  # Failures are ok for this test
    
    async def _simulate_message_operation(self):
        """Simulate message operations that might use parsers"""
        
        # Use health endpoint as a lightweight operation
        async with aiohttp.ClientSession() as session:
            try:
                async with session.get(
                    f"{self.base_url}/health",
                    timeout=aiohttp.ClientTimeout(total=5)
                ) as response:
                    await response.json()  # Force JSON parsing
            except Exception:
                pass  # Failures are ok for this test
    
    async def _simulate_config_operation(self):
        """Simulate configuration operations that might use parsers"""
        
        # Use metrics endpoint as it might involve parsing
        async with aiohttp.ClientSession() as session:
            try:
                async with session.get(
                    f"{self.base_url}/metrics",
                    timeout=aiohttp.ClientTimeout(total=5)
                ) as response:
                    await response.text()  # Force parsing
            except Exception:
                pass  # Failures are ok for this test
    
    async def _test_sustained_operations_leaks(self) -> MemoryLeakResult:
        """Test for memory leaks during sustained operations"""
        
        test_duration = 45 * 60  # 45 minutes
        sample_interval = 120   # Sample every 2 minutes
        
        snapshots = []
        start_time = time.time()
        
        # Take initial memory snapshot
        initial_snapshot = self._take_memory_snapshot()
        snapshots.append(initial_snapshot)
        
        logger.info(f"Starting sustained operations leak test for {test_duration/60:.1f} minutes...")
        
        operations_count = 0
        
        # Create multiple concurrent sustained operation tasks
        sustained_tasks = []
        
        for i in range(5):  # 5 concurrent sustained workers
            task = asyncio.create_task(
                self._sustained_worker(i, start_time + test_duration)
            )
            sustained_tasks.append(task)
        
        # Monitor memory while sustained operations run
        while time.time() - start_time < test_duration:
            # Take memory snapshot
            current_time = time.time()
            if (current_time - start_time) % sample_interval < 1:
                snapshot = self._take_memory_snapshot()
                snapshots.append(snapshot)
                
                logger.info(
                    f"Sustained test: {(current_time - start_time)/60:.1f}min, "
                    f"Memory: {snapshot.rss_mb:.2f}MB"
                )
            
            await asyncio.sleep(30)  # Check every 30 seconds
        
        # Wait for all sustained tasks to complete
        await asyncio.gather(*sustained_tasks, return_exceptions=True)
        
        # Take final snapshot
        final_snapshot = self._take_memory_snapshot()
        snapshots.append(final_snapshot)
        
        # Analyze memory pattern
        result = self._analyze_memory_pattern(
            "sustained_operations",
            snapshots,
            test_duration / 60,
            operations_count
        )
        
        logger.info(
            f"Sustained operations leak test completed: {result.memory_growth_mb:.2f}MB growth, "
            f"Leak detected: {result.leak_detected}"
        )
        
        return result
    
    async def _sustained_worker(self, worker_id: int, end_time: float):
        """A worker that performs sustained operations"""
        
        logger.debug(f"Starting sustained worker {worker_id}")
        
        while time.time() < end_time:
            try:
                # Mix of different operations
                if worker_id % 3 == 0:
                    await self._make_http_request(None)
                elif worker_id % 3 == 1:
                    await self._exercise_parser_operations()
                else:
                    await self._create_websocket_connection_cycle()
                
                # Vary the pace per worker to create realistic load
                await asyncio.sleep(10 + (worker_id * 2))
                
            except Exception as e:
                logger.debug(f"Sustained worker {worker_id} error: {e}")
                await asyncio.sleep(5)
        
        logger.debug(f"Sustained worker {worker_id} completed")
    
    async def _make_http_request(self, session: Optional[aiohttp.ClientSession]):
        """Make a single HTTP request"""
        
        if session:
            async with session.get(f"{self.base_url}/health") as response:
                await response.text()
        else:
            async with aiohttp.ClientSession() as new_session:
                async with new_session.get(f"{self.base_url}/health") as response:
                    await response.text()
    
    def _take_memory_snapshot(self) -> MemorySnapshot:
        """Take a comprehensive memory snapshot"""
        
        # Process memory info
        memory_info = self.process.memory_info()
        memory_percent = self.process.memory_percent()
        
        # System memory info
        virtual_memory = psutil.virtual_memory()
        
        # Garbage collection info
        gc_stats = gc.get_stats()
        gc_collections = (
            gc_stats[0]['collections'] if len(gc_stats) > 0 else 0,
            gc_stats[1]['collections'] if len(gc_stats) > 1 else 0,
            gc_stats[2]['collections'] if len(gc_stats) > 2 else 0
        )
        
        return MemorySnapshot(
            timestamp=time.time(),
            rss_mb=memory_info.rss / 1024 / 1024,
            vms_mb=memory_info.vms / 1024 / 1024,
            percent=memory_percent,
            available_mb=virtual_memory.available / 1024 / 1024,
            gc_objects=len(gc.get_objects()),
            gc_collections=gc_collections
        )
    
    def _analyze_memory_pattern(
        self, 
        test_name: str, 
        snapshots: List[MemorySnapshot], 
        duration_minutes: float,
        operations_count: int
    ) -> MemoryLeakResult:
        """Analyze memory snapshots to detect leaks and patterns"""
        
        if len(snapshots) < 2:
            raise ValueError("Need at least 2 snapshots for analysis")
        
        # Extract memory values
        memory_values = [s.rss_mb for s in snapshots]
        timestamps = [s.timestamp for s in snapshots]
        
        # Basic statistics
        initial_memory = memory_values[0]
        final_memory = memory_values[-1]
        peak_memory = max(memory_values)
        memory_growth = final_memory - initial_memory
        
        # Calculate growth rate per hour
        growth_rate_per_hour = (memory_growth / duration_minutes) * 60
        
        # Detect memory pattern
        memory_pattern = self._classify_memory_pattern(memory_values, timestamps)
        
        # Detect leak using multiple criteria
        leak_detected, leak_severity = self._detect_memory_leak(
            memory_values, 
            duration_minutes, 
            growth_rate_per_hour
        )
        
        # Calculate statistics
        statistics_dict = {
            'mean_memory_mb': statistics.mean(memory_values),
            'median_memory_mb': statistics.median(memory_values),
            'std_memory_mb': statistics.stdev(memory_values) if len(memory_values) > 1 else 0,
            'memory_volatility': (max(memory_values) - min(memory_values)) / initial_memory,
            'growth_rate_percent_per_hour': (growth_rate_per_hour / initial_memory) * 100,
            'operations_per_mb_growth': operations_count / memory_growth if memory_growth > 0 else float('inf')
        }
        
        return MemoryLeakResult(
            test_name=test_name,
            duration_minutes=duration_minutes,
            initial_memory_mb=initial_memory,
            final_memory_mb=final_memory,
            peak_memory_mb=peak_memory,
            memory_growth_mb=memory_growth,
            growth_rate_mb_per_hour=growth_rate_per_hour,
            leak_detected=leak_detected,
            leak_severity=leak_severity,
            memory_pattern=memory_pattern,
            snapshots=snapshots,
            statistics=statistics_dict,
            timestamp=datetime.now().isoformat()
        )
    
    def _classify_memory_pattern(self, memory_values: List[float], timestamps: List[float]) -> str:
        """Classify the memory usage pattern"""
        
        if len(memory_values) < 3:
            return 'insufficient_data'
        
        # Calculate trend using linear regression
        x = np.array(timestamps)
        y = np.array(memory_values)
        
        # Normalize x to start from 0
        x = x - x[0]
        
        # Linear regression
        slope, intercept = np.polyfit(x, y, 1)
        
        # Calculate R-squared to measure linearity
        y_pred = slope * x + intercept
        ss_res = np.sum((y - y_pred) ** 2)
        ss_tot = np.sum((y - np.mean(y)) ** 2)
        r_squared = 1 - (ss_res / ss_tot) if ss_tot != 0 else 0
        
        # Calculate volatility
        volatility = np.std(y) / np.mean(y) if np.mean(y) != 0 else 0
        
        # Classify pattern
        if abs(slope) < 0.001:  # Very flat
            return 'stable'
        elif slope > 0.01 and r_squared > 0.7:  # Strong positive trend
            return 'linear_growth'
        elif slope > 0.01 and r_squared < 0.5:  # Positive but not linear
            if volatility > 0.1:
                return 'saw_tooth'
            else:
                return 'exponential_growth'
        elif slope < -0.01:  # Negative trend
            return 'decreasing'
        else:
            return 'irregular'
    
    def _detect_memory_leak(
        self, 
        memory_values: List[float], 
        duration_minutes: float,
        growth_rate_per_hour: float
    ) -> Tuple[bool, str]:
        """Detect memory leak using multiple criteria"""
        
        # Criteria for leak detection:
        # 1. Absolute growth > 50MB over test duration
        # 2. Growth rate > 10MB/hour
        # 3. Relative growth > 10% of initial memory
        # 4. Consistent upward trend (>70% of samples above trend line)
        
        initial_memory = memory_values[0]
        final_memory = memory_values[-1]
        memory_growth = final_memory - initial_memory
        
        # Criterion 1: Absolute growth
        significant_growth = memory_growth > 50  # 50MB
        
        # Criterion 2: Growth rate
        high_growth_rate = growth_rate_per_hour > 10  # 10MB/hour
        
        # Criterion 3: Relative growth
        relative_growth = (memory_growth / initial_memory) * 100
        significant_relative_growth = relative_growth > 10  # 10%
        
        # Criterion 4: Trend consistency
        if len(memory_values) > 5:
            # Check if most values are above the trend line
            x = np.arange(len(memory_values))
            slope, intercept = np.polyfit(x, memory_values, 1)
            trend_line = slope * x + intercept
            above_trend = np.sum(np.array(memory_values) > trend_line)
            trend_consistency = above_trend / len(memory_values) > 0.7
        else:
            trend_consistency = False
        
        # Determine leak severity
        leak_indicators = sum([
            significant_growth,
            high_growth_rate, 
            significant_relative_growth,
            trend_consistency
        ])
        
        if leak_indicators >= 3:
            return True, 'severe'
        elif leak_indicators == 2:
            return True, 'moderate'
        elif leak_indicators == 1:
            return True, 'minor'
        else:
            return False, 'none'
    
    @property
    def auth_headers(self) -> Dict[str, str]:
        """Get authentication headers if available"""
        if self.auth_token:
            return {"Authorization": f"Bearer {self.auth_token}"}
        return {}

# Example usage
if __name__ == "__main__":
    async def main():
        base_url = "https://collaboration-engine-production-xxxxx.run.app"
        detector = MemoryLeakDetector(base_url)
        
        logger.info("Starting comprehensive memory leak detection...")
        
        try:
            results = await detector.run_comprehensive_memory_tests()
            
            print("\n=== MEMORY LEAK DETECTION RESULTS ===")
            
            for test_name, result in results.items():
                print(f"\n{test_name.upper()}:")
                print(f"  Duration: {result.duration_minutes:.1f} minutes")
                print(f"  Memory Growth: {result.memory_growth_mb:.2f} MB")
                print(f"  Growth Rate: {result.growth_rate_mb_per_hour:.2f} MB/hour")
                print(f"  Leak Detected: {result.leak_detected} ({result.leak_severity})")
                print(f"  Pattern: {result.memory_pattern}")
                print(f"  Growth Rate %/hour: {result.statistics['growth_rate_percent_per_hour']:.2f}%")
            
            # Overall assessment
            severe_leaks = [r for r in results.values() if r.leak_severity == 'severe']
            moderate_leaks = [r for r in results.values() if r.leak_severity == 'moderate']
            
            print(f"\n=== OVERALL ASSESSMENT ===")
            print(f"Severe leaks detected: {len(severe_leaks)}")
            print(f"Moderate leaks detected: {len(moderate_leaks)}")
            
            if severe_leaks:
                print("❌ CRITICAL: Severe memory leaks found - immediate attention required")
            elif moderate_leaks:
                print("⚠️ WARNING: Moderate memory leaks found - monitoring recommended")
            else:
                print("✅ GOOD: No significant memory leaks detected")
        
        except Exception as e:
            logger.error(f"Memory leak detection failed: {e}")
    
    asyncio.run(main())