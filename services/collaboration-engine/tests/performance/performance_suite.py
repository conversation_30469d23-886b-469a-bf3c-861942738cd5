"""
Performance Testing Framework for Collaboration Engine
Addresses EPIS-082: Performance benchmarking and regression testing missing

This module provides comprehensive performance testing capabilities including:
- Baseline endpoint performance measurement
- Load testing scenarios with concurrent users
- Connection pool exhaustion testing (EPIS-012)
- Memory leak detection (EPIS-011) 
- Database connection optimization testing (EPIS-076)
- Performance regression detection
- WebSocket real-time performance validation

Author: Wave 5 Load & Performance Testing Agent
Date: 2025-08-03
"""

import asyncio
import aiohttp
import time
import statistics
import json
import sys
import os
import psutil
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import concurrent.futures
import websockets
import jwt
import uuid
import numpy as np

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('performance_tests.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class PerformanceConfig:
    """Performance testing configuration"""
    base_url: str = "https://collaboration-engine-production-xxxxx.run.app"
    websocket_url: str = "wss://collaboration-engine-production-xxxxx.run.app/ws"
    timeout: int = 30
    baseline_samples: int = 100
    load_test_duration: int = 300  # 5 minutes
    regression_threshold: float = 0.20  # 20% performance degradation
    memory_test_duration: int = 1800  # 30 minutes
    max_concurrent_connections: int = 1000
    
    @classmethod
    def from_env(cls) -> 'PerformanceConfig':
        """Load configuration from environment variables"""
        return cls(
            base_url=os.getenv('COLLABORATION_ENGINE_URL', cls.base_url),
            websocket_url=os.getenv('COLLABORATION_ENGINE_WS_URL', cls.websocket_url),
            timeout=int(os.getenv('PERF_TEST_TIMEOUT', cls.timeout)),
            baseline_samples=int(os.getenv('PERF_BASELINE_SAMPLES', cls.baseline_samples)),
            load_test_duration=int(os.getenv('PERF_LOAD_DURATION', cls.load_test_duration)),
            regression_threshold=float(os.getenv('PERF_REGRESSION_THRESHOLD', cls.regression_threshold))
        )

@dataclass 
class EndpointMetrics:
    """Performance metrics for a single endpoint"""
    endpoint: str
    method: str
    mean: float
    median: float
    p95: float
    p99: float
    min_time: float
    max_time: float
    success_rate: float
    error_rate: float
    throughput: float
    samples: int
    timestamp: str

@dataclass
class LoadTestResult:
    """Results from load testing scenarios"""
    scenario_name: str
    concurrent_users: int
    duration: int
    total_requests: int
    successful_requests: int
    failed_requests: int
    avg_response_time: float
    p95_response_time: float
    throughput: float
    error_rate: float
    resource_usage: Dict[str, Any]
    timestamp: str

@dataclass
class WebSocketPerformanceMetrics:
    """WebSocket-specific performance metrics"""
    connection_time: float
    message_latency_p50: float
    message_latency_p95: float
    message_latency_p99: float
    max_concurrent_connections: int
    message_throughput: float
    connection_success_rate: float
    timestamp: str

class PerformanceBenchmarkSuite:
    """Comprehensive performance testing framework for Collaboration Engine"""
    
    def __init__(self, config: PerformanceConfig = None):
        self.config = config or PerformanceConfig.from_env()
        self.auth_token: Optional[str] = None
        self.baseline_metrics: Dict[str, EndpointMetrics] = {}
        self.test_results: Dict[str, Any] = {}
        self.session: Optional[aiohttp.ClientSession] = None
        
    async def initialize(self) -> bool:
        """Initialize the performance testing framework"""
        try:
            logger.info("Initializing Performance Benchmark Suite...")
            
            # Create HTTP session with optimized settings
            timeout = aiohttp.ClientTimeout(total=self.config.timeout)
            connector = aiohttp.TCPConnector(
                limit=200,
                limit_per_host=50,
                ttl_dns_cache=300,
                use_dns_cache=True,
            )
            
            self.session = aiohttp.ClientSession(
                timeout=timeout,
                connector=connector
            )
            
            # Test service availability
            health_check = await self._check_service_health()
            if not health_check['healthy']:
                logger.error(f"Service health check failed: {health_check['response']}")
                return False
            
            # Authenticate if possible
            await self._authenticate()
            
            logger.info("Performance Benchmark Suite initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize performance suite: {e}")
            return False
    
    async def _check_service_health(self) -> Dict[str, Any]:
        """Check if the collaboration service is healthy"""
        try:
            async with self.session.get(f"{self.config.base_url}/health") as response:
                return {
                    'healthy': response.status == 200,
                    'status_code': response.status,
                    'response': await response.text() if response.status != 200 else "OK"
                }
        except Exception as e:
            return {
                'healthy': False,
                'status_code': None,
                'response': str(e)
            }
    
    async def _authenticate(self) -> None:
        """Attempt to authenticate with the service"""
        try:
            auth_data = {
                "email": "<EMAIL>",
                "name": "Performance Test User",
                "provider": "test"
            }
            
            async with self.session.post(
                f"{self.config.base_url}/auth/login",
                json=auth_data
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    self.auth_token = data.get("access_token")
                    logger.info("Successfully authenticated for performance testing")
                else:
                    logger.warning(f"Authentication failed: {response.status}")
                    
        except Exception as e:
            logger.warning(f"Authentication attempt failed: {e}")
    
    @property
    def auth_headers(self) -> Dict[str, str]:
        """Get authentication headers if available"""
        if self.auth_token:
            return {"Authorization": f"Bearer {self.auth_token}"}
        return {}
    
    async def run_baseline_performance_tests(self) -> Dict[str, EndpointMetrics]:
        """
        Establish baseline performance metrics for all endpoints
        Addresses EPIS-082: Performance benchmarking missing
        """
        logger.info("Starting baseline performance measurement...")
        
        # Define all testable endpoints
        endpoints = [
            {'method': 'GET', 'path': '/health', 'name': 'health_check', 'auth_required': False},
            {'method': 'GET', 'path': '/api/v1/teams', 'name': 'teams_list', 'auth_required': True},
            {'method': 'GET', 'path': '/api/v1/sessions', 'name': 'sessions_list', 'auth_required': True},
            {'method': 'POST', 'path': '/api/v1/teams', 'name': 'team_create', 'auth_required': True},
            {'method': 'POST', 'path': '/api/v1/sessions', 'name': 'session_create', 'auth_required': True},
            {'method': 'GET', 'path': '/metrics', 'name': 'metrics', 'auth_required': False},
        ]
        
        baseline_results = {}
        
        for endpoint in endpoints:
            if endpoint['auth_required'] and not self.auth_token:
                logger.warning(f"Skipping {endpoint['name']} - authentication required")
                continue
                
            logger.info(f"Measuring baseline performance for {endpoint['name']}")
            
            try:
                metrics = await self._measure_endpoint_performance(
                    endpoint['method'],
                    endpoint['path'],
                    endpoint['name'],
                    samples=self.config.baseline_samples,
                    auth_required=endpoint['auth_required']
                )
                
                baseline_results[endpoint['name']] = metrics
                
                # Log key metrics
                logger.info(
                    f"{endpoint['name']}: Mean={metrics.mean:.2f}ms, "
                    f"P95={metrics.p95:.2f}ms, Success Rate={metrics.success_rate:.2%}"
                )
                
            except Exception as e:
                logger.error(f"Failed to measure {endpoint['name']}: {e}")
        
        self.baseline_metrics = baseline_results
        return baseline_results
    
    async def _measure_endpoint_performance(
        self, 
        method: str, 
        path: str, 
        name: str,
        samples: int = 100,
        auth_required: bool = False
    ) -> EndpointMetrics:
        """Measure detailed performance metrics for a specific endpoint"""
        
        response_times = []
        success_count = 0
        error_count = 0
        start_time = time.time()
        
        # Prepare request data for POST endpoints
        request_data = self._get_test_data_for_endpoint(name)
        headers = self.auth_headers if auth_required else {}
        
        for i in range(samples):
            request_start = time.perf_counter()
            
            try:
                if method == 'GET':
                    async with self.session.get(
                        f"{self.config.base_url}{path}",
                        headers=headers
                    ) as response:
                        await response.text()  # Ensure response is fully received
                        request_end = time.perf_counter()
                        
                        response_time = (request_end - request_start) * 1000  # Convert to ms
                        response_times.append(response_time)
                        
                        if response.status < 400:
                            success_count += 1
                        else:
                            error_count += 1
                            
                elif method == 'POST':
                    async with self.session.post(
                        f"{self.config.base_url}{path}",
                        json=request_data,
                        headers=headers
                    ) as response:
                        await response.text()
                        request_end = time.perf_counter()
                        
                        response_time = (request_end - request_start) * 1000
                        response_times.append(response_time)
                        
                        if response.status < 400:
                            success_count += 1
                        else:
                            error_count += 1
                            
            except Exception as e:
                error_count += 1
                request_end = time.perf_counter()
                response_times.append((request_end - request_start) * 1000)
                logger.debug(f"Request {i} failed for {name}: {e}")
        
        end_time = time.time()
        total_time = end_time - start_time
        
        return EndpointMetrics(
            endpoint=name,
            method=method,
            mean=statistics.mean(response_times),
            median=statistics.median(response_times),
            p95=self._percentile(response_times, 95),
            p99=self._percentile(response_times, 99),
            min_time=min(response_times),
            max_time=max(response_times),
            success_rate=success_count / samples,
            error_rate=error_count / samples,
            throughput=samples / total_time,
            samples=samples,
            timestamp=datetime.now().isoformat()
        )
    
    def _get_test_data_for_endpoint(self, endpoint_name: str) -> Dict[str, Any]:
        """Get appropriate test data for different endpoints"""
        if endpoint_name == 'team_create':
            return {
                "name": f"Perf Test Team {uuid.uuid4().hex[:8]}",
                "description": "Performance testing team",
                "settings": {
                    "max_members": 10,
                    "allow_guest_access": False
                }
            }
        elif endpoint_name == 'session_create':
            return {
                "team_id": "test-team-id",  # This might fail, but will test error handling
                "name": f"Perf Test Session {uuid.uuid4().hex[:8]}",
                "description": "Performance testing session"
            }
        return {}
    
    def _percentile(self, data: List[float], percentile: float) -> float:
        """Calculate percentile value"""
        if not data:
            return 0.0
        sorted_data = sorted(data)
        index = int((percentile / 100.0) * len(sorted_data))
        if index >= len(sorted_data):
            index = len(sorted_data) - 1
        return sorted_data[index]
    
    async def run_load_tests(self) -> Dict[str, LoadTestResult]:
        """
        Run comprehensive load testing scenarios
        Tests concurrent user capacity and system behavior under load
        """
        logger.info("Starting load testing scenarios...")
        
        scenarios = [
            {'name': 'Light Load', 'users': 25, 'duration': 180},     # 3 minutes
            {'name': 'Standard Load', 'users': 50, 'duration': 300},  # 5 minutes  
            {'name': 'High Load', 'users': 100, 'duration': 180},     # 3 minutes
            {'name': 'Burst Load', 'users': 200, 'duration': 60},     # 1 minute
            {'name': 'Sustained Load', 'users': 150, 'duration': 900} # 15 minutes
        ]
        
        load_test_results = {}
        
        for scenario in scenarios:
            logger.info(f"Running {scenario['name']}: {scenario['users']} users for {scenario['duration']}s")
            
            try:
                result = await self._run_load_test_scenario(
                    scenario['name'],
                    scenario['users'],
                    scenario['duration']
                )
                
                load_test_results[scenario['name']] = result
                
                # Log key results
                logger.info(
                    f"{scenario['name']} completed: "
                    f"Throughput={result.throughput:.2f} req/s, "
                    f"P95={result.p95_response_time:.2f}ms, "
                    f"Error Rate={result.error_rate:.2%}"
                )
                
                # Cool down between tests
                await asyncio.sleep(30)
                
            except Exception as e:
                logger.error(f"Load test scenario {scenario['name']} failed: {e}")
        
        return load_test_results
    
    async def _run_load_test_scenario(
        self,
        scenario_name: str,
        concurrent_users: int,
        duration: int
    ) -> LoadTestResult:
        """Execute a single load testing scenario"""
        
        # Create user tasks
        user_tasks = []
        for user_id in range(concurrent_users):
            task = asyncio.create_task(
                self._simulate_user_session(user_id, duration)
            )
            user_tasks.append(task)
        
        # Monitor resource usage during test
        resource_monitor_task = asyncio.create_task(
            self._monitor_system_resources(duration)
        )
        
        # Run load test
        start_time = time.time()
        
        try:
            user_results = await asyncio.gather(*user_tasks, return_exceptions=True)
            resource_usage = await resource_monitor_task
        except Exception as e:
            logger.error(f"Load test execution failed: {e}")
            # Cancel remaining tasks
            for task in user_tasks:
                if not task.done():
                    task.cancel()
            resource_monitor_task.cancel()
            raise
        
        end_time = time.time()
        actual_duration = end_time - start_time
        
        # Aggregate results
        total_requests = 0
        successful_requests = 0
        failed_requests = 0
        response_times = []
        
        for result in user_results:
            if isinstance(result, Exception):
                failed_requests += 1
                continue
                
            if isinstance(result, dict):
                total_requests += result.get('total_requests', 0)
                successful_requests += result.get('successful_requests', 0)
                failed_requests += result.get('failed_requests', 0)
                response_times.extend(result.get('response_times', []))
        
        # Calculate metrics
        avg_response_time = statistics.mean(response_times) if response_times else 0
        p95_response_time = self._percentile(response_times, 95) if response_times else 0
        throughput = total_requests / actual_duration if actual_duration > 0 else 0
        error_rate = failed_requests / total_requests if total_requests > 0 else 0
        
        return LoadTestResult(
            scenario_name=scenario_name,
            concurrent_users=concurrent_users,
            duration=int(actual_duration),
            total_requests=total_requests,
            successful_requests=successful_requests,
            failed_requests=failed_requests,
            avg_response_time=avg_response_time,
            p95_response_time=p95_response_time,
            throughput=throughput,
            error_rate=error_rate,
            resource_usage=resource_usage,
            timestamp=datetime.now().isoformat()
        )
    
    async def _simulate_user_session(self, user_id: int, duration: int) -> Dict[str, Any]:
        """Simulate realistic user behavior for load testing"""
        end_time = time.time() + duration
        
        total_requests = 0
        successful_requests = 0
        failed_requests = 0
        response_times = []
        
        # Create a dedicated session for this user
        async with aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=self.config.timeout)
        ) as user_session:
            
            while time.time() < end_time:
                try:
                    # User workflow simulation:
                    # 1. Check health (lightweight request)
                    # 2. List teams (authenticated request) 
                    # 3. List sessions (authenticated request)
                    # 4. Periodic delays to simulate real user behavior
                    
                    actions = [
                        {'method': 'GET', 'path': '/health', 'auth': False},
                        {'method': 'GET', 'path': '/api/v1/teams', 'auth': True},
                        {'method': 'GET', 'path': '/api/v1/sessions', 'auth': True},
                    ]
                    
                    for action in actions:
                        if time.time() >= end_time:
                            break
                            
                        request_start = time.perf_counter()
                        headers = self.auth_headers if action['auth'] and self.auth_token else {}
                        
                        try:
                            async with user_session.get(
                                f"{self.config.base_url}{action['path']}",
                                headers=headers
                            ) as response:
                                await response.text()
                                request_end = time.perf_counter()
                                
                                response_time = (request_end - request_start) * 1000
                                response_times.append(response_time)
                                total_requests += 1
                                
                                if response.status < 400:
                                    successful_requests += 1
                                else:
                                    failed_requests += 1
                                    
                        except Exception as e:
                            request_end = time.perf_counter()
                            response_time = (request_end - request_start) * 1000
                            response_times.append(response_time)
                            total_requests += 1
                            failed_requests += 1
                            logger.debug(f"User {user_id} request failed: {e}")
                    
                    # Simulate user think time (realistic delays)
                    think_time = 1 + (user_id % 3)  # 1-3 seconds
                    await asyncio.sleep(think_time)
                    
                except Exception as e:
                    logger.debug(f"User {user_id} session error: {e}")
                    failed_requests += 1
                    await asyncio.sleep(1)  # Brief pause on error
        
        return {
            'user_id': user_id,
            'total_requests': total_requests,
            'successful_requests': successful_requests,
            'failed_requests': failed_requests,
            'response_times': response_times
        }
    
    async def _monitor_system_resources(self, duration: int) -> Dict[str, Any]:
        """Monitor system resource usage during load testing"""
        
        cpu_samples = []
        memory_samples = []
        network_samples = []
        
        sample_interval = 5  # Sample every 5 seconds
        samples_count = duration // sample_interval
        
        initial_network = psutil.net_io_counters()
        
        for _ in range(samples_count):
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_samples.append(cpu_percent)
            
            # Memory usage
            memory = psutil.virtual_memory()
            memory_samples.append({
                'percent': memory.percent,
                'available_gb': memory.available / (1024**3),
                'used_gb': memory.used / (1024**3)
            })
            
            # Network I/O
            current_network = psutil.net_io_counters()
            network_samples.append({
                'bytes_sent': current_network.bytes_sent - initial_network.bytes_sent,
                'bytes_recv': current_network.bytes_recv - initial_network.bytes_recv,
                'packets_sent': current_network.packets_sent - initial_network.packets_sent,
                'packets_recv': current_network.packets_recv - initial_network.packets_recv
            })
            
            await asyncio.sleep(sample_interval - 1)  # Account for CPU measurement time
        
        return {
            'cpu': {
                'avg_percent': statistics.mean(cpu_samples),
                'max_percent': max(cpu_samples),
                'samples': cpu_samples
            },
            'memory': {
                'avg_percent': statistics.mean(s['percent'] for s in memory_samples),
                'max_percent': max(s['percent'] for s in memory_samples),
                'min_available_gb': min(s['available_gb'] for s in memory_samples),
                'samples': memory_samples
            },
            'network': {
                'total_bytes_sent': max(s['bytes_sent'] for s in network_samples),
                'total_bytes_recv': max(s['bytes_recv'] for s in network_samples),
                'total_packets_sent': max(s['packets_sent'] for s in network_samples),
                'total_packets_recv': max(s['packets_recv'] for s in network_samples),
                'samples': network_samples
            }
        }
    
    async def cleanup(self):
        """Clean up resources"""
        if self.session:
            await self.session.close()
            
    def export_results(self, filename: str = None) -> str:
        """Export all test results to JSON file"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"performance_results_{timestamp}.json"
        
        results = {
            'config': asdict(self.config),
            'baseline_metrics': {
                name: asdict(metrics) for name, metrics in self.baseline_metrics.items()
            },
            'test_results': self.test_results,
            'export_timestamp': datetime.now().isoformat()
        }
        
        with open(filename, 'w') as f:
            json.dump(results, f, indent=2)
        
        logger.info(f"Performance results exported to {filename}")
        return filename

# Example usage and CLI interface
if __name__ == "__main__":
    async def main():
        config = PerformanceConfig.from_env()
        suite = PerformanceBenchmarkSuite(config)
        
        try:
            # Initialize framework
            if not await suite.initialize():
                logger.error("Failed to initialize performance testing framework")
                sys.exit(1)
            
            # Run baseline performance tests
            logger.info("=== BASELINE PERFORMANCE TESTS ===")
            baseline_results = await suite.run_baseline_performance_tests()
            
            # Run load tests
            logger.info("=== LOAD TESTING SCENARIOS ===")  
            load_results = await suite.run_load_tests()
            
            # Store results
            suite.test_results['baseline'] = baseline_results
            suite.test_results['load_tests'] = load_results
            
            # Export results
            results_file = suite.export_results()
            
            logger.info("=== PERFORMANCE TESTING COMPLETED ===")
            logger.info(f"Results exported to: {results_file}")
            
        except KeyboardInterrupt:
            logger.info("Performance testing interrupted by user")
        except Exception as e:
            logger.error(f"Performance testing failed: {e}")
            sys.exit(1)
        finally:
            await suite.cleanup()
    
    # Run the performance testing suite
    asyncio.run(main())