# Performance Testing Framework Requirements
# EPIS-082: Performance benchmarking and regression testing missing

# Core async HTTP client
aiohttp>=3.8.0

# WebSocket client for real-time testing
websockets>=11.0.0

# Data analysis and statistics
numpy>=1.24.0
scipy>=1.10.0

# System monitoring
psutil>=5.9.0

# JSON Web Token handling
PyJWT>=2.6.0

# Data visualization (for reports)
matplotlib>=3.6.0

# Testing framework (if integrated with pytest)
pytest>=7.0.0
pytest-asyncio>=0.21.0

# Optional: For enhanced performance testing
uvloop>=0.17.0; sys_platform != "win32"

# Optional: For better compression in performance tests
zstandard>=0.19.0

# Optional: For database performance testing
asyncpg>=0.27.0

# Optional: For Redis performance testing  
aioredis>=2.0.0

# Optional: For advanced statistics
pandas>=1.5.0

# Development dependencies
black>=22.0.0
flake8>=5.0.0
mypy>=1.0.0