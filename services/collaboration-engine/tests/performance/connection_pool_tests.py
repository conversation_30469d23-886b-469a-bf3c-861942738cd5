"""
Connection Pool Performance Testing
Addresses EPIS-012: Connection pool exhaustion under load

This module tests database and HTTP connection pool behavior under various load conditions
to identify exhaustion thresholds and optimal configuration settings.

Author: Wave 5 Load & Performance Testing Agent  
Date: 2025-08-03
"""

import asyncio
import aiohttp
import time
import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import statistics
import psutil
from datetime import datetime

logger = logging.getLogger(__name__)

@dataclass
class ConnectionPoolResult:
    """Results from connection pool testing"""
    connection_count: int
    success_rate: float
    error_rate: float
    avg_connection_time: float
    max_connection_time: float
    pool_exhausted: bool
    time_to_exhaustion: Optional[float]
    error_types: Dict[str, int]
    resource_usage: Dict[str, float]
    timestamp: str

class ConnectionPoolTester:
    """Test connection pool behavior and identify exhaustion points"""
    
    def __init__(self, base_url: str):
        self.base_url = base_url
        self.auth_token: Optional[str] = None
    
    async def test_connection_pool_exhaustion(self) -> Dict[str, ConnectionPoolResult]:
        """
        Test EPIS-012: Connection pool exhaustion under load
        
        Tests progressively higher connection counts to identify:
        - Connection pool limits
        - Performance degradation points  
        - Error patterns under exhaustion
        - Resource utilization during stress
        """
        logger.info("Starting connection pool exhaustion testing...")
        
        # Test connection counts: start small, increase exponentially
        connection_counts = [5, 10, 25, 50, 100, 200, 500, 1000, 2000]
        exhaustion_results = {}
        
        for count in connection_counts:
            logger.info(f"Testing {count} concurrent connections...")
            
            try:
                result = await self._test_concurrent_connections(count)
                exhaustion_results[f"connections_{count}"] = result
                
                # Log key findings
                logger.info(
                    f"Connections {count}: Success Rate={result.success_rate:.2%}, "
                    f"Avg Time={result.avg_connection_time:.2f}ms, "
                    f"Exhausted={result.pool_exhausted}"
                )
                
                # If pool is severely exhausted (>50% failure), stop escalating
                if result.error_rate > 0.5:
                    logger.warning(f"High error rate at {count} connections, stopping escalation")
                    break
                    
                # Cool down between tests to allow pool recovery
                await asyncio.sleep(10)
                
            except Exception as e:
                logger.error(f"Connection test with {count} connections failed: {e}")
                break
        
        return exhaustion_results
    
    async def _test_concurrent_connections(self, connection_count: int) -> ConnectionPoolResult:
        """Test a specific number of concurrent connections"""
        
        connection_times = []
        success_count = 0
        error_count = 0
        error_types = {}
        start_time = time.time()
        exhaustion_time = None
        
        # Monitor resource usage
        initial_memory = psutil.Process().memory_info().rss
        max_memory = initial_memory
        cpu_samples = []
        
        # Create connection tasks
        tasks = []
        connector = aiohttp.TCPConnector(
            limit=connection_count + 50,  # Allow some overhead
            limit_per_host=connection_count + 50,
            ttl_dns_cache=300,
            use_dns_cache=True,
        )
        
        timeout = aiohttp.ClientTimeout(total=30)
        
        async with aiohttp.ClientSession(
            connector=connector, 
            timeout=timeout
        ) as session:
            
            for i in range(connection_count):
                task = asyncio.create_task(
                    self._create_test_connection(session, i)
                )
                tasks.append(task)
            
            # Execute all connections with timeout
            try:
                results = await asyncio.wait_for(
                    asyncio.gather(*tasks, return_exceptions=True),
                    timeout=60.0  # 1 minute total timeout
                )
                
                # Process results
                for i, result in enumerate(results):
                    if isinstance(result, Exception):
                        error_count += 1
                        error_type = type(result).__name__
                        error_types[error_type] = error_types.get(error_type, 0) + 1
                        
                        # Check if this is the first exhaustion
                        if exhaustion_time is None and error_count / (i + 1) > 0.1:
                            exhaustion_time = time.time() - start_time
                            
                    elif isinstance(result, dict):
                        if result['success']:
                            success_count += 1
                            connection_times.append(result['connection_time'])
                        else:
                            error_count += 1
                            error_type = result.get('error_type', 'Unknown')
                            error_types[error_type] = error_types.get(error_type, 0) + 1
                    
                    # Sample resource usage periodically
                    if i % 100 == 0:
                        current_memory = psutil.Process().memory_info().rss
                        max_memory = max(max_memory, current_memory)
                        cpu_samples.append(psutil.cpu_percent())
                
            except asyncio.TimeoutError:
                # Cancel remaining tasks
                for task in tasks:
                    if not task.done():
                        task.cancel()
                
                error_count = connection_count
                error_types['TimeoutError'] = connection_count
                exhaustion_time = 60.0
        
        # Calculate metrics
        total_time = time.time() - start_time
        success_rate = success_count / connection_count
        error_rate = error_count / connection_count
        avg_connection_time = statistics.mean(connection_times) if connection_times else 0
        max_connection_time = max(connection_times) if connection_times else 0
        
        # Determine if pool was exhausted (>10% failures indicates stress)
        pool_exhausted = error_rate > 0.1
        
        # Resource usage metrics
        memory_increase_mb = (max_memory - initial_memory) / (1024 * 1024)
        avg_cpu = statistics.mean(cpu_samples) if cpu_samples else 0
        
        resource_usage = {
            'memory_increase_mb': memory_increase_mb,
            'max_memory_mb': max_memory / (1024 * 1024),
            'avg_cpu_percent': avg_cpu,
            'test_duration_sec': total_time
        }
        
        return ConnectionPoolResult(
            connection_count=connection_count,
            success_rate=success_rate,
            error_rate=error_rate,
            avg_connection_time=avg_connection_time,
            max_connection_time=max_connection_time,
            pool_exhausted=pool_exhausted,
            time_to_exhaustion=exhaustion_time,
            error_types=error_types,
            resource_usage=resource_usage,
            timestamp=datetime.now().isoformat()
        )
    
    async def _create_test_connection(self, session: aiohttp.ClientSession, connection_id: int) -> Dict[str, Any]:
        """Create a single test connection and measure performance"""
        
        start_time = time.perf_counter()
        
        try:
            # Use health endpoint for lightweight connection test
            async with session.get(f"{self.base_url}/health") as response:
                await response.text()  # Ensure response is fully received
                end_time = time.perf_counter()
                
                connection_time = (end_time - start_time) * 1000  # Convert to ms
                
                return {
                    'connection_id': connection_id,
                    'success': response.status == 200,
                    'connection_time': connection_time,
                    'status_code': response.status,
                    'error_type': None if response.status == 200 else f"HTTP_{response.status}"
                }
                
        except asyncio.TimeoutError:
            end_time = time.perf_counter()
            return {
                'connection_id': connection_id,
                'success': False,
                'connection_time': (end_time - start_time) * 1000,
                'status_code': None,
                'error_type': 'TimeoutError'
            }
            
        except aiohttp.ClientConnectionError as e:
            end_time = time.perf_counter()
            return {
                'connection_id': connection_id,
                'success': False,
                'connection_time': (end_time - start_time) * 1000,
                'status_code': None,
                'error_type': 'ConnectionError'
            }
            
        except Exception as e:
            end_time = time.perf_counter()
            return {
                'connection_id': connection_id,
                'success': False,
                'connection_time': (end_time - start_time) * 1000,
                'status_code': None,
                'error_type': type(e).__name__
            }
    
    async def test_persistent_connections(self) -> Dict[str, Any]:
        """Test behavior with persistent connections over time"""
        logger.info("Testing persistent connection behavior...")
        
        connection_count = 50
        test_duration = 300  # 5 minutes
        
        # Create persistent connections
        connectors = []
        sessions = []
        
        try:
            for i in range(connection_count):
                connector = aiohttp.TCPConnector(
                    limit=10,
                    limit_per_host=10,
                    ttl_dns_cache=300,
                    keepalive_timeout=300,  # Keep connections alive
                    enable_cleanup_closed=True
                )
                
                session = aiohttp.ClientSession(
                    connector=connector,
                    timeout=aiohttp.ClientTimeout(total=30)
                )
                
                connectors.append(connector)
                sessions.append(session)
            
            # Test periodic requests over time
            start_time = time.time()
            request_results = []
            
            while time.time() - start_time < test_duration:
                # Make requests with all sessions
                tasks = []
                for i, session in enumerate(sessions):
                    task = asyncio.create_task(
                        self._make_persistent_request(session, i)
                    )
                    tasks.append(task)
                
                # Execute batch of requests
                batch_start = time.time()
                results = await asyncio.gather(*tasks, return_exceptions=True)
                batch_duration = time.time() - batch_start
                
                # Record batch results
                successful = sum(1 for r in results if isinstance(r, dict) and r.get('success', False))
                request_results.append({
                    'timestamp': time.time() - start_time,
                    'batch_duration': batch_duration,
                    'successful_requests': successful,
                    'total_requests': len(results),
                    'success_rate': successful / len(results)
                })
                
                # Wait before next batch
                await asyncio.sleep(10)
            
            # Calculate persistent connection metrics
            avg_success_rate = statistics.mean(r['success_rate'] for r in request_results)
            avg_batch_duration = statistics.mean(r['batch_duration'] for r in request_results)
            total_requests = sum(r['total_requests'] for r in request_results)
            total_successful = sum(r['successful_requests'] for r in request_results)
            
            return {
                'test_duration': test_duration,
                'connection_count': connection_count,
                'total_requests': total_requests,
                'total_successful': total_successful,
                'overall_success_rate': total_successful / total_requests if total_requests > 0 else 0,
                'avg_success_rate': avg_success_rate,
                'avg_batch_duration': avg_batch_duration,
                'request_batches': len(request_results),
                'connection_stability': 'stable' if avg_success_rate > 0.95 else 'degraded',
                'detailed_results': request_results
            }
            
        finally:
            # Clean up all sessions and connectors
            for session in sessions:
                await session.close()
            
            for connector in connectors:
                await connector.close()
    
    async def _make_persistent_request(self, session: aiohttp.ClientSession, session_id: int) -> Dict[str, Any]:
        """Make a request using a persistent session"""
        
        start_time = time.perf_counter()
        
        try:
            async with session.get(f"{self.base_url}/health") as response:
                await response.text()
                end_time = time.perf_counter()
                
                return {
                    'session_id': session_id,
                    'success': response.status == 200,
                    'response_time': (end_time - start_time) * 1000,
                    'status_code': response.status
                }
                
        except Exception as e:
            end_time = time.perf_counter()
            return {
                'session_id': session_id,
                'success': False,
                'response_time': (end_time - start_time) * 1000,
                'error': str(e)
            }
    
    async def test_database_connection_pooling(self) -> Dict[str, Any]:
        """
        Test EPIS-076: Database connection pooling optimization
        
        Tests database connection behavior through API endpoints that
        trigger database operations.
        """
        logger.info("Testing database connection pooling through API calls...")
        
        # Test different concurrency levels for database-heavy operations
        concurrency_levels = [5, 10, 25, 50, 100]
        database_test_results = {}
        
        for concurrency in concurrency_levels:
            logger.info(f"Testing database operations with {concurrency} concurrent requests...")
            
            result = await self._test_database_heavy_operations(concurrency)
            database_test_results[f"concurrency_{concurrency}"] = result
            
            # Brief pause between tests
            await asyncio.sleep(5)
        
        return database_test_results
    
    async def _test_database_heavy_operations(self, concurrency: int) -> Dict[str, Any]:
        """Test database operations under concurrent load"""
        
        # Use endpoints that likely hit the database
        endpoints = [
            '/api/v1/teams',
            '/api/v1/sessions', 
            '/health'  # May include database health checks
        ]
        
        response_times = []
        success_count = 0
        error_count = 0
        start_time = time.time()
        
        # Create tasks for concurrent database operations
        tasks = []
        
        async with aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30)
        ) as session:
            
            for i in range(concurrency):
                endpoint = endpoints[i % len(endpoints)]
                task = asyncio.create_task(
                    self._make_database_request(session, endpoint, i)
                )
                tasks.append(task)
            
            # Execute all database operations concurrently
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Process results
            for result in results:
                if isinstance(result, Exception):
                    error_count += 1
                elif isinstance(result, dict):
                    if result['success']:
                        success_count += 1
                        response_times.append(result['response_time'])
                    else:
                        error_count += 1
        
        total_time = time.time() - start_time
        
        return {
            'concurrency': concurrency,
            'total_requests': len(tasks),
            'successful_requests': success_count,
            'failed_requests': error_count,
            'success_rate': success_count / len(tasks),
            'avg_response_time': statistics.mean(response_times) if response_times else 0,
            'p95_response_time': self._percentile(response_times, 95) if response_times else 0,
            'throughput': len(tasks) / total_time,
            'total_duration': total_time,
            'database_stress_detected': statistics.mean(response_times) > 1000 if response_times else False  # >1s indicates stress
        }
    
    async def _make_database_request(self, session: aiohttp.ClientSession, endpoint: str, request_id: int) -> Dict[str, Any]:
        """Make a request to an endpoint that uses the database"""
        
        start_time = time.perf_counter()
        
        try:
            headers = {}
            if self.auth_token and endpoint.startswith('/api'):
                headers['Authorization'] = f'Bearer {self.auth_token}'
            
            async with session.get(f"{self.base_url}{endpoint}", headers=headers) as response:
                await response.text()
                end_time = time.perf_counter()
                
                return {
                    'request_id': request_id,
                    'endpoint': endpoint,
                    'success': response.status == 200,
                    'response_time': (end_time - start_time) * 1000,
                    'status_code': response.status
                }
                
        except Exception as e:
            end_time = time.perf_counter()
            return {
                'request_id': request_id,
                'endpoint': endpoint,
                'success': False,
                'response_time': (end_time - start_time) * 1000,
                'error': str(e)
            }
    
    def _percentile(self, data: List[float], percentile: float) -> float:
        """Calculate percentile value"""
        if not data:
            return 0.0
        sorted_data = sorted(data)
        index = int((percentile / 100.0) * len(sorted_data))
        if index >= len(sorted_data):
            index = len(sorted_data) - 1
        return sorted_data[index]

# Example usage
if __name__ == "__main__":
    async def main():
        base_url = "https://collaboration-engine-production-xxxxx.run.app"
        tester = ConnectionPoolTester(base_url)
        
        logger.info("Starting connection pool testing...")
        
        try:
            # Test connection pool exhaustion
            exhaustion_results = await tester.test_connection_pool_exhaustion()
            print("Connection Pool Exhaustion Results:")
            for test_name, result in exhaustion_results.items():
                print(f"  {test_name}: Success Rate={result.success_rate:.2%}, Exhausted={result.pool_exhausted}")
            
            # Test persistent connections
            persistent_results = await tester.test_persistent_connections()
            print(f"\nPersistent Connection Results:")
            print(f"  Overall Success Rate: {persistent_results['overall_success_rate']:.2%}")
            print(f"  Connection Stability: {persistent_results['connection_stability']}")
            
            # Test database connection pooling
            database_results = await tester.test_database_connection_pooling()
            print(f"\nDatabase Connection Pool Results:")
            for test_name, result in database_results.items():
                print(f"  {test_name}: Success Rate={result['success_rate']:.2%}, Stress Detected={result['database_stress_detected']}")
            
        except Exception as e:
            logger.error(f"Connection pool testing failed: {e}")
    
    asyncio.run(main())