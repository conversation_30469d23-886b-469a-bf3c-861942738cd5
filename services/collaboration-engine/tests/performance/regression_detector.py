"""
Performance Regression Detection
Automated detection of performance regressions against established baselines

This module provides comprehensive performance regression detection including:
- Baseline comparison and drift analysis
- Statistical significance testing
- Automated threshold-based alerts
- Performance trend analysis
- Multi-metric regression scoring

Author: Wave 5 Load & Performance Testing Agent
Date: 2025-08-03
"""

import json
import statistics
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import numpy as np
from pathlib import Path
import os

logger = logging.getLogger(__name__)

@dataclass
class RegressionAlert:
    """Performance regression alert"""
    metric_name: str
    endpoint: str
    baseline_value: float
    current_value: float
    regression_percentage: float
    severity: str  # 'minor', 'moderate', 'severe', 'critical'
    significance: float  # Statistical significance (0-1)
    threshold_exceeded: str  # Which threshold was exceeded
    recommendation: str
    timestamp: str

@dataclass
class RegressionAnalysis:
    """Complete regression analysis results"""
    test_timestamp: str
    baseline_timestamp: str
    total_metrics_compared: int
    regressions_detected: int
    improvements_detected: int
    stable_metrics: int
    overall_regression_score: float  # 0-10 scale
    alerts: List[RegressionAlert]
    summary: Dict[str, Any]
    recommendations: List[str]

class PerformanceRegressionDetector:
    """Detect performance regressions through statistical analysis"""
    
    def __init__(self, baseline_file: str = "performance_baseline.json"):
        self.baseline_file = baseline_file
        self.regression_thresholds = {
            'minor': 0.10,      # 10% degradation
            'moderate': 0.20,   # 20% degradation  
            'severe': 0.35,     # 35% degradation
            'critical': 0.50    # 50% degradation
        }
        self.improvement_threshold = -0.10  # 10% improvement
        self.significance_threshold = 0.95   # 95% confidence for statistical significance
        
    def load_baseline(self) -> Optional[Dict[str, Any]]:
        """Load performance baseline from file"""
        
        if not os.path.exists(self.baseline_file):
            logger.warning(f"Baseline file {self.baseline_file} not found")
            return None
        
        try:
            with open(self.baseline_file, 'r') as f:
                baseline = json.load(f)
            
            logger.info(f"Loaded baseline from {self.baseline_file}")
            return baseline
            
        except Exception as e:
            logger.error(f"Failed to load baseline: {e}")
            return None
    
    def save_baseline(self, metrics: Dict[str, Any]) -> bool:
        """Save current metrics as new baseline"""
        
        try:
            baseline_data = {
                'created_timestamp': datetime.now().isoformat(),
                'metrics': metrics,
                'version': '1.0',
                'description': 'Performance baseline for regression detection'
            }
            
            with open(self.baseline_file, 'w') as f:
                json.dump(baseline_data, f, indent=2)
            
            logger.info(f"Saved new baseline to {self.baseline_file}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save baseline: {e}")
            return False
    
    def compare_with_baseline(self, current_metrics: Dict[str, Any]) -> RegressionAnalysis:
        """
        Compare current performance metrics with baseline
        
        Performs comprehensive regression analysis including:
        - Statistical significance testing
        - Threshold-based alerting
        - Trend analysis
        - Severity classification
        """
        
        baseline = self.load_baseline()
        if not baseline:
            logger.warning("No baseline available - creating new baseline")
            self.save_baseline(current_metrics)
            return self._create_no_baseline_analysis()
        
        baseline_metrics = baseline.get('metrics', {})
        baseline_timestamp = baseline.get('created_timestamp', 'unknown')
        
        logger.info("Starting regression analysis against baseline...")
        
        alerts = []
        total_metrics = 0
        regressions_detected = 0
        improvements_detected = 0
        stable_metrics = 0
        regression_scores = []
        
        # Compare baseline endpoint metrics
        if 'baseline' in current_metrics and 'baseline' in baseline_metrics:
            current_baseline = current_metrics['baseline']
            baseline_baseline = baseline_metrics['baseline']
            
            for endpoint_name in current_baseline:
                if endpoint_name not in baseline_baseline:
                    logger.debug(f"New endpoint detected: {endpoint_name}")
                    continue
                
                current_endpoint = current_baseline[endpoint_name]
                baseline_endpoint = baseline_baseline[endpoint_name]
                
                # Compare key performance metrics
                metric_comparisons = [
                    ('mean', 'Average Response Time'),
                    ('p95', '95th Percentile Response Time'),
                    ('p99', '99th Percentile Response Time'),
                    ('error_rate', 'Error Rate'),
                    ('throughput', 'Throughput')
                ]
                
                for metric_key, metric_name in metric_comparisons:
                    if metric_key in current_endpoint and metric_key in baseline_endpoint:
                        alert = self._analyze_metric_regression(
                            metric_name,
                            endpoint_name,
                            baseline_endpoint[metric_key],
                            current_endpoint[metric_key],
                            metric_key
                        )
                        
                        if alert:
                            alerts.append(alert)
                            if alert.regression_percentage > 0:
                                regressions_detected += 1
                                regression_scores.append(self._calculate_regression_score(alert))
                            else:
                                improvements_detected += 1
                        else:
                            stable_metrics += 1
                        
                        total_metrics += 1
        
        # Compare load test metrics
        if 'load_tests' in current_metrics and 'load_tests' in baseline_metrics:
            load_alerts = self._compare_load_test_metrics(
                baseline_metrics['load_tests'],
                current_metrics['load_tests']
            )
            alerts.extend(load_alerts)
            
            for alert in load_alerts:
                if alert.regression_percentage > 0:
                    regressions_detected += 1
                    regression_scores.append(self._calculate_regression_score(alert))
                else:
                    improvements_detected += 1
                total_metrics += 1
        
        # Calculate overall regression score (0-10 scale)
        if regression_scores:
            overall_regression_score = min(10.0, statistics.mean(regression_scores))
        else:
            overall_regression_score = 0.0
        
        # Generate summary and recommendations
        summary = self._generate_summary(
            total_metrics, regressions_detected, improvements_detected, 
            stable_metrics, overall_regression_score, alerts
        )
        
        recommendations = self._generate_recommendations(alerts, overall_regression_score)
        
        analysis = RegressionAnalysis(
            test_timestamp=datetime.now().isoformat(),
            baseline_timestamp=baseline_timestamp,
            total_metrics_compared=total_metrics,
            regressions_detected=regressions_detected,
            improvements_detected=improvements_detected,
            stable_metrics=stable_metrics,
            overall_regression_score=overall_regression_score,
            alerts=alerts,
            summary=summary,
            recommendations=recommendations
        )
        
        logger.info(f"Regression analysis completed: {regressions_detected} regressions, {improvements_detected} improvements")
        
        return analysis
    
    def _analyze_metric_regression(
        self,
        metric_name: str,
        endpoint: str,
        baseline_value: float,
        current_value: float,
        metric_type: str
    ) -> Optional[RegressionAlert]:
        """Analyze a single metric for regression"""
        
        # Skip if baseline value is 0 (avoid division by zero)
        if baseline_value == 0:
            return None
        
        # Calculate regression percentage
        if metric_type == 'error_rate':
            # For error rates, higher is worse
            regression_percentage = (current_value - baseline_value) / baseline_value
        elif metric_type == 'throughput':
            # For throughput, lower is worse (so flip the sign)
            regression_percentage = -(current_value - baseline_value) / baseline_value
        else:
            # For response times, higher is worse
            regression_percentage = (current_value - baseline_value) / baseline_value
        
        # Determine if this represents a significant change
        abs_regression = abs(regression_percentage)
        
        # Check if change exceeds minimum threshold for detection
        if abs_regression < 0.05:  # Less than 5% change is considered noise
            return None
        
        # Classify severity
        severity = self._classify_severity(abs_regression)
        
        # Calculate statistical significance (simplified)
        significance = self._calculate_significance(baseline_value, current_value, abs_regression)
        
        # Determine which threshold was exceeded
        threshold_exceeded = self._determine_threshold_exceeded(abs_regression)
        
        # Generate recommendation
        recommendation = self._generate_metric_recommendation(
            metric_name, endpoint, regression_percentage, severity
        )
        
        return RegressionAlert(
            metric_name=metric_name,
            endpoint=endpoint,
            baseline_value=baseline_value,
            current_value=current_value,
            regression_percentage=regression_percentage,
            severity=severity,
            significance=significance,
            threshold_exceeded=threshold_exceeded,
            recommendation=recommendation,
            timestamp=datetime.now().isoformat()
        )
    
    def _compare_load_test_metrics(
        self,
        baseline_load_tests: Dict[str, Any],
        current_load_tests: Dict[str, Any]
    ) -> List[RegressionAlert]:
        """Compare load test metrics for regressions"""
        
        alerts = []
        
        for test_name in current_load_tests:
            if test_name not in baseline_load_tests:
                continue
            
            current_test = current_load_tests[test_name]
            baseline_test = baseline_load_tests[test_name]
            
            # Compare key load test metrics
            load_metrics = [
                ('avg_response_time', 'Load Test Average Response Time'),
                ('p95_response_time', 'Load Test P95 Response Time'),
                ('throughput', 'Load Test Throughput'),
                ('error_rate', 'Load Test Error Rate')
            ]
            
            for metric_key, metric_name in load_metrics:
                if (metric_key in current_test and metric_key in baseline_test and
                    isinstance(current_test[metric_key], (int, float)) and
                    isinstance(baseline_test[metric_key], (int, float))):
                    
                    alert = self._analyze_metric_regression(
                        metric_name,
                        test_name,
                        baseline_test[metric_key],
                        current_test[metric_key],
                        metric_key
                    )
                    
                    if alert:
                        alerts.append(alert)
        
        return alerts
    
    def _classify_severity(self, regression_percentage: float) -> str:
        """Classify regression severity based on percentage"""
        
        if regression_percentage >= self.regression_thresholds['critical']:
            return 'critical'
        elif regression_percentage >= self.regression_thresholds['severe']:
            return 'severe'
        elif regression_percentage >= self.regression_thresholds['moderate']:
            return 'moderate'
        elif regression_percentage >= self.regression_thresholds['minor']:
            return 'minor'
        else:
            return 'improvement' if regression_percentage < self.improvement_threshold else 'stable'
    
    def _calculate_significance(
        self,
        baseline_value: float,
        current_value: float,
        regression_percentage: float
    ) -> float:
        """Calculate statistical significance of the change (simplified)"""
        
        # Simplified significance calculation based on magnitude and consistency
        # In a real implementation, you would use proper statistical tests
        
        magnitude_factor = min(1.0, abs(regression_percentage) / 0.1)  # Scale by 10% baseline
        
        # Higher confidence for larger changes and consistent patterns
        base_significance = magnitude_factor * 0.8
        
        # Add noise factor (assume some measurement noise)
        noise_adjustment = 0.1 * (1 - abs(regression_percentage))
        
        significance = min(1.0, base_significance + noise_adjustment)
        
        return significance
    
    def _determine_threshold_exceeded(self, regression_percentage: float) -> str:
        """Determine which regression threshold was exceeded"""
        
        if regression_percentage >= self.regression_thresholds['critical']:
            return 'critical'
        elif regression_percentage >= self.regression_thresholds['severe']:
            return 'severe'
        elif regression_percentage >= self.regression_thresholds['moderate']:
            return 'moderate'
        elif regression_percentage >= self.regression_thresholds['minor']:
            return 'minor'
        else:
            return 'none'
    
    def _calculate_regression_score(self, alert: RegressionAlert) -> float:
        """Calculate numeric regression score for an alert (0-10 scale)"""
        
        severity_scores = {
            'minor': 2.0,
            'moderate': 4.0, 
            'severe': 7.0,
            'critical': 10.0
        }
        
        base_score = severity_scores.get(alert.severity, 0.0)
        
        # Adjust by significance
        significance_multiplier = alert.significance
        
        # Adjust by absolute regression percentage
        magnitude_multiplier = min(2.0, abs(alert.regression_percentage) / 0.5)  # Cap at 2x for 50% regression
        
        final_score = base_score * significance_multiplier * magnitude_multiplier
        
        return min(10.0, final_score)
    
    def _generate_metric_recommendation(
        self,
        metric_name: str,
        endpoint: str,
        regression_percentage: float,
        severity: str
    ) -> str:
        """Generate specific recommendation for a metric regression"""
        
        if regression_percentage < 0:
            return f"✅ Performance improved for {metric_name} on {endpoint} by {abs(regression_percentage):.1%}"
        
        base_recommendations = {
            'critical': f"🚨 IMMEDIATE ACTION REQUIRED: {metric_name} on {endpoint} degraded by {regression_percentage:.1%}",
            'severe': f"⚠️ URGENT: Investigate {metric_name} degradation on {endpoint} ({regression_percentage:.1%})",
            'moderate': f"📊 Monitor {metric_name} on {endpoint} - degraded by {regression_percentage:.1%}",
            'minor': f"📈 Minor degradation in {metric_name} on {endpoint} ({regression_percentage:.1%})"
        }
        
        base_rec = base_recommendations.get(severity, f"Monitor {metric_name} on {endpoint}")
        
        # Add specific suggestions based on metric type
        if 'Response Time' in metric_name:
            if severity in ['critical', 'severe']:
                base_rec += " - Check for database connection issues, memory leaks, or increased load"
            else:
                base_rec += " - Monitor for trends and consider optimization"
        elif 'Error Rate' in metric_name:
            if severity in ['critical', 'severe']:
                base_rec += " - Investigate error logs immediately, check service dependencies"
            else:
                base_rec += " - Review error patterns and error handling"
        elif 'Throughput' in metric_name:
            if severity in ['critical', 'severe']:
                base_rec += " - Check resource utilization, connection pools, and scaling"
            else:
                base_rec += " - Monitor capacity and consider performance tuning"
        
        return base_rec
    
    def _generate_summary(
        self,
        total_metrics: int,
        regressions: int,
        improvements: int,
        stable: int,
        overall_score: float,
        alerts: List[RegressionAlert]
    ) -> Dict[str, Any]:
        """Generate regression analysis summary"""
        
        # Categorize alerts by severity
        severity_counts = {'critical': 0, 'severe': 0, 'moderate': 0, 'minor': 0}
        for alert in alerts:
            if alert.regression_percentage > 0:  # Only count regressions, not improvements
                severity_counts[alert.severity] = severity_counts.get(alert.severity, 0) + 1
        
        # Determine overall health status
        if severity_counts['critical'] > 0:
            health_status = 'CRITICAL'
        elif severity_counts['severe'] > 0:
            health_status = 'SEVERE'
        elif severity_counts['moderate'] > 0:
            health_status = 'DEGRADED'
        elif severity_counts['minor'] > 0:
            health_status = 'MINOR_ISSUES'
        else:
            health_status = 'HEALTHY'
        
        return {
            'total_metrics_analyzed': total_metrics,
            'regressions_detected': regressions,
            'improvements_detected': improvements,
            'stable_metrics': stable,
            'regression_rate': regressions / total_metrics if total_metrics > 0 else 0,
            'improvement_rate': improvements / total_metrics if total_metrics > 0 else 0,
            'overall_regression_score': overall_score,
            'health_status': health_status,
            'severity_breakdown': severity_counts,
            'requires_immediate_attention': severity_counts['critical'] > 0 or severity_counts['severe'] > 0
        }
    
    def _generate_recommendations(
        self,
        alerts: List[RegressionAlert],
        overall_score: float
    ) -> List[str]:
        """Generate high-level recommendations based on analysis"""
        
        recommendations = []
        
        # Count critical and severe issues
        critical_alerts = [a for a in alerts if a.severity == 'critical' and a.regression_percentage > 0]
        severe_alerts = [a for a in alerts if a.severity == 'severe' and a.regression_percentage > 0]
        
        if critical_alerts:
            recommendations.append(
                f"🚨 CRITICAL: {len(critical_alerts)} critical performance regressions detected - "
                "immediate investigation and remediation required"
            )
        
        if severe_alerts:
            recommendations.append(
                f"⚠️ SEVERE: {len(severe_alerts)} severe performance issues detected - "
                "priority investigation recommended"
            )
        
        if overall_score > 7.0:
            recommendations.append(
                "📊 Overall performance significantly degraded - consider rollback or emergency fixes"
            )
        elif overall_score > 4.0:
            recommendations.append(
                "📈 Performance degradation detected - increase monitoring and plan optimizations"
            )
        elif overall_score < 1.0:
            recommendations.append(
                "✅ Performance appears stable or improved - no immediate action required"
            )
        
        # Add specific technical recommendations
        response_time_alerts = [a for a in alerts if 'Response Time' in a.metric_name and a.regression_percentage > 0]
        if len(response_time_alerts) > 2:
            recommendations.append(
                "🔧 Multiple response time regressions detected - check database performance, "
                "connection pools, and memory usage"
            )
        
        error_rate_alerts = [a for a in alerts if 'Error Rate' in a.metric_name and a.regression_percentage > 0]
        if error_rate_alerts:
            recommendations.append(
                "🐛 Error rate increases detected - review error logs, check service dependencies, "
                "and validate error handling"
            )
        
        throughput_alerts = [a for a in alerts if 'Throughput' in a.metric_name and a.regression_percentage > 0]
        if throughput_alerts:
            recommendations.append(
                "📉 Throughput degradation detected - check resource utilization, scaling policies, "
                "and bottlenecks"
            )
        
        if not recommendations:
            recommendations.append(
                "✅ No significant performance regressions detected - maintain current monitoring"
            )
        
        return recommendations
    
    def _create_no_baseline_analysis(self) -> RegressionAnalysis:
        """Create analysis result when no baseline exists"""
        
        return RegressionAnalysis(
            test_timestamp=datetime.now().isoformat(),
            baseline_timestamp='none',
            total_metrics_compared=0,
            regressions_detected=0,
            improvements_detected=0,
            stable_metrics=0,
            overall_regression_score=0.0,
            alerts=[],
            summary={
                'total_metrics_analyzed': 0,
                'health_status': 'BASELINE_CREATED',
                'requires_immediate_attention': False
            },
            recommendations=[
                "📊 New performance baseline created - run tests regularly to establish trend data",
                "🔄 Re-run performance tests to enable regression detection"
            ]
        )
    
    def export_analysis(self, analysis: RegressionAnalysis, filename: str = None) -> str:
        """Export regression analysis to JSON file"""
        
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"regression_analysis_{timestamp}.json"
        
        # Convert analysis to dict, handling dataclass serialization
        analysis_dict = asdict(analysis)
        
        with open(filename, 'w') as f:
            json.dump(analysis_dict, f, indent=2)
        
        logger.info(f"Regression analysis exported to {filename}")
        return filename

# Example usage
if __name__ == "__main__":
    def main():
        detector = PerformanceRegressionDetector()
        
        # Example: Load current metrics and compare with baseline
        # In practice, these would come from your performance testing framework
        
        current_metrics = {
            'baseline': {
                'health_check': {
                    'mean': 15.2,
                    'p95': 25.8,
                    'p99': 35.1,
                    'error_rate': 0.02,
                    'throughput': 45.3
                },
                'teams_list': {
                    'mean': 125.5,
                    'p95': 245.2,
                    'p99': 385.7,
                    'error_rate': 0.01,
                    'throughput': 28.7
                }
            },
            'load_tests': {
                'Standard Load': {
                    'avg_response_time': 185.3,
                    'p95_response_time': 325.8,
                    'throughput': 24.5,
                    'error_rate': 0.03
                }
            }
        }
        
        # Perform regression analysis
        analysis = detector.compare_with_baseline(current_metrics)
        
        # Print results
        print(f"\n=== PERFORMANCE REGRESSION ANALYSIS ===")
        print(f"Health Status: {analysis.summary['health_status']}")
        print(f"Overall Regression Score: {analysis.overall_regression_score:.2f}/10")
        print(f"Regressions Detected: {analysis.regressions_detected}")
        print(f"Improvements Detected: {analysis.improvements_detected}")
        
        if analysis.alerts:
            print(f"\n=== ALERTS ===")
            for alert in analysis.alerts:
                regression_direction = "↑" if alert.regression_percentage > 0 else "↓"
                print(f"{alert.severity.upper()}: {alert.metric_name} on {alert.endpoint}")
                print(f"  {regression_direction} {abs(alert.regression_percentage):.1%} change")
                print(f"  {alert.recommendation}")
        
        if analysis.recommendations:
            print(f"\n=== RECOMMENDATIONS ===")
            for i, rec in enumerate(analysis.recommendations, 1):
                print(f"{i}. {rec}")
        
        # Export analysis
        results_file = detector.export_analysis(analysis)
        print(f"\nAnalysis exported to: {results_file}")
    
    main()