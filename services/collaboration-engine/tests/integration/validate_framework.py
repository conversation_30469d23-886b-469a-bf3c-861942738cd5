#!/usr/bin/env python3
"""
Integration Test Framework Validation Script
Part of EPIS-017: Integration testing framework

This script validates that the integration test framework is properly set up
and can connect to the collaboration engine service.
"""

import asyncio
import sys
import os
from pathlib import Path

# Add current directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

import httpx
from conftest import TestConfig, AuthHelper, check_service_health
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def validate_framework():
    """Validate the integration test framework"""
    print("🔍 Validating Integration Test Framework...")
    print("=" * 50)
    
    # Load configuration
    config = TestConfig.from_env()
    print(f"Configuration loaded:")
    print(f"  Collaboration Engine: {config.base_url}")
    print(f"  Analysis Engine: {config.analysis_engine_url}")
    print(f"  Timeout: {config.timeout}s")
    
    validation_results = {
        "config_loaded": True,
        "service_reachable": False,
        "auth_working": False,
        "framework_ready": False
    }
    
    async with httpx.AsyncClient(timeout=config.timeout) as client:
        # Test 1: Service Health Check
        print(f"\\n1️⃣ Testing service connectivity...")
        service_health = await check_service_health(client, config.base_url)
        validation_results["service_reachable"] = service_health["healthy"]
        
        if service_health["healthy"]:
            print(f"✅ Collaboration Engine is reachable")
        else:
            print(f"❌ Collaboration Engine not reachable: {service_health['response']}")
            return validation_results
        
        # Test 2: Authentication Flow
        print(f"\\n2️⃣ Testing authentication flow...")
        auth_helper = AuthHelper(client, config.base_url)
        
        try:
            auth_result = await auth_helper.login("<EMAIL>", "Framework Test User")
            
            if "error" not in auth_result and auth_helper.user_id:
                print(f"✅ Authentication successful")
                print(f"   User ID: {auth_helper.user_id}")
                print(f"   Token: {auth_helper._token[:20]}..." if auth_helper._token else "No token")
                validation_results["auth_working"] = True
            else:
                print(f"❌ Authentication failed: {auth_result.get('error', 'Unknown error')}")
                return validation_results
                
        except Exception as e:
            print(f"❌ Authentication exception: {e}")
            return validation_results
        
        # Test 3: Basic API Call
        print(f"\\n3️⃣ Testing API functionality...")
        try:
            response = await client.get(
                f"{config.base_url}/teams",
                headers=auth_helper.headers,
                timeout=30.0
            )
            
            if response.status_code == 200:
                print(f"✅ API call successful (GET /teams)")
                teams = response.json().get("teams", [])
                print(f"   Response: {len(teams)} teams found")
            else:
                print(f"⚠️ API call returned status {response.status_code}")
                print(f"   This might be expected if no teams exist")
            
        except Exception as e:
            print(f"❌ API call failed: {e}")
            return validation_results
        
        # Test 4: Analysis Engine Check
        print(f"\\n4️⃣ Testing analysis engine connectivity...")
        analysis_health = await check_service_health(client, config.analysis_engine_url)
        
        if analysis_health["healthy"]:
            print(f"✅ Analysis Engine is reachable")
        else:
            print(f"⚠️ Analysis Engine not reachable: {analysis_health['response']}")
            print(f"   This is acceptable - some integration tests may skip")
        
        # Test 5: Framework Components
        print(f"\\n5️⃣ Checking framework components...")
        
        # Check if all test files exist
        test_files = [
            "conftest.py",
            "test_authentication.py",
            "test_team_management.py", 
            "test_session_management.py",
            "test_cross_service.py",
            "test_error_handling.py",
            "test_collaboration_features.py"
        ]
        
        missing_files = []
        for test_file in test_files:
            if not Path(test_file).exists():
                missing_files.append(test_file)
        
        if missing_files:
            print(f"❌ Missing test files: {missing_files}")
            return validation_results
        else:
            print(f"✅ All test files present ({len(test_files)} files)")
        
        # Check requirements file
        if Path("requirements.txt").exists():
            print(f"✅ Requirements file present")
        else:
            print(f"⚠️ Requirements file missing")
        
        validation_results["framework_ready"] = True
    
    return validation_results

async def main():
    """Main entry point"""
    try:
        results = await validate_framework()
        
        print(f"\\n" + "=" * 50)
        print("VALIDATION SUMMARY")
        print("=" * 50)
        
        total_checks = len(results)
        passed_checks = sum(results.values())
        
        for check, result in results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{check.replace('_', ' ').title()}: {status}")
        
        print(f"\\nOverall: {passed_checks}/{total_checks} checks passed")
        
        if results["framework_ready"]:
            print(f"\\n🎉 Integration Test Framework is ready!")
            print(f"\\n📋 EPIS-017 Progress: FRAMEWORK_CREATED")
            print(f"\\nNext steps:")
            print(f"1. Install requirements: pip install -r requirements.txt")
            print(f"2. Run full test suite: python run_integration_tests.py")
            print(f"3. View detailed results in test_report.html")
            return 0
        else:
            print(f"\\n⚠️ Framework validation failed")
            print(f"Please check the failed components above")
            return 1
            
    except Exception as e:
        print(f"\\n❌ Framework validation error: {e}")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)