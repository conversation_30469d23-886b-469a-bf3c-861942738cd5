"""
Cross-Service Integration Tests for Collaboration Engine
Part of EPIS-017: Integration testing framework

Tests integration with analysis-engine and other external services.
"""

import pytest
import httpx
import json
import uuid
from typing import Dict, Any, Optional
import logging

from .conftest import TestDataGenerator, check_service_health

logger = logging.getLogger(__name__)

class TestCrossServiceIntegration:
    """Test cross-service integration functionality"""
    
    @pytest.mark.asyncio
    async def test_integration_health_check(self, http_client: httpx.AsyncClient,
                                          test_config, integration_results):
        """Test integration services health endpoint"""
        try:
            response = await http_client.get(
                f"{test_config.base_url}/integration/health",
                timeout=30.0
            )
            
            success = response.status_code in [200, 503]  # 503 = Service Unavailable (degraded)
            details = f"Status: {response.status_code}"
            
            if response.status_code == 200:
                response_data = response.json()
                has_status = "status" in response_data
                has_services = "services" in response_data
                details += f", Has status: {has_status}, Has services: {has_services}"
                
                if has_services:
                    services = response_data["services"]
                    analysis_engine_info = services.get("analysis_engine", {})
                    details += f", Analysis engine URL: {analysis_engine_info.get('url', 'N/A')}"
                    details += f", Analysis engine healthy: {analysis_engine_info.get('healthy', False)}"
            else:
                details += f", Response: {response.text[:200]}"
            
            integration_results.add_result(
                "integration_health_check", "integration_apis", success, details
            )
            
            assert success, f"Integration health check failed: {details}"
            
            return response.json() if response.status_code == 200 else {}
            
        except Exception as e:
            integration_results.add_error("integration_health_check", str(e))
            pytest.fail(f"Integration health check exception: {e}")
    
    @pytest.mark.asyncio
    async def test_analysis_engine_connectivity(self, http_client: httpx.AsyncClient,
                                              test_config, integration_results):
        """Test direct connectivity to analysis-engine"""
        analysis_engine_health = await check_service_health(
            http_client, test_config.analysis_engine_url
        )
        
        success = analysis_engine_health["healthy"]
        details = f"Analysis Engine ({test_config.analysis_engine_url}): {analysis_engine_health}"
        
        integration_results.add_result(
            "analysis_engine_connectivity", "integration_apis", success, details
        )
        
        if not success:
            logger.warning(f"Analysis engine not available: {details}")
            # Don't fail the test as this might be expected in some environments
        
        return analysis_engine_health
    
    @pytest.mark.asyncio
    async def test_share_analysis_in_session(self, auth_helper, test_session, 
                                           http_client: httpx.AsyncClient,
                                           test_config, integration_results):
        """Test sharing analysis results in a session"""
        auth_data = await auth_helper.login()
        if "error" in auth_data:
            pytest.skip(f"Cannot test without authentication: {auth_data['error']}")
        
        session_id = test_session["id"]
        analysis_share_data = TestDataGenerator.analysis_share_data()
        
        try:
            response = await http_client.post(
                f"{test_config.base_url}/sessions/{session_id}/share/analysis",
                json=analysis_share_data,
                headers=auth_helper.headers,
                timeout=30.0
            )
            
            # Analysis sharing might fail if analysis-engine is unavailable
            # We consider various statuses as valid test outcomes
            valid_statuses = [200, 404, 503]  # 404 = Analysis not found, 503 = Service unavailable
            success = response.status_code in valid_statuses
            details = f"Status: {response.status_code}, Valid statuses: {valid_statuses}"
            
            if response.status_code == 200:
                response_data = response.json()
                has_analysis_id = "analysis_id" in response_data
                has_session_id = "session_id" in response_data
                correct_session = response_data.get("session_id") == session_id
                details += f", Has analysis_id: {has_analysis_id}, Has session_id: {has_session_id}, Correct session: {correct_session}"
            elif response.status_code == 404:
                details += ", Analysis not found (expected for test data)"
            elif response.status_code == 503:
                details += ", Service unavailable (analysis-engine down)"
            else:
                details += f", Response: {response.text[:200]}"
            
            integration_results.add_result(
                "share_analysis_in_session", "integration_apis", success, details
            )
            
            assert success, f"Share analysis test failed: {details}"
            
        except Exception as e:
            integration_results.add_error("share_analysis_in_session", str(e))
            pytest.fail(f"Share analysis exception: {e}")
    
    @pytest.mark.asyncio
    async def test_share_query_in_session(self, auth_helper, test_session,
                                        http_client: httpx.AsyncClient,
                                        test_config, integration_results):
        """Test sharing query results in a session"""
        auth_data = await auth_helper.login()
        if "error" in auth_data:
            pytest.skip(f"Cannot test without authentication: {auth_data['error']}")
        
        session_id = test_session["id"]
        query_data = {
            "query_text": "test integration query",
            "query_type": "semantic_search",
            "context": {
                "file_types": ["rs", "py"],
                "search_scope": "repository"
            }
        }
        
        try:
            response = await http_client.post(
                f"{test_config.base_url}/sessions/{session_id}/share/query",
                json=query_data,
                headers=auth_helper.headers,
                timeout=30.0
            )
            
            # Query sharing uses mock data so should work
            success = response.status_code == 200
            details = f"Status: {response.status_code}"
            
            if success:
                response_data = response.json()
                has_query_id = "query_id" in response_data
                has_session_id = "session_id" in response_data
                has_results = "results" in response_data
                correct_session = response_data.get("session_id") == session_id
                details += f", Has query_id: {has_query_id}, Has session_id: {has_session_id}, Has results: {has_results}, Correct session: {correct_session}"
                success = has_query_id and has_session_id and has_results and correct_session
            else:
                details += f", Response: {response.text[:200]}"
            
            integration_results.add_result(
                "share_query_in_session", "integration_apis", success, details
            )
            
            assert success, f"Share query failed: {details}"
            
            if success:
                return response_data
            
        except Exception as e:
            integration_results.add_error("share_query_in_session", str(e))
            pytest.fail(f"Share query exception: {e}")
    
    @pytest.mark.asyncio
    async def test_get_shared_analyses(self, auth_helper, test_session,
                                     http_client: httpx.AsyncClient,
                                     test_config, integration_results):
        """Test retrieving shared analyses for a session"""
        auth_data = await auth_helper.login()
        if "error" in auth_data:
            pytest.skip(f"Cannot test without authentication: {auth_data['error']}")
        
        session_id = test_session["id"]
        
        try:
            response = await http_client.get(
                f"{test_config.base_url}/sessions/{session_id}/shared/analyses",
                headers=auth_helper.headers,
                timeout=30.0
            )
            
            success = response.status_code == 200
            details = f"Status: {response.status_code}"
            
            if success:
                response_data = response.json()
                has_session_id = "session_id" in response_data
                has_shared_analyses = "shared_analyses" in response_data
                has_count = "count" in response_data
                analyses = response_data.get("shared_analyses", [])
                is_list = isinstance(analyses, list)
                details += f", Has session_id: {has_session_id}, Has shared_analyses: {has_shared_analyses}, Has count: {has_count}, Is list: {is_list}, Count: {len(analyses)}"
                success = has_session_id and has_shared_analyses and has_count and is_list
            else:
                details += f", Response: {response.text[:200]}"
            
            integration_results.add_result(
                "get_shared_analyses", "integration_apis", success, details
            )
            
            assert success, f"Get shared analyses failed: {details}"
            
        except Exception as e:
            integration_results.add_error("get_shared_analyses", str(e))
            pytest.fail(f"Get shared analyses exception: {e}")
    
    @pytest.mark.asyncio
    async def test_get_shared_queries(self, auth_helper, test_session,
                                    http_client: httpx.AsyncClient,
                                    test_config, integration_results):
        """Test retrieving shared queries for a session"""
        auth_data = await auth_helper.login()
        if "error" in auth_data:
            pytest.skip(f"Cannot test without authentication: {auth_data['error']}")
        
        session_id = test_session["id"]
        
        # First share a query to ensure there's something to retrieve
        query_data = {
            "query_text": "test query for retrieval",
            "query_type": "semantic_search",
            "context": {"file_types": ["rs"]}
        }
        await http_client.post(
            f"{test_config.base_url}/sessions/{session_id}/share/query",
            json=query_data,
            headers=auth_helper.headers,
            timeout=30.0
        )
        
        try:
            response = await http_client.get(
                f"{test_config.base_url}/sessions/{session_id}/shared/queries",
                headers=auth_helper.headers,
                timeout=30.0
            )
            
            success = response.status_code == 200
            details = f"Status: {response.status_code}"
            
            if success:
                response_data = response.json()
                has_session_id = "session_id" in response_data
                has_shared_queries = "shared_queries" in response_data
                has_count = "count" in response_data
                queries = response_data.get("shared_queries", [])
                is_list = isinstance(queries, list)
                details += f", Has session_id: {has_session_id}, Has shared_queries: {has_shared_queries}, Has count: {has_count}, Is list: {is_list}, Count: {len(queries)}"
                success = has_session_id and has_shared_queries and has_count and is_list
            else:
                details += f", Response: {response.text[:200]}"
            
            integration_results.add_result(
                "get_shared_queries", "integration_apis", success, details
            )
            
            assert success, f"Get shared queries failed: {details}"
            
        except Exception as e:
            integration_results.add_error("get_shared_queries", str(e))
            pytest.fail(f"Get shared queries exception: {e}")
    
    @pytest.mark.asyncio
    async def test_invalid_analysis_sharing(self, auth_helper, test_session,
                                          http_client: httpx.AsyncClient,
                                          test_config, integration_results):
        """Test sharing with invalid analysis ID"""
        auth_data = await auth_helper.login()
        if "error" in auth_data:
            pytest.skip(f"Cannot test without authentication: {auth_data['error']}")
        
        session_id = test_session["id"]
        invalid_analysis_data = {
            "analysis_id": "definitely-does-not-exist-12345",
            "sharing_permissions": ["view"]
        }
        
        try:
            response = await http_client.post(
                f"{test_config.base_url}/sessions/{session_id}/share/analysis",
                json=invalid_analysis_data,
                headers=auth_helper.headers,
                timeout=30.0
            )
            
            # Should return 404 (not found) or 503 (service unavailable)
            valid_statuses = [404, 503]
            success = response.status_code in valid_statuses
            details = f"Status: {response.status_code}, Valid statuses: {valid_statuses}"
            
            integration_results.add_result(
                "invalid_analysis_sharing", "integration_apis", success, details
            )
            
            assert success, f"Invalid analysis should be rejected: {details}"
            
        except Exception as e:
            integration_results.add_error("invalid_analysis_sharing", str(e))
            pytest.fail(f"Invalid analysis test exception: {e}")
    
    @pytest.mark.asyncio
    async def test_unauthorized_session_sharing(self, auth_helper, http_client: httpx.AsyncClient,
                                              test_config, integration_results):
        """Test sharing in session user doesn't have access to"""
        auth_data = await auth_helper.login()
        if "error" in auth_data:
            pytest.skip(f"Cannot test without authentication: {auth_data['error']}")
        
        fake_session_id = str(uuid.uuid4())
        analysis_data = TestDataGenerator.analysis_share_data()
        
        try:
            response = await http_client.post(
                f"{test_config.base_url}/sessions/{fake_session_id}/share/analysis",
                json=analysis_data,
                headers=auth_helper.headers,
                timeout=30.0
            )
            
            # Should return 404 (session not found) or 403 (forbidden)
            valid_statuses = [403, 404]
            success = response.status_code in valid_statuses
            details = f"Status: {response.status_code}, Valid statuses: {valid_statuses}"
            
            integration_results.add_result(
                "unauthorized_session_sharing", "integration_apis", success, details
            )
            
            assert success, f"Unauthorized sharing should be rejected: {details}"
            
        except Exception as e:
            integration_results.add_error("unauthorized_session_sharing", str(e))
            pytest.fail(f"Unauthorized sharing test exception: {e}")
    
    @pytest.mark.asyncio
    async def test_integration_error_handling(self, auth_helper, test_session,
                                            http_client: httpx.AsyncClient,
                                            test_config, integration_results):
        """Test error handling in integration scenarios"""
        auth_data = await auth_helper.login()
        if "error" in auth_data:
            pytest.skip(f"Cannot test without authentication: {auth_data['error']}")
        
        session_id = test_session["id"]
        
        # Test with malformed integration data
        malformed_data = {
            "malformed": "data",
            "missing_required_fields": True
        }
        
        try:
            response = await http_client.post(
                f"{test_config.base_url}/sessions/{session_id}/share/analysis",
                json=malformed_data,
                headers=auth_helper.headers,
                timeout=30.0
            )
            
            # Should return 400 Bad Request
            success = response.status_code == 400
            details = f"Status: {response.status_code}, Expected: 400"
            
            integration_results.add_result(
                "integration_error_handling", "integration_apis", success, details
            )
            
            assert success, f"Malformed integration data should be rejected: {details}"
            
        except Exception as e:
            integration_results.add_error("integration_error_handling", str(e))
            pytest.fail(f"Integration error handling exception: {e}")
    
    @pytest.mark.asyncio
    async def test_service_resilience(self, http_client: httpx.AsyncClient,
                                    test_config, integration_results):
        """Test service behavior when dependencies are unavailable"""
        # This test checks how the collaboration engine handles analysis-engine being down
        
        # First check if analysis-engine is actually available
        analysis_health = await check_service_health(
            http_client, test_config.analysis_engine_url
        )
        
        # Test integration health under these conditions
        try:
            response = await http_client.get(
                f"{test_config.base_url}/integration/health",
                timeout=30.0
            )
            
            # Service should respond even if dependencies are down
            success = response.status_code in [200, 503]
            details = f"Status: {response.status_code}, Analysis engine healthy: {analysis_health['healthy']}"
            
            if response.status_code == 200:
                response_data = response.json()
                status = response_data.get("status", "unknown")
                details += f", Service status: {status}"
                
                # If analysis-engine is down, status should be "degraded"
                if not analysis_health["healthy"]:
                    success = status == "degraded"
                    details += f", Correctly reports degraded: {success}"
            
            integration_results.add_result(
                "service_resilience", "integration_apis", success, details
            )
            
            assert success, f"Service resilience test failed: {details}"
            
        except Exception as e:
            integration_results.add_error("service_resilience", str(e))
            pytest.fail(f"Service resilience test exception: {e}")
    
    @pytest.mark.asyncio
    async def test_cross_service_complete(self, integration_results):
        """Verify cross-service integration testing is complete"""
        integration_test_results = [r for r in integration_results.results if r["category"] == "integration_apis"]
        
        expected_tests = [
            "integration_health_check",
            "analysis_engine_connectivity",
            "share_analysis_in_session",
            "share_query_in_session",
            "get_shared_analyses",
            "get_shared_queries",
            "invalid_analysis_sharing",
            "unauthorized_session_sharing",
            "integration_error_handling",
            "service_resilience"
        ]
        
        completed_tests = [r["test_name"] for r in integration_test_results]
        missing_tests = [t for t in expected_tests if t not in completed_tests]
        passed_tests = [r for r in integration_test_results if r["passed"]]
        
        success = len(missing_tests) == 0
        details = f"Completed: {len(completed_tests)}, Passed: {len(passed_tests)}, Missing: {missing_tests}"
        
        integration_results.add_result(
            "cross_service_complete", "integration_apis", success, details
        )
        
        logger.info(f"Cross-service integration test summary: {details}")
        
        if missing_tests:
            pytest.fail(f"Missing cross-service integration tests: {missing_tests}")