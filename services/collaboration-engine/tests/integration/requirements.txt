# Integration Testing Requirements for Collaboration Engine
# Part of EPIS-017: Integration testing framework

# Core testing framework
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-xdist>=3.3.1  # For parallel test execution
pytest-html>=3.2.0   # For HTML test reports

# HTTP client for API testing
httpx>=0.24.0

# Data handling and utilities
pydantic>=2.0.0
python-dateutil>=2.8.0

# Logging and monitoring
structlog>=23.1.0

# Development and debugging
pytest-cov>=4.1.0    # Coverage reporting
pytest-mock>=3.11.0  # Mocking utilities