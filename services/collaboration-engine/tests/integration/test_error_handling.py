"""
Error Handling and Resilience Integration Tests for Collaboration Engine
Part of EPIS-017: Integration testing framework

Tests error scenarios, edge cases, and service resilience.
"""

import pytest
import httpx
import json
import uuid
from typing import Dict, Any, Optional
import logging
import asyncio

from .conftest import ErrorSimulator

logger = logging.getLogger(__name__)

class TestErrorHandlingAndResilience:
    """Test error handling and service resilience"""
    
    @pytest.mark.asyncio
    async def test_rate_limiting_behavior(self, auth_helper, http_client: httpx.AsyncClient,
                                        test_config, integration_results):
        """Test API rate limiting behavior"""
        auth_data = await auth_helper.login()
        if "error" in auth_data:
            pytest.skip(f"Cannot test without authentication: {auth_data['error']}")
        
        # Make rapid requests to trigger rate limiting
        tasks = []
        for i in range(20):  # Attempt to make 20 rapid requests
            task = http_client.get(
                f"{test_config.base_url}/teams",
                headers=auth_helper.headers,
                timeout=5.0
            )
            tasks.append(task)
        
        try:
            responses = await asyncio.gather(*tasks, return_exceptions=True)
            
            status_codes = []
            for response in responses:
                if isinstance(response, httpx.Response):
                    status_codes.append(response.status_code)
                elif isinstance(response, Exception):
                    # Timeouts or connection errors might occur under rate limiting
                    status_codes.append(429)  # Treat as rate limited
            
            # Check if any requests were rate limited (429) or timed out
            rate_limited_count = status_codes.count(429)
            success_count = status_codes.count(200)
            
            # Rate limiting should kick in for some requests
            success = rate_limited_count > 0 or success_count < len(tasks)
            details = f"Total requests: {len(tasks)}, Success: {success_count}, Rate limited: {rate_limited_count}, Status codes: {status_codes[:10]}..."
            
            integration_results.add_result(
                "rate_limiting_behavior", "error_scenarios", success, details
            )
            
            # Don't fail if rate limiting isn't triggered - it depends on configuration
            if not success:
                logger.info("Rate limiting not triggered - may be configured differently")
            
        except Exception as e:
            integration_results.add_error("rate_limiting_behavior", str(e))
            pytest.fail(f"Rate limiting test exception: {e}")
    
    @pytest.mark.asyncio
    async def test_invalid_json_handling(self, auth_helper, http_client: httpx.AsyncClient,
                                       test_config, integration_results):
        """Test handling of invalid JSON payloads"""
        auth_data = await auth_helper.login()
        if "error" in auth_data:
            pytest.skip(f"Cannot test without authentication: {auth_data['error']}")
        
        invalid_json = "{ invalid json content that should fail to parse }"
        
        try:
            response = await http_client.post(
                f"{test_config.base_url}/teams",
                content=invalid_json,
                headers={
                    **auth_helper.headers,
                    "Content-Type": "application/json"
                },
                timeout=30.0
            )
            
            # Should return 400 Bad Request for invalid JSON
            success = response.status_code == 400
            details = f"Status: {response.status_code}, Expected: 400"
            
            integration_results.add_result(
                "invalid_json_handling", "error_scenarios", success, details
            )
            
            assert success, f"Invalid JSON should be rejected: {details}"
            
        except Exception as e:
            integration_results.add_error("invalid_json_handling", str(e))
            pytest.fail(f"Invalid JSON test exception: {e}")
    
    @pytest.mark.asyncio
    async def test_missing_content_type(self, auth_helper, http_client: httpx.AsyncClient,
                                      test_config, integration_results):
        """Test handling of requests without Content-Type header"""
        auth_data = await auth_helper.login()
        if "error" in auth_data:
            pytest.skip(f"Cannot test without authentication: {auth_data['error']}")
        
        valid_json_data = json.dumps({"name": "Test Team", "description": "Test"})
        
        try:
            response = await http_client.post(
                f"{test_config.base_url}/teams",
                content=valid_json_data,
                headers=auth_helper.headers,  # No Content-Type header
                timeout=30.0
            )
            
            # Should return 400 Bad Request or 415 Unsupported Media Type
            valid_statuses = [400, 415]
            success = response.status_code in valid_statuses
            details = f"Status: {response.status_code}, Valid statuses: {valid_statuses}"
            
            integration_results.add_result(
                "missing_content_type", "error_scenarios", success, details
            )
            
            assert success, f"Missing Content-Type should be rejected: {details}"
            
        except Exception as e:
            integration_results.add_error("missing_content_type", str(e))
            pytest.fail(f"Missing Content-Type test exception: {e}")
    
    @pytest.mark.asyncio
    async def test_oversized_payload(self, auth_helper, http_client: httpx.AsyncClient,
                                   test_config, integration_results):
        """Test handling of oversized request payloads"""
        auth_data = await auth_helper.login()
        if "error" in auth_data:
            pytest.skip(f"Cannot test without authentication: {auth_data['error']}")
        
        # Create a very large payload (1MB of data)
        large_description = "x" * (1024 * 1024)  # 1MB string
        oversized_data = {
            "name": "Test Team",
            "description": large_description
        }
        
        try:
            response = await http_client.post(
                f"{test_config.base_url}/teams",
                json=oversized_data,
                headers=auth_helper.headers,
                timeout=30.0
            )
            
            # Should return 413 Payload Too Large or 400 Bad Request
            valid_statuses = [400, 413]
            success = response.status_code in valid_statuses
            details = f"Status: {response.status_code}, Valid statuses: {valid_statuses}, Payload size: ~1MB"
            
            integration_results.add_result(
                "oversized_payload", "error_scenarios", success, details
            )
            
            # Don't fail if large payloads are accepted - depends on server configuration
            if not success:
                logger.info("Large payload accepted - server may have high limits")
            
        except Exception as e:
            integration_results.add_error("oversized_payload", str(e))
            # Don't fail on timeout - large payloads might cause timeouts
            logger.warning(f"Oversized payload test exception: {e}")
    
    @pytest.mark.asyncio
    async def test_concurrent_operations(self, auth_helper, test_team, http_client: httpx.AsyncClient,
                                       test_config, integration_results):
        """Test handling of concurrent operations on the same resources"""
        auth_data = await auth_helper.login()
        if "error" in auth_data:
            pytest.skip(f"Cannot test without authentication: {auth_data['error']}")
        
        team_id = test_team["id"]
        
        # Attempt concurrent updates to the same team
        update_tasks = []
        for i in range(5):
            update_data = {
                "name": f"Concurrent Update {i}",
                "description": f"Update attempt {i}"
            }
            task = http_client.put(
                f"{test_config.base_url}/teams/{team_id}",
                json=update_data,
                headers=auth_helper.headers,
                timeout=30.0
            )
            update_tasks.append(task)
        
        try:
            responses = await asyncio.gather(*update_tasks, return_exceptions=True)
            
            status_codes = []
            for response in responses:
                if isinstance(response, httpx.Response):
                    status_codes.append(response.status_code)
                elif isinstance(response, Exception):
                    status_codes.append(500)  # Treat exceptions as server errors
            
            success_count = status_codes.count(200)
            error_count = len(status_codes) - success_count
            
            # At least some operations should succeed
            success = success_count > 0
            details = f"Concurrent operations: {len(update_tasks)}, Success: {success_count}, Errors: {error_count}, Status codes: {status_codes}"
            
            integration_results.add_result(
                "concurrent_operations", "error_scenarios", success, details
            )
            
            assert success, f"Concurrent operations test failed: {details}"
            
        except Exception as e:
            integration_results.add_error("concurrent_operations", str(e))
            pytest.fail(f"Concurrent operations test exception: {e}")
    
    @pytest.mark.asyncio
    async def test_timeout_handling(self, auth_helper, http_client: httpx.AsyncClient,
                                  test_config, integration_results):
        """Test service behavior with very short timeouts"""
        auth_data = await auth_helper.login()
        if "error" in auth_data:
            pytest.skip(f"Cannot test without authentication: {auth_data['error']}")
        
        # Use a very short timeout to test timeout handling
        short_timeout = 0.001  # 1ms timeout - should cause timeouts
        
        try:
            with httpx.AsyncClient(timeout=httpx.Timeout(short_timeout)) as timeout_client:
                response = await timeout_client.get(
                    f"{test_config.base_url}/teams",
                    headers=auth_helper.headers
                )
                
                # If we get here, the service is extremely fast
                success = True
                details = f"Service responded within {short_timeout}s - very fast!"
                
        except (httpx.TimeoutException, asyncio.TimeoutError):
            # Expected - service took longer than 1ms
            success = True
            details = "Timeout occurred as expected with 1ms limit"
            
        except Exception as e:
            # Other exceptions are not expected
            success = False
            details = f"Unexpected exception during timeout test: {e}"
        
        integration_results.add_result(
            "timeout_handling", "error_scenarios", success, details
        )
        
        assert success, f"Timeout handling test failed: {details}"
    
    @pytest.mark.asyncio
    async def test_resource_not_found_scenarios(self, auth_helper, http_client: httpx.AsyncClient,
                                              test_config, integration_results):
        """Test various resource not found scenarios"""
        auth_data = await auth_helper.login()
        if "error" in auth_data:
            pytest.skip(f"Cannot test without authentication: {auth_data['error']}")
        
        # Test multiple non-existent resource scenarios
        test_scenarios = [
            ("teams", str(uuid.uuid4())),
            ("sessions", str(uuid.uuid4())),
            (f"sessions/{str(uuid.uuid4())}/messages", None),
            (f"teams/{str(uuid.uuid4())}/members", None)
        ]
        
        results = []
        for endpoint, resource_id in test_scenarios:
            try:
                url = f"{test_config.base_url}/{endpoint}"
                if resource_id:
                    url += f"/{resource_id}"
                
                response = await http_client.get(url, headers=auth_helper.headers, timeout=30.0)
                
                # Should return 404 Not Found
                success = response.status_code == 404
                results.append(success)
                
            except Exception as e:
                logger.error(f"Error testing {endpoint}: {e}")
                results.append(False)
        
        overall_success = all(results)
        details = f"Tested {len(test_scenarios)} not-found scenarios, Success: {results.count(True)}/{len(results)}"
        
        integration_results.add_result(
            "resource_not_found_scenarios", "error_scenarios", overall_success, details
        )
        
        assert overall_success, f"Resource not found tests failed: {details}"
    
    @pytest.mark.asyncio
    async def test_malformed_auth_tokens(self, http_client: httpx.AsyncClient,
                                       test_config, integration_results):
        """Test handling of various malformed authentication tokens"""
        malformed_tokens = [
            "Bearer",  # No token
            "Bearer ",  # Empty token
            "Bearer invalid-token",  # Invalid format
            "NotBearer valid-token",  # Wrong scheme
            "Bearer " + "x" * 1000,  # Extremely long token
            "Bearer token.with.too.many.parts.here.invalid",  # Too many JWT parts
        ]
        
        results = []
        for token in malformed_tokens:
            try:
                headers = {"Authorization": token}
                response = await http_client.get(
                    f"{test_config.base_url}/teams",
                    headers=headers,
                    timeout=30.0
                )
                
                # Should return 401 Unauthorized
                success = response.status_code == 401
                results.append(success)
                
            except Exception as e:
                logger.error(f"Error testing malformed token '{token[:20]}...': {e}")
                results.append(False)
        
        overall_success = all(results)
        details = f"Tested {len(malformed_tokens)} malformed tokens, Success: {results.count(True)}/{len(results)}"
        
        integration_results.add_result(
            "malformed_auth_tokens", "error_scenarios", overall_success, details
        )
        
        assert overall_success, f"Malformed token tests failed: {details}"
    
    @pytest.mark.asyncio
    async def test_http_method_not_allowed(self, auth_helper, http_client: httpx.AsyncClient,
                                         test_config, integration_results):
        """Test HTTP method not allowed scenarios"""
        auth_data = await auth_helper.login()
        if "error" in auth_data:
            pytest.skip(f"Cannot test without authentication: {auth_data['error']}")
        
        # Test wrong HTTP methods on various endpoints
        test_scenarios = [
            ("DELETE", f"{test_config.base_url}/health"),  # DELETE on health endpoint
            ("PUT", f"{test_config.base_url}/teams"),      # PUT on teams list endpoint
            ("POST", f"{test_config.base_url}/teams/123"), # POST on specific team endpoint
            ("PATCH", f"{test_config.base_url}/sessions/123"), # PATCH not supported
        ]
        
        results = []
        for method, url in test_scenarios:
            try:
                response = await http_client.request(
                    method, url, headers=auth_helper.headers, timeout=30.0
                )
                
                # Should return 405 Method Not Allowed
                success = response.status_code == 405
                results.append(success)
                
            except Exception as e:
                logger.error(f"Error testing {method} {url}: {e}")
                results.append(False)
        
        overall_success = all(results)
        details = f"Tested {len(test_scenarios)} method-not-allowed scenarios, Success: {results.count(True)}/{len(results)}"
        
        integration_results.add_result(
            "http_method_not_allowed", "error_scenarios", overall_success, details
        )
        
        assert overall_success, f"Method not allowed tests failed: {details}"
    
    @pytest.mark.asyncio
    async def test_service_graceful_degradation(self, http_client: httpx.AsyncClient,
                                              test_config, integration_results):
        """Test service graceful degradation when dependencies fail"""
        # Test that core functionality still works even if some integrations fail
        
        # Check service health
        try:
            health_response = await http_client.get(f"{test_config.base_url}/health", timeout=30.0)
            health_ok = health_response.status_code == 200
            
            # Check integration health (might be degraded)
            integration_response = await http_client.get(f"{test_config.base_url}/integration/health", timeout=30.0)
            integration_status = integration_response.status_code in [200, 503]
            
            # Service should handle degraded integrations gracefully
            success = health_ok and integration_status
            details = f"Health: {health_response.status_code}, Integration: {integration_response.status_code}"
            
            if integration_response.status_code == 503:
                details += " (degraded mode - acceptable)"
            
            integration_results.add_result(
                "service_graceful_degradation", "error_scenarios", success, details
            )
            
            assert success, f"Service degradation test failed: {details}"
            
        except Exception as e:
            integration_results.add_error("service_graceful_degradation", str(e))
            pytest.fail(f"Service degradation test exception: {e}")
    
    @pytest.mark.asyncio
    async def test_error_handling_complete(self, integration_results):
        """Verify error handling integration testing is complete"""
        error_results = [r for r in integration_results.results if r["category"] == "error_scenarios"]
        
        expected_tests = [
            "rate_limiting_behavior",
            "invalid_json_handling",
            "missing_content_type",
            "concurrent_operations",
            "timeout_handling",
            "resource_not_found_scenarios",
            "malformed_auth_tokens",
            "http_method_not_allowed",
            "service_graceful_degradation"
        ]
        
        completed_tests = [r["test_name"] for r in error_results]
        missing_tests = [t for t in expected_tests if t not in completed_tests]
        passed_tests = [r for r in error_results if r["passed"]]
        
        success = len(missing_tests) == 0
        details = f"Completed: {len(completed_tests)}, Passed: {len(passed_tests)}, Missing: {missing_tests}"
        
        integration_results.add_result(
            "error_handling_complete", "error_scenarios", success, details
        )
        
        logger.info(f"Error handling integration test summary: {details}")
        
        if missing_tests:
            pytest.fail(f"Missing error handling tests: {missing_tests}")