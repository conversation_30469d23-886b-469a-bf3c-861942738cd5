"""
Team Management Integration Tests for Collaboration Engine
Part of EPIS-017: Integration testing framework

Tests all team management endpoints and workflows.
"""

import pytest
import httpx
import json
import uuid
from typing import Dict, Any, Optional
import logging

from .conftest import TestDataGenerator, ErrorSimulator

logger = logging.getLogger(__name__)

class TestTeamManagement:
    """Test team management integration"""
    
    @pytest.mark.asyncio
    async def test_create_team_success(self, auth_helper, http_client: httpx.AsyncClient, 
                                     test_config, integration_results):
        """Test successful team creation"""
        # Ensure authentication
        auth_data = await auth_helper.login()
        if "error" in auth_data:
            pytest.skip(f"Cannot test without authentication: {auth_data['error']}")
        
        team_data = TestDataGenerator.team_data("create_success")
        
        try:
            response = await http_client.post(
                f"{test_config.base_url}/teams",
                json=team_data,
                headers=auth_helper.headers,
                timeout=30.0
            )
            
            success = response.status_code == 201
            details = f"Status: {response.status_code}"
            
            if success:
                response_data = response.json()
                has_team = "team" in response_data
                if has_team:
                    team = response_data["team"]
                    has_id = "id" in team
                    has_name = team.get("name") == team_data["name"]
                    details += f", Has team: {has_team}, Has ID: {has_id}, Name matches: {has_name}"
                    success = has_team and has_id and has_name
            else:
                details += f", Response: {response.text[:200]}"
            
            integration_results.add_result(
                "create_team_success", "team_apis", success, details
            )
            
            assert success, f"Team creation failed: {details}"
            
            if success:
                return response_data["team"]
            
        except Exception as e:
            integration_results.add_error("create_team_success", str(e))
            pytest.fail(f"Team creation exception: {e}")
    
    @pytest.mark.asyncio
    async def test_create_team_invalid_data(self, auth_helper, http_client: httpx.AsyncClient,
                                          test_config, integration_results):
        """Test team creation with invalid data"""
        auth_data = await auth_helper.login()
        if "error" in auth_data:
            pytest.skip(f"Cannot test without authentication: {auth_data['error']}")
        
        invalid_data = ErrorSimulator.malformed_request_data("team")
        
        try:
            response = await http_client.post(
                f"{test_config.base_url}/teams",
                json=invalid_data,
                headers=auth_helper.headers,
                timeout=30.0
            )
            
            # Should return 400 Bad Request
            success = response.status_code == 400
            details = f"Status: {response.status_code}, Expected: 400"
            
            integration_results.add_result(
                "create_team_invalid_data", "team_apis", success, details
            )
            
            assert success, f"Invalid data should be rejected: {details}"
            
        except Exception as e:
            integration_results.add_error("create_team_invalid_data", str(e))
            pytest.fail(f"Invalid team data test exception: {e}")
    
    @pytest.mark.asyncio
    async def test_list_teams(self, auth_helper, http_client: httpx.AsyncClient,
                            test_config, integration_results):
        """Test listing user teams"""
        auth_data = await auth_helper.login()
        if "error" in auth_data:
            pytest.skip(f"Cannot test without authentication: {auth_data['error']}")
        
        try:
            response = await http_client.get(
                f"{test_config.base_url}/teams",
                headers=auth_helper.headers,
                timeout=30.0
            )
            
            success = response.status_code == 200
            details = f"Status: {response.status_code}"
            
            if success:
                response_data = response.json()
                has_teams = "teams" in response_data
                teams = response_data.get("teams", [])
                is_list = isinstance(teams, list)
                details += f", Has teams: {has_teams}, Is list: {is_list}, Count: {len(teams)}"
                success = has_teams and is_list
            else:
                details += f", Response: {response.text[:200]}"
            
            integration_results.add_result(
                "list_teams", "team_apis", success, details
            )
            
            assert success, f"List teams failed: {details}"
            
        except Exception as e:
            integration_results.add_error("list_teams", str(e))
            pytest.fail(f"List teams exception: {e}")
    
    @pytest.mark.asyncio
    async def test_get_team_details(self, auth_helper, test_team, http_client: httpx.AsyncClient,
                                  test_config, integration_results):
        """Test getting team details"""
        auth_data = await auth_helper.login()
        if "error" in auth_data:
            pytest.skip(f"Cannot test without authentication: {auth_data['error']}")
        
        team_id = test_team["id"]
        
        try:
            response = await http_client.get(
                f"{test_config.base_url}/teams/{team_id}",
                headers=auth_helper.headers,
                timeout=30.0
            )
            
            success = response.status_code == 200
            details = f"Status: {response.status_code}"
            
            if success:
                response_data = response.json()
                has_team = "team" in response_data
                has_members = "members" in response_data
                if has_team:
                    team = response_data["team"]
                    correct_id = team.get("id") == team_id
                    details += f", Has team: {has_team}, Has members: {has_members}, Correct ID: {correct_id}"
                    success = has_team and has_members and correct_id
            else:
                details += f", Response: {response.text[:200]}"
            
            integration_results.add_result(
                "get_team_details", "team_apis", success, details
            )
            
            assert success, f"Get team details failed: {details}"
            
        except Exception as e:
            integration_results.add_error("get_team_details", str(e))
            pytest.fail(f"Get team details exception: {e}")
    
    @pytest.mark.asyncio
    async def test_get_nonexistent_team(self, auth_helper, http_client: httpx.AsyncClient,
                                      test_config, integration_results):
        """Test getting nonexistent team"""
        auth_data = await auth_helper.login()
        if "error" in auth_data:
            pytest.skip(f"Cannot test without authentication: {auth_data['error']}")
        
        fake_team_id = str(uuid.uuid4())
        
        try:
            response = await http_client.get(
                f"{test_config.base_url}/teams/{fake_team_id}",
                headers=auth_helper.headers,
                timeout=30.0
            )
            
            # Should return 404 Not Found
            success = response.status_code == 404
            details = f"Status: {response.status_code}, Expected: 404"
            
            integration_results.add_result(
                "get_nonexistent_team", "team_apis", success, details
            )
            
            assert success, f"Nonexistent team should return 404: {details}"
            
        except Exception as e:
            integration_results.add_error("get_nonexistent_team", str(e))
            pytest.fail(f"Nonexistent team test exception: {e}")
    
    @pytest.mark.asyncio
    async def test_update_team(self, auth_helper, test_team, http_client: httpx.AsyncClient,
                             test_config, integration_results):
        """Test updating team information"""
        auth_data = await auth_helper.login()
        if "error" in auth_data:
            pytest.skip(f"Cannot test without authentication: {auth_data['error']}")
        
        team_id = test_team["id"]
        update_data = {
            "name": f"Updated Team {uuid.uuid4()}",
            "description": "Updated description for integration test"
        }
        
        try:
            response = await http_client.put(
                f"{test_config.base_url}/teams/{team_id}",
                json=update_data,
                headers=auth_helper.headers,
                timeout=30.0
            )
            
            success = response.status_code == 200
            details = f"Status: {response.status_code}"
            
            if success:
                response_data = response.json()
                has_message = "message" in response_data or "team" in response_data
                details += f", Has success indicator: {has_message}"
            else:
                details += f", Response: {response.text[:200]}"
            
            integration_results.add_result(
                "update_team", "team_apis", success, details
            )
            
            assert success, f"Team update failed: {details}"
            
        except Exception as e:
            integration_results.add_error("update_team", str(e))
            pytest.fail(f"Team update exception: {e}")
    
    @pytest.mark.asyncio
    async def test_add_team_member(self, auth_helper, test_team, http_client: httpx.AsyncClient,
                                 test_config, integration_results):
        """Test adding a member to team"""
        auth_data = await auth_helper.login()
        if "error" in auth_data:
            pytest.skip(f"Cannot test without authentication: {auth_data['error']}")
        
        team_id = test_team["id"]
        member_data = {
            "email": f"new-member-{uuid.uuid4()}@example.com",
            "role": "member"
        }
        
        try:
            response = await http_client.post(
                f"{test_config.base_url}/teams/{team_id}/members",
                json=member_data,
                headers=auth_helper.headers,
                timeout=30.0
            )
            
            success = response.status_code == 201
            details = f"Status: {response.status_code}"
            
            if success:
                response_data = response.json()
                has_member = "member" in response_data
                if has_member:
                    member = response_data["member"]
                    correct_email = member.get("user_info", {}).get("email") == member_data["email"]
                    details += f", Has member: {has_member}, Correct email: {correct_email}"
                    success = has_member and correct_email
            else:
                details += f", Response: {response.text[:200]}"
            
            integration_results.add_result(
                "add_team_member", "team_apis", success, details
            )
            
            assert success, f"Add team member failed: {details}"
            
        except Exception as e:
            integration_results.add_error("add_team_member", str(e))
            pytest.fail(f"Add team member exception: {e}")
    
    @pytest.mark.asyncio
    async def test_update_member_role(self, auth_helper, test_team, http_client: httpx.AsyncClient,
                                    test_config, integration_results):
        """Test updating team member role"""
        auth_data = await auth_helper.login()
        if "error" in auth_data:
            pytest.skip(f"Cannot test without authentication: {auth_data['error']}")
        
        team_id = test_team["id"]
        user_id = auth_helper.user_id or "test-user-id"
        role_data = {
            "user_id": user_id,
            "role": "admin"
        }
        
        try:
            response = await http_client.put(
                f"{test_config.base_url}/teams/{team_id}/members/{user_id}/role",
                json=role_data,
                headers=auth_helper.headers,
                timeout=30.0
            )
            
            # Note: This might fail if user is team owner and can't change their own role
            valid_statuses = [200, 403]  # 403 = Forbidden (can't change owner role)
            success = response.status_code in valid_statuses
            details = f"Status: {response.status_code}, Valid statuses: {valid_statuses}"
            
            integration_results.add_result(
                "update_member_role", "team_apis", success, details
            )
            
            if response.status_code == 403:
                logger.info("Role update forbidden (likely team owner - expected)")
            
            assert success, f"Update member role test failed: {details}"
            
        except Exception as e:
            integration_results.add_error("update_member_role", str(e))
            pytest.fail(f"Update member role exception: {e}")
    
    @pytest.mark.asyncio
    async def test_remove_team_member(self, auth_helper, test_team, http_client: httpx.AsyncClient,
                                    test_config, integration_results):
        """Test removing team member"""
        auth_data = await auth_helper.login()
        if "error" in auth_data:
            pytest.skip(f"Cannot test without authentication: {auth_data['error']}")
        
        # First add a member to remove
        team_id = test_team["id"]
        member_email = f"remove-test-{uuid.uuid4()}@example.com"
        member_data = {
            "email": member_email,
            "role": "member"
        }
        
        # Add member
        add_response = await http_client.post(
            f"{test_config.base_url}/teams/{team_id}/members",
            json=member_data,
            headers=auth_helper.headers,
            timeout=30.0
        )
        
        if add_response.status_code != 201:
            pytest.skip(f"Cannot test removal without adding member: {add_response.status_code}")
        
        # Get the member user_id from response
        member_data = add_response.json().get("member", {})
        member_user_id = member_data.get("user_info", {}).get("user_id")
        
        if not member_user_id:
            pytest.skip("Cannot test removal without member user_id")
        
        try:
            response = await http_client.delete(
                f"{test_config.base_url}/teams/{team_id}/members/{member_user_id}",
                headers=auth_helper.headers,
                timeout=30.0
            )
            
            success = response.status_code == 204
            details = f"Status: {response.status_code}, Expected: 204"
            
            integration_results.add_result(
                "remove_team_member", "team_apis", success, details
            )
            
            assert success, f"Remove team member failed: {details}"
            
        except Exception as e:
            integration_results.add_error("remove_team_member", str(e))
            pytest.fail(f"Remove team member exception: {e}")
    
    @pytest.mark.asyncio
    async def test_delete_team(self, auth_helper, http_client: httpx.AsyncClient,
                             test_config, integration_results):
        """Test deleting a team"""
        auth_data = await auth_helper.login()
        if "error" in auth_data:
            pytest.skip(f"Cannot test without authentication: {auth_data['error']}")
        
        # Create a team specifically for deletion
        team_data = TestDataGenerator.team_data("delete_test")
        create_response = await http_client.post(
            f"{test_config.base_url}/teams",
            json=team_data,
            headers=auth_helper.headers,
            timeout=30.0
        )
        
        if create_response.status_code != 201:
            pytest.skip(f"Cannot test deletion without creating team: {create_response.status_code}")
        
        team_id = create_response.json()["team"]["id"]
        
        try:
            response = await http_client.delete(
                f"{test_config.base_url}/teams/{team_id}",
                headers=auth_helper.headers,
                timeout=30.0
            )
            
            success = response.status_code == 204
            details = f"Status: {response.status_code}, Expected: 204"
            
            integration_results.add_result(
                "delete_team", "team_apis", success, details
            )
            
            assert success, f"Delete team failed: {details}"
            
        except Exception as e:
            integration_results.add_error("delete_team", str(e))
            pytest.fail(f"Delete team exception: {e}")
    
    @pytest.mark.asyncio
    async def test_team_authorization_checks(self, auth_helper, http_client: httpx.AsyncClient,
                                           test_config, integration_results):
        """Test team-level authorization"""
        auth_data = await auth_helper.login()
        if "error" in auth_data:
            pytest.skip(f"Cannot test without authentication: {auth_data['error']}")
        
        # Try to access a team that doesn't exist or user doesn't have access to
        fake_team_id = str(uuid.uuid4())
        
        try:
            response = await http_client.get(
                f"{test_config.base_url}/teams/{fake_team_id}",
                headers=auth_helper.headers,
                timeout=30.0
            )
            
            # Should return 404 Not Found or 403 Forbidden
            valid_statuses = [403, 404]
            success = response.status_code in valid_statuses
            details = f"Status: {response.status_code}, Valid statuses: {valid_statuses}"
            
            integration_results.add_result(
                "team_authorization_checks", "team_apis", success, details
            )
            
            assert success, f"Authorization check failed: {details}"
            
        except Exception as e:
            integration_results.add_error("team_authorization_checks", str(e))
            pytest.fail(f"Authorization check exception: {e}")
    
    @pytest.mark.asyncio
    async def test_team_management_complete(self, integration_results):
        """Verify team management integration testing is complete"""
        team_results = [r for r in integration_results.results if r["category"] == "team_apis"]
        
        expected_tests = [
            "create_team_success",
            "create_team_invalid_data",
            "list_teams",
            "get_team_details",
            "get_nonexistent_team",
            "update_team",
            "add_team_member",
            "team_authorization_checks"
        ]
        
        completed_tests = [r["test_name"] for r in team_results]
        missing_tests = [t for t in expected_tests if t not in completed_tests]
        passed_tests = [r for r in team_results if r["passed"]]
        
        success = len(missing_tests) == 0
        details = f"Completed: {len(completed_tests)}, Passed: {len(passed_tests)}, Missing: {missing_tests}"
        
        integration_results.add_result(
            "team_management_complete", "team_apis", success, details
        )
        
        logger.info(f"Team management integration test summary: {details}")
        
        if missing_tests:
            pytest.fail(f"Missing team management tests: {missing_tests}")