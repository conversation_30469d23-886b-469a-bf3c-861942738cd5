"""
Authentication Integration Tests for Collaboration Engine
Part of EPIS-017: Integration testing framework

Tests all authentication endpoints and flows.
"""

import pytest
import httpx
import json
from typing import Dict, Any
import logging

logger = logging.getLogger(__name__)

class TestAuthentication:
    """Test authentication integration"""
    
    @pytest.mark.asyncio
    async def test_health_check_no_auth(self, http_client: httpx.AsyncClient, test_config, integration_results):
        """Test health check endpoint (no auth required)"""
        try:
            response = await http_client.get(f"{test_config.base_url}/health")
            
            success = response.status_code == 200
            details = f"Status: {response.status_code}, Response: {response.text[:200]}"
            
            integration_results.add_result(
                "health_check_no_auth", "auth_apis", success, details
            )
            
            assert success, f"Health check failed: {details}"
            
        except Exception as e:
            integration_results.add_error("health_check_no_auth", str(e))
            pytest.fail(f"Health check exception: {e}")
    
    @pytest.mark.asyncio
    async def test_readiness_check_no_auth(self, http_client: httpx.AsyncClient, test_config, integration_results):
        """Test readiness check endpoint (no auth required)"""
        try:
            response = await http_client.get(f"{test_config.base_url}/health/ready")
            
            success = response.status_code == 200
            details = f"Status: {response.status_code}, Response: {response.text[:200]}"
            
            integration_results.add_result(
                "readiness_check_no_auth", "auth_apis", success, details
            )
            
            assert success, f"Readiness check failed: {details}"
            
        except Exception as e:
            integration_results.add_error("readiness_check_no_auth", str(e))
            pytest.fail(f"Readiness check exception: {e}")
    
    @pytest.mark.asyncio
    async def test_liveness_check_no_auth(self, http_client: httpx.AsyncClient, test_config, integration_results):
        """Test liveness check endpoint (no auth required)"""
        try:
            response = await http_client.get(f"{test_config.base_url}/health/live")
            
            success = response.status_code == 200
            details = f"Status: {response.status_code}, Response: {response.text[:200]}"
            
            integration_results.add_result(
                "liveness_check_no_auth", "auth_apis", success, details
            )
            
            assert success, f"Liveness check failed: {details}"
            
        except Exception as e:
            integration_results.add_error("liveness_check_no_auth", str(e))
            pytest.fail(f"Liveness check exception: {e}")
    
    @pytest.mark.asyncio
    async def test_login_flow(self, http_client: httpx.AsyncClient, test_config, integration_results):
        """Test user login flow"""
        login_data = {
            "email": "<EMAIL>",
            "name": "Integration Test User",
            "provider": "test"
        }
        
        try:
            response = await http_client.post(
                f"{test_config.base_url}/auth/login",
                json=login_data,
                timeout=30.0
            )
            
            success = response.status_code == 200
            details = f"Status: {response.status_code}"
            
            if success:
                auth_data = response.json()
                has_token = "access_token" in auth_data
                has_user = "user" in auth_data
                details += f", Has token: {has_token}, Has user: {has_user}"
                success = has_token and has_user
            else:
                details += f", Response: {response.text[:200]}"
            
            integration_results.add_result(
                "login_flow", "auth_apis", success, details
            )
            
            assert success, f"Login failed: {details}"
            
            if success:
                return auth_data
            
        except Exception as e:
            integration_results.add_error("login_flow", str(e))
            pytest.fail(f"Login exception: {e}")
    
    @pytest.mark.asyncio
    async def test_service_token_generation(self, http_client: httpx.AsyncClient, test_config, integration_results):
        """Test service token generation"""
        service_data = {
            "service_name": "integration-test-service",
            "permissions": ["read", "write"]
        }
        
        try:
            response = await http_client.post(
                f"{test_config.base_url}/auth/service-token",
                json=service_data,
                timeout=30.0
            )
            
            # Service token might require special authentication or be disabled
            # So we'll consider various status codes as valid scenarios
            valid_statuses = [200, 201, 401, 403, 501]  # 501 = Not Implemented
            success = response.status_code in valid_statuses
            
            details = f"Status: {response.status_code}, Response: {response.text[:200]}"
            
            integration_results.add_result(
                "service_token_generation", "auth_apis", success, details
            )
            
            if response.status_code in [401, 403]:
                logger.info("Service token requires authentication (expected)")
            elif response.status_code == 501:
                logger.info("Service token not implemented (acceptable)")
            
            assert success, f"Service token test failed with unexpected status: {details}"
            
        except Exception as e:
            integration_results.add_error("service_token_generation", str(e))
            # Don't fail the test for this endpoint as it might not be implemented
            logger.warning(f"Service token exception: {e}")
    
    @pytest.mark.asyncio
    async def test_protected_endpoint_without_auth(self, http_client: httpx.AsyncClient, test_config, integration_results):
        """Test that protected endpoints require authentication"""
        try:
            # Try to access teams endpoint without auth
            response = await http_client.get(f"{test_config.base_url}/teams")
            
            # Should return 401 Unauthorized
            success = response.status_code == 401
            details = f"Status: {response.status_code}, Expected: 401"
            
            integration_results.add_result(
                "protected_endpoint_without_auth", "auth_apis", success, details
            )
            
            assert success, f"Protected endpoint should require auth: {details}"
            
        except Exception as e:
            integration_results.add_error("protected_endpoint_without_auth", str(e))
            pytest.fail(f"Protected endpoint test exception: {e}")
    
    @pytest.mark.asyncio
    async def test_protected_endpoint_with_invalid_token(self, http_client: httpx.AsyncClient, test_config, integration_results):
        """Test protected endpoint with invalid token"""
        invalid_headers = {"Authorization": "Bearer invalid-token-12345"}
        
        try:
            response = await http_client.get(
                f"{test_config.base_url}/teams",
                headers=invalid_headers
            )
            
            # Should return 401 Unauthorized
            success = response.status_code == 401
            details = f"Status: {response.status_code}, Expected: 401"
            
            integration_results.add_result(
                "protected_endpoint_invalid_token", "auth_apis", success, details
            )
            
            assert success, f"Invalid token should be rejected: {details}"
            
        except Exception as e:
            integration_results.add_error("protected_endpoint_invalid_token", str(e))
            pytest.fail(f"Invalid token test exception: {e}")
    
    @pytest.mark.asyncio
    async def test_token_refresh_flow(self, auth_helper, http_client: httpx.AsyncClient, test_config, integration_results):
        """Test token refresh functionality"""
        # First ensure we have a valid login
        auth_data = await auth_helper.login()
        
        if "error" in auth_data:
            pytest.skip(f"Cannot test refresh without login: {auth_data['error']}")
        
        refresh_token = auth_data.get("refresh_token")
        
        if not refresh_token:
            logger.info("No refresh token provided, skipping refresh test")
            integration_results.add_result(
                "token_refresh_flow", "auth_apis", True, "No refresh token provided (acceptable)"
            )
            return
        
        try:
            refresh_data = {"refresh_token": refresh_token}
            response = await http_client.post(
                f"{test_config.base_url}/auth/refresh",
                json=refresh_data,
                timeout=30.0
            )
            
            success = response.status_code == 200
            details = f"Status: {response.status_code}"
            
            if success:
                new_auth_data = response.json()
                has_new_token = "access_token" in new_auth_data
                details += f", Has new token: {has_new_token}"
                success = has_new_token
            else:
                details += f", Response: {response.text[:200]}"
            
            integration_results.add_result(
                "token_refresh_flow", "auth_apis", success, details
            )
            
            assert success, f"Token refresh failed: {details}"
            
        except Exception as e:
            integration_results.add_error("token_refresh_flow", str(e))
            pytest.fail(f"Token refresh exception: {e}")
    
    @pytest.mark.asyncio
    async def test_logout_flow(self, auth_helper, http_client: httpx.AsyncClient, test_config, integration_results):
        """Test user logout flow"""
        # Ensure we have a valid login
        auth_data = await auth_helper.login()
        
        if "error" in auth_data:
            pytest.skip(f"Cannot test logout without login: {auth_data['error']}")
        
        try:
            response = await http_client.post(
                f"{test_config.base_url}/auth/logout",
                headers=auth_helper.headers,
                timeout=30.0
            )
            
            # Logout should succeed or return 401 if already logged out
            valid_statuses = [200, 204, 401]
            success = response.status_code in valid_statuses
            details = f"Status: {response.status_code}, Valid statuses: {valid_statuses}"
            
            integration_results.add_result(
                "logout_flow", "auth_apis", success, details
            )
            
            assert success, f"Logout failed: {details}"
            
        except Exception as e:
            integration_results.add_error("logout_flow", str(e))
            pytest.fail(f"Logout exception: {e}")
    
    @pytest.mark.asyncio
    async def test_auth_integration_complete(self, integration_results):
        """Verify authentication integration testing is complete"""
        auth_results = [r for r in integration_results.results if r["category"] == "auth_apis"]
        
        expected_tests = [
            "health_check_no_auth",
            "readiness_check_no_auth", 
            "liveness_check_no_auth",
            "login_flow",
            "protected_endpoint_without_auth",
            "protected_endpoint_invalid_token"
        ]
        
        completed_tests = [r["test_name"] for r in auth_results]
        missing_tests = [t for t in expected_tests if t not in completed_tests]
        
        success = len(missing_tests) == 0
        details = f"Completed: {len(completed_tests)}, Missing: {missing_tests}"
        
        integration_results.add_result(
            "auth_integration_complete", "auth_apis", success, details
        )
        
        logger.info(f"Authentication integration test summary: {details}")
        
        if missing_tests:
            pytest.fail(f"Missing authentication tests: {missing_tests}")