"""
Session Management Integration Tests for Collaboration Engine
Part of EPIS-017: Integration testing framework

Tests all session management endpoints and workflows.
"""

import pytest
import httpx
import json
import uuid
from typing import Dict, Any, Optional
import logging

from .conftest import TestDataGenerator, ErrorSimulator

logger = logging.getLogger(__name__)

class TestSessionManagement:
    """Test session management integration"""
    
    @pytest.mark.asyncio
    async def test_create_session_success(self, auth_helper, test_team, http_client: httpx.AsyncClient,
                                        test_config, integration_results):
        """Test successful session creation"""
        auth_data = await auth_helper.login()
        if "error" in auth_data:
            pytest.skip(f"Cannot test without authentication: {auth_data['error']}")
        
        session_data = TestDataGenerator.session_data(test_team["id"], "create_success")
        
        try:
            response = await http_client.post(
                f"{test_config.base_url}/sessions",
                json=session_data,
                headers=auth_helper.headers,
                timeout=30.0
            )
            
            success = response.status_code == 201
            details = f"Status: {response.status_code}"
            
            if success:
                response_data = response.json()
                has_session = "session" in response_data
                if has_session:
                    session = response_data["session"]
                    has_id = "id" in session
                    correct_team = session.get("team_id") == test_team["id"]
                    correct_name = session.get("name") == session_data["name"]
                    details += f", Has session: {has_session}, Has ID: {has_id}, Team matches: {correct_team}, Name matches: {correct_name}"
                    success = has_session and has_id and correct_team and correct_name
            else:
                details += f", Response: {response.text[:200]}"
            
            integration_results.add_result(
                "create_session_success", "session_apis", success, details
            )
            
            assert success, f"Session creation failed: {details}"
            
            if success:
                return response_data["session"]
            
        except Exception as e:
            integration_results.add_error("create_session_success", str(e))
            pytest.fail(f"Session creation exception: {e}")
    
    @pytest.mark.asyncio
    async def test_create_session_invalid_team(self, auth_helper, http_client: httpx.AsyncClient,
                                             test_config, integration_results):
        """Test session creation with invalid team ID"""
        auth_data = await auth_helper.login()
        if "error" in auth_data:
            pytest.skip(f"Cannot test without authentication: {auth_data['error']}")
        
        fake_team_id = str(uuid.uuid4())
        session_data = TestDataGenerator.session_data(fake_team_id, "invalid_team")
        
        try:
            response = await http_client.post(
                f"{test_config.base_url}/sessions",
                json=session_data,
                headers=auth_helper.headers,
                timeout=30.0
            )
            
            # Should return 403 Forbidden (not a member of team)
            success = response.status_code == 403
            details = f"Status: {response.status_code}, Expected: 403"
            
            integration_results.add_result(
                "create_session_invalid_team", "session_apis", success, details
            )
            
            assert success, f"Invalid team should be rejected: {details}"
            
        except Exception as e:
            integration_results.add_error("create_session_invalid_team", str(e))
            pytest.fail(f"Invalid team test exception: {e}")
    
    @pytest.mark.asyncio
    async def test_create_session_malformed_data(self, auth_helper, test_team, http_client: httpx.AsyncClient,
                                               test_config, integration_results):
        """Test session creation with malformed data"""
        auth_data = await auth_helper.login()
        if "error" in auth_data:
            pytest.skip(f"Cannot test without authentication: {auth_data['error']}")
        
        malformed_data = ErrorSimulator.malformed_request_data("session")
        
        try:
            response = await http_client.post(
                f"{test_config.base_url}/sessions",
                json=malformed_data,
                headers=auth_helper.headers,
                timeout=30.0
            )
            
            # Should return 400 Bad Request
            success = response.status_code == 400
            details = f"Status: {response.status_code}, Expected: 400"
            
            integration_results.add_result(
                "create_session_malformed_data", "session_apis", success, details
            )
            
            assert success, f"Malformed data should be rejected: {details}"
            
        except Exception as e:
            integration_results.add_error("create_session_malformed_data", str(e))
            pytest.fail(f"Malformed data test exception: {e}")
    
    @pytest.mark.asyncio
    async def test_list_team_sessions(self, auth_helper, test_team, http_client: httpx.AsyncClient,
                                    test_config, integration_results):
        """Test listing sessions for a team"""
        auth_data = await auth_helper.login()
        if "error" in auth_data:
            pytest.skip(f"Cannot test without authentication: {auth_data['error']}")
        
        team_id = test_team["id"]
        
        try:
            response = await http_client.get(
                f"{test_config.base_url}/sessions/team/{team_id}",
                headers=auth_helper.headers,
                timeout=30.0
            )
            
            success = response.status_code == 200
            details = f"Status: {response.status_code}"
            
            if success:
                response_data = response.json()
                has_sessions = "sessions" in response_data
                sessions = response_data.get("sessions", [])
                is_list = isinstance(sessions, list)
                details += f", Has sessions: {has_sessions}, Is list: {is_list}, Count: {len(sessions)}"
                success = has_sessions and is_list
            else:
                details += f", Response: {response.text[:200]}"
            
            integration_results.add_result(
                "list_team_sessions", "session_apis", success, details
            )
            
            assert success, f"List team sessions failed: {details}"
            
        except Exception as e:
            integration_results.add_error("list_team_sessions", str(e))
            pytest.fail(f"List team sessions exception: {e}")
    
    @pytest.mark.asyncio
    async def test_get_session_details(self, auth_helper, test_session, http_client: httpx.AsyncClient,
                                     test_config, integration_results):
        """Test getting session details"""
        auth_data = await auth_helper.login()
        if "error" in auth_data:
            pytest.skip(f"Cannot test without authentication: {auth_data['error']}")
        
        session_id = test_session["id"]
        
        try:
            response = await http_client.get(
                f"{test_config.base_url}/sessions/{session_id}",
                headers=auth_helper.headers,
                timeout=30.0
            )
            
            success = response.status_code == 200
            details = f"Status: {response.status_code}"
            
            if success:
                response_data = response.json()
                has_session = "session" in response_data
                has_participants = "participants" in response_data
                has_messages = "recent_messages" in response_data
                if has_session:
                    session = response_data["session"]
                    correct_id = session.get("id") == session_id
                    details += f", Has session: {has_session}, Has participants: {has_participants}, Has messages: {has_messages}, Correct ID: {correct_id}"
                    success = has_session and correct_id
            else:
                details += f", Response: {response.text[:200]}"
            
            integration_results.add_result(
                "get_session_details", "session_apis", success, details
            )
            
            assert success, f"Get session details failed: {details}"
            
        except Exception as e:
            integration_results.add_error("get_session_details", str(e))
            pytest.fail(f"Get session details exception: {e}")
    
    @pytest.mark.asyncio
    async def test_get_nonexistent_session(self, auth_helper, http_client: httpx.AsyncClient,
                                         test_config, integration_results):
        """Test getting nonexistent session"""
        auth_data = await auth_helper.login()
        if "error" in auth_data:
            pytest.skip(f"Cannot test without authentication: {auth_data['error']}")
        
        fake_session_id = str(uuid.uuid4())
        
        try:
            response = await http_client.get(
                f"{test_config.base_url}/sessions/{fake_session_id}",
                headers=auth_helper.headers,
                timeout=30.0
            )
            
            # Should return 404 Not Found
            success = response.status_code == 404
            details = f"Status: {response.status_code}, Expected: 404"
            
            integration_results.add_result(
                "get_nonexistent_session", "session_apis", success, details
            )
            
            assert success, f"Nonexistent session should return 404: {details}"
            
        except Exception as e:
            integration_results.add_error("get_nonexistent_session", str(e))
            pytest.fail(f"Nonexistent session test exception: {e}")
    
    @pytest.mark.asyncio
    async def test_update_session(self, auth_helper, test_session, http_client: httpx.AsyncClient,
                                test_config, integration_results):
        """Test updating session information"""
        auth_data = await auth_helper.login()
        if "error" in auth_data:
            pytest.skip(f"Cannot test without authentication: {auth_data['error']}")
        
        session_id = test_session["id"]
        update_data = {
            "name": f"Updated Session {uuid.uuid4()}",
            "description": "Updated description for integration test"
        }
        
        try:
            response = await http_client.put(
                f"{test_config.base_url}/sessions/{session_id}",
                json=update_data,
                headers=auth_helper.headers,
                timeout=30.0
            )
            
            success = response.status_code == 200
            details = f"Status: {response.status_code}"
            
            if success:
                response_data = response.json()
                has_message = "message" in response_data or "session" in response_data
                details += f", Has success indicator: {has_message}"
            else:
                details += f", Response: {response.text[:200]}"
            
            integration_results.add_result(
                "update_session", "session_apis", success, details
            )
            
            assert success, f"Session update failed: {details}"
            
        except Exception as e:
            integration_results.add_error("update_session", str(e))
            pytest.fail(f"Session update exception: {e}")
    
    @pytest.mark.asyncio
    async def test_send_message_to_session(self, auth_helper, test_session, http_client: httpx.AsyncClient,
                                         test_config, integration_results):
        """Test sending a message to a session"""
        auth_data = await auth_helper.login()
        if "error" in auth_data:
            pytest.skip(f"Cannot test without authentication: {auth_data['error']}")
        
        session_id = test_session["id"]
        message_data = TestDataGenerator.message_data("Integration test message")
        
        try:
            response = await http_client.post(
                f"{test_config.base_url}/sessions/{session_id}/messages",
                json=message_data,
                headers=auth_helper.headers,
                timeout=30.0
            )
            
            success = response.status_code == 201
            details = f"Status: {response.status_code}"
            
            if success:
                response_data = response.json()
                has_message = "message" in response_data
                if has_message:
                    message = response_data["message"]
                    has_id = "id" in message
                    correct_session = message.get("session_id") == session_id
                    details += f", Has message: {has_message}, Has ID: {has_id}, Correct session: {correct_session}"
                    success = has_message and has_id and correct_session
            else:
                details += f", Response: {response.text[:200]}"
            
            integration_results.add_result(
                "send_message_to_session", "session_apis", success, details
            )
            
            assert success, f"Send message failed: {details}"
            
            if success:
                return response_data["message"]
            
        except Exception as e:
            integration_results.add_error("send_message_to_session", str(e))
            pytest.fail(f"Send message exception: {e}")
    
    @pytest.mark.asyncio
    async def test_get_session_messages(self, auth_helper, test_session, http_client: httpx.AsyncClient,
                                      test_config, integration_results):
        """Test getting messages from a session"""
        auth_data = await auth_helper.login()
        if "error" in auth_data:
            pytest.skip(f"Cannot test without authentication: {auth_data['error']}")
        
        session_id = test_session["id"]
        
        # First send a message to ensure there's something to retrieve
        message_data = TestDataGenerator.message_data("Test message for retrieval")
        await http_client.post(
            f"{test_config.base_url}/sessions/{session_id}/messages",
            json=message_data,
            headers=auth_helper.headers,
            timeout=30.0
        )
        
        try:
            # Get messages with limit
            response = await http_client.get(
                f"{test_config.base_url}/sessions/{session_id}/messages?limit=10",
                headers=auth_helper.headers,
                timeout=30.0
            )
            
            success = response.status_code == 200
            details = f"Status: {response.status_code}"
            
            if success:
                response_data = response.json()
                has_messages = "messages" in response_data
                messages = response_data.get("messages", [])
                is_list = isinstance(messages, list)
                details += f", Has messages: {has_messages}, Is list: {is_list}, Count: {len(messages)}"
                success = has_messages and is_list
            else:
                details += f", Response: {response.text[:200]}"
            
            integration_results.add_result(
                "get_session_messages", "session_apis", success, details
            )
            
            assert success, f"Get session messages failed: {details}"
            
        except Exception as e:
            integration_results.add_error("get_session_messages", str(e))
            pytest.fail(f"Get session messages exception: {e}")
    
    @pytest.mark.asyncio
    async def test_end_session(self, auth_helper, test_team, http_client: httpx.AsyncClient,
                             test_config, integration_results):
        """Test ending a session"""
        auth_data = await auth_helper.login()
        if "error" in auth_data:
            pytest.skip(f"Cannot test without authentication: {auth_data['error']}")
        
        # Create a session specifically for ending
        session_data = TestDataGenerator.session_data(test_team["id"], "end_test")
        create_response = await http_client.post(
            f"{test_config.base_url}/sessions",
            json=session_data,
            headers=auth_helper.headers,
            timeout=30.0
        )
        
        if create_response.status_code != 201:
            pytest.skip(f"Cannot test ending without creating session: {create_response.status_code}")
        
        session_id = create_response.json()["session"]["id"]
        
        try:
            response = await http_client.post(
                f"{test_config.base_url}/sessions/{session_id}/end",
                headers=auth_helper.headers,
                timeout=30.0
            )
            
            success = response.status_code == 200
            details = f"Status: {response.status_code}"
            
            if success:
                response_data = response.json()
                has_message = "message" in response_data
                details += f", Has success message: {has_message}"
            else:
                details += f", Response: {response.text[:200]}"
            
            integration_results.add_result(
                "end_session", "session_apis", success, details
            )
            
            assert success, f"End session failed: {details}"
            
        except Exception as e:
            integration_results.add_error("end_session", str(e))
            pytest.fail(f"End session exception: {e}")
    
    @pytest.mark.asyncio
    async def test_session_authorization_checks(self, auth_helper, http_client: httpx.AsyncClient,
                                              test_config, integration_results):
        """Test session-level authorization"""
        auth_data = await auth_helper.login()
        if "error" in auth_data:
            pytest.skip(f"Cannot test without authentication: {auth_data['error']}")
        
        # Try to access a session that doesn't exist or user doesn't have access to
        fake_session_id = str(uuid.uuid4())
        
        try:
            response = await http_client.get(
                f"{test_config.base_url}/sessions/{fake_session_id}",
                headers=auth_helper.headers,
                timeout=30.0
            )
            
            # Should return 404 Not Found or 403 Forbidden
            valid_statuses = [403, 404]
            success = response.status_code in valid_statuses
            details = f"Status: {response.status_code}, Valid statuses: {valid_statuses}"
            
            integration_results.add_result(
                "session_authorization_checks", "session_apis", success, details
            )
            
            assert success, f"Authorization check failed: {details}"
            
        except Exception as e:
            integration_results.add_error("session_authorization_checks", str(e))
            pytest.fail(f"Authorization check exception: {e}")
    
    @pytest.mark.asyncio
    async def test_message_validation(self, auth_helper, test_session, http_client: httpx.AsyncClient,
                                    test_config, integration_results):
        """Test message content validation"""
        auth_data = await auth_helper.login()
        if "error" in auth_data:
            pytest.skip(f"Cannot test without authentication: {auth_data['error']}")
        
        session_id = test_session["id"]
        invalid_message_data = ErrorSimulator.malformed_request_data("message")
        
        try:
            response = await http_client.post(
                f"{test_config.base_url}/sessions/{session_id}/messages",
                json=invalid_message_data,
                headers=auth_helper.headers,
                timeout=30.0
            )
            
            # Should return 400 Bad Request
            success = response.status_code == 400
            details = f"Status: {response.status_code}, Expected: 400"
            
            integration_results.add_result(
                "message_validation", "session_apis", success, details
            )
            
            assert success, f"Invalid message should be rejected: {details}"
            
        except Exception as e:
            integration_results.add_error("message_validation", str(e))
            pytest.fail(f"Message validation exception: {e}")
    
    @pytest.mark.asyncio
    async def test_session_management_complete(self, integration_results):
        """Verify session management integration testing is complete"""
        session_results = [r for r in integration_results.results if r["category"] == "session_apis"]
        
        expected_tests = [
            "create_session_success",
            "create_session_invalid_team",
            "create_session_malformed_data",
            "list_team_sessions",
            "get_session_details",
            "get_nonexistent_session",
            "update_session",
            "send_message_to_session",
            "get_session_messages",
            "session_authorization_checks",
            "message_validation"
        ]
        
        completed_tests = [r["test_name"] for r in session_results]
        missing_tests = [t for t in expected_tests if t not in completed_tests]
        passed_tests = [r for r in session_results if r["passed"]]
        
        success = len(missing_tests) == 0
        details = f"Completed: {len(completed_tests)}, Passed: {len(passed_tests)}, Missing: {missing_tests}"
        
        integration_results.add_result(
            "session_management_complete", "session_apis", success, details
        )
        
        logger.info(f"Session management integration test summary: {details}")
        
        if missing_tests:
            pytest.fail(f"Missing session management tests: {missing_tests}")