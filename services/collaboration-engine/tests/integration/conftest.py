"""
Integration Test Configuration for Collaboration Engine
Addresses EPIS-017: Integration testing framework missing

This module provides shared fixtures and configuration for the integration test suite.
"""

import pytest
import asyncio
import httpx
import json
import os
import uuid
from typing import Dict, Any, Optional, AsyncGenerator
from dataclasses import dataclass
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class TestConfig:
    """Test configuration settings"""
    base_url: str
    analysis_engine_url: str
    timeout: int
    max_retries: int
    
    @classmethod
    def from_env(cls) -> 'TestConfig':
        """Create config from environment variables"""
        return cls(
            base_url=os.getenv(
                'COLLABORATION_ENGINE_URL', 
                'https://collaboration-engine-production-xxxxx.run.app'
            ),
            analysis_engine_url=os.getenv(
                'ANALYSIS_ENGINE_URL',
                'https://analysis-engine-production-xxxxx.run.app'
            ),
            timeout=int(os.getenv('TEST_TIMEOUT', '30')),
            max_retries=int(os.getenv('TEST_MAX_RETRIES', '3'))
        )

@pytest.fixture(scope="session")
def test_config() -> TestConfig:
    """Provide test configuration"""
    return TestConfig.from_env()

@pytest.fixture(scope="session")
def event_loop():
    """Create event loop for async tests"""
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()

class AuthHelper:
    """Helper class for authentication in tests"""
    
    def __init__(self, client: httpx.AsyncClient, base_url: str):
        self.client = client
        self.base_url = base_url
        self._token: Optional[str] = None
        self._user_id: Optional[str] = None
        self._user_info: Optional[Dict[str, Any]] = None
    
    async def login(self, email: str = "<EMAIL>", name: str = "Test User") -> Dict[str, Any]:
        """Login and get auth token"""
        login_data = {
            "email": email,
            "name": name,
            "provider": "test"
        }
        
        try:
            response = await self.client.post(
                f"{self.base_url}/auth/login",
                json=login_data,
                timeout=30.0
            )
            
            if response.status_code == 200:
                auth_data = response.json()
                self._token = auth_data.get("access_token")
                self._user_id = auth_data.get("user", {}).get("user_id")
                self._user_info = auth_data.get("user", {})
                return auth_data
            else:
                logger.error(f"Login failed: {response.status_code} - {response.text}")
                return {"error": f"Login failed: {response.status_code}"}
                
        except Exception as e:
            logger.error(f"Login exception: {e}")
            return {"error": f"Login exception: {e}"}
    
    @property
    def headers(self) -> Dict[str, str]:
        """Get authentication headers"""
        if self._token:
            return {"Authorization": f"Bearer {self._token}"}
        return {}
    
    @property
    def user_id(self) -> Optional[str]:
        """Get authenticated user ID"""
        return self._user_id
    
    @property
    def user_info(self) -> Optional[Dict[str, Any]]:
        """Get authenticated user info"""
        return self._user_info

class TestDataGenerator:
    """Generate test data for various scenarios"""
    
    @staticmethod
    def team_data(name_suffix: str = None) -> Dict[str, Any]:
        """Generate team creation data"""
        suffix = name_suffix or str(uuid.uuid4())[:8]
        return {
            "name": f"Test Team {suffix}",
            "description": f"Integration test team created at {suffix}",
            "settings": {
                "max_members": 10,
                "allow_guest_access": False,
                "require_2fa": False,
                "session_recording_enabled": True,
                "retention_days": 30,
                "allowed_domains": ["example.com"]
            }
        }
    
    @staticmethod
    def session_data(team_id: str, name_suffix: str = None) -> Dict[str, Any]:
        """Generate session creation data"""
        suffix = name_suffix or str(uuid.uuid4())[:8]
        return {
            "team_id": team_id,
            "name": f"Test Session {suffix}",
            "description": f"Integration test session created at {suffix}",
            "settings": {
                "recording_enabled": True,
                "allow_anonymous": False,
                "max_participants": 50
            }
        }
    
    @staticmethod
    def message_data(content: str = None) -> Dict[str, Any]:
        """Generate message data"""
        content = content or f"Test message {uuid.uuid4()}"
        return {
            "content": {
                "type": "text",
                "text": content
            },
            "metadata": {
                "tags": ["integration-test"],
                "priority": "normal"
            }
        }
    
    @staticmethod
    def analysis_share_data(analysis_id: str = None) -> Dict[str, Any]:
        """Generate analysis sharing data"""
        analysis_id = analysis_id or f"test-analysis-{uuid.uuid4()}"
        return {
            "analysis_id": analysis_id,
            "sharing_permissions": ["view", "comment"],
            "highlights": [
                {
                    "start_line": 10,
                    "end_line": 15,
                    "comment": "Important code section"
                }
            ]
        }

@pytest.fixture
async def http_client(test_config: TestConfig) -> AsyncGenerator[httpx.AsyncClient, None]:
    """Provide HTTP client for API testing"""
    async with httpx.AsyncClient(
        timeout=httpx.Timeout(test_config.timeout),
        follow_redirects=True
    ) as client:
        yield client

@pytest.fixture
async def auth_helper(http_client: httpx.AsyncClient, test_config: TestConfig) -> AuthHelper:
    """Provide authenticated helper"""
    helper = AuthHelper(http_client, test_config.base_url)
    await helper.login()
    return helper

@pytest.fixture
async def test_team(auth_helper: AuthHelper, http_client: httpx.AsyncClient, test_config: TestConfig) -> Dict[str, Any]:
    """Create a test team for integration tests"""
    team_data = TestDataGenerator.team_data()
    
    response = await http_client.post(
        f"{test_config.base_url}/teams",
        json=team_data,
        headers=auth_helper.headers,
        timeout=30.0
    )
    
    if response.status_code == 201:
        return response.json()["team"]
    else:
        pytest.fail(f"Failed to create test team: {response.status_code} - {response.text}")

@pytest.fixture
async def test_session(auth_helper: AuthHelper, test_team: Dict[str, Any], 
                      http_client: httpx.AsyncClient, test_config: TestConfig) -> Dict[str, Any]:
    """Create a test session for integration tests"""
    session_data = TestDataGenerator.session_data(test_team["id"])
    
    response = await http_client.post(
        f"{test_config.base_url}/sessions",
        json=session_data,
        headers=auth_helper.headers,
        timeout=30.0
    )
    
    if response.status_code == 201:
        return response.json()["session"]
    else:
        pytest.fail(f"Failed to create test session: {response.status_code} - {response.text}")

class IntegrationTestResult:
    """Track integration test results for reporting"""
    
    def __init__(self):
        self.results = []
        self.coverage = {
            "team_apis": 0,
            "session_apis": 0,
            "integration_apis": 0,
            "auth_apis": 0,
            "websocket_features": 0,
            "error_scenarios": 0
        }
        self.errors = []
    
    def add_result(self, test_name: str, category: str, passed: bool, details: str = ""):
        """Add test result"""
        self.results.append({
            "test_name": test_name,
            "category": category,
            "passed": passed,
            "details": details
        })
        
        if passed:
            self.coverage[category] = self.coverage.get(category, 0) + 1
    
    def add_error(self, test_name: str, error: str):
        """Add test error"""
        self.errors.append({
            "test_name": test_name,
            "error": error
        })
    
    def get_summary(self) -> Dict[str, Any]:
        """Get test summary"""
        total_tests = len(self.results)
        passed_tests = sum(1 for r in self.results if r["passed"])
        
        return {
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "failed_tests": total_tests - passed_tests,
            "success_rate": (passed_tests / total_tests) * 100 if total_tests > 0 else 0,
            "coverage": self.coverage,
            "errors": self.errors
        }

@pytest.fixture(scope="session")
def integration_results() -> IntegrationTestResult:
    """Provide shared test results tracker"""
    return IntegrationTestResult()

# Health check helper
async def check_service_health(client: httpx.AsyncClient, base_url: str) -> Dict[str, Any]:
    """Check if service is healthy"""
    try:
        response = await client.get(f"{base_url}/health", timeout=10.0)
        return {
            "healthy": response.status_code == 200,
            "status_code": response.status_code,
            "response": response.text if response.status_code != 200 else "OK"
        }
    except Exception as e:
        return {
            "healthy": False,
            "status_code": None,
            "response": str(e)
        }

# Error simulation helpers
class ErrorSimulator:
    """Simulate various error conditions for testing"""
    
    @staticmethod
    def invalid_auth_headers() -> Dict[str, str]:
        """Generate invalid auth headers"""
        return {"Authorization": "Bearer invalid-token-12345"}
    
    @staticmethod
    def malformed_request_data(data_type: str) -> Dict[str, Any]:
        """Generate malformed request data"""
        if data_type == "team":
            return {"invalid_field": "value", "missing": "required_fields"}
        elif data_type == "session":
            return {"team_id": "invalid", "missing_name": True}
        elif data_type == "message":
            return {"content": None, "invalid": "structure"}
        else:
            return {"malformed": True}