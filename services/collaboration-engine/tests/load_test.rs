use collaboration_engine::{
    models::{WebSocketMessage, UserId, UserInfo},
};
use futures_util::{SinkExt, StreamExt};
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::{mpsc, Mutex};
use tokio_tungstenite::{connect_async, tungstenite::Message};
use tracing::{error, info};

/// Load test configuration
#[derive(Clone)]
struct LoadTestConfig {
    /// Base URL for the collaboration engine
    base_url: String,
    /// Number of concurrent connections
    concurrent_connections: usize,
    /// Messages per second per connection
    messages_per_second: f64,
    /// Test duration
    test_duration: Duration,
    /// JWT token for authentication
    jwt_token: String,
}

impl Default for LoadTestConfig {
    fn default() -> Self {
        Self {
            base_url: "ws://localhost:8001".to_string(),
            concurrent_connections: 50,
            messages_per_second: 10.0,
            test_duration: Duration::from_secs(60),
            jwt_token: "test-jwt-token".to_string(),
        }
    }
}

/// Metrics collected during load test
#[derive(Default)]
struct LoadTestMetrics {
    total_connections: usize,
    successful_connections: usize,
    failed_connections: usize,
    total_messages_sent: usize,
    total_messages_received: usize,
    total_errors: usize,
    latencies: Vec<Duration>,
}

impl LoadTestMetrics {
    fn add_latency(&mut self, latency: Duration) {
        self.latencies.push(latency);
    }
    
    fn get_latency_percentiles(&self) -> (Duration, Duration, Duration) {
        let mut sorted = self.latencies.clone();
        sorted.sort();
        
        if sorted.is_empty() {
            return (Duration::ZERO, Duration::ZERO, Duration::ZERO);
        }
        
        let p50 = sorted[sorted.len() / 2];
        let p95 = sorted[sorted.len() * 95 / 100];
        let p99 = sorted[sorted.len() * 99 / 100];
        
        (p50, p95, p99)
    }
    
    fn print_summary(&self) {
        println!("\n=== Load Test Summary ===");
        println!("Total Connections: {}", self.total_connections);
        println!("Successful Connections: {}", self.successful_connections);
        println!("Failed Connections: {}", self.failed_connections);
        println!("Total Messages Sent: {}", self.total_messages_sent);
        println!("Total Messages Received: {}", self.total_messages_received);
        println!("Total Errors: {}", self.total_errors);
        
        let (p50, p95, p99) = self.get_latency_percentiles();
        println!("\nLatency Percentiles:");
        println!("  P50: {:?}", p50);
        println!("  P95: {:?}", p95);
        println!("  P99: {:?}", p99);
        
        if !self.latencies.is_empty() {
            let avg_latency: Duration = self.latencies.iter().sum::<Duration>() / self.latencies.len() as u32;
            println!("  Average: {:?}", avg_latency);
        }
    }
}

/// Simulate a single WebSocket client
async fn simulate_client(
    client_id: usize,
    config: LoadTestConfig,
    metrics: Arc<Mutex<LoadTestMetrics>>,
) -> Result<(), Box<dyn std::error::Error>> {
    let ws_url = format!("{}/api/v1/ws", config.base_url);
    
    // Connect with auth header
    let request = http::Request::builder()
        .uri(&ws_url)
        .header("Authorization", format!("Bearer {}", config.jwt_token))
        .body(())?;
    
    let (ws_stream, _) = match connect_async(request).await {
        Ok(conn) => {
            metrics.lock().await.successful_connections += 1;
            conn
        }
        Err(e) => {
            error!("Client {} failed to connect: {}", client_id, e);
            metrics.lock().await.failed_connections += 1;
            return Err(e.into());
        }
    };
    
    let (mut write, mut read) = ws_stream.split();
    let (tx, mut rx) = mpsc::channel::<WebSocketMessage>(100);
    
    // Spawn message sender
    let send_handle = tokio::spawn({
        let metrics = metrics.clone();
        let config = config.clone();
        async move {
            let mut interval = tokio::time::interval(Duration::from_secs_f64(1.0 / config.messages_per_second));
            let mut message_count = 0;
            
            loop {
                tokio::select! {
                    _ = interval.tick() => {
                        // Send different types of messages
                        let message = match message_count % 3 {
                            0 => WebSocketMessage::Ping,
                            1 => WebSocketMessage::CursorMove {
                                session_id: "test-session".to_string(),
                                user_id: UserId::from_string(format!("user-{}", client_id)),
                                position: collaboration_engine::models::CursorPosition {
                                    x: rand::random::<f64>() * 1000.0,
                                    y: rand::random::<f64>() * 1000.0,
                                    element_id: Some("editor".to_string()),
                                },
                            },
                            _ => WebSocketMessage::MessageSent {
                                message: collaboration_engine::models::Message {
                                    id: uuid::Uuid::new_v4().to_string(),
                                    session_id: "test-session".to_string(),
                                    user_id: UserId::from_string(format!("user-{}", client_id)),
                                    content: collaboration_engine::models::MessageContent::Text {
                                        text: format!("Test message {} from client {}", message_count, client_id),
                                        mentions: vec![],
                                    },
                                    created_at: collaboration_engine::models::Timestamp::now(),
                                    updated_at: None,
                                    edited: false,
                                    deleted: false,
                                    metadata: Default::default(),
                                },
                            },
                        };
                        
                        let start = Instant::now();
                        if let Ok(json) = serde_json::to_string(&message) {
                            if write.send(Message::Text(json)).await.is_ok() {
                                metrics.lock().await.total_messages_sent += 1;
                                message_count += 1;
                                
                                // Record latency when we get a response
                                // For now, just record the send time
                                let latency = start.elapsed();
                                metrics.lock().await.add_latency(latency);
                            } else {
                                metrics.lock().await.total_errors += 1;
                            }
                        }
                    }
                    
                    msg = rx.recv() => {
                        if let Some(msg) = msg {
                            if let Ok(json) = serde_json::to_string(&msg) {
                                if write.send(Message::Text(json)).await.is_err() {
                                    break;
                                }
                            }
                        }
                    }
                }
            }
        }
    });
    
    // Spawn message receiver
    let receive_handle = tokio::spawn({
        let metrics = metrics.clone();
        async move {
            while let Some(msg) = read.next().await {
                match msg {
                    Ok(Message::Text(text)) => {
                        if let Ok(_message) = serde_json::from_str::<WebSocketMessage>(&text) {
                            metrics.lock().await.total_messages_received += 1;
                        }
                    }
                    Ok(Message::Close(_)) => break,
                    Err(e) => {
                        error!("Client {} receive error: {}", client_id, e);
                        metrics.lock().await.total_errors += 1;
                        break;
                    }
                    _ => {}
                }
            }
        }
    });
    
    // Run for test duration
    tokio::time::sleep(config.test_duration).await;
    
    // Clean shutdown
    let _ = write.close().await;
    send_handle.abort();
    receive_handle.abort();
    
    Ok(())
}

/// Run the load test
pub async fn run_load_test(config: LoadTestConfig) -> Result<(), Box<dyn std::error::Error>> {
    info!("Starting load test with {} concurrent connections", config.concurrent_connections);
    info!("Messages per second per connection: {}", config.messages_per_second);
    info!("Test duration: {:?}", config.test_duration);
    
    let metrics = Arc::new(Mutex::new(LoadTestMetrics {
        total_connections: config.concurrent_connections,
        ..Default::default()
    }));
    
    let start_time = Instant::now();
    let mut handles = vec![];
    
    // Launch concurrent clients
    for i in 0..config.concurrent_connections {
        let config = config.clone();
        let metrics = metrics.clone();
        
        let handle = tokio::spawn(async move {
            if let Err(e) = simulate_client(i, config, metrics).await {
                error!("Client {} error: {}", i, e);
            }
        });
        
        handles.push(handle);
        
        // Stagger client starts to avoid thundering herd
        tokio::time::sleep(Duration::from_millis(10)).await;
    }
    
    // Wait for all clients to complete
    for handle in handles {
        let _ = handle.await;
    }
    
    let total_duration = start_time.elapsed();
    
    // Print results
    let final_metrics = metrics.lock().await;
    final_metrics.print_summary();
    println!("\nTotal test duration: {:?}", total_duration);
    
    // Check if we met latency targets
    let (p50, p95, p99) = final_metrics.get_latency_percentiles();
    if p95 > Duration::from_millis(50) {
        println!("\n⚠️  WARNING: P95 latency ({:?}) exceeds 50ms target!", p95);
    } else {
        println!("\n✅ SUCCESS: P95 latency ({:?}) meets <50ms target!", p95);
    }
    
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    #[ignore] // Run with: cargo test --test load_test -- --ignored --nocapture
    async fn test_load_50_connections() {
        let config = LoadTestConfig {
            concurrent_connections: 50,
            messages_per_second: 10.0,
            test_duration: Duration::from_secs(30),
            ..Default::default()
        };
        
        run_load_test(config).await.unwrap();
    }
    
    #[tokio::test]
    #[ignore] // Run with: cargo test --test load_test -- --ignored --nocapture
    async fn test_load_100_connections() {
        let config = LoadTestConfig {
            concurrent_connections: 100,
            messages_per_second: 5.0,
            test_duration: Duration::from_secs(60),
            ..Default::default()
        };
        
        run_load_test(config).await.unwrap();
    }
    
    #[tokio::test]
    #[ignore] // Run with: cargo test --test load_test -- --ignored --nocapture
    async fn test_burst_load() {
        let config = LoadTestConfig {
            concurrent_connections: 200,
            messages_per_second: 20.0,
            test_duration: Duration::from_secs(10),
            ..Default::default()
        };
        
        run_load_test(config).await.unwrap();
    }
}