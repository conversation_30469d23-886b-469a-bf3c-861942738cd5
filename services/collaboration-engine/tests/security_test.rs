use axum::http::{header, StatusCode};
use collaboration_engine::api::create_api_router;
use tower::ServiceExt;
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

/// Initialize test environment
fn init_test_env() {
    let _ = tracing_subscriber::registry()
        .with(tracing_subscriber::fmt::layer())
        .try_init();
}

/// Create test app state
async fn create_test_state() -> std::sync::Arc<collaboration_engine::api::AppState> {
    let config = collaboration_engine::config::Config::from_env()
        .unwrap_or_else(|_| collaboration_engine::config::Config {
            port: 8001,
            metrics_port: 9001,
            jwt_secret: "test-secret-key-for-testing-only".to_string(),
            jwt_access_token_expiry_secs: 900,
            jwt_refresh_token_expiry_secs: 86400,
            rate_limit_requests_per_minute: 60,
            rate_limit_websocket_messages_per_minute: 60,
            ..Default::default()
        });
    
    let storage = collaboration_engine::storage::create_storage_clients(&config)
        .await
        .expect("Failed to create storage clients");
    
    collaboration_engine::api::AppState::new(config, storage)
}

#[tokio::test]
async fn test_auth_required_endpoints() {
    init_test_env();
    let state = create_test_state().await;
    let app = create_api_router(state);
    
    // Test endpoints that should require authentication
    let protected_endpoints = vec![
        ("/sessions", "GET"),
        ("/sessions", "POST"),
        ("/sessions/test-id", "GET"),
        ("/teams", "GET"),
        ("/teams", "POST"),
        ("/teams/test-id", "GET"),
        ("/ws", "GET"),
    ];
    
    for (path, method) in protected_endpoints {
        let request = match method {
            "GET" => axum::http::Request::builder()
                .uri(path)
                .method("GET")
                .body(axum::body::Body::empty())
                .unwrap(),
            "POST" => axum::http::Request::builder()
                .uri(path)
                .method("POST")
                .header(header::CONTENT_TYPE, "application/json")
                .body(axum::body::Body::from("{}"))
                .unwrap(),
            _ => panic!("Unsupported method"),
        };
        
        let response = app.clone().oneshot(request).await.unwrap();
        
        assert_eq!(
            response.status(),
            StatusCode::UNAUTHORIZED,
            "Endpoint {} {} should require authentication",
            method,
            path
        );
    }
}

#[tokio::test]
async fn test_invalid_jwt_rejected() {
    init_test_env();
    let state = create_test_state().await;
    let app = create_api_router(state);
    
    // Test with various invalid JWTs
    let invalid_tokens = vec![
        "invalid.jwt.token",
        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.invalid.signature",
        "", // Empty token
        "Bearer", // Just the prefix
    ];
    
    for token in invalid_tokens {
        let request = axum::http::Request::builder()
            .uri("/sessions")
            .method("GET")
            .header(header::AUTHORIZATION, format!("Bearer {}", token))
            .body(axum::body::Body::empty())
            .unwrap();
        
        let response = app.clone().oneshot(request).await.unwrap();
        
        assert_eq!(
            response.status(),
            StatusCode::UNAUTHORIZED,
            "Invalid JWT '{}' should be rejected",
            token
        );
    }
}

#[tokio::test]
async fn test_expired_jwt_rejected() {
    init_test_env();
    let state = create_test_state().await;
    let app = create_api_router(state.clone());
    
    // Create an expired JWT
    use jsonwebtoken::{encode, Algorithm, EncodingKey, Header};
    use serde::{Deserialize, Serialize};
    
    #[derive(Debug, Serialize, Deserialize)]
    struct Claims {
        sub: String,
        email: String,
        name: String,
        roles: Vec<String>,
        exp: usize,
        iat: usize,
        token_type: String,
    }
    
    let now = chrono::Utc::now();
    let claims = Claims {
        sub: "test-user".to_string(),
        email: "<EMAIL>".to_string(),
        name: "Test User".to_string(),
        roles: vec!["user".to_string()],
        exp: (now - chrono::Duration::hours(1)).timestamp() as usize, // Expired 1 hour ago
        iat: (now - chrono::Duration::hours(2)).timestamp() as usize,
        token_type: "access".to_string(),
    };
    
    let header = Header::new(Algorithm::HS256);
    let encoding_key = EncodingKey::from_secret(state.jwt_service.as_ref().unwrap().encoding_key.as_bytes());
    let expired_token = encode(&header, &claims, &encoding_key).unwrap();
    
    let request = axum::http::Request::builder()
        .uri("/sessions")
        .method("GET")
        .header(header::AUTHORIZATION, format!("Bearer {}", expired_token))
        .body(axum::body::Body::empty())
        .unwrap();
    
    let response = app.oneshot(request).await.unwrap();
    
    assert_eq!(
        response.status(),
        StatusCode::UNAUTHORIZED,
        "Expired JWT should be rejected"
    );
}

#[tokio::test]
async fn test_rate_limiting() {
    init_test_env();
    let state = create_test_state().await;
    let app = create_api_router(state);
    
    // Make requests up to the limit
    let rate_limit = 60; // Default rate limit per minute
    let mut success_count = 0;
    
    for i in 0..rate_limit + 10 {
        let request = axum::http::Request::builder()
            .uri("/health")
            .method("GET")
            .header("X-Real-IP", "***********") // Same IP for all requests
            .body(axum::body::Body::empty())
            .unwrap();
        
        let response = app.clone().oneshot(request).await.unwrap();
        
        if response.status() == StatusCode::OK {
            success_count += 1;
        } else if response.status() == StatusCode::TOO_MANY_REQUESTS {
            // We should hit rate limit eventually
            assert!(
                i >= rate_limit - 1,
                "Rate limit hit too early at request {}",
                i + 1
            );
            break;
        }
    }
    
    assert!(
        success_count <= rate_limit,
        "Too many requests succeeded: {} > {}",
        success_count,
        rate_limit
    );
}

#[tokio::test]
async fn test_sql_injection_prevention() {
    init_test_env();
    let state = create_test_state().await;
    let app = create_api_router(state);
    
    // Common SQL injection attempts
    let sql_injection_payloads = vec![
        r#"{"name": "'; DROP TABLE users; --"}"#,
        r#"{"email": "admin'--"}"#,
        r#"{"id": "1 OR 1=1"}"#,
        r#"{"query": "' UNION SELECT * FROM passwords --"}"#,
    ];
    
    // Create a valid JWT for testing
    let token = create_test_jwt();
    
    for payload in sql_injection_payloads {
        let request = axum::http::Request::builder()
            .uri("/teams")
            .method("POST")
            .header(header::CONTENT_TYPE, "application/json")
            .header(header::AUTHORIZATION, format!("Bearer {}", token))
            .body(axum::body::Body::from(payload))
            .unwrap();
        
        let response = app.clone().oneshot(request).await.unwrap();
        
        // Should either reject as bad request or handle safely
        assert!(
            response.status() == StatusCode::BAD_REQUEST
                || response.status() == StatusCode::UNPROCESSABLE_ENTITY
                || response.status() == StatusCode::OK,
            "SQL injection attempt should be handled safely, got status: {}",
            response.status()
        );
    }
}

#[tokio::test]
async fn test_xss_prevention() {
    init_test_env();
    let state = create_test_state().await;
    let app = create_api_router(state);
    
    // Common XSS payloads
    let xss_payloads = vec![
        r#"{"message": "<script>alert('XSS')</script>"}"#,
        r#"{"name": "<img src=x onerror=alert('XSS')>"}"#,
        r#"{"text": "javascript:alert('XSS')"}"#,
        r#"{"content": "<svg onload=alert('XSS')>"}"#,
    ];
    
    let token = create_test_jwt();
    
    for payload in xss_payloads {
        let request = axum::http::Request::builder()
            .uri("/sessions")
            .method("POST")
            .header(header::CONTENT_TYPE, "application/json")
            .header(header::AUTHORIZATION, format!("Bearer {}", token))
            .body(axum::body::Body::from(payload))
            .unwrap();
        
        let response = app.clone().oneshot(request).await.unwrap();
        
        // Get response body
        let body = axum::body::to_bytes(response.into_body(), 1024 * 1024)
            .await
            .unwrap();
        let body_str = String::from_utf8_lossy(&body);
        
        // Ensure no unescaped script tags in response
        assert!(
            !body_str.contains("<script>") && !body_str.contains("javascript:"),
            "Response contains unescaped XSS payload"
        );
    }
}

#[tokio::test]
async fn test_cors_headers() {
    init_test_env();
    let state = create_test_state().await;
    let app = create_api_router(state);
    
    // Test preflight request
    let request = axum::http::Request::builder()
        .uri("/health")
        .method("OPTIONS")
        .header(header::ORIGIN, "https://example.com")
        .header(header::ACCESS_CONTROL_REQUEST_METHOD, "GET")
        .body(axum::body::Body::empty())
        .unwrap();
    
    let response = app.clone().oneshot(request).await.unwrap();
    
    assert_eq!(response.status(), StatusCode::OK);
    assert!(response.headers().contains_key(header::ACCESS_CONTROL_ALLOW_ORIGIN));
    assert!(response.headers().contains_key(header::ACCESS_CONTROL_ALLOW_METHODS));
}

#[tokio::test]
async fn test_security_headers() {
    init_test_env();
    let state = create_test_state().await;
    let app = create_api_router(state);
    
    let request = axum::http::Request::builder()
        .uri("/health")
        .method("GET")
        .body(axum::body::Body::empty())
        .unwrap();
    
    let response = app.oneshot(request).await.unwrap();
    
    // Check security headers
    assert!(
        response.headers().contains_key("x-frame-options"),
        "Missing X-Frame-Options header"
    );
    assert!(
        response.headers().contains_key("x-content-type-options"),
        "Missing X-Content-Type-Options header"
    );
    assert!(
        response.headers().contains_key("x-xss-protection"),
        "Missing X-XSS-Protection header"
    );
    assert!(
        response.headers().contains_key("referrer-policy"),
        "Missing Referrer-Policy header"
    );
    assert!(
        response.headers().contains_key("permissions-policy"),
        "Missing Permissions-Policy header"
    );
}

#[tokio::test]
async fn test_path_traversal_prevention() {
    init_test_env();
    let state = create_test_state().await;
    let app = create_api_router(state);
    
    // Path traversal attempts
    let malicious_paths = vec![
        "/sessions/../../../etc/passwd",
        "/teams/%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd",
        "/sessions/..\\..\\..\\windows\\system32\\config\\sam",
    ];
    
    let token = create_test_jwt();
    
    for path in malicious_paths {
        let request = axum::http::Request::builder()
            .uri(path)
            .method("GET")
            .header(header::AUTHORIZATION, format!("Bearer {}", token))
            .body(axum::body::Body::empty())
            .unwrap();
        
        let response = app.clone().oneshot(request).await.unwrap();
        
        assert_ne!(
            response.status(),
            StatusCode::OK,
            "Path traversal attempt should be blocked: {}",
            path
        );
    }
}

/// Helper function to create a valid test JWT
fn create_test_jwt() -> String {
    // This would normally use the actual JWT service
    // For testing, we create a simple valid token
    "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************.test-signature"
}