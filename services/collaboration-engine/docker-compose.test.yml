version: '3.8'

services:
  redis:
    image: redis:7-alpine
    container_name: collaboration-redis
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 3s
      retries: 5

  # Optional: Spanner emulator for local testing
  spanner-emulator:
    image: gcr.io/cloud-spanner-emulator/emulator:latest
    container_name: collaboration-spanner
    ports:
      - "9010:9010"
      - "9020:9020"
    environment:
      - SPANNER_EMULATOR_HOST=0.0.0.0:9010

  # The collaboration engine service
  # Uncomment when Docker image is built
  # collaboration-engine:
  #   build:
  #     context: .
  #     dockerfile: Dockerfile
  #   container_name: collaboration-engine-test
  #   ports:
  #     - "8003:8003"
  #     - "9003:9003"
  #   env_file:
  #     - test.env
  #   environment:
  #     - REDIS_URL=redis://redis:6379
  #     - SPANNER_EMULATOR_HOST=spanner-emulator:9010
  #   depends_on:
  #     redis:
  #       condition: service_healthy
  #   healthcheck:
  #     test: ["CMD", "curl", "-f", "http://localhost:8003/health"]
  #     interval: 10s
  #     timeout: 5s
  #     retries: 5

networks:
  default:
    name: collaboration-test-network