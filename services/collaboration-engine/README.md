# Collaboration Engine

High-performance real-time collaboration service for the Episteme platform, built with Rust for <50ms WebSocket latency and 50+ concurrent users per session.

## 🚀 Features

- **Real-time WebSocket Communication**: Sub-50ms latency with tokio-tungstenite
- **Horizontal Scaling**: Redis pub/sub for multi-instance support
- **Team Management**: Create teams, manage members, and set permissions
- **Collaborative Sessions**: Real-time shared sessions with presence tracking
- **Message System**: Persistent messaging with multiple content types
- **Analysis Integration**: Share and collaborate on code analysis results
- **Production Ready**: JWT authentication, rate limiting, and comprehensive monitoring

## 🏗️ Architecture

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│   WebSocket     │────▶│  Collaboration  │────▶│     Redis       │
│    Clients      │     │     Engine      │     │   Pub/Sub       │
└─────────────────┘     └────────┬────────┘     └─────────────────┘
                                 │
                                 │
                        ┌────────┴────────┐
                        │                 │
                   ┌────▼────┐      ┌────▼────┐
                   │ Spanner │      │  Redis  │
                   │   DB    │      │  Cache  │
                   └─────────┘      └─────────┘
```

## 🛠️ Technology Stack

- **Language**: Rust 2021 Edition
- **Web Framework**: Axum 0.7
- **WebSocket**: tokio-tungstenite
- **Async Runtime**: Tokio
- **Databases**: Google Cloud Spanner (persistence) + Redis (cache/pub-sub)
- **Authentication**: JWT with jsonwebtoken
- **Monitoring**: Prometheus metrics

## 📋 Prerequisites

- Rust 1.75+
- Redis server
- Google Cloud credentials with Spanner access
- JWT secret for authentication

## 🚀 Getting Started

### Local Development

1. **Clone and navigate to the service**:
```bash
cd services/collaboration-engine
```

2. **Set up environment variables**:
```bash
cp .env.example .env
# Edit .env with your configuration
```

3. **Run locally**:
```bash
cargo run
```

4. **Run tests**:
```bash
cargo test
```

### Docker Development

```bash
# Build the image
docker build -t collaboration-engine .

# Run the container
docker run -p 8003:8003 -p 9003:9003 collaboration-engine
```

## 🔧 Configuration

Key environment variables:

```env
# Server
PORT=8003
METRICS_PORT=9003

# JWT Authentication
JWT_SECRET=your-secret-key
JWT_ISSUER=episteme
JWT_AUDIENCE=episteme-platform

# Redis
REDIS_URL=redis://localhost:6379

# Spanner
GCP_PROJECT_ID=your-project
SPANNER_INSTANCE_ID=your-instance
SPANNER_DATABASE_ID=your-database

# WebSocket
WS_HEARTBEAT_INTERVAL_SECS=30
WS_MAX_CONNECTIONS_PER_USER=5
```

## 📡 API Endpoints

### HTTP Endpoints

- `GET /health` - Health check
- `GET /health/ready` - Readiness probe
- `GET /health/live` - Liveness probe
- `GET /metrics` - Prometheus metrics (port 9003)

### WebSocket Endpoint

- `WS /api/v1/ws` - Main WebSocket connection

### Team Management (HTTP)

- `POST /api/v1/teams` - Create team
- `GET /api/v1/teams` - List user's teams
- `GET /api/v1/teams/:id` - Get team details
- `PUT /api/v1/teams/:id` - Update team
- `DELETE /api/v1/teams/:id` - Delete team

### Session Management (HTTP)

- `POST /api/v1/sessions` - Create session
- `GET /api/v1/sessions/team/:team_id` - List team sessions
- `GET /api/v1/sessions/:id` - Get session details
- `PUT /api/v1/sessions/:id` - Update session
- `POST /api/v1/sessions/:id/end` - End session

## 🔗 Integration Features

### Analysis Engine Integration

Share code analysis results in real-time collaboration sessions:

```bash
# Share an analysis
POST /api/v1/sessions/{session_id}/share/analysis
{
  "analysis_id": "uuid",
  "highlights": [
    {
      "file_path": "src/main.rs",
      "start_line": 10,
      "end_line": 20,
      "annotation": "Interesting pattern here"
    }
  ]
}

# Get shared analyses
GET /api/v1/sessions/{session_id}/shared/analyses
```

### Query Intelligence Integration

Share code search queries and results (ready for future Query Intelligence service):

```bash
# Share a query
POST /api/v1/sessions/{session_id}/share/query
{
  "query_text": "find all authentication functions",
  "query_type": "code_search",
  "include_results": true
}

# Get shared queries
GET /api/v1/sessions/{session_id}/shared/queries
```

### Cross-Service Events

The service subscribes to Pub/Sub events from other Episteme services:

- **AnalysisCompleted**: Notifies when repository analysis finishes
- **QueryExecuted**: Shares query results with active sessions
- **SecurityIssueDetected**: High-priority security alerts
- **PatternDetected**: ML-detected code patterns

### Integration Health

Check the status of integrated services:

```bash
GET /api/v1/integration/health
{
  "status": "healthy",
  "services": {
    "analysis_engine": {
      "url": "http://localhost:8001",
      "healthy": true
    },
    "query_intelligence": {
      "url": "http://localhost:8002",
      "healthy": false,
      "note": "Service not yet implemented"
    }
  }
}
```

## 🔌 WebSocket Protocol

### Message Types

```typescript
// Client -> Server
{
  "type": "join_session",
  "session_id": "uuid",
  "user_info": { ... }
}

{
  "type": "message_sent",
  "message": {
    "content": { "type": "text", "text": "Hello" },
    "metadata": { ... }
  }
}

{
  "type": "cursor_move",
  "session_id": "uuid",
  "position": { "x": 100, "y": 200 }
}

// Server -> Client
{
  "type": "session_update",
  "session_id": "uuid",
  "update": { ... }
}

{
  "type": "message_received",
  "message": { ... }
}

{
  "type": "presence_update",
  "presence": { ... }
}
```

## 🎯 Performance Targets

- **WebSocket Latency**: <50ms p99
- **Concurrent Users**: 50+ per session
- **Message Throughput**: 1000+ msg/sec
- **Memory Usage**: <100MB per 1000 connections
- **CPU Usage**: <50% at peak load

## 🔒 Security Features

- JWT authentication on all endpoints
- WebSocket authentication via upgrade request
- Rate limiting per user and connection
- Input validation and sanitization
- CORS configuration
- Security headers on all responses

## 📊 Monitoring

The service exposes Prometheus metrics on port 9003:

- `websocket_connections_total` - Total WebSocket connections
- `websocket_messages_total` - Total messages sent/received
- `session_active_count` - Number of active sessions
- `session_participant_count` - Participants per session
- `redis_pubsub_messages_total` - Redis pub/sub messages

## 🧪 Testing

```bash
# Unit tests
cargo test --lib

# Integration tests
cargo test --test '*'

# Performance benchmarks
cargo bench

# WebSocket load testing
npm install -g artillery
artillery run tests/websocket-load-test.yml
```

## 🚀 Deployment

### Cloud Run Deployment

```bash
# Build and push to GCR
gcloud builds submit --tag gcr.io/$PROJECT_ID/collaboration-engine

# Deploy to Cloud Run
gcloud run deploy collaboration-engine \
  --image gcr.io/$PROJECT_ID/collaboration-engine \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --set-env-vars-from-file .env.production
```

## 📝 Development Roadmap

### Wave 1: Foundation ✅
- [x] Service scaffolding
- [x] WebSocket server setup
- [x] Basic connection management
- [x] Redis pub/sub integration

### Wave 2: Core Features ✅
- [x] Team CRUD operations
- [x] Session management
- [x] Real-time messaging
- [x] Presence tracking

### Wave 3: Integration ✅
- [x] Analysis-engine integration
- [x] Query-intelligence integration (API ready, service not implemented)
- [x] Cross-service events
- [x] Pub/Sub event handling
- [x] Shared content storage

### Wave 4: Production Hardening
- [ ] Performance optimization
- [ ] Advanced monitoring
- [ ] Security enhancements
- [ ] Load testing

### Wave 5: Deployment
- [ ] Cloud Run configuration
- [ ] Production monitoring
- [ ] Documentation
- [ ] 95/100 readiness validation

## 🤝 Contributing

This service follows the Episteme development standards:
- Research-first development
- Evidence-based implementation
- Comprehensive testing
- Production-ready code

## 📄 License

Part of the Episteme platform - see main repository for license details.