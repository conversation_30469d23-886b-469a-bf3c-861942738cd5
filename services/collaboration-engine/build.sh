#!/bin/bash

# Build script for Collaboration Engine

set -e

echo "🚀 Building Collaboration Engine..."

# Check if cargo is installed
if ! command -v cargo &> /dev/null; then
    echo "❌ Cargo is not installed. Please install Rust from https://rustup.rs/"
    exit 1
fi

# Build the project
echo "📦 Building in release mode..."
cargo build --release

# Run tests
echo "🧪 Running tests..."
cargo test

# Check code quality
echo "🔍 Running clippy..."
cargo clippy -- -D warnings

# Format check
echo "🎨 Checking formatting..."
cargo fmt -- --check

echo "✅ Build completed successfully!"