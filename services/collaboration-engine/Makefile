.PHONY: help build test run docker-build docker-run clean fmt lint check setup

# Default target
help:
	@echo "Collaboration Engine - Development Commands"
	@echo ""
	@echo "Setup & Build:"
	@echo "  make setup        - Set up development environment"
	@echo "  make build        - Build the service in release mode"
	@echo "  make check        - Check compilation without building"
	@echo ""
	@echo "Development:"
	@echo "  make run          - Run the service locally"
	@echo "  make test         - Run all tests"
	@echo "  make fmt          - Format code"
	@echo "  make lint         - Run clippy linter"
	@echo ""
	@echo "Docker:"
	@echo "  make docker-build - Build Docker image"
	@echo "  make docker-run   - Run Docker container"
	@echo ""
	@echo "Utilities:"
	@echo "  make clean        - Clean build artifacts"

# Setup development environment
setup:
	@echo "Setting up development environment..."
	@cp -n .env.example .env || true
	@echo "✅ Environment file ready"
	@echo "⚠️  Please update .env with your configuration"

# Build the service
build:
	@echo "Building collaboration-engine..."
	cargo build --release

# Check compilation
check:
	@echo "Checking compilation..."
	cargo check --all-targets

# Run the service
run: setup
	@echo "Starting collaboration-engine..."
	RUST_LOG=collaboration_engine=debug,tower_http=debug cargo run

# Run tests
test:
	@echo "Running tests..."
	cargo test --all

# Format code
fmt:
	@echo "Formatting code..."
	cargo fmt

# Lint code
lint:
	@echo "Running clippy..."
	cargo clippy -- -D warnings

# Build Docker image
docker-build:
	@echo "Building Docker image..."
	docker build -t episteme/collaboration-engine:latest .

# Run Docker container
docker-run: docker-build
	@echo "Running Docker container..."
	docker run -p 8003:8003 -p 9003:9003 \
		--env-file .env \
		episteme/collaboration-engine:latest

# Clean build artifacts
clean:
	@echo "Cleaning build artifacts..."
	cargo clean
	rm -rf target/

# Development workflow targets
dev: fmt lint test
	@echo "✅ Code is formatted, linted, and tested"

# CI/CD simulation
ci: check fmt lint test
	@echo "✅ CI checks passed"