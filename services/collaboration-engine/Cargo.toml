[package]
name = "collaboration-engine"
version = "0.1.0"
edition = "2021"
authors = ["Episteme Team"]
description = "Real-time collaboration service for the Episteme platform"

[dependencies]
# Web framework
axum = { version = "0.7", features = ["ws", "macros"] }
axum-extra = { version = "0.9", features = ["typed-header"] }
tower = { version = "0.4", features = ["full"] }
tower-http = { version = "0.5", features = ["cors", "trace", "compression-br", "compression-gzip"] }

# Async runtime
tokio = { version = "1", features = ["full"] }
tokio-tungstenite = "0.21"

# WebSocket
futures-util = "0.3"
tungstenite = { version = "0.21", features = ["native-tls"] }

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
serde_with = "3.0"

# Database & Caching
redis = { version = "0.24", features = ["aio", "tokio-comp", "connection-manager"] }
google-cloud-spanner = "0.24"
google-cloud-googleapis = { version = "0.12", features = ["spanner"] }
google-cloud-gax = "0.17"
tonic = { version = "0.11", features = ["tls", "tls-roots", "prost"] }

# Authentication & Security
jsonwebtoken = "9.2"
sha2 = "0.10"
base64 = "0.21"
uuid = { version = "1.6", features = ["v4", "serde"] }

# Error handling
anyhow = "1.0"
thiserror = "1.0"

# HTTP client
reqwest = { version = "0.11", features = ["json", "rustls-tls"], default-features = false }

# Logging & Metrics
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter", "json", "registry"] }
tracing-opentelemetry = "0.22"
prometheus = "0.13"
metrics = "0.22"
metrics-exporter-prometheus = "0.13"

# OpenTelemetry
opentelemetry = { version = "0.21", features = ["trace", "metrics"] }
opentelemetry-otlp = { version = "0.14", features = ["tonic", "metrics", "trace"] }
opentelemetry_sdk = { version = "0.21", features = ["rt-tokio", "trace"] }

# Environment & Config
dotenvy = "0.15"
config = "0.13"

# Time
chrono = { version = "0.4", features = ["serde"] }

# Utilities
dashmap = "5.5"
parking_lot = "0.12"
once_cell = "1.19"
futures = "0.3"
pin-project = "1.1"
lazy_static = "1.4"

# Compression
flate2 = "1.0"
zstd = "0.13"

# Performance
num_cpus = "1.16"

# System info
sys-info = "0.9"

# System calls and low-level operations
libc = "0.2"

[dev-dependencies]
mockall = "0.12"
criterion = "0.5"
proptest = "1.4"
tokio-test = "0.4"
tower = { version = "0.4", features = ["util"] }
hyper = { version = "1.0", features = ["full"] }
rand = "0.8"
http = "1.0"

[[bin]]
name = "collaboration-engine"
path = "src/main.rs"

[profile.release]
opt-level = 3
lto = true
codegen-units = 1
strip = true

[profile.dev]
opt-level = 0

[profile.test]
opt-level = 0