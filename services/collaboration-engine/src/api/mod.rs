pub mod handlers;
pub mod middleware;

use crate::config::Config;
use crate::storage::StorageClients;
use crate::websocket::hub::WebSocketHub;
use crate::api::middleware::auth::JwtService;
use axum::{
    routing::{get, post, put, delete},
    Router,
};
use std::sync::Arc;
use tower::ServiceBuilder;

// Application state shared across handlers
#[derive(Clone)]
pub struct AppState {
    pub config: Config,
    pub storage: Arc<StorageClients>,
    pub ws_hub: Arc<WebSocketHub>,
    pub jwt_service: Arc<JwtService>,
}

impl AppState {
    pub fn new(config: Config, storage: StorageClients) -> Arc<Self> {
        let ws_hub = WebSocketHub::new(config.clone(), storage.redis.clone());
        let jwt_service = JwtService::new(
            config.jwt_secret.as_bytes(),
            storage.redis.clone(),
        );
        
        Arc::new(Self {
            config,
            storage: Arc::new(storage),
            ws_hub: Arc::new(ws_hub),
            jwt_service: Arc::new(jwt_service),
        })
    }
}

pub fn create_api_router(state: Arc<AppState>) -> Router {
    // Public routes (no auth required)
    let public_routes = Router::new()
        .route("/auth/login", post(handlers::auth::login_handler))
        .route("/auth/refresh", post(handlers::auth::refresh_token_handler))
        .route("/auth/service-token", post(handlers::auth::service_token_handler))
        .route("/health", get(handlers::health::health_check))
        .route("/health/ready", get(handlers::health::readiness_check))
        .route("/health/live", get(handlers::health::liveness_check));
    
    // Protected routes (auth required)
    let protected_routes = Router::new()
        // Auth endpoints
        .route("/auth/logout", post(handlers::auth::logout_handler))
        
        // WebSocket endpoint
        .route("/ws", get(handlers::websocket::websocket_handler))
        
        // Team endpoints
        .route("/teams", post(handlers::teams::create_team))
        .route("/teams", get(handlers::teams::list_teams))
        .route("/teams/:team_id", get(handlers::teams::get_team))
        .route("/teams/:team_id", put(handlers::teams::update_team))
        .route("/teams/:team_id", delete(handlers::teams::delete_team))
        .route("/teams/:team_id/members", post(handlers::teams::add_member))
        .route("/teams/:team_id/members/:user_id", delete(handlers::teams::remove_member))
        .route("/teams/:team_id/members/:user_id/role", put(handlers::teams::update_member_role))
        
        // Session endpoints
        .route("/sessions", post(handlers::sessions::create_session))
        .route("/sessions/team/:team_id", get(handlers::sessions::list_team_sessions))
        .route("/sessions/:session_id", get(handlers::sessions::get_session))
        .route("/sessions/:session_id", put(handlers::sessions::update_session))
        .route("/sessions/:session_id/end", post(handlers::sessions::end_session))
        .route("/sessions/:session_id/messages", get(handlers::sessions::get_messages))
        .route("/sessions/:session_id/messages", post(handlers::sessions::send_message))
        
        // Integration endpoints
        .route("/sessions/:session_id/share/analysis", post(handlers::integration::share_analysis))
        .route("/sessions/:session_id/share/query", post(handlers::integration::share_query))
        .route("/sessions/:session_id/shared/analyses", get(handlers::integration::get_shared_analyses))
        .route("/sessions/:session_id/shared/queries", get(handlers::integration::get_shared_queries))
        .route("/integration/health", get(handlers::integration::integration_health))
        
        // Apply auth middleware to protected routes
        .route_layer(axum::middleware::from_fn(middleware::auth::require_auth));
    
    // Combine public and protected routes
    Router::new()
        .merge(public_routes)
        .merge(protected_routes)
        // Apply global middleware
        .layer(
            ServiceBuilder::new()
                .layer(axum::Extension(state.jwt_service.clone()))
                .layer(middleware::rate_limit::RateLimitLayer::new(
                    state.storage.redis.clone(),
                    state.config.rate_limit_requests_per_minute,
                ))
                .layer(middleware::security::SecurityLayer::new()),
        )
        .with_state(state)
}