use axum::{
    extract::{Path, State},
    http::StatusCode,
    response::IntoResponse,
    Extension, Json,
};
use serde_json::json;
use std::sync::Arc;
use tracing::{error, info};

use crate::{
    api::{handlers::websocket::AuthUser, AppState},
    models::{
        CreateTeamRequest, UpdateTeamRequest, AddTeamMemberRequest, UpdateMemberRoleRequest,
        TeamResponse, TeamMemberResponse, UserId, UserInfo, TeamRole,
    },
};

pub async fn create_team(
    State(state): State<Arc<AppState>>,
    Extension(user): Extension<AuthUser>,
    Json(request): Json<CreateTeamRequest>,
) -> impl IntoResponse {
    let user_id = UserId::from_string(user.user_id.clone());
    let user_info = UserInfo {
        user_id: user_id.clone(),
        email: user.email.clone(),
        name: user.name.clone(),
        avatar_url: None,
    };
    
    match state.storage.teams.create_team(request, &user_id, user_info).await {
        Ok(team) => {
            info!("Team created successfully: {}", team.id);
            
            let response = TeamResponse {
                id: team.id.clone(),
                name: team.name,
                description: team.description,
                member_count: team.members.len(),
                created_at: team.created_at,
                updated_at: team.updated_at,
                user_role: TeamRole::Owner,
                settings: team.settings,
            };
            
            (StatusCode::CREATED, Json(json!({ "team": response })))
        }
        Err(e) => {
            error!("Failed to create team: {}", e);
            (StatusCode::INTERNAL_SERVER_ERROR, Json(json!({ "error": format!("Failed to create team: {}", e) })))
        }
    }
}

pub async fn list_teams(
    State(state): State<Arc<AppState>>,
    Extension(user): Extension<AuthUser>,
) -> impl IntoResponse {
    let user_id = UserId::from_string(user.user_id);
    
    match state.storage.teams.list_user_teams(&user_id).await {
        Ok(teams) => {
            let response: Vec<TeamResponse> = teams.into_iter().map(|team| {
                let user_member = team.members.get(user_id.as_ref()).cloned();
                let user_role = user_member.map(|m| m.role).unwrap_or(TeamRole::Member);
                
                TeamResponse {
                    id: team.id,
                    name: team.name,
                    description: team.description,
                    member_count: team.members.len(),
                    created_at: team.created_at,
                    updated_at: team.updated_at,
                    user_role,
                    settings: team.settings,
                }
            }).collect();
            
            (StatusCode::OK, Json(json!({ "teams": response })))
        }
        Err(e) => {
            error!("Failed to list teams: {}", e);
            (StatusCode::INTERNAL_SERVER_ERROR, Json(json!({ "error": format!("Failed to list teams: {}", e) })))
        }
    }
}

pub async fn get_team(
    State(state): State<Arc<AppState>>,
    Extension(user): Extension<AuthUser>,
    Path(team_id): Path<String>,
) -> impl IntoResponse {
    let user_id = UserId::from_string(user.user_id);
    
    match state.storage.teams.get_team(&team_id, &user_id).await {
        Ok(Some(team)) => {
            let user_member = team.members.get(user_id.as_ref()).cloned();
            let user_role = user_member.map(|m| m.role).unwrap_or(TeamRole::Member);
            
            let response = TeamResponse {
                id: team.id.clone(),
                name: team.name.clone(),
                description: team.description.clone(),
                member_count: team.members.len(),
                created_at: team.created_at.clone(),
                updated_at: team.updated_at.clone(),
                user_role,
                settings: team.settings.clone(),
            };
            
            let members: Vec<TeamMemberResponse> = team.members.values().map(|member| {
                TeamMemberResponse {
                    user_info: UserInfo {
                        user_id: member.user_id.clone(),
                        email: member.email.clone(),
                        name: member.name.clone(),
                        avatar_url: None,
                    },
                    role: member.role,
                    joined_at: member.joined_at.clone(),
                    last_active: member.last_active.clone(),
                }
            }).collect();
            
            (StatusCode::OK, Json(json!({ 
                "team": response,
                "members": members 
            })))
        }
        Ok(None) => {
            (StatusCode::NOT_FOUND, Json(json!({ "error": "Team not found or access denied" })))
        }
        Err(e) => {
            error!("Failed to get team: {}", e);
            (StatusCode::INTERNAL_SERVER_ERROR, Json(json!({ "error": format!("Failed to get team: {}", e) })))
        }
    }
}

pub async fn update_team(
    State(state): State<Arc<AppState>>,
    Extension(user): Extension<AuthUser>,
    Path(team_id): Path<String>,
    Json(request): Json<UpdateTeamRequest>,
) -> impl IntoResponse {
    let user_id = UserId::from_string(user.user_id);
    
    match state.storage.teams.update_team(&team_id, &user_id, request).await {
        Ok(_) => {
            info!("Team {} updated successfully", team_id);
            (StatusCode::OK, Json(json!({ "message": "Team updated successfully" })))
        }
        Err(e) => {
            error!("Failed to update team: {}", e);
            if e.to_string().contains("Insufficient permissions") {
                (StatusCode::FORBIDDEN, Json(json!({ "error": e.to_string() })))
            } else {
                (StatusCode::INTERNAL_SERVER_ERROR, Json(json!({ "error": format!("Failed to update team: {}", e) })))
            }
        }
    }
}

pub async fn delete_team(
    State(state): State<Arc<AppState>>,
    Extension(user): Extension<AuthUser>,
    Path(team_id): Path<String>,
) -> impl IntoResponse {
    let user_id = UserId::from_string(user.user_id);
    
    match state.storage.teams.delete_team(&team_id, &user_id).await {
        Ok(_) => {
            info!("Team {} deleted successfully", team_id);
            (StatusCode::NO_CONTENT, Json(json!({})))
        }
        Err(e) => {
            error!("Failed to delete team: {}", e);
            if e.to_string().contains("Only team owners") {
                (StatusCode::FORBIDDEN, Json(json!({ "error": e.to_string() })))
            } else {
                (StatusCode::INTERNAL_SERVER_ERROR, Json(json!({ "error": format!("Failed to delete team: {}", e) })))
            }
        }
    }
}

pub async fn add_member(
    State(state): State<Arc<AppState>>,
    Extension(user): Extension<AuthUser>,
    Path(team_id): Path<String>,
    Json(request): Json<AddTeamMemberRequest>,
) -> impl IntoResponse {
    let requester_id = UserId::from_string(user.user_id);
    
    // In a real implementation, you would look up the user by email
    // For now, we'll create a placeholder UserInfo
    let new_member_info = UserInfo {
        user_id: UserId::new(), // This should be looked up
        email: request.email.clone(),
        name: request.email.split('@').next().unwrap_or("User").to_string(),
        avatar_url: None,
    };
    
    match state.storage.teams.add_team_member(&team_id, &requester_id, new_member_info, request.role).await {
        Ok(member) => {
            info!("Member added to team {} successfully", team_id);
            
            let response = TeamMemberResponse {
                user_info: UserInfo {
                    user_id: member.user_id.clone(),
                    email: member.email,
                    name: member.name,
                    avatar_url: None,
                },
                role: member.role,
                joined_at: member.joined_at,
                last_active: member.last_active,
            };
            
            (StatusCode::CREATED, Json(json!({ "member": response })))
        }
        Err(e) => {
            error!("Failed to add member: {}", e);
            if e.to_string().contains("Insufficient permissions") {
                (StatusCode::FORBIDDEN, Json(json!({ "error": e.to_string() })))
            } else {
                (StatusCode::INTERNAL_SERVER_ERROR, Json(json!({ "error": format!("Failed to add member: {}", e) })))
            }
        }
    }
}

pub async fn remove_member(
    State(state): State<Arc<AppState>>,
    Extension(user): Extension<AuthUser>,
    Path((team_id, user_id)): Path<(String, String)>,
) -> impl IntoResponse {
    let requester_id = UserId::from_string(user.user_id);
    let member_id = UserId::from_string(user_id);
    
    match state.storage.teams.remove_team_member(&team_id, &requester_id, &member_id).await {
        Ok(_) => {
            info!("Member removed from team {} successfully", team_id);
            (StatusCode::NO_CONTENT, Json(json!({})))
        }
        Err(e) => {
            error!("Failed to remove member: {}", e);
            if e.to_string().contains("Insufficient permissions") || e.to_string().contains("Cannot remove team owner") {
                (StatusCode::FORBIDDEN, Json(json!({ "error": e.to_string() })))
            } else {
                (StatusCode::INTERNAL_SERVER_ERROR, Json(json!({ "error": format!("Failed to remove member: {}", e) })))
            }
        }
    }
}

pub async fn update_member_role(
    State(state): State<Arc<AppState>>,
    Extension(user): Extension<AuthUser>,
    Path((team_id, user_id)): Path<(String, String)>,
    Json(request): Json<UpdateMemberRoleRequest>,
) -> impl IntoResponse {
    let requester_id = UserId::from_string(user.user_id);
    let member_id = UserId::from_string(request.user_id);
    
    match state.storage.teams.update_member_role(&team_id, &requester_id, &member_id, request.role).await {
        Ok(_) => {
            info!("Member role updated in team {} successfully", team_id);
            (StatusCode::OK, Json(json!({ "message": "Member role updated successfully" })))
        }
        Err(e) => {
            error!("Failed to update member role: {}", e);
            if e.to_string().contains("Insufficient permissions") || e.to_string().contains("Cannot change team owner") {
                (StatusCode::FORBIDDEN, Json(json!({ "error": e.to_string() })))
            } else {
                (StatusCode::INTERNAL_SERVER_ERROR, Json(json!({ "error": format!("Failed to update member role: {}", e) })))
            }
        }
    }
}