use axum::{
    extract::{Query, State},
    http::{HeaderMap, StatusCode},
    response::IntoResponse,
    Extension, Json,
};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tracing::{debug, error, info, instrument};

use crate::{
    api::{middleware::auth::{Claims, JwtService, TokenPair, TokenType}, AppState},
    errors::ApiError,
    metrics,
};

#[derive(Debug, Deserialize)]
pub struct LoginRequest {
    pub email: String,
    pub password: String,
}

#[derive(Debug, Serialize)]
pub struct LoginResponse {
    pub access_token: String,
    pub refresh_token: String,
    pub expires_in: u64,
    pub user: UserInfo,
}

#[derive(Debug, Serialize)]
pub struct UserInfo {
    pub user_id: String,
    pub email: String,
    pub name: String,
    pub roles: Vec<String>,
}

#[derive(Debug, Deserialize)]
pub struct RefreshRequest {
    pub refresh_token: String,
}

#[derive(Debug, Serialize)]
pub struct RefreshResponse {
    pub access_token: String,
    pub expires_in: u64,
}

// Login endpoint
#[instrument(skip(state, request), fields(email = %request.email))]
pub async fn login_handler(
    State(state): State<Arc<AppState>>,
    Json(request): Json<LoginRequest>,
) -> Result<impl IntoResponse, ApiError> {
    info!("Login attempt for user: {}", request.email);
    
    // TODO: Implement actual user authentication with database
    // For now, we'll use a mock authentication
    match authenticate_user(&request.email, &request.password).await {
        Ok(user) => {
            // Create token pair
            let token_pair = state.jwt_service.create_token_pair(
                user.user_id.clone(),
                user.email.clone(),
                user.name.clone(),
                user.roles.clone(),
            )?;
            
            // Record login success metrics
            metrics::record_auth_attempt("password", true);
            metrics::record_jwt_operation("create", true);
            
            Ok(Json(LoginResponse {
                access_token: token_pair.access_token,
                refresh_token: token_pair.refresh_token,
                expires_in: token_pair.expires_in,
                user,
            }))
        }
        Err(e) => {
            // Record login failure metrics
            metrics::record_auth_attempt("password", false);
            Err(e)
        }
    }
}

// Logout endpoint
#[instrument(skip(state, headers))]
pub async fn logout_handler(
    State(state): State<Arc<AppState>>,
    headers: HeaderMap,
) -> Result<impl IntoResponse, ApiError> {
    // Extract token from Authorization header
    let token = extract_token_from_headers(&headers)
        .ok_or(ApiError::BadRequest("Missing authorization token".to_string()))?;
    
    // Revoke the token
    state.jwt_service.revoke_token(&token).await?;
    
    // Also try to extract and revoke refresh token from body if provided
    // This allows clients to revoke both tokens in one call
    
    metrics::record_jwt_operation("revoke", true);
    
    Ok(Json(serde_json::json!({
        "message": "Successfully logged out"
    })))
}

// Refresh token endpoint
#[instrument(skip(state, request))]
pub async fn refresh_token_handler(
    State(state): State<Arc<AppState>>,
    Json(request): Json<RefreshRequest>,
) -> Result<impl IntoResponse, ApiError> {
    debug!("Token refresh requested");
    
    // Generate new access token from refresh token
    match state.jwt_service.refresh_access_token(&request.refresh_token).await {
        Ok(access_token) => {
            metrics::record_jwt_operation("refresh", true);
            
            Ok(Json(RefreshResponse {
                access_token,
                expires_in: 900, // 15 minutes
            }))
        }
        Err(e) => {
            metrics::record_jwt_operation("refresh", false);
            Err(e.into())
        }
    }
}

// Service token endpoint for service-to-service auth
pub async fn service_token_handler(
    State(state): State<Arc<AppState>>,
    headers: HeaderMap,
) -> Result<impl IntoResponse, ApiError> {
    // Verify service credentials from headers
    let service_id = headers
        .get("X-Service-ID")
        .and_then(|v| v.to_str().ok())
        .ok_or(ApiError::BadRequest("Missing service ID".to_string()))?;
    
    let service_secret = headers
        .get("X-Service-Secret")
        .and_then(|v| v.to_str().ok())
        .ok_or(ApiError::BadRequest("Missing service secret".to_string()))?;
    
    // Validate service credentials
    if !validate_service_credentials(service_id, service_secret).await? {
        return Err(ApiError::Unauthorized("Invalid service credentials".to_string()));
    }
    
    // Create service token
    let claims = Claims::new(
        service_id.to_string(),
        format!("{}@service.episteme", service_id),
        format!("{} Service", service_id),
        vec!["service".to_string()],
        TokenType::Service,
    );
    
    let token = state.jwt_service.create_token(&claims)?;
    
    metrics::record_jwt_operation("service_token", true);
    
    Ok(Json(serde_json::json!({
        "token": token,
        "expires_in": 3600, // 1 hour
        "token_type": "Bearer"
    })))
}

// Helper function to extract token from headers
fn extract_token_from_headers(headers: &HeaderMap) -> Option<String> {
    headers
        .get("Authorization")
        .and_then(|value| value.to_str().ok())
        .and_then(|auth_header| {
            if auth_header.starts_with("Bearer ") {
                Some(auth_header[7..].to_string())
            } else {
                None
            }
        })
}

// Mock user authentication (replace with actual implementation)
async fn authenticate_user(email: &str, password: &str) -> Result<UserInfo, ApiError> {
    // TODO: Implement actual authentication logic
    // This should:
    // 1. Look up user by email in database
    // 2. Verify password using secure hashing (e.g., Argon2)
    // 3. Check if account is active/verified
    // 4. Return user information
    
    // For now, mock authentication
    if email == "<EMAIL>" && password == "password123" {
        Ok(UserInfo {
            user_id: uuid::Uuid::new_v4().to_string(),
            email: email.to_string(),
            name: "Test User".to_string(),
            roles: vec!["user".to_string()],
        })
    } else if email == "<EMAIL>" && password == "admin123" {
        Ok(UserInfo {
            user_id: uuid::Uuid::new_v4().to_string(),
            email: email.to_string(),
            name: "Admin User".to_string(),
            roles: vec!["user".to_string(), "admin".to_string()],
        })
    } else {
        metrics::record_auth_attempt("password", false);
        Err(ApiError::Unauthorized("Invalid credentials".to_string()))
    }
}

// Validate service credentials (replace with actual implementation)
async fn validate_service_credentials(service_id: &str, service_secret: &str) -> Result<bool, ApiError> {
    // TODO: Implement actual service credential validation
    // This should check against configured service credentials
    // possibly stored in environment variables or a secure store
    
    // For now, mock validation
    match service_id {
        "analysis-engine" => Ok(service_secret == "analysis-secret-key"),
        "query-intelligence" => Ok(service_secret == "query-secret-key"),
        _ => Ok(false),
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    async fn test_extract_token_from_headers() {
        let mut headers = HeaderMap::new();
        headers.insert("Authorization", "Bearer test-token".parse().unwrap());
        
        let token = extract_token_from_headers(&headers);
        assert_eq!(token, Some("test-token".to_string()));
    }
    
    #[tokio::test]
    async fn test_authenticate_user_success() {
        let user = authenticate_user("<EMAIL>", "password123").await.unwrap();
        assert_eq!(user.email, "<EMAIL>");
        assert_eq!(user.roles, vec!["user"]);
    }
    
    #[tokio::test]
    async fn test_authenticate_user_failure() {
        let result = authenticate_user("<EMAIL>", "wrong-password").await;
        assert!(result.is_err());
    }
}