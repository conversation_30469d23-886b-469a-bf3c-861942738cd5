use axum::{
    extract::{Extension, Path, State},
    http::StatusC<PERSON>,
    response::IntoResponse,
    Json,
};
use serde_json::json;
use std::sync::Arc;
use tracing::{debug, error, info};

use crate::api::{AppState, handlers::websocket::AuthUser};
use crate::errors::ApiError;
use crate::models::{
    ShareAnalysisRequest, ShareQueryRequest, SharedAnalysis, SharedQuery,
    IntegrationConfig, QueryType, QueryResult, MatchType,
};
use crate::services::analysis_integration::AnalysisIntegrationService;
use crate::storage::integration_storage::IntegrationStorage;

// POST /api/v1/sessions/:id/share/analysis - Share analysis results in a session
pub async fn share_analysis(
    State(state): State<Arc<AppState>>,
    Extension(user): Extension<AuthUser>,
    Path(session_id): Path<String>,
    Json(request): Json<ShareAnalysisRequest>,
) -> impl IntoResponse {
    debug!(
        "Sharing analysis {} in session {} by user {}",
        request.analysis_id, session_id, user.user_id
    );
    
    // Verify user is a participant in the session
    match state.storage.sessions.get_session(&session_id).await {
        Ok(Some(session)) => {
            if !session.participants.contains(&user.user_id) {
                return Err(ApiError::Forbidden(
                    "You are not a participant in this session".to_string()
                ));
            }
        }
        Ok(None) => {
            return Err(ApiError::NotFound("Session not found".to_string()));
        }
        Err(e) => {
            error!("Failed to get session: {}", e);
            return Err(ApiError::DatabaseError(e.to_string()));
        }
    }
    
    // Create integration service
    let integration_config = IntegrationConfig {
        analysis_engine_url: state.config.analysis_engine_url.clone(),
        query_intelligence_url: state.config.query_intelligence_url.clone(),
        service_timeout_secs: state.config.service_timeout_secs,
        max_retries: 3,
        cache_ttl_secs: state.config.integration_cache_ttl_secs,
    };
    let integration_service = AnalysisIntegrationService::new(
        integration_config,
        state.storage.redis.clone(),
    );
    
    // Get analysis from Analysis Engine
    let analysis = match integration_service.get_analysis(&request.analysis_id).await {
        Ok(analysis) => analysis,
        Err(e) => {
            error!("Failed to get analysis from Analysis Engine: {}", e);
            return Err(ApiError::ServiceUnavailable(
                "Analysis Engine unavailable".to_string()
            ));
        }
    };
    
    // Create highlights if requested
    let highlights = if let Some(highlight_requests) = request.highlights {
        match integration_service.create_highlights(
            &request.analysis_id,
            highlight_requests,
            user.user_id.clone(),
        ).await {
            Ok(highlights) => highlights,
            Err(e) => {
                error!("Failed to create highlights: {}", e);
                Vec::new()
            }
        }
    } else {
        Vec::new()
    };
    
    // Convert to shared analysis
    let shared_analysis = integration_service.convert_to_shared_analysis(
        analysis,
        session_id.clone(),
        user.user_id.clone(),
        highlights,
    );
    
    // Store shared analysis
    let integration_storage = IntegrationStorage::new(state.storage.spanner.client.clone());
    match integration_storage.store_shared_analysis(&shared_analysis).await {
        Ok(_) => {
            info!(
                "Analysis {} shared in session {} by user {}",
                request.analysis_id, session_id, user.user_id
            );
            
            // Broadcast to session participants
            let ws_message = crate::models::WebSocketMessage::AnalysisShared {
                session_id: session_id.clone(),
                analysis_id: shared_analysis.analysis_id.clone(),
                shared_by: user.user_id.clone(),
                analysis_type: "repository".to_string(),
                summary: format!(
                    "{} - {} files, {} lines",
                    shared_analysis.title,
                    shared_analysis.summary.total_files,
                    shared_analysis.summary.total_lines
                ),
            };
            
            let _ = state.ws_hub.broadcast_to_session(&session_id, ws_message).await;
            
            Ok((StatusCode::OK, Json(shared_analysis)))
        }
        Err(e) => {
            error!("Failed to store shared analysis: {}", e);
            Err(ApiError::DatabaseError(e.to_string()))
        }
    }
}

// POST /api/v1/sessions/:id/share/query - Share query results in a session
pub async fn share_query(
    State(state): State<Arc<AppState>>,
    Extension(user): Extension<AuthUser>,
    Path(session_id): Path<String>,
    Json(request): Json<ShareQueryRequest>,
) -> impl IntoResponse {
    debug!(
        "Sharing query '{}' in session {} by user {}",
        request.query_text, session_id, user.user_id
    );
    
    // Verify user is a participant in the session
    match state.storage.sessions.get_session(&session_id).await {
        Ok(Some(session)) => {
            if !session.participants.contains(&user.user_id) {
                return Err(ApiError::Forbidden(
                    "You are not a participant in this session".to_string()
                ));
            }
        }
        Ok(None) => {
            return Err(ApiError::NotFound("Session not found".to_string()));
        }
        Err(e) => {
            error!("Failed to get session: {}", e);
            return Err(ApiError::DatabaseError(e.to_string()));
        }
    }
    
    // Since Query Intelligence is not implemented, create mock results
    let mock_results = vec![
        QueryResult {
            id: uuid::Uuid::new_v4().to_string(),
            file_path: "src/main.rs".to_string(),
            match_type: MatchType::ExactMatch,
            relevance_score: 0.95,
            preview: "Mock result for query demonstration".to_string(),
            metadata: json!({"line_number": 42}),
        },
    ];
    
    // Create shared query
    let shared_query = SharedQuery::new(
        session_id.clone(),
        request.query_text.clone(),
        request.query_type,
        user.user_id.clone(),
        mock_results,
        0.85, // Mock confidence score
    );
    
    // Store shared query
    let integration_storage = IntegrationStorage::new(state.storage.spanner.client.clone());
    match integration_storage.store_shared_query(&shared_query).await {
        Ok(_) => {
            info!(
                "Query shared in session {} by user {}",
                session_id, user.user_id
            );
            
            // Broadcast to session participants
            let ws_message = crate::models::WebSocketMessage::AnalysisShared {
                session_id: session_id.clone(),
                analysis_id: shared_query.query_id.clone(),
                shared_by: user.user_id.clone(),
                analysis_type: "query".to_string(),
                summary: format!(
                    "Query: {} ({} results)",
                    shared_query.query_text,
                    shared_query.results.len()
                ),
            };
            
            let _ = state.ws_hub.broadcast_to_session(&session_id, ws_message).await;
            
            Ok((StatusCode::OK, Json(shared_query)))
        }
        Err(e) => {
            error!("Failed to store shared query: {}", e);
            Err(ApiError::DatabaseError(e.to_string()))
        }
    }
}

// GET /api/v1/sessions/:id/shared/analyses - Get shared analyses for a session
pub async fn get_shared_analyses(
    State(state): State<Arc<AppState>>,
    Extension(user): Extension<AuthUser>,
    Path(session_id): Path<String>,
) -> impl IntoResponse {
    // Verify user is a participant in the session
    match state.storage.sessions.get_session(&session_id).await {
        Ok(Some(session)) => {
            if !session.participants.contains(&user.user_id) {
                return Err(ApiError::Forbidden(
                    "You are not a participant in this session".to_string()
                ));
            }
        }
        Ok(None) => {
            return Err(ApiError::NotFound("Session not found".to_string()));
        }
        Err(e) => {
            error!("Failed to get session: {}", e);
            return Err(ApiError::DatabaseError(e.to_string()));
        }
    }
    
    // Get shared analyses
    let integration_storage = IntegrationStorage::new(state.storage.spanner.client.clone());
    match integration_storage.get_session_shared_analyses(&session_id).await {
        Ok(analyses) => {
            Ok((StatusCode::OK, Json(json!({
                "session_id": session_id,
                "shared_analyses": analyses,
                "count": analyses.len(),
            }))))
        }
        Err(e) => {
            error!("Failed to get shared analyses: {}", e);
            Err(ApiError::DatabaseError(e.to_string()))
        }
    }
}

// GET /api/v1/sessions/:id/shared/queries - Get shared queries for a session
pub async fn get_shared_queries(
    State(state): State<Arc<AppState>>,
    Extension(user): Extension<AuthUser>,
    Path(session_id): Path<String>,
) -> impl IntoResponse {
    // Verify user is a participant in the session
    match state.storage.sessions.get_session(&session_id).await {
        Ok(Some(session)) => {
            if !session.participants.contains(&user.user_id) {
                return Err(ApiError::Forbidden(
                    "You are not a participant in this session".to_string()
                ));
            }
        }
        Ok(None) => {
            return Err(ApiError::NotFound("Session not found".to_string()));
        }
        Err(e) => {
            error!("Failed to get session: {}", e);
            return Err(ApiError::DatabaseError(e.to_string()));
        }
    }
    
    // Get shared queries
    let integration_storage = IntegrationStorage::new(state.storage.spanner.client.clone());
    match integration_storage.get_session_shared_queries(&session_id).await {
        Ok(queries) => {
            Ok((StatusCode::OK, Json(json!({
                "session_id": session_id,
                "shared_queries": queries,
                "count": queries.len(),
            }))))
        }
        Err(e) => {
            error!("Failed to get shared queries: {}", e);
            Err(ApiError::DatabaseError(e.to_string()))
        }
    }
}

// GET /api/v1/integration/health - Check integration services health
pub async fn integration_health(
    State(state): State<Arc<AppState>>,
) -> impl IntoResponse {
    let integration_config = IntegrationConfig {
        analysis_engine_url: state.config.analysis_engine_url.clone(),
        query_intelligence_url: state.config.query_intelligence_url.clone(),
        service_timeout_secs: state.config.service_timeout_secs,
        max_retries: 3,
        cache_ttl_secs: state.config.integration_cache_ttl_secs,
    };
    let integration_service = AnalysisIntegrationService::new(
        integration_config,
        state.storage.redis.clone(),
    );
    
    let analysis_engine_healthy = integration_service.health_check().await.unwrap_or(false);
    let query_intelligence_healthy = false; // Not implemented yet
    
    let status = if analysis_engine_healthy {
        StatusCode::OK
    } else {
        StatusCode::SERVICE_UNAVAILABLE
    };
    
    (status, Json(json!({
        "status": if analysis_engine_healthy { "healthy" } else { "degraded" },
        "services": {
            "analysis_engine": {
                "url": state.config.analysis_engine_url,
                "healthy": analysis_engine_healthy,
            },
            "query_intelligence": {
                "url": state.config.query_intelligence_url,
                "healthy": query_intelligence_healthy,
                "note": "Service not yet implemented",
            },
        },
        "timestamp": chrono::Utc::now(),
    })))
}