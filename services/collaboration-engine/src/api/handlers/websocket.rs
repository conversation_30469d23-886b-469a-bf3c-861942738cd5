use axum::{
    extract::{
        ws::{Message, WebSocket, WebSocketUpgrade},
        State,
    },
    response::IntoResponse,
    Extension,
};
use futures_util::{sink::SinkExt, stream::StreamExt};
use std::sync::Arc;
use tokio::sync::mpsc;
use tracing::{debug, error, info, instrument, warn};
use uuid::Uuid;

use crate::{
    api::AppState,
    metrics,
    models::{UserId, UserInfo, WebSocketMessage},
    websocket::{connection::Connection, messages::MessageHandler},
};

// JWT claims structure (should match analysis-engine)
#[derive(Debug, Clone)]
pub struct AuthUser {
    pub user_id: String,
    pub email: String,
    pub name: String,
    pub roles: Vec<String>,
}

pub async fn websocket_handler(
    ws: WebSocketUpgrade,
    State(state): State<Arc<AppState>>,
    Extension(user): Extension<AuthUser>,
) -> impl IntoResponse {
    info!("WebSocket upgrade request from user: {}", user.email);
    
    ws.on_upgrade(move |socket| handle_socket(socket, state, user))
}

#[instrument(skip(socket, state), fields(user_email = %user.email, user_id = %user.user_id))]
async fn handle_socket(socket: WebSocket, state: Arc<AppState>, user: AuthUser) {
    let connection_id = Uuid::new_v4().to_string();
    let user_id = UserId::from_string(user.user_id.clone());
    
    info!(
        "WebSocket connection established - ID: {}, User: {}",
        connection_id, user.email
    );
    
    // Record connection metrics
    metrics::record_websocket_connection(true);
    
    // Check connection limit per user
    let current_connections = state.ws_hub.get_user_connection_count(&user_id).await;
    if current_connections >= state.config.ws_max_connections_per_user {
        warn!(
            "User {} exceeded connection limit ({}/{})",
            user.email, current_connections, state.config.ws_max_connections_per_user
        );
        
        if let Err(e) = send_error_and_close(
            socket,
            "CONNECTION_LIMIT_EXCEEDED",
            "Maximum concurrent connections exceeded",
        )
        .await
        {
            error!("Failed to send connection limit error: {}", e);
        }
        return;
    }
    
    // Create user info
    let user_info = UserInfo {
        user_id: user_id.clone(),
        email: user.email.clone(),
        name: user.name.clone(),
        avatar_url: None, // Could be fetched from a user service
    };
    
    // Split the WebSocket into sender and receiver
    let (ws_sender, ws_receiver) = socket.split();
    
    // Create channels for internal communication
    let (tx, rx) = mpsc::channel::<WebSocketMessage>(100);
    
    // Create connection object with performance components
    let connection = Connection::new(
        connection_id.clone(),
        user_info.clone(),
        tx.clone(),
        state.clone(),
        state.ws_hub.get_message_batcher(),
        state.ws_hub.get_performance_metrics(),
    );
    
    // Register connection with the hub
    state
        .ws_hub
        .register_connection(connection_id.clone(), connection.clone())
        .await;
    
    // Spawn task to handle outgoing messages
    let outgoing_task = tokio::spawn(handle_outgoing_messages(ws_sender, rx));
    
    // Spawn task to handle incoming messages
    let incoming_task = tokio::spawn(handle_incoming_messages(
        ws_receiver,
        connection,
        state.clone(),
    ));
    
    // Send connected message
    let connected_msg = WebSocketMessage::Connected {
        connection_id: connection_id.clone(),
        user_info,
    };
    
    if let Err(e) = tx.send(connected_msg).await {
        error!("Failed to send connected message: {}", e);
    }
    
    // Wait for either task to complete
    tokio::select! {
        _ = outgoing_task => {
            debug!("Outgoing message handler completed for connection {}", connection_id);
        }
        _ = incoming_task => {
            debug!("Incoming message handler completed for connection {}", connection_id);
        }
    }
    
    // Cleanup: unregister connection
    state.ws_hub.unregister_connection(&connection_id).await;
    
    // Record disconnection metrics
    metrics::record_websocket_connection(false);
    
    info!("WebSocket connection closed - ID: {}", connection_id);
}

async fn handle_outgoing_messages(
    mut ws_sender: futures_util::stream::SplitSink<WebSocket, Message>,
    mut rx: mpsc::Receiver<WebSocketMessage>,
) {
    // Handle both single messages and batched messages
    
    while let Some(msg) = rx.recv().await {
        match serde_json::to_string(&msg) {
            Ok(json) => {
                if let Err(e) = ws_sender.send(Message::Text(json)).await {
                    error!("Failed to send WebSocket message: {}", e);
                    break;
                }
            }
            Err(e) => {
                error!("Failed to serialize WebSocket message: {}", e);
            }
        }
    }
}

async fn handle_incoming_messages(
    mut ws_receiver: futures_util::stream::SplitStream<WebSocket>,
    connection: Connection,
    state: Arc<AppState>,
) {
    let message_handler = MessageHandler::new(state.clone());
    let heartbeat_interval = std::time::Duration::from_secs(state.config.ws_heartbeat_interval_secs);
    let mut heartbeat_timer = tokio::time::interval(heartbeat_interval);
    
    loop {
        tokio::select! {
            // Handle incoming WebSocket messages
            msg = ws_receiver.next() => {
                match msg {
                    Some(Ok(Message::Text(text))) => {
                        debug!("Received text message: {}", text);
                        
                        // Parse and handle the message
                        match serde_json::from_str::<WebSocketMessage>(&text) {
                            Ok(ws_msg) => {
                                if let Err(e) = message_handler.handle_message(ws_msg, &connection).await {
                                    error!("Failed to handle message: {}", e);
                                    
                                    // Send error response
                                    let error_msg = WebSocketMessage::Error {
                                        code: "MESSAGE_HANDLING_ERROR".to_string(),
                                        message: e.to_string(),
                                        details: None,
                                    };
                                    
                                    if let Err(e) = connection.send_message(error_msg).await {
                                        error!("Failed to send error message: {}", e);
                                    }
                                }
                            }
                            Err(e) => {
                                warn!("Failed to parse WebSocket message: {}", e);
                                
                                let error_msg = WebSocketMessage::Error {
                                    code: "INVALID_MESSAGE_FORMAT".to_string(),
                                    message: "Invalid message format".to_string(),
                                    details: Some(serde_json::json!({ "error": e.to_string() })),
                                };
                                
                                if let Err(e) = connection.send_message(error_msg).await {
                                    error!("Failed to send parse error message: {}", e);
                                }
                            }
                        }
                    }
                    Some(Ok(Message::Binary(_))) => {
                        debug!("Received binary message (not supported)");
                    }
                    Some(Ok(Message::Ping(data))) => {
                        debug!("Received ping");
                        connection.update_last_activity().await;
                        
                        // Pong is handled automatically by axum/tungstenite
                    }
                    Some(Ok(Message::Pong(_))) => {
                        debug!("Received pong");
                        connection.update_last_activity().await;
                    }
                    Some(Ok(Message::Close(_))) => {
                        info!("Client requested close");
                        break;
                    }
                    Some(Err(e)) => {
                        error!("WebSocket error: {}", e);
                        break;
                    }
                    None => {
                        info!("WebSocket stream ended");
                        break;
                    }
                }
            }
            
            // Send periodic heartbeat
            _ = heartbeat_timer.tick() => {
                if let Err(e) = connection.send_message(WebSocketMessage::Ping).await {
                    error!("Failed to send ping: {}", e);
                    break;
                }
            }
        }
    }
}

async fn send_error_and_close(
    mut socket: WebSocket,
    code: &str,
    message: &str,
) -> Result<(), axum::Error> {
    let error_msg = WebSocketMessage::Error {
        code: code.to_string(),
        message: message.to_string(),
        details: None,
    };
    
    let json = serde_json::to_string(&error_msg).map_err(|e| {
        error!("Failed to serialize error message: {}", e);
        axum::Error::new(e)
    })?;
    
    socket.send(Message::Text(json)).await?;
    socket.send(Message::Close(None)).await?;
    
    Ok(())
}