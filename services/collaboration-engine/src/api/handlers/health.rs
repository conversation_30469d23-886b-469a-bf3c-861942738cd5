use axum::{
    extract::State,
    http::StatusCode,
    response::{IntoResponse, Json},
};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tracing::{error, info, instrument};

use crate::api::AppState;

#[derive(Debug, Serialize, Deserialize)]
pub struct HealthResponse {
    pub status: String,
    pub service: String,
    pub version: String,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct DetailedHealthResponse {
    pub status: String,
    pub service: String,
    pub version: String,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub dependencies: HealthDependencies,
    pub metrics: HealthMetrics,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct HealthDependencies {
    pub redis: DependencyStatus,
    pub spanner: DependencyStatus,
    pub websocket_hub: DependencyStatus,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct DependencyStatus {
    pub healthy: bool,
    pub latency_ms: Option<u64>,
    pub error: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct HealthMetrics {
    pub active_connections: usize,
    pub active_sessions: usize,
    pub memory_usage_mb: f64,
    pub uptime_seconds: u64,
}

// Basic health check
pub async fn health_check() -> impl IntoResponse {
    let response = HealthResponse {
        status: "healthy".to_string(),
        service: "collaboration-engine".to_string(),
        version: env!("CARGO_PKG_VERSION").to_string(),
        timestamp: chrono::Utc::now(),
    };

    (StatusCode::OK, Json(response))
}

// Readiness check - verifies all dependencies are available
#[instrument(skip(state))]
pub async fn readiness_check(State(state): State<Arc<AppState>>) -> impl IntoResponse {
    let mut all_healthy = true;
    let mut dependencies = HealthDependencies {
        redis: check_redis(&state).await,
        spanner: check_spanner(&state).await,
        websocket_hub: check_websocket_hub(&state).await,
    };

    if !dependencies.redis.healthy
        || !dependencies.spanner.healthy
        || !dependencies.websocket_hub.healthy
    {
        all_healthy = false;
    }

    let response = DetailedHealthResponse {
        status: if all_healthy { "ready" } else { "not_ready" }.to_string(),
        service: "collaboration-engine".to_string(),
        version: env!("CARGO_PKG_VERSION").to_string(),
        timestamp: chrono::Utc::now(),
        dependencies,
        metrics: get_health_metrics(&state).await,
    };

    let status_code = if all_healthy {
        StatusCode::OK
    } else {
        StatusCode::SERVICE_UNAVAILABLE
    };

    (status_code, Json(response))
}

// Liveness check - simple check to verify the service is running
pub async fn liveness_check() -> impl IntoResponse {
    (StatusCode::OK, Json(serde_json::json!({"status": "alive"})))
}

async fn check_redis(state: &Arc<AppState>) -> DependencyStatus {
    let start = std::time::Instant::now();
    
    match state.storage.redis.health_check().await {
        Ok(_) => DependencyStatus {
            healthy: true,
            latency_ms: Some(start.elapsed().as_millis() as u64),
            error: None,
        },
        Err(e) => {
            error!("Redis health check failed: {}", e);
            DependencyStatus {
                healthy: false,
                latency_ms: None,
                error: Some(e.to_string()),
            }
        }
    }
}

async fn check_spanner(state: &Arc<AppState>) -> DependencyStatus {
    let start = std::time::Instant::now();
    
    match state.storage.spanner.health_check().await {
        Ok(_) => DependencyStatus {
            healthy: true,
            latency_ms: Some(start.elapsed().as_millis() as u64),
            error: None,
        },
        Err(e) => {
            error!("Spanner health check failed: {}", e);
            DependencyStatus {
                healthy: false,
                latency_ms: None,
                error: Some(e.to_string()),
            }
        }
    }
}

async fn check_websocket_hub(state: &Arc<AppState>) -> DependencyStatus {
    let metrics = state.ws_hub.get_metrics().await;
    
    DependencyStatus {
        healthy: true,
        latency_ms: Some(0), // Hub is in-memory
        error: None,
    }
}

async fn get_health_metrics(state: &Arc<AppState>) -> HealthMetrics {
    let ws_metrics = state.ws_hub.get_metrics().await;
    
    // Get memory usage
    let memory_usage_mb = if let Ok(mem_info) = sys_info::mem_info() {
        ((mem_info.total - mem_info.avail) as f64) / 1024.0 / 1024.0
    } else {
        0.0
    };
    
    // Calculate uptime
    let uptime_seconds = std::time::SystemTime::now()
        .duration_since(std::time::UNIX_EPOCH)
        .unwrap()
        .as_secs()
        - state.storage.start_time;
    
    HealthMetrics {
        active_connections: ws_metrics.active_connections,
        active_sessions: ws_metrics.active_sessions,
        memory_usage_mb,
        uptime_seconds,
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_health_check() {
        let response = health_check().await.into_response();
        assert_eq!(response.status(), StatusCode::OK);
    }

    #[tokio::test]
    async fn test_liveness_check() {
        let response = liveness_check().await.into_response();
        assert_eq!(response.status(), StatusCode::OK);
    }
}