use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::IntoResponse,
    Extension, Json,
};
use serde::Deserialize;
use serde_json::json;
use std::sync::Arc;
use tracing::{error, info};
use uuid::Uuid;

use crate::{
    api::{handlers::websocket::AuthUser, AppState},
    models::{
        CreateSessionRequest, UpdateSessionRequest, SendMessageRequest,
        SessionResponse, SessionDetailsResponse, Message, MessageContent,
        UserId, UserInfo, Timestamp, SessionStatus,
    },
};

#[derive(Deserialize)]
pub struct MessageQuery {
    limit: Option<usize>,
    before: Option<String>,
}

pub async fn create_session(
    State(state): State<Arc<AppState>>,
    Extension(user): Extension<AuthUser>,
    Json(request): Json<CreateSessionRequest>,
) -> impl IntoResponse {
    let user_id = UserId::from_string(user.user_id.clone());
    let user_info = UserInfo {
        user_id: user_id.clone(),
        email: user.email.clone(),
        name: user.name.clone(),
        avatar_url: None,
    };
    
    // Check if user is a member of the team
    match state.storage.teams.get_team(&request.team_id, &user_id).await {
        Ok(Some(_)) => {
            // User is a member, proceed
        }
        Ok(None) => {
            return (StatusCode::FORBIDDEN, Json(json!({ "error": "Not a member of this team" })));
        }
        Err(e) => {
            error!("Failed to check team membership: {}", e);
            return (StatusCode::INTERNAL_SERVER_ERROR, Json(json!({ "error": "Failed to verify team membership" })));
        }
    }
    
    match state.storage.sessions.create_session(request, &user_id, user_info).await {
        Ok(session) => {
            info!("Session created successfully: {}", session.id);
            
            let response = SessionResponse {
                id: session.id.clone(),
                team_id: session.team_id,
                name: session.name,
                description: session.description,
                created_by: session.created_by,
                created_at: session.created_at,
                updated_at: session.updated_at,
                status: session.status,
                participant_count: session.participants.len(),
                settings: session.settings,
            };
            
            (StatusCode::CREATED, Json(json!({ "session": response })))
        }
        Err(e) => {
            error!("Failed to create session: {}", e);
            (StatusCode::INTERNAL_SERVER_ERROR, Json(json!({ "error": format!("Failed to create session: {}", e) })))
        }
    }
}

pub async fn list_team_sessions(
    State(state): State<Arc<AppState>>,
    Extension(user): Extension<AuthUser>,
    Path(team_id): Path<String>,
) -> impl IntoResponse {
    let user_id = UserId::from_string(user.user_id);
    
    // Check if user is a member of the team
    match state.storage.teams.get_team(&team_id, &user_id).await {
        Ok(Some(_)) => {
            // User is a member, proceed
        }
        Ok(None) => {
            return (StatusCode::FORBIDDEN, Json(json!({ "error": "Not a member of this team" })));
        }
        Err(e) => {
            error!("Failed to check team membership: {}", e);
            return (StatusCode::INTERNAL_SERVER_ERROR, Json(json!({ "error": "Failed to verify team membership" })));
        }
    }
    
    match state.storage.sessions.list_team_sessions(&team_id).await {
        Ok(sessions) => {
            let response: Vec<SessionResponse> = sessions.into_iter().map(|session| {
                SessionResponse {
                    id: session.id,
                    team_id: session.team_id,
                    name: session.name,
                    description: session.description,
                    created_by: session.created_by,
                    created_at: session.created_at,
                    updated_at: session.updated_at,
                    status: session.status,
                    participant_count: session.participants.len(),
                    settings: session.settings,
                }
            }).collect();
            
            (StatusCode::OK, Json(json!({ "sessions": response })))
        }
        Err(e) => {
            error!("Failed to list sessions: {}", e);
            (StatusCode::INTERNAL_SERVER_ERROR, Json(json!({ "error": format!("Failed to list sessions: {}", e) })))
        }
    }
}

pub async fn get_session(
    State(state): State<Arc<AppState>>,
    Extension(user): Extension<AuthUser>,
    Path(session_id): Path<String>,
) -> impl IntoResponse {
    let user_id = UserId::from_string(user.user_id);
    
    match state.storage.sessions.get_session(&session_id).await {
        Ok(Some(session)) => {
            // Check if user has access via team membership
            match state.storage.teams.get_team(&session.team_id, &user_id).await {
                Ok(Some(_)) => {
                    // User has access
                    let participants = state.storage.sessions.get_session_participants(&session_id).await
                        .unwrap_or_default();
                    
                    let recent_messages = state.storage.sessions.get_session_messages(&session_id, 20, None).await
                        .unwrap_or_default();
                    
                    let session_response = SessionResponse {
                        id: session.id,
                        team_id: session.team_id,
                        name: session.name,
                        description: session.description,
                        created_by: session.created_by,
                        created_at: session.created_at,
                        updated_at: session.updated_at,
                        status: session.status,
                        participant_count: session.participants.len(),
                        settings: session.settings,
                    };
                    
                    let response = SessionDetailsResponse {
                        session: session_response,
                        participants,
                        recent_messages,
                    };
                    
                    (StatusCode::OK, Json(json!(response)))
                }
                Ok(None) => {
                    (StatusCode::FORBIDDEN, Json(json!({ "error": "Access denied" })))
                }
                Err(e) => {
                    error!("Failed to check team membership: {}", e);
                    (StatusCode::INTERNAL_SERVER_ERROR, Json(json!({ "error": "Failed to verify access" })))
                }
            }
        }
        Ok(None) => {
            (StatusCode::NOT_FOUND, Json(json!({ "error": "Session not found" })))
        }
        Err(e) => {
            error!("Failed to get session: {}", e);
            (StatusCode::INTERNAL_SERVER_ERROR, Json(json!({ "error": format!("Failed to get session: {}", e) })))
        }
    }
}

pub async fn update_session(
    State(state): State<Arc<AppState>>,
    Extension(user): Extension<AuthUser>,
    Path(session_id): Path<String>,
    Json(request): Json<UpdateSessionRequest>,
) -> impl IntoResponse {
    let user_id = UserId::from_string(user.user_id);
    
    match state.storage.sessions.update_session(&session_id, &user_id, request).await {
        Ok(_) => {
            info!("Session {} updated successfully", session_id);
            (StatusCode::OK, Json(json!({ "message": "Session updated successfully" })))
        }
        Err(e) => {
            error!("Failed to update session: {}", e);
            if e.to_string().contains("not a participant") {
                (StatusCode::FORBIDDEN, Json(json!({ "error": e.to_string() })))
            } else {
                (StatusCode::INTERNAL_SERVER_ERROR, Json(json!({ "error": format!("Failed to update session: {}", e) })))
            }
        }
    }
}

pub async fn end_session(
    State(state): State<Arc<AppState>>,
    Extension(user): Extension<AuthUser>,
    Path(session_id): Path<String>,
) -> impl IntoResponse {
    let user_id = UserId::from_string(user.user_id);
    
    match state.storage.sessions.end_session(&session_id, &user_id).await {
        Ok(_) => {
            info!("Session {} ended successfully", session_id);
            (StatusCode::OK, Json(json!({ "message": "Session ended successfully" })))
        }
        Err(e) => {
            error!("Failed to end session: {}", e);
            if e.to_string().contains("Only session creator") {
                (StatusCode::FORBIDDEN, Json(json!({ "error": e.to_string() })))
            } else {
                (StatusCode::INTERNAL_SERVER_ERROR, Json(json!({ "error": format!("Failed to end session: {}", e) })))
            }
        }
    }
}

pub async fn get_messages(
    State(state): State<Arc<AppState>>,
    Extension(user): Extension<AuthUser>,
    Path(session_id): Path<String>,
    Query(query): Query<MessageQuery>,
) -> impl IntoResponse {
    let user_id = UserId::from_string(user.user_id);
    let limit = query.limit.unwrap_or(50).min(100);
    
    // Check if user has access to session
    match state.storage.sessions.get_session(&session_id).await {
        Ok(Some(session)) => {
            // Check team membership
            match state.storage.teams.get_team(&session.team_id, &user_id).await {
                Ok(Some(_)) => {
                    // User has access
                    match state.storage.sessions.get_session_messages(&session_id, limit, query.before).await {
                        Ok(messages) => {
                            (StatusCode::OK, Json(json!({ "messages": messages })))
                        }
                        Err(e) => {
                            error!("Failed to get messages: {}", e);
                            (StatusCode::INTERNAL_SERVER_ERROR, Json(json!({ "error": "Failed to get messages" })))
                        }
                    }
                }
                Ok(None) => {
                    (StatusCode::FORBIDDEN, Json(json!({ "error": "Access denied" })))
                }
                Err(e) => {
                    error!("Failed to check team membership: {}", e);
                    (StatusCode::INTERNAL_SERVER_ERROR, Json(json!({ "error": "Failed to verify access" })))
                }
            }
        }
        Ok(None) => {
            (StatusCode::NOT_FOUND, Json(json!({ "error": "Session not found" })))
        }
        Err(e) => {
            error!("Failed to get session: {}", e);
            (StatusCode::INTERNAL_SERVER_ERROR, Json(json!({ "error": "Failed to get session" })))
        }
    }
}

pub async fn send_message(
    State(state): State<Arc<AppState>>,
    Extension(user): Extension<AuthUser>,
    Path(session_id): Path<String>,
    Json(request): Json<SendMessageRequest>,
) -> impl IntoResponse {
    let user_id = UserId::from_string(user.user_id);
    
    // Build metadata
    let mut metadata = crate::models::MessageMetadata::default();
    if let Some(input_metadata) = request.metadata {
        metadata.reply_to = input_metadata.reply_to;
        metadata.mentions = input_metadata.mentions.unwrap_or_default()
            .into_iter()
            .map(UserId::from_string)
            .collect();
        metadata.tags = input_metadata.tags.unwrap_or_default();
    }
    
    // Create message
    let message = Message {
        id: Uuid::new_v4().to_string(),
        session_id: session_id.clone(),
        user_id: user_id.clone(),
        content: request.content,
        created_at: Timestamp::now(),
        updated_at: None,
        edited: false,
        deleted: false,
        metadata,
    };
    
    // Save message
    match state.storage.sessions.save_message(&message).await {
        Ok(_) => {
            info!("Message saved successfully in session {}", session_id);
            
            // Broadcast message via WebSocket
            let ws_message = crate::models::WebSocketMessage::MessageReceived {
                message: message.clone(),
            };
            
            if let Err(e) = state.ws_hub.broadcast_to_session(&session_id, ws_message, None).await {
                error!("Failed to broadcast message: {}", e);
            }
            
            (StatusCode::CREATED, Json(json!({ "message": message })))
        }
        Err(e) => {
            error!("Failed to save message: {}", e);
            (StatusCode::INTERNAL_SERVER_ERROR, Json(json!({ "error": format!("Failed to save message: {}", e) })))
        }
    }
}