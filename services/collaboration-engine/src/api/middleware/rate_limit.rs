use axum::{
    body::Body,
    extract::Request,
    http::{header, HeaderMap, StatusCode},
    middleware::Next,
    response::{IntoResponse, Response},
    Json,
};
use chrono::Utc;
use serde_json::json;
use std::sync::Arc;
use tower::{Layer, Service};
use tracing::{debug, warn};

use crate::{
    api::handlers::websocket::AuthUser,
    storage::redis_client::RedisClient,
};

// Rate limit layer
#[derive(Clone)]
pub struct RateLimitLayer {
    redis_client: Arc<RedisClient>,
    requests_per_minute: u64,
}

impl RateLimitLayer {
    pub fn new(redis_client: Arc<RedisClient>, requests_per_minute: u64) -> Self {
        Self {
            redis_client,
            requests_per_minute,
        }
    }
}

impl<S> Layer<S> for RateLimitLayer {
    type Service = RateLimitMiddleware<S>;

    fn layer(&self, inner: S) -> Self::Service {
        RateLimitMiddleware {
            inner,
            redis_client: self.redis_client.clone(),
            requests_per_minute: self.requests_per_minute,
        }
    }
}

#[derive(Clone)]
pub struct RateLimitMiddleware<S> {
    inner: S,
    redis_client: Arc<RedisClient>,
    requests_per_minute: u64,
}

impl<S> Service<Request> for RateLimitMiddleware<S>
where
    S: Service<Request, Response = Response> + Send + 'static,
    S::Future: Send + 'static,
{
    type Response = S::Response;
    type Error = S::Error;
    type Future = std::pin::Pin<
        Box<dyn std::future::Future<Output = Result<Self::Response, Self::Error>> + Send>,
    >;

    fn poll_ready(
        &mut self,
        cx: &mut std::task::Context<'_>,
    ) -> std::task::Poll<Result<(), Self::Error>> {
        self.inner.poll_ready(cx)
    }

    fn call(&mut self, mut request: Request) -> Self::Future {
        let redis_client = self.redis_client.clone();
        let requests_per_minute = self.requests_per_minute;
        
        // Extract client identifier before moving request
        let client_id = extract_client_id(&request);
        
        let future = self.inner.call(request);
        
        Box::pin(async move {
            
            // Check rate limit using sliding window
            match check_rate_limit(&redis_client, &client_id, requests_per_minute).await {
                Ok(rate_limit_info) => {
                    if rate_limit_info.allowed {
                        // Add rate limit headers to response
                        let mut response = future.await?;
                        let headers = response.headers_mut();
                        
                        headers.insert(
                            "X-RateLimit-Limit",
                            header::HeaderValue::from_str(&requests_per_minute.to_string()).unwrap(),
                        );
                        headers.insert(
                            "X-RateLimit-Remaining",
                            header::HeaderValue::from_str(&rate_limit_info.remaining.to_string()).unwrap(),
                        );
                        headers.insert(
                            "X-RateLimit-Reset",
                            header::HeaderValue::from_str(&rate_limit_info.reset_at.to_string()).unwrap(),
                        );
                        
                        Ok(response)
                    } else {
                        // Rate limit exceeded
                        warn!("Rate limit exceeded for client: {}", client_id);
                        crate::metrics::record_error("rate_limit", "exceeded");
                        
                        let mut response = Response::builder()
                            .status(StatusCode::TOO_MANY_REQUESTS)
                            .body(Body::from(json!({
                                "error": {
                                    "code": 429,
                                    "message": "Too many requests. Please retry after some time.",
                                    "type": "rate_limit_exceeded"
                                }
                            }).to_string()))
                            .unwrap();
                        
                        let headers = response.headers_mut();
                        headers.insert(
                            "X-RateLimit-Limit",
                            header::HeaderValue::from_str(&requests_per_minute.to_string()).unwrap(),
                        );
                        headers.insert(
                            "X-RateLimit-Remaining",
                            header::HeaderValue::from_static("0"),
                        );
                        headers.insert(
                            "X-RateLimit-Reset",
                            header::HeaderValue::from_str(&rate_limit_info.reset_at.to_string()).unwrap(),
                        );
                        headers.insert(
                            "Retry-After",
                            header::HeaderValue::from_str(&rate_limit_info.retry_after.to_string()).unwrap(),
                        );
                        
                        Ok(response)
                    }
                }
                Err(e) => {
                    // Log error but allow request (fail open)
                    warn!("Failed to check rate limit: {}", e);
                    crate::metrics::record_error("rate_limit", "check_failed");
                    future.await
                }
            }
        })
    }
}

#[derive(Debug)]
struct RateLimitInfo {
    allowed: bool,
    remaining: u64,
    reset_at: i64,
    retry_after: u64,
}

// Extract client identifier from request
fn extract_client_id(request: &Request) -> String {
    // Try to get authenticated user ID first
    if let Some(user) = request.extensions().get::<AuthUser>() {
        return format!("user:{}", user.user_id);
    }
    
    // Fall back to IP address
    let headers = request.headers();
    let ip = headers
        .get("x-forwarded-for")
        .or_else(|| headers.get("x-real-ip"))
        .and_then(|header| header.to_str().ok())
        .and_then(|ips| ips.split(',').next())
        .unwrap_or("unknown");
    
    format!("ip:{}", ip)
}

// Check rate limit using sliding window algorithm
async fn check_rate_limit(
    redis_client: &Arc<RedisClient>,
    client_id: &str,
    limit: u64,
) -> Result<RateLimitInfo, anyhow::Error> {
    let now = Utc::now().timestamp();
    let window_start = now - 60; // 1 minute window
    let key = format!("rate_limit:{}", client_id);
    
    // Use a Lua script for atomic sliding window rate limiting
    let lua_script = r#"
        local key = KEYS[1]
        local now = tonumber(ARGV[1])
        local window_start = tonumber(ARGV[2])
        local limit = tonumber(ARGV[3])
        
        -- Remove old entries
        redis.call('ZREMRANGEBYSCORE', key, '-inf', window_start)
        
        -- Count current entries
        local current = redis.call('ZCOUNT', key, window_start, now)
        
        if current < limit then
            -- Add new entry
            redis.call('ZADD', key, now, now)
            redis.call('EXPIRE', key, 60)
            return {1, limit - current - 1, now + 60}
        else
            -- Get oldest entry to calculate retry time
            local oldest = redis.call('ZRANGE', key, 0, 0, 'WITHSCORES')
            local retry_after = 60
            if oldest[2] then
                retry_after = 60 - (now - tonumber(oldest[2]))
            end
            return {0, 0, now + 60, retry_after}
        end
    "#;
    
    // For now, use a simple counter approach since we don't have Lua script support
    // This is a simplified implementation
    let count_key = format!("{}:{}", key, now / 60); // Per-minute bucket
    
    // Get current count
    let current_count = redis_client
        .get_cached_analysis(&count_key)
        .await?
        .and_then(|s| s.parse::<u64>().ok())
        .unwrap_or(0);
    
    if current_count < limit {
        // Increment counter
        let new_count = current_count + 1;
        redis_client
            .set_cached_analysis(&count_key, &new_count.to_string(), 60)
            .await?;
        
        Ok(RateLimitInfo {
            allowed: true,
            remaining: limit - new_count,
            reset_at: now + 60,
            retry_after: 0,
        })
    } else {
        Ok(RateLimitInfo {
            allowed: false,
            remaining: 0,
            reset_at: now + 60,
            retry_after: 60 - (now % 60),
        })
    }
}