use axum::{
    body::Body,
    extract::Request,
    http::{header, HeaderValue},
    response::Response,
};
use tower::{Layer, Service};

// Security layer for adding security headers
#[derive(Clone)]
pub struct SecurityLayer;

impl SecurityLayer {
    pub fn new() -> Self {
        Self
    }
}

impl<S> Layer<S> for SecurityLayer {
    type Service = SecurityMiddleware<S>;

    fn layer(&self, inner: S) -> Self::Service {
        SecurityMiddleware { inner }
    }
}

#[derive(Clone)]
pub struct SecurityMiddleware<S> {
    inner: S,
}

impl<S> Service<Request> for SecurityMiddleware<S>
where
    S: Service<Request, Response = Response> + Send + 'static,
    S::Future: Send + 'static,
{
    type Response = S::Response;
    type Error = S::Error;
    type Future = std::pin::Pin<
        Box<dyn std::future::Future<Output = Result<Self::Response, Self::Error>> + Send>,
    >;

    fn poll_ready(
        &mut self,
        cx: &mut std::task::Context<'_>,
    ) -> std::task::Poll<Result<(), Self::Error>> {
        self.inner.poll_ready(cx)
    }

    fn call(&mut self, request: Request) -> Self::Future {
        let future = self.inner.call(request);
        
        Box::pin(async move {
            let mut response = future.await?;
            
            // Add security headers
            let headers = response.headers_mut();
            
            headers.insert(
                header::X_CONTENT_TYPE_OPTIONS,
                HeaderValue::from_static("nosniff"),
            );
            
            headers.insert(
                header::X_FRAME_OPTIONS,
                HeaderValue::from_static("DENY"),
            );
            
            headers.insert(
                header::X_XSS_PROTECTION,
                HeaderValue::from_static("1; mode=block"),
            );
            
            headers.insert(
                header::STRICT_TRANSPORT_SECURITY,
                HeaderValue::from_static("max-age=31536000; includeSubDomains"),
            );
            
            headers.insert(
                header::CONTENT_SECURITY_POLICY,
                HeaderValue::from_static(
                    "default-src 'self'; \
                     script-src 'self' 'unsafe-inline' 'unsafe-eval'; \
                     style-src 'self' 'unsafe-inline'; \
                     img-src 'self' data: https:; \
                     font-src 'self'; \
                     connect-src 'self' wss: https:; \
                     frame-ancestors 'none'; \
                     base-uri 'self'; \
                     form-action 'self'"
                ),
            );
            
            headers.insert(
                "X-Permitted-Cross-Domain-Policies",
                HeaderValue::from_static("none"),
            );
            
            headers.insert(
                header::REFERRER_POLICY,
                HeaderValue::from_static("strict-origin-when-cross-origin"),
            );
            
            // Remove server header to avoid information disclosure
            headers.remove(header::SERVER);
            
            Ok(response)
        })
    }
}