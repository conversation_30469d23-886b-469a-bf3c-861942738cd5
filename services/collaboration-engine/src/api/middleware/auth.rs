use axum::{
    body::Body,
    extract::Request,
    http::StatusCode,
    middleware::Next,
    response::{IntoResponse, Response},
};
use chrono::{Duration, Utc};
use jsonwebtoken::{decode, encode, Algorithm, Decoding<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Val<PERSON><PERSON>};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tower::{Layer, Service};
use tracing::{debug, error, warn};

use crate::{
    api::handlers::websocket::AuthUser,
    errors::ApiError,
    storage::redis_client::RedisClient,
};

// JWT Claims structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Claims {
    pub sub: String,       // user_id
    pub email: String,
    pub name: String,
    pub roles: Vec<String>,
    pub exp: usize,        // Expiration time
    pub iat: usize,        // Issued at
    pub nbf: usize,        // Not before
    pub iss: String,       // Issuer
    pub aud: String,       // Audience
    pub jti: String,       // JWT ID (for tracking/revocation)
    pub token_type: TokenType,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "snake_case")]
pub enum TokenType {
    Access,
    Refresh,
    Service,  // For service-to-service communication
}

impl Claims {
    pub fn new(user_id: String, email: String, name: String, roles: Vec<String>, token_type: TokenType) -> Self {
        let now = Utc::now();
        let (exp_duration, jti_prefix) = match token_type {
            TokenType::Access => (Duration::minutes(15), "access"),
            TokenType::Refresh => (Duration::days(7), "refresh"),
            TokenType::Service => (Duration::hours(1), "service"),
        };
        let exp = now + exp_duration;
        
        Self {
            sub: user_id,
            email,
            name,
            roles,
            exp: exp.timestamp() as usize,
            iat: now.timestamp() as usize,
            nbf: now.timestamp() as usize,
            iss: "episteme-collaboration".to_string(),
            aud: "episteme-platform".to_string(),
            jti: format!("{}_{}", jti_prefix, uuid::Uuid::new_v4()),
            token_type,
        }
    }
    
    pub fn is_expired(&self) -> bool {
        let now = Utc::now().timestamp() as usize;
        self.exp < now
    }
    
    pub fn has_role(&self, role: &str) -> bool {
        self.roles.contains(&role.to_string())
    }
}

// JWT Service for token management
pub struct JwtService {
    encoding_key: EncodingKey,
    decoding_key: DecodingKey,
    validation: Validation,
    redis_client: Arc<RedisClient>,
}

impl JwtService {
    pub fn new(secret: &[u8], redis_client: Arc<RedisClient>) -> Self {
        let mut validation = Validation::new(Algorithm::HS256);
        validation.set_issuer(&["episteme-collaboration"]);
        validation.set_audience(&["episteme-platform"]);
        validation.validate_exp = true;
        validation.validate_nbf = true;
        
        Self {
            encoding_key: EncodingKey::from_secret(secret),
            decoding_key: DecodingKey::from_secret(secret),
            validation,
            redis_client,
        }
    }
    
    pub fn create_token(&self, claims: &Claims) -> Result<String, ApiError> {
        let header = Header::new(Algorithm::HS256);
        encode(&header, claims, &self.encoding_key)
            .map_err(|e| ApiError::InternalServerError(format!("Failed to create token: {}", e)))
    }
    
    pub async fn validate_token(&self, token: &str) -> Result<Claims, ApiError> {
        // First check if token is revoked
        if self.is_token_revoked(token).await? {
            return Err(ApiError::Unauthorized("Token has been revoked".to_string()));
        }
        
        // Decode and validate token
        let token_data = decode::<Claims>(token, &self.decoding_key, &self.validation)
            .map_err(|e| match e.kind() {
                jsonwebtoken::errors::ErrorKind::ExpiredSignature => {
                    ApiError::Unauthorized("Token has expired".to_string())
                }
                jsonwebtoken::errors::ErrorKind::InvalidToken => {
                    ApiError::Unauthorized("Invalid token".to_string())
                }
                jsonwebtoken::errors::ErrorKind::InvalidSignature => {
                    ApiError::Unauthorized("Invalid token signature".to_string())
                }
                _ => ApiError::Unauthorized(format!("Token validation failed: {}", e)),
            })?;
        
        Ok(token_data.claims)
    }
    
    pub async fn revoke_token(&self, token: &str) -> Result<(), ApiError> {
        // Extract JTI from token without full validation (allows revocation of expired tokens)
        let mut validation = Validation::new(Algorithm::HS256);
        validation.validate_exp = false;
        validation.insecure_disable_signature_validation();
        
        if let Ok(token_data) = decode::<Claims>(token, &self.decoding_key, &validation) {
            let revocation_key = format!("revoked_token:{}", token_data.claims.jti);
            let ttl = match token_data.claims.token_type {
                TokenType::Access => 900,  // 15 minutes
                TokenType::Refresh => 604800,  // 7 days
                TokenType::Service => 3600,  // 1 hour
            };
            
            // Store in Redis with TTL matching token lifetime
            self.redis_client
                .set_cached_analysis(&revocation_key, "1", ttl)
                .await
                .map_err(|e| ApiError::InternalServerError(format!("Failed to revoke token: {}", e)))?;
            
            debug!("Token revoked: {}", token_data.claims.jti);
            Ok(())
        } else {
            Err(ApiError::BadRequest("Invalid token format".to_string()))
        }
    }
    
    async fn is_token_revoked(&self, token: &str) -> Result<bool, ApiError> {
        // Extract JTI without full validation
        let mut validation = Validation::new(Algorithm::HS256);
        validation.validate_exp = false;
        validation.insecure_disable_signature_validation();
        
        if let Ok(token_data) = decode::<Claims>(token, &self.decoding_key, &validation) {
            let revocation_key = format!("revoked_token:{}", token_data.claims.jti);
            
            match self.redis_client.get_cached_analysis(&revocation_key).await {
                Ok(Some(_)) => Ok(true),
                Ok(None) => Ok(false),
                Err(e) => {
                    error!("Failed to check token revocation: {}", e);
                    // Fail open - allow token if Redis is down
                    Ok(false)
                }
            }
        } else {
            Ok(false)
        }
    }
    
    pub fn create_token_pair(&self, user_id: String, email: String, name: String, roles: Vec<String>) -> Result<TokenPair, ApiError> {
        // Create access token (15 minutes)
        let access_claims = Claims::new(
            user_id.clone(),
            email.clone(),
            name.clone(),
            roles.clone(),
            TokenType::Access,
        );
        let access_token = self.create_token(&access_claims)?;
        
        // Create refresh token (7 days)
        let refresh_claims = Claims::new(
            user_id,
            email,
            name,
            roles,
            TokenType::Refresh,
        );
        let refresh_token = self.create_token(&refresh_claims)?;
        
        Ok(TokenPair {
            access_token,
            refresh_token,
            expires_in: 900, // 15 minutes in seconds
        })
    }
    
    pub async fn refresh_access_token(&self, refresh_token: &str) -> Result<String, ApiError> {
        let refresh_claims = self.validate_token(refresh_token).await?;
        
        // Verify this is actually a refresh token
        if refresh_claims.token_type != TokenType::Refresh {
            return Err(ApiError::BadRequest("Invalid token type".to_string()));
        }
        
        // Create new access token with same user info
        let access_claims = Claims::new(
            refresh_claims.sub,
            refresh_claims.email,
            refresh_claims.name,
            refresh_claims.roles,
            TokenType::Access,
        );
        
        self.create_token(&access_claims)
    }
}

#[derive(Debug, Serialize, Deserialize)]
pub struct TokenPair {
    pub access_token: String,
    pub refresh_token: String,
    pub expires_in: u64,
}

// Middleware function for routes that require authentication
pub async fn require_auth(mut req: Request, next: Next) -> Result<Response, StatusCode> {
    // Extract token from Authorization header
    let auth_header = req
        .headers()
        .get("Authorization")
        .and_then(|value| value.to_str().ok());

    if let Some(auth_header) = auth_header {
        if let Some(token) = auth_header.strip_prefix("Bearer ") {
            // Get JWT service from request extensions (added by router state)
            let jwt_service = req
                .extensions()
                .get::<Arc<JwtService>>()
                .ok_or(StatusCode::INTERNAL_SERVER_ERROR)?;
            
            // Validate token
            match jwt_service.validate_token(token).await {
                Ok(claims) => {
                    // Convert claims to AuthUser
                    let user = AuthUser {
                        user_id: claims.sub,
                        email: claims.email,
                        name: claims.name,
                        roles: claims.roles,
                    };
                    
                    req.extensions_mut().insert(user);
                    Ok(next.run(req).await)
                }
                Err(e) => {
                    warn!("JWT validation failed: {}", e);
                    Err(StatusCode::UNAUTHORIZED)
                }
            }
        } else {
            Err(StatusCode::UNAUTHORIZED)
        }
    } else {
        Err(StatusCode::UNAUTHORIZED)
    }
}

// Service-to-service authentication
pub async fn require_service_auth(mut req: Request, next: Next) -> Result<Response, StatusCode> {
    let auth_header = req
        .headers()
        .get("Authorization")
        .and_then(|value| value.to_str().ok());

    if let Some(auth_header) = auth_header {
        if let Some(token) = auth_header.strip_prefix("Bearer ") {
            let jwt_service = req
                .extensions()
                .get::<Arc<JwtService>>()
                .ok_or(StatusCode::INTERNAL_SERVER_ERROR)?;
            
            match jwt_service.validate_token(token).await {
                Ok(claims) => {
                    // Verify this is a service token
                    if claims.token_type != TokenType::Service {
                        return Err(StatusCode::FORBIDDEN);
                    }
                    
                    // Add service info to request
                    req.extensions_mut().insert(claims);
                    Ok(next.run(req).await)
                }
                Err(_) => Err(StatusCode::UNAUTHORIZED),
            }
        } else {
            Err(StatusCode::UNAUTHORIZED)
        }
    } else {
        Err(StatusCode::UNAUTHORIZED)
    }
}