use crate::config::Config;
use anyhow::{Context, Result};
use redis::{aio::MultiplexedConnection, AsyncCommands, Client};
use std::time::Duration;
use tracing::{error, info};

pub struct RedisClient {
    pub client: Client,
    connection_timeout: Duration,
}

impl RedisClient {
    pub async fn new(config: &Config) -> Result<Self> {
        info!("Connecting to Redis at: {}", config.redis_url);

        let client = Client::open(config.redis_url.clone())
            .context("Failed to create Redis client")?;

        // Test connection
        let mut conn = client
            .get_multiplexed_async_connection()
            .await
            .context("Failed to connect to Red<PERSON>")?;

        // Ping to verify connection
        let pong: String = redis::cmd("PING")
            .query_async(&mut conn)
            .await
            .context("Failed to ping Redis")?;

        info!("Redis connection established, ping response: {}", pong);

        Ok(Self {
            client,
            connection_timeout: Duration::from_millis(config.redis_connection_timeout_ms),
        })
    }

    pub async fn get_connection(&self) -> Result<MultiplexedConnection> {
        self.client
            .get_multiplexed_async_connection()
            .await
            .context("Failed to get Redis connection")
    }

    // Health check
    pub async fn health_check(&self) -> Result<()> {
        let mut conn = self.get_connection().await?;
        let _: String = redis::cmd("PING")
            .query_async(&mut conn)
            .await
            .context("Redis health check failed")?;
        Ok(())
    }

    // Rate limiting for WebSocket messages
    pub async fn check_websocket_rate_limit(
        &self,
        user_id: &str,
        limit: u32,
        window_secs: u64,
    ) -> Result<(bool, u32)> {
        let mut conn = self.get_connection().await?;
        let key = format!("ws_rate_limit:{}", user_id);
        let now = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)?
            .as_secs();

        // Use sliding window with sorted sets
        let window_start = now - window_secs;

        // Remove old entries
        let _: u32 = conn
            .zremrangebyscore(&key, 0, window_start)
            .await
            .context("Failed to remove old rate limit entries")?;

        // Count current entries
        let count: u32 = conn.zcard(&key).await.context("Failed to count rate limit entries")?;

        if count < limit {
            // Add current request
            let _: u32 = conn
                .zadd(&key, now, now)
                .await
                .context("Failed to add rate limit entry")?;

            // Set expiry
            let _: () = conn
                .expire(&key, window_secs as i64)
                .await
                .context("Failed to set rate limit expiry")?;

            Ok((true, limit - count - 1))
        } else {
            Ok((false, 0))
        }
    }

    // Session presence tracking
    pub async fn update_presence(
        &self,
        session_id: &str,
        user_id: &str,
        ttl_secs: u64,
    ) -> Result<()> {
        let mut conn = self.get_connection().await?;
        let key = format!("presence:{}:{}", session_id, user_id);
        let value = serde_json::json!({
            "user_id": user_id,
            "last_seen": chrono::Utc::now().to_rfc3339(),
        });

        let _: () = conn
            .set_ex(&key, value.to_string(), ttl_secs)
            .await
            .context("Failed to update presence")?;

        // Also add to session presence set
        let set_key = format!("session_presence:{}", session_id);
        let _: () = conn
            .sadd(&set_key, user_id)
            .await
            .context("Failed to add to presence set")?;
        
        let _: () = conn
            .expire(&set_key, ttl_secs as i64)
            .await
            .context("Failed to set presence set expiry")?;

        Ok(())
    }

    // Get active users in a session
    pub async fn get_session_presence(&self, session_id: &str) -> Result<Vec<String>> {
        let mut conn = self.get_connection().await?;
        let key = format!("session_presence:{}", session_id);
        
        let members: Vec<String> = conn
            .smembers(&key)
            .await
            .context("Failed to get session presence")?;

        Ok(members)
    }

    // Clear user presence
    pub async fn clear_presence(&self, session_id: &str, user_id: &str) -> Result<()> {
        let mut conn = self.get_connection().await?;
        
        // Remove from presence hash
        let key = format!("presence:{}:{}", session_id, user_id);
        let _: () = conn.del(&key).await.context("Failed to delete presence")?;

        // Remove from session set
        let set_key = format!("session_presence:{}", session_id);
        let _: () = conn
            .srem(&set_key, user_id)
            .await
            .context("Failed to remove from presence set")?;

        Ok(())
    }

    // Message caching for recent messages
    pub async fn cache_message(
        &self,
        session_id: &str,
        message: &str,
        ttl_secs: u64,
    ) -> Result<()> {
        let mut conn = self.get_connection().await?;
        let key = format!("messages:{}", session_id);
        
        // Use a list with trimming to keep only recent messages
        let _: () = conn
            .lpush(&key, message)
            .await
            .context("Failed to cache message")?;
        
        // Keep only the last 100 messages
        let _: () = conn
            .ltrim(&key, 0, 99)
            .await
            .context("Failed to trim message list")?;
        
        let _: () = conn
            .expire(&key, ttl_secs as i64)
            .await
            .context("Failed to set message cache expiry")?;

        Ok(())
    }

    // Get recent cached messages
    pub async fn get_cached_messages(&self, session_id: &str, count: i64) -> Result<Vec<String>> {
        let mut conn = self.get_connection().await?;
        let key = format!("messages:{}", session_id);
        
        let messages: Vec<String> = conn
            .lrange(&key, 0, count - 1)
            .await
            .context("Failed to get cached messages")?;

        Ok(messages)
    }

    // Lock management for distributed operations
    pub async fn acquire_lock(&self, resource: &str, ttl_secs: u64) -> Result<String> {
        let mut conn = self.get_connection().await?;
        let lock_key = format!("lock:{}", resource);
        let lock_value = uuid::Uuid::new_v4().to_string();

        // Try to set the lock with NX (only if not exists)
        let acquired: bool = conn
            .set_nx(&lock_key, &lock_value)
            .await
            .context("Failed to acquire lock")?;

        if acquired {
            // Set expiry
            let _: () = conn
                .expire(&lock_key, ttl_secs as i64)
                .await
                .context("Failed to set lock expiry")?;
            Ok(lock_value)
        } else {
            Err(anyhow::anyhow!("Lock already held by another process"))
        }
    }

    // Release a lock
    pub async fn release_lock(&self, resource: &str, lock_value: &str) -> Result<()> {
        let mut conn = self.get_connection().await?;
        let lock_key = format!("lock:{}", resource);

        // Use Lua script to ensure atomic check-and-delete
        let script = r#"
            if redis.call("get", KEYS[1]) == ARGV[1] then
                return redis.call("del", KEYS[1])
            else
                return 0
            end
        "#;

        let result: i32 = redis::Script::new(script)
            .key(&lock_key)
            .arg(&lock_value)
            .invoke_async(&mut conn)
            .await
            .context("Failed to release lock")?;

        if result == 0 {
            error!("Failed to release lock - value mismatch");
        }

        Ok(())
    }

    // Generic cache operations
    pub async fn set(&self, key: &str, value: &str) -> Result<()> {
        let mut conn = self.get_connection().await?;
        let _: () = conn
            .set(key, value)
            .await
            .context("Failed to set value")?;
        Ok(())
    }

    pub async fn set_with_expiry(&self, key: &str, value: &str, ttl_secs: u64) -> Result<()> {
        let mut conn = self.get_connection().await?;
        let _: () = conn
            .set_ex(key, value, ttl_secs)
            .await
            .context("Failed to set value with expiry")?;
        Ok(())
    }

    pub async fn get(&self, key: &str) -> Result<Option<String>> {
        let mut conn = self.get_connection().await?;
        let value: Option<String> = conn
            .get(key)
            .await
            .context("Failed to get value")?;
        Ok(value)
    }

    // JSON cache operations
    pub async fn set_json<T: serde::Serialize>(&self, key: &str, value: &T, ttl: u64) -> Result<()> {
        let json = serde_json::to_string(value)?;
        self.set_with_expiry(key, &json, ttl).await
    }

    pub async fn get_json<T: serde::de::DeserializeOwned>(&self, key: &str) -> Result<Option<T>> {
        match self.get(key).await? {
            Some(data) => Ok(Some(serde_json::from_str(&data)?)),
            None => Ok(None),
        }
    }

    // Specialized cache operations for SharedAnalysis
    pub async fn set_cached_analysis(&self, key: &str, value: &crate::models::SharedAnalysis, ttl: u64) -> Result<()> {
        self.set_json(key, value, ttl).await
    }

    pub async fn get_cached_analysis(&self, key: &str) -> Result<Option<crate::models::SharedAnalysis>> {
        self.get_json(key).await
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    // Integration tests would go here
    // They require a running Redis instance
}