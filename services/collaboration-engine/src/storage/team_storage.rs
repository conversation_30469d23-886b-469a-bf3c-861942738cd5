use crate::{
    config::Config,
    models::{
        Team, TeamMember, TeamRole, TeamSettings, CreateTeamRequest, UpdateTeamRequest,
        UserId, Timestamp, UserInfo,
    },
};
use anyhow::{anyhow, Result};
use google_cloud_spanner::{
    client::Client,
    key::Key,
    mutation::{delete, insert, update},
    statement::Statement,
    reader::AsyncIterator,
};
use serde_json;
use std::collections::HashMap;
use tracing::{debug, error, info};
use uuid::Uuid;

#[derive(Clone)]
pub struct TeamStorage {
    client: Client,
}

impl TeamStorage {
    pub fn new(client: Client) -> Self {
        Self { client }
    }

    pub async fn create_team(
        &self,
        request: CreateTeamRequest,
        creator_id: &UserId,
        creator_info: UserInfo,
    ) -> Result<Team> {
        let team_id = Uuid::new_v4().to_string();
        let now = Timestamp::now();
        
        // Create team with settings
        let settings = request.settings.unwrap_or_default();
        
        // Create team
        let mut team = Team {
            id: team_id.clone(),
            name: request.name,
            description: request.description,
            created_by: creator_id.clone(),
            created_at: now.clone(),
            updated_at: now.clone(),
            members: HashMap::new(),
            settings,
            metadata: HashMap::new(),
        };
        
        // Add creator as owner
        let creator_member = TeamMember {
            user_id: creator_id.clone(),
            email: creator_info.email,
            name: creator_info.name,
            role: TeamRole::Owner,
            joined_at: now.clone(),
            last_active: Some(now.clone()),
            permissions: TeamRole::Owner.default_permissions(),
        };
        
        team.members.insert(creator_id.as_ref().to_string(), creator_member.clone());
        
        // Start transaction
        let team_id_clone = team_id.clone();
        let creator_id_str = creator_id.as_ref().to_string();
        let team_name = team.name.clone();
        let team_description = team.description.clone().unwrap_or_default();
        let team_created_at = team.created_at.0.to_rfc3339();
        let team_updated_at = team.updated_at.0.to_rfc3339();
        let team_settings_json = serde_json::to_string(&team.settings)?;
        let member_email = creator_member.email.clone();
        let member_name = creator_member.name.clone();
        let member_role = format!("{:?}", creator_member.role).to_lowercase();
        let member_joined_at = creator_member.joined_at.0.to_rfc3339();
        let member_permissions_json = serde_json::to_string(&creator_member.permissions)?;
        
        let (_, _) = self.client.read_write_transaction(|tx| {
            Box::pin(async move {
                // Insert team
                let team_mutation = insert(
                    "teams",
                    &["id", "name", "description", "created_by", "created_at", "updated_at", "settings"],
                    &[&[
                        &team_id_clone.clone(),
                        &team_name,
                        &team_description,
                        &creator_id_str.clone(),
                        &team_created_at,
                        &team_updated_at,
                        &team_settings_json,
                    ]],
                );
                tx.buffer_write(vec![team_mutation]);
                
                // Insert team member
                let member_id = Uuid::new_v4().to_string();
                let member_mutation = insert(
                    "team_members",
                    &["id", "team_id", "user_id", "email", "name", "role", "joined_at", "permissions"],
                    &[&[
                        &member_id,
                        &team_id_clone,
                        &creator_id_str,
                        &member_email,
                        &member_name,
                        &member_role,
                        &member_joined_at,
                        &member_permissions_json,
                    ]],
                );
                tx.buffer_write(vec![member_mutation]);
                
                Ok::<(), google_cloud_spanner::client::Error>(())
            })
        }).await?;
        
        info!("Created team {} with owner {}", team.id, creator_id.as_ref());
        
        Ok(team)
    }

    pub async fn get_team(&self, team_id: &str, user_id: &UserId) -> Result<Option<Team>> {
        // First check if user is a member
        let is_member = self.is_team_member(team_id, user_id).await?;
        if !is_member {
            return Ok(None);
        }
        
        // Get team details
        let mut stmt = Statement::new(
            "SELECT id, name, description, created_by, created_at, updated_at, settings
             FROM teams WHERE id = @team_id"
        );
        stmt.add_param("team_id", &team_id);
        
        let mut result_set = self.client
            .single()
            .execute_query(stmt)
            .await?;
        
        if let Some(row) = result_set.next().await? {
            // Get team members
            let members = self.get_team_members(team_id).await?;
            
            let team = Team {
                id: row.column_by_name::<String>("id")?,
                name: row.column_by_name::<String>("name")?,
                description: Some(row.column_by_name::<String>("description")?).filter(|s| !s.is_empty()),
                created_by: UserId::from_string(row.column_by_name::<String>("created_by")?),
                created_at: Timestamp(chrono::DateTime::parse_from_rfc3339(
                    &row.column_by_name::<String>("created_at")?
                )?.into()),
                updated_at: Timestamp(chrono::DateTime::parse_from_rfc3339(
                    &row.column_by_name::<String>("updated_at")?
                )?.into()),
                members,
                settings: serde_json::from_str(&row.column_by_name::<String>("settings")?)?,
                metadata: HashMap::new(),
            };
            
            Ok(Some(team))
        } else {
            Ok(None)
        }
    }

    pub async fn list_user_teams(&self, user_id: &UserId) -> Result<Vec<Team>> {
        let mut stmt = Statement::new(
            "SELECT t.id, t.name, t.description, t.created_by, t.created_at, t.updated_at, t.settings
             FROM teams t
             JOIN team_members tm ON t.id = tm.team_id
             WHERE tm.user_id = @user_id
             ORDER BY tm.joined_at DESC"
        );
        stmt.add_param("user_id", user_id.as_ref());
        
        let mut result_set = self.client
            .single()
            .execute_query(stmt)
            .await?;
        
        let mut teams = Vec::new();
        while let Some(row) = result_set.next().await? {
            let team_id = row.column_by_name::<String>("id")?;
            let members = self.get_team_members(&team_id).await?;
            
            let team = Team {
                id: team_id,
                name: row.column_by_name::<String>("name")?,
                description: Some(row.column_by_name::<String>("description")?).filter(|s| !s.is_empty()),
                created_by: UserId::from_string(row.column_by_name::<String>("created_by")?),
                created_at: Timestamp(chrono::DateTime::parse_from_rfc3339(
                    &row.column_by_name::<String>("created_at")?
                )?.into()),
                updated_at: Timestamp(chrono::DateTime::parse_from_rfc3339(
                    &row.column_by_name::<String>("updated_at")?
                )?.into()),
                members,
                settings: serde_json::from_str(&row.column_by_name::<String>("settings")?)?,
                metadata: HashMap::new(),
            };
            teams.push(team);
        }
        
        Ok(teams)
    }

    pub async fn update_team(
        &self,
        team_id: &str,
        user_id: &UserId,
        request: UpdateTeamRequest,
    ) -> Result<()> {
        // Check permissions
        let member = self.get_team_member(team_id, user_id).await?
            .ok_or_else(|| anyhow!("User is not a member of this team"))?;
        
        if !member.permissions.can_manage_team {
            return Err(anyhow!("Insufficient permissions to update team"));
        }
        
        let now = Timestamp::now();
        
        // Build update SQL
        let mut sql_parts = vec!["UPDATE teams SET updated_at = @updated_at"];
        let mut statement = Statement::new("");
        
        if request.name.is_some() {
            sql_parts.push(", name = @name");
        }
        if request.description.is_some() {
            sql_parts.push(", description = @description");
        }
        if request.settings.is_some() {
            sql_parts.push(", settings = @settings");
        }
        
        sql_parts.push(" WHERE id = @id");
        let sql = sql_parts.join("");
        
        statement = Statement::new(&sql);
        statement.add_param("id", team_id);
        statement.add_param("updated_at", &now.0.to_rfc3339());
        
        if let Some(name) = &request.name {
            statement.add_param("name", name);
        }
        if let Some(description) = &request.description {
            statement.add_param("description", description);
        }
        if let Some(settings) = &request.settings {
            let settings_json = serde_json::to_string(settings)?;
            statement.add_param("settings", &settings_json);
        }
        
        let (_, _) = self.client.read_write_transaction(|tx| {
            Box::pin(async move {
                // Can't use Statement in buffer_write, need to use execute_update
                let _rows = tx.execute_update(statement).await?;
                Ok::<(), google_cloud_spanner::client::Error>(())
            })
        }).await?;
        
        info!("Updated team {} by user {}", team_id, user_id.as_ref());
        
        Ok(())
    }

    pub async fn delete_team(&self, team_id: &str, user_id: &UserId) -> Result<()> {
        // Check if user is owner
        let member = self.get_team_member(team_id, user_id).await?
            .ok_or_else(|| anyhow!("User is not a member of this team"))?;
        
        if member.role != TeamRole::Owner {
            return Err(anyhow!("Only team owners can delete teams"));
        }
        
        // Delete team members first (foreign key constraint)
        let mut delete_members_stmt = Statement::new(
            "DELETE FROM team_members WHERE team_id = @team_id"
        );
        delete_members_stmt.add_param("team_id", &team_id);
        
        let team_id_str = team_id.to_string();
        let (_, _) = self.client.read_write_transaction(|tx| {
            Box::pin(async move {
                // Can't use Statement in buffer_write, need to use execute_update
                let _rows = tx.execute_update(delete_members_stmt).await?;
                // Delete team
                tx.buffer_write(vec![delete(
                    "teams",
                    Key::new(&[&team_id_str]),
                )]);
                Ok::<(), google_cloud_spanner::client::Error>(())
            })
        }).await?;
        
        info!("Deleted team {} by owner {}", team_id, user_id.as_ref());
        
        Ok(())
    }

    async fn get_team_members(&self, team_id: &str) -> Result<HashMap<String, TeamMember>> {
        let mut stmt = Statement::new(
            "SELECT user_id, email, name, role, joined_at, permissions
             FROM team_members
             WHERE team_id = @team_id
             ORDER BY joined_at ASC"
        );
        stmt.add_param("team_id", &team_id);
        
        let mut result_set = self.client
            .single()
            .execute_query(stmt)
            .await?;
        
        let mut members = HashMap::new();
        while let Some(row) = result_set.next().await? {
            let user_id = row.column_by_name::<String>("user_id")?;
            let role_str = row.column_by_name::<String>("role")?;
            let role = match role_str.as_str() {
                "owner" => TeamRole::Owner,
                "admin" => TeamRole::Admin,
                "member" => TeamRole::Member,
                "viewer" => TeamRole::Viewer,
                _ => TeamRole::Member,
            };
            
            let member = TeamMember {
                user_id: UserId::from_string(user_id.clone()),
                email: row.column_by_name::<String>("email")?,
                name: row.column_by_name::<String>("name")?,
                role,
                joined_at: Timestamp(chrono::DateTime::parse_from_rfc3339(
                    &row.column_by_name::<String>("joined_at")?
                )?.into()),
                last_active: None,
                permissions: serde_json::from_str(&row.column_by_name::<String>("permissions")?)?,
            };
            
            members.insert(user_id, member);
        }
        
        Ok(members)
    }

    async fn get_team_member(&self, team_id: &str, user_id: &UserId) -> Result<Option<TeamMember>> {
        let mut stmt = Statement::new(
            "SELECT email, name, role, joined_at, permissions
             FROM team_members
             WHERE team_id = @team_id AND user_id = @user_id"
        );
        stmt.add_param("team_id", &team_id);
        stmt.add_param("user_id", user_id.as_ref());
        
        let mut result_set = self.client
            .single()
            .execute_query(stmt)
            .await?;
        
        if let Some(row) = result_set.next().await? {
            let role_str = row.column_by_name::<String>("role")?;
            let role = match role_str.as_str() {
                "owner" => TeamRole::Owner,
                "admin" => TeamRole::Admin,
                "member" => TeamRole::Member,
                "viewer" => TeamRole::Viewer,
                _ => TeamRole::Member,
            };
            
            let member = TeamMember {
                user_id: user_id.clone(),
                email: row.column_by_name::<String>("email")?,
                name: row.column_by_name::<String>("name")?,
                role,
                joined_at: Timestamp(chrono::DateTime::parse_from_rfc3339(
                    &row.column_by_name::<String>("joined_at")?
                )?.into()),
                last_active: None,
                permissions: serde_json::from_str(&row.column_by_name::<String>("permissions")?)?,
            };
            
            Ok(Some(member))
        } else {
            Ok(None)
        }
    }

    async fn is_team_member(&self, team_id: &str, user_id: &UserId) -> Result<bool> {
        let mut stmt = Statement::new(
            "SELECT COUNT(*) as count
             FROM team_members
             WHERE team_id = @team_id AND user_id = @user_id"
        );
        stmt.add_param("team_id", &team_id);
        stmt.add_param("user_id", user_id.as_ref());
        
        let mut result_set = self.client
            .single()
            .execute_query(stmt)
            .await?;
        
        if let Some(row) = result_set.next().await? {
            let count: i64 = row.column_by_name("count")?;
            Ok(count > 0)
        } else {
            Ok(false)
        }
    }

    pub async fn add_team_member(
        &self,
        team_id: &str,
        requester_id: &UserId,
        new_member: UserInfo,
        role: TeamRole,
    ) -> Result<TeamMember> {
        // Check permissions
        let requester = self.get_team_member(team_id, requester_id).await?
            .ok_or_else(|| anyhow!("Requester is not a member of this team"))?;
        
        if !requester.permissions.can_invite_members {
            return Err(anyhow!("Insufficient permissions to add members"));
        }
        
        // Check if user is already a member
        if self.is_team_member(team_id, &new_member.user_id).await? {
            return Err(anyhow!("User is already a member of this team"));
        }
        
        let now = Timestamp::now();
        let member = TeamMember {
            user_id: new_member.user_id.clone(),
            email: new_member.email,
            name: new_member.name,
            role,
            joined_at: now.clone(),
            last_active: None,
            permissions: role.default_permissions(),
        };
        
        // Insert new member
        let team_id_str = team_id.to_string();
        let user_id_str = member.user_id.as_ref().to_string();
        let email = member.email.clone();
        let name = member.name.clone();
        let role_str = format!("{:?}", member.role).to_lowercase();
        let joined_at_str = member.joined_at.0.to_rfc3339();
        let permissions_json = serde_json::to_string(&member.permissions)?;
        
        let (_, _) = self.client.read_write_transaction(|tx| {
            Box::pin(async move {
                let member_id = Uuid::new_v4().to_string();
                let member_mutation = insert(
                    "team_members",
                    &["id", "team_id", "user_id", "email", "name", "role", "joined_at", "permissions"],
                    &[&[
                        &member_id,
                        &team_id_str,
                        &user_id_str,
                        &email,
                        &name,
                        &role_str,
                        &joined_at_str,
                        &permissions_json,
                    ]],
                );
                tx.buffer_write(vec![member_mutation]);
                Ok::<(), google_cloud_spanner::client::Error>(())
            })
        }).await?;
        
        info!("Added member {} to team {} with role {:?}", 
              member.user_id.as_ref(), team_id, member.role);
        
        Ok(member)
    }

    pub async fn remove_team_member(
        &self,
        team_id: &str,
        requester_id: &UserId,
        member_id: &UserId,
    ) -> Result<()> {
        // Check permissions
        let requester = self.get_team_member(team_id, requester_id).await?
            .ok_or_else(|| anyhow!("Requester is not a member of this team"))?;
        
        if !requester.permissions.can_remove_members {
            return Err(anyhow!("Insufficient permissions to remove members"));
        }
        
        // Can't remove the owner
        let member = self.get_team_member(team_id, member_id).await?
            .ok_or_else(|| anyhow!("Member not found in team"))?;
        
        if member.role == TeamRole::Owner {
            return Err(anyhow!("Cannot remove team owner"));
        }
        
        // Delete member
        let mut delete_stmt = Statement::new(
            "DELETE FROM team_members WHERE team_id = @team_id AND user_id = @user_id"
        );
        delete_stmt.add_param("team_id", &team_id);
        delete_stmt.add_param("user_id", member_id.as_ref());
        
        let (_, _) = self.client.read_write_transaction(|tx| {
            Box::pin(async move {
                // Can't use Statement in buffer_write, need to use execute_update
                let _rows = tx.execute_update(delete_stmt).await?;
                Ok::<(), google_cloud_spanner::client::Error>(())
            })
        }).await?;
        
        info!("Removed member {} from team {}", member_id.as_ref(), team_id);
        
        Ok(())
    }

    pub async fn update_member_role(
        &self,
        team_id: &str,
        requester_id: &UserId,
        member_id: &UserId,
        new_role: TeamRole,
    ) -> Result<()> {
        // Check permissions
        let requester = self.get_team_member(team_id, requester_id).await?
            .ok_or_else(|| anyhow!("Requester is not a member of this team"))?;
        
        if !requester.permissions.can_manage_team {
            return Err(anyhow!("Insufficient permissions to update member roles"));
        }
        
        // Can't change owner role
        let member = self.get_team_member(team_id, member_id).await?
            .ok_or_else(|| anyhow!("Member not found in team"))?;
        
        if member.role == TeamRole::Owner {
            return Err(anyhow!("Cannot change team owner's role"));
        }
        
        // Update role
        let mut update_stmt = Statement::new(
            "UPDATE team_members 
             SET role = @role, permissions = @permissions
             WHERE team_id = @team_id AND user_id = @user_id"
        );
        update_stmt.add_param("role", &format!("{:?}", new_role).to_lowercase());
        update_stmt.add_param("permissions", &serde_json::to_string(&new_role.default_permissions())?);
        update_stmt.add_param("team_id", &team_id);
        update_stmt.add_param("user_id", member_id.as_ref());
        
        let (_, _) = self.client.read_write_transaction(|tx| {
            Box::pin(async move {
                // Can't use Statement in buffer_write, need to use execute_update
                let _rows = tx.execute_update(update_stmt).await?;
                Ok::<(), google_cloud_spanner::client::Error>(())
            })
        }).await?;
        
        info!("Updated role for member {} in team {} to {:?}", 
              member_id.as_ref(), team_id, new_role);
        
        Ok(())
    }
}