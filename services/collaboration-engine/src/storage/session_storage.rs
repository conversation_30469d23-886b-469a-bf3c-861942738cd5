use crate::{
    models::{
        Session, SessionStatus, SessionParticipantInfo, SessionSettings, ParticipantStatus,
        CreateSessionRequest, UpdateSessionRequest,
        Message, MessageContent, UserId, Timestamp, UserInfo,
    },
};
use anyhow::{anyhow, Result};
use google_cloud_spanner::{
    client::Client,
    key::Key,
    mutation::{delete, insert, update},
    statement::Statement,
    reader::AsyncIterator,
};
use serde_json;
use std::collections::HashMap;
use tracing::{debug, error, info};
use uuid::Uuid;

#[derive(Clone)]
pub struct SessionStorage {
    client: Client,
}

impl SessionStorage {
    pub fn new(client: Client) -> Self {
        Self { client }
    }

    pub async fn create_session(
        &self,
        request: CreateSessionRequest,
        creator_id: &UserId,
        creator_info: UserInfo,
    ) -> Result<Session> {
        let session_id = Uuid::new_v4().to_string();
        let now = Timestamp::now();
        
        // Create session
        let mut session = Session {
            id: session_id.clone(),
            team_id: request.team_id,
            name: request.name,
            description: request.description,
            created_by: creator_id.clone(),
            created_at: now.clone(),
            updated_at: now.clone(),
            ended_at: None,
            status: SessionStatus::Active,
            participants: std::collections::HashSet::new(),
            settings: request.settings.unwrap_or_default(),
            metadata: HashMap::new(),
        };
        
        // Add creator as participant
        session.participants.insert(creator_id.clone());
        
        // Start transaction
        let session_id_clone = session_id.clone();
        let team_id = session.team_id.clone();
        let name = session.name.clone();
        let description = session.description.clone().unwrap_or_default();
        let created_by = creator_id.as_ref().to_string();
        let created_at = session.created_at.0.to_rfc3339();
        let updated_at = session.updated_at.0.to_rfc3339();
        let status = format!("{:?}", session.status).to_lowercase();
        let settings_json = serde_json::to_string(&session.settings)?;
        let participant_id = Uuid::new_v4().to_string();
        let email = creator_info.email.clone();
        let creator_name = creator_info.name.clone();
        let joined_at = now.0.to_rfc3339();
        let participant_status = format!("{:?}", ParticipantStatus::Active).to_lowercase();
        
        let (_, _) = self.client.read_write_transaction(|tx| {
            let session_id = session_id_clone.clone();
            let team_id = team_id.clone();
            let name = name.clone();
            let description = description.clone();
            let created_by = created_by.clone();
            let created_at = created_at.clone();
            let updated_at = updated_at.clone();
            let status = status.clone();
            let settings_json = settings_json.clone();
            let participant_id = participant_id.clone();
            let email = email.clone();
            let creator_name = creator_name.clone();
            let joined_at = joined_at.clone();
            let participant_status = participant_status.clone();
            
            Box::pin(async move {
                // Insert session
                let session_mutation = insert(
                    "sessions",
                    &["id", "team_id", "name", "description", "created_by", "created_at", "updated_at", "status", "settings"],
                    &[&[
                        &session_id.clone(),
                        &team_id,
                        &name,
                        &description,
                        &created_by.clone(),
                        &created_at,
                        &updated_at,
                        &status,
                        &settings_json,
                    ]],
                );
                
                // Insert session participant
                let participant_mutation = insert(
                    "session_participants",
                    &["id", "session_id", "user_id", "email", "name", "joined_at", "status"],
                    &[&[
                        &participant_id,
                        &session_id,
                        &created_by,
                        &email,
                        &creator_name,
                        &joined_at,
                        &participant_status,
                    ]],
                );
                
                tx.buffer_write(vec![session_mutation, participant_mutation]);
                Ok::<(), google_cloud_spanner::client::Error>(())
            })
        }).await?;
        
        info!("Created session {} with creator {}", session.id, creator_id.as_ref());
        
        Ok(session)
    }

    pub async fn get_session(&self, session_id: &str) -> Result<Option<Session>> {
        // Get session details
        let mut stmt = Statement::new(
            "SELECT id, team_id, name, description, created_by, created_at, updated_at, ended_at, status, settings
             FROM sessions WHERE id = @session_id"
        );
        stmt.add_param("session_id", &session_id);
        
        let mut transaction = self.client
            .single()
            .await?;
        let mut result_set = transaction
            .query(stmt)
            .await?;
        
        if let Some(row) = result_set.next().await? {
            // Get session participants
            let participants = self.get_session_participant_ids(session_id).await?;
            
            let status_str = row.column_by_name::<String>("status")?;
            let status = match status_str.as_str() {
                "active" => SessionStatus::Active,
                "paused" => SessionStatus::Paused,
                "ended" => SessionStatus::Ended,
                "archived" => SessionStatus::Archived,
                _ => SessionStatus::Active,
            };
            
            let session = Session {
                id: row.column_by_name::<String>("id")?,
                team_id: row.column_by_name::<String>("team_id")?,
                name: row.column_by_name::<String>("name")?,
                description: Some(row.column_by_name::<String>("description")?).filter(|s| !s.is_empty()),
                created_by: UserId::from_string(row.column_by_name::<String>("created_by")?),
                created_at: Timestamp(chrono::DateTime::parse_from_rfc3339(
                    &row.column_by_name::<String>("created_at")?
                )?.into()),
                updated_at: Timestamp(chrono::DateTime::parse_from_rfc3339(
                    &row.column_by_name::<String>("updated_at")?
                )?.into()),
                ended_at: row.column_by_name::<Option<String>>("ended_at")?
                    .map(|s| Timestamp(chrono::DateTime::parse_from_rfc3339(&s).unwrap().into())),
                status,
                participants,
                settings: serde_json::from_str(&row.column_by_name::<String>("settings")?)?,
                metadata: HashMap::new(),
            };
            
            Ok(Some(session))
        } else {
            Ok(None)
        }
    }

    pub async fn list_team_sessions(&self, team_id: &str) -> Result<Vec<Session>> {
        let mut stmt = Statement::new(
            "SELECT id, team_id, name, description, created_by, created_at, updated_at, ended_at, status, settings
             FROM sessions
             WHERE team_id = @team_id
             ORDER BY created_at DESC"
        );
        stmt.add_param("team_id", &team_id);
        
        let mut transaction = self.client
            .single()
            .await?;
        let mut result_set = transaction
            .query(stmt)
            .await?;
        
        let mut sessions = Vec::new();
        while let Some(row) = result_set.next().await? {
            let session_id = row.column_by_name::<String>("id")?;
            let participants = self.get_session_participant_ids(&session_id).await?;
            
            let status_str = row.column_by_name::<String>("status")?;
            let status = match status_str.as_str() {
                "active" => SessionStatus::Active,
                "paused" => SessionStatus::Paused,
                "ended" => SessionStatus::Ended,
                "archived" => SessionStatus::Archived,
                _ => SessionStatus::Active,
            };
            
            let session = Session {
                id: session_id,
                team_id: row.column_by_name::<String>("team_id")?,
                name: row.column_by_name::<String>("name")?,
                description: Some(row.column_by_name::<String>("description")?).filter(|s| !s.is_empty()),
                created_by: UserId::from_string(row.column_by_name::<String>("created_by")?),
                created_at: Timestamp(chrono::DateTime::parse_from_rfc3339(
                    &row.column_by_name::<String>("created_at")?
                )?.into()),
                updated_at: Timestamp(chrono::DateTime::parse_from_rfc3339(
                    &row.column_by_name::<String>("updated_at")?
                )?.into()),
                ended_at: row.column_by_name::<Option<String>>("ended_at")?
                    .map(|s| Timestamp(chrono::DateTime::parse_from_rfc3339(&s).unwrap().into())),
                status,
                participants,
                settings: serde_json::from_str(&row.column_by_name::<String>("settings")?)?,
                metadata: HashMap::new(),
            };
            sessions.push(session);
        }
        
        Ok(sessions)
    }

    pub async fn update_session(
        &self,
        session_id: &str,
        user_id: &UserId,
        request: UpdateSessionRequest,
    ) -> Result<()> {
        // Check if user is a participant
        if !self.is_session_participant(session_id, user_id).await? {
            return Err(anyhow!("User is not a participant in this session"));
        }
        
        let now = Timestamp::now();
        
        // Build update SQL
        let mut sql_parts = vec!["UPDATE sessions SET updated_at = @updated_at"];
        let mut statement = Statement::new("");
        
        if request.name.is_some() {
            sql_parts.push(", name = @name");
        }
        if request.description.is_some() {
            sql_parts.push(", description = @description");
        }
        if request.status.is_some() {
            sql_parts.push(", status = @status");
        }
        
        sql_parts.push(" WHERE id = @id");
        let sql = sql_parts.join("");
        
        statement = Statement::new(&sql);
        statement.add_param("id", session_id);
        statement.add_param("updated_at", &now.0.to_rfc3339());
        
        if let Some(name) = &request.name {
            statement.add_param("name", name);
        }
        if let Some(description) = &request.description {
            statement.add_param("description", description);
        }
        if let Some(status) = &request.status {
            statement.add_param("status", &format!("{:?}", status).to_lowercase());
        }
        
        let (_, _) = self.client.read_write_transaction(|tx| {
            Box::pin(async move {
                tx.buffer_write(vec![statement]);
                Ok::<(), google_cloud_spanner::client::Error>(())
            })
        }).await?;
        
        info!("Updated session {} by user {}", session_id, user_id.as_ref());
        
        Ok(())
    }

    pub async fn end_session(&self, session_id: &str, user_id: &UserId) -> Result<()> {
        // Check if user is the creator
        let session = self.get_session(session_id).await?
            .ok_or_else(|| anyhow!("Session not found"))?;
        
        if session.created_by.as_ref() != user_id.as_ref() {
            return Err(anyhow!("Only session creator can end the session"));
        }
        
        let now = Timestamp::now();
        
        // Update session status
        let mut end_stmt = Statement::new(
            "UPDATE sessions SET updated_at = @updated_at, ended_at = @ended_at, status = @status WHERE id = @id"
        );
        end_stmt.add_param("id", session_id);
        end_stmt.add_param("updated_at", &now.0.to_rfc3339());
        end_stmt.add_param("ended_at", &now.0.to_rfc3339());
        end_stmt.add_param("status", &"ended");
        
        let (_, _) = self.client.read_write_transaction(|tx| {
            Box::pin(async move {
                tx.buffer_write(vec![end_stmt]);
                Ok::<(), google_cloud_spanner::client::Error>(())
            })
        }).await?;
        
        info!("Ended session {} by user {}", session_id, user_id.as_ref());
        
        Ok(())
    }

    async fn get_session_participant_ids(&self, session_id: &str) -> Result<std::collections::HashSet<UserId>> {
        let mut stmt = Statement::new(
            "SELECT user_id FROM session_participants WHERE session_id = @session_id"
        );
        stmt.add_param("session_id", &session_id);
        
        let mut transaction = self.client
            .single()
            .await?;
        let mut result_set = transaction
            .query(stmt)
            .await?;
        
        let mut participants = std::collections::HashSet::new();
        while let Some(row) = result_set.next().await? {
            let user_id = row.column_by_name::<String>("user_id")?;
            participants.insert(UserId::from_string(user_id));
        }
        
        Ok(participants)
    }

    pub async fn get_session_participants(&self, session_id: &str) -> Result<Vec<SessionParticipantInfo>> {
        let mut stmt = Statement::new(
            "SELECT user_id, email, name, joined_at, status
             FROM session_participants
             WHERE session_id = @session_id
             ORDER BY joined_at ASC"
        );
        stmt.add_param("session_id", &session_id);
        
        let mut transaction = self.client
            .single()
            .await?;
        let mut result_set = transaction
            .query(stmt)
            .await?;
        
        let mut participants = Vec::new();
        while let Some(row) = result_set.next().await? {
            let status_str = row.column_by_name::<String>("status")?;
            let status = match status_str.as_str() {
                "active" => ParticipantStatus::Active,
                "idle" => ParticipantStatus::Idle,
                "disconnected" => ParticipantStatus::Disconnected,
                _ => ParticipantStatus::Disconnected,
            };
            
            let participant = SessionParticipantInfo {
                user_id: UserId::from_string(row.column_by_name::<String>("user_id")?),
                email: row.column_by_name::<String>("email")?,
                name: row.column_by_name::<String>("name")?,
                status,
                joined_at: Timestamp(chrono::DateTime::parse_from_rfc3339(
                    &row.column_by_name::<String>("joined_at")?
                )?.into()),
                last_active: Timestamp::now(), // Would be fetched from separate tracking
            };
            
            participants.push(participant);
        }
        
        Ok(participants)
    }

    async fn is_session_participant(&self, session_id: &str, user_id: &UserId) -> Result<bool> {
        let mut stmt = Statement::new(
            "SELECT COUNT(*) as count
             FROM session_participants
             WHERE session_id = @session_id AND user_id = @user_id"
        );
        stmt.add_param("session_id", &session_id);
        stmt.add_param("user_id", &user_id.as_ref());
        
        let mut transaction = self.client
            .single()
            .await?;
        let mut result_set = transaction
            .query(stmt)
            .await?;
        
        if let Some(row) = result_set.next().await? {
            let count: i64 = row.column_by_name("count")?;
            Ok(count > 0)
        } else {
            Ok(false)
        }
    }

    pub async fn add_participant(
        &self,
        session_id: &str,
        user_info: UserInfo,
    ) -> Result<SessionParticipantInfo> {
        let now = Timestamp::now();
        
        let participant = SessionParticipantInfo {
            user_id: user_info.user_id.clone(),
            email: user_info.email,
            name: user_info.name,
            status: ParticipantStatus::Active,
            joined_at: now.clone(),
            last_active: now.clone(),
        };
        
        // Insert participant
        let session_id_str = session_id.to_string();
        let user_id_str = participant.user_id.as_ref().to_string();
        let email = participant.email.clone();
        let name = participant.name.clone();
        let joined_at_str = participant.joined_at.0.to_rfc3339();
        let status_str = format!("{:?}", participant.status).to_lowercase();
        
        let (_, _) = self.client.read_write_transaction(|tx| {
            Box::pin(async move {
                let participant_id = Uuid::new_v4().to_string();
                tx.buffer_write(vec![insert(
                    "session_participants",
                    &["id", "session_id", "user_id", "email", "name", "joined_at", "status"],
                    &[&[
                        &participant_id,
                        &session_id_str,
                        &user_id_str,
                        &email,
                        &name,
                        &joined_at_str,
                        &status_str,
                    ]],
                )]);
                Ok::<(), google_cloud_spanner::client::Error>(())
            })
        }).await?;
        
        info!("Added participant {} to session {}", participant.user_id.as_ref(), session_id);
        
        Ok(participant)
    }

    pub async fn remove_participant(
        &self,
        session_id: &str,
        user_id: &UserId,
    ) -> Result<()> {
        let mut delete_stmt = Statement::new(
            "DELETE FROM session_participants WHERE session_id = @session_id AND user_id = @user_id"
        );
        delete_stmt.add_param("session_id", &session_id);
        delete_stmt.add_param("user_id", &user_id.as_ref());
        
        let (_, _) = self.client.read_write_transaction(|tx| {
            Box::pin(async move {
                tx.buffer_write(vec![delete_stmt]);
                Ok::<(), google_cloud_spanner::client::Error>(())
            })
        }).await?;
        
        info!("Removed participant {} from session {}", user_id.as_ref(), session_id);
        
        Ok(())
    }

    pub async fn get_session_messages(
        &self,
        session_id: &str,
        limit: usize,
        before: Option<String>,
    ) -> Result<Vec<Message>> {
        let mut sql = r#"
            SELECT id, session_id, user_id, content, created_at, edited, deleted, metadata
            FROM messages
            WHERE session_id = @session_id AND deleted = false
        "#.to_string();
        
        if before.is_some() {
            sql.push_str(" AND created_at < @before");
        }
        
        sql.push_str(" ORDER BY created_at DESC LIMIT @limit");
        
        let mut stmt = Statement::new(&sql);
        stmt.add_param("session_id", &session_id);
        stmt.add_param("limit", &(limit as i64));
        
        if let Some(before_time) = before {
            stmt.add_param("before", &before_time);
        }
        
        let mut transaction = self.client
            .single()
            .await?;
        let mut result_set = transaction
            .query(stmt)
            .await?;
        
        let mut messages = Vec::new();
        while let Some(row) = result_set.next().await? {
            let message = Message {
                id: row.column_by_name::<String>("id")?,
                session_id: row.column_by_name::<String>("session_id")?,
                user_id: UserId::from_string(row.column_by_name::<String>("user_id")?),
                content: serde_json::from_str(&row.column_by_name::<String>("content")?)?,
                created_at: Timestamp(chrono::DateTime::parse_from_rfc3339(
                    &row.column_by_name::<String>("created_at")?
                )?.into()),
                updated_at: None, // Would need to be stored separately
                edited: row.column_by_name::<bool>("edited")?,
                deleted: row.column_by_name::<bool>("deleted")?,
                metadata: serde_json::from_str(&row.column_by_name::<String>("metadata")?)?,
            };
            messages.push(message);
        }
        
        // Reverse to get chronological order
        messages.reverse();
        
        Ok(messages)
    }

    pub async fn save_message(&self, message: &Message) -> Result<()> {
        let message_id = message.id.clone();
        let session_id = message.session_id.clone();
        let user_id_str = message.user_id.as_ref().to_string();
        let content_json = serde_json::to_string(&message.content)?;
        let created_at_str = message.created_at.0.to_rfc3339();
        let edited = message.edited.to_string();
        let deleted = message.deleted.to_string();
        let metadata_json = serde_json::to_string(&message.metadata)?;
        
        let (_, _) = self.client.read_write_transaction(|tx| {
            Box::pin(async move {
                tx.buffer_write(vec![insert(
                    "messages",
                    &["id", "session_id", "user_id", "content", "created_at", "edited", "deleted", "metadata"],
                    &[&[
                        &message_id,
                        &session_id,
                        &user_id_str,
                        &content_json,
                        &created_at_str,
                        &edited,
                        &deleted,
                        &metadata_json,
                    ]],
                )]);
                Ok::<(), google_cloud_spanner::client::Error>(())
            })
        }).await?;
        
        info!("Saved message {} in session {}", message.id, message.session_id);
        
        Ok(())
    }
}