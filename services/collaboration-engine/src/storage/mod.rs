pub mod redis_client;
pub mod redis_pubsub;
pub mod spanner_client;
pub mod team_storage;
pub mod session_storage;
pub mod integration_storage;

pub use team_storage::TeamStorage;
pub use session_storage::SessionStorage;
pub use integration_storage::IntegrationStorage;

use crate::config::Config;
use anyhow::Result;
use std::sync::Arc;

pub struct StorageClients {
    pub redis: Arc<redis_client::RedisClient>,
    pub spanner: Arc<spanner_client::SpannerClient>,
    pub teams: Arc<team_storage::TeamStorage>,
    pub sessions: Arc<session_storage::SessionStorage>,
    pub start_time: u64,
}

pub async fn create_storage_clients(config: &Config) -> Result<StorageClients> {
    // Initialize Redis client
    let redis = Arc::new(redis_client::RedisClient::new(config).await?);

    // Initialize Spanner client
    let spanner = Arc::new(spanner_client::SpannerClient::new(config).await?);

    // Initialize team storage
    let teams = Arc::new(team_storage::TeamStorage::new(spanner.client.clone()));

    // Initialize session storage
    let sessions = Arc::new(session_storage::SessionStorage::new(spanner.client.clone()));

    // Record start time for uptime tracking
    let start_time = std::time::SystemTime::now()
        .duration_since(std::time::UNIX_EPOCH)
        .unwrap()
        .as_secs();

    Ok(StorageClients {
        redis,
        spanner,
        teams,
        sessions,
        start_time,
    })
}