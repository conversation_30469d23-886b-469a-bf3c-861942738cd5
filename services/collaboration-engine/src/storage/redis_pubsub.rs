use crate::storage::redis_client::RedisClient;
use anyhow::{Context, Result};
use redis::aio::PubSub;
use std::sync::Arc;
use tokio::sync::mpsc;
use tracing::{debug, error, info, warn};

pub struct RedisPubSubClient {
    redis_client: Arc<RedisClient>,
}

impl RedisPubSubClient {
    pub fn new(redis_client: Arc<RedisClient>) -> Self {
        Self { redis_client }
    }

    // Publish a message to a channel
    pub async fn publish<T: serde::Serialize>(
        &self,
        channel: &str,
        message: &T,
    ) -> Result<()> {
        let mut conn = self.redis_client.get_connection().await?;
        let payload = serde_json::to_string(message)
            .context("Failed to serialize message")?;

        let _: i32 = redis::cmd("PUBLISH")
            .arg(channel)
            .arg(&payload)
            .query_async(&mut conn)
            .await
            .context("Failed to publish message")?;

        debug!("Published message to channel: {}", channel);
        Ok(())
    }

    // Subscribe to channels and return a receiver for messages
    pub async fn subscribe(
        &self,
        channels: Vec<String>,
    ) -> Result<mpsc::Receiver<(String, String)>> {
        let mut pubsub = self
            .redis_client
            .client
            .get_async_pubsub()
            .await
            .context("Failed to create pubsub connection")?;

        // Subscribe to channels
        for channel in &channels {
            pubsub
                .subscribe(channel)
                .await
                .context("Failed to subscribe to channel")?;
            info!("Subscribed to channel: {}", channel);
        }

        // Create channel for sending messages
        let (tx, rx) = mpsc::channel::<(String, String)>(100);

        // Spawn task to handle incoming messages
        tokio::spawn(async move {
            loop {
                match pubsub.on_message().next().await {
                    Some(msg) => {
                        let channel_name = msg.get_channel_name().to_string();
                        match msg.get_payload::<String>() {
                            Ok(payload) => {
                                debug!("Received message on channel {}: {}", channel_name, payload);
                                if let Err(e) = tx.send((channel_name, payload)).await {
                                    error!("Failed to send message to channel: {}", e);
                                    break;
                                }
                            }
                            Err(e) => {
                                error!("Failed to parse message payload: {}", e);
                            }
                        }
                    }
                    None => {
                        warn!("PubSub stream ended");
                        break;
                    }
                }
            }
        });

        Ok(rx)
    }

    // Pattern subscribe (e.g., "session:*" to subscribe to all session channels)
    pub async fn psubscribe(
        &self,
        patterns: Vec<String>,
    ) -> Result<mpsc::Receiver<(String, String)>> {
        let mut pubsub = self
            .redis_client
            .client
            .get_async_pubsub()
            .await
            .context("Failed to create pubsub connection")?;

        // Subscribe to patterns
        for pattern in &patterns {
            pubsub
                .psubscribe(pattern)
                .await
                .context("Failed to subscribe to pattern")?;
            info!("Subscribed to pattern: {}", pattern);
        }

        // Create channel for sending messages
        let (tx, rx) = mpsc::channel::<(String, String)>(100);

        // Spawn task to handle incoming messages
        tokio::spawn(async move {
            loop {
                match pubsub.on_message().next().await {
                    Some(msg) => {
                        let channel_name = msg.get_channel_name().to_string();
                        match msg.get_payload::<String>() {
                            Ok(payload) => {
                                debug!("Received message on channel {}: {}", channel_name, payload);
                                if let Err(e) = tx.send((channel_name, payload)).await {
                                    error!("Failed to send message to channel: {}", e);
                                    break;
                                }
                            }
                            Err(e) => {
                                error!("Failed to parse message payload: {}", e);
                            }
                        }
                    }
                    None => {
                        warn!("PubSub stream ended");
                        break;
                    }
                }
            }
        });

        Ok(rx)
    }

    // Unsubscribe from specific channels
    pub async fn unsubscribe(&self, channels: Vec<String>, pubsub: &mut PubSub) -> Result<()> {
        for channel in channels {
            pubsub
                .unsubscribe(&channel)
                .await
                .context("Failed to unsubscribe from channel")?;
            info!("Unsubscribed from channel: {}", channel);
        }
        Ok(())
    }

    // Broadcast to multiple channels atomically
    pub async fn multi_publish<T: serde::Serialize>(
        &self,
        channel_messages: Vec<(&str, &T)>,
    ) -> Result<()> {
        let mut conn = self.redis_client.get_connection().await?;
        let mut pipe = redis::pipe();

        for (channel, message) in channel_messages {
            let payload = serde_json::to_string(message)
                .context("Failed to serialize message")?;
            pipe.cmd("PUBLISH").arg(channel).arg(&payload);
        }

        pipe.query_async(&mut conn)
            .await
            .context("Failed to publish multiple messages")?;

        Ok(())
    }

    // Get the number of subscribers for a channel
    pub async fn get_subscriber_count(&self, channel: &str) -> Result<i32> {
        let mut conn = self.redis_client.get_connection().await?;
        
        let result: Vec<redis::Value> = redis::cmd("PUBSUB")
            .arg("NUMSUB")
            .arg(channel)
            .query_async(&mut conn)
            .await
            .context("Failed to get subscriber count")?;

        // The result is [channel_name, subscriber_count]
        if result.len() >= 2 {
            if let redis::Value::Int(count) = &result[1] {
                return Ok(*count as i32);
            }
        }

        Ok(0)
    }
}

// Extension trait for PubSub to add async iterator support
use futures_util::StreamExt;
use redis::Msg;

trait PubSubExt {
    async fn on_message(&mut self) -> Option<Msg>;
}

impl PubSubExt for PubSub {
    async fn on_message(&mut self) -> Option<Msg> {
        self.into_on_message().next().await
    }
}