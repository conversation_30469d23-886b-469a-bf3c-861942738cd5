use anyhow::{Result, anyhow};
use google_cloud_spanner::client::Client as SpannerClient;
use google_cloud_spanner::statement::Statement;
use google_cloud_spanner::mutation::{insert, delete};
use google_cloud_spanner::value::CommitTimestamp;
use google_cloud_spanner::reader::AsyncIterator;
use tracing::{debug, error};

use crate::models::{SharedAnalysis, SharedQuery, CodeHighlight, LanguageSummary, QueryResult};

pub struct IntegrationStorage {
    client: SpannerClient,
}

impl IntegrationStorage {
    pub fn new(client: SpannerClient) -> Self {
        Self { client }
    }
    
    // Store a shared analysis
    pub async fn store_shared_analysis(&self, analysis: &SharedAnalysis) -> Result<()> {
        // Serialize highlights and summary to JSON
        let highlights_json = serde_json::to_string(&analysis.highlights)?;
        let summary_json = serde_json::to_string(&analysis.summary)?;
        
        // Clone values for closure
        let id = analysis.id.clone();
        let session_id = analysis.session_id.clone();
        let analysis_id = analysis.analysis_id.clone();
        let shared_by = analysis.shared_by.as_ref().to_string();
        let shared_at = chrono::Utc::now().to_rfc3339();
        let title = analysis.title.clone();
        let repository_url = analysis.repository_url.clone();
        let branch = analysis.branch.clone();
        let commit_sha = analysis.commit_sha.clone();
        
        let (_, _) = self.client.read_write_transaction(|tx| {
            let id = id.clone();
            let session_id = session_id.clone();
            let analysis_id = analysis_id.clone();
            let shared_by = shared_by.clone();
            let shared_at = shared_at.clone();
            let title = title.clone();
            let repository_url = repository_url.clone();
            let branch = branch.clone();
            let commit_sha = commit_sha.clone();
            let highlights_json = highlights_json.clone();
            let summary_json = summary_json.clone();
            
            Box::pin(async move {
                use google_cloud_spanner::mutation::insert;
                
                let mutation = insert(
                    "shared_analyses",
                    &["id", "session_id", "analysis_id", "shared_by", "shared_at", "title", 
                      "repository_url", "branch", "commit_sha", "highlights", "summary"],
                    &[
                        &id,
                        &session_id,
                        &analysis_id,
                        &shared_by,
                        &shared_at,
                        &title,
                        &repository_url,
                        &branch,
                        &commit_sha,
                        &highlights_json,
                        &summary_json,
                    ],
                );
                
                tx.buffer_write(vec![mutation]);
                Ok::<(), google_cloud_spanner::client::Error>(())
            })
        }).await?;
        
        debug!("Stored shared analysis: {}", id);
        Ok(())
    }
    
    // Store a shared query
    pub async fn store_shared_query(&self, query: &SharedQuery) -> Result<()> {
        // Serialize results to JSON
        let results_json = serde_json::to_string(&query.results)?;
        let query_type = format!("{:?}", query.query_type);
        
        // Clone values for closure
        let id = query.id.clone();
        let session_id = query.session_id.clone();
        let query_id = query.query_id.clone();
        let shared_by = query.shared_by.as_ref().to_string();
        let shared_at = chrono::Utc::now().to_rfc3339();
        let query_text = query.query_text.clone();
        let confidence_score = query.confidence_score;
        
        let (_, _) = self.client.read_write_transaction(|tx| {
            let id = id.clone();
            let session_id = session_id.clone();
            let query_id = query_id.clone();
            let shared_by = shared_by.clone();
            let shared_at = shared_at.clone();
            let query_text = query_text.clone();
            let query_type = query_type.clone();
            let results_json = results_json.clone();
            
            Box::pin(async move {
                use google_cloud_spanner::mutation::insert;
                
                let mutation = insert(
                    "shared_queries",
                    &["id", "session_id", "query_id", "shared_by", "shared_at", "query_text", 
                      "query_type", "results", "confidence_score"],
                    &[
                        &id,
                        &session_id,
                        &query_id,
                        &shared_by,
                        &shared_at,
                        &query_text,
                        &query_type,
                        &results_json,
                        &confidence_score,
                    ],
                );
                
                tx.buffer_write(vec![mutation]);
                Ok::<(), google_cloud_spanner::client::Error>(())
            })
        }).await?;
        
        debug!("Stored shared query: {}", id);
        Ok(())
    }
    
    // Get shared analyses for a session
    pub async fn get_session_shared_analyses(&self, session_id: &str) -> Result<Vec<SharedAnalysis>> {
        let mut statement = Statement::new(
            "SELECT id, session_id, analysis_id, shared_by, shared_at, title,
                    repository_url, branch, commit_sha, highlights, summary
             FROM shared_analyses
             WHERE session_id = @session_id
             ORDER BY shared_at DESC"
        );
        statement.add_param("session_id", &session_id);
        
        let mut transaction = self.client.single().await?;
        let mut result_set = transaction.query(statement).await?;
        let mut analyses = Vec::new();
        
        while let Some(row) = result_set.next().await? {
            let highlights_json: String = row.column_by_name("highlights")?;
            let summary_json: String = row.column_by_name("summary")?;
            
            let analysis = SharedAnalysis {
                id: row.column_by_name("id")?,
                session_id: row.column_by_name("session_id")?,
                analysis_id: row.column_by_name("analysis_id")?,
                shared_by: crate::models::UserId(row.column_by_name("shared_by")?),
                shared_at: {
                    let timestamp_str: String = row.column_by_name("shared_at")?;
                    crate::models::Timestamp(chrono::DateTime::parse_from_rfc3339(&timestamp_str)?.into())
                },
                title: row.column_by_name("title")?,
                repository_url: row.column_by_name("repository_url")?,
                branch: row.column_by_name("branch")?,
                commit_sha: row.column_by_name("commit_sha")?,
                highlights: serde_json::from_str(&highlights_json)?,
                summary: serde_json::from_str(&summary_json)?,
            };
            
            analyses.push(analysis);
        }
        
        Ok(analyses)
    }
    
    // Get shared queries for a session
    pub async fn get_session_shared_queries(&self, session_id: &str) -> Result<Vec<SharedQuery>> {
        let mut statement = Statement::new(
            "SELECT id, session_id, query_id, shared_by, shared_at, query_text,
                    query_type, results, confidence_score
             FROM shared_queries
             WHERE session_id = @session_id
             ORDER BY shared_at DESC"
        );
        statement.add_param("session_id", &session_id);
        
        let mut transaction = self.client.single().await?;
        let mut result_set = transaction.query(statement).await?;
        let mut queries = Vec::new();
        
        while let Some(row) = result_set.next().await? {
            let results_json: String = row.column_by_name("results")?;
            let query_type_str: String = row.column_by_name("query_type")?;
            
            // Parse query type
            let query_type = match query_type_str.as_str() {
                "CodeSearch" => crate::models::QueryType::CodeSearch,
                "PatternDetection" => crate::models::QueryType::PatternDetection,
                "DocumentationLookup" => crate::models::QueryType::DocumentationLookup,
                "SecurityScan" => crate::models::QueryType::SecurityScan,
                "PerformanceAnalysis" => crate::models::QueryType::PerformanceAnalysis,
                _ => crate::models::QueryType::Custom,
            };
            
            let query = SharedQuery {
                id: row.column_by_name("id")?,
                session_id: row.column_by_name("session_id")?,
                query_id: row.column_by_name("query_id")?,
                shared_by: crate::models::UserId(row.column_by_name("shared_by")?),
                shared_at: {
                    let timestamp_str: String = row.column_by_name("shared_at")?;
                    crate::models::Timestamp(chrono::DateTime::parse_from_rfc3339(&timestamp_str)?.into())
                },
                query_text: row.column_by_name("query_text")?,
                query_type,
                results: serde_json::from_str(&results_json)?,
                confidence_score: row.column_by_name("confidence_score")?,
            };
            
            queries.push(query);
        }
        
        Ok(queries)
    }
    
    // Get a specific shared analysis
    pub async fn get_shared_analysis(&self, id: &str) -> Result<Option<SharedAnalysis>> {
        let mut statement = Statement::new(
            "SELECT id, session_id, analysis_id, shared_by, shared_at, title,
                    repository_url, branch, commit_sha, highlights, summary
             FROM shared_analyses
             WHERE id = @id"
        );
        statement.add_param("id", &id);
        
        let mut transaction = self.client.single().await?;
        let mut result_set = transaction.query(statement).await?;
        
        if let Some(row) = result_set.next().await? {
            let highlights_json: String = row.column_by_name("highlights")?;
            let summary_json: String = row.column_by_name("summary")?;
            
            let analysis = SharedAnalysis {
                id: row.column_by_name("id")?,
                session_id: row.column_by_name("session_id")?,
                analysis_id: row.column_by_name("analysis_id")?,
                shared_by: crate::models::UserId(row.column_by_name("shared_by")?),
                shared_at: {
                    let timestamp_str: String = row.column_by_name("shared_at")?;
                    crate::models::Timestamp(chrono::DateTime::parse_from_rfc3339(&timestamp_str)?.into())
                },
                title: row.column_by_name("title")?,
                repository_url: row.column_by_name("repository_url")?,
                branch: row.column_by_name("branch")?,
                commit_sha: row.column_by_name("commit_sha")?,
                highlights: serde_json::from_str(&highlights_json)?,
                summary: serde_json::from_str(&summary_json)?,
            };
            
            Ok(Some(analysis))
        } else {
            Ok(None)
        }
    }
    
    // Delete shared analyses for a session (cleanup)
    pub async fn delete_session_shared_content(&self, session_id: &str) -> Result<()> {
        // For DELETE operations, we need to execute them as separate statements
        let session_id = session_id.to_string();
        
        self.client.read_write_transaction(|tx| {
            let session_id = session_id.clone();
            Box::pin(async move {
                let mut delete_analyses = Statement::new(
                    "DELETE FROM shared_analyses WHERE session_id = @session_id"
                );
                delete_analyses.add_param("session_id", &session_id);
                
                let mut delete_queries = Statement::new(
                    "DELETE FROM shared_queries WHERE session_id = @session_id"
                );
                delete_queries.add_param("session_id", &session_id);
                
                // Execute both deletes in the transaction
                let _result1 = tx.query(delete_analyses).await?;
                let _result2 = tx.query(delete_queries).await?;
                
                Ok::<(), google_cloud_spanner::client::Error>(())
            })
        }).await?;
        
        debug!("Deleted shared content for session: {}", session_id);
        Ok(())
    }
}