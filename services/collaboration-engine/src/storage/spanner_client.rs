use crate::{
    config::Config,
    models::{
        Message, Session, SessionParticipantInfo, SessionStatus, Team, TeamMember,
        TeamRole, Timestamp, UserId,
    },
};
use anyhow::{Context, Result};
use google_cloud_googleapis::spanner::v1::{
    ExecuteSqlRequest, PartialResultSet, ResultSet, Session as SpannerSession,
    Type, TypeCode, TransactionSelector,
};
use google_cloud_spanner::{
    admin::client::Client as AdminClient,
    client::{Client, ClientConfig},
    mutation::{insert, update, delete},
    row::Row,
    statement::Statement,
    transaction::Transaction,
};
use std::collections::{HashMap, HashSet};
use tracing::{debug, error, info};

pub struct SpannerClient {
    pub client: Client,
    database_path: String,
}

impl SpannerClient {
    pub async fn new(config: &Config) -> Result<Self> {
        let database_path = format!(
            "projects/{}/instances/{}/databases/{}",
            config.gcp_project_id, config.spanner_instance_id, config.spanner_database_id
        );

        info!("Connecting to Spanner database: {}", database_path);

        // Create client configuration
        let client_config = ClientConfig::default()
            .with_auth()
            .await
            .context("Failed to create Spanner client configuration")?;

        // Create a google_cloud_spanner client
        let client = Client::new(&database_path, client_config)
            .await
            .context("Failed to create Spanner client")?;

        Ok(Self {
            client,
            database_path,
        })
    }

    // Health check
    pub async fn health_check(&self) -> Result<()> {
        let statement = Statement::new("SELECT 1");
        
        let mut tx = self.client
            .single()
            .await
            .context("Failed to create single-use transaction")?;

        let _result = tx
            .query(statement)
            .await
            .context("Failed to execute health check query")?;

        Ok(())
    }

    // Team operations
    pub async fn create_team(&self, team: &Team) -> Result<()> {
        let mut statement = Statement::new(
            "INSERT INTO teams (id, name, description, created_by, created_at, updated_at, settings, metadata) 
             VALUES (@id, @name, @description, @created_by, @created_at, @updated_at, @settings, @metadata)",
        );
        statement.add_param("id", &team.id);
        statement.add_param("name", &team.name);
        statement.add_param("description", &team.description);
        statement.add_param("created_by", team.created_by.as_ref());
        statement.add_param("created_at", &team.created_at.0.to_rfc3339());
        statement.add_param("updated_at", &team.updated_at.0.to_rfc3339());
        statement.add_param("settings", &serde_json::to_string(&team.settings)?);
        statement.add_param("metadata", &serde_json::to_string(&team.metadata)?);

        let (_, _) = self.client
            .read_write_transaction(|tx| {
                Box::pin(async move {
                    // Can't use Statement in buffer_write, need to use execute_update
                    let _rows = tx.execute_update(statement).await?;
                    Ok::<(), google_cloud_spanner::client::Error>(())
                })
            })
            .await
            .context("Failed to create team")?;

        Ok(())
    }

    pub async fn get_team(&self, team_id: &str) -> Result<Option<Team>> {
                
        let mut statement = Statement::new(
            "SELECT id, name, description, created_by, created_at, updated_at, settings, metadata 
             FROM teams WHERE id = @id",
        );
        statement.add_param("id", &team_id);

        let mut tx = self.client
            .single()
            .await
            .context("Failed to create transaction")?;

        let result = tx.query(statement).await?;

        if let Some(row) = result.next().await? {
            Ok(Some(parse_team_row(row)?))
        } else {
            Ok(None)
        }
    }

    pub async fn get_user_teams(&self, user_id: &str) -> Result<Vec<Team>> {
                
        let mut statement = Statement::new(
            "SELECT t.id, t.name, t.description, t.created_by, t.created_at, t.updated_at, t.settings, t.metadata 
             FROM teams t
             JOIN team_members tm ON t.id = tm.team_id
             WHERE tm.user_id = @user_id
             ORDER BY t.created_at DESC",
        );
        statement.add_param("user_id", &user_id);

        let mut tx = self.client
            .single()
            .await
            .context("Failed to create transaction")?;

        let result = tx.query(statement).await?;

        let mut teams = Vec::new();
        while let Some(row) = result.next().await? {
            teams.push(parse_team_row(row)?);
        }

        Ok(teams)
    }

    pub async fn add_team_member(&self, team_id: &str, member: &TeamMember) -> Result<()> {
                
        let mut statement = Statement::new(
            "INSERT INTO team_members (team_id, user_id, email, name, role, joined_at, permissions) 
             VALUES (@team_id, @user_id, @email, @name, @role, @joined_at, @permissions)",
        );
        statement.add_param("team_id", &team_id);
        statement.add_param("user_id", member.user_id.as_ref());
        statement.add_param("email", &member.email);
        statement.add_param("name", &member.name);
        statement.add_param("role", &member.role.to_string());
        statement.add_param("joined_at", &member.joined_at.0.to_rfc3339());
        statement.add_param("permissions", &serde_json::to_string(&member.permissions)?);

        let (_, _) = self.client
            .read_write_transaction(|tx| {
                Box::pin(async move {
                    // Can't use Statement in buffer_write, need to use execute_update
                    let _rows = tx.execute_update(statement).await?;
                    Ok::<(), google_cloud_spanner::client::Error>(())
                })
            })
            .await
            .context("Failed to add team member")?;

        Ok(())
    }

    pub async fn is_team_member(&self, team_id: &str, user_id: &str) -> Result<bool> {
                
        let mut statement = Statement::new(
            "SELECT COUNT(*) as count FROM team_members WHERE team_id = @team_id AND user_id = @user_id",
        );
        statement.add_param("team_id", &team_id);
        statement.add_param("user_id", &user_id);

        let mut tx = self.client
            .single()
            .await
            .context("Failed to create transaction")?;

        let result = tx.query(statement).await?;

        if let Some(row) = result.next().await? {
            let count: i64 = row.column_by_name("count")?;
            Ok(count > 0)
        } else {
            Ok(false)
        }
    }

    // Session operations
    pub async fn create_session(&self, session: &Session) -> Result<()> {
                
        let mut statement = Statement::new(
            "INSERT INTO sessions (id, team_id, name, description, created_by, created_at, updated_at, status, settings, metadata) 
             VALUES (@id, @team_id, @name, @description, @created_by, @created_at, @updated_at, @status, @settings, @metadata)",
        );
        statement.add_param("id", &session.id);
        statement.add_param("team_id", &session.team_id);
        statement.add_param("name", &session.name);
        statement.add_param("description", &session.description);
        statement.add_param("created_by", session.created_by.as_ref());
        statement.add_param("created_at", &session.created_at.0.to_rfc3339());
        statement.add_param("updated_at", &session.updated_at.0.to_rfc3339());
        statement.add_param("status", &session.status.to_string());
        statement.add_param("settings", &serde_json::to_string(&session.settings)?);
        statement.add_param("metadata", &serde_json::to_string(&session.metadata)?);

        let (_, _) = self.client
            .read_write_transaction(|tx| {
                Box::pin(async move {
                    // Can't use Statement in buffer_write, need to use execute_update
                    let _rows = tx.execute_update(statement).await?;
                    Ok::<(), google_cloud_spanner::client::Error>(())
                })
            })
            .await
            .context("Failed to create session")?;

        Ok(())
    }

    pub async fn get_session(&self, session_id: &str) -> Result<Option<Session>> {
                
        let mut statement = Statement::new(
            "SELECT id, team_id, name, description, created_by, created_at, updated_at, ended_at, status, settings, metadata 
             FROM sessions WHERE id = @id",
        );
        statement.add_param("id", &session_id);

        let mut tx = self.client
            .single()
            .await
            .context("Failed to create transaction")?;

        let result = tx.query(statement).await?;

        if let Some(row) = result.next().await? {
            Ok(Some(parse_session_row(row)?))
        } else {
            Ok(None)
        }
    }

    pub async fn add_session_participant(&self, session_id: &str, user_id: &str) -> Result<()> {
                
        let mut statement = Statement::new(
            "INSERT INTO session_participants (session_id, user_id, joined_at) 
             VALUES (@session_id, @user_id, @joined_at)",
        );
        statement.add_param("session_id", &session_id);
        statement.add_param("user_id", &user_id);
        statement.add_param("joined_at", &chrono::Utc::now().to_rfc3339());

        let (_, _) = self.client
            .read_write_transaction(|tx| {
                Box::pin(async move {
                    // Can't use Statement in buffer_write, need to use execute_update
                    let _rows = tx.execute_update(statement).await?;
                    Ok::<(), google_cloud_spanner::client::Error>(())
                })
            })
            .await
            .context("Failed to add session participant")?;

        Ok(())
    }

    pub async fn remove_session_participant(&self, session_id: &str, user_id: &str) -> Result<()> {
                
        let mut statement = Statement::new(
            "DELETE FROM session_participants WHERE session_id = @session_id AND user_id = @user_id",
        );
        statement.add_param("session_id", &session_id);
        statement.add_param("user_id", &user_id);

        let (_, _) = self.client
            .read_write_transaction(|tx| {
                Box::pin(async move {
                    // Can't use Statement in buffer_write, need to use execute_update
                    let _rows = tx.execute_update(statement).await?;
                    Ok::<(), google_cloud_spanner::client::Error>(())
                })
            })
            .await
            .context("Failed to remove session participant")?;

        Ok(())
    }

    pub async fn get_session_participants(&self, session_id: &str) -> Result<Vec<SessionParticipantInfo>> {
                
        let mut statement = Statement::new(
            "SELECT sp.user_id, tm.email, tm.name, sp.joined_at, sp.last_active
             FROM session_participants sp
             JOIN sessions s ON sp.session_id = s.id
             JOIN team_members tm ON sp.user_id = tm.user_id AND s.team_id = tm.team_id
             WHERE sp.session_id = @session_id",
        );
        statement.add_param("session_id", &session_id);

        let mut tx = self.client
            .single()
            .await
            .context("Failed to create transaction")?;

        let result = tx.query(statement).await?;

        let mut participants = Vec::new();
        while let Some(row) = result.next().await? {
            participants.push(SessionParticipantInfo {
                user_id: UserId::from_string(row.column_by_name::<String>("user_id")?),
                email: row.column_by_name::<String>("email")?,
                name: row.column_by_name::<String>("name")?,
                status: crate::models::ParticipantStatus::Active, // TODO: Calculate from last_active
                joined_at: Timestamp(chrono::DateTime::parse_from_rfc3339(&row.column_by_name::<String>("joined_at")?)?.into()),
                last_active: Timestamp(chrono::Utc::now()), // TODO: Get from Redis presence
            });
        }

        Ok(participants)
    }

    // Message operations
    pub async fn save_message(&self, message: &Message) -> Result<()> {
                
        let mut statement = Statement::new(
            "INSERT INTO messages (id, session_id, user_id, content, created_at, edited, deleted, metadata) 
             VALUES (@id, @session_id, @user_id, @content, @created_at, @edited, @deleted, @metadata)",
        );
        statement.add_param("id", &message.id);
        statement.add_param("session_id", &message.session_id);
        statement.add_param("user_id", message.user_id.as_ref());
        statement.add_param("content", &serde_json::to_string(&message.content)?);
        statement.add_param("created_at", &message.created_at.0.to_rfc3339());
        statement.add_param("edited", &message.edited);
        statement.add_param("deleted", &message.deleted);
        statement.add_param("metadata", &serde_json::to_string(&message.metadata)?);

        let (_, _) = self.client
            .read_write_transaction(|tx| {
                Box::pin(async move {
                    // Can't use Statement in buffer_write, need to use execute_update
                    let _rows = tx.execute_update(statement).await?;
                    Ok::<(), google_cloud_spanner::client::Error>(())
                })
            })
            .await
            .context("Failed to save message")?;

        Ok(())
    }

    pub async fn get_session_messages(&self, session_id: &str, limit: usize) -> Result<Vec<Message>> {
                
        let mut statement = Statement::new(
            "SELECT id, session_id, user_id, content, created_at, updated_at, edited, deleted, metadata 
             FROM messages 
             WHERE session_id = @session_id 
             ORDER BY created_at DESC 
             LIMIT @limit",
        );
        statement.add_param("session_id", &session_id);
        statement.add_param("limit", &(limit as i64));

        let mut tx = self.client
            .single()
            .await
            .context("Failed to create transaction")?;

        let result = tx.query(statement).await?;

        let mut messages = Vec::new();
        while let Some(row) = result.next().await? {
            messages.push(parse_message_row(row)?);
        }

        Ok(messages)
    }
}

// Helper functions to parse Spanner rows
fn parse_team_row(row: Row) -> Result<Team> {
    Ok(Team {
        id: row.column_by_name::<String>("id")?,
        name: row.column_by_name::<String>("name")?,
        description: row.column_by_name::<Option<String>>("description")?,
        created_by: UserId::from_string(row.column_by_name::<String>("created_by")?),
        created_at: Timestamp(chrono::DateTime::parse_from_rfc3339(&row.column_by_name::<String>("created_at")?)?.into()),
        updated_at: Timestamp(chrono::DateTime::parse_from_rfc3339(&row.column_by_name::<String>("updated_at")?)?.into()),
        members: HashMap::new(), // Would need a separate query to populate
        settings: serde_json::from_str(&row.column_by_name::<String>("settings")?)?,
        metadata: serde_json::from_str(&row.column_by_name::<String>("metadata")?)?,
    })
}

fn parse_session_row(row: Row) -> Result<Session> {
    let ended_at: Option<String> = row.column_by_name::<Option<String>>("ended_at")?;
    let status_str: String = row.column_by_name::<String>("status")?;
    
    Ok(Session {
        id: row.column_by_name::<String>("id")?,
        team_id: row.column_by_name::<String>("team_id")?,
        name: row.column_by_name::<String>("name")?,
        description: row.column_by_name::<Option<String>>("description")?,
        created_by: UserId::from_string(row.column_by_name::<String>("created_by")?),
        created_at: Timestamp(chrono::DateTime::parse_from_rfc3339(&row.column_by_name::<String>("created_at")?)?.into()),
        updated_at: Timestamp(chrono::DateTime::parse_from_rfc3339(&row.column_by_name::<String>("updated_at")?)?.into()),
        ended_at: ended_at.as_deref().map(|s| Timestamp(chrono::DateTime::parse_from_rfc3339(s).unwrap().into())),
        status: serde_json::from_str(&format!("\"{}\"", status_str))?,
        participants: HashSet::new(), // Would need a separate query to populate
        settings: serde_json::from_str(&row.column_by_name::<String>("settings")?)?,
        metadata: serde_json::from_str(&row.column_by_name::<String>("metadata")?)?,
    })
}

fn parse_message_row(row: Row) -> Result<Message> {
    let updated_at: Option<String> = row.column_by_name::<Option<String>>("updated_at")?;
    
    Ok(Message {
        id: row.column_by_name::<String>("id")?,
        session_id: row.column_by_name::<String>("session_id")?,
        user_id: UserId::from_string(row.column_by_name::<String>("user_id")?),
        content: serde_json::from_str(&row.column_by_name::<String>("content")?)?,
        created_at: Timestamp(chrono::DateTime::parse_from_rfc3339(&row.column_by_name::<String>("created_at")?)?.into()),
        updated_at: updated_at.as_deref().map(|s| Timestamp(chrono::DateTime::parse_from_rfc3339(s).unwrap().into())),
        edited: row.column_by_name::<bool>("edited")?,
        deleted: row.column_by_name::<bool>("deleted")?,
        metadata: serde_json::from_str(&row.column_by_name::<String>("metadata")?)?,
    })
}

// Implement Display for enums to support to_string()
impl std::fmt::Display for TeamRole {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            TeamRole::Owner => write!(f, "owner"),
            TeamRole::Admin => write!(f, "admin"),
            TeamRole::Member => write!(f, "member"),
            TeamRole::Viewer => write!(f, "viewer"),
        }
    }
}

impl std::fmt::Display for SessionStatus {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            SessionStatus::Active => write!(f, "active"),
            SessionStatus::Paused => write!(f, "paused"),
            SessionStatus::Ended => write!(f, "ended"),
            SessionStatus::Archived => write!(f, "archived"),
        }
    }
}