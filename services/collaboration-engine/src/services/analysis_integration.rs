use std::sync::Arc;
use std::time::Duration;
use anyhow::{Result, anyhow};
use reqwest::{Client, StatusCode};
use serde::{Deserialize, Serialize};
use tracing::{debug, error, info, warn};

use crate::models::{
    IntegrationConfig, SharedAnalysis, AnalysisSummary, 
    LanguageSummary, UserId, CodeHighlight, CreateHighlightRequest,
    AnalysisStatus, SecuritySeverity,
};
use crate::storage::redis_client::RedisClient;

// Analysis Engine API response models
#[derive(Debug, Deserialize)]
pub struct AnalysisResponse {
    pub analysis_id: String,
    pub status: AnalysisStatus,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub repository: RepositoryInfo,
    pub progress_url: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct RepositoryInfo {
    pub url: String,
    pub branch: String,
    pub commit_sha: Option<String>,
    pub size_bytes: Option<u64>,
}

#[derive(Debug, Deserialize)]
pub struct AnalysisResult {
    pub id: String,
    pub repository_url: String,
    pub branch: String,
    pub commit_hash: Option<String>,
    pub status: AnalysisStatus,
    pub metrics: AnalysisMetricsResponse,
    pub languages: Vec<LanguageMetric>,
    pub warnings: Vec<AnalysisWarning>,
    pub started_at: chrono::DateTime<chrono::Utc>,
    pub completed_at: Option<chrono::DateTime<chrono::Utc>>,
}

#[derive(Debug, Deserialize)]
pub struct AnalysisMetricsResponse {
    pub total_files: u64,
    pub total_lines: u64,
    pub code_lines: u64,
    pub comment_lines: u64,
    pub blank_lines: u64,
    pub complexity_score: Option<f64>,
    pub duplication_ratio: Option<f64>,
}

#[derive(Debug, Deserialize)]
pub struct LanguageMetric {
    pub language: String,
    pub files: u64,
    pub lines: u64,
    pub code: u64,
    pub comments: u64,
    pub blanks: u64,
}

#[derive(Debug, Deserialize)]
pub struct AnalysisWarning {
    pub severity: String,
    pub warning_type: String,
    pub message: String,
    pub file_path: Option<String>,
    pub line_number: Option<u32>,
}

// Service for integrating with Analysis Engine
pub struct AnalysisIntegrationService {
    client: Client,
    config: IntegrationConfig,
    redis: Arc<RedisClient>,
    base_url: String,
}

impl AnalysisIntegrationService {
    pub fn new(config: IntegrationConfig, redis: Arc<RedisClient>) -> Self {
        let client = Client::builder()
            .timeout(Duration::from_secs(config.service_timeout_secs))
            .build()
            .expect("Failed to create HTTP client");
        
        let base_url = config.analysis_engine_url.clone();
        
        Self {
            client,
            config,
            redis,
            base_url,
        }
    }
    
    // Get analysis results from Analysis Engine
    pub async fn get_analysis(&self, analysis_id: &str) -> Result<AnalysisResult> {
        // Check cache first
        let cache_key = format!("analysis:{}", analysis_id);
        if let Ok(Some(cached)) = self.redis.get_json::<AnalysisResult>(&cache_key).await {
            debug!("Analysis {} found in cache", analysis_id);
            return Ok(cached);
        }
        
        // Fetch from Analysis Engine
        let url = format!("{}/api/v1/analysis/{}", self.base_url, analysis_id);
        let response = self.client
            .get(&url)
            .send()
            .await?;
        
        match response.status() {
            StatusCode::OK => {
                let analysis: AnalysisResult = response.json().await?;
                
                // Cache the result
                if analysis.status == AnalysisStatus::Completed {
                    let _ = self.redis
                        .set_json(&cache_key, &analysis, self.config.cache_ttl_secs)
                        .await;
                }
                
                Ok(analysis)
            }
            StatusCode::NOT_FOUND => {
                Err(anyhow!("Analysis {} not found", analysis_id))
            }
            status => {
                let error_body = response.text().await.unwrap_or_default();
                Err(anyhow!("Failed to get analysis: {} - {}", status, error_body))
            }
        }
    }
    
    // Convert Analysis Engine result to SharedAnalysis
    pub fn convert_to_shared_analysis(
        &self,
        analysis: AnalysisResult,
        session_id: String,
        shared_by: UserId,
        highlights: Vec<CodeHighlight>,
    ) -> SharedAnalysis {
        let summary = AnalysisSummary {
            total_files: analysis.metrics.total_files as u32,
            total_lines: analysis.metrics.total_lines,
            languages: analysis.languages.iter().map(|lang| LanguageSummary {
                language: lang.language.clone(),
                files: lang.files as u32,
                lines: lang.lines,
                percentage: (lang.lines as f64 / analysis.metrics.total_lines as f64) * 100.0,
            }).collect(),
            complexity_score: analysis.metrics.complexity_score.unwrap_or(0.0),
            security_score: None, // TODO: Calculate from warnings
            quality_score: None,  // TODO: Calculate from metrics
            key_patterns: Vec::new(), // TODO: Extract from pattern detection
        };
        
        let mut shared = SharedAnalysis::new(
            session_id,
            analysis.id.clone(),
            shared_by,
            format!("Analysis of {}", extract_repo_name(&analysis.repository_url)),
            analysis.repository_url,
            summary,
        );
        
        shared.branch = Some(analysis.branch);
        shared.commit_sha = analysis.commit_hash;
        shared.highlights = highlights;
        
        shared
    }
    
    // Get file content for creating highlights
    pub async fn get_file_content(
        &self,
        analysis_id: &str,
        file_path: &str,
        start_line: u32,
        end_line: u32,
    ) -> Result<String> {
        // This would typically fetch from Cloud Storage or the analysis results
        // For now, return a placeholder
        Ok(format!(
            "// Code snippet from {} lines {}-{}\n// Content would be fetched from storage",
            file_path, start_line, end_line
        ))
    }
    
    // Get language for a file
    pub async fn get_file_language(&self, analysis_id: &str, file_path: &str) -> Result<String> {
        // Determine language from file extension
        let extension = file_path
            .split('.')
            .last()
            .unwrap_or("txt");
        
        let language = match extension {
            "rs" => "rust",
            "py" => "python",
            "js" | "jsx" => "javascript",
            "ts" | "tsx" => "typescript",
            "go" => "go",
            "java" => "java",
            "cpp" | "cc" | "cxx" => "cpp",
            "c" | "h" => "c",
            "rb" => "ruby",
            "php" => "php",
            "cs" => "csharp",
            "swift" => "swift",
            "kt" => "kotlin",
            "scala" => "scala",
            "r" => "r",
            "sql" => "sql",
            "sh" | "bash" => "bash",
            "yml" | "yaml" => "yaml",
            "json" => "json",
            "xml" => "xml",
            "html" => "html",
            "css" => "css",
            "md" => "markdown",
            _ => "text",
        };
        
        Ok(language.to_string())
    }
    
    // Create highlights from requests
    pub async fn create_highlights(
        &self,
        analysis_id: &str,
        requests: Vec<CreateHighlightRequest>,
        created_by: UserId,
    ) -> Result<Vec<CodeHighlight>> {
        let mut highlights = Vec::new();
        
        for request in requests {
            let code_snippet = self.get_file_content(
                analysis_id,
                &request.file_path,
                request.start_line,
                request.end_line,
            ).await?;
            
            let language = self.get_file_language(analysis_id, &request.file_path).await?;
            
            let mut highlight = CodeHighlight::new(
                request.file_path,
                request.start_line,
                request.end_line,
                language,
                code_snippet,
                created_by.clone(),
            );
            
            highlight.annotation = request.annotation;
            highlights.push(highlight);
        }
        
        Ok(highlights)
    }
    
    // Check if Analysis Engine is healthy
    pub async fn health_check(&self) -> Result<bool> {
        let url = format!("{}/health", self.base_url);
        match self.client.get(&url).send().await {
            Ok(response) => Ok(response.status().is_success()),
            Err(e) => {
                warn!("Analysis Engine health check failed: {}", e);
                Ok(false)
            }
        }
    }
}

// Helper function to extract repository name from URL
fn extract_repo_name(url: &str) -> &str {
    url.split('/')
        .last()
        .unwrap_or(url)
        .trim_end_matches(".git")
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_extract_repo_name() {
        assert_eq!(extract_repo_name("https://github.com/owner/repo.git"), "repo");
        assert_eq!(extract_repo_name("https://github.com/owner/repo"), "repo");
        assert_eq!(extract_repo_name("**************:owner/repo.git"), "repo");
        assert_eq!(extract_repo_name("repo"), "repo");
    }
}