use std::sync::Arc;
use anyhow::{Result, anyhow};
use tokio::sync::mpsc;
use tracing::{debug, error, info, warn};

use crate::models::{
    PubSubMessage, CollaborationEvent, EventHandler, EventError,
    EventSubscriptionConfig, EventPriority, extract_session_id, get_event_priority,
    WebSocketMessage, UserId, AnalysisStatus,
};
use crate::websocket::hub::WebSocketHub;
use crate::storage::StorageClients;

// Handler for processing Pub/Sub events
pub struct PubSubEventHandler {
    ws_hub: Arc<WebSocketHub>,
    storage: Arc<StorageClients>,
    event_tx: mpsc::Sender<PubSubMessage>,
}

impl PubSubEventHandler {
    pub fn new(
        ws_hub: Arc<WebSocketHub>,
        storage: Arc<StorageClients>,
    ) -> (Self, mpsc::Receiver<PubSubMessage>) {
        let (event_tx, event_rx) = mpsc::channel(1000);
        
        let handler = Self {
            ws_hub,
            storage,
            event_tx,
        };
        
        (handler, event_rx)
    }
    
    // Start processing events
    pub async fn start_processing(
        self: Arc<Self>,
        mut event_rx: mpsc::Receiver<PubSubMessage>,
    ) {
        info!("Starting Pub/Sub event processing");
        
        while let Some(message) = event_rx.recv().await {
            let handler = self.clone();
            tokio::spawn(async move {
                if let Err(e) = handler.process_message(message).await {
                    error!("Failed to process Pub/Sub message: {}", e);
                }
            });
        }
    }
    
    // Process a single Pub/Sub message
    async fn process_message(&self, message: PubSubMessage) -> Result<()> {
        debug!(
            "Processing Pub/Sub message: type={}, source={}",
            message.event_type, message.source_service
        );
        
        // Parse the event data
        let event: CollaborationEvent = serde_json::from_value(message.data.clone())
            .map_err(|e| anyhow!("Failed to parse event: {}", e))?;
        
        // Route based on event type
        match event {
            CollaborationEvent::AnalysisCompleted { 
                analysis_id, 
                user_id, 
                repository_url, 
                status,
                duration_secs,
                metrics,
            } => {
                self.handle_analysis_completed(
                    analysis_id,
                    user_id,
                    repository_url,
                    status,
                    duration_secs,
                    metrics,
                ).await?;
            }
            
            CollaborationEvent::QueryExecuted {
                query_id,
                user_id,
                query_text,
                query_type,
                result_count,
                confidence_score,
                session_id,
            } => {
                if let Some(session_id) = session_id {
                    self.handle_query_executed(
                        query_id,
                        user_id,
                        query_text,
                        result_count,
                        confidence_score,
                        session_id,
                    ).await?;
                }
            }
            
            CollaborationEvent::SecurityIssueDetected {
                issue_id,
                analysis_id,
                severity,
                issue_type,
                affected_files,
                session_id,
                ..
            } => {
                if let Some(session_id) = session_id {
                    self.handle_security_issue(
                        issue_id,
                        analysis_id,
                        severity,
                        issue_type,
                        affected_files,
                        session_id,
                    ).await?;
                }
            }
            
            CollaborationEvent::PatternDetected {
                pattern_id,
                analysis_id,
                pattern_type,
                confidence,
                occurrences,
                session_id,
            } => {
                if let Some(session_id) = session_id {
                    self.handle_pattern_detected(
                        pattern_id,
                        analysis_id,
                        pattern_type,
                        confidence,
                        occurrences,
                        session_id,
                    ).await?;
                }
            }
            
            _ => {
                debug!("Ignoring event type: {}", message.event_type);
            }
        }
        
        Ok(())
    }
    
    // Handle analysis completed event
    async fn handle_analysis_completed(
        &self,
        analysis_id: String,
        user_id: UserId,
        repository_url: String,
        status: AnalysisStatus,
        duration_secs: u64,
        metrics: crate::models::events::AnalysisMetrics,
    ) -> Result<()> {
        info!(
            "Analysis completed: id={}, status={:?}, duration={}s",
            analysis_id, status, duration_secs
        );
        
        // Find active sessions for this user
        if let Ok(user_sessions) = self.storage.sessions.get_user_active_sessions(&user_id).await {
            for session in user_sessions {
                // Send notification to session participants
                let message = WebSocketMessage::AnalysisShared {
                    session_id: session.id.clone(),
                    analysis_id: analysis_id.clone(),
                    shared_by: user_id.clone(),
                    analysis_type: "repository".to_string(),
                    summary: format!(
                        "Analysis completed for {} - {} files, {} lines across {} languages",
                        extract_repo_name(&repository_url),
                        metrics.total_files,
                        metrics.total_lines,
                        metrics.languages_detected.len()
                    ),
                };
                
                let _ = self.ws_hub.broadcast_to_session(&session.id, message).await;
            }
        }
        
        Ok(())
    }
    
    // Handle query executed event
    async fn handle_query_executed(
        &self,
        query_id: String,
        user_id: UserId,
        query_text: String,
        result_count: u32,
        confidence_score: f64,
        session_id: String,
    ) -> Result<()> {
        info!(
            "Query executed in session {}: {} results with {:.2}% confidence",
            session_id, result_count, confidence_score * 100.0
        );
        
        // Notify session participants
        let message = WebSocketMessage::AnalysisShared {
            session_id: session_id.clone(),
            analysis_id: query_id,
            shared_by: user_id,
            analysis_type: "query".to_string(),
            summary: format!(
                "Query '{}' found {} results ({:.0}% confidence)",
                truncate_string(&query_text, 50),
                result_count,
                confidence_score * 100.0
            ),
        };
        
        let _ = self.ws_hub.broadcast_to_session(&session_id, message).await;
        Ok(())
    }
    
    // Handle security issue detected
    async fn handle_security_issue(
        &self,
        issue_id: String,
        analysis_id: String,
        severity: crate::models::SecuritySeverity,
        issue_type: String,
        affected_files: Vec<String>,
        session_id: String,
    ) -> Result<()> {
        warn!(
            "Security issue detected: {} ({:?}) affecting {} files",
            issue_type, severity, affected_files.len()
        );
        
        // Send high-priority notification
        let message = WebSocketMessage::AnalysisShared {
            session_id: session_id.clone(),
            analysis_id: issue_id,
            shared_by: UserId::from_string("system".to_string()),
            analysis_type: "security".to_string(),
            summary: format!(
                "🚨 {} security issue: {} ({} files affected)",
                format!("{:?}", severity).to_uppercase(),
                issue_type,
                affected_files.len()
            ),
        };
        
        let _ = self.ws_hub.broadcast_to_session(&session_id, message).await;
        Ok(())
    }
    
    // Handle pattern detected
    async fn handle_pattern_detected(
        &self,
        pattern_id: String,
        analysis_id: String,
        pattern_type: String,
        confidence: f64,
        occurrences: u32,
        session_id: String,
    ) -> Result<()> {
        info!(
            "Pattern detected: {} with {:.2}% confidence ({} occurrences)",
            pattern_type, confidence * 100.0, occurrences
        );
        
        // Notify session participants
        let message = WebSocketMessage::AnalysisShared {
            session_id: session_id.clone(),
            analysis_id: pattern_id,
            shared_by: UserId::from_string("system".to_string()),
            analysis_type: "pattern".to_string(),
            summary: format!(
                "Pattern '{}' detected {} times ({:.0}% confidence)",
                pattern_type,
                occurrences,
                confidence * 100.0
            ),
        };
        
        let _ = self.ws_hub.broadcast_to_session(&session_id, message).await;
        Ok(())
    }
}

// Pub/Sub subscription manager
pub struct PubSubSubscriptionManager {
    config: EventSubscriptionConfig,
    handler: Arc<PubSubEventHandler>,
}

impl PubSubSubscriptionManager {
    pub fn new(
        config: EventSubscriptionConfig,
        ws_hub: Arc<WebSocketHub>,
        storage: Arc<StorageClients>,
    ) -> (Self, mpsc::Receiver<PubSubMessage>) {
        let (handler, event_rx) = PubSubEventHandler::new(ws_hub, storage);
        
        let manager = Self {
            config,
            handler: Arc::new(handler),
        };
        
        (manager, event_rx)
    }
    
    // Start subscription (placeholder - actual implementation would use Google Cloud Pub/Sub client)
    pub async fn start_subscription(&self) -> Result<()> {
        info!(
            "Starting Pub/Sub subscription: project={}, subscription={}",
            self.config.project_id, self.config.subscription_name
        );
        
        // TODO: Implement actual Google Cloud Pub/Sub subscription
        // For now, this is a placeholder that would:
        // 1. Create Pub/Sub client
        // 2. Subscribe to the configured subscription
        // 3. Process messages and send to event handler
        
        warn!("Pub/Sub subscription not yet implemented - using mock");
        
        // Start a mock event generator for testing
        let handler = self.handler.clone();
        tokio::spawn(async move {
            Self::mock_event_generator(handler).await;
        });
        
        Ok(())
    }
    
    // Mock event generator for testing
    async fn mock_event_generator(handler: Arc<PubSubEventHandler>) {
        use crate::models::events::EventBuilder;
        
        let event_builder = EventBuilder::new("mock-service".to_string());
        let mut interval = tokio::time::interval(tokio::time::Duration::from_secs(30));
        
        loop {
            interval.tick().await;
            
            // Generate a mock analysis completed event
            let event = CollaborationEvent::AnalysisCompleted {
                analysis_id: uuid::Uuid::new_v4().to_string(),
                user_id: UserId::from_string("mock-user".to_string()),
                repository_url: "https://github.com/example/repo".to_string(),
                status: AnalysisStatus::Completed,
                duration_secs: 120,
                metrics: crate::models::events::AnalysisMetrics {
                    total_files: 100,
                    total_lines: 10000,
                    languages_detected: vec!["rust".to_string(), "typescript".to_string()],
                    complexity_score: 7.5,
                    quality_score: Some(8.2),
                    security_score: Some(9.1),
                },
            };
            
            if let Ok(message) = event_builder.build_message(
                "analysis_completed".to_string(),
                event,
                None,
            ) {
                let _ = handler.event_tx.send(message).await;
            }
        }
    }
}

// Helper function to truncate strings
fn truncate_string(s: &str, max_len: usize) -> String {
    if s.len() <= max_len {
        s.to_string()
    } else {
        format!("{}...", &s[..max_len - 3])
    }
}

// Helper function to extract repository name from URL
fn extract_repo_name(url: &str) -> &str {
    url.split('/')
        .last()
        .unwrap_or(url)
        .trim_end_matches(".git")
}