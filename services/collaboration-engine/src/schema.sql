-- Collaboration Engine Spanner Schema
-- This schema defines the tables for teams, sessions, messages, and participants

-- Teams table
CREATE TABLE teams (
    id STRING(36) NOT NULL,
    name STRING(100) NOT NULL,
    description STRING(500),
    created_by STRING(100) NOT NULL,
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL,
    settings JSON NOT NULL,
) PRIMARY KEY (id);

CREATE INDEX idx_teams_created_by ON teams(created_by);

-- Team members table
CREATE TABLE team_members (
    id STRING(36) NOT NULL,
    team_id STRING(36) NOT NULL,
    user_id STRING(100) NOT NULL,
    email STRING(100) NOT NULL,
    name STRING(100) NOT NULL,
    role STRING(20) NOT NULL, -- owner, admin, member, viewer
    joined_at TIMESTAMP NOT NULL,
    permissions JSON NOT NULL,
) PRIMARY KEY (id),
  INTERLEAVE IN PARENT teams ON DELETE CASCADE;

CREATE UNIQUE INDEX idx_team_members_team_user ON team_members(team_id, user_id);
CREATE INDEX idx_team_members_user_id ON team_members(user_id);

-- Sessions table
CREATE TABLE sessions (
    id STRING(36) NOT NULL,
    team_id STRING(36) NOT NULL,
    name STRING(100) NOT NULL,
    description STRING(500),
    created_by STRING(100) NOT NULL,
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL,
    ended_at TIMESTAMP,
    status STRING(20) NOT NULL, -- active, paused, ended, archived
    settings JSON NOT NULL,
) PRIMARY KEY (id);

CREATE INDEX idx_sessions_team_id ON sessions(team_id);
CREATE INDEX idx_sessions_created_by ON sessions(created_by);
CREATE INDEX idx_sessions_status ON sessions(status);

-- Session participants table
CREATE TABLE session_participants (
    id STRING(36) NOT NULL,
    session_id STRING(36) NOT NULL,
    user_id STRING(100) NOT NULL,
    email STRING(100) NOT NULL,
    name STRING(100) NOT NULL,
    joined_at TIMESTAMP NOT NULL,
    status STRING(20) NOT NULL, -- active, idle, disconnected
) PRIMARY KEY (id),
  INTERLEAVE IN PARENT sessions ON DELETE CASCADE;

CREATE UNIQUE INDEX idx_session_participants_session_user ON session_participants(session_id, user_id);
CREATE INDEX idx_session_participants_user_id ON session_participants(user_id);

-- Messages table
CREATE TABLE messages (
    id STRING(36) NOT NULL,
    session_id STRING(36) NOT NULL,
    user_id STRING(100) NOT NULL,
    content JSON NOT NULL, -- MessageContent enum serialized as JSON
    created_at TIMESTAMP NOT NULL,
    edited BOOL NOT NULL DEFAULT (false),
    deleted BOOL NOT NULL DEFAULT (false),
    metadata JSON NOT NULL, -- MessageMetadata serialized as JSON
) PRIMARY KEY (id),
  INTERLEAVE IN PARENT sessions ON DELETE CASCADE;

CREATE INDEX idx_messages_session_id_created_at ON messages(session_id, created_at DESC);
CREATE INDEX idx_messages_user_id ON messages(user_id);

-- Message edits table (for edit history)
CREATE TABLE message_edits (
    id STRING(36) NOT NULL,
    message_id STRING(36) NOT NULL,
    edited_by STRING(100) NOT NULL,
    edited_at TIMESTAMP NOT NULL,
    previous_content JSON NOT NULL,
) PRIMARY KEY (id),
  INTERLEAVE IN PARENT messages ON DELETE CASCADE;

-- Analysis shares table (for tracking shared analyses)
CREATE TABLE analysis_shares (
    id STRING(36) NOT NULL,
    session_id STRING(36) NOT NULL,
    analysis_id STRING(100) NOT NULL,
    shared_by STRING(100) NOT NULL,
    shared_at TIMESTAMP NOT NULL,
    analysis_type STRING(50) NOT NULL,
    summary STRING(1000),
    metadata JSON,
) PRIMARY KEY (id);

CREATE INDEX idx_analysis_shares_session_id ON analysis_shares(session_id);
CREATE INDEX idx_analysis_shares_analysis_id ON analysis_shares(analysis_id);

-- User presence table (for real-time presence tracking)
CREATE TABLE user_presence (
    user_id STRING(100) NOT NULL,
    session_id STRING(36) NOT NULL,
    connection_id STRING(36) NOT NULL,
    status STRING(20) NOT NULL, -- online, away, offline
    last_seen TIMESTAMP NOT NULL,
    cursor_position JSON,
    metadata JSON,
) PRIMARY KEY (user_id, session_id);

CREATE INDEX idx_user_presence_session_id ON user_presence(session_id);

-- Shared analyses table (for integration with Analysis Engine)
CREATE TABLE shared_analyses (
    id STRING(36) NOT NULL,
    session_id STRING(36) NOT NULL,
    analysis_id STRING(100) NOT NULL,
    shared_by STRING(100) NOT NULL,
    shared_at TIMESTAMP NOT NULL,
    title STRING(500) NOT NULL,
    repository_url STRING(1000) NOT NULL,
    branch STRING(100),
    commit_sha STRING(100),
    highlights JSON NOT NULL, -- Array of CodeHighlight objects
    summary JSON NOT NULL, -- AnalysisSummary object
) PRIMARY KEY (id);

CREATE INDEX idx_shared_analyses_session_id ON shared_analyses(session_id);
CREATE INDEX idx_shared_analyses_analysis_id ON shared_analyses(analysis_id);

-- Shared queries table (for integration with Query Intelligence)
CREATE TABLE shared_queries (
    id STRING(36) NOT NULL,
    session_id STRING(36) NOT NULL,
    query_id STRING(100) NOT NULL,
    shared_by STRING(100) NOT NULL,
    shared_at TIMESTAMP NOT NULL,
    query_text STRING(1000) NOT NULL,
    query_type STRING(50) NOT NULL,
    results JSON NOT NULL, -- Array of QueryResult objects
    confidence_score FLOAT64 NOT NULL,
) PRIMARY KEY (id);

CREATE INDEX idx_shared_queries_session_id ON shared_queries(session_id);
CREATE INDEX idx_shared_queries_query_id ON shared_queries(query_id);
CREATE INDEX idx_user_presence_last_seen ON user_presence(last_seen);