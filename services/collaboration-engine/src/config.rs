use anyhow::{Context, Result};
use serde::{Deserialize, Serialize};
use std::env;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Config {
    // Server configuration
    pub port: u16,
    pub metrics_port: u16,
    pub environment: String,

    // JWT configuration
    pub jwt_secret: String,
    pub jwt_issuer: String,
    pub jwt_audience: String,
    pub jwt_expiry_hours: u64,

    // Redis configuration
    pub redis_url: String,
    pub redis_pool_size: u32,
    pub redis_connection_timeout_ms: u64,

    // Spanner configuration
    pub gcp_project_id: String,
    pub spanner_instance_id: String,
    pub spanner_database_id: String,
    pub spanner_pool_min: u32,
    pub spanner_pool_max: u32,

    // WebSocket configuration
    pub ws_heartbeat_interval_secs: u64,
    pub ws_client_timeout_secs: u64,
    pub ws_max_connections_per_user: usize,
    pub ws_max_message_size: usize,

    // Rate limiting
    pub rate_limit_requests_per_minute: u64,
    pub rate_limit_websocket_messages_per_minute: u32,

    // Session configuration
    pub max_session_participants: usize,
    pub session_idle_timeout_minutes: u64,
    pub max_message_history: usize,

    // Feature flags
    pub enable_message_persistence: bool,
    pub enable_presence_tracking: bool,
    pub enable_analytics: bool,
    
    // Integration configuration
    pub analysis_engine_url: String,
    pub query_intelligence_url: Option<String>,
    pub pubsub_subscription: String,
    pub pubsub_max_concurrent_messages: u32,
    pub service_timeout_secs: u64,
    pub integration_cache_ttl_secs: u64,
}

impl Config {
    pub fn from_env() -> Result<Self> {
        dotenvy::dotenv().ok();

        Ok(Config {
            // Server configuration
            port: env::var("PORT")
                .unwrap_or_else(|_| "8003".to_string())
                .parse()
                .context("Invalid PORT")?,
            metrics_port: env::var("METRICS_PORT")
                .unwrap_or_else(|_| "9003".to_string())
                .parse()
                .context("Invalid METRICS_PORT")?,
            environment: env::var("ENVIRONMENT").unwrap_or_else(|_| "development".to_string()),

            // JWT configuration
            jwt_secret: env::var("JWT_SECRET").context("JWT_SECRET is required")?,
            jwt_issuer: env::var("JWT_ISSUER").unwrap_or_else(|_| "episteme".to_string()),
            jwt_audience: env::var("JWT_AUDIENCE")
                .unwrap_or_else(|_| "episteme-platform".to_string()),
            jwt_expiry_hours: env::var("JWT_EXPIRY_HOURS")
                .unwrap_or_else(|_| "24".to_string())
                .parse()
                .context("Invalid JWT_EXPIRY_HOURS")?,

            // Redis configuration
            redis_url: env::var("REDIS_URL")
                .unwrap_or_else(|_| "redis://127.0.0.1:6379".to_string()),
            redis_pool_size: env::var("REDIS_POOL_SIZE")
                .unwrap_or_else(|_| "10".to_string())
                .parse()
                .context("Invalid REDIS_POOL_SIZE")?,
            redis_connection_timeout_ms: env::var("REDIS_CONNECTION_TIMEOUT_MS")
                .unwrap_or_else(|_| "5000".to_string())
                .parse()
                .context("Invalid REDIS_CONNECTION_TIMEOUT_MS")?,

            // Spanner configuration
            gcp_project_id: env::var("GCP_PROJECT_ID").context("GCP_PROJECT_ID is required")?,
            spanner_instance_id: env::var("SPANNER_INSTANCE_ID")
                .context("SPANNER_INSTANCE_ID is required")?,
            spanner_database_id: env::var("SPANNER_DATABASE_ID")
                .context("SPANNER_DATABASE_ID is required")?,
            spanner_pool_min: env::var("SPANNER_POOL_MIN")
                .unwrap_or_else(|_| "1".to_string())
                .parse()
                .context("Invalid SPANNER_POOL_MIN")?,
            spanner_pool_max: env::var("SPANNER_POOL_MAX")
                .unwrap_or_else(|_| "10".to_string())
                .parse()
                .context("Invalid SPANNER_POOL_MAX")?,

            // WebSocket configuration
            ws_heartbeat_interval_secs: env::var("WS_HEARTBEAT_INTERVAL_SECS")
                .unwrap_or_else(|_| "30".to_string())
                .parse()
                .context("Invalid WS_HEARTBEAT_INTERVAL_SECS")?,
            ws_client_timeout_secs: env::var("WS_CLIENT_TIMEOUT_SECS")
                .unwrap_or_else(|_| "60".to_string())
                .parse()
                .context("Invalid WS_CLIENT_TIMEOUT_SECS")?,
            ws_max_connections_per_user: env::var("WS_MAX_CONNECTIONS_PER_USER")
                .unwrap_or_else(|_| "5".to_string())
                .parse()
                .context("Invalid WS_MAX_CONNECTIONS_PER_USER")?,
            ws_max_message_size: env::var("WS_MAX_MESSAGE_SIZE")
                .unwrap_or_else(|_| "65536".to_string()) // 64KB
                .parse()
                .context("Invalid WS_MAX_MESSAGE_SIZE")?,

            // Rate limiting
            rate_limit_requests_per_minute: env::var("RATE_LIMIT_REQUESTS_PER_MINUTE")
                .unwrap_or_else(|_| "100".to_string())
                .parse()
                .context("Invalid RATE_LIMIT_REQUESTS_PER_MINUTE")?,
            rate_limit_websocket_messages_per_minute: env::var(
                "RATE_LIMIT_WEBSOCKET_MESSAGES_PER_MINUTE",
            )
            .unwrap_or_else(|_| "60".to_string())
            .parse()
            .context("Invalid RATE_LIMIT_WEBSOCKET_MESSAGES_PER_MINUTE")?,

            // Session configuration
            max_session_participants: env::var("MAX_SESSION_PARTICIPANTS")
                .unwrap_or_else(|_| "50".to_string())
                .parse()
                .context("Invalid MAX_SESSION_PARTICIPANTS")?,
            session_idle_timeout_minutes: env::var("SESSION_IDLE_TIMEOUT_MINUTES")
                .unwrap_or_else(|_| "30".to_string())
                .parse()
                .context("Invalid SESSION_IDLE_TIMEOUT_MINUTES")?,
            max_message_history: env::var("MAX_MESSAGE_HISTORY")
                .unwrap_or_else(|_| "100".to_string())
                .parse()
                .context("Invalid MAX_MESSAGE_HISTORY")?,

            // Feature flags
            enable_message_persistence: env::var("ENABLE_MESSAGE_PERSISTENCE")
                .unwrap_or_else(|_| "true".to_string())
                .parse()
                .context("Invalid ENABLE_MESSAGE_PERSISTENCE")?,
            enable_presence_tracking: env::var("ENABLE_PRESENCE_TRACKING")
                .unwrap_or_else(|_| "true".to_string())
                .parse()
                .context("Invalid ENABLE_PRESENCE_TRACKING")?,
            enable_analytics: env::var("ENABLE_ANALYTICS")
                .unwrap_or_else(|_| "true".to_string())
                .parse()
                .context("Invalid ENABLE_ANALYTICS")?,
                
            // Integration configuration
            analysis_engine_url: env::var("ANALYSIS_ENGINE_URL")
                .unwrap_or_else(|_| "http://localhost:8001".to_string()),
            query_intelligence_url: env::var("QUERY_INTELLIGENCE_URL").ok(),
            pubsub_subscription: env::var("PUBSUB_SUBSCRIPTION")
                .unwrap_or_else(|_| "collaboration-events".to_string()),
            pubsub_max_concurrent_messages: env::var("PUBSUB_MAX_CONCURRENT_MESSAGES")
                .unwrap_or_else(|_| "100".to_string())
                .parse()
                .context("Invalid PUBSUB_MAX_CONCURRENT_MESSAGES")?,
            service_timeout_secs: env::var("SERVICE_TIMEOUT_SECS")
                .unwrap_or_else(|_| "30".to_string())
                .parse()
                .context("Invalid SERVICE_TIMEOUT_SECS")?,
            integration_cache_ttl_secs: env::var("INTEGRATION_CACHE_TTL_SECS")
                .unwrap_or_else(|_| "300".to_string())
                .parse()
                .context("Invalid INTEGRATION_CACHE_TTL_SECS")?,
        })
    }

    pub fn is_production(&self) -> bool {
        self.environment == "production"
    }

    pub fn is_development(&self) -> bool {
        self.environment == "development"
    }
}