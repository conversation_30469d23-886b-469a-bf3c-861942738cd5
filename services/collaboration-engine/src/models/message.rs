use super::{Timestamp, UserId};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Message {
    pub id: String,
    pub session_id: String,
    pub user_id: UserId,
    pub content: MessageContent,
    pub created_at: Timestamp,
    pub updated_at: Option<Timestamp>,
    pub edited: bool,
    pub deleted: bool,
    pub metadata: MessageMetadata,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type", rename_all = "snake_case")]
pub enum MessageContent {
    Text {
        text: String,
    },
    Code {
        language: String,
        code: String,
        filename: Option<String>,
    },
    AnalysisShare {
        analysis_id: String,
        analysis_type: String,
        summary: String,
        highlights: Vec<AnalysisHighlight>,
    },
    SystemEvent {
        event_type: SystemEventType,
        description: String,
    },
    FileShare {
        file_id: String,
        filename: String,
        file_type: String,
        size_bytes: u64,
    },
    Reaction {
        target_message_id: String,
        emoji: String,
    },
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct AnalysisHighlight {
    pub title: String,
    pub value: serde_json::Value,
    pub severity: Option<HighlightSeverity>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum HighlightSeverity {
    Info,
    Warning,
    Error,
    Success,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "snake_case")]
pub enum SystemEventType {
    UserJoined,
    UserLeft,
    SessionStarted,
    SessionEnded,
    AnalysisCompleted,
    IntegrationEvent,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MessageMetadata {
    pub reply_to: Option<String>,
    pub mentions: Vec<UserId>,
    pub tags: Vec<String>,
    pub reactions: HashMap<String, Vec<UserId>>, // emoji -> user_ids
    pub thread_id: Option<String>,
    pub edit_history: Vec<MessageEdit>,
}

impl Default for MessageMetadata {
    fn default() -> Self {
        Self {
            reply_to: None,
            mentions: Vec::new(),
            tags: Vec::new(),
            reactions: HashMap::new(),
            thread_id: None,
            edit_history: Vec::new(),
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MessageEdit {
    pub edited_at: Timestamp,
    pub edited_by: UserId,
    pub previous_content: MessageContent,
}

// DTOs for API requests/responses
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SendMessageRequest {
    pub session_id: String,
    pub content: MessageContent,
    pub metadata: Option<MessageMetadataInput>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MessageMetadataInput {
    pub reply_to: Option<String>,
    pub mentions: Option<Vec<String>>,
    pub tags: Option<Vec<String>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateMessageRequest {
    pub content: MessageContent,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MessageResponse {
    pub id: String,
    pub session_id: String,
    pub user: MessageUserInfo,
    pub content: MessageContent,
    pub created_at: Timestamp,
    pub updated_at: Option<Timestamp>,
    pub edited: bool,
    pub metadata: MessageMetadata,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MessageUserInfo {
    pub user_id: UserId,
    pub email: String,
    pub name: String,
    pub avatar_url: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MessageListResponse {
    pub messages: Vec<MessageResponse>,
    pub has_more: bool,
    pub next_cursor: Option<String>,
}

// Real-time message events
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "event", rename_all = "snake_case")]
pub enum MessageEvent {
    NewMessage {
        message: MessageResponse,
    },
    MessageUpdated {
        message_id: String,
        content: MessageContent,
        updated_by: UserId,
        updated_at: Timestamp,
    },
    MessageDeleted {
        message_id: String,
        deleted_by: UserId,
        deleted_at: Timestamp,
    },
    ReactionAdded {
        message_id: String,
        emoji: String,
        user_id: UserId,
    },
    ReactionRemoved {
        message_id: String,
        emoji: String,
        user_id: UserId,
    },
}