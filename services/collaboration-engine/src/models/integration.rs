use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

use crate::models::{UserId, Timestamp};

// Analysis Engine integration models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SharedAnalysis {
    pub id: String,
    pub session_id: String,
    pub analysis_id: String,
    pub shared_by: UserId,
    pub shared_at: Timestamp,
    pub title: String,
    pub repository_url: String,
    pub branch: Option<String>,
    pub commit_sha: Option<String>,
    pub highlights: Vec<CodeHighlight>,
    pub summary: AnalysisSummary,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CodeHighlight {
    pub id: String,
    pub file_path: String,
    pub start_line: u32,
    pub end_line: u32,
    pub language: String,
    pub code_snippet: String,
    pub annotation: Option<String>,
    pub created_by: UserId,
    pub created_at: Timestamp,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct AnalysisSummary {
    pub total_files: u32,
    pub total_lines: u64,
    pub languages: Vec<LanguageSummary>,
    pub complexity_score: f64,
    pub security_score: Option<f64>,
    pub quality_score: Option<f64>,
    pub key_patterns: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LanguageSummary {
    pub language: String,
    pub files: u32,
    pub lines: u64,
    pub percentage: f64,
}

// Query Intelligence integration models
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SharedQuery {
    pub id: String,
    pub session_id: String,
    pub query_id: String,
    pub shared_by: UserId,
    pub shared_at: Timestamp,
    pub query_text: String,
    pub query_type: QueryType,
    pub results: Vec<QueryResult>,
    pub confidence_score: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "snake_case")]
pub enum QueryType {
    CodeSearch,
    PatternDetection,
    DocumentationLookup,
    SecurityScan,
    PerformanceAnalysis,
    Custom,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QueryResult {
    pub id: String,
    pub file_path: String,
    pub match_type: MatchType,
    pub relevance_score: f64,
    pub preview: String,
    pub metadata: serde_json::Value,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "snake_case")]
pub enum MatchType {
    ExactMatch,
    SemanticMatch,
    PatternMatch,
    StructuralMatch,
}

// Cross-service events
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "event_type", rename_all = "snake_case")]
pub enum IntegrationEvent {
    AnalysisCompleted {
        analysis_id: String,
        user_id: UserId,
        repository_url: String,
        status: AnalysisStatus,
        completion_time: DateTime<Utc>,
    },
    QueryExecuted {
        query_id: String,
        user_id: UserId,
        query_text: String,
        result_count: u32,
        execution_time_ms: u64,
    },
    PatternDetected {
        analysis_id: String,
        pattern_type: String,
        confidence: f64,
        file_count: u32,
    },
    SecurityIssueFound {
        analysis_id: String,
        severity: SecuritySeverity,
        issue_type: String,
        file_path: String,
        line_number: Option<u32>,
    },
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "snake_case")]
pub enum AnalysisStatus {
    Pending,
    InProgress,
    Completed,
    Failed,
    Cancelled,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "snake_case")]
pub enum SecuritySeverity {
    Critical,
    High,
    Medium,
    Low,
    Info,
}

// Request/Response models for integration endpoints
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ShareAnalysisRequest {
    pub analysis_id: String,
    pub highlights: Option<Vec<CreateHighlightRequest>>,
    pub annotation: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateHighlightRequest {
    pub file_path: String,
    pub start_line: u32,
    pub end_line: u32,
    pub annotation: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ShareQueryRequest {
    pub query_text: String,
    pub query_type: QueryType,
    pub include_results: Option<bool>,
    pub max_results: Option<u32>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IntegrationConfig {
    pub analysis_engine_url: String,
    pub query_intelligence_url: Option<String>, // Optional since it's not implemented yet
    pub service_timeout_secs: u64,
    pub max_retries: u32,
    pub cache_ttl_secs: u64,
}

impl Default for IntegrationConfig {
    fn default() -> Self {
        Self {
            analysis_engine_url: "http://localhost:8001".to_string(),
            query_intelligence_url: Some("http://localhost:8002".to_string()),
            service_timeout_secs: 30,
            max_retries: 3,
            cache_ttl_secs: 300, // 5 minutes
        }
    }
}

// Helper implementations
impl SharedAnalysis {
    pub fn new(
        session_id: String,
        analysis_id: String,
        shared_by: UserId,
        title: String,
        repository_url: String,
        summary: AnalysisSummary,
    ) -> Self {
        Self {
            id: Uuid::new_v4().to_string(),
            session_id,
            analysis_id,
            shared_by,
            shared_at: Timestamp::now(),
            title,
            repository_url,
            branch: None,
            commit_sha: None,
            highlights: Vec::new(),
            summary,
        }
    }
}

impl CodeHighlight {
    pub fn new(
        file_path: String,
        start_line: u32,
        end_line: u32,
        language: String,
        code_snippet: String,
        created_by: UserId,
    ) -> Self {
        Self {
            id: Uuid::new_v4().to_string(),
            file_path,
            start_line,
            end_line,
            language,
            code_snippet,
            annotation: None,
            created_by,
            created_at: Timestamp::now(),
        }
    }
}

impl SharedQuery {
    pub fn new(
        session_id: String,
        query_text: String,
        query_type: QueryType,
        shared_by: UserId,
        results: Vec<QueryResult>,
        confidence_score: f64,
    ) -> Self {
        Self {
            id: Uuid::new_v4().to_string(),
            session_id,
            query_id: Uuid::new_v4().to_string(),
            shared_by,
            shared_at: Timestamp::now(),
            query_text,
            query_type,
            results,
            confidence_score,
        }
    }
}