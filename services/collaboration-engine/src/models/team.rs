use super::{<PERSON>tamp, UserId, UserInfo};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Team {
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub created_by: UserId,
    pub created_at: Timestamp,
    pub updated_at: Timestamp,
    pub members: HashMap<String, TeamMember>, // user_id -> TeamMember
    pub settings: TeamSettings,
    pub metadata: HashMap<String, serde_json::Value>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TeamMember {
    pub user_id: UserId,
    pub email: String,
    pub name: String,
    pub role: TeamR<PERSON>,
    pub joined_at: Timestamp,
    pub last_active: Option<Timestamp>,
    pub permissions: TeamPermissions,
}

#[derive(Debug, <PERSON><PERSON>, Co<PERSON>, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "lowercase")]
pub enum TeamRole {
    Owner,
    <PERSON><PERSON>,
    Member,
    Viewer,
}

impl TeamRole {
    pub fn default_permissions(&self) -> TeamPermissions {
        match self {
            TeamRole::Owner => TeamPermissions {
                can_manage_team: true,
                can_invite_members: true,
                can_remove_members: true,
                can_create_sessions: true,
                can_delete_sessions: true,
                can_view_analytics: true,
                can_manage_integrations: true,
            },
            TeamRole::Admin => TeamPermissions {
                can_manage_team: true,
                can_invite_members: true,
                can_remove_members: true,
                can_create_sessions: true,
                can_delete_sessions: true,
                can_view_analytics: true,
                can_manage_integrations: false,
            },
            TeamRole::Member => TeamPermissions {
                can_manage_team: false,
                can_invite_members: true,
                can_remove_members: false,
                can_create_sessions: true,
                can_delete_sessions: false,
                can_view_analytics: true,
                can_manage_integrations: false,
            },
            TeamRole::Viewer => TeamPermissions {
                can_manage_team: false,
                can_invite_members: false,
                can_remove_members: false,
                can_create_sessions: false,
                can_delete_sessions: false,
                can_view_analytics: false,
                can_manage_integrations: false,
            },
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TeamPermissions {
    pub can_manage_team: bool,
    pub can_invite_members: bool,
    pub can_remove_members: bool,
    pub can_create_sessions: bool,
    pub can_delete_sessions: bool,
    pub can_view_analytics: bool,
    pub can_manage_integrations: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TeamSettings {
    pub max_members: usize,
    pub allow_guest_access: bool,
    pub require_2fa: bool,
    pub session_recording_enabled: bool,
    pub retention_days: u32,
    pub allowed_domains: Vec<String>,
}

impl Default for TeamSettings {
    fn default() -> Self {
        Self {
            max_members: 100,
            allow_guest_access: false,
            require_2fa: false,
            session_recording_enabled: true,
            retention_days: 30,
            allowed_domains: Vec::new(),
        }
    }
}

// DTOs for API requests/responses
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateTeamRequest {
    pub name: String,
    pub description: Option<String>,
    pub settings: Option<TeamSettings>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateTeamRequest {
    pub name: Option<String>,
    pub description: Option<String>,
    pub settings: Option<TeamSettings>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AddTeamMemberRequest {
    pub email: String,
    pub role: TeamRole,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateMemberRoleRequest {
    pub user_id: String,
    pub role: TeamRole,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TeamResponse {
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub member_count: usize,
    pub created_at: Timestamp,
    pub updated_at: Timestamp,
    pub user_role: TeamRole,
    pub settings: TeamSettings,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TeamMemberResponse {
    pub user_info: UserInfo,
    pub role: TeamRole,
    pub joined_at: Timestamp,
    pub last_active: Option<Timestamp>,
}