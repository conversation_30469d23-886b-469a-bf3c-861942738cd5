pub mod message;
pub mod session;
pub mod team;
pub mod integration;
pub mod events;

pub use message::*;
pub use session::*;
pub use team::*;
pub use integration::*;
pub use events::*;

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

// Common types used across models
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub struct UserId(pub String);

impl UserId {
    pub fn new() -> Self {
        Self(Uuid::new_v4().to_string())
    }

    pub fn from_string(id: String) -> Self {
        Self(id)
    }
}

impl Default for UserId {
    fn default() -> Self {
        Self::new()
    }
}

impl AsRef<str> for UserId {
    fn as_ref(&self) -> &str {
        &self.0
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserInfo {
    pub user_id: UserId,
    pub email: String,
    pub name: String,
    pub avatar_url: Option<String>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Timestamp(pub DateTime<Utc>);

impl Timestamp {
    pub fn now() -> Self {
        Self(Utc::now())
    }
}

impl Default for Timestamp {
    fn default() -> Self {
        Self::now()
    }
}

// Presence information for real-time tracking
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PresenceInfo {
    pub user_id: UserId,
    pub session_id: String,
    pub status: PresenceStatus,
    pub last_seen: Timestamp,
    pub cursor_position: Option<CursorPosition>,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "lowercase")]
pub enum PresenceStatus {
    Online,
    Away,
    Offline,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CursorPosition {
    pub x: f64,
    pub y: f64,
    pub element_id: Option<String>,
}

// WebSocket message types
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type", rename_all = "snake_case")]
pub enum WebSocketMessage {
    // Connection management
    Connected {
        connection_id: String,
        user_info: UserInfo,
    },
    Disconnected {
        connection_id: String,
        reason: Option<String>,
    },
    Error {
        code: String,
        message: String,
        details: Option<serde_json::Value>,
    },

    // Session events
    JoinSession {
        session_id: String,
        user_info: UserInfo,
    },
    LeaveSession {
        session_id: String,
        user_id: UserId,
    },
    SessionUpdate {
        session_id: String,
        update: SessionUpdate,
    },

    // Messages
    MessageSent {
        message: Message,
    },
    MessageReceived {
        message: Message,
    },

    // Presence
    PresenceUpdate {
        presence: PresenceInfo,
    },
    CursorMove {
        session_id: String,
        user_id: UserId,
        position: CursorPosition,
    },

    // Analysis sharing
    AnalysisShared {
        session_id: String,
        analysis_id: String,
        shared_by: UserId,
        analysis_type: String,
        summary: String,
    },

    // Team notifications
    TeamNotification {
        team_id: String,
        notification: TeamNotification,
    },

    // System
    Ping,
    Pong,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SessionUpdate {
    pub field: String,
    pub value: serde_json::Value,
    pub updated_by: UserId,
    pub timestamp: Timestamp,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type", rename_all = "snake_case")]
pub enum TeamNotification {
    MemberAdded {
        user_info: UserInfo,
        added_by: UserId,
    },
    MemberRemoved {
        user_id: UserId,
        removed_by: UserId,
    },
    RoleChanged {
        user_id: UserId,
        old_role: TeamRole,
        new_role: TeamRole,
        changed_by: UserId,
    },
    TeamUpdated {
        fields: Vec<String>,
        updated_by: UserId,
    },
}