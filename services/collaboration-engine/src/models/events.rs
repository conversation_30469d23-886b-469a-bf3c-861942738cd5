use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

use crate::models::{UserId, Timestamp};
use crate::models::integration::{AnalysisStatus, SecuritySeverity, QueryType};

// Pub/Sub event wrapper
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct PubSubMessage {
    pub id: String,
    pub event_type: String,
    pub source_service: String,
    pub timestamp: DateTime<Utc>,
    pub correlation_id: Option<String>,
    pub data: serde_json::Value,
}

// Event types for cross-service communication
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type", rename_all = "snake_case")]
pub enum CollaborationEvent {
    // Analysis Engine events
    AnalysisStarted {
        analysis_id: String,
        user_id: UserId,
        repository_url: String,
        branch: Option<String>,
        session_id: Option<String>,
    },
    AnalysisCompleted {
        analysis_id: String,
        user_id: UserId,
        repository_url: String,
        status: AnalysisStatus,
        duration_secs: u64,
        metrics: AnalysisMetrics,
    },
    AnalysisShared {
        analysis_id: String,
        session_id: String,
        shared_by: UserId,
        participant_count: u32,
    },
    
    // Query Intelligence events
    QueryExecuted {
        query_id: String,
        user_id: UserId,
        query_text: String,
        query_type: QueryType,
        result_count: u32,
        confidence_score: f64,
        session_id: Option<String>,
    },
    QueryShared {
        query_id: String,
        session_id: String,
        shared_by: UserId,
        query_text: String,
    },
    
    // Pattern Mining events
    PatternDetected {
        pattern_id: String,
        analysis_id: String,
        pattern_type: String,
        confidence: f64,
        occurrences: u32,
        session_id: Option<String>,
    },
    PatternPublished {
        pattern_id: String,
        publisher_id: UserId,
        pattern_name: String,
        description: String,
        visibility: PatternVisibility,
    },
    
    // Security events
    SecurityIssueDetected {
        issue_id: String,
        analysis_id: String,
        severity: SecuritySeverity,
        issue_type: String,
        cve_id: Option<String>,
        affected_files: Vec<String>,
        session_id: Option<String>,
    },
    
    // Collaboration events
    SessionCreated {
        session_id: String,
        team_id: String,
        created_by: UserId,
        participant_count: u32,
    },
    SessionEnded {
        session_id: String,
        duration_secs: u64,
        message_count: u64,
        shared_items_count: u32,
    },
    TeamActivityUpdate {
        team_id: String,
        activity_type: TeamActivityType,
        user_id: UserId,
        metadata: serde_json::Value,
    },
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AnalysisMetrics {
    pub total_files: u32,
    pub total_lines: u64,
    pub languages_detected: Vec<String>,
    pub complexity_score: f64,
    pub quality_score: Option<f64>,
    pub security_score: Option<f64>,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "snake_case")]
pub enum PatternVisibility {
    Private,
    Team,
    Public,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "snake_case")]
pub enum TeamActivityType {
    AnalysisShared,
    QueryShared,
    PatternCreated,
    DiscussionStarted,
    FileHighlighted,
    SecurityReviewed,
}

// Event handlers trait
pub trait EventHandler: Send + Sync {
    fn can_handle(&self, event_type: &str) -> bool;
    fn handle_event(&self, message: PubSubMessage) -> Result<(), EventError>;
}

#[derive(Debug, thiserror::Error)]
pub enum EventError {
    #[error("Failed to deserialize event: {0}")]
    DeserializationError(String),
    
    #[error("Event handler not found for type: {0}")]
    HandlerNotFound(String),
    
    #[error("Event processing failed: {0}")]
    ProcessingError(String),
    
    #[error("Service communication error: {0}")]
    ServiceError(String),
}

// Event builder for creating well-formed events
pub struct EventBuilder {
    source_service: String,
}

impl EventBuilder {
    pub fn new(source_service: String) -> Self {
        Self { source_service }
    }
    
    pub fn build_message<T: Serialize>(
        &self,
        event_type: String,
        data: T,
        correlation_id: Option<String>,
    ) -> Result<PubSubMessage, serde_json::Error> {
        Ok(PubSubMessage {
            id: uuid::Uuid::new_v4().to_string(),
            event_type,
            source_service: self.source_service.clone(),
            timestamp: Utc::now(),
            correlation_id,
            data: serde_json::to_value(data)?,
        })
    }
}

// Event subscription configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EventSubscriptionConfig {
    pub project_id: String,
    pub subscription_name: String,
    pub max_concurrent_messages: u32,
    pub ack_deadline_seconds: u32,
    pub retry_policy: RetryPolicy,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RetryPolicy {
    pub max_attempts: u32,
    pub initial_backoff_ms: u64,
    pub max_backoff_ms: u64,
    pub backoff_multiplier: f64,
}

impl Default for EventSubscriptionConfig {
    fn default() -> Self {
        Self {
            project_id: "episteme".to_string(),
            subscription_name: "collaboration-events".to_string(),
            max_concurrent_messages: 100,
            ack_deadline_seconds: 60,
            retry_policy: RetryPolicy {
                max_attempts: 5,
                initial_backoff_ms: 1000,
                max_backoff_ms: 60000,
                backoff_multiplier: 2.0,
            },
        }
    }
}

// Helper functions for event routing
pub fn extract_session_id(event: &CollaborationEvent) -> Option<String> {
    match event {
        CollaborationEvent::AnalysisStarted { session_id, .. } => session_id.clone(),
        CollaborationEvent::AnalysisShared { session_id, .. } => Some(session_id.clone()),
        CollaborationEvent::QueryExecuted { session_id, .. } => session_id.clone(),
        CollaborationEvent::QueryShared { session_id, .. } => Some(session_id.clone()),
        CollaborationEvent::PatternDetected { session_id, .. } => session_id.clone(),
        CollaborationEvent::SecurityIssueDetected { session_id, .. } => session_id.clone(),
        CollaborationEvent::SessionCreated { session_id, .. } => Some(session_id.clone()),
        CollaborationEvent::SessionEnded { session_id, .. } => Some(session_id.clone()),
        _ => None,
    }
}

pub fn get_event_priority(event: &CollaborationEvent) -> EventPriority {
    match event {
        CollaborationEvent::SecurityIssueDetected { severity, .. } => {
            match severity {
                SecuritySeverity::Critical => EventPriority::Critical,
                SecuritySeverity::High => EventPriority::High,
                _ => EventPriority::Normal,
            }
        }
        CollaborationEvent::AnalysisCompleted { .. } => EventPriority::High,
        CollaborationEvent::SessionCreated { .. } => EventPriority::High,
        _ => EventPriority::Normal,
    }
}

#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord)]
pub enum EventPriority {
    Low,
    Normal,
    High,
    Critical,
}