use super::{Message, Timestamp, UserId};
use serde::{Deserialize, Serialize};
use std::collections::{HashMap, HashSet};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Session {
    pub id: String,
    pub team_id: String,
    pub name: String,
    pub description: Option<String>,
    pub created_by: UserId,
    pub created_at: Timestamp,
    pub updated_at: Timestamp,
    pub ended_at: Option<Timestamp>,
    pub status: SessionStatus,
    pub participants: HashSet<UserId>,
    pub settings: SessionSettings,
    pub metadata: HashMap<String, serde_json::Value>,
}

#[derive(Debug, <PERSON>lone, Copy, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "lowercase")]
pub enum SessionStatus {
    Active,
    Paused,
    Ended,
    Archived,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct SessionSettings {
    pub max_participants: usize,
    pub allow_anonymous: bool,
    pub record_session: bool,
    pub auto_save_interval_seconds: u64,
    pub idle_timeout_minutes: u64,
    pub enable_voice: bool,
    pub enable_video: bool,
}

impl Default for SessionSettings {
    fn default() -> Self {
        Self {
            max_participants: 50,
            allow_anonymous: false,
            record_session: true,
            auto_save_interval_seconds: 60,
            idle_timeout_minutes: 30,
            enable_voice: false,
            enable_video: false,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SessionParticipant {
    pub user_id: UserId,
    pub joined_at: Timestamp,
    pub last_active: Timestamp,
    pub connection_count: usize,
    pub permissions: SessionPermissions,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SessionPermissions {
    pub can_send_messages: bool,
    pub can_share_screen: bool,
    pub can_share_analysis: bool,
    pub can_invite_others: bool,
    pub can_moderate: bool,
}

impl Default for SessionPermissions {
    fn default() -> Self {
        Self {
            can_send_messages: true,
            can_share_screen: true,
            can_share_analysis: true,
            can_invite_others: false,
            can_moderate: false,
        }
    }
}

// Session-related events for real-time updates
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "event", rename_all = "snake_case")]
pub enum SessionEvent {
    ParticipantJoined {
        user_id: UserId,
        timestamp: Timestamp,
    },
    ParticipantLeft {
        user_id: UserId,
        timestamp: Timestamp,
    },
    MessageSent {
        message: Message,
    },
    AnalysisShared {
        analysis_id: String,
        shared_by: UserId,
        timestamp: Timestamp,
    },
    SessionUpdated {
        updated_fields: Vec<String>,
        updated_by: UserId,
        timestamp: Timestamp,
    },
    SessionEnded {
        ended_by: UserId,
        timestamp: Timestamp,
    },
}

// DTOs for API requests/responses
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateSessionRequest {
    pub team_id: String,
    pub name: String,
    pub description: Option<String>,
    pub settings: Option<SessionSettings>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateSessionRequest {
    pub name: Option<String>,
    pub description: Option<String>,
    pub status: Option<SessionStatus>,
    pub settings: Option<SessionSettings>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SessionResponse {
    pub id: String,
    pub team_id: String,
    pub name: String,
    pub description: Option<String>,
    pub created_by: UserId,
    pub created_at: Timestamp,
    pub updated_at: Timestamp,
    pub status: SessionStatus,
    pub participant_count: usize,
    pub settings: SessionSettings,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SessionDetailsResponse {
    pub session: SessionResponse,
    pub participants: Vec<SessionParticipantInfo>,
    pub recent_messages: Vec<Message>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SessionParticipantInfo {
    pub user_id: UserId,
    pub email: String,
    pub name: String,
    pub status: ParticipantStatus,
    pub joined_at: Timestamp,
    pub last_active: Timestamp,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "lowercase")]
pub enum ParticipantStatus {
    Active,
    Idle,
    Disconnected,
}