use opentelemetry::{
    global,
    trace::{Span, SpanKind, Status, TraceContextExt},
    Context, KeyValue,
};
use opentelemetry_sdk::{
    propagation::TraceContextPropagator,
    trace::{self, RandomIdGenerator, Sampler},
    Resource,
    runtime,
};
use opentelemetry_otlp::{ExportConfig, WithExportConfig};
use std::time::Duration;
use tracing::{error, info};
use tracing_opentelemetry::OpenTelemetryLayer;
use tracing_subscriber::{layer::SubscriberExt, Registry};

/// Initialize OpenTelemetry tracing
pub fn init_tracing(service_name: &str, otlp_endpoint: Option<String>) -> Result<(), Box<dyn std::error::Error>> {
    // Set global propagator
    global::set_text_map_propagator(TraceContextPropagator::new());
    
    // Configure resource
    let resource = Resource::new(vec![
        KeyValue::new("service.name", service_name.to_string()),
        KeyValue::new("service.version", env!("CARGO_PKG_VERSION")),
        KeyValue::new("service.environment", std::env::var("ENVIRONMENT").unwrap_or_else(|_| "development".to_string())),
    ]);
    
    // Create tracer
    let tracer = if let Some(endpoint) = otlp_endpoint {
        // Production: Export to OTLP endpoint
        let export_config = ExportConfig {
            endpoint,
            timeout: Duration::from_secs(10),
            protocol: opentelemetry_otlp::Protocol::Grpc,
        };
        
        opentelemetry_otlp::new_pipeline()
            .tracing()
            .with_exporter(
                opentelemetry_otlp::new_exporter()
                    .tonic()
                    .with_export_config(export_config)
            )
            .with_trace_config(
                trace::config()
                    .with_sampler(Sampler::AlwaysOn)
                    .with_id_generator(RandomIdGenerator::default())
                    .with_max_events_per_span(64)
                    .with_max_attributes_per_span(16)
                    .with_resource(resource)
            )
            .install_batch(runtime::Tokio)?
    } else {
        // Development: No telemetry export, just in-memory
        info!("No OTLP endpoint configured, skipping telemetry export");
        return Ok(()); // Skip telemetry setup for development
    };
    
    // Create OpenTelemetry layer
    let telemetry = OpenTelemetryLayer::new(tracer);
    
    // Set as global default
    let subscriber = Registry::default().with(telemetry);
    tracing::subscriber::set_global_default(subscriber)?;
    
    info!("OpenTelemetry tracing initialized");
    Ok(())
}

/// Shutdown OpenTelemetry tracing
pub fn shutdown_tracing() {
    global::shutdown_tracer_provider();
}

/// Helper to create a span with standard attributes
pub fn create_span(name: &str, kind: SpanKind) -> tracing::Span {
    tracing::span!(
        tracing::Level::INFO,
        "collaboration",
        otel.name = name,
        otel.kind = ?kind,
        service.name = "collaboration-engine",
    )
}

/// WebSocket-specific span attributes
pub fn add_websocket_attributes(span: &tracing::Span, connection_id: &str, user_id: &str) {
    span.record("websocket.connection_id", &connection_id);
    span.record("websocket.user_id", &user_id);
}

/// Session-specific span attributes
pub fn add_session_attributes(span: &tracing::Span, session_id: &str, team_id: &str) {
    span.record("collaboration.session_id", &session_id);
    span.record("collaboration.team_id", &team_id);
}

/// Message-specific span attributes
pub fn add_message_attributes(span: &tracing::Span, message_type: &str, size: usize) {
    span.record("message.type", &message_type);
    span.record("message.size", &size);
}

/// Storage operation span attributes
pub fn add_storage_attributes(span: &tracing::Span, storage_type: &str, operation: &str) {
    span.record("storage.type", &storage_type);
    span.record("storage.operation", &operation);
}

/// Error span attributes
pub fn add_error_attributes(span: &tracing::Span, error: &str, error_type: &str) {
    span.record("error", &true);
    span.record("error.message", &error);
    span.record("error.type", &error_type);
}

/// Instrumentation macros for common operations
#[macro_export]
macro_rules! trace_websocket_operation {
    ($name:expr, $connection_id:expr, $user_id:expr, $code:block) => {{
        let span = $crate::tracing::create_span($name, opentelemetry::trace::SpanKind::Server);
        $crate::tracing::add_websocket_attributes(&span, $connection_id, $user_id);
        let _enter = span.enter();
        $code
    }};
}

#[macro_export]
macro_rules! trace_storage_operation {
    ($storage:expr, $operation:expr, $code:block) => {{
        let span = $crate::tracing::create_span(
            &format!("{}.{}", $storage, $operation),
            opentelemetry::trace::SpanKind::Client
        );
        $crate::tracing::add_storage_attributes(&span, $storage, $operation);
        let _enter = span.enter();
        $code
    }};
}

#[macro_export]
macro_rules! trace_with_error {
    ($span:expr, $code:block) => {{
        match $code {
            Ok(result) => Ok(result),
            Err(e) => {
                $crate::tracing::add_error_attributes(&$span, &e.to_string(), "error");
                Err(e)
            }
        }
    }};
}

/// Context propagation helpers
pub fn inject_context(context: &Context) -> std::collections::HashMap<String, String> {
    let mut carrier = std::collections::HashMap::new();
    global::get_text_map_propagator(|propagator| {
        propagator.inject_context(context, &mut carrier);
    });
    carrier
}

pub fn extract_context(carrier: &std::collections::HashMap<String, String>) -> Context {
    global::get_text_map_propagator(|propagator| {
        propagator.extract(carrier)
    })
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_span_creation() {
        let span = create_span("test_operation", SpanKind::Internal);
        add_websocket_attributes(&span, "conn-123", "user-456");
        
        // Span should be created without panic
        let _guard = span.enter();
    }
    
    #[test]
    fn test_context_propagation() {
        let context = Context::current();
        let carrier = inject_context(&context);
        let extracted = extract_context(&carrier);
        
        // Context should round-trip
        assert_eq!(context.span().span_context(), extracted.span().span_context());
    }
}