use std::time::Duration;
use tokio::runtime::{Builder, Runtime};
use tracing::info;

/// Create an optimized Tokio runtime for low-latency WebSocket operations
pub fn create_optimized_runtime() -> Result<Runtime, std::io::Error> {
    let num_workers = num_cpus::get();
    info!("Creating optimized runtime with {} worker threads", num_workers);
    
    Builder::new_multi_thread()
        .worker_threads(num_workers)
        .max_blocking_threads(32)
        .thread_name("collab-worker")
        .thread_stack_size(2 * 1024 * 1024) // 2MB stack
        .enable_all()
        .build()
}

/// Runtime tuning recommendations for <50ms latency
pub struct RuntimeTuning;

impl RuntimeTuning {
    /// Get OS-level tuning recommendations
    pub fn get_recommendations() -> Vec<&'static str> {
        vec![
            "Increase file descriptor limits: ulimit -n 65536",
            "Tune TCP settings for low latency:",
            "  - net.core.rmem_default = 134217728",
            "  - net.core.wmem_default = 134217728", 
            "  - net.core.rmem_max = 134217728",
            "  - net.core.wmem_max = 134217728",
            "  - net.ipv4.tcp_mem = 134217728 134217728 134217728",
            "  - net.ipv4.tcp_rmem = 4096 87380 134217728",
            "  - net.ipv4.tcp_wmem = 4096 65536 134217728",
            "  - net.core.netdev_max_backlog = 30000",
            "  - net.ipv4.tcp_congestion_control = bbr",
            "  - net.ipv4.tcp_mtu_probing = 1",
            "Disable CPU frequency scaling for consistent performance",
            "Pin worker threads to specific CPU cores if possible",
        ]
    }
    
    /// Apply runtime-level optimizations
    pub fn apply_optimizations() {
        // Set thread priorities if available
        #[cfg(target_os = "linux")]
        {
            // Attempt to set real-time scheduling
            unsafe {
                let tid = libc::syscall(libc::SYS_gettid) as libc::pid_t;
                let param = libc::sched_param { sched_priority: 50 };
                libc::sched_setscheduler(tid, libc::SCHED_FIFO, &param);
            }
        }
        
        // Disable Nagle's algorithm for WebSocket connections
        // This is handled per-connection in the WebSocket setup
        
        info!("Runtime optimizations applied");
    }
}

/// Performance monitoring for runtime
pub struct RuntimeMonitor {
    start_time: std::time::Instant,
}

impl RuntimeMonitor {
    pub fn new() -> Self {
        Self {
            start_time: std::time::Instant::now(),
        }
    }
    
    /// Start monitoring task execution times
    pub fn start_monitoring() {
        // This would integrate with tokio-console or custom metrics
        // For now, we'll use the metrics crate
        
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(10));
            loop {
                interval.tick().await;
                
                // Emit runtime metrics
                let handle = tokio::runtime::Handle::current();
                let metrics = handle.metrics();
                
                // TODO: Add runtime worker gauge to the metrics module
                // For now, we'll skip this metric until proper gauge support is added
                let _num_workers = metrics.num_workers();
                
                // Would add more detailed metrics with tokio_metrics crate
            }
        });
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_runtime_creation() {
        let runtime = create_optimized_runtime().unwrap();
        assert!(runtime.metrics().num_workers() > 0);
    }
}