use lazy_static::lazy_static;
use prometheus::{
    register_counter_vec, register_gauge_vec, register_histogram_vec,
    CounterV<PERSON>, GaugeVec, HistogramVec,
};
use std::time::Duration;

lazy_static! {
    // WebSocket Metrics
    pub static ref WEBSOCKET_CONNECTIONS: GaugeVec = register_gauge_vec!(
        "websocket_connections_total",
        "Total number of active WebSocket connections",
        &["status"]
    ).unwrap();
    
    pub static ref WEBSOCKET_MESSAGES: CounterVec = register_counter_vec!(
        "websocket_messages_total",
        "Total number of WebSocket messages",
        &["type", "direction"]
    ).unwrap();
    
    pub static ref WEBSOCKET_LATENCY: HistogramVec = register_histogram_vec!(
        "websocket_latency_seconds",
        "WebSocket message processing latency",
        &["message_type"],
        vec![0.001, 0.005, 0.010, 0.025, 0.050, 0.100, 0.250, 0.500, 1.0]
    ).unwrap();
    
    pub static ref WEBSOCKET_MESSAGE_SIZE: HistogramVec = register_histogram_vec!(
        "websocket_message_size_bytes",
        "Size of WebSocket messages in bytes",
        &["type", "direction"],
        vec![100.0, 500.0, 1000.0, 5000.0, 10000.0, 50000.0, 100000.0]
    ).unwrap();
    
    // Session Metrics
    pub static ref ACTIVE_SESSIONS: GaugeVec = register_gauge_vec!(
        "collaboration_sessions_active",
        "Number of active collaboration sessions",
        &["team_id"]
    ).unwrap();
    
    pub static ref SESSION_PARTICIPANTS: GaugeVec = register_gauge_vec!(
        "collaboration_session_participants",
        "Number of participants per session",
        &["session_id"]
    ).unwrap();
    
    pub static ref SESSION_DURATION: HistogramVec = register_histogram_vec!(
        "collaboration_session_duration_seconds",
        "Duration of collaboration sessions",
        &["team_id"],
        vec![60.0, 300.0, 600.0, 1800.0, 3600.0, 7200.0, 14400.0]
    ).unwrap();
    
    // Rate Limiting Metrics
    pub static ref RATE_LIMIT_EXCEEDED: CounterVec = register_counter_vec!(
        "rate_limit_exceeded_total",
        "Number of rate limit violations",
        &["limit_type", "client_type"]
    ).unwrap();
    
    pub static ref RATE_LIMIT_CHECKS: CounterVec = register_counter_vec!(
        "rate_limit_checks_total",
        "Total rate limit checks performed",
        &["limit_type", "result"]
    ).unwrap();
    
    // Authentication Metrics
    pub static ref AUTH_ATTEMPTS: CounterVec = register_counter_vec!(
        "auth_attempts_total",
        "Authentication attempts",
        &["method", "result"]
    ).unwrap();
    
    pub static ref JWT_OPERATIONS: CounterVec = register_counter_vec!(
        "jwt_operations_total",
        "JWT token operations",
        &["operation", "result"]
    ).unwrap();
    
    pub static ref TOKEN_EXPIRY: HistogramVec = register_histogram_vec!(
        "jwt_token_expiry_seconds",
        "Time until JWT token expiry",
        &["token_type"],
        vec![300.0, 600.0, 900.0, 1800.0, 3600.0, 7200.0, 86400.0]
    ).unwrap();
    
    // Storage Metrics
    pub static ref STORAGE_OPERATIONS: CounterVec = register_counter_vec!(
        "storage_operations_total",
        "Storage operations by type",
        &["storage", "operation", "result"]
    ).unwrap();
    
    pub static ref STORAGE_LATENCY: HistogramVec = register_histogram_vec!(
        "storage_latency_seconds",
        "Storage operation latency",
        &["storage", "operation"],
        vec![0.001, 0.005, 0.010, 0.025, 0.050, 0.100, 0.250, 0.500, 1.0]
    ).unwrap();
    
    // Performance Metrics
    pub static ref MESSAGE_BATCH_SIZE: HistogramVec = register_histogram_vec!(
        "websocket_message_batch_size",
        "Number of messages in a batch",
        &["connection_id"],
        vec![1.0, 2.0, 5.0, 10.0, 20.0, 50.0]
    ).unwrap();
    
    pub static ref COMPRESSION_RATIO: HistogramVec = register_histogram_vec!(
        "websocket_compression_ratio",
        "Compression ratio for messages",
        &["message_type"],
        vec![0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0]
    ).unwrap();
    
    // Error Metrics
    pub static ref ERRORS_TOTAL: CounterVec = register_counter_vec!(
        "collaboration_errors_total",
        "Total number of errors",
        &["component", "error_type"]
    ).unwrap();
    
    // Auth Metrics
    pub static ref AUTH_SERVICE_TOKEN_ISSUED: CounterVec = register_counter_vec!(
        "auth_service_token_issued",
        "Total number of service tokens issued",
        &["service"]
    ).unwrap();
    
    pub static ref AUTH_LOGIN_FAILED: CounterVec = register_counter_vec!(
        "auth_login_failed",
        "Total number of failed login attempts",
        &[]
    ).unwrap();
}

/// Record WebSocket connection event
pub fn record_websocket_connection(connected: bool) {
    if connected {
        WEBSOCKET_CONNECTIONS.with_label_values(&["connected"]).inc();
    } else {
        WEBSOCKET_CONNECTIONS.with_label_values(&["connected"]).dec();
    }
}

/// Record WebSocket message
pub fn record_websocket_message(message_type: &str, direction: &str, size: usize) {
    WEBSOCKET_MESSAGES
        .with_label_values(&[message_type, direction])
        .inc();
    
    WEBSOCKET_MESSAGE_SIZE
        .with_label_values(&[message_type, direction])
        .observe(size as f64);
}

/// Record WebSocket latency
pub fn record_websocket_latency(message_type: &str, duration: Duration) {
    WEBSOCKET_LATENCY
        .with_label_values(&[message_type])
        .observe(duration.as_secs_f64());
}

/// Record rate limit check
pub fn record_rate_limit_check(limit_type: &str, allowed: bool) {
    let result = if allowed { "allowed" } else { "denied" };
    RATE_LIMIT_CHECKS
        .with_label_values(&[limit_type, result])
        .inc();
    
    if !allowed {
        RATE_LIMIT_EXCEEDED
            .with_label_values(&[limit_type, "unknown"])
            .inc();
    }
}

/// Record authentication attempt
pub fn record_auth_attempt(method: &str, success: bool) {
    let result = if success { "success" } else { "failure" };
    AUTH_ATTEMPTS
        .with_label_values(&[method, result])
        .inc();
}

/// Record JWT operation
pub fn record_jwt_operation(operation: &str, success: bool) {
    let result = if success { "success" } else { "failure" };
    JWT_OPERATIONS
        .with_label_values(&[operation, result])
        .inc();
}

/// Record storage operation
pub fn record_storage_operation(storage: &str, operation: &str, duration: Duration, success: bool) {
    let result = if success { "success" } else { "failure" };
    
    STORAGE_OPERATIONS
        .with_label_values(&[storage, operation, result])
        .inc();
    
    STORAGE_LATENCY
        .with_label_values(&[storage, operation])
        .observe(duration.as_secs_f64());
}

/// Record error
pub fn record_error(component: &str, error_type: &str) {
    ERRORS_TOTAL
        .with_label_values(&[component, error_type])
        .inc();
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_metrics_registration() {
        // Metrics should be registered without panic
        record_websocket_connection(true);
        record_websocket_connection(false);
        
        record_websocket_message("ping", "inbound", 100);
        record_websocket_latency("message", Duration::from_millis(25));
        
        record_rate_limit_check("websocket", true);
        record_auth_attempt("jwt", true);
        
        record_storage_operation("redis", "get", Duration::from_millis(5), true);
        record_error("websocket", "parse_error");
    }
}