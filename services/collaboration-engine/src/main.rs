use anyhow::Result;
use axum::{
    routing::{get, post},
    Router,
};
use std::net::SocketAddr;
use tower_http::{
    compression::CompressionLayer,
    cors::{Any, CorsLayer},
    trace::{DefaultMakeSpan, DefaultOnResponse, TraceLayer},
};
use tracing::{info, Level};

mod api;
mod config;
mod errors;
mod models;
mod services;
mod storage;
mod websocket;

use crate::api::create_api_router;
use crate::config::Config;
use crate::storage::create_storage_clients;

fn main() -> Result<()> {
    // Create optimized runtime for low-latency WebSocket operations
    let runtime = collaboration_engine::runtime::create_optimized_runtime()?;
    
    // Apply runtime optimizations
    collaboration_engine::runtime::RuntimeTuning::apply_optimizations();
    
    // Run the async main
    runtime.block_on(async_main())
}

async fn async_main() -> Result<()> {
    // Initialize tracing with OpenTelemetry if configured
    let otlp_endpoint = std::env::var("OTLP_ENDPOINT").ok();
    if let Some(endpoint) = otlp_endpoint.clone() {
        // Production: Use OpenTelemetry
        collaboration_engine::tracing::init_tracing("collaboration-engine", Some(endpoint))?;
        info!("OpenTelemetry tracing enabled");
    } else {
        // Development: Use standard tracing
        tracing_subscriber::fmt()
            .with_target(false)
            .with_thread_ids(true)
            .with_level(true)
            .with_ansi(true)
            .with_env_filter(
                tracing_subscriber::EnvFilter::try_from_default_env()
                    .unwrap_or_else(|_| "collaboration_engine=debug,tower_http=debug".into()),
            )
            .init();
    }

    info!("Starting Episteme Collaboration Engine");

    // Load configuration
    let config = Config::from_env()?;

    // Initialize storage clients
    let storage = create_storage_clients(&config).await?;

    // Create application state
    let app_state = api::AppState::new(config.clone(), storage.clone());

    // Start Pub/Sub event handler (if configured)
    if !config.pubsub_subscription.is_empty() {
        let subscription_config = crate::models::EventSubscriptionConfig {
            project_id: config.gcp_project_id.clone(),
            subscription_name: config.pubsub_subscription.clone(),
            max_concurrent_messages: config.pubsub_max_concurrent_messages,
            ..Default::default()
        };
        
        let (pubsub_manager, event_rx) = crate::services::pubsub_handler::PubSubSubscriptionManager::new(
            subscription_config,
            app_state.ws_hub.clone(),
            storage.clone(),
        );
        
        // Start the event processor
        let handler = pubsub_manager.handler.clone();
        tokio::spawn(async move {
            handler.start_processing(event_rx).await;
        });
        
        // Start the subscription (currently using mock)
        tokio::spawn(async move {
            if let Err(e) = pubsub_manager.start_subscription().await {
                tracing::error!("Failed to start Pub/Sub subscription: {}", e);
            }
        });
        
        info!("Started Pub/Sub event handler");
    }

    // Build the application router
    let app = Router::new()
        .nest("/api/v1", create_api_router(app_state.clone()))
        .route("/health", get(api::handlers::health::health_check))
        .route("/health/ready", get(api::handlers::health::readiness_check))
        .route("/health/live", get(api::handlers::health::liveness_check))
        .layer(
            CorsLayer::new()
                .allow_origin(Any)
                .allow_methods(Any)
                .allow_headers(Any),
        )
        .layer(CompressionLayer::new())
        .layer(
            TraceLayer::new_for_http()
                .make_span_with(DefaultMakeSpan::new().level(Level::INFO))
                .on_response(DefaultOnResponse::new().level(Level::INFO)),
        );

    // Bind to the configured address
    let addr = SocketAddr::from(([0, 0, 0, 0], config.port));
    let listener = tokio::net::TcpListener::bind(addr).await?;
    
    info!("Collaboration Engine listening on {}", addr);

    // Start runtime performance monitoring
    collaboration_engine::runtime::RuntimeMonitor::new();
    collaboration_engine::runtime::RuntimeMonitor::start_monitoring();

    // Start Prometheus metrics server on a separate port
    tokio::spawn(start_metrics_server(config.metrics_port));

    // Run the server
    axum::serve(listener, app)
        .with_graceful_shutdown(shutdown_signal())
        .await?;

    info!("Collaboration Engine shutdown complete");
    
    // Shutdown OpenTelemetry if enabled
    if otlp_endpoint.is_some() {
        collaboration_engine::tracing::shutdown_tracing();
    }
    
    Ok(())
}

async fn shutdown_signal() {
    let ctrl_c = async {
        tokio::signal::ctrl_c()
            .await
            .expect("failed to install Ctrl+C handler");
    };

    #[cfg(unix)]
    let terminate = async {
        tokio::signal::unix::signal(tokio::signal::unix::SignalKind::terminate())
            .expect("failed to install signal handler")
            .recv()
            .await;
    };

    #[cfg(not(unix))]
    let terminate = std::future::pending::<()>();

    tokio::select! {
        _ = ctrl_c => {
            info!("Received Ctrl+C, initiating graceful shutdown");
        }
        _ = terminate => {
            info!("Received terminate signal, initiating graceful shutdown");
        }
    }
}

async fn start_metrics_server(port: u16) {
    use axum::routing::get;
    use metrics_exporter_prometheus::PrometheusBuilder;
    use std::net::SocketAddr;

    // Install Prometheus metrics exporter
    let builder = PrometheusBuilder::new();
    let handle = builder
        .install_recorder()
        .expect("failed to install Prometheus recorder");

    // Create metrics endpoint
    let app = Router::new().route("/metrics", get(move || async move { handle.render() }));

    let addr = SocketAddr::from(([0, 0, 0, 0], port));
    let listener = tokio::net::TcpListener::bind(addr)
        .await
        .expect("failed to bind metrics server");

    info!("Metrics server listening on {}", addr);

    axum::serve(listener, app)
        .await
        .expect("metrics server failed");
}