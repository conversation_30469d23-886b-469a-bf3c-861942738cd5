use crate::{
    config::Config,
    models::{UserId, WebSocketMessage},
    storage::redis_pubsub::RedisPubSubClient,
    websocket::{
        connection::Connection, 
        performance::{MessageBatcher, PerformanceConfig, PerformanceMetrics},
        rate_limiter::WebSocketRateLimiter
    },
};
use anyhow::Result;
use dashmap::DashMap;
use serde::{Deserialize, Serialize};
use std::{collections::HashSet, sync::Arc, time::Duration};
use tokio::sync::RwLock;
use tracing::{debug, error, info, warn};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HubMetrics {
    pub active_connections: usize,
    pub active_sessions: usize,
    pub connections_by_user: usize,
    pub messages_per_minute: u64,
}

pub struct WebSocketHub {
    config: Config,
    connections: Arc<DashMap<String, Connection>>, // connection_id -> Connection
    user_connections: Arc<DashMap<UserId, HashSet<String>>>, // user_id -> connection_ids
    session_participants: Arc<DashMap<String, HashSet<String>>>, // session_id -> connection_ids
    redis_pubsub: Arc<RedisPubSubClient>,
    rate_limiter: Arc<WebSocketRateLimiter>,
    message_batcher: Arc<MessageBatcher>,
    performance_metrics: Arc<PerformanceMetrics>,
    metrics: Arc<RwLock<HubMetrics>>,
}

impl WebSocketHub {
    pub fn new(config: Config, redis_client: Arc<crate::storage::redis_client::RedisClient>) -> Self {
        let redis_pubsub = Arc::new(RedisPubSubClient::new(redis_client.clone()));
        let rate_limiter = Arc::new(WebSocketRateLimiter::new(
            config.rate_limit_websocket_messages_per_minute,
            config.rate_limit_websocket_messages_per_minute * 3, // 3x for cursor updates
            Duration::from_secs(60),
            redis_client,
        ));
        
        // Initialize performance components
        let perf_config = PerformanceConfig::default();
        let message_batcher = Arc::new(MessageBatcher::new(perf_config));
        let performance_metrics = Arc::new(PerformanceMetrics::new());
        
        let hub = Self {
            config,
            connections: Arc::new(DashMap::new()),
            user_connections: Arc::new(DashMap::new()),
            session_participants: Arc::new(DashMap::new()),
            redis_pubsub: redis_pubsub.clone(),
            rate_limiter,
            message_batcher,
            performance_metrics,
            metrics: Arc::new(RwLock::new(HubMetrics {
                active_connections: 0,
                active_sessions: 0,
                connections_by_user: 0,
                messages_per_minute: 0,
            })),
        };
        
        // Start Redis subscription handler
        let hub_clone = hub.clone();
        tokio::spawn(async move {
            if let Err(e) = hub_clone.start_redis_subscription().await {
                error!("Failed to start Redis subscription: {}", e);
            }
        });
        
        hub
    }
    
    pub async fn register_connection(&self, connection_id: String, connection: Connection) {
        let user_id = connection.user_info.user_id.clone();
        
        // Add to connections map
        self.connections.insert(connection_id.clone(), connection);
        
        // Add to user connections
        self.user_connections
            .entry(user_id.clone())
            .or_insert_with(HashSet::new)
            .insert(connection_id.clone());
        
        // Update metrics
        let mut metrics = self.metrics.write().await;
        metrics.active_connections = self.connections.len();
        metrics.connections_by_user = self.user_connections.len();
        
        info!(
            "Connection registered - ID: {}, User: {:?}, Total connections: {}",
            connection_id, user_id, metrics.active_connections
        );
    }
    
    pub async fn unregister_connection(&self, connection_id: &str) {
        if let Some((_, connection)) = self.connections.remove(connection_id) {
            let user_id = connection.user_info.user_id.clone();
            
            // Remove from user connections
            if let Some(mut user_conns) = self.user_connections.get_mut(&user_id) {
                user_conns.remove(connection_id);
                if user_conns.is_empty() {
                    drop(user_conns);
                    self.user_connections.remove(&user_id);
                }
            }
            
            // Remove from all sessions
            for mut session_conns in self.session_participants.iter_mut() {
                session_conns.remove(connection_id);
            }
            
            // Clean up empty sessions
            self.session_participants.retain(|_, conns| !conns.is_empty());
            
            // Update metrics
            let mut metrics = self.metrics.write().await;
            metrics.active_connections = self.connections.len();
            metrics.connections_by_user = self.user_connections.len();
            metrics.active_sessions = self.session_participants.len();
            
            info!(
                "Connection unregistered - ID: {}, User: {:?}, Remaining connections: {}",
                connection_id, user_id, metrics.active_connections
            );
        }
    }
    
    pub async fn get_user_connection_count(&self, user_id: &UserId) -> usize {
        self.user_connections
            .get(user_id)
            .map(|conns| conns.len())
            .unwrap_or(0)
    }
    
    pub async fn add_to_session(&self, session_id: &str, connection_id: &str) {
        self.session_participants
            .entry(session_id.to_string())
            .or_insert_with(HashSet::new)
            .insert(connection_id.to_string());
        
        // Update metrics
        let mut metrics = self.metrics.write().await;
        metrics.active_sessions = self.session_participants.len();
        
        debug!(
            "Connection {} added to session {}, Total sessions: {}",
            connection_id, session_id, metrics.active_sessions
        );
    }
    
    pub async fn remove_from_session(&self, session_id: &str, connection_id: &str) {
        if let Some(mut session_conns) = self.session_participants.get_mut(session_id) {
            session_conns.remove(connection_id);
            if session_conns.is_empty() {
                drop(session_conns);
                self.session_participants.remove(session_id);
            }
        }
        
        // Update metrics
        let mut metrics = self.metrics.write().await;
        metrics.active_sessions = self.session_participants.len();
        
        debug!(
            "Connection {} removed from session {}, Total sessions: {}",
            connection_id, session_id, metrics.active_sessions
        );
    }
    
    pub async fn broadcast_to_session(&self, session_id: &str, message: WebSocketMessage, exclude: Option<&str>) -> Result<()> {
        self.broadcast_to_session_with_exclusion(session_id, message, exclude).await
    }
    
    pub async fn broadcast_to_session_with_exclusion(&self, session_id: &str, message: WebSocketMessage, exclude: Option<&str>) -> Result<()> {
        // Broadcast via Redis for multi-instance support
        let channel = format!("session:{}", session_id);
        self.redis_pubsub.publish(&channel, &message).await?;
        
        // Also send directly to local connections
        if let Some(session_conns) = self.session_participants.get(session_id) {
            for conn_id in session_conns.iter() {
                if Some(conn_id.as_str()) == exclude {
                    continue;
                }
                
                if let Some(connection) = self.connections.get(conn_id) {
                    if let Err(e) = connection.send_message(message.clone()).await {
                        warn!("Failed to send message to connection {}: {}", conn_id, e);
                    }
                }
            }
        }
        
        Ok(())
    }
    
    pub async fn send_to_user(&self, user_id: &UserId, message: WebSocketMessage) -> Result<()> {
        // Send via Redis for multi-instance support
        let channel = format!("user:{}", user_id.as_ref());
        self.redis_pubsub.publish(&channel, &message).await?;
        
        // Also send directly to local connections
        if let Some(user_conns) = self.user_connections.get(user_id) {
            for conn_id in user_conns.iter() {
                if let Some(connection) = self.connections.get(conn_id) {
                    if let Err(e) = connection.send_message(message.clone()).await {
                        warn!("Failed to send message to connection {}: {}", conn_id, e);
                    }
                }
            }
        }
        
        Ok(())
    }
    
    pub async fn send_to_connection(&self, connection_id: &str, message: WebSocketMessage) -> Result<()> {
        if let Some(connection) = self.connections.get(connection_id) {
            connection.send_message(message).await
        } else {
            Err(anyhow::anyhow!("Connection not found: {}", connection_id))
        }
    }
    
    pub async fn get_metrics(&self) -> HubMetrics {
        self.metrics.read().await.clone()
    }
    
    pub fn get_rate_limiter(&self) -> Arc<WebSocketRateLimiter> {
        self.rate_limiter.clone()
    }
    
    pub fn get_message_batcher(&self) -> Arc<MessageBatcher> {
        self.message_batcher.clone()
    }
    
    pub fn get_performance_metrics(&self) -> Arc<PerformanceMetrics> {
        self.performance_metrics.clone()
    }
    
    async fn start_redis_subscription(&self) -> Result<()> {
        info!("Starting Redis subscription handler");
        
        // Subscribe to relevant channels
        let channels = vec![
            "broadcast:*".to_string(),
            "session:*".to_string(),
            "user:*".to_string(),
        ];
        
        let mut receiver = self.redis_pubsub.subscribe(channels).await?;
        
        while let Some((channel, message)) = receiver.recv().await {
            debug!("Received message from Redis channel: {}", channel);
            
            match serde_json::from_str::<WebSocketMessage>(&message) {
                Ok(ws_message) => {
                    // Route message based on channel type
                    if channel.starts_with("session:") {
                        let session_id = channel.strip_prefix("session:").unwrap();
                        if let Err(e) = self.handle_session_message(session_id, ws_message).await {
                            error!("Failed to handle session message: {}", e);
                        }
                    } else if channel.starts_with("user:") {
                        let user_id = channel.strip_prefix("user:").unwrap();
                        if let Err(e) = self.handle_user_message(user_id, ws_message).await {
                            error!("Failed to handle user message: {}", e);
                        }
                    } else if channel.starts_with("broadcast:") {
                        if let Err(e) = self.handle_broadcast_message(ws_message).await {
                            error!("Failed to handle broadcast message: {}", e);
                        }
                    }
                }
                Err(e) => {
                    error!("Failed to deserialize Redis message: {}", e);
                }
            }
        }
        
        Ok(())
    }
    
    async fn handle_session_message(&self, session_id: &str, message: WebSocketMessage) -> Result<()> {
        if let Some(session_conns) = self.session_participants.get(session_id) {
            for conn_id in session_conns.iter() {
                if let Some(connection) = self.connections.get(conn_id) {
                    if let Err(e) = connection.send_message(message.clone()).await {
                        warn!("Failed to send session message to connection {}: {}", conn_id, e);
                    }
                }
            }
        }
        Ok(())
    }
    
    async fn handle_user_message(&self, user_id: &str, message: WebSocketMessage) -> Result<()> {
        let user_id = UserId::from_string(user_id.to_string());
        if let Some(user_conns) = self.user_connections.get(&user_id) {
            for conn_id in user_conns.iter() {
                if let Some(connection) = self.connections.get(conn_id) {
                    if let Err(e) = connection.send_message(message.clone()).await {
                        warn!("Failed to send user message to connection {}: {}", conn_id, e);
                    }
                }
            }
        }
        Ok(())
    }
    
    async fn handle_broadcast_message(&self, message: WebSocketMessage) -> Result<()> {
        for connection in self.connections.iter() {
            if let Err(e) = connection.send_message(message.clone()).await {
                warn!("Failed to send broadcast message to connection {}: {}", connection.key(), e);
            }
        }
        Ok(())
    }
}

// Clone implementation for WebSocketHub
impl Clone for WebSocketHub {
    fn clone(&self) -> Self {
        Self {
            config: self.config.clone(),
            connections: self.connections.clone(),
            user_connections: self.user_connections.clone(),
            session_participants: self.session_participants.clone(),
            redis_pubsub: self.redis_pubsub.clone(),
            rate_limiter: self.rate_limiter.clone(),
            message_batcher: self.message_batcher.clone(),
            performance_metrics: self.performance_metrics.clone(),
            metrics: self.metrics.clone(),
        }
    }
}