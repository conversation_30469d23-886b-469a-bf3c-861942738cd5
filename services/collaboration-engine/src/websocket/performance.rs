use std::sync::Arc;
use std::time::{Duration, Instant};
use dashmap::DashMap;
use tokio::sync::{mpsc, RwLock};
use tracing::{debug, warn};

use crate::models::{UserId, WebSocketMessage};

/// Performance configuration for WebSocket optimization
#[derive(Debug, Clone)]
pub struct PerformanceConfig {
    /// Enable message batching
    pub enable_batching: bool,
    /// Maximum batch size
    pub max_batch_size: usize,
    /// Maximum batch wait time
    pub max_batch_wait: Duration,
    /// Enable compression for large messages
    pub enable_compression: bool,
    /// Compression threshold in bytes
    pub compression_threshold: usize,
    /// Enable binary encoding for performance
    pub enable_binary_encoding: bool,
}

impl Default for PerformanceConfig {
    fn default() -> Self {
        Self {
            enable_batching: true,
            max_batch_size: 10,
            max_batch_wait: Duration::from_millis(10), // 10ms max wait
            enable_compression: true,
            compression_threshold: 1024, // 1KB
            enable_binary_encoding: false, // For now, stick with JSO<PERSON>
        }
    }
}

/// Message batcher for optimizing WebSocket throughput
pub struct MessageBatcher {
    config: PerformanceConfig,
    batches: Arc<DashMap<String, MessageBatch>>, // connection_id -> batch
}

#[derive(Debug)]
struct MessageBatch {
    messages: Vec<WebSocketMessage>,
    created_at: Instant,
    tx: mpsc::Sender<Vec<WebSocketMessage>>,
}

impl MessageBatcher {
    pub fn new(config: PerformanceConfig) -> Self {
        Self {
            config,
            batches: Arc::new(DashMap::new()),
        }
    }
    
    /// Add a message to the batch for a connection
    pub async fn add_message(
        &self,
        connection_id: String,
        message: WebSocketMessage,
        tx: mpsc::Sender<WebSocketMessage>,
    ) -> Result<(), anyhow::Error> {
        if !self.config.enable_batching {
            // Send immediately if batching is disabled
            tx.send(message).await?;
            return Ok(());
        }
        
        // Check if this is a high-priority message that should skip batching
        if self.is_high_priority(&message) {
            tx.send(message).await?;
            return Ok(());
        }
        
        // For now, send immediately until we implement proper batching
        // TODO: Implement proper batching with message aggregation
        tx.send(message).await?;
        
        Ok(())
    }
    
    /// Check if a message is high priority and should skip batching
    fn is_high_priority(&self, message: &WebSocketMessage) -> bool {
        matches!(
            message,
            WebSocketMessage::Error { .. }
            | WebSocketMessage::Connected { .. }
            | WebSocketMessage::Disconnected { .. }
            | WebSocketMessage::Ping
            | WebSocketMessage::Pong
        )
    }
    
    /// Flush all pending batches for a connection
    pub async fn flush_connection(&self, connection_id: &str) -> Result<(), anyhow::Error> {
        if let Some((_, mut batch)) = self.batches.remove(connection_id) {
            if !batch.messages.is_empty() {
                batch.tx.send(batch.messages).await?;
            }
        }
        Ok(())
    }
}

/// Performance metrics tracker
pub struct PerformanceMetrics {
    /// Message latency samples (connection_id -> samples)
    latency_samples: Arc<DashMap<String, LatencySamples>>,
    /// Global metrics
    global_metrics: Arc<RwLock<GlobalMetrics>>,
}

#[derive(Debug)]
struct LatencySamples {
    samples: Vec<Duration>,
    max_samples: usize,
}

#[derive(Debug, Default)]
#[derive(Clone)]
struct GlobalMetrics {
    total_messages: u64,
    total_bytes: u64,
    avg_latency_ms: f64,
    p50_latency_ms: f64,
    p95_latency_ms: f64,
    p99_latency_ms: f64,
}

impl PerformanceMetrics {
    pub fn new() -> Self {
        Self {
            latency_samples: Arc::new(DashMap::new()),
            global_metrics: Arc::new(RwLock::new(GlobalMetrics::default())),
        }
    }
    
    /// Record message latency
    pub async fn record_latency(&self, connection_id: &str, latency: Duration) {
        let mut entry = self.latency_samples
            .entry(connection_id.to_string())
            .or_insert_with(|| LatencySamples {
                samples: Vec::with_capacity(1000),
                max_samples: 1000,
            });
        
        entry.samples.push(latency);
        
        // Keep only recent samples
        if entry.samples.len() > entry.max_samples {
            entry.samples.remove(0);
        }
        
        // Update global metrics periodically
        if entry.samples.len() % 100 == 0 {
            self.update_global_metrics().await;
        }
        
        // Log if latency exceeds threshold
        if latency > Duration::from_millis(50) {
            warn!(
                "High latency detected for connection {}: {:?}ms",
                connection_id,
                latency.as_millis()
            );
        }
    }
    
    /// Update global performance metrics
    async fn update_global_metrics(&self) {
        let mut all_samples = Vec::new();
        
        for entry in self.latency_samples.iter() {
            all_samples.extend(entry.samples.iter().cloned());
        }
        
        if all_samples.is_empty() {
            return;
        }
        
        all_samples.sort();
        
        let mut metrics = self.global_metrics.write().await;
        
        // Calculate percentiles
        let len = all_samples.len();
        metrics.avg_latency_ms = all_samples.iter()
            .map(|d| d.as_micros() as f64 / 1000.0)
            .sum::<f64>() / len as f64;
        
        metrics.p50_latency_ms = all_samples[len / 2].as_micros() as f64 / 1000.0;
        metrics.p95_latency_ms = all_samples[len * 95 / 100].as_micros() as f64 / 1000.0;
        metrics.p99_latency_ms = all_samples[len * 99 / 100].as_micros() as f64 / 1000.0;
        
        debug!(
            "WebSocket performance - Avg: {:.2}ms, P50: {:.2}ms, P95: {:.2}ms, P99: {:.2}ms",
            metrics.avg_latency_ms,
            metrics.p50_latency_ms,
            metrics.p95_latency_ms,
            metrics.p99_latency_ms
        );
        
        // TODO: Add gauge metrics to the metrics module for WebSocket latency percentiles
        // For now, we'll skip these metrics until proper gauge support is added
    }
    
    /// Get current performance metrics
    pub async fn get_metrics(&self) -> GlobalMetrics {
        self.global_metrics.read().await.clone()
    }
    
    /// Record message sent
    pub async fn record_message(&self, size_bytes: usize) {
        let mut metrics = self.global_metrics.write().await;
        metrics.total_messages += 1;
        metrics.total_bytes += size_bytes as u64;
        
        // Use the custom metrics module to record WebSocket messages
        crate::metrics::record_websocket_message("performance_tracked", "outbound", size_bytes);
    }
}

/// Zero-copy message serialization for performance
pub struct FastSerializer;

impl FastSerializer {
    /// Serialize message with minimal allocations
    pub fn serialize(message: &WebSocketMessage) -> Result<Vec<u8>, anyhow::Error> {
        // For now, use standard serde_json
        // TODO: Implement zero-copy serialization with rkyv or bincode
        let json = serde_json::to_vec(message)?;
        Ok(json)
    }
    
    /// Deserialize message with minimal allocations
    pub fn deserialize(data: &[u8]) -> Result<WebSocketMessage, anyhow::Error> {
        // For now, use standard serde_json
        // TODO: Implement zero-copy deserialization
        let message = serde_json::from_slice(data)?;
        Ok(message)
    }
    
    /// Check if compression would be beneficial
    pub fn should_compress(data: &[u8], threshold: usize) -> bool {
        data.len() > threshold
    }
    
    /// Compress data using fast algorithm
    pub fn compress(data: &[u8]) -> Result<Vec<u8>, anyhow::Error> {
        // Use zstd for fast compression
        let compressed = zstd::encode_all(data, 1)?; // Level 1 for speed
        Ok(compressed)
    }
    
    /// Decompress data
    pub fn decompress(data: &[u8]) -> Result<Vec<u8>, anyhow::Error> {
        let decompressed = zstd::decode_all(data)?;
        Ok(decompressed)
    }
}

/// Tokio runtime optimization for low latency
pub struct RuntimeOptimizer;

impl RuntimeOptimizer {
    /// Get optimized runtime configuration
    pub fn get_runtime_config() -> tokio::runtime::Builder {
        let mut builder = tokio::runtime::Builder::new_multi_thread();
        
        // Configure for low latency
        builder
            .worker_threads(num_cpus::get())
            .max_blocking_threads(32)
            .thread_name("collaboration-worker")
            .thread_stack_size(2 * 1024 * 1024) // 2MB stack
            .enable_all();
        
        builder
    }
    
    /// Configure task priorities (requires tokio_unstable)
    pub fn configure_priorities() {
        // High priority for WebSocket message handling
        // Normal priority for background tasks
        // Low priority for cleanup tasks
        
        // This requires tokio_unstable features
        // For now, we rely on proper task scheduling
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    async fn test_message_batching() {
        let config = PerformanceConfig {
            enable_batching: false, // Currently batching sends immediately
            ..Default::default()
        };
        
        let batcher = MessageBatcher::new(config);
        let (tx, mut rx) = mpsc::channel(10);
        
        // Add message
        let msg = WebSocketMessage::Ping;
        batcher.add_message(
            "test-conn".to_string(),
            msg,
            tx.clone(),
        ).await.unwrap();
        
        // Should receive immediately
        let received = rx.recv().await.unwrap();
        assert!(matches!(received, WebSocketMessage::Ping));
    }
    
    #[tokio::test]
    async fn test_high_priority_bypass() {
        let config = PerformanceConfig::default();
        let batcher = MessageBatcher::new(config);
        let (tx, mut rx) = mpsc::channel(10);
        
        // High priority message should bypass batching
        let error_msg = WebSocketMessage::Error {
            code: "TEST".to_string(),
            message: "Test error".to_string(),
            details: None,
        };
        
        batcher.add_message(
            "test-conn".to_string(),
            error_msg,
            tx.clone(),
        ).await.unwrap();
        
        // Should receive immediately
        let received = rx.recv().await.unwrap();
        assert!(matches!(received, WebSocketMessage::Error { .. }));
    }
}