use std::sync::Arc;
use std::time::{Duration, Instant};
use dashmap::DashMap;
use tokio::time;
use tracing::{debug, warn};

use crate::{
    models::UserId,
    storage::redis_client::RedisClient,
};

/// WebSocket-specific rate limiter for message frequency control
pub struct WebSocketRateLimiter {
    /// Maximum messages per window
    message_limit: u32,
    /// Maximum cursor updates per window  
    cursor_limit: u32,
    /// Time window duration
    window_duration: Duration,
    /// In-memory cache for performance
    local_cache: Arc<DashMap<UserId, UserRateLimit>>,
    /// Redis client for distributed rate limiting
    redis_client: Arc<RedisClient>,
}

#[derive(Debug, Clone)]
struct UserRateLimit {
    message_count: u32,
    cursor_count: u32,
    window_start: Instant,
}

impl WebSocketRateLimiter {
    pub fn new(
        message_limit: u32,
        cursor_limit: u32,
        window_duration: Duration,
        redis_client: Arc<RedisClient>,
    ) -> Self {
        let limiter = Self {
            message_limit,
            cursor_limit,
            window_duration,
            local_cache: Arc::new(DashMap::new()),
            redis_client,
        };
        
        // Start cleanup task
        let cache = limiter.local_cache.clone();
        let duration = window_duration;
        tokio::spawn(async move {
            let mut interval = time::interval(duration);
            loop {
                interval.tick().await;
                // Clean up old entries
                let now = Instant::now();
                cache.retain(|_, v| now.duration_since(v.window_start) < duration);
            }
        });
        
        limiter
    }
    
    /// Check if a message is allowed
    pub async fn check_message(&self, user_id: &UserId) -> Result<bool, anyhow::Error> {
        self.check_rate_limit(user_id, true).await
    }
    
    /// Check if a cursor update is allowed
    pub async fn check_cursor(&self, user_id: &UserId) -> Result<bool, anyhow::Error> {
        self.check_rate_limit(user_id, false).await
    }
    
    async fn check_rate_limit(&self, user_id: &UserId, is_message: bool) -> Result<bool, anyhow::Error> {
        let now = Instant::now();
        
        // Try local cache first for performance
        let mut entry = self.local_cache.entry(user_id.clone()).or_insert_with(|| {
            UserRateLimit {
                message_count: 0,
                cursor_count: 0,
                window_start: now,
            }
        });
        
        // Check if window has expired
        if now.duration_since(entry.window_start) >= self.window_duration {
            // Reset window
            entry.message_count = 0;
            entry.cursor_count = 0;
            entry.window_start = now;
        }
        
        // Check limits
        let allowed = if is_message {
            if entry.message_count < self.message_limit {
                entry.message_count += 1;
                true
            } else {
                false
            }
        } else {
            if entry.cursor_count < self.cursor_limit {
                entry.cursor_count += 1;
                true
            } else {
                false
            }
        };
        
        if !allowed {
            let event_type = if is_message { "message" } else { "cursor" };
            warn!(
                "WebSocket rate limit exceeded for user {} ({})",
                user_id.as_ref(),
                event_type
            );
            metrics::counter!("websocket_rate_limit_exceeded", 
                "user_id" => user_id.as_ref().to_string(),
                "event_type" => event_type
            ).increment(1);
        } else {
            debug!(
                "Rate limit check passed for user {} - messages: {}/{}, cursors: {}/{}",
                user_id.as_ref(),
                entry.message_count,
                self.message_limit,
                entry.cursor_count,
                self.cursor_limit
            );
        }
        
        // Update Redis for distributed rate limiting (async, don't block)
        if allowed {
            let redis_client = self.redis_client.clone();
            let user_id = user_id.clone();
            let event_type = if is_message { "msg" } else { "cursor" };
            tokio::spawn(async move {
                let key = format!("ws_rate:{}:{}:{}", user_id.as_ref(), event_type, chrono::Utc::now().timestamp() / 60);
                if let Err(e) = redis_client.set_cached_analysis(&key, "1", 60).await {
                    debug!("Failed to update distributed rate limit: {}", e);
                }
            });
        }
        
        Ok(allowed)
    }
    
    /// Get current rate limit status for a user
    pub fn get_status(&self, user_id: &UserId) -> RateLimitStatus {
        let now = Instant::now();
        
        if let Some(entry) = self.local_cache.get(user_id) {
            let time_remaining = if now.duration_since(entry.window_start) < self.window_duration {
                self.window_duration - now.duration_since(entry.window_start)
            } else {
                Duration::from_secs(0)
            };
            
            RateLimitStatus {
                messages_remaining: self.message_limit.saturating_sub(entry.message_count),
                cursors_remaining: self.cursor_limit.saturating_sub(entry.cursor_count),
                reset_in: time_remaining,
            }
        } else {
            RateLimitStatus {
                messages_remaining: self.message_limit,
                cursors_remaining: self.cursor_limit,
                reset_in: self.window_duration,
            }
        }
    }
}

#[derive(Debug, Clone)]
pub struct RateLimitStatus {
    pub messages_remaining: u32,
    pub cursors_remaining: u32,
    pub reset_in: Duration,
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::sync::Arc;
    
    #[tokio::test]
    async fn test_message_rate_limiting() {
        let redis_client = Arc::new(RedisClient::new("redis://localhost:6379").await.unwrap());
        let limiter = WebSocketRateLimiter::new(
            5, // 5 messages per window
            20, // 20 cursor updates per window
            Duration::from_secs(60),
            redis_client,
        );
        
        let user_id = UserId::from_string("test-user".to_string());
        
        // Should allow first 5 messages
        for i in 0..5 {
            assert!(limiter.check_message(&user_id).await.unwrap(), "Message {} should be allowed", i + 1);
        }
        
        // 6th message should be blocked
        assert!(!limiter.check_message(&user_id).await.unwrap(), "6th message should be blocked");
        
        // Cursor updates should still work
        assert!(limiter.check_cursor(&user_id).await.unwrap(), "Cursor update should be allowed");
    }
    
    #[tokio::test]
    async fn test_window_reset() {
        let redis_client = Arc::new(RedisClient::new("redis://localhost:6379").await.unwrap());
        let limiter = WebSocketRateLimiter::new(
            1, // 1 message per window
            1, // 1 cursor per window
            Duration::from_millis(100), // Short window for testing
            redis_client,
        );
        
        let user_id = UserId::from_string("test-user".to_string());
        
        // First message allowed
        assert!(limiter.check_message(&user_id).await.unwrap());
        
        // Second message blocked
        assert!(!limiter.check_message(&user_id).await.unwrap());
        
        // Wait for window to reset
        tokio::time::sleep(Duration::from_millis(150)).await;
        
        // Should be allowed again
        assert!(limiter.check_message(&user_id).await.unwrap());
    }
}