use crate::{
    api::AppState,
    metrics,
    models::{
        CursorPosition, Message, MessageContent, PresenceInfo, PresenceStatus, SessionEvent,
        TeamNotification, Timestamp, UserId, WebSocketMessage,
    },
    websocket::connection::Connection,
};
use anyhow::{Context, Result};
use std::sync::Arc;
use std::time::Instant;
use tracing::{debug, error, info, instrument, warn};
use uuid::Uuid;

pub struct MessageHandler {
    state: Arc<AppState>,
}

impl MessageHandler {
    pub fn new(state: Arc<AppState>) -> Self {
        Self { state }
    }

    #[instrument(skip(self, message, connection), fields(connection_id = %connection.id, user_id = %connection.user_info.user_id.as_ref()))]
    pub async fn handle_message(
        &self,
        message: WebSocketMessage,
        connection: &Connection,
    ) -> Result<()> {
        let start = Instant::now();
        let message_type = get_message_type(&message);
        
        // Record incoming message metrics
        let message_size = serde_json::to_vec(&message)?.len();
        metrics::record_websocket_message(&message_type, "inbound", message_size);
        
        // Update last activity
        connection.update_last_activity().await;
        
        // Get rate limiter
        let rate_limiter = self.state.ws_hub.get_rate_limiter();

        match message {
            WebSocketMessage::JoinSession { session_id, .. } => {
                self.handle_join_session(connection, &session_id).await
            }
            WebSocketMessage::LeaveSession { session_id, .. } => {
                self.handle_leave_session(connection, &session_id).await
            }
            WebSocketMessage::MessageSent { message } => {
                // Check rate limit for messages
                if !rate_limiter.check_message(&connection.user_info.user_id).await? {
                    let status = rate_limiter.get_status(&connection.user_info.user_id);
                    let error_msg = WebSocketMessage::Error {
                        code: "RATE_LIMIT_EXCEEDED".to_string(),
                        message: format!(
                            "Message rate limit exceeded. {} messages remaining. Resets in {} seconds.",
                            status.messages_remaining,
                            status.reset_in.as_secs()
                        ),
                        details: Some(serde_json::json!({
                            "messages_remaining": status.messages_remaining,
                            "reset_in_seconds": status.reset_in.as_secs(),
                        })),
                    };
                    connection.send_message(error_msg).await?;
                    return Ok(());
                }
                
                self.handle_message_sent(connection, message).await
            }
            WebSocketMessage::CursorMove {
                session_id,
                position,
                ..
            } => {
                // Check rate limit for cursor updates
                if !rate_limiter.check_cursor(&connection.user_info.user_id).await? {
                    // Silently drop cursor updates when rate limited (too frequent to send errors)
                    debug!(
                        "Cursor update rate limited for user {}",
                        connection.user_info.user_id.as_ref()
                    );
                    return Ok(());
                }
                
                self.handle_cursor_move(connection, &session_id, position).await
            },
            WebSocketMessage::PresenceUpdate { .. } => {
                // Presence is updated automatically
                Ok(())
            }
            WebSocketMessage::AnalysisShared {
                session_id,
                analysis_id,
                analysis_type,
                summary,
                ..
            } => {
                // Check rate limit for analysis sharing (counts as message)
                if !rate_limiter.check_message(&connection.user_info.user_id).await? {
                    let status = rate_limiter.get_status(&connection.user_info.user_id);
                    let error_msg = WebSocketMessage::Error {
                        code: "RATE_LIMIT_EXCEEDED".to_string(),
                        message: format!(
                            "Rate limit exceeded. {} messages remaining. Resets in {} seconds.",
                            status.messages_remaining,
                            status.reset_in.as_secs()
                        ),
                        details: None,
                    };
                    connection.send_message(error_msg).await?;
                    return Ok(());
                }
                
                self.handle_analysis_shared(
                    connection,
                    &session_id,
                    &analysis_id,
                    &analysis_type,
                    &summary,
                )
                .await
            }
            WebSocketMessage::Ping => {
                connection.send_message(WebSocketMessage::Pong).await
            }
            WebSocketMessage::Pong => {
                // Client acknowledged our ping
                Ok(())
            }
            _ => {
                warn!("Unhandled message type: {:?}", message);
                Ok(())
            }
        }?;
        
        // Record processing latency
        let latency = start.elapsed();
        metrics::record_websocket_latency(&message_type, latency);
        
        Ok(())
    }

    async fn handle_join_session(
        &self,
        connection: &Connection,
        session_id: &str,
    ) -> Result<()> {
        info!(
            "User {} joining session {}",
            connection.user_info.email, session_id
        );

        // Verify session exists and user has access
        let session = self
            .state
            .storage
            .spanner
            .get_session(session_id)
            .await
            .context("Failed to get session")?
            .ok_or_else(|| anyhow::anyhow!("Session not found"))?;

        // Check if user is a team member
        let is_member = self
            .state
            .storage
            .spanner
            .is_team_member(&session.team_id, connection.user_info.user_id.as_ref())
            .await
            .context("Failed to check team membership")?;

        if !is_member {
            return Err(anyhow::anyhow!("Access denied: not a team member"));
        }

        // Check participant limit
        if session.participants.len() >= session.settings.max_participants {
            return Err(anyhow::anyhow!("Session is full"));
        }

        // Join the session
        connection.join_session(session_id.to_string()).await?;

        // Update session in database
        self.state
            .storage
            .spanner
            .add_session_participant(session_id, connection.user_info.user_id.as_ref())
            .await
            .context("Failed to add participant to database")?;

        // Broadcast user joined event
        let event = WebSocketMessage::SessionUpdate {
            session_id: session_id.to_string(),
            update: crate::models::SessionUpdate {
                field: "participant_joined".to_string(),
                value: serde_json::json!({
                    "user_id": connection.user_info.user_id.as_ref(),
                    "email": connection.user_info.email,
                    "name": connection.user_info.name,
                }),
                updated_by: connection.user_info.user_id.clone(),
                timestamp: Timestamp::now(),
            },
        };

        self.state
            .ws_hub
            .broadcast_to_session(session_id, event, Some(&connection.id))
            .await?;

        // Send session state to the joining user
        let participants = self
            .state
            .storage
            .spanner
            .get_session_participants(session_id)
            .await
            .context("Failed to get participants")?;

        let recent_messages = self
            .state
            .storage
            .spanner
            .get_session_messages(session_id, self.state.config.max_message_history)
            .await
            .context("Failed to get messages")?;

        // Send current state to the user
        connection
            .send_message(WebSocketMessage::SessionUpdate {
                session_id: session_id.to_string(),
                update: crate::models::SessionUpdate {
                    field: "session_state".to_string(),
                    value: serde_json::json!({
                        "session": session,
                        "participants": participants,
                        "recent_messages": recent_messages,
                    }),
                    updated_by: connection.user_info.user_id.clone(),
                    timestamp: Timestamp::now(),
                },
            })
            .await?;

        Ok(())
    }

    async fn handle_leave_session(
        &self,
        connection: &Connection,
        session_id: &str,
    ) -> Result<()> {
        info!(
            "User {} leaving session {}",
            connection.user_info.email, session_id
        );

        // Leave the session
        connection.leave_session().await?;

        // Update session in database
        self.state
            .storage
            .spanner
            .remove_session_participant(session_id, connection.user_info.user_id.as_ref())
            .await
            .context("Failed to remove participant from database")?;

        // Broadcast user left event
        let event = WebSocketMessage::SessionUpdate {
            session_id: session_id.to_string(),
            update: crate::models::SessionUpdate {
                field: "participant_left".to_string(),
                value: serde_json::json!({
                    "user_id": connection.user_info.user_id.as_ref(),
                    "email": connection.user_info.email,
                }),
                updated_by: connection.user_info.user_id.clone(),
                timestamp: Timestamp::now(),
            },
        };

        self.state
            .ws_hub
            .broadcast_to_session(session_id, event, Some(&connection.id))
            .await?;

        Ok(())
    }

    async fn handle_message_sent(
        &self,
        connection: &Connection,
        mut message: Message,
    ) -> Result<()> {
        // Verify user is in the session
        let current_session = connection
            .get_current_session()
            .await
            .ok_or_else(|| anyhow::anyhow!("Not in a session"))?;

        if message.session_id != current_session {
            return Err(anyhow::anyhow!("Session mismatch"));
        }

        // Set message metadata
        message.id = Uuid::new_v4().to_string();
        message.user_id = connection.user_info.user_id.clone();
        message.created_at = Timestamp::now();

        // Persist message if enabled
        if self.state.config.enable_message_persistence {
            self.state
                .storage
                .spanner
                .save_message(&message)
                .await
                .context("Failed to save message")?;
        }

        // Broadcast message to all session participants
        let broadcast_msg = WebSocketMessage::MessageReceived {
            message: message.clone(),
        };

        self.state
            .ws_hub
            .broadcast_to_session(&current_session, broadcast_msg, None)
            .await?;

        Ok(())
    }

    async fn handle_cursor_move(
        &self,
        connection: &Connection,
        session_id: &str,
        position: CursorPosition,
    ) -> Result<()> {
        // Verify user is in the session
        let current_session = connection
            .get_current_session()
            .await
            .ok_or_else(|| anyhow::anyhow!("Not in a session"))?;

        if current_session != session_id {
            return Err(anyhow::anyhow!("Session mismatch"));
        }

        // Broadcast cursor position to other participants
        let event = WebSocketMessage::CursorMove {
            session_id: session_id.to_string(),
            user_id: connection.user_info.user_id.clone(),
            position,
        };

        self.state
            .ws_hub
            .broadcast_to_session(session_id, event, Some(&connection.id))
            .await?;

        Ok(())
    }

    async fn handle_analysis_shared(
        &self,
        connection: &Connection,
        session_id: &str,
        analysis_id: &str,
        analysis_type: &str,
        summary: &str,
    ) -> Result<()> {
        // Verify user is in the session
        let current_session = connection
            .get_current_session()
            .await
            .ok_or_else(|| anyhow::anyhow!("Not in a session"))?;

        if current_session != session_id {
            return Err(anyhow::anyhow!("Session mismatch"));
        }

        // Create a system message about the shared analysis
        let message = Message {
            id: Uuid::new_v4().to_string(),
            session_id: session_id.to_string(),
            user_id: connection.user_info.user_id.clone(),
            content: MessageContent::AnalysisShare {
                analysis_id: analysis_id.to_string(),
                analysis_type: analysis_type.to_string(),
                summary: summary.to_string(),
                highlights: Vec::new(), // Could be populated with key findings
            },
            created_at: Timestamp::now(),
            updated_at: None,
            edited: false,
            deleted: false,
            metadata: Default::default(),
        };

        // Persist the message
        if self.state.config.enable_message_persistence {
            self.state
                .storage
                .spanner
                .save_message(&message)
                .await
                .context("Failed to save analysis share message")?;
        }

        // Broadcast the analysis share event
        let event = WebSocketMessage::AnalysisShared {
            session_id: session_id.to_string(),
            analysis_id: analysis_id.to_string(),
            shared_by: connection.user_info.user_id.clone(),
            analysis_type: analysis_type.to_string(),
            summary: summary.to_string(),
        };

        self.state
            .ws_hub
            .broadcast_to_session(session_id, event, None)
            .await?;

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    // Tests would require mocking AppState and other dependencies
    // For now, we'll test the message handling logic in integration tests
}

// Helper function to get message type as string
fn get_message_type(message: &WebSocketMessage) -> String {
    match message {
        WebSocketMessage::Connected { .. } => "connected",
        WebSocketMessage::Disconnected { .. } => "disconnected",
        WebSocketMessage::Error { .. } => "error",
        WebSocketMessage::JoinSession { .. } => "join_session",
        WebSocketMessage::LeaveSession { .. } => "leave_session",
        WebSocketMessage::SessionUpdate { .. } => "session_update",
        WebSocketMessage::MessageSent { .. } => "message_sent",
        WebSocketMessage::MessageReceived { .. } => "message_received",
        WebSocketMessage::PresenceUpdate { .. } => "presence_update",
        WebSocketMessage::CursorMove { .. } => "cursor_move",
        WebSocketMessage::AnalysisShared { .. } => "analysis_shared",
        WebSocketMessage::TeamNotification { .. } => "team_notification",
        WebSocketMessage::Ping => "ping",
        WebSocketMessage::Pong => "pong",
    }.to_string()
}