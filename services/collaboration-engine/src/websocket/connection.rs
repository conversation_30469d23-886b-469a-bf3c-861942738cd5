use crate::{
    api::AppState,
    metrics,
    models::{UserInfo, WebSocketMessage},
    websocket::performance::{MessageBatcher, PerformanceMetrics},
};
use anyhow::Result;
use chrono::Utc;
use std::sync::Arc;
use std::time::Instant;
use tokio::sync::{mpsc, RwLock};
use tracing::{debug, error, instrument};

#[derive(Clone)]
pub struct Connection {
    pub id: String,
    pub user_info: UserInfo,
    pub current_session: Arc<RwLock<Option<String>>>,
    pub last_activity: Arc<RwLock<chrono::DateTime<chrono::Utc>>>,
    sender: mpsc::Sender<WebSocketMessage>,
    state: Arc<AppState>,
    batcher: Arc<MessageBatcher>,
    metrics: Arc<PerformanceMetrics>,
}

impl Connection {
    pub fn new(
        id: String,
        user_info: UserInfo,
        sender: mpsc::Sender<WebSocketMessage>,
        state: Arc<AppState>,
        batcher: Arc<MessageBatcher>,
        metrics: Arc<PerformanceMetrics>,
    ) -> Self {
        Self {
            id,
            user_info,
            current_session: Arc::new(RwLock::new(None)),
            last_activity: Arc::new(RwLock::new(Utc::now())),
            sender,
            state,
            batcher,
            metrics,
        }
    }
    
    #[instrument(skip(self, message), fields(connection_id = %self.id, user_id = %self.user_info.user_id.as_ref()))]
    pub async fn send_message(&self, message: WebSocketMessage) -> Result<()> {
        let start = Instant::now();
        let message_type = get_message_type(&message);
        let message_size = serde_json::to_vec(&message)?.len();
        
        // Record outbound message metrics
        metrics::record_websocket_message(&message_type, "outbound", message_size);
        
        // Send through batcher for optimization
        self.batcher
            .add_message(self.id.clone(), message, self.sender.clone())
            .await
            .map_err(|e| anyhow::anyhow!("Failed to send message: {}", e))?;
        
        // Record latency
        let latency = start.elapsed();
        self.metrics.record_latency(&self.id, latency).await;
        metrics::record_websocket_latency(&message_type, latency);
        
        Ok(())
    }
    
    pub async fn send_immediate(&self, message: WebSocketMessage) -> Result<()> {
        // Bypass batching for high-priority messages
        self.sender
            .send(message)
            .await
            .map_err(|e| anyhow::anyhow!("Failed to send immediate message: {}", e))
    }
    
    pub async fn join_session(&self, session_id: String) -> Result<()> {
        // Leave current session if any
        if let Some(current) = self.current_session.read().await.as_ref() {
            self.state
                .ws_hub
                .remove_from_session(current, &self.id)
                .await;
        }
        
        // Join new session
        self.state
            .ws_hub
            .add_to_session(&session_id, &self.id)
            .await;
        
        // Update current session
        let mut current = self.current_session.write().await;
        *current = Some(session_id.clone());
        
        // Update presence in Redis
        if self.state.config.enable_presence_tracking {
            self.update_presence(&session_id).await?;
        }
        
        debug!(
            "Connection {} joined session {}",
            self.id, session_id
        );
        
        Ok(())
    }
    
    pub async fn leave_session(&self) -> Result<()> {
        if let Some(session_id) = self.current_session.read().await.as_ref() {
            // Remove from hub
            self.state
                .ws_hub
                .remove_from_session(session_id, &self.id)
                .await;
            
            // Clear presence in Redis
            if self.state.config.enable_presence_tracking {
                self.clear_presence(session_id).await?;
            }
            
            debug!(
                "Connection {} left session {}",
                self.id, session_id
            );
        }
        
        // Clear current session
        let mut current = self.current_session.write().await;
        *current = None;
        
        Ok(())
    }
    
    pub async fn get_current_session(&self) -> Option<String> {
        self.current_session.read().await.clone()
    }
    
    pub async fn update_last_activity(&self) {
        let mut last_activity = self.last_activity.write().await;
        *last_activity = Utc::now();
    }
    
    pub async fn get_last_activity(&self) -> chrono::DateTime<chrono::Utc> {
        *self.last_activity.read().await
    }
    
    pub async fn is_active(&self) -> bool {
        let last_activity = self.get_last_activity().await;
        let timeout = chrono::Duration::seconds(self.state.config.ws_client_timeout_secs as i64);
        
        Utc::now() - last_activity < timeout
    }
    
    async fn update_presence(&self, session_id: &str) -> Result<()> {
        let key = format!(
            "presence:{}:{}",
            session_id,
            self.user_info.user_id.as_ref()
        );
        
        let presence_data = serde_json::json!({
            "user_id": self.user_info.user_id.as_ref(),
            "email": self.user_info.email,
            "name": self.user_info.name,
            "connection_id": self.id,
            "timestamp": Utc::now().to_rfc3339(),
        });
        
        let ttl = self.state.config.ws_heartbeat_interval_secs * 2; // 2x heartbeat interval
        
        self.state
            .storage
            .redis
            .set_cached_analysis(&key, &presence_data.to_string(), ttl)
            .await
            .map_err(|e| {
                error!("Failed to update presence: {}", e);
                e
            })
    }
    
    async fn clear_presence(&self, session_id: &str) -> Result<()> {
        let key = format!(
            "presence:{}:{}",
            session_id,
            self.user_info.user_id.as_ref()
        );
        
        // We don't have a delete method in RedisClient, so we set with 1 second TTL
        self.state
            .storage
            .redis
            .set_cached_analysis(&key, "", 1)
            .await
            .map_err(|e| {
                error!("Failed to clear presence: {}", e);
                e
            })
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    async fn test_connection_activity_tracking() {
        // This would require a mock AppState and sender
        // For now, we'll just test the logic
        
        let now = Utc::now();
        let last_activity = Arc::new(RwLock::new(now));
        
        // Update activity
        {
            let mut activity = last_activity.write().await;
            *activity = Utc::now();
        }
        
        // Check activity
        let activity = *last_activity.read().await;
        assert!(activity >= now);
    }
}

// Helper function to get message type as string
fn get_message_type(message: &WebSocketMessage) -> String {
    match message {
        WebSocketMessage::Connected { .. } => "connected",
        WebSocketMessage::Disconnected { .. } => "disconnected",
        WebSocketMessage::Error { .. } => "error",
        WebSocketMessage::JoinSession { .. } => "join_session",
        WebSocketMessage::LeaveSession { .. } => "leave_session",
        WebSocketMessage::SessionUpdate { .. } => "session_update",
        WebSocketMessage::MessageSent { .. } => "message_sent",
        WebSocketMessage::MessageReceived { .. } => "message_received",
        WebSocketMessage::PresenceUpdate { .. } => "presence_update",
        WebSocketMessage::CursorMove { .. } => "cursor_move",
        WebSocketMessage::AnalysisShared { .. } => "analysis_shared",
        WebSocketMessage::TeamNotification { .. } => "team_notification",
        WebSocketMessage::Ping => "ping",
        WebSocketMessage::Pong => "pong",
    }.to_string()
}