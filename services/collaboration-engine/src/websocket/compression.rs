use std::io::{Read, Write};
use flate2::Compression;
use flate2::read::GzDecoder;
use flate2::write::GzEncoder;
use serde::{Deserialize, Serialize};
use tracing::{debug, warn};

/// Message compression for WebSocket optimization
pub struct MessageCompressor {
    compression_threshold: usize,
    compression_level: Compression,
}

impl MessageCompressor {
    pub fn new(compression_threshold: usize) -> Self {
        Self {
            compression_threshold,
            compression_level: Compression::fast(), // Fast compression for low latency
        }
    }
    
    /// Compress data if it exceeds threshold
    pub fn compress(&self, data: &[u8]) -> Result<Vec<u8>, anyhow::Error> {
        if data.len() < self.compression_threshold {
            return Ok(data.to_vec());
        }
        
        let mut encoder = GzEncoder::new(Vec::new(), self.compression_level);
        encoder.write_all(data)?;
        let compressed = encoder.finish()?;
        
        // Only use compression if it actually reduces size
        if compressed.len() < data.len() {
            debug!(
                "Compressed message from {} to {} bytes ({}% reduction)",
                data.len(),
                compressed.len(),
                ((data.len() - compressed.len()) * 100) / data.len()
            );
            Ok(compressed)
        } else {
            Ok(data.to_vec())
        }
    }
    
    /// Decompress data
    pub fn decompress(&self, data: &[u8]) -> Result<Vec<u8>, anyhow::Error> {
        // Check if data is gzip compressed (magic bytes: 1f 8b)
        if data.len() >= 2 && data[0] == 0x1f && data[1] == 0x8b {
            let mut decoder = GzDecoder::new(data);
            let mut decompressed = Vec::new();
            decoder.read_to_end(&mut decompressed)?;
            Ok(decompressed)
        } else {
            // Not compressed
            Ok(data.to_vec())
        }
    }
}

/// Wrapper for compressed WebSocket messages
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "encoding")]
pub enum EncodedMessage {
    #[serde(rename = "raw")]
    Raw {
        data: String,
    },
    #[serde(rename = "gzip")]
    Compressed {
        data: String, // Base64 encoded compressed data
        original_size: usize,
    },
}

impl EncodedMessage {
    /// Create from raw JSON string
    pub fn from_json(json: String, compressor: &MessageCompressor) -> Result<Self, anyhow::Error> {
        let data = json.as_bytes();
        let compressed = compressor.compress(data)?;
        
        if compressed.len() < data.len() {
            // Use compression
            Ok(EncodedMessage::Compressed {
                data: base64::encode(&compressed),
                original_size: data.len(),
            })
        } else {
            // Don't compress
            Ok(EncodedMessage::Raw { data: json })
        }
    }
    
    /// Extract JSON string
    pub fn to_json(&self, compressor: &MessageCompressor) -> Result<String, anyhow::Error> {
        match self {
            EncodedMessage::Raw { data } => Ok(data.clone()),
            EncodedMessage::Compressed { data, .. } => {
                let compressed = base64::decode(data)?;
                let decompressed = compressor.decompress(&compressed)?;
                String::from_utf8(decompressed).map_err(Into::into)
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::models::WebSocketMessage;
    
    #[test]
    fn test_compression() {
        let compressor = MessageCompressor::new(100);
        
        // Small data shouldn't be compressed
        let small_data = b"Hello, World!";
        let result = compressor.compress(small_data).unwrap();
        assert_eq!(result, small_data);
        
        // Large repetitive data should compress well
        let large_data = "a".repeat(1000).into_bytes();
        let compressed = compressor.compress(&large_data).unwrap();
        assert!(compressed.len() < large_data.len());
        
        // Decompress should restore original
        let decompressed = compressor.decompress(&compressed).unwrap();
        assert_eq!(decompressed, large_data);
    }
    
    #[test]
    fn test_encoded_message() {
        let compressor = MessageCompressor::new(50);
        
        // Small message
        let small_json = r#"{"type":"ping"}"#.to_string();
        let encoded = EncodedMessage::from_json(small_json.clone(), &compressor).unwrap();
        assert!(matches!(encoded, EncodedMessage::Raw { .. }));
        
        // Large message
        let large_json = format!(r#"{{"type":"message","content":"{}"}}"#, "x".repeat(100));
        let encoded = EncodedMessage::from_json(large_json.clone(), &compressor).unwrap();
        
        // Decode should match original
        let decoded = encoded.to_json(&compressor).unwrap();
        assert_eq!(decoded, large_json);
    }
}