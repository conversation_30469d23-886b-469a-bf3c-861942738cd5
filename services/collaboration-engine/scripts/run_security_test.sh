#!/bin/bash

# Security test runner for collaboration engine
set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}🔒 Collaboration Engine Security Test Suite${NC}"
echo "==========================================="

# Function to run a test and report results
run_test() {
    local test_name=$1
    local test_function=$2
    
    echo -e "\n${YELLOW}Running: $test_name${NC}"
    
    if cargo test --test security_test $test_function -- --nocapture 2>&1 | grep -q "test result: ok"; then
        echo -e "${GREEN}✅ PASSED${NC}"
        return 0
    else
        echo -e "${RED}❌ FAILED${NC}"
        return 1
    fi
}

# Track test results
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Run security tests
echo -e "${YELLOW}Starting security test suite...${NC}"

# Authentication tests
run_test "Authentication Required" "test_auth_required_endpoints" && ((PASSED_TESTS++)) || ((FAILED_TESTS++))
((TOTAL_TESTS++))

run_test "Invalid JWT Rejection" "test_invalid_jwt_rejected" && ((PASSED_TESTS++)) || ((FAILED_TESTS++))
((TOTAL_TESTS++))

run_test "Expired JWT Rejection" "test_expired_jwt_rejected" && ((PASSED_TESTS++)) || ((FAILED_TESTS++))
((TOTAL_TESTS++))

# Rate limiting
run_test "Rate Limiting" "test_rate_limiting" && ((PASSED_TESTS++)) || ((FAILED_TESTS++))
((TOTAL_TESTS++))

# Injection prevention
run_test "SQL Injection Prevention" "test_sql_injection_prevention" && ((PASSED_TESTS++)) || ((FAILED_TESTS++))
((TOTAL_TESTS++))

run_test "XSS Prevention" "test_xss_prevention" && ((PASSED_TESTS++)) || ((FAILED_TESTS++))
((TOTAL_TESTS++))

# Security headers
run_test "CORS Headers" "test_cors_headers" && ((PASSED_TESTS++)) || ((FAILED_TESTS++))
((TOTAL_TESTS++))

run_test "Security Headers" "test_security_headers" && ((PASSED_TESTS++)) || ((FAILED_TESTS++))
((TOTAL_TESTS++))

# Path traversal
run_test "Path Traversal Prevention" "test_path_traversal_prevention" && ((PASSED_TESTS++)) || ((FAILED_TESTS++))
((TOTAL_TESTS++))

# Summary
echo ""
echo -e "${GREEN}========== Security Test Summary ==========${NC}"
echo -e "Total Tests:  $TOTAL_TESTS"
echo -e "Passed:       ${GREEN}$PASSED_TESTS${NC}"
echo -e "Failed:       ${RED}$FAILED_TESTS${NC}"

if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "\n${GREEN}✅ All security tests passed!${NC}"
    exit 0
else
    echo -e "\n${RED}❌ Some security tests failed!${NC}"
    exit 1
fi