#!/bin/bash

# Wave 2: Authentication & Security Testing Script
# Tests JWT auth, security headers, and rate limiting

set -e

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BASE_URL="${COLLABORATION_ENGINE_URL:-https://collaboration-engine-production-xxxxx.run.app}"
TEST_USER="${TEST_USER:-<EMAIL>}"
TEST_PASSWORD="${TEST_PASSWORD:-testpassword123}"

echo "=========================================="
echo "Wave 2: Authentication & Security Testing"
echo "Target: ${BASE_URL}"
echo "=========================================="

# Track test results
PASSED=0
FAILED=0

# Function to test endpoint with detailed response
test_auth_endpoint() {
    local method=$1
    local endpoint=$2
    local data=$3
    local headers=$4
    local expected_status=$5
    local description=$6
    
    echo -e "\n${YELLOW}Testing:${NC} ${description}"
    echo "Method: ${method} ${endpoint}"
    
    # Build curl command
    cmd="curl -s -w '\n%{http_code}' -X ${method} '${BASE_URL}${endpoint}'"
    
    if [ -n "$data" ]; then
        cmd="${cmd} -H 'Content-Type: application/json' -d '${data}'"
    fi
    
    if [ -n "$headers" ]; then
        cmd="${cmd} ${headers}"
    fi
    
    # Execute request
    response=$(eval "${cmd} 2>/dev/null" || echo "000")
    http_code=$(echo "$response" | tail -n1)
    body=$(echo "$response" | sed '$d')
    
    if [ "$http_code" = "$expected_status" ]; then
        echo -e "${GREEN}✅ PASS${NC} - HTTP ${http_code}"
        if [ -n "$body" ]; then
            echo "Response: ${body}"
        fi
        echo "$body"  # Return body for further processing
        return 0
    else
        echo -e "${RED}❌ FAIL${NC} - Expected ${expected_status}, got ${http_code}"
        if [ -n "$body" ]; then
            echo "Response: ${body}"
        fi
        return 1
    fi
}

# Function to check security headers
check_security_headers() {
    local endpoint=$1
    
    echo -e "\n${YELLOW}Checking security headers on ${endpoint}${NC}"
    
    headers=$(curl -s -I "${BASE_URL}${endpoint}" 2>/dev/null || echo "")
    
    # Check each required header
    check_header() {
        local header_name=$1
        local expected_value=$2
        
        if echo "$headers" | grep -i "^${header_name}:" > /dev/null; then
            value=$(echo "$headers" | grep -i "^${header_name}:" | cut -d' ' -f2- | tr -d '\r')
            if [ -n "$expected_value" ]; then
                if echo "$value" | grep -q "$expected_value"; then
                    echo -e "${GREEN}✅${NC} ${header_name}: ${value}"
                    return 0
                else
                    echo -e "${RED}❌${NC} ${header_name}: ${value} (expected: ${expected_value})"
                    return 1
                fi
            else
                echo -e "${GREEN}✅${NC} ${header_name}: ${value}"
                return 0
            fi
        else
            echo -e "${RED}❌${NC} ${header_name}: Missing"
            return 1
        fi
    }
    
    local headers_passed=0
    local headers_failed=0
    
    # Check required security headers
    if check_header "X-Frame-Options" "DENY"; then ((headers_passed++)); else ((headers_failed++)); fi
    if check_header "X-Content-Type-Options" "nosniff"; then ((headers_passed++)); else ((headers_failed++)); fi
    if check_header "Referrer-Policy" "strict-origin-when-cross-origin"; then ((headers_passed++)); else ((headers_failed++)); fi
    if check_header "Content-Security-Policy" ""; then ((headers_passed++)); else ((headers_failed++)); fi
    
    # Check CORS headers
    echo -e "\n${YELLOW}Checking CORS configuration:${NC}"
    cors_response=$(curl -s -I -H "Origin: https://episteme.app" "${BASE_URL}${endpoint}" 2>/dev/null || echo "")
    
    if echo "$cors_response" | grep -i "^Access-Control-Allow-Origin:" > /dev/null; then
        cors_value=$(echo "$cors_response" | grep -i "^Access-Control-Allow-Origin:" | cut -d' ' -f2- | tr -d '\r')
        if echo "$cors_value" | grep -E "(https://episteme.app|\*)" > /dev/null; then
            echo -e "${GREEN}✅${NC} CORS configured for episteme.app: ${cors_value}"
            ((headers_passed++))
        else
            echo -e "${RED}❌${NC} CORS misconfigured: ${cors_value}"
            ((headers_failed++))
        fi
    else
        echo -e "${YELLOW}⚠️${NC} No CORS headers (may be intentional)"
    fi
    
    return $headers_failed
}

echo -e "\n${BLUE}=== 2.1 JWT Authentication Tests ===${NC}"

# Test login endpoint
echo -e "\n${YELLOW}Testing login flow:${NC}"
login_response=$(test_auth_endpoint "POST" "/api/v1/auth/login" \
    '{"email":"'${TEST_USER}'","password":"'${TEST_PASSWORD}'"}' \
    "" "200" "Login with credentials")

if [ $? -eq 0 ]; then
    ((PASSED++))
    # Extract tokens if login successful
    if echo "$login_response" | grep -q "access_token"; then
        ACCESS_TOKEN=$(echo "$login_response" | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4)
        REFRESH_TOKEN=$(echo "$login_response" | grep -o '"refresh_token":"[^"]*"' | cut -d'"' -f4)
        echo -e "${GREEN}✅${NC} Tokens received"
        
        # Test protected endpoint with token
        if [ -n "$ACCESS_TOKEN" ]; then
            if test_auth_endpoint "GET" "/api/v1/sessions" "" \
                "-H 'Authorization: Bearer ${ACCESS_TOKEN}'" "200" \
                "Access protected endpoint with valid JWT"; then
                ((PASSED++))
            else
                ((FAILED++))
            fi
        fi
        
        # Test refresh token
        if [ -n "$REFRESH_TOKEN" ]; then
            if test_auth_endpoint "POST" "/api/v1/auth/refresh" \
                '{"refresh_token":"'${REFRESH_TOKEN}'"}' \
                "" "200" "Refresh access token"; then
                ((PASSED++))
            else
                ((FAILED++))
            fi
        fi
        
        # Test logout
        if [ -n "$ACCESS_TOKEN" ]; then
            if test_auth_endpoint "POST" "/api/v1/auth/logout" "" \
                "-H 'Authorization: Bearer ${ACCESS_TOKEN}'" "200" \
                "Logout with valid token"; then
                ((PASSED++))
            else
                ((FAILED++))
            fi
        fi
    else
        echo -e "${YELLOW}⚠️${NC} No tokens in response - auth may not be fully configured"
    fi
else
    ((FAILED++))
    echo -e "${YELLOW}Note:${NC} Login test failed - this may be expected if test credentials are not configured"
fi

# Test protected endpoint without auth
if test_auth_endpoint "GET" "/api/v1/sessions" "" "" "401" \
    "Access protected endpoint without auth"; then
    ((PASSED++))
else
    ((FAILED++))
fi

# Test teams endpoint
if test_auth_endpoint "GET" "/api/v1/teams" "" "" "401" \
    "Access teams endpoint without auth"; then
    ((PASSED++))
else
    ((FAILED++))
fi

echo -e "\n${BLUE}=== 2.2 Security Headers Validation ===${NC}"

# Check security headers on different endpoints
headers_failed=0
if ! check_security_headers "/health"; then
    ((headers_failed++))
fi

if [ $headers_failed -eq 0 ]; then
    ((PASSED++))
else
    ((FAILED++))
fi

echo -e "\n${BLUE}=== 2.3 Rate Limiting Tests ===${NC}"

# Test HTTP rate limiting
echo -e "\n${YELLOW}Testing HTTP rate limiting (60 requests/minute):${NC}"
echo "Sending 70 rapid requests to /health endpoint..."

rate_limit_hit=false
for i in {1..70}; do
    response_code=$(curl -s -o /dev/null -w "%{http_code}" "${BASE_URL}/health" 2>/dev/null)
    if [ "$response_code" = "429" ]; then
        echo -e "${GREEN}✅${NC} Rate limit enforced after request #${i}"
        rate_limit_hit=true
        ((PASSED++))
        break
    fi
    if [ $((i % 10)) -eq 0 ]; then
        echo "Sent ${i} requests..."
    fi
done

if [ "$rate_limit_hit" = false ]; then
    echo -e "${RED}❌${NC} Rate limit not enforced after 70 requests"
    ((FAILED++))
fi

# Test WebSocket rate limiting
echo -e "\n${YELLOW}Testing WebSocket rate limiting:${NC}"
echo -e "${BLUE}Note:${NC} WebSocket rate limiting requires a WebSocket client tool"
echo "Would test: 60 messages/minute rate limit"
echo "Manual test command: wscat -c wss://[domain]/ws -H 'Authorization: Bearer [token]'"

# Test rate limit headers
echo -e "\n${YELLOW}Checking rate limit headers:${NC}"
rate_headers=$(curl -s -I "${BASE_URL}/health" 2>/dev/null)
if echo "$rate_headers" | grep -i "X-RateLimit-Limit\|RateLimit" > /dev/null; then
    echo -e "${GREEN}✅${NC} Rate limit headers present:"
    echo "$rate_headers" | grep -i "ratelimit" | sed 's/^/  /'
    ((PASSED++))
else
    echo -e "${YELLOW}⚠️${NC} No rate limit headers found (may use different implementation)"
fi

# Summary
echo -e "\n=========================================="
echo -e "${YELLOW}Wave 2 Test Summary${NC}"
echo -e "Passed: ${GREEN}${PASSED}${NC}"
echo -e "Failed: ${RED}${FAILED}${NC}"
echo -e "Total: $((PASSED + FAILED))"

if [ $FAILED -eq 0 ]; then
    echo -e "\n${GREEN}✅ Wave 2: All security tests PASSED!${NC}"
    exit 0
else
    echo -e "\n${YELLOW}⚠️ Wave 2: Some tests failed or skipped${NC}"
    echo -e "${BLUE}Note:${NC} Some failures may be expected without proper test credentials"
    exit 0  # Don't fail the script for expected auth failures
fi