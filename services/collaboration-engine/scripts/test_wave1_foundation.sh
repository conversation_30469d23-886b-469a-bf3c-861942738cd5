#!/bin/bash

# Wave 1: Foundation Testing Script
# Tests service health and environment validation

set -e

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
# Note: Using placeholder URL - needs to be updated with actual production URL
BASE_URL="${COLLABORATION_ENGINE_URL:-https://collaboration-engine-production-xxxxx.run.app}"
METRICS_PORT="9003"

echo "=========================================="
echo "Wave 1: Foundation Testing"
echo "Target: ${BASE_URL}"
echo "=========================================="

# Function to test endpoint
test_endpoint() {
    local endpoint=$1
    local expected_status=$2
    local description=$3
    
    echo -e "\n${YELLOW}Testing:${NC} ${description}"
    echo "Endpoint: ${endpoint}"
    
    response=$(curl -s -w "\n%{http_code}" "${BASE_URL}${endpoint}" 2>/dev/null || echo "000")
    http_code=$(echo "$response" | tail -n1)
    body=$(echo "$response" | sed '$d')
    
    if [ "$http_code" = "$expected_status" ]; then
        echo -e "${GREEN}✅ PASS${NC} - HTTP ${http_code}"
        if [ -n "$body" ]; then
            echo "Response: ${body}"
        fi
        return 0
    else
        echo -e "${RED}❌ FAIL${NC} - Expected ${expected_status}, got ${http_code}"
        if [ -n "$body" ]; then
            echo "Response: ${body}"
        fi
        return 1
    fi
}

# Track test results
PASSED=0
FAILED=0

echo -e "\n${YELLOW}=== 1.1 Service Health Validation ===${NC}"

# Test basic health endpoint
if test_endpoint "/health" "200" "Basic health check"; then
    ((PASSED++))
else
    ((FAILED++))
fi

# Test readiness endpoint
if test_endpoint "/health/ready" "200" "Redis & Spanner connectivity"; then
    ((PASSED++))
    # Parse response to check connections
    if echo "$body" | grep -q "redis.*ok" && echo "$body" | grep -q "spanner.*ok"; then
        echo -e "${GREEN}✅${NC} Redis and Spanner connections confirmed"
    else
        echo -e "${YELLOW}⚠️${NC} Could not confirm all connections from response"
    fi
else
    ((FAILED++))
fi

# Test liveness endpoint
if test_endpoint "/health/live" "200" "Liveness probe"; then
    ((PASSED++))
else
    ((FAILED++))
fi

# Test metrics endpoint (may be on different port)
echo -e "\n${YELLOW}Testing:${NC} Prometheus metrics endpoint"
echo "Endpoint: /metrics (port ${METRICS_PORT})"

# Try metrics on main port first
metrics_response=$(curl -s -w "\n%{http_code}" "${BASE_URL}/metrics" 2>/dev/null || echo "000")
metrics_code=$(echo "$metrics_response" | tail -n1)

if [ "$metrics_code" = "200" ]; then
    echo -e "${GREEN}✅ PASS${NC} - Metrics available on main port"
    ((PASSED++))
    # Check for key metrics
    metrics_body=$(echo "$metrics_response" | sed '$d')
    if echo "$metrics_body" | grep -q "websocket_connections"; then
        echo -e "${GREEN}✅${NC} WebSocket metrics exposed"
    fi
    if echo "$metrics_body" | grep -q "collaboration_sessions"; then
        echo -e "${GREEN}✅${NC} Session metrics exposed"
    fi
else
    echo -e "${YELLOW}⚠️${NC} Metrics not available on main port (may be on separate port)"
    ((FAILED++))
fi

echo -e "\n${YELLOW}=== 1.2 Environment Setup Validation ===${NC}"

# Test for required endpoints existence
echo -e "\n${YELLOW}Checking API endpoints availability:${NC}"

# Check auth endpoints
if test_endpoint "/api/v1/auth/login" "405" "Auth login endpoint (expect 405 for GET)"; then
    echo -e "${GREEN}✅${NC} Auth endpoints configured"
    ((PASSED++))
else
    ((FAILED++))
fi

# Check protected endpoints
if test_endpoint "/api/v1/sessions" "401" "Sessions endpoint (expect 401 without auth)"; then
    echo -e "${GREEN}✅${NC} Protected endpoints properly secured"
    ((PASSED++))
else
    ((FAILED++))
fi

# Check WebSocket endpoint
echo -e "\n${YELLOW}Testing:${NC} WebSocket endpoint availability"
ws_response=$(curl -s -w "\n%{http_code}" \
    -H "Connection: Upgrade" \
    -H "Upgrade: websocket" \
    -H "Sec-WebSocket-Version: 13" \
    -H "Sec-WebSocket-Key: x3JJHMbDL1EzLkh9GBhXDw==" \
    "${BASE_URL}/ws" 2>/dev/null || echo "000")
ws_code=$(echo "$ws_response" | tail -n1)

if [ "$ws_code" = "426" ] || [ "$ws_code" = "401" ]; then
    echo -e "${GREEN}✅ PASS${NC} - WebSocket endpoint responding (${ws_code})"
    ((PASSED++))
else
    echo -e "${RED}❌ FAIL${NC} - WebSocket endpoint not responding correctly (${ws_code})"
    ((FAILED++))
fi

# Summary
echo -e "\n=========================================="
echo -e "${YELLOW}Wave 1 Test Summary${NC}"
echo -e "Passed: ${GREEN}${PASSED}${NC}"
echo -e "Failed: ${RED}${FAILED}${NC}"
echo -e "Total: $((PASSED + FAILED))"

if [ $FAILED -eq 0 ]; then
    echo -e "\n${GREEN}✅ Wave 1: All foundation tests PASSED!${NC}"
    exit 0
else
    echo -e "\n${RED}❌ Wave 1: Some tests FAILED${NC}"
    exit 1
fi