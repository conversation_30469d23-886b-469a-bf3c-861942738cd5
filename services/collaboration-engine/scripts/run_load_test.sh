#!/bin/bash

# Load test runner for collaboration engine
set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}🚀 Collaboration Engine Load Test Runner${NC}"
echo "========================================"

# Check if service is running
if ! curl -s http://localhost:8001/health > /dev/null; then
    echo -e "${RED}❌ Error: Collaboration engine is not running on port 8001${NC}"
    echo "Please start the service first with: cargo run"
    exit 1
fi

# Configuration
BASE_URL=${BASE_URL:-"ws://localhost:8001"}
CONNECTIONS=${CONNECTIONS:-50}
MESSAGES_PER_SEC=${MESSAGES_PER_SEC:-10}
DURATION=${DURATION:-30}

echo -e "${YELLOW}Configuration:${NC}"
echo "  Base URL: $BASE_URL"
echo "  Concurrent connections: $CONNECTIONS"
echo "  Messages per second: $MESSAGES_PER_SEC"
echo "  Test duration: ${DURATION}s"
echo ""

# Get JWT token for testing (in production, this would be a real auth flow)
echo -e "${YELLOW}Getting test JWT token...${NC}"
JWT_TOKEN=$(curl -s -X POST http://localhost:8001/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"test123"}' \
  | jq -r '.access_token' || echo "test-token")

if [ "$JWT_TOKEN" == "null" ] || [ -z "$JWT_TOKEN" ]; then
    echo -e "${YELLOW}Using mock JWT token for testing${NC}"
    JWT_TOKEN="test-token"
fi

# Export configuration for the test
export LOAD_TEST_BASE_URL=$BASE_URL
export LOAD_TEST_CONNECTIONS=$CONNECTIONS
export LOAD_TEST_MESSAGES_PER_SEC=$MESSAGES_PER_SEC
export LOAD_TEST_DURATION=$DURATION
export LOAD_TEST_JWT_TOKEN=$JWT_TOKEN

# Run different load test scenarios
echo ""
echo -e "${GREEN}Running load tests...${NC}"

# Test 1: Standard load
echo -e "\n${YELLOW}Test 1: Standard Load (50 connections, 10 msg/s)${NC}"
cargo test --test load_test test_load_50_connections -- --ignored --nocapture

# Test 2: High load
echo -e "\n${YELLOW}Test 2: High Load (100 connections, 5 msg/s)${NC}"
cargo test --test load_test test_load_100_connections -- --ignored --nocapture

# Test 3: Burst load
echo -e "\n${YELLOW}Test 3: Burst Load (200 connections, 20 msg/s for 10s)${NC}"
cargo test --test load_test test_burst_load -- --ignored --nocapture

echo ""
echo -e "${GREEN}✅ Load tests completed!${NC}"
echo ""
echo "Check the metrics endpoint for detailed performance data:"
echo "  http://localhost:9001/metrics"