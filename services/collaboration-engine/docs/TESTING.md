# Collaboration Engine Testing Guide

## Overview

The collaboration engine includes comprehensive testing suites to ensure reliability, performance, and security in production environments.

## Test Suites

### 1. Unit Tests

Run all unit tests:
```bash
cargo test
```

Run tests with output:
```bash
cargo test -- --nocapture
```

### 2. Integration Tests

Integration tests are included in the unit test suite and test cross-component functionality.

### 3. Load Tests

Load tests validate performance under various connection and message loads.

#### Prerequisites
- Service must be running on port 8001
- Redis must be available
- Sufficient system resources for concurrent connections

#### Running Load Tests

Quick load test:
```bash
./scripts/run_load_test.sh
```

Custom configuration:
```bash
CONNECTIONS=100 MESSAGES_PER_SEC=20 DURATION=60 ./scripts/run_load_test.sh
```

Individual load test scenarios:
```bash
# Standard load (50 connections)
cargo test --test load_test test_load_50_connections -- --ignored --nocapture

# High load (100 connections)
cargo test --test load_test test_load_100_connections -- --ignored --nocapture

# Burst load (200 connections)
cargo test --test load_test test_burst_load -- --ignored --nocapture
```

#### Load Test Metrics

The load tests measure:
- Connection success/failure rates
- Message throughput (sent/received)
- Latency percentiles (P50, P95, P99)
- Error rates
- <50ms latency target validation

### 4. Security Tests

Security tests validate authentication, authorization, and protection against common vulnerabilities.

#### Running Security Tests

Full security test suite:
```bash
./scripts/run_security_test.sh
```

Individual security tests:
```bash
# Authentication tests
cargo test --test security_test test_auth_required_endpoints -- --nocapture

# Rate limiting
cargo test --test security_test test_rate_limiting -- --nocapture

# Injection prevention
cargo test --test security_test test_sql_injection_prevention -- --nocapture
cargo test --test security_test test_xss_prevention -- --nocapture

# Security headers
cargo test --test security_test test_security_headers -- --nocapture
```

#### Security Test Coverage

- **Authentication**: JWT validation, expired tokens, invalid tokens
- **Authorization**: Protected endpoint access control
- **Rate Limiting**: Request throttling validation
- **Injection Prevention**: SQL injection, XSS protection
- **Security Headers**: CORS, CSP, X-Frame-Options, etc.
- **Path Traversal**: Directory traversal prevention

### 5. Performance Benchmarks

Run performance benchmarks:
```bash
cargo bench
```

Benchmarks measure:
- WebSocket message serialization
- Compression performance
- Round-trip latency

## Monitoring During Tests

### Prometheus Metrics

View real-time metrics during tests:
```bash
curl http://localhost:9001/metrics
```

Key metrics to monitor:
- `websocket_connections_total`: Active connections
- `websocket_latency_seconds`: Message processing latency
- `rate_limit_exceeded_total`: Rate limit violations
- `collaboration_errors_total`: Error counts by component

### Application Logs

Monitor logs during tests:
```bash
RUST_LOG=collaboration_engine=debug cargo run
```

### Health Checks

Check service health:
```bash
# Basic health
curl http://localhost:8001/health

# Detailed readiness
curl http://localhost:8001/health/ready

# Liveness
curl http://localhost:8001/health/live
```

## Performance Targets

The collaboration engine targets:
- **Latency**: <50ms P95 for WebSocket messages
- **Connections**: 50+ concurrent users per session
- **Throughput**: 10+ messages/second per connection
- **Availability**: 99.9% uptime

## Continuous Integration

For CI/CD pipelines:

```yaml
# Example GitHub Actions workflow
test:
  - cargo test
  - cargo clippy -- -D warnings
  - cargo fmt -- --check
  
security:
  - ./scripts/run_security_test.sh
  
performance:
  - cargo bench
  - ./scripts/run_load_test.sh
```

## Troubleshooting

### Common Issues

1. **Load test connection failures**
   - Check service is running: `curl http://localhost:8001/health`
   - Verify Redis is available: `redis-cli ping`
   - Check system limits: `ulimit -n` (should be >65536)

2. **Security test failures**
   - Ensure test environment matches config
   - Check JWT secret configuration
   - Verify rate limit settings

3. **Performance issues**
   - Monitor CPU/memory during tests
   - Check Redis latency
   - Review Tokio runtime configuration

### Debug Mode

Enable detailed logging:
```bash
RUST_LOG=collaboration_engine=trace,tower_http=debug cargo run
```

## Best Practices

1. **Before Production**
   - Run full test suite
   - Validate <50ms latency target
   - Check security test pass rate
   - Review error rates

2. **Load Testing**
   - Start with low connection counts
   - Gradually increase load
   - Monitor system resources
   - Check for memory leaks

3. **Security Testing**
   - Test with production-like config
   - Validate all auth flows
   - Check rate limiting effectiveness
   - Review security headers

4. **Performance Testing**
   - Test with realistic message sizes
   - Simulate production traffic patterns
   - Monitor latency percentiles
   - Check compression effectiveness