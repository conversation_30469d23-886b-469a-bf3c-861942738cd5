# Collaboration Engine Deployment Guide

## Overview

This guide covers the deployment of the Collaboration Engine to Google Cloud Run, including prerequisites, deployment steps, monitoring setup, and troubleshooting.

## Prerequisites

### Required Tools
- Google Cloud SDK (`gcloud`)
- Docker
- kubectl
- curl
- jq

### GCP Resources
- Google Cloud Project: `vibe-match-463114`
- Artifact Registry repository configured
- Cloud Run API enabled
- Secret Manager API enabled
- Redis instance deployed (internal VPC)
- Spanner instance configured

### Service Account Permissions
The service account `<EMAIL>` requires:
- `roles/spanner.databaseUser`
- `roles/redis.editor`
- `roles/secretmanager.secretAccessor`
- `roles/cloudtrace.agent`
- `roles/monitoring.metricWriter`
- `roles/logging.logWriter`

## Deployment Steps

### 1. Set Up Secrets

First, configure all production secrets using Google Secret Manager:

```bash
# Run the secrets management script
./scripts/deploy/collaboration-engine-secrets.sh
```

This script will:
- Create/update JWT secret
- Configure Redis connection URL
- Set up Spanner configuration
- Configure OpenTelemetry endpoint
- Grant appropriate permissions

### 2. Build and Deploy

Deploy the service to Cloud Run:

```bash
# Set the image tag (optional, defaults to 'latest')
export IMAGE_TAG="v1.0.0"

# Run the deployment script
./scripts/deploy/deploy-collaboration-engine.sh
```

The deployment script will:
1. Validate prerequisites
2. Build Docker image with optimizations
3. Push to Artifact Registry
4. Deploy to Cloud Run
5. Configure traffic routing
6. Validate deployment health

### 3. Configure Load Balancer

For production WebSocket support with custom domain:

```bash
# Apply load balancer configuration
kubectl apply -f infrastructure/load-balancer/collaboration-engine-lb.yaml
```

This creates:
- Global static IP address
- HTTPS load balancer with WebSocket support
- SSL certificate for `*.episteme.app`
- Backend service with session affinity
- Health checks
- URL mapping for `/ws` paths

### 4. Set Up Monitoring

#### Deploy Prometheus Configuration
```bash
kubectl apply -f infrastructure/monitoring/cloud-run/prometheus.yaml
```

#### Import Dashboard
1. Go to [Cloud Monitoring Dashboards](https://console.cloud.google.com/monitoring/dashboards)
2. Click "Create Dashboard"
3. Import `infrastructure/monitoring/dashboards/collaboration-dashboard.json`

#### Configure Alerts
```bash
kubectl apply -f infrastructure/monitoring/alerts/collaboration-alerts.yaml
```

### 5. Validate Deployment

Run the production validation script:

```bash
./scripts/deploy/validate-collaboration-production.sh
```

This validates:
- Service deployment configuration
- Health endpoints
- Security settings
- Performance targets
- Monitoring integration
- Production readiness score (target: 95/100)

## Configuration

### Environment Variables

See `.env.production` for all available environment variables. Key configurations:

```bash
# Core settings
PORT=8003
METRICS_PORT=9003
ENVIRONMENT=production

# WebSocket performance
WS_MAX_CONNECTIONS_PER_USER=10
ENABLE_MESSAGE_COMPRESSION=true
MESSAGE_BATCH_TIMEOUT_MS=10

# Rate limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=60
RATE_LIMIT_WEBSOCKET_MESSAGES_PER_MINUTE=60

# Session limits
MAX_SESSION_PARTICIPANTS=100
MAX_CONCURRENT_SESSIONS=1000
```

### Auto-scaling Configuration

```yaml
Min instances: 1
Max instances: 50
Target concurrency: 100 connections per instance
Scale-up period: 60 seconds
```

### Resource Allocation

```yaml
CPU: 2 cores (1 core request, 2 core limit)
Memory: 4Gi (2Gi request, 4Gi limit)
Timeout: 3600 seconds (for WebSocket connections)
```

## Monitoring

### Key Metrics

1. **WebSocket Performance**
   - `websocket_latency_seconds` - Message processing latency (target: <50ms P95)
   - `websocket_connections_total` - Active connections
   - `websocket_messages_total` - Message throughput

2. **System Health**
   - `collaboration_sessions_active` - Active collaboration sessions
   - `rate_limit_exceeded_total` - Rate limit violations
   - `collaboration_errors_total` - Error rate by component

3. **Resource Usage**
   - CPU utilization (target: <80%)
   - Memory usage (target: <80%)
   - Instance count

### Dashboards

- **Main Dashboard**: `https://console.cloud.google.com/monitoring/dashboards/collaboration-engine`
- **Cloud Run Console**: `https://console.cloud.google.com/run/detail/us-central1/collaboration-engine-production`
- **Logs Explorer**: `https://console.cloud.google.com/logs/query`

### Alerts

Critical alerts are configured for:
- High latency (>50ms P95)
- High error rate (>0.1/sec)
- Service downtime
- Resource exhaustion

## Rollback Procedures

### Quick Rollback

```bash
# List recent revisions
gcloud run revisions list \
  --service=collaboration-engine-production \
  --region=us-central1

# Route traffic to previous revision
gcloud run services update-traffic collaboration-engine-production \
  --region=us-central1 \
  --to-revisions=PREVIOUS_REVISION_NAME=100
```

### Full Rollback

1. Identify the last known good revision
2. Update traffic routing
3. Monitor health metrics
4. Investigate issues in the failed revision

## Troubleshooting

### Common Issues

#### 1. WebSocket Connection Failures
```bash
# Check WebSocket endpoint
curl -i -N \
  -H "Connection: Upgrade" \
  -H "Upgrade: websocket" \
  -H "Sec-WebSocket-Version: 13" \
  -H "Sec-WebSocket-Key: x3JJHMbDL1EzLkh9GBhXDw==" \
  https://collab.episteme.app/ws
```

#### 2. High Latency
- Check instance CPU/memory usage
- Verify Redis connectivity
- Review message batch settings
- Check for rate limiting

#### 3. Authentication Issues
- Verify JWT secret in Secret Manager
- Check token expiry settings
- Review CORS configuration

#### 4. Deployment Failures
```bash
# View deployment logs
gcloud run services logs read collaboration-engine-production \
  --region=us-central1 \
  --limit=50

# Check service status
gcloud run services describe collaboration-engine-production \
  --region=us-central1
```

### Debug Commands

```bash
# Get service details
gcloud run services describe collaboration-engine-production \
  --region=us-central1 \
  --format=json

# View recent errors
gcloud logging read \
  'resource.type="cloud_run_revision"
   resource.labels.service_name="collaboration-engine-production"
   severity>=ERROR' \
  --limit 20 \
  --format json

# Check health endpoint
curl https://collaboration-engine-production-xxxxx.run.app/health

# Test readiness
curl https://collaboration-engine-production-xxxxx.run.app/health/ready
```

## Security Considerations

1. **Secrets Management**
   - All secrets stored in Google Secret Manager
   - Automatic rotation supported
   - Service account has minimal required permissions

2. **Network Security**
   - VPC connector for internal Redis access
   - Binary authorization enabled
   - HTTPS/WSS only

3. **Application Security**
   - JWT authentication required
   - Rate limiting enforced
   - CORS properly configured
   - Security headers implemented

## Maintenance

### Regular Tasks

1. **Weekly**
   - Review error logs
   - Check performance metrics
   - Validate backup procedures

2. **Monthly**
   - Rotate JWT secrets
   - Review and update rate limits
   - Performance optimization review
   - Security audit

3. **Quarterly**
   - Load test production
   - Disaster recovery drill
   - Dependency updates

## Contact

For issues or questions:
- **Platform Team**: <EMAIL>
- **On-Call**: Use PagerDuty for critical issues
- **Runbooks**: https://wiki.episteme.app/runbooks/collaboration-engine