# Multi-stage Dockerfile for Collaboration Engine

# Stage 1: Build environment
FROM rust:1.88-slim AS builder

# Install dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    pkg-config \
    libssl-dev \
    libpq-dev \
    ca-certificates \
    git \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create app directory
WORKDIR /app

# Copy Cargo files first for better caching
COPY Cargo.toml Cargo.lock ./

# Create dummy main.rs to build dependencies
RUN mkdir src && \
    echo "fn main() {}" > src/main.rs && \
    echo "pub fn dummy() {}" > src/lib.rs

# Build dependencies only
RUN cargo build --release && \
    rm -rf src

# Copy source code
COPY src ./src

# Build the application with optimizations
RUN touch src/main.rs src/lib.rs && \
    RUSTFLAGS="-C target-cpu=native -C opt-level=3" cargo build --release

# Stage 2: Runtime environment
FROM debian:bookworm-slim

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    ca-certificates \
    libssl3 \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user with explicit group
RUN groupadd -g 1001 appuser && \
    useradd -m -u 1001 -g appuser -s /bin/bash appuser

# Copy binary from builder
COPY --from=builder /app/target/release/collaboration-engine /usr/local/bin/collaboration-engine

# Set ownership and permissions
RUN chown appuser:appuser /usr/local/bin/collaboration-engine && \
    chmod 755 /usr/local/bin/collaboration-engine

# Create necessary directories
RUN mkdir -p /var/log/collaboration-engine /tmp/collaboration-engine && \
    chown -R appuser:appuser /var/log/collaboration-engine /tmp/collaboration-engine

# Switch to non-root user
USER appuser

# Set working directory
WORKDIR /home/<USER>

# Expose ports
EXPOSE 8003 9003

# Health check optimized for Cloud Run
HEALTHCHECK --interval=30s --timeout=5s --start-period=10s --retries=3 \
    CMD curl -f http://localhost:8003/health/live || exit 1

# Set default environment variables for production
ENV RUST_LOG=collaboration_engine=info,tower_http=info
ENV PORT=8003
ENV METRICS_PORT=9003
ENV RUST_BACKTRACE=1
ENV TOKIO_WORKER_THREADS=4

# Use exec form for proper signal handling
ENTRYPOINT ["/usr/local/bin/collaboration-engine"]