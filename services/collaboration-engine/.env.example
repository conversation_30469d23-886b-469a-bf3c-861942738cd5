# Collaboration Engine Environment Variables

# Server Configuration
PORT=8003
METRICS_PORT=9003
ENVIRONMENT=development

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-at-least-32-characters-long
JWT_ISSUER=episteme
JWT_AUDIENCE=episteme-platform
JWT_EXPIRY_HOURS=24

# Redis Configuration
REDIS_URL=redis://127.0.0.1:6379
REDIS_POOL_SIZE=10
REDIS_CONNECTION_TIMEOUT_MS=5000

# Spanner Configuration
GCP_PROJECT_ID=your-gcp-project-id
SPANNER_INSTANCE_ID=your-spanner-instance
SPANNER_DATABASE_ID=your-spanner-database
SPANNER_POOL_MIN=1
SPANNER_POOL_MAX=10

# WebSocket Configuration
WS_HEARTBEAT_INTERVAL_SECS=30
WS_CLIENT_TIMEOUT_SECS=60
WS_MAX_CONNECTIONS_PER_USER=5
WS_MAX_MESSAGE_SIZE=65536

# Rate Limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=100
RATE_LIMIT_WEBSOCKET_MESSAGES_PER_MINUTE=60

# Session Configuration
MAX_SESSION_PARTICIPANTS=50
SESSION_IDLE_TIMEOUT_MINUTES=30
MAX_MESSAGE_HISTORY=100

# Feature Flags
ENABLE_MESSAGE_PERSISTENCE=true
ENABLE_PRESENCE_TRACKING=true
ENABLE_ANALYTICS=true

# Integration Configuration
ANALYSIS_ENGINE_URL=http://localhost:8001
QUERY_INTELLIGENCE_URL=http://localhost:8002
PUBSUB_SUBSCRIPTION=collaboration-events
PUBSUB_MAX_CONCURRENT_MESSAGES=100
SERVICE_TIMEOUT_SECS=30
INTEGRATION_CACHE_TTL_SECS=300