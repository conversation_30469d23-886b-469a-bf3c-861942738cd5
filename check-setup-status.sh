#!/bin/bash
# Check Google Cloud Build setup status
# This script helps verify what's been set up correctly

set -e

PROJECT_ID="${PROJECT_ID:-vibe-match-463114}"
REGION="${REGION:-us-central1}"

echo "🔍 Checking Google Cloud Build Setup Status"
echo "Project ID: $PROJECT_ID"
echo "Region: $REGION"
echo ""

# Check if APIs are enabled
echo "📡 Checking enabled APIs..."
REQUIRED_APIS=(
  "cloudbuild.googleapis.com"
  "run.googleapis.com" 
  "artifactregistry.googleapis.com"
  "containeranalysis.googleapis.com"
  "secretmanager.googleapis.com"
  "storage.googleapis.com"
)

for api in "${REQUIRED_APIS[@]}"; do
  if gcloud services list --enabled --filter="name:$api" --format="value(name)" | grep -q "$api"; then
    echo "  ✅ $api - enabled"
  else
    echo "  ❌ $api - not enabled"
  fi
done

echo ""

# Check Artifact Registry
echo "📦 Checking Artifact Registry..."
if gcloud artifacts repositories describe ccl-services --location=$REGION --project=$PROJECT_ID >/dev/null 2>&1; then
  echo "  ✅ Artifact Registry 'ccl-services' exists"
else
  echo "  ❌ Artifact Registry 'ccl-services' not found"
fi

echo ""

# Check Cloud Storage buckets
echo "🗄️ Checking Cloud Storage buckets..."
if gsutil ls -p $PROJECT_ID | grep -q "gs://${PROJECT_ID}-build-artifacts/"; then
  echo "  ✅ Build artifacts bucket exists"
else
  echo "  ❌ Build artifacts bucket not found"
fi

if gsutil ls -p $PROJECT_ID | grep -q "gs://${PROJECT_ID}-build-cache/"; then
  echo "  ✅ Build cache bucket exists"
else
  echo "  ❌ Build cache bucket not found"
fi

echo ""

# Check Cloud Build triggers
echo "🔨 Checking Cloud Build triggers..."
TRIGGERS=$(gcloud builds triggers list --project=$PROJECT_ID --format="value(name)" 2>/dev/null)
if echo "$TRIGGERS" | grep -q "episteme-main-build"; then
  echo "  ✅ Main build trigger exists"
else
  echo "  ⏳ Main build trigger not created yet (requires GitHub connection)"
fi

if echo "$TRIGGERS" | grep -q "analysis-engine-build"; then
  echo "  ✅ Analysis Engine trigger exists"
else
  echo "  ⏳ Analysis Engine trigger not created yet (requires GitHub connection)"
fi

echo ""

# Check secrets
echo "🔑 Checking Secret Manager secrets..."
SECRETS=$(gcloud secrets list --project=$PROJECT_ID --format="value(name)" 2>/dev/null)
if echo "$SECRETS" | grep -q "database-url"; then
  echo "  ✅ Database URL secret exists"
else
  echo "  ⏳ Database URL secret not created yet"
fi

if echo "$SECRETS" | grep -q "redis-url"; then
  echo "  ✅ Redis URL secret exists"
else
  echo "  ⏳ Redis URL secret not created yet"
fi

echo ""

# Check Cloud Run services
echo "🌍 Checking Cloud Run services..."
SERVICES=$(gcloud run services list --platform=managed --region=$REGION --project=$PROJECT_ID --format="value(metadata.name)" 2>/dev/null)
if echo "$SERVICES" | grep -q "analysis-engine-dev"; then
  echo "  ✅ Analysis Engine dev service exists"
else
  echo "  ⏳ Analysis Engine dev service not created yet"
fi

echo ""

# Check GitHub repository connection
echo "🔗 Checking GitHub repository connection..."
if gcloud source repos list --project=$PROJECT_ID 2>/dev/null | grep -q "github_star-boy-95_episteme"; then
  echo "  ✅ GitHub repository connected"
else
  echo "  ⏳ GitHub repository not connected yet - MANUAL STEP REQUIRED"
  echo "     Go to: https://console.cloud.google.com/cloud-build/repos?project=$PROJECT_ID"
fi

echo ""

# Overall status
echo "📋 Setup Status Summary:"
echo ""
if gcloud builds triggers list --project=$PROJECT_ID --format="value(name)" 2>/dev/null | grep -q "episteme-main-build"; then
  echo "🎉 SETUP COMPLETE! Your CI/CD pipeline is ready."
  echo ""
  echo "🧪 Test your pipeline:"
  echo "  git add . && git commit -m 'test: trigger cloud build' && git push"
  echo ""
  echo "📊 Monitor builds:"
  echo "  https://console.cloud.google.com/cloud-build/builds?project=$PROJECT_ID"
else
  echo "⏳ SETUP IN PROGRESS"
  echo ""
  echo "📝 Next steps:"
  echo "  1. Connect GitHub repository (manual step)"
  echo "  2. Run: ./continue-setup.sh"  
  echo "  3. Test the pipeline with a git push"
  echo ""
  echo "🔗 Connect GitHub here:"
  echo "  https://console.cloud.google.com/cloud-build/repos?project=$PROJECT_ID"
fi

echo ""