# Episteme Project Structure

## Root Directory
```
episteme/
├── services/              # Microservices
│   ├── analysis-engine/   # Rust - Core analysis service
│   ├── query-intelligence/# Python - Natural language search
│   ├── pattern-mining/    # Python - Pattern detection
│   ├── marketplace/       # Go - Pattern marketplace
│   ├── collaboration/     # TypeScript - Real-time collab
│   └── web/              # TypeScript - Frontend
├── PRPs/                 # Product Requirements Prompts
├── research/             # Third-party documentation
├── scripts/              # Automation scripts
│   ├── security/         # Security scripts
│   └── disaster-recovery/# DR procedures
├── docker/               # Docker configurations
├── infrastructure/       # Cloud infrastructure
├── contracts/            # API contracts
├── docs/                 # Documentation
├── audits/              # Security audits
├── build/               # Build configurations
├── api/                 # API specifications
└── tests/               # Integration tests
```

## Key Files
- `TASK.md` - Current sprint tasks and progress
- `CLAUDE.md` - AI development instructions
- `Makefile` - Common development commands
- `.env.example` - Environment configuration template
- `.pre-commit-config.yaml` - Git hooks configuration

## Service Architecture
Each service follows consistent structure:
```
service/
├── src/          # Source code
├── tests/        # Unit/integration tests
├── docs/         # Service documentation
├── scripts/      # Service scripts
├── Dockerfile    # Container definition
├── Makefile      # Service commands
└── README.md     # Service overview
```

## Configuration Files
- Environment: `.env.example`, `.env.development`
- Docker: `docker-compose.yml`, `docker-compose.dev.yml`
- CI/CD: `cloudbuild.yaml`, `.github/workflows/`
- Testing: `pytest.ini`, `jest.config.js`, `.cargo/config.toml`