# Darwin (macOS) System Commands

## File System Navigation
```bash
ls -la              # List all files with details
open .              # Open current directory in Finder
open file.txt       # Open file with default application
pbcopy < file.txt   # Copy file contents to clipboard
pbpaste > file.txt  # Paste clipboard to file
```

## Process Management
```bash
ps aux | grep process_name  # Find processes
kill -9 PID                 # Force kill process
top                        # Process monitor
htop                       # Better process monitor (if installed)
lsof -i :8080              # Find process using port
```

## Network Commands
```bash
netstat -an | grep LISTEN   # List listening ports
lsof -nP -i4TCP:8080       # Find process on port
curl http://localhost:8080  # Test HTTP endpoint
nc -zv localhost 8080       # Test port connectivity
```

## Development Tools
```bash
brew install <package>      # Install packages via Homebrew
brew update && brew upgrade # Update Homebrew packages
which <command>            # Find command location
echo $PATH                 # Show PATH variable
```

## Git Commands
```bash
git status              # Check repository status
git add -A              # Stage all changes
git commit -m "message" # Commit with message
git push origin branch  # Push to remote
git pull --rebase      # Pull with rebase
git stash              # Temporarily store changes
```

## Docker on Darwin
```bash
docker ps              # List running containers
docker logs -f container # Follow container logs
docker exec -it container sh # Shell into container
docker system prune -a # Clean up Docker
```

## File Search
```bash
find . -name "*.rs"    # Find files by name
grep -r "pattern" .    # Search in files recursively
ag "pattern"           # Fast search with Silver Searcher
rg "pattern"           # Faster search with ripgrep
```