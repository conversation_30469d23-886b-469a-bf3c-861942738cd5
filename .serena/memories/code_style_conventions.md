# Code Style and Conventions

## General Principles
- **Evidence-based development**: All decisions backed by research documentation
- **Production-ready code**: No placeholders, comprehensive error handling
- **Modular architecture**: Files <500 lines, clear separation of concerns
- **Documentation**: Every public function needs docstrings/comments

## Rust Conventions (analysis-engine)
- **Edition**: Rust 2021
- **Error Handling**: Use `Result<T, E>`, avoid `unwrap()` in production
- **Unsafe Code**: All `unsafe` blocks must have `// SAFETY:` comments
- **Formatting**: `cargo fmt` enforced
- **Linting**: `cargo clippy -- -D warnings` (zero warnings policy)
- **Testing**: 100% test pass rate required
- **Documentation**: Rust doc comments with examples

## Python Conventions (query-intelligence, pattern-mining)
- **Version**: Python 3.11+
- **Type Hints**: Required for all functions
- **Formatting**: Black formatter
- **Linting**: <PERSON>uff and mypy
- **Docstrings**: Google style
- **Testing**: pytest with >80% coverage
- **Async**: FastAPI async/await patterns

## Go Conventions (marketplace)
- **Version**: Go 1.21+
- **Error <PERSON>ling**: Always check errors
- **Formatting**: `go fmt`
- **Linting**: golangci-lint
- **Testing**: Table-driven tests
- **Comments**: Godoc style

## TypeScript/JavaScript Conventions
- **TypeScript**: Strict mode enabled
- **React**: Functional components with hooks
- **Formatting**: Prettier
- **Linting**: ESLint with Next.js config
- **Testing**: Jest and React Testing Library
- **State**: Redux Toolkit patterns

## Git Conventions
- **Commit Messages**: Conventional commits (feat:, fix:, docs:, etc.)
- **Branching**: feature/*, bugfix/*, hotfix/*
- **PR Requirements**: Tests passing, approved review, no conflicts