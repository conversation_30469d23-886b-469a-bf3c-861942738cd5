# Task Completion Checklist

## Before Marking Any Task Complete

### 1. Code Quality Checks
- [ ] Run tests: `make test` or service-specific test command
- [ ] Run linters: `make lint` (must pass with zero warnings)
- [ ] Format code: `make format`
- [ ] Security scan: `make security` or `cargo audit`

### 2. Documentation Updates
- [ ] Update `TASK.md` immediately after completing task
- [ ] Add discovered sub-tasks to "Discovered During Work" section
- [ ] Update relevant documentation (README, API docs, etc.)
- [ ] Add inline comments for complex logic with `// Reason:`

### 3. Validation Requirements
- [ ] All validation commands from TASK.md must pass
- [ ] Confidence score must be documented (60-100%)
- [ ] Evidence collected in appropriate directory
- [ ] Performance metrics validated if applicable

### 4. Research Compliance
- [ ] Minimum 5 research file references per task
- [ ] Evidence-based decision documentation
- [ ] Validation loops completed
- [ ] No placeholders or TODO comments in production code

### 5. Git Workflow
- [ ] Pre-commit hooks passing
- [ ] Conventional commit message (feat:, fix:, docs:, etc.)
- [ ] No merge conflicts
- [ ] PR description includes validation evidence

## Service-Specific Requirements

### Rust Services
- `cargo clippy -- -D warnings` must pass
- All unsafe blocks documented with SAFETY comments
- Performance benchmarks validate claims

### Python Services  
- pytest coverage >80%
- Type hints for all functions
- FastAPI async patterns followed

### TypeScript Services
- No TypeScript errors
- React best practices followed
- Tests for new components