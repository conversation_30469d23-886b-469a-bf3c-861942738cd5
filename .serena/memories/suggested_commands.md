# Suggested Development Commands

## Essential Make Commands
```bash
# Development workflow
make setup          # Initial setup
make start          # Start all services
make stop           # Stop services
make restart        # Restart services
make logs           # View logs (use SVC=name for specific service)

# Code quality
make test           # Run all tests
make lint           # Run all linters
make format         # Format all code
make security       # Security scans
make check          # Run lint, test, security, health

# Service-specific
make test-rust      # Test Rust services
make test-python    # Test Python services
make lint-go        # Lint Go code
make format-typescript # Format TypeScript
```

## Service-Specific Commands

### analysis-engine (Rust)
```bash
cd services/analysis-engine
cargo build --release      # Production build
cargo test --all          # Run all tests
cargo clippy -- -D warnings # Lint with zero warnings
cargo bench               # Performance benchmarks
cargo audit               # Security audit
```

### Python Services
```bash
cd services/query-intelligence
pytest                    # Run tests
ruff check .             # Lint
black .                  # Format
mypy .                   # Type checking
```

### Deployment Commands
```bash
./scripts/ccl-deploy      # Deploy to Cloud Run
./scripts/ccl-validate    # Validate deployment
./scripts/ccl-dev         # Development utilities
```

## Docker Commands
```bash
docker-compose -f docker/docker-compose.yml up -d    # Start services
docker-compose -f docker/docker-compose.yml logs -f  # View logs
docker-compose -f docker/docker-compose.yml down     # Stop services
```

## Git Hooks
```bash
make install-hooks       # Install pre-commit hooks
pre-commit run --all-files # Run hooks manually
```