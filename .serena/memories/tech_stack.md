# Episteme Tech Stack

## Languages & Frameworks

### Backend Services
- **Rust** (analysis-engine): Axum 0.8.4, <PERSON><PERSON><PERSON>, Tree-sitter 0.24
- **Python 3.11+** (query-intelligence, pattern-mining): FastAPI, Pydantic, NumPy, Pandas
- **Go** (marketplace): Gin framework, database/sql
- **TypeScript** (collaboration): Socket.io, Express

### Frontend
- **TypeScript/JavaScript**: Next.js 14, React 18, Tai<PERSON><PERSON> CSS
- **State Management**: Redux Toolkit, React Query
- **UI Components**: Radix UI, shadcn/ui

### Databases & Storage
- **Google Cloud Spanner**: Primary database
- **PostgreSQL**: Local development database
- **Redis**: Caching and rate limiting
- **Google Cloud Storage**: File storage

### Infrastructure
- **Container**: Docker, Docker Compose
- **Orchestration**: Kubernetes, Cloud Run
- **CI/CD**: GitHub Actions, Google Cloud Build
- **Monitoring**: Prometheus, Grafana, OpenTelemetry, Jaeger

### AI/ML Integration
- **Google Gemini**: Natural language processing
- **Vertex AI**: ML model deployment
- **Tree-sitter**: Multi-language AST parsing

### Security & Auth
- **JWT**: Authentication tokens
- **OAuth 2.0**: Third-party authentication
- **Binary Authorization**: Container security
- **Rate Limiting**: Governor (Rust), Redis-based

### Development Tools
- **Pre-commit hooks**: Multi-language linting and formatting
- **Testing**: Cargo test, pytest, go test, Jest
- **Documentation**: Markdown, OpenAPI/Swagger