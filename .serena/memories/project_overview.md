# Episteme Project Overview

## Project Purpose
Episteme (formerly CCL - Contextual Code Library) is a comprehensive code analysis and intelligence platform that provides:
- Advanced multi-language code analysis with AST parsing
- Pattern detection and mining capabilities
- Query intelligence for natural language code search
- Marketplace for sharing code patterns
- Collaborative development features
- Web interface for all functionality

## Current Status
- **Phase 2 Production Hardening** completed for analysis-engine (2025-07-17)
- Production deployment ready with security enhancements
- 21 languages supported (18 tree-sitter + 3 adapters)
- Performance validated: 67,900 LOC/second (20x minimum requirement)
- Security: 5/6 critical gaps resolved (EPIS-001 through EPIS-005)

## Architecture
Microservices-based platform with:
- **analysis-engine** (Rust): Core parsing and analysis
- **query-intelligence** (Python): Natural language search with Gemini AI
- **pattern-mining** (Python): Pattern detection and ML
- **marketplace** (Go): Pattern sharing and monetization
- **collaboration** (TypeScript): Real-time collaboration
- **web** (TypeScript/Next.js): Frontend application

## Infrastructure
- Google Cloud Platform (Cloud Run, Spanner, Redis)
- Docker-based development environment
- Kubernetes deployment architecture
- Comprehensive monitoring (Prometheus, Grafana, Jaeger)

## Research-Driven Development
- Context Engineering methodology with evidence-based development
- Research documentation in `/research/` directory
- Multi-agent coordination for comprehensive documentation gathering
- Validation loops and systematic testing approach