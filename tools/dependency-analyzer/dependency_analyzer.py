#!/usr/bin/env python3
"""
Episteme Service Dependency Analyzer

This tool analyzes service dependencies and performs impact analysis
for the Episteme platform microservices architecture.

Usage:
    python dependency_analyzer.py --analyze-all
    python dependency_analyzer.py --impact-analysis <service-name>
    python dependency_analyzer.py --validate-dependencies
    python dependency_analyzer.py --generate-graph
"""

import json
import os
import sys
import argparse
import yaml
import networkx as nx
import matplotlib.pyplot as plt
from pathlib import Path
from typing import Dict, List, Set, Tuple, Optional
from dataclasses import dataclass, asdict
from enum import Enum

class DependencyType(Enum):
    HTTP_API = "http_api"
    WEBSOCKET = "websocket"
    DATABASE = "database"
    CACHE = "cache"
    MESSAGE_QUEUE = "message_queue"
    EXTERNAL_SERVICE = "external_service"
    MONITORING = "monitoring"

class CriticalityLevel(Enum):
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"

@dataclass
class ServiceDependency:
    """Represents a dependency between services or external systems"""
    source: str
    target: str
    dependency_type: DependencyType
    protocol: str
    port: Optional[int]
    criticality: CriticalityLevel
    description: str
    health_check_endpoint: Optional[str] = None
    timeout_ms: Optional[int] = None
    retry_policy: Optional[str] = None

@dataclass
class Service:
    """Represents a microservice in the platform"""
    name: str
    language: str
    framework: str
    port: int
    repository_path: str
    dependencies: List[ServiceDependency]
    exposed_apis: List[str]
    health_check: str
    metrics_endpoint: str
    
@dataclass
class ImpactAnalysis:
    """Results of dependency impact analysis"""
    target_service: str
    affected_services: List[str]
    critical_paths: List[List[str]]
    alternative_paths: List[List[str]]
    estimated_downtime: str
    mitigation_strategies: List[str]
    rollback_plan: List[str]

class DependencyAnalyzer:
    """Main analyzer class for service dependencies"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.services_dir = self.project_root / "services"
        self.graph = nx.DiGraph()
        self.services: Dict[str, Service] = {}
        self.dependencies: List[ServiceDependency] = []
        
    def discover_services(self) -> Dict[str, Service]:
        """Discover all services by examining the services directory"""
        services = {}
        
        if not self.services_dir.exists():
            print(f"Services directory not found: {self.services_dir}")
            return services
            
        for service_dir in self.services_dir.iterdir():
            if service_dir.is_dir():
                service = self._analyze_service(service_dir)
                if service:
                    services[service.name] = service
                    
        return services
    
    def _analyze_service(self, service_dir: Path) -> Optional[Service]:
        """Analyze a single service directory to extract dependency information"""
        service_name = service_dir.name
        
        # Check for explicit dependency configuration first
        deps_config_path = service_dir / ".episteme-deps.yml"
        if deps_config_path.exists():
            return self._analyze_service_from_config(service_dir, deps_config_path)
        
        # Determine language and framework
        language, framework, port = self._detect_service_type(service_dir)
        
        if not language:
            print(f"Could not determine service type for {service_name}")
            return None
            
        dependencies = self._extract_dependencies(service_dir, language)
        
        return Service(
            name=service_name,
            language=language,
            framework=framework,
            port=port,
            repository_path=str(service_dir),
            dependencies=dependencies,
            exposed_apis=self._extract_api_endpoints(service_dir, language),
            health_check=f"/health",
            metrics_endpoint=f"/metrics"
        )
    
    def _analyze_service_from_config(self, service_dir: Path, config_path: Path) -> Optional[Service]:
        """Analyze service using explicit .episteme-deps.yml configuration"""
        try:
            config = self._read_yaml(config_path)
            if not config:
                return None
                
            service_config = config.get('service', {})
            dependencies_config = config.get('dependencies', {})
            
            service_name = service_config.get('name', service_dir.name)
            language = service_config.get('language', 'unknown')
            framework = service_config.get('framework', 'unknown')
            port = service_config.get('port', 8000)
            
            # Parse dependencies from config
            dependencies = []
            for dep_category, deps_list in dependencies_config.items():
                if isinstance(deps_list, list):
                    for dep in deps_list:
                        if isinstance(dep, dict):
                            # Map criticality strings to enum
                            criticality_map = {
                                'critical': CriticalityLevel.CRITICAL,
                                'high': CriticalityLevel.HIGH,
                                'medium': CriticalityLevel.MEDIUM,
                                'low': CriticalityLevel.LOW
                            }
                            
                            # Map dependency types
                            type_map = {
                                'database': DependencyType.DATABASE,
                                'cache': DependencyType.CACHE,
                                'http_api': DependencyType.HTTP_API,
                                'external_service': DependencyType.EXTERNAL_SERVICE,
                                'monitoring': DependencyType.MONITORING,
                                'websocket': DependencyType.WEBSOCKET,
                                'message_queue': DependencyType.MESSAGE_QUEUE
                            }
                            
                            dependencies.append(ServiceDependency(
                                source=service_name,
                                target=dep.get('name', ''),
                                dependency_type=type_map.get(dep.get('type', 'external_service'), DependencyType.EXTERNAL_SERVICE),
                                protocol=dep.get('protocol', 'http'),
                                port=dep.get('port'),
                                criticality=criticality_map.get(dep.get('criticality', 'medium'), CriticalityLevel.MEDIUM),
                                description=dep.get('description', ''),
                                health_check_endpoint=dep.get('health_check'),
                                timeout_ms=dep.get('timeout_ms'),
                                retry_policy=dep.get('retry_policy')
                            ))
            
            # Extract exposed APIs
            exposed_apis = config.get('exposed_apis', [])
            health_check = config.get('health_check', {}).get('endpoint', '/health')
            metrics_endpoint = config.get('metrics', {}).get('endpoint', '/metrics')
            
            return Service(
                name=service_name,
                language=language,
                framework=framework,
                port=port,
                repository_path=str(service_dir),
                dependencies=dependencies,
                exposed_apis=exposed_apis,
                health_check=health_check,
                metrics_endpoint=metrics_endpoint
            )
            
        except Exception as e:
            print(f"Error parsing configuration for {service_dir.name}: {e}")
            return None
    
    def _detect_service_type(self, service_dir: Path) -> Tuple[str, str, int]:
        """Detect the programming language and framework of a service"""
        
        # Rust service
        if (service_dir / "Cargo.toml").exists():
            cargo_toml = self._read_toml(service_dir / "Cargo.toml")
            if cargo_toml and "dependencies" in cargo_toml:
                deps = cargo_toml["dependencies"]
                if "axum" in deps:
                    return "rust", "axum", 8001
                elif "warp" in deps:
                    return "rust", "warp", 8001
                elif "actix-web" in deps:
                    return "rust", "actix-web", 8001
            return "rust", "unknown", 8001
            
        # Node.js service
        elif (service_dir / "package.json").exists():
            package_json = self._read_json(service_dir / "package.json")
            if package_json and "dependencies" in package_json:
                deps = package_json["dependencies"]
                if "express" in deps:
                    return "typescript", "express", 8002
                elif "next" in deps:
                    return "typescript", "next", 3000
                elif "socket.io" in deps:
                    return "typescript", "express+socketio", 8002
            return "typescript", "unknown", 8002
            
        # Python service
        elif (service_dir / "pyproject.toml").exists() or (service_dir / "requirements.txt").exists():
            if (service_dir / "pyproject.toml").exists():
                pyproject = self._read_toml(service_dir / "pyproject.toml")
                if pyproject and "project" in pyproject and "dependencies" in pyproject["project"]:
                    deps = pyproject["project"]["dependencies"]
                    if any("fastapi" in dep for dep in deps):
                        return "python", "fastapi", 8003
                    elif any("flask" in dep for dep in deps):
                        return "python", "flask", 8003
            return "python", "unknown", 8003
            
        # Go service
        elif (service_dir / "go.mod").exists():
            go_mod = self._read_file(service_dir / "go.mod")
            if "gin-gonic/gin" in go_mod:
                return "go", "gin", 8005
            elif "gorilla/mux" in go_mod:
                return "go", "gorilla", 8005
            return "go", "unknown", 8005
            
        return "", "", 0
    
    def _extract_dependencies(self, service_dir: Path, language: str) -> List[ServiceDependency]:
        """Extract dependencies from service configuration files"""
        dependencies = []
        service_name = service_dir.name
        
        if language == "rust":
            dependencies.extend(self._extract_rust_dependencies(service_dir, service_name))
        elif language == "typescript":
            dependencies.extend(self._extract_typescript_dependencies(service_dir, service_name))
        elif language == "python":
            dependencies.extend(self._extract_python_dependencies(service_dir, service_name))
        elif language == "go":
            dependencies.extend(self._extract_go_dependencies(service_dir, service_name))
            
        # Add common infrastructure dependencies
        dependencies.extend(self._extract_infrastructure_dependencies(service_name))
        
        return dependencies
    
    def _extract_rust_dependencies(self, service_dir: Path, service_name: str) -> List[ServiceDependency]:
        """Extract dependencies from Rust Cargo.toml"""
        dependencies = []
        cargo_toml = self._read_toml(service_dir / "Cargo.toml")
        
        if not cargo_toml or "dependencies" not in cargo_toml:
            return dependencies
            
        deps = cargo_toml["dependencies"]
        
        # Database dependencies
        if any(db in deps for db in ["google-cloud-spanner", "spanner"]):
            dependencies.append(ServiceDependency(
                source=service_name,
                target="google-cloud-spanner",
                dependency_type=DependencyType.DATABASE,
                protocol="gRPC",
                port=443,
                criticality=CriticalityLevel.CRITICAL,
                description="Primary database for persistent storage",
                timeout_ms=30000
            ))
            
        # Redis cache
        if "redis" in deps:
            dependencies.append(ServiceDependency(
                source=service_name,
                target="redis",
                dependency_type=DependencyType.CACHE,
                protocol="TCP",
                port=6379,
                criticality=CriticalityLevel.HIGH,
                description="Caching layer for performance optimization",
                timeout_ms=5000
            ))
            
        # Monitoring
        if any(mon in deps for mon in ["prometheus", "metrics"]):
            dependencies.append(ServiceDependency(
                source=service_name,
                target="prometheus",
                dependency_type=DependencyType.MONITORING,
                protocol="HTTP",
                port=9090,
                criticality=CriticalityLevel.LOW,
                description="Metrics collection and monitoring"
            ))
            
        return dependencies
    
    def _extract_typescript_dependencies(self, service_dir: Path, service_name: str) -> List[ServiceDependency]:
        """Extract dependencies from TypeScript package.json"""
        dependencies = []
        package_json = self._read_json(service_dir / "package.json")
        
        if not package_json or "dependencies" not in package_json:
            return dependencies
            
        deps = package_json["dependencies"]
        
        # Google Cloud dependencies
        if "@google-cloud/spanner" in deps:
            dependencies.append(ServiceDependency(
                source=service_name,
                target="google-cloud-spanner",
                dependency_type=DependencyType.DATABASE,
                protocol="gRPC",
                port=443,
                criticality=CriticalityLevel.CRITICAL,
                description="Google Cloud Spanner database client"
            ))
            
        if "@google-cloud/firestore" in deps:
            dependencies.append(ServiceDependency(
                source=service_name,
                target="google-cloud-firestore",
                dependency_type=DependencyType.DATABASE,
                protocol="gRPC",
                port=443,
                criticality=CriticalityLevel.HIGH,
                description="Firestore for real-time document synchronization"
            ))
            
        # Redis
        if any(redis in deps for redis in ["redis", "ioredis"]):
            dependencies.append(ServiceDependency(
                source=service_name,
                target="redis",
                dependency_type=DependencyType.CACHE,
                protocol="TCP",
                port=6379,
                criticality=CriticalityLevel.HIGH,
                description="Redis for caching and session management"
            ))
            
        # Service-to-service dependencies based on service type
        if service_name == "web":
            # Web frontend depends on all backend services
            for target in ["analysis-engine", "collaboration", "marketplace", "query-intelligence"]:
                dependencies.append(ServiceDependency(
                    source=service_name,
                    target=target,
                    dependency_type=DependencyType.HTTP_API,
                    protocol="HTTP",
                    port=None,
                    criticality=CriticalityLevel.CRITICAL,
                    description=f"API calls to {target} service"
                ))
                
        elif service_name == "collaboration":
            dependencies.append(ServiceDependency(
                source=service_name,
                target="analysis-engine",
                dependency_type=DependencyType.HTTP_API,
                protocol="HTTP",
                port=8001,
                criticality=CriticalityLevel.HIGH,
                description="Code analysis for collaborative editing"
            ))
            
        return dependencies
    
    def _extract_python_dependencies(self, service_dir: Path, service_name: str) -> List[ServiceDependency]:
        """Extract dependencies from Python pyproject.toml or requirements.txt"""
        dependencies = []
        deps = []
        
        # Read from pyproject.toml
        if (service_dir / "pyproject.toml").exists():
            pyproject = self._read_toml(service_dir / "pyproject.toml")
            if pyproject and "project" in pyproject and "dependencies" in pyproject["project"]:
                deps = pyproject["project"]["dependencies"]
        
        # Read from requirements.txt
        elif (service_dir / "requirements.txt").exists():
            requirements = self._read_file(service_dir / "requirements.txt")
            deps = requirements.split('\n')
        
        # Google Cloud dependencies
        if any("google-cloud-bigquery" in dep for dep in deps):
            dependencies.append(ServiceDependency(
                source=service_name,
                target="google-cloud-bigquery",
                dependency_type=DependencyType.DATABASE,
                protocol="gRPC",
                port=443,
                criticality=CriticalityLevel.HIGH,
                description="BigQuery for analytics and ML training data"
            ))
            
        if any("google-cloud-aiplatform" in dep for dep in deps):
            dependencies.append(ServiceDependency(
                source=service_name,
                target="google-cloud-aiplatform",
                dependency_type=DependencyType.EXTERNAL_SERVICE,
                protocol="gRPC",
                port=443,
                criticality=CriticalityLevel.HIGH,
                description="AI Platform for ML model serving"
            ))
            
        if any("pinecone" in dep for dep in deps):
            dependencies.append(ServiceDependency(
                source=service_name,
                target="pinecone",
                dependency_type=DependencyType.DATABASE,
                protocol="HTTPS",
                port=443,
                criticality=CriticalityLevel.CRITICAL,
                description="Pinecone vector database for similarity search"
            ))
            
        # Redis
        if any("redis" in dep for dep in deps):
            dependencies.append(ServiceDependency(
                source=service_name,
                target="redis",
                dependency_type=DependencyType.CACHE,
                protocol="TCP",
                port=6379,
                criticality=CriticalityLevel.HIGH,
                description="Redis for caching and session storage"
            ))
            
        # Service-to-service dependencies
        if service_name == "query-intelligence":
            dependencies.extend([
                ServiceDependency(
                    source=service_name,
                    target="analysis-engine",
                    dependency_type=DependencyType.HTTP_API,
                    protocol="HTTP",
                    port=8001,
                    criticality=CriticalityLevel.CRITICAL,
                    description="Get code context for intelligent responses"
                ),
                ServiceDependency(
                    source=service_name,
                    target="pattern-mining",
                    dependency_type=DependencyType.HTTP_API,
                    protocol="HTTP",
                    port=8004,
                    criticality=CriticalityLevel.MEDIUM,
                    description="Get pattern insights for query responses"
                )
            ])
            
        elif service_name == "pattern-mining":
            dependencies.append(ServiceDependency(
                source=service_name,
                target="analysis-engine",
                dependency_type=DependencyType.HTTP_API,
                protocol="HTTP",
                port=8001,
                criticality=CriticalityLevel.HIGH,
                description="Get parsed AST data for pattern detection"
            ))
            
        return dependencies
    
    def _extract_go_dependencies(self, service_dir: Path, service_name: str) -> List[ServiceDependency]:
        """Extract dependencies from Go go.mod"""
        dependencies = []
        go_mod = self._read_file(service_dir / "go.mod")
        
        # PostgreSQL
        if "lib/pq" in go_mod:
            dependencies.append(ServiceDependency(
                source=service_name,
                target="postgresql",
                dependency_type=DependencyType.DATABASE,
                protocol="TCP",
                port=5432,
                criticality=CriticalityLevel.HIGH,
                description="PostgreSQL for marketplace transactions"
            ))
            
        return dependencies
    
    def _extract_infrastructure_dependencies(self, service_name: str) -> List[ServiceDependency]:
        """Add common infrastructure dependencies for all services"""
        dependencies = []
        
        # All services have Redis cache dependency
        dependencies.append(ServiceDependency(
            source=service_name,
            target="redis",
            dependency_type=DependencyType.CACHE,
            protocol="TCP",
            port=6379,
            criticality=CriticalityLevel.HIGH,
            description="Shared Redis cache for performance optimization"
        ))
        
        # All services have Prometheus monitoring
        dependencies.append(ServiceDependency(
            source=service_name,
            target="prometheus",
            dependency_type=DependencyType.MONITORING,
            protocol="HTTP",
            port=9090,
            criticality=CriticalityLevel.LOW,
            description="Prometheus metrics collection"
        ))
        
        return dependencies
    
    def _extract_api_endpoints(self, service_dir: Path, language: str) -> List[str]:
        """Extract API endpoints from service source code"""
        # This is a simplified implementation
        # In practice, you'd parse the actual source code
        endpoints = ["/health", "/metrics"]
        
        service_name = service_dir.name
        
        if service_name == "analysis-engine":
            endpoints.extend(["/analyze", "/parse", "/languages"])
        elif service_name == "collaboration":
            endpoints.extend(["/collaborate", "/documents", "/sessions"])
        elif service_name == "query-intelligence":
            endpoints.extend(["/query", "/search", "/suggestions"])
        elif service_name == "pattern-mining":
            endpoints.extend(["/patterns", "/detect", "/train"])
        elif service_name == "marketplace":
            endpoints.extend(["/products", "/transactions", "/payments"])
        elif service_name == "web":
            endpoints = []  # Frontend doesn't expose APIs
            
        return endpoints
    
    def build_dependency_graph(self):
        """Build NetworkX graph from discovered dependencies"""
        self.services = self.discover_services()
        
        # Add nodes for services and external systems
        for service_name, service in self.services.items():
            self.graph.add_node(service_name, **{
                "type": "service",
                "language": service.language,
                "framework": service.framework,
                "port": service.port
            })
            
        # Add external system nodes
        external_systems = set()
        for service in self.services.values():
            for dep in service.dependencies:
                if dep.target not in self.services:
                    external_systems.add(dep.target)
                    
        for ext_system in external_systems:
            self.graph.add_node(ext_system, type="external")
            
        # Add edges for dependencies
        for service in self.services.values():
            for dep in service.dependencies:
                self.graph.add_edge(
                    dep.source,
                    dep.target,
                    **{
                        "dependency_type": dep.dependency_type.value,
                        "protocol": dep.protocol,
                        "criticality": dep.criticality.value,
                        "description": dep.description
                    }
                )
                self.dependencies.append(dep)
    
    def analyze_impact(self, target_service: str) -> ImpactAnalysis:
        """Analyze the impact of a service failure or change"""
        if not self.graph.has_node(target_service):
            raise ValueError(f"Service {target_service} not found in dependency graph")
        
        # Find all services that depend on the target service
        affected_services = list(self.graph.predecessors(target_service))
        
        # Find critical paths (paths that include critical dependencies)
        critical_paths = []
        alternative_paths = []
        
        for affected in affected_services:
            # Simple path analysis (can be enhanced with more sophisticated algorithms)
            try:
                paths = list(nx.all_simple_paths(self.graph, "web", affected, cutoff=5))
                for path in paths:
                    if target_service in path:
                        # Check if path contains critical dependencies
                        is_critical = any(
                            self.graph[u][v].get("criticality") == "critical"
                            for u, v in zip(path[:-1], path[1:])
                        )
                        if is_critical:
                            critical_paths.append(path)
                        else:
                            alternative_paths.append(path)
            except nx.NetworkXNoPath:
                continue
        
        # Estimate downtime based on criticality and affected services
        downtime = self._estimate_downtime(target_service, affected_services)
        
        # Generate mitigation strategies
        mitigation_strategies = self._generate_mitigation_strategies(target_service, affected_services)
        
        # Generate rollback plan
        rollback_plan = self._generate_rollback_plan(target_service)
        
        return ImpactAnalysis(
            target_service=target_service,
            affected_services=affected_services,
            critical_paths=critical_paths,
            alternative_paths=alternative_paths,
            estimated_downtime=downtime,
            mitigation_strategies=mitigation_strategies,
            rollback_plan=rollback_plan
        )
    
    def _estimate_downtime(self, target_service: str, affected_services: List[str]) -> str:
        """Estimate potential downtime based on service criticality"""
        if target_service in ["google-cloud-spanner", "redis"]:
            return "5-30 minutes (automatic failover)"
        elif target_service == "analysis-engine":
            return "2-15 minutes (service restart)"
        elif len(affected_services) > 3:
            return "10-60 minutes (cascading failure)"
        else:
            return "1-5 minutes (isolated failure)"
    
    def _generate_mitigation_strategies(self, target_service: str, affected_services: List[str]) -> List[str]:
        """Generate mitigation strategies for service failures"""
        strategies = []
        
        if target_service == "google-cloud-spanner":
            strategies.extend([
                "Activate read replicas for read-only operations",
                "Use cached data where possible",
                "Implement circuit breaker pattern",
                "Graceful degradation of non-critical features"
            ])
        elif target_service == "redis":
            strategies.extend([
                "Fallback to direct database queries",
                "Use local in-memory cache as backup",
                "Implement cache warming procedures",
                "Reduce cache-dependent features temporarily"
            ])
        elif target_service == "analysis-engine":
            strategies.extend([
                "Route to healthy analysis-engine instances",
                "Use pre-computed analysis results",
                "Queue analysis requests for later processing",
                "Implement simplified analysis mode"
            ])
        else:
            strategies.extend([
                "Implement circuit breaker pattern",
                "Use cached responses where available",
                "Graceful feature degradation",
                "Redirect to alternative service instances"
            ])
            
        return strategies
    
    def _generate_rollback_plan(self, target_service: str) -> List[str]:
        """Generate rollback plan for service deployment issues"""
        return [
            f"1. Stop new {target_service} deployment",
            f"2. Restore previous {target_service} version",
            "3. Verify service health checks",
            "4. Validate dependent service connectivity",
            "5. Monitor for cascading issues",
            "6. Update load balancer configuration",
            "7. Confirm system stability"
        ]
    
    def validate_dependencies(self) -> Dict[str, List[str]]:
        """Validate that all dependencies are properly configured"""
        issues = {}
        
        for service_name, service in self.services.items():
            service_issues = []
            
            # Check for missing health checks
            if not service.health_check:
                service_issues.append("Missing health check endpoint")
                
            # Check for circular dependencies
            if self._has_circular_dependency(service_name):
                service_issues.append("Circular dependency detected")
                
            # Check for missing critical dependencies
            critical_deps = [dep for dep in service.dependencies 
                           if dep.criticality == CriticalityLevel.CRITICAL]
            if not critical_deps and service_name != "web":
                service_issues.append("No critical dependencies defined")
                
            if service_issues:
                issues[service_name] = service_issues
                
        return issues
    
    def _has_circular_dependency(self, service_name: str) -> bool:
        """Check if a service has circular dependencies"""
        try:
            cycles = list(nx.simple_cycles(self.graph))
            return any(service_name in cycle for cycle in cycles)
        except:
            return False
    
    def generate_graph_visualization(self, output_file: str = "dependency_graph.png"):
        """Generate a visual representation of the dependency graph"""
        plt.figure(figsize=(16, 12))
        
        # Create layout
        pos = nx.spring_layout(self.graph, k=3, iterations=50)
        
        # Separate nodes by type
        service_nodes = [n for n, d in self.graph.nodes(data=True) if d.get("type") == "service"]
        external_nodes = [n for n, d in self.graph.nodes(data=True) if d.get("type") == "external"]
        
        # Draw nodes
        nx.draw_networkx_nodes(self.graph, pos, nodelist=service_nodes, 
                             node_color='lightblue', node_size=2000, alpha=0.8)
        nx.draw_networkx_nodes(self.graph, pos, nodelist=external_nodes,
                             node_color='lightcoral', node_size=1500, alpha=0.8)
        
        # Draw edges with different colors for criticality
        critical_edges = [(u, v) for u, v, d in self.graph.edges(data=True) 
                         if d.get("criticality") == "critical"]
        high_edges = [(u, v) for u, v, d in self.graph.edges(data=True) 
                     if d.get("criticality") == "high"]
        other_edges = [(u, v) for u, v, d in self.graph.edges(data=True) 
                      if d.get("criticality") not in ["critical", "high"]]
        
        nx.draw_networkx_edges(self.graph, pos, edgelist=critical_edges, 
                             edge_color='red', width=3, alpha=0.8, arrows=True)
        nx.draw_networkx_edges(self.graph, pos, edgelist=high_edges,
                             edge_color='orange', width=2, alpha=0.6, arrows=True)
        nx.draw_networkx_edges(self.graph, pos, edgelist=other_edges,
                             edge_color='gray', width=1, alpha=0.4, arrows=True)
        
        # Draw labels
        nx.draw_networkx_labels(self.graph, pos, font_size=10, font_weight='bold')
        
        plt.title("Episteme Service Dependency Graph", size=16, weight='bold')
        plt.legend(['Services', 'External Systems', 'Critical Dependencies', 
                   'High Priority Dependencies', 'Other Dependencies'],
                  loc='upper left')
        plt.axis('off')
        plt.tight_layout()
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"Dependency graph saved to {output_file}")
    
    def export_analysis(self, output_file: str = "dependency_analysis.json"):
        """Export complete dependency analysis to JSON"""
        analysis_data = {
            "services": {name: asdict(service) for name, service in self.services.items()},
            "dependencies": [asdict(dep) for dep in self.dependencies],
            "graph_stats": {
                "nodes": self.graph.number_of_nodes(),
                "edges": self.graph.number_of_edges(),
                "density": nx.density(self.graph),
                "is_weakly_connected": nx.is_weakly_connected(self.graph)
            },
            "validation_issues": self.validate_dependencies()
        }
        
        with open(output_file, 'w') as f:
            json.dump(analysis_data, f, indent=2, default=str)
            
        print(f"Analysis exported to {output_file}")
    
    def _read_json(self, file_path: Path) -> Optional[Dict]:
        """Read and parse JSON file"""
        try:
            with open(file_path, 'r') as f:
                return json.load(f)
        except:
            return None
    
    def _read_toml(self, file_path: Path) -> Optional[Dict]:
        """Read and parse TOML file"""
        try:
            import tomli
            with open(file_path, 'rb') as f:
                return tomli.load(f)
        except ImportError:
            # Fallback to basic parsing for simple TOML files
            try:
                content = self._read_file(file_path)
                return self._parse_simple_toml(content)
            except:
                return None
        except:
            return None
    
    def _read_yaml(self, file_path: Path) -> Optional[Dict]:
        """Read and parse YAML file"""
        try:
            with open(file_path, 'r') as f:
                return yaml.safe_load(f)
        except:
            return None
    
    def _parse_simple_toml(self, content: str) -> Dict:
        """Simple TOML parser for basic dependency extraction"""
        result = {}
        current_section = None
        
        for line in content.split('\n'):
            line = line.strip()
            if not line or line.startswith('#'):
                continue
                
            if line.startswith('[') and line.endswith(']'):
                current_section = line[1:-1]
                if current_section not in result:
                    result[current_section] = {}
            elif '=' in line and current_section:
                key, value = line.split('=', 1)
                key = key.strip()
                value = value.strip().strip('"\'')
                
                if current_section == "dependencies":
                    # Parse dependency version
                    if '{' in value:
                        # Complex dependency: axum = { version = "0.8.4", features = ["ws"] }
                        result[current_section][key] = value
                    else:
                        # Simple dependency: redis = "5.2.0"
                        result[current_section][key] = value
                        
        return result
    
    def _read_file(self, file_path: Path) -> str:
        """Read file content as string"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except:
            return ""

def main():
    parser = argparse.ArgumentParser(description="Episteme Service Dependency Analyzer")
    parser.add_argument("--analyze-all", action="store_true", 
                       help="Analyze all services and dependencies")
    parser.add_argument("--impact-analysis", type=str, metavar="SERVICE",
                       help="Perform impact analysis for a specific service")
    parser.add_argument("--validate-dependencies", action="store_true",
                       help="Validate dependency configurations")
    parser.add_argument("--generate-graph", action="store_true",
                       help="Generate visual dependency graph")
    parser.add_argument("--export", type=str, metavar="FILE", default="dependency_analysis.json",
                       help="Export analysis to JSON file")
    parser.add_argument("--project-root", type=str, default=".",
                       help="Project root directory")
    
    args = parser.parse_args()
    
    analyzer = DependencyAnalyzer(args.project_root)
    analyzer.build_dependency_graph()
    
    if args.analyze_all:
        print("=== Episteme Service Dependency Analysis ===")
        print(f"Discovered {len(analyzer.services)} services:")
        for name, service in analyzer.services.items():
            print(f"  - {name} ({service.language}/{service.framework}) on port {service.port}")
            print(f"    Dependencies: {len(service.dependencies)}")
            print(f"    APIs: {', '.join(service.exposed_apis[:3])}{'...' if len(service.exposed_apis) > 3 else ''}")
        
        print(f"\nTotal dependencies: {len(analyzer.dependencies)}")
        print(f"Graph nodes: {analyzer.graph.number_of_nodes()}")
        print(f"Graph edges: {analyzer.graph.number_of_edges()}")
        
    if args.impact_analysis:
        try:
            impact = analyzer.analyze_impact(args.impact_analysis)
            print(f"\n=== Impact Analysis: {args.impact_analysis} ===")
            print(f"Affected services: {', '.join(impact.affected_services)}")
            print(f"Critical paths: {len(impact.critical_paths)}")
            print(f"Estimated downtime: {impact.estimated_downtime}")
            print("\nMitigation strategies:")
            for i, strategy in enumerate(impact.mitigation_strategies, 1):
                print(f"  {i}. {strategy}")
        except ValueError as e:
            print(f"Error: {e}")
    
    if args.validate_dependencies:
        issues = analyzer.validate_dependencies()
        if issues:
            print("\n=== Dependency Validation Issues ===")
            for service, service_issues in issues.items():
                print(f"{service}:")
                for issue in service_issues:
                    print(f"  - {issue}")
        else:
            print("\n✓ All dependencies are properly configured")
    
    if args.generate_graph:
        try:
            analyzer.generate_graph_visualization()
        except ImportError:
            print("Error: matplotlib required for graph visualization")
            print("Install with: pip install matplotlib")
    
    if args.export:
        analyzer.export_analysis(args.export)

if __name__ == "__main__":
    main()