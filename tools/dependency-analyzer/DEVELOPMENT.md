# Development Environment Setup for Dependency Testing

This document explains how to set up the development environment to test dependency validation.

## Using Docker Compose

The easiest way to test dependencies is using the provided docker-compose.dev.yml:

```bash
# Start all infrastructure services
docker-compose -f docker-compose.dev.yml up -d

# Verify services are running
docker-compose -f docker-compose.dev.yml ps

# Test dependency validation
cd tools/dependency-analyzer
python validate_dependencies.py --all
```

## Manual Service Setup

### Redis
```bash
# Using Docker
docker run -d --name redis -p 6379:6379 redis:7-alpine

# Using package manager (Ubuntu/Debian)
sudo apt-get install redis-server
sudo systemctl start redis-server
```

### PostgreSQL
```bash
# Using Docker
docker run -d --name postgres \
  -e POSTGRES_DB=ccl_dev \
  -e POSTGRES_USER=ccl \
  -e POSTGRES_PASSWORD=dev_password \
  -p 5432:5432 \
  postgres:15

# Using package manager (Ubuntu/Debian)
sudo apt-get install postgresql postgresql-contrib
sudo -u postgres createdb ccl_dev
sudo -u postgres createuser ccl
```

### Spanner Emulator
```bash
# Using Docker
docker run -d --name spanner-emulator \
  -p 9010:9010 -p 9020:9020 \
  gcr.io/cloud-spanner-emulator/emulator

# Set environment variable
export SPANNER_EMULATOR_HOST=localhost:9010
```

## Environment Variables

Create a `.env` file in the project root:

```bash
# Database connections
REDIS_HOST=localhost
REDIS_PORT=6379

POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=ccl_dev
POSTGRES_USER=ccl
POSTGRES_PASSWORD=dev_password

# Google Cloud Platform (for production)
GCP_PROJECT=your-project-id
SPANNER_INSTANCE=your-instance
SPANNER_DATABASE=your-database

# For development with emulator
SPANNER_EMULATOR_HOST=localhost:9010
```

## Service Health Endpoints

When services are running, they should expose health endpoints:

- analysis-engine: http://localhost:8001/health
- collaboration: http://localhost:8002/health
- web: http://localhost:3000/health
- marketplace: http://localhost:8005/health
- pattern-mining: http://localhost:8003/health
- query-intelligence: http://localhost:8004/health

## Testing Dependency Validation

```bash
# Test all dependencies
python validate_dependencies.py --all

# Test specific service
python validate_dependencies.py --service analysis-engine

# Test only critical dependencies
python validate_dependencies.py --critical-only

# Export health report
python validate_dependencies.py --all --export-report health-report.json
```

## Expected Output (Healthy System)

```
=== Episteme Dependency Health Report ===
Generated: 2025-01-21 15:30:45
Overall Health: 100.0% (6/6 services healthy)

📋 Recommendations:
  - All services are healthy - no immediate action required

📊 Service Details:
  ✅ analysis-engine: 4/4 dependencies healthy
  ✅ collaboration: 5/5 dependencies healthy
  ✅ web: 4/4 dependencies healthy
  ✅ marketplace: 4/4 dependencies healthy
  ✅ pattern-mining: 4/4 dependencies healthy
  ✅ query-intelligence: 4/4 dependencies healthy
```

## CI/CD Integration

The validation returns appropriate exit codes:
- 0: All healthy
- 1: Critical issues found
- 2: Degraded services found

This makes it suitable for CI/CD pipeline health checks.