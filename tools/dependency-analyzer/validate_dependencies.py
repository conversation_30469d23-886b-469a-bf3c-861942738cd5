#!/usr/bin/env python3
"""
Dependency Health Validation Script

This script validates the health and connectivity of service dependencies
in the Episteme platform. It can be run as part of CI/CD or for manual validation.

Usage:
    python validate_dependencies.py --all
    python validate_dependencies.py --service <service-name>
    python validate_dependencies.py --critical-only
    python validate_dependencies.py --export-report
"""

import asyncio
import aiohttp
import redis
import psycopg2
import time
import json
import argparse
import logging
from dataclasses import dataclass, asdict
from typing import Dict, List, Optional, Tuple
from enum import Enum
import os
from pathlib import Path

class ValidationStatus(Enum):
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"

@dataclass
class DependencyCheck:
    """Represents a dependency health check result"""
    service: str
    dependency: str
    status: ValidationStatus
    response_time_ms: Optional[float]
    error_message: Optional[str]
    last_checked: str
    endpoint: Optional[str] = None

@dataclass
class ServiceHealth:
    """Overall health status of a service"""
    service_name: str
    overall_status: ValidationStatus
    checks: List[DependencyCheck]
    healthy_count: int
    total_count: int
    critical_failures: List[str]

class DependencyValidator:
    """Main validator class for checking dependency health"""
    
    def __init__(self):
        self.logger = self._setup_logging()
        self.session = None
        
        # Service configurations
        self.services = {
            "analysis-engine": {
                "port": 8001,
                "health_endpoint": "/health",
                "critical_dependencies": ["google-cloud-spanner", "redis"]
            },
            "collaboration": {
                "port": 8002,
                "health_endpoint": "/health",
                "critical_dependencies": ["google-cloud-spanner", "google-cloud-firestore", "redis"]
            },
            "web": {
                "port": 3000,
                "health_endpoint": "/health",
                "critical_dependencies": ["analysis-engine", "collaboration"]
            },
            "marketplace": {
                "port": 8005,
                "health_endpoint": "/health",
                "critical_dependencies": ["postgresql", "redis"]
            },
            "pattern-mining": {
                "port": 8003,
                "health_endpoint": "/health",
                "critical_dependencies": ["google-cloud-bigquery", "redis"]
            },
            "query-intelligence": {
                "port": 8004,
                "health_endpoint": "/health",
                "critical_dependencies": ["pinecone", "redis"]
            }
        }
        
        # Infrastructure configurations
        self.infrastructure = {
            "redis": {
                "host": os.getenv("REDIS_HOST", "localhost"),
                "port": int(os.getenv("REDIS_PORT", "6379")),
                "timeout": 5
            },
            "postgresql": {
                "host": os.getenv("POSTGRES_HOST", "localhost"),
                "port": int(os.getenv("POSTGRES_PORT", "5432")),
                "database": os.getenv("POSTGRES_DB", "ccl_dev"),
                "user": os.getenv("POSTGRES_USER", "ccl"),
                "password": os.getenv("POSTGRES_PASSWORD", "dev_password"),
                "timeout": 10
            },
            "google-cloud-spanner": {
                "project": os.getenv("GCP_PROJECT", ""),
                "instance": os.getenv("SPANNER_INSTANCE", ""),
                "database": os.getenv("SPANNER_DATABASE", ""),
                "timeout": 30
            }
        }
    
    def _setup_logging(self) -> logging.Logger:
        """Setup logging configuration"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        return logging.getLogger("dependency-validator")
    
    async def __aenter__(self):
        """Async context manager entry"""
        timeout = aiohttp.ClientTimeout(total=30)
        self.session = aiohttp.ClientSession(timeout=timeout)
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    async def validate_service_health(self, service_name: str) -> ServiceHealth:
        """Validate health of a single service and its dependencies"""
        if service_name not in self.services:
            raise ValueError(f"Unknown service: {service_name}")
        
        service_config = self.services[service_name]
        checks = []
        
        # Check service itself
        service_check = await self._check_service_endpoint(service_name, service_config)
        checks.append(service_check)
        
        # Check critical dependencies
        for dep in service_config["critical_dependencies"]:
            dep_check = await self._check_dependency(service_name, dep)
            checks.append(dep_check)
        
        # Check common infrastructure
        redis_check = await self._check_redis(service_name)
        checks.append(redis_check)
        
        # Calculate overall health
        healthy_count = sum(1 for check in checks if check.status == ValidationStatus.HEALTHY)
        total_count = len(checks)
        critical_failures = [
            check.dependency for check in checks 
            if check.status == ValidationStatus.UNHEALTHY and 
            check.dependency in service_config["critical_dependencies"]
        ]
        
        # Determine overall status
        if critical_failures:
            overall_status = ValidationStatus.UNHEALTHY
        elif healthy_count == total_count:
            overall_status = ValidationStatus.HEALTHY
        elif healthy_count / total_count >= 0.7:
            overall_status = ValidationStatus.DEGRADED
        else:
            overall_status = ValidationStatus.UNHEALTHY
        
        return ServiceHealth(
            service_name=service_name,
            overall_status=overall_status,
            checks=checks,
            healthy_count=healthy_count,
            total_count=total_count,
            critical_failures=critical_failures
        )
    
    async def _check_service_endpoint(self, service_name: str, config: Dict) -> DependencyCheck:
        """Check if service health endpoint is responding"""
        url = f"http://localhost:{config['port']}{config['health_endpoint']}"
        
        start_time = time.time()
        try:
            async with self.session.get(url) as response:
                response_time = (time.time() - start_time) * 1000
                
                if response.status == 200:
                    status = ValidationStatus.HEALTHY
                    error_msg = None
                else:
                    status = ValidationStatus.DEGRADED
                    error_msg = f"HTTP {response.status}"
                    
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            status = ValidationStatus.UNHEALTHY
            error_msg = str(e)
        
        return DependencyCheck(
            service=service_name,
            dependency="self",
            status=status,
            response_time_ms=response_time,
            error_message=error_msg,
            last_checked=time.strftime("%Y-%m-%d %H:%M:%S"),
            endpoint=url
        )
    
    async def _check_dependency(self, service_name: str, dependency: str) -> DependencyCheck:
        """Check specific dependency health"""
        start_time = time.time()
        
        try:
            if dependency == "google-cloud-spanner":
                status, error_msg = await self._check_spanner()
            elif dependency == "google-cloud-firestore":
                status, error_msg = await self._check_firestore()
            elif dependency == "google-cloud-bigquery":
                status, error_msg = await self._check_bigquery()
            elif dependency == "pinecone":
                status, error_msg = await self._check_pinecone()
            elif dependency == "postgresql":
                status, error_msg = await self._check_postgresql()
            elif dependency in self.services:
                # Service-to-service dependency
                target_config = self.services[dependency]
                dep_check = await self._check_service_endpoint(dependency, target_config)
                return DependencyCheck(
                    service=service_name,
                    dependency=dependency,
                    status=dep_check.status,
                    response_time_ms=dep_check.response_time_ms,
                    error_message=dep_check.error_message,
                    last_checked=time.strftime("%Y-%m-%d %H:%M:%S"),
                    endpoint=dep_check.endpoint
                )
            else:
                status = ValidationStatus.UNKNOWN
                error_msg = f"Unknown dependency type: {dependency}"
                
        except Exception as e:
            status = ValidationStatus.UNHEALTHY
            error_msg = str(e)
        
        response_time = (time.time() - start_time) * 1000
        
        return DependencyCheck(
            service=service_name,
            dependency=dependency,
            status=status,
            response_time_ms=response_time,
            error_message=error_msg,
            last_checked=time.strftime("%Y-%m-%d %H:%M:%S")
        )
    
    async def _check_redis(self, service_name: str) -> DependencyCheck:
        """Check Redis connectivity"""
        start_time = time.time()
        
        try:
            config = self.infrastructure["redis"]
            r = redis.Redis(
                host=config["host"],
                port=config["port"],
                socket_timeout=config["timeout"],
                socket_connect_timeout=config["timeout"]
            )
            
            # Simple ping test
            r.ping()
            
            status = ValidationStatus.HEALTHY
            error_msg = None
            
        except redis.ConnectionError as e:
            status = ValidationStatus.UNHEALTHY
            error_msg = f"Redis connection failed: {str(e)}"
        except Exception as e:
            status = ValidationStatus.UNHEALTHY
            error_msg = f"Redis error: {str(e)}"
        
        response_time = (time.time() - start_time) * 1000
        
        return DependencyCheck(
            service=service_name,
            dependency="redis",
            status=status,
            response_time_ms=response_time,
            error_message=error_msg,
            last_checked=time.strftime("%Y-%m-%d %H:%M:%S")
        )
    
    async def _check_postgresql(self) -> Tuple[ValidationStatus, Optional[str]]:
        """Check PostgreSQL connectivity"""
        try:
            config = self.infrastructure["postgresql"]
            conn = psycopg2.connect(
                host=config["host"],
                port=config["port"],
                database=config["database"],
                user=config["user"],
                password=config["password"],
                connect_timeout=config["timeout"]
            )
            
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            cursor.close()
            conn.close()
            
            return ValidationStatus.HEALTHY, None
            
        except psycopg2.OperationalError as e:
            return ValidationStatus.UNHEALTHY, f"PostgreSQL connection failed: {str(e)}"
        except Exception as e:
            return ValidationStatus.UNHEALTHY, f"PostgreSQL error: {str(e)}"
    
    async def _check_spanner(self) -> Tuple[ValidationStatus, Optional[str]]:
        """Check Google Cloud Spanner connectivity"""
        try:
            config = self.infrastructure["google-cloud-spanner"]
            
            if not all([config["project"], config["instance"], config["database"]]):
                return ValidationStatus.UNKNOWN, "Spanner configuration incomplete"
            
            # This would require google-cloud-spanner client library
            # For now, return unknown status in development environment
            return ValidationStatus.UNKNOWN, "Spanner check not implemented (requires GCP credentials)"
            
        except Exception as e:
            return ValidationStatus.UNHEALTHY, f"Spanner error: {str(e)}"
    
    async def _check_firestore(self) -> Tuple[ValidationStatus, Optional[str]]:
        """Check Google Cloud Firestore connectivity"""
        try:
            # This would require google-cloud-firestore client library
            return ValidationStatus.UNKNOWN, "Firestore check not implemented (requires GCP credentials)"
        except Exception as e:
            return ValidationStatus.UNHEALTHY, f"Firestore error: {str(e)}"
    
    async def _check_bigquery(self) -> Tuple[ValidationStatus, Optional[str]]:
        """Check Google Cloud BigQuery connectivity"""
        try:
            # This would require google-cloud-bigquery client library
            return ValidationStatus.UNKNOWN, "BigQuery check not implemented (requires GCP credentials)"
        except Exception as e:
            return ValidationStatus.UNHEALTHY, f"BigQuery error: {str(e)}"
    
    async def _check_pinecone(self) -> Tuple[ValidationStatus, Optional[str]]:
        """Check Pinecone vector database connectivity"""
        try:
            # This would require pinecone-client library and API key
            return ValidationStatus.UNKNOWN, "Pinecone check not implemented (requires API key)"
        except Exception as e:
            return ValidationStatus.UNHEALTHY, f"Pinecone error: {str(e)}"
    
    async def validate_all_services(self) -> Dict[str, ServiceHealth]:
        """Validate health of all services"""
        results = {}
        
        for service_name in self.services.keys():
            try:
                health = await self.validate_service_health(service_name)
                results[service_name] = health
                self.logger.info(f"Validated {service_name}: {health.overall_status.value}")
            except Exception as e:
                self.logger.error(f"Failed to validate {service_name}: {str(e)}")
                # Create a failure result
                results[service_name] = ServiceHealth(
                    service_name=service_name,
                    overall_status=ValidationStatus.UNHEALTHY,
                    checks=[],
                    healthy_count=0,
                    total_count=0,
                    critical_failures=[f"Validation failed: {str(e)}"]
                )
        
        return results
    
    async def validate_critical_dependencies(self) -> Dict[str, ServiceHealth]:
        """Validate only critical dependencies across all services"""
        critical_services = []
        
        for service_name, config in self.services.items():
            if config.get("critical_dependencies"):
                critical_services.append(service_name)
        
        results = {}
        for service_name in critical_services:
            results[service_name] = await self.validate_service_health(service_name)
        
        return results
    
    def generate_health_report(self, results: Dict[str, ServiceHealth]) -> Dict:
        """Generate comprehensive health report"""
        overall_healthy = sum(1 for health in results.values() 
                            if health.overall_status == ValidationStatus.HEALTHY)
        overall_total = len(results)
        
        critical_issues = []
        degraded_services = []
        
        for service_name, health in results.items():
            if health.overall_status == ValidationStatus.UNHEALTHY:
                critical_issues.extend([
                    f"{service_name}: {failure}" for failure in health.critical_failures
                ])
            elif health.overall_status == ValidationStatus.DEGRADED:
                degraded_services.append(service_name)
        
        return {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "overall_health": {
                "healthy_services": overall_healthy,
                "total_services": overall_total,
                "health_percentage": round((overall_healthy / overall_total) * 100, 2) if overall_total > 0 else 0
            },
            "critical_issues": critical_issues,
            "degraded_services": degraded_services,
            "service_details": {
                service_name: {
                    "status": health.overall_status.value,
                    "healthy_dependencies": health.healthy_count,
                    "total_dependencies": health.total_count,
                    "critical_failures": health.critical_failures,
                    "checks": [asdict(check) for check in health.checks]
                }
                for service_name, health in results.items()
            },
            "recommendations": self._generate_recommendations(results)
        }
    
    def _generate_recommendations(self, results: Dict[str, ServiceHealth]) -> List[str]:
        """Generate actionable recommendations based on health check results"""
        recommendations = []
        
        unhealthy_services = [name for name, health in results.items() 
                            if health.overall_status == ValidationStatus.UNHEALTHY]
        
        if unhealthy_services:
            recommendations.append(
                f"Immediate action required: {', '.join(unhealthy_services)} services are unhealthy"
            )
        
        degraded_services = [name for name, health in results.items() 
                           if health.overall_status == ValidationStatus.DEGRADED]
        
        if degraded_services:
            recommendations.append(
                f"Monitor closely: {', '.join(degraded_services)} services are degraded"
            )
        
        # Check for common infrastructure issues
        redis_issues = []
        for service_name, health in results.items():
            redis_checks = [check for check in health.checks if check.dependency == "redis"]
            if redis_checks and redis_checks[0].status != ValidationStatus.HEALTHY:
                redis_issues.append(service_name)
        
        if redis_issues:
            recommendations.append(
                f"Redis connectivity issues affecting: {', '.join(redis_issues)}"
            )
        
        # Check response times
        slow_services = []
        for service_name, health in results.items():
            for check in health.checks:
                if check.response_time_ms and check.response_time_ms > 5000:  # 5 seconds
                    slow_services.append(f"{service_name}/{check.dependency}")
        
        if slow_services:
            recommendations.append(
                f"Performance issues (>5s response): {', '.join(slow_services)}"
            )
        
        if not recommendations:
            recommendations.append("All services are healthy - no immediate action required")
        
        return recommendations

async def main():
    parser = argparse.ArgumentParser(description="Episteme Dependency Health Validator")
    parser.add_argument("--all", action="store_true", 
                       help="Validate all services and dependencies")
    parser.add_argument("--service", type=str, metavar="SERVICE",
                       help="Validate specific service")
    parser.add_argument("--critical-only", action="store_true",
                       help="Validate only critical dependencies")
    parser.add_argument("--export-report", type=str, metavar="FILE",
                       help="Export health report to JSON file")
    parser.add_argument("--verbose", action="store_true",
                       help="Enable verbose logging")
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    async with DependencyValidator() as validator:
        if args.service:
            # Validate specific service
            try:
                health = await validator.validate_service_health(args.service)
                results = {args.service: health}
            except ValueError as e:
                print(f"Error: {e}")
                return 1
                
        elif args.critical_only:
            # Validate only critical dependencies
            results = await validator.validate_critical_dependencies()
            
        else:
            # Validate all services (default)
            results = await validator.validate_all_services()
        
        # Generate and display report
        report = validator.generate_health_report(results)
        
        print("=== Episteme Dependency Health Report ===")
        print(f"Generated: {report['timestamp']}")
        print(f"Overall Health: {report['overall_health']['health_percentage']}% "
              f"({report['overall_health']['healthy_services']}/{report['overall_health']['total_services']} services healthy)")
        
        if report['critical_issues']:
            print("\n🔴 Critical Issues:")
            for issue in report['critical_issues']:
                print(f"  - {issue}")
        
        if report['degraded_services']:
            print(f"\n🟡 Degraded Services: {', '.join(report['degraded_services'])}")
        
        print("\n📋 Recommendations:")
        for rec in report['recommendations']:
            print(f"  - {rec}")
        
        # Show service details
        print("\n📊 Service Details:")
        for service_name, details in report['service_details'].items():
            status_icon = {"healthy": "✅", "degraded": "⚠️", "unhealthy": "❌", "unknown": "❓"}
            icon = status_icon.get(details['status'], "❓")
            print(f"  {icon} {service_name}: {details['healthy_dependencies']}/{details['total_dependencies']} dependencies healthy")
            
            if details['critical_failures']:
                for failure in details['critical_failures']:
                    print(f"    ❌ {failure}")
        
        # Export report if requested
        if args.export_report:
            with open(args.export_report, 'w') as f:
                json.dump(report, f, indent=2)
            print(f"\nReport exported to {args.export_report}")
        
        # Return exit code based on health
        if report['critical_issues']:
            return 1  # Critical issues found
        elif report['degraded_services']:
            return 2  # Degraded services found
        else:
            return 0  # All healthy

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)