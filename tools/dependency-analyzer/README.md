# Episteme Dependency Analyzer Tools

This directory contains tools for analyzing and managing service dependencies within the Episteme platform.

## Overview

The dependency analyzer addresses **EPIS-028: Service dependencies not mapped** by providing:

1. **Dependency Discovery**: Automatically discovers services and their dependencies
2. **Impact Analysis**: Analyzes the impact of service failures or changes
3. **Health Validation**: Validates dependency health and connectivity
4. **Visualization**: Generates dependency graphs and documentation
5. **Continuous Monitoring**: Provides scripts for ongoing dependency validation

## Tools Included

### 1. dependency_analyzer.py

Main dependency analysis tool that discovers services and analyzes their relationships.

**Features:**
- Discovers services by examining configuration files (Cargo.toml, package.json, pyproject.toml, go.mod)
- Extracts dependencies from service configurations
- Builds dependency graphs using NetworkX
- Performs impact analysis for service failures
- Generates visual dependency graphs
- Exports analysis data to JSON

**Usage:**
```bash
# Analyze all services
python dependency_analyzer.py --analyze-all

# Impact analysis for specific service
python dependency_analyzer.py --impact-analysis analysis-engine

# Validate dependencies
python dependency_analyzer.py --validate-dependencies

# Generate visual graph
python dependency_analyzer.py --generate-graph

# Export analysis
python dependency_analyzer.py --export dependency_report.json
```

### 2. validate_dependencies.py

Health validation tool that checks the connectivity and health of service dependencies.

**Features:**
- Checks service health endpoints
- Validates database connectivity (Redis, PostgreSQL)
- Tests external service connectivity
- Generates health reports with recommendations
- Supports async validation for performance
- Provides exit codes for CI/CD integration

**Usage:**
```bash
# Validate all services
python validate_dependencies.py --all

# Validate specific service
python validate_dependencies.py --service analysis-engine

# Validate only critical dependencies
python validate_dependencies.py --critical-only

# Export health report
python validate_dependencies.py --all --export-report health_report.json
```

### 3. Main Script (../../scripts/dependency-mapper.sh)

Convenience script that orchestrates all dependency analysis tasks.

**Usage:**
```bash
# Setup environment
./scripts/dependency-mapper.sh setup

# Run full analysis
./scripts/dependency-mapper.sh analyze

# Generate dependency graph
./scripts/dependency-mapper.sh graph

# Impact analysis
./scripts/dependency-mapper.sh impact analysis-engine

# Validate dependencies
./scripts/dependency-mapper.sh validate

# Generate comprehensive report
./scripts/dependency-mapper.sh report

# Update all documentation
./scripts/dependency-mapper.sh update-docs
```

## Installation

1. **Install dependencies:**
```bash
pip install -r requirements.txt
```

2. **Or use the setup script:**
```bash
./scripts/dependency-mapper.sh setup
```

## Service Discovery

The analyzer automatically discovers services in the `services/` directory by examining:

### Rust Services (Cargo.toml)
- Framework detection: axum, warp, actix-web
- Dependencies: google-cloud-spanner, redis, prometheus
- Port assignment: 8001 (default)

### TypeScript/Node.js Services (package.json)
- Framework detection: express, next, socket.io
- Dependencies: @google-cloud/spanner, @google-cloud/firestore, redis
- Port assignment: 3000 (web), 8002 (services)

### Python Services (pyproject.toml, requirements.txt)
- Framework detection: fastapi, flask
- Dependencies: google-cloud-bigquery, google-cloud-aiplatform, pinecone-client
- Port assignment: 8003/8004

### Go Services (go.mod)
- Framework detection: gin-gonic/gin, gorilla/mux
- Dependencies: lib/pq (PostgreSQL)
- Port assignment: 8005

## Dependency Types

The analyzer categorizes dependencies into:

1. **HTTP_API**: Service-to-service HTTP communication
2. **WEBSOCKET**: Real-time WebSocket connections
3. **DATABASE**: Database connections (Spanner, PostgreSQL, BigQuery)
4. **CACHE**: Caching layers (Redis)
5. **MESSAGE_QUEUE**: Message queues (Kafka, Pub/Sub)
6. **EXTERNAL_SERVICE**: Third-party services (Pinecone, OpenAI)
7. **MONITORING**: Observability tools (Prometheus, Jaeger)

## Criticality Levels

Dependencies are classified by criticality:

- **CRITICAL**: Service cannot function without this dependency
- **HIGH**: Service is significantly degraded without this dependency
- **MEDIUM**: Service has reduced functionality without this dependency
- **LOW**: Service can operate normally without this dependency

## Output Files

### Dependency Analysis JSON
```json
{
  "services": {
    "analysis-engine": {
      "name": "analysis-engine",
      "language": "rust",
      "framework": "axum",
      "dependencies": [...]
    }
  },
  "dependencies": [...],
  "graph_stats": {
    "nodes": 13,
    "edges": 26,
    "density": 0.192
  },
  "validation_issues": {...}
}
```

### Health Report JSON
```json
{
  "timestamp": "2025-01-21 15:30:45",
  "overall_health": {
    "healthy_services": 5,
    "total_services": 6,
    "health_percentage": 83.33
  },
  "critical_issues": [...],
  "degraded_services": [...],
  "service_details": {...},
  "recommendations": [...]
}
```

## Integration with CI/CD

### GitHub Actions Example
```yaml
name: Dependency Health Check
on: [push, pull_request]
jobs:
  dependency-health:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'
      - name: Install dependencies
        run: |
          cd tools/dependency-analyzer
          pip install -r requirements.txt
      - name: Validate dependencies
        run: |
          cd tools/dependency-analyzer
          python validate_dependencies.py --critical-only
```

### Pre-commit Hook Example
```bash
#!/bin/bash
# .git/hooks/pre-commit

# Run dependency validation before commit
./scripts/dependency-mapper.sh validate

if [ $? -ne 0 ]; then
    echo "Dependency validation failed. Commit aborted."
    exit 1
fi
```

## Environment Variables

The validation script supports environment variables for configuration:

```bash
# Redis
REDIS_HOST=localhost
REDIS_PORT=6379

# PostgreSQL
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=ccl_dev
POSTGRES_USER=ccl
POSTGRES_PASSWORD=dev_password

# Google Cloud Platform
GCP_PROJECT=your-project-id
SPANNER_INSTANCE=your-instance
SPANNER_DATABASE=your-database
```

## Monitoring and Alerting

### Prometheus Metrics
The dependency analyzer can be extended to export metrics:

```python
# Example metrics
dependency_health_status{service="analysis-engine", dependency="redis"} 1
dependency_response_time_seconds{service="analysis-engine", dependency="spanner"} 0.150
dependency_check_total{service="analysis-engine"} 5
```

### Alert Rules
```yaml
# Prometheus alert rules
groups:
- name: dependency-health
  rules:
  - alert: DependencyUnhealthy
    expr: dependency_health_status == 0
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "Dependency {{ $labels.dependency }} is unhealthy for service {{ $labels.service }}"
```

## Troubleshooting

### Common Issues

1. **Service Discovery Fails**
   - Ensure service directories contain proper configuration files
   - Check file permissions and paths
   - Verify configuration file syntax

2. **Health Checks Fail**
   - Ensure services are running on expected ports
   - Check network connectivity
   - Verify environment variables

3. **Graph Generation Fails**
   - Install matplotlib: `pip install matplotlib`
   - Ensure X11 forwarding for remote systems
   - Use headless mode for server environments

4. **Dependency Validation Errors**
   - Check service configurations
   - Verify external service connectivity
   - Review authentication credentials

### Debug Mode
```bash
# Enable verbose logging
python dependency_analyzer.py --analyze-all --verbose
python validate_dependencies.py --all --verbose
```

## Contributing

When adding new services or dependencies:

1. **Update Service Configuration**: Add service details to the analyzer
2. **Add Health Checks**: Implement health check logic for new dependencies
3. **Update Documentation**: Modify the generated documentation templates
4. **Test Validation**: Ensure new dependencies are properly validated

## Future Enhancements

1. **Real-time Monitoring**: Integration with monitoring systems
2. **Automated Remediation**: Self-healing dependency issues
3. **Performance Analysis**: Dependency performance impact analysis
4. **Cost Analysis**: Cloud service cost impact analysis
5. **Security Analysis**: Dependency security vulnerability scanning

## Related Documentation

- [Service Dependencies](../../docs/architecture/service-dependencies.md)
- [Dependency Graph](../../docs/architecture/dependency-graph.md)
- [Data Flows](../../docs/architecture/data-flows.md)
- [EPIS-028 Issue](https://github.com/episteme/platform/issues/28)

## Support

For issues or questions about the dependency analyzer tools:

1. Check the troubleshooting section above
2. Review the generated health reports
3. Check service logs for detailed error information
4. Create an issue in the platform repository