# Environment variables for secret-based configuration
# Copy to .env.secrets and customize for your environment

# Database Configuration
DATABASE_HOST=postgres
DATABASE_PORT=5432
DATABASE_NAME=ccl_local
DATABASE_USER=ccl_dev
# Password loaded from secrets/dev-postgres-password.txt

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379
# Password loaded from secrets/dev-redis-password.txt

# Application Configuration
# JWT secret loaded from secrets/dev-jwt-secret.txt
# Session secret loaded from secrets/dev-session-secret.txt

# GCP Configuration
# Service account key loaded from secrets/dev-gcp-service-account-key.json
BIGQUERY_PROJECT=ccl-local
BIGQUERY_DATASET=patterns

# Development-specific settings
ENVIRONMENT=development
LOG_LEVEL=DEBUG
DEBUG=true
