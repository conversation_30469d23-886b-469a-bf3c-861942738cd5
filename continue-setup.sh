#!/bin/bash
# Continue Cloud Build setup after GitHub repository connection
# Run this script AFTER connecting your GitHub repository

set -e

# Configuration
PROJECT_ID="${PROJECT_ID:-vibe-match-463114}"
REGION="${REGION:-us-central1}"
GITHUB_REPO="${GITHUB_REPO:-star-boy-95/episteme}"
BRANCH_PATTERN="${BRANCH_PATTERN:-^(main|develop)$}"

echo "🔨 Creating Cloud Build triggers..."
echo "Project ID: $PROJECT_ID"
echo "GitHub Repository: $GITHUB_REPO"

# Main monorepo trigger
echo "Creating main monorepo build trigger..."
gcloud beta builds triggers create github \
  --project=$PROJECT_ID \
  --repo-name=episteme \
  --repo-owner=star-boy-95 \
  --branch-pattern="$BRANCH_PATTERN" \
  --build-config=cloudbuild.yaml \
  --name="episteme-main-build" \
  --description="Main monorepo build for all services" \
  --include-logs-with-status

# Analysis Engine specific trigger
echo "Creating Analysis Engine build trigger..."
gcloud beta builds triggers create github \
  --project=$PROJECT_ID \
  --repo-name=episteme \
  --repo-owner=star-boy-95 \
  --branch-pattern="$BRANCH_PATTERN" \
  --build-config=services/analysis-engine/cloudbuild-service.yaml \
  --included-files="services/analysis-engine/**" \
  --name="analysis-engine-build" \
  --description="Analysis Engine Rust service build" \
  --include-logs-with-status

# Query Intelligence specific trigger  
echo "Creating Query Intelligence build trigger..."
gcloud beta builds triggers create github \
  --project=$PROJECT_ID \
  --repo-name=episteme \
  --repo-owner=star-boy-95 \
  --branch-pattern="$BRANCH_PATTERN" \
  --build-config=services/query-intelligence/cloudbuild-service.yaml \
  --included-files="services/query-intelligence/**" \
  --name="query-intelligence-build" \
  --description="Query Intelligence Python service build" \
  --include-logs-with-status

echo "🔑 Creating configuration secrets..."

# Database connection string (placeholder - update with real values)
echo "Creating database-url secret..."
echo -n "********************************/episteme" | \
  gcloud secrets create database-url \
    --project=$PROJECT_ID \
    --data-file=- 2>/dev/null || echo "Secret database-url already exists"

# Redis connection string (placeholder - update with real values)  
echo "Creating redis-url secret..."
echo -n "redis://localhost:6379" | \
  gcloud secrets create redis-url \
    --project=$PROJECT_ID \
    --data-file=- 2>/dev/null || echo "Secret redis-url already exists"

# Slack webhook for notifications (placeholder)
echo "Creating slack-webhook-url secret..."
echo -n "https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK" | \
  gcloud secrets create slack-webhook-url \
    --project=$PROJECT_ID \
    --data-file=- 2>/dev/null || echo "Secret slack-webhook-url already exists"

echo "🌍 Setting up deployment environments..."

# Development environment (placeholder services)
for service in analysis-engine query-intelligence pattern-mining marketplace; do
  echo "Creating $service-dev service..."
  
  gcloud run deploy $service-dev \
    --image=us-docker.pkg.dev/cloudrun/container/hello \
    --platform=managed \
    --region=$REGION \
    --memory=1Gi \
    --cpu=1 \
    --max-instances=5 \
    --min-instances=0 \
    --allow-unauthenticated \
    --project=$PROJECT_ID \
    --no-traffic \
    --tag=placeholder 2>/dev/null || echo "$service-dev service might already exist"
done

echo ""
echo "✅ Cloud Build setup continuation complete!"
echo ""
echo "📋 What was completed:"
echo "  • Build triggers created for GitHub repository"
echo "  • Configuration secrets created in Secret Manager"
echo "  • Placeholder Cloud Run services created"
echo ""
echo "🔗 Useful links:"
echo "  • Cloud Build Triggers: https://console.cloud.google.com/cloud-build/triggers?project=$PROJECT_ID"
echo "  • Cloud Build History: https://console.cloud.google.com/cloud-build/builds?project=$PROJECT_ID"
echo "  • Secret Manager: https://console.cloud.google.com/security/secret-manager?project=$PROJECT_ID"
echo "  • Cloud Run Services: https://console.cloud.google.com/run?project=$PROJECT_ID"
echo ""
echo "📝 Next steps:"
echo "  1. Update secrets with real values using: gcloud secrets versions add SECRET_NAME --data-file=-"
echo "  2. Test the pipeline by pushing changes to main or develop branch"
echo "  3. Monitor builds in Cloud Build console"
echo ""
echo "🧪 Test the pipeline:"
echo "  git add . && git commit -m 'test: trigger cloud build' && git push"
echo ""
echo "🚀 Your CI/CD pipeline is ready for testing!"